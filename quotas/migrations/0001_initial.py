# Generated by Django 5.2.3 on 2025-06-15 23:58

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('projects', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProjectQuota',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quota_number', models.CharField(max_length=50, verbose_name='定额编号')),
                ('name', models.CharField(max_length=200, verbose_name='定额项名称')),
                ('work_content', models.TextField(verbose_name='工作内容')),
                ('unit', models.CharField(max_length=50, verbose_name='单位')),
                ('unit_price', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='单价')),
                ('category', models.CharField(choices=[('labor', '人工'), ('material', '材料'), ('machinery', '机械'), ('comprehensive', '综合')], default='comprehensive', max_length=20, verbose_name='定额分类')),
                ('remarks', models.TextField(blank=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='quotas', to='projects.project', verbose_name='项目')),
            ],
            options={
                'verbose_name': '项目定额',
                'verbose_name_plural': '项目定额',
                'ordering': ['quota_number'],
                'unique_together': {('project', 'quota_number')},
            },
        ),
        migrations.CreateModel(
            name='QuotaResourceConsumption',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('resource_number', models.CharField(max_length=50, verbose_name='资源编号')),
                ('resource_name', models.CharField(max_length=200, verbose_name='资源名称')),
                ('category', models.CharField(choices=[('labor', '人工'), ('material', '材料'), ('machinery', '机械'), ('indirect', '间接费用')], max_length=20, verbose_name='资源类别')),
                ('unit', models.CharField(max_length=50, verbose_name='单位')),
                ('consumption', models.DecimalField(decimal_places=4, max_digits=10, verbose_name='消耗数量')),
                ('unit_price', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='单价')),
                ('total_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='合价')),
                ('remarks', models.TextField(blank=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('quota', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='resource_consumptions', to='quotas.projectquota', verbose_name='定额项')),
            ],
            options={
                'verbose_name': '定额资源消耗明细',
                'verbose_name_plural': '定额资源消耗明细',
                'ordering': ['resource_number'],
                'unique_together': {('quota', 'resource_number')},
            },
        ),
    ]
