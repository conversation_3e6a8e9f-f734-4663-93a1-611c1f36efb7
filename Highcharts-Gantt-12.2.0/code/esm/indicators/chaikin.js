import*as e from"../highcharts.js";import"../modules/stock.js";var t={};t.n=e=>{var r=e&&e.__esModule?()=>e.default:()=>e;return t.d(r,{a:r}),r},t.d=(e,r)=>{for(var a in r)t.o(r,a)&&!t.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:r[a]})},t.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);let r=e.default;var a=t.n(r);t.d({},{});let s=e.default.SeriesRegistry;var o=t.n(s);let{sma:i}=o().seriesTypes,{error:l,extend:u,merge:n}=a();class p extends i{static populateAverage(e,t,r,a,s){let o=t[a][1],i=t[a][2],l=t[a][3],u=r[a],n=l===o&&l===i||o===i?0:(2*l-i-o)/(o-i)*u;return[e[a],n]}getValues(e,t){let r,a,s,o=t.period,i=e.xData,u=e.yData,n=t.volumeSeriesID,d=e.chart.get(n),h=d?.getColumn("y"),m=u?u.length:0,c=[],g=[],f=[];if(!(i.length<=o)||!m||4===u[0].length){if(!d){l("Series "+n+" not found! Check `volumeSeriesID`.",!0,e.chart);return}for(a=o;a<m;a++)r=c.length,s=p.populateAverage(i,u,h,a,o),r>0&&(s[1]+=c[r-1][1]),c.push(s),g.push(s[0]),f.push(s[1]);return{values:c,xData:g,yData:f}}}}p.defaultOptions=n(i.defaultOptions,{params:{index:void 0,volumeSeriesID:"volume"}}),u(p.prototype,{nameComponents:!1,nameBase:"Accumulation/Distribution"}),o().registerSeriesType("ad",p);let{ema:d}=o().seriesTypes,{correctFloat:h,extend:m,merge:c,error:g}=a();class f extends d{getValues(e,t){let r,a,s=t.periods,o=t.period,i=[],l=[],u=[];if(2!==s.length||s[1]<=s[0]){g('Error: "Chaikin requires two periods. Notice, first period should be lower than the second one."');return}let n=p.prototype.getValues.call(this,e,{volumeSeriesID:t.volumeSeriesID,period:o});if(!n)return;let d=super.getValues.call(this,n,{period:s[0]}),m=super.getValues.call(this,n,{period:s[1]});if(!d||!m)return;let c=s[1]-s[0];for(a=0;a<m.yData.length;a++)r=h(d.yData[a+c]-m.yData[a]),i.push([m.xData[a],r]),l.push(m.xData[a]),u.push(r);return{values:i,xData:l,yData:u}}}f.defaultOptions=c(d.defaultOptions,{params:{index:void 0,volumeSeriesID:"volume",period:9,periods:[3,10]}}),m(f.prototype,{nameBase:"Chaikin Osc",nameComponents:["periods"]}),o().registerSeriesType("chaikin",f);let v=a();export{v as default};