import*as t from"../highcharts.js";import"../modules/stock.js";var e,o={};o.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return o.d(e,{a:e}),e},o.d=(t,e)=>{for(var i in e)o.o(e,i)&&!o.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},o.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);let i=t.default;var a=o.n(i);o.d({},{});let r=t.default.SeriesRegistry;var s=o.n(r);let{sma:{prototype:n}}=s().seriesTypes,{defined:l,error:p,merge:h}=a();!function(t){let e=["bottomLine"],o=["top","bottom"],i=["top"];function a(t){return"plot"+t.charAt(0).toUpperCase()+t.slice(1)}function r(t,e){let o=[];return(t.pointArrayMap||[]).forEach(t=>{t!==e&&o.push(a(t))}),o}function s(){let t=this,e=t.pointValKey,o=t.linesApiNames,i=t.areaLinesNames,s=t.points,d=t.options,c=t.graph,m={options:{gapSize:d.gapSize}},u=[],y=r(t,e),f=s.length,g;if(y.forEach((t,e)=>{for(u[e]=[];f--;)g=s[f],u[e].push({x:g.x,plotX:g.plotX,plotY:g[t],isNull:!l(g[t])});f=s.length}),t.userOptions.fillColor&&i.length){let e=u[y.indexOf(a(i[0]))],o=1===i.length?s:u[y.indexOf(a(i[1]))],r=t.color;t.points=o,t.nextPoints=e,t.color=t.userOptions.fillColor,t.options=h(s,m),t.graph=t.area,t.fillGraph=!0,n.drawGraph.call(t),t.area=t.graph,delete t.nextPoints,delete t.fillGraph,t.color=r}o.forEach((e,o)=>{u[o]?(t.points=u[o],d[e]?t.options=h(d[e].styles,m):p('Error: "There is no '+e+' in DOCS options declared. Check if linesApiNames are consistent with your DOCS line names."'),t.graph=t["graph"+e],n.drawGraph.call(t),t["graph"+e]=t.graph):p('Error: "'+e+" doesn't have equivalent in pointArrayMap. To many elements in linesApiNames relative to pointArrayMap.\"")}),t.points=s,t.options=d,t.graph=c,n.drawGraph.call(t)}function d(t){let e,o=[],i=[];if(t=t||this.points,this.fillGraph&&this.nextPoints){if((e=n.getGraphPath.call(this,this.nextPoints))&&e.length){e[0][0]="L",o=n.getGraphPath.call(this,t),i=e.slice(0,o.length);for(let t=i.length-1;t>=0;t--)o.push(i[t])}}else o=n.getGraphPath.apply(this,arguments);return o}function c(t){let e=[];return(this.pointArrayMap||[]).forEach(o=>{e.push(t[o])}),e}function m(){let t=this.pointArrayMap,e=[],o;e=r(this),n.translate.apply(this,arguments),this.points.forEach(i=>{t.forEach((t,a)=>{o=i[t],this.dataModify&&(o=this.dataModify.modifyValue(o)),null!==o&&(i[e[a]]=this.yAxis.toPixels(o,!0))})})}t.compose=function(t){let a=t.prototype;return a.linesApiNames=a.linesApiNames||e.slice(),a.pointArrayMap=a.pointArrayMap||o.slice(),a.pointValKey=a.pointValKey||"top",a.areaLinesNames=a.areaLinesNames||i.slice(),a.drawGraph=s,a.getGraphPath=d,a.toYData=c,a.translate=m,t}}(e||(e={}));let d=e,{sma:c}=s().seriesTypes,{extend:m,isArray:u,merge:y}=a();class f extends c{init(){s().seriesTypes.sma.prototype.init.apply(this,arguments),this.options=y({topLine:{styles:{lineColor:this.color}},bottomLine:{styles:{lineColor:this.color}}},this.options)}getValues(t,e){let o,i,a,r,n,l,p,h,d,c=e.period,m=e.standardDeviation,y=[],f=[],g=t.xData,b=t.yData,x=b?b.length:0,A=[];if(g.length<c)return;let v=u(b[0]);for(d=c;d<=x;d++)n=g.slice(d-c,d),l=b.slice(d-c,d),r=(h=s().seriesTypes.sma.prototype.getValues.call(this,{xData:n,yData:l},e)).xData[0],o=h.yData[0],p=function(t,e,o,i){let a=t.length,r=0,s=0,n,l=0;for(;r<a;r++)l+=(n=(o?t[r][e]:t[r])-i)*n;return Math.sqrt(l/=a-1)}(l,e.index,v,o),i=o+m*p,a=o-m*p,A.push([r,i,o,a]),y.push(r),f.push([i,o,a]);return{values:A,xData:y,yData:f}}}f.defaultOptions=y(c.defaultOptions,{params:{period:20,standardDeviation:2,index:3},bottomLine:{styles:{lineWidth:1,lineColor:void 0}},topLine:{styles:{lineWidth:1,lineColor:void 0}},tooltip:{pointFormat:'<span style="color:{point.color}">●</span><b> {series.name}</b><br/>Top: {point.top}<br/>Middle: {point.middle}<br/>Bottom: {point.bottom}<br/>'},marker:{enabled:!1},dataGrouping:{approximation:"averages"}}),m(f.prototype,{areaLinesNames:["top","bottom"],linesApiNames:["topLine","bottomLine"],nameComponents:["period","standardDeviation"],pointArrayMap:["top","middle","bottom"],pointValKey:"middle"}),d.compose(f),s().registerSeriesType("bb",f);let g=a();export{g as default};