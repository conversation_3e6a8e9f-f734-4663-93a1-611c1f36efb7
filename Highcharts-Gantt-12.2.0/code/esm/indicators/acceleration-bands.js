import*as e from"../highcharts.js";import"../modules/stock.js";var t,a={};a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},a.d=(e,t)=>{for(var i in t)a.o(t,i)&&!a.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);let i=e.default;var o=a.n(i);a.d({},{});let s=e.default.SeriesRegistry;var r=a.n(s);let{sma:{prototype:n}}=r().seriesTypes,{defined:l,error:p,merge:h}=o();!function(e){let t=["bottomLine"],a=["top","bottom"],i=["top"];function o(e){return"plot"+e.charAt(0).toUpperCase()+e.slice(1)}function s(e,t){let a=[];return(e.pointArrayMap||[]).forEach(e=>{e!==t&&a.push(o(e))}),a}function r(){let e=this,t=e.pointValKey,a=e.linesApiNames,i=e.areaLinesNames,r=e.points,c=e.options,d=e.graph,u={options:{gapSize:c.gapSize}},f=[],y=s(e,t),m=r.length,g;if(y.forEach((e,t)=>{for(f[t]=[];m--;)g=r[m],f[t].push({x:g.x,plotX:g.plotX,plotY:g[e],isNull:!l(g[e])});m=r.length}),e.userOptions.fillColor&&i.length){let t=f[y.indexOf(o(i[0]))],a=1===i.length?r:f[y.indexOf(o(i[1]))],s=e.color;e.points=a,e.nextPoints=t,e.color=e.userOptions.fillColor,e.options=h(r,u),e.graph=e.area,e.fillGraph=!0,n.drawGraph.call(e),e.area=e.graph,delete e.nextPoints,delete e.fillGraph,e.color=s}a.forEach((t,a)=>{f[a]?(e.points=f[a],c[t]?e.options=h(c[t].styles,u):p('Error: "There is no '+t+' in DOCS options declared. Check if linesApiNames are consistent with your DOCS line names."'),e.graph=e["graph"+t],n.drawGraph.call(e),e["graph"+t]=e.graph):p('Error: "'+t+" doesn't have equivalent in pointArrayMap. To many elements in linesApiNames relative to pointArrayMap.\"")}),e.points=r,e.options=c,e.graph=d,n.drawGraph.call(e)}function c(e){let t,a=[],i=[];if(e=e||this.points,this.fillGraph&&this.nextPoints){if((t=n.getGraphPath.call(this,this.nextPoints))&&t.length){t[0][0]="L",a=n.getGraphPath.call(this,e),i=t.slice(0,a.length);for(let e=i.length-1;e>=0;e--)a.push(i[e])}}else a=n.getGraphPath.apply(this,arguments);return a}function d(e){let t=[];return(this.pointArrayMap||[]).forEach(a=>{t.push(e[a])}),t}function u(){let e=this.pointArrayMap,t=[],a;t=s(this),n.translate.apply(this,arguments),this.points.forEach(i=>{e.forEach((e,o)=>{a=i[e],this.dataModify&&(a=this.dataModify.modifyValue(a)),null!==a&&(i[t[o]]=this.yAxis.toPixels(a,!0))})})}e.compose=function(e){let o=e.prototype;return o.linesApiNames=o.linesApiNames||t.slice(),o.pointArrayMap=o.pointArrayMap||a.slice(),o.pointValKey=o.pointValKey||"top",o.areaLinesNames=o.areaLinesNames||i.slice(),o.drawGraph=r,o.getGraphPath=c,o.toYData=d,o.translate=u,e}}(t||(t={}));let c=t,{sma:d}=r().seriesTypes,{correctFloat:u,extend:f,merge:y}=o();class m extends d{getValues(e,t){let a,i,o,s,r,n,l,p,h,c,d,f=t.period,y=t.factor,m=t.index,g=e.xData,x=e.yData,A=x?x.length:0,D=[],G=[],b=[],v=[],M=[];if(!(A<f)){for(d=0;d<=A;d++){if(d<A){var N,O;N=x[d][2],r=u((O=x[d][1])-N)/(u(O+N)/2)*1e3*y,D.push(x[d][1]*u(1+2*r)),G.push(x[d][2]*u(1-2*r))}d>=f&&(h=g.slice(d-f,d),c=x.slice(d-f,d),l=super.getValues.call(this,{xData:h,yData:D.slice(d-f,d)},{period:f}),p=super.getValues.call(this,{xData:h,yData:G.slice(d-f,d)},{period:f}),s=(n=super.getValues.call(this,{xData:h,yData:c},{period:f,index:m})).xData[0],i=l.yData[0],o=p.yData[0],a=n.yData[0],b.push([s,i,a,o]),v.push(s),M.push([i,a,o]))}return{values:b,xData:v,yData:M}}}}m.defaultOptions=y(d.defaultOptions,{params:{period:20,factor:.001,index:3},lineWidth:1,topLine:{styles:{lineWidth:1}},bottomLine:{styles:{lineWidth:1}},dataGrouping:{approximation:"averages"}}),f(m.prototype,{areaLinesNames:["top","bottom"],linesApiNames:["topLine","bottomLine"],nameBase:"Acceleration Bands",nameComponents:["period","factor"],pointArrayMap:["top","middle","bottom"],pointValKey:"middle"}),c.compose(m),r().registerSeriesType("abands",m);let g=o();export{g as default};