{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.2.0 (2025-04-07)\n * @module highcharts/indicators/cmf\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * (c) 2010-2025 Highsoft AS\n * Author: <PERSON>\n *\n * License: www.highcharts.com/license\n */\nimport * as __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__ from \"../highcharts.js\";\nimport * as __WEBPACK_EXTERNAL_MODULE__modules_stock_src_js_b3d80146__ from \"../modules/stock.js\";\n/******/ // The require scope\n/******/ var __webpack_require__ = {};\n/******/ \n/************************************************************************/\n/******/ /* webpack/runtime/compat get default export */\n/******/ (() => {\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = (module) => {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t() => (module['default']) :\n/******/ \t\t\t() => (module);\n/******/ \t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\treturn getter;\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/define property getters */\n/******/ (() => {\n/******/ \t// define getter functions for harmony exports\n/******/ \t__webpack_require__.d = (exports, definition) => {\n/******/ \t\tfor(var key in definition) {\n/******/ \t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t}\n/******/ \t\t}\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/hasOwnProperty shorthand */\n/******/ (() => {\n/******/ \t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ })();\n/******/ \n/************************************************************************/\n\n;// external [\"../highcharts.js\",\"default\"]\nconst external_highcharts_src_js_default_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"];\nvar external_highcharts_src_js_default_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_namespaceObject);\n;// external \"../modules/stock.js\"\nvar x = (y) => {\n\tvar x = {}; __webpack_require__.d(x,\n    \ty); return x\n    } \n    var y = (x) => (() => (x))\n    const stock_src_js_namespaceObject = x({  });\n;// external [\"../highcharts.js\",\"default\",\"SeriesRegistry\"]\nconst external_highcharts_src_js_default_SeriesRegistry_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].SeriesRegistry;\nvar external_highcharts_src_js_default_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_SeriesRegistry_namespaceObject);\n;// ./code/es-modules/Stock/Indicators/CMF/CMFIndicator.js\n/* *\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Sebastian Domas\n *\n *  Chaikin Money Flow indicator for Highcharts Stock\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The CMF series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.cmf\n *\n * @augments Highcharts.Series\n */\nclass CMFIndicator extends SMAIndicator {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        super(...arguments);\n        this.nameBase = 'Chaikin Money Flow';\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Checks if the series and volumeSeries are accessible, number of\n     * points.x is longer than period, is series has OHLC data\n     * @private\n     * @param {Highcharts.CMFIndicator} this indicator to use.\n     * @return {boolean} True if series is valid and can be computed,\n     * otherwise false.\n     */\n    isValid() {\n        const chart = this.chart, options = this.options, series = this.linkedParent, volumeSeries = (this.volumeSeries ||\n            (this.volumeSeries =\n                chart.get(options.params.volumeSeriesID))), isSeriesOHLC = (series?.pointArrayMap?.length === 4);\n        /**\n         * @private\n         * @param {Highcharts.Series} serie to check length validity on.\n         * @return {boolean|undefined} true if length is valid.\n         */\n        function isLengthValid(serie) {\n            return serie.dataTable.rowCount >=\n                options.params.period;\n        }\n        return !!(series &&\n            volumeSeries &&\n            isLengthValid(series) &&\n            isLengthValid(volumeSeries) && isSeriesOHLC);\n    }\n    /**\n     * Returns indicator's data.\n     * @private\n     * @param {Highcharts.CMFIndicator} this indicator to use.\n     * @param {Highcharts.Series} series to calculate values from\n     * @param {Highcharts.CMFIndicatorParamsOptions} params to pass\n     * @return {boolean|Highcharts.IndicatorNullableValuesObject} Returns false if the\n     * indicator is not valid, otherwise returns Values object.\n     */\n    getValues(series, params) {\n        if (!this.isValid()) {\n            return;\n        }\n        return this.getMoneyFlow(series.xData, series.yData, this.volumeSeries.getColumn('y'), params.period);\n    }\n    /**\n     * @private\n     *\n     * @param {Array<number>} xData\n     * x timestamp values\n     *\n     * @param {Array<number>} seriesYData\n     * yData of basic series\n     *\n     * @param {Array<number>} volumeSeriesYData\n     * yData of volume series\n     *\n     * @param {number} period\n     * indicator's param\n     *\n     * @return {Highcharts.IndicatorNullableValuesObject}\n     * object containing computed money flow data\n     */\n    getMoneyFlow(xData, seriesYData, volumeSeriesYData, period) {\n        const len = seriesYData.length, moneyFlowVolume = [], moneyFlowXData = [], moneyFlowYData = [], values = [];\n        let i, point, nullIndex = -1, sumVolume = 0, sumMoneyFlowVolume = 0;\n        /**\n         * Calculates money flow volume, changes i, nullIndex vars from\n         * upper scope!\n         *\n         * @private\n         *\n         * @param {Array<number>} ohlc\n         * OHLC point\n         *\n         * @param {number} volume\n         * Volume point's y value\n         *\n         * @return {number|null}\n         * Volume * moneyFlowMultiplier\n         */\n        function getMoneyFlowVolume(ohlc, volume) {\n            const high = ohlc[1], low = ohlc[2], close = ohlc[3], isValid = volume !== null &&\n                high !== null &&\n                low !== null &&\n                close !== null &&\n                high !== low;\n            /**\n             * @private\n             * @param {number} h\n             * High value\n             * @param {number} l\n             * Low value\n             * @param {number} c\n             * Close value\n             * @return {number}\n             * Calculated multiplier for the point\n             */\n            function getMoneyFlowMultiplier(h, l, c) {\n                return ((c - l) - (h - c)) / (h - l);\n            }\n            return isValid ?\n                getMoneyFlowMultiplier(high, low, close) * volume :\n                ((nullIndex = i), null);\n        }\n        if (period > 0 && period <= len) {\n            for (i = 0; i < period; i++) {\n                moneyFlowVolume[i] = getMoneyFlowVolume(seriesYData[i], volumeSeriesYData[i]);\n                sumVolume += volumeSeriesYData[i];\n                sumMoneyFlowVolume += moneyFlowVolume[i];\n            }\n            moneyFlowXData.push(xData[i - 1]);\n            moneyFlowYData.push(i - nullIndex >= period && sumVolume !== 0 ?\n                sumMoneyFlowVolume / sumVolume :\n                null);\n            values.push([moneyFlowXData[0], moneyFlowYData[0]]);\n            for (; i < len; i++) {\n                moneyFlowVolume[i] = getMoneyFlowVolume(seriesYData[i], volumeSeriesYData[i]);\n                sumVolume -= volumeSeriesYData[i - period];\n                sumVolume += volumeSeriesYData[i];\n                sumMoneyFlowVolume -= moneyFlowVolume[i - period];\n                sumMoneyFlowVolume += moneyFlowVolume[i];\n                point = [\n                    xData[i],\n                    i - nullIndex >= period ?\n                        sumMoneyFlowVolume / sumVolume :\n                        null\n                ];\n                moneyFlowXData.push(point[0]);\n                moneyFlowYData.push(point[1]);\n                values.push([point[0], point[1]]);\n            }\n        }\n        return {\n            values: values,\n            xData: moneyFlowXData,\n            yData: moneyFlowYData\n        };\n    }\n}\n/**\n * Chaikin Money Flow indicator (cmf).\n *\n * @sample stock/indicators/cmf/\n *         Chaikin Money Flow indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @excluding    animationLimit\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/cmf\n * @optionparent plotOptions.cmf\n */\nCMFIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    /**\n     * @excluding index\n     */\n    params: {\n        index: void 0, // Unused index, do not inherit (#15362)\n        /**\n         * The id of another series to use its data as volume data for the\n         * indicator calculation.\n         */\n        volumeSeriesID: 'volume'\n    }\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('cmf', CMFIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const CMF_CMFIndicator = ((/* unused pure expression or super */ null && (CMFIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `CMF` series. If the [type](#series.cmf.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.cmf\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/cmf\n * @apioption series.cmf\n */\n''; // Adds doclet above to the transpiled file\n\n;// ./code/es-modules/masters/indicators/cmf.js\n\n\n\n\n\n/* harmony default export */ const cmf_src = ((external_highcharts_src_js_default_default()));\n\nexport { cmf_src as default };\n"], "names": ["__WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__", "__webpack_require__", "n", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "external_highcharts_src_js_default_namespaceObject", "external_highcharts_src_js_default_default", "external_highcharts_src_js_default_SeriesRegistry_namespaceObject", "SeriesRegistry", "external_highcharts_src_js_default_SeriesRegistry_default", "sma", "SMAIndicator", "seriesTypes", "merge", "CMFIndicator", "constructor", "arguments", "nameBase", "<PERSON><PERSON><PERSON><PERSON>", "chart", "options", "series", "linkedParent", "volumeSeries", "params", "volumeSeriesID", "isSeriesOHLC", "pointArrayMap", "length", "is<PERSON>ength<PERSON><PERSON>d", "serie", "dataTable", "rowCount", "period", "getV<PERSON>ues", "getMoneyFlow", "xData", "yData", "getColumn", "seriesYData", "volumeSeriesYData", "len", "moneyFlowVolume", "moneyFlowXData", "moneyFlowYData", "values", "i", "point", "nullIndex", "sumVolume", "sumMoneyFlowVolume", "getMoneyFlowVolume", "ohlc", "volume", "high", "low", "close", "getMoneyFlowMultiplier", "h", "c", "l", "push", "defaultOptions", "index", "registerSeriesType", "cmf_src", "default"], "mappings": "AAWA,UAAYA,MAA6D,sBAAuB,AAChG,OAA4E,yBAA0B,CAE7F,IAAIC,EAAsB,CAAC,CAM1BA,CAAAA,EAAoBC,CAAC,CAAG,AAACC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,UAAU,CACvC,IAAOF,EAAO,OAAU,CACxB,IAAOA,EAER,OADAF,EAAoBK,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAH,EAAoBK,CAAC,CAAG,CAACE,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXR,EAAoBU,CAAC,CAACF,EAAYC,IAAQ,CAACT,EAAoBU,CAAC,CAACH,EAASE,IAC5EE,OAAOC,cAAc,CAACL,EAASE,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAT,EAAoBU,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAM5F,IAAMI,EAAqDrB,EAAwD,OAAU,CAC7H,IAAIsB,EAA0DrB,EAAoBC,CAAC,CAACmB,GAGvEpB,EAAoBK,CAAC,CAAzB,CAAC,EAIiC,CAAG,GAE9C,IAAMiB,EAAoEvB,EAAwD,OAAU,CAACwB,cAAc,CAC3J,IAAIC,EAAyExB,EAAoBC,CAAC,CAACqB,GAiBnG,GAAM,CAAEG,IAAKC,CAAY,CAAE,CAAG,AAACF,IAA6DG,WAAW,CAEjG,CAAEC,MAAAA,CAAK,CAAE,CAAIP,GAenB,OAAMQ,UAAqBH,EACvBI,aAAc,CAMV,KAAK,IAAIC,WACT,IAAI,CAACC,QAAQ,CAAG,oBACpB,CAcAC,SAAU,CACN,IAAMC,EAAQ,IAAI,CAACA,KAAK,CAAEC,EAAU,IAAI,CAACA,OAAO,CAAEC,EAAS,IAAI,CAACC,YAAY,CAAEC,EAAgB,IAAI,CAACA,YAAY,EAC1G,CAAA,IAAI,CAACA,YAAY,CACdJ,EAAMpB,GAAG,CAACqB,EAAQI,MAAM,CAACC,cAAc,CAAA,EAAKC,EAAgBL,GAAQM,eAAeC,SAAW,EAMtG,SAASC,EAAcC,CAAK,EACxB,OAAOA,EAAMC,SAAS,CAACC,QAAQ,EAC3BZ,EAAQI,MAAM,CAACS,MAAM,AAC7B,CACA,MAAO,CAAC,CAAEZ,CAAAA,GACNE,GACAM,EAAcR,IACdQ,EAAcN,IAAiBG,CAAW,CAClD,CAUAQ,UAAUb,CAAM,CAAEG,CAAM,CAAE,CACtB,GAAK,IAAI,CAACN,OAAO,GAGjB,OAAO,IAAI,CAACiB,YAAY,CAACd,EAAOe,KAAK,CAAEf,EAAOgB,KAAK,CAAE,IAAI,CAACd,YAAY,CAACe,SAAS,CAAC,KAAMd,EAAOS,MAAM,CACxG,CAmBAE,aAAaC,CAAK,CAAEG,CAAW,CAAEC,CAAiB,CAAEP,CAAM,CAAE,CACxD,IAAMQ,EAAMF,EAAYX,MAAM,CAAEc,EAAkB,EAAE,CAAEC,EAAiB,EAAE,CAAEC,EAAiB,EAAE,CAAEC,EAAS,EAAE,CACvGC,EAAGC,EAAOC,EAAY,GAAIC,EAAY,EAAGC,EAAqB,EAgBlE,SAASC,EAAmBC,CAAI,CAAEC,CAAM,EACpC,IAAMC,EAAOF,CAAI,CAAC,EAAE,CAAEG,EAAMH,CAAI,CAAC,EAAE,CAAEI,EAAQJ,CAAI,CAAC,EAAE,CAmBpD,OAAOlC,AAnByDmC,AAAW,OAAXA,GAC5DC,AAAS,OAATA,GACAC,AAAQ,OAARA,GACAC,AAAU,OAAVA,GACAF,IAASC,EAgBTE,AAHQ,CAAA,AAG0BD,EAALD,EAHVG,CAAAA,AAGIJ,EAAWE,CAHXG,CAAC,EAAMD,CAAAA,AAGPJ,EAAMC,CAHKK,EAGSP,EAC1C,CAAA,AAACL,EAAYF,EAAI,IAAG,CAC7B,CACA,GAAIb,EAAS,GAAKA,GAAUQ,EAAK,CAC7B,IAAKK,EAAI,EAAGA,EAAIb,EAAQa,IACpBJ,CAAe,CAACI,EAAE,CAAGK,EAAmBZ,CAAW,CAACO,EAAE,CAAEN,CAAiB,CAACM,EAAE,EAC5EG,GAAaT,CAAiB,CAACM,EAAE,CACjCI,GAAsBR,CAAe,CAACI,EAAE,CAO5C,IALAH,EAAekB,IAAI,CAACzB,CAAK,CAACU,EAAI,EAAE,EAChCF,EAAeiB,IAAI,CAACf,EAAIE,GAAaf,GAAUgB,AAAc,IAAdA,EAC3CC,EAAqBD,EACrB,MACJJ,EAAOgB,IAAI,CAAC,CAAClB,CAAc,CAAC,EAAE,CAAEC,CAAc,CAAC,EAAE,CAAC,EAC3CE,EAAIL,EAAKK,IACZJ,CAAe,CAACI,EAAE,CAAGK,EAAmBZ,CAAW,CAACO,EAAE,CAAEN,CAAiB,CAACM,EAAE,EAC5EG,GAAaT,CAAiB,CAACM,EAAIb,EAAO,CAC1CgB,GAAaT,CAAiB,CAACM,EAAE,CACjCI,GAAsBR,CAAe,CAACI,EAAIb,EAAO,CACjDiB,GAAsBR,CAAe,CAACI,EAAE,CACxCC,EAAQ,CACJX,CAAK,CAACU,EAAE,CACRA,EAAIE,GAAaf,EACbiB,EAAqBD,EACrB,KACP,CACDN,EAAekB,IAAI,CAACd,CAAK,CAAC,EAAE,EAC5BH,EAAeiB,IAAI,CAACd,CAAK,CAAC,EAAE,EAC5BF,EAAOgB,IAAI,CAAC,CAACd,CAAK,CAAC,EAAE,CAAEA,CAAK,CAAC,EAAE,CAAC,CAExC,CACA,MAAO,CACHF,OAAQA,EACRT,MAAOO,EACPN,MAAOO,CACX,CACJ,CACJ,CAeA9B,EAAagD,cAAc,CAAGjD,EAAMF,EAAamD,cAAc,CAAE,CAI7DtC,OAAQ,CACJuC,MAAO,KAAK,EAKZtC,eAAgB,QACpB,CACJ,GACAhB,IAA4DuD,kBAAkB,CAAC,MAAOlD,GAgCzD,IAAMmD,EAAY3D,WAEtC2D,KAAWC,OAAO"}