import*as e from"../highcharts.js";import"../modules/stock.js";var t={};t.n=e=>{var s=e&&e.__esModule?()=>e.default:()=>e;return t.d(s,{a:s}),s},t.d=(e,s)=>{for(var o in s)t.o(s,o)&&!t.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:s[o]})},t.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);let s=e.default;var o=t.n(s);t.d({},{});let i=e.default.SeriesRegistry;var a=t.n(i);let{sma:{prototype:{pointClass:n}}}=a().seriesTypes,r=class extends n{destroy(){this.negativeGraphic&&(this.negativeGraphic=this.negativeGraphic.destroy()),super.destroy.apply(this,arguments)}},{animObject:l}=o(),{noop:p}=o(),{column:{prototype:h},sma:d}=a().seriesTypes,{addEvent:u,arrayMax:m,arrayMin:c,correctFloat:g,defined:v,error:f,extend:y,isArray:x,merge:D}=o(),V=Math.abs;class S extends d{init(e,t){let s=this;delete t.data,super.init.apply(s,arguments);let o=u(this.chart.constructor,"afterLinkSeries",function(){if(s.options){let t=s.options.params,o=s.linkedParent,i=e.get(t.volumeSeriesID);s.addCustomEvents(o,i)}o()},{order:1});return s}addCustomEvents(e,t){let s=this,o=()=>{s.chart.redraw(),s.setData([]),s.zoneStarts=[],s.zoneLinesSVG&&(s.zoneLinesSVG=s.zoneLinesSVG.destroy())};return s.dataEventsToUnbind.push(u(e,"remove",function(){o()})),t&&s.dataEventsToUnbind.push(u(t,"remove",function(){o()})),s}animate(e){let t=this,s=t.chart.inverted,o=t.group,i={};if(!e&&o){let e=s?t.yAxis.top:t.xAxis.left;s?(o["forceAnimate:translateY"]=!0,i.translateY=e):(o["forceAnimate:translateX"]=!0,i.translateX=e),o.animate(i,y(l(t.options.animation),{step:function(e,s){t.group.attr({scaleX:Math.max(.001,s.pos)})}}))}}drawPoints(){this.options.volumeDivision.enabled&&(this.posNegVolume(!0,!0),h.drawPoints.apply(this,arguments),this.posNegVolume(!1,!1)),h.drawPoints.apply(this,arguments)}posNegVolume(e,t){let s=t?["positive","negative"]:["negative","positive"],o=this.options.volumeDivision,i=this.points.length,a=[],n=[],r=0,l,p,h,d;for(e?(this.posWidths=a,this.negWidths=n):(a=this.posWidths,n=this.negWidths);r<i;r++)(d=this.points[r])[s[0]+"Graphic"]=d.graphic,d.graphic=d[s[1]+"Graphic"],e&&(l=d.shapeArgs.width,(h=(p=this.priceZones[r]).wholeVolumeData)?(a.push(l/h*p.positiveVolumeData),n.push(l/h*p.negativeVolumeData)):(a.push(0),n.push(0))),d.color=t?o.styles.positiveColor:o.styles.negativeColor,d.shapeArgs.width=t?this.posWidths[r]:this.negWidths[r],d.shapeArgs.x=t?d.shapeArgs.x:this.posWidths[r]}translate(){let e=this,t=e.options,s=e.chart,o=e.yAxis,i=o.min,a=e.options.zoneLines,n=e.priceZones,r=0,l,p,d,u,c,v,f,y,x,D;h.translate.apply(e);let S=e.points;S.length&&(f=t.pointPadding<.5?t.pointPadding:.1,l=m(e.volumeDataArray),p=s.plotWidth/2,y=s.plotTop,d=V(o.toPixels(i)-o.toPixels(i+e.rangeStep)),c=V(o.toPixels(i)-o.toPixels(i+e.rangeStep)),f&&(u=V(d*(1-2*f)),r=V((d-u)/2),d=V(u)),S.forEach(function(t,s){x=t.barX=t.plotX=0,D=t.plotY=o.toPixels(n[s].start)-y-(o.reversed?d-c:d)-r,t.pointWidth=v=g(p*n[s].wholeVolumeData/l),t.shapeArgs=e.crispCol.apply(e,[x,D,v,d]),t.volumeNeg=n[s].negativeVolumeData,t.volumePos=n[s].positiveVolumeData,t.volumeAll=n[s].wholeVolumeData}),a.enabled&&e.drawZones(s,o,e.zoneStarts,a.styles))}getExtremes(){let e,t=this.options.compare,s=this.options.cumulative;return this.options.compare?(this.options.compare=void 0,e=super.getExtremes(),this.options.compare=t):this.options.cumulative?(this.options.cumulative=!1,e=super.getExtremes(),this.options.cumulative=s):e=super.getExtremes(),e}getValues(e,t){let s=e.getColumn("x",!0),o=e.processedYData,i=this.chart,a=t.ranges,n=[],r=[],l=[],p=i.get(t.volumeSeriesID);if(!e.chart){f("Base series not found! In case it has been removed, add a new one.",!0,i);return}if(!p||!p.getColumn("x",!0).length){let e=p&&!p.getColumn("x",!0).length?" does not contain any data.":" not found! Check `volumeSeriesID`.";f("Series "+t.volumeSeriesID+e,!0,i);return}let h=x(o[0]);if(h&&4!==o[0].length){f("Type of "+e.name+" series is different than line, OHLC or candlestick.",!0,i);return}return(this.priceZones=this.specifyZones(h,s,o,a,p)).forEach(function(e,t){n.push([e.x,e.end]),r.push(n[t][0]),l.push(n[t][1])}),{values:n,xData:r,yData:l}}specifyZones(e,t,s,o,i){let a=!!e&&function(e){let t=e.length,s=e[0][3],o=s,i=1,a;for(;i<t;i++)(a=e[i][3])<s&&(s=a),a>o&&(o=a);return{min:s,max:o}}(s),n=this.zoneStarts=[],r=[],l=a?a.min:c(s),p=a?a.max:m(s),h=0,d=1,u=this.linkedParent;if(!this.options.compareToMain&&u.dataModify&&(l=u.dataModify.modifyValue(l),p=u.dataModify.modifyValue(p)),!v(l)||!v(p))return this.points.length&&(this.setData([]),this.zoneStarts=[],this.zoneLinesSVG&&(this.zoneLinesSVG=this.zoneLinesSVG.destroy())),[];let f=this.rangeStep=g(p-l)/o;for(n.push(l);h<o-1;h++)n.push(g(n[h]+f));n.push(p);let y=n.length;for(;d<y;d++)r.push({index:d-1,x:t[0],start:n[d-1],end:n[d]});return this.volumePerZone(e,r,i,t,s)}volumePerZone(e,t,s,o,i){let a,n,r,l,p,h=this,d=s.getColumn("x",!0),u=s.getColumn("y",!0),m=t.length-1,c=i.length,g=u.length;return V(c-g)&&(o[0]!==d[0]&&u.unshift(0),o[c-1]!==d[g-1]&&u.push(0)),h.volumeDataArray=[],t.forEach(function(t){for(p=0,t.wholeVolumeData=0,t.positiveVolumeData=0,t.negativeVolumeData=0;p<c;p++){n=!1,r=!1,l=e?i[p][3]:i[p],a=p?e?i[p-1][3]:i[p-1]:l;let s=h.linkedParent;!h.options.compareToMain&&s.dataModify&&(l=s.dataModify.modifyValue(l),a=s.dataModify.modifyValue(a)),l<=t.start&&0===t.index&&(n=!0),l>=t.end&&t.index===m&&(r=!0),(l>t.start||n)&&(l<t.end||r)&&(t.wholeVolumeData+=u[p],a>l?t.negativeVolumeData+=u[p]:t.positiveVolumeData+=u[p])}h.volumeDataArray.push(t.wholeVolumeData)}),t}drawZones(e,t,s,o){let i=e.renderer,a=e.plotWidth,n=e.plotTop,r=this.zoneLinesSVG,l=[],p;s.forEach(function(s){p=t.toPixels(s)-n,l=l.concat(e.renderer.crispLine([["M",0,p],["L",a,p]],o.lineWidth))}),r?r.animate({d:l}):r=this.zoneLinesSVG=i.path(l).attr({"stroke-width":o.lineWidth,stroke:o.color,dashstyle:o.dashStyle,zIndex:this.group.zIndex+.1}).add(this.group)}}S.defaultOptions=D(d.defaultOptions,{params:{index:void 0,period:void 0,ranges:12,volumeSeriesID:"volume"},zoneLines:{enabled:!0,styles:{color:"#0A9AC9",dashStyle:"LongDash",lineWidth:1}},volumeDivision:{enabled:!0,styles:{positiveColor:"rgba(144, 237, 125, 0.8)",negativeColor:"rgba(244, 91, 91, 0.8)"}},animationLimit:1e3,enableMouseTracking:!1,pointPadding:0,zIndex:-1,crisp:!0,dataGrouping:{enabled:!1},dataLabels:{align:"left",allowOverlap:!0,enabled:!0,format:"P: {point.volumePos:.2f} | N: {point.volumeNeg:.2f}",padding:0,style:{fontSize:"0.5em"},verticalAlign:"top"}}),y(S.prototype,{nameBase:"Volume by Price",nameComponents:["ranges"],calculateOn:{chart:"render",xAxis:"afterSetExtremes"},pointClass:r,markerAttribs:p,drawGraph:p,getColumnMetrics:h.getColumnMetrics,crispCol:h.crispCol}),a().registerSeriesType("vbp",S);let P=o();export{P as default};