{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.2.0 (2025-04-07)\n * @module highcharts/indicators/roc\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Kacper Madej\n *\n * License: www.highcharts.com/license\n */\nimport * as __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__ from \"../highcharts.js\";\nimport * as __WEBPACK_EXTERNAL_MODULE__modules_stock_src_js_b3d80146__ from \"../modules/stock.js\";\n/******/ // The require scope\n/******/ var __webpack_require__ = {};\n/******/ \n/************************************************************************/\n/******/ /* webpack/runtime/compat get default export */\n/******/ (() => {\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = (module) => {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t() => (module['default']) :\n/******/ \t\t\t() => (module);\n/******/ \t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\treturn getter;\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/define property getters */\n/******/ (() => {\n/******/ \t// define getter functions for harmony exports\n/******/ \t__webpack_require__.d = (exports, definition) => {\n/******/ \t\tfor(var key in definition) {\n/******/ \t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t}\n/******/ \t\t}\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/hasOwnProperty shorthand */\n/******/ (() => {\n/******/ \t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ })();\n/******/ \n/************************************************************************/\n\n;// external [\"../highcharts.js\",\"default\"]\nconst external_highcharts_src_js_default_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"];\nvar external_highcharts_src_js_default_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_namespaceObject);\n;// external \"../modules/stock.js\"\nvar x = (y) => {\n\tvar x = {}; __webpack_require__.d(x,\n    \ty); return x\n    } \n    var y = (x) => (() => (x))\n    const stock_src_js_namespaceObject = x({  });\n;// external [\"../highcharts.js\",\"default\",\"SeriesRegistry\"]\nconst external_highcharts_src_js_default_SeriesRegistry_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].SeriesRegistry;\nvar external_highcharts_src_js_default_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_SeriesRegistry_namespaceObject);\n;// ./code/es-modules/Stock/Indicators/ROC/ROCIndicator.js\n/* *\n *\n *  (c) 2010-2025 Kacper Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sma: SMAIndicator } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { isArray, merge, extend } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n// Utils:\n/**\n *\n */\nfunction populateAverage(xVal, yVal, i, period, index) {\n    /* Calculated as:\n\n       (Closing Price [today] - Closing Price [n days ago]) /\n        Closing Price [n days ago] * 100\n\n       Return y as null when avoiding division by zero */\n    let nDaysAgoY, rocY;\n    if (index < 0) {\n        // Y data given as an array of values\n        nDaysAgoY = yVal[i - period];\n        rocY = nDaysAgoY ?\n            (yVal[i] - nDaysAgoY) / nDaysAgoY * 100 :\n            null;\n    }\n    else {\n        // Y data given as an array of arrays and the index should be used\n        nDaysAgoY = yVal[i - period][index];\n        rocY = nDaysAgoY ?\n            (yVal[i][index] - nDaysAgoY) / nDaysAgoY * 100 :\n            null;\n    }\n    return [xVal[i], rocY];\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The ROC series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.roc\n *\n * @augments Highcharts.Series\n */\nclass ROCIndicator extends SMAIndicator {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getValues(series, params) {\n        const period = params.period, xVal = series.xData, yVal = series.yData, yValLen = yVal ? yVal.length : 0, ROC = [], xData = [], yData = [];\n        let i, index = -1, ROCPoint;\n        // Period is used as a number of time periods ago, so we need more\n        // (at least 1 more) data than the period value\n        if (xVal.length <= period) {\n            return;\n        }\n        // Switch index for OHLC / Candlestick / Arearange\n        if (isArray(yVal[0])) {\n            index = params.index;\n        }\n        // I = period <-- skip first N-points\n        // Calculate value one-by-one for each period in visible data\n        for (i = period; i < yValLen; i++) {\n            ROCPoint = populateAverage(xVal, yVal, i, period, index);\n            ROC.push(ROCPoint);\n            xData.push(ROCPoint[0]);\n            yData.push(ROCPoint[1]);\n        }\n        return {\n            values: ROC,\n            xData: xData,\n            yData: yData\n        };\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\n/**\n * Rate of change indicator (ROC). The indicator value for each point\n * is defined as:\n *\n * `(C - Cn) / Cn * 100`\n *\n * where: `C` is the close value of the point of the same x in the\n * linked series and `Cn` is the close value of the point `n` periods\n * ago. `n` is set through [period](#plotOptions.roc.params.period).\n *\n * This series requires `linkedTo` option to be set.\n *\n * @sample stock/indicators/roc\n *         Rate of change indicator\n *\n * @extends      plotOptions.sma\n * @since        6.0.0\n * @product      highstock\n * @requires     stock/indicators/indicators\n * @requires     stock/indicators/roc\n * @optionparent plotOptions.roc\n */\nROCIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n    params: {\n        index: 3,\n        period: 9\n    }\n});\nextend(ROCIndicator.prototype, {\n    nameBase: 'Rate of Change'\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('roc', ROCIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const ROC_ROCIndicator = ((/* unused pure expression or super */ null && (ROCIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `ROC` series. If the [type](#series.wma.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * Rate of change indicator (ROC). The indicator value for each point\n * is defined as:\n *\n * `(C - Cn) / Cn * 100`\n *\n * where: `C` is the close value of the point of the same x in the\n * linked series and `Cn` is the close value of the point `n` periods\n * ago. `n` is set through [period](#series.roc.params.period).\n *\n * This series requires `linkedTo` option to be set.\n *\n * @extends   series,plotOptions.roc\n * @since     6.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/roc\n * @apioption series.roc\n */\n''; // To include the above in the js output\n\n;// ./code/es-modules/masters/indicators/roc.js\n\n\n\n\n\n/* harmony default export */ const roc_src = ((external_highcharts_src_js_default_default()));\n\nexport { roc_src as default };\n"], "names": ["__WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__", "__webpack_require__", "n", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "external_highcharts_src_js_default_namespaceObject", "external_highcharts_src_js_default_default", "external_highcharts_src_js_default_SeriesRegistry_namespaceObject", "SeriesRegistry", "external_highcharts_src_js_default_SeriesRegistry_default", "sma", "SMAIndicator", "seriesTypes", "isArray", "merge", "extend", "ROCIndicator", "getV<PERSON>ues", "series", "params", "period", "xVal", "xData", "yVal", "yData", "yValLen", "length", "ROC", "i", "index", "ROCPoint", "populateAverage", "nDaysAgoY", "rocY", "push", "values", "defaultOptions", "nameBase", "registerSeriesType", "roc_src", "default"], "mappings": "AAYA,UAAYA,MAA6D,sBAAuB,AAChG,OAA4E,yBAA0B,CAE7F,IAAIC,EAAsB,CAAC,CAM1BA,CAAAA,EAAoBC,CAAC,CAAG,AAACC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,UAAU,CACvC,IAAOF,EAAO,OAAU,CACxB,IAAOA,EAER,OADAF,EAAoBK,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAH,EAAoBK,CAAC,CAAG,CAACE,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXR,EAAoBU,CAAC,CAACF,EAAYC,IAAQ,CAACT,EAAoBU,CAAC,CAACH,EAASE,IAC5EE,OAAOC,cAAc,CAACL,EAASE,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAT,EAAoBU,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAM5F,IAAMI,EAAqDrB,EAAwD,OAAU,CAC7H,IAAIsB,EAA0DrB,EAAoBC,CAAC,CAACmB,GAGvEpB,EAAoBK,CAAC,CAAzB,CAAC,EAIiC,CAAG,GAE9C,IAAMiB,EAAoEvB,EAAwD,OAAU,CAACwB,cAAc,CAC3J,IAAIC,EAAyExB,EAAoBC,CAAC,CAACqB,GAanG,GAAM,CAAEG,IAAKC,CAAY,CAAE,CAAG,AAACF,IAA6DG,WAAW,CAEjG,CAAEC,QAAAA,CAAO,CAAEC,MAAAA,CAAK,CAAEC,OAAAA,CAAM,CAAE,CAAIT,GAgDpC,OAAMU,UAAqBL,EAMvBM,UAAUC,CAAM,CAAEC,CAAM,CAAE,CACtB,IAAMC,EAASD,EAAOC,MAAM,CAAEC,EAAOH,EAAOI,KAAK,CAAEC,EAAOL,EAAOM,KAAK,CAAEC,EAAUF,EAAOA,EAAKG,MAAM,CAAG,EAAGC,EAAM,EAAE,CAAEL,EAAQ,EAAE,CAAEE,EAAQ,EAAE,CACtII,EAAGC,EAAQ,GAAIC,EAGnB,IAAIT,CAAAA,EAAKK,MAAM,EAAIN,CAAK,GASxB,IALIP,EAAQU,CAAI,CAAC,EAAE,GACfM,CAAAA,EAAQV,EAAOU,KAAK,AAAD,EAIlBD,EAAIR,EAAQQ,EAAIH,EAASG,IAC1BE,EAAWC,AA3DvB,SAAyBV,CAAI,CAAEE,CAAI,CAAEK,CAAC,CAAER,CAAM,CAAES,CAAK,EAOjD,IAAIG,EAAWC,EAef,OAXIA,EAHAJ,EAAQ,EAGDG,AADPA,CAAAA,EAAYT,CAAI,CAACK,EAAIR,EAAO,AAAD,EAEvB,AAACG,CAAAA,CAAI,CAACK,EAAE,CAAGI,CAAQ,EAAKA,EAAY,IACpC,KAKGA,AADPA,CAAAA,EAAYT,CAAI,CAACK,EAAIR,EAAO,CAACS,EAAM,AAAD,EAE9B,AAACN,CAAAA,CAAI,CAACK,EAAE,CAACC,EAAM,CAAGG,CAAQ,EAAKA,EAAY,IAC3C,KAED,CAACX,CAAI,CAACO,EAAE,CAAEK,EAAK,AAC1B,EAoCuCZ,EAAME,EAAMK,EAAGR,EAAQS,GAClDF,EAAIO,IAAI,CAACJ,GACTR,EAAMY,IAAI,CAACJ,CAAQ,CAAC,EAAE,EACtBN,EAAMU,IAAI,CAACJ,CAAQ,CAAC,EAAE,EAE1B,MAAO,CACHK,OAAQR,EACRL,MAAOA,EACPE,MAAOA,CACX,EACJ,CACJ,CA4BAR,EAAaoB,cAAc,CAAGtB,EAAMH,EAAayB,cAAc,CAAE,CAC7DjB,OAAQ,CACJU,MAAO,EACPT,OAAQ,CACZ,CACJ,GACAL,EAAOC,EAAad,SAAS,CAAE,CAC3BmC,SAAU,gBACd,GACA5B,IAA4D6B,kBAAkB,CAAC,MAAOtB,GA2CzD,IAAMuB,EAAYjC,WAEtCiC,KAAWC,OAAO"}