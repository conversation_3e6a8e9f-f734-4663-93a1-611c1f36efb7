import*as t from"../highcharts.js";import"../modules/stock.js";var e={};e.n=t=>{var a=t&&t.__esModule?()=>t.default:()=>t;return e.d(a,{a:a}),a},e.d=(t,a)=>{for(var l in a)e.o(a,l)&&!e.o(t,l)&&Object.defineProperty(t,l,{enumerable:!0,get:a[l]})},e.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);let a=t.default;var l=e.n(a);e.d({},{});let o=t.default.SeriesRegistry;var n=e.n(o);let s=n().seriesTypes.sma.prototype.pointClass;function r(t,e){let a=t.series.pointArrayMap,l,o=a.length;for(n().seriesTypes.sma.prototype.pointClass.prototype[e].call(t);o--;)t[l="dataLabel"+a[o]]&&t[l].element&&t[l].destroy(),t[l]=null}let{sma:i}=n().seriesTypes,{merge:p,extend:d,defined:u,isArray:c}=l();class h extends i{toYData(t){return[t.P]}translate(){let t=this;super.translate.apply(t),t.points.forEach(function(e){t.pointArrayMap.forEach(function(a){u(e[a])&&(e["plot"+a]=t.yAxis.toPixels(e[a],!0))})}),t.plotEndPoint=t.xAxis.toPixels(t.endPoint,!0)}getGraphPath(t){let e=this,a=[[],[],[],[],[],[],[],[],[]],l=e.pointArrayMap.length,o=e.plotEndPoint,n=[],s,r,i=t.length,p;for(;i--;){for(p=0,r=t[i];p<l;p++)u(r[s=e.pointArrayMap[p]])&&a[p].push({plotX:r.plotX,plotY:r["plot"+s],isNull:!1},{plotX:o,plotY:r["plot"+s],isNull:!1},{plotX:o,plotY:null,isNull:!0});o=r.plotX}return a.forEach(t=>{n=n.concat(super.getGraphPath.call(e,t))}),n}drawDataLabels(){let t,e,a,l,o=this,n=o.pointArrayMap;o.options.dataLabels.enabled&&(e=o.points.length,n.concat([!1]).forEach((s,r)=>{for(l=e;l--;)a=o.points[l],s?(a.y=a[s],a.pivotLine=s,a.plotY=a["plot"+s],t=a["dataLabel"+s],r&&(a["dataLabel"+n[r-1]]=a.dataLabel),a.dataLabels||(a.dataLabels=[]),a.dataLabels[0]=a.dataLabel=t=t&&t.element?t:null):a["dataLabel"+n[r-1]]=a.dataLabel;super.drawDataLabels.call(o)}))}getValues(t,e){let a,l,o,n,s,r,i,p=e.period,d=t.xData,u=t.yData,h=u?u.length:0,y=this[e.algorithm+"Placement"],m=[],f=[],g=[];if(!(d.length<p)&&c(u[0])&&4===u[0].length){for(i=p+1;i<=h+p;i+=p)o=d.slice(i-p-1,i),n=u.slice(i-p-1,i),l=o.length,a=o[l-1],r=y(this.getPivotAndHLC(n)),s=m.push([a].concat(r)),f.push(a),g.push(m[s-1].slice(1));return this.endPoint=o[0]+(a-o[0])/l*p,{values:m,xData:f,yData:g}}}getPivotAndHLC(t){let e=t[t.length-1][3],a=-1/0,l=1/0;return t.forEach(function(t){a=Math.max(a,t[1]),l=Math.min(l,t[2])}),[(a+l+e)/3,a,l,e]}standardPlacement(t){let e=t[1]-t[2];return[null,null,t[0]+e,2*t[0]-t[2],t[0],2*t[0]-t[1],t[0]-e,null,null]}camarillaPlacement(t){let e=t[1]-t[2];return[t[3]+1.5*e,t[3]+1.25*e,t[3]+1.1666*e,t[3]+1.0833*e,t[0],t[3]-1.0833*e,t[3]-1.1666*e,t[3]-1.25*e,t[3]-1.5*e]}fibonacciPlacement(t){let e=t[1]-t[2];return[null,t[0]+e,t[0]+.618*e,t[0]+.382*e,t[0],t[0]-.382*e,t[0]-.618*e,t[0]-e,null]}}h.defaultOptions=p(i.defaultOptions,{params:{index:void 0,period:28,algorithm:"standard"},marker:{enabled:!1},enableMouseTracking:!1,dataLabels:{enabled:!0,format:"{point.pivotLine}"},dataGrouping:{approximation:"averages"}}),d(h.prototype,{nameBase:"Pivot Points",pointArrayMap:["R4","R3","R2","R1","P","S1","S2","S3","S4"],pointValKey:"P",pointClass:class extends s{destroyElements(){r(this,"destroyElements")}destroy(){r(this,"destroyElements")}}}),n().registerSeriesType("pivotpoints",h);let y=l();export{y as default};