import*as t from"../highcharts.js";import"./annotations.js";var i,s,e,o,n={};n.n=t=>{var i=t&&t.__esModule?()=>t.default:()=>t;return n.d(i,{a:i}),i},n.d=(t,i)=>{for(var s in i)n.o(i,s)&&!n.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:i[s]})},n.o=(t,i)=>Object.prototype.hasOwnProperty.call(t,i);let a=t.default;var r=n.n(a);n.d({},{});let{addEvent:h,erase:l,find:p,fireEvent:c,pick:d,wrap:u}=r();function x(t,i){let s=this.initAnnotation(t);return this.options.annotations.push(s.options),d(i,!0)&&(s.redraw(),s.graphic.attr({opacity:1})),s}function y(){let t=this;t.plotBoxClip=this.renderer.clipRect(this.plotBox),t.controlPointsGroup=t.renderer.g("control-points").attr({zIndex:99}).clip(t.plotBoxClip).add(),t.options.annotations.forEach((i,s)=>{if(!t.annotations.some(t=>t.options===i)){let e=t.initAnnotation(i);t.options.annotations[s]=e.options}}),t.drawAnnotations(),h(t,"redraw",t.drawAnnotations),h(t,"destroy",function(){t.plotBoxClip.destroy(),t.controlPointsGroup.destroy()}),h(t,"exportData",function(i){let s=t.annotations,e=(this.options.exporting&&this.options.exporting.csv||{}).columnHeaderFormatter,o=!i.dataRows[1].xValues,n=t.options.lang&&t.options.lang.exportData&&t.options.lang.exportData.annotationHeader,a=i.dataRows[0].length,r=t.options.exporting&&t.options.exporting.csv&&t.options.exporting.csv.annotations&&t.options.exporting.csv.annotations.itemDelimiter,h=t.options.exporting&&t.options.exporting.csv&&t.options.exporting.csv.annotations&&t.options.exporting.csv.annotations.join;s.forEach(t=>{t.options.labelOptions&&t.options.labelOptions.includeInDataExport&&t.labels.forEach(t=>{if(t.options.text){let s=t.options.text;t.points.forEach(t=>{let e=t.x,o=t.series.xAxis?t.series.xAxis.index:-1,n=!1;if(-1===o){let t=i.dataRows[0].length,a=Array(t);for(let i=0;i<t;++i)a[i]="";a.push(s),a.xValues=[],a.xValues[o]=e,i.dataRows.push(a),n=!0}if(n||i.dataRows.forEach(t=>{!n&&t.xValues&&void 0!==o&&e===t.xValues[o]&&(h&&t.length>a?t[t.length-1]+=r+s:t.push(s),n=!0)}),!n){let t=i.dataRows[0].length,n=Array(t);for(let i=0;i<t;++i)n[i]="";n[0]=e,n.push(s),n.xValues=[],void 0!==o&&(n.xValues[o]=e),i.dataRows.push(n)}})}})});let l=0;i.dataRows.forEach(t=>{l=Math.max(l,t.length)});let p=l-i.dataRows[0].length;for(let t=0;t<p;t++){let s=function(t){let i;return e&&!1!==(i=e(t))?i:(i=n+" "+t,o)?{columnTitle:i,topLevelColumnTitle:i}:i}(t+1);o?(i.dataRows[0].push(s.topLevelColumnTitle),i.dataRows[1].push(s.columnTitle)):i.dataRows[0].push(s)}})}function g(){this.plotBoxClip.attr(this.plotBox),this.annotations.forEach(t=>{t.redraw(),t.graphic.animate({opacity:1},t.animationConfig)})}function f(t){let i=this.annotations,s="annotations"===t.coll?t:p(i,function(i){return i.options.id===t});s&&(c(s,"remove"),l(this.options.annotations,s.options),l(i,s),s.destroy())}function m(){this.annotations=[],this.options.annotations||(this.options.annotations=[])}function b(t){this.chart.hasDraggedAnnotation||t.apply(this,Array.prototype.slice.call(arguments,1))}(s||(s={})).compose=function(t,i,s){let e=i.prototype;if(!e.addAnnotation){let o=s.prototype;h(i,"afterInit",m),e.addAnnotation=x,e.callbacks.push(y),e.collectionsWithInit.annotations=[x],e.collectionsWithUpdate.push("annotations"),e.drawAnnotations=g,e.removeAnnotation=f,e.initAnnotation=function(i){let s=new(t.types[i.type]||t)(this,i);return this.annotations.push(s),s},u(o,"onContainerMouseDown",b)}};let v=s,{defined:A}=r(),{doc:P,isTouchDevice:M}=r(),{addEvent:O,fireEvent:w,objectEach:k,pick:E,removeEvent:C}=r(),T=class{addEvents(){let t=this,i=function(i){O(i,M?"touchstart":"mousedown",i=>{t.onMouseDown(i)},{passive:!1})};if(i(this.graphic.element),(t.labels||[]).forEach(t=>{t.options.useHTML&&t.graphic.text&&!t.graphic.text.foreignObject&&i(t.graphic.text.element)}),k(t.options.events,(i,s)=>{let e=function(e){"click"===s&&t.cancelClick||i.call(t,t.chart.pointer?.normalize(e),t.target)};-1===(t.nonDOMEvents||[]).indexOf(s)?(O(t.graphic.element,s,e,{passive:!1}),t.graphic.div&&O(t.graphic.div,s,e,{passive:!1})):O(t,s,e,{passive:!1})}),t.options.draggable&&(O(t,"drag",t.onDrag),!t.graphic.renderer.styledMode)){let i={cursor:{x:"ew-resize",y:"ns-resize",xy:"move"}[t.options.draggable]};t.graphic.css(i),(t.labels||[]).forEach(t=>{t.options.useHTML&&t.graphic.text&&!t.graphic.text.foreignObject&&t.graphic.text.css(i)})}t.isUpdating||w(t,"add")}destroy(){this.removeDocEvents(),C(this),this.hcEvents=null}mouseMoveToRadians(t,i,s){let e=t.prevChartY-s,o=t.prevChartX-i,n=t.chartY-s,a=t.chartX-i,r;return this.chart.inverted&&(r=o,o=e,e=r,r=a,a=n,n=r),Math.atan2(n,a)-Math.atan2(e,o)}mouseMoveToScale(t,i,s){let e=t.prevChartX-i,o=t.prevChartY-s,n=t.chartX-i,a=t.chartY-s,r=(n||1)/(e||1),h=(a||1)/(o||1);if(this.chart.inverted){let t=h;h=r,r=t}return{x:r,y:h}}mouseMoveToTranslation(t){let i=t.chartX-t.prevChartX,s=t.chartY-t.prevChartY,e;return this.chart.inverted&&(e=s,s=i,i=e),{x:i,y:s}}onDrag(t){if(this.chart.isInsidePlot(t.chartX-this.chart.plotLeft,t.chartY-this.chart.plotTop,{visiblePlotOnly:!0})){let i=this.mouseMoveToTranslation(t);"x"===this.options.draggable&&(i.y=0),"y"===this.options.draggable&&(i.x=0),this.points.length?this.translate(i.x,i.y):(this.shapes.forEach(t=>t.translate(i.x,i.y)),this.labels.forEach(t=>t.translate(i.x,i.y))),this.redraw(!1)}}onMouseDown(t){if(t.preventDefault&&t.preventDefault(),2===t.button)return;let i=this,s=i.chart.pointer,e=t?.sourceCapabilities?.firesTouchEvents||!1,o=(t=s?.normalize(t)||t).chartX,n=t.chartY;i.cancelClick=!1,i.chart.hasDraggedAnnotation=!0,i.removeDrag=O(P,M||e?"touchmove":"mousemove",function(t){i.hasDragged=!0,(t=s?.normalize(t)||t).prevChartX=o,t.prevChartY=n,w(i,"drag",t),o=t.chartX,n=t.chartY},M||e?{passive:!1}:void 0),i.removeMouseUp=O(P,M||e?"touchend":"mouseup",function(){let t=E(i.target&&i.target.annotation,i.target);t&&(t.cancelClick=i.hasDragged),i.cancelClick=i.hasDragged,i.chart.hasDraggedAnnotation=!1,i.hasDragged&&w(E(t,i),"afterUpdate"),i.hasDragged=!1,i.onMouseUp()},M||e?{passive:!1}:void 0)}onMouseUp(){this.removeDocEvents()}removeDocEvents(){this.removeDrag&&(this.removeDrag=this.removeDrag()),this.removeMouseUp&&(this.removeMouseUp=this.removeMouseUp())}},{merge:X,pick:Y}=r(),L=class extends T{constructor(t,i,s,e){super(),this.nonDOMEvents=["drag"],this.chart=t,this.target=i,this.options=s,this.index=Y(s.index,e)}destroy(){super.destroy(),this.graphic&&(this.graphic=this.graphic.destroy()),this.chart=null,this.target=null,this.options=null}redraw(t){this.graphic[t?"animate":"attr"](this.options.positioner.call(this,this.target))}render(){let t=this.chart,i=this.options;this.graphic=t.renderer.symbol(i.symbol,0,0,i.width,i.height).add(t.controlPointsGroup).css(i.style),this.setVisibility(i.visible),this.addEvents()}setVisibility(t){this.graphic[t?"show":"hide"](),this.options.visible=t}update(t){let i=this.chart,s=this.target,e=this.index,o=X(!0,this.options,t);this.destroy(),this.constructor(i,s,o,e),this.render(i.controlPointsGroup),this.redraw()}},S=t.default.SeriesRegistry;var N=n.n(S);let{series:{prototype:B}}=N(),{defined:I,fireEvent:R}=r();class D{static fromPoint(t){return new D(t.series.chart,null,{x:t.x,y:t.y,xAxis:t.series.xAxis,yAxis:t.series.yAxis})}static pointToPixels(t,i){let s=t.series,e=s.chart,o=t.plotX||0,n=t.plotY||0,a;return e.inverted&&(t.mock?(o=t.plotY,n=t.plotX):(o=e.plotWidth-(t.plotY||0),n=e.plotHeight-(t.plotX||0))),s&&!i&&(o+=(a=s.getPlotBox()).translateX,n+=a.translateY),{x:o,y:n}}static pointToOptions(t){return{x:t.x,y:t.y,xAxis:t.series.xAxis,yAxis:t.series.yAxis}}constructor(t,i,s){this.mock=!0,this.point=this,this.series={visible:!0,chart:t,getPlotBox:B.getPlotBox},this.target=i||null,this.options=s,this.applyOptions(this.getOptions())}applyOptions(t){this.command=t.command,this.setAxis(t,"x"),this.setAxis(t,"y"),this.refresh()}getOptions(){return this.hasDynamicOptions()?this.options(this.target):this.options}hasDynamicOptions(){return"function"==typeof this.options}isInsidePlot(){let t=this.plotX,i=this.plotY,s=this.series.xAxis,e=this.series.yAxis,o={x:t,y:i,isInsidePlot:!0,options:{}};return s&&(o.isInsidePlot=I(t)&&t>=0&&t<=s.len),e&&(o.isInsidePlot=o.isInsidePlot&&I(i)&&i>=0&&i<=e.len),R(this.series.chart,"afterIsInsidePlot",o),o.isInsidePlot}refresh(){let t=this.series,i=t.xAxis,s=t.yAxis,e=this.getOptions();i?(this.x=e.x,this.plotX=i.toPixels(e.x,!0)):(this.x=void 0,this.plotX=e.x),s?(this.y=e.y,this.plotY=s.toPixels(e.y,!0)):(this.y=null,this.plotY=e.y),this.isInside=this.isInsidePlot()}refreshOptions(){let t=this.series,i=t.xAxis,s=t.yAxis;this.x=this.options.x=i?this.options.x=i.toValue(this.plotX,!0):this.plotX,this.y=this.options.y=s?s.toValue(this.plotY,!0):this.plotY}rotate(t,i,s){if(!this.hasDynamicOptions()){let e=Math.cos(s),o=Math.sin(s),n=this.plotX-t,a=this.plotY-i;this.plotX=n*e-a*o+t,this.plotY=n*o+a*e+i,this.refreshOptions()}}scale(t,i,s,e){if(!this.hasDynamicOptions()){let o=this.plotX*s,n=this.plotY*e;this.plotX=(1-s)*t+o,this.plotY=(1-e)*i+n,this.refreshOptions()}}setAxis(t,i){let s=i+"Axis",e=t[s],o=this.series.chart;this.series[s]="object"==typeof e?e:I(e)?o[s][e]||o.get(e):null}toAnchor(){let t=[this.plotX,this.plotY,0,0];return this.series.chart.inverted&&(t[0]=this.plotY,t[1]=this.plotX),t}translate(t,i,s,e){this.hasDynamicOptions()||(this.plotX+=s,this.plotY+=e,this.refreshOptions())}}!function(t){function i(){let t=this.controlPoints,i=this.options.controlPoints||[];i.forEach((s,e)=>{let o=r().merge(this.options.controlPointOptions,s);o.index||(o.index=e),i[e]=o,t.push(new L(this.chart,this,o))})}function s(t){let i=t.series.getPlotBox(),s=t.series.chart,e=t.mock?t.toAnchor():s.tooltip&&s.tooltip.getAnchor.call({chart:t.series.chart},t)||[0,0,0,0],o={x:e[0]+(this.options.x||0),y:e[1]+(this.options.y||0),height:e[2]||0,width:e[3]||0};return{relativePosition:o,absolutePosition:r().merge(o,{x:o.x+(t.mock?i.translateX:s.plotLeft),y:o.y+(t.mock?i.translateY:s.plotTop)})}}function e(){this.controlPoints.forEach(t=>t.destroy()),this.chart=null,this.controlPoints=null,this.points=null,this.options=null,this.annotation&&(this.annotation=null)}function o(){let t=this.options;return t.points||t.point&&r().splat(t.point)}function n(){let t,i,s=this.getPointsOptions(),e=this.points,o=s&&s.length||0;for(t=0;t<o;t++){if(!(i=this.point(s[t],e[t]))){e.length=0;return}i.mock&&i.refresh(),e[t]=i}return e}function a(t,i){if(t&&t.series)return t;if(!i||null===i.series){if(r().isObject(t))i=new D(this.chart,this,t);else if(r().isString(t))i=this.chart.get(t)||null;else if("function"==typeof t){let s=t.call(i,this);i=s.series?s:new D(this.chart,this,t)}}return i}function h(t){this.controlPoints.forEach(i=>i.redraw(t))}function l(){this.controlPoints.forEach(t=>t.render())}function p(t,i,s,e,o){if(this.chart.inverted){let t=i;i=s,s=t}this.points.forEach((n,a)=>this.transformPoint(t,i,s,e,o,a),this)}function c(t,i,s,e,o,n){let a=this.points[n];a.mock||(a=this.points[n]=D.fromPoint(a)),a[t](i,s,e,o)}function d(t,i){this.transform("translate",null,null,t,i)}function u(t,i,s){this.transformPoint("translate",null,null,t,i,s)}t.compose=function(t){let x=t.prototype;x.addControlPoints||r().merge(!0,x,{addControlPoints:i,anchor:s,destroyControlTarget:e,getPointsOptions:o,linkPoints:n,point:a,redrawControlPoints:h,renderControlPoints:l,transform:p,transformPoint:c,translate:d,translatePoint:u})}}(e||(e={}));let F=e,{merge:z}=r();class W{constructor(t,i,s,e){this.annotation=t,this.chart=t.chart,this.collection="label"===e?"labels":"shapes",this.controlPoints=[],this.options=i,this.points=[],this.index=s,this.itemType=e,this.init(t,i,s)}attr(...t){this.graphic.attr.apply(this.graphic,arguments)}attrsFromOptions(t){let i,s,e=this.constructor.attrsMap,o={},n=this.chart.styledMode;for(i in t)s=e[i],void 0===e[i]||n&&-1!==["fill","stroke","stroke-width"].indexOf(s)||(o[s]=t[i]);return o}destroy(){this.graphic&&(this.graphic=this.graphic.destroy()),this.tracker&&(this.tracker=this.tracker.destroy()),this.destroyControlTarget()}init(t,i,s){this.annotation=t,this.chart=t.chart,this.options=i,this.points=[],this.controlPoints=[],this.index=s,this.linkPoints(),this.addControlPoints()}redraw(t){this.redrawControlPoints(t)}render(t){this.options.className&&this.graphic&&this.graphic.addClass(this.options.className),this.renderControlPoints()}rotate(t,i,s){this.transform("rotate",t,i,s)}scale(t,i,s,e){this.transform("scale",t,i,s,e)}setControlPointsVisibility(t){this.controlPoints.forEach(i=>{i.setVisibility(t)})}shouldBeDrawn(){return!!this.points.length}translateShape(t,i,s){let e=this.annotation.chart,o=this.annotation.userOptions,n=e.annotations.indexOf(this.annotation),a=e.options.annotations[n];this.translatePoint(t,i,0),s&&this.translatePoint(t,i,1),a[this.collection][this.index].point=this.options.point,o[this.collection][this.index].point=this.options.point}update(t){let i=this.annotation,s=z(!0,this.options,t),e=this.graphic.parentGroup,o=this.constructor;this.destroy(),z(!0,this,new o(i,s,this.index,this.itemType)),this.render(e),this.redraw()}}F.compose(W);let V=W,{defaultMarkers:j}={defaultMarkers:{arrow:{tagName:"marker",attributes:{id:"arrow",refY:5,refX:9,markerWidth:10,markerHeight:10},children:[{tagName:"path",attributes:{d:"M 0 0 L 10 5 L 0 10 Z","stroke-width":0}}]},"reverse-arrow":{tagName:"marker",attributes:{id:"reverse-arrow",refY:5,refX:1,markerWidth:10,markerHeight:10},children:[{tagName:"path",attributes:{d:"M 0 5 L 10 0 L 10 10 Z","stroke-width":0}}]}}},{addEvent:H,defined:U,extend:q,merge:G,uniqueKey:Z}=r(),K=J("marker-end"),_=J("marker-start"),$="rgba(192,192,192,"+(r().svg?1e-4:.002)+")";function J(t){return function(i){this.attr(t,"url(#"+i+")")}}function Q(){this.options.defs=G(j,this.options.defs||{})}function tt(t,i){let s={attributes:{id:t}},e={stroke:i.color||"none",fill:i.color||"rgba(0, 0, 0, 0.75)"};s.children=i.children&&i.children.map(function(t){return G(e,t)});let o=G(!0,{attributes:{markerWidth:20,markerHeight:20,refX:0,refY:0,orient:"auto"}},i,s),n=this.definition(o);return n.id=t,n}class ti extends V{static compose(t,i){let s=i.prototype;s.addMarker||(H(t,"afterGetContainer",Q),s.addMarker=tt)}constructor(t,i,s){super(t,i,s,"shape"),this.type="path"}toD(){let t=this.options.d;if(t)return"function"==typeof t?t.call(this):t;let i=this.points,s=i.length,e=[],o=s,n=i[0],a=o&&this.anchor(n).absolutePosition,r=0,h;if(a)for(e.push(["M",a.x,a.y]);++r<s&&o;)h=(n=i[r]).command||"L",a=this.anchor(n).absolutePosition,"M"===h?e.push([h,a.x,a.y]):"L"===h?e.push([h,a.x,a.y]):"Z"===h&&e.push([h]),o=n.series.visible;return o&&this.graphic?this.chart.renderer.crispLine(e,this.graphic.strokeWidth()):null}shouldBeDrawn(){return super.shouldBeDrawn()||!!this.options.d}render(t){let i=this.options,s=this.attrsFromOptions(i);this.graphic=this.annotation.chart.renderer.path([["M",0,0]]).attr(s).add(t),this.tracker=this.annotation.chart.renderer.path([["M",0,0]]).addClass("highcharts-tracker-line").attr({zIndex:2}).add(t),this.annotation.chart.styledMode||this.tracker.attr({"stroke-linejoin":"round",stroke:$,fill:$,"stroke-width":this.graphic.strokeWidth()+2*i.snap}),super.render(),q(this.graphic,{markerStartSetter:_,markerEndSetter:K}),this.setMarkers(this)}redraw(t){if(this.graphic){let i=this.toD(),s=t?"animate":"attr";i?(this.graphic[s]({d:i}),this.tracker[s]({d:i})):(this.graphic.attr({d:"M 0 -9000000000"}),this.tracker.attr({d:"M 0 -9000000000"})),this.graphic.placed=this.tracker.placed=!!i}super.redraw(t)}setMarkers(t){let i=t.options,s=t.chart,e=s.options.defs,o=i.fill,n=U(o)&&"none"!==o?o:i.stroke;["markerStart","markerEnd"].forEach(function(o){let a,r,h,l,p=i[o];if(p){for(h in e)if((p===((a=e[h]).attributes&&a.attributes.id)||p===a.id)&&"marker"===a.tagName){r=a;break}r&&(l=t[o]=s.renderer.addMarker((i.id||Z())+"-"+p,G(r,{color:n})),t.attr(o,l.getAttribute("id")))}})}}ti.attrsMap={dashStyle:"dashstyle",strokeWidth:"stroke-width",stroke:"stroke",fill:"fill",zIndex:"zIndex"};let{merge:ts}=r();class te extends V{constructor(t,i,s){super(t,i,s,"shape"),this.type="rect",this.translate=super.translateShape}render(t){let i=this.attrsFromOptions(this.options);this.graphic=this.annotation.chart.renderer.rect(0,-9e9,0,0).attr(i).add(t),super.render()}redraw(t){if(this.graphic){let i=this.anchor(this.points[0]).absolutePosition;i?this.graphic[t?"animate":"attr"]({x:i.x,y:i.y,width:this.options.width,height:this.options.height}):this.attr({x:0,y:-9e9}),this.graphic.placed=!!i}super.redraw(t)}}te.attrsMap=ts(ti.attrsMap,{width:"width",height:"height"});let{merge:to}=r();class tn extends V{constructor(t,i,s){super(t,i,s,"shape"),this.type="circle",this.translate=super.translateShape}redraw(t){if(this.graphic){let i=this.anchor(this.points[0]).absolutePosition;i?this.graphic[t?"animate":"attr"]({x:i.x,y:i.y,r:this.options.r}):this.graphic.attr({x:0,y:-9e9}),this.graphic.placed=!!i}super.redraw.call(this,t)}render(t){let i=this.attrsFromOptions(this.options);this.graphic=this.annotation.chart.renderer.circle(0,-9e9,0).attr(i).add(t),super.render()}setRadius(t){this.options.r=t}}tn.attrsMap=to(ti.attrsMap,{r:"r"});let{merge:ta,defined:tr}=r();class th extends V{constructor(t,i,s){super(t,i,s,"shape"),this.type="ellipse"}init(t,i,s){tr(i.yAxis)&&i.points.forEach(t=>{t.yAxis=i.yAxis}),tr(i.xAxis)&&i.points.forEach(t=>{t.xAxis=i.xAxis}),super.init(t,i,s)}render(t){this.graphic=this.annotation.chart.renderer.createElement("ellipse").attr(this.attrsFromOptions(this.options)).add(t),super.render()}translate(t,i){super.translateShape(t,i,!0)}getDistanceFromLine(t,i,s,e){return Math.abs((i.y-t.y)*s-(i.x-t.x)*e+i.x*t.y-i.y*t.x)/Math.sqrt((i.y-t.y)*(i.y-t.y)+(i.x-t.x)*(i.x-t.x))}getAttrs(t,i){let s=t.x,e=t.y,o=i.x,n=i.y,a=(s+o)/2,r=Math.sqrt((s-o)*(s-o)/4+(e-n)*(e-n)/4),h=180*Math.atan((n-e)/(o-s))/Math.PI;return a<s&&(h+=180),{cx:a,cy:(e+n)/2,rx:r,ry:this.getRY(),angle:h}}getRY(){let t=this.getYAxis();return tr(t)?Math.abs(t.toPixels(this.options.ry)-t.toPixels(0)):this.options.ry}getYAxis(){let t=this.options.yAxis;return this.chart.yAxis[t]}getAbsolutePosition(t){return this.anchor(t).absolutePosition}redraw(t){if(this.graphic){let i=this.getAbsolutePosition(this.points[0]),s=this.getAbsolutePosition(this.points[1]),e=this.getAttrs(i,s);i?this.graphic[t?"animate":"attr"]({cx:e.cx,cy:e.cy,rx:e.rx,ry:e.ry,rotation:e.angle,rotationOriginX:e.cx,rotationOriginY:e.cy}):this.graphic.attr({x:0,y:-9e9}),this.graphic.placed=!!i}super.redraw(t)}setYRadius(t){let i=this.annotation.userOptions.shapes;this.options.ry=t,i&&i[0]&&(i[0].ry=t,i[0].ry=t)}}th.attrsMap=ta(ti.attrsMap,{ry:"ry"});let tl=t.default.Templating,{format:tp}=n.n(tl)(),{extend:tc,getAlignFactor:td,isNumber:tu,pick:tx}=r();function ty(t,i,s,e,o){let n=o&&o.anchorX,a=o&&o.anchorY,r,h,l=s/2;return tu(n)&&tu(a)&&(r=[["M",n,a]],(h=i-a)<0&&(h=-e-h),h<s&&(l=n<t+s/2?h:s-h),a>i+e?r.push(["L",t+l,i+e]):a<i?r.push(["L",t+l,i]):n<t?r.push(["L",t,i+e/2]):n>t+s&&r.push(["L",t+s,i+e/2])),r||[]}class tg extends V{static alignedPosition(t,i){return{x:Math.round((i.x||0)+(t.x||0)+(i.width-(t.width||0))*td(t.align)),y:Math.round((i.y||0)+(t.y||0)+(i.height-(t.height||0))*td(t.verticalAlign))}}static compose(t){t.prototype.symbols.connector=ty}static justifiedOptions(t,i,s,e){let o,n=s.align,a=s.verticalAlign,r=i.box?0:i.padding||0,h=i.getBBox(),l={align:n,verticalAlign:a,x:s.x,y:s.y,width:i.width,height:i.height},p=(e.x||0)-t.plotLeft,c=(e.y||0)-t.plotTop;return(o=p+r)<0&&("right"===n?l.align="left":l.x=(l.x||0)-o),(o=p+h.width-r)>t.plotWidth&&("left"===n?l.align="right":l.x=(l.x||0)+t.plotWidth-o),(o=c+r)<0&&("bottom"===a?l.verticalAlign="top":l.y=(l.y||0)-o),(o=c+h.height-r)>t.plotHeight&&("top"===a?l.verticalAlign="bottom":l.y=(l.y||0)+t.plotHeight-o),l}constructor(t,i,s){super(t,i,s,"label")}translatePoint(t,i){super.translatePoint(t,i,0)}translate(t,i){let s=this.annotation.chart,e=this.annotation.userOptions,o=s.annotations.indexOf(this.annotation),n=s.options.annotations[o];if(s.inverted){let s=t;t=i,i=s}this.options.x+=t,this.options.y+=i,n[this.collection][this.index].x=this.options.x,n[this.collection][this.index].y=this.options.y,e[this.collection][this.index].x=this.options.x,e[this.collection][this.index].y=this.options.y}render(t){let i=this.options,s=this.attrsFromOptions(i),e=i.style;this.graphic=this.annotation.chart.renderer.label("",0,-9999,i.shape,null,null,i.useHTML,null,"annotation-label").attr(s).add(t),this.annotation.chart.styledMode||("contrast"===e.color&&(e.color=this.annotation.chart.renderer.getContrast(tg.shapesWithoutBackground.indexOf(i.shape)>-1?"#FFFFFF":i.backgroundColor)),this.graphic.css(i.style).shadow(i.shadow)),this.graphic.labelrank=i.labelrank,super.render()}redraw(t){let i=this.options,s=this.text||i.format||i.text,e=this.graphic,o=this.points[0];if(!e){this.redraw(t);return}e.attr({text:s?tp(String(s),o,this.annotation.chart):i.formatter.call(o,this)});let n=this.anchor(o),a=this.position(n);a?(e.alignAttr=a,a.anchorX=n.absolutePosition.x,a.anchorY=n.absolutePosition.y,e[t?"animate":"attr"](a)):e.attr({x:0,y:-9999}),e.placed=!!a,super.redraw(t)}anchor(t){let i=super.anchor.apply(this,arguments),s=this.options.x||0,e=this.options.y||0;return i.absolutePosition.x-=s,i.absolutePosition.y-=e,i.relativePosition.x-=s,i.relativePosition.y-=e,i}position(t){let i=this.graphic,s=this.annotation.chart,e=s.tooltip,o=this.points[0],n=this.options,a=t.absolutePosition,r=t.relativePosition,h,l,p,c,d=o.series.visible&&D.prototype.isInsidePlot.call(o);if(i&&d){let{width:t=0,height:u=0}=i;n.distance&&e?h=e.getPosition.call({chart:s,distance:tx(n.distance,16),getPlayingField:e.getPlayingField,pointer:e.pointer},t,u,{plotX:r.x,plotY:r.y,negative:o.negative,ttBelow:o.ttBelow,h:r.height||r.width}):n.positioner?h=n.positioner.call(this):(l={x:a.x,y:a.y,width:0,height:0},h=tg.alignedPosition(tc(n,{width:t,height:u}),l),"justify"===this.options.overflow&&(h=tg.alignedPosition(tg.justifiedOptions(s,i,n,h),l))),n.crop&&(p=h.x-s.plotLeft,c=h.y-s.plotTop,d=s.isInsidePlot(p,c)&&s.isInsidePlot(p+t,c+u))}return d?h:null}}tg.attrsMap={backgroundColor:"fill",borderColor:"stroke",borderWidth:"stroke-width",zIndex:"zIndex",borderRadius:"r",padding:"padding"},tg.shapesWithoutBackground=["connector"];class tf extends V{constructor(t,i,s){super(t,i,s,"shape"),this.type="image",this.translate=super.translateShape}render(t){let i=this.attrsFromOptions(this.options),s=this.options;this.graphic=this.annotation.chart.renderer.image(s.src,0,-9e9,s.width,s.height).attr(i).add(t),this.graphic.width=s.width,this.graphic.height=s.height,super.render()}redraw(t){if(this.graphic){let i=this.anchor(this.points[0]),s=tg.prototype.position.call(this,i);s?this.graphic[t?"animate":"attr"]({x:s.x,y:s.y}):this.graphic.attr({x:0,y:-9e9}),this.graphic.placed=!!s}super.redraw(t)}}tf.attrsMap={width:"width",height:"height",zIndex:"zIndex"};let tm=t.default.AST;var tb=n.n(tm);let{addEvent:tv,createElement:tA}=r(),tP=class{constructor(t,i){this.iconsURL=i,this.container=this.createPopupContainer(t),this.closeButton=this.addCloseButton()}createPopupContainer(t,i="highcharts-popup highcharts-no-tooltip"){return tA("div",{className:i},void 0,t)}addCloseButton(t="highcharts-popup-close"){let i=this,s=this.iconsURL,e=tA("button",{className:t},void 0,this.container);return e.style["background-image"]="url("+(s.match(/png|svg|jpeg|jpg|gif/ig)?s:s+"close.svg")+")",["click","touchstart"].forEach(t=>{tv(e,t,i.closeButtonEvents.bind(i))}),tv(document,"keydown",function(t){"Escape"===t.code&&i.closeButtonEvents()}),e}closeButtonEvents(){this.closePopup()}showPopup(t="highcharts-annotation-toolbar"){let i=this.container,s=this.closeButton;this.type=void 0,i.innerHTML=tb().emptyHTML,i.className.indexOf(t)>=0&&(i.classList.remove(t),i.removeAttribute("style")),i.appendChild(s),i.style.display="block",i.style.height=""}closePopup(){this.container.style.display="none"}},{doc:tM,isFirefox:tO}=r(),{createElement:tw,isArray:tk,isObject:tE,objectEach:tC,pick:tT,stableSort:tX}=r();function tY(t,i,s,e,o,n){let a,r;if(!i)return;let h=this.addInput,l=this.lang;tC(e,(e,n)=>{a=""!==s?s+"."+n:n,tE(e)&&(!tk(e)||tk(e)&&tE(e[0])?((r=l[n]||n).match(/\d/g)||o.push([!0,r,t]),tY.call(this,t,i,a,e,o,!1)):o.push([this,a,"annotation",t,e]))}),n&&(tX(o,t=>t[1].match(/format/g)?-1:1),tO&&o.reverse(),o.forEach(t=>{!0===t[0]?tw("span",{className:"highcharts-annotation-title"},void 0,t[2]).appendChild(tM.createTextNode(t[1])):(t[4]={value:t[4][0],type:t[4][1]},h.apply(t[0],t.splice(1)))}))}let{doc:tL}=r(),{seriesTypes:tS}=N(),{addEvent:tN,createElement:tB,defined:tI,isArray:tR,isObject:tD,objectEach:tF,stableSort:tz}=r();(i=o||(o={}))[i["params.algorithm"]=0]="params.algorithm",i[i["params.average"]=1]="params.average";let tW={"algorithm-pivotpoints":["standard","fibonacci","camarilla"],"average-disparityindex":["sma","ema","dema","tema","wma"]};function tV(t){let i=tB("div",{className:"highcharts-popup-lhs-col"},void 0,t),s=tB("div",{className:"highcharts-popup-rhs-col"},void 0,t);return tB("div",{className:"highcharts-popup-rhs-col-wrapper"},void 0,s),{lhsCol:i,rhsCol:s}}function tj(t,i,s,e){let o=i.params||i.options.params;e.innerHTML=tb().emptyHTML,tB("h3",{className:"highcharts-indicator-title"},void 0,e).appendChild(tL.createTextNode(t$(i,s).indicatorFullName)),tB("input",{type:"hidden",name:"highcharts-type-"+s,value:s},void 0,e),tJ.call(this,s,"series",t,e,i,i.linkedParent&&i.linkedParent.options.id),o.volumeSeriesID&&tJ.call(this,s,"volume",t,e,i,i.linkedParent&&o.volumeSeriesID),tU.call(this,t,"params",o,s,e)}function tH(t,i,s,e){function o(i,s){let e=x.parentNode.children[1];tj.call(n,t,i,s,x),e&&(e.style.display="block"),l&&i.options&&tB("input",{type:"hidden",name:"highcharts-id-"+s,value:i.options.id},void 0,x).setAttribute("highcharts-data-series-id",i.options.id)}let n=this,a=n.lang,r=i.querySelectorAll(".highcharts-popup-lhs-col")[0],h=i.querySelectorAll(".highcharts-popup-rhs-col")[0],l="edit"===s,p=l?t.series:t.options.plotOptions||{};if(!t&&p)return;let c,d=[];l||tR(p)?tR(p)&&(d=t_.call(this,p)):d=tK.call(this,p,e),tz(d,(t,i)=>{let s=t.indicatorFullName.toLowerCase(),e=i.indicatorFullName.toLowerCase();return s<e?-1:+(s>e)}),r.children[1]&&r.children[1].remove();let u=tB("ul",{className:"highcharts-indicator-list"},void 0,r),x=h.querySelectorAll(".highcharts-popup-rhs-col-wrapper")[0];if(d.forEach(t=>{let{indicatorFullName:i,indicatorType:s,series:e}=t;c=tB("li",{className:"highcharts-indicator-list"},void 0,u);let n=tB("button",{className:"highcharts-indicator-list-item",textContent:i},void 0,c);["click","touchstart"].forEach(t=>{tN(n,t,function(){o(e,s)})})}),d.length>0){let{series:t,indicatorType:i}=d[0];o(t,i)}else l||(tb().setElementHTML(x.parentNode.children[0],a.noFilterMatch||""),x.parentNode.children[1].style.display="none")}function tU(t,i,s,e,n){if(!t)return;let a=this.addInput;tF(s,(s,r)=>{let h=i+"."+r;if(tI(s)&&h){if(tD(s)&&(a.call(this,h,e,n,{}),tU.call(this,t,h,s,e,n)),h in o){let o=tG.call(this,e,h,n);tZ.call(this,t,i,o,e,r,s)}else"params.volumeSeriesID"===h||tR(s)||a.call(this,h,e,n,{value:s,type:"number"})}})}function tq(t,i){let s=this,e=i.querySelectorAll(".highcharts-popup-lhs-col")[0],o=this.lang.clearFilter,n=tB("div",{className:"highcharts-input-wrapper"},void 0,e),a=function(i){tH.call(s,t,s.container,"add",i)},r=this.addInput("searchIndicators","input",n,{value:"",type:"text",htmlFor:"search-indicators",labelClassName:"highcharts-input-search-indicators-label"}),h=tB("a",{textContent:o},void 0,n);r.classList.add("highcharts-input-search-indicators"),h.classList.add("clear-filter-button"),tN(r,"input",function(){a(this.value),this.value.length?h.style.display="inline-block":h.style.display="none"}),["click","touchstart"].forEach(t=>{tN(h,t,function(){r.value="",a(""),h.style.display="none"})})}function tG(t,i,s){let e=i.split("."),o=e[e.length-1],n="highcharts-"+i+"-type-"+t,a=this.lang;tB("label",{htmlFor:n},null,s).appendChild(tL.createTextNode(a[o]||i));let r=tB("select",{name:n,className:"highcharts-popup-field",id:"highcharts-select-"+i},null,s);return r.setAttribute("id","highcharts-select-"+i),r}function tZ(t,i,s,e,o,n,a){"series"===i||"volume"===i?t.series.forEach(t=>{let e=t.options,o=e.name||e.params?t.name:e.id||"";"highcharts-navigator-series"!==e.id&&e.id!==(a&&a.options&&a.options.id)&&(tI(n)||"volume"!==i||"column"!==t.type||(n=e.id),tB("option",{value:e.id},void 0,s).appendChild(tL.createTextNode(o)))}):e&&o&&tW[o+"-"+e].forEach(t=>{tB("option",{value:t},void 0,s).appendChild(tL.createTextNode(t))}),tI(n)&&(s.value=n)}function tK(t,i){let s,e=this.chart&&this.chart.options.lang,o=e&&e.navigation&&e.navigation.popup&&e.navigation.popup.indicatorAliases,n=[];return tF(t,(t,e)=>{let a=t&&t.options;if(t.params||a&&a.params){let{indicatorFullName:a,indicatorType:r}=t$(t,e);if(i){let e=RegExp(i.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"i"),h=o&&o[r]&&o[r].join(" ")||"";(a.match(e)||h.match(e))&&(s={indicatorFullName:a,indicatorType:r,series:t},n.push(s))}else s={indicatorFullName:a,indicatorType:r,series:t},n.push(s)}}),n}function t_(t){let i=[];return t.forEach(t=>{t.is("sma")&&i.push({indicatorFullName:t.name,indicatorType:t.type,series:t})}),i}function t$(t,i){let s=t.options,e=tS[i]&&tS[i].prototype.nameBase||i.toUpperCase(),o=i;return s&&s.type&&(o=t.options.type,e=t.name),{indicatorFullName:e,indicatorType:o}}function tJ(t,i,s,e,o,n){if(!s)return;let a=tG.call(this,t,i,e);tZ.call(this,s,i,a,void 0,void 0,void 0,o),tI(n)&&(a.value=n)}let{doc:tQ}=r(),{addEvent:t0,createElement:t1}=r();function t2(){return t1("div",{className:"highcharts-tab-item-content highcharts-no-mousewheel"},void 0,this.container)}function t9(t,i){let s=this.container,e=this.lang,o="highcharts-tab-item";0===i&&(o+=" highcharts-tab-disabled");let n=t1("button",{className:o},void 0,s);return n.appendChild(tQ.createTextNode(e[t+"Button"]||t)),n.setAttribute("highcharts-data-tab-type",t),n}function t3(){let t=this.container,i=t.querySelectorAll(".highcharts-tab-item"),s=t.querySelectorAll(".highcharts-tab-item-content");for(let t=0;t<i.length;t++)i[t].classList.remove("highcharts-tab-item-active"),s[t].classList.remove("highcharts-tab-item-show")}function t5(t,i){let s=this.container.querySelectorAll(".highcharts-tab-item-content");t.className+=" highcharts-tab-item-active",s[i].className+=" highcharts-tab-item-show"}function t4(t){let i=this;this.container.querySelectorAll(".highcharts-tab-item").forEach((s,e)=>{(0!==t||"edit"!==s.getAttribute("highcharts-data-tab-type"))&&["click","touchstart"].forEach(t=>{t0(s,t,function(){t3.call(i),t5.call(i,this,e)})})})}let{doc:t6}=r(),{getOptions:t7}=r(),{addEvent:t8,createElement:it,extend:ii,fireEvent:is,pick:ie}=r();class io extends tP{constructor(t,i,s){super(t,i),this.chart=s,this.lang=(t7().lang.navigation||{}).popup||{},t8(this.container,"mousedown",()=>{let t=s&&s.navigationBindings&&s.navigationBindings.activeAnnotation;if(t){t.cancelClick=!0;let i=t8(t6,"click",()=>{setTimeout(()=>{t.cancelClick=!1},0),i()})}})}addInput(t,i,s,e){let o=t.split("."),n=o[o.length-1],a=this.lang,r="highcharts-"+i+"-"+ie(e.htmlFor,n);n.match(/^\d+$/)||it("label",{htmlFor:r,className:e.labelClassName},void 0,s).appendChild(t6.createTextNode(a[n]||n));let h=it("input",{name:r,value:e.value,type:e.type,className:"highcharts-popup-field"},void 0,s);return h.setAttribute("highcharts-data-name",t),h}closeButtonEvents(){if(this.chart){let t=this.chart.navigationBindings;is(t,"closePopup"),t&&t.selectedButtonElement&&is(t,"deselectButton",{button:t.selectedButtonElement})}else super.closeButtonEvents()}addButton(t,i,s,e,o){let n=it("button",void 0,void 0,t);return n.appendChild(t6.createTextNode(i)),o&&["click","touchstart"].forEach(t=>{t8(n,t,()=>(this.closePopup(),o(function(t,i){let s=Array.prototype.slice.call(t.querySelectorAll("input")),e=Array.prototype.slice.call(t.querySelectorAll("select")),o=t.querySelectorAll("#highcharts-select-series > option:checked")[0],n=t.querySelectorAll("#highcharts-select-volume > option:checked")[0],a={actionType:i,linkedTo:o&&o.getAttribute("value")||"",fields:{}};return s.forEach(t=>{let i=t.getAttribute("highcharts-data-name");t.getAttribute("highcharts-data-series-id")?a.seriesId=t.value:i?a.fields[i]=t.value:a.type=t.value}),e.forEach(t=>{let i=t.id;if("highcharts-select-series"!==i&&"highcharts-select-volume"!==i){let s=i.split("highcharts-select-")[1];a.fields[s]=t.value}}),n&&(a.fields["params.volumeSeriesID"]=n.getAttribute("value")||""),a}(e,s))))}),n}showForm(t,i,s,e){i&&(this.showPopup(),"indicators"===t&&this.indicators.addForm.call(this,i,s,e),"annotation-toolbar"===t&&this.annotations.addToolbar.call(this,i,s,e),"annotation-edit"===t&&this.annotations.addForm.call(this,i,s,e),"flag"===t&&this.annotations.addForm.call(this,i,s,e,!0),this.type=t,this.container.style.height=this.container.offsetHeight+"px")}}ii(io.prototype,{annotations:{addForm:function(t,i,s,e){if(!t)return;let o=this.container,n=this.lang,a=tw("h2",{className:"highcharts-popup-main-title"},void 0,o);a.appendChild(tM.createTextNode(n[i.langKey]||i.langKey||"")),a=tw("div",{className:"highcharts-popup-lhs-col highcharts-popup-lhs-full"},void 0,o);let r=tw("div",{className:"highcharts-popup-bottom-row"},void 0,o);tY.call(this,a,t,"",i,[],!0),this.addButton(r,e?n.addButton||"Add":n.saveButton||"Save",e?"add":"save",o,s)},addToolbar:function(t,i,s){let e=this.lang,o=this.container,n=this.showForm,a="highcharts-annotation-toolbar";-1===o.className.indexOf(a)&&(o.className+=" "+a+" highcharts-no-mousewheel"),t&&(o.style.top=t.plotTop+10+"px");let r=tw("p",{className:"highcharts-annotation-label"},void 0,o);r.setAttribute("aria-label","Annotation type"),r.appendChild(tM.createTextNode(tT(e[i.langKey]||i.langKey,i.shapes&&i.shapes[0].type,"")));let h=this.addButton(o,e.editButton||"Edit","edit",o,()=>{n.call(this,"annotation-edit",t,i,s)});h.className+=" highcharts-annotation-edit-button",h.style["background-image"]="url("+this.iconsURL+"edit.svg)",h=this.addButton(o,e.removeButton||"Remove","remove",o,s),h.className+=" highcharts-annotation-remove-button",h.style["background-image"]="url("+this.iconsURL+"destroy.svg)"}},indicators:{addForm:function(t,i,s){let e,o=this.lang;if(!t)return;this.tabs.init.call(this,t);let n=this.container.querySelectorAll(".highcharts-tab-item-content");tV(n[0]),tq.call(this,t,n[0]),tH.call(this,t,n[0],"add"),e=n[0].querySelectorAll(".highcharts-popup-rhs-col")[0],this.addButton(e,o.addButton||"add","add",e,s),tV(n[1]),tH.call(this,t,n[1],"edit"),e=n[1].querySelectorAll(".highcharts-popup-rhs-col")[0],this.addButton(e,o.saveButton||"save","edit",e,s),this.addButton(e,o.removeButton||"remove","remove",e,s)},getAmount:function(){let t=0;return this.series.forEach(i=>{(i.params||i.options.params)&&t++}),t}},tabs:{init:function(t){if(!t)return;let i=this.indicators.getAmount.call(t),s=t9.call(this,"add");t9.call(this,"edit",i),t2.call(this),t2.call(this),t4.call(this,i),t5.call(this,s,0)}}});let{composed:ia}=r(),{addEvent:ir,pushUnique:ih,wrap:il}=r();function ip(){this.popup&&this.popup.closePopup()}function ic(t){this.popup||(this.popup=new io(this.chart.container,this.chart.options.navigation.iconsURL||this.chart.options.stockTools&&this.chart.options.stockTools.gui.iconsURL||"https://code.highcharts.com/12.2.0/gfx/stock-icons/",this.chart)),this.popup.showForm(t.formType,this.chart,t.options,t.onSubmit)}function id(t,i){this.inClass(i.target,"highcharts-popup")||t.apply(this,Array.prototype.slice.call(arguments,1))}let iu=function(t,i){ih(ia,"Popup")&&(ir(t,"closePopup",ip),ir(t,"showPopup",ic),il(i.prototype,"onContainerMouseDown",id))},{getDeferredAnimation:ix}=r(),{destroyObjectProperties:iy,erase:ig,fireEvent:im,merge:ib,pick:iv,splat:iA}=r();function iP(t,i){let s={};return["labels","shapes"].forEach(e=>{let o=t[e],n=i[e];o&&(n?s[e]=iA(n).map((t,i)=>ib(o[i],t)):s[e]=t[e])}),s}class iM extends T{static compose(t,i,s,e){v.compose(iM,t,s),tg.compose(e),ti.compose(t,e),i.compose(iM,t),iu(i,s)}constructor(t,i){super(),this.coll="annotations",this.chart=t,this.points=[],this.controlPoints=[],this.coll="annotations",this.index=-1,this.labels=[],this.shapes=[],this.options=ib(this.defaultOptions,i),this.userOptions=i;let s=iP(this.options,i);this.options.labels=s.labels,this.options.shapes=s.shapes,this.init(t,this.options)}addClipPaths(){this.setClipAxes(),this.clipXAxis&&this.clipYAxis&&this.options.crop&&(this.clipRect=this.chart.renderer.clipRect(this.getClipBox()))}addLabels(){let t=this.options.labels||[];t.forEach((i,s)=>{let e=this.initLabel(i,s);ib(!0,t[s],e.options)})}addShapes(){let t=this.options.shapes||[];t.forEach((i,s)=>{let e=this.initShape(i,s);ib(!0,t[s],e.options)})}destroy(){let t=this.chart,i=function(t){t.destroy()};this.labels.forEach(i),this.shapes.forEach(i),this.clipXAxis=null,this.clipYAxis=null,ig(t.labelCollectors,this.labelCollector),super.destroy(),this.destroyControlTarget(),iy(this,t)}destroyItem(t){ig(this[t.itemType+"s"],t),t.destroy()}getClipBox(){if(this.clipXAxis&&this.clipYAxis)return{x:this.clipXAxis.left,y:this.clipYAxis.top,width:this.clipXAxis.width,height:this.clipYAxis.height}}initProperties(t,i){this.setOptions(i);let s=iP(this.options,i);this.options.labels=s.labels,this.options.shapes=s.shapes,this.chart=t,this.points=[],this.controlPoints=[],this.coll="annotations",this.userOptions=i,this.labels=[],this.shapes=[]}init(t,i,s=this.index){let e=this.chart,o=this.options.animation;this.index=s,this.linkPoints(),this.addControlPoints(),this.addShapes(),this.addLabels(),this.setLabelCollector(),this.animationConfig=ix(e,o)}initLabel(t,i){let s=new tg(this,ib(this.options.labelOptions,{controlPointOptions:this.options.controlPointOptions},t),i);return s.itemType="label",this.labels.push(s),s}initShape(t,i){let s=ib(this.options.shapeOptions,{controlPointOptions:this.options.controlPointOptions},t),e=new iM.shapesMap[s.type](this,s,i);return e.itemType="shape",this.shapes.push(e),e}redraw(t){this.linkPoints(),this.graphic||this.render(),this.clipRect&&this.clipRect.animate(this.getClipBox()),this.redrawItems(this.shapes,t),this.redrawItems(this.labels,t),this.redrawControlPoints(t)}redrawItem(t,i){t.linkPoints(),t.shouldBeDrawn()?(t.graphic||this.renderItem(t),t.redraw(iv(i,!0)&&t.graphic.placed),t.points.length&&function(t){let i=t.graphic,s=t.points.some(t=>!1!==t.series.visible&&!1!==t.visible);i&&(s?"hidden"===i.visibility&&i.show():i.hide())}(t)):this.destroyItem(t)}redrawItems(t,i){let s=t.length;for(;s--;)this.redrawItem(t[s],i)}remove(){return this.chart.removeAnnotation(this)}render(){let t=this.chart.renderer;this.graphic=t.g("annotation").attr({opacity:0,zIndex:this.options.zIndex,visibility:this.options.visible?"inherit":"hidden"}).add(),this.shapesGroup=t.g("annotation-shapes").add(this.graphic),this.options.crop&&this.shapesGroup.clip(this.chart.plotBoxClip),this.labelsGroup=t.g("annotation-labels").attr({translateX:0,translateY:0}).add(this.graphic),this.addClipPaths(),this.clipRect&&this.graphic.clip(this.clipRect),this.renderItems(this.shapes),this.renderItems(this.labels),this.addEvents(),this.renderControlPoints()}renderItem(t){t.render("label"===t.itemType?this.labelsGroup:this.shapesGroup)}renderItems(t){let i=t.length;for(;i--;)this.renderItem(t[i])}setClipAxes(){let t=this.chart.xAxis,i=this.chart.yAxis,s=(this.options.labels||[]).concat(this.options.shapes||[]).reduce((s,e)=>{let o=e&&(e.point||e.points&&e.points[0]);return[t[o&&o.xAxis]||s[0],i[o&&o.yAxis]||s[1]]},[]);this.clipXAxis=s[0],this.clipYAxis=s[1]}setControlPointsVisibility(t){let i=function(i){i.setControlPointsVisibility(t)};this.controlPoints.forEach(i=>{i.setVisibility(t)}),this.shapes.forEach(i),this.labels.forEach(i)}setLabelCollector(){let t=this;t.labelCollector=function(){return t.labels.reduce(function(t,i){return i.options.allowOverlap||t.push(i.graphic),t},[])},t.chart.labelCollectors.push(t.labelCollector)}setOptions(t){this.options=ib(this.defaultOptions,t)}setVisibility(t){let i=this.options,s=this.chart.navigationBindings,e=iv(t,!i.visible);if(this.graphic.attr("visibility",e?"inherit":"hidden"),!e){let t=function(t){t.setControlPointsVisibility(e)};this.shapes.forEach(t),this.labels.forEach(t),s.activeAnnotation===this&&s.popup&&"annotation-toolbar"===s.popup.type&&im(s,"closePopup")}i.visible=e}update(t,i){let s=this.chart,e=iP(this.userOptions,t),o=s.annotations.indexOf(this),n=ib(!0,this.userOptions,t);n.labels=e.labels,n.shapes=e.shapes,this.destroy(),this.initProperties(s,n),this.init(s,n),s.options.annotations[o]=this.options,this.isUpdating=!0,iv(i,!0)&&s.drawAnnotations(),im(this,"afterUpdate"),this.isUpdating=!1}}iM.ControlPoint=L,iM.MockPoint=D,iM.shapesMap={rect:te,circle:tn,ellipse:th,path:ti,image:tf},iM.types={},iM.prototype.defaultOptions={visible:!0,animation:{},crop:!0,draggable:"xy",labelOptions:{align:"center",allowOverlap:!1,backgroundColor:"rgba(0, 0, 0, 0.75)",borderColor:"#000000",borderRadius:3,borderWidth:1,className:"highcharts-no-tooltip",crop:!1,formatter:function(){return A(this.y)?""+this.y:"Annotation label"},includeInDataExport:!0,overflow:"justify",padding:5,shadow:!1,shape:"callout",style:{fontSize:"0.7em",fontWeight:"normal",color:"contrast"},useHTML:!1,verticalAlign:"bottom",x:0,y:-16},shapeOptions:{stroke:"rgba(0, 0, 0, 0.75)",strokeWidth:1,fill:"rgba(0, 0, 0, 0.75)",r:0,snap:2},controlPointOptions:{events:{},style:{cursor:"pointer",fill:"#ffffff",stroke:"#000000","stroke-width":2},height:10,symbol:"circle",visible:!1,width:10},events:{},zIndex:6},iM.prototype.nonDOMEvents=["add","afterUpdate","drag","remove"],F.compose(iM);let iO=iM,{merge:iw}=r();class ik extends iO{addControlPoints(){let t=this.options,i=ik.basicControlPoints,s=this.basicType;(t.labels||t.shapes||[]).forEach(t=>{t.controlPoints=i[s]})}init(){let t=this.options;if(t.shapes){delete t.labelOptions;let i=t.shapes[0].type;t.shapes[0].className=(t.shapes[0].className||"")+" highcharts-basic-shape",i&&"path"!==i?this.basicType=i:this.basicType="rectangle"}else delete t.shapes,this.basicType="label";super.init.apply(this,arguments)}}ik.basicControlPoints={label:[{symbol:"triangle-down",positioner:function(t){if(!t.graphic.placed)return{x:0,y:-9e7};let i=D.pointToPixels(t.points[0]);return{x:i.x-(this.graphic.width||0)/2,y:i.y-(this.graphic.height||0)/2}},events:{drag:function(t,i){let s=this.mouseMoveToTranslation(t);i.translatePoint(s.x,s.y),i.annotation.userOptions.labels[0].point=i.options.point,i.redraw(!1)}}},{symbol:"square",positioner:function(t){return t.graphic.placed?{x:t.graphic.alignAttr.x-(this.graphic.width||0)/2,y:t.graphic.alignAttr.y-(this.graphic.height||0)/2}:{x:0,y:-9e7}},events:{drag:function(t,i){let s=this.mouseMoveToTranslation(t);i.translate(s.x,s.y),i.annotation.userOptions.labels[0].point=i.options.point,i.redraw(!1)}}}],rectangle:[{positioner:function(t){let i=D.pointToPixels(t.points[2]);return{x:i.x-4,y:i.y-4}},events:{drag:function(t,i){let s=i.annotation,e=this.chart.pointer?.getCoordinates(t),o=i.options.points,n=s.userOptions.shapes,a=s.clipXAxis?.index||0,r=s.clipYAxis?.index||0;if(e){let t=e.xAxis[a].value,s=e.yAxis[r].value;o[1].x=t,o[2].x=t,o[2].y=s,o[3].y=s,n&&n[0]&&(n[0].points=i.options.points)}s.redraw(!1)}}}],circle:[{positioner:function(t){let i=D.pointToPixels(t.points[0]),s=t.options.r;return{x:i.x+s*Math.cos(Math.PI/4)-(this.graphic.width||0)/2,y:i.y+s*Math.sin(Math.PI/4)-(this.graphic.height||0)/2}},events:{drag:function(t,i){let s=i.annotation,e=this.mouseMoveToTranslation(t),o=s.userOptions.shapes;i.setRadius(Math.max(i.options.r+e.y/Math.sin(Math.PI/4),5)),o&&o[0]&&(o[0].r=i.options.r,o[0].point=i.options.point),i.redraw(!1)}}}],ellipse:[{positioner:function(t){let i=t.getAbsolutePosition(t.points[0]);return{x:i.x-(this.graphic.width||0)/2,y:i.y-(this.graphic.height||0)/2}},events:{drag:function(t,i){let s=i.getAbsolutePosition(i.points[0]);i.translatePoint(t.chartX-s.x,t.chartY-s.y,0),i.redraw(!1)}}},{positioner:function(t){let i=t.getAbsolutePosition(t.points[1]);return{x:i.x-(this.graphic.width||0)/2,y:i.y-(this.graphic.height||0)/2}},events:{drag:function(t,i){let s=i.getAbsolutePosition(i.points[1]);i.translatePoint(t.chartX-s.x,t.chartY-s.y,1),i.redraw(!1)}}},{positioner:function(t){let i=t.getAbsolutePosition(t.points[0]),s=t.getAbsolutePosition(t.points[1]),e=t.getAttrs(i,s);return{x:e.cx-(this.graphic.width||0)/2+e.ry*Math.sin(e.angle*Math.PI/180),y:e.cy-(this.graphic.height||0)/2-e.ry*Math.cos(e.angle*Math.PI/180)}},events:{drag:function(t,i){let s=i.getAbsolutePosition(i.points[0]),e=i.getAbsolutePosition(i.points[1]),o=i.getDistanceFromLine(s,e,t.chartX,t.chartY),n=i.getYAxis(),a=Math.abs(n.toValue(0)-n.toValue(o));i.setYRadius(a),i.redraw(!1)}}}]},ik.prototype.defaultOptions=iw(iO.prototype.defaultOptions,{}),iO.types.basicAnnotation=ik;let{merge:iE}=r();class iC extends iO{setClipAxes(){this.clipXAxis=this.chart.xAxis[this.options.typeOptions.xAxis],this.clipYAxis=this.chart.yAxis[this.options.typeOptions.yAxis]}getPointsOptions(){let t=this.options.typeOptions;return(t.points||[]).map(i=>(i.xAxis=t.xAxis,i.yAxis=t.yAxis,i))}getControlPointsOptions(){return this.getPointsOptions()}addControlPoints(){this.getControlPointsOptions().forEach(function(t,i){let s=new L(this.chart,this,iE(this.options.controlPointOptions,t.controlPoint),i);this.controlPoints.push(s),t.controlPoint=s.options},this)}addShapes(){let t=this.options.typeOptions,i=this.initShape(iE(t.line,{type:"path",className:"highcharts-crooked-lines",points:this.points.map((t,i)=>function(t){return t.annotation.points[i]})}),0);t.line=i.options}}iC.prototype.defaultOptions=iE(iO.prototype.defaultOptions,{typeOptions:{xAxis:0,yAxis:0,line:{fill:"none"}},controlPointOptions:{positioner:function(t){let i=this.graphic,s=D.pointToPixels(t.points[this.index]);return{x:s.x-(i.width||0)/2,y:s.y-(i.height||0)/2}},events:{drag:function(t,i){if(i.chart.isInsidePlot(t.chartX-i.chart.plotLeft,t.chartY-i.chart.plotTop,{visiblePlotOnly:!0})){let s=this.mouseMoveToTranslation(t),e=i.options.typeOptions;i.translatePoint(s.x,s.y,this.index),e.points[this.index].x=i.points[this.index].x,e.points[this.index].y=i.points[this.index].y,i.redraw(!1)}}}}}),iO.types.crookedLine=iC;let iT=iC,{merge:iX}=r();class iY extends iT{addLabels(){this.getPointsOptions().forEach((t,i)=>{let s=this.options.typeOptions,e=this.initLabel(iX(t.label,{text:s.labels[i],point:function(t){return t.annotation.points[i]}}),!1);t.label=e.options})}}iY.prototype.defaultOptions=iX(iT.prototype.defaultOptions,{typeOptions:{labels:["(0)","(A)","(B)","(C)","(D)","(E)"],line:{strokeWidth:1}},labelOptions:{align:"center",allowOverlap:!0,crop:!0,overflow:"none",type:"rect",backgroundColor:"none",borderWidth:0,y:-5}}),iO.types.elliottWave=iY;let{merge:iL}=r();class iS extends iT{getPointsOptions(){let t=iT.prototype.getPointsOptions.call(this),i=this.options.typeOptions.yAxis||0,s=this.chart.yAxis[i];if(t[2]=this.heightPointOptions(t[1]),t[3]=this.heightPointOptions(t[0]),s&&s.logarithmic){let i=s.toPixels(t[2].y)-s.toPixels(t[1].y),e=s.toPixels(t[0].y)+i;t[3].y=s.toValue(e)}return t}getControlPointsOptions(){return this.getPointsOptions().slice(0,2)}heightPointOptions(t){let i=iL(t),s=this.options.typeOptions;return i.y+=s.height,i}addControlPoints(){iT.prototype.addControlPoints.call(this);let t=this.options,i=t.typeOptions,s=new L(this.chart,this,iL(t.controlPointOptions,i.heightControlPoint),2);this.controlPoints.push(s),i.heightControlPoint=s.options}addShapes(){this.addLine(),this.addBackground()}addLine(){let t=this.initShape(iL(this.options.typeOptions.line,{type:"path",points:[this.points[0],this.points[1],function(t){let i=D.pointToOptions(t.annotation.points[2]);return i.command="M",i},this.points[3]],className:"highcharts-tunnel-lines"}),0);this.options.typeOptions.line=t.options}addBackground(){let t=this.initShape(iL(this.options.typeOptions.background,{type:"path",points:this.points.slice(),className:"highcharts-tunnel-background"}),1);this.options.typeOptions.background=t.options}translateSide(t,i,s){let e=Number(s);this.translatePoint(t,i,e),this.translatePoint(t,i,0===e?3:2)}translateHeight(t){this.translatePoint(0,t,2),this.translatePoint(0,t,3),this.options.typeOptions.height=this.points[3].y-this.points[0].y,this.userOptions.typeOptions.height=this.options.typeOptions.height}}iS.prototype.defaultOptions=iL(iT.prototype.defaultOptions,{typeOptions:{background:{fill:"rgba(130, 170, 255, 0.4)",strokeWidth:0},line:{strokeWidth:1},height:-2,heightControlPoint:{positioner:function(t){let i=D.pointToPixels(t.points[2]),s=D.pointToPixels(t.points[3]),e=(i.x+s.x)/2;return{x:e-(this.graphic.width||0)/2,y:(s.y-i.y)/(s.x-i.x)*(e-i.x)+i.y-(this.graphic.height||0)/2}},events:{drag:function(t,i){i.chart.isInsidePlot(t.chartX-i.chart.plotLeft,t.chartY-i.chart.plotTop,{visiblePlotOnly:!0})&&(i.translateHeight(this.mouseMoveToTranslation(t).y),i.redraw(!1))}}}},controlPointOptions:{events:{drag:function(t,i){if(i.chart.isInsidePlot(t.chartX-i.chart.plotLeft,t.chartY-i.chart.plotTop,{visiblePlotOnly:!0})){let s=this.mouseMoveToTranslation(t);i.translateSide(s.x,s.y,!!this.index),i.redraw(!1)}}}}}),iO.types.tunnel=iS;let iN=iS,{merge:iB}=r();class iI extends iT{static edgePoint(t,i){return function(s){let e=s.annotation,o=e.options.typeOptions.type,n=e.points;return("horizontalLine"===o||"verticalLine"===o)&&(n=[n[0],new D(e.chart,n[0].target,{x:n[0].x+ +("horizontalLine"===o),y:n[0].y+ +("verticalLine"===o),xAxis:n[0].options.xAxis,yAxis:n[0].options.yAxis})]),iI.findEdgePoint(n[t],n[i])}}static findEdgeCoordinate(t,i,s,e){let o="x"===s?"y":"x";return(i[s]-t[s])*(e-t[o])/(i[o]-t[o])+t[s]}static findEdgePoint(t,i){let s,e,o,n=t.series.chart,a=t.series.xAxis,r=i.series.yAxis,h=D.pointToPixels(t),l=D.pointToPixels(i),p=l.x-h.x,c=l.y-h.y,d=a.left,u=d+a.width,x=r.top,y=x+r.height,g=p<0?d:u,f=c<0?x:y,m={x:0===p?h.x:g,y:0===c?h.y:f};return 0!==p&&0!==c&&(e=iI.findEdgeCoordinate(h,l,"y",g),s=iI.findEdgeCoordinate(h,l,"x",f),e>=x&&e<=y?(m.x=g,m.y=e):(m.x=s,m.y=f)),m.x-=n.plotLeft,m.y-=n.plotTop,t.series.chart.inverted&&(o=m.x,m.x=m.y,m.y=o),m}addShapes(){let t=this.options.typeOptions,i=[this.points[0],iI.endEdgePoint];t.type.match(/line/gi)&&(i[0]=iI.startEdgePoint);let s=this.initShape(iB(t.line,{type:"path",points:i,className:"highcharts-infinity-lines"}),0);t.line=s.options}}iI.endEdgePoint=iI.edgePoint(0,1),iI.startEdgePoint=iI.edgePoint(1,0),iI.prototype.defaultOptions=iB(iT.prototype.defaultOptions,{}),iO.types.infinityLine=iI;let iR=iI,{merge:iD,isNumber:iF,defined:iz}=r();class iW extends iT{init(t,i,s){iz(i.yAxis)&&i.points.forEach(t=>{t.yAxis=i.yAxis}),iz(i.xAxis)&&i.points.forEach(t=>{t.xAxis=i.xAxis}),super.init(t,i,s)}setPath(){this.shapes[0].options.d=this.getPath()}getPath(){return[["M",this.startX,this.y]].concat(function(t,i,s,e){let o=[];for(let n=1;n<=i;n++)o.push(["A",t/2,t/2,0,1,1,s+n*t,e]);return o}(this.pixelInterval,this.numberOfCircles,this.startX,this.y))}addShapes(){let t=this.options.typeOptions;this.setPathProperties();let i=this.initShape(iD(t.line,{type:"path",d:this.getPath(),points:this.options.points,className:"highcharts-timecycles-lines"}),0);t.line=i.options}addControlPoints(){let t=this.options,i=t.typeOptions;t.controlPointOptions.style.cursor=this.chart.inverted?"ns-resize":"ew-resize",i.controlPointOptions.forEach(i=>{let s=iD(t.controlPointOptions,i),e=new L(this.chart,this,s,0);this.controlPoints.push(e)})}setPathProperties(){let t=this.options.typeOptions,i=t.points;if(!i)return;let s=i[0],e=i[1],o=t.xAxis||0,n=t.yAxis||0,a=this.chart.xAxis[o],r=this.chart.yAxis[n],h=s.x,l=s.y,p=e.x;if(!h||!p)return;let c=iF(l)?r.toPixels(l):r.top+r.height,d=iF(h)?a.toPixels(h):a.left,u=iF(p)?a.toPixels(p):a.left+30,x=a.len,y=Math.round(Math.max(Math.abs(u-d),2)),g=Math.floor(x/y)+2,f=(Math.floor((d-a.left)/y)+1)*y;this.startX=d-f,this.y=c,this.pixelInterval=y,this.numberOfCircles=g}redraw(t){this.setPathProperties(),this.setPath(),super.redraw(t)}}iW.prototype.defaultOptions=iD(iT.prototype.defaultOptions,{typeOptions:{controlPointOptions:[{positioner:function(t){let i=t.points[0];return{x:t.anchor(i).absolutePosition.x-(this.graphic.width||0)/2,y:t.y-(this.graphic.height||0)}},events:{drag:function(t,i){let s=i.anchor(i.points[0]).absolutePosition;i.translatePoint(t.chartX-s.x,0,0),i.redraw(!1)}}},{positioner:function(t){let i=t.points[1];return{x:t.anchor(i).absolutePosition.x-(this.graphic.width||0)/2,y:t.y-(this.graphic.height||0)}},events:{drag:function(t,i){let s=i.anchor(i.points[1]).absolutePosition;i.translatePoint(t.chartX-s.x,0,1),i.redraw(!1)}}}]}}),iO.types.timeCycles=iW;let{merge:iV}=r();function ij(t,i){return function(){let s=this.annotation;if(!s.startRetracements||!s.endRetracements)return[];let e=this.anchor(s.startRetracements[t]).absolutePosition,o=this.anchor(s.endRetracements[t]).absolutePosition,n=[["M",Math.round(e.x),Math.round(e.y)],["L",Math.round(o.x),Math.round(o.y)]];if(i){let i=this.anchor(s.endRetracements[t-1]).absolutePosition,e=this.anchor(s.startRetracements[t-1]).absolutePosition;n.push(["L",Math.round(i.x),Math.round(i.y)],["L",Math.round(e.x),Math.round(e.y)])}return n}}class iH extends iN{linkPoints(){super.linkPoints(),this.linkRetracementsPoints()}linkRetracementsPoints(){let t=this.points,i=t[0].y-t[3].y,s=t[1].y-t[2].y,e=t[0].x,o=t[1].x;iH.levels.forEach((n,a)=>{let r=t[0].y-i*n,h=t[1].y-s*n,l=this.options.typeOptions.reversed?iH.levels.length-a-1:a;this.startRetracements=this.startRetracements||[],this.endRetracements=this.endRetracements||[],this.linkRetracementPoint(l,e,r,this.startRetracements),this.linkRetracementPoint(l,o,h,this.endRetracements)})}linkRetracementPoint(t,i,s,e){let o=e[t],n=this.options.typeOptions;o?(o.options.x=i,o.options.y=s,o.refresh()):e[t]=new D(this.chart,this,{x:i,y:s,xAxis:n.xAxis,yAxis:n.yAxis})}addShapes(){iH.levels.forEach(function(t,i){let{backgroundColors:s,lineColor:e,lineColors:o}=this.options.typeOptions;this.initShape({type:"path",d:ij(i),stroke:o[i]||e,className:"highcharts-fibonacci-line"},i),i>0&&this.initShape({type:"path",fill:s[i-1],strokeWidth:0,d:ij(i,!0),className:"highcharts-fibonacci-background-"+(i-1)})},this)}addLabels(){iH.levels.forEach(function(t,i){let s=this.options.typeOptions,e=this.initLabel(iV(s.labels[i],{point:function(t){return D.pointToOptions(t.annotation.startRetracements[i])},text:t.toString()}));s.labels[i]=e.options},this)}}iH.levels=[0,.236,.382,.5,.618,.786,1],iH.prototype.defaultOptions=iV(iN.prototype.defaultOptions,{typeOptions:{reversed:!1,height:2,backgroundColors:["rgba(130, 170, 255, 0.4)","rgba(139, 191, 216, 0.4)","rgba(150, 216, 192, 0.4)","rgba(156, 229, 161, 0.4)","rgba(162, 241, 130, 0.4)","rgba(169, 255, 101, 0.4)"],lineColor:"#999999",lineColors:[],labels:[]},labelOptions:{allowOverlap:!0,align:"right",backgroundColor:"none",borderWidth:0,crop:!1,overflow:"none",shape:"rect",style:{color:"grey"},verticalAlign:"middle",y:0}}),iO.types.fibonacci=iH;let{merge:iU}=r();function iq(t,i,s){return function(e){let o=e.annotation.chart,n=o.inverted?o.plotTop:o.plotLeft,a=e.annotation.points,r=a[0].series.xAxis,h=a.length>1?a[1].plotX-a[0].plotX:0,l=r.toValue(a[0].plotX+n+s*h);return a=[new D(o,a[0].target,{x:l,y:0,xAxis:a[0].options.xAxis,yAxis:a[0].options.yAxis}),new D(o,a[0].target,{x:l,y:1,xAxis:a[0].options.xAxis,yAxis:a[0].options.yAxis})],iR.findEdgePoint(a[t],a[i])}}class iG extends iT{addShapes(){let t=1,i=1;for(let s=0;s<11;s++){let e=s?t:0,o=[iq(1,0,e),iq(0,1,e)];t=(i=t+i)-t,1===s&&(this.secondLineEdgePoints=[o[0],o[1]]),this.initShape(iU(this.options.typeOptions.line,{type:"path",points:o,className:"highcharts-fibonacci-timezones-lines"}),s)}}addControlPoints(){let t=this.options,i=t.typeOptions,s=new L(this.chart,this,iU(t.controlPointOptions,i.controlPointOptions),0);this.controlPoints.push(s),i.controlPointOptions=s.options}}iG.prototype.defaultOptions=iU(iT.prototype.defaultOptions,{typeOptions:{line:{stroke:"rgba(0, 0, 0, 0.75)",strokeWidth:1,fill:void 0},controlPointOptions:{positioner:function(){let t=this.target,i=this.graphic,s=t.secondLineEdgePoints,e={annotation:t},o=s[0](e).y,n=s[1](e).y,a=this.chart.plotLeft,r=this.chart.plotTop,h=s[0](e).x,l=(o+n)/2;return this.chart.inverted&&([h,l]=[l,h]),{x:a+h-(i.width||0)/2,y:r+l-(i.height||0)/2}},events:{drag:function(t,i){if(i.chart.isInsidePlot(t.chartX-i.chart.plotLeft,t.chartY-i.chart.plotTop,{visiblePlotOnly:!0})){let s=this.mouseMoveToTranslation(t);i.translatePoint(s.x,0,1),i.redraw(!1)}}}}}}),iO.types.fibonacciTimeZones=iG;let{merge:iZ}=r();class iK extends iR{static outerLineEdgePoint(t){return function(i){let s=i.annotation,e=s.points;return iK.findEdgePoint(e[t],e[0],new D(s.chart,i,s.midPointOptions()))}}static findEdgePoint(t,i,s){let e=Math.atan2(s.plotY-i.plotY,s.plotX-i.plotX);return{x:t.plotX+1e7*Math.cos(e),y:t.plotY+1e7*Math.sin(e)}}static middleLineEdgePoint(t){let i=t.annotation,s=i.points;return iR.findEdgePoint(s[0],new D(i.chart,t,i.midPointOptions()))}midPointOptions(){let t=this.points;return{x:(t[1].x+t[2].x)/2,y:(t[1].y+t[2].y)/2,xAxis:t[0].series.xAxis,yAxis:t[0].series.yAxis}}addShapes(){this.addLines(),this.addBackgrounds()}addLines(){let t="highcharts-pitchfork-lines";this.initShape({type:"path",points:[this.points[0],iK.middleLineEdgePoint],className:t},0),this.initShape({type:"path",points:[this.points[1],iK.topLineEdgePoint],className:t},1),this.initShape({type:"path",points:[this.points[2],iK.bottomLineEdgePoint],className:t},2)}addBackgrounds(){let t=this.shapes,i=this.options.typeOptions,s=this.initShape(iZ(i.innerBackground,{type:"path",points:[function(t){let i=t.annotation,s=i.points,e=i.midPointOptions();return{x:(s[1].x+e.x)/2,y:(s[1].y+e.y)/2,xAxis:e.xAxis,yAxis:e.yAxis}},t[1].points[1],t[2].points[1],function(t){let i=t.annotation,s=i.points,e=i.midPointOptions();return{x:(e.x+s[2].x)/2,y:(e.y+s[2].y)/2,xAxis:e.xAxis,yAxis:e.yAxis}}],className:"highcharts-pitchfork-inner-background"}),3),e=this.initShape(iZ(i.outerBackground,{type:"path",points:[this.points[1],t[1].points[1],t[2].points[1],this.points[2]],className:"highcharts-pitchfork-outer-background"}),4);i.innerBackground=s.options,i.outerBackground=e.options}}iK.topLineEdgePoint=iK.outerLineEdgePoint(1),iK.bottomLineEdgePoint=iK.outerLineEdgePoint(0),iK.prototype.defaultOptions=iZ(iR.prototype.defaultOptions,{typeOptions:{innerBackground:{fill:"rgba(130, 170, 255, 0.4)",strokeWidth:0},outerBackground:{fill:"rgba(156, 229, 161, 0.4)",strokeWidth:0}}}),iO.types.pitchfork=iK;let{merge:i_,pick:i$}=r();class iJ extends iO{static connectorFirstPoint(t){let i=t.annotation,s=i.chart,e=s.inverted,o=i.points[0],n=i$(o.series.yAxis&&o.series.yAxis.left,0),a=i$(o.series.yAxis&&o.series.yAxis.top,0),r=i.options.typeOptions.label.offset,h=D.pointToPixels(o,!0)[e?"x":"y"];return{x:o.x,xAxis:o.series.xAxis,y:h+r+(e?n-s.plotLeft:a-s.plotTop)}}static connectorSecondPoint(t){let i=t.annotation,s=i.chart,e=s.inverted,o=i.options.typeOptions,n=i.points[0],a=i$(n.series.yAxis&&n.series.yAxis.left,0),r=i$(n.series.yAxis&&n.series.yAxis.top,0),h=D.pointToPixels(n,!0)[e?"x":"y"],l=o.yOffset;return o.label.offset<0&&(l*=-1),{x:n.x,xAxis:n.series.xAxis,y:h+l+(e?a-s.plotLeft:r-s.plotTop)}}getPointsOptions(){return[this.options.typeOptions.point]}addShapes(){let t=this.options.typeOptions,i=this.initShape(i_(t.connector,{type:"path",points:[iJ.connectorFirstPoint,iJ.connectorSecondPoint],className:"highcharts-vertical-line"}),0);t.connector=i.options,this.userOptions.typeOptions.point=t.point}addLabels(){let t=this.options.typeOptions,i=t.label,s=0,e=i.offset,o=i.offset<0?"bottom":"top",n="center";this.chart.inverted&&(s=i.offset,e=0,o="middle",n=i.offset<0?"right":"left"),t.label=this.initLabel(i_(i,{verticalAlign:o,align:n,x:s,y:e})).options}}iJ.prototype.defaultOptions=i_(iO.prototype.defaultOptions,{typeOptions:{yOffset:10,label:{offset:-40,point:function(t){return t.annotation.points[0]},allowOverlap:!0,backgroundColor:"none",borderWidth:0,crop:!0,overflow:"none",shape:"rect",text:"{y:.2f}"},connector:{strokeWidth:1,markerEnd:"arrow"}}}),iO.types.verticalLine=iJ;let{defined:iQ,extend:i0,isNumber:i1,merge:i2,pick:i9}=r();function i3(){let t=0,i=0,s=0,e=this.chart.series,o=i7(this.xAxisMin,this.xAxisMax,this.yAxisMin,this.yAxisMax);return e.forEach(t=>{t.visible&&"highcharts-navigator-series"!==t.options.id&&t.points.forEach(t=>{i5(t,o)&&i1(t.y)&&(i+=t.y,s++)})}),s>0&&(t=i/s),t}function i5(t,i){return!t.isNull&&i1(t.y)&&t.x>i.xAxisMin&&t.x<=i.xAxisMax&&t.y>i.yAxisMin&&t.y<=i.yAxisMax}function i4(){let t=this.chart.series,i=i7(this.xAxisMin,this.xAxisMax,this.yAxisMin,this.yAxisMax),s=0;return t.forEach(t=>{t.visible&&"highcharts-navigator-series"!==t.options.id&&t.points.forEach(t=>{i5(t,i)&&s++})}),s}function i6(){return"Min: "+this.min+"<br>Max: "+this.max+"<br>Average: "+this.average.toFixed(2)+"<br>Bins: "+this.bins}function i7(t,i,s,e){return{xAxisMin:Math.min(i,t),xAxisMax:Math.max(i,t),yAxisMin:Math.min(e,s),yAxisMax:Math.max(e,s)}}function i8(t,i,s){return t.toValue(t.toPixels(i)+s)}function st(){let t=this.options.typeOptions,i=this.chart,s=i.inverted,e=i.xAxis[t.xAxis],o=i.yAxis[t.yAxis],n=t.background,a=s?n.height:n.width,r=s?n.width:n.height,h=t.selectType,l=s?e.left:o.top,p=s?o.top:e.left;this.startXMin=t.point.x,this.startYMin=t.point.y,i1(a)?this.startXMax=this.startXMin+a:this.startXMax=i8(e,this.startXMin,parseFloat(a)),i1(r)?this.startYMax=this.startYMin-r:this.startYMax=i8(o,this.startYMin,parseFloat(r)),"x"===h?(this.startYMin=o.toValue(l),this.startYMax=o.toValue(l+o.len)):"y"===h&&(this.startXMin=e.toValue(p),this.startXMax=e.toValue(p+e.len))}function si(){let t=this.chart.series,i=i7(this.xAxisMin,this.xAxisMax,this.yAxisMin,this.yAxisMax),s=-1/0,e=!1;return t.forEach(t=>{t.visible&&"highcharts-navigator-series"!==t.options.id&&t.points.forEach(t=>{i1(t.y)&&t.y>s&&i5(t,i)&&(s=t.y,e=!0)})}),e||(s=0),s}function ss(){let t=this.chart.series,i=i7(this.xAxisMin,this.xAxisMax,this.yAxisMin,this.yAxisMax),s=1/0,e=!1;return t.forEach(t=>{t.visible&&"highcharts-navigator-series"!==t.options.id&&t.points.forEach(t=>{i1(t.y)&&t.y<s&&i5(t,i)&&(s=t.y,e=!0)})}),e||(s=0),s}function se(t){let i=this.options.typeOptions,s=this.chart.xAxis[i.xAxis],e=this.chart.yAxis[i.yAxis],o=this.offsetX,n=this.offsetY;this.xAxisMin=i8(s,this.startXMin,o),this.xAxisMax=i8(s,this.startXMax,o),this.yAxisMin=i8(e,this.startYMin,n),this.yAxisMax=i8(e,this.startYMax,n),this.min=ss.call(this),this.max=si.call(this),this.average=i3.call(this),this.bins=i4.call(this),t&&this.resize(0,0)}function so(t,i,s,e,o){let n=this.options.typeOptions,a=n.selectType,r=this.chart.xAxis[n.xAxis],h=this.chart.yAxis[n.yAxis],l=this.startXMin,p=this.startXMax,c=this.startYMin,d=this.startYMax,u=this.offsetX,x=this.offsetY;i&&("x"===a?0===s?this.startXMin=i8(r,l,e):this.startXMax=i8(r,p,e):"y"===a?0===s?this.startYMin=i8(h,c,o):this.startYMax=i8(h,d,o):(this.startXMax=i8(r,p,e),this.startYMax=i8(h,d,o))),t&&(this.startXMin=i8(r,l,u),this.startXMax=i8(r,p,u),this.startYMin=i8(h,c,x),this.startYMax=i8(h,d,x),this.offsetX=0,this.offsetY=0),this.options.typeOptions.point={x:this.startXMin,y:this.startYMin},this.userOptions.typeOptions.point={x:this.startXMin,y:this.startYMin}}class sn extends iO{init(t,i,s){super.init(t,i,s),this.offsetX=0,this.offsetY=0,this.resizeX=0,this.resizeY=0,st.call(this),this.addValues(),this.addShapes()}setClipAxes(){this.clipXAxis=this.chart.xAxis[this.options.typeOptions.xAxis],this.clipYAxis=this.chart.yAxis[this.options.typeOptions.yAxis]}shapePointsOptions(){let t=this.options.typeOptions,i=t.xAxis,s=t.yAxis;return[{x:this.xAxisMin,y:this.yAxisMin,xAxis:i,yAxis:s},{x:this.xAxisMax,y:this.yAxisMin,xAxis:i,yAxis:s},{x:this.xAxisMax,y:this.yAxisMax,xAxis:i,yAxis:s},{x:this.xAxisMin,y:this.yAxisMax,xAxis:i,yAxis:s},{command:"Z"}]}addControlPoints(){let t=this.chart.inverted,i=this.options.controlPointOptions,s=this.options.typeOptions.selectType;iQ(this.userOptions.controlPointOptions?.style?.cursor)||("x"===s?i.style.cursor=t?"ns-resize":"ew-resize":"y"!==s||(i.style.cursor=t?"ew-resize":"ns-resize"));let e=new L(this.chart,this,this.options.controlPointOptions,0);this.controlPoints.push(e),"xy"!==s&&(e=new L(this.chart,this,this.options.controlPointOptions,1),this.controlPoints.push(e))}addValues(t){let i=this.options.typeOptions,s=i.label.formatter;se.call(this,t),i.label.enabled&&(this.labels.length>0?this.labels[0].text=s&&s.call(this)||i6.call(this):this.initLabel(i0({shape:"rect",backgroundColor:"none",color:"black",borderWidth:0,dashStyle:"Dash",overflow:"allow",align:"left",y:0,x:0,verticalAlign:"top",crop:!0,xAxis:0,yAxis:0,point:function(t){let s=t.annotation,e=t.options;return{x:s.xAxisMin,y:s.yAxisMin,xAxis:i9(i.xAxis,e.xAxis),yAxis:i9(i.yAxis,e.yAxis)}},text:s&&s.call(this)||i6.call(this)},i.label),void 0))}addShapes(){this.addCrosshairs(),this.addBackground()}addBackground(){let t=this.shapePointsOptions();void 0!==t[0].x&&this.initShape(i0({type:"path",points:t,className:"highcharts-measure-background"},this.options.typeOptions.background),2)}addCrosshairs(){let t=this.chart,i=this.options.typeOptions,s=this.options.typeOptions.point,e=t.xAxis[i.xAxis],o=t.yAxis[i.yAxis],n=t.inverted,a={point:s,type:"path"},r=e.toPixels(this.xAxisMin),h=e.toPixels(this.xAxisMax),l=o.toPixels(this.yAxisMin),p=o.toPixels(this.yAxisMax),c=[],d=[],u,x,y;n&&(y=r,r=l,l=y,y=h,h=p,p=y),i.crosshairX.enabled&&(c=[["M",r,l+(p-l)/2],["L",h,l+(p-l)/2]]),i.crosshairY.enabled&&(d=[["M",r+(h-r)/2,l],["L",r+(h-r)/2,p]]),this.shapes.length>0?(this.shapes[0].options.d=c,this.shapes[1].options.d=d):(u=i2(a,{className:"highcharts-measure-crosshair-x"},i.crosshairX),x=i2(a,{className:"highcharts-measure-crosshair-y"},i.crosshairY),this.initShape(i0({d:c},u),0),this.initShape(i0({d:d},x),1))}onDrag(t){let i=this.mouseMoveToTranslation(t),s=this.options.typeOptions.selectType,e="y"===s?0:i.x,o="x"===s?0:i.y;this.translate(e,o),this.offsetX+=e,this.offsetY+=o,this.redraw(!1,!1,!0)}resize(t,i,s,e){let o=this.shapes[2];"x"===e?0===s?(o.translatePoint(t,0,0),o.translatePoint(t,i,3)):(o.translatePoint(t,0,1),o.translatePoint(t,i,2)):"y"===e?0===s?(o.translatePoint(0,i,0),o.translatePoint(0,i,1)):(o.translatePoint(0,i,2),o.translatePoint(0,i,3)):(o.translatePoint(t,0,1),o.translatePoint(t,i,2),o.translatePoint(0,i,3)),so.call(this,!1,!0,s,t,i),this.options.typeOptions.background.height=Math.abs(this.startYMax-this.startYMin),this.options.typeOptions.background.width=Math.abs(this.startXMax-this.startXMin)}redraw(t,i,s){this.linkPoints(),this.graphic||this.render(),s&&so.call(this,!0,!1),this.clipRect&&this.clipRect.animate(this.getClipBox()),this.addValues(i),this.addCrosshairs(),this.redrawItems(this.shapes,t),this.redrawItems(this.labels,t);let e=this.options.typeOptions.background;if(e?.strokeWidth&&this.shapes[2]?.graphic){let t=e.strokeWidth/2,i=this.shapes[2],s=i.graphic.pathArray,o=s[0],n=s[1],a=s[2],r=s[3];o[1]=(o[1]||0)+t,n[1]=(n[1]||0)-t,a[1]=(a[1]||0)-t,r[1]=(r[1]||0)+t,o[2]=(o[2]||0)+t,n[2]=(n[2]||0)+t,a[2]=(a[2]||0)-t,r[2]=(r[2]||0)-t,i.graphic.attr({d:s})}this.controlPoints.forEach(t=>t.redraw())}translate(t,i){this.shapes.forEach(s=>s.translate(t,i))}}sn.prototype.defaultOptions=i2(iO.prototype.defaultOptions,{typeOptions:{selectType:"xy",xAxis:0,yAxis:0,background:{fill:"rgba(130, 170, 255, 0.4)",strokeWidth:0,stroke:void 0},crosshairX:{enabled:!0,zIndex:6,dashStyle:"Dash",markerEnd:"arrow"},crosshairY:{enabled:!0,zIndex:6,dashStyle:"Dash",markerEnd:"arrow"},label:{enabled:!0,style:{fontSize:"0.7em",color:"#666666"},formatter:void 0}},controlPointOptions:{positioner:function(t){let i=this.index,s=t.chart,e=t.options,o=e.typeOptions,n=o.selectType,a=e.controlPointOptions,r=s.inverted,h=s.xAxis[o.xAxis],l=s.yAxis[o.yAxis],p=i7(t.xAxisMin,t.xAxisMax,t.yAxisMin,t.yAxisMax),c=t.xAxisMax,d=t.yAxisMax,u,x;return"x"===n&&(d=(p.yAxisMax+p.yAxisMin)/2,0===i&&(c=t.xAxisMin)),"y"===n&&(c=p.xAxisMin+(p.xAxisMax-p.xAxisMin)/2,0===i&&(d=t.yAxisMin)),r?(u=l.toPixels(d),x=h.toPixels(c)):(u=h.toPixels(c),x=l.toPixels(d)),{x:u-a.width/2,y:x-a.height/2}},events:{drag:function(t,i){let s=this.mouseMoveToTranslation(t),e=i.options.typeOptions.selectType,o=this.index,n="y"===e?0:s.x,a="x"===e?0:s.y;i.resize(n,a,o,e),i.resizeX+=n,i.resizeY+=a,i.redraw(!1,!0)}}}}),iO.types.measure=sn;let sa=r();export{sa as default};