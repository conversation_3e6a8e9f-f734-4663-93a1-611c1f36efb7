import*as t from"../highcharts.js";import"../highcharts-more.js";var e,o,s={};s.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return s.d(e,{a:e}),e},s.d=(t,e)=>{for(var o in e)s.o(e,o)&&!s.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},s.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);let a=t.default;var r=s.n(a);s.d({},{});let{defaultOptions:i}=r(),{noop:l}=r(),{addEvent:n,extend:d,isObject:h,merge:c,relativeLength:p}=r(),g={radius:0,scope:"stack",where:void 0};function u(t,e){return h(t)||(t={radius:t||0}),c(g,e,t)}let f={optionsToObject:u},m=t.default.SeriesRegistry;var C=s.n(m);let R=t.default.Color,{parse:y}=s.n(R)(),{merge:v}=r();(e=o||(o={})).initDataClasses=function(t){let e=this.chart,o=this.legendItem=this.legendItem||{},s=this.options,a=t.dataClasses||[],r,i,l=e.options.chart.colorCount,n=0,d;this.dataClasses=i=[],o.labels=[];for(let t=0,o=a.length;t<o;++t)r=v(r=a[t]),i.push(r),(e.styledMode||!r.color)&&("category"===s.dataClassColor?(e.styledMode||(l=(d=e.options.colors||[]).length,r.color=d[n]),r.colorIndex=n,++n===l&&(n=0)):r.color=y(s.minColor).tweenTo(y(s.maxColor),o<2?.5:t/(o-1)))},e.initStops=function(){let t=this.options,e=this.stops=t.stops||[[0,t.minColor||""],[1,t.maxColor||""]];for(let t=0,o=e.length;t<o;++t)e[t].color=y(e[t][1])},e.normalizedValue=function(t){let e=this.max||0,o=this.min||0;return this.logarithmic&&(t=this.logarithmic.log2lin(t)),1-(e-t)/(e-o||1)},e.toColor=function(t,e){let o,s,a,r,i,l,n=this.dataClasses,d=this.stops;if(n){for(l=n.length;l--;)if(s=(i=n[l]).from,a=i.to,(void 0===s||t>=s)&&(void 0===a||t<=a)){r=i.color,e&&(e.dataClass=l,e.colorIndex=i.colorIndex);break}}else{for(o=this.normalizedValue(t),l=d.length;l--&&!(o>d[l][0]););s=d[l]||d[l+1],o=1-((a=d[l+1]||s)[0]-o)/(a[0]-s[0]||1),r=s.color.tweenTo(a.color,o)}return r};let x=o,{extend:A}=r(),b=function(t){A(t,x)},{gauge:M,pie:w}=C().seriesTypes,{clamp:I,extend:P,isNumber:j,merge:k,pick:O,pInt:T}=r();class S extends M{translate(){let t=this.yAxis;b(t),!t.dataClasses&&t.options.dataClasses&&t.initDataClasses(t.options),t.initStops(),M.prototype.translate.call(this)}drawPoints(){let t,e=this.yAxis,o=e.center,s=this.options,a=this.chart.renderer,r=s.overshoot,i=s.rounded&&void 0===s.borderRadius,l=j(r)?r/180*Math.PI:0;for(let r of(j(s.threshold)&&(t=e.startAngleRad+e.translate(s.threshold,void 0,void 0,void 0,!0)),this.thresholdAngleRad=O(t,e.startAngleRad),this.points))if(!r.isNull){let t=T(O(r.options.radius,s.radius,100))*o[2]/200,n=T(O(r.options.innerRadius,s.innerRadius,60))*o[2]/200,d=Math.min(e.startAngleRad,e.endAngleRad),h=Math.max(e.startAngleRad,e.endAngleRad),c=r.graphic,p=e.startAngleRad+e.translate(r.y,void 0,void 0,void 0,!0),g,u,m=e.toColor(r.y,r);"none"===m&&(m=r.color||this.color||"none"),"none"!==m&&(r.color=m),p=I(p,d-l,h+l),!1===s.wrap&&(p=I(p,d,h));let C=i?(t-n)/2/t:0,R=Math.min(p,this.thresholdAngleRad)-C,y=Math.max(p,this.thresholdAngleRad)+C;y-R>2*Math.PI&&(y=R+2*Math.PI);let v=i?"50%":0;s.borderRadius&&(v=f.optionsToObject(s.borderRadius).radius),r.shapeArgs=g={x:o[0],y:o[1],r:t,innerR:n,start:R,end:y,borderRadius:v},r.startR=t,c?(u=g.d,c.animate(P({fill:m},g)),u&&(g.d=u)):r.graphic=c=a.arc(g).attr({fill:m,"sweep-flag":0}).add(this.group),this.chart.styledMode||("square"!==s.linecap&&c.attr({"stroke-linecap":"round","stroke-linejoin":"round"}),c.attr({stroke:s.borderColor||"none","stroke-width":s.borderWidth||0})),c&&c.addClass(r.getClassName(),!0)}}animate(t){t||(this.startAngleRad=this.thresholdAngleRad,w.prototype.animate.call(this,t))}}S.defaultOptions=k(M.defaultOptions,{colorByPoint:!0,dataLabels:{y:0}}),C().registerSeriesType("solidgauge",S);let _=r();export{_ as default};