{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/sonification\n * @requires highcharts\n *\n * Sonification module\n *\n * (c) 2010-2025 Highsoft AS\n * Author: Øystein Moseng\n *\n * License: www.highcharts.com/license\n */\nimport * as __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__ from \"../highcharts.js\";\n/******/ // The require scope\n/******/ var __webpack_require__ = {};\n/******/ \n/************************************************************************/\n/******/ /* webpack/runtime/compat get default export */\n/******/ (() => {\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = (module) => {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t() => (module['default']) :\n/******/ \t\t\t() => (module);\n/******/ \t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\treturn getter;\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/define property getters */\n/******/ (() => {\n/******/ \t// define getter functions for harmony exports\n/******/ \t__webpack_require__.d = (exports, definition) => {\n/******/ \t\tfor(var key in definition) {\n/******/ \t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t}\n/******/ \t\t}\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/hasOwnProperty shorthand */\n/******/ (() => {\n/******/ \t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ })();\n/******/ \n/************************************************************************/\n\n;// external [\"../highcharts.js\",\"default\"]\nconst external_highcharts_src_js_default_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"];\nvar external_highcharts_src_js_default_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_namespaceObject);\n;// ./code/es-modules/Extensions/Sonification/Options.js\n/* *\n *\n *  (c) 2009-2025 Øystein Moseng\n *\n *  Default options for sonification.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nconst Options = {\n    /**\n     * Options for configuring sonification and audio charts. Requires the\n     * [sonification module](https://code.highcharts.com/modules/sonification.js)\n     * to be loaded.\n     *\n     * @sample  highcharts/demo/all-instruments\n     *          All predefined instruments\n     * @sample  highcharts/demo/audio-boxplot\n     *          Audio boxplots\n     * @sample  highcharts/demo/plotline-context\n     *          Context tracks\n     * @sample  highcharts/demo/sonification-music\n     *          Musical chart\n     *\n     * @since        11.0.0\n     * @requires     modules/sonification\n     * @optionparent sonification\n     */\n    sonification: {\n        /**\n         * Global tracks to add to every series.\n         *\n         * Defined as an array of either instrument or speech tracks,\n         * or a combination.\n         *\n         * @declare   Highcharts.SonificationTracksOptions\n         * @extends   sonification.defaultSpeechOptions\n         * @extends   sonification.defaultInstrumentOptions\n         * @type      {Array<*>}\n         * @apioption sonification.globalTracks\n         */\n        /**\n         * Rate mapping for speech tracks.\n         *\n         * @declare   Highcharts.SonificationTracksRateOptions\n         * @extends   sonification.defaultSpeechOptions.mapping.rate\n         * @apioption sonification.globalTracks.mapping.rate\n         */\n        /**\n         * Text mapping for speech tracks.\n         *\n         * @declare   Highcharts.SonificationTracksTextOptions\n         * @extends   sonification.defaultSpeechOptions.mapping.text\n         * @apioption sonification.globalTracks.mapping.text\n         */\n        /**\n         * Context tracks to add globally, an array of either instrument\n         * tracks, speech tracks, or a mix.\n         *\n         * Context tracks are not tied to data points, but play at a set\n         * interval - either based on time or on prop values.\n         *\n         * @sample  highcharts/demo/plotline-context\n         *          Using contexts\n         *\n         * @declare   Highcharts.SonificationContextTracksOptions\n         * @extends   sonification.globalTracks\n         * @type      {Array<*>}\n         * @apioption sonification.globalContextTracks\n         */\n        /**\n         * Set a context track to play periodically every `timeInterval`\n         * milliseconds while the sonification is playing.\n         *\n         * @sample  highcharts/demo/plotline-context\n         *          Using contexts\n         *\n         * @type      {number}\n         * @apioption sonification.globalContextTracks.timeInterval\n         */\n        /**\n         * Set a context track to play periodically every `valueInterval`\n         * units of a data property `valueProp` while the sonification is\n         * playing.\n         *\n         * For example, setting `valueProp` to `x` and `valueInterval` to 5\n         * will play the context track for every 5th X value.\n         *\n         * The context audio events will be mapped to time according to the\n         * prop value relative to the min/max values for that prop.\n         *\n         * @sample  highcharts/demo/plotline-context\n         *          Using contexts\n         *\n         * @type      {number}\n         * @apioption sonification.globalContextTracks.valueInterval\n         */\n        /**\n         * The point property to play context for when using `valueInterval`.\n         *\n         * @type      {string}\n         * @default   \"x\"\n         * @apioption sonification.globalContextTracks.valueProp\n         */\n        /**\n         * How to map context events to time when using the `valueInterval`\n         * option.\n         *\n         * @type      {\"linear\"|\"logarithmic\"}\n         * @default   \"linear\"\n         * @apioption sonification.globalContextTracks.valueMapFunction\n         */\n        /**\n         * Set up event handlers for the sonification\n         *\n         * @apioption sonification.events\n         */\n        /**\n         * Called on play.\n         *\n         * A context object is passed to the function, with properties `chart`\n         * and `timeline`.\n         *\n         * @type      {Function}\n         * @apioption sonification.events.onPlay\n         */\n        /**\n         * Called on pause, cancel, or if play is completed.\n         *\n         * A context object is passed to the function, with properties `chart`,\n         * `timeline` and `pointsPlayed`. `pointsPlayed` is an array of `Point`\n         * objects, referencing data points that were related to the audio\n         * events played.\n         *\n         * @type      {Function}\n         * @apioption sonification.events.onStop\n         */\n        /**\n         * Called when play is completed.\n         *\n         * A context object is passed to the function, with properties `chart`,\n         * `timeline` and `pointsPlayed`. `pointsPlayed` is an array of `Point`\n         * objects, referencing data points that were related to the audio\n         * events played.\n         *\n         * @type      {Function}\n         * @apioption sonification.events.onEnd\n         */\n        /**\n         * Called immediately when a play is requested.\n         *\n         * A context object is passed to the function, with properties `chart`\n         * and `timeline`.\n         *\n         * @type      {Function}\n         * @apioption sonification.events.beforePlay\n         */\n        /**\n         * Called before updating the sonification.\n         *\n         * A context object is passed to the function, with properties `chart`\n         * and `timeline`.\n         *\n         * @type      {Function}\n         * @apioption sonification.events.beforeUpdate\n         */\n        /**\n         * Called after updating the sonification.\n         *\n         * A context object is passed to the function, with properties `chart`\n         * and `timeline`.\n         *\n         * @type      {Function}\n         * @apioption sonification.events.afterUpdate\n         */\n        /**\n         * Called on the beginning of playing a series.\n         *\n         * A context object is passed to the function, with properties `series`\n         * and `timeline`.\n         *\n         * @type      {Function}\n         * @apioption sonification.events.onSeriesStart\n         */\n        /**\n         * Called when finished playing a series.\n         *\n         * A context object is passed to the function, with properties `series`\n         * and `timeline`.\n         *\n         * @type      {Function}\n         * @apioption sonification.events.onSeriesEnd\n         */\n        /**\n         * Called when attempting to play an adjacent point or series, and\n         * there is none.\n         *\n         * By default a percussive sound is played.\n         *\n         * A context object is passed to the function, with properties `chart`,\n         * `timeline`, and `attemptedNext`. `attemptedNext` is a boolean\n         * property that is `true` if the boundary hit was from trying to play\n         * the next series/point, and `false` if it was from trying to play the\n         * previous.\n         *\n         * @type      {Function}\n         * @apioption sonification.events.onBoundaryHit\n         */\n        /**\n         * Enable sonification functionality for the chart.\n         */\n        enabled: true,\n        /**\n         * The total duration of the sonification, in milliseconds.\n         */\n        duration: 6000,\n        /**\n         * The time to wait in milliseconds after each data series when playing\n         * the series one after the other.\n         *\n         * @sample highcharts/sonification/chart-earcon\n         *         Notification after series\n         *\n         * @see [order](#sonification.order)\n         */\n        afterSeriesWait: 700,\n        /**\n         * How long to wait between each recomputation of the sonification, if\n         * the chart updates rapidly. This avoids slowing down processes like\n         * panning. Given in milliseconds.\n         */\n        updateInterval: 200,\n        /**\n         * Overall/master volume for the sonification, from 0 to 1.\n         */\n        masterVolume: 0.7,\n        /**\n         * What order to play the data series in, either `sequential` where\n         * the series play individually one after the other, or `simultaneous`\n         * where the series play all at once.\n         *\n         * @sample highcharts/sonification/chart-simultaneous\n         *         Simultaneous sonification\n         *\n         * @type  {\"sequential\"|\"simultaneous\"}\n         */\n        order: 'sequential',\n        /**\n         * Show tooltip as the chart plays.\n         *\n         * Note that if multiple tracks that play at different times try to\n         * show the tooltip, it can be glitchy, so it is recommended in\n         * those cases to turn this on/off for individual tracks using the\n         * [showPlayMarker](#plotOptions.series.sonification.tracks.showPlayMarker)\n         * option.\n         *\n         * @see [showCrosshair](#sonification.showCrosshair)\n         */\n        showTooltip: true,\n        /**\n         * Show X and Y axis crosshairs (if they exist) as the chart plays.\n         *\n         * Note that if multiple tracks that play at different times try to\n         * show the crosshairs, it can be glitchy, so it is recommended in\n         * those cases to turn this on/off for individual tracks using the\n         * [showPlayMarker](#plotOptions.series.sonification.tracks.showPlayMarker)\n         * option.\n         *\n         * @see [showTooltip](#sonification.showTooltip)\n         * @see [crosshair](#xAxis.crosshair)\n         */\n        showCrosshair: true,\n        /**\n         * Options for grouping data points together when sonifying. This\n         * allows for the visual presentation to contain more points than what\n         * is being played. If not enabled, all visible / uncropped points are\n         * played.\n         *\n         * @see [series.cropThreshold](#plotOptions.series.cropThreshold)\n         */\n        pointGrouping: {\n            /**\n             * Whether or not to group points\n             */\n            enabled: true,\n            /**\n             * The size of each group in milliseconds. Audio events closer than\n             * this are grouped together.\n             */\n            groupTimespan: 15,\n            /**\n             * The grouping algorithm, deciding which points to keep when\n             * grouping a set of points together. By default `\"minmax\"` is\n             * used, which keeps both the minimum and maximum points.\n             *\n             * The other algorithms will either keep the first point in the\n             * group (time wise), last point, middle point, or both the first\n             * and the last point.\n             *\n             * The timing of the resulting point(s) is then adjusted to play\n             * evenly, regardless of its original position within the group.\n             *\n             * @type {\"minmax\"|\"first\"|\"last\"|\"middle\"|\"firstlast\"}\n             */\n            algorithm: 'minmax',\n            /**\n             * The data property for each point to compare when deciding which\n             * points to keep in the group.\n             *\n             * By default it is \"y\", which means that if the `\"minmax\"`\n             * algorithm is used, the two points the group with the lowest and\n             * highest `y` value will be kept, and the others not played.\n             */\n            prop: 'y'\n        },\n        /**\n         * Default sonification options for all instrument tracks.\n         *\n         * If specific options are also set on individual tracks or per\n         * series, those will override these options.\n         *\n         * @sample  highcharts/sonification/point-sonify\n         *          Sonify points on click\n         *\n         * @declare Highcharts.SonificationInstrumentOptions\n         */\n        defaultInstrumentOptions: {\n            /**\n             * Round pitch mapping to musical notes.\n             *\n             * If `false`, will play the exact mapped note, even if it is out\n             * of tune compared to the musical notes as defined by 440Hz\n             * standard tuning.\n             */\n            roundToMusicalNotes: true,\n            /**\n             * Type of track. Always `\"instrument\"` for instrument tracks, and\n             * `\"speech\"` for speech tracks.\n             *\n             * @declare    Highcharts.SonifcationTypeValue\n             * @type       {string}\n             * @default    instrument\n             * @validvalue [\"instrument\",\"speech\"]\n             * @apioption  sonification.defaultInstrumentOptions.type\n             */\n            /**\n             * Show play marker (tooltip and/or crosshair) for a track.\n             *\n             * @type      {boolean}\n             * @default   true\n             * @apioption sonification.defaultInstrumentOptions.showPlayMarker\n             */\n            /**\n             * Name to use for a track when exporting to MIDI.\n             * By default it uses the series name if the track is related to\n             * a series.\n             *\n             * @type      {string}\n             * @apioption sonification.defaultInstrumentOptions.midiName\n             */\n            /**\n             * Options for point grouping, specifically for instrument tracks.\n             *\n             * @declare   Highcharts.SonificationInstrumentPointGroupingOptions\n             * @extends   sonification.pointGrouping\n             * @apioption sonification.defaultInstrumentOptions.pointGrouping\n             */\n            /**\n             * Define a condition for when a track should be active and not.\n             *\n             * Can either be a function callback or a configuration object.\n             *\n             * If a function is used, it should return a `boolean` for whether\n             * or not the track should be active. The function is called for\n             * each audio event, and receives a parameter object with `time`,\n             * and potentially `point` and `value` properties depending on the\n             * track. `point` is available if the audio event is related to a\n             * data point. `value` is available if the track is used as a\n             * context track, and `valueInterval` is used.\n             *\n             * @sample highcharts/sonification/mapping-zones\n             *         Mapping zones\n             *\n             * @declare   Highcharts.SonificationInstrumentActiveWhenOptions\n             * @type      {Function|*}\n             * @apioption sonification.defaultInstrumentOptions.activeWhen\n             */\n            /**\n             * Track is only active when `prop` is above or at this value.\n             *\n             * @type      {number}\n             * @apioption sonification.defaultInstrumentOptions.activeWhen.min\n             */\n            /**\n             * Track is only active when `prop` is below or at this value.\n             *\n             * @type      {number}\n             * @apioption sonification.defaultInstrumentOptions.activeWhen.max\n             */\n            /**\n             * Track is only active when `prop` was below, and is now at or\n             * above this value.\n             *\n             * If both `crossingUp` and `crossingDown` are defined, the track\n             * is active if either condition is met.\n             *\n             * @type      {number}\n             * @apioption sonification.defaultInstrumentOptions.activeWhen.crossingUp\n             */\n            /**\n             * Track is only active when `prop` was above, and is now at or\n             * below this value.\n             *\n             * If both `crossingUp` and `crossingDown` are defined, the track\n             * is active if either condition is met.\n             *\n             * @type      {number}\n             * @apioption sonification.defaultInstrumentOptions.activeWhen.crossingDown\n             */\n            /**\n             * The point property to compare, for example `y` or `x`.\n             *\n             * @type      {string}\n             * @apioption sonification.defaultInstrumentOptions.activeWhen.prop\n             */\n            /**\n             * Instrument to use for playing.\n             *\n             * Can either be a string referencing a synth preset, or it can be\n             * a synth configuration object.\n             *\n             * @sample  highcharts/demo/all-instruments\n             *          Overview of available presets\n             * @sample  highcharts/sonification/custom-instrument\n             *          Custom instrument\n             *\n             * @type {string|Highcharts.SynthPatchOptionsObject}\n             */\n            instrument: 'piano',\n            /**\n             * Mapping options for the audio parameters.\n             *\n             * All parameters can be either:\n             *  - A string, referencing a point property to map to.\n             *  - A number, setting the value of the audio parameter directly.\n             *  - A callback function, returning the value programmatically.\n             *  - An object defining detailed configuration of the mapping.\n             *\n             * If a function is used, it should return the desired value for\n             * the audio parameter. The function is called for each audio event\n             * to be played, and receives a context object parameter with\n             * `time`, and potentially `point` and `value` depending on the\n             * track. `point` is available if the audio event is related to a\n             * data point, and `value` is available if the track is used for a\n             * context track using `valueInterval`.\n             *\n             * @sample  highcharts/sonification/mapping-overview\n             *          Overview of common mapping parameters\n             * @sample  highcharts/sonification/pitch-mapping\n             *          Various types of mapping used\n             * @sample  highcharts/sonification/polarity-invert\n             *          Inverted mapping to property\n             * @sample  highcharts/sonification/log-mapping\n             *          Logarithmic mapping to property\n             *\n             * @declare Highcharts.SonificationInstrumentMappingOptions\n             */\n            mapping: {\n                /**\n                 * The volume of notes, from 0 to 1.\n                 *\n                 * @declare   Highcharts.SonificationInstrumentVolumeOptions\n                 * @extends   sonification.defaultInstrumentOptions.mapping.time\n                 * @default   1\n                 * @apioption sonification.defaultInstrumentOptions.mapping.volume\n                 */\n                /**\n                 * Frequency in Hertz of notes. Overrides pitch mapping if set.\n                 *\n                 * @declare   Highcharts.SonificationInstrumentFrequencyOptions\n                 * @extends   sonification.defaultInstrumentOptions.mapping.time\n                 * @apioption sonification.defaultInstrumentOptions.mapping.frequency\n                 */\n                /**\n                 * Milliseconds to wait before playing, comes in addition to\n                 * the time determined by the `time` mapping.\n                 *\n                 * Can also be negative to play before the mapped time.\n                 *\n                 * @declare   Highcharts.SonificationInstrumentPlayDelayOptions\n                 * @extends   sonification.defaultInstrumentOptions.mapping.time\n                 * @apioption sonification.defaultInstrumentOptions.mapping.playDelay\n                 */\n                /**\n                 * Mapping options for tremolo effects.\n                 *\n                 * Tremolo is repeated changes of volume over time.\n                 *\n                 * @declare   Highcharts.SonificationInstrumentTremoloOptions\n                 * @apioption sonification.defaultInstrumentOptions.mapping.tremolo\n                 */\n                /**\n                 * Map to tremolo depth, from 0 to 1.\n                 *\n                 * This determines the intensity of the tremolo effect, how\n                 * much the volume changes.\n                 *\n                 * @declare   Highcharts.SonificationInstrumentTremoloDepthOptions\n                 * @extends   sonification.defaultInstrumentOptions.mapping.time\n                 * @apioption sonification.defaultInstrumentOptions.mapping.tremolo.depth\n                 */\n                /**\n                 * Map to tremolo speed, from 0 to 1.\n                 *\n                 * This determines the speed of the tremolo effect, how fast\n                 * the volume changes.\n                 *\n                 * @declare   Highcharts.SonificationInstrumentTremoloSpeedOptions\n                 * @extends   sonification.defaultInstrumentOptions.mapping.time\n                 * @apioption sonification.defaultInstrumentOptions.mapping.tremolo.speed\n                 */\n                /**\n                 * Mapping options for the lowpass filter.\n                 *\n                 * A lowpass filter lets low frequencies through, but stops high\n                 * frequencies, making the sound more dull.\n                 *\n                 * @declare   Highcharts.SonificationInstrumentLowpassOptions\n                 * @apioption sonification.defaultInstrumentOptions.mapping.lowpass\n                 */\n                /**\n                 * Map to filter frequency in Hertz from 1 to 20,000Hz.\n                 *\n                 * @declare   Highcharts.SonificationInstrumentLowpassFrequencyOptions\n                 * @extends   sonification.defaultInstrumentOptions.mapping.time\n                 * @apioption sonification.defaultInstrumentOptions.mapping.lowpass.frequency\n                 */\n                /**\n                 * Map to filter resonance in dB. Can be negative to cause a\n                 * dip, or positive to cause a bump.\n                 *\n                 * @declare   Highcharts.SonificationInstrumentLowpassResonanceOptions\n                 * @extends   sonification.defaultInstrumentOptions.mapping.time\n                 * @apioption sonification.defaultInstrumentOptions.mapping.lowpass.resonance\n                 */\n                /**\n                 * Mapping options for the highpass filter.\n                 *\n                 * A highpass filter lets high frequencies through, but stops\n                 * low frequencies, making the sound thinner.\n                 *\n                 * @declare   Highcharts.SonificationInstrumentHighpassOptions\n                 * @extends   sonification.defaultInstrumentOptions.mapping.lowpass\n                 * @apioption sonification.defaultInstrumentOptions.mapping.highpass\n                 */\n                /**\n                 * Time mapping determines what time each point plays. It is\n                 * defined as an offset in milliseconds, where 0 means it\n                 * plays immediately when the chart is sonified.\n                 *\n                 * By default time is mapped to `x`, meaning points with the\n                 * lowest `x` value plays first, and points with the highest\n                 * `x` value plays last.\n                 *\n                 * Can be set to a fixed value, a prop to map to, a function,\n                 * or a mapping object.\n                 *\n                 * @sample  highcharts/sonification/point-play-time\n                 *          Play points in order of Y value\n                 *\n                 * @declare Highcharts.SonificationInstrumentTimeOptions\n                 * @type    {string|number|Function|*}\n                 * @default \"x\"\n                 */\n                time: 'x',\n                /**\n                 * A point property to map the mapping parameter to.\n                 *\n                 * A negative sign `-` can be placed before the property name\n                 * to make mapping inverted.\n                 *\n                 * @sample  highcharts/sonification/polarity-invert\n                 *          Inverted mapping to property\n                 *\n                 * @type      {string}\n                 * @apioption sonification.defaultInstrumentOptions.mapping.time.mapTo\n                 */\n                /**\n                 * The minimum value for the audio parameter. This is the\n                 * lowest value the audio parameter will be mapped to.\n                 *\n                 * @type      {number}\n                 * @apioption sonification.defaultInstrumentOptions.mapping.time.min\n                 */\n                /**\n                 * The maximum value for the audio parameter. This is the\n                 * highest value the audio parameter will be mapped to.\n                 *\n                 * @type      {number}\n                 * @apioption sonification.defaultInstrumentOptions.mapping.time.max\n                 */\n                /**\n                 * What data values to map the parameter within.\n                 *\n                 * Mapping within `\"series\"` will make the lowest value point\n                 * in the series map to the min audio parameter value, and the\n                 * highest value will map to the max audio parameter.\n                 *\n                 * Mapping within `\"chart\"` will make the lowest value point in\n                 * the whole chart map to the min audio parameter value, and\n                 * the highest value in the whole chart will map to the max\n                 * audio parameter.\n                 *\n                 * You can also map within the X or Y axis of each series.\n                 *\n                 * @sample highcharts/sonification/mapping-within\n                 *         Mapping within demonstrated\n                 *\n                 * @type      {\"chart\"|\"series\"|\"xAxis\"|\"yAxis\"}\n                 * @apioption sonification.defaultInstrumentOptions.mapping.time.within\n                 */\n                /**\n                 * How to perform the mapping.\n                 * @sample highcharts/sonification/log-mapping\n                 *         Logarithmic mapping to property\n                 *\n                 * @type      {\"linear\"|\"logarithmic\"}\n                 * @apioption sonification.defaultInstrumentOptions.mapping.time.mapFunction\n                 */\n                /**\n                 * A fixed value to use for the prop when mapping.\n                 *\n                 * For example, if mapping to `y`, setting value to `4` will\n                 * map as if all points had a y value of 4.\n                 *\n                 * @sample highcharts/demo/plotline-context\n                 *         Map to fixed y value\n                 *\n                 * @type      {number}\n                 * @apioption sonification.defaultInstrumentOptions.mapping.time.value\n                 */\n                /**\n                 * Pan refers to the stereo panning position of the sound.\n                 * It is defined from -1 (left) to 1 (right).\n                 *\n                 * By default it is mapped to `x`, making the sound move from\n                 * left to right as the chart plays.\n                 *\n                 * Can be set to a fixed value, a prop to map to, a function,\n                 * or a mapping object.\n                 *\n                 * @extends sonification.defaultInstrumentOptions.mapping.time\n                 * @default \"x\"\n                 */\n                pan: 'x',\n                /**\n                 * Note duration determines for how long a note plays, in\n                 * milliseconds.\n                 *\n                 * It only affects instruments that are able to play\n                 * continuous sustained notes. Examples of these instruments\n                 * from the presets include `flute`, `saxophone`, `trumpet`,\n                 * `sawsynth`, `wobble`, `basic1`, `basic2`, `sine`,\n                 * `sineGlide`, `triangle`, `square`, `sawtooth`, `noise`,\n                 * `filteredNoise`, and `wind`.\n                 *\n                 * Can be set to a fixed value, a prop to map to, a function,\n                 * or a mapping object.\n                 *\n                 * @extends sonification.defaultInstrumentOptions.mapping.time\n                 * @default 200\n                 */\n                noteDuration: 200,\n                /**\n                 * Musical pitch refers to how high or low notes are played.\n                 *\n                 * By default it is mapped to `y` so low `y` values are played\n                 * with a lower pitch, and high values are played with a higher\n                 * pitch.\n                 *\n                 * Pitch mapping has a few extra features compared to other\n                 * audio parameters.\n                 *\n                 * Firstly, it accepts not only number values, but also string\n                 * values denoting note names. These are given in the form\n                 * `<note><octave>`, for example `\"c6\"` or `\"F#2\"`.\n                 *\n                 * Secondly, it is possible to map pitch to an array of notes.\n                 * In this case, the `[gapBetweenNotes](#sonification.defaultInstrumentOptions.mapping.gapBetweenNotes)`\n                 * mapping determines the delay between these notes.\n                 *\n                 * Thirdly, it is possible to define a musical scale to follow\n                 * when mapping.\n                 *\n                 * Can be set to a fixed value, an array, a prop to map to, a\n                 * function, or a mapping object.\n                 *\n                 * @sample  highcharts/sonification/pitch-mapping\n                 *          Various types of mapping used\n                 * @sample  highcharts/sonification/polarity-invert\n                 *          Inverted mapping to property\n                 * @sample  highcharts/sonification/log-mapping\n                 *          Logarithmic mapping to property\n                 *\n                 * @declare Highcharts.SonificationInstrumentPitchOptions\n                 * @extends sonification.defaultInstrumentOptions.mapping.time\n                 * @type    {string|number|Function|Array<string|number>|*}\n                 */\n                pitch: {\n                    mapTo: 'y',\n                    min: 'c2',\n                    max: 'c6',\n                    within: 'yAxis'\n                },\n                /**\n                 * Map pitches to a musical scale. The scale is defined as an\n                 * array of semitone offsets from the root note.\n                 *\n                 * @sample  highcharts/sonification/all-scales\n                 *          Predefined scale presets\n                 *\n                 * @type      {Array<number>}\n                 * @apioption sonification.defaultInstrumentOptions.mapping.pitch.scale\n                 */\n                /**\n                 * Gap in milliseconds between notes if pitch is mapped to an\n                 * array of notes.\n                 *\n                 * Can be set to a fixed value, a prop to map to, a function,\n                 * or a mapping object.\n                 *\n                 * @sample  maps/demo/audio-map\n                 *          Mapping to gap between notes\n                 *\n                 * @extends sonification.defaultInstrumentOptions.mapping.time\n                 * @default 100\n                 */\n                gapBetweenNotes: 100\n            }\n        },\n        /**\n         * Default sonification options for all speech tracks.\n         *\n         * If specific options are also set on individual tracks or per\n         * series, those will override these options.\n         *\n         * @sample  highcharts/sonification/speak-values\n         *          Speak values\n         *\n         * @declare   Highcharts.SonificationSpeechOptions\n         * @extends   sonification.defaultInstrumentOptions\n         * @excluding roundToMusicalNotes, midiName, instrument\n         */\n        defaultSpeechOptions: {\n            /**\n             * Type of track. Always `\"instrument\"` for instrument tracks, and\n             * `\"speech\"` for speech tracks.\n             *\n             * @declare    Highcharts.SonifcationTypeValue\n             * @type       {string}\n             * @default    speech\n             * @validvalue [\"instrument\",\"speech\"]\n             * @apioption  sonification.defaultSpeechOptions.type\n             */\n            /**\n             * Name of the voice synthesis to prefer for speech tracks.\n             *\n             * If not available, falls back to the default voice for the\n             * selected language.\n             *\n             * Different platforms provide different voices for web speech\n             * synthesis.\n             *\n             * @type      {string}\n             * @apioption sonification.defaultSpeechOptions.preferredVoice\n             */\n            /**\n             * The language to speak in for speech tracks, as an IETF BCP 47\n             * language tag.\n             *\n             * @sample  maps/demo/audio-map\n             *          French language speech\n             */\n            language: 'en-US',\n            /**\n             * Mapping configuration for the speech/audio parameters.\n             *\n             * All parameters except `text` can be either:\n             *  - A string, referencing a point property to map to.\n             *  - A number, setting the value of the speech parameter directly.\n             *  - A callback function, returning the value programmatically.\n             *  - An object defining detailed configuration of the mapping.\n             *\n             * If a function is used, it should return the desired value for\n             * the speech parameter. The function is called for each speech\n             * event to be played, and receives a context object parameter with\n             * `time`, and potentially `point` and `value` depending on the\n             * track. `point` is available if the audio event is related to a\n             * data point, and `value` is available if the track is used for a\n             * context track using `valueInterval`.\n             *\n             * @declare   Highcharts.SonificationSpeechMappingOptions\n             * @extends   sonification.defaultInstrumentOptions.mapping\n             * @excluding frequency, gapBetweenNotes, highpass, lowpass,\n             *            tremolo, noteDuration, pan\n             * @apioption sonification.defaultSpeechOptions.mapping\n             */\n            mapping: {\n                /**\n                 * Milliseconds to wait before playing, comes in addition to\n                 * the time determined by the `time` mapping.\n                 *\n                 * Can also be negative to play before the mapped time.\n                 *\n                 * @declare   Highcharts.SonificationSpeechPlayDelayOptions\n                 * @extends   sonification.defaultInstrumentOptions.mapping.time\n                 * @apioption sonification.defaultSpeechOptions.mapping.playDelay\n                 */\n                /**\n                 * Speech pitch (how high/low the voice is) multiplier.\n                 * @sample  highcharts/sonification/speak-values\n                 *          Speak values\n                 *\n                 * @declare   Highcharts.SonificationSpeechPitchOptions\n                 * @extends   sonification.defaultInstrumentOptions.mapping.time\n                 * @excluding scale\n                 * @type      {string|number|Function|*}\n                 * @default   1\n                 * @apioption sonification.defaultSpeechOptions.mapping.pitch\n                 */\n                /**\n                 * @default   undefined\n                 * @apioption sonification.defaultSpeechOptions.mapping.pitch.mapTo\n                 */\n                /**\n                 * @default   undefined\n                 * @apioption sonification.defaultSpeechOptions.mapping.pitch.min\n                 */\n                /**\n                 * @default   undefined\n                 * @apioption sonification.defaultSpeechOptions.mapping.pitch.max\n                 */\n                /**\n                 * @default   undefined\n                 * @apioption sonification.defaultSpeechOptions.mapping.pitch.within\n                 */\n                /**\n                 * The text to announce for speech tracks. Can either be a\n                 * format string or a function.\n                 *\n                 * If it is a function, it should return the format string to\n                 * announce. The function is called for each audio event, and\n                 * receives a parameter object with `time`, and potentially\n                 * `point` and `value` properties depending on the track.\n                 * `point` is available if the audio event is related to a data\n                 * point. `value` is available if the track is used as a\n                 * context track, and `valueInterval` is used.\n                 *\n                 * If it is a format string, in addition to normal string\n                 * content, format values can be accessed using bracket\n                 * notation. For example `\"Value is {point.y}%\"`.\n                 *\n                 * `time`, `point` and `value` are available to the format\n                 * strings similarly to with functions. Nested properties can\n                 * be accessed with dot notation, for example\n                 * `\"Density: {point.options.custom.density}\"`\n                 *\n                 * @sample highcharts/sonification/speak-values\n                 *         Speak values\n                 *\n                 * @type      {string|Function}\n                 * @apioption sonification.defaultSpeechOptions.mapping.text\n                 */\n                /**\n                 * @extends sonification.defaultInstrumentOptions.mapping.time\n                 * @default \"x\"\n                 */\n                time: 'x',\n                /**\n                 * Speech rate (speed) multiplier.\n                 * @extends sonification.defaultInstrumentOptions.mapping.time\n                 * @default 1.3\n                 */\n                rate: 1.3,\n                /**\n                 * Volume of the speech announcement.\n                 * @extends sonification.defaultInstrumentOptions.mapping.volume\n                 * @default 0.4\n                 */\n                volume: 0.4\n            },\n            pointGrouping: {\n                algorithm: 'last'\n            }\n        }\n    },\n    exporting: {\n        menuItemDefinitions: {\n            downloadMIDI: {\n                textKey: 'downloadMIDI',\n                onclick: function () {\n                    if (this.sonification) {\n                        this.sonification.downloadMIDI();\n                    }\n                }\n            },\n            playAsSound: {\n                textKey: 'playAsSound',\n                onclick: function () {\n                    const s = this.sonification;\n                    if (s && s.isPlaying()) {\n                        s.cancel();\n                    }\n                    else {\n                        this.sonify();\n                    }\n                }\n            }\n        }\n    },\n    /**\n     * @optionparent lang\n     * @private\n     */\n    lang: {\n        /**\n         * The text for the MIDI download menu item in the export menu.\n         * @requires modules/sonification\n         * @since 11.0.0\n         */\n        downloadMIDI: 'Download MIDI',\n        /**\n         * The text for the Play as sound menu item in the export menu.\n         * @requires modules/sonification\n         * @since 11.0.0\n         */\n        playAsSound: 'Play as sound'\n    }\n};\n/* harmony default export */ const Sonification_Options = (Options);\n/* *\n *\n *  API declarations\n *\n * */\n/**\n * Sonification/audio chart options for a series.\n *\n * @declare    Highcharts.SeriesSonificationOptions\n * @since      11.0.0\n * @requires   modules/sonification\n * @apioption  plotOptions.series.sonification\n */\n/**\n * Whether or not sonification is enabled for this series.\n *\n * @type       {boolean}\n * @default    true\n * @apioption  plotOptions.series.sonification.enabled\n */\n/**\n * Context tracks for this series. Context tracks are tracks that are not\n * tied to data points.\n *\n * Given as an array of instrument tracks, speech tracks, or a mix of both.\n *\n * @declare    Highcharts.SeriesSonificationContextTracksOptions\n * @type       {Array<*>}\n * @extends    sonification.globalContextTracks\n * @apioption  plotOptions.series.sonification.contextTracks\n */\n/**\n * Tracks for this series.\n *\n * Given as an array of instrument tracks, speech tracks, or a mix of both.\n *\n * @declare    Highcharts.SeriesSonificationTracksOptions\n * @type       {Array<*>}\n * @extends    sonification.globalTracks\n * @apioption  plotOptions.series.sonification.tracks\n */\n/**\n * Default options for all this series' instrument tracks.\n *\n * @declare    Highcharts.SeriesSonificationInstrumentOptions\n * @extends    sonification.defaultInstrumentOptions\n * @apioption  plotOptions.series.sonification.defaultInstrumentOptions\n */\n/**\n * Default options for all this series' speech tracks.\n *\n * @declare    Highcharts.SeriesSonificationSpeechOptions\n * @extends    sonification.defaultSpeechOptions\n * @apioption  plotOptions.series.sonification.defaultSpeechOptions\n */\n/**\n * Sonification point grouping options for this series.\n *\n * @declare    Highcharts.SeriesSonificationPointGroupingOptions\n * @extends    sonification.pointGrouping\n * @apioption  plotOptions.series.sonification.pointGrouping\n */\n/**\n * Event context object sent to sonification chart events.\n * @requires  modules/sonification\n * @interface Highcharts.SonificationChartEventCallbackContext\n */ /**\n* The relevant chart\n* @name Highcharts.SonificationChartEventCallbackContext#chart\n* @type {Highcharts.Chart|undefined}\n*/ /**\n* The points that were played, if any\n* @name Highcharts.SonificationChartEventCallbackContext#pointsPlayed\n* @type {Array<Highcharts.Point>|undefined}\n*/ /**\n* The playing timeline object with advanced and internal content\n* @name Highcharts.SonificationChartEventCallbackContext#timeline\n* @type {object|undefined}\n*/\n/**\n * Event context object sent to sonification series events.\n * @requires  modules/sonification\n * @interface Highcharts.SonificationSeriesEventCallbackContext\n */ /**\n* The relevant series\n* @name Highcharts.SonificationSeriesEventCallbackContext#series\n* @type {Highcharts.Series|undefined}\n*/ /**\n* The playing timeline object with advanced and internal content\n* @name Highcharts.SonificationSeriesEventCallbackContext#timeline\n* @type {object|undefined}\n*/\n/**\n * Callback function for sonification events on chart.\n * @callback Highcharts.SonificationChartEventCallback\n * @param {Highcharts.SonificationChartEventCallbackContext} e Sonification chart event context\n */\n/**\n * Callback function for sonification events on series.\n * @callback Highcharts.SonificationSeriesEventCallback\n * @param {Highcharts.SonificationSeriesEventCallbackContext} e Sonification series event context\n */\n(''); // Keep above doclets in JS file\n\n;// ./code/es-modules/Extensions/Sonification/SynthPatch.js\n/* *\n *\n *  (c) 2009-2025 Øystein Moseng\n *\n *  Class representing a Synth Patch, used by Instruments in the\n *  sonification.js module.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { clamp, defined, pick } = (external_highcharts_src_js_default_default());\n/**\n * Get the multiplier value from a pitch tracked multiplier. The parameter\n * specifies the multiplier at ca 3200Hz. It is 1 at ca 50Hz. In between\n * it is mapped logarithmically.\n * @private\n * @param {number} multiplier The multiplier to track.\n * @param {number} freq The current frequency.\n */\nfunction getPitchTrackedMultiplierVal(multiplier, freq) {\n    const a = 0.2414 * multiplier - 0.2414, b = (3.5 - 1.7 * multiplier) / 1.8;\n    return a * Math.log(freq) + b;\n}\n/**\n * Schedule a mini ramp to volume at time - avoid clicks/pops.\n * @private\n * @param {Object} gainNode The gain node to schedule for.\n * @param {number} time The time in seconds to start ramp.\n * @param {number} vol The volume to ramp to.\n */\nfunction miniRampToVolAtTime(gainNode, time, vol) {\n    gainNode.gain.cancelScheduledValues(time);\n    gainNode.gain.setTargetAtTime(vol, time, SynthPatch.stopRampTime / 4);\n    gainNode.gain.setValueAtTime(vol, time + SynthPatch.stopRampTime);\n}\n/**\n * Schedule a gain envelope for a gain node.\n * @private\n * @param {Array<Object>} envelope The envelope to schedule.\n * @param {string} type Type of envelope, attack or release.\n * @param {number} time At what time (in seconds) to start envelope.\n * @param {Object} gainNode The gain node to schedule on.\n * @param {number} [volumeMultiplier] Volume multiplier for the envelope.\n */\nfunction scheduleGainEnvelope(envelope, type, time, gainNode, volumeMultiplier = 1) {\n    const isAtk = type === 'attack', gain = gainNode.gain;\n    gain.cancelScheduledValues(time);\n    if (!envelope.length) {\n        miniRampToVolAtTime(gainNode, time, isAtk ? volumeMultiplier : 0);\n        return;\n    }\n    if (envelope[0].t > 1) {\n        envelope.unshift({ t: 0, vol: isAtk ? 0 : 1 });\n    }\n    envelope.forEach((ep, ix) => {\n        const prev = envelope[ix - 1], delta = prev ? (ep.t - prev.t) / 1000 : 0, startTime = time + (prev ? prev.t / 1000 + SynthPatch.stopRampTime : 0);\n        gain.setTargetAtTime(ep.vol * volumeMultiplier, startTime, Math.max(delta, SynthPatch.stopRampTime) / 2);\n    });\n}\n/**\n * Internal class used by Oscillator, representing a Pulse Oscillator node.\n * Combines two sawtooth oscillators to create a pulse by phase inverting and\n * delaying one of them.\n * @class\n * @private\n */\nclass PulseOscNode {\n    constructor(context, options) {\n        this.pulseWidth = Math.min(Math.max(0, options.pulseWidth || 0.5));\n        const makeOsc = () => new OscillatorNode(context, {\n            type: 'sawtooth',\n            detune: options.detune,\n            frequency: Math.max(1, options.frequency || 350)\n        });\n        this.sawOscA = makeOsc();\n        this.sawOscB = makeOsc();\n        this.phaseInverter = new GainNode(context, { gain: -1 });\n        this.masterGain = new GainNode(context);\n        this.delayNode = new DelayNode(context, {\n            delayTime: this.pulseWidth / this.sawOscA.frequency.value\n        });\n        this.sawOscA.connect(this.masterGain);\n        this.sawOscB.connect(this.phaseInverter);\n        this.phaseInverter.connect(this.delayNode);\n        this.delayNode.connect(this.masterGain);\n    }\n    connect(destination) {\n        this.masterGain.connect(destination);\n    }\n    // Polymorph with normal osc.frequency API\n    getFrequencyFacade() {\n        const pulse = this;\n        return {\n            cancelScheduledValues(fromTime) {\n                pulse.sawOscA.frequency.cancelScheduledValues(fromTime);\n                pulse.sawOscB.frequency.cancelScheduledValues(fromTime);\n                pulse.delayNode.delayTime.cancelScheduledValues(fromTime);\n                return pulse.sawOscA.frequency;\n            },\n            setValueAtTime(frequency, time) {\n                this.cancelScheduledValues(time);\n                pulse.sawOscA.frequency.setValueAtTime(frequency, time);\n                pulse.sawOscB.frequency.setValueAtTime(frequency, time);\n                pulse.delayNode.delayTime.setValueAtTime(Math.round(10000 * pulse.pulseWidth / frequency) / 10000, time);\n                return pulse.sawOscA.frequency;\n            },\n            setTargetAtTime(frequency, time, timeConstant) {\n                this.cancelScheduledValues(time);\n                pulse.sawOscA.frequency\n                    .setTargetAtTime(frequency, time, timeConstant);\n                pulse.sawOscB.frequency\n                    .setTargetAtTime(frequency, time, timeConstant);\n                pulse.delayNode.delayTime.setTargetAtTime(Math.round(10000 * pulse.pulseWidth / frequency) / 10000, time, timeConstant);\n                return pulse.sawOscA.frequency;\n            }\n        };\n    }\n    getPWMTarget() {\n        return this.delayNode.delayTime;\n    }\n    start() {\n        this.sawOscA.start();\n        this.sawOscB.start();\n    }\n    stop(time) {\n        this.sawOscA.stop(time);\n        this.sawOscB.stop(time);\n    }\n}\n/**\n * Internal class used by SynthPatch\n * @class\n * @private\n */\nclass Oscillator {\n    constructor(audioContext, options, destination) {\n        this.audioContext = audioContext;\n        this.options = options;\n        this.fmOscillatorIx = options.fmOscillator;\n        this.vmOscillatorIx = options.vmOscillator;\n        this.createSoundSource();\n        this.createGain();\n        this.createFilters();\n        this.createVolTracking();\n        if (destination) {\n            this.connect(destination);\n        }\n    }\n    // Connect the node tree from destination down to oscillator,\n    // depending on which nodes exist. Done automatically unless\n    // no destination was passed to constructor.\n    connect(destination) {\n        [\n            this.lowpassNode,\n            this.highpassNode,\n            this.volTrackingNode,\n            this.vmNode,\n            this.gainNode,\n            this.whiteNoise,\n            this.pulseNode,\n            this.oscNode\n        ].reduce((prev, cur) => (cur ?\n            (cur.connect(prev), cur) :\n            prev), destination);\n    }\n    start() {\n        if (this.oscNode) {\n            this.oscNode.start();\n        }\n        if (this.whiteNoise) {\n            this.whiteNoise.start();\n        }\n        if (this.pulseNode) {\n            this.pulseNode.start();\n        }\n    }\n    stopAtTime(time) {\n        if (this.oscNode) {\n            this.oscNode.stop(time);\n        }\n        if (this.whiteNoise) {\n            this.whiteNoise.stop(time);\n        }\n        if (this.pulseNode) {\n            this.pulseNode.stop(time);\n        }\n    }\n    setFreqAtTime(time, frequency, glideDuration = 0) {\n        const opts = this.options, f = clamp(pick(opts.fixedFrequency, frequency) *\n            (opts.freqMultiplier || 1), 0, 21000), oscTarget = this.getOscTarget(), timeConstant = glideDuration / 5000;\n        if (oscTarget) {\n            oscTarget.cancelScheduledValues(time);\n            if (glideDuration && time - (this.lastUpdateTime || -1) > 0.01) {\n                oscTarget.setTargetAtTime(f, time, timeConstant);\n                oscTarget.setValueAtTime(f, time + timeConstant);\n            }\n            else {\n                oscTarget.setValueAtTime(f, time);\n            }\n        }\n        this.scheduleVolTrackingChange(f, time, glideDuration);\n        this.scheduleFilterTrackingChange(f, time, glideDuration);\n        this.lastUpdateTime = time;\n    }\n    // Get target for FM synthesis if another oscillator wants to modulate.\n    // Pulse nodes don't do FM, but do PWM instead.\n    getFMTarget() {\n        return this.oscNode && this.oscNode.detune ||\n            this.whiteNoise && this.whiteNoise.detune ||\n            this.pulseNode && this.pulseNode.getPWMTarget();\n    }\n    // Get target for volume modulation if another oscillator wants to modulate.\n    getVMTarget() {\n        return this.vmNode && this.vmNode.gain;\n    }\n    // Schedule one of the oscillator envelopes at a specified time in\n    // seconds (in AudioContext timespace).\n    runEnvelopeAtTime(type, time) {\n        if (!this.gainNode) {\n            return;\n        }\n        const env = (type === 'attack' ? this.options.attackEnvelope :\n            this.options.releaseEnvelope) || [];\n        scheduleGainEnvelope(env, type, time, this.gainNode, this.options.volume);\n    }\n    // Cancel any envelopes or frequency changes currently scheduled\n    cancelScheduled() {\n        if (this.gainNode) {\n            this.gainNode.gain\n                .cancelScheduledValues(this.audioContext.currentTime);\n        }\n        const oscTarget = this.getOscTarget();\n        if (oscTarget) {\n            oscTarget.cancelScheduledValues(0);\n        }\n        if (this.lowpassNode) {\n            this.lowpassNode.frequency.cancelScheduledValues(0);\n        }\n        if (this.highpassNode) {\n            this.highpassNode.frequency.cancelScheduledValues(0);\n        }\n        if (this.volTrackingNode) {\n            this.volTrackingNode.gain.cancelScheduledValues(0);\n        }\n    }\n    // Set the pitch dependent volume to fit some frequency at some time\n    scheduleVolTrackingChange(frequency, time, glideDuration) {\n        if (this.volTrackingNode) {\n            const v = getPitchTrackedMultiplierVal(this.options.volumePitchTrackingMultiplier || 1, frequency), rampTime = glideDuration ? glideDuration / 1000 :\n                SynthPatch.stopRampTime;\n            this.volTrackingNode.gain.cancelScheduledValues(time);\n            this.volTrackingNode.gain.setTargetAtTime(v, time, rampTime / 5);\n            this.volTrackingNode.gain.setValueAtTime(v, time + rampTime);\n        }\n    }\n    // Set the pitch dependent filter frequency to fit frequency at some time\n    scheduleFilterTrackingChange(frequency, time, glideDuration) {\n        const opts = this.options, rampTime = glideDuration ? glideDuration / 1000 :\n            SynthPatch.stopRampTime, scheduleFilterTarget = (filterNode, filterOptions) => {\n            const multiplier = getPitchTrackedMultiplierVal(filterOptions.frequencyPitchTrackingMultiplier || 1, frequency), f = clamp((filterOptions.frequency || 1000) * multiplier, 0, 21000);\n            filterNode.frequency.cancelScheduledValues(time);\n            filterNode.frequency.setTargetAtTime(f, time, rampTime / 5);\n            filterNode.frequency.setValueAtTime(f, time + rampTime);\n        };\n        if (this.lowpassNode && opts.lowpass) {\n            scheduleFilterTarget(this.lowpassNode, opts.lowpass);\n        }\n        if (this.highpassNode && opts.highpass) {\n            scheduleFilterTarget(this.highpassNode, opts.highpass);\n        }\n    }\n    createGain() {\n        const opts = this.options, needsGainNode = defined(opts.volume) ||\n            opts.attackEnvelope && opts.attackEnvelope.length ||\n            opts.releaseEnvelope && opts.releaseEnvelope.length;\n        if (needsGainNode) {\n            this.gainNode = new GainNode(this.audioContext, {\n                gain: pick(opts.volume, 1)\n            });\n        }\n        // We always need VM gain, so make that\n        this.vmNode = new GainNode(this.audioContext);\n    }\n    // Create the oscillator or audio buffer acting as the sound source\n    createSoundSource() {\n        const opts = this.options, ctx = this.audioContext, frequency = (opts.fixedFrequency || 0) *\n            (opts.freqMultiplier || 1);\n        if (opts.type === 'whitenoise') {\n            const bSize = ctx.sampleRate * 2, buffer = ctx.createBuffer(1, bSize, ctx.sampleRate), data = buffer.getChannelData(0);\n            for (let i = 0; i < bSize; ++i) {\n                // More pleasant \"white\" noise with less variance than -1 to +1\n                data[i] = Math.random() * 1.2 - 0.6;\n            }\n            const wn = this.whiteNoise = ctx.createBufferSource();\n            wn.buffer = buffer;\n            wn.loop = true;\n        }\n        else if (opts.type === 'pulse') {\n            this.pulseNode = new PulseOscNode(ctx, {\n                detune: opts.detune,\n                pulseWidth: opts.pulseWidth,\n                frequency\n            });\n        }\n        else {\n            this.oscNode = new OscillatorNode(ctx, {\n                type: opts.type || 'sine',\n                detune: opts.detune,\n                frequency\n            });\n        }\n    }\n    // Lowpass/Highpass filters\n    createFilters() {\n        const opts = this.options;\n        if (opts.lowpass && opts.lowpass.frequency) {\n            this.lowpassNode = new BiquadFilterNode(this.audioContext, {\n                type: 'lowpass',\n                Q: opts.lowpass.Q || 1,\n                frequency: opts.lowpass.frequency\n            });\n        }\n        if (opts.highpass && opts.highpass.frequency) {\n            this.highpassNode = new BiquadFilterNode(this.audioContext, {\n                type: 'highpass',\n                Q: opts.highpass.Q || 1,\n                frequency: opts.highpass.frequency\n            });\n        }\n    }\n    // Gain node used for frequency dependent volume tracking\n    createVolTracking() {\n        const opts = this.options;\n        if (opts.volumePitchTrackingMultiplier &&\n            opts.volumePitchTrackingMultiplier !== 1) {\n            this.volTrackingNode = new GainNode(this.audioContext, {\n                gain: 1\n            });\n        }\n    }\n    // Get the oscillator frequency target\n    getOscTarget() {\n        return this.oscNode ? this.oscNode.frequency :\n            this.pulseNode && this.pulseNode.getFrequencyFacade();\n    }\n}\n/**\n * The SynthPatch class. This class represents an instance and configuration\n * of the built-in Highcharts synthesizer. It can be used to play various\n * generated sounds.\n *\n * @sample highcharts/sonification/manual-using-synth\n *         Using Synth directly to sonify manually\n * @sample highcharts/sonification/custom-instrument\n *         Using custom Synth options with chart\n *\n * @requires modules/sonification\n *\n * @class\n * @name Highcharts.SynthPatch\n *\n * @param {AudioContext} audioContext\n *        The AudioContext to use.\n * @param {Highcharts.SynthPatchOptionsObject} options\n *        Configuration for the synth.\n */\nclass SynthPatch {\n    constructor(audioContext, options) {\n        this.audioContext = audioContext;\n        this.options = options;\n        this.eqNodes = [];\n        this.midiInstrument = options.midiInstrument || 1;\n        this.outputNode = new GainNode(audioContext, { gain: 0 });\n        this.createEqChain(this.outputNode);\n        const inputNode = this.eqNodes.length ?\n            this.eqNodes[0] : this.outputNode;\n        this.oscillators = (this.options.oscillators || []).map((oscOpts) => new Oscillator(audioContext, oscOpts, defined(oscOpts.fmOscillator) || defined(oscOpts.vmOscillator) ?\n            void 0 : inputNode));\n        // Now that we have all oscillators, connect the ones\n        // that are used for modulation.\n        this.oscillators.forEach((osc) => {\n            const connectTarget = (targetFunc, targetOsc) => {\n                if (targetOsc) {\n                    const target = targetOsc[targetFunc]();\n                    if (target) {\n                        osc.connect(target);\n                    }\n                }\n            };\n            if (defined(osc.fmOscillatorIx)) {\n                connectTarget('getFMTarget', this.oscillators[osc.fmOscillatorIx]);\n            }\n            if (defined(osc.vmOscillatorIx)) {\n                connectTarget('getVMTarget', this.oscillators[osc.vmOscillatorIx]);\n            }\n        });\n    }\n    /**\n     * Start the oscillators, but don't output sound.\n     * @function Highcharts.SynthPatch#startSilently\n     */\n    startSilently() {\n        this.outputNode.gain.value = 0;\n        this.oscillators.forEach((o) => o.start());\n    }\n    /**\n     * Stop the synth. It can't be started again.\n     * @function Highcharts.SynthPatch#stop\n     */\n    stop() {\n        const curTime = this.audioContext.currentTime, endTime = curTime + SynthPatch.stopRampTime;\n        miniRampToVolAtTime(this.outputNode, curTime, 0);\n        this.oscillators.forEach((o) => o.stopAtTime(endTime));\n        this.outputNode.disconnect();\n    }\n    /**\n     * Mute sound at time (in seconds).\n     * Will still run release envelope. Note: If scheduled multiple times in\n     * succession, the release envelope will run, and that could make sound.\n     * @function Highcharts.SynthPatch#silenceAtTime\n     * @param {number} time Time offset from now, in seconds\n     */\n    silenceAtTime(time) {\n        if (!time && this.outputNode.gain.value < 0.01) {\n            this.outputNode.gain.value = 0;\n            return; // Skip if not needed\n        }\n        this.releaseAtTime((time || 0) + this.audioContext.currentTime);\n    }\n    /**\n     * Mute sound immediately.\n     * @function Highcharts.SynthPatch#mute\n     */\n    mute() {\n        this.cancelScheduled();\n        miniRampToVolAtTime(this.outputNode, this.audioContext.currentTime, 0);\n    }\n    /**\n     * Play a frequency at time (in seconds).\n     * Time denotes when the attack ramp starts. Note duration is given in\n     * milliseconds. If note duration is not given, the note plays indefinitely.\n     * @function Highcharts.SynthPatch#silenceAtTime\n     * @param {number} time Time offset from now, in seconds\n     * @param {number} frequency The frequency to play at\n     * @param {number|undefined} noteDuration Duration to play, in milliseconds\n     */\n    playFreqAtTime(time, frequency, noteDuration) {\n        const t = (time || 0) + this.audioContext.currentTime, opts = this.options;\n        this.oscillators.forEach((o) => {\n            o.setFreqAtTime(t, frequency, opts.noteGlideDuration);\n            o.runEnvelopeAtTime('attack', t);\n        });\n        scheduleGainEnvelope(opts.masterAttackEnvelope || [], 'attack', t, this.outputNode, opts.masterVolume);\n        if (noteDuration) {\n            this.releaseAtTime(t + noteDuration / 1000);\n        }\n    }\n    /**\n     * Cancel any scheduled actions\n     * @function Highcharts.SynthPatch#cancelScheduled\n     */\n    cancelScheduled() {\n        this.outputNode.gain.cancelScheduledValues(this.audioContext.currentTime);\n        this.oscillators.forEach((o) => o.cancelScheduled());\n    }\n    /**\n     * Connect the SynthPatch output to an audio node / destination.\n     * @function Highcharts.SynthPatch#connect\n     * @param {AudioNode} destinationNode The node to connect to.\n     * @return {AudioNode} The destination node, to allow chaining.\n     */\n    connect(destinationNode) {\n        return this.outputNode.connect(destinationNode);\n    }\n    /**\n     * Create nodes for master EQ\n     * @private\n     */\n    createEqChain(outputNode) {\n        this.eqNodes = (this.options.eq || []).map((eqDef) => new BiquadFilterNode(this.audioContext, {\n            type: 'peaking',\n            ...eqDef\n        }));\n        // Connect nodes\n        this.eqNodes.reduceRight((chain, node) => {\n            node.connect(chain);\n            return node;\n        }, outputNode);\n    }\n    /**\n     * Fade by release envelopes at time\n     * @private\n     */\n    releaseAtTime(time) {\n        let maxReleaseDuration = 0;\n        this.oscillators.forEach((o) => {\n            const env = o.options.releaseEnvelope;\n            if (env && env.length) {\n                maxReleaseDuration = Math.max(maxReleaseDuration, env[env.length - 1].t);\n                o.runEnvelopeAtTime('release', time);\n            }\n        });\n        const masterEnv = this.options.masterReleaseEnvelope || [];\n        if (masterEnv.length) {\n            scheduleGainEnvelope(masterEnv, 'release', time, this.outputNode, this.options.masterVolume);\n            maxReleaseDuration = Math.max(maxReleaseDuration, masterEnv[masterEnv.length - 1].t);\n        }\n        miniRampToVolAtTime(this.outputNode, time + maxReleaseDuration / 1000, 0);\n    }\n}\nSynthPatch.stopRampTime = 0.012; // Ramp time to 0 when stopping sound\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Sonification_SynthPatch = (SynthPatch);\n/* *\n *\n *  API declarations\n *\n * */\n/**\n * An EQ filter definition for a low/highpass filter.\n * @requires modules/sonification\n * @interface Highcharts.SynthPatchPassFilter\n */ /**\n* Filter frequency.\n* @name Highcharts.SynthPatchPassFilter#frequency\n* @type {number|undefined}\n*/ /**\n* A pitch tracking multiplier similarly to the one for oscillator volume.\n* Affects the filter frequency.\n* @name Highcharts.SynthPatchPassFilter#frequencyPitchTrackingMultiplier\n* @type {number|undefined}\n*/ /**\n* Filter resonance bump/dip in dB. Defaults to 0.\n* @name Highcharts.SynthPatchPassFilter#Q\n* @type {number|undefined}\n*/\n/**\n * @typedef {Highcharts.Record<\"t\"|\"vol\",number>} Highcharts.SynthEnvelopePoint\n * @requires modules/sonification\n */\n/**\n * @typedef {Array<Highcharts.SynthEnvelopePoint>} Highcharts.SynthEnvelope\n * @requires modules/sonification\n */\n/**\n * @typedef {\"sine\"|\"square\"|\"sawtooth\"|\"triangle\"|\"whitenoise\"|\"pulse\"} Highcharts.SynthPatchOscillatorType\n * @requires modules/sonification\n */\n/**\n * Configuration for an oscillator for the synth.\n * @requires modules/sonification\n * @interface Highcharts.SynthPatchOscillatorOptionsObject\n */ /**\n* The type of oscillator. This describes the waveform of the oscillator.\n* @name Highcharts.SynthPatchOscillatorOptionsObject#type\n* @type {Highcharts.SynthPatchOscillatorType|undefined}\n*/ /**\n* A volume modifier for the oscillator. Defaults to 1.\n* @name Highcharts.SynthPatchOscillatorOptionsObject#volume\n* @type {number|undefined}\n*/ /**\n* A multiplier for the input frequency of the oscillator. Defaults to 1. If\n* this is for example set to 4, an input frequency of 220Hz will cause the\n* oscillator to play at 880Hz.\n* @name Highcharts.SynthPatchOscillatorOptionsObject#freqMultiplier\n* @type {number|undefined}\n*/ /**\n* Play a fixed frequency for the oscillator - ignoring input frequency. The\n* frequency multiplier is still applied.\n* @name Highcharts.SynthPatchOscillatorOptionsObject#fixedFrequency\n* @type {number|undefined}\n*/ /**\n* Applies a detuning of all frequencies. Set in cents. Defaults to 0.\n* @name Highcharts.SynthPatchOscillatorOptionsObject#detune\n* @type {number|undefined}\n*/ /**\n* Width of the pulse waveform. Only applies to \"pulse\" type oscillators. A\n* width of 0.5 is roughly equal to a square wave. This is the default.\n* @name Highcharts.SynthPatchOscillatorOptionsObject#pulseWidth\n* @type {number|undefined}\n*/ /**\n* Index of another oscillator to use as carrier, with this oscillator being\n* used as a volume modulator. The first oscillator in the array has index 0,\n* and so on. This option can be used to produce tremolo effects.\n* @name Highcharts.SynthPatchOscillatorOptionsObject#vmOscillator\n* @type {number|undefined}\n*/ /**\n* Index of another oscillator to use as carrier, with this oscillator being\n* used as a frequency modulator. Note: If the carrier is a pulse oscillator,\n* the modulation will be on pulse width instead of frequency, allowing for\n* PWM effects.\n* @name Highcharts.SynthPatchOscillatorOptionsObject#fmOscillator\n* @type {number|undefined}\n*/ /**\n* A tracking multiplier used for frequency dependent behavior. For example, by\n* setting the volume tracking multiplier to 0.01, the volume will be lower at\n* higher notes. The multiplier is a logarithmic function, where 1 is at ca\n* 50Hz, and you define the output multiplier for an input frequency around\n* 3.2kHz.\n* @name Highcharts.SynthPatchOscillatorOptionsObject#volumePitchTrackingMultiplier\n* @type {number|undefined}\n*/ /**\n* Volume envelope for note attack, specific to this oscillator.\n* @name Highcharts.SynthPatchOscillatorOptionsObject#attackEnvelope\n* @type {Highcharts.SynthEnvelope|undefined}\n*/ /**\n* Volume envelope for note release, specific to this oscillator.\n* @name Highcharts.SynthPatchOscillatorOptionsObject#releaseEnvelope\n* @type {Highcharts.SynthEnvelope|undefined}\n*/ /**\n* Highpass filter options for the oscillator.\n* @name Highcharts.SynthPatchOscillatorOptionsObject#highpass\n* @type {Highcharts.SynthPatchPassFilter|undefined}\n*/ /**\n* Lowpass filter options for the oscillator.\n* @name Highcharts.SynthPatchOscillatorOptionsObject#lowpass\n* @type {Highcharts.SynthPatchPassFilter|undefined}\n*/\n/**\n * An EQ filter definition for a bell filter.\n * @requires modules/sonification\n * @interface Highcharts.SynthPatchEQFilter\n */ /**\n* Filter frequency.\n* @name Highcharts.SynthPatchEQFilter#frequency\n* @type {number|undefined}\n*/ /**\n* Filter gain. Defaults to 0.\n* @name Highcharts.SynthPatchEQFilter#gain\n* @type {number|undefined}\n*/ /**\n* Filter Q. Defaults to 1. Lower numbers mean a wider bell.\n* @name Highcharts.SynthPatchEQFilter#Q\n* @type {number|undefined}\n*/\n/**\n * A set of options for the SynthPatch class.\n *\n * @requires modules/sonification\n *\n * @interface Highcharts.SynthPatchOptionsObject\n */ /**\n* Global volume modifier for the synth. Defaults to 1. Note that if the total\n* volume of all oscillators is too high, the browser's audio engine can\n* distort.\n* @name Highcharts.SynthPatchOptionsObject#masterVolume\n* @type {number|undefined}\n*/ /**\n* Time in milliseconds to glide between notes. Causes a glissando effect.\n* @name Highcharts.SynthPatchOptionsObject#noteGlideDuration\n* @type {number|undefined}\n*/ /**\n* MIDI instrument ID for the synth. Used with MIDI export of Timelines to have\n* tracks open with a similar instrument loaded when imported into other\n* applications. Defaults to 1, \"Acoustic Grand Piano\".\n* @name Highcharts.SynthPatchOptionsObject#midiInstrument\n* @type {number|undefined}\n*/ /**\n* Volume envelope for the overall attack of a note - what happens to the\n* volume when a note first plays. If the volume goes to 0 in the attack\n* envelope, the synth will not be able to play the note continuously/\n* sustained, and the notes will be staccato.\n* @name Highcharts.SynthPatchOptionsObject#masterAttackEnvelope\n* @type {Highcharts.SynthEnvelope|undefined}\n*/ /**\n* Volume envelope for the overall release of a note - what happens to the\n* volume when a note stops playing. If the release envelope starts at a higher\n* volume than the attack envelope ends, the volume will first rise to that\n* volume before falling when releasing a note. If the note is released while\n* the attack envelope is still in effect, the attack envelope is interrupted,\n* and the release envelope plays instead.\n* @name Highcharts.SynthPatchOptionsObject#masterReleaseEnvelope\n* @type {Highcharts.SynthEnvelope|undefined}\n*/ /**\n* Master EQ filters for the synth, affecting the overall sound.\n* @name Highcharts.SynthPatchOptionsObject#eq\n* @type {Array<Highcharts.SynthPatchEQFilter>|undefined}\n*/ /**\n* Array of oscillators to add to the synth.\n* @name Highcharts.SynthPatchOptionsObject#oscillators\n* @type {Array<Highcharts.SynthPatchOscillatorOptionsObject>|undefined}\n*/\n(''); // Keep above doclets in JS file\n\n;// ./code/es-modules/Extensions/Sonification/InstrumentPresets.js\n/* *\n *\n *  (c) 2009-2025 Øystein Moseng\n *\n *  Presets for SynthPatch.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nconst InstrumentPresets = {\n    // PIANO ----------------------------\n    piano: {\n        masterVolume: 0.45,\n        masterAttackEnvelope: [\n            { t: 1, vol: 0.71 },\n            { t: 40, vol: 0.79 },\n            { t: 82, vol: 0.64 },\n            { t: 147, vol: 0.29 },\n            { t: 260, vol: 0.15 },\n            { t: 417, vol: 0.05 },\n            { t: 589, vol: 0 }\n        ],\n        eq: [\n            { frequency: 200, Q: 0.7, gain: 6 },\n            { frequency: 450, gain: 6 },\n            { frequency: 1300, gain: 2 },\n            { frequency: 2600, Q: 0.8, gain: 8 },\n            { frequency: 3500, Q: 0.8, gain: 6 },\n            { frequency: 6200, Q: 0.8, gain: 10 },\n            { frequency: 8000, gain: -23 },\n            { frequency: 10000, Q: 0.4, gain: -12 }\n        ],\n        oscillators: [{\n                type: 'pulse',\n                volume: 0.5,\n                pulseWidth: 0.55,\n                volumePitchTrackingMultiplier: 0.1,\n                lowpass: {\n                    frequency: 4.5,\n                    frequencyPitchTrackingMultiplier: 900,\n                    Q: -2\n                },\n                highpass: { frequency: 270 },\n                attackEnvelope: [{ t: 1, vol: 1 }],\n                releaseEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 282, vol: 0.64 },\n                    { t: 597, vol: 0 }\n                ]\n            }, {\n                type: 'whitenoise',\n                volume: 0.8,\n                lowpass: { frequency: 400 },\n                highpass: { frequency: 300 },\n                attackEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 19, vol: 0 }\n                ]\n            }]\n    },\n    // PLUCKED --------------------------\n    plucked: {\n        masterVolume: 0.5,\n        midiInstrument: 25,\n        masterAttackEnvelope: [\n            { t: 1, vol: 0.71 },\n            { t: 4, vol: 0.71 },\n            { t: 31, vol: 0.4 },\n            { t: 109, vol: 0.12 },\n            { t: 234, vol: 0.04 },\n            { t: 442, vol: 0 }\n        ],\n        eq: [\n            { frequency: 800, gain: -8 },\n            { frequency: 1400, Q: 4, gain: 4 },\n            { frequency: 1600, gain: -14 },\n            { frequency: 2200, gain: -8 },\n            { frequency: 3600, gain: -2 },\n            { frequency: 6400, Q: 2, gain: -6 }\n        ],\n        oscillators: [{\n                type: 'sawtooth',\n                volume: 0.9,\n                volumePitchTrackingMultiplier: 0.6,\n                highpass: { frequency: 100 },\n                lowpass: { frequency: 8000 },\n                releaseEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 315, vol: 0.56 },\n                    { t: 550, vol: 0 }\n                ]\n            }]\n    },\n    // FLUTE ----------------------------\n    flute: {\n        masterVolume: 1.1,\n        midiInstrument: 74,\n        noteGlideDuration: 30,\n        masterAttackEnvelope: [\n            { t: 0, vol: 0 },\n            { t: 29, vol: 1 },\n            { t: 76, vol: 0.48 },\n            { t: 600, vol: 0.36 }\n        ],\n        masterReleaseEnvelope: [\n            { t: 1, vol: 0.36 },\n            { t: 24, vol: 0.15 },\n            { t: 119, vol: 0 }\n        ],\n        eq: [\n            { frequency: 150, Q: 0.6, gain: -10 },\n            { frequency: 500, gain: 4 },\n            { frequency: 1100, gain: -4 },\n            { frequency: 2200, gain: -14 },\n            { frequency: 5000, gain: 8 },\n            { frequency: 6400, gain: 10 },\n            { frequency: 8000, gain: 12 },\n            { frequency: 10800, gain: 8 }\n        ],\n        oscillators: [{\n                type: 'triangle',\n                volume: 1,\n                volumePitchTrackingMultiplier: 0.4,\n                lowpass: {\n                    frequency: 12,\n                    frequencyPitchTrackingMultiplier: 100\n                },\n                highpass: {\n                    frequency: 200\n                }\n            }, {\n                type: 'sine',\n                fixedFrequency: 5,\n                volume: 0.2,\n                vmOscillator: 0,\n                attackEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 48, vol: 0 },\n                    { t: 225, vol: 0.05 },\n                    { t: 600, vol: 0.77 }\n                ]\n            }, {\n                type: 'whitenoise',\n                volume: 0.13,\n                lowpass: {\n                    frequency: 9000,\n                    Q: 3\n                },\n                highpass: {\n                    frequency: 6000,\n                    Q: 3\n                },\n                vmOscillator: 0,\n                attackEnvelope: [\n                    { t: 0, vol: 0 },\n                    { t: 26, vol: 1 },\n                    { t: 93, vol: 0.8 }\n                ]\n            }]\n    },\n    // LEAD -----------------------------\n    lead: {\n        masterVolume: 1,\n        midiInstrument: 20,\n        masterAttackEnvelope: [\n            { t: 1, vol: 0.81 },\n            { t: 98, vol: 0.5 },\n            { t: 201, vol: 0.18 },\n            { t: 377, vol: 0.04 },\n            { t: 586, vol: 0 },\n            { t: 586, vol: 0 }\n        ],\n        eq: [\n            { frequency: 200, gain: -6 },\n            { frequency: 400, gain: -8 },\n            { frequency: 800, Q: 0.5, gain: -10 },\n            { frequency: 1200, gain: 4 },\n            { frequency: 3600, gain: -4 },\n            { frequency: 4200, gain: -12 },\n            { frequency: 7400, gain: -14 },\n            { frequency: 10000, gain: 2 }\n        ],\n        oscillators: [{\n                type: 'triangle',\n                volume: 1.1,\n                volumePitchTrackingMultiplier: 0.6,\n                lowpass: { frequency: 5000 },\n                highpass: { frequency: 100 }\n            }, {\n                type: 'sawtooth',\n                volume: 0.4,\n                lowpass: { frequency: 7000 },\n                highpass: { frequency: 800, Q: 6 },\n                releaseEnvelope: [\n                    { t: 0, vol: 0.99 },\n                    { t: 200, vol: 0.83 },\n                    { t: 495, vol: 0 }\n                ]\n            }]\n    },\n    // VIBRAPHONE -----------------------\n    vibraphone: {\n        masterVolume: 1,\n        midiInstrument: 12,\n        masterAttackEnvelope: [\n            { t: 1, vol: 0 },\n            { t: 10, vol: 0.63 },\n            { t: 82, vol: 0.64 },\n            { t: 149, vol: 0.26 },\n            { t: 600, vol: 0 }\n        ],\n        eq: [\n            { frequency: 200, Q: 0.8, gain: -12 },\n            { frequency: 400, gain: -4 },\n            { frequency: 1600, Q: 0.5, gain: 6 },\n            { frequency: 2200, Q: 0.5, gain: 6 },\n            { frequency: 6400, gain: 4 },\n            { frequency: 12800, gain: 4 }\n        ],\n        oscillators: [{\n                type: 'sine',\n                volume: 1.5,\n                volumePitchTrackingMultiplier: 0.0000001,\n                attackEnvelope: [{ t: 1, vol: 1 }],\n                releaseEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 146, vol: 0.39 },\n                    { t: 597, vol: 0 }\n                ]\n            }, {\n                type: 'whitenoise',\n                volume: 0.03,\n                volumePitchTrackingMultiplier: 0.0001,\n                lowpass: {\n                    frequency: 900\n                },\n                highpass: {\n                    frequency: 800\n                },\n                attackEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 9, vol: 0 }\n                ]\n            }, {\n                type: 'sine',\n                freqMultiplier: 4,\n                volume: 0.15,\n                volumePitchTrackingMultiplier: 0.0001\n            }, {\n                type: 'sine',\n                fixedFrequency: 3,\n                volume: 6,\n                fmOscillator: 0,\n                releaseEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 190, vol: 0.41 },\n                    { t: 600, vol: 0 }\n                ]\n            }, {\n                type: 'sine',\n                fixedFrequency: 6,\n                volume: 3,\n                fmOscillator: 2\n            }, {\n                type: 'sine',\n                freqMultiplier: 9,\n                volume: 0.0005,\n                volumePitchTrackingMultiplier: 0.0001,\n                releaseEnvelope: [\n                    { t: 1, vol: 0.97 },\n                    { t: 530, vol: 0 }\n                ]\n            }]\n    },\n    // SAXOPHONE ------------------------\n    saxophone: {\n        masterVolume: 1,\n        midiInstrument: 67,\n        noteGlideDuration: 10,\n        masterAttackEnvelope: [\n            { t: 1, vol: 0.57 },\n            { t: 35, vol: 1 },\n            { t: 87, vol: 0.84 },\n            { t: 111, vol: 0.6 },\n            { t: 296, vol: 0.49 },\n            { t: 600, vol: 0.58 }\n        ],\n        masterReleaseEnvelope: [\n            { t: 1, vol: 0.58 },\n            { t: 47, vol: 0.16 },\n            { t: 119, vol: 0 }\n        ],\n        eq: [\n            { frequency: 200, gain: -2 },\n            { frequency: 600, gain: 2 },\n            { frequency: 800, gain: -10 },\n            { frequency: 1100, gain: -2 },\n            { frequency: 2200, gain: -2 },\n            { frequency: 3500, gain: 10 },\n            { frequency: 12800, gain: 4 }\n        ],\n        oscillators: [{\n                type: 'sawtooth',\n                volume: 0.45,\n                volumePitchTrackingMultiplier: 0.06,\n                lowpass: {\n                    frequency: 18,\n                    frequencyPitchTrackingMultiplier: 200\n                },\n                highpass: {\n                    frequency: 300\n                }\n            }, {\n                type: 'whitenoise',\n                fixedFrequency: 1,\n                volume: 0.4,\n                highpass: {\n                    frequency: 7000\n                },\n                vmOscillator: 0,\n                attackEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 51, vol: 1 },\n                    { t: 86, vol: 0.84 },\n                    { t: 500, vol: 0.78 }\n                ]\n            }, {\n                type: 'sine',\n                fixedFrequency: 4,\n                volume: 2,\n                fmOscillator: 0,\n                attackEnvelope: [\n                    { t: 0, vol: 0 },\n                    { t: 15, vol: 0.94 },\n                    { t: 79, vol: 1 },\n                    { t: 172, vol: 0.47 },\n                    { t: 500, vol: 0.26 }\n                ]\n            }, {\n                type: 'sine',\n                fixedFrequency: 7,\n                volume: 6,\n                fmOscillator: 0,\n                attackEnvelope: [\n                    { t: 0, vol: 0 },\n                    { t: 25, vol: 0.99 },\n                    { t: 85, vol: 0 },\n                    { t: 85, vol: 0 },\n                    { t: 387, vol: 0.02 },\n                    { t: 511, vol: 0.43 },\n                    { t: 600, vol: 0 }\n                ]\n            }]\n    },\n    // TRUMPET ------------------------\n    trumpet: {\n        masterVolume: 0.3,\n        midiInstrument: 57,\n        noteGlideDuration: 40,\n        masterAttackEnvelope: [\n            { t: 1, vol: 0 },\n            { t: 17, vol: 1 },\n            { t: 42, vol: 0.85 },\n            { t: 76, vol: 1 },\n            { t: 202, vol: 0.65 },\n            { t: 226, vol: 0.86 },\n            { t: 282, vol: 0.63 }\n        ],\n        masterReleaseEnvelope: [\n            { t: 1, vol: 0.62 },\n            { t: 34, vol: 0.14 },\n            { t: 63, vol: 0.21 },\n            { t: 96, vol: 0 }\n        ],\n        eq: [\n            { frequency: 200, Q: 0.6, gain: 10 },\n            { frequency: 600, Q: 0.5, gain: 6 },\n            { frequency: 1500, Q: 0.7, gain: 14 },\n            { frequency: 3200, Q: 2, gain: 8 },\n            { frequency: 3800, Q: 0.8, gain: 10 },\n            { frequency: 6200, gain: 12 },\n            { frequency: 8400, gain: -20 },\n            { frequency: 12800, Q: 0.5, gain: -18 }\n        ],\n        oscillators: [{\n                type: 'sawtooth',\n                volume: 0.15,\n                pulseWidth: 0.5,\n                volumePitchTrackingMultiplier: 0.5,\n                lowpass: { frequency: 1900, Q: 3 }\n            }, {\n                type: 'sine',\n                fixedFrequency: 6,\n                volume: 0.2,\n                vmOscillator: 0,\n                attackEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 102, vol: 0.13 },\n                    { t: 556, vol: 0.24 }\n                ]\n            }, {\n                type: 'whitenoise',\n                volume: 0.45,\n                highpass: { frequency: 7000, Q: 9 },\n                vmOscillator: 0,\n                attackEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 89, vol: 0.51 },\n                    { t: 577, vol: 0.29 }\n                ]\n            }, {\n                type: 'sine',\n                fixedFrequency: 5.7,\n                volume: 20,\n                fmOscillator: 0,\n                attackEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 89, vol: 1 },\n                    { t: 137, vol: 0.46 },\n                    { t: 283, vol: 0.15 },\n                    { t: 600, vol: 0.28 }\n                ]\n            }]\n    },\n    // SAWSYNTH --------------------------\n    sawsynth: {\n        masterVolume: 0.3,\n        midiInstrument: 51,\n        noteGlideDuration: 40,\n        masterAttackEnvelope: [\n            { t: 0, vol: 0.6 },\n            { t: 9, vol: 1 },\n            { t: 102, vol: 0.48 }\n        ],\n        eq: [{ frequency: 200, gain: -6 }],\n        oscillators: [{\n                type: 'sawtooth',\n                volume: 0.4,\n                volumePitchTrackingMultiplier: 0.3\n            }, {\n                type: 'sawtooth',\n                volume: 0.4,\n                detune: 11,\n                volumePitchTrackingMultiplier: 0.3\n            }, {\n                type: 'sawtooth',\n                volume: 0.4,\n                detune: -11,\n                volumePitchTrackingMultiplier: 0.3\n            }]\n    },\n    // BASIC1 ---------------------------\n    basic1: {\n        masterVolume: 1,\n        noteGlideDuration: 0,\n        masterReleaseEnvelope: [\n            { t: 1, vol: 0.36 },\n            { t: 24, vol: 0.15 },\n            { t: 119, vol: 0 }\n        ],\n        eq: [\n            { frequency: 150, Q: 0.6, gain: -12 },\n            { frequency: 1100, gain: -2 },\n            { frequency: 2200, gain: -16 },\n            { frequency: 5000, gain: 8 },\n            { frequency: 6400, gain: 10 },\n            { frequency: 8000, gain: 12 },\n            { frequency: 10800, gain: 8 }\n        ],\n        oscillators: [{\n                type: 'triangle',\n                volume: 1,\n                volumePitchTrackingMultiplier: 0.05,\n                lowpass: { frequency: 17, frequencyPitchTrackingMultiplier: 100 },\n                highpass: { frequency: 200 }\n            }, {\n                type: 'whitenoise',\n                volume: 0.04,\n                lowpass: { frequency: 9000, Q: 3 },\n                highpass: { frequency: 6000, Q: 3 },\n                vmOscillator: 0,\n                attackEnvelope: [\n                    { t: 0, vol: 0 },\n                    { t: 26, vol: 1 },\n                    { t: 71, vol: 0.73 }\n                ]\n            }]\n    },\n    // BASIC2 ---------------------------\n    basic2: {\n        masterVolume: 0.3,\n        eq: [\n            { frequency: 200, Q: 0.7, gain: 6 },\n            { frequency: 450, gain: 2 },\n            { frequency: 1300, gain: -2 },\n            { frequency: 2600, Q: 0.8, gain: 6 },\n            { frequency: 3500, Q: 0.8, gain: 6 },\n            { frequency: 6200, Q: 0.8, gain: 10 },\n            { frequency: 8000, gain: -18 },\n            { frequency: 10000, Q: 0.4, gain: -12 }\n        ],\n        oscillators: [{\n                type: 'pulse',\n                volume: 0.4,\n                pulseWidth: 0.55,\n                volumePitchTrackingMultiplier: 0.1,\n                lowpass: {\n                    frequency: 4.5,\n                    frequencyPitchTrackingMultiplier: 900,\n                    Q: -2\n                },\n                highpass: { frequency: 270 }\n            }]\n    },\n    // CHORD -------------------------------\n    chord: {\n        masterVolume: 1,\n        masterAttackEnvelope: [\n            { t: 1, vol: 0.79 },\n            { t: 27, vol: 0.86 },\n            { t: 62, vol: 0.81 },\n            { t: 150, vol: 0.35 },\n            { t: 408, vol: 0.04 },\n            { t: 600, vol: 0 }\n        ],\n        eq: [\n            { frequency: 200, gain: -8 },\n            { frequency: 600, Q: 2, gain: 4 },\n            { frequency: 800, gain: -10 },\n            { frequency: 1600, gain: -2 },\n            { frequency: 2200, gain: -6 },\n            { frequency: 3600, Q: 0.7, gain: -2 },\n            { frequency: 6400, gain: 6 },\n            { frequency: 12800, gain: 6 }\n        ],\n        oscillators: [{\n                type: 'triangle',\n                volume: 1.1,\n                volumePitchTrackingMultiplier: 0.05,\n                lowpass: { frequency: 8000 },\n                highpass: { frequency: 100 },\n                releaseEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 315, vol: 0.56 },\n                    { t: 540, vol: 0 }\n                ]\n            }, {\n                type: 'triangle',\n                freqMultiplier: 1.17,\n                volume: 0.4,\n                volumePitchTrackingMultiplier: 0.07,\n                lowpass: { frequency: 5000 },\n                highpass: { frequency: 100 },\n                releaseEnvelope: [\n                    { t: 0, vol: 1 },\n                    { t: 476, vol: 0 }\n                ]\n            }, {\n                type: 'triangle',\n                freqMultiplier: 1.58333,\n                volume: 0.7,\n                volumePitchTrackingMultiplier: 0.02,\n                highpass: { frequency: 200 },\n                releaseEnvelope: [\n                    { t: 0, vol: 1 },\n                    { t: 422, vol: 0.56 },\n                    { t: 577, vol: 0 }\n                ]\n            }, {\n                type: 'sine',\n                fixedFrequency: 10,\n                volume: 4,\n                fmOscillator: 0,\n                attackEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 157, vol: 0.65 }\n                ]\n            }, {\n                type: 'sine',\n                fixedFrequency: 5,\n                volume: 0.3,\n                vmOscillator: 2,\n                attackEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 155, vol: 0.91 },\n                    { t: 289, vol: 0.78 }\n                ]\n            }]\n    },\n    // WOBBLE ---------------------------\n    wobble: {\n        masterVolume: 0.9,\n        masterReleaseEnvelope: [\n            { t: 1, vol: 0.36 },\n            { t: 24, vol: 0.15 },\n            { t: 119, vol: 0 }\n        ],\n        eq: [\n            { frequency: 150, Q: 0.6, gain: -12 },\n            { frequency: 1100, gain: -2 },\n            { frequency: 2200, gain: -16 },\n            { frequency: 5000, gain: 8 },\n            { frequency: 6400, gain: 10 },\n            { frequency: 8000, gain: 12 },\n            { frequency: 10800, gain: 8 }\n        ],\n        oscillators: [{\n                type: 'triangle',\n                volume: 0.9,\n                volumePitchTrackingMultiplier: 0.1,\n                lowpass: { frequency: 17, frequencyPitchTrackingMultiplier: 100 },\n                highpass: { frequency: 200 }\n            }, {\n                type: 'whitenoise',\n                volume: 0.04,\n                lowpass: { frequency: 9000, Q: 3 },\n                highpass: { frequency: 6000, Q: 3 },\n                vmOscillator: 0,\n                attackEnvelope: [\n                    { t: 0, vol: 0 },\n                    { t: 26, vol: 1 },\n                    { t: 71, vol: 0.73 }\n                ]\n            }, {\n                type: 'sine',\n                freqMultiplier: 0.011,\n                volume: 30,\n                fmOscillator: 0\n            }]\n    },\n    // SINE -----------------------------\n    sine: {\n        masterVolume: 1,\n        oscillators: [{\n                type: 'sine',\n                volumePitchTrackingMultiplier: 0.07\n            }]\n    },\n    // SINE GLIDE -----------------------\n    sineGlide: {\n        masterVolume: 1,\n        noteGlideDuration: 100,\n        oscillators: [{\n                type: 'sine',\n                volumePitchTrackingMultiplier: 0.07\n            }]\n    },\n    // TRIANGLE -------------------------\n    triangle: {\n        masterVolume: 0.5,\n        oscillators: [{\n                type: 'triangle',\n                volume: 1,\n                volumePitchTrackingMultiplier: 0.07\n            }]\n    },\n    // SAWTOOTH -------------------------\n    sawtooth: {\n        masterVolume: 0.25,\n        midiInstrument: 82,\n        oscillators: [{\n                type: 'sawtooth',\n                volume: 0.3,\n                volumePitchTrackingMultiplier: 0.07\n            }]\n    },\n    // SQUARE ---------------------------\n    square: {\n        masterVolume: 0.3,\n        midiInstrument: 81,\n        oscillators: [{\n                type: 'square',\n                volume: 0.2,\n                volumePitchTrackingMultiplier: 0.07\n            }]\n    },\n    // PERCUSSION INSTRUMENTS ----------\n    chop: {\n        masterVolume: 1,\n        midiInstrument: 116,\n        masterAttackEnvelope: [{ t: 1, vol: 1 }, { t: 44, vol: 0 }],\n        oscillators: [{\n                type: 'whitenoise',\n                volume: 1,\n                lowpass: { frequency: 600 },\n                highpass: { frequency: 200 }\n            }]\n    },\n    shaker: {\n        masterVolume: 0.4,\n        midiInstrument: 116,\n        masterAttackEnvelope: [{ t: 1, vol: 1 }, { t: 44, vol: 0 }],\n        oscillators: [{\n                type: 'whitenoise',\n                volume: 1,\n                lowpass: { frequency: 6500 },\n                highpass: { frequency: 5000 }\n            }]\n    },\n    step: {\n        masterVolume: 1,\n        midiInstrument: 116,\n        masterAttackEnvelope: [{ t: 1, vol: 1 }, { t: 44, vol: 0 }],\n        eq: [\n            { frequency: 200, gain: -1 },\n            { frequency: 400, gain: -14 },\n            { frequency: 800, gain: 8 },\n            { frequency: 1000, Q: 5, gain: -24 },\n            { frequency: 1600, gain: 8 },\n            { frequency: 2200, gain: -10 },\n            { frequency: 5400, gain: 4 },\n            { frequency: 12800, gain: -36 }\n        ],\n        oscillators: [{\n                type: 'whitenoise',\n                volume: 1.5,\n                lowpass: { frequency: 300 },\n                highpass: { frequency: 100, Q: 6 }\n            }]\n    },\n    kick: {\n        masterVolume: 0.55,\n        masterAttackEnvelope: [\n            { t: 1, vol: 0.8 },\n            { t: 15, vol: 1 },\n            { t: 45, vol: 0.35 },\n            { t: 121, vol: 0.11 },\n            { t: 242, vol: 0 }\n        ],\n        eq: [\n            { frequency: 50, gain: 6 },\n            { frequency: 400, gain: -18 },\n            { frequency: 1600, gain: 18 }\n        ],\n        oscillators: [{\n                type: 'triangle',\n                fixedFrequency: 90,\n                volume: 1,\n                lowpass: { frequency: 300 },\n                attackEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 6, vol: 1 },\n                    { t: 45, vol: 0.01 }\n                ]\n            }, {\n                type: 'whitenoise',\n                volume: 0.4,\n                lowpass: { frequency: 200 },\n                attackEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 30, vol: 0 }\n                ]\n            }, {\n                type: 'triangle',\n                freqMultiplier: 0.1,\n                volume: 1,\n                lowpass: { frequency: 200 }\n            }]\n    },\n    shortnote: {\n        masterVolume: 0.8,\n        midiInstrument: 116,\n        masterAttackEnvelope: [\n            { t: 1, vol: 1 },\n            { t: 15, vol: 0 }\n        ],\n        eq: [\n            { frequency: 400, gain: -4 },\n            { frequency: 800, gain: -12 },\n            { frequency: 2400, gain: 4 },\n            { frequency: 7200, gain: -20 },\n            { frequency: 1000, Q: 5, gain: -12 },\n            { frequency: 5400, gain: -32 },\n            { frequency: 12800, gain: -14 }\n        ],\n        oscillators: [{\n                type: 'sawtooth',\n                volume: 0.6,\n                lowpass: { frequency: 1000 }\n            }, {\n                type: 'whitenoise',\n                volume: 0.2,\n                lowpass: { frequency: 10000 },\n                highpass: { frequency: 7000 },\n                attackEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 10, vol: 0 }\n                ]\n            }, {\n                type: 'whitenoise',\n                volume: 1.3,\n                lowpass: { frequency: 700, Q: 4 },\n                highpass: { frequency: 250 }\n            }]\n    },\n    // NOISE ----------------------------\n    noise: {\n        masterVolume: 0.3,\n        midiInstrument: 122,\n        oscillators: [{\n                type: 'whitenoise'\n            }]\n    },\n    // FILTERED NOISE -------------------\n    filteredNoise: {\n        masterVolume: 0.3,\n        midiInstrument: 122,\n        eq: [\n            { frequency: 1600, gain: -8 },\n            { frequency: 2200, gain: -4 }\n        ],\n        oscillators: [{\n                type: 'whitenoise',\n                lowpass: {\n                    frequency: 5,\n                    frequencyPitchTrackingMultiplier: 1300,\n                    Q: 6\n                },\n                highpass: {\n                    frequency: 5,\n                    frequencyPitchTrackingMultiplier: 300,\n                    Q: 6\n                }\n            }]\n    },\n    // WIND -------------------------------\n    wind: {\n        masterVolume: 0.75,\n        midiInstrument: 122,\n        noteGlideDuration: 150,\n        masterReleaseEnvelope: [\n            { t: 0, vol: 1 },\n            { t: 124, vol: 0.24 },\n            { t: 281, vol: 0 }\n        ],\n        oscillators: [{\n                type: 'whitenoise',\n                volume: 1,\n                lowpass: {\n                    frequency: 100,\n                    frequencyPitchTrackingMultiplier: 6,\n                    Q: 23\n                },\n                highpass: {\n                    frequency: 170,\n                    frequencyPitchTrackingMultiplier: 6\n                }\n            }, {\n                type: 'sine',\n                freqMultiplier: 0.016,\n                volume: 1000,\n                fmOscillator: 0\n            }]\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Sonification_InstrumentPresets = (InstrumentPresets);\n/* *\n *\n *  API declarations\n *\n * */\n/**\n * @typedef {\"piano\"|\"plucked\"|\"flute\"|\"lead\"|\"vibraphone\"|\"saxophone\"|\"trumpet\"|\"sawsynth\"|\"basic1\"|\"basic2\"|\"chord\"|\"wobble\"|\"sine\"|\"sineGlide\"|\"triangle\"|\"sawtooth\"|\"square\"|\"chop\"|\"shaker\"|\"step\"|\"kick\"|\"shortnote\"|\"noise\"|\"filteredNoise\"|\"wind\"} Highcharts.SonificationSynthPreset\n * @requires modules/sonification\n */\n(''); // Keep above doclets in JS file\n\n;// ./code/es-modules/Extensions/Sonification/SonificationInstrument.js\n/* *\n *\n *  (c) 2009-2025 Øystein Moseng\n *\n *  Class representing an Instrument with mappable parameters for sonification.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { defined: SonificationInstrument_defined, extend } = (external_highcharts_src_js_default_default());\n/**\n * The SonificationInstrument class. This class represents an instrument with\n * mapping capabilities. The instrument wraps a SynthPatch object, and extends\n * it with functionality such as panning, tremolo, and global low/highpass\n * filters.\n *\n * @sample highcharts/sonification/instrument-raw\n *         Using SonificationInstrument directly, with no chart.\n *\n * @requires modules/sonification\n *\n * @class\n * @name Highcharts.SonificationInstrument\n *\n * @param {AudioContext} audioContext\n *        The AudioContext to use.\n * @param {AudioNode} outputNode\n *        The destination node to connect to.\n * @param {Highcharts.SonificationInstrumentOptionsObject} options\n *        Configuration for the instrument.\n */\nclass SonificationInstrument {\n    constructor(audioContext, outputNode, options) {\n        this.audioContext = audioContext;\n        this.curParams = {};\n        this.midiTrackName = options.midiTrackName;\n        this.masterVolNode = new GainNode(audioContext);\n        this.masterVolNode.connect(outputNode);\n        this.volumeNode = new GainNode(audioContext);\n        this.createNodesFromCapabilities(extend({\n            pan: true\n        }, options.capabilities || {}));\n        this.connectCapabilityNodes(this.volumeNode, this.masterVolNode);\n        this.synthPatch = new Sonification_SynthPatch(audioContext, typeof options.synthPatch === 'string' ?\n            Sonification_InstrumentPresets[options.synthPatch] : options.synthPatch);\n        this.midiInstrument = this.synthPatch.midiInstrument || 1;\n        this.synthPatch.startSilently();\n        this.synthPatch.connect(this.volumeNode);\n    }\n    /**\n     * Set the overall volume.\n     * @function Highcharts.SonificationInstrument#setMasterVolume\n     * @param {number} volume The volume to set, from 0 to 1.\n     */\n    setMasterVolume(volume) {\n        this.masterVolNode.gain.setTargetAtTime(volume, 0, SonificationInstrument.rampTime);\n    }\n    /**\n     * Schedule an instrument event at a given time offset, whether it is\n     * playing a note or changing the parameters of the instrument.\n     * @function Highcharts.SonificationInstrument#scheduleEventAtTime\n     * @param {number} time Time is given in seconds, where 0 is now.\n     * @param {Highcharts.SonificationInstrumentScheduledEventOptionsObject} params\n     * Parameters for the instrument event.\n     */\n    scheduleEventAtTime(time, params) {\n        const mergedParams = extend(this.curParams, params), freq = SonificationInstrument_defined(params.frequency) ?\n            params.frequency : SonificationInstrument_defined(params.note) ?\n            SonificationInstrument.musicalNoteToFrequency(params.note) :\n            220;\n        if (SonificationInstrument_defined(freq)) {\n            this.synthPatch.playFreqAtTime(time, freq, mergedParams.noteDuration);\n        }\n        if (SonificationInstrument_defined(mergedParams.tremoloDepth) ||\n            SonificationInstrument_defined(mergedParams.tremoloSpeed)) {\n            this.setTremoloAtTime(time, mergedParams.tremoloDepth, mergedParams.tremoloSpeed);\n        }\n        if (SonificationInstrument_defined(mergedParams.pan)) {\n            this.setPanAtTime(time, mergedParams.pan);\n        }\n        if (SonificationInstrument_defined(mergedParams.volume)) {\n            this.setVolumeAtTime(time, mergedParams.volume);\n        }\n        if (SonificationInstrument_defined(mergedParams.lowpassFreq) ||\n            SonificationInstrument_defined(mergedParams.lowpassResonance)) {\n            this.setFilterAtTime('lowpass', time, mergedParams.lowpassFreq, mergedParams.lowpassResonance);\n        }\n        if (SonificationInstrument_defined(mergedParams.highpassFreq) ||\n            SonificationInstrument_defined(mergedParams.highpassResonance)) {\n            this.setFilterAtTime('highpass', time, mergedParams.highpassFreq, mergedParams.highpassResonance);\n        }\n    }\n    /**\n     * Schedule silencing the instrument at a given time offset.\n     * @function Highcharts.SonificationInstrument#silenceAtTime\n     * @param {number} time Time is given in seconds, where 0 is now.\n     */\n    silenceAtTime(time) {\n        this.synthPatch.silenceAtTime(time);\n    }\n    /**\n     * Cancel currently playing sounds and any scheduled actions.\n     * @function Highcharts.SonificationInstrument#cancel\n     */\n    cancel() {\n        this.synthPatch.mute();\n        [\n            this.tremoloDepth && this.tremoloDepth.gain,\n            this.tremoloOsc && this.tremoloOsc.frequency,\n            this.lowpassNode && this.lowpassNode.frequency,\n            this.lowpassNode && this.lowpassNode.Q,\n            this.highpassNode && this.highpassNode.frequency,\n            this.highpassNode && this.highpassNode.Q,\n            this.panNode && this.panNode.pan,\n            this.volumeNode.gain\n        ].forEach((p) => (p && p.cancelScheduledValues(0)));\n    }\n    /**\n     * Stop instrument and destroy it, cleaning up used resources.\n     * @function Highcharts.SonificationInstrument#destroy\n     */\n    destroy() {\n        this.cancel();\n        this.synthPatch.stop();\n        if (this.tremoloOsc) {\n            this.tremoloOsc.stop();\n        }\n        [\n            this.tremoloDepth, this.tremoloOsc, this.lowpassNode,\n            this.highpassNode, this.panNode, this.volumeNode,\n            this.masterVolNode\n        ].forEach(((n) => n && n.disconnect()));\n    }\n    /**\n     * Schedule a pan value at a given time offset.\n     * @private\n     */\n    setPanAtTime(time, pan) {\n        if (this.panNode) {\n            this.panNode.pan.setTargetAtTime(pan, time + this.audioContext.currentTime, SonificationInstrument.rampTime);\n        }\n    }\n    /**\n     * Schedule a filter configuration at a given time offset.\n     * @private\n     */\n    setFilterAtTime(filter, time, frequency, resonance) {\n        const node = this[filter + 'Node'], audioTime = this.audioContext.currentTime + time;\n        if (node) {\n            if (SonificationInstrument_defined(resonance)) {\n                node.Q.setTargetAtTime(resonance, audioTime, SonificationInstrument.rampTime);\n            }\n            if (SonificationInstrument_defined(frequency)) {\n                node.frequency.setTargetAtTime(frequency, audioTime, SonificationInstrument.rampTime);\n            }\n        }\n    }\n    /**\n     * Schedule a volume value at a given time offset.\n     * @private\n     */\n    setVolumeAtTime(time, volume) {\n        if (this.volumeNode) {\n            this.volumeNode.gain.setTargetAtTime(volume, time + this.audioContext.currentTime, SonificationInstrument.rampTime);\n        }\n    }\n    /**\n     * Schedule a tremolo configuration at a given time offset.\n     * @private\n     */\n    setTremoloAtTime(time, depth, speed) {\n        const audioTime = this.audioContext.currentTime + time;\n        if (this.tremoloDepth && SonificationInstrument_defined(depth)) {\n            this.tremoloDepth.gain.setTargetAtTime(depth, audioTime, SonificationInstrument.rampTime);\n        }\n        if (this.tremoloOsc && SonificationInstrument_defined(speed)) {\n            this.tremoloOsc.frequency.setTargetAtTime(15 * speed, audioTime, SonificationInstrument.rampTime);\n        }\n    }\n    /**\n     * Create audio nodes according to instrument capabilities\n     * @private\n     */\n    createNodesFromCapabilities(capabilities) {\n        const ctx = this.audioContext;\n        if (capabilities.pan) {\n            this.panNode = new StereoPannerNode(ctx);\n        }\n        if (capabilities.tremolo) {\n            this.tremoloOsc = new OscillatorNode(ctx, {\n                type: 'sine',\n                frequency: 3\n            });\n            this.tremoloDepth = new GainNode(ctx);\n            this.tremoloOsc.connect(this.tremoloDepth);\n            this.tremoloDepth.connect(this.masterVolNode.gain);\n            this.tremoloOsc.start();\n        }\n        if (capabilities.filters) {\n            this.lowpassNode = new BiquadFilterNode(ctx, {\n                type: 'lowpass',\n                frequency: 20000\n            });\n            this.highpassNode = new BiquadFilterNode(ctx, {\n                type: 'highpass',\n                frequency: 0\n            });\n        }\n    }\n    /**\n     * Connect audio node chain from output down to input, depending on which\n     * nodes exist.\n     * @private\n     */\n    connectCapabilityNodes(input, output) {\n        [\n            this.panNode,\n            this.lowpassNode,\n            this.highpassNode,\n            input\n        ].reduce((prev, cur) => (cur ?\n            (cur.connect(prev), cur) :\n            prev), output);\n    }\n    /**\n     * Get number of notes from C0 from a string like \"F#4\"\n     * @static\n     * @private\n     */\n    static noteStringToC0Distance(note) {\n        const match = note.match(/^([a-g][#b]?)([0-8])$/i), semitone = match ? match[1] : 'a', wholetone = semitone[0].toLowerCase(), accidental = semitone[1], octave = match ? parseInt(match[2], 10) : 4, accidentalOffset = accidental === '#' ?\n            1 : accidental === 'b' ? -1 : 0;\n        return ({\n            c: 0, d: 2, e: 4, f: 5, g: 7, a: 9, b: 11\n        }[wholetone] || 0) + accidentalOffset + octave * 12;\n    }\n    /**\n     * Convert a note value to a frequency.\n     * @static\n     * @function Highcharts.SonificationInstrument#musicalNoteToFrequency\n     * @param {string|number} note\n     * Note to convert. Can be a string 'c0' to 'b8' or a number of semitones\n     * from c0.\n     * @return {number} The converted frequency\n     */\n    static musicalNoteToFrequency(note) {\n        const notesFromC0 = typeof note === 'string' ?\n            this.noteStringToC0Distance(note) : note;\n        return 16.3516 * Math.pow(2, Math.min(notesFromC0, 107) / 12);\n    }\n}\nSonificationInstrument.rampTime = Sonification_SynthPatch.stopRampTime / 4;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Sonification_SonificationInstrument = (SonificationInstrument);\n/* *\n *\n *  API definitions\n *\n * */\n/**\n * Capabilities configuration for a SonificationInstrument.\n * @requires modules/sonification\n * @interface Highcharts.SonificationInstrumentCapabilitiesOptionsObject\n */ /**\n* Whether or not instrument should be able to pan. Defaults to `true`.\n* @name Highcharts.SonificationInstrumentCapabilitiesOptionsObject#pan\n* @type {boolean|undefined}\n*/ /**\n* Whether or not instrument should be able to use tremolo effects. Defaults\n* to `false`.\n* @name Highcharts.SonificationInstrumentCapabilitiesOptionsObject#tremolo\n* @type {boolean|undefined}\n*/ /**\n* Whether or not instrument should be able to use filter effects. Defaults\n* to `false`.\n* @name Highcharts.SonificationInstrumentCapabilitiesOptionsObject#filters\n* @type {boolean|undefined}\n*/\n/**\n * Configuration for a SonificationInstrument.\n * @requires modules/sonification\n * @interface Highcharts.SonificationInstrumentOptionsObject\n */ /**\n* The synth configuration for the instrument. Can be either a string,\n* referencing the instrument presets, or an actual SynthPatch configuration.\n* @name Highcharts.SonificationInstrumentOptionsObject#synthPatch\n* @type {Highcharts.SonificationSynthPreset|Highcharts.SynthPatchOptionsObject}\n* @sample highcharts/demo/all-instruments\n*      All instrument presets\n* @sample highcharts/sonification/custom-instrument\n*      Custom instrument preset\n*/ /**\n* Define additional capabilities for the instrument, such as panning, filters,\n* and tremolo effects.\n* @name Highcharts.SonificationInstrumentOptionsObject#capabilities\n* @type {Highcharts.SonificationInstrumentCapabilitiesOptionsObject|undefined}\n*/ /**\n* A track name to use for this instrument in MIDI export.\n* @name Highcharts.SonificationInstrumentOptionsObject#midiTrackName\n* @type {string|undefined}\n*/\n/**\n * Options for a scheduled event for a SonificationInstrument\n * @requires modules/sonification\n * @interface Highcharts.SonificationInstrumentScheduledEventOptionsObject\n */ /**\n* Number of semitones from c0, or a note string - such as \"c4\" or \"F#6\".\n* @name Highcharts.SonificationInstrumentScheduledEventOptionsObject#note\n* @type {number|string|undefined}\n*/ /**\n* Note frequency in Hertz. Overrides note, if both are given.\n* @name Highcharts.SonificationInstrumentScheduledEventOptionsObject#frequency\n* @type {number|undefined}\n*/ /**\n* Duration to play the note in milliseconds. If not given, the note keeps\n* playing indefinitely\n* @name Highcharts.SonificationInstrumentScheduledEventOptionsObject#noteDuration\n* @type {number|undefined}\n*/ /**\n* Depth/intensity of the tremolo effect - which is a periodic change in\n* volume. From 0 to 1.\n* @name Highcharts.SonificationInstrumentScheduledEventOptionsObject#tremoloDepth\n* @type {number|undefined}\n*/ /**\n* Speed of the tremolo effect, from 0 to 1.\n* @name Highcharts.SonificationInstrumentScheduledEventOptionsObject#tremoloSpeed\n* @type {number|undefined}\n*/ /**\n* Stereo panning value, from -1 (left) to 1 (right).\n* @name Highcharts.SonificationInstrumentScheduledEventOptionsObject#pan\n* @type {number|undefined}\n*/ /**\n* Volume of the instrument, from 0 to 1. Can be set independent of the\n* master/overall volume.\n* @name Highcharts.SonificationInstrumentScheduledEventOptionsObject#volume\n* @type {number|undefined}\n*/ /**\n* Frequency of the lowpass filter, in Hertz.\n* @name Highcharts.SonificationInstrumentScheduledEventOptionsObject#lowpassFreq\n* @type {number|undefined}\n*/ /**\n* Resonance of the lowpass filter, in dB. Can be negative for a dip, or\n* positive for a bump.\n* @name Highcharts.SonificationInstrumentScheduledEventOptionsObject#lowpassResonance\n* @type {number|undefined}\n*/ /**\n* Frequency of the highpass filter, in Hertz.\n* @name Highcharts.SonificationInstrumentScheduledEventOptionsObject#highpassFreq\n* @type {number|undefined}\n*/ /**\n* Resonance of the highpass filter, in dB. Can be negative for a dip, or\n* positive for a bump.\n* @name Highcharts.SonificationInstrumentScheduledEventOptionsObject#highpassResonance\n* @type {number|undefined}\n*/\n(''); // Keep above doclets in JS file\n\n;// ./code/es-modules/Extensions/Sonification/SonificationSpeaker.js\n/* *\n *\n *  (c) 2009-2025 Øystein Moseng\n *\n *  Class representing a speech synthesis voice.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { pick: SonificationSpeaker_pick } = (external_highcharts_src_js_default_default());\n/**\n * The SonificationSpeaker class. This class represents an announcer using\n * speech synthesis. It allows for scheduling speech announcements, as well\n * as speech parameter changes - including rate, volume and pitch.\n *\n * @sample highcharts/demo/sonification-navigation\n *         Demo using SonificationSpeaker directly for some announcements\n *\n * @requires modules/sonification\n *\n * @class\n * @name Highcharts.SonificationSpeaker\n *\n * @param {Highcharts.SonificationSpeakerOptionsObject} options\n *        Configuration for the speaker\n */\nclass SonificationSpeaker {\n    constructor(options) {\n        this.options = options;\n        this.masterVolume = 1;\n        this.synthesis = window.speechSynthesis;\n        if (typeof speechSynthesis.onvoiceschanged !== 'undefined') {\n            speechSynthesis.onvoiceschanged = this.setVoice.bind(this);\n        }\n        this.setVoice();\n        this.scheduled = [];\n    }\n    /**\n     * Say a message using the speaker voice. Interrupts other currently\n     * speaking announcements from this speaker.\n     * @function Highcharts.SonificationSpeaker#say\n     * @param {string} message The message to speak.\n     * @param {SonificationSpeakerOptionsObject} [options]\n     * Optionally override speaker configuration.\n     */\n    say(message, options) {\n        if (this.synthesis) {\n            this.synthesis.cancel();\n            const utterance = new SpeechSynthesisUtterance(message);\n            if (this.voice) {\n                utterance.voice = this.voice;\n            }\n            utterance.rate = options && options.rate || this.options.rate || 1;\n            utterance.pitch = options && options.pitch ||\n                this.options.pitch || 1;\n            utterance.volume = SonificationSpeaker_pick(options && options.volume, this.options.volume, 1) * this.masterVolume;\n            this.synthesis.speak(utterance);\n        }\n    }\n    /**\n     * Schedule a message using the speaker voice.\n     * @function Highcharts.SonificationSpeaker#sayAtTime\n     * @param {number} time\n     * The time offset to speak at, in milliseconds from now.\n     * @param {string} message\n     * The message to speak.\n     * @param {SonificationSpeakerOptionsObject} [options]\n     * Optionally override speaker configuration.\n     */\n    sayAtTime(time, message, options) {\n        this.scheduled.push(setTimeout(this.say.bind(this, message, options), time));\n    }\n    /**\n     * Clear scheduled announcements, and stop current speech.\n     * @function Highcharts.SonificationSpeaker#cancel\n     */\n    cancel() {\n        this.scheduled.forEach(clearTimeout);\n        this.scheduled = [];\n        this.synthesis.cancel();\n    }\n    /**\n     * Stop speech and release any used resources\n     * @private\n     */\n    destroy() {\n        // Ran on TimelineChannel.destroy\n        // (polymorphism with SonificationInstrument).\n        // Currently all we need to do is cancel.\n        this.cancel();\n    }\n    /**\n     * Set speaker overall/master volume modifier. This affects all\n     * announcements, and applies in addition to the individual announcement\n     * volume.\n     * @function Highcharts.SonificationSpeaker#setMasterVolume\n     * @param {number} vol Volume from 0 to 1.\n     */\n    setMasterVolume(vol) {\n        this.masterVolume = vol;\n    }\n    /**\n     * Set the active synthesis voice for the speaker.\n     * @private\n     */\n    setVoice() {\n        if (this.synthesis) {\n            const name = this.options.name, lang = this.options.language || 'en-US', voices = this.synthesis.getVoices(), len = voices.length;\n            let langFallback;\n            for (let i = 0; i < len; ++i) {\n                if (name && voices[i].name === name) {\n                    this.voice = voices[i];\n                    return;\n                }\n                if (!langFallback && voices[i].lang === lang) {\n                    langFallback = voices[i];\n                    if (!name) {\n                        break;\n                    }\n                }\n            }\n            this.voice = langFallback;\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Sonification_SonificationSpeaker = (SonificationSpeaker);\n/* *\n *\n *  API declarations\n *\n * */\n/**\n * Configuration for a SonificationSpeaker.\n * @requires modules/sonification\n * @interface Highcharts.SonificationSpeakerOptionsObject\n */ /**\n* Name of the voice synthesis to use. If not found, reverts to the default\n* voice for the language chosen.\n* @name Highcharts.SonificationSpeakerOptionsObject#name\n* @type {string|undefined}\n*/ /**\n* The language of the voice synthesis. Defaults to `\"en-US\"`.\n* @name Highcharts.SonificationSpeakerOptionsObject#language\n* @type {string|undefined}\n*/ /**\n* The pitch modifier of the voice. Defaults to `1`. Set higher for a higher\n* voice pitch.\n* @name Highcharts.SonificationSpeakerOptionsObject#pitch\n* @type {number|undefined}\n*/ /**\n* The speech rate modifier. Defaults to `1`.\n* @name Highcharts.SonificationSpeakerOptionsObject#rate\n* @type {number|undefined}\n*/ /**\n* The speech volume, from 0 to 1. Defaults to `1`.\n* @name Highcharts.SonificationSpeakerOptionsObject#volume\n* @type {number|undefined}\n*/\n(''); // Keep above doclets in JS file\n\n;// ./code/es-modules/Extensions/Sonification/TimelineChannel.js\n/* *\n *\n *  (c) 2009-2025 Øystein Moseng\n *\n *  Class representing a TimelineChannel with sonification events to play.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/**\n * Represents a channel of TimelineEvents for an engine (either an instrument\n * or a speaker).\n * @private\n */\nclass TimelineChannel {\n    constructor(type, engine, showPlayMarker = false, events, muted) {\n        this.type = type;\n        this.engine = engine;\n        this.showPlayMarker = showPlayMarker;\n        this.muted = muted;\n        this.events = events || [];\n    }\n    addEvent(event) {\n        const lastEvent = this.events[this.events.length - 1];\n        if (lastEvent && event.time < lastEvent.time) {\n            // Ensure we are sorted by time, so insert at the right place\n            let i = this.events.length;\n            while (i-- && this.events[i].time > event.time) { /* */ }\n            this.events.splice(i + 1, 0, event);\n        }\n        else {\n            this.events.push(event);\n        }\n        return event;\n    }\n    mute() {\n        this.muted = true;\n    }\n    unmute() {\n        this.muted = false;\n    }\n    cancel() {\n        this.engine.cancel();\n    }\n    destroy() {\n        this.engine.destroy();\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Sonification_TimelineChannel = (TimelineChannel);\n/* *\n *\n *  API declarations\n *\n * */\n/**\n * A TimelineEvent object represents a scheduled audio event to play for a\n * SonificationTimeline.\n * @requires modules/sonification\n * @interface Highcharts.SonificationTimelineEvent\n */ /**\n* Time is given in milliseconds, where 0 is now.\n* @name Highcharts.SonificationTimelineEvent#time\n* @type {number}\n*/ /**\n* A reference to a data point related to the TimelineEvent. Populated when\n* sonifying points.\n* @name Highcharts.SonificationTimelineEvent#relatedPoint\n* @type {Highcharts.Point|undefined}\n*/ /**\n* Options for an instrument event to be played.\n* @name Highcharts.SonificationTimelineEvent#instrumentEventOptions\n* @type {Highcharts.SonificationInstrumentScheduledEventOptionsObject|undefined}\n*/ /**\n* Options for a speech event to be played.\n* @name Highcharts.SonificationTimelineEvent#speechOptions\n* @type {Highcharts.SonificationSpeakerOptionsObject|undefined}\n*/ /**\n* The message to speak for speech events.\n* @name Highcharts.SonificationTimelineEvent#message\n* @type {string|undefined}\n*/ /**\n* Callback to call when playing the event.\n* @name Highcharts.SonificationTimelineEvent#callback\n* @type {Function|undefined}\n*/\n(''); // Keep above doclets in JS file\n\n;// ./code/es-modules/Extensions/Sonification/MIDI.js\n/* *\n *\n *  (c) 2009-2025 Øystein Moseng\n *\n *  Small MIDI file writer for sonification export.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n/* eslint-disable no-multi-spaces */\n\n\n\nconst { pick: MIDI_pick } = (external_highcharts_src_js_default_default());\nconst freqToNote = (f) => Math.round(12 * Math.log(f) / Math.LN2 - 48.37632), b = (byte, n) => n >>> 8 * byte & 0xFF, getHeader = (nTracks) => [\n    0x4D, 0x54, 0x68, 0x64, // HD_TYPE\n    0, 0, 0, 6, // HD_SIZE\n    0, nTracks > 1 ? 1 : 0, // HD_FORMAT\n    b(1, nTracks), b(0, nTracks), // HD_NTRACKS\n    // SMTPE: 0xE7 0x28\n    // -25/40 time div gives us millisecond SMTPE, but not widely supported.\n    1, 0xF4 // HD_TIMEDIV, 500 ticks per beat = millisecond at 120bpm\n], timeInfo = [0, 0xFF, 0x51, 0x03, 0x07, 0xA1, 0x20], // META_TEMPO\nvarLenEnc = (n) => {\n    let buf = n & 0x7F;\n    const res = [];\n    while (n >>= 7) { // eslint-disable-line no-cond-assign\n        buf <<= 8;\n        buf |= (n & 0x7F) | 0x80;\n    }\n    while (true) { // eslint-disable-line no-constant-condition\n        res.push(buf & 0xFF);\n        if (buf & 0x80) {\n            buf >>= 8;\n        }\n        else {\n            break;\n        }\n    }\n    return res;\n}, toMIDIEvents = (events) => {\n    let cachedVel, cachedDur;\n    const res = [], add = (el) => {\n        let ix = res.length;\n        while (ix-- && res[ix].timeMS > el.timeMS) { /* */ }\n        res.splice(ix + 1, 0, el);\n    };\n    events.forEach((e) => {\n        const o = e.instrumentEventOptions || {}, t = e.time, dur = cachedDur = MIDI_pick(o.noteDuration, cachedDur), tNOF = dur && e.time + dur, ctrl = [{\n                valMap: (n) => 64 + 63 * n & 0x7F,\n                data: {\n                    0x0A: o.pan, // Use MSB only, no need for fine adjust\n                    0x5C: o.tremoloDepth,\n                    0x5D: o.tremoloSpeed\n                }\n            }, {\n                valMap: (n) => 127 * n / 20000 & 0x7F,\n                data: {\n                    0x4A: o.lowpassFreq,\n                    0x4B: o.highpassFreq\n                }\n            }, {\n                valMap: (n) => 63 * Math.min(18, Math.max(-18, n)) / 18 + 63 & 0x7F,\n                data: {\n                    0x47: o.lowpassResonance,\n                    0x4C: o.highpassResonance\n                }\n            }], v = cachedVel = o.volume === void 0 ?\n            MIDI_pick(cachedVel, 127) : 127 * o.volume & 0x7F, freq = o.frequency, note = o.note || 0, noteVal = 12 + (freq ? freqToNote(freq) : // MIDI note #0 is C-1\n            typeof note === 'string' ? Sonification_SonificationInstrument\n                .noteStringToC0Distance(note) : note) & 0x7F;\n        // CTRL_CHANGE events\n        ctrl.forEach((ctrlDef) => Object.keys(ctrlDef.data)\n            .forEach((ctrlSignal) => {\n            const val = ctrlDef.data[ctrlSignal];\n            if (val !== void 0) {\n                add({\n                    timeMS: t,\n                    type: 'CTRL_CHG',\n                    data: [\n                        0xB0, parseInt(ctrlSignal, 10),\n                        ctrlDef.valMap(val)\n                    ]\n                });\n            }\n        }));\n        // NON/NOF\n        if (tNOF) {\n            add({ timeMS: t, type: 'NON', data: [0x90, noteVal, v] });\n            add({ timeMS: tNOF, type: 'NOF', data: [0x80, noteVal, v] });\n        }\n    });\n    return res;\n}, getMetaEvents = (midiTrackName, midiInstrument) => {\n    const events = [];\n    if (midiInstrument) {\n        // Program Change MIDI event\n        events.push(0, 0xC0, midiInstrument & 0x7F);\n    }\n    if (midiTrackName) {\n        // Track name meta event\n        const textArr = [];\n        for (let i = 0; i < midiTrackName.length; ++i) {\n            const code = midiTrackName.charCodeAt(i);\n            if (code < 128) { // Keep ASCII only\n                textArr.push(code);\n            }\n        }\n        return events.concat([0, 0xFF, 0x03], varLenEnc(textArr.length), textArr);\n    }\n    return events;\n}, getTrackChunk = (events, addTimeInfo, midiTrackName, midiInstrument) => {\n    let prevTime = 0;\n    const metaEvents = getMetaEvents(midiTrackName, midiInstrument), trackEvents = toMIDIEvents(events).reduce((data, e) => {\n        const t = varLenEnc(e.timeMS - prevTime);\n        prevTime = e.timeMS;\n        return data.concat(t, e.data);\n    }, []);\n    const trackEnd = [0, 0xFF, 0x2F, 0], size = (addTimeInfo ? timeInfo.length : 0) +\n        metaEvents.length +\n        trackEvents.length + trackEnd.length;\n    return [\n        0x4D, 0x54, 0x72, 0x6B, // TRK_TYPE\n        b(3, size), b(2, size), // TRK_SIZE\n        b(1, size), b(0, size)\n    ].concat(addTimeInfo ? timeInfo : [], metaEvents, trackEvents, trackEnd // SYSEX_TRACK_END\n    );\n};\n/**\n * Get MIDI data from a set of Timeline instrument channels.\n *\n * Outputs multi-track MIDI for Timelines with multiple channels.\n *\n * @private\n */\nfunction toMIDI(channels) {\n    const channelsToAdd = channels.filter((c) => !!c.events.length), numCh = channelsToAdd.length, multiCh = numCh > 1;\n    return new Uint8Array(getHeader(multiCh ? numCh + 1 : numCh).concat(multiCh ? getTrackChunk([], true) : [], // Time info only\n    channelsToAdd.reduce((chunks, channel) => {\n        const engine = channel.engine;\n        return chunks.concat(getTrackChunk(channel.events, !multiCh, engine.midiTrackName, engine.midiInstrument));\n    }, [])));\n}\n/* harmony default export */ const MIDI = (toMIDI);\n\n;// ./code/es-modules/Extensions/DownloadURL.js\n/* *\n *\n *  (c) 2015-2025 Oystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Mixin for downloading content in the browser\n *\n * */\n\n/* *\n *\n *  Imports\n *\n * */\n\nconst { isSafari, win, win: { document: doc } } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Constants\n *\n * */\nconst domurl = win.URL || win.webkitURL || win;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Convert base64 dataURL to Blob if supported, otherwise returns undefined.\n * @private\n * @function Highcharts.dataURLtoBlob\n * @param {string} dataURL\n *        URL to convert\n * @return {string|undefined}\n *         Blob\n */\nfunction dataURLtoBlob(dataURL) {\n    const parts = dataURL\n        .replace(/filename=.*;/, '')\n        .match(/data:([^;]*)(;base64)?,([A-Z+\\d\\/]+)/i);\n    if (parts &&\n        parts.length > 3 &&\n        (win.atob) &&\n        win.ArrayBuffer &&\n        win.Uint8Array &&\n        win.Blob &&\n        (domurl.createObjectURL)) {\n        // Try to convert data URL to Blob\n        const binStr = win.atob(parts[3]), buf = new win.ArrayBuffer(binStr.length), binary = new win.Uint8Array(buf);\n        for (let i = 0; i < binary.length; ++i) {\n            binary[i] = binStr.charCodeAt(i);\n        }\n        return domurl\n            .createObjectURL(new win.Blob([binary], { 'type': parts[1] }));\n    }\n}\n/**\n * Download a data URL in the browser. Can also take a blob as first param.\n *\n * @private\n * @function Highcharts.downloadURL\n * @param {string|global.URL} dataURL\n *        The dataURL/Blob to download\n * @param {string} filename\n *        The name of the resulting file (w/extension)\n * @return {void}\n */\nfunction downloadURL(dataURL, filename) {\n    const nav = win.navigator, a = doc.createElement('a');\n    // IE specific blob implementation\n    // Don't use for normal dataURLs\n    if (typeof dataURL !== 'string' &&\n        !(dataURL instanceof String) &&\n        nav.msSaveOrOpenBlob) {\n        nav.msSaveOrOpenBlob(dataURL, filename);\n        return;\n    }\n    dataURL = '' + dataURL;\n    if (nav.userAgent.length > 1000 /* RegexLimits.shortLimit */) {\n        throw new Error('Input too long');\n    }\n    const // Some browsers have limitations for data URL lengths. Try to convert\n    // to Blob or fall back. Edge always needs that blob.\n    isOldEdgeBrowser = /Edge\\/\\d+/.test(nav.userAgent), \n    // Safari on iOS needs Blob in order to download PDF\n    safariBlob = (isSafari &&\n        typeof dataURL === 'string' &&\n        dataURL.indexOf('data:application/pdf') === 0);\n    if (safariBlob || isOldEdgeBrowser || dataURL.length > 2000000) {\n        dataURL = dataURLtoBlob(dataURL) || '';\n        if (!dataURL) {\n            throw new Error('Failed to convert to blob');\n        }\n    }\n    // Try HTML5 download attr if supported\n    if (typeof a.download !== 'undefined') {\n        a.href = dataURL;\n        a.download = filename; // HTML5 download attribute\n        doc.body.appendChild(a);\n        a.click();\n        doc.body.removeChild(a);\n    }\n    else {\n        // No download attr, just opening data URI\n        try {\n            if (!win.open(dataURL, 'chart')) {\n                throw new Error('Failed to open window');\n            }\n        }\n        catch {\n            // If window.open failed, try location.href\n            win.location.href = dataURL;\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst DownloadURL = {\n    dataURLtoBlob,\n    downloadURL\n};\n/* harmony default export */ const Extensions_DownloadURL = (DownloadURL);\n\n;// ./code/es-modules/Extensions/Sonification/SonificationTimeline.js\n/* *\n *\n *  (c) 2009-2025 Øystein Moseng\n *\n *  Class representing a Timeline with sonification events to play.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { downloadURL: SonificationTimeline_downloadURL } = Extensions_DownloadURL;\n\nconst { defined: SonificationTimeline_defined, find, merge } = (external_highcharts_src_js_default_default());\n/**\n * Get filtered channels. Timestamps are compensated, so that the first\n * event starts immediately.\n * @private\n */\nfunction filterChannels(filter, channels) {\n    const filtered = channels.map((channel) => {\n        channel.cancel();\n        return {\n            channel,\n            filteredEvents: channel.muted ?\n                [] : channel.events.filter(filter)\n        };\n    }), minTime = filtered.reduce((acc, cur) => Math.min(acc, cur.filteredEvents.length ?\n        cur.filteredEvents[0].time : Infinity), Infinity);\n    return filtered.map((c) => (new Sonification_TimelineChannel(c.channel.type, c.channel.engine, c.channel.showPlayMarker, c.filteredEvents.map((e) => merge(e, { time: e.time - minTime })), c.channel.muted)));\n}\n/**\n * The SonificationTimeline class. This class represents a timeline of\n * audio events scheduled to play. It provides functionality for manipulating\n * and navigating the timeline.\n * @private\n */\nclass SonificationTimeline {\n    constructor(options, chart) {\n        this.chart = chart;\n        this.isPaused = false;\n        this.isPlaying = false;\n        this.channels = [];\n        this.scheduledCallbacks = [];\n        this.playTimestamp = 0;\n        this.resumeFromTime = 0;\n        this.options = options || {};\n    }\n    // Add a channel, optionally with events, to be played.\n    // Note: Only one speech channel is supported at a time.\n    addChannel(type, engine, showPlayMarker = false, events) {\n        if (type === 'instrument' &&\n            !engine.scheduleEventAtTime ||\n            type === 'speech' &&\n                !engine.sayAtTime) {\n            throw new Error('Highcharts Sonification: Invalid channel engine.');\n        }\n        const channel = new Sonification_TimelineChannel(type, engine, showPlayMarker, events);\n        this.channels.push(channel);\n        return channel;\n    }\n    // Play timeline, optionally filtering out only some of the events to play.\n    // Note that if not all instrument parameters are updated on each event,\n    // parameters may update differently depending on the events filtered out,\n    // since some of the events that update parameters can be filtered out too.\n    // The filterPersists argument determines whether or not the filter persists\n    // after e.g. pausing and resuming. Usually this should be true.\n    play(filter, filterPersists = true, resetAfter = true, onEnd) {\n        if (this.isPlaying) {\n            this.cancel();\n        }\n        else {\n            this.clearScheduledCallbacks();\n        }\n        this.onEndArgument = onEnd;\n        this.playTimestamp = Date.now();\n        this.resumeFromTime = 0;\n        this.isPaused = false;\n        this.isPlaying = true;\n        const skipThreshold = this.options.skipThreshold || 2, onPlay = this.options.onPlay, showTooltip = this.options.showTooltip, showCrosshair = this.options.showCrosshair, channels = filter ?\n            filterChannels(filter, this.playingChannels || this.channels) :\n            this.channels, getEventKeysSignature = (e) => Object.keys(e.speechOptions || {})\n            .concat(Object.keys(e.instrumentEventOptions || {}))\n            .join(), pointsPlayed = [];\n        if (filterPersists) {\n            this.playingChannels = channels;\n        }\n        if (onPlay) {\n            onPlay({ chart: this.chart, timeline: this });\n        }\n        let maxTime = 0;\n        channels.forEach((channel) => {\n            if (channel.muted) {\n                return;\n            }\n            const numEvents = channel.events.length;\n            let lastCallbackTime = -Infinity, lastEventTime = -Infinity, lastEventKeys = '';\n            maxTime = Math.max(channel.events[numEvents - 1] &&\n                channel.events[numEvents - 1].time || 0, maxTime);\n            for (let i = 0; i < numEvents; ++i) {\n                const e = channel.events[i], keysSig = getEventKeysSignature(e);\n                // Optimize by skipping extremely close events (<2ms apart by\n                // default), as long as they don't introduce new event options\n                if (keysSig === lastEventKeys &&\n                    e.time - lastEventTime < skipThreshold) {\n                    continue;\n                }\n                lastEventKeys = keysSig;\n                lastEventTime = e.time;\n                if (channel.type === 'instrument') {\n                    channel.engine\n                        .scheduleEventAtTime(e.time / 1000, e.instrumentEventOptions || {});\n                }\n                else {\n                    channel.engine.sayAtTime(e.time, e.message || '', e.speechOptions || {});\n                }\n                const point = e.relatedPoint, chart = point && point.series && point.series.chart, needsCallback = e.callback ||\n                    point && (showTooltip || showCrosshair) &&\n                        channel.showPlayMarker !== false &&\n                        (e.time - lastCallbackTime > 50 || i === numEvents - 1);\n                if (point) {\n                    pointsPlayed.push(point);\n                }\n                if (needsCallback) {\n                    this.scheduledCallbacks.push(setTimeout(() => {\n                        if (e.callback) {\n                            e.callback();\n                        }\n                        if (point) {\n                            if (showCrosshair) {\n                                const s = point.series;\n                                if (s && s.xAxis && s.xAxis.crosshair) {\n                                    s.xAxis.drawCrosshair(void 0, point);\n                                }\n                                if (s && s.yAxis && s.yAxis.crosshair) {\n                                    s.yAxis.drawCrosshair(void 0, point);\n                                }\n                            }\n                            if (showTooltip && !(\n                            // Don't re-hover if shared tooltip\n                            chart && chart.hoverPoints &&\n                                chart.hoverPoints.length > 1 &&\n                                find(chart.hoverPoints, (p) => p === point) &&\n                                // Stock issue w/Navigator\n                                point.onMouseOver)) {\n                                point.onMouseOver();\n                            }\n                        }\n                    }, e.time));\n                    lastCallbackTime = e.time;\n                }\n            }\n        });\n        const onEndOpt = this.options.onEnd, onStop = this.options.onStop;\n        this.scheduledCallbacks.push(setTimeout(() => {\n            const chart = this.chart, context = { chart, timeline: this, pointsPlayed };\n            this.isPlaying = false;\n            if (resetAfter) {\n                this.resetPlayState();\n            }\n            if (onStop) {\n                onStop(context);\n            }\n            if (onEndOpt) {\n                onEndOpt(context);\n            }\n            if (onEnd) {\n                onEnd(context);\n            }\n            if (chart) {\n                if (chart.tooltip) {\n                    chart.tooltip.hide(0);\n                }\n                if (chart.hoverSeries) {\n                    chart.hoverSeries.onMouseOut();\n                }\n                chart.axes.forEach((a) => a.hideCrosshair());\n            }\n        }, maxTime + 250));\n        this.resumeFromTime = filterPersists ? maxTime : this.getLength();\n    }\n    // Pause for later resuming. Returns current timestamp to resume from.\n    pause() {\n        this.isPaused = true;\n        this.cancel();\n        this.resumeFromTime = Date.now() - this.playTimestamp - 10;\n        return this.resumeFromTime;\n    }\n    // Get current time\n    getCurrentTime() {\n        return this.isPlaying ?\n            Date.now() - this.playTimestamp :\n            this.resumeFromTime;\n    }\n    // Get length of timeline in milliseconds\n    getLength() {\n        return this.channels.reduce((maxTime, channel) => {\n            const lastEvent = channel.events[channel.events.length - 1];\n            return lastEvent ? Math.max(lastEvent.time, maxTime) : maxTime;\n        }, 0);\n    }\n    // Resume from paused\n    resume() {\n        if (this.playingChannels) {\n            const resumeFrom = this.resumeFromTime - 50;\n            this.play((e) => e.time > resumeFrom, false, false, this.onEndArgument);\n            this.playTimestamp -= resumeFrom;\n        }\n        else {\n            this.play(void 0, false, false, this.onEndArgument);\n        }\n    }\n    // Play a short moment, then pause, setting the cursor to the final\n    // event's time.\n    anchorPlayMoment(eventFilter, onEnd) {\n        if (this.isPlaying) {\n            this.pause();\n        }\n        let finalEventTime = 0;\n        this.play((e, ix, arr) => {\n            // We have to keep track of final event time ourselves, since\n            // play() messes with the time internally upon filtering.\n            const res = eventFilter(e, ix, arr);\n            if (res && e.time > finalEventTime) {\n                finalEventTime = e.time;\n            }\n            return res;\n        }, false, false, onEnd);\n        this.playingChannels = this.playingChannels || this.channels;\n        this.isPaused = true;\n        this.isPlaying = false;\n        this.resumeFromTime = finalEventTime;\n    }\n    // Play event(s) occurring next/prev from paused state.\n    playAdjacent(next, onEnd, onBoundaryHit, eventFilter) {\n        if (this.isPlaying) {\n            this.pause();\n        }\n        const fromTime = this.resumeFromTime, closestTime = this.channels.reduce((time, channel) => {\n            // Adapted binary search since events are sorted by time\n            const events = eventFilter ?\n                channel.events.filter(eventFilter) : channel.events;\n            let s = 0, e = events.length, lastValidTime = time;\n            while (s < e) {\n                const mid = (s + e) >> 1, t = events[mid].time, cmp = t - fromTime;\n                if (cmp > 0) { // Ahead\n                    if (next && t < lastValidTime) {\n                        lastValidTime = t;\n                    }\n                    e = mid;\n                }\n                else if (cmp < 0) { // Behind\n                    if (!next && t > lastValidTime) {\n                        lastValidTime = t;\n                    }\n                    s = mid + 1;\n                }\n                else { // Same as from time\n                    if (next) {\n                        s = mid + 1;\n                    }\n                    else {\n                        e = mid;\n                    }\n                }\n            }\n            return lastValidTime;\n        }, next ? Infinity : -Infinity), margin = 0.02;\n        if (closestTime === Infinity || closestTime === -Infinity) {\n            if (onBoundaryHit) {\n                onBoundaryHit({\n                    chart: this.chart, timeline: this, attemptedNext: next\n                });\n            }\n            return;\n        }\n        this.anchorPlayMoment((e, ix, arr) => {\n            const withinTime = next ?\n                e.time > fromTime && e.time <= closestTime + margin :\n                e.time < fromTime && e.time >= closestTime - margin;\n            return eventFilter ? withinTime && eventFilter(e, ix, arr) :\n                withinTime;\n        }, onEnd);\n    }\n    // Play event with related point, where the value of a prop on the\n    // related point is closest to a target value.\n    // Note: not very efficient.\n    playClosestToPropValue(prop, targetVal, onEnd, onBoundaryHit, eventFilter) {\n        const filter = (e, ix, arr) => !!(eventFilter ?\n            eventFilter(e, ix, arr) && e.relatedPoint :\n            e.relatedPoint);\n        let closestValDiff = Infinity, closestEvent = null;\n        (this.playingChannels || this.channels).forEach((channel) => {\n            const events = channel.events;\n            let i = events.length;\n            while (i--) {\n                if (!filter(events[i], i, events)) {\n                    continue;\n                }\n                const val = events[i].relatedPoint[prop], diff = SonificationTimeline_defined(val) && Math.abs(targetVal - val);\n                if (diff !== false && diff < closestValDiff) {\n                    closestValDiff = diff;\n                    closestEvent = events[i];\n                }\n            }\n        });\n        if (closestEvent) {\n            this.play((e) => !!(closestEvent &&\n                e.time < closestEvent.time + 1 &&\n                e.time > closestEvent.time - 1 &&\n                e.relatedPoint === closestEvent.relatedPoint), false, false, onEnd);\n            this.playingChannels = this.playingChannels || this.channels;\n            this.isPaused = true;\n            this.isPlaying = false;\n            this.resumeFromTime = closestEvent.time;\n        }\n        else if (onBoundaryHit) {\n            onBoundaryHit({ chart: this.chart, timeline: this });\n        }\n    }\n    // Get timeline events that are related to a certain point.\n    // Note: Point grouping may cause some points not to have a\n    //  related point in the timeline.\n    getEventsForPoint(point) {\n        return this.channels.reduce((events, channel) => {\n            const pointEvents = channel.events\n                .filter((e) => e.relatedPoint === point);\n            return events.concat(pointEvents);\n        }, []);\n    }\n    // Divide timeline into 100 parts of equal time, and play one of them.\n    // Used for scrubbing.\n    // Note: Should be optimized?\n    playSegment(segment, onEnd) {\n        const numSegments = 100;\n        const eventTimes = {\n            first: Infinity,\n            last: -Infinity\n        };\n        this.channels.forEach((c) => {\n            if (c.events.length) {\n                eventTimes.first = Math.min(c.events[0].time, eventTimes.first);\n                eventTimes.last = Math.max(c.events[c.events.length - 1].time, eventTimes.last);\n            }\n        });\n        if (eventTimes.first < Infinity) {\n            const segmentSize = (eventTimes.last - eventTimes.first) / numSegments, fromTime = eventTimes.first + segment * segmentSize, toTime = fromTime + segmentSize;\n            // Binary search, do we have any events within time range?\n            if (!this.channels.some((c) => {\n                const events = c.events;\n                let s = 0, e = events.length;\n                while (s < e) {\n                    const mid = (s + e) >> 1, t = events[mid].time;\n                    if (t < fromTime) { // Behind\n                        s = mid + 1;\n                    }\n                    else if (t > toTime) { // Ahead\n                        e = mid;\n                    }\n                    else {\n                        return true;\n                    }\n                }\n                return false;\n            })) {\n                return; // If not, don't play - avoid cancelling current play\n            }\n            this.play((e) => e.time >= fromTime && e.time <= toTime, false, false, onEnd);\n            this.playingChannels = this.playingChannels || this.channels;\n            this.isPaused = true;\n            this.isPlaying = false;\n            this.resumeFromTime = toTime;\n        }\n    }\n    // Get last played / current point\n    // Since events are scheduled we can't just store points as we play them\n    getLastPlayedPoint(filter) {\n        const curTime = this.getCurrentTime(), channels = this.playingChannels || this.channels;\n        let closestDiff = Infinity, closestPoint = null;\n        channels.forEach((c) => {\n            const events = c.events.filter((e, ix, arr) => !!(e.relatedPoint && e.time <= curTime &&\n                (!filter || filter(e, ix, arr)))), closestEvent = events[events.length - 1];\n            if (closestEvent) {\n                const closestTime = closestEvent.time, diff = Math.abs(closestTime - curTime);\n                if (diff < closestDiff) {\n                    closestDiff = diff;\n                    closestPoint = closestEvent.relatedPoint;\n                }\n            }\n        });\n        return closestPoint;\n    }\n    // Reset play/pause state so that a later call to resume() will start over\n    reset() {\n        if (this.isPlaying) {\n            this.cancel();\n        }\n        this.resetPlayState();\n    }\n    cancel() {\n        const onStop = this.options.onStop;\n        if (onStop) {\n            onStop({ chart: this.chart, timeline: this });\n        }\n        this.isPlaying = false;\n        this.channels.forEach((c) => c.cancel());\n        if (this.playingChannels && this.playingChannels !== this.channels) {\n            this.playingChannels.forEach((c) => c.cancel());\n        }\n        this.clearScheduledCallbacks();\n        this.resumeFromTime = 0;\n    }\n    destroy() {\n        this.cancel();\n        if (this.playingChannels && this.playingChannels !== this.channels) {\n            this.playingChannels.forEach((c) => c.destroy());\n        }\n        this.channels.forEach((c) => c.destroy());\n    }\n    setMasterVolume(vol) {\n        this.channels.forEach((c) => c.engine.setMasterVolume(vol));\n    }\n    getMIDIData() {\n        return MIDI(this.channels.filter((c) => c.type === 'instrument'));\n    }\n    downloadMIDI(filename) {\n        const data = this.getMIDIData(), name = (filename ||\n            this.chart &&\n                this.chart.options.title &&\n                this.chart.options.title.text ||\n            'chart') + '.mid', blob = new Blob([data], { type: 'application/octet-stream' }), url = window.URL.createObjectURL(blob);\n        SonificationTimeline_downloadURL(url, name);\n        window.URL.revokeObjectURL(url);\n    }\n    resetPlayState() {\n        delete this.playingChannels;\n        delete this.onEndArgument;\n        this.playTimestamp = this.resumeFromTime = 0;\n        this.isPaused = false;\n    }\n    clearScheduledCallbacks() {\n        this.scheduledCallbacks.forEach(clearTimeout);\n        this.scheduledCallbacks = [];\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Sonification_SonificationTimeline = (SonificationTimeline);\n/* *\n *\n *  API declarations\n *\n * */\n/**\n * Filter callback for filtering timeline events on a SonificationTimeline.\n *\n * @callback Highcharts.SonificationTimelineFilterCallback\n *\n * @param {Highcharts.SonificationTimelineEvent} e TimelineEvent being filtered\n *\n * @param {number} ix Index of TimelineEvent in current event array\n *\n * @param {Array<Highcharts.SonificationTimelineEvent>} arr The current event array\n *\n * @return {boolean}\n * The function should return true if the TimelineEvent should be included,\n * false otherwise.\n */\n(''); // Keep above doclets in JS file\n\n;// external [\"../highcharts.js\",\"default\",\"Templating\"]\nconst external_highcharts_src_js_default_Templating_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].Templating;\nvar external_highcharts_src_js_default_Templating_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_Templating_namespaceObject);\n;// ./code/es-modules/Extensions/Sonification/TimelineFromChart.js\n/* *\n *\n *  (c) 2009-2025 Øystein Moseng\n *\n *  Build a timeline from a chart.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\nconst { clamp: TimelineFromChart_clamp, defined: TimelineFromChart_defined, extend: TimelineFromChart_extend, getNestedProperty, merge: TimelineFromChart_merge, pick: TimelineFromChart_pick } = (external_highcharts_src_js_default_default());\n\nconst { format } = (external_highcharts_src_js_default_Templating_default());\nconst isNoteDefinition = (str) => (/^([a-g][#b]?)[0-8]$/i).test(str);\n/**\n * Get the value of a point property from string.\n * @private\n */\nfunction getPointPropValue(point, prop) {\n    let ret;\n    if (prop) {\n        ret = point[prop];\n        if (typeof ret === 'number') {\n            return ret;\n        }\n        ret = getNestedProperty(prop, point);\n    }\n    return typeof ret === 'number' ? ret : void 0;\n}\n/**\n * Get chart wide min/max for a set of props, as well as per\n * series min/max for selected props.\n * @private\n */\nfunction getChartExtremesForProps(chart, props, perSeriesProps) {\n    const series = chart.series, numProps = props.length, numSeriesProps = perSeriesProps.length, initCache = (propList) => propList.reduce((cache, prop) => {\n        ((cache[prop] = { min: Infinity, max: -Infinity }), cache);\n        return cache;\n    }, {}), updateCache = (cache, point, prop) => {\n        let val = point[prop];\n        if (val === void 0) {\n            val = getNestedProperty(prop, point);\n        }\n        if (typeof val === 'number') {\n            cache[prop].min = Math.min(cache[prop].min, val);\n            cache[prop].max = Math.max(cache[prop].max, val);\n        }\n    }, globalExtremes = initCache(props);\n    let i = series.length;\n    const allSeriesExtremes = new Array(i);\n    while (i--) {\n        const seriesExtremes = initCache(perSeriesProps);\n        const opts = series[i].options;\n        if (!series[i].visible ||\n            opts && opts.sonification && opts.sonification.enabled === false) {\n            continue;\n        }\n        const points = series[i].points || [];\n        let j = points.length;\n        while (j--) {\n            let k = numProps;\n            while (k--) {\n                updateCache(globalExtremes, points[j], props[k]);\n            }\n            k = numSeriesProps;\n            while (k--) {\n                updateCache(seriesExtremes, points[j], perSeriesProps[k]);\n            }\n        }\n        allSeriesExtremes[i] = seriesExtremes;\n    }\n    return {\n        globalExtremes,\n        seriesExtremes: allSeriesExtremes\n    };\n}\n/**\n * Build a cache of prop extremes for the chart. Goes through\n * options to find out which props are needed.\n * @private\n */\nfunction getPropMetrics(chart) {\n    const globalOpts = chart.options.sonification ||\n        {}, defaultInstrMapping = (globalOpts.defaultInstrumentOptions || {})\n        .mapping || { time: 'x', pitch: 'y' }, defaultSpeechMapping = globalOpts.defaultSpeechOptions &&\n        globalOpts.defaultSpeechOptions.mapping || {}, seriesTimeProps = [], commonTimeProps = {}, addTimeProp = (prop, seriesIx) => {\n        if (seriesIx !== null) {\n            seriesTimeProps[seriesIx] =\n                seriesTimeProps[seriesIx] || {};\n            seriesTimeProps[seriesIx][prop] = true;\n        }\n        else {\n            commonTimeProps[prop] = true;\n        }\n    }, props = {}, perSeriesProps = {}, addPropFromMappingParam = (param, val, seriesIx) => {\n        const removeInvertedFlag = (s) => (s.charAt(0) === '-' ? s.slice(1) : s);\n        if (typeof val === 'string' && param !== 'text') {\n            if (param === 'pitch' && isNoteDefinition(val)) {\n                return;\n            }\n            if (param === 'time') {\n                perSeriesProps[val] = true;\n                addTimeProp(val, seriesIx);\n            }\n            props[removeInvertedFlag(val)] = true;\n            return;\n        }\n        const paramOpts = val;\n        if (paramOpts && paramOpts.mapTo &&\n            typeof paramOpts.mapTo === 'string') {\n            const mapTo = removeInvertedFlag(paramOpts.mapTo);\n            if (param === 'time') {\n                addTimeProp(mapTo, seriesIx);\n            }\n            if (param === 'time' || paramOpts.within === 'series') {\n                perSeriesProps[mapTo] = true;\n            }\n            props[mapTo] = true;\n            return;\n        }\n        if (['tremolo', 'lowpass', 'highpass'].indexOf(param) > -1 &&\n            typeof val === 'object') {\n            Object.keys(val).forEach((subParam) => addPropFromMappingParam(subParam, val[subParam], seriesIx));\n        }\n    }, addPropsFromMappingOptions = (mapping, seriesIx) => {\n        (Object.keys(mapping)).forEach((param) => addPropFromMappingParam(param, mapping[param], seriesIx));\n    }, addPropsFromContextTracks = (tracks) => tracks.forEach((track) => {\n        props[track.valueProp || 'x'] =\n            perSeriesProps[track.valueProp || 'x'] = true;\n    });\n    addPropsFromMappingOptions(defaultInstrMapping, null);\n    addPropsFromMappingOptions(defaultSpeechMapping, null);\n    addPropsFromContextTracks(globalOpts.globalContextTracks || []);\n    const hasCommonTimeProps = Object.keys(commonTimeProps).length;\n    chart.series.forEach((series) => {\n        const sOpts = series.options.sonification;\n        if (series.visible && !(sOpts && sOpts.enabled === false)) {\n            if (hasCommonTimeProps) {\n                seriesTimeProps[series.index] = TimelineFromChart_merge(commonTimeProps);\n            }\n            if (sOpts) {\n                const defaultInstrMapping = (sOpts.defaultInstrumentOptions || {}).mapping, defaultSpeechMapping = (sOpts.defaultSpeechOptions || {}).mapping;\n                if (defaultInstrMapping) {\n                    addPropsFromMappingOptions(defaultInstrMapping, series.index);\n                }\n                if (defaultSpeechMapping) {\n                    addPropsFromMappingOptions(defaultSpeechMapping, series.index);\n                }\n                addPropsFromContextTracks(sOpts.contextTracks || []);\n                (sOpts.tracks || [])\n                    .concat(sOpts.contextTracks || [])\n                    .forEach((trackOpts) => {\n                    if (trackOpts.mapping) {\n                        addPropsFromMappingOptions(trackOpts.mapping, series.index);\n                    }\n                });\n            }\n        }\n    });\n    return {\n        seriesTimeProps,\n        ...getChartExtremesForProps(chart, Object.keys(props), Object.keys(perSeriesProps))\n    };\n}\n/**\n * Map a relative value onto a virtual axis.\n * @private\n */\nfunction mapToVirtualAxis(value, valueExtremes, virtualAxisExtremes, invert, logarithmic // Virtual axis is logarithmic\n) {\n    const lenValueAxis = valueExtremes.max - valueExtremes.min;\n    if (lenValueAxis <= 0) {\n        return virtualAxisExtremes.min;\n    }\n    const lenVirtualAxis = virtualAxisExtremes.max - virtualAxisExtremes.min, valueDelta = value - valueExtremes.min;\n    let virtualValueDelta = lenVirtualAxis * valueDelta / lenValueAxis;\n    if (logarithmic) {\n        const log = valueExtremes.min > 0 ?\n            // Normal log formula\n            (x) => Math.log(x) / Math.LOG10E :\n            // Negative logarithmic support needed\n            (x) => {\n                let adjustedNum = Math.abs(x);\n                if (adjustedNum < 10) {\n                    adjustedNum += (10 - adjustedNum) / 10;\n                }\n                const res = Math.log(adjustedNum) / Math.LN10;\n                return x < 0 ? -res : res;\n            };\n        const logValMin = log(valueExtremes.min);\n        virtualValueDelta = lenVirtualAxis *\n            (log(value) - logValMin) /\n            (log(valueExtremes.max) - logValMin);\n    }\n    const val = invert ?\n        virtualAxisExtremes.max - virtualValueDelta :\n        virtualAxisExtremes.min + virtualValueDelta;\n    return TimelineFromChart_clamp(val, virtualAxisExtremes.min, virtualAxisExtremes.max);\n}\n/**\n * Get the value of a mapped parameter for a point.\n * @private\n */\nfunction getMappingParameterValue(context, propMetrics, useSeriesExtremes, defaultMapping, mappingOptions, contextValueProp) {\n    if (typeof mappingOptions === 'number') {\n        return mappingOptions;\n    }\n    if (typeof mappingOptions === 'function') {\n        return mappingOptions(TimelineFromChart_extend({ time: 0 }, context));\n    }\n    let mapTo = mappingOptions, mapFunc = defaultMapping.mapFunction, min = defaultMapping.min, max = defaultMapping.max, within = defaultMapping.within, scale;\n    if (typeof mappingOptions === 'object') {\n        mapTo = mappingOptions.mapTo;\n        mapFunc = mappingOptions.mapFunction || mapFunc;\n        min = TimelineFromChart_pick(mappingOptions.min, min);\n        max = TimelineFromChart_pick(mappingOptions.max, max);\n        within = mappingOptions.within || defaultMapping.within;\n        scale = mappingOptions.scale;\n    }\n    if (!mapTo) {\n        return null;\n    }\n    const isInverted = mapTo.charAt(0) === '-';\n    if (isInverted) {\n        mapTo = mapTo.slice(1);\n    }\n    let value = context.value;\n    const useContextValue = mapTo === 'value' && value !== void 0 &&\n        contextValueProp;\n    if (!useContextValue) {\n        const fixedValue = mappingOptions.value;\n        if (fixedValue !== void 0) {\n            value = fixedValue;\n        }\n        else {\n            if (!context.point) {\n                return null;\n            }\n            value = context.point[mapTo];\n        }\n        if (value === void 0) {\n            value = getNestedProperty(mapTo, context.point);\n        }\n    }\n    if (typeof value !== 'number' || value === null) {\n        return null;\n    }\n    // Figure out extremes for this mapping\n    let extremes = null;\n    if (context.point) {\n        if (within === 'xAxis' || within === 'yAxis') {\n            const axis = context.point.series[within];\n            if (axis && TimelineFromChart_defined(axis.dataMin) && TimelineFromChart_defined(axis.dataMax)) {\n                extremes = {\n                    min: axis.dataMin,\n                    max: axis.dataMax\n                };\n            }\n        }\n        else if ((within === 'series' || useSeriesExtremes) &&\n            context.point.series) {\n            extremes = propMetrics.seriesExtremes[context.point.series.index][useContextValue ? contextValueProp : mapTo];\n        }\n    }\n    if (!extremes) { // Chart extremes\n        extremes = propMetrics.globalExtremes[useContextValue ? contextValueProp : mapTo];\n    }\n    if (scale) {\n        // Build a musical scale from array\n        const scaleAxis = [], minOctave = Math.floor(min / 12), maxOctave = Math.ceil(max / 12) + 1, lenScale = scale.length;\n        for (let octave = minOctave; octave < maxOctave; ++octave) {\n            for (let scaleIx = 0; scaleIx < lenScale; ++scaleIx) {\n                const note = 12 * octave + scale[scaleIx];\n                if (note >= min && note <= max) {\n                    scaleAxis.push(note);\n                }\n            }\n        }\n        // Map to the scale\n        const noteNum = mapToVirtualAxis(value, extremes, { min: 0, max: scaleAxis.length - 1 }, isInverted, mapFunc === 'logarithmic');\n        return scaleAxis[Math.round(noteNum)];\n    }\n    return mapToVirtualAxis(value, extremes, { min, max }, isInverted, mapFunc === 'logarithmic');\n}\n/**\n * Get mapping parameter value with defined fallback and defaults.\n * @private\n */\nfunction getParamValWithDefault(context, propMetrics, useSeriesExtremes, mappingParamOptions, fallback, defaults, contextValueProp) {\n    return TimelineFromChart_pick(getMappingParameterValue(context, propMetrics, useSeriesExtremes, TimelineFromChart_extend({\n        min: 0, max: 1, mapTo: 'y', mapFunction: 'linear', within: 'chart'\n    }, (defaults || {})), mappingParamOptions, contextValueProp), fallback);\n}\n/**\n * Get time value for a point event.\n * @private\n */\nfunction getPointTime(point, startTime, duration, timeMappingOptions, propMetrics, useSeriesExtremes) {\n    const time = getParamValWithDefault({ point, time: 0 }, propMetrics, useSeriesExtremes, timeMappingOptions, 0, { min: 0, max: duration, mapTo: 'x' });\n    return time + startTime;\n}\n/**\n * Get duration for a series\n * @private\n */\nfunction getAvailableDurationForSeries(series, totalDuration, propMetrics, afterSeriesWait) {\n    let timeProp, seriesDuration;\n    const availableDuration = totalDuration -\n        (series.chart.series.length - 1) * afterSeriesWait, hasGlobalTimeProp = propMetrics.seriesTimeProps.every((timeProps) => {\n        const props = Object.keys(timeProps);\n        if (props.length > 1) {\n            return false;\n        }\n        if (!timeProp) {\n            timeProp = props[0];\n        }\n        return timeProp === props[0];\n    });\n    if (hasGlobalTimeProp) {\n        // Chart-wide single time prop, use time prop extremes\n        const seriesExtremes = propMetrics\n            .seriesExtremes[series.index][timeProp], seriesTimeLen = seriesExtremes.max - seriesExtremes.min, totalTimeLen = propMetrics.seriesExtremes.reduce((sum, s) => (s[timeProp] ?\n            sum + s[timeProp].max - s[timeProp].min :\n            sum), 0);\n        seriesDuration = Math.round(seriesTimeLen / totalTimeLen * availableDuration);\n    }\n    else {\n        // No common time prop, so use percent of total points\n        const totalPoints = series.chart.series.reduce((sum, s) => sum + s.points.length, 0);\n        seriesDuration = Math.round((series.points || []).length / totalPoints * availableDuration);\n    }\n    return Math.max(50, seriesDuration);\n}\n/**\n * Build and add a track to the timeline.\n * @private\n */\nfunction addTimelineChannelFromTrack(timeline, audioContext, destinationNode, options) {\n    const speechOpts = options, instrMappingOpts = (options.mapping || {}), engine = options.type === 'speech' ?\n        new Sonification_SonificationSpeaker({\n            language: speechOpts.language,\n            name: speechOpts.preferredVoice\n        }) :\n        new Sonification_SonificationInstrument(audioContext, destinationNode, {\n            capabilities: {\n                pan: !!instrMappingOpts.pan,\n                tremolo: !!instrMappingOpts.tremolo,\n                filters: !!(instrMappingOpts.highpass ||\n                    instrMappingOpts.lowpass)\n            },\n            synthPatch: options.instrument,\n            midiTrackName: options.midiName\n        });\n    return timeline.addChannel(options.type || 'instrument', engine, TimelineFromChart_pick(options.showPlayMarker, true));\n}\n/**\n * Add event from a point to a mapped instrument track.\n * @private\n */\nfunction addMappedInstrumentEvent(context, channel, mappingOptions, propMetrics, roundToMusicalNotes, contextValueProp) {\n    const getParam = (param, fallback, defaults, parent) => getParamValWithDefault(context, propMetrics, false, (parent || mappingOptions)[param], fallback, defaults, contextValueProp);\n    const eventsAdded = [], eventOpts = {\n        noteDuration: getParam('noteDuration', 200, { min: 40, max: 1000 }),\n        pan: getParam('pan', 0, { min: -1, max: 1 }),\n        volume: getParam('volume', 1, { min: 0.1, max: 1 })\n    };\n    if (mappingOptions.frequency) {\n        eventOpts.frequency = getParam('frequency', 440, { min: 50, max: 6000 });\n    }\n    if (mappingOptions.lowpass) {\n        eventOpts.lowpassFreq = getParam('frequency', 20000, { min: 0, max: 20000 }, mappingOptions.lowpass);\n        eventOpts.lowpassResonance = getParam('resonance', 0, { min: -6, max: 12 }, mappingOptions.lowpass);\n    }\n    if (mappingOptions.highpass) {\n        eventOpts.highpassFreq = getParam('frequency', 20000, { min: 0, max: 20000 }, mappingOptions.highpass);\n        eventOpts.highpassResonance = getParam('resonance', 0, { min: -6, max: 12 }, mappingOptions.highpass);\n    }\n    if (mappingOptions.tremolo) {\n        eventOpts.tremoloDepth = getParam('depth', 0, { min: 0, max: 0.8 }, mappingOptions.tremolo);\n        eventOpts.tremoloSpeed = getParam('speed', 0, { min: 0, max: 0.8 }, mappingOptions.tremolo);\n    }\n    const gapBetweenNotes = getParam('gapBetweenNotes', 150, { min: 50, max: 1000 }), playDelay = getParam('playDelay', 0, { max: 200 });\n    const addNoteEvent = (noteDef, ix = 0) => {\n        let opts = noteDef;\n        if (noteDef.mapTo) {\n            // Transform the pitch mapping options to normal mapping options\n            if (typeof noteDef.min === 'string') {\n                opts.min = Sonification_SonificationInstrument\n                    .noteStringToC0Distance(noteDef.min);\n            }\n            if (typeof noteDef.max === 'string') {\n                opts.max = Sonification_SonificationInstrument\n                    .noteStringToC0Distance(noteDef.max);\n            }\n        }\n        else if (typeof noteDef === 'string' && isNoteDefinition(noteDef)) {\n            opts = Sonification_SonificationInstrument.noteStringToC0Distance(noteDef);\n        }\n        eventOpts.note = getParamValWithDefault(context, propMetrics, false, opts, -1, { min: 0, max: 107 }, contextValueProp);\n        if (eventOpts.note > -1) {\n            if (roundToMusicalNotes) {\n                eventOpts.note = Math.round(eventOpts.note);\n            }\n            eventsAdded.push(channel.addEvent({\n                time: context.time + playDelay + gapBetweenNotes * ix,\n                relatedPoint: context.point,\n                instrumentEventOptions: ix !== void 0 ?\n                    TimelineFromChart_extend({}, eventOpts) : eventOpts\n            }));\n        }\n    };\n    if (mappingOptions.pitch &&\n        mappingOptions.pitch.constructor === Array) {\n        mappingOptions.pitch.forEach(addNoteEvent);\n    }\n    else if (mappingOptions.pitch) {\n        addNoteEvent(mappingOptions.pitch);\n    }\n    else if (mappingOptions.frequency) {\n        eventsAdded.push(channel.addEvent({\n            time: context.time + playDelay,\n            relatedPoint: context.point,\n            instrumentEventOptions: eventOpts\n        }));\n    }\n    return eventsAdded;\n}\n/**\n * Get the message value to speak for a point.\n * @private\n */\nfunction getSpeechMessageValue(context, messageParam) {\n    return format(typeof messageParam === 'function' ?\n        messageParam(context) :\n        messageParam, context, context.point && context.point.series.chart);\n}\n/**\n * Add an event from a point to a mapped speech track.\n * @private\n */\nfunction addMappedSpeechEvent(context, channel, mappingOptions, propMetrics, contextValueProp) {\n    const getParam = (param, fallback, defaults) => getParamValWithDefault(context, propMetrics, false, mappingOptions[param], fallback, defaults, contextValueProp);\n    const playDelay = getParam('playDelay', 0, { max: 200 }), pitch = getParam('pitch', 1, { min: 0.3, max: 2 }), rate = getParam('rate', 1, { min: 0.4, max: 4 }), volume = getParam('volume', 1, { min: 0.1 }), message = getSpeechMessageValue(context, mappingOptions.text);\n    if (message) {\n        return channel.addEvent({\n            time: context.time + playDelay,\n            relatedPoint: context.point,\n            speechOptions: {\n                pitch,\n                rate,\n                volume\n            },\n            message\n        });\n    }\n}\n/**\n * Add events to a channel for a point&track combo.\n * @private\n */\nfunction addMappedEventForPoint(context, channel, trackOptions, propMetrics) {\n    let eventsAdded = [];\n    if (trackOptions.type === 'speech' && trackOptions.mapping) {\n        const eventAdded = addMappedSpeechEvent(context, channel, trackOptions.mapping, propMetrics);\n        if (eventAdded) {\n            eventsAdded = [eventAdded];\n        }\n    }\n    else if (trackOptions.mapping) {\n        eventsAdded = addMappedInstrumentEvent(context, channel, trackOptions.mapping, propMetrics, TimelineFromChart_pick(trackOptions\n            .roundToMusicalNotes, true));\n    }\n    return eventsAdded;\n}\n/**\n * Get a reduced set of points from a list, depending on grouping opts.\n * @private\n */\nfunction getGroupedPoints(pointGroupOpts, points) {\n    const alg = pointGroupOpts.algorithm || 'minmax', r = (ix) => (points[ix] ? [points[ix].point] : []);\n    if (alg === 'first') {\n        return r(0);\n    }\n    if (alg === 'last') {\n        return r(points.length - 1);\n    }\n    if (alg === 'middle') {\n        return r(points.length >> 1);\n    }\n    if (alg === 'firstlast') {\n        return r(0).concat(r(points.length - 1));\n    }\n    if (alg === 'minmax') {\n        const prop = pointGroupOpts.prop || 'y';\n        let min, max, minVal, maxVal;\n        points.forEach((p) => {\n            const val = getPointPropValue(p.point, prop);\n            if (val === void 0) {\n                return;\n            }\n            if (!min || val < minVal) {\n                min = p;\n                minVal = val;\n            }\n            if (!max || val > maxVal) {\n                max = p;\n                maxVal = val;\n            }\n        });\n        if (min && max) {\n            if (min.point === max.point) {\n                return [min.point];\n            }\n            return min.time > max.time ?\n                [max.point, min.point] :\n                [min.point, max.point];\n        }\n    }\n    return [];\n}\n/**\n * Should a track be active for this event?\n * @private\n */\nfunction isActive(context, activeWhen, lastPropValue) {\n    if (typeof activeWhen === 'function') {\n        return activeWhen(context);\n    }\n    if (typeof activeWhen === 'object') {\n        const prop = activeWhen.prop, val = TimelineFromChart_pick(context.value, context.point && getPointPropValue(context.point, prop));\n        if (typeof val !== 'number') {\n            return false;\n        }\n        let crossingOk = true;\n        const crossingUp = activeWhen.crossingUp, crossingDown = activeWhen.crossingDown, hasLastValue = typeof lastPropValue === 'number';\n        if (crossingUp && crossingDown) {\n            crossingOk = hasLastValue && (lastPropValue < crossingUp && val >= crossingUp ||\n                lastPropValue > crossingDown && val <= crossingDown);\n        }\n        else {\n            crossingOk = (crossingUp === void 0 ||\n                hasLastValue && lastPropValue < crossingUp &&\n                    val >= crossingUp) && (crossingDown === void 0 ||\n                hasLastValue && lastPropValue > crossingDown &&\n                    val <= crossingDown);\n        }\n        const max = TimelineFromChart_pick(activeWhen.max, Infinity), min = TimelineFromChart_pick(activeWhen.min, -Infinity);\n        return val <= max && val >= min && crossingOk;\n    }\n    return true;\n}\n/**\n * Build a new timeline object from a chart.\n * @private\n */\nfunction timelineFromChart(audioContext, destinationNode, chart) {\n    const options = chart.options.sonification ||\n        {}, defaultInstrOpts = options.defaultInstrumentOptions, defaultSpeechOpts = options.defaultSpeechOptions, defaultPointGroupOpts = TimelineFromChart_merge({\n        enabled: true,\n        groupTimespan: 15,\n        algorithm: 'minmax',\n        prop: 'y'\n    }, options.pointGrouping), globalTracks = options.globalTracks || [], globalContextTracks = options.globalContextTracks || [], isSequential = options.order === 'sequential', \n    // Slight margin for note end\n    totalDuration = Math.max(50, options.duration - 300), afterSeriesWait = options.afterSeriesWait, eventOptions = options.events || {}, propMetrics = getPropMetrics(chart), timeline = new Sonification_SonificationTimeline({\n        onPlay: eventOptions.onPlay,\n        onEnd: eventOptions.onEnd,\n        onStop: eventOptions.onStop,\n        showCrosshair: options.showCrosshair,\n        showTooltip: options.showTooltip\n    }, chart);\n    // Expose PropMetrics for tests\n    if (chart.sonification) {\n        chart.sonification.propMetrics = propMetrics;\n    }\n    let startTime = 0;\n    chart.series.forEach((series, seriesIx) => {\n        const sOptions = series.options.sonification ||\n            {};\n        if (series.visible && sOptions.enabled !== false) {\n            const seriesDuration = isSequential ? getAvailableDurationForSeries(series, totalDuration, propMetrics, afterSeriesWait) : totalDuration, seriesDefaultInstrOpts = TimelineFromChart_merge(defaultInstrOpts, sOptions.defaultInstrumentOptions), seriesDefaultSpeechOpts = TimelineFromChart_merge(defaultSpeechOpts, sOptions.defaultSpeechOptions), seriesPointGroupOpts = TimelineFromChart_merge(defaultPointGroupOpts, sOptions.pointGrouping), mainTracks = (sOptions.tracks || [seriesDefaultInstrOpts])\n                .concat(globalTracks), hasAddedSeries = !!timeline.channels.length, contextTracks = hasAddedSeries && !isSequential ?\n                sOptions.contextTracks || [] :\n                (sOptions.contextTracks || []).concat(globalContextTracks), eventsAdded = [];\n            // For crossing threshold notifications\n            let lastPropValue;\n            // Add events for the mapped tracks\n            mainTracks.forEach((trackOpts) => {\n                const mergedOpts = TimelineFromChart_merge({\n                    pointGrouping: seriesPointGroupOpts,\n                    midiName: trackOpts.midiName || series.name\n                }, trackOpts.type === 'speech' ?\n                    seriesDefaultSpeechOpts : seriesDefaultInstrOpts, trackOpts), pointGroupOpts = mergedOpts.pointGrouping, activeWhen = mergedOpts.activeWhen, updateLastPropValue = (point) => {\n                    if (typeof activeWhen === 'object' &&\n                        activeWhen.prop) {\n                        lastPropValue = getPointPropValue(point, activeWhen.prop);\n                    }\n                };\n                const channel = addTimelineChannelFromTrack(timeline, audioContext, destinationNode, mergedOpts), add = (c) => eventsAdded.push(\n                // Note arrays add multiple events\n                ...addMappedEventForPoint(c, channel, mergedOpts, propMetrics));\n                // Go through the points and add events to channel\n                let pointGroup = [], pointGroupTime = 0;\n                const addCurrentPointGroup = (groupSpanTime) => {\n                    if (pointGroup.length === 1) {\n                        add({\n                            point: pointGroup[0].point,\n                            time: pointGroupTime + groupSpanTime / 2\n                        });\n                    }\n                    else {\n                        const points = getGroupedPoints(pointGroupOpts, pointGroup), t = groupSpanTime / points.length;\n                        points.forEach((p, ix) => add({\n                            point: p,\n                            time: pointGroupTime + t / 2 + t * ix\n                        }));\n                    }\n                    pointGroup = [];\n                };\n                (series.points || []).forEach((point, pointIx) => {\n                    const isLastPoint = pointIx === series.points.length - 1;\n                    const time = getPointTime(point, startTime, seriesDuration, mergedOpts.mapping && mergedOpts.mapping.time || 0, propMetrics, isSequential);\n                    const context = { point, time };\n                    // Is this point active?\n                    if (!mergedOpts.mapping ||\n                        !isActive(context, activeWhen, lastPropValue)) {\n                        updateLastPropValue(point);\n                        // Remaining points in group\n                        if (isLastPoint && pointGroup.length) {\n                            addCurrentPointGroup(pointGroup[pointGroup.length - 1].time -\n                                pointGroup[0].time);\n                        }\n                        return;\n                    }\n                    updateLastPropValue(point);\n                    // Add the events\n                    if (!pointGroupOpts.enabled) {\n                        add(context);\n                    }\n                    else {\n                        const dT = time - pointGroupTime, groupSpan = pointGroupOpts.groupTimespan, spanTime = isLastPoint &&\n                            dT <= groupSpan ? dT : groupSpan;\n                        if (isLastPoint || dT > groupSpan) {\n                            if (dT <= groupSpan) {\n                                // Only happens if last point is within group\n                                pointGroup.push(context);\n                            }\n                            addCurrentPointGroup(spanTime);\n                            pointGroupTime = Math.floor(time / groupSpan) *\n                                groupSpan;\n                            if (isLastPoint && dT > groupSpan) {\n                                add({\n                                    point: context.point,\n                                    time: pointGroupTime + spanTime / 2\n                                });\n                            }\n                            else {\n                                pointGroup = [context];\n                            }\n                        }\n                        else {\n                            pointGroup.push(context);\n                        }\n                    }\n                });\n            });\n            // Add callbacks to first/last events\n            const firstEvent = eventsAdded.reduce((first, e) => (e.time < first.time ? e : first), { time: Infinity });\n            const lastEvent = eventsAdded.reduce((last, e) => (e.time > last.time ? e : last), { time: -Infinity });\n            firstEvent.callback = eventOptions.onSeriesStart ?\n                eventOptions.onSeriesStart.bind(null, { series, timeline }) :\n                void 0;\n            lastEvent.callback = eventOptions.onSeriesEnd ?\n                eventOptions.onSeriesEnd.bind(null, { series, timeline }) :\n                void 0;\n            // Add the context tracks that are not related to points\n            contextTracks.forEach((trackOpts) => {\n                const mergedOpts = trackOpts.type === 'speech' ?\n                    TimelineFromChart_merge(defaultSpeechOpts, trackOpts) :\n                    TimelineFromChart_merge(defaultInstrOpts, {\n                        mapping: { pitch: { mapTo: 'value' } }\n                    }, trackOpts);\n                const contextChannel = addTimelineChannelFromTrack(timeline, audioContext, destinationNode, mergedOpts);\n                lastPropValue = void 0;\n                const { timeInterval, valueInterval } = mergedOpts, valueProp = mergedOpts.valueProp || 'x', activeWhen = mergedOpts.activeWhen, contextExtremes = propMetrics\n                    .seriesExtremes[seriesIx][valueProp], addContextEvent = (time, value) => {\n                    if (!mergedOpts.mapping ||\n                        !isActive({ time, value }, typeof activeWhen === 'object' ?\n                            TimelineFromChart_extend({ prop: valueProp }, activeWhen) :\n                            activeWhen, lastPropValue)) {\n                        lastPropValue = value;\n                        return;\n                    }\n                    lastPropValue = value;\n                    if (mergedOpts.type === 'speech') {\n                        addMappedSpeechEvent({ time, value }, contextChannel, mergedOpts.mapping, propMetrics, valueProp);\n                    }\n                    else {\n                        addMappedInstrumentEvent({ time, value }, contextChannel, mergedOpts.mapping, propMetrics, TimelineFromChart_pick(mergedOpts.roundToMusicalNotes, true), valueProp);\n                    }\n                };\n                if (timeInterval) {\n                    let time = 0;\n                    while (time <= seriesDuration) {\n                        const val = mapToVirtualAxis(time, { min: 0, max: seriesDuration }, contextExtremes);\n                        addContextEvent(time + startTime, val);\n                        time += timeInterval;\n                    }\n                }\n                if (valueInterval) {\n                    let val = contextExtremes.min;\n                    while (val <= contextExtremes.max) {\n                        const time = mapToVirtualAxis(val, contextExtremes, { min: 0, max: seriesDuration }, false, mergedOpts.valueMapFunction === 'logarithmic');\n                        addContextEvent(time + startTime, val);\n                        val += valueInterval;\n                    }\n                }\n            });\n            if (isSequential) {\n                startTime += seriesDuration + afterSeriesWait;\n            }\n        }\n    });\n    return timeline;\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const TimelineFromChart = (timelineFromChart);\n\n;// ./code/es-modules/Extensions/Sonification/Sonification.js\n/* *\n *\n *  (c) 2009-2025 Øystein Moseng\n *\n *  Sonification module.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Imports\n *\n * */\n\nconst { defaultOptions, getOptions } = (external_highcharts_src_js_default_default());\n\nconst { addEvent, extend: Sonification_extend, fireEvent, merge: Sonification_merge, pick: Sonification_pick } = (external_highcharts_src_js_default_default());\n\nconst { doc: Sonification_doc, win: Sonification_win } = (external_highcharts_src_js_default_default());\n\n\n\n\n\n\n/**\n * The Sonification class. This class represents a chart's sonification\n * capabilities. A chart automatically gets an instance of this class when\n * applicable.\n *\n * @sample highcharts/sonification/chart-events\n *         Basic demo accessing some of the chart.sonification methods.\n * @sample highcharts/demo/sonification-navigation\n *         More advanced demo using more functionality.\n *\n * @requires modules/sonification\n *\n * @class\n * @name Highcharts.Sonification\n *\n * @param {Highcharts.Chart} chart The chart to tie the sonification to\n */\nclass Sonification {\n    constructor(chart) {\n        this.chart = chart;\n        this.retryContextCounter = 0;\n        this.lastUpdate = 0;\n        this.unbindKeydown = addEvent(Sonification_doc, 'keydown', function (e) {\n            if (chart && chart.sonification &&\n                (e.key === 'Esc' || e.key === 'Escape')) {\n                chart.sonification.cancel();\n            }\n        });\n        try {\n            this.audioContext = new Sonification_win.AudioContext();\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            this.audioContext.suspend();\n            this.audioDestination = this.audioContext.destination;\n        }\n        catch (e) { /* Ignore */ }\n    }\n    /**\n     * Set the audio destination node to something other than the default\n     * output. This allows for inserting custom WebAudio chains after the\n     * sonification.\n     * @function Highcharts.Sonification#setAudioDestination\n     * @param {AudioDestinationNode} audioDestination The destination node\n     */\n    setAudioDestination(audioDestination) {\n        this.audioDestination = audioDestination;\n        this.update();\n    }\n    /**\n     * Check if sonification is playing currently\n     * @function Highcharts.Sonification#isPlaying\n     * @return {boolean} `true` if currently playing, `false` if not\n     */\n    isPlaying() {\n        return !!this.timeline && this.timeline.isPlaying;\n    }\n    /**\n     * Divide timeline into 100 parts of equal time, and play one of them.\n     * Can be used for scrubbing navigation.\n     * @function Highcharts.Sonification#playSegment\n     *\n     * @sample highcharts/sonification/scrubbing\n     *         Scrubbing with slider\n     *\n     * @param {number} segment The segment to play, from 0 to 100\n     * @param {Highcharts.SonificationChartEventCallback} [onEnd] Callback to call after play completed\n     */\n    playSegment(segment, onEnd) {\n        if (!this.ready(this.playSegment.bind(this, segment, onEnd))) {\n            return;\n        }\n        if (this.timeline) {\n            this.timeline.playSegment(segment, onEnd);\n        }\n    }\n    /**\n     * Play point(s)/event(s) adjacent to current timeline cursor location.\n     * @function Highcharts.Sonification#playAdjacent\n     *\n     * @sample highcharts/demo/sonification-navigation\n     *         Sonification keyboard navigation\n     *\n     * @param {number} next Pass `true` to play next point, `false` for previous\n     * @param {Highcharts.SonificationChartEventCallback} [onEnd]\n     * Callback to call after play completed\n     * @param {Highcharts.SonificationTimelineFilterCallback} [eventFilter]\n     * Filter to apply to the events before finding adjacent to play\n     */\n    playAdjacent(next, onEnd, eventFilter) {\n        if (!this.ready(this.playAdjacent.bind(this, next, onEnd, eventFilter))) {\n            return;\n        }\n        if (this.timeline) {\n            const opts = this.chart.options.sonification, onHit = opts && opts.events && opts.events.onBoundaryHit;\n            if (!onHit) {\n                this.initBoundaryInstrument();\n            }\n            this.timeline.playAdjacent(next, onEnd, onHit || (() => {\n                this.defaultBoundaryHit();\n            }), eventFilter);\n        }\n    }\n    /**\n     * Play next/previous series, picking the point closest to a prop value\n     * from last played point. By default picks the point in the adjacent\n     * series with the closest x value as the last played point.\n     * @function Highcharts.Sonification#playAdjacentSeries\n     *\n     * @sample highcharts/demo/sonification-navigation\n     *         Sonification keyboard navigation\n     *\n     * @param {number} next Pass `true` to play next series, `false` for previous\n     * @param {string} [prop] Prop to find closest value of, defaults to `x`.\n     * @param {Highcharts.SonificationChartEventCallback} [onEnd]\n     * Callback to call after play completed\n     *\n     * @return {Highcharts.Series|null} The played series, or `null` if none found\n     */\n    playAdjacentSeries(next, prop = 'x', onEnd) {\n        const lastPlayed = this.getLastPlayedPoint();\n        if (lastPlayed) {\n            const targetSeriesIx = lastPlayed.series.index + (next ? 1 : -1);\n            this.playClosestToProp(prop, lastPlayed[prop], (e) => !!e.relatedPoint &&\n                e.relatedPoint.series.index === targetSeriesIx, onEnd);\n            return this.chart.series[targetSeriesIx] || null;\n        }\n        return null;\n    }\n    /**\n     * Play point(s)/event(s) closest to a prop relative to a reference value.\n     * @function Highcharts.Sonification#playClosestToProp\n     *\n     * @param {string} prop Prop to compare.\n     * @param {number} targetValue Target value to find closest value of.\n     * @param {Highcharts.SonificationTimelineFilterCallback} [targetFilter]\n     * Filter to apply to the events before finding closest point(s)\n     * @param {Highcharts.SonificationChartEventCallback} [onEnd]\n     * Callback to call after play completed\n     */\n    playClosestToProp(prop, targetValue, targetFilter, onEnd) {\n        if (!this.ready(this.playClosestToProp.bind(this, prop, targetValue, targetFilter, onEnd))) {\n            return;\n        }\n        if (this.timeline) {\n            const opts = this.chart.options.sonification, onHit = opts && opts.events && opts.events.onBoundaryHit;\n            if (!onHit) {\n                this.initBoundaryInstrument();\n            }\n            this.timeline.playClosestToPropValue(prop, targetValue, onEnd, onHit || (() => this.defaultBoundaryHit()), targetFilter);\n        }\n    }\n    /**\n     * Get last played point\n     * @function Highcharts.Sonification#getLastPlayedPoint\n     *\n     * @sample highcharts/demo/sonification-navigation\n     *         Sonification keyboard navigation\n     *\n     * @return {Highcharts.Point|null} The point, or null if none\n     */\n    getLastPlayedPoint() {\n        if (this.timeline) {\n            return this.timeline.getLastPlayedPoint();\n        }\n        return null;\n    }\n    /**\n     * Play a note with a specific instrument, and optionally a time offset.\n     * @function Highcharts.Sonification#playNote\n     *\n     * @sample highcharts/sonification/chart-events\n     *         Custom notifications\n     *\n     * @param {Highcharts.SonificationSynthPreset|Highcharts.SynthPatchOptionsObject} instrument\n     * The instrument to play. Can be either a string referencing the\n     * instrument presets, or an actual SynthPatch configuration.\n     * @param {Highcharts.SonificationInstrumentScheduledEventOptionsObject} options\n     * Configuration for the instrument event to play.\n     * @param {number} [delayMs]\n     * Time offset from now, in milliseconds. Defaults to 0.\n     */\n    playNote(instrument, options, delayMs = 0) {\n        if (!this.ready(this.playNote.bind(this, instrument, options))) {\n            return;\n        }\n        const duration = options.noteDuration = options.noteDuration || 500;\n        const instr = new Sonification_SonificationInstrument(this.audioContext, this.audioDestination, {\n            synthPatch: instrument,\n            capabilities: {\n                filters: true,\n                tremolo: true,\n                pan: true\n            }\n        });\n        instr.scheduleEventAtTime(delayMs / 1000, options);\n        setTimeout(() => instr && instr.destroy(), delayMs + duration + 500);\n    }\n    /**\n     * Speak a text string, optionally with a custom speaker configuration\n     * @function Highcharts.Sonification#speak\n     *\n     * @sample highcharts/sonification/chart-events\n     *         Custom notifications\n     *\n     * @param {string} text Text to announce\n     * @param {Highcharts.SonificationSpeakerOptionsObject} [speakerOptions]\n     * Options for the announcement\n     * @param {number} [delayMs]\n     * Time offset from now, in milliseconds. Defaults to 0.\n     */\n    speak(text, speakerOptions, delayMs = 0) {\n        const speaker = new Sonification_SonificationSpeaker(Sonification_merge({\n            language: 'en-US',\n            rate: 1.5,\n            volume: 0.4\n        }, speakerOptions || {}));\n        speaker.sayAtTime(delayMs, text);\n    }\n    /**\n     * Cancel current playing audio and reset the timeline.\n     * @function Highcharts.Sonification#cancel\n     */\n    cancel() {\n        if (this.timeline) {\n            this.timeline.cancel();\n        }\n        fireEvent(this, 'cancel');\n    }\n    /**\n     * Start download of a MIDI file export of the timeline.\n     * @function Highcharts.Sonification#downloadMIDI\n     */\n    downloadMIDI() {\n        if (!this.ready(this.downloadMIDI.bind(this))) {\n            return;\n        }\n        if (this.timeline) {\n            this.timeline.reset();\n            this.timeline.downloadMIDI();\n        }\n    }\n    /**\n     * Implementation of chart.sonify\n     * @private\n     */\n    sonifyChart(resetAfter, onEnd) {\n        if (!this.ready(this.sonifyChart.bind(this, resetAfter, onEnd))) {\n            return;\n        }\n        if (this.timeline) {\n            this.timeline.reset();\n            this.beforePlay();\n            this.timeline.play(void 0, void 0, resetAfter, onEnd);\n        }\n    }\n    /**\n     * Implementation of series.sonify\n     * @private\n     */\n    sonifySeries(series, resetAfter, onEnd) {\n        if (!this.ready(this.sonifySeries.bind(this, series, resetAfter, onEnd))) {\n            return;\n        }\n        if (this.timeline) {\n            this.timeline.reset();\n            this.beforePlay();\n            this.timeline.play((e) => !!e.relatedPoint && e.relatedPoint.series === series, void 0, resetAfter, onEnd);\n        }\n    }\n    /**\n     * Implementation of point.sonify\n     * @private\n     */\n    sonifyPoint(point, onEnd) {\n        if (!this.ready(this.sonifyPoint.bind(this, point, onEnd))) {\n            return;\n        }\n        if (this.timeline) {\n            this.timeline.reset();\n            this.beforePlay();\n            this.timeline.anchorPlayMoment((e) => e.relatedPoint === point, onEnd);\n        }\n    }\n    /**\n     * Set the overall/master volume for the sonification.\n     * Usually handled through chart update.\n     * @private\n     */\n    setMasterVolume(vol) {\n        if (this.timeline) {\n            this.timeline.setMasterVolume(vol);\n        }\n    }\n    /**\n     * Destroy the sonification capabilities\n     * @private\n     */\n    destroy() {\n        this.unbindKeydown();\n        if (this.timeline) {\n            this.timeline.destroy();\n            delete this.timeline;\n        }\n        if (this.boundaryInstrument) {\n            this.boundaryInstrument.stop();\n        }\n        if (this.audioContext) {\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            this.audioContext.close();\n            delete this.audioContext;\n        }\n    }\n    /**\n     * Update the timeline with latest chart changes. Usually handled\n     * automatically. Note that the [sonification.updateInterval](https://api.highcharts.com/highcharts/sonification.updateInterval)\n     * option can stop updates from happening in rapid succession, including\n     * manual calls to this function.\n     * @private\n     */\n    update() {\n        const sOpts = this.chart.options && this.chart.options.sonification;\n        if (!this.ready(this.update.bind(this)) || !sOpts) {\n            return;\n        }\n        // Don't update too often, it gets performance intensive\n        const now = Date.now(), updateInterval = sOpts.updateInterval;\n        if (now - this.lastUpdate < updateInterval && !this.forceReady) {\n            clearTimeout(this.scheduledUpdate);\n            this.scheduledUpdate = setTimeout(this.update.bind(this), updateInterval / 2);\n            return;\n        }\n        const events = sOpts.events || {};\n        if (events.beforeUpdate) {\n            events.beforeUpdate({ chart: this.chart, timeline: this.timeline });\n        }\n        this.lastUpdate = now;\n        if (this.timeline) {\n            this.timeline.destroy();\n        }\n        if (this.audioContext && this.audioDestination) {\n            this.timeline = TimelineFromChart(this.audioContext, this.audioDestination, this.chart);\n            const sOpts = this.chart.options.sonification;\n            this.timeline.setMasterVolume(Sonification_pick(sOpts && sOpts.masterVolume, 1));\n        }\n        if (events.afterUpdate) {\n            events.afterUpdate({ chart: this.chart, timeline: this.timeline });\n        }\n    }\n    /**\n     * Only continue if sonification enabled. If audioContext is\n     * suspended, retry up to 20 times with a small delay.\n     * @private\n     */\n    ready(whenReady) {\n        if (!this.audioContext ||\n            !this.audioDestination ||\n            !this.chart.options ||\n            this.chart.options.sonification &&\n                this.chart.options.sonification.enabled === false) {\n            return false;\n        }\n        if (this.audioContext.state === 'suspended' && !this.forceReady) {\n            if (this.retryContextCounter++ < 20) {\n                setTimeout(() => {\n                    if (this.audioContext &&\n                        this.audioContext.state === 'suspended') {\n                        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n                        this.audioContext.resume().then(whenReady);\n                    }\n                    else {\n                        whenReady();\n                    }\n                }, 5);\n            }\n            return false;\n        }\n        this.retryContextCounter = 0;\n        return true;\n    }\n    /**\n     * Call beforePlay event handler if exists\n     * @private\n     */\n    beforePlay() {\n        const opts = this.chart.options.sonification, beforePlay = opts && opts.events && opts.events.beforePlay;\n        if (beforePlay) {\n            beforePlay({ chart: this.chart, timeline: this.timeline });\n        }\n    }\n    /**\n     * Initialize the builtin boundary hit instrument\n     * @private\n     */\n    initBoundaryInstrument() {\n        if (!this.boundaryInstrument) {\n            this.boundaryInstrument = new Sonification_SynthPatch(this.audioContext, Sonification_merge(Sonification_InstrumentPresets.chop, { masterVolume: 0.3 }));\n            this.boundaryInstrument.startSilently();\n            this.boundaryInstrument.connect(this.audioDestination);\n        }\n    }\n    /**\n     * The default boundary hit sound\n     * @private\n     */\n    defaultBoundaryHit() {\n        if (this.boundaryInstrument) {\n            this.boundaryInstrument.playFreqAtTime(0.1, 1, 200);\n            this.boundaryInstrument.playFreqAtTime(0.2, 1, 200);\n        }\n    }\n}\n(function (Sonification) {\n    const composedClasses = [];\n    /**\n     * Update sonification object on chart.\n     * @private\n     */\n    function updateSonificationEnabled() {\n        const sonification = this.sonification, sOptions = this.options && this.options.sonification;\n        if (sOptions && sOptions.enabled) {\n            if (sonification) {\n                sonification.update();\n            }\n            else {\n                this.sonification = new Sonification(this);\n                this.sonification.update();\n            }\n        }\n        else if (sonification) {\n            sonification.destroy();\n            delete this.sonification;\n        }\n    }\n    /**\n     * Destroy with chart.\n     * @private\n     */\n    function chartOnDestroy() {\n        if (this && this.sonification) {\n            this.sonification.destroy();\n        }\n    }\n    /**\n     * Update on render\n     * @private\n     */\n    function chartOnRender() {\n        if (this.updateSonificationEnabled) {\n            this.updateSonificationEnabled();\n        }\n    }\n    /**\n     * Update\n     * @private\n     */\n    function chartOnUpdate(e) {\n        const newOptions = e.options.sonification;\n        if (newOptions) {\n            Sonification_merge(true, this.options.sonification, newOptions);\n            chartOnRender.call(this);\n        }\n    }\n    /**\n     * Compose\n     * @private\n     */\n    function compose(ChartClass, SeriesClass, PointClass) {\n        // Extend chart\n        if (composedClasses.indexOf(ChartClass) === -1) {\n            composedClasses.push(ChartClass);\n            Sonification_extend(ChartClass.prototype, {\n                updateSonificationEnabled,\n                sonify: function (onEnd) {\n                    if (this.sonification) {\n                        this.sonification.sonifyChart(false, onEnd);\n                    }\n                },\n                toggleSonify: function (reset = true, onEnd) {\n                    if (!this.sonification) {\n                        return;\n                    }\n                    const timeline = this.sonification.timeline;\n                    if (Sonification_win.speechSynthesis) {\n                        Sonification_win.speechSynthesis.cancel();\n                    }\n                    if (timeline && this.sonification.isPlaying()) {\n                        if (reset) {\n                            this.sonification.cancel();\n                        }\n                        else {\n                            timeline.pause();\n                        }\n                    }\n                    else if (timeline && timeline.isPaused) {\n                        timeline.resume();\n                    }\n                    else {\n                        this.sonification.sonifyChart(reset, onEnd);\n                    }\n                }\n            });\n            addEvent(ChartClass, 'destroy', chartOnDestroy);\n            addEvent(ChartClass, 'render', chartOnRender);\n            addEvent(ChartClass, 'update', chartOnUpdate);\n        }\n        // Extend series\n        if (composedClasses.indexOf(SeriesClass) === -1) {\n            composedClasses.push(SeriesClass);\n            SeriesClass.prototype.sonify = function (onEnd) {\n                if (this.chart.sonification) {\n                    this.chart.sonification.sonifySeries(this, false, onEnd);\n                }\n            };\n        }\n        // Extend points\n        if (composedClasses.indexOf(PointClass) === -1) {\n            composedClasses.push(PointClass);\n            PointClass.prototype.sonify = function (onEnd) {\n                if (this.series.chart.sonification) {\n                    this.series.chart.sonification.sonifyPoint(this, onEnd);\n                }\n            };\n        }\n        // Add items to the exporting menu\n        const exportingOptions = getOptions().exporting;\n        if (exportingOptions &&\n            exportingOptions.buttons &&\n            exportingOptions.buttons.contextButton.menuItems) {\n            exportingOptions.buttons.contextButton.menuItems.push('separator', 'downloadMIDI', 'playAsSound');\n        }\n    }\n    Sonification.compose = compose;\n})(Sonification || (Sonification = {}));\n// Add default options\nSonification_merge(true, defaultOptions, Sonification_Options);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Sonification_Sonification = (Sonification);\n/* *\n *\n *  API declarations\n *\n * */\n/**\n * Play a sonification of a chart.\n *\n * @function Highcharts.Chart#sonify\n * @param {Highcharts.SonificationChartEventCallback} [onEnd]\n * Callback to call after play completed\n *\n * @requires modules/sonification\n */\n/**\n * Play/pause sonification of a chart.\n *\n * @function Highcharts.Chart#toggleSonify\n *\n * @param {boolean} [reset]\n * Reset the playing cursor after play completed. Defaults to `true`.\n * @param {Highcharts.SonificationChartEventCallback} [onEnd]\n * Callback to call after play completed\n *\n * @requires modules/sonification\n */\n/**\n * Play a sonification of a series.\n *\n * @function Highcharts.Series#sonify\n * @param {Highcharts.SonificationChartEventCallback} [onEnd]\n * Callback to call after play completed\n *\n * @requires modules/sonification\n */\n/**\n * Play a sonification of a point.\n *\n * @function Highcharts.Point#sonify\n * @param {Highcharts.SonificationChartEventCallback} [onEnd]\n * Callback to call after play completed\n *\n * @requires modules/sonification\n */\n/**\n * Sonification capabilities for the chart.\n *\n * @name Highcharts.Chart#sonification\n * @type {Highcharts.Sonification|undefined}\n *\n * @requires modules/sonification\n */\n/**\n * Collection of Sonification classes and objects.\n * @requires modules/sonification\n * @interface Highcharts.SonificationGlobalObject\n */ /**\n* SynthPatch presets\n* @name Highcharts.SonificationGlobalObject#InstrumentPresets\n* @type {Record<Highcharts.SonificationSynthPreset,Highcharts.SynthPatchOptionsObject>|undefined}\n*/ /**\n* Musical scale presets\n* @name Highcharts.SonificationGlobalObject#Scales\n* @type {Highcharts.SonificationScalePresetsObject|undefined}\n*/ /**\n* SynthPatch class\n* @name Highcharts.SonificationGlobalObject#SynthPatch\n* @type {Highcharts.SynthPatch|undefined}\n*/ /**\n* SonificationInstrument class\n* @name Highcharts.SonificationGlobalObject#SonificationInstrument\n* @type {Highcharts.SonificationInstrument|undefined}\n*/ /**\n* SonificationSpeaker class\n* @name Highcharts.SonificationGlobalObject#SonificationSpeaker\n* @type {Highcharts.SonificationSpeaker|undefined}\n*/\n/**\n * Global Sonification classes and objects.\n *\n * @name Highcharts.sonification\n * @type {Highcharts.SonificationGlobalObject}\n *\n * @requires modules/sonification\n */\n(''); // Keep above doclets in JS file\n\n;// ./code/es-modules/Extensions/Sonification/Scales.js\n/* *\n *\n *  (c) 2009-2025 Øystein Moseng\n *\n *  Musical scales for sonification.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nconst Scales = {\n    minor: [0, 2, 3, 5, 7, 8, 10],\n    dorian: [0, 2, 3, 5, 7, 9, 10],\n    harmonicMinor: [0, 2, 3, 5, 7, 8, 11],\n    phrygian: [0, 1, 3, 5, 7, 8, 11],\n    major: [0, 2, 4, 5, 7, 9, 11],\n    lydian: [0, 2, 4, 6, 7, 9, 11],\n    mixolydian: [0, 2, 4, 5, 7, 9, 10],\n    majorPentatonic: [0, 2, 4, 7, 9],\n    minorPentatonic: [0, 3, 5, 7, 10]\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Sonification_Scales = (Scales);\n/* *\n *\n *  API declarations\n *\n * */\n/**\n * Preset scales for pitch mapping.\n * @requires modules/sonification\n * @interface Highcharts.SonificationScalePresetsObject\n */ /**\n* Minor scale (aeolian)\n* @name Highcharts.SonificationScalePresetsObject#minor\n* @type {Array<number>}\n*/ /**\n* Dorian scale\n* @name Highcharts.SonificationScalePresetsObject#dorian\n* @type {Array<number>}\n*/ /**\n* Harmonic minor scale\n* @name Highcharts.SonificationScalePresetsObject#harmonicMinor\n* @type {Array<number>}\n*/ /**\n* Phrygian scale\n* @name Highcharts.SonificationScalePresetsObject#phrygian\n* @type {Array<number>}\n*/ /**\n* Major (ionian) scale\n* @name Highcharts.SonificationScalePresetsObject#major\n* @type {Array<number>}\n*/ /**\n* Lydian scale\n* @name Highcharts.SonificationScalePresetsObject#lydian\n* @type {Array<number>}\n*/ /**\n* Mixolydian scale\n* @name Highcharts.SonificationScalePresetsObject#mixolydian\n* @type {Array<number>}\n*/ /**\n* Major pentatonic scale\n* @name Highcharts.SonificationScalePresetsObject#majorPentatonic\n* @type {Array<number>}\n*/ /**\n* Minor pentatonic scale\n* @name Highcharts.SonificationScalePresetsObject#minorPentatonic\n* @type {Array<number>}\n*/\n(''); // Keep above doclets in JS file\n\n;// ./code/es-modules/masters/modules/sonification.js\n\n\n\n\n\n\n\n\n\n\nconst G = (external_highcharts_src_js_default_default());\n// Global objects\nG.sonification = {\n    InstrumentPresets: Sonification_InstrumentPresets,\n    Scales: Sonification_Scales,\n    SynthPatch: Sonification_SynthPatch,\n    SonificationInstrument: Sonification_SonificationInstrument,\n    SonificationSpeaker: Sonification_SonificationSpeaker,\n    SonificationTimeline: Sonification_SonificationTimeline,\n    Sonification: Sonification_Sonification\n};\nSonification_Sonification.compose(G.Chart, G.Series, G.Point);\n/* harmony default export */ const sonification_src = ((external_highcharts_src_js_default_default()));\n\nexport { sonification_src as default };\n"], "names": ["__WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__", "__webpack_require__", "n", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "external_highcharts_src_js_default_namespaceObject", "external_highcharts_src_js_default_default", "clamp", "defined", "pick", "getPitchTrackedMultiplierVal", "multiplier", "freq", "Math", "log", "miniRampToVolAtTime", "gainNode", "time", "vol", "gain", "cancelScheduledValues", "setTargetAtTime", "SynthPatch", "stopRampTime", "setValueAtTime", "scheduleGainEnvelope", "envelope", "type", "volumeMultiplier", "isAtk", "length", "t", "unshift", "for<PERSON>ach", "ep", "ix", "prev", "delta", "startTime", "max", "PulseOscNode", "constructor", "context", "options", "pulseWidth", "min", "makeOsc", "OscillatorNode", "detune", "frequency", "sawOscA", "sawOscB", "phaseInverter", "GainNode", "masterGain", "delayNode", "DelayNode", "delayTime", "value", "connect", "destination", "getFrequencyFacade", "pulse", "fromTime", "round", "timeConstant", "getPWMTarget", "start", "stop", "Oscillator", "audioContext", "fmOscillatorIx", "fmOscillator", "vmOscillatorIx", "vmOscillator", "createSoundSource", "createGain", "createFilters", "createVolTracking", "lowpassNode", "highpassNode", "volTrackingNode", "vmNode", "white<PERSON><PERSON>", "pulseNode", "oscNode", "reduce", "cur", "stopAtTime", "setFreqAtTime", "glideDuration", "opts", "f", "fixedFrequency", "freqMultiplier", "oscTarget", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastUpdateTime", "scheduleVolTrackingChange", "scheduleFilterTrackingChange", "getFMTarget", "getVMTarget", "runEnvelopeAtTime", "attackEnvelope", "releaseEnvelope", "volume", "cancelScheduled", "currentTime", "v", "volumePitchTrackingMultiplier", "rampTime", "scheduleFilter<PERSON>arget", "filterNode", "filterOptions", "frequencyPitchTrackingMultiplier", "lowpass", "highpass", "ctx", "bSize", "sampleRate", "buffer", "createBuffer", "data", "getChannelData", "i", "random", "wn", "createBufferSource", "loop", "BiquadFilterNode", "Q", "eqNodes", "midiInstrument", "outputNode", "createEqChain", "inputNode", "oscillators", "map", "oscOpts", "osc", "connectTarget", "targetFunc", "targetOsc", "target", "startSilently", "curTime", "endTime", "disconnect", "silenceAtTime", "releaseAtTime", "mute", "playFreqAtTime", "noteDuration", "noteGlideDuration", "masterAttackEnvelope", "masterVolume", "destinationNode", "eq", "eqDef", "reduceRight", "chain", "node", "maxReleaseDuration", "env", "masterEnv", "masterReleaseEnvelope", "InstrumentPresets", "piano", "plucked", "flute", "lead", "vibraphone", "saxophone", "trumpet", "sawsynth", "basic1", "basic2", "chord", "wobble", "sine", "sineGlide", "triangle", "sawtooth", "square", "chop", "shaker", "step", "kick", "shortnote", "noise", "filteredN<PERSON>", "wind", "SonificationInstrument_defined", "extend", "SonificationInstrument", "curPara<PERSON>", "midiTrackName", "masterVolNode", "volumeNode", "createNodesFromCapabilities", "pan", "capabilities", "connectCapabilityNodes", "synthPatch", "Sonification_InstrumentPresets", "setMasterVolume", "scheduleEventAtTime", "params", "mergedParams", "note", "musicalNoteToFrequency", "tremolo<PERSON><PERSON>h", "tremoloSpeed", "setTremoloAtTime", "setPanAtTime", "setVolumeAtTime", "lowpassFreq", "lowpassResonance", "setFilterAtTime", "highpassFreq", "highpassResonance", "cancel", "tremoloOsc", "panNode", "p", "destroy", "filter", "resonance", "audioTime", "depth", "speed", "StereoPannerNode", "tremolo", "filters", "input", "output", "noteStringToC0Distance", "match", "semitone", "wholetone", "toLowerCase", "accidental", "c", "e", "g", "b", "octave", "parseInt", "pow", "Sonification_SynthPatch", "SonificationSpeaker_pick", "Sonification_SonificationSpeaker", "synthesis", "window", "speechSynthesis", "onvoiceschanged", "setVoice", "bind", "scheduled", "say", "message", "utterance", "SpeechSynthesisUtterance", "voice", "rate", "pitch", "speak", "sayAtTime", "push", "setTimeout", "clearTimeout", "lang<PERSON><PERSON><PERSON>", "name", "lang", "language", "voices", "getVoices", "len", "Sonification_TimelineChannel", "engine", "showPlayMarker", "events", "muted", "addEvent", "event", "lastEvent", "splice", "unmute", "MIDI_pick", "freqToNote", "LN2", "byte", "<PERSON><PERSON><PERSON><PERSON>", "nTracks", "timeInfo", "varLenEnc", "buf", "res", "toMIDIEvents", "cachedVel", "cachedDur", "add", "el", "timeMS", "instrumentEventOptions", "dur", "tNOF", "ctrl", "valMap", "noteVal", "Sonification_SonificationInstrument", "ctrlDef", "keys", "ctrlSignal", "val", "getMetaEvents", "textArr", "code", "charCodeAt", "concat", "getTrackChunk", "addTimeInfo", "prevTime", "metaEvents", "trackEvents", "trackEnd", "size", "MIDI", "channels", "channelsToAdd", "numCh", "multiCh", "Uint8Array", "chunks", "channel", "<PERSON><PERSON><PERSON><PERSON>", "win", "document", "doc", "domurl", "URL", "webkitURL", "dataURLtoBlob", "dataURL", "parts", "replace", "atob", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Blob", "createObjectURL", "binStr", "binary", "downloadURL", "SonificationTimeline_downloadURL", "filename", "nav", "navigator", "createElement", "String", "msSaveOrOpenBlob", "userAgent", "Error", "isOldEdgeBrowser", "test", "safariBlob", "indexOf", "download", "href", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "open", "location", "SonificationTimeline_defined", "find", "merge", "Sonification_SonificationTimeline", "chart", "isPaused", "isPlaying", "scheduledCallbacks", "playTimestamp", "resumeFromTime", "addChannel", "play", "filterPersists", "resetAfter", "onEnd", "clearScheduledCallbacks", "onEndArgument", "Date", "now", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onPlay", "showTooltip", "showCrosshair", "filterChannels", "filtered", "filteredEvents", "minTime", "acc", "Infinity", "playingChannels", "getEventKeysSignature", "speechOptions", "join", "pointsPlayed", "timeline", "maxTime", "numEvents", "lastCallbackTime", "lastEventTime", "lastEvent<PERSON>eys", "keysSig", "point", "relatedPoint", "series", "<PERSON><PERSON><PERSON><PERSON>", "callback", "s", "xAxis", "crosshair", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yAxis", "hoverPoints", "onMouseOver", "onEndOpt", "onStop", "resetPlayState", "tooltip", "hide", "hoverSeries", "onMouseOut", "axes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "pause", "getCurrentTime", "resume", "resumeFrom", "anchorPlayMoment", "eventFilter", "finalEventTime", "arr", "play<PERSON>d<PERSON><PERSON>", "next", "onBoundaryHit", "closestTime", "lastValidTime", "mid", "cmp", "attemptedNext", "withinTime", "playClosestToPropValue", "targetVal", "closestValDiff", "closestEvent", "diff", "abs", "getEventsForPoint", "pointEvents", "playSegment", "segment", "eventTimes", "first", "last", "segmentSize", "toTime", "some", "getLastPlayedPoint", "closestDiff", "closestPoint", "reset", "getMIDIData", "downloadMIDI", "title", "text", "blob", "url", "revokeObjectURL", "external_highcharts_src_js_default_Templating_namespaceObject", "Templating", "external_highcharts_src_js_default_Templating_default", "TimelineFromChart_clamp", "TimelineFromChart_defined", "TimelineFromChart_extend", "getNestedProperty", "TimelineFromChart_merge", "TimelineFrom<PERSON><PERSON>_pick", "format", "isNoteDefinition", "str", "getPointPropValue", "ret", "mapToVirtualAxis", "valueExtremes", "virtualAxisExtremes", "invert", "logarithmic", "lenValueAxis", "lenVirtualAxis", "virtualValueDelta", "x", "LOG10E", "adjustedNum", "LN10", "logValMin", "getParamValWithDefault", "propMetrics", "useSeriesExtremes", "mappingParamOptions", "fallback", "defaults", "contextValueProp", "getMappingParameterValue", "defaultMapping", "mappingOptions", "mapTo", "mapFunc", "mapFunction", "within", "scale", "isInverted", "char<PERSON>t", "slice", "useContextValue", "fixedValue", "extremes", "axis", "dataMin", "dataMax", "seriesExtremes", "index", "globalExtremes", "scaleAxis", "minOctave", "floor", "maxOctave", "ceil", "lenScale", "scaleIx", "noteNum", "addTimelineChannelFromTrack", "instrMappingOpts", "mapping", "speechOpts", "preferredVoice", "instrument", "midiName", "addMappedInstrumentEvent", "roundToMusicalNotes", "getPara<PERSON>", "param", "parent", "eventsAdded", "eventOpts", "gapBetweenNotes", "play<PERSON>elay", "addNoteEvent", "noteDef", "Array", "addMappedSpeechEvent", "messageParam", "isActive", "activeWhen", "lastPropValue", "crossingOk", "crossingUp", "crossingDown", "hasLastValue", "TimelineFromChart", "sonification", "defaultInstrOpts", "defaultInstrumentOptions", "defaultSpeechOpts", "defaultSpeechOptions", "defaultPointGroupOpts", "enabled", "groupTimespan", "algorithm", "pointGrouping", "globalTracks", "globalContextTracks", "isSequential", "order", "totalDuration", "duration", "afterSeriesWait", "eventOptions", "getPropMetrics", "globalOpts", "defaultInstrMapping", "defaultSpeechMapping", "seriesTimeProps", "commonTimeProps", "addTimeProp", "seriesIx", "props", "perSeriesProps", "addPropFromMappingParam", "removeInvertedFlag", "paramOpts", "subParam", "addPropsFromMappingOptions", "addPropsFromContextTracks", "tracks", "track", "valueProp", "hasCommonTimeProps", "sOpts", "visible", "contextTracks", "trackOpts", "getChartExtremesForProps", "numProps", "numSeriesProps", "initCache", "propList", "cache", "updateCache", "allSeriesExtremes", "points", "j", "k", "sOptions", "seriesDuration", "getAvailableDurationForSeries", "timeProp", "availableDuration", "every", "timeProps", "seriesTimeLen", "sum", "totalPoints", "seriesDefaultInstrOpts", "seriesDefaultSpeechOpts", "seriesPointGroupOpts", "mainTracks", "hasAddedSeries", "mergedOpts", "pointGroupOpts", "updateLastPropValue", "addMappedEventForPoint", "trackOptions", "eventAdded", "pointGroup", "pointGroupTime", "addCurrentPointGroup", "groupSpanTime", "getGroupedPoints", "alg", "r", "minVal", "maxVal", "pointIx", "isLastPoint", "dT", "groupSpan", "spanTime", "firstEvent", "onSeriesStart", "onSeriesEnd", "contextChannel", "timeInterval", "valueInterval", "contextExtremes", "addContextEvent", "valueMapFunction", "defaultOptions", "getOptions", "Sonification_extend", "fireEvent", "Sonification_merge", "Sonification_pick", "Sonification_doc", "Sonification_win", "Sonification", "retryContextCounter", "lastUpdate", "unbindKeydown", "AudioContext", "suspend", "audioDestination", "setAudioDestination", "update", "ready", "onHit", "initBoundaryInstrument", "defaultBoundaryHit", "playAdjacentSeries", "lastPlayed", "targetSeriesIx", "playClosestToProp", "targetValue", "targetFilter", "playNote", "delayMs", "instr", "speakerOptions", "speaker", "son<PERSON><PERSON><PERSON>", "beforePlay", "sonifySeries", "sonifyPoint", "boundaryInstrument", "close", "updateInterval", "forceReady", "scheduledUpdate", "beforeUpdate", "afterUpdate", "when<PERSON><PERSON><PERSON>", "state", "then", "composedClasses", "updateSonificationEnabled", "chartOnDestroy", "chartOnRender", "chartOnUpdate", "newOptions", "compose", "ChartClass", "SeriesClass", "PointClass", "sonify", "toggleSonify", "exportingOptions", "exporting", "buttons", "contextButton", "menuItems", "menuItemDefinitions", "<PERSON><PERSON><PERSON>", "onclick", "playAsSound", "Sonification_Sonification", "G", "Scales", "minor", "dorian", "harmonicMinor", "phrygian", "major", "lydian", "mixolydian", "majorPentatonic", "minorPentatonic", "SonificationSpeaker", "SonificationTimeline", "Chart", "Series", "Point", "sonification_src", "default"], "mappings": "AAYA,UAAYA,MAA6D,sBAAuB,CAEvF,IAAIC,EAAsB,CAAC,CAM1BA,CAAAA,EAAoBC,CAAC,CAAG,AAACC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,UAAU,CACvC,IAAOF,EAAO,OAAU,CACxB,IAAOA,EAER,OADAF,EAAoBK,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAH,EAAoBK,CAAC,CAAG,CAACE,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXR,EAAoBU,CAAC,CAACF,EAAYC,IAAQ,CAACT,EAAoBU,CAAC,CAACH,EAASE,IAC5EE,OAAOC,cAAc,CAACL,EAASE,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAT,EAAoBU,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAM5F,IAAMI,EAAqDrB,EAAwD,OAAU,CAC7H,IAAIsB,EAA0DrB,EAAoBC,CAAC,CAACmB,GAwiCpF,GAAM,CAAEE,MAAAA,CAAK,CAAEC,QAAAA,CAAO,CAAEC,KAAAA,CAAI,CAAE,CAAIH,IASlC,SAASI,EAA6BC,CAAU,CAAEC,CAAI,EAElD,MAAOrB,AADG,CAAA,MAASoB,EAAa,KAAK,EAC1BE,KAAKC,GAAG,CAACF,GADwB,AAAC,CAAA,IAAM,IAAMD,CAAS,EAAK,GAE3E,CAQA,SAASI,EAAoBC,CAAQ,CAAEC,CAAI,CAAEC,CAAG,EAC5CF,EAASG,IAAI,CAACC,qBAAqB,CAACH,GACpCD,EAASG,IAAI,CAACE,eAAe,CAACH,EAAKD,EAAMK,EAAWC,YAAY,CAAG,GACnEP,EAASG,IAAI,CAACK,cAAc,CAACN,EAAKD,EAAOK,EAAWC,YAAY,CACpE,CAUA,SAASE,EAAqBC,CAAQ,CAAEC,CAAI,CAAEV,CAAI,CAAED,CAAQ,CAAEY,EAAmB,CAAC,EAC9E,IAAMC,EAAQF,AAAS,WAATA,EAAmBR,EAAOH,EAASG,IAAI,CAErD,GADAA,EAAKC,qBAAqB,CAACH,GACvB,CAACS,EAASI,MAAM,CAAE,CAClBf,EAAoBC,EAAUC,EAAMY,EAAQD,EAAmB,GAC/D,MACJ,CACIF,CAAQ,CAAC,EAAE,CAACK,CAAC,CAAG,GAChBL,EAASM,OAAO,CAAC,CAAED,EAAG,EAAGb,IAAKW,EAAAA,CAAc,GAEhDH,EAASO,OAAO,CAAC,CAACC,EAAIC,KAClB,IAAMC,EAAOV,CAAQ,CAACS,EAAK,EAAE,CAAEE,EAAQD,EAAO,AAACF,CAAAA,EAAGH,CAAC,CAAGK,EAAKL,CAAC,AAADA,EAAK,IAAO,EAAGO,EAAYrB,EAAQmB,CAAAA,EAAOA,EAAKL,CAAC,CAAG,IAAOT,EAAWC,YAAY,CAAG,CAAA,EAC/IJ,EAAKE,eAAe,CAACa,EAAGhB,GAAG,CAAGU,EAAkBU,EAAWzB,KAAK0B,GAAG,CAACF,EAAOf,EAAWC,YAAY,EAAI,EAC1G,EACJ,CAQA,MAAMiB,EACFC,YAAYC,CAAO,CAAEC,CAAO,CAAE,CAC1B,IAAI,CAACC,UAAU,CAAG/B,KAAKgC,GAAG,CAAChC,KAAK0B,GAAG,CAAC,EAAGI,EAAQC,UAAU,EAAI,KAC7D,IAAME,EAAU,IAAM,IAAIC,eAAeL,EAAS,CAC9Cf,KAAM,WACNqB,OAAQL,EAAQK,MAAM,CACtBC,UAAWpC,KAAK0B,GAAG,CAAC,EAAGI,EAAQM,SAAS,EAAI,IAChD,EACA,CAAA,IAAI,CAACC,OAAO,CAAGJ,IACf,IAAI,CAACK,OAAO,CAAGL,IACf,IAAI,CAACM,aAAa,CAAG,IAAIC,SAASX,EAAS,CAAEvB,KAAM,EAAG,GACtD,IAAI,CAACmC,UAAU,CAAG,IAAID,SAASX,GAC/B,IAAI,CAACa,SAAS,CAAG,IAAIC,UAAUd,EAAS,CACpCe,UAAW,IAAI,CAACb,UAAU,CAAG,IAAI,CAACM,OAAO,CAACD,SAAS,CAACS,KAAK,AAC7D,GACA,IAAI,CAACR,OAAO,CAACS,OAAO,CAAC,IAAI,CAACL,UAAU,EACpC,IAAI,CAACH,OAAO,CAACQ,OAAO,CAAC,IAAI,CAACP,aAAa,EACvC,IAAI,CAACA,aAAa,CAACO,OAAO,CAAC,IAAI,CAACJ,SAAS,EACzC,IAAI,CAACA,SAAS,CAACI,OAAO,CAAC,IAAI,CAACL,UAAU,CAC1C,CACAK,QAAQC,CAAW,CAAE,CACjB,IAAI,CAACN,UAAU,CAACK,OAAO,CAACC,EAC5B,CAEAC,oBAAqB,CACjB,IAAMC,EAAQ,IAAI,CAClB,MAAO,CACH1C,sBAAAA,AAAsB2C,IAClBD,EAAMZ,OAAO,CAACD,SAAS,CAAC7B,qBAAqB,CAAC2C,GAC9CD,EAAMX,OAAO,CAACF,SAAS,CAAC7B,qBAAqB,CAAC2C,GAC9CD,EAAMP,SAAS,CAACE,SAAS,CAACrC,qBAAqB,CAAC2C,GACzCD,EAAMZ,OAAO,CAACD,SAAS,EAElCzB,eAAeyB,CAAS,CAAEhC,CAAI,EAK1B,OAJA,IAAI,CAACG,qBAAqB,CAACH,GAC3B6C,EAAMZ,OAAO,CAACD,SAAS,CAACzB,cAAc,CAACyB,EAAWhC,GAClD6C,EAAMX,OAAO,CAACF,SAAS,CAACzB,cAAc,CAACyB,EAAWhC,GAClD6C,EAAMP,SAAS,CAACE,SAAS,CAACjC,cAAc,CAACX,KAAKmD,KAAK,CAAC,IAAQF,EAAMlB,UAAU,CAAGK,GAAa,IAAOhC,GAC5F6C,EAAMZ,OAAO,CAACD,SAAS,AAClC,EACA5B,gBAAgB4B,CAAS,CAAEhC,CAAI,CAAEgD,CAAY,EAOzC,OANA,IAAI,CAAC7C,qBAAqB,CAACH,GAC3B6C,EAAMZ,OAAO,CAACD,SAAS,CAClB5B,eAAe,CAAC4B,EAAWhC,EAAMgD,GACtCH,EAAMX,OAAO,CAACF,SAAS,CAClB5B,eAAe,CAAC4B,EAAWhC,EAAMgD,GACtCH,EAAMP,SAAS,CAACE,SAAS,CAACpC,eAAe,CAACR,KAAKmD,KAAK,CAAC,IAAQF,EAAMlB,UAAU,CAAGK,GAAa,IAAOhC,EAAMgD,GACnGH,EAAMZ,OAAO,CAACD,SAAS,AAClC,CACJ,CACJ,CACAiB,cAAe,CACX,OAAO,IAAI,CAACX,SAAS,CAACE,SAAS,AACnC,CACAU,OAAQ,CACJ,IAAI,CAACjB,OAAO,CAACiB,KAAK,GAClB,IAAI,CAAChB,OAAO,CAACgB,KAAK,EACtB,CACAC,KAAKnD,CAAI,CAAE,CACP,IAAI,CAACiC,OAAO,CAACkB,IAAI,CAACnD,GAClB,IAAI,CAACkC,OAAO,CAACiB,IAAI,CAACnD,EACtB,CACJ,CAMA,MAAMoD,EACF5B,YAAY6B,CAAY,CAAE3B,CAAO,CAAEiB,CAAW,CAAE,CAC5C,IAAI,CAACU,YAAY,CAAGA,EACpB,IAAI,CAAC3B,OAAO,CAAGA,EACf,IAAI,CAAC4B,cAAc,CAAG5B,EAAQ6B,YAAY,CAC1C,IAAI,CAACC,cAAc,CAAG9B,EAAQ+B,YAAY,CAC1C,IAAI,CAACC,iBAAiB,GACtB,IAAI,CAACC,UAAU,GACf,IAAI,CAACC,aAAa,GAClB,IAAI,CAACC,iBAAiB,GAClBlB,GACA,IAAI,CAACD,OAAO,CAACC,EAErB,CAIAD,QAAQC,CAAW,CAAE,CACjB,CACI,IAAI,CAACmB,WAAW,CAChB,IAAI,CAACC,YAAY,CACjB,IAAI,CAACC,eAAe,CACpB,IAAI,CAACC,MAAM,CACX,IAAI,CAAClE,QAAQ,CACb,IAAI,CAACmE,UAAU,CACf,IAAI,CAACC,SAAS,CACd,IAAI,CAACC,OAAO,CACf,CAACC,MAAM,CAAC,CAAClD,EAAMmD,IAASA,EACpBA,CAAAA,EAAI5B,OAAO,CAACvB,GAAOmD,CAAE,EACtBnD,EAAOwB,EACf,CACAO,OAAQ,CACA,IAAI,CAACkB,OAAO,EACZ,IAAI,CAACA,OAAO,CAAClB,KAAK,GAElB,IAAI,CAACgB,UAAU,EACf,IAAI,CAACA,UAAU,CAAChB,KAAK,GAErB,IAAI,CAACiB,SAAS,EACd,IAAI,CAACA,SAAS,CAACjB,KAAK,EAE5B,CACAqB,WAAWvE,CAAI,CAAE,CACT,IAAI,CAACoE,OAAO,EACZ,IAAI,CAACA,OAAO,CAACjB,IAAI,CAACnD,GAElB,IAAI,CAACkE,UAAU,EACf,IAAI,CAACA,UAAU,CAACf,IAAI,CAACnD,GAErB,IAAI,CAACmE,SAAS,EACd,IAAI,CAACA,SAAS,CAAChB,IAAI,CAACnD,EAE5B,CACAwE,cAAcxE,CAAI,CAAEgC,CAAS,CAAEyC,EAAgB,CAAC,CAAE,CAC9C,IAAMC,EAAO,IAAI,CAAChD,OAAO,CAAEiD,EAAIrF,EAAME,EAAKkF,EAAKE,cAAc,CAAE5C,GAC1D0C,CAAAA,EAAKG,cAAc,EAAI,CAAA,EAAI,EAAG,MAAQC,EAAY,IAAI,CAACC,YAAY,GAAI/B,EAAeyB,EAAgB,IACvGK,IACAA,EAAU3E,qBAAqB,CAACH,GAC5ByE,GAAiBzE,EAAQ,CAAA,IAAI,CAACgF,cAAc,EAAI,EAAC,EAAK,KACtDF,EAAU1E,eAAe,CAACuE,EAAG3E,EAAMgD,GACnC8B,EAAUvE,cAAc,CAACoE,EAAG3E,EAAOgD,IAGnC8B,EAAUvE,cAAc,CAACoE,EAAG3E,IAGpC,IAAI,CAACiF,yBAAyB,CAACN,EAAG3E,EAAMyE,GACxC,IAAI,CAACS,4BAA4B,CAACP,EAAG3E,EAAMyE,GAC3C,IAAI,CAACO,cAAc,CAAGhF,CAC1B,CAGAmF,aAAc,CACV,OAAO,IAAI,CAACf,OAAO,EAAI,IAAI,CAACA,OAAO,CAACrC,MAAM,EACtC,IAAI,CAACmC,UAAU,EAAI,IAAI,CAACA,UAAU,CAACnC,MAAM,EACzC,IAAI,CAACoC,SAAS,EAAI,IAAI,CAACA,SAAS,CAAClB,YAAY,EACrD,CAEAmC,aAAc,CACV,OAAO,IAAI,CAACnB,MAAM,EAAI,IAAI,CAACA,MAAM,CAAC/D,IAAI,AAC1C,CAGAmF,kBAAkB3E,CAAI,CAAEV,CAAI,CAAE,CACrB,IAAI,CAACD,QAAQ,EAKlBS,EAFY,AAACE,CAAAA,AAAS,WAATA,EAAoB,IAAI,CAACgB,OAAO,CAAC4D,cAAc,CACxD,IAAI,CAAC5D,OAAO,CAAC6D,eAAe,AAAD,GAAM,EAAE,CACb7E,EAAMV,EAAM,IAAI,CAACD,QAAQ,CAAE,IAAI,CAAC2B,OAAO,CAAC8D,MAAM,CAC5E,CAEAC,iBAAkB,CACV,IAAI,CAAC1F,QAAQ,EACb,IAAI,CAACA,QAAQ,CAACG,IAAI,CACbC,qBAAqB,CAAC,IAAI,CAACkD,YAAY,CAACqC,WAAW,EAE5D,IAAMZ,EAAY,IAAI,CAACC,YAAY,GAC/BD,GACAA,EAAU3E,qBAAqB,CAAC,GAEhC,IAAI,CAAC2D,WAAW,EAChB,IAAI,CAACA,WAAW,CAAC9B,SAAS,CAAC7B,qBAAqB,CAAC,GAEjD,IAAI,CAAC4D,YAAY,EACjB,IAAI,CAACA,YAAY,CAAC/B,SAAS,CAAC7B,qBAAqB,CAAC,GAElD,IAAI,CAAC6D,eAAe,EACpB,IAAI,CAACA,eAAe,CAAC9D,IAAI,CAACC,qBAAqB,CAAC,EAExD,CAEA8E,0BAA0BjD,CAAS,CAAEhC,CAAI,CAAEyE,CAAa,CAAE,CACtD,GAAI,IAAI,CAACT,eAAe,CAAE,CACtB,IAAM2B,EAAIlG,EAA6B,IAAI,CAACiC,OAAO,CAACkE,6BAA6B,EAAI,EAAG5D,GAAY6D,EAAWpB,EAAgBA,EAAgB,IAC3IpE,EAAWC,YAAY,CAC3B,IAAI,CAAC0D,eAAe,CAAC9D,IAAI,CAACC,qBAAqB,CAACH,GAChD,IAAI,CAACgE,eAAe,CAAC9D,IAAI,CAACE,eAAe,CAACuF,EAAG3F,EAAM6F,EAAW,GAC9D,IAAI,CAAC7B,eAAe,CAAC9D,IAAI,CAACK,cAAc,CAACoF,EAAG3F,EAAO6F,EACvD,CACJ,CAEAX,6BAA6BlD,CAAS,CAAEhC,CAAI,CAAEyE,CAAa,CAAE,CACzD,IAAMC,EAAO,IAAI,CAAChD,OAAO,CAAEmE,EAAWpB,EAAgBA,EAAgB,IAClEpE,EAAWC,YAAY,CAAEwF,EAAuB,CAACC,EAAYC,KAC7D,IAAMtG,EAAaD,EAA6BuG,EAAcC,gCAAgC,EAAI,EAAGjE,GAAY2C,EAAIrF,EAAM,AAAC0G,CAAAA,EAAchE,SAAS,EAAI,GAAG,EAAKtC,EAAY,EAAG,MAC9KqG,EAAW/D,SAAS,CAAC7B,qBAAqB,CAACH,GAC3C+F,EAAW/D,SAAS,CAAC5B,eAAe,CAACuE,EAAG3E,EAAM6F,EAAW,GACzDE,EAAW/D,SAAS,CAACzB,cAAc,CAACoE,EAAG3E,EAAO6F,EAClD,CACI,CAAA,IAAI,CAAC/B,WAAW,EAAIY,EAAKwB,OAAO,EAChCJ,EAAqB,IAAI,CAAChC,WAAW,CAAEY,EAAKwB,OAAO,EAEnD,IAAI,CAACnC,YAAY,EAAIW,EAAKyB,QAAQ,EAClCL,EAAqB,IAAI,CAAC/B,YAAY,CAAEW,EAAKyB,QAAQ,CAE7D,CACAxC,YAAa,CACT,IAAMe,EAAO,IAAI,CAAChD,OAAO,CAAkBnC,CAAAA,EAAQmF,EAAKc,MAAM,GAC1Dd,EAAKY,cAAc,EAAIZ,EAAKY,cAAc,CAACzE,MAAM,EACjD6D,EAAKa,eAAe,EAAIb,EAAKa,eAAe,CAAC1E,MAAM,AAAD,GAElD,CAAA,IAAI,CAACd,QAAQ,CAAG,IAAIqC,SAAS,IAAI,CAACiB,YAAY,CAAE,CAC5CnD,KAAMV,EAAKkF,EAAKc,MAAM,CAAE,EAC5B,EAAC,EAGL,IAAI,CAACvB,MAAM,CAAG,IAAI7B,SAAS,IAAI,CAACiB,YAAY,CAChD,CAEAK,mBAAoB,CAChB,IAAMgB,EAAO,IAAI,CAAChD,OAAO,CAAE0E,EAAM,IAAI,CAAC/C,YAAY,CAAErB,EAAY,AAAC0C,CAAAA,EAAKE,cAAc,EAAI,CAAA,EACnFF,CAAAA,EAAKG,cAAc,EAAI,CAAA,EAC5B,GAAIH,AAAc,eAAdA,EAAKhE,IAAI,CAAmB,CAC5B,IAAM2F,EAAQD,AAAiB,EAAjBA,EAAIE,UAAU,CAAMC,EAASH,EAAII,YAAY,CAAC,EAAGH,EAAOD,EAAIE,UAAU,EAAGG,EAAOF,EAAOG,cAAc,CAAC,GACpH,IAAK,IAAIC,EAAI,EAAGA,EAAIN,EAAO,EAAEM,EAEzBF,CAAI,CAACE,EAAE,CAAG/G,AAAgB,IAAhBA,KAAKgH,MAAM,GAAW,GAEpC,IAAMC,EAAK,IAAI,CAAC3C,UAAU,CAAGkC,EAAIU,kBAAkB,EACnDD,CAAAA,EAAGN,MAAM,CAAGA,EACZM,EAAGE,IAAI,CAAG,CAAA,CACd,KACSrC,AAAc,UAAdA,EAAKhE,IAAI,CACd,IAAI,CAACyD,SAAS,CAAG,IAAI5C,EAAa6E,EAAK,CACnCrE,OAAQ2C,EAAK3C,MAAM,CACnBJ,WAAY+C,EAAK/C,UAAU,CAC3BK,UAAAA,CACJ,GAGA,IAAI,CAACoC,OAAO,CAAG,IAAItC,eAAesE,EAAK,CACnC1F,KAAMgE,EAAKhE,IAAI,EAAI,OACnBqB,OAAQ2C,EAAK3C,MAAM,CACnBC,UAAAA,CACJ,EAER,CAEA4B,eAAgB,CACZ,IAAMc,EAAO,IAAI,CAAChD,OAAO,AACrBgD,CAAAA,EAAKwB,OAAO,EAAIxB,EAAKwB,OAAO,CAAClE,SAAS,EACtC,CAAA,IAAI,CAAC8B,WAAW,CAAG,IAAIkD,iBAAiB,IAAI,CAAC3D,YAAY,CAAE,CACvD3C,KAAM,UACNuG,EAAGvC,EAAKwB,OAAO,CAACe,CAAC,EAAI,EACrBjF,UAAW0C,EAAKwB,OAAO,CAAClE,SAAS,AACrC,EAAC,EAED0C,EAAKyB,QAAQ,EAAIzB,EAAKyB,QAAQ,CAACnE,SAAS,EACxC,CAAA,IAAI,CAAC+B,YAAY,CAAG,IAAIiD,iBAAiB,IAAI,CAAC3D,YAAY,CAAE,CACxD3C,KAAM,WACNuG,EAAGvC,EAAKyB,QAAQ,CAACc,CAAC,EAAI,EACtBjF,UAAW0C,EAAKyB,QAAQ,CAACnE,SAAS,AACtC,EAAC,CAET,CAEA6B,mBAAoB,CAChB,IAAMa,EAAO,IAAI,CAAChD,OAAO,AACrBgD,CAAAA,EAAKkB,6BAA6B,EAClClB,AAAuC,IAAvCA,EAAKkB,6BAA6B,EAClC,CAAA,IAAI,CAAC5B,eAAe,CAAG,IAAI5B,SAAS,IAAI,CAACiB,YAAY,CAAE,CACnDnD,KAAM,CACV,EAAC,CAET,CAEA6E,cAAe,CACX,OAAO,IAAI,CAACX,OAAO,CAAG,IAAI,CAACA,OAAO,CAACpC,SAAS,CACxC,IAAI,CAACmC,SAAS,EAAI,IAAI,CAACA,SAAS,CAACvB,kBAAkB,EAC3D,CACJ,CAqBA,MAAMvC,EACFmB,YAAY6B,CAAY,CAAE3B,CAAO,CAAE,CAC/B,IAAI,CAAC2B,YAAY,CAAGA,EACpB,IAAI,CAAC3B,OAAO,CAAGA,EACf,IAAI,CAACwF,OAAO,CAAG,EAAE,CACjB,IAAI,CAACC,cAAc,CAAGzF,EAAQyF,cAAc,EAAI,EAChD,IAAI,CAACC,UAAU,CAAG,IAAIhF,SAASiB,EAAc,CAAEnD,KAAM,CAAE,GACvD,IAAI,CAACmH,aAAa,CAAC,IAAI,CAACD,UAAU,EAClC,IAAME,EAAY,IAAI,CAACJ,OAAO,CAACrG,MAAM,CACjC,IAAI,CAACqG,OAAO,CAAC,EAAE,CAAG,IAAI,CAACE,UAAU,AACrC,CAAA,IAAI,CAACG,WAAW,CAAG,AAAC,CAAA,IAAI,CAAC7F,OAAO,CAAC6F,WAAW,EAAI,EAAE,AAAD,EAAGC,GAAG,CAAC,AAACC,GAAY,IAAIrE,EAAWC,EAAcoE,EAASlI,EAAQkI,EAAQlE,YAAY,GAAKhE,EAAQkI,EAAQhE,YAAY,EACpK,KAAK,EAAI6D,IAGb,IAAI,CAACC,WAAW,CAACvG,OAAO,CAAC,AAAC0G,IACtB,IAAMC,EAAgB,CAACC,EAAYC,KAC/B,GAAIA,EAAW,CACX,IAAMC,EAASD,CAAS,CAACD,EAAW,GAChCE,GACAJ,EAAIhF,OAAO,CAACoF,EAEpB,CACJ,EACIvI,EAAQmI,EAAIpE,cAAc,GAC1BqE,EAAc,cAAe,IAAI,CAACJ,WAAW,CAACG,EAAIpE,cAAc,CAAC,EAEjE/D,EAAQmI,EAAIlE,cAAc,GAC1BmE,EAAc,cAAe,IAAI,CAACJ,WAAW,CAACG,EAAIlE,cAAc,CAAC,CAEzE,EACJ,CAKAuE,eAAgB,CACZ,IAAI,CAACX,UAAU,CAAClH,IAAI,CAACuC,KAAK,CAAG,EAC7B,IAAI,CAAC8E,WAAW,CAACvG,OAAO,CAAC,AAACtC,GAAMA,EAAEwE,KAAK,GAC3C,CAKAC,MAAO,CACH,IAAM6E,EAAU,IAAI,CAAC3E,YAAY,CAACqC,WAAW,CAAEuC,EAAUD,EAAU3H,EAAWC,YAAY,CAC1FR,EAAoB,IAAI,CAACsH,UAAU,CAAEY,EAAS,GAC9C,IAAI,CAACT,WAAW,CAACvG,OAAO,CAAC,AAACtC,GAAMA,EAAE6F,UAAU,CAAC0D,IAC7C,IAAI,CAACb,UAAU,CAACc,UAAU,EAC9B,CAQAC,cAAcnI,CAAI,CAAE,CAChB,GAAI,CAACA,GAAQ,IAAI,CAACoH,UAAU,CAAClH,IAAI,CAACuC,KAAK,CAAG,IAAM,CAC5C,IAAI,CAAC2E,UAAU,CAAClH,IAAI,CAACuC,KAAK,CAAG,EAC7B,MACJ,CACA,IAAI,CAAC2F,aAAa,CAAC,AAACpI,CAAAA,GAAQ,CAAA,EAAK,IAAI,CAACqD,YAAY,CAACqC,WAAW,CAClE,CAKA2C,MAAO,CACH,IAAI,CAAC5C,eAAe,GACpB3F,EAAoB,IAAI,CAACsH,UAAU,CAAE,IAAI,CAAC/D,YAAY,CAACqC,WAAW,CAAE,EACxE,CAUA4C,eAAetI,CAAI,CAAEgC,CAAS,CAAEuG,CAAY,CAAE,CAC1C,IAAMzH,EAAI,AAACd,CAAAA,GAAQ,CAAA,EAAK,IAAI,CAACqD,YAAY,CAACqC,WAAW,CAAEhB,EAAO,IAAI,CAAChD,OAAO,CAC1E,IAAI,CAAC6F,WAAW,CAACvG,OAAO,CAAC,AAACtC,IACtBA,EAAE8F,aAAa,CAAC1D,EAAGkB,EAAW0C,EAAK8D,iBAAiB,EACpD9J,EAAE2G,iBAAiB,CAAC,SAAUvE,EAClC,GACAN,EAAqBkE,EAAK+D,oBAAoB,EAAI,EAAE,CAAE,SAAU3H,EAAG,IAAI,CAACsG,UAAU,CAAE1C,EAAKgE,YAAY,EACjGH,GACA,IAAI,CAACH,aAAa,CAACtH,EAAIyH,EAAe,IAE9C,CAKA9C,iBAAkB,CACd,IAAI,CAAC2B,UAAU,CAAClH,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAACkD,YAAY,CAACqC,WAAW,EACxE,IAAI,CAAC6B,WAAW,CAACvG,OAAO,CAAC,AAACtC,GAAMA,EAAE+G,eAAe,GACrD,CAOA/C,QAAQiG,CAAe,CAAE,CACrB,OAAO,IAAI,CAACvB,UAAU,CAAC1E,OAAO,CAACiG,EACnC,CAKAtB,cAAcD,CAAU,CAAE,CACtB,IAAI,CAACF,OAAO,CAAG,AAAC,CAAA,IAAI,CAACxF,OAAO,CAACkH,EAAE,EAAI,EAAE,AAAD,EAAGpB,GAAG,CAAC,AAACqB,GAAU,IAAI7B,iBAAiB,IAAI,CAAC3D,YAAY,CAAE,CAC1F3C,KAAM,UACN,GAAGmI,CAAK,AACZ,IAEA,IAAI,CAAC3B,OAAO,CAAC4B,WAAW,CAAC,CAACC,EAAOC,KAC7BA,EAAKtG,OAAO,CAACqG,GACNC,GACR5B,EACP,CAKAgB,cAAcpI,CAAI,CAAE,CAChB,IAAIiJ,EAAqB,EACzB,IAAI,CAAC1B,WAAW,CAACvG,OAAO,CAAC,AAACtC,IACtB,IAAMwK,EAAMxK,EAAEgD,OAAO,CAAC6D,eAAe,CACjC2D,GAAOA,EAAIrI,MAAM,GACjBoI,EAAqBrJ,KAAK0B,GAAG,CAAC2H,EAAoBC,CAAG,CAACA,EAAIrI,MAAM,CAAG,EAAE,CAACC,CAAC,EACvEpC,EAAE2G,iBAAiB,CAAC,UAAWrF,GAEvC,GACA,IAAMmJ,EAAY,IAAI,CAACzH,OAAO,CAAC0H,qBAAqB,EAAI,EAAE,AACtDD,CAAAA,EAAUtI,MAAM,GAChBL,EAAqB2I,EAAW,UAAWnJ,EAAM,IAAI,CAACoH,UAAU,CAAE,IAAI,CAAC1F,OAAO,CAACgH,YAAY,EAC3FO,EAAqBrJ,KAAK0B,GAAG,CAAC2H,EAAoBE,CAAS,CAACA,EAAUtI,MAAM,CAAG,EAAE,CAACC,CAAC,GAEvFhB,EAAoB,IAAI,CAACsH,UAAU,CAAEpH,EAAOiJ,EAAqB,IAAM,EAC3E,CACJ,CACA5I,EAAWC,YAAY,CAAG,KA+L1B,IAAM+I,EAAoB,CAEtBC,MAAO,CACHZ,aAAc,IACdD,qBAAsB,CAClB,CAAE3H,EAAG,EAAGb,IAAK,GAAK,EAClB,CAAEa,EAAG,GAAIb,IAAK,GAAK,EACnB,CAAEa,EAAG,GAAIb,IAAK,GAAK,EACnB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,CAAE,EACpB,CACD2I,GAAI,CACA,CAAE5G,UAAW,IAAKiF,EAAG,GAAK/G,KAAM,CAAE,EAClC,CAAE8B,UAAW,IAAK9B,KAAM,CAAE,EAC1B,CAAE8B,UAAW,KAAM9B,KAAM,CAAE,EAC3B,CAAE8B,UAAW,KAAMiF,EAAG,GAAK/G,KAAM,CAAE,EACnC,CAAE8B,UAAW,KAAMiF,EAAG,GAAK/G,KAAM,CAAE,EACnC,CAAE8B,UAAW,KAAMiF,EAAG,GAAK/G,KAAM,EAAG,EACpC,CAAE8B,UAAW,IAAM9B,KAAM,GAAI,EAC7B,CAAE8B,UAAW,IAAOiF,EAAG,GAAK/G,KAAM,GAAI,EACzC,CACDqH,YAAa,CAAC,CACN7G,KAAM,QACN8E,OAAQ,GACR7D,WAAY,IACZiE,8BAA+B,GAC/BM,QAAS,CACLlE,UAAW,IACXiE,iCAAkC,IAClCgB,EAAG,EACP,EACAd,SAAU,CAAEnE,UAAW,GAAI,EAC3BsD,eAAgB,CAAC,CAAExE,EAAG,EAAGb,IAAK,CAAE,EAAE,CAClCsF,gBAAiB,CACb,CAAEzE,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,CAAE,EACpB,AACL,EAAG,CACCS,KAAM,aACN8E,OAAQ,GACRU,QAAS,CAAElE,UAAW,GAAI,EAC1BmE,SAAU,CAAEnE,UAAW,GAAI,EAC3BsD,eAAgB,CACZ,CAAExE,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,GAAIb,IAAK,CAAE,EACnB,AACL,EAAE,AACV,EAEAsJ,QAAS,CACLb,aAAc,GACdvB,eAAgB,GAChBsB,qBAAsB,CAClB,CAAE3H,EAAG,EAAGb,IAAK,GAAK,EAClB,CAAEa,EAAG,EAAGb,IAAK,GAAK,EAClB,CAAEa,EAAG,GAAIb,IAAK,EAAI,EAClB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,CAAE,EACpB,CACD2I,GAAI,CACA,CAAE5G,UAAW,IAAK9B,KAAM,EAAG,EAC3B,CAAE8B,UAAW,KAAMiF,EAAG,EAAG/G,KAAM,CAAE,EACjC,CAAE8B,UAAW,KAAM9B,KAAM,GAAI,EAC7B,CAAE8B,UAAW,KAAM9B,KAAM,EAAG,EAC5B,CAAE8B,UAAW,KAAM9B,KAAM,EAAG,EAC5B,CAAE8B,UAAW,KAAMiF,EAAG,EAAG/G,KAAM,EAAG,EACrC,CACDqH,YAAa,CAAC,CACN7G,KAAM,WACN8E,OAAQ,GACRI,8BAA+B,GAC/BO,SAAU,CAAEnE,UAAW,GAAI,EAC3BkE,QAAS,CAAElE,UAAW,GAAK,EAC3BuD,gBAAiB,CACb,CAAEzE,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,CAAE,EACpB,AACL,EAAE,AACV,EAEAuJ,MAAO,CACHd,aAAc,IACdvB,eAAgB,GAChBqB,kBAAmB,GACnBC,qBAAsB,CAClB,CAAE3H,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,GAAIb,IAAK,CAAE,EAChB,CAAEa,EAAG,GAAIb,IAAK,GAAK,EACnB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACvB,CACDmJ,sBAAuB,CACnB,CAAEtI,EAAG,EAAGb,IAAK,GAAK,EAClB,CAAEa,EAAG,GAAIb,IAAK,GAAK,EACnB,CAAEa,EAAG,IAAKb,IAAK,CAAE,EACpB,CACD2I,GAAI,CACA,CAAE5G,UAAW,IAAKiF,EAAG,GAAK/G,KAAM,GAAI,EACpC,CAAE8B,UAAW,IAAK9B,KAAM,CAAE,EAC1B,CAAE8B,UAAW,KAAM9B,KAAM,EAAG,EAC5B,CAAE8B,UAAW,KAAM9B,KAAM,GAAI,EAC7B,CAAE8B,UAAW,IAAM9B,KAAM,CAAE,EAC3B,CAAE8B,UAAW,KAAM9B,KAAM,EAAG,EAC5B,CAAE8B,UAAW,IAAM9B,KAAM,EAAG,EAC5B,CAAE8B,UAAW,MAAO9B,KAAM,CAAE,EAC/B,CACDqH,YAAa,CAAC,CACN7G,KAAM,WACN8E,OAAQ,EACRI,8BAA+B,GAC/BM,QAAS,CACLlE,UAAW,GACXiE,iCAAkC,GACtC,EACAE,SAAU,CACNnE,UAAW,GACf,CACJ,EAAG,CACCtB,KAAM,OACNkE,eAAgB,EAChBY,OAAQ,GACR/B,aAAc,EACd6B,eAAgB,CACZ,CAAExE,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,GAAIb,IAAK,CAAE,EAChB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACvB,AACL,EAAG,CACCS,KAAM,aACN8E,OAAQ,IACRU,QAAS,CACLlE,UAAW,IACXiF,EAAG,CACP,EACAd,SAAU,CACNnE,UAAW,IACXiF,EAAG,CACP,EACAxD,aAAc,EACd6B,eAAgB,CACZ,CAAExE,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,GAAIb,IAAK,CAAE,EAChB,CAAEa,EAAG,GAAIb,IAAK,EAAI,EACrB,AACL,EAAE,AACV,EAEAwJ,KAAM,CACFf,aAAc,EACdvB,eAAgB,GAChBsB,qBAAsB,CAClB,CAAE3H,EAAG,EAAGb,IAAK,GAAK,EAClB,CAAEa,EAAG,GAAIb,IAAK,EAAI,EAClB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,CAAE,EACjB,CAAEa,EAAG,IAAKb,IAAK,CAAE,EACpB,CACD2I,GAAI,CACA,CAAE5G,UAAW,IAAK9B,KAAM,EAAG,EAC3B,CAAE8B,UAAW,IAAK9B,KAAM,EAAG,EAC3B,CAAE8B,UAAW,IAAKiF,EAAG,GAAK/G,KAAM,GAAI,EACpC,CAAE8B,UAAW,KAAM9B,KAAM,CAAE,EAC3B,CAAE8B,UAAW,KAAM9B,KAAM,EAAG,EAC5B,CAAE8B,UAAW,KAAM9B,KAAM,GAAI,EAC7B,CAAE8B,UAAW,KAAM9B,KAAM,GAAI,EAC7B,CAAE8B,UAAW,IAAO9B,KAAM,CAAE,EAC/B,CACDqH,YAAa,CAAC,CACN7G,KAAM,WACN8E,OAAQ,IACRI,8BAA+B,GAC/BM,QAAS,CAAElE,UAAW,GAAK,EAC3BmE,SAAU,CAAEnE,UAAW,GAAI,CAC/B,EAAG,CACCtB,KAAM,WACN8E,OAAQ,GACRU,QAAS,CAAElE,UAAW,GAAK,EAC3BmE,SAAU,CAAEnE,UAAW,IAAKiF,EAAG,CAAE,EACjC1B,gBAAiB,CACb,CAAEzE,EAAG,EAAGb,IAAK,GAAK,EAClB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,CAAE,EACpB,AACL,EAAE,AACV,EAEAyJ,WAAY,CACRhB,aAAc,EACdvB,eAAgB,GAChBsB,qBAAsB,CAClB,CAAE3H,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,GAAIb,IAAK,GAAK,EACnB,CAAEa,EAAG,GAAIb,IAAK,GAAK,EACnB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,CAAE,EACpB,CACD2I,GAAI,CACA,CAAE5G,UAAW,IAAKiF,EAAG,GAAK/G,KAAM,GAAI,EACpC,CAAE8B,UAAW,IAAK9B,KAAM,EAAG,EAC3B,CAAE8B,UAAW,KAAMiF,EAAG,GAAK/G,KAAM,CAAE,EACnC,CAAE8B,UAAW,KAAMiF,EAAG,GAAK/G,KAAM,CAAE,EACnC,CAAE8B,UAAW,KAAM9B,KAAM,CAAE,EAC3B,CAAE8B,UAAW,MAAO9B,KAAM,CAAE,EAC/B,CACDqH,YAAa,CAAC,CACN7G,KAAM,OACN8E,OAAQ,IACRI,8BAA+B,KAC/BN,eAAgB,CAAC,CAAExE,EAAG,EAAGb,IAAK,CAAE,EAAE,CAClCsF,gBAAiB,CACb,CAAEzE,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,CAAE,EACpB,AACL,EAAG,CACCS,KAAM,aACN8E,OAAQ,IACRI,8BAA+B,KAC/BM,QAAS,CACLlE,UAAW,GACf,EACAmE,SAAU,CACNnE,UAAW,GACf,EACAsD,eAAgB,CACZ,CAAExE,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,EAAGb,IAAK,CAAE,EAClB,AACL,EAAG,CACCS,KAAM,OACNmE,eAAgB,EAChBW,OAAQ,IACRI,8BAA+B,IACnC,EAAG,CACClF,KAAM,OACNkE,eAAgB,EAChBY,OAAQ,EACRjC,aAAc,EACdgC,gBAAiB,CACb,CAAEzE,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,CAAE,EACpB,AACL,EAAG,CACCS,KAAM,OACNkE,eAAgB,EAChBY,OAAQ,EACRjC,aAAc,CAClB,EAAG,CACC7C,KAAM,OACNmE,eAAgB,EAChBW,OAAQ,KACRI,8BAA+B,KAC/BL,gBAAiB,CACb,CAAEzE,EAAG,EAAGb,IAAK,GAAK,EAClB,CAAEa,EAAG,IAAKb,IAAK,CAAE,EACpB,AACL,EAAE,AACV,EAEA0J,UAAW,CACPjB,aAAc,EACdvB,eAAgB,GAChBqB,kBAAmB,GACnBC,qBAAsB,CAClB,CAAE3H,EAAG,EAAGb,IAAK,GAAK,EAClB,CAAEa,EAAG,GAAIb,IAAK,CAAE,EAChB,CAAEa,EAAG,GAAIb,IAAK,GAAK,EACnB,CAAEa,EAAG,IAAKb,IAAK,EAAI,EACnB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACvB,CACDmJ,sBAAuB,CACnB,CAAEtI,EAAG,EAAGb,IAAK,GAAK,EAClB,CAAEa,EAAG,GAAIb,IAAK,GAAK,EACnB,CAAEa,EAAG,IAAKb,IAAK,CAAE,EACpB,CACD2I,GAAI,CACA,CAAE5G,UAAW,IAAK9B,KAAM,EAAG,EAC3B,CAAE8B,UAAW,IAAK9B,KAAM,CAAE,EAC1B,CAAE8B,UAAW,IAAK9B,KAAM,GAAI,EAC5B,CAAE8B,UAAW,KAAM9B,KAAM,EAAG,EAC5B,CAAE8B,UAAW,KAAM9B,KAAM,EAAG,EAC5B,CAAE8B,UAAW,KAAM9B,KAAM,EAAG,EAC5B,CAAE8B,UAAW,MAAO9B,KAAM,CAAE,EAC/B,CACDqH,YAAa,CAAC,CACN7G,KAAM,WACN8E,OAAQ,IACRI,8BAA+B,IAC/BM,QAAS,CACLlE,UAAW,GACXiE,iCAAkC,GACtC,EACAE,SAAU,CACNnE,UAAW,GACf,CACJ,EAAG,CACCtB,KAAM,aACNkE,eAAgB,EAChBY,OAAQ,GACRW,SAAU,CACNnE,UAAW,GACf,EACAyB,aAAc,EACd6B,eAAgB,CACZ,CAAExE,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,GAAIb,IAAK,CAAE,EAChB,CAAEa,EAAG,GAAIb,IAAK,GAAK,EACnB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACvB,AACL,EAAG,CACCS,KAAM,OACNkE,eAAgB,EAChBY,OAAQ,EACRjC,aAAc,EACd+B,eAAgB,CACZ,CAAExE,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,GAAIb,IAAK,GAAK,EACnB,CAAEa,EAAG,GAAIb,IAAK,CAAE,EAChB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACvB,AACL,EAAG,CACCS,KAAM,OACNkE,eAAgB,EAChBY,OAAQ,EACRjC,aAAc,EACd+B,eAAgB,CACZ,CAAExE,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,GAAIb,IAAK,GAAK,EACnB,CAAEa,EAAG,GAAIb,IAAK,CAAE,EAChB,CAAEa,EAAG,GAAIb,IAAK,CAAE,EAChB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,CAAE,EACpB,AACL,EAAE,AACV,EAEA2J,QAAS,CACLlB,aAAc,GACdvB,eAAgB,GAChBqB,kBAAmB,GACnBC,qBAAsB,CAClB,CAAE3H,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,GAAIb,IAAK,CAAE,EAChB,CAAEa,EAAG,GAAIb,IAAK,GAAK,EACnB,CAAEa,EAAG,GAAIb,IAAK,CAAE,EAChB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACvB,CACDmJ,sBAAuB,CACnB,CAAEtI,EAAG,EAAGb,IAAK,GAAK,EAClB,CAAEa,EAAG,GAAIb,IAAK,GAAK,EACnB,CAAEa,EAAG,GAAIb,IAAK,GAAK,EACnB,CAAEa,EAAG,GAAIb,IAAK,CAAE,EACnB,CACD2I,GAAI,CACA,CAAE5G,UAAW,IAAKiF,EAAG,GAAK/G,KAAM,EAAG,EACnC,CAAE8B,UAAW,IAAKiF,EAAG,GAAK/G,KAAM,CAAE,EAClC,CAAE8B,UAAW,KAAMiF,EAAG,GAAK/G,KAAM,EAAG,EACpC,CAAE8B,UAAW,KAAMiF,EAAG,EAAG/G,KAAM,CAAE,EACjC,CAAE8B,UAAW,KAAMiF,EAAG,GAAK/G,KAAM,EAAG,EACpC,CAAE8B,UAAW,KAAM9B,KAAM,EAAG,EAC5B,CAAE8B,UAAW,KAAM9B,KAAM,GAAI,EAC7B,CAAE8B,UAAW,MAAOiF,EAAG,GAAK/G,KAAM,GAAI,EACzC,CACDqH,YAAa,CAAC,CACN7G,KAAM,WACN8E,OAAQ,IACR7D,WAAY,GACZiE,8BAA+B,GAC/BM,QAAS,CAAElE,UAAW,KAAMiF,EAAG,CAAE,CACrC,EAAG,CACCvG,KAAM,OACNkE,eAAgB,EAChBY,OAAQ,GACR/B,aAAc,EACd6B,eAAgB,CACZ,CAAExE,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACvB,AACL,EAAG,CACCS,KAAM,aACN8E,OAAQ,IACRW,SAAU,CAAEnE,UAAW,IAAMiF,EAAG,CAAE,EAClCxD,aAAc,EACd6B,eAAgB,CACZ,CAAExE,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,GAAIb,IAAK,GAAK,EACnB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACvB,AACL,EAAG,CACCS,KAAM,OACNkE,eAAgB,IAChBY,OAAQ,GACRjC,aAAc,EACd+B,eAAgB,CACZ,CAAExE,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,GAAIb,IAAK,CAAE,EAChB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACvB,AACL,EAAE,AACV,EAEA4J,SAAU,CACNnB,aAAc,GACdvB,eAAgB,GAChBqB,kBAAmB,GACnBC,qBAAsB,CAClB,CAAE3H,EAAG,EAAGb,IAAK,EAAI,EACjB,CAAEa,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACvB,CACD2I,GAAI,CAAC,CAAE5G,UAAW,IAAK9B,KAAM,EAAG,EAAE,CAClCqH,YAAa,CAAC,CACN7G,KAAM,WACN8E,OAAQ,GACRI,8BAA+B,EACnC,EAAG,CACClF,KAAM,WACN8E,OAAQ,GACRzD,OAAQ,GACR6D,8BAA+B,EACnC,EAAG,CACClF,KAAM,WACN8E,OAAQ,GACRzD,OAAQ,IACR6D,8BAA+B,EACnC,EAAE,AACV,EAEAkE,OAAQ,CACJpB,aAAc,EACdF,kBAAmB,EACnBY,sBAAuB,CACnB,CAAEtI,EAAG,EAAGb,IAAK,GAAK,EAClB,CAAEa,EAAG,GAAIb,IAAK,GAAK,EACnB,CAAEa,EAAG,IAAKb,IAAK,CAAE,EACpB,CACD2I,GAAI,CACA,CAAE5G,UAAW,IAAKiF,EAAG,GAAK/G,KAAM,GAAI,EACpC,CAAE8B,UAAW,KAAM9B,KAAM,EAAG,EAC5B,CAAE8B,UAAW,KAAM9B,KAAM,GAAI,EAC7B,CAAE8B,UAAW,IAAM9B,KAAM,CAAE,EAC3B,CAAE8B,UAAW,KAAM9B,KAAM,EAAG,EAC5B,CAAE8B,UAAW,IAAM9B,KAAM,EAAG,EAC5B,CAAE8B,UAAW,MAAO9B,KAAM,CAAE,EAC/B,CACDqH,YAAa,CAAC,CACN7G,KAAM,WACN8E,OAAQ,EACRI,8BAA+B,IAC/BM,QAAS,CAAElE,UAAW,GAAIiE,iCAAkC,GAAI,EAChEE,SAAU,CAAEnE,UAAW,GAAI,CAC/B,EAAG,CACCtB,KAAM,aACN8E,OAAQ,IACRU,QAAS,CAAElE,UAAW,IAAMiF,EAAG,CAAE,EACjCd,SAAU,CAAEnE,UAAW,IAAMiF,EAAG,CAAE,EAClCxD,aAAc,EACd6B,eAAgB,CACZ,CAAExE,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,GAAIb,IAAK,CAAE,EAChB,CAAEa,EAAG,GAAIb,IAAK,GAAK,EACtB,AACL,EAAE,AACV,EAEA8J,OAAQ,CACJrB,aAAc,GACdE,GAAI,CACA,CAAE5G,UAAW,IAAKiF,EAAG,GAAK/G,KAAM,CAAE,EAClC,CAAE8B,UAAW,IAAK9B,KAAM,CAAE,EAC1B,CAAE8B,UAAW,KAAM9B,KAAM,EAAG,EAC5B,CAAE8B,UAAW,KAAMiF,EAAG,GAAK/G,KAAM,CAAE,EACnC,CAAE8B,UAAW,KAAMiF,EAAG,GAAK/G,KAAM,CAAE,EACnC,CAAE8B,UAAW,KAAMiF,EAAG,GAAK/G,KAAM,EAAG,EACpC,CAAE8B,UAAW,IAAM9B,KAAM,GAAI,EAC7B,CAAE8B,UAAW,IAAOiF,EAAG,GAAK/G,KAAM,GAAI,EACzC,CACDqH,YAAa,CAAC,CACN7G,KAAM,QACN8E,OAAQ,GACR7D,WAAY,IACZiE,8BAA+B,GAC/BM,QAAS,CACLlE,UAAW,IACXiE,iCAAkC,IAClCgB,EAAG,EACP,EACAd,SAAU,CAAEnE,UAAW,GAAI,CAC/B,EAAE,AACV,EAEAgI,MAAO,CACHtB,aAAc,EACdD,qBAAsB,CAClB,CAAE3H,EAAG,EAAGb,IAAK,GAAK,EAClB,CAAEa,EAAG,GAAIb,IAAK,GAAK,EACnB,CAAEa,EAAG,GAAIb,IAAK,GAAK,EACnB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,CAAE,EACpB,CACD2I,GAAI,CACA,CAAE5G,UAAW,IAAK9B,KAAM,EAAG,EAC3B,CAAE8B,UAAW,IAAKiF,EAAG,EAAG/G,KAAM,CAAE,EAChC,CAAE8B,UAAW,IAAK9B,KAAM,GAAI,EAC5B,CAAE8B,UAAW,KAAM9B,KAAM,EAAG,EAC5B,CAAE8B,UAAW,KAAM9B,KAAM,EAAG,EAC5B,CAAE8B,UAAW,KAAMiF,EAAG,GAAK/G,KAAM,EAAG,EACpC,CAAE8B,UAAW,KAAM9B,KAAM,CAAE,EAC3B,CAAE8B,UAAW,MAAO9B,KAAM,CAAE,EAC/B,CACDqH,YAAa,CAAC,CACN7G,KAAM,WACN8E,OAAQ,IACRI,8BAA+B,IAC/BM,QAAS,CAAElE,UAAW,GAAK,EAC3BmE,SAAU,CAAEnE,UAAW,GAAI,EAC3BuD,gBAAiB,CACb,CAAEzE,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,CAAE,EACpB,AACL,EAAG,CACCS,KAAM,WACNmE,eAAgB,KAChBW,OAAQ,GACRI,8BAA+B,IAC/BM,QAAS,CAAElE,UAAW,GAAK,EAC3BmE,SAAU,CAAEnE,UAAW,GAAI,EAC3BuD,gBAAiB,CACb,CAAEzE,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,IAAKb,IAAK,CAAE,EACpB,AACL,EAAG,CACCS,KAAM,WACNmE,eAAgB,QAChBW,OAAQ,GACRI,8BAA+B,IAC/BO,SAAU,CAAEnE,UAAW,GAAI,EAC3BuD,gBAAiB,CACb,CAAEzE,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,CAAE,EACpB,AACL,EAAG,CACCS,KAAM,OACNkE,eAAgB,GAChBY,OAAQ,EACRjC,aAAc,EACd+B,eAAgB,CACZ,CAAExE,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACvB,AACL,EAAG,CACCS,KAAM,OACNkE,eAAgB,EAChBY,OAAQ,GACR/B,aAAc,EACd6B,eAAgB,CACZ,CAAExE,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACvB,AACL,EAAE,AACV,EAEAgK,OAAQ,CACJvB,aAAc,GACdU,sBAAuB,CACnB,CAAEtI,EAAG,EAAGb,IAAK,GAAK,EAClB,CAAEa,EAAG,GAAIb,IAAK,GAAK,EACnB,CAAEa,EAAG,IAAKb,IAAK,CAAE,EACpB,CACD2I,GAAI,CACA,CAAE5G,UAAW,IAAKiF,EAAG,GAAK/G,KAAM,GAAI,EACpC,CAAE8B,UAAW,KAAM9B,KAAM,EAAG,EAC5B,CAAE8B,UAAW,KAAM9B,KAAM,GAAI,EAC7B,CAAE8B,UAAW,IAAM9B,KAAM,CAAE,EAC3B,CAAE8B,UAAW,KAAM9B,KAAM,EAAG,EAC5B,CAAE8B,UAAW,IAAM9B,KAAM,EAAG,EAC5B,CAAE8B,UAAW,MAAO9B,KAAM,CAAE,EAC/B,CACDqH,YAAa,CAAC,CACN7G,KAAM,WACN8E,OAAQ,GACRI,8BAA+B,GAC/BM,QAAS,CAAElE,UAAW,GAAIiE,iCAAkC,GAAI,EAChEE,SAAU,CAAEnE,UAAW,GAAI,CAC/B,EAAG,CACCtB,KAAM,aACN8E,OAAQ,IACRU,QAAS,CAAElE,UAAW,IAAMiF,EAAG,CAAE,EACjCd,SAAU,CAAEnE,UAAW,IAAMiF,EAAG,CAAE,EAClCxD,aAAc,EACd6B,eAAgB,CACZ,CAAExE,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,GAAIb,IAAK,CAAE,EAChB,CAAEa,EAAG,GAAIb,IAAK,GAAK,EACtB,AACL,EAAG,CACCS,KAAM,OACNmE,eAAgB,KAChBW,OAAQ,GACRjC,aAAc,CAClB,EAAE,AACV,EAEA2G,KAAM,CACFxB,aAAc,EACdnB,YAAa,CAAC,CACN7G,KAAM,OACNkF,8BAA+B,GACnC,EAAE,AACV,EAEAuE,UAAW,CACPzB,aAAc,EACdF,kBAAmB,IACnBjB,YAAa,CAAC,CACN7G,KAAM,OACNkF,8BAA+B,GACnC,EAAE,AACV,EAEAwE,SAAU,CACN1B,aAAc,GACdnB,YAAa,CAAC,CACN7G,KAAM,WACN8E,OAAQ,EACRI,8BAA+B,GACnC,EAAE,AACV,EAEAyE,SAAU,CACN3B,aAAc,IACdvB,eAAgB,GAChBI,YAAa,CAAC,CACN7G,KAAM,WACN8E,OAAQ,GACRI,8BAA+B,GACnC,EAAE,AACV,EAEA0E,OAAQ,CACJ5B,aAAc,GACdvB,eAAgB,GAChBI,YAAa,CAAC,CACN7G,KAAM,SACN8E,OAAQ,GACRI,8BAA+B,GACnC,EAAE,AACV,EAEA2E,KAAM,CACF7B,aAAc,EACdvB,eAAgB,IAChBsB,qBAAsB,CAAC,CAAE3H,EAAG,EAAGb,IAAK,CAAE,EAAG,CAAEa,EAAG,GAAIb,IAAK,CAAE,EAAE,CAC3DsH,YAAa,CAAC,CACN7G,KAAM,aACN8E,OAAQ,EACRU,QAAS,CAAElE,UAAW,GAAI,EAC1BmE,SAAU,CAAEnE,UAAW,GAAI,CAC/B,EAAE,AACV,EACAwI,OAAQ,CACJ9B,aAAc,GACdvB,eAAgB,IAChBsB,qBAAsB,CAAC,CAAE3H,EAAG,EAAGb,IAAK,CAAE,EAAG,CAAEa,EAAG,GAAIb,IAAK,CAAE,EAAE,CAC3DsH,YAAa,CAAC,CACN7G,KAAM,aACN8E,OAAQ,EACRU,QAAS,CAAElE,UAAW,IAAK,EAC3BmE,SAAU,CAAEnE,UAAW,GAAK,CAChC,EAAE,AACV,EACAyI,KAAM,CACF/B,aAAc,EACdvB,eAAgB,IAChBsB,qBAAsB,CAAC,CAAE3H,EAAG,EAAGb,IAAK,CAAE,EAAG,CAAEa,EAAG,GAAIb,IAAK,CAAE,EAAE,CAC3D2I,GAAI,CACA,CAAE5G,UAAW,IAAK9B,KAAM,EAAG,EAC3B,CAAE8B,UAAW,IAAK9B,KAAM,GAAI,EAC5B,CAAE8B,UAAW,IAAK9B,KAAM,CAAE,EAC1B,CAAE8B,UAAW,IAAMiF,EAAG,EAAG/G,KAAM,GAAI,EACnC,CAAE8B,UAAW,KAAM9B,KAAM,CAAE,EAC3B,CAAE8B,UAAW,KAAM9B,KAAM,GAAI,EAC7B,CAAE8B,UAAW,KAAM9B,KAAM,CAAE,EAC3B,CAAE8B,UAAW,MAAO9B,KAAM,GAAI,EACjC,CACDqH,YAAa,CAAC,CACN7G,KAAM,aACN8E,OAAQ,IACRU,QAAS,CAAElE,UAAW,GAAI,EAC1BmE,SAAU,CAAEnE,UAAW,IAAKiF,EAAG,CAAE,CACrC,EAAE,AACV,EACAyD,KAAM,CACFhC,aAAc,IACdD,qBAAsB,CAClB,CAAE3H,EAAG,EAAGb,IAAK,EAAI,EACjB,CAAEa,EAAG,GAAIb,IAAK,CAAE,EAChB,CAAEa,EAAG,GAAIb,IAAK,GAAK,EACnB,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,CAAE,EACpB,CACD2I,GAAI,CACA,CAAE5G,UAAW,GAAI9B,KAAM,CAAE,EACzB,CAAE8B,UAAW,IAAK9B,KAAM,GAAI,EAC5B,CAAE8B,UAAW,KAAM9B,KAAM,EAAG,EAC/B,CACDqH,YAAa,CAAC,CACN7G,KAAM,WACNkE,eAAgB,GAChBY,OAAQ,EACRU,QAAS,CAAElE,UAAW,GAAI,EAC1BsD,eAAgB,CACZ,CAAExE,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,GAAIb,IAAK,GAAK,EACtB,AACL,EAAG,CACCS,KAAM,aACN8E,OAAQ,GACRU,QAAS,CAAElE,UAAW,GAAI,EAC1BsD,eAAgB,CACZ,CAAExE,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,GAAIb,IAAK,CAAE,EACnB,AACL,EAAG,CACCS,KAAM,WACNmE,eAAgB,GAChBW,OAAQ,EACRU,QAAS,CAAElE,UAAW,GAAI,CAC9B,EAAE,AACV,EACA2I,UAAW,CACPjC,aAAc,GACdvB,eAAgB,IAChBsB,qBAAsB,CAClB,CAAE3H,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,GAAIb,IAAK,CAAE,EACnB,CACD2I,GAAI,CACA,CAAE5G,UAAW,IAAK9B,KAAM,EAAG,EAC3B,CAAE8B,UAAW,IAAK9B,KAAM,GAAI,EAC5B,CAAE8B,UAAW,KAAM9B,KAAM,CAAE,EAC3B,CAAE8B,UAAW,KAAM9B,KAAM,GAAI,EAC7B,CAAE8B,UAAW,IAAMiF,EAAG,EAAG/G,KAAM,GAAI,EACnC,CAAE8B,UAAW,KAAM9B,KAAM,GAAI,EAC7B,CAAE8B,UAAW,MAAO9B,KAAM,GAAI,EACjC,CACDqH,YAAa,CAAC,CACN7G,KAAM,WACN8E,OAAQ,GACRU,QAAS,CAAElE,UAAW,GAAK,CAC/B,EAAG,CACCtB,KAAM,aACN8E,OAAQ,GACRU,QAAS,CAAElE,UAAW,GAAM,EAC5BmE,SAAU,CAAEnE,UAAW,GAAK,EAC5BsD,eAAgB,CACZ,CAAExE,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,GAAIb,IAAK,CAAE,EACnB,AACL,EAAG,CACCS,KAAM,aACN8E,OAAQ,IACRU,QAAS,CAAElE,UAAW,IAAKiF,EAAG,CAAE,EAChCd,SAAU,CAAEnE,UAAW,GAAI,CAC/B,EAAE,AACV,EAEA4I,MAAO,CACHlC,aAAc,GACdvB,eAAgB,IAChBI,YAAa,CAAC,CACN7G,KAAM,YACV,EAAE,AACV,EAEAmK,cAAe,CACXnC,aAAc,GACdvB,eAAgB,IAChByB,GAAI,CACA,CAAE5G,UAAW,KAAM9B,KAAM,EAAG,EAC5B,CAAE8B,UAAW,KAAM9B,KAAM,EAAG,EAC/B,CACDqH,YAAa,CAAC,CACN7G,KAAM,aACNwF,QAAS,CACLlE,UAAW,EACXiE,iCAAkC,KAClCgB,EAAG,CACP,EACAd,SAAU,CACNnE,UAAW,EACXiE,iCAAkC,IAClCgB,EAAG,CACP,CACJ,EAAE,AACV,EAEA6D,KAAM,CACFpC,aAAc,IACdvB,eAAgB,IAChBqB,kBAAmB,IACnBY,sBAAuB,CACnB,CAAEtI,EAAG,EAAGb,IAAK,CAAE,EACf,CAAEa,EAAG,IAAKb,IAAK,GAAK,EACpB,CAAEa,EAAG,IAAKb,IAAK,CAAE,EACpB,CACDsH,YAAa,CAAC,CACN7G,KAAM,aACN8E,OAAQ,EACRU,QAAS,CACLlE,UAAW,IACXiE,iCAAkC,EAClCgB,EAAG,EACP,EACAd,SAAU,CACNnE,UAAW,IACXiE,iCAAkC,CACtC,CACJ,EAAG,CACCvF,KAAM,OACNmE,eAAgB,KAChBW,OAAQ,IACRjC,aAAc,CAClB,EAAE,AACV,CACJ,EAkCM,CAAEhE,QAASwL,CAA8B,CAAEC,OAAAA,CAAM,CAAE,CAAI3L,GAsB7D,OAAM4L,EACFzJ,YAAY6B,CAAY,CAAE+D,CAAU,CAAE1F,CAAO,CAAE,CAC3C,IAAI,CAAC2B,YAAY,CAAGA,EACpB,IAAI,CAAC6H,SAAS,CAAG,CAAC,EAClB,IAAI,CAACC,aAAa,CAAGzJ,EAAQyJ,aAAa,CAC1C,IAAI,CAACC,aAAa,CAAG,IAAIhJ,SAASiB,GAClC,IAAI,CAAC+H,aAAa,CAAC1I,OAAO,CAAC0E,GAC3B,IAAI,CAACiE,UAAU,CAAG,IAAIjJ,SAASiB,GAC/B,IAAI,CAACiI,2BAA2B,CAACN,EAAO,CACpCO,IAAK,CAAA,CACT,EAAG7J,EAAQ8J,YAAY,EAAI,CAAC,IAC5B,IAAI,CAACC,sBAAsB,CAAC,IAAI,CAACJ,UAAU,CAAE,IAAI,CAACD,aAAa,EAC/D,IAAI,CAACM,UAAU,CAAG,IA1kCoCrL,EA0kCRgD,EAAc,AAA8B,UAA9B,OAAO3B,EAAQgK,UAAU,CACjFC,AA/DyDtC,CA+D3B,CAAC3H,EAAQgK,UAAU,CAAC,CAAGhK,EAAQgK,UAAU,EAC3E,IAAI,CAACvE,cAAc,CAAG,IAAI,CAACuE,UAAU,CAACvE,cAAc,EAAI,EACxD,IAAI,CAACuE,UAAU,CAAC3D,aAAa,GAC7B,IAAI,CAAC2D,UAAU,CAAChJ,OAAO,CAAC,IAAI,CAAC2I,UAAU,CAC3C,CAMAO,gBAAgBpG,CAAM,CAAE,CACpB,IAAI,CAAC4F,aAAa,CAAClL,IAAI,CAACE,eAAe,CAACoF,EAAQ,EAAGyF,EAAuBpF,QAAQ,CACtF,CASAgG,oBAAoB7L,CAAI,CAAE8L,CAAM,CAAE,CAC9B,IAAMC,EAAef,EAAO,IAAI,CAACE,SAAS,CAAEY,GAASnM,EAAOoL,EAA+Be,EAAO9J,SAAS,EACvG8J,EAAO9J,SAAS,CAAG+I,EAA+Be,EAAOE,IAAI,EAC7Df,EAAuBgB,sBAAsB,CAACH,EAAOE,IAAI,EACzD,IACAjB,EAA+BpL,IAC/B,IAAI,CAAC+L,UAAU,CAACpD,cAAc,CAACtI,EAAML,EAAMoM,EAAaxD,YAAY,EAEpEwC,CAAAA,EAA+BgB,EAAaG,YAAY,GACxDnB,EAA+BgB,EAAaI,YAAY,CAAA,GACxD,IAAI,CAACC,gBAAgB,CAACpM,EAAM+L,EAAaG,YAAY,CAAEH,EAAaI,YAAY,EAEhFpB,EAA+BgB,EAAaR,GAAG,GAC/C,IAAI,CAACc,YAAY,CAACrM,EAAM+L,EAAaR,GAAG,EAExCR,EAA+BgB,EAAavG,MAAM,GAClD,IAAI,CAAC8G,eAAe,CAACtM,EAAM+L,EAAavG,MAAM,EAE9CuF,CAAAA,EAA+BgB,EAAaQ,WAAW,GACvDxB,EAA+BgB,EAAaS,gBAAgB,CAAA,GAC5D,IAAI,CAACC,eAAe,CAAC,UAAWzM,EAAM+L,EAAaQ,WAAW,CAAER,EAAaS,gBAAgB,EAE7FzB,CAAAA,EAA+BgB,EAAaW,YAAY,GACxD3B,EAA+BgB,EAAaY,iBAAiB,CAAA,GAC7D,IAAI,CAACF,eAAe,CAAC,WAAYzM,EAAM+L,EAAaW,YAAY,CAAEX,EAAaY,iBAAiB,CAExG,CAMAxE,cAAcnI,CAAI,CAAE,CAChB,IAAI,CAAC0L,UAAU,CAACvD,aAAa,CAACnI,EAClC,CAKA4M,QAAS,CACL,IAAI,CAAClB,UAAU,CAACrD,IAAI,GACpB,CACI,IAAI,CAAC6D,YAAY,EAAI,IAAI,CAACA,YAAY,CAAChM,IAAI,CAC3C,IAAI,CAAC2M,UAAU,EAAI,IAAI,CAACA,UAAU,CAAC7K,SAAS,CAC5C,IAAI,CAAC8B,WAAW,EAAI,IAAI,CAACA,WAAW,CAAC9B,SAAS,CAC9C,IAAI,CAAC8B,WAAW,EAAI,IAAI,CAACA,WAAW,CAACmD,CAAC,CACtC,IAAI,CAAClD,YAAY,EAAI,IAAI,CAACA,YAAY,CAAC/B,SAAS,CAChD,IAAI,CAAC+B,YAAY,EAAI,IAAI,CAACA,YAAY,CAACkD,CAAC,CACxC,IAAI,CAAC6F,OAAO,EAAI,IAAI,CAACA,OAAO,CAACvB,GAAG,CAChC,IAAI,CAACF,UAAU,CAACnL,IAAI,CACvB,CAACc,OAAO,CAAC,AAAC+L,GAAOA,GAAKA,EAAE5M,qBAAqB,CAAC,GACnD,CAKA6M,SAAU,CACN,IAAI,CAACJ,MAAM,GACX,IAAI,CAAClB,UAAU,CAACvI,IAAI,GAChB,IAAI,CAAC0J,UAAU,EACf,IAAI,CAACA,UAAU,CAAC1J,IAAI,GAExB,CACI,IAAI,CAAC+I,YAAY,CAAE,IAAI,CAACW,UAAU,CAAE,IAAI,CAAC/I,WAAW,CACpD,IAAI,CAACC,YAAY,CAAE,IAAI,CAAC+I,OAAO,CAAE,IAAI,CAACzB,UAAU,CAChD,IAAI,CAACD,aAAa,CACrB,CAACpK,OAAO,CAAE,AAAC/C,GAAMA,GAAKA,EAAEiK,UAAU,GACvC,CAKAmE,aAAarM,CAAI,CAAEuL,CAAG,CAAE,CAChB,IAAI,CAACuB,OAAO,EACZ,IAAI,CAACA,OAAO,CAACvB,GAAG,CAACnL,eAAe,CAACmL,EAAKvL,EAAO,IAAI,CAACqD,YAAY,CAACqC,WAAW,CAAEuF,EAAuBpF,QAAQ,CAEnH,CAKA4G,gBAAgBQ,CAAM,CAAEjN,CAAI,CAAEgC,CAAS,CAAEkL,CAAS,CAAE,CAChD,IAAMlE,EAAO,IAAI,CAACiE,EAAS,OAAO,CAAEE,EAAY,IAAI,CAAC9J,YAAY,CAACqC,WAAW,CAAG1F,EAC5EgJ,IACI+B,EAA+BmC,IAC/BlE,EAAK/B,CAAC,CAAC7G,eAAe,CAAC8M,EAAWC,EAAWlC,EAAuBpF,QAAQ,EAE5EkF,EAA+B/I,IAC/BgH,EAAKhH,SAAS,CAAC5B,eAAe,CAAC4B,EAAWmL,EAAWlC,EAAuBpF,QAAQ,EAGhG,CAKAyG,gBAAgBtM,CAAI,CAAEwF,CAAM,CAAE,CACtB,IAAI,CAAC6F,UAAU,EACf,IAAI,CAACA,UAAU,CAACnL,IAAI,CAACE,eAAe,CAACoF,EAAQxF,EAAO,IAAI,CAACqD,YAAY,CAACqC,WAAW,CAAEuF,EAAuBpF,QAAQ,CAE1H,CAKAuG,iBAAiBpM,CAAI,CAAEoN,CAAK,CAAEC,CAAK,CAAE,CACjC,IAAMF,EAAY,IAAI,CAAC9J,YAAY,CAACqC,WAAW,CAAG1F,CAC9C,CAAA,IAAI,CAACkM,YAAY,EAAInB,EAA+BqC,IACpD,IAAI,CAAClB,YAAY,CAAChM,IAAI,CAACE,eAAe,CAACgN,EAAOD,EAAWlC,EAAuBpF,QAAQ,EAExF,IAAI,CAACgH,UAAU,EAAI9B,EAA+BsC,IAClD,IAAI,CAACR,UAAU,CAAC7K,SAAS,CAAC5B,eAAe,CAAC,GAAKiN,EAAOF,EAAWlC,EAAuBpF,QAAQ,CAExG,CAKAyF,4BAA4BE,CAAY,CAAE,CACtC,IAAMpF,EAAM,IAAI,CAAC/C,YAAY,AACzBmI,CAAAA,EAAaD,GAAG,EAChB,CAAA,IAAI,CAACuB,OAAO,CAAG,IAAIQ,iBAAiBlH,EAAG,EAEvCoF,EAAa+B,OAAO,GACpB,IAAI,CAACV,UAAU,CAAG,IAAI/K,eAAesE,EAAK,CACtC1F,KAAM,OACNsB,UAAW,CACf,GACA,IAAI,CAACkK,YAAY,CAAG,IAAI9J,SAASgE,GACjC,IAAI,CAACyG,UAAU,CAACnK,OAAO,CAAC,IAAI,CAACwJ,YAAY,EACzC,IAAI,CAACA,YAAY,CAACxJ,OAAO,CAAC,IAAI,CAAC0I,aAAa,CAAClL,IAAI,EACjD,IAAI,CAAC2M,UAAU,CAAC3J,KAAK,IAErBsI,EAAagC,OAAO,GACpB,IAAI,CAAC1J,WAAW,CAAG,IAAIkD,iBAAiBZ,EAAK,CACzC1F,KAAM,UACNsB,UAAW,GACf,GACA,IAAI,CAAC+B,YAAY,CAAG,IAAIiD,iBAAiBZ,EAAK,CAC1C1F,KAAM,WACNsB,UAAW,CACf,GAER,CAMAyJ,uBAAuBgC,CAAK,CAAEC,CAAM,CAAE,CAClC,CACI,IAAI,CAACZ,OAAO,CACZ,IAAI,CAAChJ,WAAW,CAChB,IAAI,CAACC,YAAY,CACjB0J,EACH,CAACpJ,MAAM,CAAC,CAAClD,EAAMmD,IAASA,EACpBA,CAAAA,EAAI5B,OAAO,CAACvB,GAAOmD,CAAE,EACtBnD,EAAOuM,EACf,CAMA,OAAOC,uBAAuB3B,CAAI,CAAE,CAChC,IAAM4B,EAAQ5B,EAAK4B,KAAK,CAAC,0BAA2BC,EAAWD,EAAQA,CAAK,CAAC,EAAE,CAAG,IAAKE,EAAYD,CAAQ,CAAC,EAAE,CAACE,WAAW,GAAIC,EAAaH,CAAQ,CAAC,EAAE,CAEtJ,MAAO,AAAC,CAAA,CAAA,CACJI,EAAG,EAAG5P,EAAG,EAAG6P,EAAG,EAAGvJ,EAAG,EAAGwJ,EAAG,EAAG7P,EAAG,EAAG8P,EAAG,EAC3C,CAAA,CAAC,CAACN,EAAU,EAAI,CAAA,EAJwME,CAAAA,AAAe,MAAfA,EACpN,EAAIA,AAAe,MAAfA,EAAqB,GAAK,CAAA,EAGMK,AAAS,GAJgHT,CAAAA,EAAQU,SAASV,CAAK,CAAC,EAAE,CAAE,IAAM,CAAA,CAKtM,CAUA,OAAO3B,uBAAuBD,CAAI,CAAE,CAGhC,OAAO,QAAUpM,KAAK2O,GAAG,CAAC,EAAG3O,KAAKgC,GAAG,CAFjB,AAAgB,UAAhB,OAAOoK,EACvB,IAAI,CAAC2B,sBAAsB,CAAC3B,GAAQA,EACW,KAAO,GAC9D,CACJ,CACAf,EAAuBpF,QAAQ,CAAG2I,AA1xC4BnO,EA0xCJC,YAAY,CAAG,EA4HzE,GAAM,CAAEd,KAAMiP,CAAwB,CAAE,CAAIpP,IAyHTqP,EAxGnC,MACIlN,YAAYE,CAAO,CAAE,CACjB,IAAI,CAACA,OAAO,CAAGA,EACf,IAAI,CAACgH,YAAY,CAAG,EACpB,IAAI,CAACiG,SAAS,CAAGC,OAAOC,eAAe,CACQ,KAAA,IAApCA,gBAAgBC,eAAe,EACtCD,CAAAA,gBAAgBC,eAAe,CAAG,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC,IAAI,CAAA,EAE7D,IAAI,CAACD,QAAQ,GACb,IAAI,CAACE,SAAS,CAAG,EAAE,AACvB,CASAC,IAAIC,CAAO,CAAEzN,CAAO,CAAE,CAClB,GAAI,IAAI,CAACiN,SAAS,CAAE,CAChB,IAAI,CAACA,SAAS,CAAC/B,MAAM,GACrB,IAAMwC,EAAY,IAAIC,yBAAyBF,EAC3C,CAAA,IAAI,CAACG,KAAK,EACVF,CAAAA,EAAUE,KAAK,CAAG,IAAI,CAACA,KAAK,AAAD,EAE/BF,EAAUG,IAAI,CAAG7N,GAAWA,EAAQ6N,IAAI,EAAI,IAAI,CAAC7N,OAAO,CAAC6N,IAAI,EAAI,EACjEH,EAAUI,KAAK,CAAG9N,GAAWA,EAAQ8N,KAAK,EACtC,IAAI,CAAC9N,OAAO,CAAC8N,KAAK,EAAI,EAC1BJ,EAAU5J,MAAM,CAAGiJ,EAAyB/M,GAAWA,EAAQ8D,MAAM,CAAE,IAAI,CAAC9D,OAAO,CAAC8D,MAAM,CAAE,GAAK,IAAI,CAACkD,YAAY,CAClH,IAAI,CAACiG,SAAS,CAACc,KAAK,CAACL,EACzB,CACJ,CAWAM,UAAU1P,CAAI,CAAEmP,CAAO,CAAEzN,CAAO,CAAE,CAC9B,IAAI,CAACuN,SAAS,CAACU,IAAI,CAACC,WAAW,IAAI,CAACV,GAAG,CAACF,IAAI,CAAC,IAAI,CAAEG,EAASzN,GAAU1B,GAC1E,CAKA4M,QAAS,CACL,IAAI,CAACqC,SAAS,CAACjO,OAAO,CAAC6O,cACvB,IAAI,CAACZ,SAAS,CAAG,EAAE,CACnB,IAAI,CAACN,SAAS,CAAC/B,MAAM,EACzB,CAKAI,SAAU,CAIN,IAAI,CAACJ,MAAM,EACf,CAQAhB,gBAAgB3L,CAAG,CAAE,CACjB,IAAI,CAACyI,YAAY,CAAGzI,CACxB,CAKA8O,UAAW,CACP,GAAI,IAAI,CAACJ,SAAS,CAAE,CAChB,IACImB,EADEC,EAAO,IAAI,CAACrO,OAAO,CAACqO,IAAI,CAAEC,EAAO,IAAI,CAACtO,OAAO,CAACuO,QAAQ,EAAI,QAASC,EAAS,IAAI,CAACvB,SAAS,CAACwB,SAAS,GAAIC,EAAMF,EAAOrP,MAAM,CAEjI,IAAK,IAAI8F,EAAI,EAAGA,EAAIyJ,EAAK,EAAEzJ,EAAG,CAC1B,GAAIoJ,GAAQG,CAAM,CAACvJ,EAAE,CAACoJ,IAAI,GAAKA,EAAM,CACjC,IAAI,CAACT,KAAK,CAAGY,CAAM,CAACvJ,EAAE,CACtB,MACJ,CACA,GAAI,CAACmJ,GAAgBI,CAAM,CAACvJ,EAAE,CAACqJ,IAAI,GAAKA,IACpCF,EAAeI,CAAM,CAACvJ,EAAE,CACpB,CAACoJ,GACD,KAGZ,CACA,IAAI,CAACT,KAAK,CAAGQ,CACjB,CACJ,CACJ,EAkGmCO,EAvCnC,MACI7O,YAAYd,CAAI,CAAE4P,CAAM,CAAEC,EAAiB,CAAA,CAAK,CAAEC,CAAM,CAAEC,CAAK,CAAE,CAC7D,IAAI,CAAC/P,IAAI,CAAGA,EACZ,IAAI,CAAC4P,MAAM,CAAGA,EACd,IAAI,CAACC,cAAc,CAAGA,EACtB,IAAI,CAACE,KAAK,CAAGA,EACb,IAAI,CAACD,MAAM,CAAGA,GAAU,EAAE,AAC9B,CACAE,SAASC,CAAK,CAAE,CACZ,IAAMC,EAAY,IAAI,CAACJ,MAAM,CAAC,IAAI,CAACA,MAAM,CAAC3P,MAAM,CAAG,EAAE,CACrD,GAAI+P,GAAaD,EAAM3Q,IAAI,CAAG4Q,EAAU5Q,IAAI,CAAE,CAE1C,IAAI2G,EAAI,IAAI,CAAC6J,MAAM,CAAC3P,MAAM,CAC1B,KAAO8F,KAAO,IAAI,CAAC6J,MAAM,CAAC7J,EAAE,CAAC3G,IAAI,CAAG2Q,EAAM3Q,IAAI,GAC9C,IAAI,CAACwQ,MAAM,CAACK,MAAM,CAAClK,EAAI,EAAG,EAAGgK,EACjC,MAEI,IAAI,CAACH,MAAM,CAACb,IAAI,CAACgB,GAErB,OAAOA,CACX,CACAtI,MAAO,CACH,IAAI,CAACoI,KAAK,CAAG,CAAA,CACjB,CACAK,QAAS,CACL,IAAI,CAACL,KAAK,CAAG,CAAA,CACjB,CACA7D,QAAS,CACL,IAAI,CAAC0D,MAAM,CAAC1D,MAAM,EACtB,CACAI,SAAU,CACN,IAAI,CAACsD,MAAM,CAACtD,OAAO,EACvB,CACJ,EA6DM,CAAExN,KAAMuR,CAAS,CAAE,CAAI1R,IACvB2R,EAAa,AAACrM,GAAM/E,KAAKmD,KAAK,CAAC,GAAKnD,KAAKC,GAAG,CAAC8E,GAAK/E,KAAKqR,GAAG,CAAG,UAAW7C,EAAI,CAAC8C,EAAMjT,IAAMA,IAAM,EAAIiT,EAAO,IAAMC,EAAY,AAACC,GAAY,CAC3I,GAAM,GAAM,IAAM,IAClB,EAAG,EAAG,EAAG,EACT,EAAGA,CAAAA,CAAAA,EAAU,CAAA,EACbhD,EAAE,EAAGgD,GAAUhD,EAAE,EAAGgD,GAGpB,EAAG,IACN,CAAEC,EAAW,CAAC,EAAG,IAAM,GAAM,EAAM,EAAM,IAAM,GAAK,CACrDC,EAAY,AAACrT,IACT,IAAIsT,EAAMtT,AAAI,IAAJA,EACJuT,EAAM,EAAE,CACd,KAAOvT,IAAM,GACTsT,IAAQ,EACRA,GAAO,AAAK,IAAJtT,EAAY,IAExB,OAEI,GADAuT,EAAI7B,IAAI,CAAC4B,AAAM,IAANA,GACLA,AAAM,IAANA,EACAA,IAAQ,OAGR,MAGR,OAAOC,CACX,EAAGC,EAAe,AAACjB,IAEf,IADIkB,EAAWC,EACTH,EAAM,EAAE,CAAEI,EAAM,AAACC,IACnB,IAAI3Q,EAAKsQ,EAAI3Q,MAAM,CACnB,KAAOK,KAAQsQ,CAAG,CAACtQ,EAAG,CAAC4Q,MAAM,CAAGD,EAAGC,MAAM,GACzCN,EAAIX,MAAM,CAAC3P,EAAK,EAAG,EAAG2Q,EAC1B,EA8CA,OA7CArB,EAAOxP,OAAO,CAAC,AAACkN,IACZ,IAAMxP,EAAIwP,EAAE6D,sBAAsB,EAAI,CAAC,EAAGjR,EAAIoN,EAAElO,IAAI,CAAEgS,EAAML,EAAYZ,EAAUrS,EAAE6J,YAAY,CAAEoJ,GAAYM,EAAOD,GAAO9D,EAAElO,IAAI,CAAGgS,EAAKE,EAAO,CAAC,CAC1IC,OAAQ,AAAClU,GAAM,GAAK,GAAKA,EAAI,IAC7BwI,KAAM,CACF,GAAM/H,EAAE6M,GAAG,CACX,GAAM7M,EAAEwN,YAAY,CACpB,GAAMxN,EAAEyN,YAAY,AACxB,CACJ,EAAG,CACCgG,OAAQ,AAAClU,GAAM,IAAMA,EAAI,IAAQ,IACjCwI,KAAM,CACF,GAAM/H,EAAE6N,WAAW,CACnB,GAAM7N,EAAEgO,YAAY,AACxB,CACJ,EAAG,CACCyF,OAAQ,AAAClU,GAAM,GAAK2B,KAAKgC,GAAG,CAAC,GAAIhC,KAAK0B,GAAG,CAAC,IAAKrD,IAAM,GAAK,GAAK,IAC/DwI,KAAM,CACF,GAAM/H,EAAE8N,gBAAgB,CACxB,GAAM9N,EAAEiO,iBAAiB,AAC7B,CACJ,EAAE,CAAEhH,EAAI+L,EAAYhT,AAAa,KAAK,IAAlBA,EAAE8G,MAAM,CAC5BuL,EAAUW,EAAW,KAAO,IAAMhT,EAAE8G,MAAM,CAAG,IAAM7F,EAAOjB,EAAEsD,SAAS,CAAEgK,EAAOtN,EAAEsN,IAAI,EAAI,EAAGoG,EAAU,GAAMzS,CAAAA,EAAOqR,EAAWrR,GAC7H,AAAgB,UAAhB,OAAOqM,EAAoBqG,AA1bmCpH,EA2bzD0C,sBAAsB,CAAC3B,GAAQA,CAAG,EAAK,IAEhDkG,EAAKlR,OAAO,CAAC,AAACsR,GAAY3T,OAAO4T,IAAI,CAACD,EAAQ7L,IAAI,EAC7CzF,OAAO,CAAC,AAACwR,IACV,IAAMC,EAAMH,EAAQ7L,IAAI,CAAC+L,EAAW,AACxB,MAAK,IAAbC,GACAb,EAAI,CACAE,OAAQhR,EACRJ,KAAM,WACN+F,KAAM,CACF,IAAM6H,SAASkE,EAAY,IAC3BF,EAAQH,MAAM,CAACM,GAClB,AACL,EAER,IAEIR,IACAL,EAAI,CAAEE,OAAQhR,EAAGJ,KAAM,MAAO+F,KAAM,CAAC,IAAM2L,EAASzM,EAAE,AAAC,GACvDiM,EAAI,CAAEE,OAAQG,EAAMvR,KAAM,MAAO+F,KAAM,CAAC,IAAM2L,EAASzM,EAAE,AAAC,GAElE,GACO6L,CACX,EAAGkB,EAAgB,CAACvH,EAAehE,KAC/B,IAAMqJ,EAAS,EAAE,CAKjB,GAJIrJ,GAEAqJ,EAAOb,IAAI,CAAC,EAAG,IAAMxI,AAAiB,IAAjBA,GAErBgE,EAAe,CAEf,IAAMwH,EAAU,EAAE,CAClB,IAAK,IAAIhM,EAAI,EAAGA,EAAIwE,EAActK,MAAM,CAAE,EAAE8F,EAAG,CAC3C,IAAMiM,EAAOzH,EAAc0H,UAAU,CAAClM,GAClCiM,EAAO,KACPD,EAAQhD,IAAI,CAACiD,EAErB,CACA,OAAOpC,EAAOsC,MAAM,CAAC,CAAC,EAAG,IAAM,EAAK,CAAExB,EAAUqB,EAAQ9R,MAAM,EAAG8R,EACrE,CACA,OAAOnC,CACX,EAAGuC,EAAgB,CAACvC,EAAQwC,EAAa7H,EAAehE,KACpD,IAAI8L,EAAW,EACTC,EAAaR,EAAcvH,EAAehE,GAAiBgM,EAAc1B,EAAajB,GAAQnM,MAAM,CAAC,CAACoC,EAAMyH,KAC9G,IAAMpN,EAAIwQ,EAAUpD,EAAE4D,MAAM,CAAGmB,GAE/B,OADAA,EAAW/E,EAAE4D,MAAM,CACZrL,EAAKqM,MAAM,CAAChS,EAAGoN,EAAEzH,IAAI,CAChC,EAAG,EAAE,EACC2M,EAAW,CAAC,EAAG,IAAM,GAAM,EAAE,CAAEC,EAAO,AAACL,CAAAA,EAAc3B,EAASxQ,MAAM,CAAG,CAAA,EACzEqS,EAAWrS,MAAM,CACjBsS,EAAYtS,MAAM,CAAGuS,EAASvS,MAAM,CACxC,MAAO,CACH,GAAM,GAAM,IAAM,IAClBuN,EAAE,EAAGiF,GAAOjF,EAAE,EAAGiF,GACjBjF,EAAE,EAAGiF,GAAOjF,EAAE,EAAGiF,GACpB,CAACP,MAAM,CAACE,EAAc3B,EAAW,EAAE,CAAE6B,EAAYC,EAAaC,EAEnE,EAgBmCE,EARnC,SAAgBC,CAAQ,EACpB,IAAMC,EAAgBD,EAAStG,MAAM,CAAC,AAACgB,GAAM,CAAC,CAACA,EAAEuC,MAAM,CAAC3P,MAAM,EAAG4S,EAAQD,EAAc3S,MAAM,CAAE6S,EAAUD,EAAQ,EACjH,OAAO,IAAIE,WAAWxC,EAAUuC,EAAUD,EAAQ,EAAIA,GAAOX,MAAM,CAACY,EAAUX,EAAc,EAAE,CAAE,CAAA,GAAQ,EAAE,CAC1GS,EAAcnP,MAAM,CAAC,CAACuP,EAAQC,KAC1B,IAAMvD,EAASuD,EAAQvD,MAAM,CAC7B,OAAOsD,EAAOd,MAAM,CAACC,EAAcc,EAAQrD,MAAM,CAAE,CAACkD,EAASpD,EAAOnF,aAAa,CAAEmF,EAAOnJ,cAAc,EAC5G,EAAG,EAAE,GACT,EAsBM,CAAE2M,SAAAA,CAAQ,CAAEC,IAAAA,CAAG,CAAEA,IAAK,CAAEC,SAAUC,CAAG,CAAE,CAAE,CAAI5U,IAM7C6U,EAASH,EAAII,GAAG,EAAIJ,EAAIK,SAAS,EAAIL,EAe3C,SAASM,EAAcC,CAAO,EAC1B,IAAMC,EAAQD,EACTE,OAAO,CAAC,eAAgB,IACxB5G,KAAK,CAAC,yCACX,GAAI2G,GACAA,EAAM1T,MAAM,CAAG,GACdkT,EAAIU,IAAI,EACTV,EAAIW,WAAW,EACfX,EAAIJ,UAAU,EACdI,EAAIY,IAAI,EACPT,EAAOU,eAAe,CAAG,CAE1B,IAAMC,EAASd,EAAIU,IAAI,CAACF,CAAK,CAAC,EAAE,EAAGhD,EAAM,IAAIwC,EAAIW,WAAW,CAACG,EAAOhU,MAAM,EAAGiU,EAAS,IAAIf,EAAIJ,UAAU,CAACpC,GACzG,IAAK,IAAI5K,EAAI,EAAGA,EAAImO,EAAOjU,MAAM,CAAE,EAAE8F,EACjCmO,CAAM,CAACnO,EAAE,CAAGkO,EAAOhC,UAAU,CAAClM,GAElC,OAAOuN,EACFU,eAAe,CAAC,IAAIb,EAAIY,IAAI,CAAC,CAACG,EAAO,CAAE,CAAE,KAAQP,CAAK,CAAC,EAAE,AAAC,GACnE,CACJ,CAuFA,GAAM,CAAEQ,YAAaC,CAAgC,CAAE,CAtBnC,CAChBX,cAAAA,EACAU,YAvDJ,SAAqBT,CAAO,CAAEW,CAAQ,EAClC,IAAMC,EAAMnB,EAAIoB,SAAS,CAAE7W,EAAI2V,EAAImB,aAAa,CAAC,KAGjD,GAAI,AAAmB,UAAnB,OAAOd,GACP,CAAEA,CAAAA,aAAmBe,MAAK,GAC1BH,EAAII,gBAAgB,CAAE,CACtBJ,EAAII,gBAAgB,CAAChB,EAASW,GAC9B,MACJ,CAEA,GADAX,EAAU,GAAKA,EACXY,EAAIK,SAAS,CAAC1U,MAAM,CAAG,IACvB,MAAM,AAAI2U,MAAM,kBAEpB,IAEAC,EAAmB,YAAYC,IAAI,CAACR,EAAIK,SAAS,EAKjD,GAAII,CAAAA,AAHU7B,GACV,AAAmB,UAAnB,OAAOQ,GACPA,AAA4C,IAA5CA,EAAQsB,OAAO,CAAC,yBACFH,GAAoBnB,EAAQzT,MAAM,CAAG,GAAM,GAErD,CADJyT,CAAAA,EAAUD,EAAcC,IAAY,EAAC,EAEjC,MAAM,AAAIkB,MAAM,6BAIxB,GAAI,AAAsB,KAAA,IAAflX,EAAEuX,QAAQ,CACjBvX,EAAEwX,IAAI,CAAGxB,EACThW,EAAEuX,QAAQ,CAAGZ,EACbhB,EAAI8B,IAAI,CAACC,WAAW,CAAC1X,GACrBA,EAAE2X,KAAK,GACPhC,EAAI8B,IAAI,CAACG,WAAW,CAAC5X,QAIrB,GAAI,CACA,GAAI,CAACyV,EAAIoC,IAAI,CAAC7B,EAAS,SACnB,MAAM,AAAIkB,MAAM,wBAExB,CACA,KAAM,CAEFzB,EAAIqC,QAAQ,CAACN,IAAI,CAAGxB,CACxB,CAER,CASA,EAqBM,CAAE/U,QAAS8W,CAA4B,CAAEC,KAAAA,CAAI,CAAEC,MAAAA,CAAK,CAAE,CAAIlX,IAqb7BmX,EA7ZnC,MACIhV,YAAYE,CAAO,CAAE+U,CAAK,CAAE,CACxB,IAAI,CAACA,KAAK,CAAGA,EACb,IAAI,CAACC,QAAQ,CAAG,CAAA,EAChB,IAAI,CAACC,SAAS,CAAG,CAAA,EACjB,IAAI,CAACpD,QAAQ,CAAG,EAAE,CAClB,IAAI,CAACqD,kBAAkB,CAAG,EAAE,CAC5B,IAAI,CAACC,aAAa,CAAG,EACrB,IAAI,CAACC,cAAc,CAAG,EACtB,IAAI,CAACpV,OAAO,CAAGA,GAAW,CAAC,CAC/B,CAGAqV,WAAWrW,CAAI,CAAE4P,CAAM,CAAEC,EAAiB,CAAA,CAAK,CAAEC,CAAM,CAAE,CACrD,GAAI9P,AAAS,eAATA,GACA,CAAC4P,EAAOzE,mBAAmB,EAC3BnL,AAAS,WAATA,GACI,CAAC4P,EAAOZ,SAAS,CACrB,MAAM,AAAI8F,MAAM,oDAEpB,IAAM3B,EAAU,IAAIxD,EAA6B3P,EAAM4P,EAAQC,EAAgBC,GAE/E,OADA,IAAI,CAAC+C,QAAQ,CAAC5D,IAAI,CAACkE,GACZA,CACX,CAOAmD,KAAK/J,CAAM,CAAEgK,EAAiB,CAAA,CAAI,CAAEC,EAAa,CAAA,CAAI,CAAEC,CAAK,CAAE,CACtD,IAAI,CAACR,SAAS,CACd,IAAI,CAAC/J,MAAM,GAGX,IAAI,CAACwK,uBAAuB,GAEhC,IAAI,CAACC,aAAa,CAAGF,EACrB,IAAI,CAACN,aAAa,CAAGS,KAAKC,GAAG,GAC7B,IAAI,CAACT,cAAc,CAAG,EACtB,IAAI,CAACJ,QAAQ,CAAG,CAAA,EAChB,IAAI,CAACC,SAAS,CAAG,CAAA,EACjB,IAAMa,EAAgB,IAAI,CAAC9V,OAAO,CAAC8V,aAAa,EAAI,EAAGC,EAAS,IAAI,CAAC/V,OAAO,CAAC+V,MAAM,CAAEC,EAAc,IAAI,CAAChW,OAAO,CAACgW,WAAW,CAAEC,EAAgB,IAAI,CAACjW,OAAO,CAACiW,aAAa,CAAEpE,EAAWtG,EAChL2K,AA7DZ,SAAwB3K,CAAM,CAAEsG,CAAQ,EACpC,IAAMsE,EAAWtE,EAAS/L,GAAG,CAAC,AAACqM,IAC3BA,EAAQjH,MAAM,GACP,CACHiH,QAAAA,EACAiE,eAAgBjE,EAAQpD,KAAK,CACzB,EAAE,CAAGoD,EAAQrD,MAAM,CAACvD,MAAM,CAACA,EACnC,IACA8K,EAAUF,EAASxT,MAAM,CAAC,CAAC2T,EAAK1T,IAAQ1E,KAAKgC,GAAG,CAACoW,EAAK1T,EAAIwT,cAAc,CAACjX,MAAM,CAC/EyD,EAAIwT,cAAc,CAAC,EAAE,CAAC9X,IAAI,CAAGiY,KAAWA,KAC5C,OAAOJ,EAASrQ,GAAG,CAAC,AAACyG,GAAO,IAAIoC,EAA6BpC,EAAE4F,OAAO,CAACnT,IAAI,CAAEuN,EAAE4F,OAAO,CAACvD,MAAM,CAAErC,EAAE4F,OAAO,CAACtD,cAAc,CAAEtC,EAAE6J,cAAc,CAACtQ,GAAG,CAAC,AAAC0G,GAAMqI,EAAMrI,EAAG,CAAElO,KAAMkO,EAAElO,IAAI,CAAG+X,CAAQ,IAAK9J,EAAE4F,OAAO,CAACpD,KAAK,EAC/M,EAkD2BxD,EAAQ,IAAI,CAACiL,eAAe,EAAI,IAAI,CAAC3E,QAAQ,EAC5D,IAAI,CAACA,QAAQ,CAAE4E,EAAwB,AAACjK,GAAMvP,OAAO4T,IAAI,CAACrE,EAAEkK,aAAa,EAAI,CAAC,GAC7EtF,MAAM,CAACnU,OAAO4T,IAAI,CAACrE,EAAE6D,sBAAsB,EAAI,CAAC,IAChDsG,IAAI,GAAIC,EAAe,EAAE,CAC1BrB,GACA,CAAA,IAAI,CAACiB,eAAe,CAAG3E,CAAO,EAE9BkE,GACAA,EAAO,CAAEhB,MAAO,IAAI,CAACA,KAAK,CAAE8B,SAAU,IAAI,AAAC,GAE/C,IAAIC,EAAU,EACdjF,EAASvS,OAAO,CAAC,AAAC6S,IACd,GAAIA,EAAQpD,KAAK,CACb,OAEJ,IAAMgI,EAAY5E,EAAQrD,MAAM,CAAC3P,MAAM,CACnC6X,EAAmB,CAACT,IAAUU,EAAgB,CAACV,IAAUW,EAAgB,GAC7EJ,EAAU5Y,KAAK0B,GAAG,CAACuS,EAAQrD,MAAM,CAACiI,EAAY,EAAE,EAC5C5E,EAAQrD,MAAM,CAACiI,EAAY,EAAE,CAACzY,IAAI,EAAI,EAAGwY,GAC7C,IAAK,IAAI7R,EAAI,EAAGA,EAAI8R,EAAW,EAAE9R,EAAG,CAChC,IAAMuH,EAAI2F,EAAQrD,MAAM,CAAC7J,EAAE,CAAEkS,EAAUV,EAAsBjK,GAG7D,GAAI2K,IAAYD,GACZ1K,EAAElO,IAAI,CAAG2Y,EAAgBnB,EACzB,SAEJoB,EAAgBC,EAChBF,EAAgBzK,EAAElO,IAAI,CAClB6T,AAAiB,eAAjBA,EAAQnT,IAAI,CACZmT,EAAQvD,MAAM,CACTzE,mBAAmB,CAACqC,EAAElO,IAAI,CAAG,IAAMkO,EAAE6D,sBAAsB,EAAI,CAAC,GAGrE8B,EAAQvD,MAAM,CAACZ,SAAS,CAACxB,EAAElO,IAAI,CAAEkO,EAAEiB,OAAO,EAAI,GAAIjB,EAAEkK,aAAa,EAAI,CAAC,GAE1E,IAAMU,EAAQ5K,EAAE6K,YAAY,CAAEtC,EAAQqC,GAASA,EAAME,MAAM,EAAIF,EAAME,MAAM,CAACvC,KAAK,CAAEwC,EAAgB/K,EAAEgL,QAAQ,EACzGJ,GAAUpB,CAAAA,GAAeC,CAAY,GACjC9D,AAA2B,CAAA,IAA3BA,EAAQtD,cAAc,EACrBrC,CAAAA,EAAElO,IAAI,CAAG0Y,EAAmB,IAAM/R,IAAM8R,EAAY,CAAA,EACzDK,GACAR,EAAa3I,IAAI,CAACmJ,GAElBG,IACA,IAAI,CAACrC,kBAAkB,CAACjH,IAAI,CAACC,WAAW,KAIpC,GAHI1B,EAAEgL,QAAQ,EACVhL,EAAEgL,QAAQ,GAEVJ,EAAO,CACP,GAAInB,EAAe,CACf,IAAMwB,EAAIL,EAAME,MAAM,CAClBG,GAAKA,EAAEC,KAAK,EAAID,EAAEC,KAAK,CAACC,SAAS,EACjCF,EAAEC,KAAK,CAACE,aAAa,CAAC,KAAK,EAAGR,GAE9BK,GAAKA,EAAEI,KAAK,EAAIJ,EAAEI,KAAK,CAACF,SAAS,EACjCF,EAAEI,KAAK,CAACD,aAAa,CAAC,KAAK,EAAGR,EAEtC,CACIpB,GAAe,CAEnBjB,CAAAA,GAASA,EAAM+C,WAAW,EACtB/C,EAAM+C,WAAW,CAAC3Y,MAAM,CAAG,GAC3ByV,EAAKG,EAAM+C,WAAW,CAAE,AAACzM,GAAMA,IAAM+L,IAErCA,EAAMW,WAAW,AAAD,GAChBX,EAAMW,WAAW,EAEzB,CACJ,EAAGvL,EAAElO,IAAI,GACT0Y,EAAmBxK,EAAElO,IAAI,CAEjC,CACJ,GACA,IAAM0Z,EAAW,IAAI,CAAChY,OAAO,CAACyV,KAAK,CAAEwC,EAAS,IAAI,CAACjY,OAAO,CAACiY,MAAM,CACjE,IAAI,CAAC/C,kBAAkB,CAACjH,IAAI,CAACC,WAAW,KACpC,IAAM6G,EAAQ,IAAI,CAACA,KAAK,CAAEhV,EAAU,CAAEgV,MAAAA,EAAO8B,SAAU,IAAI,CAAED,aAAAA,CAAa,CAC1E,CAAA,IAAI,CAAC3B,SAAS,CAAG,CAAA,EACbO,GACA,IAAI,CAAC0C,cAAc,GAEnBD,GACAA,EAAOlY,GAEPiY,GACAA,EAASjY,GAET0V,GACAA,EAAM1V,GAENgV,IACIA,EAAMoD,OAAO,EACbpD,EAAMoD,OAAO,CAACC,IAAI,CAAC,GAEnBrD,EAAMsD,WAAW,EACjBtD,EAAMsD,WAAW,CAACC,UAAU,GAEhCvD,EAAMwD,IAAI,CAACjZ,OAAO,CAAC,AAAC1C,GAAMA,EAAE4b,aAAa,IAEjD,EAAG1B,EAAU,MACb,IAAI,CAAC1B,cAAc,CAAGG,EAAiBuB,EAAU,IAAI,CAAC2B,SAAS,EACnE,CAEAC,OAAQ,CAIJ,OAHA,IAAI,CAAC1D,QAAQ,CAAG,CAAA,EAChB,IAAI,CAAC9J,MAAM,GACX,IAAI,CAACkK,cAAc,CAAGQ,KAAKC,GAAG,GAAK,IAAI,CAACV,aAAa,CAAG,GACjD,IAAI,CAACC,cAAc,AAC9B,CAEAuD,gBAAiB,CACb,OAAO,IAAI,CAAC1D,SAAS,CACjBW,KAAKC,GAAG,GAAK,IAAI,CAACV,aAAa,CAC/B,IAAI,CAACC,cAAc,AAC3B,CAEAqD,WAAY,CACR,OAAO,IAAI,CAAC5G,QAAQ,CAAClP,MAAM,CAAC,CAACmU,EAAS3E,KAClC,IAAMjD,EAAYiD,EAAQrD,MAAM,CAACqD,EAAQrD,MAAM,CAAC3P,MAAM,CAAG,EAAE,CAC3D,OAAO+P,EAAYhR,KAAK0B,GAAG,CAACsP,EAAU5Q,IAAI,CAAEwY,GAAWA,CAC3D,EAAG,EACP,CAEA8B,QAAS,CACL,GAAI,IAAI,CAACpC,eAAe,CAAE,CACtB,IAAMqC,EAAa,IAAI,CAACzD,cAAc,CAAG,GACzC,IAAI,CAACE,IAAI,CAAC,AAAC9I,GAAMA,EAAElO,IAAI,CAAGua,EAAY,CAAA,EAAO,CAAA,EAAO,IAAI,CAAClD,aAAa,EACtE,IAAI,CAACR,aAAa,EAAI0D,CAC1B,MAEI,IAAI,CAACvD,IAAI,CAAC,KAAK,EAAG,CAAA,EAAO,CAAA,EAAO,IAAI,CAACK,aAAa,CAE1D,CAGAmD,iBAAiBC,CAAW,CAAEtD,CAAK,CAAE,CAC7B,IAAI,CAACR,SAAS,EACd,IAAI,CAACyD,KAAK,GAEd,IAAIM,EAAiB,EACrB,IAAI,CAAC1D,IAAI,CAAC,CAAC9I,EAAGhN,EAAIyZ,KAGd,IAAMnJ,EAAMiJ,EAAYvM,EAAGhN,EAAIyZ,GAI/B,OAHInJ,GAAOtD,EAAElO,IAAI,CAAG0a,GAChBA,CAAAA,EAAiBxM,EAAElO,IAAI,AAAD,EAEnBwR,CACX,EAAG,CAAA,EAAO,CAAA,EAAO2F,GACjB,IAAI,CAACe,eAAe,CAAG,IAAI,CAACA,eAAe,EAAI,IAAI,CAAC3E,QAAQ,CAC5D,IAAI,CAACmD,QAAQ,CAAG,CAAA,EAChB,IAAI,CAACC,SAAS,CAAG,CAAA,EACjB,IAAI,CAACG,cAAc,CAAG4D,CAC1B,CAEAE,aAAaC,CAAI,CAAE1D,CAAK,CAAE2D,CAAa,CAAEL,CAAW,CAAE,CAC9C,IAAI,CAAC9D,SAAS,EACd,IAAI,CAACyD,KAAK,GAEd,IAAMtX,EAAW,IAAI,CAACgU,cAAc,CAAEiE,EAAc,IAAI,CAACxH,QAAQ,CAAClP,MAAM,CAAC,CAACrE,EAAM6T,KAE5E,IAAMrD,EAASiK,EACX5G,EAAQrD,MAAM,CAACvD,MAAM,CAACwN,GAAe5G,EAAQrD,MAAM,CACnD2I,EAAI,EAAGjL,EAAIsC,EAAO3P,MAAM,CAAEma,EAAgBhb,EAC9C,KAAOmZ,EAAIjL,GAAG,CACV,IAAM+M,EAAM,AAAC9B,EAAIjL,GAAM,EAAGpN,EAAI0P,CAAM,CAACyK,EAAI,CAACjb,IAAI,CAAEkb,EAAMpa,EAAIgC,CACtDoY,CAAAA,EAAM,GACFL,GAAQ/Z,EAAIka,GACZA,CAAAA,EAAgBla,CAAAA,EAEpBoN,EAAI+M,GAECC,EAAM,GACP,CAACL,GAAQ/Z,EAAIka,GACbA,CAAAA,EAAgBla,CAAAA,EAEpBqY,EAAI8B,EAAM,GAGNJ,EACA1B,EAAI8B,EAAM,EAGV/M,EAAI+M,CAGhB,CACA,OAAOD,CACX,EAAGH,EAAO5C,IAAW,CAACA,KACtB,GAAI8C,IAAgB9C,KAAY8C,IAAgB,CAAC9C,IAAU,CACnD6C,GACAA,EAAc,CACVrE,MAAO,IAAI,CAACA,KAAK,CAAE8B,SAAU,IAAI,CAAE4C,cAAeN,CACtD,GAEJ,MACJ,CACA,IAAI,CAACL,gBAAgB,CAAC,CAACtM,EAAGhN,EAAIyZ,KAC1B,IAAMS,EAAaP,EACf3M,EAAElO,IAAI,CAAG8C,GAAYoL,EAAElO,IAAI,EAAI+a,EAXG,IAYlC7M,EAAElO,IAAI,CAAG8C,GAAYoL,EAAElO,IAAI,EAAI+a,EAZG,IAatC,OAAON,EAAcW,GAAcX,EAAYvM,EAAGhN,EAAIyZ,GAClDS,CACR,EAAGjE,EACP,CAIAkE,uBAAuBrc,CAAI,CAAEsc,CAAS,CAAEnE,CAAK,CAAE2D,CAAa,CAAEL,CAAW,CAAE,CACvE,IAAMxN,EAAS,CAACiB,EAAGhN,EAAIyZ,IAAQ,CAAC,CAAEF,CAAAA,EAC9BA,EAAYvM,EAAGhN,EAAIyZ,IAAQzM,EAAE6K,YAAY,CACzC7K,EAAE6K,YAAY,AAAD,EACbwC,EAAiBtD,IAAUuD,EAAe,KAC9C,AAAC,CAAA,IAAI,CAACtD,eAAe,EAAI,IAAI,CAAC3E,QAAQ,AAAD,EAAGvS,OAAO,CAAC,AAAC6S,IAC7C,IAAMrD,EAASqD,EAAQrD,MAAM,CACzB7J,EAAI6J,EAAO3P,MAAM,CACrB,KAAO8F,KAAK,CACR,GAAI,CAACsG,EAAOuD,CAAM,CAAC7J,EAAE,CAAEA,EAAG6J,GACtB,SAEJ,IAAMiC,EAAMjC,CAAM,CAAC7J,EAAE,CAACoS,YAAY,CAAC/Z,EAAK,CAAEyc,EAAOpF,EAA6B5D,IAAQ7S,KAAK8b,GAAG,CAACJ,EAAY7I,EAC9F,EAAA,IAATgJ,GAAkBA,EAAOF,IACzBA,EAAiBE,EACjBD,EAAehL,CAAM,CAAC7J,EAAE,CAEhC,CACJ,GACI6U,GACA,IAAI,CAACxE,IAAI,CAAC,AAAC9I,GAAM,CAAC,CAAEsN,CAAAA,GAChBtN,EAAElO,IAAI,CAAGwb,EAAaxb,IAAI,CAAG,GAC7BkO,EAAElO,IAAI,CAAGwb,EAAaxb,IAAI,CAAG,GAC7BkO,EAAE6K,YAAY,GAAKyC,EAAazC,YAAY,AAAD,EAAI,CAAA,EAAO,CAAA,EAAO5B,GACjE,IAAI,CAACe,eAAe,CAAG,IAAI,CAACA,eAAe,EAAI,IAAI,CAAC3E,QAAQ,CAC5D,IAAI,CAACmD,QAAQ,CAAG,CAAA,EAChB,IAAI,CAACC,SAAS,CAAG,CAAA,EACjB,IAAI,CAACG,cAAc,CAAG0E,EAAaxb,IAAI,EAElC8a,GACLA,EAAc,CAAErE,MAAO,IAAI,CAACA,KAAK,CAAE8B,SAAU,IAAI,AAAC,EAE1D,CAIAoD,kBAAkB7C,CAAK,CAAE,CACrB,OAAO,IAAI,CAACvF,QAAQ,CAAClP,MAAM,CAAC,CAACmM,EAAQqD,KACjC,IAAM+H,EAAc/H,EAAQrD,MAAM,CAC7BvD,MAAM,CAAC,AAACiB,GAAMA,EAAE6K,YAAY,GAAKD,GACtC,OAAOtI,EAAOsC,MAAM,CAAC8I,EACzB,EAAG,EAAE,CACT,CAIAC,YAAYC,CAAO,CAAE3E,CAAK,CAAE,CAExB,IAAM4E,EAAa,CACfC,MAAO/D,IACPgE,KAAM,CAAChE,GACX,EAOA,GANA,IAAI,CAAC1E,QAAQ,CAACvS,OAAO,CAAC,AAACiN,IACfA,EAAEuC,MAAM,CAAC3P,MAAM,GACfkb,EAAWC,KAAK,CAAGpc,KAAKgC,GAAG,CAACqM,EAAEuC,MAAM,CAAC,EAAE,CAACxQ,IAAI,CAAE+b,EAAWC,KAAK,EAC9DD,EAAWE,IAAI,CAAGrc,KAAK0B,GAAG,CAAC2M,EAAEuC,MAAM,CAACvC,EAAEuC,MAAM,CAAC3P,MAAM,CAAG,EAAE,CAACb,IAAI,CAAE+b,EAAWE,IAAI,EAEtF,GACIF,EAAWC,KAAK,CAAG/D,IAAU,CAC7B,IAAMiE,EAAc,AAACH,CAAAA,EAAWE,IAAI,CAAGF,EAAWC,KAAK,AAAD,EAZtC,IAYwDlZ,EAAWiZ,EAAWC,KAAK,CAAGF,EAAUI,EAAaC,EAASrZ,EAAWoZ,EAEjJ,GAAI,CAAC,IAAI,CAAC3I,QAAQ,CAAC6I,IAAI,CAAC,AAACnO,IACrB,IAAMuC,EAASvC,EAAEuC,MAAM,CACnB2I,EAAI,EAAGjL,EAAIsC,EAAO3P,MAAM,CAC5B,KAAOsY,EAAIjL,GAAG,CACV,IAAM+M,EAAM,AAAC9B,EAAIjL,GAAM,EAAGpN,EAAI0P,CAAM,CAACyK,EAAI,CAACjb,IAAI,CAC9C,GAAIc,EAAIgC,EACJqW,EAAI8B,EAAM,OAET,IAAIna,CAAAA,EAAIqb,CAAK,EAId,MAAO,CAAA,EAHPjO,EAAI+M,EAKZ,CACA,MAAO,CAAA,CACX,GACI,OAEJ,IAAI,CAACjE,IAAI,CAAC,AAAC9I,GAAMA,EAAElO,IAAI,EAAI8C,GAAYoL,EAAElO,IAAI,EAAImc,EAAQ,CAAA,EAAO,CAAA,EAAOhF,GACvE,IAAI,CAACe,eAAe,CAAG,IAAI,CAACA,eAAe,EAAI,IAAI,CAAC3E,QAAQ,CAC5D,IAAI,CAACmD,QAAQ,CAAG,CAAA,EAChB,IAAI,CAACC,SAAS,CAAG,CAAA,EACjB,IAAI,CAACG,cAAc,CAAGqF,CAC1B,CACJ,CAGAE,mBAAmBpP,CAAM,CAAE,CACvB,IAAMjF,EAAU,IAAI,CAACqS,cAAc,GAAI9G,EAAW,IAAI,CAAC2E,eAAe,EAAI,IAAI,CAAC3E,QAAQ,CACnF+I,EAAcrE,IAAUsE,EAAe,KAY3C,OAXAhJ,EAASvS,OAAO,CAAC,AAACiN,IACd,IAAMuC,EAASvC,EAAEuC,MAAM,CAACvD,MAAM,CAAC,CAACiB,EAAGhN,EAAIyZ,IAAQ,CAAC,CAAEzM,CAAAA,EAAE6K,YAAY,EAAI7K,EAAElO,IAAI,EAAIgI,GACzE,CAAA,CAACiF,GAAUA,EAAOiB,EAAGhN,EAAIyZ,EAAG,CAAC,GAAKa,EAAehL,CAAM,CAACA,EAAO3P,MAAM,CAAG,EAAE,CAC/E,GAAI2a,EAAc,CACd,IAAuCC,EAAO7b,KAAK8b,GAAG,CAACX,AAAnCS,EAAaxb,IAAI,CAAgCgI,GACjEyT,EAAOa,IACPA,EAAcb,EACdc,EAAef,EAAazC,YAAY,CAEhD,CACJ,GACOwD,CACX,CAEAC,OAAQ,CACA,IAAI,CAAC7F,SAAS,EACd,IAAI,CAAC/J,MAAM,GAEf,IAAI,CAACgN,cAAc,EACvB,CACAhN,QAAS,CACL,IAAM+M,EAAS,IAAI,CAACjY,OAAO,CAACiY,MAAM,CAC9BA,GACAA,EAAO,CAAElD,MAAO,IAAI,CAACA,KAAK,CAAE8B,SAAU,IAAI,AAAC,GAE/C,IAAI,CAAC5B,SAAS,CAAG,CAAA,EACjB,IAAI,CAACpD,QAAQ,CAACvS,OAAO,CAAC,AAACiN,GAAMA,EAAErB,MAAM,IACjC,IAAI,CAACsL,eAAe,EAAI,IAAI,CAACA,eAAe,GAAK,IAAI,CAAC3E,QAAQ,EAC9D,IAAI,CAAC2E,eAAe,CAAClX,OAAO,CAAC,AAACiN,GAAMA,EAAErB,MAAM,IAEhD,IAAI,CAACwK,uBAAuB,GAC5B,IAAI,CAACN,cAAc,CAAG,CAC1B,CACA9J,SAAU,CACN,IAAI,CAACJ,MAAM,GACP,IAAI,CAACsL,eAAe,EAAI,IAAI,CAACA,eAAe,GAAK,IAAI,CAAC3E,QAAQ,EAC9D,IAAI,CAAC2E,eAAe,CAAClX,OAAO,CAAC,AAACiN,GAAMA,EAAEjB,OAAO,IAEjD,IAAI,CAACuG,QAAQ,CAACvS,OAAO,CAAC,AAACiN,GAAMA,EAAEjB,OAAO,GAC1C,CACApB,gBAAgB3L,CAAG,CAAE,CACjB,IAAI,CAACsT,QAAQ,CAACvS,OAAO,CAAC,AAACiN,GAAMA,EAAEqC,MAAM,CAAC1E,eAAe,CAAC3L,GAC1D,CACAwc,aAAc,CACV,OAAOnJ,EAAK,IAAI,CAACC,QAAQ,CAACtG,MAAM,CAAC,AAACgB,GAAMA,AAAW,eAAXA,EAAEvN,IAAI,EAClD,CACAgc,aAAazH,CAAQ,CAAE,CACnB,IAAMxO,EAAO,IAAI,CAACgW,WAAW,GAAI1M,EAAO,AAACkF,CAAAA,GACrC,IAAI,CAACwB,KAAK,EACN,IAAI,CAACA,KAAK,CAAC/U,OAAO,CAACib,KAAK,EACxB,IAAI,CAAClG,KAAK,CAAC/U,OAAO,CAACib,KAAK,CAACC,IAAI,EACjC,OAAM,EAAK,OAAQC,EAAO,IAAIlI,KAAK,CAAClO,EAAK,CAAE,CAAE/F,KAAM,0BAA2B,GAAIoc,EAAMlO,OAAOuF,GAAG,CAACS,eAAe,CAACiI,GACvH7H,EAAiC8H,EAAK/M,GACtCnB,OAAOuF,GAAG,CAAC4I,eAAe,CAACD,EAC/B,CACAlD,gBAAiB,CACb,OAAO,IAAI,CAAC1B,eAAe,CAC3B,OAAO,IAAI,CAACb,aAAa,CACzB,IAAI,CAACR,aAAa,CAAG,IAAI,CAACC,cAAc,CAAG,EAC3C,IAAI,CAACJ,QAAQ,CAAG,CAAA,CACpB,CACAU,yBAA0B,CACtB,IAAI,CAACR,kBAAkB,CAAC5V,OAAO,CAAC6O,cAChC,IAAI,CAAC+G,kBAAkB,CAAG,EAAE,AAChC,CACJ,EA8BMoG,EAAgEjf,EAAwD,OAAU,CAACkf,UAAU,CACnJ,IAAIC,EAAqElf,EAAoBC,CAAC,CAAC+e,GAkB/F,GAAM,CAAE1d,MAAO6d,CAAuB,CAAE5d,QAAS6d,CAAyB,CAAEpS,OAAQqS,CAAwB,CAAEC,kBAAAA,CAAiB,CAAE/G,MAAOgH,CAAuB,CAAE/d,KAAMge,CAAsB,CAAE,CAAIne,IAE7L,CAAEoe,OAAAA,CAAM,CAAE,CAAIP,IACdQ,EAAmB,AAACC,GAAQ,AAAC,uBAAwBjI,IAAI,CAACiI,GAKhE,SAASC,EAAkB9E,CAAK,CAAE9Z,CAAI,EAClC,IAAI6e,EACJ,GAAI7e,EAAM,CAEN,GAAI,AAAe,UAAf,MADJ6e,CAAAA,EAAM/E,CAAK,CAAC9Z,EAAK,AAAD,EAEZ,OAAO6e,EAEXA,EAAMP,EAAkBte,EAAM8Z,EAClC,CACA,MAAO,AAAe,UAAf,OAAO+E,EAAmBA,EAAM,KAAK,CAChD,CA4IA,SAASC,EAAiBrb,CAAK,CAAEsb,CAAa,CAAEC,CAAmB,CAAEC,CAAM,CAAEC,CAAW,EAEpF,IAAMC,EAAeJ,EAAczc,GAAG,CAAGyc,EAAcnc,GAAG,CAC1D,GAAIuc,GAAgB,EAChB,OAAOH,EAAoBpc,GAAG,CAElC,IAAMwc,EAAiBJ,EAAoB1c,GAAG,CAAG0c,EAAoBpc,GAAG,CACpEyc,EAAoBD,EAD+D3b,CAAAA,EAAQsb,EAAcnc,GAAG,AAAD,EACzDuc,EACtD,GAAID,EAAa,CACb,IAAMre,EAAMke,EAAcnc,GAAG,CAAG,EAE5B,AAAC0c,GAAM1e,KAAKC,GAAG,CAACye,GAAK1e,KAAK2e,MAAM,CAEhC,AAACD,IACG,IAAIE,EAAc5e,KAAK8b,GAAG,CAAC4C,GACvBE,EAAc,IACdA,CAAAA,GAAe,AAAC,CAAA,GAAKA,CAAU,EAAK,EAAC,EAEzC,IAAMhN,EAAM5R,KAAKC,GAAG,CAAC2e,GAAe5e,KAAK6e,IAAI,CAC7C,OAAOH,EAAI,EAAI,CAAC9M,EAAMA,CAC1B,EACEkN,EAAY7e,EAAIke,EAAcnc,GAAG,EACvCyc,EAAoBD,EACfve,CAAAA,EAAI4C,GAASic,CAAQ,EACrB7e,CAAAA,EAAIke,EAAczc,GAAG,EAAIod,CAAQ,CAC1C,CAIA,OAAOvB,EAHKc,EACRD,EAAoB1c,GAAG,CAAG+c,EAC1BL,EAAoBpc,GAAG,CAAGyc,EACML,EAAoBpc,GAAG,CAAEoc,EAAoB1c,GAAG,CACxF,CA0FA,SAASqd,EAAuBld,CAAO,CAAEmd,CAAW,CAAEC,CAAiB,CAAEC,CAAmB,CAAEC,CAAQ,CAAEC,CAAQ,CAAEC,CAAgB,EAC9H,OAAOzB,EAAuB0B,AAtFlC,SAAkCzd,CAAO,CAAEmd,CAAW,CAAEC,CAAiB,CAAEM,CAAc,CAAEC,CAAc,CAAEH,CAAgB,EACvH,GAAI,AAA0B,UAA1B,OAAOG,EACP,OAAOA,EAEX,GAAI,AAA0B,YAA1B,OAAOA,EACP,OAAOA,EAAe/B,EAAyB,CAAErd,KAAM,CAAE,EAAGyB,IAEhE,IAAI4d,EAAQD,EAAgBE,EAAUH,EAAeI,WAAW,CAAE3d,EAAMud,EAAevd,GAAG,CAAEN,EAAM6d,EAAe7d,GAAG,CAAEke,EAASL,EAAeK,MAAM,CAAEC,EAStJ,GAR8B,UAA1B,OAAOL,IACPC,EAAQD,EAAeC,KAAK,CAC5BC,EAAUF,EAAeG,WAAW,EAAID,EACxC1d,EAAM4b,EAAuB4B,EAAexd,GAAG,CAAEA,GACjDN,EAAMkc,EAAuB4B,EAAe9d,GAAG,CAAEA,GACjDke,EAASJ,EAAeI,MAAM,EAAIL,EAAeK,MAAM,CACvDC,EAAQL,EAAeK,KAAK,EAE5B,CAACJ,EACD,OAAO,KAEX,IAAMK,EAAaL,AAAoB,MAApBA,EAAMM,MAAM,CAAC,GAC5BD,GACAL,CAAAA,EAAQA,EAAMO,KAAK,CAAC,EAAC,EAEzB,IAAInd,EAAQhB,EAAQgB,KAAK,CACnBod,EAAkBR,AAAU,UAAVA,GAAqB5c,AAAU,KAAK,IAAfA,GACzCwc,EACJ,GAAI,CAACY,EAAiB,CAClB,IAAMC,EAAaV,EAAe3c,KAAK,CACvC,GAAIqd,AAAe,KAAK,IAApBA,EACArd,EAAQqd,MAEP,CACD,GAAI,CAACre,EAAQqX,KAAK,CACd,OAAO,KAEXrW,EAAQhB,EAAQqX,KAAK,CAACuG,EAAM,AAChC,CACc,KAAK,IAAf5c,GACAA,CAAAA,EAAQ6a,EAAkB+B,EAAO5d,EAAQqX,KAAK,CAAA,CAEtD,CACA,GAAI,AAAiB,UAAjB,OAAOrW,GAAsBA,AAAU,OAAVA,EAC7B,OAAO,KAGX,IAAIsd,EAAW,KACf,GAAIte,EAAQqX,KAAK,EACb,GAAI0G,AAAW,UAAXA,GAAsBA,AAAW,UAAXA,EAAoB,CAC1C,IAAMQ,EAAOve,EAAQqX,KAAK,CAACE,MAAM,CAACwG,EAAO,CACrCQ,GAAQ5C,EAA0B4C,EAAKC,OAAO,GAAK7C,EAA0B4C,EAAKE,OAAO,GACzFH,CAAAA,EAAW,CACPne,IAAKoe,EAAKC,OAAO,CACjB3e,IAAK0e,EAAKE,OAAO,AACrB,CAAA,CAER,KACUV,CAAAA,AAAW,WAAXA,GAAuBX,CAAgB,GAC7Cpd,EAAQqX,KAAK,CAACE,MAAM,EACpB+G,CAAAA,EAAWnB,EAAYuB,cAAc,CAAC1e,EAAQqX,KAAK,CAACE,MAAM,CAACoH,KAAK,CAAC,CAACP,EAAkBZ,EAAmBI,EAAM,AAAD,EAMpH,GAHKU,GACDA,CAAAA,EAAWnB,EAAYyB,cAAc,CAACR,EAAkBZ,EAAmBI,EAAM,AAAD,EAEhFI,EAAO,CAEP,IAAMa,EAAY,EAAE,CAAEC,EAAY3gB,KAAK4gB,KAAK,CAAC5e,EAAM,IAAK6e,EAAY7gB,KAAK8gB,IAAI,CAACpf,EAAM,IAAM,EAAGqf,EAAWlB,EAAM5e,MAAM,CACpH,IAAK,IAAIwN,EAASkS,EAAWlS,EAASoS,EAAW,EAAEpS,EAC/C,IAAK,IAAIuS,EAAU,EAAGA,EAAUD,EAAU,EAAEC,EAAS,CACjD,IAAM5U,EAAO,GAAKqC,EAASoR,CAAK,CAACmB,EAAQ,CACrC5U,GAAQpK,GAAOoK,GAAQ1K,GACvBgf,EAAU3Q,IAAI,CAAC3D,EAEvB,CAGJ,IAAM6U,EAAU/C,EAAiBrb,EAAOsd,EAAU,CAAEne,IAAK,EAAGN,IAAKgf,EAAUzf,MAAM,CAAG,CAAE,EAAG6e,EAAYJ,AAAY,gBAAZA,GACrG,OAAOgB,CAAS,CAAC1gB,KAAKmD,KAAK,CAAC8d,GAAS,AACzC,CACA,OAAO/C,EAAiBrb,EAAOsd,EAAU,CAAEne,IAAAA,EAAKN,IAAAA,CAAI,EAAGoe,EAAYJ,AAAY,gBAAZA,EACvE,EAM2D7d,EAASmd,EAAaC,EAAmBxB,EAAyB,CACrHzb,IAAK,EAAGN,IAAK,EAAG+d,MAAO,IAAKE,YAAa,SAAUC,OAAQ,OAC/D,EAAIR,GAAY,CAAC,GAAKF,EAAqBG,GAAmBF,EAClE,CA6CA,SAAS+B,EAA4BvI,CAAQ,CAAElV,CAAY,CAAEsF,CAAe,CAAEjH,CAAO,EACjF,IAA4Bqf,EAAoBrf,EAAQsf,OAAO,EAAI,CAAC,EAAI1Q,EAAS5O,AAAiB,WAAjBA,EAAQhB,IAAI,CACzF,IAAIgO,EAAiC,CACjCuB,SAAUgR,AAFCvf,EAEUuO,QAAQ,CAC7BF,KAAMkR,AAHKvf,EAGMwf,cAAc,AACnC,GACA,IAv8CkEjW,EAu8C1B5H,EAAcsF,EAAiB,CACnE6C,aAAc,CACVD,IAAK,CAAC,CAACwV,EAAiBxV,GAAG,CAC3BgC,QAAS,CAAC,CAACwT,EAAiBxT,OAAO,CACnCC,QAAS,CAAC,CAAEuT,CAAAA,EAAiB5a,QAAQ,EACjC4a,EAAiB7a,OAAO,AAAD,CAC/B,EACAwF,WAAYhK,EAAQyf,UAAU,CAC9BhW,cAAezJ,EAAQ0f,QAAQ,AACnC,GACJ,OAAO7I,EAASxB,UAAU,CAACrV,EAAQhB,IAAI,EAAI,aAAc4P,EAAQkN,EAAuB9b,EAAQ6O,cAAc,CAAE,CAAA,GACpH,CAKA,SAAS8Q,GAAyB5f,CAAO,CAAEoS,CAAO,CAAEuL,CAAc,CAAER,CAAW,CAAE0C,CAAmB,CAAErC,CAAgB,EAClH,IAAMsC,EAAW,CAACC,EAAOzC,EAAUC,EAAUyC,IAAW9C,EAAuBld,EAASmd,EAAa,CAAA,EAAO,AAAC6C,CAAAA,GAAUrC,CAAa,CAAE,CAACoC,EAAM,CAAEzC,EAAUC,EAAUC,GAC7JyC,EAAc,EAAE,CAAEC,EAAY,CAChCpZ,aAAcgZ,EAAS,eAAgB,IAAK,CAAE3f,IAAK,GAAIN,IAAK,GAAK,GACjEiK,IAAKgW,EAAS,MAAO,EAAG,CAAE3f,IAAK,GAAIN,IAAK,CAAE,GAC1CkE,OAAQ+b,EAAS,SAAU,EAAG,CAAE3f,IAAK,GAAKN,IAAK,CAAE,EACrD,CACI8d,CAAAA,EAAepd,SAAS,EACxB2f,CAAAA,EAAU3f,SAAS,CAAGuf,EAAS,YAAa,IAAK,CAAE3f,IAAK,GAAIN,IAAK,GAAK,EAAC,EAEvE8d,EAAelZ,OAAO,GACtByb,EAAUpV,WAAW,CAAGgV,EAAS,YAAa,IAAO,CAAE3f,IAAK,EAAGN,IAAK,GAAM,EAAG8d,EAAelZ,OAAO,EACnGyb,EAAUnV,gBAAgB,CAAG+U,EAAS,YAAa,EAAG,CAAE3f,IAAK,GAAIN,IAAK,EAAG,EAAG8d,EAAelZ,OAAO,GAElGkZ,EAAejZ,QAAQ,GACvBwb,EAAUjV,YAAY,CAAG6U,EAAS,YAAa,IAAO,CAAE3f,IAAK,EAAGN,IAAK,GAAM,EAAG8d,EAAejZ,QAAQ,EACrGwb,EAAUhV,iBAAiB,CAAG4U,EAAS,YAAa,EAAG,CAAE3f,IAAK,GAAIN,IAAK,EAAG,EAAG8d,EAAejZ,QAAQ,GAEpGiZ,EAAe7R,OAAO,GACtBoU,EAAUzV,YAAY,CAAGqV,EAAS,QAAS,EAAG,CAAE3f,IAAK,EAAGN,IAAK,EAAI,EAAG8d,EAAe7R,OAAO,EAC1FoU,EAAUxV,YAAY,CAAGoV,EAAS,QAAS,EAAG,CAAE3f,IAAK,EAAGN,IAAK,EAAI,EAAG8d,EAAe7R,OAAO,GAE9F,IAAMqU,EAAkBL,EAAS,kBAAmB,IAAK,CAAE3f,IAAK,GAAIN,IAAK,GAAK,GAAIugB,EAAYN,EAAS,YAAa,EAAG,CAAEjgB,IAAK,GAAI,GAC5HwgB,EAAe,CAACC,EAAS7gB,EAAK,CAAC,IACjC,IAAIwD,EAAOqd,CACPA,CAAAA,EAAQ1C,KAAK,EAEc,UAAvB,OAAO0C,EAAQngB,GAAG,EAClB8C,CAAAA,EAAK9C,GAAG,CAAGyQ,AAn/C+CpH,EAo/CrD0C,sBAAsB,CAACoU,EAAQngB,GAAG,CAAA,EAEhB,UAAvB,OAAOmgB,EAAQzgB,GAAG,EAClBoD,CAAAA,EAAKpD,GAAG,CAAG+Q,AAv/C+CpH,EAw/CrD0C,sBAAsB,CAACoU,EAAQzgB,GAAG,CAAA,GAGnB,UAAnB,OAAOygB,GAAwBrE,EAAiBqE,IACrDrd,CAAAA,EAAO2N,AA5/CuDpH,EA4/CnB0C,sBAAsB,CAACoU,EAAO,EAE7EJ,EAAU3V,IAAI,CAAG2S,EAAuBld,EAASmd,EAAa,CAAA,EAAOla,EAAM,GAAI,CAAE9C,IAAK,EAAGN,IAAK,GAAI,EAAG2d,GACjG0C,EAAU3V,IAAI,CAAG,KACbsV,GACAK,CAAAA,EAAU3V,IAAI,CAAGpM,KAAKmD,KAAK,CAAC4e,EAAU3V,IAAI,CAAA,EAE9C0V,EAAY/R,IAAI,CAACkE,EAAQnD,QAAQ,CAAC,CAC9B1Q,KAAMyB,EAAQzB,IAAI,CAAG6hB,EAAYD,EAAkB1gB,EACnD6X,aAActX,EAAQqX,KAAK,CAC3B/G,uBAAwB7Q,AAAO,KAAK,IAAZA,EACpBmc,EAAyB,CAAC,EAAGsE,GAAaA,CAClD,IAER,EAeA,OAdIvC,EAAe5P,KAAK,EACpB4P,EAAe5P,KAAK,CAAChO,WAAW,GAAKwgB,MACrC5C,EAAe5P,KAAK,CAACxO,OAAO,CAAC8gB,GAExB1C,EAAe5P,KAAK,CACzBsS,EAAa1C,EAAe5P,KAAK,EAE5B4P,EAAepd,SAAS,EAC7B0f,EAAY/R,IAAI,CAACkE,EAAQnD,QAAQ,CAAC,CAC9B1Q,KAAMyB,EAAQzB,IAAI,CAAG6hB,EACrB9I,aAActX,EAAQqX,KAAK,CAC3B/G,uBAAwB4P,CAC5B,IAEGD,CACX,CAcA,SAASO,GAAqBxgB,CAAO,CAAEoS,CAAO,CAAEuL,CAAc,CAAER,CAAW,CAAEK,CAAgB,MATrDiD,EAUpC,IAAMX,EAAW,CAACC,EAAOzC,EAAUC,IAAaL,EAAuBld,EAASmd,EAAa,CAAA,EAAOQ,CAAc,CAACoC,EAAM,CAAEzC,EAAUC,EAAUC,GACzI4C,EAAYN,EAAS,YAAa,EAAG,CAAEjgB,IAAK,GAAI,GAAIkO,EAAQ+R,EAAS,QAAS,EAAG,CAAE3f,IAAK,GAAKN,IAAK,CAAE,GAAIiO,EAAOgS,EAAS,OAAQ,EAAG,CAAE3f,IAAK,GAAKN,IAAK,CAAE,GAAIkE,EAAS+b,EAAS,SAAU,EAAG,CAAE3f,IAAK,EAAI,GAAIuN,EAVvMsO,EAAO,AAAwB,YAAxB,OADsByE,EAWmN9C,EAAexC,IAAI,EATtQsF,EAS0OzgB,GAR1OygB,EAQ0OzgB,EARnNA,AAQmNA,EAR3MqX,KAAK,EAAIrX,AAQkMA,EAR1LqX,KAAK,CAACE,MAAM,CAACvC,KAAK,EAStE,GAAItH,EACA,OAAO0E,EAAQnD,QAAQ,CAAC,CACpB1Q,KAAMyB,EAAQzB,IAAI,CAAG6hB,EACrB9I,aAActX,EAAQqX,KAAK,CAC3BV,cAAe,CACX5I,MAAAA,EACAD,KAAAA,EACA/J,OAAAA,CACJ,EACA2J,QAAAA,CACJ,EAER,CAqEA,SAASgT,GAAS1gB,CAAO,CAAE2gB,CAAU,CAAEC,CAAa,EAChD,GAAI,AAAsB,YAAtB,OAAOD,EACP,OAAOA,EAAW3gB,GAEtB,GAAI,AAAsB,UAAtB,OAAO2gB,EAAyB,CAChC,IAAMpjB,EAAOojB,EAAWpjB,IAAI,CAAEyT,EAAM+K,EAAuB/b,EAAQgB,KAAK,CAAEhB,EAAQqX,KAAK,EAAI8E,EAAkBnc,EAAQqX,KAAK,CAAE9Z,IAC5H,GAAI,AAAe,UAAf,OAAOyT,EACP,MAAO,CAAA,EAEX,IAAI6P,EAAa,CAAA,EACXC,EAAaH,EAAWG,UAAU,CAAEC,EAAeJ,EAAWI,YAAY,CAAEC,EAAe,AAAyB,UAAzB,OAAOJ,EAEpGC,EADAC,GAAcC,EACDC,GAAiBJ,CAAAA,EAAgBE,GAAc9P,GAAO8P,GAC/DF,EAAgBG,GAAgB/P,GAAO+P,CAAW,EAGzC,AAACD,CAAAA,AAAe,KAAK,IAApBA,GACVE,GAAgBJ,EAAgBE,GAC5B9P,GAAO8P,CAAS,GAAOC,CAAAA,AAAiB,KAAK,IAAtBA,GAC3BC,GAAgBJ,EAAgBG,GAC5B/P,GAAO+P,CAAW,EAE9B,IAAMlhB,EAAMkc,EAAuB4E,EAAW9gB,GAAG,CAAE2W,KAAWrW,EAAM4b,EAAuB4E,EAAWxgB,GAAG,CAAE,CAACqW,KAC5G,OAAOxF,GAAOnR,GAAOmR,GAAO7Q,GAAO0gB,CACvC,CACA,MAAO,CAAA,CACX,CAoL6B,IAAMI,GA/KnC,SAA2Brf,CAAY,CAAEsF,CAAe,CAAE8N,CAAK,EAC3D,IAAM/U,EAAU+U,EAAM/U,OAAO,CAACihB,YAAY,EACtC,CAAC,EAAGC,EAAmBlhB,EAAQmhB,wBAAwB,CAAEC,EAAoBphB,EAAQqhB,oBAAoB,CAAEC,EAAwBzF,EAAwB,CAC3J0F,QAAS,CAAA,EACTC,cAAe,GACfC,UAAW,SACXnkB,KAAM,GACV,EAAG0C,EAAQ0hB,aAAa,EAAGC,EAAe3hB,EAAQ2hB,YAAY,EAAI,EAAE,CAAEC,EAAsB5hB,EAAQ4hB,mBAAmB,EAAI,EAAE,CAAEC,EAAe7hB,AAAkB,eAAlBA,EAAQ8hB,KAAK,CAE3JC,EAAgB7jB,KAAK0B,GAAG,CAAC,GAAII,EAAQgiB,QAAQ,CAAG,KAAMC,EAAkBjiB,EAAQiiB,eAAe,CAAEC,EAAeliB,EAAQ8O,MAAM,EAAI,CAAC,EAAGoO,EAAciF,AAnexJ,SAAwBpN,CAAK,EACzB,IAAMqN,EAAarN,EAAM/U,OAAO,CAACihB,YAAY,EACzC,CAAC,EAAGoB,EAAsB,AAACD,CAAAA,EAAWjB,wBAAwB,EAAI,CAAC,CAAA,EAClE7B,OAAO,EAAI,CAAEhhB,KAAM,IAAKwP,MAAO,GAAI,EAAGwU,EAAuBF,EAAWf,oBAAoB,EAC7Fe,EAAWf,oBAAoB,CAAC/B,OAAO,EAAI,CAAC,EAAGiD,EAAkB,EAAE,CAAEC,EAAkB,CAAC,EAAGC,EAAc,CAACnlB,EAAMolB,KAC5GA,AAAa,OAAbA,GACAH,CAAe,CAACG,EAAS,CACrBH,CAAe,CAACG,EAAS,EAAI,CAAC,EAClCH,CAAe,CAACG,EAAS,CAACplB,EAAK,CAAG,CAAA,GAGlCklB,CAAe,CAACllB,EAAK,CAAG,CAAA,CAEhC,EAAGqlB,EAAQ,CAAC,EAAGC,EAAiB,CAAC,EAAGC,EAA0B,CAAC/C,EAAO/O,EAAK2R,KACvE,IAAMI,EAAqB,AAACrL,GAAOA,AAAgB,MAAhBA,EAAEwG,MAAM,CAAC,GAAaxG,EAAEyG,KAAK,CAAC,GAAKzG,EACtE,GAAI,AAAe,UAAf,OAAO1G,GAAoB+O,AAAU,SAAVA,EAAkB,CAC7C,GAAIA,AAAU,UAAVA,GAAqB9D,EAAiBjL,GACtC,MAEU,CAAA,SAAV+O,IACA8C,CAAc,CAAC7R,EAAI,CAAG,CAAA,EACtB0R,EAAY1R,EAAK2R,IAErBC,CAAK,CAACG,EAAmB/R,GAAK,CAAG,CAAA,EACjC,MACJ,CAEA,GAAIgS,AADchS,GACDgS,AADChS,EACS4M,KAAK,EAC5B,AAA2B,UAA3B,OAAOoF,AAFOhS,EAEG4M,KAAK,CAAe,CACrC,IAAMA,EAAQmF,EAAmBC,AAHnBhS,EAG6B4M,KAAK,CAClC,CAAA,SAAVmC,GACA2C,EAAY9E,EAAO+E,GAEnB5C,CAAAA,AAAU,SAAVA,GAAoBiD,AAAqB,WAArBA,AAPVhS,EAOoB+M,MAAM,AAAY,GAChD8E,CAAAA,CAAc,CAACjF,EAAM,CAAG,CAAA,CAAG,EAE/BgF,CAAK,CAAChF,EAAM,CAAG,CAAA,EACf,MACJ,CACI,CAAC,UAAW,UAAW,WAAW,CAACzJ,OAAO,CAAC4L,GAAS,IACpD,AAAe,UAAf,OAAO/O,GACP9T,OAAO4T,IAAI,CAACE,GAAKzR,OAAO,CAAC,AAAC0jB,GAAaH,EAAwBG,EAAUjS,CAAG,CAACiS,EAAS,CAAEN,GAEhG,EAAGO,EAA6B,CAAC3D,EAASoD,KACtC,AAACzlB,OAAO4T,IAAI,CAACyO,GAAUhgB,OAAO,CAAC,AAACwgB,GAAU+C,EAAwB/C,EAAOR,CAAO,CAACQ,EAAM,CAAE4C,GAC7F,EAAGQ,EAA4B,AAACC,GAAWA,EAAO7jB,OAAO,CAAC,AAAC8jB,IACvDT,CAAK,CAACS,EAAMC,SAAS,EAAI,IAAI,CACzBT,CAAc,CAACQ,EAAMC,SAAS,EAAI,IAAI,CAAG,CAAA,CACjD,GACAJ,EAA2BZ,EAAqB,MAChDY,EAA2BX,EAAsB,MACjDY,EAA0Bd,EAAWR,mBAAmB,EAAI,EAAE,EAC9D,IAAM0B,EAAqBrmB,OAAO4T,IAAI,CAAC2R,GAAiBrjB,MAAM,CA0B9D,OAzBA4V,EAAMuC,MAAM,CAAChY,OAAO,CAAC,AAACgY,IAClB,IAAMiM,EAAQjM,EAAOtX,OAAO,CAACihB,YAAY,CACzC,GAAI3J,EAAOkM,OAAO,EAAI,CAAED,CAAAA,GAASA,AAAkB,CAAA,IAAlBA,EAAMhC,OAAO,AAAS,IAC/C+B,GACAf,CAAAA,CAAe,CAACjL,EAAOoH,KAAK,CAAC,CAAG7C,EAAwB2G,EAAe,EAEvEe,GAAO,CACP,IAAMlB,EAAsB,AAACkB,CAAAA,EAAMpC,wBAAwB,EAAI,CAAC,CAAA,EAAG7B,OAAO,CAAEgD,EAAuB,AAACiB,CAAAA,EAAMlC,oBAAoB,EAAI,CAAC,CAAA,EAAG/B,OAAO,CACzI+C,GACAY,EAA2BZ,EAAqB/K,EAAOoH,KAAK,EAE5D4D,GACAW,EAA2BX,EAAsBhL,EAAOoH,KAAK,EAEjEwE,EAA0BK,EAAME,aAAa,EAAI,EAAE,EACnD,AAACF,CAAAA,EAAMJ,MAAM,EAAI,EAAE,AAAD,EACb/R,MAAM,CAACmS,EAAME,aAAa,EAAI,EAAE,EAChCnkB,OAAO,CAAC,AAACokB,IACNA,EAAUpE,OAAO,EACjB2D,EAA2BS,EAAUpE,OAAO,CAAEhI,EAAOoH,KAAK,CAElE,EACJ,CAER,GACO,CACH6D,gBAAAA,EACA,GAAGoB,AA/HX,SAAkC5O,CAAK,CAAE4N,CAAK,CAAEC,CAAc,EAC1D,IAAMtL,EAASvC,EAAMuC,MAAM,CAAEsM,EAAWjB,EAAMxjB,MAAM,CAAE0kB,EAAiBjB,EAAezjB,MAAM,CAAE2kB,EAAY,AAACC,GAAaA,EAASphB,MAAM,CAAC,CAACqhB,EAAO1mB,KAC1I0mB,CAAK,CAAC1mB,EAAK,CAAG,CAAE4C,IAAKqW,IAAU3W,IAAK,CAAC2W,GAAS,EACzCyN,GACR,CAAC,GAAIC,EAAc,CAACD,EAAO5M,EAAO9Z,KACjC,IAAIyT,EAAMqG,CAAK,CAAC9Z,EAAK,AACT,MAAK,IAAbyT,GACAA,CAAAA,EAAM6K,EAAkBte,EAAM8Z,EAAK,EAEpB,UAAf,OAAOrG,IACPiT,CAAK,CAAC1mB,EAAK,CAAC4C,GAAG,CAAGhC,KAAKgC,GAAG,CAAC8jB,CAAK,CAAC1mB,EAAK,CAAC4C,GAAG,CAAE6Q,GAC5CiT,CAAK,CAAC1mB,EAAK,CAACsC,GAAG,CAAG1B,KAAK0B,GAAG,CAACokB,CAAK,CAAC1mB,EAAK,CAACsC,GAAG,CAAEmR,GAEpD,EAAG4N,EAAiBmF,EAAUnB,GAC1B1d,EAAIqS,EAAOnY,MAAM,CACf+kB,EAAoB,AAAI5D,MAAMrb,GACpC,KAAOA,KAAK,CACR,IAAMwZ,EAAiBqF,EAAUlB,GAC3B5f,EAAOsU,CAAM,CAACrS,EAAE,CAACjF,OAAO,CAC9B,GAAI,CAACsX,CAAM,CAACrS,EAAE,CAACue,OAAO,EAClBxgB,GAAQA,EAAKie,YAAY,EAAIje,AAA8B,CAAA,IAA9BA,EAAKie,YAAY,CAACM,OAAO,CACtD,SAEJ,IAAM4C,EAAS7M,CAAM,CAACrS,EAAE,CAACkf,MAAM,EAAI,EAAE,CACjCC,EAAID,EAAOhlB,MAAM,CACrB,KAAOilB,KAAK,CACR,IAAIC,EAAIT,EACR,KAAOS,KACHJ,EAAYtF,EAAgBwF,CAAM,CAACC,EAAE,CAAEzB,CAAK,CAAC0B,EAAE,EAGnD,IADAA,EAAIR,EACGQ,KACHJ,EAAYxF,EAAgB0F,CAAM,CAACC,EAAE,CAAExB,CAAc,CAACyB,EAAE,CAEhE,CACAH,CAAiB,CAACjf,EAAE,CAAGwZ,CAC3B,CACA,MAAO,CACHE,eAAAA,EACAF,eAAgByF,CACpB,CACJ,EAsFoCnP,EAAO9X,OAAO4T,IAAI,CAAC8R,GAAQ1lB,OAAO4T,IAAI,CAAC+R,GAAgB,AACvF,CACJ,EAiZuK7N,GAAQ8B,EAAW,IAAI/B,EAAkC,CACxNiB,OAAQmM,EAAanM,MAAM,CAC3BN,MAAOyM,EAAazM,KAAK,CACzBwC,OAAQiK,EAAajK,MAAM,CAC3BhC,cAAejW,EAAQiW,aAAa,CACpCD,YAAahW,EAAQgW,WAAW,AACpC,EAAGjB,EAECA,CAAAA,EAAMkM,YAAY,EAClBlM,CAAAA,EAAMkM,YAAY,CAAC/D,WAAW,CAAGA,CAAU,EAE/C,IAAIvd,EAAY,EAoJhB,OAnJAoV,EAAMuC,MAAM,CAAChY,OAAO,CAAC,CAACgY,EAAQoL,KAC1B,IAAM4B,EAAWhN,EAAOtX,OAAO,CAACihB,YAAY,EACxC,CAAC,EACL,GAAI3J,EAAOkM,OAAO,EAAIc,AAAqB,CAAA,IAArBA,EAAS/C,OAAO,CAAY,CAC9C,IAKIZ,EALE4D,EAAiB1C,EAAe2C,AAnRlD,SAAuClN,CAAM,CAAEyK,CAAa,CAAE7E,CAAW,CAAE+E,CAAe,EAEtF,IADIwC,EAAUF,EACRG,EAAoB3C,EACtB,AAACzK,CAAAA,EAAOvC,KAAK,CAACuC,MAAM,CAACnY,MAAM,CAAG,CAAA,EAAK8iB,EAUvC,GAV4E/E,EAAYqF,eAAe,CAACoC,KAAK,CAAC,AAACC,IAC3G,IAAMjC,EAAQ1lB,OAAO4T,IAAI,CAAC+T,SAC1B,CAAIjC,CAAAA,EAAMxjB,MAAM,CAAG,CAAA,IAGdslB,GACDA,CAAAA,EAAW9B,CAAK,CAAC,EAAE,AAAD,EAEf8B,IAAa9B,CAAK,CAAC,EAAE,CAChC,GACuB,CAEnB,IAAMlE,EAAiBvB,EAClBuB,cAAc,CAACnH,EAAOoH,KAAK,CAAC,CAAC+F,EAAS,CAG3CF,EAAiBrmB,KAAKmD,KAAK,CAACwjB,AAHiCpG,CAAAA,EAAe7e,GAAG,CAAG6e,EAAeve,GAAG,AAAD,EAAkBgd,EAAYuB,cAAc,CAAC9b,MAAM,CAAC,CAACmiB,EAAKrN,IAAOA,CAAC,CAACgN,EAAS,CAC3KK,EAAMrN,CAAC,CAACgN,EAAS,CAAC7kB,GAAG,CAAG6X,CAAC,CAACgN,EAAS,CAACvkB,GAAG,CACvC4kB,EAAM,GACiDJ,EAC/D,KACK,CAED,IAAMK,EAAczN,EAAOvC,KAAK,CAACuC,MAAM,CAAC3U,MAAM,CAAC,CAACmiB,EAAKrN,IAAMqN,EAAMrN,EAAE0M,MAAM,CAAChlB,MAAM,CAAE,GAClFolB,EAAiBrmB,KAAKmD,KAAK,CAAC,AAACiW,CAAAA,EAAO6M,MAAM,EAAI,EAAE,AAAD,EAAGhlB,MAAM,CAAG4lB,EAAcL,EAC7E,CACA,OAAOxmB,KAAK0B,GAAG,CAAC,GAAI2kB,EACxB,EAwPgFjN,EAAQyK,EAAe7E,EAAa+E,GAAmBF,EAAeiD,EAAyBnJ,EAAwBqF,EAAkBoD,EAASnD,wBAAwB,EAAG8D,EAA0BpJ,EAAwBuF,EAAmBkD,EAASjD,oBAAoB,EAAG6D,EAAuBrJ,EAAwByF,EAAuBgD,EAAS5C,aAAa,EAAGyD,EAAa,AAACb,CAAAA,EAASnB,MAAM,EAAI,CAAC6B,EAAuB,AAAD,EACxe5T,MAAM,CAACuQ,GAA4D8B,EAAgB2B,AAA1CvO,EAAShF,QAAQ,CAAC1S,MAAM,EAAoC,CAAC0iB,EACvGyC,EAASb,aAAa,EAAI,EAAE,CAC5B,AAACa,CAAAA,EAASb,aAAa,EAAI,EAAE,AAAD,EAAGrS,MAAM,CAACwQ,GAAsB5B,EAAc,EAAE,CAIhFmF,EAAW7lB,OAAO,CAAC,AAACokB,IAChB,IAAM2B,EAAaxJ,EAAwB,CACvC6F,cAAewD,EACfxF,SAAUgE,EAAUhE,QAAQ,EAAIpI,EAAOjJ,IAAI,AAC/C,EAAGqV,AAAmB,WAAnBA,EAAU1kB,IAAI,CACbimB,EAA0BD,EAAwBtB,GAAY4B,EAAiBD,EAAW3D,aAAa,CAAEhB,EAAa2E,EAAW3E,UAAU,CAAE6E,EAAsB,AAACnO,IAC1I,UAAtB,OAAOsJ,GACPA,EAAWpjB,IAAI,EACfqjB,CAAAA,EAAgBzE,EAAkB9E,EAAOsJ,EAAWpjB,IAAI,CAAA,CAEhE,EACM6U,EAAUiN,EAA4BvI,EAAUlV,EAAcsF,EAAiBoe,GAAanV,EAAM,AAAC3D,GAAMyT,EAAY/R,IAAI,IAE5HuX,AA5InB,SAAgCzlB,CAAO,CAAEoS,CAAO,CAAEsT,CAAY,CAAEvI,CAAW,EACvE,IAAI8C,EAAc,EAAE,CACpB,GAAIyF,AAAsB,WAAtBA,EAAazmB,IAAI,EAAiBymB,EAAanG,OAAO,CAAE,CACxD,IAAMoG,EAAanF,GAAqBxgB,EAASoS,EAASsT,EAAanG,OAAO,CAAEpC,GAC5EwI,GACA1F,CAAAA,EAAc,CAAC0F,EAAW,AAAD,CAEjC,MACSD,EAAanG,OAAO,EACzBU,CAAAA,EAAcL,GAAyB5f,EAASoS,EAASsT,EAAanG,OAAO,CAAEpC,EAAapB,EAAuB2J,EAC9G7F,mBAAmB,CAAE,CAAA,GAAK,EAEnC,OAAOI,CACX,EA+H0CzT,EAAG4F,EAASkT,EAAYnI,IAE9CyI,EAAa,EAAE,CAAEC,EAAiB,EAChCC,EAAuB,AAACC,IAC1B,GAAIH,AAAsB,IAAtBA,EAAWxmB,MAAM,CACjB+Q,EAAI,CACAkH,MAAOuO,CAAU,CAAC,EAAE,CAACvO,KAAK,CAC1B9Y,KAAMsnB,EAAiBE,EAAgB,CAC3C,OAEC,CACD,IAAM3B,EAAS4B,AArIvC,SAA0BT,CAAc,CAAEnB,CAAM,EAC5C,IAAM6B,EAAMV,EAAe7D,SAAS,EAAI,SAAUwE,EAAI,AAACzmB,GAAQ2kB,CAAM,CAAC3kB,EAAG,CAAG,CAAC2kB,CAAM,CAAC3kB,EAAG,CAAC4X,KAAK,CAAC,CAAG,EAAE,CACnG,GAAI4O,AAAQ,UAARA,EACA,OAAOC,EAAE,GAEb,GAAID,AAAQ,SAARA,EACA,OAAOC,EAAE9B,EAAOhlB,MAAM,CAAG,GAE7B,GAAI6mB,AAAQ,WAARA,EACA,OAAOC,EAAE9B,EAAOhlB,MAAM,EAAI,GAE9B,GAAI6mB,AAAQ,cAARA,EACA,OAAOC,EAAE,GAAG7U,MAAM,CAAC6U,EAAE9B,EAAOhlB,MAAM,CAAG,IAEzC,GAAI6mB,AAAQ,WAARA,EAAkB,CAClB,IACI9lB,EAAKN,EAAKsmB,EAAQC,EADhB7oB,EAAOgoB,EAAehoB,IAAI,EAAI,IAgBpC,GAdA6mB,EAAO7kB,OAAO,CAAC,AAAC+L,IACZ,IAAM0F,EAAMmL,EAAkB7Q,EAAE+L,KAAK,CAAE9Z,EAC3B,MAAK,IAAbyT,IAGA,CAAA,CAAC7Q,GAAO6Q,EAAMmV,CAAK,IACnBhmB,EAAMmL,EACN6a,EAASnV,GAET,CAAA,CAACnR,GAAOmR,EAAMoV,CAAK,IACnBvmB,EAAMyL,EACN8a,EAASpV,GAEjB,GACI7Q,GAAON,SACP,AAAIM,EAAIkX,KAAK,GAAKxX,EAAIwX,KAAK,CAChB,CAAClX,EAAIkX,KAAK,CAAC,CAEflX,EAAI5B,IAAI,CAAGsB,EAAItB,IAAI,CACtB,CAACsB,EAAIwX,KAAK,CAAElX,EAAIkX,KAAK,CAAC,CACtB,CAAClX,EAAIkX,KAAK,CAAExX,EAAIwX,KAAK,CAAC,AAElC,CACA,MAAO,EAAE,AACb,EA4FwDkO,EAAgBK,GAAavmB,EAAI0mB,EAAgB3B,EAAOhlB,MAAM,CAC9FglB,EAAO7kB,OAAO,CAAC,CAAC+L,EAAG7L,IAAO0Q,EAAI,CAC1BkH,MAAO/L,EACP/M,KAAMsnB,EAAiBxmB,EAAI,EAAIA,EAAII,CACvC,GACJ,CACAmmB,EAAa,EAAE,AACnB,EACA,AAACrO,CAAAA,EAAO6M,MAAM,EAAI,EAAE,AAAD,EAAG7kB,OAAO,CAAC,CAAC8X,EAAOgP,SAlUzBzmB,EAmUT,IAAM0mB,EAAcD,IAAY9O,EAAO6M,MAAM,CAAChlB,MAAM,CAAG,EACjDb,GApUGqB,EAoUwBA,EAlU1CrB,AADM2e,EAAuB,CAAE7F,MAmUIA,EAnUG9Y,KAAM,CAAE,EAmU2E4e,EAAa2E,EAAjEwD,EAAW/F,OAAO,EAAI+F,EAAW/F,OAAO,CAAChhB,IAAI,EAAI,EAnUjB,EAAG,CAAE4B,IAAK,EAAGN,IAmU7D2kB,EAnU4E5G,MAAO,GAAI,GACrIhe,GAmUQI,EAAU,CAAEqX,MAAAA,EAAO9Y,KAAAA,CAAK,EAE9B,GAAI,CAAC+mB,EAAW/F,OAAO,EACnB,CAACmB,GAAS1gB,EAAS2gB,EAAYC,GAAgB,CAC/C4E,EAAoBnO,GAEhBiP,GAAeV,EAAWxmB,MAAM,EAChC0mB,EAAqBF,CAAU,CAACA,EAAWxmB,MAAM,CAAG,EAAE,CAACb,IAAI,CACvDqnB,CAAU,CAAC,EAAE,CAACrnB,IAAI,EAE1B,MACJ,CAGA,GAFAinB,EAAoBnO,GAEfkO,EAAe/D,OAAO,CAGtB,CACD,IAAM+E,EAAKhoB,EAAOsnB,EAAgBW,EAAYjB,EAAe9D,aAAa,CAAEgF,EAAWH,GACnFC,GAAMC,EAAYD,EAAKC,CACvBF,CAAAA,GAAeC,EAAKC,GAChBD,GAAMC,GAENZ,EAAW1X,IAAI,CAAClO,GAEpB8lB,EAAqBW,GACrBZ,EAAiB1nB,KAAK4gB,KAAK,CAACxgB,EAAOioB,GAC/BA,EACAF,GAAeC,EAAKC,EACpBrW,EAAI,CACAkH,MAAOrX,EAAQqX,KAAK,CACpB9Y,KAAMsnB,EAAiBY,EAAW,CACtC,GAGAb,EAAa,CAAC5lB,EAAQ,EAI1B4lB,EAAW1X,IAAI,CAAClO,EAExB,MA1BImQ,EAAInQ,EA2BZ,EACJ,GAEA,IAAM0mB,EAAazG,EAAYrd,MAAM,CAAC,CAAC2X,EAAO9N,IAAOA,EAAElO,IAAI,CAAGgc,EAAMhc,IAAI,CAAGkO,EAAI8N,EAAQ,CAAEhc,KAAMiY,GAAS,GAClGrH,EAAY8Q,EAAYrd,MAAM,CAAC,CAAC4X,EAAM/N,IAAOA,EAAElO,IAAI,CAAGic,EAAKjc,IAAI,CAAGkO,EAAI+N,EAAO,CAAEjc,KAAM,CAACiY,GAAS,EACrGkQ,CAAAA,EAAWjP,QAAQ,CAAG0K,EAAawE,aAAa,CAC5CxE,EAAawE,aAAa,CAACpZ,IAAI,CAAC,KAAM,CAAEgK,OAAAA,EAAQT,SAAAA,CAAS,GACzD,KAAK,EACT3H,EAAUsI,QAAQ,CAAG0K,EAAayE,WAAW,CACzCzE,EAAayE,WAAW,CAACrZ,IAAI,CAAC,KAAM,CAAEgK,OAAAA,EAAQT,SAAAA,CAAS,GACvD,KAAK,EAET4M,EAAcnkB,OAAO,CAAC,AAACokB,IACnB,IAAM2B,EAAa3B,AAAmB,WAAnBA,EAAU1kB,IAAI,CAC7B6c,EAAwBuF,EAAmBsC,GAC3C7H,EAAwBqF,EAAkB,CACtC5B,QAAS,CAAExR,MAAO,CAAE6P,MAAO,OAAQ,CAAE,CACzC,EAAG+F,GACDkD,EAAiBxH,EAA4BvI,EAAUlV,EAAcsF,EAAiBoe,GAC5F1E,EAAgB,KAAK,EACrB,GAAM,CAAEkG,aAAAA,CAAY,CAAEC,cAAAA,CAAa,CAAE,CAAGzB,EAAYhC,EAAYgC,EAAWhC,SAAS,EAAI,IAAK3C,EAAa2E,EAAW3E,UAAU,CAAEqG,EAAkB7J,EAC9IuB,cAAc,CAACiE,EAAS,CAACW,EAAU,CAAE2D,EAAkB,CAAC1oB,EAAMyC,KAC/D,GAAI,CAACskB,EAAW/F,OAAO,EACnB,CAACmB,GAAS,CAAEniB,KAAAA,EAAMyC,MAAAA,CAAM,EAAG,AAAsB,UAAtB,OAAO2f,EAC9B/E,EAAyB,CAAEre,KAAM+lB,CAAU,EAAG3C,GAC9CA,EAAYC,GAAgB,CAChCA,EAAgB5f,EAChB,MACJ,CACA4f,EAAgB5f,EACZskB,AAAoB,WAApBA,EAAWrmB,IAAI,CACfuhB,GAAqB,CAAEjiB,KAAAA,EAAMyC,MAAAA,CAAM,EAAG6lB,EAAgBvB,EAAW/F,OAAO,CAAEpC,EAAamG,GAGvF1D,GAAyB,CAAErhB,KAAAA,EAAMyC,MAAAA,CAAM,EAAG6lB,EAAgBvB,EAAW/F,OAAO,CAAEpC,EAAapB,EAAuBuJ,EAAWzF,mBAAmB,CAAE,CAAA,GAAOyD,EAEjK,EACA,GAAIwD,EAAc,CACd,IAAIvoB,EAAO,EACX,KAAOA,GAAQimB,GAAgB,CAC3B,IAAMxT,EAAMqL,EAAiB9d,EAAM,CAAE4B,IAAK,EAAGN,IAAK2kB,CAAe,EAAGwC,GACpEC,EAAgB1oB,EAAOqB,EAAWoR,GAClCzS,GAAQuoB,CACZ,CACJ,CACA,GAAIC,EAAe,CACf,IAAI/V,EAAMgW,EAAgB7mB,GAAG,CAC7B,KAAO6Q,GAAOgW,EAAgBnnB,GAAG,EAE7BonB,EAAgB1oB,AADH8d,EAAiBrL,EAAKgW,EAAiB,CAAE7mB,IAAK,EAAGN,IAAK2kB,CAAe,EAAG,CAAA,EAAOc,AAAgC,gBAAhCA,EAAW4B,gBAAgB,EAChGtnB,EAAWoR,GAClCA,GAAO+V,CAEf,CACJ,GACIjF,GACAliB,CAAAA,GAAa4kB,EAAiBtC,CAAc,CAEpD,CACJ,GACOpL,CACX,EA2BM,CAAEqQ,eAAAA,EAAc,CAAEC,WAAAA,EAAU,CAAE,CAAIxpB,IAElC,CAAEqR,SAAAA,EAAQ,CAAE1F,OAAQ8d,EAAmB,CAAEC,UAAAA,EAAS,CAAExS,MAAOyS,EAAkB,CAAExpB,KAAMypB,EAAiB,CAAE,CAAI5pB,IAE5G,CAAE4U,IAAKiV,EAAgB,CAAEnV,IAAKoV,EAAgB,CAAE,CAAI9pB,GAwB1D,OAAM+pB,GACF5nB,YAAYiV,CAAK,CAAE,CACf,IAAI,CAACA,KAAK,CAAGA,EACb,IAAI,CAAC4S,mBAAmB,CAAG,EAC3B,IAAI,CAACC,UAAU,CAAG,EAClB,IAAI,CAACC,aAAa,CAAG7Y,GAASwY,GAAkB,UAAW,SAAUhb,CAAC,EAC9DuI,GAASA,EAAMkM,YAAY,EAC1BzU,CAAAA,AAAU,QAAVA,EAAEzP,GAAG,EAAcyP,AAAU,WAAVA,EAAEzP,GAAG,AAAY,GACrCgY,EAAMkM,YAAY,CAAC/V,MAAM,EAEjC,GACA,GAAI,CACA,IAAI,CAACvJ,YAAY,CAAG,IAAI8lB,GAAiBK,YAAY,CAErD,IAAI,CAACnmB,YAAY,CAAComB,OAAO,GACzB,IAAI,CAACC,gBAAgB,CAAG,IAAI,CAACrmB,YAAY,CAACV,WAAW,AACzD,CACA,MAAOuL,EAAG,CAAe,CAC7B,CAQAyb,oBAAoBD,CAAgB,CAAE,CAClC,IAAI,CAACA,gBAAgB,CAAGA,EACxB,IAAI,CAACE,MAAM,EACf,CAMAjT,WAAY,CACR,MAAO,CAAC,CAAC,IAAI,CAAC4B,QAAQ,EAAI,IAAI,CAACA,QAAQ,CAAC5B,SAAS,AACrD,CAYAkF,YAAYC,CAAO,CAAE3E,CAAK,CAAE,CACnB,IAAI,CAAC0S,KAAK,CAAC,IAAI,CAAChO,WAAW,CAAC7M,IAAI,CAAC,IAAI,CAAE8M,EAAS3E,KAGjD,IAAI,CAACoB,QAAQ,EACb,IAAI,CAACA,QAAQ,CAACsD,WAAW,CAACC,EAAS3E,EAE3C,CAcAyD,aAAaC,CAAI,CAAE1D,CAAK,CAAEsD,CAAW,CAAE,CACnC,GAAK,IAAI,CAACoP,KAAK,CAAC,IAAI,CAACjP,YAAY,CAAC5L,IAAI,CAAC,IAAI,CAAE6L,EAAM1D,EAAOsD,KAGtD,IAAI,CAAClC,QAAQ,CAAE,CACf,IAAM7T,EAAO,IAAI,CAAC+R,KAAK,CAAC/U,OAAO,CAACihB,YAAY,CAAEmH,EAAQplB,GAAQA,EAAK8L,MAAM,EAAI9L,EAAK8L,MAAM,CAACsK,aAAa,CACjGgP,GACD,IAAI,CAACC,sBAAsB,GAE/B,IAAI,CAACxR,QAAQ,CAACqC,YAAY,CAACC,EAAM1D,EAAO2S,GAAU,CAAA,KAC9C,IAAI,CAACE,kBAAkB,EAC3B,CAAA,EAAIvP,EACR,CACJ,CAiBAwP,mBAAmBpP,CAAI,CAAE7b,EAAO,GAAG,CAAEmY,CAAK,CAAE,CACxC,IAAM+S,EAAa,IAAI,CAAC7N,kBAAkB,GAC1C,GAAI6N,EAAY,CACZ,IAAMC,EAAiBD,EAAWlR,MAAM,CAACoH,KAAK,CAAIvF,CAAAA,EAAO,EAAI,EAAC,EAG9D,OAFA,IAAI,CAACuP,iBAAiB,CAACprB,EAAMkrB,CAAU,CAAClrB,EAAK,CAAE,AAACkP,GAAM,CAAC,CAACA,EAAE6K,YAAY,EAClE7K,EAAE6K,YAAY,CAACC,MAAM,CAACoH,KAAK,GAAK+J,EAAgBhT,GAC7C,IAAI,CAACV,KAAK,CAACuC,MAAM,CAACmR,EAAe,EAAI,IAChD,CACA,OAAO,IACX,CAYAC,kBAAkBprB,CAAI,CAAEqrB,CAAW,CAAEC,CAAY,CAAEnT,CAAK,CAAE,CACtD,GAAK,IAAI,CAAC0S,KAAK,CAAC,IAAI,CAACO,iBAAiB,CAACpb,IAAI,CAAC,IAAI,CAAEhQ,EAAMqrB,EAAaC,EAAcnT,KAG/E,IAAI,CAACoB,QAAQ,CAAE,CACf,IAAM7T,EAAO,IAAI,CAAC+R,KAAK,CAAC/U,OAAO,CAACihB,YAAY,CAAEmH,EAAQplB,GAAQA,EAAK8L,MAAM,EAAI9L,EAAK8L,MAAM,CAACsK,aAAa,CACjGgP,GACD,IAAI,CAACC,sBAAsB,GAE/B,IAAI,CAACxR,QAAQ,CAAC8C,sBAAsB,CAACrc,EAAMqrB,EAAalT,EAAO2S,GAAU,CAAA,IAAM,IAAI,CAACE,kBAAkB,EAAC,EAAIM,EAC/G,CACJ,CAUAjO,oBAAqB,QACjB,AAAI,IAAI,CAAC9D,QAAQ,CACN,IAAI,CAACA,QAAQ,CAAC8D,kBAAkB,GAEpC,IACX,CAgBAkO,SAASpJ,CAAU,CAAEzf,CAAO,CAAE8oB,EAAU,CAAC,CAAE,CACvC,GAAI,CAAC,IAAI,CAACX,KAAK,CAAC,IAAI,CAACU,QAAQ,CAACvb,IAAI,CAAC,IAAI,CAAEmS,EAAYzf,IACjD,OAEJ,IAAMgiB,EAAWhiB,EAAQ6G,YAAY,CAAG7G,EAAQ6G,YAAY,EAAI,IAC1DkiB,EAAQ,IAniEoDxf,EAmiEZ,IAAI,CAAC5H,YAAY,CAAE,IAAI,CAACqmB,gBAAgB,CAAE,CAC5Fhe,WAAYyV,EACZ3V,aAAc,CACVgC,QAAS,CAAA,EACTD,QAAS,CAAA,EACThC,IAAK,CAAA,CACT,CACJ,GACAkf,EAAM5e,mBAAmB,CAAC2e,EAAU,IAAM9oB,GAC1CkO,WAAW,IAAM6a,GAASA,EAAMzd,OAAO,GAAIwd,EAAU9G,EAAW,IACpE,CAcAjU,MAAMmN,CAAI,CAAE8N,CAAc,CAAEF,EAAU,CAAC,CAAE,CAMrCG,AALgB,IAAIjc,EAAiCsa,GAAmB,CACpE/Y,SAAU,QACVV,KAAM,IACN/J,OAAQ,EACZ,EAAGklB,GAAkB,CAAC,IACdhb,SAAS,CAAC8a,EAAS5N,EAC/B,CAKAhQ,QAAS,CACD,IAAI,CAAC2L,QAAQ,EACb,IAAI,CAACA,QAAQ,CAAC3L,MAAM,GAExBmc,GAAU,IAAI,CAAE,SACpB,CAKArM,cAAe,CACN,IAAI,CAACmN,KAAK,CAAC,IAAI,CAACnN,YAAY,CAAC1N,IAAI,CAAC,IAAI,IAGvC,IAAI,CAACuJ,QAAQ,GACb,IAAI,CAACA,QAAQ,CAACiE,KAAK,GACnB,IAAI,CAACjE,QAAQ,CAACmE,YAAY,GAElC,CAKAkO,YAAY1T,CAAU,CAAEC,CAAK,CAAE,CACtB,IAAI,CAAC0S,KAAK,CAAC,IAAI,CAACe,WAAW,CAAC5b,IAAI,CAAC,IAAI,CAAEkI,EAAYC,KAGpD,IAAI,CAACoB,QAAQ,GACb,IAAI,CAACA,QAAQ,CAACiE,KAAK,GACnB,IAAI,CAACqO,UAAU,GACf,IAAI,CAACtS,QAAQ,CAACvB,IAAI,CAAC,KAAK,EAAG,KAAK,EAAGE,EAAYC,GAEvD,CAKA2T,aAAa9R,CAAM,CAAE9B,CAAU,CAAEC,CAAK,CAAE,CAC/B,IAAI,CAAC0S,KAAK,CAAC,IAAI,CAACiB,YAAY,CAAC9b,IAAI,CAAC,IAAI,CAAEgK,EAAQ9B,EAAYC,KAG7D,IAAI,CAACoB,QAAQ,GACb,IAAI,CAACA,QAAQ,CAACiE,KAAK,GACnB,IAAI,CAACqO,UAAU,GACf,IAAI,CAACtS,QAAQ,CAACvB,IAAI,CAAC,AAAC9I,GAAM,CAAC,CAACA,EAAE6K,YAAY,EAAI7K,EAAE6K,YAAY,CAACC,MAAM,GAAKA,EAAQ,KAAK,EAAG9B,EAAYC,GAE5G,CAKA4T,YAAYjS,CAAK,CAAE3B,CAAK,CAAE,CACjB,IAAI,CAAC0S,KAAK,CAAC,IAAI,CAACkB,WAAW,CAAC/b,IAAI,CAAC,IAAI,CAAE8J,EAAO3B,KAG/C,IAAI,CAACoB,QAAQ,GACb,IAAI,CAACA,QAAQ,CAACiE,KAAK,GACnB,IAAI,CAACqO,UAAU,GACf,IAAI,CAACtS,QAAQ,CAACiC,gBAAgB,CAAC,AAACtM,GAAMA,EAAE6K,YAAY,GAAKD,EAAO3B,GAExE,CAMAvL,gBAAgB3L,CAAG,CAAE,CACb,IAAI,CAACsY,QAAQ,EACb,IAAI,CAACA,QAAQ,CAAC3M,eAAe,CAAC3L,EAEtC,CAKA+M,SAAU,CACN,IAAI,CAACuc,aAAa,GACd,IAAI,CAAChR,QAAQ,GACb,IAAI,CAACA,QAAQ,CAACvL,OAAO,GACrB,OAAO,IAAI,CAACuL,QAAQ,EAEpB,IAAI,CAACyS,kBAAkB,EACvB,IAAI,CAACA,kBAAkB,CAAC7nB,IAAI,GAE5B,IAAI,CAACE,YAAY,GAEjB,IAAI,CAACA,YAAY,CAAC4nB,KAAK,GACvB,OAAO,IAAI,CAAC5nB,YAAY,CAEhC,CAQAumB,QAAS,CACL,IAAM3E,EAAQ,IAAI,CAACxO,KAAK,CAAC/U,OAAO,EAAI,IAAI,CAAC+U,KAAK,CAAC/U,OAAO,CAACihB,YAAY,CACnE,GAAI,CAAC,IAAI,CAACkH,KAAK,CAAC,IAAI,CAACD,MAAM,CAAC5a,IAAI,CAAC,IAAI,IAAM,CAACiW,EACxC,OAGJ,IAAM1N,EAAMD,KAAKC,GAAG,GAAI2T,EAAiBjG,EAAMiG,cAAc,CAC7D,GAAI3T,EAAM,IAAI,CAAC+R,UAAU,CAAG4B,GAAkB,CAAC,IAAI,CAACC,UAAU,CAAE,CAC5Dtb,aAAa,IAAI,CAACub,eAAe,EACjC,IAAI,CAACA,eAAe,CAAGxb,WAAW,IAAI,CAACga,MAAM,CAAC5a,IAAI,CAAC,IAAI,EAAGkc,EAAiB,GAC3E,MACJ,CACA,IAAM1a,EAASyU,EAAMzU,MAAM,EAAI,CAAC,EAQhC,GAPIA,EAAO6a,YAAY,EACnB7a,EAAO6a,YAAY,CAAC,CAAE5U,MAAO,IAAI,CAACA,KAAK,CAAE8B,SAAU,IAAI,CAACA,QAAQ,AAAC,GAErE,IAAI,CAAC+Q,UAAU,CAAG/R,EACd,IAAI,CAACgB,QAAQ,EACb,IAAI,CAACA,QAAQ,CAACvL,OAAO,GAErB,IAAI,CAAC3J,YAAY,EAAI,IAAI,CAACqmB,gBAAgB,CAAE,CAC5C,IAAI,CAACnR,QAAQ,CAAGmK,GAAkB,IAAI,CAACrf,YAAY,CAAE,IAAI,CAACqmB,gBAAgB,CAAE,IAAI,CAACjT,KAAK,EACtF,IAAMwO,EAAQ,IAAI,CAACxO,KAAK,CAAC/U,OAAO,CAACihB,YAAY,CAC7C,IAAI,CAACpK,QAAQ,CAAC3M,eAAe,CAACqd,GAAkBhE,GAASA,EAAMvc,YAAY,CAAE,GACjF,CACI8H,EAAO8a,WAAW,EAClB9a,EAAO8a,WAAW,CAAC,CAAE7U,MAAO,IAAI,CAACA,KAAK,CAAE8B,SAAU,IAAI,CAACA,QAAQ,AAAC,EAExE,CAMAsR,MAAM0B,CAAS,CAAE,OACb,EAAK,IAAI,CAACloB,YAAY,IACjB,IAAI,CAACqmB,gBAAgB,IACrB,IAAI,CAACjT,KAAK,CAAC/U,OAAO,EACnB,CAAA,CAAA,IAAI,CAAC+U,KAAK,CAAC/U,OAAO,CAACihB,YAAY,EAC3B,AAA4C,CAAA,IAA5C,IAAI,CAAClM,KAAK,CAAC/U,OAAO,CAACihB,YAAY,CAACM,OAAO,AAAS,IAGpD,AAA4B,cAA5B,IAAI,CAAC5f,YAAY,CAACmoB,KAAK,EAAqB,IAAI,CAACL,UAAU,EAe/D,IAAI,CAAC9B,mBAAmB,CAAG,EACpB,CAAA,IAfC,IAAI,CAACA,mBAAmB,GAAK,IAC7BzZ,WAAW,KACH,IAAI,CAACvM,YAAY,EACjB,AAA4B,cAA5B,IAAI,CAACA,YAAY,CAACmoB,KAAK,CAEvB,IAAI,CAACnoB,YAAY,CAACiX,MAAM,GAAGmR,IAAI,CAACF,GAGhCA,GAER,EAAG,GAEA,CAAA,GAIf,CAKAV,YAAa,CACT,IAAMnmB,EAAO,IAAI,CAAC+R,KAAK,CAAC/U,OAAO,CAACihB,YAAY,CAAEkI,EAAanmB,GAAQA,EAAK8L,MAAM,EAAI9L,EAAK8L,MAAM,CAACqa,UAAU,CACpGA,GACAA,EAAW,CAAEpU,MAAO,IAAI,CAACA,KAAK,CAAE8B,SAAU,IAAI,CAACA,QAAQ,AAAC,EAEhE,CAKAwR,wBAAyB,CAChB,IAAI,CAACiB,kBAAkB,GACxB,IAAI,CAACA,kBAAkB,CAAG,IAphHwB3qB,EAohHI,IAAI,CAACgD,YAAY,CAAE2lB,GAAmBrd,AAxgFnCtC,EAwgFkEkB,IAAI,CAAE,CAAE7B,aAAc,EAAI,IACrJ,IAAI,CAACsiB,kBAAkB,CAACjjB,aAAa,GACrC,IAAI,CAACijB,kBAAkB,CAACtoB,OAAO,CAAC,IAAI,CAACgnB,gBAAgB,EAE7D,CAKAM,oBAAqB,CACb,IAAI,CAACgB,kBAAkB,GACvB,IAAI,CAACA,kBAAkB,CAAC1iB,cAAc,CAAC,GAAK,EAAG,KAC/C,IAAI,CAAC0iB,kBAAkB,CAAC1iB,cAAc,CAAC,GAAK,EAAG,KAEvD,CACJ,EACA,AAAC,SAAU8gB,CAAY,EACnB,IAAMsC,EAAkB,EAAE,CAK1B,SAASC,IACL,IAAMhJ,EAAe,IAAI,CAACA,YAAY,CAAEqD,EAAW,IAAI,CAACtkB,OAAO,EAAI,IAAI,CAACA,OAAO,CAACihB,YAAY,AACxFqD,CAAAA,GAAYA,EAAS/C,OAAO,CACxBN,EACAA,EAAaiH,MAAM,IAGnB,IAAI,CAACjH,YAAY,CAAG,IAAIyG,EAAa,IAAI,EACzC,IAAI,CAACzG,YAAY,CAACiH,MAAM,IAGvBjH,IACLA,EAAa3V,OAAO,GACpB,OAAO,IAAI,CAAC2V,YAAY,CAEhC,CAKA,SAASiJ,IACD,IAAI,EAAI,IAAI,CAACjJ,YAAY,EACzB,IAAI,CAACA,YAAY,CAAC3V,OAAO,EAEjC,CAKA,SAAS6e,IACD,IAAI,CAACF,yBAAyB,EAC9B,IAAI,CAACA,yBAAyB,EAEtC,CAKA,SAASG,EAAc5d,CAAC,EACpB,IAAM6d,EAAa7d,EAAExM,OAAO,CAACihB,YAAY,CACrCoJ,IACA/C,GAAmB,CAAA,EAAM,IAAI,CAACtnB,OAAO,CAACihB,YAAY,CAAEoJ,GACpDF,EAAc1sB,IAAI,CAAC,IAAI,EAE/B,CAsEAiqB,EAAa4C,OAAO,CAjEpB,SAAiBC,CAAU,CAAEC,CAAW,CAAEC,CAAU,EAEJ,KAAxCT,EAAgB9V,OAAO,CAACqW,KACxBP,EAAgB/b,IAAI,CAACsc,GACrBnD,GAAoBmD,EAAWhtB,SAAS,CAAE,CACtC0sB,0BAAAA,EACAS,OAAQ,SAAUjV,CAAK,EACf,IAAI,CAACwL,YAAY,EACjB,IAAI,CAACA,YAAY,CAACiI,WAAW,CAAC,CAAA,EAAOzT,EAE7C,EACAkV,aAAc,SAAU7P,EAAQ,CAAA,CAAI,CAAErF,CAAK,EACvC,GAAI,CAAC,IAAI,CAACwL,YAAY,CAClB,OAEJ,IAAMpK,EAAW,IAAI,CAACoK,YAAY,CAACpK,QAAQ,AACvC4Q,CAAAA,GAAiBta,eAAe,EAChCsa,GAAiBta,eAAe,CAACjC,MAAM,GAEvC2L,GAAY,IAAI,CAACoK,YAAY,CAAChM,SAAS,GACnC6F,EACA,IAAI,CAACmG,YAAY,CAAC/V,MAAM,GAGxB2L,EAAS6B,KAAK,GAGb7B,GAAYA,EAAS7B,QAAQ,CAClC6B,EAAS+B,MAAM,GAGf,IAAI,CAACqI,YAAY,CAACiI,WAAW,CAACpO,EAAOrF,EAE7C,CACJ,GACAzG,GAASub,EAAY,UAAWL,GAChClb,GAASub,EAAY,SAAUJ,GAC/Bnb,GAASub,EAAY,SAAUH,IAGU,KAAzCJ,EAAgB9V,OAAO,CAACsW,KACxBR,EAAgB/b,IAAI,CAACuc,GACrBA,EAAYjtB,SAAS,CAACmtB,MAAM,CAAG,SAAUjV,CAAK,EACtC,IAAI,CAACV,KAAK,CAACkM,YAAY,EACvB,IAAI,CAAClM,KAAK,CAACkM,YAAY,CAACmI,YAAY,CAAC,IAAI,CAAE,CAAA,EAAO3T,EAE1D,GAGwC,KAAxCuU,EAAgB9V,OAAO,CAACuW,KACxBT,EAAgB/b,IAAI,CAACwc,GACrBA,EAAWltB,SAAS,CAACmtB,MAAM,CAAG,SAAUjV,CAAK,EACrC,IAAI,CAAC6B,MAAM,CAACvC,KAAK,CAACkM,YAAY,EAC9B,IAAI,CAAC3J,MAAM,CAACvC,KAAK,CAACkM,YAAY,CAACoI,WAAW,CAAC,IAAI,CAAE5T,EAEzD,GAGJ,IAAMmV,EAAmBzD,KAAa0D,SAAS,CAC3CD,GACAA,EAAiBE,OAAO,EACxBF,EAAiBE,OAAO,CAACC,aAAa,CAACC,SAAS,EAChDJ,EAAiBE,OAAO,CAACC,aAAa,CAACC,SAAS,CAAC/c,IAAI,CAAC,YAAa,eAAgB,cAE3F,CAEJ,EAAGyZ,IAAiBA,CAAAA,GAAe,CAAC,CAAA,GAEpCJ,GAAmB,CAAA,EAAMJ,GAnrKT,CAmBZjG,aAAc,CAuLVM,QAAS,CAAA,EAITS,SAAU,IAUVC,gBAAiB,IAMjBuH,eAAgB,IAIhBxiB,aAAc,GAWd8a,MAAO,aAYP9L,YAAa,CAAA,EAabC,cAAe,CAAA,EASfyL,cAAe,CAIXH,QAAS,CAAA,EAKTC,cAAe,GAefC,UAAW,SASXnkB,KAAM,GACV,EAYA6jB,yBAA0B,CAQtBvB,oBAAqB,CAAA,EAwGrBH,WAAY,QA6BZH,QAAS,CA2GLhhB,KAAM,IAgFNuL,IAAK,IAkBLhD,aAAc,IAoCdiH,MAAO,CACH6P,MAAO,IACPzd,IAAK,KACLN,IAAK,KACLke,OAAQ,OACZ,EAwBAoC,gBAAiB,GACrB,CACJ,EAcAmB,qBAAsB,CA8BlB9S,SAAU,QAwBV+Q,QAAS,CAsELhhB,KAAM,IAMNuP,KAAM,IAMN/J,OAAQ,EACZ,EACA4d,cAAe,CACXD,UAAW,MACf,CACJ,CACJ,EACAoJ,UAAW,CACPI,oBAAqB,CACjBjQ,aAAc,CACVkQ,QAAS,eACTC,QAAS,WACD,IAAI,CAAClK,YAAY,EACjB,IAAI,CAACA,YAAY,CAACjG,YAAY,EAEtC,CACJ,EACAoQ,YAAa,CACTF,QAAS,cACTC,QAAS,WACL,IAAM1T,EAAI,IAAI,CAACwJ,YAAY,AACvBxJ,CAAAA,GAAKA,EAAExC,SAAS,GAChBwC,EAAEvM,MAAM,GAGR,IAAI,CAACwf,MAAM,EAEnB,CACJ,CACJ,CACJ,EAKApc,KAAM,CAMF0M,aAAc,gBAMdoQ,YAAa,eACjB,CACJ,GAwxI6B,IAAMC,GAA6B3D,GAiL1D4D,GAAK3tB,GAEX2tB,CAAAA,GAAErK,YAAY,CAAG,CACbtZ,kBA70FiEA,EA80FjE4jB,OAhFW,CACXC,MAAO,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAG,CAC7BC,OAAQ,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAG,CAC9BC,cAAe,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAG,CACrCC,SAAU,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAG,CAChCC,MAAO,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAG,CAC7BC,OAAQ,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAG,CAC9BC,WAAY,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAG,CAClCC,gBAAiB,CAAC,EAAG,EAAG,EAAG,EAAG,EAAE,CAChCC,gBAAiB,CAAC,EAAG,EAAG,EAAG,EAAG,GAAG,AACrC,EAuEIrtB,WA31H0DA,EA41H1D4K,uBA5jFsEA,EA6jFtE0iB,oBAAqBjf,EACrBkf,qBAAsBpX,EACtB4S,aAAc2D,EAClB,EACAA,GAA0Bf,OAAO,CAACgB,GAAEa,KAAK,CAAEb,GAAEc,MAAM,CAAEd,GAAEe,KAAK,EAC/B,IAAMC,GAAqB3uB,WAE/C2uB,MAAoBC,OAAO"}