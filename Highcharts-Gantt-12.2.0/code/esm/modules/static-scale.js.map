{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts Gantt JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/static-scale\n * @requires highcharts\n *\n * StaticScale\n *\n * (c) 2016-2025 <PERSON><PERSON>, Lars A<PERSON>\n *\n * License: www.highcharts.com/license\n */\nimport * as __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__ from \"../highcharts.js\";\n/******/ // The require scope\n/******/ var __webpack_require__ = {};\n/******/ \n/************************************************************************/\n/******/ /* webpack/runtime/compat get default export */\n/******/ (() => {\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = (module) => {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t() => (module['default']) :\n/******/ \t\t\t() => (module);\n/******/ \t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\treturn getter;\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/define property getters */\n/******/ (() => {\n/******/ \t// define getter functions for harmony exports\n/******/ \t__webpack_require__.d = (exports, definition) => {\n/******/ \t\tfor(var key in definition) {\n/******/ \t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t}\n/******/ \t\t}\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/hasOwnProperty shorthand */\n/******/ (() => {\n/******/ \t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ })();\n/******/ \n/************************************************************************/\n\n;// external [\"../highcharts.js\",\"default\"]\nconst external_highcharts_src_js_default_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"];\nvar external_highcharts_src_js_default_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_namespaceObject);\n;// ./code/es-modules/Extensions/StaticScale.js\n/* *\n *\n *  (c) 2016-2025 Torstein Honsi, Lars Cabrera\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { addEvent, defined, isNumber, pick } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Composition\n *\n * */\n/** @private */\nfunction compose(AxisClass, ChartClass) {\n    const chartProto = ChartClass.prototype;\n    if (!chartProto.adjustHeight) {\n        addEvent(AxisClass, 'afterSetOptions', onAxisAfterSetOptions);\n        chartProto.adjustHeight = chartAdjustHeight;\n        addEvent(ChartClass, 'render', chartProto.adjustHeight);\n    }\n}\n/** @private */\nfunction onAxisAfterSetOptions() {\n    const chartOptions = this.chart.options.chart;\n    if (!this.horiz &&\n        isNumber(this.options.staticScale) &&\n        (!chartOptions.height ||\n            (chartOptions.scrollablePlotArea &&\n                chartOptions.scrollablePlotArea.minHeight))) {\n        this.staticScale = this.options.staticScale;\n    }\n}\n/** @private */\nfunction chartAdjustHeight() {\n    const chart = this;\n    if (chart.redrawTrigger !== 'adjustHeight') {\n        for (const axis of (chart.axes || [])) {\n            const chart = axis.chart, animate = !!chart.initiatedScale &&\n                chart.options.animation, staticScale = axis.options.staticScale;\n            if (axis.staticScale && defined(axis.min)) {\n                let height = pick(axis.brokenAxis && axis.brokenAxis.unitLength, axis.max + axis.tickInterval - axis.min) * staticScale;\n                // Minimum height is 1 x staticScale.\n                height = Math.max(height, staticScale);\n                const diff = height - chart.plotHeight;\n                if (!chart.scrollablePixelsY && Math.abs(diff) >= 1) {\n                    chart.plotHeight = height;\n                    chart.redrawTrigger = 'adjustHeight';\n                    chart.setSize(void 0, chart.chartHeight + diff, animate);\n                }\n                // Make sure clip rects have the right height before initial\n                // animation.\n                axis.series.forEach(function (series) {\n                    const clipRect = series.sharedClipKey &&\n                        chart.sharedClips[series.sharedClipKey];\n                    if (clipRect) {\n                        clipRect.attr(chart.inverted ? {\n                            width: chart.plotHeight\n                        } : {\n                            height: chart.plotHeight\n                        });\n                    }\n                });\n            }\n        }\n        this.initiatedScale = true;\n    }\n    this.redrawTrigger = null;\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst StaticScale = {\n    compose\n};\n/* harmony default export */ const Extensions_StaticScale = (StaticScale);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * For vertical axes only. Setting the static scale ensures that each tick unit\n * is translated into a fixed pixel height. For example, setting the static\n * scale to 24 results in each Y axis category taking up 24 pixels, and the\n * height of the chart adjusts. Adding or removing items will make the chart\n * resize.\n *\n * @sample gantt/xrange-series/demo/\n *         X-range series with static scale\n *\n * @type      {number}\n * @default   50\n * @since     6.2.0\n * @product   gantt\n * @apioption yAxis.staticScale\n */\n''; // Keeps doclets above in JS file\n\n;// ./code/es-modules/masters/modules/static-scale.js\n\n\n\n\nconst G = (external_highcharts_src_js_default_default());\nExtensions_StaticScale.compose(G.Axis, G.Chart);\n/* harmony default export */ const static_scale_src = ((external_highcharts_src_js_default_default()));\n\nexport { static_scale_src as default };\n"], "names": ["__WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__", "__webpack_require__", "n", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "external_highcharts_src_js_default_namespaceObject", "external_highcharts_src_js_default_default", "addEvent", "defined", "isNumber", "pick", "onAxisAfterSetOptions", "chartOptions", "chart", "options", "horiz", "staticScale", "height", "scrollablePlotArea", "minHeight", "chartAdjustHeight", "redrawTrigger", "axis", "axes", "animate", "initiatedScale", "animation", "min", "broken<PERSON><PERSON>s", "unitLength", "max", "tickInterval", "diff", "Math", "plotHeight", "scrollablePixelsY", "abs", "setSize", "chartHeight", "series", "for<PERSON>ach", "clipRect", "sharedClipKey", "sharedClips", "attr", "inverted", "width", "G", "Extensions_StaticScale", "compose", "AxisClass", "ChartClass", "chartProto", "adjustHeight", "Axis", "Chart", "static_scale_src", "default"], "mappings": "AAWA,UAAYA,MAA6D,sBAAuB,CAEvF,IAAIC,EAAsB,CAAC,CAM1BA,CAAAA,EAAoBC,CAAC,CAAG,AAACC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,UAAU,CACvC,IAAOF,EAAO,OAAU,CACxB,IAAOA,EAER,OADAF,EAAoBK,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAH,EAAoBK,CAAC,CAAG,CAACE,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXR,EAAoBU,CAAC,CAACF,EAAYC,IAAQ,CAACT,EAAoBU,CAAC,CAACH,EAASE,IAC5EE,OAAOC,cAAc,CAACL,EAASE,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAT,EAAoBU,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAM5F,IAAMI,EAAqDrB,EAAwD,OAAU,CAC7H,IAAIsB,EAA0DrB,EAAoBC,CAAC,CAACmB,GAapF,GAAM,CAAEE,SAAAA,CAAQ,CAAEC,QAAAA,CAAO,CAAEC,SAAAA,CAAQ,CAAEC,KAAAA,CAAI,CAAE,CAAIJ,IAgB/C,SAASK,IACL,IAAMC,EAAe,IAAI,CAACC,KAAK,CAACC,OAAO,CAACD,KAAK,AACzC,EAAC,IAAI,CAACE,KAAK,EACXN,EAAS,IAAI,CAACK,OAAO,CAACE,WAAW,GAChC,CAAA,CAACJ,EAAaK,MAAM,EAChBL,EAAaM,kBAAkB,EAC5BN,EAAaM,kBAAkB,CAACC,SAAS,GACjD,CAAA,IAAI,CAACH,WAAW,CAAG,IAAI,CAACF,OAAO,CAACE,WAAW,AAAD,CAElD,CAEA,SAASI,IAEL,GAAIP,AAAwB,iBAAxBA,AADU,IAAI,CACRQ,aAAa,CAAqB,CACxC,IAAK,IAAMC,KAAST,AAFV,IAAI,CAEYU,IAAI,EAAI,EAAE,CAAG,CACnC,IAAMV,EAAQS,EAAKT,KAAK,CAAEW,EAAU,CAAC,CAACX,EAAMY,cAAc,EACtDZ,EAAMC,OAAO,CAACY,SAAS,CAAEV,EAAcM,EAAKR,OAAO,CAACE,WAAW,CACnE,GAAIM,EAAKN,WAAW,EAAIR,EAAQc,EAAKK,GAAG,EAAG,CACvC,IAAIV,EAASP,EAAKY,EAAKM,UAAU,EAAIN,EAAKM,UAAU,CAACC,UAAU,CAAEP,EAAKQ,GAAG,CAAGR,EAAKS,YAAY,CAAGT,EAAKK,GAAG,EAAIX,EAGtGgB,EAAOf,AADbA,CAAAA,EAASgB,KAAKH,GAAG,CAACb,EAAQD,EAAW,EACfH,EAAMqB,UAAU,AAClC,EAACrB,EAAMsB,iBAAiB,EAAIF,KAAKG,GAAG,CAACJ,IAAS,IAC9CnB,EAAMqB,UAAU,CAAGjB,EACnBJ,EAAMQ,aAAa,CAAG,eACtBR,EAAMwB,OAAO,CAAC,KAAK,EAAGxB,EAAMyB,WAAW,CAAGN,EAAMR,IAIpDF,EAAKiB,MAAM,CAACC,OAAO,CAAC,SAAUD,CAAM,EAChC,IAAME,EAAWF,EAAOG,aAAa,EACjC7B,EAAM8B,WAAW,CAACJ,EAAOG,aAAa,CAAC,CACvCD,GACAA,EAASG,IAAI,CAAC/B,EAAMgC,QAAQ,CAAG,CAC3BC,MAAOjC,EAAMqB,UAAU,AAC3B,EAAI,CACAjB,OAAQJ,EAAMqB,UAAU,AAC5B,EAER,EACJ,CACJ,CACA,IAAI,CAACT,cAAc,CAAG,CAAA,CAC1B,CACA,IAAI,CAACJ,aAAa,CAAG,IACzB,CAsCA,IAAM0B,EAAKzC,IACX0C,AAjCoB,CAAA,CAChBC,QA7DJ,SAAiBC,CAAS,CAAEC,CAAU,EAClC,IAAMC,EAAaD,EAAWjD,SAAS,AAClCkD,CAAAA,EAAWC,YAAY,GACxB9C,EAAS2C,EAAW,kBAAmBvC,GACvCyC,EAAWC,YAAY,CAAGjC,EAC1Bb,EAAS4C,EAAY,SAAUC,EAAWC,YAAY,EAE9D,CAuDA,CAAA,EA+BuBJ,OAAO,CAACF,EAAEO,IAAI,CAAEP,EAAEQ,KAAK,EACjB,IAAMC,EAAqBlD,WAE/CkD,KAAoBC,OAAO"}