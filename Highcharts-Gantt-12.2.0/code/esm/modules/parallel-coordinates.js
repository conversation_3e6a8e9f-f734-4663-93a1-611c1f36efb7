import*as t from"../highcharts.js";var e,i,s,a={};a.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return a.d(e,{a:e}),e},a.d=(t,e)=>{for(var i in e)a.o(e,i)&&!a.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},a.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);let l=t.default;var o=a.n(l);let r={chart:{parallelCoordinates:!1,parallelAxes:{lineWidth:1,title:{text:"",reserveSpace:!1},labels:{x:0,y:4,align:"center",reserveSpace:!1},offset:0}},xAxis:{lineWidth:0,tickLength:0,opposite:!0,type:"category"}},{addEvent:n,arrayMax:h,arrayMin:p,isNumber:c,merge:d,pick:f}=o();class u{constructor(t){this.axis=t}setPosition(t,e){let i=this.axis,s=i.chart,a=((this.position||0)+.5)/(s.parallelInfo.counter+1);s.polar?e.angle=360*a:(e[t[0]]=100*a+"%",i[t[1]]=e[t[1]]=0,i[t[2]]=e[t[2]]=null,i[t[3]]=e[t[3]]=null)}}!function(t){function e(t){let e=this.chart,i=this.parallelCoordinates,s=["left","width","height","top"];if(e.hasParallelCoordinates){if(e.inverted&&(s=s.reverse()),this.isXAxis)this.options=d(this.options,r.xAxis,t.userOptions);else{let a=e.yAxis.indexOf(this);this.options=d(this.options,this.chart.options.chart.parallelAxes,t.userOptions),i.position=f(i.position,a>=0?a:e.yAxis.length),i.setPosition(s,this.options)}}}function i(t){let e=this.chart,i=this.parallelCoordinates;if(i&&e&&e.hasParallelCoordinates&&!this.isXAxis){let e=i.position,s=[];this.series.forEach(function(t){t.visible&&c(e)&&(s=(t.pointArrayMap||["y"]).reduce((i,s)=>[...i,t.getColumn(s)?.[e]??null],s))}),s=s.filter(c),this.dataMin=p(s),this.dataMax=h(s),t.preventDefault()}}function s(){this.parallelCoordinates||(this.parallelCoordinates=new u(this))}t.compose=function(t){t.keepProps.includes("parallel")||(t.keepProps.push("parallel"),n(t,"init",s),n(t,"afterSetOptions",e),n(t,"getSeriesExtremes",i))}}(e||(e={}));let x=e,A=t.default.Templating;var y=a.n(A);let{composed:m}=o(),{format:g}=y(),{addEvent:P,defined:v,erase:C,extend:b,insertItem:I,isArray:X,isNumber:M,pushUnique:O}=o();!function(t){function e(){let t=this.chart,e=this.points,i=e&&e.length,s=Number.MAX_VALUE,a,l;if(this.chart.hasParallelCoordinates){for(let o=0;o<i;o++)v((l=e[o]).y)?(t.polar?l.plotX=t.yAxis[o].angleRad||0:t.inverted?l.plotX=t.plotHeight-t.yAxis[o].top+t.plotTop:l.plotX=t.yAxis[o].left-t.plotLeft,l.clientX=l.plotX,l.plotY=t.yAxis[o].translate(l.y,!1,!0,void 0,!0),M(l.high)&&(l.plotHigh=t.yAxis[o].translate(l.high,!1,!0,void 0,!0)),void 0!==a&&(s=Math.min(s,Math.abs(l.plotX-a))),a=l.plotX,l.isInside=t.isInsidePlot(l.plotX,l.plotY,{inverted:t.inverted})):l.isNull=!0;this.closestPointRangePx=s}}function i(t){let e=this.chart;if(e.hasParallelCoordinates){for(let t of e.axes)I(this,t.series),t.isDirty=!0;this.xAxis=e.xAxis[0],this.yAxis=e.yAxis[0],t.preventDefault()}}function s(){let t=this.chart;if(t.hasParallelCoordinates)for(let e of t.axes||[])e&&e.series&&(C(e.series,this),e.isDirty=e.forceRedraw=!0)}function a(){let t=this.chart;if(t?.hasParallelCoordinates)for(let e of this.points){let i,s=t.yAxis[e.x||0],a=s.options,l=a.tooltipValueFormat??a.labels.format;i=l?g(l,b(e,{value:e.y}),t):s.dateTime?t.time.dateFormat(t.time.resolveDTLFormat(a.dateTimeLabelFormats?.[s.tickPositions.info?.unitName||"year"]||"").main,e.y??void 0):X(a.categories)?a.categories[e.y??-1]:String(e.y??""),e.formattedValue=i}}t.compose=function(t){O(m,"ParallelSeries")&&(P(t,"afterTranslate",e,{order:1}),P(t,"bindAxes",i),P(t,"destroy",s),P(t,"afterGeneratePoints",a))}}(i||(i={}));let T=i,{addEvent:E,defined:L,merge:S,splat:_}=o();class N{constructor(t){this.chart=t}setParallelInfo(t){let e=this.chart||this,i=t.series;for(let t of(e.parallelInfo={counter:0},i))t.data&&(e.parallelInfo.counter=Math.max(e.parallelInfo.counter,t.data.length-1))}}!function(t){function e(t){let e=t.args[0],i=_(e.yAxis||{}),s=[],a=i.length;if(this.hasParallelCoordinates=e.chart&&e.chart.parallelCoordinates,this.hasParallelCoordinates){for(this.setParallelInfo(e);a<=this.parallelInfo.counter;a++)s.push({});e.legend||(e.legend={}),e.legend&&void 0===e.legend.enabled&&(e.legend.enabled=!1),S(!0,e,{boost:{seriesThreshold:Number.MAX_VALUE},plotOptions:{series:{boostThreshold:Number.MAX_VALUE}}}),e.yAxis=i.concat(s),e.xAxis=S(r.xAxis,_(e.xAxis||{})[0])}}function i(t){let e=t.options;if(e.chart&&(L(e.chart.parallelCoordinates)&&(this.hasParallelCoordinates=e.chart.parallelCoordinates),this.options.chart.parallelAxes=S(this.options.chart.parallelAxes,e.chart.parallelAxes)),this.hasParallelCoordinates)for(let t of(e.series&&this.setParallelInfo(e),this.yAxis))t.update({},!1)}t.compose=function(t,s,a,l){x.compose(t),T.compose(l);let o=N.prototype,n=s.prototype;n.setParallelInfo||(n.setParallelInfo=o.setParallelInfo,E(s,"init",e),E(s,"update",i),S(!0,a.chart,r.chart))}}(s||(s={}));let D=s,V=o();D.compose(V.Axis,V.Chart,V.defaultOptions,V.Series);let k=o();export{k as default};