import*as t from"../highcharts.js";var e,s,i,o={};o.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return o.d(e,{a:e}),e},o.d=(t,e)=>{for(var s in e)o.o(e,s)&&!o.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:e[s]})},o.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);let l=t.default;var r=o.n(l);let a=t.default.Axis;var n=o.n(a);let h=t.default.Color;var d=o.n(h);let{parse:c}=d(),{addEvent:p,extend:f,merge:g,pick:u,splat:m}=r();!function(t){let e;function s(){let{userOptions:t}=this;this.colorAxis=[],t.colorAxis&&(t.colorAxis=m(t.colorAxis),t.colorAxis.map(t=>new e(this,t)))}function i(t){let e=this.chart.colorAxis||[],s=e=>{let s=t.allItems.indexOf(e);-1!==s&&(this.destroyItem(t.allItems[s]),t.allItems.splice(s,1))},i=[],o,l;for(e.forEach(function(t){o=t.options,o?.showInLegend&&(o.dataClasses&&o.visible?i=i.concat(t.getDataClassLegendSymbols()):o.visible&&i.push(t),t.series.forEach(function(t){(!t.options.showInLegend||o.dataClasses)&&("point"===t.options.legendType?t.points.forEach(function(t){s(t)}):s(t))}))}),l=i.length;l--;)t.allItems.unshift(i[l])}function o(t){t.visible&&t.item.legendColor&&t.item.legendItem.symbol.attr({fill:t.item.legendColor})}function l(t){this.chart.colorAxis?.forEach(e=>{e.update({},t.redraw)})}function r(){(this.chart.colorAxis?.length||this.colorAttribs)&&this.translateColors()}function a(){let t=this.axisTypes;t?-1===t.indexOf("colorAxis")&&t.push("colorAxis"):this.axisTypes=["colorAxis"]}function n(t){let e=this,s=t?"show":"hide";e.visible=e.options.visible=!!t,["graphic","dataLabel"].forEach(function(t){e[t]&&e[t][s]()}),this.series.buildKDTree()}function h(){let t=this,e=this.getPointsCollection(),s=this.options.nullColor,i=this.colorAxis,o=this.colorKey;e.forEach(e=>{let l=e.getNestedProperty(o),r=e.options.color||(e.isNull||null===e.value?s:i&&void 0!==l?i.toColor(l,e):e.color||t.color);r&&e.color!==r&&(e.color=r,"point"===t.options.legendType&&e.legendItem&&e.legendItem.label&&t.chart.legend.colorizeItem(e,e.visible))})}function d(){this.elem.attr("fill",c(this.start).tweenTo(c(this.end),this.pos),void 0,!0)}function x(){this.elem.attr("stroke",c(this.start).tweenTo(c(this.end),this.pos),void 0,!0)}t.compose=function(t,c,m,y,C){let b=c.prototype,v=m.prototype,A=C.prototype;b.collectionsWithUpdate.includes("colorAxis")||(e=t,b.collectionsWithUpdate.push("colorAxis"),b.collectionsWithInit.colorAxis=[b.addColorAxis],p(c,"afterCreateAxes",s),function(t){let s=t.prototype.createAxis;t.prototype.createAxis=function(t,i){if("colorAxis"!==t)return s.apply(this,arguments);let o=new e(this,g(i.axis,{index:this[t].length,isX:!1}));return this.isDirtyLegend=!0,this.axes.forEach(t=>{t.series=[]}),this.series.forEach(t=>{t.bindAxes(),t.isDirtyData=!0}),u(i.redraw,!0)&&this.redraw(i.animation),o}}(c),v.fillSetter=d,v.strokeSetter=x,p(y,"afterGetAllItems",i),p(y,"afterColorizeItem",o),p(y,"afterUpdate",l),f(A,{optionalAxis:"colorAxis",translateColors:h}),f(A.pointClass.prototype,{setVisible:n}),p(C,"afterTranslate",r,{order:1}),p(C,"bindAxes",a))},t.pointSetVisible=n}(s||(s={}));let x=s,{parse:y}=d(),{merge:C}=r();(e=i||(i={})).initDataClasses=function(t){let e=this.chart,s=this.legendItem=this.legendItem||{},i=this.options,o=t.dataClasses||[],l,r,a=e.options.chart.colorCount,n=0,h;this.dataClasses=r=[],s.labels=[];for(let t=0,s=o.length;t<s;++t)l=C(l=o[t]),r.push(l),(e.styledMode||!l.color)&&("category"===i.dataClassColor?(e.styledMode||(a=(h=e.options.colors||[]).length,l.color=h[n]),l.colorIndex=n,++n===a&&(n=0)):l.color=y(i.minColor).tweenTo(y(i.maxColor),s<2?.5:t/(s-1)))},e.initStops=function(){let t=this.options,e=this.stops=t.stops||[[0,t.minColor||""],[1,t.maxColor||""]];for(let t=0,s=e.length;t<s;++t)e[t].color=y(e[t][1])},e.normalizedValue=function(t){let e=this.max||0,s=this.min||0;return this.logarithmic&&(t=this.logarithmic.log2lin(t)),1-(e-t)/(e-s||1)},e.toColor=function(t,e){let s,i,o,l,r,a,n=this.dataClasses,h=this.stops;if(n){for(a=n.length;a--;)if(i=(r=n[a]).from,o=r.to,(void 0===i||t>=i)&&(void 0===o||t<=o)){l=r.color,e&&(e.dataClass=a,e.colorIndex=r.colorIndex);break}}else{for(s=this.normalizedValue(t),a=h.length;a--&&!(s>h[a][0]););i=h[a]||h[a+1],s=1-((o=h[a+1]||i)[0]-s)/(o[0]-i[0]||1),l=i.color.tweenTo(o.color,s)}return l};let b=i,v=t.default.LegendSymbol;var A=o.n(v);let I=t.default.SeriesRegistry;var L=o.n(I);let{defaultOptions:w}=r(),{series:S}=L(),{defined:M,extend:P,fireEvent:D,isArray:k,isNumber:z,merge:E,pick:O,relativeLength:V}=r();w.colorAxis=E(w.xAxis,{lineWidth:0,minPadding:0,maxPadding:0,gridLineColor:"#ffffff",gridLineWidth:1,tickPixelInterval:72,startOnTick:!0,endOnTick:!0,offset:0,marker:{animation:{duration:50},width:.01,color:"#999999"},labels:{distance:8,overflow:"justify",rotation:0},minColor:"#e6e9ff",maxColor:"#0022ff",tickLength:5,showInLegend:!0});class T extends n(){static compose(t,e,s,i){x.compose(T,t,e,s,i)}constructor(t,e){super(t,e),this.coll="colorAxis",this.visible=!0,this.init(t,e)}init(t,e){let s=t.options.legend||{},i=e.layout?"vertical"!==e.layout:"vertical"!==s.layout;this.side=e.side||i?2:1,this.reversed=e.reversed||!i,this.opposite=!i,super.init(t,e,"colorAxis"),this.userOptions=e,k(t.userOptions.colorAxis)&&(t.userOptions.colorAxis[this.index]=e),e.dataClasses&&this.initDataClasses(e),this.initStops(),this.horiz=i,this.zoomEnabled=!1}hasData(){return!!(this.tickPositions||[]).length}setTickPositions(){if(!this.dataClasses)return super.setTickPositions()}setOptions(t){let e=E(w.colorAxis,t,{showEmpty:!1,title:null,visible:this.chart.options.legend.enabled&&!1!==t.visible});super.setOptions(e),this.options.crosshair=this.options.marker}setAxisSize(){let t=this.chart,e=this.legendItem?.symbol,{width:s,height:i}=this.getSize();e&&(this.left=+e.attr("x"),this.top=+e.attr("y"),this.width=s=+e.attr("width"),this.height=i=+e.attr("height"),this.right=t.chartWidth-this.left-s,this.bottom=t.chartHeight-this.top-i,this.pos=this.horiz?this.left:this.top),this.len=(this.horiz?s:i)||T.defaultLegendLength}getOffset(){let t=this.legendItem?.group,e=this.chart.axisOffset[this.side];if(t){this.axisParent=t,super.getOffset();let s=this.chart.legend;s.allItems.forEach(function(t){t instanceof T&&t.drawLegendSymbol(s,t)}),s.render(),this.chart.getMargins(!0),this.chart.series.some(t=>t.isDrilling)||(this.isDirty=!0),this.added||(this.added=!0,this.labelLeft=0,this.labelRight=this.width),this.chart.axisOffset[this.side]=e}}setLegendColor(){let t=this.horiz,e=this.reversed,s=+!!e,i=+!e,o=t?[s,0,i,0]:[0,i,0,s];this.legendColor={linearGradient:{x1:o[0],y1:o[1],x2:o[2],y2:o[3]},stops:this.stops}}drawLegendSymbol(t,e){let s=e.legendItem||{},i=t.padding,o=t.options,l=this.options.labels,r=O(o.itemDistance,10),a=this.horiz,{width:n,height:h}=this.getSize(),d=O(o.labelPadding,a?16:30);this.setLegendColor(),s.symbol||(s.symbol=this.chart.renderer.symbol("roundedRect").attr({r:o.symbolRadius??3,zIndex:1}).add(s.group)),s.symbol.attr({x:0,y:(t.baseline||0)-11,width:n,height:h}),s.labelWidth=n+i+(a?r:O(l.x,l.distance)+(this.maxLabelLength||0)),s.labelHeight=h+i+(a?d:0)}setState(t){this.series.forEach(function(e){e.setState(t)})}setVisible(){}getSeriesExtremes(){let t=this.series,e,s,i,o,l=t.length;for(this.dataMin=1/0,this.dataMax=-1/0;l--;){for(let r of(s=(o=t[l]).colorKey=O(o.options.colorKey,o.colorKey,o.pointValKey,o.zoneAxis,"y"),i=o[s+"Min"]&&o[s+"Max"],[s,"value","y"]))if((e=o.getColumn(r)).length)break;if(i)o.minColorValue=o[s+"Min"],o.maxColorValue=o[s+"Max"];else{let t=S.prototype.getExtremes.call(o,e);o.minColorValue=t.dataMin,o.maxColorValue=t.dataMax}M(o.minColorValue)&&M(o.maxColorValue)&&(this.dataMin=Math.min(this.dataMin,o.minColorValue),this.dataMax=Math.max(this.dataMax,o.maxColorValue)),i||S.prototype.applyExtremes.call(o)}}drawCrosshair(t,e){let s,i=this.legendItem||{},o=e?.plotX,l=e?.plotY,r=this.pos,a=this.len;e&&((s=this.toPixels(e.getNestedProperty(e.series.colorKey)))<r?s=r-2:s>r+a&&(s=r+a+2),e.plotX=s,e.plotY=this.len-s,super.drawCrosshair(t,e),e.plotX=o,e.plotY=l,this.cross&&!this.cross.addedToColorAxis&&i.group&&(this.cross.addClass("highcharts-coloraxis-marker").add(i.group),this.cross.addedToColorAxis=!0,this.chart.styledMode||"object"!=typeof this.crosshair||this.cross.attr({fill:this.crosshair.color})))}getPlotLinePath(t){let e=this.left,s=t.translatedValue,i=this.top;return z(s)?this.horiz?[["M",s-4,i-6],["L",s+4,i-6],["L",s,i],["Z"]]:[["M",e,s],["L",e-6,s+6],["L",e-6,s-6],["Z"]]:super.getPlotLinePath(t)}update(t,e){let s=this.chart.legend;this.series.forEach(t=>{t.isDirtyData=!0}),(t.dataClasses&&s.allItems||this.dataClasses)&&this.destroyItems(),super.update(t,e),this.legendItem?.label&&(this.setLegendColor(),s.colorizeItem(this,!0))}destroyItems(){let t=this.chart,e=this.legendItem||{};if(e.label)t.legend.destroyItem(this);else if(e.labels)for(let s of e.labels)t.legend.destroyItem(s);t.isDirtyLegend=!0}destroy(){this.chart.isDirtyLegend=!0,this.destroyItems(),super.destroy(...[].slice.call(arguments))}remove(t){this.destroyItems(),super.remove(t)}getDataClassLegendSymbols(){let t,e=this,s=e.chart,i=e.legendItem&&e.legendItem.labels||[],o=s.options.legend,l=O(o.valueDecimals,-1),r=O(o.valueSuffix,""),a=t=>e.series.reduce((e,s)=>(e.push(...s.points.filter(e=>e.dataClass===t)),e),[]);return i.length||e.dataClasses.forEach((o,n)=>{let h=o.from,d=o.to,{numberFormatter:c}=s,p=!0;t="",void 0===h?t="< ":void 0===d&&(t="> "),void 0!==h&&(t+=c(h,l)+r),void 0!==h&&void 0!==d&&(t+=" - "),void 0!==d&&(t+=c(d,l)+r),i.push(P({chart:s,name:t,options:{},drawLegendSymbol:A().rectangle,visible:!0,isDataClass:!0,setState:t=>{for(let e of a(n))e.setState(t)},setVisible:function(){this.visible=p=e.visible=!p;let t=[];for(let e of a(n))e.setVisible(p),e.hiddenInDataClass=!p,-1===t.indexOf(e.series)&&t.push(e.series);s.legend.colorizeItem(this,p),t.forEach(t=>{D(t,"afterDataClassLegendClick")})}},o))}),i}getSize(){let{chart:t,horiz:e}=this,{height:s,width:i}=this.options,{legend:o}=t.options;return{width:O(M(i)?V(i,t.chartWidth):void 0,o?.symbolWidth,e?T.defaultLegendLength:12),height:O(M(s)?V(s,t.chartHeight):void 0,o?.symbolHeight,e?12:T.defaultLegendLength)}}}T.defaultLegendLength=200,T.keepProps=["legendItem"],P(T.prototype,b),Array.prototype.push.apply(n().keepProps,T.keepProps);let W=r();W.ColorAxis=W.ColorAxis||T,W.ColorAxis.compose(W.Chart,W.Fx,W.Legend,W.Series);let K=r();export{K as default};