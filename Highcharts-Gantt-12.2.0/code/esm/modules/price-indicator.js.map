{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/price-indicator\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Advanced Highcharts Stock tools\n *\n * (c) 2010-2025 Highsoft AS\n * Author: Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\nimport * as __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__ from \"../highcharts.js\";\nimport * as __WEBPACK_EXTERNAL_MODULE__stock_src_js_3de69a45__ from \"./stock.js\";\n/******/ // The require scope\n/******/ var __webpack_require__ = {};\n/******/ \n/************************************************************************/\n/******/ /* webpack/runtime/compat get default export */\n/******/ (() => {\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = (module) => {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t() => (module['default']) :\n/******/ \t\t\t() => (module);\n/******/ \t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\treturn getter;\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/define property getters */\n/******/ (() => {\n/******/ \t// define getter functions for harmony exports\n/******/ \t__webpack_require__.d = (exports, definition) => {\n/******/ \t\tfor(var key in definition) {\n/******/ \t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t}\n/******/ \t\t}\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/hasOwnProperty shorthand */\n/******/ (() => {\n/******/ \t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ })();\n/******/ \n/************************************************************************/\n\n;// external [\"../highcharts.js\",\"default\"]\nconst external_highcharts_src_js_default_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"];\nvar external_highcharts_src_js_default_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_namespaceObject);\n;// external \"./stock.js\"\nvar x = (y) => {\n\tvar x = {}; __webpack_require__.d(x,\n    \ty); return x\n    } \n    var y = (x) => (() => (x))\n    const external_stock_src_js_namespaceObject = x({  });\n;// ./code/es-modules/Extensions/PriceIndication.js\n/**\n * (c) 2009-2025 Sebastian Bochann\n *\n * Price indicator for Highcharts\n *\n * License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n */\n\n\nconst { composed } = (external_highcharts_src_js_default_default());\n\nconst { addEvent, merge, pushUnique } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Composition\n *\n * */\n/** @private */\nfunction compose(SeriesClass) {\n    if (pushUnique(composed, 'PriceIndication')) {\n        addEvent(SeriesClass, 'afterRender', onSeriesAfterRender);\n    }\n}\n/** @private */\nfunction onSeriesAfterRender() {\n    const series = this, seriesOptions = series.options, lastVisiblePrice = seriesOptions.lastVisiblePrice, lastPrice = seriesOptions.lastPrice;\n    if ((lastVisiblePrice || lastPrice) &&\n        seriesOptions.id !== 'highcharts-navigator-series') {\n        const xAxis = series.xAxis, yAxis = series.yAxis, origOptions = yAxis.crosshair, origGraphic = yAxis.cross, origLabel = yAxis.crossLabel, points = series.points, pLength = points.length, dataLength = series.dataTable.rowCount, x = series.getColumn('x')[dataLength - 1], y = series.getColumn('y')[dataLength - 1] ??\n            series.getColumn('close')[dataLength - 1];\n        let yValue;\n        if (lastPrice && lastPrice.enabled) {\n            yAxis.crosshair = yAxis.options.crosshair = seriesOptions.lastPrice;\n            if (!series.chart.styledMode &&\n                yAxis.crosshair &&\n                yAxis.options.crosshair &&\n                seriesOptions.lastPrice) {\n                // Set the default color from the series, #14888.\n                yAxis.crosshair.color = yAxis.options.crosshair.color =\n                    seriesOptions.lastPrice.color || series.color;\n            }\n            yAxis.cross = series.lastPrice;\n            yValue = y;\n            if (series.lastPriceLabel) {\n                series.lastPriceLabel.destroy();\n            }\n            delete yAxis.crossLabel;\n            yAxis.drawCrosshair(null, ({\n                x: x,\n                y: yValue,\n                plotX: xAxis.toPixels(x, true),\n                plotY: yAxis.toPixels(yValue, true)\n            }));\n            // Save price\n            if (series.yAxis.cross) {\n                series.lastPrice = series.yAxis.cross;\n                series.lastPrice.addClass('highcharts-color-' + series.colorIndex); // #15222\n                series.lastPrice.y = yValue;\n            }\n            series.lastPriceLabel = yAxis.crossLabel;\n        }\n        if (lastVisiblePrice && lastVisiblePrice.enabled && pLength > 0) {\n            yAxis.crosshair = yAxis.options.crosshair = merge({\n                color: 'transparent' // Line invisible by default\n            }, seriesOptions.lastVisiblePrice);\n            yAxis.cross = series.lastVisiblePrice;\n            const lastPoint = points[pLength - 1].isInside ?\n                points[pLength - 1] : points[pLength - 2];\n            if (series.lastVisiblePriceLabel) {\n                series.lastVisiblePriceLabel.destroy();\n            }\n            // Set to undefined to avoid collision with\n            // the yAxis crosshair #11480\n            // Delete the crossLabel each time the code is invoked, #13876.\n            delete yAxis.crossLabel;\n            // Save price\n            yAxis.drawCrosshair(null, lastPoint);\n            if (yAxis.cross) {\n                series.lastVisiblePrice = yAxis.cross;\n                if (lastPoint && typeof lastPoint.y === 'number') {\n                    series.lastVisiblePrice.y = lastPoint.y;\n                }\n            }\n            series.lastVisiblePriceLabel = yAxis.crossLabel;\n        }\n        // Restore crosshair:\n        yAxis.crosshair = yAxis.options.crosshair = origOptions;\n        yAxis.cross = origGraphic;\n        yAxis.crossLabel = origLabel;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst PriceIndication = {\n    compose\n};\n/* harmony default export */ const Extensions_PriceIndication = (PriceIndication);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * The line marks the last price from visible range of points.\n *\n * @sample {highstock} stock/indicators/last-visible-price\n *         Last visible price\n *\n * @declare   Highcharts.SeriesLastVisiblePriceOptionsObject\n * @product   highstock\n * @requires  modules/price-indicator\n * @apioption plotOptions.series.lastVisiblePrice\n */\n/**\n * The color of the line of last visible price.\n * By default, color is not applied and the line is not visible.\n *\n * @type      {string}\n * @product   highstock\n * @apioption plotOptions.series.lastVisiblePrice.color\n *\n */\n/**\n * Name of the dash style to use for the line of last visible price.\n *\n * @sample {highstock} highcharts/plotoptions/series-dashstyle-all/\n *         Possible values demonstrated\n *\n * @type      {Highcharts.DashStyleValue}\n * @product   highstock\n * @default   Solid\n * @apioption plotOptions.series.lastVisiblePrice.dashStyle\n *\n */\n/**\n * Width of the last visible price line.\n *\n * @type      {number}\n * @product   highstock\n * @default   1\n * @apioption plotOptions.series.lastVisiblePrice.width\n *\n */\n/**\n * Enable or disable the indicator.\n *\n * @type      {boolean}\n * @product   highstock\n * @default   false\n * @apioption plotOptions.series.lastVisiblePrice.enabled\n */\n/**\n * @declare   Highcharts.SeriesLastVisiblePriceLabelOptionsObject\n * @extends   yAxis.crosshair.label\n * @since     7.0.0\n * @apioption plotOptions.series.lastVisiblePrice.label\n */\n/**\n * @since     7.0.0\n * @apioption plotOptions.series.lastVisiblePrice.label.align\n */\n/**\n * @since     7.0.0\n * @apioption plotOptions.series.lastVisiblePrice.label.backgroundColor\n */\n/**\n * The border color for the `lastVisiblePrice` label.\n *\n * @type      {Highcharts.ColorType}\n * @since     7.0.0\n * @product   highstock\n * @apioption plotOptions.series.lastVisiblePrice.label.borderColor\n */\n/**\n * The border corner radius of the `lastVisiblePrice` label.\n *\n * @type      {number}\n * @default   3\n * @since     7.0.0\n * @product   highstock\n * @apioption plotOptions.series.lastVisiblePrice.label.borderRadius\n*/\n/**\n * Flag to enable `lastVisiblePrice` label.\n *\n *\n * @type      {boolean}\n * @default   false\n * @since     7.0\n * @product   highstock\n * @apioption plotOptions.series.lastVisiblePrice.label.enabled\n */\n/**\n * A format string for the `lastVisiblePrice` label. Defaults to `{value}` for\n * numeric axes and `{value:%b %d, %Y}` for datetime axes.\n *\n * @type      {string}\n * @since     7.0\n * @product   highstock\n * @apioption plotOptions.series.lastVisiblePrice.label.format\n*/\n/**\n * @since     7.0.0\n * @apioption plotOptions.series.lastVisiblePrice.label.formatter\n */\n/**\n * @since     7.0.0\n * @apioption plotOptions.series.lastVisiblePrice.label.padding\n */\n/**\n * @since     7.0.0\n * @apioption plotOptions.series.lastVisiblePrice.label.shape\n */\n/**\n * Text styles for the `lastVisiblePrice` label.\n *\n * @type      {Highcharts.CSSObject}\n * @default   {\"color\": \"white\", \"fontWeight\": \"normal\", \"fontSize\": \"11px\", \"textAlign\": \"center\"}\n * @since     7.0\n * @product   highstock\n * @apioption plotOptions.series.lastVisiblePrice.label.style\n */\n/**\n * The border width for the `lastVisiblePrice` label.\n *\n * @type      {number}\n * @default   0\n * @since     7.0\n * @product   highstock\n * @apioption plotOptions.series.lastVisiblePrice.label.borderWidth\n*/\n/**\n * Padding inside the `lastVisiblePrice` label.\n *\n * @type      {number}\n * @default   8\n * @since     7.0\n * @product   highstock\n * @apioption plotOptions.series.lastVisiblePrice.label.padding\n */\n/**\n * The line marks the last price from all points.\n *\n * @sample {highstock} stock/indicators/last-price\n *         Last price\n *\n * @declare   Highcharts.SeriesLastPriceOptionsObject\n * @product   highstock\n * @requires  modules/price-indicator\n * @apioption plotOptions.series.lastPrice\n */\n/**\n * Enable or disable the indicator.\n *\n * @type      {boolean}\n * @product   highstock\n * @default   false\n * @apioption plotOptions.series.lastPrice.enabled\n */\n/**\n * @declare   Highcharts.SeriesLastPriceLabelOptionsObject\n * @extends   yAxis.crosshair.label\n * @since     7.0.0\n * @apioption plotOptions.series.lastPrice.label\n */\n/**\n * @since     7.0.0\n * @apioption plotOptions.series.lastPrice.label.align\n * */\n/**\n * @since     7.0.0\n * @apioption plotOptions.series.lastPrice.label.backgroundColor\n * */\n/**\n * The border color of `lastPrice` label.\n * @since     7.0.0\n * @apioption plotOptions.series.lastPrice.label.borderColor\n * */\n/**\n * The border radius of `lastPrice` label.\n * @since     7.0.0\n * @apioption plotOptions.series.lastPrice.label.borderRadius\n * */\n/**\n * The border width of `lastPrice` label.\n * @since     7.0.0\n * @apioption plotOptions.series.lastPrice.label.borderWidth\n * */\n/**\n * Flag to enable `lastPrice` label.\n * @since     7.0.0\n * @apioption plotOptions.series.lastPrice.label.enabled\n * */\n/**\n * A format string for the `lastPrice` label. Defaults to `{value}` for\n * numeric axes and `{value:%b %d, %Y}` for datetime axes.\n *\n * @type      {string}\n * @since     7.0\n * @product   highstock\n * @apioption plotOptions.series.lastPrice.label.format\n*/\n/**\n * @since     7.0.0\n * @apioption plotOptions.series.lastPrice.label.formatter\n */\n/**\n * @since     7.0.0\n * @apioption plotOptions.series.lastPrice.label.padding\n */\n/**\n * @since     7.0.0\n * @apioption plotOptions.series.lastPrice.label.shape\n */\n/**\n * Text styles for the `lastPrice` label.\n *\n * @type      {Highcharts.CSSObject}\n * @default   {\"color\": \"white\", \"fontWeight\": \"normal\", \"fontSize\": \"11px\", \"textAlign\": \"center\"}\n * @since     7.0\n * @product   highstock\n * @apioption plotOptions.series.lastPrice.label.style\n */\n/**\n * The border width for the `lastPrice` label.\n *\n * @type      {number}\n * @default   0\n * @since     7.0\n * @product   highstock\n * @apioption plotOptions.series.lastPrice.label.borderWidth\n*/\n/**\n * Padding inside the `lastPrice` label.\n *\n * @type      {number}\n * @default   8\n * @since     7.0\n * @product   highstock\n * @apioption plotOptions.series.lastPrice.label.padding\n */\n/**\n * The color of the line of last price.\n * By default, the line has the same color as the series.\n *\n * @type      {string}\n * @product   highstock\n * @apioption plotOptions.series.lastPrice.color\n *\n */\n/**\n * Name of the dash style to use for the line of last price.\n *\n * @sample {highstock} highcharts/plotoptions/series-dashstyle-all/\n *         Possible values demonstrated\n *\n * @type      {Highcharts.DashStyleValue}\n * @product   highstock\n * @default   Solid\n * @apioption plotOptions.series.lastPrice.dashStyle\n *\n */\n/**\n * Width of the last price line.\n *\n * @type      {number}\n * @product   highstock\n * @default   1\n * @apioption plotOptions.series.lastPrice.width\n *\n */\n''; // Keeps doclets above in JS file\n\n;// ./code/es-modules/masters/modules/price-indicator.js\n\n\n\n\n\nconst G = (external_highcharts_src_js_default_default());\nExtensions_PriceIndication.compose(G.Series);\n/* harmony default export */ const price_indicator_src = ((external_highcharts_src_js_default_default()));\n\nexport { price_indicator_src as default };\n"], "names": ["__WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__", "__webpack_require__", "n", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "external_highcharts_src_js_default_namespaceObject", "external_highcharts_src_js_default_default", "composed", "addEvent", "merge", "pushUnique", "onSeriesAfterRender", "seriesOptions", "series", "options", "lastVisiblePrice", "lastPrice", "id", "xAxis", "yAxis", "origOptions", "crosshair", "origGraphic", "cross", "origLabel", "crossLabel", "points", "p<PERSON><PERSON>th", "length", "dataLength", "dataTable", "rowCount", "x", "getColumn", "y", "enabled", "chart", "styledMode", "color", "lastPriceLabel", "destroy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "plotX", "toPixels", "plotY", "addClass", "colorIndex", "lastPoint", "isInside", "lastVisiblePriceLabel", "Extensions_PriceIndication", "compose", "SeriesClass", "G", "Series", "price_indicator_src", "default"], "mappings": "AAaA,UAAYA,MAA6D,sBAAuB,AAChG,OAAoE,gBAAiB,CAE5E,IAAIC,EAAsB,CAAC,CAM1BA,CAAAA,EAAoBC,CAAC,CAAG,AAACC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,UAAU,CACvC,IAAOF,EAAO,OAAU,CACxB,IAAOA,EAER,OADAF,EAAoBK,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAH,EAAoBK,CAAC,CAAG,CAACE,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXR,EAAoBU,CAAC,CAACF,EAAYC,IAAQ,CAACT,EAAoBU,CAAC,CAACH,EAASE,IAC5EE,OAAOC,cAAc,CAACL,EAASE,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAT,EAAoBU,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAM5F,IAAMI,EAAqDrB,EAAwD,OAAU,CAC7H,IAAIsB,EAA0DrB,EAAoBC,CAAC,CAACmB,GAGvEpB,EAAoBK,CAAC,CAAzB,CAAC,EAI0C,CAAG,GAavD,GAAM,CAAEiB,SAAAA,CAAQ,CAAE,CAAID,IAEhB,CAAEE,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,WAAAA,CAAU,CAAE,CAAIJ,IAazC,SAASK,IACL,IAAqBC,EAAgBC,AAAtB,IAAI,CAAyBC,OAAO,CAAEC,EAAmBH,EAAcG,gBAAgB,CAAEC,EAAYJ,EAAcI,SAAS,CAC3I,GAAI,AAACD,CAAAA,GAAoBC,CAAQ,GAC7BJ,AAAqB,gCAArBA,EAAcK,EAAE,CAAoC,CACpD,IAAMC,EAAQL,AAHH,IAAI,CAGMK,KAAK,CAAEC,EAAQN,AAHzB,IAAI,CAG4BM,KAAK,CAAEC,EAAcD,EAAME,SAAS,CAAEC,EAAcH,EAAMI,KAAK,CAAEC,EAAYL,EAAMM,UAAU,CAAEC,EAASb,AAHxI,IAAI,CAG2Ia,MAAM,CAAEC,EAAUD,EAAOE,MAAM,CAAEC,EAAahB,AAH7L,IAAI,CAGgMiB,SAAS,CAACC,QAAQ,CAAEC,EAAInB,AAH5N,IAAI,CAG+NoB,SAAS,CAAC,IAAI,CAACJ,EAAa,EAAE,CAAEK,EAAIrB,AAHvQ,IAAI,CAG0QoB,SAAS,CAAC,IAAI,CAACJ,EAAa,EAAE,EACnThB,AAJO,IAAI,CAIJoB,SAAS,CAAC,QAAQ,CAACJ,EAAa,EAAE,CAgC7C,GA9BIb,GAAaA,EAAUmB,OAAO,GAC9BhB,EAAME,SAAS,CAAGF,EAAML,OAAO,CAACO,SAAS,CAAGT,EAAcI,SAAS,CAC/D,CAACH,AARE,IAAI,CAQCuB,KAAK,CAACC,UAAU,EACxBlB,EAAME,SAAS,EACfF,EAAML,OAAO,CAACO,SAAS,EACvBT,EAAcI,SAAS,EAEvBG,CAAAA,EAAME,SAAS,CAACiB,KAAK,CAAGnB,EAAML,OAAO,CAACO,SAAS,CAACiB,KAAK,CACjD1B,EAAcI,SAAS,CAACsB,KAAK,EAAIzB,AAdlC,IAAI,CAcqCyB,KAAK,AAAD,EAEpDnB,EAAMI,KAAK,CAAGV,AAhBP,IAAI,CAgBUG,SAAS,CAE1BH,AAlBG,IAAI,CAkBA0B,cAAc,EACrB1B,AAnBG,IAAI,CAmBA0B,cAAc,CAACC,OAAO,GAEjC,OAAOrB,EAAMM,UAAU,CACvBN,EAAMsB,aAAa,CAAC,KAAO,CACvBT,EAAGA,EACHE,EAPKA,EAQLQ,MAAOxB,EAAMyB,QAAQ,CAACX,EAAG,CAAA,GACzBY,MAAOzB,EAAMwB,QAAQ,CAThBT,EASyB,CAAA,EAClC,GAEIrB,AA7BG,IAAI,CA6BAM,KAAK,CAACI,KAAK,GAClBV,AA9BG,IAAI,CA8BAG,SAAS,CAAGH,AA9BhB,IAAI,CA8BmBM,KAAK,CAACI,KAAK,CACrCV,AA/BG,IAAI,CA+BAG,SAAS,CAAC6B,QAAQ,CAAC,oBAAsBhC,AA/B7C,IAAI,CA+BgDiC,UAAU,EACjEjC,AAhCG,IAAI,CAgCAG,SAAS,CAACkB,CAAC,CAfbA,GAiBTrB,AAlCO,IAAI,CAkCJ0B,cAAc,CAAGpB,EAAMM,UAAU,EAExCV,GAAoBA,EAAiBoB,OAAO,EAAIR,EAAU,EAAG,CAC7DR,EAAME,SAAS,CAAGF,EAAML,OAAO,CAACO,SAAS,CAAGZ,EAAM,CAC9C6B,MAAO,aACX,EAAG1B,EAAcG,gBAAgB,EACjCI,EAAMI,KAAK,CAAGV,AAxCP,IAAI,CAwCUE,gBAAgB,CACrC,IAAMgC,EAAYrB,CAAM,CAACC,EAAU,EAAE,CAACqB,QAAQ,CAC1CtB,CAAM,CAACC,EAAU,EAAE,CAAGD,CAAM,CAACC,EAAU,EAAE,AACzCd,CA3CG,IAAI,CA2CAoC,qBAAqB,EAC5BpC,AA5CG,IAAI,CA4CAoC,qBAAqB,CAACT,OAAO,GAKxC,OAAOrB,EAAMM,UAAU,CAEvBN,EAAMsB,aAAa,CAAC,KAAMM,GACtB5B,EAAMI,KAAK,GACXV,AArDG,IAAI,CAqDAE,gBAAgB,CAAGI,EAAMI,KAAK,CACjCwB,GAAa,AAAuB,UAAvB,OAAOA,EAAUb,CAAC,EAC/BrB,CAAAA,AAvDD,IAAI,CAuDIE,gBAAgB,CAACmB,CAAC,CAAGa,EAAUb,CAAC,AAADA,GAG9CrB,AA1DO,IAAI,CA0DJoC,qBAAqB,CAAG9B,EAAMM,UAAU,AACnD,CAEAN,EAAME,SAAS,CAAGF,EAAML,OAAO,CAACO,SAAS,CAAGD,EAC5CD,EAAMI,KAAK,CAAGD,EACdH,EAAMM,UAAU,CAAGD,CACvB,CACJ,CAqSA0B,AA/RwB,CAAA,CACpBC,QA/EJ,SAAiBC,CAAW,EACpB1C,EAAWH,EAAU,oBACrBC,EAAS4C,EAAa,cAAezC,EAE7C,CA4EA,CAAA,EA6R2BwC,OAAO,CAACE,AADxB/C,IAC0BgD,MAAM,EACd,IAAMC,EAAwBjD,WAElDiD,KAAuBC,OAAO"}