{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/sunburst\n * @requires highcharts\n *\n * (c) 2016-2025 Highsoft AS\n * Authors: <AUTHORS>\n *\n * License: www.highcharts.com/license\n */\nimport * as __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__ from \"../highcharts.js\";\n/******/ // The require scope\n/******/ var __webpack_require__ = {};\n/******/ \n/************************************************************************/\n/******/ /* webpack/runtime/compat get default export */\n/******/ (() => {\n/******/ \t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t__webpack_require__.n = (module) => {\n/******/ \t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t() => (module['default']) :\n/******/ \t\t\t() => (module);\n/******/ \t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\treturn getter;\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/define property getters */\n/******/ (() => {\n/******/ \t// define getter functions for harmony exports\n/******/ \t__webpack_require__.d = (exports, definition) => {\n/******/ \t\tfor(var key in definition) {\n/******/ \t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t}\n/******/ \t\t}\n/******/ \t};\n/******/ })();\n/******/ \n/******/ /* webpack/runtime/hasOwnProperty shorthand */\n/******/ (() => {\n/******/ \t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ })();\n/******/ \n/************************************************************************/\n\n;// external [\"../highcharts.js\",\"default\"]\nconst external_highcharts_src_js_default_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"];\nvar external_highcharts_src_js_default_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_namespaceObject);\n;// ./code/es-modules/Extensions/Breadcrumbs/BreadcrumbsDefaults.js\n/* *\n *\n *  Highcharts Breadcrumbs module\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n/**\n * @optionparent lang\n */\nconst lang = {\n    /**\n     * @since   10.0.0\n     * @product highcharts highmaps\n     *\n     * @private\n     */\n    mainBreadcrumb: 'Main'\n};\n/**\n * Options for breadcrumbs. Breadcrumbs general options are defined in\n * `navigation.breadcrumbs`. Specific options for drilldown are set in\n * `drilldown.breadcrumbs` and for tree-like series traversing, in\n * `plotOptions[series].breadcrumbs`.\n *\n * @since        10.0.0\n * @product      highcharts\n * @optionparent navigation.breadcrumbs\n */\nconst options = {\n    /**\n     * A collection of attributes for the buttons. The object takes SVG\n     * attributes like `fill`, `stroke`, `stroke-width`, as well as `style`,\n     * a collection of CSS properties for the text.\n     *\n     * The object can also be extended with states, so you can set\n     * presentational options for `hover`, `select` or `disabled` button\n     * states.\n     *\n     * @sample {highcharts} highcharts/breadcrumbs/single-button\n     *         Themed, single button\n     *\n     * @type    {Highcharts.SVGAttributes}\n     * @since   10.0.0\n     * @product highcharts\n     */\n    buttonTheme: {\n        /** @ignore */\n        fill: 'none',\n        /** @ignore */\n        height: 18,\n        /** @ignore */\n        padding: 2,\n        /** @ignore */\n        'stroke-width': 0,\n        /** @ignore */\n        zIndex: 7,\n        /** @ignore */\n        states: {\n            select: {\n                fill: 'none'\n            }\n        },\n        style: {\n            color: \"#334eff\" /* Palette.highlightColor80 */\n        }\n    },\n    /**\n     * The default padding for each button and separator in each direction.\n     *\n     * @type  {number}\n     * @since 10.0.0\n     */\n    buttonSpacing: 5,\n    /**\n     * Fires when clicking on the breadcrumbs button. Two arguments are\n     * passed to the function. First breadcrumb button as an SVG element.\n     * Second is the breadcrumbs class, containing reference to the chart,\n     * series etc.\n     *\n     * ```js\n     * click: function(button, breadcrumbs) {\n     *   console.log(button);\n     * }\n     * ```\n     *\n     * Return false to stop default buttons click action.\n     *\n     * @type      {Highcharts.BreadcrumbsClickCallbackFunction}\n     * @since     10.0.0\n     * @apioption navigation.breadcrumbs.events.click\n     */\n    /**\n     * When the breadcrumbs are floating, the plot area will not move to\n     * make space for it. By default, the chart will not make space for the\n     * buttons. This property won't work when positioned in the middle.\n     *\n     * @sample highcharts/breadcrumbs/single-button\n     *         Floating button\n     *\n     * @type  {boolean}\n     * @since 10.0.0\n     */\n    floating: false,\n    /**\n     * A format string for the breadcrumbs button. Variables are enclosed by\n     * curly brackets. Available values are passed in the declared point\n     * options.\n     *\n     * @type      {string|undefined}\n     * @since 10.0.0\n     * @default   undefined\n     * @sample {highcharts} highcharts/breadcrumbs/format Display custom\n     *          values in breadcrumb button.\n     */\n    format: void 0,\n    /**\n     * Callback function to format the breadcrumb text from scratch.\n     *\n     * @type      {Highcharts.BreadcrumbsFormatterCallbackFunction}\n     * @since     10.0.0\n     * @default   undefined\n     * @apioption navigation.breadcrumbs.formatter\n     */\n    /**\n     * What box to align the button to. Can be either `plotBox` or\n     * `spacingBox`.\n     *\n     * @type    {Highcharts.ButtonRelativeToValue}\n     * @default plotBox\n     * @since   10.0.0\n     * @product highcharts highmaps\n     */\n    relativeTo: 'plotBox',\n    /**\n     * Whether to reverse the order of buttons. This is common in Arabic\n     * and Hebrew.\n     *\n     * @sample {highcharts} highcharts/breadcrumbs/rtl\n     *         Breadcrumbs in RTL\n     *\n     * @type  {boolean}\n     * @since 10.2.0\n     */\n    rtl: false,\n    /**\n     * Positioning for the button row. The breadcrumbs buttons will be\n     * aligned properly for the default chart layout (title,  subtitle,\n     * legend, range selector) for the custom chart layout set the position\n     * properties.\n     *\n     * @sample  {highcharts} highcharts/breadcrumbs/single-button\n     *          Single, right aligned button\n     *\n     * @type    {Highcharts.BreadcrumbsAlignOptions}\n     * @since   10.0.0\n     * @product highcharts highmaps\n     */\n    position: {\n        /**\n         * Horizontal alignment of the breadcrumbs buttons.\n         *\n         * @type {Highcharts.AlignValue}\n         */\n        align: 'left',\n        /**\n         * Vertical alignment of the breadcrumbs buttons.\n         *\n         * @type {Highcharts.VerticalAlignValue}\n         */\n        verticalAlign: 'top',\n        /**\n         * The X offset of the breadcrumbs button group.\n         *\n         * @type {number}\n         */\n        x: 0,\n        /**\n         * The Y offset of the breadcrumbs button group. When `undefined`,\n         * and `floating` is `false`, the `y` position is adapted so that\n         * the breadcrumbs are rendered outside the target area.\n         *\n         * @type {number|undefined}\n         */\n        y: void 0\n    },\n    /**\n     * Options object for Breadcrumbs separator.\n     *\n     * @since 10.0.0\n     */\n    separator: {\n        /**\n         * @type    {string}\n         * @since   10.0.0\n         * @product highcharts\n         */\n        text: '/',\n        /**\n         * CSS styles for the breadcrumbs separator.\n         *\n         * In styled mode, the breadcrumbs separators are styled by the\n         * `.highcharts-separator` rule with its different states.\n         *  @type  {Highcharts.CSSObject}\n         *  @since 10.0.0\n         */\n        style: {\n            color: \"#666666\" /* Palette.neutralColor60 */,\n            fontSize: '0.8em'\n        }\n    },\n    /**\n     * Show full path or only a single button.\n     *\n     * @sample {highcharts} highcharts/breadcrumbs/single-button\n     *         Single, styled button\n     *\n     * @type  {boolean}\n     * @since 10.0.0\n     */\n    showFullPath: true,\n    /**\n     * CSS styles for all breadcrumbs.\n     *\n     * In styled mode, the breadcrumbs buttons are styled by the\n     * `.highcharts-breadcrumbs-buttons .highcharts-button` rule with its\n     * different states.\n     *\n     * @type  {Highcharts.SVGAttributes}\n     * @since 10.0.0\n     */\n    style: {},\n    /**\n     * Whether to use HTML to render the breadcrumbs items texts.\n     *\n     * @type  {boolean}\n     * @since 10.0.0\n     */\n    useHTML: false,\n    /**\n     * The z index of the breadcrumbs group.\n     *\n     * @type  {number}\n     * @since 10.0.0\n     */\n    zIndex: 7\n};\n/* *\n *\n *  Default Export\n *\n * */\nconst BreadcrumbsDefaults = {\n    lang,\n    options\n};\n/* harmony default export */ const Breadcrumbs_BreadcrumbsDefaults = (BreadcrumbsDefaults);\n\n;// external [\"../highcharts.js\",\"default\",\"Templating\"]\nconst external_highcharts_src_js_default_Templating_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].Templating;\nvar external_highcharts_src_js_default_Templating_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_Templating_namespaceObject);\n;// ./code/es-modules/Extensions/Breadcrumbs/Breadcrumbs.js\n/* *\n *\n *  Highcharts Breadcrumbs module\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { format } = (external_highcharts_src_js_default_Templating_default());\n\nconst { composed } = (external_highcharts_src_js_default_default());\n\nconst { addEvent, defined, extend, fireEvent, isString, merge, objectEach, pick, pushUnique } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Shift the drillUpButton to make the space for resetZoomButton, #8095.\n * @private\n */\nfunction onChartAfterShowResetZoom() {\n    const chart = this;\n    if (chart.breadcrumbs) {\n        const bbox = chart.resetZoomButton &&\n            chart.resetZoomButton.getBBox(), breadcrumbsOptions = chart.breadcrumbs.options;\n        if (bbox &&\n            breadcrumbsOptions.position.align === 'right' &&\n            breadcrumbsOptions.relativeTo === 'plotBox') {\n            chart.breadcrumbs.alignBreadcrumbsGroup(-bbox.width - breadcrumbsOptions.buttonSpacing);\n        }\n    }\n}\n/**\n * Remove resize/afterSetExtremes at chart destroy.\n * @private\n */\nfunction onChartDestroy() {\n    if (this.breadcrumbs) {\n        this.breadcrumbs.destroy();\n        this.breadcrumbs = void 0;\n    }\n}\n/**\n * Logic for making space for the buttons above the plot area\n * @private\n */\nfunction onChartGetMargins() {\n    const breadcrumbs = this.breadcrumbs;\n    if (breadcrumbs &&\n        !breadcrumbs.options.floating &&\n        breadcrumbs.level) {\n        const breadcrumbsOptions = breadcrumbs.options, buttonTheme = breadcrumbsOptions.buttonTheme, breadcrumbsHeight = ((buttonTheme.height || 0) +\n            2 * (buttonTheme.padding || 0) +\n            breadcrumbsOptions.buttonSpacing), verticalAlign = breadcrumbsOptions.position.verticalAlign;\n        if (verticalAlign === 'bottom') {\n            this.marginBottom = (this.marginBottom || 0) + breadcrumbsHeight;\n            breadcrumbs.yOffset = breadcrumbsHeight;\n        }\n        else if (verticalAlign !== 'middle') {\n            this.plotTop += breadcrumbsHeight;\n            breadcrumbs.yOffset = -breadcrumbsHeight;\n        }\n        else {\n            breadcrumbs.yOffset = void 0;\n        }\n    }\n}\n/**\n * @private\n */\nfunction onChartRedraw() {\n    this.breadcrumbs && this.breadcrumbs.redraw();\n}\n/**\n * After zooming out, shift the drillUpButton to the previous position, #8095.\n * @private\n */\nfunction onChartSelection(event) {\n    if (event.resetSelection === true &&\n        this.breadcrumbs) {\n        this.breadcrumbs.alignBreadcrumbsGroup();\n    }\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Breadcrumbs class\n *\n * @private\n * @class\n * @name Highcharts.Breadcrumbs\n *\n * @param {Highcharts.Chart} chart\n *        Chart object\n * @param {Highcharts.Options} userOptions\n *        User options\n */\nclass Breadcrumbs {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    static compose(ChartClass, highchartsDefaultOptions) {\n        if (pushUnique(composed, 'Breadcrumbs')) {\n            addEvent(ChartClass, 'destroy', onChartDestroy);\n            addEvent(ChartClass, 'afterShowResetZoom', onChartAfterShowResetZoom);\n            addEvent(ChartClass, 'getMargins', onChartGetMargins);\n            addEvent(ChartClass, 'redraw', onChartRedraw);\n            addEvent(ChartClass, 'selection', onChartSelection);\n            // Add language support.\n            extend(highchartsDefaultOptions.lang, Breadcrumbs_BreadcrumbsDefaults.lang);\n        }\n    }\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(chart, userOptions) {\n        this.elementList = {};\n        this.isDirty = true;\n        this.level = 0;\n        this.list = [];\n        const chartOptions = merge(chart.options.drilldown &&\n            chart.options.drilldown.drillUpButton, Breadcrumbs.defaultOptions, chart.options.navigation && chart.options.navigation.breadcrumbs, userOptions);\n        this.chart = chart;\n        this.options = chartOptions || {};\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Update Breadcrumbs properties, like level and list.\n     *\n     * @function Highcharts.Breadcrumbs#updateProperties\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    updateProperties(list) {\n        this.setList(list);\n        this.setLevel();\n        this.isDirty = true;\n    }\n    /**\n     * Set breadcrumbs list.\n     * @function Highcharts.Breadcrumbs#setList\n     *\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     * @param {Highcharts.BreadcrumbsOptions} list\n     *        Breadcrumbs list.\n     */\n    setList(list) {\n        this.list = list;\n    }\n    /**\n     * Calculate level on which chart currently is.\n     *\n     * @function Highcharts.Breadcrumbs#setLevel\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    setLevel() {\n        this.level = this.list.length && this.list.length - 1;\n    }\n    /**\n     * Get Breadcrumbs level\n     *\n     * @function Highcharts.Breadcrumbs#getLevel\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    getLevel() {\n        return this.level;\n    }\n    /**\n     * Default button text formatter.\n     *\n     * @function Highcharts.Breadcrumbs#getButtonText\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     * @param {Highcharts.Breadcrumbs} breadcrumb\n     *        Breadcrumb.\n     * @return {string}\n     *         Formatted text.\n     */\n    getButtonText(breadcrumb) {\n        const breadcrumbs = this, chart = breadcrumbs.chart, breadcrumbsOptions = breadcrumbs.options, lang = chart.options.lang, textFormat = pick(breadcrumbsOptions.format, breadcrumbsOptions.showFullPath ?\n            '{level.name}' : '← {level.name}'), defaultText = lang && pick(lang.drillUpText, lang.mainBreadcrumb);\n        let returnText = breadcrumbsOptions.formatter &&\n            breadcrumbsOptions.formatter(breadcrumb) ||\n            format(textFormat, { level: breadcrumb.levelOptions }, chart) || '';\n        if (((isString(returnText) &&\n            !returnText.length) ||\n            returnText === '← ') &&\n            defined(defaultText)) {\n            returnText = !breadcrumbsOptions.showFullPath ?\n                '← ' + defaultText :\n                defaultText;\n        }\n        return returnText;\n    }\n    /**\n     * Redraw.\n     *\n     * @function Highcharts.Breadcrumbs#redraw\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    redraw() {\n        if (this.isDirty) {\n            this.render();\n        }\n        if (this.group) {\n            this.group.align();\n        }\n        this.isDirty = false;\n    }\n    /**\n     * Create a group, then draw breadcrumbs together with the separators.\n     *\n     * @function Highcharts.Breadcrumbs#render\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    render() {\n        const breadcrumbs = this, chart = breadcrumbs.chart, breadcrumbsOptions = breadcrumbs.options;\n        // A main group for the breadcrumbs.\n        if (!breadcrumbs.group && breadcrumbsOptions) {\n            breadcrumbs.group = chart.renderer\n                .g('breadcrumbs-group')\n                .addClass('highcharts-no-tooltip highcharts-breadcrumbs')\n                .attr({\n                zIndex: breadcrumbsOptions.zIndex\n            })\n                .add();\n        }\n        // Draw breadcrumbs.\n        if (breadcrumbsOptions.showFullPath) {\n            this.renderFullPathButtons();\n        }\n        else {\n            this.renderSingleButton();\n        }\n        this.alignBreadcrumbsGroup();\n    }\n    /**\n     * Draw breadcrumbs together with the separators.\n     *\n     * @function Highcharts.Breadcrumbs#renderFullPathButtons\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    renderFullPathButtons() {\n        // Make sure that only one type of button is visible.\n        this.destroySingleButton();\n        this.resetElementListState();\n        this.updateListElements();\n        this.destroyListElements();\n    }\n    /**\n     * Render Single button - when showFullPath is not used. The button is\n     * similar to the old drillUpButton\n     *\n     * @function Highcharts.Breadcrumbs#renderSingleButton\n     * @param {Highcharts.Breadcrumbs} this Breadcrumbs class.\n     */\n    renderSingleButton() {\n        const breadcrumbs = this, chart = breadcrumbs.chart, list = breadcrumbs.list, breadcrumbsOptions = breadcrumbs.options, buttonSpacing = breadcrumbsOptions.buttonSpacing;\n        // Make sure that only one type of button is visible.\n        this.destroyListElements();\n        // Draw breadcrumbs. Initial position for calculating the breadcrumbs\n        // group.\n        const posX = breadcrumbs.group ?\n            breadcrumbs.group.getBBox().width :\n            buttonSpacing, posY = buttonSpacing;\n        const previousBreadcrumb = list[list.length - 2];\n        if (!chart.drillUpButton && (this.level > 0)) {\n            chart.drillUpButton = breadcrumbs.renderButton(previousBreadcrumb, posX, posY);\n        }\n        else if (chart.drillUpButton) {\n            if (this.level > 0) {\n                // Update button.\n                this.updateSingleButton();\n            }\n            else {\n                this.destroySingleButton();\n            }\n        }\n    }\n    /**\n     * Update group position based on align and it's width.\n     *\n     * @function Highcharts.Breadcrumbs#renderSingleButton\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    alignBreadcrumbsGroup(xOffset) {\n        const breadcrumbs = this;\n        if (breadcrumbs.group) {\n            const breadcrumbsOptions = breadcrumbs.options, buttonTheme = breadcrumbsOptions.buttonTheme, positionOptions = breadcrumbsOptions.position, alignTo = (breadcrumbsOptions.relativeTo === 'chart' ||\n                breadcrumbsOptions.relativeTo === 'spacingBox' ?\n                void 0 :\n                'plotBox'), bBox = breadcrumbs.group.getBBox(), additionalSpace = 2 * (buttonTheme.padding || 0) +\n                breadcrumbsOptions.buttonSpacing;\n            // Store positionOptions\n            positionOptions.width = bBox.width + additionalSpace;\n            positionOptions.height = bBox.height + additionalSpace;\n            const newPositions = merge(positionOptions);\n            // Add x offset if specified.\n            if (xOffset) {\n                newPositions.x += xOffset;\n            }\n            if (breadcrumbs.options.rtl) {\n                newPositions.x += positionOptions.width;\n            }\n            newPositions.y = pick(newPositions.y, this.yOffset, 0);\n            breadcrumbs.group.align(newPositions, true, alignTo);\n        }\n    }\n    /**\n     * Render a button.\n     *\n     * @function Highcharts.Breadcrumbs#renderButton\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     * @param {Highcharts.Breadcrumbs} breadcrumb\n     *        Current breadcrumb\n     * @param {Highcharts.Breadcrumbs} posX\n     *        Initial horizontal position\n     * @param {Highcharts.Breadcrumbs} posY\n     *        Initial vertical position\n     * @return {SVGElement|void}\n     *        Returns the SVG button\n     */\n    renderButton(breadcrumb, posX, posY) {\n        const breadcrumbs = this, chart = this.chart, breadcrumbsOptions = breadcrumbs.options, buttonTheme = merge(breadcrumbsOptions.buttonTheme);\n        const button = chart.renderer\n            .button(breadcrumbs.getButtonText(breadcrumb), posX, posY, function (e) {\n            // Extract events from button object and call\n            const buttonEvents = breadcrumbsOptions.events &&\n                breadcrumbsOptions.events.click;\n            let callDefaultEvent;\n            if (buttonEvents) {\n                callDefaultEvent = buttonEvents.call(breadcrumbs, e, breadcrumb);\n            }\n            // (difference in behaviour of showFullPath and drillUp)\n            if (callDefaultEvent !== false) {\n                // For single button we are not going to the button\n                // level, but the one level up\n                if (!breadcrumbsOptions.showFullPath) {\n                    e.newLevel = breadcrumbs.level - 1;\n                }\n                else {\n                    e.newLevel = breadcrumb.level;\n                }\n                fireEvent(breadcrumbs, 'up', e);\n            }\n        }, buttonTheme)\n            .addClass('highcharts-breadcrumbs-button')\n            .add(breadcrumbs.group);\n        if (!chart.styledMode) {\n            button.attr(breadcrumbsOptions.style);\n        }\n        return button;\n    }\n    /**\n     * Render a separator.\n     *\n     * @function Highcharts.Breadcrumbs#renderSeparator\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     * @param {Highcharts.Breadcrumbs} posX\n     *        Initial horizontal position\n     * @param {Highcharts.Breadcrumbs} posY\n     *        Initial vertical position\n     * @return {Highcharts.SVGElement}\n     *        Returns the SVG button\n     */\n    renderSeparator(posX, posY) {\n        const breadcrumbs = this, chart = this.chart, breadcrumbsOptions = breadcrumbs.options, separatorOptions = breadcrumbsOptions.separator;\n        const separator = chart.renderer\n            .label(separatorOptions.text, posX, posY, void 0, void 0, void 0, false)\n            .addClass('highcharts-breadcrumbs-separator')\n            .add(breadcrumbs.group);\n        if (!chart.styledMode) {\n            separator.css(separatorOptions.style);\n        }\n        return separator;\n    }\n    /**\n     * Update.\n     * @function Highcharts.Breadcrumbs#update\n     *\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     * @param {Highcharts.BreadcrumbsOptions} options\n     *        Breadcrumbs class.\n     * @param {boolean} redraw\n     *        Redraw flag\n     */\n    update(options) {\n        merge(true, this.options, options);\n        this.destroy();\n        this.isDirty = true;\n    }\n    /**\n     * Update button text when the showFullPath set to false.\n     * @function Highcharts.Breadcrumbs#updateSingleButton\n     *\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    updateSingleButton() {\n        const chart = this.chart, currentBreadcrumb = this.list[this.level - 1];\n        if (chart.drillUpButton) {\n            chart.drillUpButton.attr({\n                text: this.getButtonText(currentBreadcrumb)\n            });\n        }\n    }\n    /**\n     * Destroy the chosen breadcrumbs group\n     *\n     * @function Highcharts.Breadcrumbs#destroy\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    destroy() {\n        this.destroySingleButton();\n        // Destroy elements one by one. It's necessary because\n        // g().destroy() does not remove added HTML\n        this.destroyListElements(true);\n        // Then, destroy the group itself.\n        if (this.group) {\n            this.group.destroy();\n        }\n        this.group = void 0;\n    }\n    /**\n     * Destroy the elements' buttons and separators.\n     *\n     * @function Highcharts.Breadcrumbs#destroyListElements\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    destroyListElements(force) {\n        const elementList = this.elementList;\n        objectEach(elementList, (element, level) => {\n            if (force ||\n                !elementList[level].updated) {\n                element = elementList[level];\n                element.button && element.button.destroy();\n                element.separator && element.separator.destroy();\n                delete element.button;\n                delete element.separator;\n                delete elementList[level];\n            }\n        });\n        if (force) {\n            this.elementList = {};\n        }\n    }\n    /**\n     * Destroy the single button if exists.\n     *\n     * @function Highcharts.Breadcrumbs#destroySingleButton\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    destroySingleButton() {\n        if (this.chart.drillUpButton) {\n            this.chart.drillUpButton.destroy();\n            this.chart.drillUpButton = void 0;\n        }\n    }\n    /**\n     * Reset state for all buttons in elementList.\n     *\n     * @function Highcharts.Breadcrumbs#resetElementListState\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    resetElementListState() {\n        objectEach(this.elementList, (element) => {\n            element.updated = false;\n        });\n    }\n    /**\n     * Update rendered elements inside the elementList.\n     *\n     * @function Highcharts.Breadcrumbs#updateListElements\n     *\n     * @param {Highcharts.Breadcrumbs} this\n     *        Breadcrumbs class.\n     */\n    updateListElements() {\n        const breadcrumbs = this, elementList = breadcrumbs.elementList, buttonSpacing = breadcrumbs.options.buttonSpacing, posY = buttonSpacing, list = breadcrumbs.list, rtl = breadcrumbs.options.rtl, rtlFactor = rtl ? -1 : 1, updateXPosition = function (element, spacing) {\n            return rtlFactor * element.getBBox().width +\n                rtlFactor * spacing;\n        }, adjustToRTL = function (element, posX, posY) {\n            element.translate(posX - element.getBBox().width, posY);\n        };\n        // Initial position for calculating the breadcrumbs group.\n        let posX = breadcrumbs.group ?\n            updateXPosition(breadcrumbs.group, buttonSpacing) :\n            buttonSpacing, currentBreadcrumb, breadcrumb;\n        for (let i = 0, iEnd = list.length; i < iEnd; ++i) {\n            const isLast = i === iEnd - 1;\n            let button, separator;\n            breadcrumb = list[i];\n            if (elementList[breadcrumb.level]) {\n                currentBreadcrumb = elementList[breadcrumb.level];\n                button = currentBreadcrumb.button;\n                // Render a separator if it was not created before.\n                if (!currentBreadcrumb.separator &&\n                    !isLast) {\n                    // Add spacing for the next separator\n                    posX += rtlFactor * buttonSpacing;\n                    currentBreadcrumb.separator =\n                        breadcrumbs.renderSeparator(posX, posY);\n                    if (rtl) {\n                        adjustToRTL(currentBreadcrumb.separator, posX, posY);\n                    }\n                    posX += updateXPosition(currentBreadcrumb.separator, buttonSpacing);\n                }\n                else if (currentBreadcrumb.separator &&\n                    isLast) {\n                    currentBreadcrumb.separator.destroy();\n                    delete currentBreadcrumb.separator;\n                }\n                elementList[breadcrumb.level].updated = true;\n            }\n            else {\n                // Render a button.\n                button = breadcrumbs.renderButton(breadcrumb, posX, posY);\n                if (rtl) {\n                    adjustToRTL(button, posX, posY);\n                }\n                posX += updateXPosition(button, buttonSpacing);\n                // Render a separator.\n                if (!isLast) {\n                    separator = breadcrumbs.renderSeparator(posX, posY);\n                    if (rtl) {\n                        adjustToRTL(separator, posX, posY);\n                    }\n                    posX += updateXPosition(separator, buttonSpacing);\n                }\n                elementList[breadcrumb.level] = {\n                    button,\n                    separator,\n                    updated: true\n                };\n            }\n            if (button) {\n                button.setState(isLast ? 2 : 0);\n            }\n        }\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nBreadcrumbs.defaultOptions = Breadcrumbs_BreadcrumbsDefaults.options;\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Breadcrumbs_Breadcrumbs = (Breadcrumbs);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Callback function to react on button clicks.\n *\n * @callback Highcharts.BreadcrumbsClickCallbackFunction\n *\n * @param {Highcharts.Event} event\n * Event.\n *\n * @param {Highcharts.BreadcrumbOptions} options\n * Breadcrumb options.\n *\n * @param {global.Event} e\n * Event arguments.\n */\n/**\n * Callback function to format the breadcrumb text from scratch.\n *\n * @callback Highcharts.BreadcrumbsFormatterCallbackFunction\n *\n * @param {Highcharts.BreadcrumbOptions} options\n * Breadcrumb options.\n *\n * @return {string}\n * Formatted text or false\n */\n/**\n * Options for the one breadcrumb.\n *\n * @interface Highcharts.BreadcrumbOptions\n */\n/**\n * Level connected to a specific breadcrumb.\n * @name Highcharts.BreadcrumbOptions#level\n * @type {number}\n */\n/**\n * Options for series or point connected to a specific breadcrumb.\n * @name Highcharts.BreadcrumbOptions#levelOptions\n * @type {SeriesOptions|PointOptionsObject}\n */\n/**\n * Options for aligning breadcrumbs group.\n *\n * @interface Highcharts.BreadcrumbsAlignOptions\n */\n/**\n * Align of a Breadcrumb group.\n * @default right\n * @name Highcharts.BreadcrumbsAlignOptions#align\n * @type {AlignValue}\n */\n/**\n * Vertical align of a Breadcrumb group.\n * @default top\n * @name Highcharts.BreadcrumbsAlignOptions#verticalAlign\n * @type {VerticalAlignValue}\n */\n/**\n * X offset of a Breadcrumbs group.\n * @name Highcharts.BreadcrumbsAlignOptions#x\n * @type {number}\n */\n/**\n * Y offset of a Breadcrumbs group.\n * @name Highcharts.BreadcrumbsAlignOptions#y\n * @type {number}\n */\n/**\n * Options for all breadcrumbs.\n *\n * @interface Highcharts.BreadcrumbsOptions\n */\n/**\n * Button theme.\n * @name Highcharts.BreadcrumbsOptions#buttonTheme\n * @type { SVGAttributes | undefined }\n */\n(''); // Keeps doclets above in JS file\n\n;// external [\"../highcharts.js\",\"default\",\"Color\"]\nconst external_highcharts_src_js_default_Color_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].Color;\nvar external_highcharts_src_js_default_Color_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_Color_namespaceObject);\n;// external [\"../highcharts.js\",\"default\",\"SeriesRegistry\"]\nconst external_highcharts_src_js_default_SeriesRegistry_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].SeriesRegistry;\nvar external_highcharts_src_js_default_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_SeriesRegistry_namespaceObject);\n;// external [\"../highcharts.js\",\"default\",\"SVGElement\"]\nconst external_highcharts_src_js_default_SVGElement_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].SVGElement;\nvar external_highcharts_src_js_default_SVGElement_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_SVGElement_namespaceObject);\n;// ./code/es-modules/Series/ColorMapComposition.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { column: { prototype: columnProto } } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\n\nconst { addEvent: ColorMapComposition_addEvent, defined: ColorMapComposition_defined } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Composition\n *\n * */\nvar ColorMapComposition;\n(function (ColorMapComposition) {\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    ColorMapComposition.pointMembers = {\n        dataLabelOnNull: true,\n        moveToTopOnHover: true,\n        isValid: pointIsValid\n    };\n    ColorMapComposition.seriesMembers = {\n        colorKey: 'value',\n        axisTypes: ['xAxis', 'yAxis', 'colorAxis'],\n        parallelArrays: ['x', 'y', 'value'],\n        pointArrayMap: ['value'],\n        trackerGroups: ['group', 'markerGroup', 'dataLabelsGroup'],\n        colorAttribs: seriesColorAttribs,\n        pointAttribs: columnProto.pointAttribs\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    function compose(SeriesClass) {\n        const PointClass = SeriesClass.prototype.pointClass;\n        ColorMapComposition_addEvent(PointClass, 'afterSetState', onPointAfterSetState);\n        return SeriesClass;\n    }\n    ColorMapComposition.compose = compose;\n    /**\n     * Move points to the top of the z-index order when hovered.\n     * @private\n     */\n    function onPointAfterSetState(e) {\n        const point = this, series = point.series, renderer = series.chart.renderer;\n        if (point.moveToTopOnHover && point.graphic) {\n            if (!series.stateMarkerGraphic) {\n                // Create a `use` element and add it to the end of the group,\n                // which would make it appear on top of the other elements. This\n                // deals with z-index without reordering DOM elements (#13049).\n                series.stateMarkerGraphic = new (external_highcharts_src_js_default_SVGElement_default())(renderer, 'use')\n                    .css({\n                    pointerEvents: 'none'\n                })\n                    .add(point.graphic.parentGroup);\n            }\n            if (e?.state === 'hover') {\n                // Give the graphic DOM element the same id as the Point\n                // instance\n                point.graphic.attr({\n                    id: this.id\n                });\n                series.stateMarkerGraphic.attr({\n                    href: `${renderer.url}#${this.id}`,\n                    visibility: 'visible'\n                });\n            }\n            else {\n                series.stateMarkerGraphic.attr({\n                    href: ''\n                });\n            }\n        }\n    }\n    /**\n     * Color points have a value option that determines whether or not it is\n     * a null point\n     * @private\n     */\n    function pointIsValid() {\n        return (this.value !== null &&\n            this.value !== Infinity &&\n            this.value !== -Infinity &&\n            // Undefined is allowed, but NaN is not (#17279)\n            (this.value === void 0 || !isNaN(this.value)));\n    }\n    /**\n     * Get the color attributes to apply on the graphic\n     * @private\n     * @function Highcharts.colorMapSeriesMixin.colorAttribs\n     * @param {Highcharts.Point} point\n     * @return {Highcharts.SVGAttributes}\n     *         The SVG attributes\n     */\n    function seriesColorAttribs(point) {\n        const ret = {};\n        if (ColorMapComposition_defined(point.color) &&\n            (!point.state || point.state === 'normal') // #15746\n        ) {\n            ret[this.colorProp || 'fill'] = point.color;\n        }\n        return ret;\n    }\n})(ColorMapComposition || (ColorMapComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Series_ColorMapComposition = (ColorMapComposition);\n\n;// external [\"../highcharts.js\",\"default\",\"Series\"]\nconst external_highcharts_src_js_default_Series_namespaceObject = __WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__[\"default\"].Series;\nvar external_highcharts_src_js_default_Series_default = /*#__PURE__*/__webpack_require__.n(external_highcharts_src_js_default_Series_namespaceObject);\n;// ./code/es-modules/Series/Treemap/TreemapAlgorithmGroup.js\n/* *\n *\n *  (c) 2014-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Class\n *\n * */\nclass TreemapAlgorithmGroup {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    constructor(h, w, d, p) {\n        this.height = h;\n        this.width = w;\n        this.plot = p;\n        this.direction = d;\n        this.startDirection = d;\n        this.total = 0;\n        this.nW = 0;\n        this.lW = 0;\n        this.nH = 0;\n        this.lH = 0;\n        this.elArr = [];\n        this.lP = {\n            total: 0,\n            lH: 0,\n            nH: 0,\n            lW: 0,\n            nW: 0,\n            nR: 0,\n            lR: 0,\n            aspectRatio: function (w, h) {\n                return Math.max((w / h), (h / w));\n            }\n        };\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    addElement(el) {\n        this.lP.total = this.elArr[this.elArr.length - 1];\n        this.total = this.total + el;\n        if (this.direction === 0) {\n            // Calculate last point old aspect ratio\n            this.lW = this.nW;\n            this.lP.lH = this.lP.total / this.lW;\n            this.lP.lR = this.lP.aspectRatio(this.lW, this.lP.lH);\n            // Calculate last point new aspect ratio\n            this.nW = this.total / this.height;\n            this.lP.nH = this.lP.total / this.nW;\n            this.lP.nR = this.lP.aspectRatio(this.nW, this.lP.nH);\n        }\n        else {\n            // Calculate last point old aspect ratio\n            this.lH = this.nH;\n            this.lP.lW = this.lP.total / this.lH;\n            this.lP.lR = this.lP.aspectRatio(this.lP.lW, this.lH);\n            // Calculate last point new aspect ratio\n            this.nH = this.total / this.width;\n            this.lP.nW = this.lP.total / this.nH;\n            this.lP.nR = this.lP.aspectRatio(this.lP.nW, this.nH);\n        }\n        this.elArr.push(el);\n    }\n    reset() {\n        this.nW = 0;\n        this.lW = 0;\n        this.elArr = [];\n        this.total = 0;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Treemap_TreemapAlgorithmGroup = (TreemapAlgorithmGroup);\n\n;// ./code/es-modules/Series/Treemap/TreemapNode.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Class\n *\n * */\nclass TreemapNode {\n    constructor() {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.childrenTotal = 0;\n        this.visible = false;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    init(id, i, children, height, level, series, parent) {\n        this.id = id;\n        this.i = i;\n        this.children = children;\n        this.height = height;\n        this.level = level;\n        this.series = series;\n        this.parent = parent;\n        return this;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Treemap_TreemapNode = (TreemapNode);\n\n;// ./code/es-modules/Series/DrawPointUtilities.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Handles the drawing of a component.\n * Can be used for any type of component that reserves the graphic property,\n * and provides a shouldDraw on its context.\n *\n * @private\n *\n * @todo add type checking.\n * @todo export this function to enable usage\n */\nfunction draw(point, params) {\n    const { animatableAttribs, onComplete, css, renderer } = params;\n    const animation = (point.series && point.series.chart.hasRendered) ?\n        // Chart-level animation on updates\n        void 0 :\n        // Series-level animation on new points\n        (point.series &&\n            point.series.options.animation);\n    let graphic = point.graphic;\n    params.attribs = {\n        ...params.attribs,\n        'class': point.getClassName()\n    } || {};\n    if ((point.shouldDraw())) {\n        if (!graphic) {\n            if (params.shapeType === 'text') {\n                graphic = renderer.text();\n            }\n            else if (params.shapeType === 'image') {\n                graphic = renderer.image(params.imageUrl || '')\n                    .attr(params.shapeArgs || {});\n            }\n            else {\n                graphic = renderer[params.shapeType](params.shapeArgs || {});\n            }\n            point.graphic = graphic;\n            graphic.add(params.group);\n        }\n        if (css) {\n            graphic.css(css);\n        }\n        graphic\n            .attr(params.attribs)\n            .animate(animatableAttribs, params.isNew ? false : animation, onComplete);\n    }\n    else if (graphic) {\n        const destroy = () => {\n            point.graphic = graphic = (graphic && graphic.destroy());\n            if (typeof onComplete === 'function') {\n                onComplete();\n            }\n        };\n        // Animate only runs complete callback if something was animated.\n        if (Object.keys(animatableAttribs).length) {\n            graphic.animate(animatableAttribs, void 0, () => destroy());\n        }\n        else {\n            destroy();\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst DrawPointUtilities = {\n    draw\n};\n/* harmony default export */ const Series_DrawPointUtilities = (DrawPointUtilities);\n\n;// ./code/es-modules/Series/Treemap/TreemapPoint.js\n/* *\n *\n *  (c) 2014-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { pie: { prototype: { pointClass: PiePoint } }, scatter: { prototype: { pointClass: ScatterPoint } } } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: TreemapPoint_extend, isNumber, pick: TreemapPoint_pick } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\nclass TreemapPoint extends ScatterPoint {\n    constructor() {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        super(...arguments);\n        this.groupedPointsAmount = 0;\n        this.shapeType = 'rect';\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    draw(params) {\n        Series_DrawPointUtilities.draw(this, params);\n    }\n    getClassName() {\n        const series = this.series, options = series.options;\n        let className = super.getClassName();\n        // Above the current level\n        if (this.node.level <= series.nodeMap[series.rootNode].level &&\n            this.node.children.length) {\n            className += ' highcharts-above-level';\n        }\n        else if (!this.node.isGroup &&\n            !this.node.isLeaf &&\n            !series.nodeMap[series.rootNode].isGroup &&\n            !TreemapPoint_pick(options.interactByLeaf, !options.allowTraversingTree)) {\n            className += ' highcharts-internal-node-interactive';\n        }\n        else if (!this.node.isGroup &&\n            !this.node.isLeaf &&\n            !series.nodeMap[series.rootNode].isGroup) {\n            className += ' highcharts-internal-node';\n        }\n        return className;\n    }\n    /**\n     * A tree point is valid if it has han id too, assume it may be a parent\n     * item.\n     *\n     * @private\n     * @function Highcharts.Point#isValid\n     */\n    isValid() {\n        return Boolean(this.id || isNumber(this.value));\n    }\n    setState(state) {\n        super.setState.apply(this, arguments);\n        // Graphic does not exist when point is not visible.\n        if (this.graphic) {\n            this.graphic.attr({\n                zIndex: state === 'hover' ? 1 : 0\n            });\n        }\n    }\n    shouldDraw() {\n        return isNumber(this.plotY) && this.y !== null;\n    }\n}\nTreemapPoint_extend(TreemapPoint.prototype, {\n    setVisible: PiePoint.prototype.setVisible\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Treemap_TreemapPoint = (TreemapPoint);\n\n;// ./code/es-modules/Series/Treemap/TreemapSeriesDefaults.js\n/* *\n *\n *  (c) 2014-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { isString: TreemapSeriesDefaults_isString } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A treemap displays hierarchical data using nested rectangles. The data\n * can be laid out in varying ways depending on options.\n *\n * @sample highcharts/demo/treemap-large-dataset/\n *         Treemap\n *\n * @extends      plotOptions.scatter\n * @excluding    connectEnds, connectNulls, dataSorting, dragDrop, jitter, marker\n * @product      highcharts\n * @requires     modules/treemap\n * @optionparent plotOptions.treemap\n */\nconst TreemapSeriesDefaults = {\n    /**\n     * When enabled the user can click on a point which is a parent and\n     * zoom in on its children. Deprecated and replaced by\n     * [allowTraversingTree](#plotOptions.treemap.allowTraversingTree).\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-allowdrilltonode/\n     *         Enabled\n     *\n     * @deprecated\n     * @type      {boolean}\n     * @default   false\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.allowDrillToNode\n     */\n    /**\n     * When enabled the user can click on a point which is a parent and\n     * zoom in on its children.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-allowtraversingtree/\n     *         Enabled\n     * @sample {highcharts} highcharts/plotoptions/treemap-grouping-traversing/\n     *         Traversing to Grouped Points node\n     *\n     * @since     7.0.3\n     * @product   highcharts\n     */\n    allowTraversingTree: false,\n    animationLimit: 250,\n    /**\n     * The border radius for each treemap item.\n     */\n    borderRadius: 0,\n    /**\n     * Options for the breadcrumbs, the navigation at the top leading the\n     * way up through the traversed levels.\n     *\n     *\n     * @since 10.0.0\n     * @product   highcharts\n     * @extends   navigation.breadcrumbs\n     * @apioption plotOptions.treemap.breadcrumbs\n     */\n    /**\n     * When the series contains less points than the crop threshold, all\n     * points are drawn, event if the points fall outside the visible plot\n     * area at the current zoom. The advantage of drawing all points\n     * (including markers and columns), is that animation is performed on\n     * updates. On the other hand, when the series contains more points than\n     * the crop threshold, the series data is cropped to only contain points\n     * that fall within the plot area. The advantage of cropping away\n     * invisible points is to increase performance on large series.\n     *\n     * @type      {number}\n     * @default   300\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.cropThreshold\n     */\n    /**\n     * Fires on a request for change of root node for the tree, before the\n     * update is made. An event object is passed to the function, containing\n     * additional properties `newRootId`, `previousRootId`, `redraw` and\n     * `trigger`.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-events-setrootnode/\n     *         Alert update information on setRootNode event.\n     *\n     * @type {Function}\n     * @default undefined\n     * @since 7.0.3\n     * @product highcharts\n     * @apioption plotOptions.treemap.events.setRootNode\n     */\n    /**\n     * This option decides if the user can interact with the parent nodes\n     * or just the leaf nodes. When this option is undefined, it will be\n     * true by default. However when allowTraversingTree is true, then it\n     * will be false by default.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-interactbyleaf-false/\n     *         False\n     * @sample {highcharts} highcharts/plotoptions/treemap-interactbyleaf-true-and-allowtraversingtree/\n     *         InteractByLeaf and allowTraversingTree is true\n     *\n     * @type      {boolean}\n     * @since     4.1.2\n     * @product   highcharts\n     * @apioption plotOptions.treemap.interactByLeaf\n     */\n    /**\n     * The sort index of the point inside the treemap level.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-sortindex/\n     *         Sort by years\n     *\n     * @type      {number}\n     * @since     4.1.10\n     * @product   highcharts\n     * @apioption plotOptions.treemap.sortIndex\n     */\n    /**\n     * A series specific or series type specific color set to apply instead\n     * of the global [colors](#colors) when\n     * [colorByPoint](#plotOptions.treemap.colorByPoint) is true.\n     *\n     * @type      {Array<Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject>}\n     * @since     3.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.colors\n     */\n    /**\n     * Whether to display this series type or specific series item in the\n     * legend.\n     */\n    showInLegend: false,\n    /**\n     * @ignore-option\n     */\n    marker: void 0,\n    /**\n     * When using automatic point colors pulled from the `options.colors`\n     * collection, this option determines whether the chart should receive\n     * one color per series or one color per point.\n     *\n     * @see [series colors](#plotOptions.treemap.colors)\n     *\n     * @since     2.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.colorByPoint\n     */\n    colorByPoint: false,\n    /**\n     * @since 4.1.0\n     */\n    dataLabels: {\n        enabled: true,\n        formatter: function () {\n            const point = this && this.point ?\n                this.point :\n                {}, name = TreemapSeriesDefaults_isString(point.name) ? point.name : '';\n            return name;\n        },\n        /**\n         * Whether the data label should act as a group-level header. For leaf\n         * nodes, headers are not supported and the data label will be rendered\n         * inside.\n         *\n         * @sample {highcharts} highcharts/series-treemap/headers\n         *         Headers for parent nodes\n         *\n         * @since 12.2.0\n         */\n        headers: false,\n        inside: true,\n        padding: 2,\n        verticalAlign: 'middle',\n        style: {\n            textOverflow: 'ellipsis'\n        }\n    },\n    tooltip: {\n        headerFormat: '',\n        pointFormat: '<b>{point.name}</b>: {point.value}<br/>',\n        /**\n         * The HTML of the grouped point's nodes in the tooltip. Works only for\n         * Treemap series grouping and analogously to\n         * [pointFormat](#tooltip.pointFormat).\n         *\n         * The grouped nodes point tooltip can be also formatted using\n         * `tooltip.formatter` callback function and `point.isGroupNode` flag.\n         *\n         * @type      {string}\n         * @default   '+ {point.groupedPointsAmount} more...'\n         * @apioption tooltip.clusterFormat\n         */\n        clusterFormat: '+ {point.groupedPointsAmount} more...<br/>'\n    },\n    /**\n     * Whether to ignore hidden points when the layout algorithm runs.\n     * If `false`, hidden points will leave open spaces.\n     *\n     * @since 5.0.8\n     */\n    ignoreHiddenPoint: true,\n    /**\n     * This option decides which algorithm is used for setting position\n     * and dimensions of the points.\n     *\n     * @see [How to write your own algorithm](https://www.highcharts.com/docs/chart-and-series-types/treemap)\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-layoutalgorithm-sliceanddice/\n     *         SliceAndDice by default\n     * @sample {highcharts} highcharts/plotoptions/treemap-layoutalgorithm-stripes/\n     *         Stripes\n     * @sample {highcharts} highcharts/plotoptions/treemap-layoutalgorithm-squarified/\n     *         Squarified\n     * @sample {highcharts} highcharts/plotoptions/treemap-layoutalgorithm-strip/\n     *         Strip\n     *\n     * @since      4.1.0\n     * @validvalue [\"sliceAndDice\", \"stripes\", \"squarified\", \"strip\"]\n     */\n    layoutAlgorithm: 'sliceAndDice',\n    /**\n     * Defines which direction the layout algorithm will start drawing.\n     *\n     * @since       4.1.0\n     * @validvalue [\"vertical\", \"horizontal\"]\n     */\n    layoutStartingDirection: 'vertical',\n    /**\n     * Enabling this option will make the treemap alternate the drawing\n     * direction between vertical and horizontal. The next levels starting\n     * direction will always be the opposite of the previous.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-alternatestartingdirection-true/\n     *         Enabled\n     *\n     * @since 4.1.0\n     */\n    alternateStartingDirection: false,\n    /**\n     * Used together with the levels and allowTraversingTree options. When\n     * set to false the first level visible to be level one, which is\n     * dynamic when traversing the tree. Otherwise the level will be the\n     * same as the tree structure.\n     *\n     * @since 4.1.0\n     */\n    levelIsConstant: true,\n    /**\n     * Options for the button appearing when traversing down in a treemap.\n     *\n     * Since v9.3.3 the `traverseUpButton` is replaced by `breadcrumbs`.\n     *\n     * @deprecated\n     */\n    traverseUpButton: {\n        /**\n         * The position of the button.\n         */\n        position: {\n            /**\n             * Vertical alignment of the button.\n             *\n             * @type      {Highcharts.VerticalAlignValue}\n             * @default   top\n             * @product   highcharts\n             * @apioption plotOptions.treemap.traverseUpButton.position.verticalAlign\n             */\n            /**\n             * Horizontal alignment of the button.\n             *\n             * @type {Highcharts.AlignValue}\n             */\n            align: 'right',\n            /**\n             * Horizontal offset of the button.\n             */\n            x: -10,\n            /**\n             * Vertical offset of the button.\n             */\n            y: 10\n        }\n    },\n    /**\n     * Group padding for parent elements in terms of pixels. See also the\n     * `nodeSizeBy` option that controls how the leaf nodes' size is affected by\n     * the padding.\n     *\n     * @sample    {highcharts} highcharts/series-treemap/grouppadding/\n     *            Group padding\n     * @type      {number}\n     * @since 12.2.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.groupPadding\n     */\n    /**\n     * Set options on specific levels. Takes precedence over series options,\n     * but not point options.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-levels/\n     *         Styling dataLabels and borders\n     * @sample {highcharts} highcharts/demo/treemap-with-levels/\n     *         Different layoutAlgorithm\n     *\n     * @type      {Array<*>}\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels\n     */\n    /**\n     * Experimental. How to set the size of child nodes when a header or padding\n     * is present. When `leaf`, the group is expanded to make room for headers\n     * and padding in order to preserve the relative sizes between leaves. When\n     * `group`, the leaves are naïvely fit into the remaining area after the\n     * header and padding are subtracted.\n     *\n     * @sample    {highcharts} highcharts/series-treemap/nodesizeby/\n     *            Node sizing\n     * @since 12.2.0\n     * @type      {string}\n     * @validvalue [\"group\", \"leaf\"]\n     * @default   group\n     * @apioption plotOptions.treemap.nodeSizeBy\n     */\n    /**\n     * Can set a `borderColor` on all points which lies on the same level.\n     *\n     * @type      {Highcharts.ColorString}\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels.borderColor\n     */\n    /**\n     * Set the dash style of the border of all the point which lies on the\n     * level. See\n     * [plotOptions.scatter.dashStyle](#plotoptions.scatter.dashstyle)\n     * for possible options.\n     *\n     * @type      {Highcharts.DashStyleValue}\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels.borderDashStyle\n     */\n    /**\n     * Can set the borderWidth on all points which lies on the same level.\n     *\n     * @type      {number}\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels.borderWidth\n     */\n    /**\n     * Can set a color on all points which lies on the same level.\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels.color\n     */\n    /**\n     * A configuration object to define how the color of a child varies from\n     * the parent's color. The variation is distributed among the children\n     * of node. For example when setting brightness, the brightness change\n     * will range from the parent's original brightness on the first child,\n     * to the amount set in the `to` setting on the last node. This allows a\n     * gradient-like color scheme that sets children out from each other\n     * while highlighting the grouping on treemaps and sectors on sunburst\n     * charts.\n     *\n     * @sample highcharts/demo/sunburst/\n     *         Sunburst with color variation\n     *\n     * @sample highcharts/series-treegraph/color-variation\n     *         Treegraph nodes with color variation\n     *\n     * @since     6.0.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels.colorVariation\n     */\n    /**\n     * The key of a color variation. Currently supports `brightness` only.\n     *\n     * @type       {string}\n     * @since      6.0.0\n     * @product    highcharts\n     * @validvalue [\"brightness\"]\n     * @apioption  plotOptions.treemap.levels.colorVariation.key\n     */\n    /**\n     * The ending value of a color variation. The last sibling will receive\n     * this value.\n     *\n     * @type      {number}\n     * @since     6.0.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels.colorVariation.to\n     */\n    /**\n     * Can set the options of dataLabels on each point which lies on the\n     * level.\n     * [plotOptions.treemap.dataLabels](#plotOptions.treemap.dataLabels) for\n     * possible values.\n     *\n     * @extends   plotOptions.treemap.dataLabels\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels.dataLabels\n     */\n    /**\n     * Can set the layoutAlgorithm option on a specific level.\n     *\n     * @type       {string}\n     * @since      4.1.0\n     * @product    highcharts\n     * @validvalue [\"sliceAndDice\", \"stripes\", \"squarified\", \"strip\"]\n     * @apioption  plotOptions.treemap.levels.layoutAlgorithm\n     */\n    /**\n     * Can set the layoutStartingDirection option on a specific level.\n     *\n     * @type       {string}\n     * @since      4.1.0\n     * @product    highcharts\n     * @validvalue [\"vertical\", \"horizontal\"]\n     * @apioption  plotOptions.treemap.levels.layoutStartingDirection\n     */\n    /**\n     * Decides which level takes effect from the options set in the levels\n     * object.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-levels/\n     *         Styling of both levels\n     *\n     * @type      {number}\n     * @since     4.1.0\n     * @product   highcharts\n     * @apioption plotOptions.treemap.levels.level\n     */\n    // Presentational options\n    /**\n     * The color of the border surrounding each tree map item.\n     *\n     * @type {Highcharts.ColorString}\n     */\n    borderColor: \"#e6e6e6\" /* Palette.neutralColor10 */,\n    /**\n     * The width of the border surrounding each tree map item.\n     */\n    borderWidth: 1,\n    colorKey: 'colorValue',\n    /**\n     * The opacity of grouped points in treemap. When a point has children, the\n     * group point is covering the children, and is given this opacity. The\n     * visibility of the children is determined by the opacity.\n     *\n     * @since 4.2.4\n     */\n    opacity: 0.15,\n    /**\n     * A wrapper object for all the series options in specific states.\n     *\n     * @extends plotOptions.heatmap.states\n     */\n    states: {\n        /**\n         * Options for the hovered series\n         *\n         * @extends   plotOptions.heatmap.states.hover\n         * @excluding halo\n         */\n        hover: {\n            /**\n             * The border color for the hovered state.\n             */\n            borderColor: \"#999999\" /* Palette.neutralColor40 */,\n            /**\n             * Brightness for the hovered point. Defaults to 0 if the\n             * heatmap series is loaded first, otherwise 0.1.\n             *\n             * @type    {number}\n             * @default undefined\n             */\n            brightness: (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes.heatmap ? 0 : 0.1,\n            /**\n             * @extends plotOptions.heatmap.states.hover.halo\n             */\n            halo: false,\n            /**\n             * The opacity of a point in treemap. When a point has children,\n             * the visibility of the children is determined by the opacity.\n             *\n             * @since 4.2.4\n             */\n            opacity: 0.75,\n            /**\n             * The shadow option for hovered state.\n             */\n            shadow: false\n        }\n    },\n    legendSymbol: 'rectangle',\n    /**\n     * This option enables automatic traversing to the last child level upon\n     * node interaction. This feature simplifies navigation by immediately\n     * focusing on the deepest layer of the data structure without intermediate\n     * steps.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-traverse-to-leaf/\n     *         Traverse to leaf enabled\n     *\n     * @since   11.4.4\n     *\n     * @product highcharts\n     */\n    traverseToLeaf: false,\n    /**\n     * An option to optimize treemap series rendering by grouping smaller leaf\n     * nodes below a certain square area threshold in pixels. If the square area\n     * of a point becomes smaller than the specified threshold, determined by\n     * the `pixelWidth` and/or `pixelHeight` options, then this point is moved\n     * into one group point per series.\n     *\n     * @sample {highcharts} highcharts/plotoptions/treemap-grouping-simple\n     *         Simple demo of Treemap grouping\n     * @sample {highcharts} highcharts/plotoptions/treemap-grouping-multiple-parents\n     *         Treemap grouping with multiple parents\n     * @sample {highcharts} highcharts/plotoptions/treemap-grouping-advanced\n     *         Advanced demo of Treemap grouping\n     *\n     * @since 12.1.0\n     *\n     * @excluding allowOverlap, animation, dataLabels, drillToCluster, events,\n     * layoutAlgorithm, marker, states, zones\n     *\n     * @product highcharts\n     */\n    cluster: {\n        /**\n         * An additional, individual class name for the grouped point's graphic\n         * representation.\n         *\n         * @type      string\n         * @product   highcharts\n         */\n        className: void 0,\n        /**\n         * Individual color for the grouped point. By default the color is\n         * pulled from the parent color.\n         *\n         * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         * @product   highcharts\n         */\n        color: void 0,\n        /**\n         * Enable or disable Treemap grouping.\n         *\n         * @type {boolean}\n         * @since 12.1.0\n         * @product highcharts\n         */\n        enabled: false,\n        /**\n         * The pixel threshold width of area, which is used in Treemap grouping.\n         *\n         * @type {number}\n         * @since 12.1.0\n         * @product highcharts\n         */\n        pixelWidth: void 0,\n        /**\n         * The pixel threshold height of area, which is used in Treemap\n         * grouping.\n         *\n         * @type {number}\n         * @since 12.1.0\n         * @product highcharts\n         */\n        pixelHeight: void 0,\n        /**\n         * The name of the point of grouped nodes shown in the tooltip,\n         * dataLabels, etc. By default it is set to '+ n', where n is number of\n         * grouped points.\n         *\n         * @type {string}\n         * @since 12.1.0\n         * @product highcharts\n         */\n        name: void 0,\n        /**\n         * A configuration property that specifies the factor by which the value\n         * and size of a grouped node are reduced. This can be particularly\n         * useful when a grouped node occupies a disproportionately large\n         * portion of the graph, ensuring better visual balance and readability.\n         *\n         * @type {number}\n         * @since 12.1.0\n         * @product highcharts\n         */\n        reductionFactor: void 0,\n        /**\n         * Defines the minimum number of child nodes required to create a group\n         * of small nodes.\n         *\n         * @type {number}\n         * @since 12.1.0\n         * @product highcharts\n         */\n        minimumClusterSize: 5,\n        layoutAlgorithm: {\n            distance: 0,\n            gridSize: 0,\n            kmeansThreshold: 0\n        },\n        marker: {\n            lineWidth: 0,\n            radius: 0\n        }\n    }\n};\n/**\n * A `treemap` series. If the [type](#series.treemap.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.treemap\n * @excluding dataParser, dataURL, stack, dataSorting\n * @product   highcharts\n * @requires  modules/treemap\n * @apioption series.treemap\n */\n/**\n * An array of data points for the series. For the `treemap` series\n * type, points can be given in the following ways:\n *\n * 1. An array of numerical values. In this case, the numerical values will be\n *    interpreted as `value` options. Example:\n *    ```js\n *    data: [0, 5, 3, 5]\n *    ```\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.treemap.turboThreshold),\n *    this option is not available.\n *    ```js\n *      data: [{\n *        value: 9,\n *        name: \"Point2\",\n *        color: \"#00FF00\"\n *      }, {\n *        value: 6,\n *        name: \"Point1\",\n *        color: \"#FF00FF\"\n *      }]\n *    ```\n *\n * @sample {highcharts} highcharts/chart/reflow-true/\n *         Numerical values\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<number|null|*>}\n * @extends   series.heatmap.data\n * @excluding x, y, pointPadding\n * @product   highcharts\n * @apioption series.treemap.data\n */\n/**\n * The value of the point, resulting in a relative area of the point\n * in the treemap.\n *\n * @type      {number|null}\n * @product   highcharts\n * @apioption series.treemap.data.value\n */\n/**\n * Serves a purpose only if a `colorAxis` object is defined in the chart\n * options. This value will decide which color the point gets from the\n * scale of the colorAxis.\n *\n * @type      {number}\n * @since     4.1.0\n * @product   highcharts\n * @apioption series.treemap.data.colorValue\n */\n/**\n * Only for treemap. Use this option to build a tree structure. The\n * value should be the id of the point which is the parent. If no points\n * has a matching id, or this option is undefined, then the parent will\n * be set to the root.\n *\n * @sample {highcharts} highcharts/point/parent/\n *         Point parent\n * @sample {highcharts} highcharts/demo/treemap-with-levels/\n *         Example where parent id is not matching\n *\n * @type      {string}\n * @since     4.1.0\n * @product   highcharts\n * @apioption series.treemap.data.parent\n */\n''; // Keeps doclets above detached\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Treemap_TreemapSeriesDefaults = (TreemapSeriesDefaults);\n\n;// ./code/es-modules/Series/Treemap/TreemapUtilities.js\n/* *\n *\n *  (c) 2014-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Namespace\n *\n * */\nvar TreemapUtilities;\n(function (TreemapUtilities) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @todo find correct name for this function.\n     * @todo Similar to reduce, this function is likely redundant\n     */\n    function recursive(item, func, context) {\n        const next = func.call(context || this, item);\n        if (next !== false) {\n            recursive(next, func, context);\n        }\n    }\n    TreemapUtilities.recursive = recursive;\n})(TreemapUtilities || (TreemapUtilities = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Treemap_TreemapUtilities = (TreemapUtilities);\n\n;// ./code/es-modules/Series/TreeUtilities.js\n/* *\n *\n *  (c) 2014-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { extend: TreeUtilities_extend, isArray, isNumber: TreeUtilities_isNumber, isObject, merge: TreeUtilities_merge, pick: TreeUtilities_pick, relativeLength } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/* eslint-disable valid-jsdoc */\n/**\n * @private\n */\nfunction getColor(node, options) {\n    const index = options.index, mapOptionsToLevel = options.mapOptionsToLevel, parentColor = options.parentColor, parentColorIndex = options.parentColorIndex, series = options.series, colors = options.colors, siblings = options.siblings, points = series.points, chartOptionsChart = series.chart.options.chart;\n    let getColorByPoint, point, level, colorByPoint, colorIndexByPoint, color, colorIndex;\n    /**\n     * @private\n     */\n    const variateColor = (color) => {\n        const colorVariation = level && level.colorVariation;\n        if (colorVariation &&\n            colorVariation.key === 'brightness' &&\n            index &&\n            siblings) {\n            return external_highcharts_src_js_default_Color_default().parse(color).brighten(colorVariation.to * (index / siblings)).get();\n        }\n        return color;\n    };\n    if (node) {\n        point = points[node.i];\n        level = mapOptionsToLevel[node.level] || {};\n        getColorByPoint = point && level.colorByPoint;\n        if (getColorByPoint) {\n            colorIndexByPoint = point.index % (colors ?\n                colors.length :\n                chartOptionsChart.colorCount);\n            colorByPoint = colors && colors[colorIndexByPoint];\n        }\n        // Select either point color, level color or inherited color.\n        if (!series.chart.styledMode) {\n            color = TreeUtilities_pick(point && point.options.color, level && level.color, colorByPoint, parentColor && variateColor(parentColor), series.color);\n        }\n        colorIndex = TreeUtilities_pick(point && point.options.colorIndex, level && level.colorIndex, colorIndexByPoint, parentColorIndex, options.colorIndex);\n    }\n    return {\n        color: color,\n        colorIndex: colorIndex\n    };\n}\n/**\n * Creates a map from level number to its given options.\n *\n * @private\n *\n * @param {Object} params\n * Object containing parameters.\n * - `defaults` Object containing default options. The default options are\n *   merged with the userOptions to get the final options for a specific\n *   level.\n * - `from` The lowest level number.\n * - `levels` User options from series.levels.\n * - `to` The highest level number.\n *\n * @return {Highcharts.Dictionary<object>|null}\n * Returns a map from level number to its given options.\n */\nfunction getLevelOptions(params) {\n    const result = {};\n    let defaults, converted, i, from, to, levels;\n    if (isObject(params)) {\n        from = TreeUtilities_isNumber(params.from) ? params.from : 1;\n        levels = params.levels;\n        converted = {};\n        defaults = isObject(params.defaults) ? params.defaults : {};\n        if (isArray(levels)) {\n            converted = levels.reduce((obj, item) => {\n                let level, levelIsConstant, options;\n                if (isObject(item) && TreeUtilities_isNumber(item.level)) {\n                    options = TreeUtilities_merge({}, item);\n                    levelIsConstant = TreeUtilities_pick(options.levelIsConstant, defaults.levelIsConstant);\n                    // Delete redundant properties.\n                    delete options.levelIsConstant;\n                    delete options.level;\n                    // Calculate which level these options apply to.\n                    level = item.level + (levelIsConstant ? 0 : from - 1);\n                    if (isObject(obj[level])) {\n                        TreeUtilities_merge(true, obj[level], options); // #16329\n                    }\n                    else {\n                        obj[level] = options;\n                    }\n                }\n                return obj;\n            }, {});\n        }\n        to = TreeUtilities_isNumber(params.to) ? params.to : 1;\n        for (i = 0; i <= to; i++) {\n            result[i] = TreeUtilities_merge({}, defaults, isObject(converted[i]) ? converted[i] : {});\n        }\n    }\n    return result;\n}\n/**\n * @private\n * @todo Combine buildTree and buildNode with setTreeValues\n * @todo Remove logic from Treemap and make it utilize this mixin.\n */\nfunction setTreeValues(tree, options) {\n    const before = options.before, idRoot = options.idRoot, mapIdToNode = options.mapIdToNode, nodeRoot = mapIdToNode[idRoot], levelIsConstant = (options.levelIsConstant !== false), points = options.points, point = points[tree.i], optionsPoint = point && point.options || {}, children = [];\n    let childrenTotal = 0;\n    tree.levelDynamic = tree.level - (levelIsConstant ? 0 : nodeRoot.level);\n    tree.name = TreeUtilities_pick(point && point.name, '');\n    tree.visible = (idRoot === tree.id ||\n        options.visible === true);\n    if (typeof before === 'function') {\n        tree = before(tree, options);\n    }\n    // First give the children some values\n    tree.children.forEach((child, i) => {\n        const newOptions = TreeUtilities_extend({}, options);\n        TreeUtilities_extend(newOptions, {\n            index: i,\n            siblings: tree.children.length,\n            visible: tree.visible\n        });\n        child = setTreeValues(child, newOptions);\n        children.push(child);\n        if (child.visible) {\n            childrenTotal += child.val;\n        }\n    });\n    // Set the values\n    const value = TreeUtilities_pick(optionsPoint.value, childrenTotal);\n    tree.visible = value >= 0 && (childrenTotal > 0 || tree.visible);\n    tree.children = children;\n    tree.childrenTotal = childrenTotal;\n    tree.isLeaf = tree.visible && !childrenTotal;\n    tree.val = value;\n    return tree;\n}\n/**\n * Update the rootId property on the series. Also makes sure that it is\n * accessible to exporting.\n *\n * @private\n *\n * @param {Object} series\n * The series to operate on.\n *\n * @return {string}\n * Returns the resulting rootId after update.\n */\nfunction updateRootId(series) {\n    let rootId, options;\n    if (isObject(series)) {\n        // Get the series options.\n        options = isObject(series.options) ? series.options : {};\n        // Calculate the rootId.\n        rootId = TreeUtilities_pick(series.rootNode, options.rootId, '');\n        // Set rootId on series.userOptions to pick it up in exporting.\n        if (isObject(series.userOptions)) {\n            series.userOptions.rootId = rootId;\n        }\n        // Set rootId on series to pick it up on next update.\n        series.rootNode = rootId;\n    }\n    return rootId;\n}\n/**\n * Get the node width, which relies on the plot width and the nodeDistance\n * option.\n *\n * @private\n */\nfunction getNodeWidth(series, columnCount) {\n    const { chart, options } = series, { nodeDistance = 0, nodeWidth = 0 } = options, { plotSizeX = 1 } = chart;\n    // Node width auto means they are evenly distributed along the width of\n    // the plot area\n    if (nodeWidth === 'auto') {\n        if (typeof nodeDistance === 'string' && /%$/.test(nodeDistance)) {\n            const fraction = parseFloat(nodeDistance) / 100, total = columnCount + fraction * (columnCount - 1);\n            return plotSizeX / total;\n        }\n        const nDistance = Number(nodeDistance);\n        return ((plotSizeX + nDistance) /\n            (columnCount || 1)) - nDistance;\n    }\n    return relativeLength(nodeWidth, plotSizeX);\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst TreeUtilities = {\n    getColor,\n    getLevelOptions,\n    getNodeWidth,\n    setTreeValues,\n    updateRootId\n};\n/* harmony default export */ const Series_TreeUtilities = (TreeUtilities);\n\n;// ./code/es-modules/Series/Treemap/TreemapSeries.js\n/* *\n *\n *  (c) 2014-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { parse: color } = (external_highcharts_src_js_default_Color_default());\n\n\nconst { composed: TreemapSeries_composed, noop } = (external_highcharts_src_js_default_default());\n\n\nconst { column: ColumnSeries, scatter: ScatterSeries } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\n\n\n\n\n\nconst { getColor: TreemapSeries_getColor, getLevelOptions: TreemapSeries_getLevelOptions, updateRootId: TreemapSeries_updateRootId } = Series_TreeUtilities;\n\nconst { addEvent: TreemapSeries_addEvent, arrayMax, clamp, correctFloat, crisp, defined: TreemapSeries_defined, error, extend: TreemapSeries_extend, fireEvent: TreemapSeries_fireEvent, isArray: TreemapSeries_isArray, isNumber: TreemapSeries_isNumber, isObject: TreemapSeries_isObject, isString: TreemapSeries_isString, merge: TreemapSeries_merge, pick: TreemapSeries_pick, pushUnique: TreemapSeries_pushUnique, splat, stableSort } = (external_highcharts_src_js_default_default());\nexternal_highcharts_src_js_default_Series_default().keepProps.push('simulation', 'hadOutsideDataLabels');\n/* *\n *\n *  Constants\n *\n * */\nconst axisMax = 100;\n/* *\n *\n *  Variables\n *\n * */\nlet treemapAxisDefaultValues = false;\n/* *\n *\n *  Functions\n *\n * */\n/** @private */\nfunction onSeriesAfterBindAxes() {\n    const series = this, xAxis = series.xAxis, yAxis = series.yAxis;\n    let treeAxis;\n    if (xAxis && yAxis) {\n        if (series.is('treemap')) {\n            treeAxis = {\n                endOnTick: false,\n                gridLineWidth: 0,\n                lineWidth: 0,\n                min: 0,\n                minPadding: 0,\n                max: axisMax,\n                maxPadding: 0,\n                startOnTick: false,\n                title: void 0,\n                tickPositions: []\n            };\n            TreemapSeries_extend(yAxis.options, treeAxis);\n            TreemapSeries_extend(xAxis.options, treeAxis);\n            treemapAxisDefaultValues = true;\n        }\n        else if (treemapAxisDefaultValues) {\n            yAxis.setOptions(yAxis.userOptions);\n            xAxis.setOptions(xAxis.userOptions);\n            treemapAxisDefaultValues = false;\n        }\n    }\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.treemap\n *\n * @augments Highcharts.Series\n */\nclass TreemapSeries extends ScatterSeries {\n    constructor() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        super(...arguments);\n        this.simulation = 0;\n        /* eslint-enable valid-jsdoc */\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    static compose(SeriesClass) {\n        if (TreemapSeries_pushUnique(TreemapSeries_composed, 'TreemapSeries')) {\n            TreemapSeries_addEvent(SeriesClass, 'afterBindAxes', onSeriesAfterBindAxes);\n        }\n    }\n    /* *\n     *\n     *  Function\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    algorithmCalcPoints(directionChange, last, group, childrenArea) {\n        const plot = group.plot, end = group.elArr.length - 1;\n        let pX, pY, pW, pH, gW = group.lW, gH = group.lH, keep, i = 0;\n        if (last) {\n            gW = group.nW;\n            gH = group.nH;\n        }\n        else {\n            keep = group.elArr[end];\n        }\n        for (const p of group.elArr) {\n            if (last || (i < end)) {\n                if (group.direction === 0) {\n                    pX = plot.x;\n                    pY = plot.y;\n                    pW = gW;\n                    pH = p / pW;\n                }\n                else {\n                    pX = plot.x;\n                    pY = plot.y;\n                    pH = gH;\n                    pW = p / pH;\n                }\n                childrenArea.push({\n                    x: pX,\n                    y: pY,\n                    width: pW,\n                    height: correctFloat(pH)\n                });\n                if (group.direction === 0) {\n                    plot.y = plot.y + pH;\n                }\n                else {\n                    plot.x = plot.x + pW;\n                }\n            }\n            i = i + 1;\n        }\n        // Reset variables\n        group.reset();\n        if (group.direction === 0) {\n            group.width = group.width - gW;\n        }\n        else {\n            group.height = group.height - gH;\n        }\n        plot.y = plot.parent.y + (plot.parent.height - group.height);\n        plot.x = plot.parent.x + (plot.parent.width - group.width);\n        if (directionChange) {\n            group.direction = 1 - group.direction;\n        }\n        // If not last, then add uncalculated element\n        if (!last) {\n            group.addElement(keep);\n        }\n    }\n    algorithmFill(directionChange, parent, children) {\n        const childrenArea = [];\n        let pTot, direction = parent.direction, x = parent.x, y = parent.y, width = parent.width, height = parent.height, pX, pY, pW, pH;\n        for (const child of children) {\n            pTot =\n                (parent.width * parent.height) * (child.val / parent.val);\n            pX = x;\n            pY = y;\n            if (direction === 0) {\n                pH = height;\n                pW = pTot / pH;\n                width = width - pW;\n                x = x + pW;\n            }\n            else {\n                pW = width;\n                pH = pTot / pW;\n                height = height - pH;\n                y = y + pH;\n            }\n            childrenArea.push({\n                x: pX,\n                y: pY,\n                width: pW,\n                height: pH,\n                direction: 0,\n                val: 0\n            });\n            if (directionChange) {\n                direction = 1 - direction;\n            }\n        }\n        return childrenArea;\n    }\n    algorithmLowAspectRatio(directionChange, parent, children) {\n        const series = this, childrenArea = [], plot = {\n            x: parent.x,\n            y: parent.y,\n            parent: parent\n        }, direction = parent.direction, end = children.length - 1, group = new Treemap_TreemapAlgorithmGroup(parent.height, parent.width, direction, plot);\n        let pTot, i = 0;\n        // Loop through and calculate all areas\n        for (const child of children) {\n            pTot =\n                (parent.width * parent.height) * (child.val / parent.val);\n            group.addElement(pTot);\n            if (group.lP.nR > group.lP.lR) {\n                series.algorithmCalcPoints(directionChange, false, group, childrenArea, plot // @todo no supported\n                );\n            }\n            // If last child, then calculate all remaining areas\n            if (i === end) {\n                series.algorithmCalcPoints(directionChange, true, group, childrenArea, plot // @todo not supported\n                );\n            }\n            ++i;\n        }\n        return childrenArea;\n    }\n    /**\n     * Over the alignment method by setting z index.\n     * @private\n     */\n    alignDataLabel(point, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    dataLabel, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    labelOptions) {\n        ColumnSeries.prototype.alignDataLabel.apply(this, arguments);\n        if (point.dataLabel) {\n            // `point.node.zIndex` could be undefined (#6956)\n            point.dataLabel.attr({ zIndex: (point.node.zIndex || 0) + 1 });\n        }\n    }\n    applyTreeGrouping() {\n        const series = this, parentList = series.parentList || {}, { cluster } = series.options, minimumClusterSize = cluster?.minimumClusterSize || 5;\n        if (cluster?.enabled) {\n            const parentGroups = {};\n            const checkIfHide = (node) => {\n                if (node?.point?.shapeArgs) {\n                    const { width = 0, height = 0 } = node.point.shapeArgs, area = width * height;\n                    const { pixelWidth = 0, pixelHeight = 0 } = cluster, compareHeight = TreemapSeries_defined(pixelHeight), thresholdArea = pixelHeight ?\n                        pixelWidth * pixelHeight :\n                        pixelWidth * pixelWidth;\n                    if (width < pixelWidth ||\n                        height < (compareHeight ? pixelHeight : pixelWidth) ||\n                        area < thresholdArea) {\n                        if (!node.isGroup && TreemapSeries_defined(node.parent)) {\n                            if (!parentGroups[node.parent]) {\n                                parentGroups[node.parent] = [];\n                            }\n                            parentGroups[node.parent].push(node);\n                        }\n                    }\n                }\n                node?.children.forEach((child) => {\n                    checkIfHide(child);\n                });\n            };\n            checkIfHide(series.tree);\n            for (const parent in parentGroups) {\n                if (parentGroups[parent]) {\n                    if (parentGroups[parent].length > minimumClusterSize) {\n                        parentGroups[parent].forEach((node) => {\n                            const index = parentList[parent].indexOf(node.i);\n                            if (index !== -1) {\n                                parentList[parent].splice(index, 1);\n                                const id = `highcharts-grouped-treemap-points-${node.parent || 'root'}`;\n                                let groupPoint = series.points\n                                    .find((p) => p.id === id);\n                                if (!groupPoint) {\n                                    const PointClass = series.pointClass, pointIndex = series.points.length;\n                                    groupPoint = new PointClass(series, {\n                                        className: cluster.className,\n                                        color: cluster.color,\n                                        id,\n                                        index: pointIndex,\n                                        isGroup: true,\n                                        value: 0\n                                    });\n                                    TreemapSeries_extend(groupPoint, {\n                                        formatPrefix: 'cluster'\n                                    });\n                                    series.points.push(groupPoint);\n                                    parentList[parent].push(pointIndex);\n                                    parentList[id] = [];\n                                }\n                                const amount = groupPoint.groupedPointsAmount + 1, val = series.points[groupPoint.index]\n                                    .options.value || 0, name = cluster.name ||\n                                    `+ ${amount}`;\n                                // Update the point directly in points array to\n                                // prevent wrong instance update\n                                series.points[groupPoint.index]\n                                    .groupedPointsAmount = amount;\n                                series.points[groupPoint.index].options.value =\n                                    val + (node.point.value || 0);\n                                series.points[groupPoint.index].name = name;\n                                parentList[id].push(node.point.index);\n                            }\n                        });\n                    }\n                }\n            }\n            series.nodeMap = {};\n            series.nodeList = [];\n            series.parentList = parentList;\n            const tree = series.buildTree('', -1, 0, series.parentList);\n            series.translate(tree);\n        }\n    }\n    /**\n     * Recursive function which calculates the area for all children of a\n     * node.\n     *\n     * @private\n     * @function Highcharts.Series#calculateChildrenAreas\n     *\n     * @param {Object} parent\n     * The node which is parent to the children.\n     *\n     * @param {Object} area\n     * The rectangular area of the parent.\n     */\n    calculateChildrenAreas(parent, area) {\n        const series = this, options = series.options, mapOptionsToLevel = series.mapOptionsToLevel, level = mapOptionsToLevel[parent.level + 1], algorithm = TreemapSeries_pick((level?.layoutAlgorithm &&\n            series[level?.layoutAlgorithm] &&\n            level.layoutAlgorithm), options.layoutAlgorithm), alternate = options.alternateStartingDirection, \n        // Collect all children which should be included\n        children = parent.children.filter((n) => parent.isGroup || !n.ignore), groupPadding = level?.groupPadding ?? options.groupPadding ?? 0, rootNode = series.nodeMap[series.rootNode];\n        if (!algorithm) {\n            return;\n        }\n        let childrenValues = [], axisWidth = rootNode.pointValues?.width || 0, axisHeight = rootNode.pointValues?.height || 0;\n        if (level?.layoutStartingDirection) {\n            area.direction = level.layoutStartingDirection === 'vertical' ?\n                0 :\n                1;\n        }\n        childrenValues = series[algorithm](area, children);\n        let i = -1;\n        for (const child of children) {\n            const values = childrenValues[++i];\n            if (child === rootNode) {\n                axisWidth = axisWidth || values.width;\n                axisHeight = values.height;\n            }\n            const groupPaddingXValues = groupPadding / (series.xAxis.len / axisHeight), groupPaddingYValues = groupPadding / (series.yAxis.len / axisHeight);\n            child.values = TreemapSeries_merge(values, {\n                val: child.childrenTotal,\n                direction: (alternate ? 1 - area.direction : area.direction)\n            });\n            // Make room for outside data labels\n            if (child.children.length &&\n                child.point.dataLabels?.length) {\n                const dlHeight = arrayMax(child.point.dataLabels.map((dl) => dl.options\n                    ?.headers && dl.height || 0)) / (series.yAxis.len / axisHeight);\n                // Make room for data label unless the group is too small\n                if (dlHeight < child.values.height / 2) {\n                    child.values.y += dlHeight;\n                    child.values.height -= dlHeight;\n                }\n            }\n            if (groupPadding) {\n                const xPad = Math.min(groupPaddingXValues, child.values.width / 4), yPad = Math.min(groupPaddingYValues, child.values.height / 4);\n                child.values.x += xPad;\n                child.values.width -= 2 * xPad;\n                child.values.y += yPad;\n                child.values.height -= 2 * yPad;\n            }\n            child.pointValues = TreemapSeries_merge(values, {\n                x: (values.x / series.axisRatio),\n                // Flip y-values to avoid visual regression with csvCoord in\n                // Axis.translate at setPointValues. #12488\n                y: axisMax - values.y - values.height,\n                width: (values.width / series.axisRatio)\n            });\n            // If node has children, then call method recursively\n            if (child.children.length) {\n                series.calculateChildrenAreas(child, child.values);\n            }\n        }\n        const getChildrenRecursive = (node, result = [], getLeaves = true) => {\n            node.children.forEach((child) => {\n                if (getLeaves && child.isLeaf) {\n                    result.push(child.point);\n                }\n                else if (!getLeaves && !child.isLeaf) {\n                    result.push(child.point);\n                }\n                if (child.children.length) {\n                    getChildrenRecursive(child, result, getLeaves);\n                }\n            });\n            return result;\n        };\n        // Experimental block to make space for the outside data labels\n        if (options.nodeSizeBy === 'leaf' &&\n            parent === rootNode &&\n            this.hasOutsideDataLabels &&\n            // Sizing by leaf value is not possible if any of the groups have\n            // explicit values\n            !getChildrenRecursive(rootNode, void 0, false)\n                .some((point) => TreemapSeries_isNumber(point.options.value)) &&\n            !TreemapSeries_isNumber(rootNode.point?.options.value)) {\n            const leaves = getChildrenRecursive(rootNode), values = leaves.map((point) => point.options.value || 0), \n            // Areas in terms of axis units squared\n            areas = leaves.map(({ node: { pointValues } }) => (pointValues ?\n                pointValues.width * pointValues.height :\n                0)), valueSum = values.reduce((sum, value) => sum + value, 0), areaSum = areas.reduce((sum, value) => sum + value, 0), expectedAreaPerValue = areaSum / valueSum;\n            let minMiss = 0, maxMiss = 0;\n            leaves.forEach((point, i) => {\n                const areaPerValue = values[i] ? (areas[i] / values[i]) : 1, \n                // Less than 1 => rendered too small, greater than 1 =>\n                // rendered too big\n                fit = clamp(areaPerValue / expectedAreaPerValue, 0.8, 1.4);\n                let miss = 1 - fit;\n                if (point.value) {\n                    // Very small areas are more sensitive, and matter less to\n                    // the visual impression. Give them less weight.\n                    if (areas[i] < 20) {\n                        miss *= areas[i] / 20;\n                    }\n                    if (miss > maxMiss) {\n                        maxMiss = miss;\n                    }\n                    if (miss < minMiss) {\n                        minMiss = miss;\n                    }\n                    point.simulatedValue = (point.simulatedValue || point.value) / fit;\n                }\n            });\n            /* /\n            console.log('--- simulation',\n                this.simulation,\n                'worstMiss',\n                minMiss,\n                maxMiss\n            );\n            // */\n            if (\n            // An area error less than 5% is acceptable, the human ability\n            // to assess area size is not that accurate\n            (minMiss < -0.05 || maxMiss > 0.05) &&\n                // In case an eternal loop is brewing, pull the emergency brake\n                this.simulation < 10) {\n                this.simulation++;\n                this.setTreeValues(parent);\n                area.val = parent.val;\n                this.calculateChildrenAreas(parent, area);\n                // Simulation is settled, proceed to rendering. Reset the simulated\n                // values and set the tree values with real data.\n            }\n            else {\n                leaves.forEach((point) => {\n                    delete point.simulatedValue;\n                });\n                this.setTreeValues(parent);\n                this.simulation = 0;\n            }\n        }\n    }\n    /**\n     * Create level list.\n     * @private\n     */\n    createList(e) {\n        const chart = this.chart, breadcrumbs = chart.breadcrumbs, list = [];\n        if (breadcrumbs) {\n            let currentLevelNumber = 0;\n            list.push({\n                level: currentLevelNumber,\n                levelOptions: chart.series[0]\n            });\n            let node = e.target.nodeMap[e.newRootId];\n            const extraNodes = [];\n            // When the root node is set and has parent,\n            // recreate the path from the node tree.\n            while (node.parent || node.parent === '') {\n                extraNodes.push(node);\n                node = e.target.nodeMap[node.parent];\n            }\n            for (const node of extraNodes.reverse()) {\n                list.push({\n                    level: ++currentLevelNumber,\n                    levelOptions: node\n                });\n            }\n            // If the list has only first element, we should clear it\n            if (list.length <= 1) {\n                list.length = 0;\n            }\n        }\n        return list;\n    }\n    /**\n     * Extend drawDataLabels with logic to handle custom options related to\n     * the treemap series:\n     *\n     * - Points which is not a leaf node, has dataLabels disabled by\n     *   default.\n     *\n     * - Options set on series.levels is merged in.\n     *\n     * - Width of the dataLabel is set to match the width of the point\n     *   shape.\n     *\n     * @private\n     */\n    drawDataLabels() {\n        const series = this, mapOptionsToLevel = series.mapOptionsToLevel, points = series.points.filter(function (n) {\n            return n.node.visible || TreemapSeries_defined(n.dataLabel);\n        }), padding = splat(series.options.dataLabels || {})[0]?.padding, positionsAreSet = points.some((p) => TreemapSeries_isNumber(p.plotY));\n        for (const point of points) {\n            const style = {}, \n            // Set options to new object to avoid problems with scope\n            options = { style }, level = mapOptionsToLevel[point.node.level];\n            // If not a leaf, then label should be disabled as default\n            if (!point.node.isLeaf &&\n                !point.node.isGroup ||\n                (point.node.isGroup &&\n                    point.node.level <= series.nodeMap[series.rootNode].level)) {\n                options.enabled = false;\n            }\n            // If options for level exists, include them as well\n            if (level?.dataLabels) {\n                TreemapSeries_merge(true, options, splat(level.dataLabels)[0]);\n                series.hasDataLabels = () => true;\n            }\n            // Headers are always top-aligned. Leaf nodes no not support\n            // headers.\n            if (point.node.isLeaf) {\n                options.inside = true;\n            }\n            else if (options.headers) {\n                options.verticalAlign = 'top';\n            }\n            // Set dataLabel width to the width of the point shape minus the\n            // padding\n            if (point.shapeArgs && positionsAreSet) {\n                const { height = 0, width = 0 } = point.shapeArgs;\n                if (width > 32 && height > 16 && point.shouldDraw()) {\n                    const dataLabelWidth = width -\n                        2 * (options.padding || padding || 0);\n                    style.width = `${dataLabelWidth}px`;\n                    style.lineClamp ?? (style.lineClamp = Math.floor(height / 16));\n                    style.visibility = 'inherit';\n                    // Make the label box itself fill the width\n                    if (options.headers) {\n                        point.dataLabel?.attr({\n                            width: dataLabelWidth\n                        });\n                    }\n                    // Hide labels for shapes that are too small\n                }\n                else {\n                    style.width = `${width}px`;\n                    style.visibility = 'hidden';\n                }\n            }\n            // Merge custom options with point options\n            point.dlOptions = TreemapSeries_merge(options, point.options.dataLabels);\n        }\n        super.drawDataLabels(points);\n    }\n    /**\n     * Override drawPoints\n     * @private\n     */\n    drawPoints(points = this.points) {\n        const series = this, chart = series.chart, renderer = chart.renderer, styledMode = chart.styledMode, options = series.options, shadow = styledMode ? {} : options.shadow, borderRadius = options.borderRadius, withinAnimationLimit = chart.pointCount < options.animationLimit, allowTraversingTree = options.allowTraversingTree;\n        for (const point of points) {\n            const levelDynamic = point.node.levelDynamic, animatableAttribs = {}, attribs = {}, css = {}, groupKey = 'level-group-' + point.node.level, hasGraphic = !!point.graphic, shouldAnimate = withinAnimationLimit && hasGraphic, shapeArgs = point.shapeArgs;\n            // Don't bother with calculate styling if the point is not drawn\n            if (point.shouldDraw()) {\n                point.isInside = true;\n                if (borderRadius) {\n                    attribs.r = borderRadius;\n                }\n                TreemapSeries_merge(true, // Extend object\n                // Which object to extend\n                shouldAnimate ? animatableAttribs : attribs, \n                // Add shapeArgs to animate/attr if graphic exists\n                hasGraphic ? shapeArgs : {}, \n                // Add style attribs if !styleMode\n                styledMode ?\n                    {} :\n                    series.pointAttribs(point, point.selected ? 'select' : void 0));\n                // In styled mode apply point.color. Use CSS, otherwise the\n                // fill used in the style sheet will take precedence over\n                // the fill attribute.\n                if (series.colorAttribs && styledMode) {\n                    // Heatmap is loaded\n                    TreemapSeries_extend(css, series.colorAttribs(point));\n                }\n                if (!series[groupKey]) {\n                    series[groupKey] = renderer.g(groupKey)\n                        .attr({\n                        // @todo Set the zIndex based upon the number of\n                        // levels, instead of using 1000\n                        zIndex: 1000 - (levelDynamic || 0)\n                    })\n                        .add(series.group);\n                    series[groupKey].survive = true;\n                }\n            }\n            // Draw the point\n            point.draw({\n                animatableAttribs,\n                attribs,\n                css,\n                group: series[groupKey],\n                imageUrl: point.imageUrl,\n                renderer,\n                shadow,\n                shapeArgs,\n                shapeType: point.shapeType\n            });\n            // If setRootNode is allowed, set a point cursor on clickables &\n            // add drillId to point\n            if (allowTraversingTree && point.graphic) {\n                point.drillId = options.interactByLeaf ?\n                    series.drillToByLeaf(point) :\n                    series.drillToByGroup(point);\n            }\n        }\n    }\n    /**\n     * Finds the drill id for a parent node. Returns false if point should\n     * not have a click event.\n     * @private\n     */\n    drillToByGroup(point) {\n        return (!point.node.isLeaf || point.node.isGroup) ?\n            point.id : false;\n    }\n    /**\n     * Finds the drill id for a leaf node. Returns false if point should not\n     * have a click event\n     * @private\n     */\n    drillToByLeaf(point) {\n        const { traverseToLeaf } = point.series.options;\n        let drillId = false, nodeParent;\n        if ((point.node.parent !== this.rootNode) &&\n            point.node.isLeaf) {\n            if (traverseToLeaf) {\n                drillId = point.id;\n            }\n            else {\n                nodeParent = point.node;\n                while (!drillId) {\n                    if (typeof nodeParent.parent !== 'undefined') {\n                        nodeParent = this.nodeMap[nodeParent.parent];\n                    }\n                    if (nodeParent.parent === this.rootNode) {\n                        drillId = nodeParent.id;\n                    }\n                }\n            }\n        }\n        return drillId;\n    }\n    /**\n     * @todo remove this function at a suitable version.\n     * @private\n     */\n    drillToNode(id, redraw) {\n        error(32, false, void 0, { 'treemap.drillToNode': 'use treemap.setRootNode' });\n        this.setRootNode(id, redraw);\n    }\n    drillUp() {\n        const series = this, node = series.nodeMap[series.rootNode];\n        if (node && TreemapSeries_isString(node.parent)) {\n            series.setRootNode(node.parent, true, { trigger: 'traverseUpButton' });\n        }\n    }\n    getExtremes() {\n        // Get the extremes from the value data\n        const { dataMin, dataMax } = super.getExtremes(this.colorValueData);\n        this.valueMin = dataMin;\n        this.valueMax = dataMax;\n        // Get the extremes from the y data\n        return super.getExtremes();\n    }\n    /**\n     * Creates an object map from parent id to childrens index.\n     *\n     * @private\n     * @function Highcharts.Series#getListOfParents\n     *\n     * @param {Highcharts.SeriesTreemapDataOptions} [data]\n     *        List of points set in options.\n     *\n     * @param {Array<string>} [existingIds]\n     *        List of all point ids.\n     *\n     * @return {Object}\n     *         Map from parent id to children index in data.\n     */\n    getListOfParents(data, existingIds) {\n        const arr = TreemapSeries_isArray(data) ? data : [], ids = TreemapSeries_isArray(existingIds) ? existingIds : [], listOfParents = arr.reduce(function (prev, curr, i) {\n            const parent = TreemapSeries_pick(curr.parent, '');\n            if (typeof prev[parent] === 'undefined') {\n                prev[parent] = [];\n            }\n            prev[parent].push(i);\n            return prev;\n        }, {\n            '': [] // Root of tree\n        });\n        // If parent does not exist, hoist parent to root of tree.\n        for (const parent of Object.keys(listOfParents)) {\n            const children = listOfParents[parent];\n            if ((parent !== '') && (ids.indexOf(parent) === -1)) {\n                for (const child of children) {\n                    listOfParents[''].push(child);\n                }\n                delete listOfParents[parent];\n            }\n        }\n        return listOfParents;\n    }\n    /**\n     * Creates a tree structured object from the series points.\n     * @private\n     */\n    getTree() {\n        const series = this, allIds = this.data.map(function (d) {\n            return d.id;\n        });\n        series.parentList = series.getListOfParents(this.data, allIds);\n        series.nodeMap = {};\n        series.nodeList = [];\n        return series.buildTree('', -1, 0, series.parentList || {});\n    }\n    buildTree(id, index, level, list, parent) {\n        const series = this, children = [], point = series.points[index];\n        let height = 0, child;\n        // Actions\n        for (const i of (list[id] || [])) {\n            child = series.buildTree(series.points[i].id, i, level + 1, list, id);\n            height = Math.max(child.height + 1, height);\n            children.push(child);\n        }\n        const node = new series.NodeClass().init(id, index, children, height, level, series, parent);\n        for (const child of children) {\n            child.parentNode = node;\n        }\n        series.nodeMap[node.id] = node;\n        series.nodeList.push(node);\n        if (point) {\n            point.node = node;\n            node.point = point;\n        }\n        return node;\n    }\n    /**\n     * Define hasData function for non-cartesian series. Returns true if the\n     * series has points at all.\n     * @private\n     */\n    hasData() {\n        return !!this.dataTable.rowCount;\n    }\n    init(chart, options) {\n        const series = this, breadcrumbsOptions = TreemapSeries_merge(options.drillUpButton, options.breadcrumbs), setOptionsEvent = TreemapSeries_addEvent(series, 'setOptions', (event) => {\n            const options = event.userOptions;\n            // Deprecated options\n            if (TreemapSeries_defined(options.allowDrillToNode) &&\n                !TreemapSeries_defined(options.allowTraversingTree)) {\n                options.allowTraversingTree = options.allowDrillToNode;\n                delete options.allowDrillToNode;\n            }\n            if (TreemapSeries_defined(options.drillUpButton) &&\n                !TreemapSeries_defined(options.traverseUpButton)) {\n                options.traverseUpButton = options.drillUpButton;\n                delete options.drillUpButton;\n            }\n            // Check if we need to reserve space for headers\n            const dataLabels = splat(options.dataLabels || {});\n            options.levels?.forEach((level) => {\n                dataLabels.push.apply(dataLabels, splat(level.dataLabels || {}));\n            });\n            this.hasOutsideDataLabels = dataLabels.some((dl) => dl.headers);\n        });\n        super.init(chart, options);\n        // Treemap's opacity is a different option from other series\n        delete series.opacity;\n        // Handle deprecated options.\n        series.eventsToUnbind.push(setOptionsEvent);\n        if (series.options.allowTraversingTree) {\n            series.eventsToUnbind.push(TreemapSeries_addEvent(series, 'click', series.onClickDrillToNode));\n            series.eventsToUnbind.push(TreemapSeries_addEvent(series, 'setRootNode', function (e) {\n                const chart = series.chart;\n                if (chart.breadcrumbs) {\n                    // Create a list using the event after drilldown.\n                    chart.breadcrumbs.updateProperties(series.createList(e));\n                }\n            }));\n            series.eventsToUnbind.push(TreemapSeries_addEvent(series, 'update', \n            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n            function (e, redraw) {\n                const breadcrumbs = this.chart.breadcrumbs;\n                if (breadcrumbs && e.options.breadcrumbs) {\n                    breadcrumbs.update(e.options.breadcrumbs);\n                }\n                this.hadOutsideDataLabels = this.hasOutsideDataLabels;\n            }));\n            series.eventsToUnbind.push(TreemapSeries_addEvent(series, 'destroy', function destroyEvents(e) {\n                const chart = this.chart;\n                if (chart.breadcrumbs && !e.keepEventsForUpdate) {\n                    chart.breadcrumbs.destroy();\n                    chart.breadcrumbs = void 0;\n                }\n            }));\n        }\n        if (!chart.breadcrumbs) {\n            chart.breadcrumbs = new Breadcrumbs_Breadcrumbs(chart, breadcrumbsOptions);\n        }\n        series.eventsToUnbind.push(TreemapSeries_addEvent(chart.breadcrumbs, 'up', function (e) {\n            const drillUpsNumber = this.level - e.newLevel;\n            for (let i = 0; i < drillUpsNumber; i++) {\n                series.drillUp();\n            }\n        }));\n    }\n    /**\n     * Add drilling on the suitable points.\n     * @private\n     */\n    onClickDrillToNode(event) {\n        const series = this, point = event.point, drillId = point?.drillId;\n        // If a drill id is returned, add click event and cursor.\n        if (TreemapSeries_isString(drillId)) {\n            point.setState(''); // Remove hover\n            series.setRootNode(drillId, true, { trigger: 'click' });\n        }\n    }\n    /**\n     * Get presentational attributes\n     * @private\n     */\n    pointAttribs(point, state) {\n        const series = this, mapOptionsToLevel = (TreemapSeries_isObject(series.mapOptionsToLevel) ?\n            series.mapOptionsToLevel :\n            {}), level = point && mapOptionsToLevel[point.node.level] || {}, options = this.options, stateOptions = state && options.states && options.states[state] || {}, className = point?.getClassName() || '', \n        // Set attributes by precedence. Point trumps level trumps series.\n        // Stroke width uses pick because it can be 0.\n        attr = {\n            'stroke': (point && point.borderColor) ||\n                level.borderColor ||\n                stateOptions.borderColor ||\n                options.borderColor,\n            'stroke-width': TreemapSeries_pick(point && point.borderWidth, level.borderWidth, stateOptions.borderWidth, options.borderWidth),\n            'dashstyle': point?.borderDashStyle ||\n                level.borderDashStyle ||\n                stateOptions.borderDashStyle ||\n                options.borderDashStyle,\n            'fill': point?.color || this.color\n        };\n        // Hide levels above the current view\n        if (className.indexOf('highcharts-above-level') !== -1) {\n            attr.fill = 'none';\n            attr['stroke-width'] = 0;\n            // Nodes with children that accept interaction\n        }\n        else if (className.indexOf('highcharts-internal-node-interactive') !== -1) {\n            attr['fill-opacity'] = stateOptions.opacity ?? options.opacity ?? 1;\n            attr.cursor = 'pointer';\n            // Hide nodes that have children\n        }\n        else if (className.indexOf('highcharts-internal-node') !== -1) {\n            attr.fill = 'none';\n        }\n        else if (state && stateOptions.brightness) {\n            // Brighten and hoist the hover nodes\n            attr.fill = color(attr.fill)\n                .brighten(stateOptions.brightness)\n                .get();\n        }\n        return attr;\n    }\n    /**\n     * Set the node's color recursively, from the parent down.\n     * @private\n     */\n    setColorRecursive(node, parentColor, colorIndex, index, siblings) {\n        const series = this, chart = series?.chart, colors = chart?.options?.colors;\n        if (node) {\n            const colorInfo = TreemapSeries_getColor(node, {\n                colors: colors,\n                index: index,\n                mapOptionsToLevel: series.mapOptionsToLevel,\n                parentColor: parentColor,\n                parentColorIndex: colorIndex,\n                series: series,\n                siblings: siblings\n            }), point = series.points[node.i];\n            if (point) {\n                point.color = colorInfo.color;\n                point.colorIndex = colorInfo.colorIndex;\n            }\n            let i = -1;\n            // Do it all again with the children\n            for (const child of (node.children || [])) {\n                series.setColorRecursive(child, colorInfo.color, colorInfo.colorIndex, ++i, node.children.length);\n            }\n        }\n    }\n    setPointValues() {\n        const series = this;\n        const { points, xAxis, yAxis } = series;\n        const styledMode = series.chart.styledMode;\n        // Get the crisp correction in classic mode. For this to work in\n        // styled mode, we would need to first add the shape (without x,\n        // y, width and height), then read the rendered stroke width\n        // using point.graphic.strokeWidth(), then modify and apply the\n        // shapeArgs. This applies also to column series, but the\n        // downside is performance and code complexity.\n        const getStrokeWidth = (point) => (styledMode ?\n            0 :\n            (series.pointAttribs(point)['stroke-width'] || 0));\n        for (const point of points) {\n            const { pointValues: values, visible } = point.node;\n            // Points which is ignored, have no values.\n            if (values && visible) {\n                const { height, width, x, y } = values, strokeWidth = getStrokeWidth(point), xValue = xAxis.toPixels(x, true), x2Value = xAxis.toPixels(x + width, true), yValue = yAxis.toPixels(y, true), y2Value = yAxis.toPixels(y + height, true), \n                // If the edge of a rectangle is on the edge, make sure it\n                // stays within the plot area by adding or substracting half\n                // of the stroke width.\n                x1 = xValue === 0 ?\n                    strokeWidth / 2 :\n                    crisp(xAxis.toPixels(x, true), strokeWidth, true), x2 = x2Value === xAxis.len ?\n                    xAxis.len - strokeWidth / 2 :\n                    crisp(xAxis.toPixels(x + width, true), strokeWidth, true), y1 = yValue === yAxis.len ?\n                    yAxis.len - strokeWidth / 2 :\n                    crisp(yAxis.toPixels(y, true), strokeWidth, true), y2 = y2Value === 0 ?\n                    strokeWidth / 2 :\n                    crisp(yAxis.toPixels(y + height, true), strokeWidth, true);\n                // Set point values\n                const shapeArgs = {\n                    x: Math.min(x1, x2),\n                    y: Math.min(y1, y2),\n                    width: Math.abs(x2 - x1),\n                    height: Math.abs(y2 - y1)\n                };\n                point.plotX = shapeArgs.x + (shapeArgs.width / 2);\n                point.plotY = shapeArgs.y + (shapeArgs.height / 2);\n                point.shapeArgs = shapeArgs;\n            }\n            else {\n                // Reset visibility\n                delete point.plotX;\n                delete point.plotY;\n            }\n        }\n    }\n    /**\n     * Sets a new root node for the series.\n     *\n     * @private\n     * @function Highcharts.Series#setRootNode\n     *\n     * @param {string} id\n     * The id of the new root node.\n     *\n     * @param {boolean} [redraw=true]\n     * Whether to redraw the chart or not.\n     *\n     * @param {Object} [eventArguments]\n     * Arguments to be accessed in event handler.\n     *\n     * @param {string} [eventArguments.newRootId]\n     * Id of the new root.\n     *\n     * @param {string} [eventArguments.previousRootId]\n     * Id of the previous root.\n     *\n     * @param {boolean} [eventArguments.redraw]\n     * Whether to redraw the chart after.\n     *\n     * @param {Object} [eventArguments.series]\n     * The series to update the root of.\n     *\n     * @param {string} [eventArguments.trigger]\n     * The action which triggered the event. Undefined if the setRootNode is\n     * called directly.\n     *\n     * @emits Highcharts.Series#event:setRootNode\n     */\n    setRootNode(id, redraw, eventArguments) {\n        const series = this, eventArgs = TreemapSeries_extend({\n            newRootId: id,\n            previousRootId: series.rootNode,\n            redraw: TreemapSeries_pick(redraw, true),\n            series: series\n        }, eventArguments);\n        /**\n         * The default functionality of the setRootNode event.\n         *\n         * @private\n         * @param {Object} args The event arguments.\n         * @param {string} args.newRootId Id of the new root.\n         * @param {string} args.previousRootId Id of the previous root.\n         * @param {boolean} args.redraw Whether to redraw the chart after.\n         * @param {Object} args.series The series to update the root of.\n         * @param {string} [args.trigger=undefined] The action which\n         * triggered the event. Undefined if the setRootNode is called\n         * directly.\n             */\n        const defaultFn = function (args) {\n            const series = args.series;\n            // Store previous and new root ids on the series.\n            series.idPreviousRoot = args.previousRootId;\n            series.rootNode = args.newRootId;\n            // Redraw the chart\n            series.isDirty = true; // Force redraw\n            if (args.redraw) {\n                series.chart.redraw();\n            }\n        };\n        // Fire setRootNode event.\n        TreemapSeries_fireEvent(series, 'setRootNode', eventArgs, defaultFn);\n    }\n    /**\n     * Workaround for `inactive` state. Since `series.opacity` option is\n     * already reserved, don't use that state at all by disabling\n     * `inactiveOtherPoints` and not inheriting states by points.\n     * @private\n     */\n    setState(state) {\n        this.options.inactiveOtherPoints = true;\n        super.setState(state, false);\n        this.options.inactiveOtherPoints = false;\n    }\n    setTreeValues(tree) {\n        const series = this, options = series.options, idRoot = series.rootNode, mapIdToNode = series.nodeMap, nodeRoot = mapIdToNode[idRoot], levelIsConstant = (typeof options.levelIsConstant === 'boolean' ?\n            options.levelIsConstant :\n            true), children = [], point = series.points[tree.i];\n        // First give the children some values\n        let childrenTotal = 0;\n        for (let child of tree.children) {\n            child = series.setTreeValues(child);\n            children.push(child);\n            if (!child.ignore) {\n                childrenTotal += child.val;\n            }\n        }\n        // Sort the children\n        stableSort(children, (a, b) => ((a.sortIndex || 0) - (b.sortIndex || 0)));\n        // Set the values\n        let val = TreemapSeries_pick(point?.simulatedValue, point?.options.value, childrenTotal);\n        if (point) {\n            point.value = val;\n        }\n        if (point?.isGroup && options.cluster?.reductionFactor) {\n            val /= options.cluster.reductionFactor;\n        }\n        if (tree.parentNode?.point?.isGroup && series.rootNode !== tree.parent) {\n            tree.visible = false;\n        }\n        TreemapSeries_extend(tree, {\n            children: children,\n            childrenTotal: childrenTotal,\n            // Ignore this node if point is not visible\n            ignore: !(TreemapSeries_pick(point?.visible, true) && (val > 0)),\n            isLeaf: tree.visible && !childrenTotal,\n            isGroup: point?.isGroup,\n            levelDynamic: (tree.level - (levelIsConstant ? 0 : nodeRoot.level)),\n            name: TreemapSeries_pick(point?.name, ''),\n            sortIndex: TreemapSeries_pick(point?.sortIndex, -val),\n            val: val\n        });\n        return tree;\n    }\n    sliceAndDice(parent, children) {\n        return this.algorithmFill(true, parent, children);\n    }\n    squarified(parent, children) {\n        return this.algorithmLowAspectRatio(true, parent, children);\n    }\n    strip(parent, children) {\n        return this.algorithmLowAspectRatio(false, parent, children);\n    }\n    stripes(parent, children) {\n        return this.algorithmFill(false, parent, children);\n    }\n    translate(tree) {\n        const series = this, options = series.options, applyGrouping = !tree;\n        let // NOTE: updateRootId modifies series.\n        rootId = TreemapSeries_updateRootId(series), rootNode, pointValues, seriesArea, val;\n        if (!tree && !rootId.startsWith('highcharts-grouped-treemap-points-')) {\n            // Group points are removed, but not destroyed during generatePoints\n            (this.points || []).forEach((point) => {\n                if (point.isGroup) {\n                    point.destroy();\n                }\n            });\n            // Call prototype function\n            super.translate();\n            // @todo Only if series.isDirtyData is true\n            tree = series.getTree();\n        }\n        // Ensure `tree` and `series.tree` are synchronized\n        series.tree = tree = tree || series.tree;\n        rootNode = series.nodeMap[rootId];\n        if (rootId !== '' && !rootNode) {\n            series.setRootNode('', false);\n            rootId = series.rootNode;\n            rootNode = series.nodeMap[rootId];\n        }\n        if (!rootNode.point?.isGroup) {\n            series.mapOptionsToLevel = TreemapSeries_getLevelOptions({\n                from: rootNode.level + 1,\n                levels: options.levels,\n                to: tree.height,\n                defaults: {\n                    levelIsConstant: series.options.levelIsConstant,\n                    colorByPoint: options.colorByPoint\n                }\n            });\n        }\n        // Parents of the root node is by default visible\n        Treemap_TreemapUtilities.recursive(series.nodeMap[series.rootNode], (node) => {\n            const p = node.parent;\n            let next = false;\n            node.visible = true;\n            if (p || p === '') {\n                next = series.nodeMap[p];\n            }\n            return next;\n        });\n        // Children of the root node is by default visible\n        Treemap_TreemapUtilities.recursive(series.nodeMap[series.rootNode].children, (children) => {\n            let next = false;\n            for (const child of children) {\n                child.visible = true;\n                if (child.children.length) {\n                    next = (next || []).concat(child.children);\n                }\n            }\n            return next;\n        });\n        series.setTreeValues(tree);\n        // Calculate plotting values.\n        series.axisRatio = (series.xAxis.len / series.yAxis.len);\n        series.nodeMap[''].pointValues = pointValues = {\n            x: 0,\n            y: 0,\n            width: axisMax,\n            height: axisMax\n        };\n        series.nodeMap[''].values = seriesArea = TreemapSeries_merge(pointValues, {\n            width: (pointValues.width * series.axisRatio),\n            direction: (options.layoutStartingDirection === 'vertical' ? 0 : 1),\n            val: tree.val\n        });\n        // We need to pre-render the data labels in order to measure the height\n        // of data label group\n        if (this.hasOutsideDataLabels || this.hadOutsideDataLabels) {\n            this.drawDataLabels();\n        }\n        series.calculateChildrenAreas(tree, seriesArea);\n        // Logic for point colors\n        if (!series.colorAxis &&\n            !options.colorByPoint) {\n            series.setColorRecursive(series.tree);\n        }\n        // Update axis extremes according to the root node.\n        if (options.allowTraversingTree && rootNode.pointValues) {\n            val = rootNode.pointValues;\n            series.xAxis.setExtremes(val.x, val.x + val.width, false);\n            series.yAxis.setExtremes(val.y, val.y + val.height, false);\n            series.xAxis.setScale();\n            series.yAxis.setScale();\n        }\n        // Assign values to points.\n        series.setPointValues();\n        if (applyGrouping) {\n            series.applyTreeGrouping();\n        }\n    }\n}\nTreemapSeries.defaultOptions = TreemapSeries_merge(ScatterSeries.defaultOptions, Treemap_TreemapSeriesDefaults);\nTreemapSeries_extend(TreemapSeries.prototype, {\n    buildKDTree: noop,\n    colorAttribs: Series_ColorMapComposition.seriesMembers.colorAttribs,\n    colorKey: 'colorValue', // Point color option key\n    directTouch: true,\n    getExtremesFromAll: true,\n    getSymbol: noop,\n    optionalAxis: 'colorAxis',\n    parallelArrays: ['x', 'y', 'value', 'colorValue'],\n    pointArrayMap: ['value', 'colorValue'],\n    pointClass: Treemap_TreemapPoint,\n    NodeClass: Treemap_TreemapNode,\n    trackerGroups: ['group', 'dataLabelsGroup'],\n    utils: Treemap_TreemapUtilities\n});\nSeries_ColorMapComposition.compose(TreemapSeries);\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('treemap', TreemapSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Treemap_TreemapSeries = ((/* unused pure expression or super */ null && (TreemapSeries)));\n\n;// ./code/es-modules/Series/CenteredUtilities.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { deg2rad } = (external_highcharts_src_js_default_default());\n\n\nconst { fireEvent: CenteredUtilities_fireEvent, isNumber: CenteredUtilities_isNumber, pick: CenteredUtilities_pick, relativeLength: CenteredUtilities_relativeLength } = (external_highcharts_src_js_default_default());\n/**\n * @private\n */\nvar CenteredUtilities;\n(function (CenteredUtilities) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * Get the center of the pie based on the size and center options relative\n     * to the plot area. Borrowed by the polar and gauge series types.\n     *\n     * @private\n     * @function Highcharts.CenteredSeriesMixin.getCenter\n     */\n    function getCenter() {\n        const options = this.options, chart = this.chart, slicingRoom = 2 * (options.slicedOffset || 0), plotWidth = chart.plotWidth - 2 * slicingRoom, plotHeight = chart.plotHeight - 2 * slicingRoom, centerOption = options.center, smallestSize = Math.min(plotWidth, plotHeight), thickness = options.thickness;\n        let handleSlicingRoom, size = options.size, innerSize = options.innerSize || 0, i, value;\n        if (typeof size === 'string') {\n            size = parseFloat(size);\n        }\n        if (typeof innerSize === 'string') {\n            innerSize = parseFloat(innerSize);\n        }\n        const positions = [\n            CenteredUtilities_pick(centerOption?.[0], '50%'),\n            CenteredUtilities_pick(centerOption?.[1], '50%'),\n            // Prevent from negative values\n            CenteredUtilities_pick(size && size < 0 ? void 0 : options.size, '100%'),\n            CenteredUtilities_pick(innerSize && innerSize < 0 ? void 0 : options.innerSize || 0, '0%')\n        ];\n        // No need for inner size in angular (gauges) series but still required\n        // for pie series\n        if (chart.angular && !(this instanceof (external_highcharts_src_js_default_Series_default()))) {\n            positions[3] = 0;\n        }\n        for (i = 0; i < 4; ++i) {\n            value = positions[i];\n            handleSlicingRoom = i < 2 || (i === 2 && /%$/.test(value));\n            // I == 0: centerX, relative to width\n            // i == 1: centerY, relative to height\n            // i == 2: size, relative to smallestSize\n            // i == 3: innerSize, relative to size\n            positions[i] = CenteredUtilities_relativeLength(value, [plotWidth, plotHeight, smallestSize, positions[2]][i]) + (handleSlicingRoom ? slicingRoom : 0);\n        }\n        // Inner size cannot be larger than size (#3632)\n        if (positions[3] > positions[2]) {\n            positions[3] = positions[2];\n        }\n        // Thickness overrides innerSize, need to be less than pie size (#6647)\n        if (CenteredUtilities_isNumber(thickness) &&\n            thickness * 2 < positions[2] && thickness > 0) {\n            positions[3] = positions[2] - thickness * 2;\n        }\n        CenteredUtilities_fireEvent(this, 'afterGetCenter', { positions });\n        return positions;\n    }\n    CenteredUtilities.getCenter = getCenter;\n    /**\n     * GetStartAndEndRadians - Calculates start and end angles in radians.\n     * Used in series types such as pie and sunburst.\n     *\n     * @private\n     * @function Highcharts.CenteredSeriesMixin.getStartAndEndRadians\n     *\n     * @param {number} [start]\n     *        Start angle in degrees.\n     *\n     * @param {number} [end]\n     *        Start angle in degrees.\n     *\n     * @return {Highcharts.RadianAngles}\n     *         Returns an object containing start and end angles as radians.\n     */\n    function getStartAndEndRadians(start, end) {\n        const startAngle = CenteredUtilities_isNumber(start) ? start : 0, // Must be a number\n        endAngle = ((CenteredUtilities_isNumber(end) && // Must be a number\n            end > startAngle && // Must be larger than the start angle\n            // difference must be less than 360 degrees\n            (end - startAngle) < 360) ?\n            end :\n            startAngle + 360), correction = -90;\n        return {\n            start: deg2rad * (startAngle + correction),\n            end: deg2rad * (endAngle + correction)\n        };\n    }\n    CenteredUtilities.getStartAndEndRadians = getStartAndEndRadians;\n})(CenteredUtilities || (CenteredUtilities = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Series_CenteredUtilities = (CenteredUtilities);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * @private\n * @interface Highcharts.RadianAngles\n */ /**\n* @name Highcharts.RadianAngles#end\n* @type {number}\n*/ /**\n* @name Highcharts.RadianAngles#start\n* @type {number}\n*/\n''; // Keeps doclets above in JS file\n\n;// ./code/es-modules/Series/Sunburst/SunburstPoint.js\n/* *\n *\n *  This module implements sunburst charts in Highcharts.\n *\n *  (c) 2016-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { series: { prototype: { pointClass: Point } }, seriesTypes: { treemap: { prototype: { pointClass: SunburstPoint_TreemapPoint } } } } = (external_highcharts_src_js_default_SeriesRegistry_default());\n\nconst { correctFloat: SunburstPoint_correctFloat, extend: SunburstPoint_extend, pInt } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Class\n *\n * */\nclass SunburstPoint extends SunburstPoint_TreemapPoint {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    getDataLabelPath(label) {\n        const renderer = this.series.chart.renderer, shapeArgs = this.shapeExisting, r = shapeArgs.r + pInt(label.options?.distance || 0);\n        let start = shapeArgs.start, end = shapeArgs.end;\n        const angle = start + (end - start) / 2; // Arc middle value\n        let upperHalf = angle < 0 &&\n            angle > -Math.PI ||\n            angle > Math.PI, moreThanHalf;\n        // Check if point is a full circle\n        if (start === -Math.PI / 2 &&\n            SunburstPoint_correctFloat(end) === SunburstPoint_correctFloat(Math.PI * 1.5)) {\n            start = -Math.PI + Math.PI / 360;\n            end = -Math.PI / 360;\n            upperHalf = true;\n        }\n        // Check if dataLabels should be render in the upper half of the circle\n        if (end - start > Math.PI) {\n            upperHalf = false;\n            moreThanHalf = true;\n            // Close to the full circle, add some padding so that the SVG\n            // renderer treats it as separate points (#18884).\n            if ((end - start) > 2 * Math.PI - 0.01) {\n                start += 0.01;\n                end -= 0.01;\n            }\n        }\n        if (this.dataLabelPath) {\n            this.dataLabelPath = this.dataLabelPath.destroy();\n        }\n        // All times\n        this.dataLabelPath = renderer\n            .arc({\n            open: true,\n            longArc: moreThanHalf ? 1 : 0\n        })\n            .attr({\n            start: (upperHalf ? start : end),\n            end: (upperHalf ? end : start),\n            clockwise: +upperHalf,\n            x: shapeArgs.x,\n            y: shapeArgs.y,\n            r: (r + shapeArgs.innerR) / 2\n        })\n            .add(renderer.defs);\n        return this.dataLabelPath;\n    }\n    isValid() {\n        return true;\n    }\n}\nSunburstPoint_extend(SunburstPoint.prototype, {\n    getClassName: Point.prototype.getClassName,\n    haloPath: Point.prototype.haloPath,\n    setState: Point.prototype.setState\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Sunburst_SunburstPoint = (SunburstPoint);\n\n;// ./code/es-modules/Series/Sunburst/SunburstUtilities.js\n/* *\n *\n *  This module implements sunburst charts in Highcharts.\n *\n *  (c) 2016-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { seriesTypes: { treemap: SunburstUtilities_TreemapSeries } } = (external_highcharts_src_js_default_SeriesRegistry_default());\n\nconst { isNumber: SunburstUtilities_isNumber, isObject: SunburstUtilities_isObject, merge: SunburstUtilities_merge } = (external_highcharts_src_js_default_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n * @function calculateLevelSizes\n *\n * @param {Object} levelOptions\n * Map of level to its options.\n *\n * @param {Highcharts.Dictionary<number>} params\n * Object containing number parameters `innerRadius` and `outerRadius`.\n *\n * @return {Highcharts.SunburstSeriesLevelsOptions|undefined}\n * Returns the modified options, or undefined.\n */\nfunction calculateLevelSizes(levelOptions, params) {\n    const p = SunburstUtilities_isObject(params) ? params : {};\n    let result, totalWeight = 0, diffRadius, levels, levelsNotIncluded, remainingSize, from, to;\n    if (SunburstUtilities_isObject(levelOptions)) {\n        result = SunburstUtilities_merge({}, levelOptions);\n        from = SunburstUtilities_isNumber(p.from) ? p.from : 0;\n        to = SunburstUtilities_isNumber(p.to) ? p.to : 0;\n        levels = range(from, to);\n        levelsNotIncluded = Object.keys(result).filter((key) => (levels.indexOf(+key) === -1));\n        diffRadius = remainingSize = SunburstUtilities_isNumber(p.diffRadius) ?\n            p.diffRadius : 0;\n        // Convert percentage to pixels.\n        // Calculate the remaining size to divide between \"weight\" levels.\n        // Calculate total weight to use in conversion from weight to\n        // pixels.\n        for (const level of levels) {\n            const options = result[level], unit = options.levelSize.unit, value = options.levelSize.value;\n            if (unit === 'weight') {\n                totalWeight += value;\n            }\n            else if (unit === 'percentage') {\n                options.levelSize = {\n                    unit: 'pixels',\n                    value: (value / 100) * diffRadius\n                };\n                remainingSize -= options.levelSize.value;\n            }\n            else if (unit === 'pixels') {\n                remainingSize -= value;\n            }\n        }\n        // Convert weight to pixels.\n        for (const level of levels) {\n            const options = result[level];\n            if (options.levelSize.unit === 'weight') {\n                const weight = options.levelSize.value;\n                result[level].levelSize = {\n                    unit: 'pixels',\n                    value: (weight / totalWeight) * remainingSize\n                };\n            }\n        }\n        // Set all levels not included in interval [from,to] to have 0\n        // pixels.\n        for (const level of levelsNotIncluded) {\n            result[level].levelSize = {\n                value: 0,\n                unit: 'pixels'\n            };\n        }\n    }\n    return result;\n}\n/**\n * @private\n */\nfunction getLevelFromAndTo({ level, height }) {\n    //  Never displays level below 1\n    const from = level > 0 ? level : 1;\n    const to = level + height;\n    return { from, to };\n}\n/**\n * TODO introduce step, which should default to 1.\n * @private\n */\nfunction range(from, to) {\n    const result = [];\n    if (SunburstUtilities_isNumber(from) && SunburstUtilities_isNumber(to) && from <= to) {\n        for (let i = from; i <= to; i++) {\n            result.push(i);\n        }\n    }\n    return result;\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst SunburstUtilities = {\n    calculateLevelSizes,\n    getLevelFromAndTo,\n    range,\n    recursive: SunburstUtilities_TreemapSeries.prototype.utils.recursive\n};\n/* harmony default export */ const Sunburst_SunburstUtilities = (SunburstUtilities);\n\n;// ./code/es-modules/Series/Sunburst/SunburstNode.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n/* *\n *\n *  Class\n *\n * */\nclass SunburstNode extends Treemap_TreemapNode {\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Sunburst_SunburstNode = (SunburstNode);\n\n;// ./code/es-modules/Series/Sunburst/SunburstSeriesDefaults.js\n/* *\n *\n *  This module implements sunburst charts in Highcharts.\n *\n *  (c) 2016-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A Sunburst displays hierarchical data, where a level in the hierarchy is\n * represented by a circle. The center represents the root node of the tree.\n * The visualization bears a resemblance to both treemap and pie charts.\n *\n * @sample highcharts/demo/sunburst\n *         Sunburst chart\n *\n * @extends      plotOptions.pie\n * @excluding    allAreas, clip, colorAxis, colorKey, compare, compareBase,\n *               dataGrouping, depth, dragDrop, endAngle, gapSize, gapUnit,\n *               ignoreHiddenPoint, innerSize, joinBy, legendType, linecap,\n *               minSize, navigatorOptions, pointRange\n * @product      highcharts\n * @requires     modules/sunburst\n * @optionparent plotOptions.sunburst\n *\n * @private\n */\nconst SunburstSeriesDefaults = {\n    /**\n     * Options for the breadcrumbs, the navigation at the top leading the\n     * way up through the traversed levels.\n     *\n     * @since 10.0.0\n     * @product   highcharts\n     * @extends   navigation.breadcrumbs\n     * @apioption plotOptions.sunburst.breadcrumbs\n     */\n    /**\n     * Set options on specific levels. Takes precedence over series options,\n     * but not point options.\n     *\n     * @sample highcharts/demo/sunburst\n     *         Sunburst chart\n     *\n     * @type      {Array<*>}\n     * @apioption plotOptions.sunburst.levels\n     */\n    /**\n     * Can set a `borderColor` on all points which lies on the same level.\n     *\n     * @type      {Highcharts.ColorString}\n     * @apioption plotOptions.sunburst.levels.borderColor\n     */\n    /**\n     * Can set a `borderWidth` on all points which lies on the same level.\n     *\n     * @type      {number}\n     * @apioption plotOptions.sunburst.levels.borderWidth\n     */\n    /**\n     * Can set a `borderDashStyle` on all points which lies on the same\n     * level.\n     *\n     * @type      {Highcharts.DashStyleValue}\n     * @apioption plotOptions.sunburst.levels.borderDashStyle\n     */\n    /**\n     * Can set a `color` on all points which lies on the same level.\n     *\n     * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n     * @apioption plotOptions.sunburst.levels.color\n     */\n    /**\n     * Determines whether the chart should receive one color per point based\n     * on this level.\n     *\n     * @type      {boolean}\n     * @apioption plotOptions.sunburst.levels.colorByPoint\n     */\n    /**\n     * Can set a `colorVariation` on all points which lies on the same\n     * level.\n     *\n     * @apioption plotOptions.sunburst.levels.colorVariation\n     */\n    /**\n     * The key of a color variation. Currently supports `brightness` only.\n     *\n     * @type      {string}\n     * @apioption plotOptions.sunburst.levels.colorVariation.key\n     */\n    /**\n     * The ending value of a color variation. The last sibling will receive\n     * this value.\n     *\n     * @type      {number}\n     * @apioption plotOptions.sunburst.levels.colorVariation.to\n     */\n    /**\n     * Can set `dataLabels` on all points which lies on the same level.\n     *\n     * @extends   plotOptions.sunburst.dataLabels\n     * @apioption plotOptions.sunburst.levels.dataLabels\n     */\n    /**\n     * Decides which level takes effect from the options set in the levels\n     * object.\n     *\n     * @sample highcharts/demo/sunburst\n     *         Sunburst chart\n     *\n     * @type      {number}\n     * @apioption plotOptions.sunburst.levels.level\n     */\n    /**\n     * Can set a `levelSize` on all points which lies on the same level.\n     *\n     * @type      {Object}\n     * @apioption plotOptions.sunburst.levels.levelSize\n     */\n    /**\n     * When enabled the user can click on a point which is a parent and\n     * zoom in on its children. Deprecated and replaced by\n     * [allowTraversingTree](#plotOptions.sunburst.allowTraversingTree).\n     *\n     * @deprecated\n     * @type      {boolean}\n     * @default   false\n     * @since     6.0.0\n     * @product   highcharts\n     * @apioption plotOptions.sunburst.allowDrillToNode\n     */\n    /**\n     * When enabled the user can click on a point which is a parent and\n     * zoom in on its children.\n     *\n     * @type      {boolean}\n     * @default   false\n     * @since     7.0.3\n     * @product   highcharts\n     * @apioption plotOptions.sunburst.allowTraversingTree\n     */\n    /**\n     * The center of the sunburst chart relative to the plot area. Can be\n     * percentages or pixel values.\n     *\n     * @sample {highcharts} highcharts/plotoptions/pie-center/\n     *         Centered at 100, 100\n     *\n     * @type    {Array<number|string>}\n     * @default [\"50%\", \"50%\"]\n     * @product highcharts\n     *\n     * @private\n     */\n    center: ['50%', '50%'],\n    /**\n     * @product highcharts\n     *\n     * @private\n     */\n    clip: false,\n    colorByPoint: false,\n    /**\n     * Disable inherited opacity from Treemap series.\n     *\n     * @ignore-option\n     *\n     * @private\n     */\n    opacity: 1,\n    /**\n     * @declare Highcharts.SeriesSunburstDataLabelsOptionsObject\n     *\n     * @private\n     */\n    dataLabels: {\n        allowOverlap: true,\n        defer: true,\n        /**\n         * Decides how the data label will be rotated relative to the\n         * perimeter of the sunburst. Valid values are `circular`, `auto`,\n         * `parallel` and `perpendicular`. When `circular`, the best fit\n         * will be computed for the point, so that the label is curved\n         * around the center when there is room for it, otherwise\n         * perpendicular. The legacy `auto` option works similar to\n         * `circular`, but instead of curving the labels they are tangent to\n         * the perimeter.\n         *\n         * The `rotation` option takes precedence over `rotationMode`.\n         *\n         * @type       {string}\n         * @sample {highcharts}\n         *         highcharts/plotoptions/sunburst-datalabels-rotationmode-circular/\n         *         Circular rotation mode\n         * @validvalue [\"auto\", \"perpendicular\", \"parallel\", \"circular\"]\n         * @since      6.0.0\n         */\n        rotationMode: 'circular',\n        style: {\n            /** @internal */\n            textOverflow: 'ellipsis'\n        }\n    },\n    /**\n     * Which point to use as a root in the visualization.\n     *\n     * @type {string}\n     *\n     * @private\n     */\n    rootId: void 0,\n    /**\n     * Used together with the levels and `allowDrillToNode` options. When\n     * set to false the first level visible when drilling is considered\n     * to be level one. Otherwise the level will be the same as the tree\n     * structure.\n     *\n     * @private\n     */\n    levelIsConstant: true,\n    /**\n     * Determines the width of the ring per level.\n     *\n     * @sample {highcharts} highcharts/plotoptions/sunburst-levelsize/\n     *         Sunburst with various sizes per level\n     *\n     * @since 6.0.5\n     *\n     * @private\n     */\n    levelSize: {\n        /**\n         * The value used for calculating the width of the ring. Its' affect\n         * is determined by `levelSize.unit`.\n         *\n         * @sample {highcharts} highcharts/plotoptions/sunburst-levelsize/\n         *         Sunburst with various sizes per level\n         */\n        value: 1,\n        /**\n         * How to interpret `levelSize.value`.\n         *\n         * - `percentage` gives a width relative to result of outer radius\n         *   minus inner radius.\n         *\n         * - `pixels` gives the ring a fixed width in pixels.\n         *\n         * - `weight` takes the remaining width after percentage and pixels,\n         *   and distributes it across all \"weighted\" levels. The value\n         *   relative to the sum of all weights determines the width.\n         *\n         * @sample {highcharts} highcharts/plotoptions/sunburst-levelsize/\n         *         Sunburst with various sizes per level\n         *\n         * @validvalue [\"percentage\", \"pixels\", \"weight\"]\n         */\n        unit: 'weight'\n    },\n    /**\n     * Options for the button appearing when traversing down in a sunburst.\n     * Since v9.3.3 the `traverseUpButton` is replaced by `breadcrumbs`.\n     *\n     * @extends   plotOptions.treemap.traverseUpButton\n     * @since     6.0.0\n     * @deprecated\n     * @apioption plotOptions.sunburst.traverseUpButton\n     *\n     */\n    /**\n     * If a point is sliced, moved out from the center, how many pixels\n     * should it be moved?.\n     *\n     * @sample highcharts/plotoptions/sunburst-sliced\n     *         Sliced sunburst\n     *\n     * @since 6.0.4\n     *\n     * @private\n     */\n    slicedOffset: 10\n};\n/**\n * A `sunburst` series. If the [type](#series.sunburst.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.sunburst\n * @excluding dataParser, dataURL, stack, dataSorting, boostThreshold,\n *            boostBlending\n * @product   highcharts\n * @requires  modules/sunburst\n * @apioption series.sunburst\n */\n/**\n * @type      {Array<number|null|*>}\n * @extends   series.treemap.data\n * @excluding x, y\n * @product   highcharts\n * @apioption series.sunburst.data\n */\n/**\n * @type      {Highcharts.SeriesSunburstDataLabelsOptionsObject|Array<Highcharts.SeriesSunburstDataLabelsOptionsObject>}\n * @product   highcharts\n * @apioption series.sunburst.data.dataLabels\n */\n/**\n * The value of the point, resulting in a relative area of the point\n * in the sunburst.\n *\n * @type      {number|null}\n * @since     6.0.0\n * @product   highcharts\n * @apioption series.sunburst.data.value\n */\n/**\n * Use this option to build a tree structure. The value should be the id of the\n * point which is the parent. If no points has a matching id, or this option is\n * undefined, then the parent will be set to the root.\n *\n * @type      {string}\n * @since     6.0.0\n * @product   highcharts\n * @apioption series.sunburst.data.parent\n */\n/**\n  * Whether to display a slice offset from the center. When a sunburst point is\n  * sliced, its children are also offset.\n  *\n  * @sample highcharts/plotoptions/sunburst-sliced\n  *         Sliced sunburst\n  *\n  * @type      {boolean}\n  * @default   false\n  * @since     6.0.4\n  * @product   highcharts\n  * @apioption series.sunburst.data.sliced\n  */\n''; // Detach doclets above\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Sunburst_SunburstSeriesDefaults = (SunburstSeriesDefaults);\n\n;// ./code/es-modules/Extensions/TextPath.js\n/* *\n *\n *  Highcharts module with textPath functionality.\n *\n *  (c) 2009-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { deg2rad: TextPath_deg2rad } = (external_highcharts_src_js_default_default());\nconst { addEvent: TextPath_addEvent, merge: TextPath_merge, uniqueKey, defined: TextPath_defined, extend: TextPath_extend } = (external_highcharts_src_js_default_default());\n/**\n * Set a text path for a `text` or `label` element, allowing the text to\n * flow along a path.\n *\n * In order to unset the path for an existing element, call `setTextPath`\n * with `{ enabled: false }` as the second argument.\n *\n * Text path support is not bundled into `highcharts.js`, and requires the\n * `modules/textpath.js` file. However, it is included in the script files of\n * those series types that use it by default\n *\n * @sample highcharts/members/renderer-textpath/ Text path demonstrated\n *\n * @function Highcharts.SVGElement#setTextPath\n *\n * @param {Highcharts.SVGElement|undefined} path\n *        Path to follow. If undefined, it allows changing options for the\n *        existing path.\n *\n * @param {Highcharts.DataLabelsTextPathOptionsObject} textPathOptions\n *        Options.\n *\n * @return {Highcharts.SVGElement} Returns the SVGElement for chaining.\n */\nfunction setTextPath(path, textPathOptions) {\n    // Defaults\n    textPathOptions = TextPath_merge(true, {\n        enabled: true,\n        attributes: {\n            dy: -5,\n            startOffset: '50%',\n            textAnchor: 'middle'\n        }\n    }, textPathOptions);\n    const url = this.renderer.url, textWrapper = this.text || this, textPath = textWrapper.textPath, { attributes, enabled } = textPathOptions;\n    path = path || (textPath && textPath.path);\n    // Remove previously added event\n    if (textPath) {\n        textPath.undo();\n    }\n    if (path && enabled) {\n        const undo = TextPath_addEvent(textWrapper, 'afterModifyTree', (e) => {\n            if (path && enabled) {\n                // Set ID for the path\n                let textPathId = path.attr('id');\n                if (!textPathId) {\n                    path.attr('id', textPathId = uniqueKey());\n                }\n                // Set attributes for the <text>\n                const textAttribs = {\n                    // `dx`/`dy` options must by set on <text> (parent), the\n                    // rest should be set on <textPath>\n                    x: 0,\n                    y: 0\n                };\n                if (TextPath_defined(attributes.dx)) {\n                    textAttribs.dx = attributes.dx;\n                    delete attributes.dx;\n                }\n                if (TextPath_defined(attributes.dy)) {\n                    textAttribs.dy = attributes.dy;\n                    delete attributes.dy;\n                }\n                textWrapper.attr(textAttribs);\n                // Handle label properties\n                this.attr({ transform: '' });\n                if (this.box) {\n                    this.box = this.box.destroy();\n                }\n                // Wrap the nodes in a textPath\n                const children = e.nodes.slice(0);\n                e.nodes.length = 0;\n                e.nodes[0] = {\n                    tagName: 'textPath',\n                    attributes: TextPath_extend(attributes, {\n                        'text-anchor': attributes.textAnchor,\n                        href: `${url}#${textPathId}`\n                    }),\n                    children\n                };\n            }\n        });\n        // Set the reference\n        textWrapper.textPath = { path, undo };\n    }\n    else {\n        textWrapper.attr({ dx: 0, dy: 0 });\n        delete textWrapper.textPath;\n    }\n    if (this.added) {\n        // Rebuild text after added\n        textWrapper.textCache = '';\n        this.renderer.buildText(textWrapper);\n    }\n    return this;\n}\n/**\n * Attach a polygon to a bounding box if the element contains a textPath.\n *\n * @function Highcharts.SVGElement#setPolygon\n *\n * @param {any} event\n *        An event containing a bounding box object\n *\n * @return {Highcharts.BBoxObject} Returns the bounding box object.\n */\nfunction setPolygon(event) {\n    const bBox = event.bBox, tp = this.element?.querySelector('textPath');\n    if (tp) {\n        const polygon = [], { b, h } = this.renderer.fontMetrics(this.element), descender = h - b, lineCleanerRegex = new RegExp('(<tspan>|' +\n            '<tspan(?!\\\\sclass=\"highcharts-br\")[^>]*>|' +\n            '<\\\\/tspan>)', 'g'), lines = tp\n            .innerHTML\n            .replace(lineCleanerRegex, '')\n            .split(/<tspan class=\"highcharts-br\"[^>]*>/), numOfLines = lines.length;\n        // Calculate top and bottom coordinates for\n        // either the start or the end of a single\n        // character, and append it to the polygon.\n        const appendTopAndBottom = (charIndex, positionOfChar) => {\n            const { x, y } = positionOfChar, rotation = (tp.getRotationOfChar(charIndex) - 90) * TextPath_deg2rad, cosRot = Math.cos(rotation), sinRot = Math.sin(rotation);\n            return [\n                [\n                    x - descender * cosRot,\n                    y - descender * sinRot\n                ],\n                [\n                    x + b * cosRot,\n                    y + b * sinRot\n                ]\n            ];\n        };\n        for (let i = 0, lineIndex = 0; lineIndex < numOfLines; lineIndex++) {\n            const line = lines[lineIndex], lineLen = line.length;\n            for (let lineCharIndex = 0; lineCharIndex < lineLen; lineCharIndex += 5) {\n                try {\n                    const srcCharIndex = (i +\n                        lineCharIndex +\n                        lineIndex), [lower, upper] = appendTopAndBottom(srcCharIndex, tp.getStartPositionOfChar(srcCharIndex));\n                    if (lineCharIndex === 0) {\n                        polygon.push(upper);\n                        polygon.push(lower);\n                    }\n                    else {\n                        if (lineIndex === 0) {\n                            polygon.unshift(upper);\n                        }\n                        if (lineIndex === numOfLines - 1) {\n                            polygon.push(lower);\n                        }\n                    }\n                }\n                catch (e) {\n                    // Safari fails on getStartPositionOfChar even if the\n                    // character is within the `textContent.length`\n                    break;\n                }\n            }\n            i += lineLen - 1;\n            try {\n                const srcCharIndex = i + lineIndex, charPos = tp.getEndPositionOfChar(srcCharIndex), [lower, upper] = appendTopAndBottom(srcCharIndex, charPos);\n                polygon.unshift(upper);\n                polygon.unshift(lower);\n            }\n            catch (e) {\n                // Safari fails on getStartPositionOfChar even if the character\n                // is within the `textContent.length`\n                break;\n            }\n        }\n        // Close it\n        if (polygon.length) {\n            polygon.push(polygon[0].slice());\n        }\n        bBox.polygon = polygon;\n    }\n    return bBox;\n}\n/**\n * Draw text along a textPath for a dataLabel.\n *\n * @function Highcharts.SVGElement#setTextPath\n *\n * @param {any} event\n *        An event containing label options\n *\n * @return {void}\n */\nfunction drawTextPath(event) {\n    const labelOptions = event.labelOptions, point = event.point, textPathOptions = (labelOptions[point.formatPrefix + 'TextPath'] ||\n        labelOptions.textPath);\n    if (textPathOptions && !labelOptions.useHTML) {\n        this.setTextPath(point.getDataLabelPath?.(this) || point.graphic, textPathOptions);\n        if (point.dataLabelPath &&\n            !textPathOptions.enabled) {\n            // Clean the DOM\n            point.dataLabelPath = (point.dataLabelPath.destroy());\n        }\n    }\n}\nfunction compose(SVGElementClass) {\n    TextPath_addEvent(SVGElementClass, 'afterGetBBox', setPolygon);\n    TextPath_addEvent(SVGElementClass, 'beforeAddingDataLabel', drawTextPath);\n    const svgElementProto = SVGElementClass.prototype;\n    if (!svgElementProto.setTextPath) {\n        svgElementProto.setTextPath = setTextPath;\n    }\n}\nconst TextPath = {\n    compose\n};\n/* harmony default export */ const Extensions_TextPath = (TextPath);\n\n;// ./code/es-modules/Series/Sunburst/SunburstSeries.js\n/* *\n *\n *  This module implements sunburst charts in Highcharts.\n *\n *  (c) 2016-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { getCenter, getStartAndEndRadians } = Series_CenteredUtilities;\n\nconst { noop: SunburstSeries_noop } = (external_highcharts_src_js_default_default());\n\nconst { column: SunburstSeries_ColumnSeries, treemap: SunburstSeries_TreemapSeries } = (external_highcharts_src_js_default_SeriesRegistry_default()).seriesTypes;\n\n\n\nconst { getColor: SunburstSeries_getColor, getLevelOptions: SunburstSeries_getLevelOptions, setTreeValues: SunburstSeries_setTreeValues, updateRootId: SunburstSeries_updateRootId } = Series_TreeUtilities;\n\n\n\nconst { defined: SunburstSeries_defined, error: SunburstSeries_error, extend: SunburstSeries_extend, fireEvent: SunburstSeries_fireEvent, isNumber: SunburstSeries_isNumber, isObject: SunburstSeries_isObject, isString: SunburstSeries_isString, merge: SunburstSeries_merge, splat: SunburstSeries_splat } = (external_highcharts_src_js_default_default());\n\n\nExtensions_TextPath.compose((external_highcharts_src_js_default_SVGElement_default()));\n/* *\n *\n *  Constants\n *\n * */\nconst rad2deg = 180 / Math.PI;\n/* *\n *\n *  Functions\n *\n * */\n/** @private */\nfunction isBoolean(x) {\n    return typeof x === 'boolean';\n}\n/**\n * Find a set of coordinates given a start coordinates, an angle, and a\n * distance.\n *\n * @private\n * @function getEndPoint\n *\n * @param {number} x\n *        Start coordinate x\n *\n * @param {number} y\n *        Start coordinate y\n *\n * @param {number} angle\n *        Angle in radians\n *\n * @param {number} distance\n *        Distance from start to end coordinates\n *\n * @return {Highcharts.SVGAttributes}\n *         Returns the end coordinates, x and y.\n */\nconst getEndPoint = function getEndPoint(x, y, angle, distance) {\n    return {\n        x: x + (Math.cos(angle) * distance),\n        y: y + (Math.sin(angle) * distance)\n    };\n};\n/** @private */\nfunction getDlOptions(params) {\n    // Set options to new object to avoid problems with scope\n    const point = params.point, shape = SunburstSeries_isObject(params.shapeArgs) ? params.shapeArgs : {}, { end = 0, radius = 0, start = 0 } = shape, optionsPoint = (SunburstSeries_isObject(params.optionsPoint) ?\n        params.optionsPoint.dataLabels :\n        {}), \n    // The splat was used because levels dataLabels\n    // options doesn't work as an array\n    optionsLevel = SunburstSeries_splat(SunburstSeries_isObject(params.level) ?\n        params.level.dataLabels :\n        {})[0], options = SunburstSeries_merge(optionsLevel, optionsPoint), style = options.style = options.style || {}, { innerArcLength = 0, outerArcLength = 0 } = point;\n    let rotationRad, rotation, rotationMode = options.rotationMode, width = SunburstSeries_defined(style.width) ?\n        parseInt(style.width || '0', 10) : void 0;\n    if (!SunburstSeries_isNumber(options.rotation)) {\n        if (rotationMode === 'auto' || rotationMode === 'circular') {\n            if (options.useHTML &&\n                rotationMode === 'circular') {\n                // Change rotationMode to 'auto' to avoid using text paths\n                // for HTML labels, see #18953\n                rotationMode = 'auto';\n            }\n            if (innerArcLength < 1 && outerArcLength > radius) {\n                rotationRad = 0;\n                // Trigger setTextPath function to get textOutline etc.\n                if (point.dataLabelPath && rotationMode === 'circular') {\n                    options.textPath = {\n                        enabled: true\n                    };\n                }\n                // If the slice is less than 180 degrees, set a reasonable width\n                // for fitting into the open slice (#22532)\n                if (end - start < Math.PI) {\n                    width = radius * 0.7;\n                }\n            }\n            else if (innerArcLength > 1 && outerArcLength > 1.5 * radius) {\n                if (rotationMode === 'circular') {\n                    options.textPath = {\n                        enabled: true,\n                        attributes: {\n                            dy: 5\n                        }\n                    };\n                }\n                else {\n                    rotationMode = 'parallel';\n                }\n            }\n            else {\n                // Trigger the destroyTextPath function\n                if (point.dataLabel?.textPath &&\n                    rotationMode === 'circular') {\n                    options.textPath = {\n                        enabled: false\n                    };\n                }\n                rotationMode = 'perpendicular';\n            }\n        }\n        if (rotationMode !== 'auto' && rotationMode !== 'circular') {\n            if (point.dataLabel?.textPath) {\n                options.textPath = {\n                    enabled: false\n                };\n            }\n            rotationRad = end - (end - start) / 2;\n        }\n        if (rotationMode === 'parallel') {\n            width = Math.min(radius * 2.5, (outerArcLength + innerArcLength) / 2);\n        }\n        else {\n            if (!SunburstSeries_defined(width) && radius) {\n                width = point.node.level === 1 ? 2 * radius : radius;\n            }\n        }\n        if (rotationMode === 'perpendicular') {\n            // 16 is the inferred line height. We don't know the real line\n            // yet because the label is not rendered. A better approach for this\n            // would be to hide the label from the `alignDataLabel` function\n            // when the actual line height is known.\n            const h = 16;\n            if (outerArcLength < h) {\n                width = 1;\n            }\n            else if (shape.radius) {\n                style.lineClamp = Math.floor(innerArcLength / h) || 1;\n                // When the slice is narrow (< 16px) in the inner end, compute a\n                // safe margin to avoid the label overlapping the border\n                // (#22532)\n                const safeMargin = innerArcLength < h ?\n                    radius * ((h - innerArcLength) /\n                        (outerArcLength - innerArcLength)) :\n                    0;\n                width = radius - safeMargin;\n            }\n        }\n        // Apply padding (#8515)\n        width = Math.max((width || 0) - 2 * (options.padding || 0), 1);\n        rotation = ((rotationRad || 0) * rad2deg) % 180;\n        if (rotationMode === 'parallel') {\n            rotation -= 90;\n        }\n        // Prevent text from rotating upside down\n        if (rotation > 90) {\n            rotation -= 180;\n        }\n        else if (rotation < -90) {\n            rotation += 180;\n        }\n        options.rotation = rotation;\n    }\n    if (options.textPath) {\n        if (point.shapeExisting.innerR === 0 &&\n            options.textPath.enabled) {\n            // Enable rotation to render text\n            options.rotation = 0;\n            // Center dataLabel - disable textPath\n            options.textPath.enabled = false;\n            // Setting width and padding\n            width = Math.max((point.shapeExisting.r * 2) -\n                2 * (options.padding || 0), 1);\n        }\n        else if (point.dlOptions?.textPath &&\n            !point.dlOptions.textPath.enabled &&\n            rotationMode === 'circular') {\n            // Bring dataLabel back if was a center dataLabel\n            options.textPath.enabled = true;\n        }\n        if (options.textPath.enabled) {\n            // Enable rotation to render text\n            options.rotation = 0;\n            // Setting width and padding\n            width = Math.max((outerArcLength + innerArcLength) / 2 -\n                2 * (options.padding || 0), 1);\n            style.whiteSpace = 'nowrap';\n        }\n    }\n    style.width = width + 'px';\n    return options;\n}\n/** @private */\nfunction getAnimation(shape, params) {\n    const point = params.point, radians = params.radians, innerR = params.innerR, idRoot = params.idRoot, idPreviousRoot = params.idPreviousRoot, shapeExisting = params.shapeExisting, shapeRoot = params.shapeRoot, shapePreviousRoot = params.shapePreviousRoot, visible = params.visible;\n    let from = {}, to = {\n        end: shape.end,\n        start: shape.start,\n        innerR: shape.innerR,\n        r: shape.r,\n        x: shape.x,\n        y: shape.y\n    };\n    if (visible) {\n        // Animate points in\n        if (!point.graphic && shapePreviousRoot) {\n            if (idRoot === point.id) {\n                from = {\n                    start: radians.start,\n                    end: radians.end\n                };\n            }\n            else {\n                from = (shapePreviousRoot.end <= shape.start) ? {\n                    start: radians.end,\n                    end: radians.end\n                } : {\n                    start: radians.start,\n                    end: radians.start\n                };\n            }\n            // Animate from center and outwards.\n            from.innerR = from.r = innerR;\n        }\n    }\n    else {\n        // Animate points out\n        if (point.graphic) {\n            if (idPreviousRoot === point.id) {\n                to = {\n                    innerR: innerR,\n                    r: innerR\n                };\n            }\n            else if (shapeRoot) {\n                to = (shapeRoot.end <= shapeExisting.start) ?\n                    {\n                        innerR: innerR,\n                        r: innerR,\n                        start: radians.end,\n                        end: radians.end\n                    } : {\n                    innerR: innerR,\n                    r: innerR,\n                    start: radians.start,\n                    end: radians.start\n                };\n            }\n        }\n    }\n    return {\n        from: from,\n        to: to\n    };\n}\n/** @private */\nfunction getDrillId(point, idRoot, mapIdToNode) {\n    const node = point.node;\n    let drillId, nodeRoot;\n    if (!node.isLeaf) {\n        // When it is the root node, the drillId should be set to parent.\n        if (idRoot === point.id) {\n            nodeRoot = mapIdToNode[idRoot];\n            drillId = nodeRoot.parent;\n        }\n        else {\n            drillId = point.id;\n        }\n    }\n    return drillId;\n}\n/** @private */\nfunction cbSetTreeValuesBefore(node, options) {\n    const mapIdToNode = options.mapIdToNode, parent = node.parent, nodeParent = parent ? mapIdToNode[parent] : void 0, series = options.series, chart = series.chart, points = series.points, point = points[node.i], colors = series.options.colors || chart && chart.options.colors, colorInfo = SunburstSeries_getColor(node, {\n        colors: colors,\n        colorIndex: series.colorIndex,\n        index: options.index,\n        mapOptionsToLevel: options.mapOptionsToLevel,\n        parentColor: nodeParent && nodeParent.color,\n        parentColorIndex: nodeParent && nodeParent.colorIndex,\n        series: options.series,\n        siblings: options.siblings\n    });\n    node.color = colorInfo.color;\n    node.colorIndex = colorInfo.colorIndex;\n    if (point) {\n        point.color = node.color;\n        point.colorIndex = node.colorIndex;\n        // Set slicing on node, but avoid slicing the top node.\n        node.sliced = (node.id !== options.idRoot) ? point.sliced : false;\n    }\n    return node;\n}\n/* *\n *\n *  Class\n *\n * */\nclass SunburstSeries extends SunburstSeries_TreemapSeries {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    alignDataLabel(point, dataLabel, labelOptions) {\n        if (labelOptions.textPath && labelOptions.textPath.enabled) {\n            return;\n        }\n        // In sunburst dataLabel may be placed, but this should be reset to\n        // make sure the dataLabel can be aligned to a new position (#21913)\n        dataLabel.placed = false;\n        return super.alignDataLabel.apply(this, arguments);\n    }\n    /**\n     * Animate the slices in. Similar to the animation of polar charts.\n     * @private\n     */\n    animate(init) {\n        const chart = this.chart, center = [\n            chart.plotWidth / 2,\n            chart.plotHeight / 2\n        ], plotLeft = chart.plotLeft, plotTop = chart.plotTop, group = this.group;\n        let attribs;\n        // Initialize the animation\n        if (init) {\n            // Scale down the group and place it in the center\n            attribs = {\n                translateX: center[0] + plotLeft,\n                translateY: center[1] + plotTop,\n                scaleX: 0.001, // #1499\n                scaleY: 0.001,\n                rotation: 10,\n                opacity: 0.01\n            };\n            group.attr(attribs);\n            // Run the animation\n        }\n        else {\n            attribs = {\n                translateX: plotLeft,\n                translateY: plotTop,\n                scaleX: 1,\n                scaleY: 1,\n                rotation: 0,\n                opacity: 1\n            };\n            group.animate(attribs, this.options.animation);\n        }\n    }\n    drawPoints() {\n        const series = this, mapOptionsToLevel = series.mapOptionsToLevel, shapeRoot = series.shapeRoot, group = series.group, hasRendered = series.hasRendered, idRoot = series.rootNode, idPreviousRoot = series.idPreviousRoot, nodeMap = series.nodeMap, nodePreviousRoot = nodeMap[idPreviousRoot], shapePreviousRoot = nodePreviousRoot && nodePreviousRoot.shapeArgs, points = series.points, radians = series.startAndEndRadians, chart = series.chart, optionsChart = chart && chart.options && chart.options.chart || {}, animation = (isBoolean(optionsChart.animation) ?\n            optionsChart.animation :\n            true), positions = series.center, center = {\n            x: positions[0],\n            y: positions[1]\n        }, innerR = positions[3] / 2, renderer = series.chart.renderer, hackDataLabelAnimation = !!(animation &&\n            hasRendered &&\n            idRoot !== idPreviousRoot &&\n            series.dataLabelsGroup);\n        let animateLabels, animateLabelsCalled = false, addedHack = false;\n        if (hackDataLabelAnimation) {\n            series.dataLabelsGroup.attr({ opacity: 0 });\n            animateLabels = function () {\n                const s = series;\n                animateLabelsCalled = true;\n                if (s.dataLabelsGroup) {\n                    s.dataLabelsGroup.animate({\n                        opacity: 1,\n                        visibility: 'inherit'\n                    });\n                }\n            };\n        }\n        for (const point of points) {\n            const node = point.node, level = mapOptionsToLevel[node.level], shapeExisting = (point.shapeExisting || {}), shape = node.shapeArgs || {}, visible = !!(node.visible && node.shapeArgs);\n            let animationInfo, onComplete;\n            // Border radius requires the border-radius.js module. Adding it\n            // here because the SunburstSeries is a mess and I can't find the\n            // regular shapeArgs. Usually shapeArgs are created in the series'\n            // `translate` function and then passed directly on to the renderer\n            // in the `drawPoints` function.\n            shape.borderRadius = series.options.borderRadius;\n            if (hasRendered && animation) {\n                animationInfo = getAnimation(shape, {\n                    center: center,\n                    point: point,\n                    radians: radians,\n                    innerR: innerR,\n                    idRoot: idRoot,\n                    idPreviousRoot: idPreviousRoot,\n                    shapeExisting: shapeExisting,\n                    shapeRoot: shapeRoot,\n                    shapePreviousRoot: shapePreviousRoot,\n                    visible: visible\n                });\n            }\n            else {\n                // When animation is disabled, attr is called from animation.\n                animationInfo = {\n                    to: shape,\n                    from: {}\n                };\n            }\n            SunburstSeries_extend(point, {\n                shapeExisting: shape, // Store for use in animation\n                tooltipPos: [shape.plotX, shape.plotY],\n                drillId: getDrillId(point, idRoot, nodeMap),\n                name: '' + (point.name || point.id || point.index),\n                plotX: shape.plotX, // Used for data label position\n                plotY: shape.plotY, // Used for data label position\n                value: node.val,\n                isInside: visible,\n                isNull: !visible // Used for dataLabels & point.draw\n            });\n            point.dlOptions = getDlOptions({\n                point: point,\n                level: level,\n                optionsPoint: point.options,\n                shapeArgs: shape\n            });\n            if (!addedHack && visible) {\n                addedHack = true;\n                onComplete = animateLabels;\n            }\n            point.draw({\n                animatableAttribs: animationInfo.to,\n                attribs: SunburstSeries_extend(animationInfo.from, (!chart.styledMode && series.pointAttribs(point, (point.selected && 'select')))),\n                onComplete: onComplete,\n                group: group,\n                renderer: renderer,\n                shapeType: 'arc',\n                shapeArgs: shape\n            });\n        }\n        // Draw data labels after points\n        // TODO draw labels one by one to avoid additional looping\n        if (hackDataLabelAnimation && addedHack) {\n            series.hasRendered = false;\n            series.options.dataLabels.defer = true;\n            SunburstSeries_ColumnSeries.prototype.drawDataLabels.call(series);\n            series.hasRendered = true;\n            // If animateLabels is called before labels were hidden, then call\n            // it again.\n            if (animateLabelsCalled) {\n                animateLabels();\n            }\n        }\n        else {\n            SunburstSeries_ColumnSeries.prototype.drawDataLabels.call(series);\n        }\n        series.idPreviousRoot = idRoot;\n    }\n    /**\n     * The layout algorithm for the levels.\n     * @private\n     */\n    layoutAlgorithm(parent, children, options) {\n        let startAngle = parent.start;\n        const range = parent.end - startAngle, total = parent.val, x = parent.x, y = parent.y, radius = ((options &&\n            SunburstSeries_isObject(options.levelSize) &&\n            SunburstSeries_isNumber(options.levelSize.value)) ?\n            options.levelSize.value :\n            0), innerRadius = parent.r, outerRadius = innerRadius + radius, slicedOffset = options && SunburstSeries_isNumber(options.slicedOffset) ?\n            options.slicedOffset :\n            0;\n        return (children || []).reduce((arr, child) => {\n            const percentage = (1 / total) * child.val, radians = percentage * range, radiansCenter = startAngle + (radians / 2), offsetPosition = getEndPoint(x, y, radiansCenter, slicedOffset), values = {\n                x: child.sliced ? offsetPosition.x : x,\n                y: child.sliced ? offsetPosition.y : y,\n                innerR: innerRadius,\n                r: outerRadius,\n                radius: radius,\n                start: startAngle,\n                end: startAngle + radians\n            };\n            arr.push(values);\n            startAngle = values.end;\n            return arr;\n        }, []);\n    }\n    setRootNode(id, redraw, eventArguments) {\n        const series = this;\n        if ( // If the target node is the only one at level 1, skip it. (#18658)\n        series.nodeMap[id].level === 1 &&\n            series.nodeList\n                .filter((node) => node.level === 1)\n                .length === 1) {\n            if (series.idPreviousRoot === '') {\n                return;\n            }\n            id = '';\n        }\n        super.setRootNode(id, redraw, eventArguments);\n    }\n    /**\n     * Set the shape arguments on the nodes. Recursive from root down.\n     * @private\n     */\n    setShapeArgs(parent, parentValues, mapOptionsToLevel) {\n        const level = parent.level + 1, options = mapOptionsToLevel[level], \n        // Collect all children which should be included\n        children = parent.children.filter(function (n) {\n            return n.visible;\n        }), twoPi = 6.28; // Two times Pi.\n        let childrenValues = [];\n        childrenValues = this.layoutAlgorithm(parentValues, children, options);\n        let i = -1;\n        for (const child of children) {\n            const values = childrenValues[++i], angle = values.start + ((values.end - values.start) / 2), radius = values.innerR + ((values.r - values.innerR) / 2), radians = (values.end - values.start), isCircle = (values.innerR === 0 && radians > twoPi), center = (isCircle ?\n                { x: values.x, y: values.y } :\n                getEndPoint(values.x, values.y, angle, radius)), val = (child.val ?\n                (child.childrenTotal > child.val ?\n                    child.childrenTotal :\n                    child.val) :\n                child.childrenTotal);\n            // The inner arc length is a convenience for data label filters.\n            if (this.points[child.i]) {\n                this.points[child.i].innerArcLength = radians * values.innerR;\n                this.points[child.i].outerArcLength = radians * values.r;\n            }\n            child.shapeArgs = SunburstSeries_merge(values, {\n                plotX: center.x,\n                plotY: center.y\n            });\n            child.values = SunburstSeries_merge(values, {\n                val: val\n            });\n            // If node has children, then call method recursively\n            if (child.children.length) {\n                this.setShapeArgs(child, child.values, mapOptionsToLevel);\n            }\n        }\n    }\n    translate() {\n        const series = this, options = series.options, positions = series.center = series.getCenter(), radians = series.startAndEndRadians = getStartAndEndRadians(options.startAngle, options.endAngle), innerRadius = positions[3] / 2, outerRadius = positions[2] / 2, diffRadius = outerRadius - innerRadius, \n        // NOTE: updateRootId modifies series.\n        rootId = SunburstSeries_updateRootId(series);\n        let mapIdToNode = series.nodeMap, mapOptionsToLevel, nodeRoot = mapIdToNode && mapIdToNode[rootId], nodeIds = {};\n        series.shapeRoot = nodeRoot && nodeRoot.shapeArgs;\n        series.generatePoints();\n        SunburstSeries_fireEvent(series, 'afterTranslate');\n        // @todo Only if series.isDirtyData is true\n        const tree = series.tree = series.getTree();\n        // Render traverseUpButton, after series.nodeMap i calculated.\n        mapIdToNode = series.nodeMap;\n        nodeRoot = mapIdToNode[rootId];\n        const idTop = SunburstSeries_isString(nodeRoot.parent) ? nodeRoot.parent : '', nodeTop = mapIdToNode[idTop], { from, to } = Sunburst_SunburstUtilities.getLevelFromAndTo(nodeRoot);\n        mapOptionsToLevel = SunburstSeries_getLevelOptions({\n            from,\n            levels: series.options.levels,\n            to,\n            defaults: {\n                colorByPoint: options.colorByPoint,\n                dataLabels: options.dataLabels,\n                levelIsConstant: options.levelIsConstant,\n                levelSize: options.levelSize,\n                slicedOffset: options.slicedOffset\n            }\n        });\n        // NOTE consider doing calculateLevelSizes in a callback to\n        // getLevelOptions\n        mapOptionsToLevel = Sunburst_SunburstUtilities.calculateLevelSizes(mapOptionsToLevel, {\n            diffRadius,\n            from,\n            to\n        });\n        // TODO Try to combine setTreeValues & setColorRecursive to avoid\n        //  unnecessary looping.\n        SunburstSeries_setTreeValues(tree, {\n            before: cbSetTreeValuesBefore,\n            idRoot: rootId,\n            levelIsConstant: options.levelIsConstant,\n            mapOptionsToLevel: mapOptionsToLevel,\n            mapIdToNode: mapIdToNode,\n            points: series.points,\n            series: series\n        });\n        const values = mapIdToNode[''].shapeArgs = {\n            end: radians.end,\n            r: innerRadius,\n            start: radians.start,\n            val: nodeRoot.val,\n            x: positions[0],\n            y: positions[1]\n        };\n        this.setShapeArgs(nodeTop, values, mapOptionsToLevel);\n        // Set mapOptionsToLevel on series for use in drawPoints.\n        series.mapOptionsToLevel = mapOptionsToLevel;\n        // #10669 - verify if all nodes have unique ids\n        for (const point of series.points) {\n            if (nodeIds[point.id]) {\n                SunburstSeries_error(31, false, series.chart);\n            }\n            // Map\n            nodeIds[point.id] = true;\n        }\n        // Reset object\n        nodeIds = {};\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nSunburstSeries.defaultOptions = SunburstSeries_merge(SunburstSeries_TreemapSeries.defaultOptions, Sunburst_SunburstSeriesDefaults);\nSunburstSeries_extend(SunburstSeries.prototype, {\n    axisTypes: [],\n    drawDataLabels: SunburstSeries_noop, // `drawDataLabels` is called in `drawPoints`\n    getCenter: getCenter,\n    isCartesian: false,\n    // Mark that the sunburst is supported by the series on point feature.\n    onPointSupported: true,\n    pointAttribs: SunburstSeries_ColumnSeries.prototype.pointAttribs,\n    pointClass: Sunburst_SunburstPoint,\n    NodeClass: Sunburst_SunburstNode,\n    utils: Sunburst_SunburstUtilities\n});\nexternal_highcharts_src_js_default_SeriesRegistry_default().registerSeriesType('sunburst', SunburstSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Sunburst_SunburstSeries = ((/* unused pure expression or super */ null && (SunburstSeries)));\n\n;// ./code/es-modules/masters/modules/sunburst.js\n\n\n\n\n\n\nconst G = (external_highcharts_src_js_default_default());\nG.Breadcrumbs = G.Breadcrumbs || Breadcrumbs_Breadcrumbs;\nG.Breadcrumbs.compose(G.Chart, G.defaultOptions);\n/* harmony default export */ const sunburst_src = ((external_highcharts_src_js_default_default()));\n\nexport { sunburst_src as default };\n"], "names": ["__WEBPACK_EXTERNAL_MODULE__highcharts_src_js_8202131d__", "CenteredUtilities", "ColorMapComposition", "TreemapUtilities", "__webpack_require__", "n", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "external_highcharts_src_js_default_namespaceObject", "external_highcharts_src_js_default_default", "Breadcrumbs_BreadcrumbsDefaults", "lang", "mainBreadcrumb", "options", "buttonTheme", "fill", "height", "padding", "zIndex", "states", "select", "style", "color", "buttonSpacing", "floating", "format", "relativeTo", "rtl", "position", "align", "verticalAlign", "x", "y", "separator", "text", "fontSize", "show<PERSON>ull<PERSON>ath", "useHTML", "external_highcharts_src_js_default_Templating_namespaceObject", "Templating", "external_highcharts_src_js_default_Templating_default", "composed", "addEvent", "defined", "extend", "fireEvent", "isString", "merge", "objectEach", "pick", "pushUnique", "onChartAfterShowResetZoom", "chart", "breadcrumbs", "bbox", "resetZoomButton", "getBBox", "breadcrumbsOptions", "alignBreadcrumbsGroup", "width", "onChartDestroy", "destroy", "onChart<PERSON><PERSON><PERSON><PERSON><PERSON>", "level", "breadcrumbsHeight", "marginBottom", "yOffset", "plotTop", "onChartRedraw", "redraw", "onChartSelection", "event", "resetSelection", "Breadcrumbs", "compose", "ChartClass", "highchartsDefaultOptions", "constructor", "userOptions", "elementList", "isDirty", "list", "chartOptions", "drilldown", "drillUpButton", "defaultOptions", "navigation", "updateProperties", "setList", "setLevel", "length", "getLevel", "getButtonText", "breadcrumb", "textFormat", "defaultText", "drillUpText", "returnText", "formatter", "levelOptions", "render", "group", "renderer", "g", "addClass", "attr", "add", "renderFullPathButtons", "renderSingleButton", "destroySingleButton", "resetElementListState", "updateListElements", "destroyListElements", "posX", "previousBreadcrumb", "renderButton", "updateSingleButton", "xOffset", "positionOptions", "alignTo", "bBox", "additionalSpace", "newPositions", "posY", "button", "e", "callDefaultEvent", "buttonEvents", "events", "click", "newLevel", "styledMode", "renderSeparator", "separatorOptions", "label", "css", "update", "currentBreadcrumb", "force", "element", "updated", "rtlFactor", "updateXPosition", "spacing", "adjustToRTL", "translate", "i", "iEnd", "isLast", "setState", "external_highcharts_src_js_default_Color_namespaceObject", "Color", "external_highcharts_src_js_default_Color_default", "external_highcharts_src_js_default_SeriesRegistry_namespaceObject", "SeriesRegistry", "external_highcharts_src_js_default_SeriesRegistry_default", "external_highcharts_src_js_default_SVGElement_namespaceObject", "SVGElement", "external_highcharts_src_js_default_SVGElement_default", "column", "columnProto", "seriesTypes", "ColorMapComposition_addEvent", "ColorMapComposition_defined", "onPointAfterSetState", "series", "point", "moveToTopOnHover", "graphic", "stateMarkerGraphic", "pointerEvents", "parentGroup", "state", "id", "href", "url", "visibility", "pointMembers", "dataLabelOnNull", "<PERSON><PERSON><PERSON><PERSON>", "value", "Infinity", "isNaN", "seriesMembers", "colorKey", "axisTypes", "parallelArrays", "pointArrayMap", "trackerGroups", "colorAttribs", "ret", "colorProp", "pointAttribs", "SeriesClass", "pointClass", "Series_ColorMapComposition", "external_highcharts_src_js_default_Series_namespaceObject", "Series", "external_highcharts_src_js_default_Series_default", "Treemap_TreemapAlgorithmGroup", "h", "w", "p", "plot", "direction", "startDirection", "total", "nW", "lW", "nH", "lH", "el<PERSON>rr", "lP", "nR", "lR", "aspectRatio", "Math", "max", "addElement", "el", "push", "reset", "Treemap_TreemapNode", "childrenTotal", "visible", "init", "children", "parent", "params", "animatableAttribs", "onComplete", "animation", "hasRendered", "attribs", "getClassName", "shouldDraw", "shapeType", "image", "imageUrl", "shapeArgs", "animate", "isNew", "keys", "pie", "PiePoint", "scatter", "ScatterPoint", "TreemapPoint_extend", "isNumber", "TreemapPoint_pick", "TreemapPoint", "arguments", "groupedPointsAmount", "draw", "Series_DrawPointUtilities", "className", "node", "nodeMap", "rootNode", "isGroup", "<PERSON><PERSON><PERSON><PERSON>", "interactByLeaf", "allowTraversingTree", "Boolean", "apply", "plotY", "setVisible", "TreemapSeriesDefaults_isString", "TreemapSeriesDefaults", "animationLimit", "borderRadius", "showInLegend", "marker", "colorByPoint", "dataLabels", "enabled", "name", "headers", "inside", "textOverflow", "tooltip", "headerFormat", "pointFormat", "clusterFormat", "ignoreHiddenPoint", "layoutAlgorithm", "layoutStartingDirection", "alternateStartingDirection", "levelIsConstant", "traverseUpButton", "borderColor", "borderWidth", "opacity", "hover", "brightness", "heatmap", "halo", "shadow", "legendSymbol", "traverseToLeaf", "cluster", "pixelWidth", "pixelHeight", "reductionFactor", "minimumClusterSize", "distance", "gridSize", "kmeansT<PERSON><PERSON>old", "lineWidth", "radius", "recursive", "item", "func", "context", "next", "Treemap_TreemapUtilities", "TreeUtilities_extend", "isArray", "TreeUtilities_isNumber", "isObject", "TreeUtilities_merge", "TreeUtilities_pick", "<PERSON><PERSON><PERSON><PERSON>", "Series_TreeUtilities", "getColor", "colorIndexByPoint", "colorIndex", "index", "mapOptionsToLevel", "parentColor", "parentColorIndex", "colors", "siblings", "points", "chartOptionsChart", "colorCount", "variateColor", "colorVariation", "parse", "brighten", "to", "getLevelOptions", "defaults", "converted", "from", "levels", "result", "reduce", "getNodeWidth", "columnCount", "nodeDistance", "nodeWidth", "plotSizeX", "test", "fraction", "parseFloat", "nDistance", "Number", "setTreeValues", "tree", "before", "idRoot", "nodeRoot", "mapIdToNode", "optionsPoint", "levelDynamic", "for<PERSON>ach", "child", "newOptions", "val", "updateRootId", "rootId", "TreemapSeries_composed", "noop", "ColumnSeries", "ScatterSeries", "TreemapSeries_getColor", "TreemapSeries_getLevelOptions", "TreemapSeries_updateRootId", "TreemapSeries_addEvent", "arrayMax", "clamp", "correctFloat", "crisp", "TreemapSeries_defined", "error", "TreemapSeries_extend", "TreemapSeries_fireEvent", "TreemapSeries_isArray", "TreemapSeries_isNumber", "TreemapSeries_isObject", "TreemapSeries_isString", "TreemapSeries_merge", "TreemapSeries_pick", "TreemapSeries_pushUnique", "splat", "stableSort", "keepProps", "treemapAxisDefaultValues", "onSeriesAfterBindAxes", "treeAxis", "xAxis", "yAxis", "is", "endOnTick", "gridLineWidth", "min", "minPadding", "maxPadding", "startOnTick", "title", "tickPositions", "setOptions", "TreemapSeries", "simulation", "algorithmCalcPoints", "directionChange", "last", "children<PERSON>rea", "end", "pX", "pY", "pW", "pH", "gW", "gH", "keep", "algorithmFill", "pTot", "algorithmLowAspectRatio", "alignDataLabel", "dataLabel", "labelOptions", "applyTreeGrouping", "parentList", "parentGroups", "checkIfHide", "compareHeight", "thresholdArea", "area", "indexOf", "splice", "groupPoint", "find", "PointClass", "pointIndex", "formatPrefix", "amount", "nodeList", "buildTree", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "algorithm", "alternate", "filter", "ignore", "groupPadding", "children<PERSON><PERSON><PERSON>", "axisWidth", "pointV<PERSON>ues", "axisHeight", "values", "groupPaddingXValues", "len", "groupPaddingYValues", "dlH<PERSON>ght", "map", "dl", "xPad", "yPad", "axisRatio", "axisMax", "getChildrenRecursive", "getLeaves", "nodeSizeBy", "hasOutsideDataLabels", "some", "leaves", "areas", "valueSum", "sum", "expectedAreaPerValue", "areaSum", "minMiss", "max<PERSON><PERSON>", "fit", "areaPerValue", "miss", "simulatedValue", "createList", "currentLevelNumber", "target", "newRootId", "extraNodes", "reverse", "drawDataLabels", "positionsAreSet", "hasDataLabels", "dataLabelWidth", "lineClamp", "floor", "dlOptions", "drawPoints", "withinAnimationLimit", "pointCount", "groupKey", "hasGraphic", "shouldAnimate", "isInside", "r", "selected", "survive", "drillId", "drillToByLeaf", "drillToByGroup", "nodeParent", "drillToNode", "setRootNode", "drillUp", "trigger", "getExtremes", "dataMin", "dataMax", "colorValueData", "valueMin", "valueMax", "getListOfParents", "data", "existingIds", "arr", "ids", "listOfParents", "prev", "curr", "getTree", "allIds", "NodeClass", "parentNode", "hasData", "dataTable", "rowCount", "setOptionsEvent", "allowDrillToNode", "eventsToUnbind", "onClickDrillToNode", "hadOutsideDataLabels", "keepEventsForUpdate", "drillUpsNumber", "stateOptions", "borderDashStyle", "cursor", "setColorRecursive", "colorInfo", "setPointV<PERSON>ues", "getStrokeWidth", "strokeWidth", "xValue", "toPixels", "x2Value", "yValue", "y2Value", "x1", "x2", "y1", "y2", "abs", "plotX", "eventArguments", "previousRootId", "args", "idPreviousRoot", "inactiveOtherPoints", "b", "sortIndex", "sliceAndDice", "squarified", "strip", "stripes", "applyGrouping", "seriesArea", "startsWith", "concat", "colorAxis", "setExtremes", "setScale", "buildKDTree", "directTouch", "getExtremesFromAll", "getSymbol", "optionalAxis", "utils", "registerSeriesType", "deg2rad", "CenteredUtilities_fireEvent", "CenteredUtilities_isNumber", "CenteredUtilities_pick", "CenteredUtilities_relativeLength", "getCenter", "slicingRoom", "slicedOffset", "plot<PERSON>id<PERSON>", "plotHeight", "centerOption", "center", "smallestSize", "thickness", "handleSlicingRoom", "size", "innerSize", "positions", "angular", "getStartAndEndRadians", "start", "startAngle", "endAngle", "Series_CenteredUtilities", "Point", "treemap", "SunburstPoint_TreemapPoint", "SunburstPoint_correctFloat", "SunburstPoint_extend", "pInt", "SunburstPoint", "getDataLabelPath", "shapeExisting", "angle", "upperHalf", "PI", "moreThanHalf", "dataLabelPath", "arc", "open", "longArc", "clockwise", "innerR", "defs", "haloPath", "SunburstUtilities_TreemapSeries", "SunburstUtilities_isNumber", "SunburstUtilities_isObject", "SunburstUtilities_merge", "range", "SunburstUtilities", "calculateLevelSizes", "totalWeight", "diffRadius", "levelsNotIncluded", "remainingSize", "unit", "levelSize", "weight", "getLevelFromAndTo", "TextPath_deg2rad", "TextPath_addEvent", "TextPath_merge", "<PERSON><PERSON><PERSON>", "TextPath_defined", "TextPath_extend", "setTextPath", "path", "textPathOptions", "attributes", "dy", "startOffset", "textAnchor", "textWrapper", "textPath", "undo", "textPathId", "textAttribs", "dx", "transform", "box", "nodes", "slice", "tagName", "added", "textCache", "buildText", "setPolygon", "tp", "querySelector", "polygon", "fontMetrics", "descender", "lineCleanerRegex", "RegExp", "lines", "innerHTML", "replace", "split", "numOfLines", "appendTopAndBottom", "charIndex", "positionOfChar", "rotation", "getRotationOfChar", "cosRot", "cos", "sinRot", "sin", "lineIndex", "lineLen", "line", "lineCharIndex", "srcCharIndex", "lower", "upper", "getStartPositionOfChar", "unshift", "char<PERSON><PERSON>", "getEndPositionOfChar", "drawTextPath", "SunburstSeries_noop", "SunburstSeries_ColumnSeries", "SunburstSeries_TreemapSeries", "SunburstSeries_getColor", "SunburstSeries_getLevelOptions", "SunburstSeries_setTreeValues", "SunburstSeries_updateRootId", "SunburstSeries_defined", "SunburstSeries_error", "SunburstSeries_extend", "SunburstSeries_fireEvent", "SunburstSeries_isNumber", "SunburstSeries_isObject", "SunburstSeries_isString", "SunburstSeries_merge", "SunburstSeries_splat", "Extensions_TextPath", "SVGElementClass", "svgElementProto", "rad2deg", "getEndPoint", "cbSetTreeValuesBefore", "sliced", "SunburstSeries", "placed", "plotLeft", "translateX", "translateY", "scaleX", "scaleY", "shapeRoot", "nodePreviousRoot", "shapePreviousRoot", "radians", "startAndEndRadians", "optionsChart", "isBoolean", "hackDataLabelAnimation", "dataLabelsGroup", "animate<PERSON><PERSON><PERSON>", "animateLabelsCalled", "addedHack", "s", "animationInfo", "shape", "getAnimation", "tooltipPos", "getDrillId", "isNull", "getDlOptions", "innerArcLength", "outerArcLength", "rotationRad", "rotationMode", "parseInt", "whiteSpace", "defer", "innerRadius", "outerRadius", "percentage", "offsetPosition", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parentValues", "isCircle", "nodeIds", "generatePoints", "idTop", "nodeTop", "Sunburst_SunburstUtilities", "clip", "allowOverlap", "isCartesian", "onPointSupported", "G", "Chart", "sunburst_src", "default"], "mappings": "AAUA,UAAYA,MAA6D,sBAAuB,CAEvF,IA4lHEC,EA1mFPC,EAypCAC,EAg9CAF,EA3lHSG,EAAsB,CAAC,CAM1BA,CAAAA,EAAoBC,CAAC,CAAG,AAACC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,UAAU,CACvC,IAAOF,EAAO,OAAU,CACxB,IAAOA,EAER,OADAF,EAAoBK,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAH,EAAoBK,CAAC,CAAG,CAACE,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXR,EAAoBU,CAAC,CAACF,EAAYC,IAAQ,CAACT,EAAoBU,CAAC,CAACH,EAASE,IAC5EE,OAAOC,cAAc,CAACL,EAASE,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAT,EAAoBU,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAM5F,IAAMI,EAAqDxB,EAAwD,OAAU,CAC7H,IAAIyB,EAA0DrB,EAAoBC,CAAC,CAACmB,GA4QvD,IAAME,EAJP,CACxBC,KAnPS,CAOTC,eAAgB,MACpB,EA4OIC,QAjOY,CAiBZC,YAAa,CAETC,KAAM,OAENC,OAAQ,GAERC,QAAS,EAET,eAAgB,EAEhBC,OAAQ,EAERC,OAAQ,CACJC,OAAQ,CACJL,KAAM,MACV,CACJ,EACAM,MAAO,CACHC,MAAO,SACX,CACJ,EAOAC,cAAe,EA8BfC,SAAU,CAAA,EAYVC,OAAQ,KAAK,EAkBbC,WAAY,UAWZC,IAAK,CAAA,EAcLC,SAAU,CAMNC,MAAO,OAMPC,cAAe,MAMfC,EAAG,EAQHC,EAAG,KAAK,CACZ,EAMAC,UAAW,CAMPC,KAAM,IASNb,MAAO,CACHC,MAAO,UACPa,SAAU,OACd,CACJ,EAUAC,aAAc,CAAA,EAWdf,MAAO,CAAC,EAORgB,QAAS,CAAA,EAOTnB,OAAQ,CACZ,CASA,EAIMoB,EAAgEtD,EAAwD,OAAU,CAACuD,UAAU,CAiB7I,CAAEd,OAAAA,CAAM,CAAE,CAAIe,AAhBqDpD,EAAoBC,CAAC,CAACiD,KAkBzF,CAAEG,SAAAA,CAAQ,CAAE,CAAIhC,IAEhB,CAAEiC,SAAAA,CAAQ,CAAEC,QAAAA,CAAO,CAAEC,OAAAA,CAAM,CAAEC,UAAAA,CAAS,CAAEC,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,WAAAA,CAAU,CAAEC,KAAAA,CAAI,CAAEC,WAAAA,CAAU,CAAE,CAAIzC,IAUjG,SAAS0C,IAEL,GAAIC,AADU,IAAI,CACRC,WAAW,CAAE,CACnB,IAAMC,EAAOF,AAFH,IAAI,CAEKG,eAAe,EAC9BH,AAHM,IAAI,CAGJG,eAAe,CAACC,OAAO,GAAIC,EAAqBL,AAHhD,IAAI,CAGkDC,WAAW,CAACxC,OAAO,CAC/EyC,GACAG,AAAsC,UAAtCA,EAAmB7B,QAAQ,CAACC,KAAK,EACjC4B,AAAkC,YAAlCA,EAAmB/B,UAAU,EAC7B0B,AAPM,IAAI,CAOJC,WAAW,CAACK,qBAAqB,CAAC,CAACJ,EAAKK,KAAK,CAAGF,EAAmBlC,aAAa,CAE9F,CACJ,CAKA,SAASqC,IACD,IAAI,CAACP,WAAW,GAChB,IAAI,CAACA,WAAW,CAACQ,OAAO,GACxB,IAAI,CAACR,WAAW,CAAG,KAAK,EAEhC,CAKA,SAASS,IACL,IAAMT,EAAc,IAAI,CAACA,WAAW,CACpC,GAAIA,GACA,CAACA,EAAYxC,OAAO,CAACW,QAAQ,EAC7B6B,EAAYU,KAAK,CAAE,CACnB,IAAMN,EAAqBJ,EAAYxC,OAAO,CAAEC,EAAc2C,EAAmB3C,WAAW,CAAEkD,EAAqB,AAAClD,CAAAA,EAAYE,MAAM,EAAI,CAAA,EACtI,EAAKF,CAAAA,EAAYG,OAAO,EAAI,CAAA,EAC5BwC,EAAmBlC,aAAa,CAAGO,EAAgB2B,EAAmB7B,QAAQ,CAACE,aAAa,AAC5FA,AAAkB,CAAA,WAAlBA,GACA,IAAI,CAACmC,YAAY,CAAG,AAAC,CAAA,IAAI,CAACA,YAAY,EAAI,CAAA,EAAKD,EAC/CX,EAAYa,OAAO,CAAGF,GAEjBlC,AAAkB,WAAlBA,GACL,IAAI,CAACqC,OAAO,EAAIH,EAChBX,EAAYa,OAAO,CAAG,CAACF,GAGvBX,EAAYa,OAAO,CAAG,KAAK,CAEnC,CACJ,CAIA,SAASE,IACL,IAAI,CAACf,WAAW,EAAI,IAAI,CAACA,WAAW,CAACgB,MAAM,EAC/C,CAKA,SAASC,EAAiBC,CAAK,EACE,CAAA,IAAzBA,EAAMC,cAAc,EACpB,IAAI,CAACnB,WAAW,EAChB,IAAI,CAACA,WAAW,CAACK,qBAAqB,EAE9C,CAkBA,MAAMe,EAMF,OAAOC,QAAQC,CAAU,CAAEC,CAAwB,CAAE,CAC7C1B,EAAWT,EAAU,iBACrBC,EAASiC,EAAY,UAAWf,GAChClB,EAASiC,EAAY,qBAAsBxB,GAC3CT,EAASiC,EAAY,aAAcb,GACnCpB,EAASiC,EAAY,SAAUP,GAC/B1B,EAASiC,EAAY,YAAaL,GAElC1B,EAAOgC,EAAyBjE,IAAI,CAAED,EAAgCC,IAAI,EAElF,CAMAkE,YAAYzB,CAAK,CAAE0B,CAAW,CAAE,CAC5B,IAAI,CAACC,WAAW,CAAG,CAAC,EACpB,IAAI,CAACC,OAAO,CAAG,CAAA,EACf,IAAI,CAACjB,KAAK,CAAG,EACb,IAAI,CAACkB,IAAI,CAAG,EAAE,CACd,IAAMC,EAAenC,EAAMK,EAAMvC,OAAO,CAACsE,SAAS,EAC9C/B,EAAMvC,OAAO,CAACsE,SAAS,CAACC,aAAa,CAAEX,EAAYY,cAAc,CAAEjC,EAAMvC,OAAO,CAACyE,UAAU,EAAIlC,EAAMvC,OAAO,CAACyE,UAAU,CAACjC,WAAW,CAAEyB,EACzI,CAAA,IAAI,CAAC1B,KAAK,CAAGA,EACb,IAAI,CAACvC,OAAO,CAAGqE,GAAgB,CAAC,CACpC,CAaAK,iBAAiBN,CAAI,CAAE,CACnB,IAAI,CAACO,OAAO,CAACP,GACb,IAAI,CAACQ,QAAQ,GACb,IAAI,CAACT,OAAO,CAAG,CAAA,CACnB,CAUAQ,QAAQP,CAAI,CAAE,CACV,IAAI,CAACA,IAAI,CAAGA,CAChB,CAQAQ,UAAW,CACP,IAAI,CAAC1B,KAAK,CAAG,IAAI,CAACkB,IAAI,CAACS,MAAM,EAAI,IAAI,CAACT,IAAI,CAACS,MAAM,CAAG,CACxD,CAQAC,UAAW,CACP,OAAO,IAAI,CAAC5B,KAAK,AACrB,CAYA6B,cAAcC,CAAU,CAAE,CACtB,IAA0BzC,EAAQC,AAAd,IAAI,CAAsBD,KAAK,CAAEK,EAAqBJ,AAAtD,IAAI,CAA8DxC,OAAO,CAAEF,EAAOyC,EAAMvC,OAAO,CAACF,IAAI,CAAEmF,EAAa7C,EAAKQ,EAAmBhC,MAAM,CAAEgC,EAAmBrB,YAAY,CAClM,eAAiB,kBAAmB2D,EAAcpF,GAAQsC,EAAKtC,EAAKqF,WAAW,CAAErF,EAAKC,cAAc,EACpGqF,EAAaxC,EAAmByC,SAAS,EACzCzC,EAAmByC,SAAS,CAACL,IAC7BpE,EAAOqE,EAAY,CAAE/B,MAAO8B,EAAWM,YAAY,AAAC,EAAG/C,IAAU,GASrE,MARK,CAAA,AAACN,EAASmD,IACX,CAACA,EAAWP,MAAM,EAClBO,AAAe,OAAfA,CAAkB,GAClBtD,EAAQoD,IACRE,CAAAA,EAAa,AAACxC,EAAmBrB,YAAY,CAEzC2D,EADA,KAAOA,CACG,EAEXE,CACX,CAQA5B,QAAS,CACD,IAAI,CAACW,OAAO,EACZ,IAAI,CAACoB,MAAM,GAEX,IAAI,CAACC,KAAK,EACV,IAAI,CAACA,KAAK,CAACxE,KAAK,GAEpB,IAAI,CAACmD,OAAO,CAAG,CAAA,CACnB,CAQAoB,QAAS,CACL,IAA0BhD,EAAQC,AAAd,IAAI,CAAsBD,KAAK,CAAEK,EAAqBJ,AAAtD,IAAI,CAA8DxC,OAAO,AAEzF,EAACwC,AAFe,IAAI,CAEPgD,KAAK,EAAI5C,GACtBJ,CAAAA,AAHgB,IAAI,CAGRgD,KAAK,CAAGjD,EAAMkD,QAAQ,CAC7BC,CAAC,CAAC,qBACFC,QAAQ,CAAC,gDACTC,IAAI,CAAC,CACNvF,OAAQuC,EAAmBvC,MAAM,AACrC,GACKwF,GAAG,EAAC,EAGTjD,EAAmBrB,YAAY,CAC/B,IAAI,CAACuE,qBAAqB,GAG1B,IAAI,CAACC,kBAAkB,GAE3B,IAAI,CAAClD,qBAAqB,EAC9B,CAQAiD,uBAAwB,CAEpB,IAAI,CAACE,mBAAmB,GACxB,IAAI,CAACC,qBAAqB,GAC1B,IAAI,CAACC,kBAAkB,GACvB,IAAI,CAACC,mBAAmB,EAC5B,CAQAJ,oBAAqB,CACjB,IAA0BxD,EAAQC,AAAd,IAAI,CAAsBD,KAAK,CAAE6B,EAAO5B,AAAxC,IAAI,CAAgD4B,IAAI,CAA4C1D,EAAgBkC,AAArCJ,AAA/E,IAAI,CAAuFxC,OAAO,CAAqCU,aAAa,CAExK,IAAI,CAACyF,mBAAmB,GAGxB,IAAMC,EAAO5D,AALO,IAAI,CAKCgD,KAAK,CAC1BhD,AANgB,IAAI,CAMRgD,KAAK,CAAC7C,OAAO,GAAGG,KAAK,CACjCpC,EACE2F,EAAqBjC,CAAI,CAACA,EAAKS,MAAM,CAAG,EAAE,AAC5C,EAACtC,EAAMgC,aAAa,EAAK,IAAI,CAACrB,KAAK,CAAG,EACtCX,EAAMgC,aAAa,CAAG/B,AAVN,IAAI,CAUc8D,YAAY,CAACD,EAAoBD,EAH7C1F,GAKjB6B,EAAMgC,aAAa,GACpB,IAAI,CAACrB,KAAK,CAAG,EAEb,IAAI,CAACqD,kBAAkB,GAGvB,IAAI,CAACP,mBAAmB,GAGpC,CAQAnD,sBAAsB2D,CAAO,CAAE,CAE3B,GAAIhE,AADgB,IAAI,CACRgD,KAAK,CAAE,CACnB,IAAM5C,EAAqBJ,AAFX,IAAI,CAEmBxC,OAAO,CAAEC,EAAc2C,EAAmB3C,WAAW,CAAEwG,EAAkB7D,EAAmB7B,QAAQ,CAAE2F,EAAW9D,AAAkC,UAAlCA,EAAmB/B,UAAU,EACjL+B,AAAkC,eAAlCA,EAAmB/B,UAAU,CAC7B,KAAK,EACL,UAAY8F,EAAOnE,AALP,IAAI,CAKegD,KAAK,CAAC7C,OAAO,GAAIiE,EAAkB,EAAK3G,CAAAA,EAAYG,OAAO,EAAI,CAAA,EAC9FwC,EAAmBlC,aAAa,AAEpC+F,CAAAA,EAAgB3D,KAAK,CAAG6D,EAAK7D,KAAK,CAAG8D,EACrCH,EAAgBtG,MAAM,CAAGwG,EAAKxG,MAAM,CAAGyG,EACvC,IAAMC,EAAe3E,EAAMuE,GAEvBD,GACAK,CAAAA,EAAa3F,CAAC,EAAIsF,CAAM,EAExBhE,AAfY,IAAI,CAeJxC,OAAO,CAACc,GAAG,EACvB+F,CAAAA,EAAa3F,CAAC,EAAIuF,EAAgB3D,KAAK,AAAD,EAE1C+D,EAAa1F,CAAC,CAAGiB,EAAKyE,EAAa1F,CAAC,CAAE,IAAI,CAACkC,OAAO,CAAE,GACpDb,AAnBgB,IAAI,CAmBRgD,KAAK,CAACxE,KAAK,CAAC6F,EAAc,CAAA,EAAMH,EAChD,CACJ,CAgBAJ,aAAatB,CAAU,CAAEoB,CAAI,CAAEU,CAAI,CAAE,CACjC,IAAMtE,EAAc,IAAI,CAAED,EAAQ,IAAI,CAACA,KAAK,CAAEK,EAAqBJ,EAAYxC,OAAO,CAAEC,EAAciC,EAAMU,EAAmB3C,WAAW,EACpI8G,EAASxE,EAAMkD,QAAQ,CACxBsB,MAAM,CAACvE,EAAYuC,aAAa,CAACC,GAAaoB,EAAMU,EAAM,SAAUE,CAAC,EAEtE,IAEIC,EAFEC,EAAetE,EAAmBuE,MAAM,EAC1CvE,EAAmBuE,MAAM,CAACC,KAAK,CAE/BF,GACAD,CAAAA,EAAmBC,EAAaxH,IAAI,CAAC8C,EAAawE,EAAGhC,EAAU,EAG1C,CAAA,IAArBiC,IAGKrE,EAAmBrB,YAAY,CAIhCyF,EAAEK,QAAQ,CAAGrC,EAAW9B,KAAK,CAH7B8D,EAAEK,QAAQ,CAAG7E,EAAYU,KAAK,CAAG,EAKrClB,EAAUQ,EAAa,KAAMwE,GAErC,EAAG/G,GACE0F,QAAQ,CAAC,iCACTE,GAAG,CAACrD,EAAYgD,KAAK,EAI1B,OAHKjD,EAAM+E,UAAU,EACjBP,EAAOnB,IAAI,CAAChD,EAAmBpC,KAAK,EAEjCuG,CACX,CAcAQ,gBAAgBnB,CAAI,CAAEU,CAAI,CAAE,CACxB,IAA0BvE,EAAQ,IAAI,CAACA,KAAK,CAA4CiF,EAAmB5E,AAAxCJ,AAA/C,IAAI,CAAuDxC,OAAO,CAAwCoB,SAAS,CACjIA,EAAYmB,EAAMkD,QAAQ,CAC3BgC,KAAK,CAACD,EAAiBnG,IAAI,CAAE+E,EAAMU,EAAM,KAAK,EAAG,KAAK,EAAG,KAAK,EAAG,CAAA,GACjEnB,QAAQ,CAAC,oCACTE,GAAG,CAACrD,AAJW,IAAI,CAIHgD,KAAK,EAI1B,OAHKjD,EAAM+E,UAAU,EACjBlG,EAAUsG,GAAG,CAACF,EAAiBhH,KAAK,EAEjCY,CACX,CAYAuG,OAAO3H,CAAO,CAAE,CACZkC,EAAM,CAAA,EAAM,IAAI,CAAClC,OAAO,CAAEA,GAC1B,IAAI,CAACgD,OAAO,GACZ,IAAI,CAACmB,OAAO,CAAG,CAAA,CACnB,CAQAoC,oBAAqB,CACjB,IAAMhE,EAAQ,IAAI,CAACA,KAAK,CAAEqF,EAAoB,IAAI,CAACxD,IAAI,CAAC,IAAI,CAAClB,KAAK,CAAG,EAAE,AACnEX,CAAAA,EAAMgC,aAAa,EACnBhC,EAAMgC,aAAa,CAACqB,IAAI,CAAC,CACrBvE,KAAM,IAAI,CAAC0D,aAAa,CAAC6C,EAC7B,EAER,CAQA5E,SAAU,CACN,IAAI,CAACgD,mBAAmB,GAGxB,IAAI,CAACG,mBAAmB,CAAC,CAAA,GAErB,IAAI,CAACX,KAAK,EACV,IAAI,CAACA,KAAK,CAACxC,OAAO,GAEtB,IAAI,CAACwC,KAAK,CAAG,KAAK,CACtB,CAQAW,oBAAoB0B,CAAK,CAAE,CACvB,IAAM3D,EAAc,IAAI,CAACA,WAAW,CACpC/B,EAAW+B,EAAa,CAAC4D,EAAS5E,KAC1B2E,CAAAA,GACA,CAAC3D,CAAW,CAAChB,EAAM,CAAC6E,OAAO,AAAD,IAE1BD,AADAA,CAAAA,EAAU5D,CAAW,CAAChB,EAAM,AAAD,EACnB6D,MAAM,EAAIe,EAAQf,MAAM,CAAC/D,OAAO,GACxC8E,EAAQ1G,SAAS,EAAI0G,EAAQ1G,SAAS,CAAC4B,OAAO,GAC9C,OAAO8E,EAAQf,MAAM,CACrB,OAAOe,EAAQ1G,SAAS,CACxB,OAAO8C,CAAW,CAAChB,EAAM,CAEjC,GACI2E,GACA,CAAA,IAAI,CAAC3D,WAAW,CAAG,CAAC,CAAA,CAE5B,CAQA8B,qBAAsB,CACd,IAAI,CAACzD,KAAK,CAACgC,aAAa,GACxB,IAAI,CAAChC,KAAK,CAACgC,aAAa,CAACvB,OAAO,GAChC,IAAI,CAACT,KAAK,CAACgC,aAAa,CAAG,KAAK,EAExC,CAQA0B,uBAAwB,CACpB9D,EAAW,IAAI,CAAC+B,WAAW,CAAE,AAAC4D,IAC1BA,EAAQC,OAAO,CAAG,CAAA,CACtB,EACJ,CASA7B,oBAAqB,CACjB,IAA0BhC,EAAc1B,AAApB,IAAI,CAA4B0B,WAAW,CAAExD,EAAgB8B,AAA7D,IAAI,CAAqExC,OAAO,CAACU,aAAa,CAAwB0D,EAAO5B,AAA7H,IAAI,CAAqI4B,IAAI,CAAEtD,EAAM0B,AAArJ,IAAI,CAA6JxC,OAAO,CAACc,GAAG,CAAEkH,EAAYlH,EAAM,GAAK,EAAGmH,EAAkB,SAAUH,CAAO,CAAEI,CAAO,EACpQ,OAAOF,EAAYF,EAAQnF,OAAO,GAAGG,KAAK,CACtCkF,EAAYE,CACpB,EAAGC,EAAc,SAAUL,CAAO,CAAE1B,CAAI,CAAEU,CAAI,EAC1CgB,EAAQM,SAAS,CAAChC,EAAO0B,EAAQnF,OAAO,GAAGG,KAAK,CAAEgE,EACtD,EAEIV,EAAO5D,AAPS,IAAI,CAODgD,KAAK,CACxByC,EAAgBzF,AARA,IAAI,CAQQgD,KAAK,CAAE9E,GACnCA,EAAekH,EAAmB5C,EACtC,IAAK,IAAIqD,EAAI,EAAGC,EAAOlE,EAAKS,MAAM,CAAEwD,EAAIC,EAAM,EAAED,EAAG,CAC/C,IACItB,EAAQ3F,EADNmH,EAASF,IAAMC,EAAO,CAGxBpE,CAAAA,CAAW,CAACc,AADhBA,CAAAA,EAAaZ,CAAI,CAACiE,EAAE,AAAD,EACQnF,KAAK,CAAC,EAE7B6D,EAASa,AADTA,CAAAA,EAAoB1D,CAAW,CAACc,EAAW9B,KAAK,CAAC,AAAD,EACrB6D,MAAM,CAE7B,AAACa,EAAkBxG,SAAS,EAC3BmH,EAUIX,EAAkBxG,SAAS,EAChCmH,IACAX,EAAkBxG,SAAS,CAAC4B,OAAO,GACnC,OAAO4E,EAAkBxG,SAAS,GAXlCgF,GAAQ4B,EAAYtH,EACpBkH,EAAkBxG,SAAS,CACvBoB,AAvBI,IAAI,CAuBI+E,eAAe,CAACnB,EAvB+E1F,GAwB3GI,GACAqH,EAAYP,EAAkBxG,SAAS,CAAEgF,EAzBkE1F,GA2B/G0F,GAAQ6B,EAAgBL,EAAkBxG,SAAS,CAAEV,IAOzDwD,CAAW,CAACc,EAAW9B,KAAK,CAAC,CAAC6E,OAAO,CAAG,CAAA,IAIxChB,EAASvE,AAtCG,IAAI,CAsCK8D,YAAY,CAACtB,EAAYoB,EAtCqE1F,GAuC/GI,GACAqH,EAAYpB,EAAQX,EAxC2F1F,GA0CnH0F,GAAQ6B,EAAgBlB,EAAQrG,GAE3B6H,IACDnH,EAAYoB,AA7CJ,IAAI,CA6CY+E,eAAe,CAACnB,EA7CuE1F,GA8C3GI,GACAqH,EAAY/G,EAAWgF,EA/CoF1F,GAiD/G0F,GAAQ6B,EAAgB7G,EAAWV,IAEvCwD,CAAW,CAACc,EAAW9B,KAAK,CAAC,CAAG,CAC5B6D,OAAAA,EACA3F,UAAAA,EACA2G,QAAS,CAAA,CACb,GAEAhB,GACAA,EAAOyB,QAAQ,CAACD,AAAS,IAATA,EAExB,CACJ,CACJ,CAMA3E,EAAYY,cAAc,CAAG3E,EAAgCG,OAAO,CA4FpE,IAAMyI,EAA2DtK,EAAwD,OAAU,CAACuK,KAAK,CACzI,IAAIC,EAAgEpK,EAAoBC,CAAC,CAACiK,GAE1F,IAAMG,EAAoEzK,EAAwD,OAAU,CAAC0K,cAAc,CAC3J,IAAIC,EAAyEvK,EAAoBC,CAAC,CAACoK,GAEnG,IAAMG,EAAgE5K,EAAwD,OAAU,CAAC6K,UAAU,CACnJ,IAAIC,EAAqE1K,EAAoBC,CAAC,CAACuK,GAa/F,GAAM,CAAEG,OAAQ,CAAE1J,UAAW2J,CAAW,CAAE,CAAE,CAAG,AAACL,IAA6DM,WAAW,CAGlH,CAAEvH,SAAUwH,CAA4B,CAAEvH,QAASwH,CAA2B,CAAE,CAAI1J,KAO1F,AAAC,SAAUvB,CAAmB,EAsC1B,SAASkL,EAAqBvC,CAAC,EAC3B,IAAoBwC,EAASC,AAAf,IAAI,CAAiBD,MAAM,CAAE/D,EAAW+D,EAAOjH,KAAK,CAACkD,QAAQ,AACvEgE,CADU,IAAI,CACRC,gBAAgB,EAAID,AADhB,IAAI,CACkBE,OAAO,GAClCH,EAAOI,kBAAkB,EAI1BJ,CAAAA,EAAOI,kBAAkB,CAAG,GAAKX,CAAAA,GAAsD,EAAGxD,EAAU,OAC/FiC,GAAG,CAAC,CACLmC,cAAe,MACnB,GACKhE,GAAG,CAAC4D,AAVH,IAAI,CAUKE,OAAO,CAACG,WAAW,CAAA,EAElC9C,GAAG+C,QAAU,SAGbN,AAfM,IAAI,CAeJE,OAAO,CAAC/D,IAAI,CAAC,CACfoE,GAAI,IAAI,CAACA,EAAE,AACf,GACAR,EAAOI,kBAAkB,CAAChE,IAAI,CAAC,CAC3BqE,KAAM,CAAC,EAAExE,EAASyE,GAAG,CAAC,CAAC,EAAE,IAAI,CAACF,EAAE,CAAC,CAAC,CAClCG,WAAY,SAChB,IAGAX,EAAOI,kBAAkB,CAAChE,IAAI,CAAC,CAC3BqE,KAAM,EACV,GAGZ,CA9DA5L,EAAoB+L,YAAY,CAAG,CAC/BC,gBAAiB,CAAA,EACjBX,iBAAkB,CAAA,EAClBY,QAiEJ,WACI,OAAQ,AAAe,OAAf,IAAI,CAACC,KAAK,EACd,IAAI,CAACA,KAAK,GAAKC,KACf,IAAI,CAACD,KAAK,GAAK,CAACC,KAEf,CAAA,AAAe,KAAK,IAApB,IAAI,CAACD,KAAK,EAAe,CAACE,MAAM,IAAI,CAACF,KAAK,CAAA,CACnD,CAtEA,EACAlM,EAAoBqM,aAAa,CAAG,CAChCC,SAAU,QACVC,UAAW,CAAC,QAAS,QAAS,YAAY,CAC1CC,eAAgB,CAAC,IAAK,IAAK,QAAQ,CACnCC,cAAe,CAAC,QAAQ,CACxBC,cAAe,CAAC,QAAS,cAAe,kBAAkB,CAC1DC,aAwEJ,SAA4BvB,CAAK,EAC7B,IAAMwB,EAAM,CAAC,EAMb,OALI3B,EAA4BG,EAAMhJ,KAAK,GACtC,CAAA,CAACgJ,EAAMM,KAAK,EAAIN,AAAgB,WAAhBA,EAAMM,KAAK,AAAY,GAExCkB,CAAAA,CAAG,CAAC,IAAI,CAACC,SAAS,EAAI,OAAO,CAAGzB,EAAMhJ,KAAK,AAAD,EAEvCwK,CACX,EA/EIE,aAAchC,EAAYgC,YAAY,AAC1C,EAcA9M,EAAoBwF,OAAO,CAL3B,SAAiBuH,CAAW,EAGxB,OADA/B,EADmB+B,EAAY5L,SAAS,CAAC6L,UAAU,CACV,gBAAiB9B,GACnD6B,CACX,CAkEJ,EAAG/M,GAAwBA,CAAAA,EAAsB,CAAC,CAAA,GAMrB,IAAMiN,EAA8BjN,EAG3DkN,EAA4DpN,EAAwD,OAAU,CAACqN,MAAM,CAC3I,IAAIC,EAAiElN,EAAoBC,CAAC,CAAC+M,GA4F9D,IAAMG,EAzEnC,MAMI1H,YAAY2H,CAAC,CAAEC,CAAC,CAAEhN,CAAC,CAAEiN,CAAC,CAAE,CACpB,IAAI,CAAC1L,MAAM,CAAGwL,EACd,IAAI,CAAC7I,KAAK,CAAG8I,EACb,IAAI,CAACE,IAAI,CAAGD,EACZ,IAAI,CAACE,SAAS,CAAGnN,EACjB,IAAI,CAACoN,cAAc,CAAGpN,EACtB,IAAI,CAACqN,KAAK,CAAG,EACb,IAAI,CAACC,EAAE,CAAG,EACV,IAAI,CAACC,EAAE,CAAG,EACV,IAAI,CAACC,EAAE,CAAG,EACV,IAAI,CAACC,EAAE,CAAG,EACV,IAAI,CAACC,KAAK,CAAG,EAAE,CACf,IAAI,CAACC,EAAE,CAAG,CACNN,MAAO,EACPI,GAAI,EACJD,GAAI,EACJD,GAAI,EACJD,GAAI,EACJM,GAAI,EACJC,GAAI,EACJC,YAAa,SAAUd,CAAC,CAAED,CAAC,EACvB,OAAOgB,KAAKC,GAAG,CAAEhB,EAAID,EAAKA,EAAIC,EAClC,CACJ,CACJ,CAMAiB,WAAWC,CAAE,CAAE,CACX,IAAI,CAACP,EAAE,CAACN,KAAK,CAAG,IAAI,CAACK,KAAK,CAAC,IAAI,CAACA,KAAK,CAACzH,MAAM,CAAG,EAAE,CACjD,IAAI,CAACoH,KAAK,CAAG,IAAI,CAACA,KAAK,CAAGa,EACtB,AAAmB,IAAnB,IAAI,CAACf,SAAS,EAEd,IAAI,CAACI,EAAE,CAAG,IAAI,CAACD,EAAE,CACjB,IAAI,CAACK,EAAE,CAACF,EAAE,CAAG,IAAI,CAACE,EAAE,CAACN,KAAK,CAAG,IAAI,CAACE,EAAE,CACpC,IAAI,CAACI,EAAE,CAACE,EAAE,CAAG,IAAI,CAACF,EAAE,CAACG,WAAW,CAAC,IAAI,CAACP,EAAE,CAAE,IAAI,CAACI,EAAE,CAACF,EAAE,EAEpD,IAAI,CAACH,EAAE,CAAG,IAAI,CAACD,KAAK,CAAG,IAAI,CAAC9L,MAAM,CAClC,IAAI,CAACoM,EAAE,CAACH,EAAE,CAAG,IAAI,CAACG,EAAE,CAACN,KAAK,CAAG,IAAI,CAACC,EAAE,CACpC,IAAI,CAACK,EAAE,CAACC,EAAE,CAAG,IAAI,CAACD,EAAE,CAACG,WAAW,CAAC,IAAI,CAACR,EAAE,CAAE,IAAI,CAACK,EAAE,CAACH,EAAE,IAIpD,IAAI,CAACC,EAAE,CAAG,IAAI,CAACD,EAAE,CACjB,IAAI,CAACG,EAAE,CAACJ,EAAE,CAAG,IAAI,CAACI,EAAE,CAACN,KAAK,CAAG,IAAI,CAACI,EAAE,CACpC,IAAI,CAACE,EAAE,CAACE,EAAE,CAAG,IAAI,CAACF,EAAE,CAACG,WAAW,CAAC,IAAI,CAACH,EAAE,CAACJ,EAAE,CAAE,IAAI,CAACE,EAAE,EAEpD,IAAI,CAACD,EAAE,CAAG,IAAI,CAACH,KAAK,CAAG,IAAI,CAACnJ,KAAK,CACjC,IAAI,CAACyJ,EAAE,CAACL,EAAE,CAAG,IAAI,CAACK,EAAE,CAACN,KAAK,CAAG,IAAI,CAACG,EAAE,CACpC,IAAI,CAACG,EAAE,CAACC,EAAE,CAAG,IAAI,CAACD,EAAE,CAACG,WAAW,CAAC,IAAI,CAACH,EAAE,CAACL,EAAE,CAAE,IAAI,CAACE,EAAE,GAExD,IAAI,CAACE,KAAK,CAACS,IAAI,CAACD,EACpB,CACAE,OAAQ,CACJ,IAAI,CAACd,EAAE,CAAG,EACV,IAAI,CAACC,EAAE,CAAG,EACV,IAAI,CAACG,KAAK,CAAG,EAAE,CACf,IAAI,CAACL,KAAK,CAAG,CACjB,CACJ,EAuDmCgB,EA/BnC,MACIjJ,aAAc,CAMV,IAAI,CAACkJ,aAAa,CAAG,EACrB,IAAI,CAACC,OAAO,CAAG,CAAA,CACnB,CAMAC,KAAKpD,CAAE,CAAE3B,CAAC,CAAEgF,CAAQ,CAAElN,CAAM,CAAE+C,CAAK,CAAEsG,CAAM,CAAE8D,CAAM,CAAE,CAQjD,OAPA,IAAI,CAACtD,EAAE,CAAGA,EACV,IAAI,CAAC3B,CAAC,CAAGA,EACT,IAAI,CAACgF,QAAQ,CAAGA,EAChB,IAAI,CAAClN,MAAM,CAAGA,EACd,IAAI,CAAC+C,KAAK,CAAGA,EACb,IAAI,CAACsG,MAAM,CAAGA,EACd,IAAI,CAAC8D,MAAM,CAAGA,EACP,IAAI,AACf,CACJ,IA8BA,SAAc7D,CAAK,CAAE8D,CAAM,EACvB,GAAM,CAAEC,kBAAAA,CAAiB,CAAEC,WAAAA,CAAU,CAAE/F,IAAAA,CAAG,CAAEjC,SAAAA,CAAQ,CAAE,CAAG8H,EACnDG,EAAY,AAACjE,EAAMD,MAAM,EAAIC,EAAMD,MAAM,CAACjH,KAAK,CAACoL,WAAW,CAE7D,KAAK,EAEJlE,EAAMD,MAAM,EACTC,EAAMD,MAAM,CAACxJ,OAAO,CAAC0N,SAAS,CAClC/D,EAAUF,EAAME,OAAO,CAK3B,GAJA4D,EAAOK,OAAO,CAAG,CACb,GAAGL,EAAOK,OAAO,CACjB,MAASnE,EAAMoE,YAAY,EAC/B,EACKpE,EAAMqE,UAAU,GACZnE,IAWDF,EAAME,OAAO,CATTA,EADA4D,AAAqB,SAArBA,EAAOQ,SAAS,CACNtI,EAASpE,IAAI,GAElBkM,AAAqB,UAArBA,EAAOQ,SAAS,CACXtI,EAASuI,KAAK,CAACT,EAAOU,QAAQ,EAAI,IACvCrI,IAAI,CAAC2H,EAAOW,SAAS,EAAI,CAAC,GAGrBzI,CAAQ,CAAC8H,EAAOQ,SAAS,CAAC,CAACR,EAAOW,SAAS,EAAI,CAAC,GAG9DvE,EAAQ9D,GAAG,CAAC0H,EAAO/H,KAAK,GAExBkC,GACAiC,EAAQjC,GAAG,CAACA,GAEhBiC,EACK/D,IAAI,CAAC2H,EAAOK,OAAO,EACnBO,OAAO,CAACX,EAAmBD,CAAAA,EAAOa,KAAK,EAAWV,EAAWD,QAEjE,GAAI9D,EAAS,CACd,IAAM3G,EAAU,KACZyG,EAAME,OAAO,CAAGA,EAAWA,GAAWA,EAAQ3G,OAAO,GAC3B,YAAtB,OAAOyK,GACPA,GAER,CAEIvO,CAAAA,OAAOmP,IAAI,CAACb,GAAmB3I,MAAM,CACrC8E,EAAQwE,OAAO,CAACX,EAAmB,KAAK,EAAG,IAAMxK,KAGjDA,GAER,CACJ,EA0BM,CAAEsL,IAAK,CAAE9O,UAAW,CAAE6L,WAAYkD,CAAQ,CAAE,CAAE,CAAEC,QAAS,CAAEhP,UAAW,CAAE6L,WAAYoD,CAAY,CAAE,CAAE,CAAE,CAAG,AAAC3F,IAA6DM,WAAW,CAElL,CAAErH,OAAQ2M,CAAmB,CAAEC,SAAAA,CAAQ,CAAEvM,KAAMwM,CAAiB,CAAE,CAAIhP,GAM5E,OAAMiP,UAAqBJ,EACvBzK,aAAc,CAMV,KAAK,IAAI8K,WACT,IAAI,CAACC,mBAAmB,CAAG,EAC3B,IAAI,CAAChB,SAAS,CAAG,MACrB,CAMAiB,KAAKzB,CAAM,CAAE,CACT0B,EAA+B,IAAI,CAAE1B,EACzC,CACAM,cAAe,CACX,IAAMrE,EAAS,IAAI,CAACA,MAAM,CAAExJ,EAAUwJ,EAAOxJ,OAAO,CAChDkP,EAAY,KAAK,CAACrB,eAiBtB,OAfI,IAAI,CAACsB,IAAI,CAACjM,KAAK,EAAIsG,EAAO4F,OAAO,CAAC5F,EAAO6F,QAAQ,CAAC,CAACnM,KAAK,EACxD,IAAI,CAACiM,IAAI,CAAC9B,QAAQ,CAACxI,MAAM,CACzBqK,GAAa,0BAER,AAAC,IAAI,CAACC,IAAI,CAACG,OAAO,EACtB,IAAI,CAACH,IAAI,CAACI,MAAM,EAChB/F,EAAO4F,OAAO,CAAC5F,EAAO6F,QAAQ,CAAC,CAACC,OAAO,EACvCV,EAAkB5O,EAAQwP,cAAc,CAAE,CAACxP,EAAQyP,mBAAmB,EAGjE,IAAI,CAACN,IAAI,CAACG,OAAO,EACtB,IAAI,CAACH,IAAI,CAACI,MAAM,EAChB/F,EAAO4F,OAAO,CAAC5F,EAAO6F,QAAQ,CAAC,CAACC,OAAO,EACxCJ,CAAAA,GAAa,2BAA0B,EALvCA,GAAa,wCAOVA,CACX,CAQA5E,SAAU,CACN,MAAOoF,CAAAA,CAAQ,CAAA,IAAI,CAAC1F,EAAE,EAAI2E,EAAS,IAAI,CAACpE,KAAK,CAAA,CACjD,CACA/B,SAASuB,CAAK,CAAE,CACZ,KAAK,CAACvB,SAASmH,KAAK,CAAC,IAAI,CAAEb,WAEvB,IAAI,CAACnF,OAAO,EACZ,IAAI,CAACA,OAAO,CAAC/D,IAAI,CAAC,CACdvF,OAAQ0J,CAAAA,CAAAA,AAAU,UAAVA,CAAgB,CAC5B,EAER,CACA+D,YAAa,CACT,OAAOa,EAAS,IAAI,CAACiB,KAAK,GAAK,AAAW,OAAX,IAAI,CAACzO,CAAC,AACzC,CACJ,CACAuN,EAAoBG,EAAarP,SAAS,CAAE,CACxCqQ,WAAYtB,EAAS/O,SAAS,CAACqQ,UAAU,AAC7C,GAuBA,GAAM,CAAE5N,SAAU6N,CAA8B,CAAE,CAAIlQ,IAmBhDmQ,EAAwB,CA4B1BN,oBAAqB,CAAA,EACrBO,eAAgB,IAIhBC,aAAc,EAmFdC,aAAc,CAAA,EAIdC,OAAQ,KAAK,EAYbC,aAAc,CAAA,EAIdC,WAAY,CACRC,QAAS,CAAA,EACTjL,UAAW,WACP,IAAMoE,EAAQ,IAAI,EAAI,IAAI,CAACA,KAAK,CAC5B,IAAI,CAACA,KAAK,CACV,CAAC,EACL,OADeqG,EAA+BrG,EAAM8G,IAAI,EAAI9G,EAAM8G,IAAI,CAAG,EAE7E,EAWAC,QAAS,CAAA,EACTC,OAAQ,CAAA,EACRrQ,QAAS,EACTa,cAAe,SACfT,MAAO,CACHkQ,aAAc,UAClB,CACJ,EACAC,QAAS,CACLC,aAAc,GACdC,YAAa,0CAabC,cAAe,4CACnB,EAOAC,kBAAmB,CAAA,EAmBnBC,gBAAiB,eAOjBC,wBAAyB,WAWzBC,2BAA4B,CAAA,EAS5BC,gBAAiB,CAAA,EAQjBC,iBAAkB,CAIdrQ,SAAU,CAcNC,MAAO,QAIPE,EAAG,IAIHC,EAAG,EACP,CACJ,EAkKAkQ,YAAa,UAIbC,YAAa,EACb3G,SAAU,aAQV4G,QAAS,IAMTjR,OAAQ,CAOJkR,MAAO,CAIHH,YAAa,UAQbI,WAAY,AAAwF,IAAxF,AAAC3I,IAA6DM,WAAW,CAACsI,OAAO,CAI7FC,KAAM,CAAA,EAONJ,QAAS,IAITK,OAAQ,CAAA,CACZ,CACJ,EACAC,aAAc,YAcdC,eAAgB,CAAA,EAsBhBC,QAAS,CAQL7C,UAAW,KAAK,EAQhBzO,MAAO,KAAK,EAQZ6P,QAAS,CAAA,EAQT0B,WAAY,KAAK,EASjBC,YAAa,KAAK,EAUlB1B,KAAM,KAAK,EAWX2B,gBAAiB,KAAK,EAStBC,mBAAoB,EACpBnB,gBAAiB,CACboB,SAAU,EACVC,SAAU,EACVC,gBAAiB,CACrB,EACAnC,OAAQ,CACJoC,UAAW,EACXC,OAAQ,CACZ,CACJ,CACJ,CAmIIlU,CACDA,CAAAA,GAAqBA,CAAAA,EAAmB,CAAC,CAAA,CAAC,EADxBmU,SAAS,CAN1B,SAASA,EAAUC,CAAI,CAAEC,CAAI,CAAEC,CAAO,EAClC,IAAMC,EAAOF,EAAKjT,IAAI,CAACkT,GAAW,IAAI,CAAEF,EAC3B,EAAA,IAATG,GACAJ,EAAUI,EAAMF,EAAMC,EAE9B,EAQyB,IAAME,EAA4BxU,EAiBzD,CAAEyD,OAAQgR,CAAoB,CAAEC,QAAAA,CAAO,CAAErE,SAAUsE,CAAsB,CAAEC,SAAAA,EAAQ,CAAEhR,MAAOiR,EAAmB,CAAE/Q,KAAMgR,EAAkB,CAAEC,eAAAA,EAAc,CAAE,CAAIzT,IAuMlI0T,GAPb,CAClBC,SAvLJ,SAAkBpE,CAAI,CAAEnP,CAAO,EAC3B,IACqByJ,EAAOvG,EAAOkN,EAAcoD,EAAmB/S,EAAOgT,EADrEC,EAAQ1T,EAAQ0T,KAAK,CAAEC,EAAoB3T,EAAQ2T,iBAAiB,CAAEC,EAAc5T,EAAQ4T,WAAW,CAAEC,EAAmB7T,EAAQ6T,gBAAgB,CAAErK,EAASxJ,EAAQwJ,MAAM,CAAEsK,EAAS9T,EAAQ8T,MAAM,CAAEC,EAAW/T,EAAQ+T,QAAQ,CAAEC,EAASxK,EAAOwK,MAAM,CAAEC,EAAoBzK,EAAOjH,KAAK,CAACvC,OAAO,CAACuC,KAAK,CA+BjT,OAhBI4M,IACA1F,EAAQuK,CAAM,CAAC7E,EAAK9G,CAAC,CAAC,CACtBnF,EAAQyQ,CAAiB,CAACxE,EAAKjM,KAAK,CAAC,EAAI,CAAC,EACxBuG,GAASvG,EAAMkN,YAAY,GAEzCoD,EAAoB/J,EAAMiK,KAAK,CAAII,CAAAA,EAC/BA,EAAOjP,MAAM,CACboP,EAAkBC,UAAU,AAAD,EAC/B9D,EAAe0D,GAAUA,CAAM,CAACN,EAAkB,EAGjDhK,EAAOjH,KAAK,CAAC+E,UAAU,EACxB7G,CAAAA,EAAQ2S,GAAmB3J,GAASA,EAAMzJ,OAAO,CAACS,KAAK,CAAEyC,GAASA,EAAMzC,KAAK,CAAE2P,EAAcwD,GAAeO,AAtB/F,CAAA,AAAC1T,IAClB,IAAM2T,EAAiBlR,GAASA,EAAMkR,cAAc,QACpD,AAAIA,GACAA,AAAuB,eAAvBA,EAAepV,GAAG,EAClB0U,GACAK,EACOpL,IAAmD0L,KAAK,CAAC5T,GAAO6T,QAAQ,CAACF,EAAeG,EAAE,CAAIb,CAAAA,EAAQK,CAAO,GAAI1U,GAAG,GAExHoB,CACX,CAAA,EAaiImT,GAAcpK,EAAO/I,KAAK,CAAA,EAEvJgT,EAAaL,GAAmB3J,GAASA,EAAMzJ,OAAO,CAACyT,UAAU,CAAEvQ,GAASA,EAAMuQ,UAAU,CAAED,EAAmBK,EAAkB7T,EAAQyT,UAAU,GAElJ,CACHhT,MAAOA,EACPgT,WAAYA,CAChB,CACJ,EAoJIe,gBAlIJ,SAAyBjH,CAAM,EAC3B,IACIkH,EAAUC,EAAWrM,EAAGsM,EAAMJ,EAAIK,EADhCC,EAAS,CAAC,EAEhB,GAAI3B,GAAS3F,GA2BT,IA1BAoH,EAAO1B,EAAuB1F,EAAOoH,IAAI,EAAIpH,EAAOoH,IAAI,CAAG,EAC3DC,EAASrH,EAAOqH,MAAM,CACtBF,EAAY,CAAC,EACbD,EAAWvB,GAAS3F,EAAOkH,QAAQ,EAAIlH,EAAOkH,QAAQ,CAAG,CAAC,EACtDzB,EAAQ4B,IACRF,CAAAA,EAAYE,EAAOE,MAAM,CAAC,CAACxV,EAAKoT,KAC5B,IAAIxP,EAAOiO,EAAiBnR,EAgB5B,OAfIkT,GAASR,IAASO,EAAuBP,EAAKxP,KAAK,IAEnDiO,EAAkBiC,GAAmBpT,AADrCA,CAAAA,EAAUmT,GAAoB,CAAC,EAAGT,EAAI,EACOvB,eAAe,CAAEsD,EAAStD,eAAe,EAEtF,OAAOnR,EAAQmR,eAAe,CAC9B,OAAOnR,EAAQkD,KAAK,CAGhBgQ,GAAS5T,CAAG,CADhB4D,EAAQwP,EAAKxP,KAAK,CAAIiO,CAAAA,EAAkB,EAAIwD,EAAO,CAAA,EAC5B,EACnBxB,GAAoB,CAAA,EAAM7T,CAAG,CAAC4D,EAAM,CAAElD,GAGtCV,CAAG,CAAC4D,EAAM,CAAGlD,GAGdV,CACX,EAAG,CAAC,EAAC,EAETiV,EAAKtB,EAAuB1F,EAAOgH,EAAE,EAAIhH,EAAOgH,EAAE,CAAG,EAChDlM,EAAI,EAAGA,GAAKkM,EAAIlM,IACjBwM,CAAM,CAACxM,EAAE,CAAG8K,GAAoB,CAAC,EAAGsB,EAAUvB,GAASwB,CAAS,CAACrM,EAAE,EAAIqM,CAAS,CAACrM,EAAE,CAAG,CAAC,GAG/F,OAAOwM,CACX,EAgGIE,aAvBJ,SAAsBvL,CAAM,CAAEwL,CAAW,EACrC,GAAM,CAAEzS,MAAAA,CAAK,CAAEvC,QAAAA,CAAO,CAAE,CAAGwJ,EAAQ,CAAEyL,aAAAA,EAAe,CAAC,CAAEC,UAAAA,EAAY,CAAC,CAAE,CAAGlV,EAAS,CAAEmV,UAAAA,EAAY,CAAC,CAAE,CAAG5S,EAGtG,GAAI2S,AAAc,SAAdA,EAAsB,CACtB,GAAI,AAAwB,UAAxB,OAAOD,GAA6B,KAAKG,IAAI,CAACH,GAE9C,OAAOE,EADkDH,CAAAA,EAAcK,AAAtDC,WAAWL,GAAgB,IAAuCD,CAAAA,EAAc,CAAA,CAAC,EAGtG,IAAMO,EAAYC,OAAOP,GACzB,MAAO,AAAEE,CAAAA,EAAYI,CAAQ,EACxBP,CAAAA,GAAe,CAAA,EAAMO,CAC9B,CACA,OAAOlC,GAAe6B,EAAWC,EACrC,EAUIM,cA3FJ,SAASA,EAAcC,CAAI,CAAE1V,CAAO,EAChC,IAAM2V,EAAS3V,EAAQ2V,MAAM,CAAEC,EAAS5V,EAAQ4V,MAAM,CAAqCC,EAAWC,AAAhC9V,EAAQ8V,WAAW,AAAwB,CAACF,EAAO,CAAEzE,EAAmBnR,AAA4B,CAAA,IAA5BA,EAAQmR,eAAe,CAAsC1H,EAAQuK,AAAxBhU,EAAQgU,MAAM,AAAgB,CAAC0B,EAAKrN,CAAC,CAAC,CAAE0N,EAAetM,GAASA,EAAMzJ,OAAO,EAAI,CAAC,EAAGqN,EAAW,EAAE,CACzRH,EAAgB,CACpBwI,CAAAA,EAAKM,YAAY,CAAGN,EAAKxS,KAAK,CAAIiO,CAAAA,EAAkB,EAAI0E,EAAS3S,KAAK,AAAD,EACrEwS,EAAKnF,IAAI,CAAG6C,GAAmB3J,GAASA,EAAM8G,IAAI,CAAE,IACpDmF,EAAKvI,OAAO,CAAIyI,IAAWF,EAAK1L,EAAE,EAC9BhK,AAAoB,CAAA,IAApBA,EAAQmN,OAAO,CACG,YAAlB,OAAOwI,GACPD,CAAAA,EAAOC,EAAOD,EAAM1V,EAAO,EAG/B0V,EAAKrI,QAAQ,CAAC4I,OAAO,CAAC,CAACC,EAAO7N,KAC1B,IAAM8N,EAAapD,EAAqB,CAAC,EAAG/S,GAC5C+S,EAAqBoD,EAAY,CAC7BzC,MAAOrL,EACP0L,SAAU2B,EAAKrI,QAAQ,CAACxI,MAAM,CAC9BsI,QAASuI,EAAKvI,OAAO,AACzB,GACA+I,EAAQT,EAAcS,EAAOC,GAC7B9I,EAASN,IAAI,CAACmJ,GACVA,EAAM/I,OAAO,EACbD,CAAAA,GAAiBgJ,EAAME,GAAG,AAAD,CAEjC,GAEA,IAAM7L,EAAQ6I,GAAmB2C,EAAaxL,KAAK,CAAE2C,GAMrD,OALAwI,EAAKvI,OAAO,CAAG5C,GAAS,GAAM2C,CAAAA,EAAgB,GAAKwI,EAAKvI,OAAO,AAAD,EAC9DuI,EAAKrI,QAAQ,CAAGA,EAChBqI,EAAKxI,aAAa,CAAGA,EACrBwI,EAAKnG,MAAM,CAAGmG,EAAKvI,OAAO,EAAI,CAACD,EAC/BwI,EAAKU,GAAG,CAAG7L,EACJmL,CACX,EA4DIW,aA/CJ,SAAsB7M,CAAM,EACxB,IAAI8M,EAAQtW,EAaZ,OAZIkT,GAAS1J,KAETxJ,EAAUkT,GAAS1J,EAAOxJ,OAAO,EAAIwJ,EAAOxJ,OAAO,CAAG,CAAC,EAEvDsW,EAASlD,GAAmB5J,EAAO6F,QAAQ,CAAErP,EAAQsW,MAAM,CAAE,IAEzDpD,GAAS1J,EAAOvF,WAAW,GAC3BuF,CAAAA,EAAOvF,WAAW,CAACqS,MAAM,CAAGA,CAAK,EAGrC9M,EAAO6F,QAAQ,CAAGiH,GAEfA,CACX,CAiCA,EAkBM,CAAEjC,MAAO5T,EAAK,CAAE,CAAIkI,IAGpB,CAAE/G,SAAU2U,EAAsB,CAAEC,KAAAA,EAAI,CAAE,CAAI5W,IAG9C,CAAEsJ,OAAQuN,EAAY,CAAEjI,QAASkI,EAAa,CAAE,CAAG,AAAC5N,IAA6DM,WAAW,CAO5H,CAAEmK,SAAUoD,EAAsB,CAAEnC,gBAAiBoC,EAA6B,CAAEP,aAAcQ,EAA0B,CAAE,CAAGvD,GAEjI,CAAEzR,SAAUiV,EAAsB,CAAEC,SAAAA,EAAQ,CAAEC,MAAAA,EAAK,CAAEC,aAAAA,EAAY,CAAEC,MAAAA,EAAK,CAAEpV,QAASqV,EAAqB,CAAEC,MAAAA,EAAK,CAAErV,OAAQsV,EAAoB,CAAErV,UAAWsV,EAAuB,CAAEtE,QAASuE,EAAqB,CAAE5I,SAAU6I,EAAsB,CAAEtE,SAAUuE,EAAsB,CAAExV,SAAUyV,EAAsB,CAAExV,MAAOyV,EAAmB,CAAEvV,KAAMwV,EAAkB,CAAEvV,WAAYwV,EAAwB,CAAEC,MAAAA,EAAK,CAAEC,WAAAA,EAAU,CAAE,CAAInY,IAClb6L,IAAoDuM,SAAS,CAACjL,IAAI,CAAC,aAAc,wBAYjF,IAAIkL,GAA2B,CAAA,EAO/B,SAASC,KACL,IACIC,EADiBC,EAAQ5O,AAAd,IAAI,CAAiB4O,KAAK,CAAEC,EAAQ7O,AAApC,IAAI,CAAuC6O,KAAK,CAE3DD,GAASC,IACL7O,AAHO,IAAI,CAGJ8O,EAAE,CAAC,YACVH,EAAW,CACPI,UAAW,CAAA,EACXC,cAAe,EACfjG,UAAW,EACXkG,IAAK,EACLC,WAAY,EACZ9L,IAxBA,IAyBA+L,WAAY,EACZC,YAAa,CAAA,EACbC,MAAO,KAAK,EACZC,cAAe,EAAE,AACrB,EACAzB,GAAqBgB,EAAMrY,OAAO,CAAEmY,GACpCd,GAAqBe,EAAMpY,OAAO,CAAEmY,GACpCF,GAA2B,CAAA,GAEtBA,KACLI,EAAMU,UAAU,CAACV,EAAMpU,WAAW,EAClCmU,EAAMW,UAAU,CAACX,EAAMnU,WAAW,EAClCgU,GAA2B,CAAA,GAGvC,CAaA,MAAMe,WAAsBtC,GACxB1S,aAAc,CAMV,KAAK,IAAI8K,WACT,IAAI,CAACmK,UAAU,CAAG,CAEtB,CAMA,OAAOpV,QAAQuH,CAAW,CAAE,CACpByM,GAAyBtB,GAAwB,kBACjDO,GAAuB1L,EAAa,gBAAiB8M,GAE7D,CAOAgB,oBAAoBC,CAAe,CAAEC,CAAI,CAAE5T,CAAK,CAAE6T,CAAY,CAAE,CAC5D,IAAMvN,EAAOtG,EAAMsG,IAAI,CAAEwN,EAAM9T,EAAM8G,KAAK,CAACzH,MAAM,CAAG,EAChD0U,EAAIC,EAAIC,EAAIC,EAAIC,EAAKnU,EAAM2G,EAAE,CAAEyN,EAAKpU,EAAM6G,EAAE,CAAEwN,EAAMxR,EAAI,EAQ5D,IAAK,IAAMwD,KAPPuN,GACAO,EAAKnU,EAAM0G,EAAE,CACb0N,EAAKpU,EAAM4G,EAAE,EAGbyN,EAAOrU,EAAM8G,KAAK,CAACgN,EAAI,CAEX9T,EAAM8G,KAAK,EACnB8M,CAAAA,GAAS/Q,EAAIiR,CAAG,IACZ9T,AAAoB,IAApBA,EAAMuG,SAAS,EACfwN,EAAKzN,EAAK5K,CAAC,CACXsY,EAAK1N,EAAK3K,CAAC,CAEXuY,EAAK7N,EADL4N,CAAAA,EAAKE,CAAC,IAINJ,EAAKzN,EAAK5K,CAAC,CACXsY,EAAK1N,EAAK3K,CAAC,CAEXsY,EAAK5N,EADL6N,CAAAA,EAAKE,CAAC,GAGVP,EAAatM,IAAI,CAAC,CACd7L,EAAGqY,EACHpY,EAAGqY,EACH1W,MAAO2W,EACPtZ,OAAQ8W,GAAayC,EACzB,GACIlU,AAAoB,IAApBA,EAAMuG,SAAS,CACfD,EAAK3K,CAAC,CAAG2K,EAAK3K,CAAC,CAAGuY,EAGlB5N,EAAK5K,CAAC,CAAG4K,EAAK5K,CAAC,CAAGuY,GAG1BpR,GAAQ,EAGZ7C,EAAMwH,KAAK,GACPxH,AAAoB,IAApBA,EAAMuG,SAAS,CACfvG,EAAM1C,KAAK,CAAG0C,EAAM1C,KAAK,CAAG6W,EAG5BnU,EAAMrF,MAAM,CAAGqF,EAAMrF,MAAM,CAAGyZ,EAElC9N,EAAK3K,CAAC,CAAG2K,EAAKwB,MAAM,CAACnM,CAAC,CAAI2K,CAAAA,EAAKwB,MAAM,CAACnN,MAAM,CAAGqF,EAAMrF,MAAM,AAAD,EAC1D2L,EAAK5K,CAAC,CAAG4K,EAAKwB,MAAM,CAACpM,CAAC,CAAI4K,CAAAA,EAAKwB,MAAM,CAACxK,KAAK,CAAG0C,EAAM1C,KAAK,AAAD,EACpDqW,GACA3T,CAAAA,EAAMuG,SAAS,CAAG,EAAIvG,EAAMuG,SAAS,AAAD,EAGnCqN,GACD5T,EAAMqH,UAAU,CAACgN,EAEzB,CACAC,cAAcX,CAAe,CAAE7L,CAAM,CAAED,CAAQ,CAAE,CAC7C,IAAMgM,EAAe,EAAE,CACnBU,EAAMhO,EAAYuB,EAAOvB,SAAS,CAAE7K,EAAIoM,EAAOpM,CAAC,CAAEC,EAAImM,EAAOnM,CAAC,CAAE2B,EAAQwK,EAAOxK,KAAK,CAAE3C,EAASmN,EAAOnN,MAAM,CAAEoZ,EAAIC,EAAIC,EAAIC,EAC9H,IAAK,IAAMxD,KAAS7I,EAChB0M,EACI,AAACzM,EAAOxK,KAAK,CAAGwK,EAAOnN,MAAM,CAAK+V,CAAAA,EAAME,GAAG,CAAG9I,EAAO8I,GAAG,AAAD,EAC3DmD,EAAKrY,EACLsY,EAAKrY,EACD4K,AAAc,IAAdA,GAGAjJ,GADA2W,EAAKM,EADLL,CAAAA,EAAKvZ,CAAK,EAGVe,GAAQuY,IAKRtZ,GADAuZ,EAAKK,EADLN,CAAAA,EAAK3W,CAAI,EAGT3B,GAAQuY,GAEZL,EAAatM,IAAI,CAAC,CACd7L,EAAGqY,EACHpY,EAAGqY,EACH1W,MAAO2W,EACPtZ,OAAQuZ,EACR3N,UAAW,EACXqK,IAAK,CACT,GACI+C,GACApN,CAAAA,EAAY,EAAIA,CAAQ,EAGhC,OAAOsN,CACX,CACAW,wBAAwBb,CAAe,CAAE7L,CAAM,CAAED,CAAQ,CAAE,CACvD,IAAqBgM,EAAe,EAAE,CAAEvN,EAAO,CAC3C5K,EAAGoM,EAAOpM,CAAC,CACXC,EAAGmM,EAAOnM,CAAC,CACXmM,OAAQA,CACZ,EAAGvB,EAAYuB,EAAOvB,SAAS,CAAEuN,EAAMjM,EAASxI,MAAM,CAAG,EAAGW,EAAQ,IAAIkG,EAA8B4B,EAAOnN,MAAM,CAAEmN,EAAOxK,KAAK,CAAEiJ,EAAWD,GAC1IiO,EAAM1R,EAAI,EAEd,IAAK,IAAM6N,KAAS7I,EAChB0M,EACI,AAACzM,EAAOxK,KAAK,CAAGwK,EAAOnN,MAAM,CAAK+V,CAAAA,EAAME,GAAG,CAAG9I,EAAO8I,GAAG,AAAD,EAC3D5Q,EAAMqH,UAAU,CAACkN,GACbvU,EAAM+G,EAAE,CAACC,EAAE,CAAGhH,EAAM+G,EAAE,CAACE,EAAE,EACzBjD,AAZO,IAAI,CAYJ0P,mBAAmB,CAACC,EAAiB,CAAA,EAAO3T,EAAO6T,EAAcvN,GAIxEzD,IAAMiR,GACN9P,AAjBO,IAAI,CAiBJ0P,mBAAmB,CAACC,EAAiB,CAAA,EAAM3T,EAAO6T,EAAcvN,GAG3E,EAAEzD,EAEN,OAAOgR,CACX,CAKAY,eAAexQ,CAAK,CAEpByQ,CAAS,CAETC,CAAY,CAAE,CACV1D,GAAajX,SAAS,CAACya,cAAc,CAACtK,KAAK,CAAC,IAAI,CAAEb,WAC9CrF,EAAMyQ,SAAS,EAEfzQ,EAAMyQ,SAAS,CAACtU,IAAI,CAAC,CAAEvF,OAAQ,AAACoJ,CAAAA,EAAM0F,IAAI,CAAC9O,MAAM,EAAI,CAAA,EAAK,CAAE,EAEpE,CACA+Z,mBAAoB,CAChB,IAAM5Q,EAAS,IAAI,CAAE6Q,EAAa7Q,EAAO6Q,UAAU,EAAI,CAAC,EAAG,CAAEtI,QAAAA,CAAO,CAAE,CAAGvI,EAAOxJ,OAAO,CAAEmS,EAAqBJ,GAASI,oBAAsB,EAC7I,GAAIJ,GAASzB,QAAS,CAClB,IAAMgK,EAAe,CAAC,EAChBC,EAAc,AAACpL,IACjB,GAAIA,GAAM1F,OAAOyE,UAAW,CACxB,GAAM,CAAEpL,MAAAA,EAAQ,CAAC,CAAE3C,OAAAA,EAAS,CAAC,CAAE,CAAGgP,EAAK1F,KAAK,CAACyE,SAAS,CAChD,CAAE8D,WAAAA,EAAa,CAAC,CAAEC,YAAAA,EAAc,CAAC,CAAE,CAAGF,EAASyI,EAAgBrD,GAAsBlF,GAAcwI,EAAgBxI,EACrHD,EAAaC,EACbD,EAAaA,EACblP,CAAAA,EAAQkP,GACR7R,EAAUqa,CAAAA,EAAgBvI,EAAcD,CAAS,GACjD0I,AAN2D5X,EAAQ3C,EAM5Dsa,CAAY,GACf,CAACtL,EAAKG,OAAO,EAAI6H,GAAsBhI,EAAK7B,MAAM,IAC7CgN,CAAY,CAACnL,EAAK7B,MAAM,CAAC,EAC1BgN,CAAAA,CAAY,CAACnL,EAAK7B,MAAM,CAAC,CAAG,EAAE,AAAD,EAEjCgN,CAAY,CAACnL,EAAK7B,MAAM,CAAC,CAACP,IAAI,CAACoC,GAG3C,CACAA,GAAM9B,SAAS4I,QAAQ,AAACC,IACpBqE,EAAYrE,EAChB,EACJ,EAEA,IAAK,IAAM5I,KADXiN,EAAY/Q,EAAOkM,IAAI,EACF4E,EACbA,CAAY,CAAChN,EAAO,EAChBgN,CAAY,CAAChN,EAAO,CAACzI,MAAM,CAAGsN,GAC9BmI,CAAY,CAAChN,EAAO,CAAC2I,OAAO,CAAC,AAAC9G,IAC1B,IAAMuE,EAAQ2G,CAAU,CAAC/M,EAAO,CAACqN,OAAO,CAACxL,EAAK9G,CAAC,EAC/C,GAAIqL,AAAU,KAAVA,EAAc,CACd2G,CAAU,CAAC/M,EAAO,CAACsN,MAAM,CAAClH,EAAO,GACjC,IAAM1J,EAAK,CAAC,kCAAkC,EAAEmF,EAAK7B,MAAM,EAAI,OAAO,CAAC,CACnEuN,EAAarR,EAAOwK,MAAM,CACzB8G,IAAI,CAAC,AAACjP,GAAMA,EAAE7B,EAAE,GAAKA,GAC1B,GAAI,CAAC6Q,EAAY,CACb,IAAME,EAAavR,EAAO6B,UAAU,CAAE2P,EAAaxR,EAAOwK,MAAM,CAACnP,MAAM,CASvEwS,GARAwD,EAAa,IAAIE,EAAWvR,EAAQ,CAChC0F,UAAW6C,EAAQ7C,SAAS,CAC5BzO,MAAOsR,EAAQtR,KAAK,CACpBuJ,GAAAA,EACA0J,MAAOsH,EACP1L,QAAS,CAAA,EACT/E,MAAO,CACX,GACiC,CAC7B0Q,aAAc,SAClB,GACAzR,EAAOwK,MAAM,CAACjH,IAAI,CAAC8N,GACnBR,CAAU,CAAC/M,EAAO,CAACP,IAAI,CAACiO,GACxBX,CAAU,CAACrQ,EAAG,CAAG,EAAE,AACvB,CACA,IAAMkR,EAASL,EAAW9L,mBAAmB,CAAG,EAAGqH,EAAM5M,EAAOwK,MAAM,CAAC6G,EAAWnH,KAAK,CAAC,CACnF1T,OAAO,CAACuK,KAAK,EAAI,EAAGgG,EAAOwB,EAAQxB,IAAI,EACxC,CAAC,EAAE,EAAE2K,EAAO,CAAC,AAGjB1R,CAAAA,EAAOwK,MAAM,CAAC6G,EAAWnH,KAAK,CAAC,CAC1B3E,mBAAmB,CAAGmM,EAC3B1R,EAAOwK,MAAM,CAAC6G,EAAWnH,KAAK,CAAC,CAAC1T,OAAO,CAACuK,KAAK,CACzC6L,EAAOjH,CAAAA,EAAK1F,KAAK,CAACc,KAAK,EAAI,CAAA,EAC/Bf,EAAOwK,MAAM,CAAC6G,EAAWnH,KAAK,CAAC,CAACnD,IAAI,CAAGA,EACvC8J,CAAU,CAACrQ,EAAG,CAAC+C,IAAI,CAACoC,EAAK1F,KAAK,CAACiK,KAAK,CACxC,CACJ,EAIZlK,CAAAA,EAAO4F,OAAO,CAAG,CAAC,EAClB5F,EAAO2R,QAAQ,CAAG,EAAE,CACpB3R,EAAO6Q,UAAU,CAAGA,EACpB,IAAM3E,EAAOlM,EAAO4R,SAAS,CAAC,GAAI,GAAI,EAAG5R,EAAO6Q,UAAU,EAC1D7Q,EAAOpB,SAAS,CAACsN,EACrB,CACJ,CAcA2F,uBAAuB/N,CAAM,CAAEoN,CAAI,CAAE,CACjC,IAAqB1a,EAAUwJ,AAAhB,IAAI,CAAmBxJ,OAAO,CAAgDkD,EAAQyQ,AAAlCnK,AAApD,IAAI,CAAuDmK,iBAAiB,AAA2B,CAACrG,EAAOpK,KAAK,CAAG,EAAE,CAAEoY,EAAY1D,GAAoB1U,GAAO8N,iBAC7KxH,AADW,IAAI,AACT,CAACtG,GAAO8N,gBAAgB,EAC9B9N,EAAM8N,eAAe,CAAGhR,EAAQgR,eAAe,EAAGuK,EAAYvb,EAAQkR,0BAA0B,CAEpG7D,EAAWC,EAAOD,QAAQ,CAACmO,MAAM,CAAC,AAAChd,GAAM8O,EAAOgC,OAAO,EAAI,CAAC9Q,EAAEid,MAAM,EAAGC,EAAexY,GAAOwY,cAAgB1b,EAAQ0b,YAAY,EAAI,EAAGrM,EAAW7F,AAJpI,IAAI,CAIuI4F,OAAO,CAAC5F,AAJnJ,IAAI,CAIsJ6F,QAAQ,CAAC,CAClL,GAAI,CAACiM,EACD,OAEJ,IAAIK,EAAiB,EAAE,CAAEC,EAAYvM,EAASwM,WAAW,EAAE/Y,OAAS,EAAGgZ,EAAazM,EAASwM,WAAW,EAAE1b,QAAU,EAChH+C,GAAO+N,yBACPyJ,CAAAA,EAAK3O,SAAS,CAAG7I,CAAAA,CAAAA,AAAkC,aAAlCA,EAAM+N,uBAAuB,AAAc,CAExD,EAER0K,EAAiBnS,AAdF,IAAI,AAcI,CAAC8R,EAAU,CAACZ,EAAMrN,GACzC,IAAIhF,EAAI,GACR,IAAK,IAAM6N,KAAS7I,EAAU,CAC1B,IAAM0O,EAASJ,CAAc,CAAC,EAAEtT,EAAE,CAC9B6N,IAAU7G,IACVuM,EAAYA,GAAaG,EAAOjZ,KAAK,CACrCgZ,EAAaC,EAAO5b,MAAM,EAE9B,IAAM6b,EAAsBN,EAAgBlS,CAAAA,AAtBjC,IAAI,CAsBoC4O,KAAK,CAAC6D,GAAG,CAAGH,CAAS,EAAII,EAAsBR,EAAgBlS,CAAAA,AAtBvG,IAAI,CAsB0G6O,KAAK,CAAC4D,GAAG,CAAGH,CAAS,EAM9I,GALA5F,EAAM6F,MAAM,CAAGpE,GAAoBoE,EAAQ,CACvC3F,IAAKF,EAAMhJ,aAAa,CACxBnB,UAAYwP,EAAY,EAAIb,EAAK3O,SAAS,CAAG2O,EAAK3O,SAAS,AAC/D,GAEImK,EAAM7I,QAAQ,CAACxI,MAAM,EACrBqR,EAAMzM,KAAK,CAAC4G,UAAU,EAAExL,OAAQ,CAChC,IAAMsX,EAAWpF,GAASb,EAAMzM,KAAK,CAAC4G,UAAU,CAAC+L,GAAG,CAAC,AAACC,GAAOA,EAAGrc,OAAO,EACjEwQ,SAAW6L,EAAGlc,MAAM,EAAI,IAAOqJ,CAAAA,AA/B9B,IAAI,CA+BiC6O,KAAK,CAAC4D,GAAG,CAAGH,CAAS,EAE7DK,EAAWjG,EAAM6F,MAAM,CAAC5b,MAAM,CAAG,IACjC+V,EAAM6F,MAAM,CAAC5a,CAAC,EAAIgb,EAClBjG,EAAM6F,MAAM,CAAC5b,MAAM,EAAIgc,EAE/B,CACA,GAAIT,EAAc,CACd,IAAMY,EAAO3P,KAAK8L,GAAG,CAACuD,EAAqB9F,EAAM6F,MAAM,CAACjZ,KAAK,CAAG,GAAIyZ,EAAO5P,KAAK8L,GAAG,CAACyD,EAAqBhG,EAAM6F,MAAM,CAAC5b,MAAM,CAAG,EAC/H+V,CAAAA,EAAM6F,MAAM,CAAC7a,CAAC,EAAIob,EAClBpG,EAAM6F,MAAM,CAACjZ,KAAK,EAAI,EAAIwZ,EAC1BpG,EAAM6F,MAAM,CAAC5a,CAAC,EAAIob,EAClBrG,EAAM6F,MAAM,CAAC5b,MAAM,EAAI,EAAIoc,CAC/B,CACArG,EAAM2F,WAAW,CAAGlE,GAAoBoE,EAAQ,CAC5C7a,EAAI6a,EAAO7a,CAAC,CAAGsI,AA9CR,IAAI,CA8CWgT,SAAS,CAG/Brb,EAAGsb,AA9VH,IA8VaV,EAAO5a,CAAC,CAAG4a,EAAO5b,MAAM,CACrC2C,MAAQiZ,EAAOjZ,KAAK,CAAG0G,AAlDhB,IAAI,CAkDmBgT,SAAS,AAC3C,GAEItG,EAAM7I,QAAQ,CAACxI,MAAM,EACrB2E,AAtDO,IAAI,CAsDJ6R,sBAAsB,CAACnF,EAAOA,EAAM6F,MAAM,CAEzD,CACA,IAAMW,EAAuB,CAACvN,EAAM0F,EAAS,EAAE,CAAE8H,EAAY,CAAA,CAAI,IAC7DxN,EAAK9B,QAAQ,CAAC4I,OAAO,CAAC,AAACC,IACfyG,GAAazG,EAAM3G,MAAM,CACzBsF,EAAO9H,IAAI,CAACmJ,EAAMzM,KAAK,EAEjBkT,GAAczG,EAAM3G,MAAM,EAChCsF,EAAO9H,IAAI,CAACmJ,EAAMzM,KAAK,EAEvByM,EAAM7I,QAAQ,CAACxI,MAAM,EACrB6X,EAAqBxG,EAAOrB,EAAQ8H,EAE5C,GACO9H,GAGX,GAAI7U,AAAuB,SAAvBA,EAAQ4c,UAAU,EAClBtP,IAAW+B,GACX,IAAI,CAACwN,oBAAoB,EAGzB,CAACH,EAAqBrN,EAAU,KAAK,EAAG,CAAA,GACnCyN,IAAI,CAAC,AAACrT,GAAU+N,GAAuB/N,EAAMzJ,OAAO,CAACuK,KAAK,IAC/D,CAACiN,GAAuBnI,EAAS5F,KAAK,EAAEzJ,QAAQuK,OAAQ,CACxD,IAAMwS,EAASL,EAAqBrN,GAAW0M,EAASgB,EAAOX,GAAG,CAAC,AAAC3S,GAAUA,EAAMzJ,OAAO,CAACuK,KAAK,EAAI,GAErGyS,EAAQD,EAAOX,GAAG,CAAC,CAAC,CAAEjN,KAAM,CAAE0M,YAAAA,CAAW,CAAE,CAAE,GAAMA,EAC/CA,EAAY/Y,KAAK,CAAG+Y,EAAY1b,MAAM,CACtC,GAAK8c,EAAWlB,EAAOjH,MAAM,CAAC,CAACoI,EAAK3S,IAAU2S,EAAM3S,EAAO,GAA4D4S,EAAuBC,AAArEJ,EAAMlI,MAAM,CAAC,CAACoI,EAAK3S,IAAU2S,EAAM3S,EAAO,GAAqC0S,EACxJI,EAAU,EAAGC,EAAU,EAC3BP,EAAO9G,OAAO,CAAC,CAACxM,EAAOpB,KACnB,IAGAkV,EAAMvG,GAAMwG,AAHSzB,CAAAA,CAAM,CAAC1T,EAAE,CAAI2U,CAAK,CAAC3U,EAAE,CAAG0T,CAAM,CAAC1T,EAAE,CAAI,CAAA,EAG/B8U,EAAsB,GAAK,KAClDM,EAAO,EAAIF,CACX9T,CAAAA,EAAMc,KAAK,GAGPyS,CAAK,CAAC3U,EAAE,CAAG,IACXoV,CAAAA,GAAQT,CAAK,CAAC3U,EAAE,CAAG,EAAC,EAEpBoV,EAAOH,GACPA,CAAAA,EAAUG,CAAG,EAEbA,EAAOJ,GACPA,CAAAA,EAAUI,CAAG,EAEjBhU,EAAMiU,cAAc,CAAG,AAACjU,CAAAA,EAAMiU,cAAc,EAAIjU,EAAMc,KAAK,AAAD,EAAKgT,EAEvE,GAYA,AAACF,CAAAA,EAAU,MAASC,EAAU,GAAG,GAE7B,IAAI,CAACrE,UAAU,CAAG,IAClB,IAAI,CAACA,UAAU,GACf,IAAI,CAACxD,aAAa,CAACnI,GACnBoN,EAAKtE,GAAG,CAAG9I,EAAO8I,GAAG,CACrB,IAAI,CAACiF,sBAAsB,CAAC/N,EAAQoN,KAKpCqC,EAAO9G,OAAO,CAAC,AAACxM,IACZ,OAAOA,EAAMiU,cAAc,AAC/B,GACA,IAAI,CAACjI,aAAa,CAACnI,GACnB,IAAI,CAAC2L,UAAU,CAAG,EAE1B,CACJ,CAKA0E,WAAW3W,CAAC,CAAE,CACV,IAAMzE,EAAQ,IAAI,CAACA,KAAK,CAAEC,EAAcD,EAAMC,WAAW,CAAE4B,EAAO,EAAE,CACpE,GAAI5B,EAAa,CACb,IAAIob,EAAqB,EACzBxZ,EAAK2I,IAAI,CAAC,CACN7J,MAAO0a,EACPtY,aAAc/C,EAAMiH,MAAM,CAAC,EAAE,AACjC,GACA,IAAI2F,EAAOnI,EAAE6W,MAAM,CAACzO,OAAO,CAACpI,EAAE8W,SAAS,CAAC,CAClCC,EAAa,EAAE,CAGrB,KAAO5O,EAAK7B,MAAM,EAAI6B,AAAgB,KAAhBA,EAAK7B,MAAM,EAC7ByQ,EAAWhR,IAAI,CAACoC,GAChBA,EAAOnI,EAAE6W,MAAM,CAACzO,OAAO,CAACD,EAAK7B,MAAM,CAAC,CAExC,IAAK,IAAM6B,KAAQ4O,EAAWC,OAAO,GACjC5Z,EAAK2I,IAAI,CAAC,CACN7J,MAAO,EAAE0a,EACTtY,aAAc6J,CAClB,EAGA/K,CAAAA,EAAKS,MAAM,EAAI,GACfT,CAAAA,EAAKS,MAAM,CAAG,CAAA,CAEtB,CACA,OAAOT,CACX,CAeA6Z,gBAAiB,CACb,IAAqBtK,EAAoBnK,AAA1B,IAAI,CAA6BmK,iBAAiB,CAAEK,EAASxK,AAA7D,IAAI,CAAgEwK,MAAM,CAACwH,MAAM,CAAC,SAAUhd,CAAC,EACxG,OAAOA,EAAE2Q,IAAI,CAAChC,OAAO,EAAIgK,GAAsB3Y,EAAE0b,SAAS,CAC9D,GAAI9Z,EAAU0X,GAAMtO,AAFL,IAAI,CAEQxJ,OAAO,CAACqQ,UAAU,EAAI,CAAC,EAAE,CAAC,EAAE,EAAEjQ,QAAS8d,EAAkBlK,EAAO8I,IAAI,CAAC,AAACjR,GAAM2L,GAAuB3L,EAAE+D,KAAK,GACrI,IAAK,IAAMnG,KAASuK,EAAQ,CACxB,IAAMxT,EAAQ,CAAC,EAEfR,EAAU,CAAEQ,MAAAA,CAAM,EAAG0C,EAAQyQ,CAAiB,CAAClK,EAAM0F,IAAI,CAACjM,KAAK,CAAC,CAuBhE,GArBI,CAAA,CAACuG,EAAM0F,IAAI,CAACI,MAAM,EAClB,CAAC9F,EAAM0F,IAAI,CAACG,OAAO,EAClB7F,EAAM0F,IAAI,CAACG,OAAO,EACf7F,EAAM0F,IAAI,CAACjM,KAAK,EAAIsG,AAXjB,IAAI,CAWoB4F,OAAO,CAAC5F,AAXhC,IAAI,CAWmC6F,QAAQ,CAAC,CAACnM,KAAK,GAC7DlD,CAAAA,EAAQsQ,OAAO,CAAG,CAAA,CAAI,EAGtBpN,GAAOmN,aACPsH,GAAoB,CAAA,EAAM3X,EAAS8X,GAAM5U,EAAMmN,UAAU,CAAC,CAAC,EAAE,EAC7D7G,AAjBO,IAAI,CAiBJ2U,aAAa,CAAG,IAAM,CAAA,GAI7B1U,EAAM0F,IAAI,CAACI,MAAM,CACjBvP,EAAQyQ,MAAM,CAAG,CAAA,EAEZzQ,EAAQwQ,OAAO,EACpBxQ,CAAAA,EAAQiB,aAAa,CAAG,KAAI,EAI5BwI,EAAMyE,SAAS,EAAIgQ,EAAiB,CACpC,GAAM,CAAE/d,OAAAA,EAAS,CAAC,CAAE2C,MAAAA,EAAQ,CAAC,CAAE,CAAG2G,EAAMyE,SAAS,CACjD,GAAIpL,EAAQ,IAAM3C,EAAS,IAAMsJ,EAAMqE,UAAU,GAAI,CACjD,IAAMsQ,EAAiBtb,EACnB,EAAK9C,CAAAA,EAAQI,OAAO,EAAIA,GAAW,CAAA,CACvCI,CAAAA,EAAMsC,KAAK,CAAG,CAAC,EAAEsb,EAAe,EAAE,CAAC,CACnC5d,EAAM6d,SAAS,EAAK7d,CAAAA,EAAM6d,SAAS,CAAG1R,KAAK2R,KAAK,CAACne,EAAS,GAAE,EAC5DK,EAAM2J,UAAU,CAAG,UAEfnK,EAAQwQ,OAAO,EACf/G,EAAMyQ,SAAS,EAAEtU,KAAK,CAClB9C,MAAOsb,CACX,EAGR,MAEI5d,EAAMsC,KAAK,CAAG,CAAC,EAAEA,EAAM,EAAE,CAAC,CAC1BtC,EAAM2J,UAAU,CAAG,QAE3B,CAEAV,EAAM8U,SAAS,CAAG5G,GAAoB3X,EAASyJ,EAAMzJ,OAAO,CAACqQ,UAAU,CAC3E,CACA,KAAK,CAAC4N,eAAejK,EACzB,CAKAwK,WAAWxK,EAAS,IAAI,CAACA,MAAM,CAAE,CAC7B,IAAqBzR,EAAQiH,AAAd,IAAI,CAAiBjH,KAAK,CAAEkD,EAAWlD,EAAMkD,QAAQ,CAAE6B,EAAa/E,EAAM+E,UAAU,CAAEtH,EAAUwJ,AAAhG,IAAI,CAAmGxJ,OAAO,CAAE4R,EAAStK,EAAa,CAAC,EAAItH,EAAQ4R,MAAM,CAAE3B,EAAejQ,EAAQiQ,YAAY,CAAEwO,EAAuBlc,EAAMmc,UAAU,CAAG1e,EAAQgQ,cAAc,CAAEP,EAAsBzP,EAAQyP,mBAAmB,CAClU,IAAK,IAAMhG,KAASuK,EAAQ,CACxB,IAAMgC,EAAevM,EAAM0F,IAAI,CAAC6G,YAAY,CAAExI,EAAoB,CAAC,EAAGI,EAAU,CAAC,EAAGlG,EAAM,CAAC,EAAGiX,EAAW,eAAiBlV,EAAM0F,IAAI,CAACjM,KAAK,CAAE0b,EAAa,CAAC,CAACnV,EAAME,OAAO,CAAEkV,EAAgBJ,GAAwBG,EAAY1Q,EAAYzE,EAAMyE,SAAS,CAErPzE,EAAMqE,UAAU,KAChBrE,EAAMqV,QAAQ,CAAG,CAAA,EACb7O,GACArC,CAAAA,EAAQmR,CAAC,CAAG9O,CAAW,EAE3B0H,GAAoB,CAAA,EAEpBkH,EAAgBrR,EAAoBI,EAEpCgR,EAAa1Q,EAAY,CAAC,EAE1B5G,EACI,CAAC,EACDkC,AAjBG,IAAI,CAiBA2B,YAAY,CAAC1B,EAAOA,EAAMuV,QAAQ,CAAG,SAAW,KAAK,IAI5DxV,AArBG,IAAI,CAqBAwB,YAAY,EAAI1D,GAEvB+P,GAAqB3P,EAAK8B,AAvBvB,IAAI,CAuB0BwB,YAAY,CAACvB,IAE7CD,AAzBE,IAAI,AAyBA,CAACmV,EAAS,GACjBnV,AA1BG,IAAI,AA0BD,CAACmV,EAAS,CAAGlZ,EAASC,CAAC,CAACiZ,GACzB/Y,IAAI,CAAC,CAGNvF,OAAQ,IAAQ2V,CAAAA,GAAgB,CAAA,CACpC,GACKnQ,GAAG,CAAC2D,AAhCN,IAAI,CAgCShE,KAAK,EACrBgE,AAjCG,IAAI,AAiCD,CAACmV,EAAS,CAACM,OAAO,CAAG,CAAA,IAInCxV,EAAMuF,IAAI,CAAC,CACPxB,kBAAAA,EACAI,QAAAA,EACAlG,IAAAA,EACAlC,MAAOgE,AAzCA,IAAI,AAyCE,CAACmV,EAAS,CACvB1Q,SAAUxE,EAAMwE,QAAQ,CACxBxI,SAAAA,EACAmM,OAAAA,EACA1D,UAAAA,EACAH,UAAWtE,EAAMsE,SAAS,AAC9B,GAGI0B,GAAuBhG,EAAME,OAAO,EACpCF,CAAAA,EAAMyV,OAAO,CAAGlf,EAAQwP,cAAc,CAClChG,AApDG,IAAI,CAoDA2V,aAAa,CAAC1V,GACrBD,AArDG,IAAI,CAqDA4V,cAAc,CAAC3V,EAAK,CAEvC,CACJ,CAMA2V,eAAe3V,CAAK,CAAE,CAClB,MAAO,AAAC,CAAA,CAACA,EAAM0F,IAAI,CAACI,MAAM,IAAI9F,EAAM0F,IAAI,CAACG,OAAO,AAAD,GAC3C7F,EAAMO,EAAE,AAChB,CAMAmV,cAAc1V,CAAK,CAAE,CACjB,GAAM,CAAEqI,eAAAA,CAAc,CAAE,CAAGrI,EAAMD,MAAM,CAACxJ,OAAO,CAC3Ckf,EAAU,CAAA,EAAOG,EACrB,GAAI,AAAC5V,EAAM0F,IAAI,CAAC7B,MAAM,GAAK,IAAI,CAAC+B,QAAQ,EACpC5F,EAAM0F,IAAI,CAACI,MAAM,EACjB,GAAIuC,EACAoN,EAAUzV,EAAMO,EAAE,MAIlB,IADAqV,EAAa5V,EAAM0F,IAAI,CAChB,CAAC+P,GAC6B,KAAA,IAAtBG,EAAW/R,MAAM,EACxB+R,CAAAA,EAAa,IAAI,CAACjQ,OAAO,CAACiQ,EAAW/R,MAAM,CAAC,AAAD,EAE3C+R,EAAW/R,MAAM,GAAK,IAAI,CAAC+B,QAAQ,EACnC6P,CAAAA,EAAUG,EAAWrV,EAAE,AAAD,EAKtC,OAAOkV,CACX,CAKAI,YAAYtV,CAAE,CAAExG,CAAM,CAAE,CACpB4T,GAAM,GAAI,CAAA,EAAO,KAAK,EAAG,CAAE,sBAAuB,yBAA0B,GAC5E,IAAI,CAACmI,WAAW,CAACvV,EAAIxG,EACzB,CACAgc,SAAU,CACN,IAAqBrQ,EAAO3F,AAAb,IAAI,CAAgB4F,OAAO,CAAC5F,AAA5B,IAAI,CAA+B6F,QAAQ,CAAC,CACvDF,GAAQuI,GAAuBvI,EAAK7B,MAAM,GAC1C9D,AAFW,IAAI,CAER+V,WAAW,CAACpQ,EAAK7B,MAAM,CAAE,CAAA,EAAM,CAAEmS,QAAS,kBAAmB,EAE5E,CACAC,aAAc,CAEV,GAAM,CAAEC,QAAAA,CAAO,CAAEC,QAAAA,CAAO,CAAE,CAAG,KAAK,CAACF,YAAY,IAAI,CAACG,cAAc,EAIlE,OAHA,IAAI,CAACC,QAAQ,CAAGH,EAChB,IAAI,CAACI,QAAQ,CAAGH,EAET,KAAK,CAACF,aACjB,CAgBAM,iBAAiBC,CAAI,CAAEC,CAAW,CAAE,CAChC,IAAMC,EAAM5I,GAAsB0I,GAAQA,EAAO,EAAE,CAAEG,EAAM7I,GAAsB2I,GAAeA,EAAc,EAAE,CAAEG,EAAgBF,EAAIrL,MAAM,CAAC,SAAUwL,CAAI,CAAEC,CAAI,CAAElY,CAAC,EAChK,IAAMiF,EAASsK,GAAmB2I,EAAKjT,MAAM,CAAE,IAK/C,OAJ4B,KAAA,IAAjBgT,CAAI,CAAChT,EAAO,EACnBgT,CAAAA,CAAI,CAAChT,EAAO,CAAG,EAAE,AAAD,EAEpBgT,CAAI,CAAChT,EAAO,CAACP,IAAI,CAAC1E,GACXiY,CACX,EAAG,CACC,GAAI,EAAE,AACV,GAEA,IAAK,IAAMhT,KAAUpO,OAAOmP,IAAI,CAACgS,GAAgB,CAC7C,IAAMhT,EAAWgT,CAAa,CAAC/S,EAAO,CACtC,GAAI,AAAY,KAAXA,GAAmB8S,AAAwB,KAAxBA,EAAIzF,OAAO,CAACrN,GAAiB,CACjD,IAAK,IAAM4I,KAAS7I,EAChBgT,CAAa,CAAC,GAAG,CAACtT,IAAI,CAACmJ,EAE3B,QAAOmK,CAAa,CAAC/S,EAAO,AAChC,CACJ,CACA,OAAO+S,CACX,CAKAG,SAAU,CACN,IAAqBC,EAAS,IAAI,CAACR,IAAI,CAAC7D,GAAG,CAAC,SAAUxd,CAAC,EACnD,OAAOA,EAAEoL,EAAE,AACf,GAIA,OAHAR,AAHe,IAAI,CAGZ6Q,UAAU,CAAG7Q,AAHL,IAAI,CAGQwW,gBAAgB,CAAC,IAAI,CAACC,IAAI,CAAEQ,GACvDjX,AAJe,IAAI,CAIZ4F,OAAO,CAAG,CAAC,EAClB5F,AALe,IAAI,CAKZ2R,QAAQ,CAAG,EAAE,CACb3R,AANQ,IAAI,CAML4R,SAAS,CAAC,GAAI,GAAI,EAAG5R,AANpB,IAAI,CAMuB6Q,UAAU,EAAI,CAAC,EAC7D,CACAe,UAAUpR,CAAE,CAAE0J,CAAK,CAAExQ,CAAK,CAAEkB,CAAI,CAAEkJ,CAAM,CAAE,CACtC,IAAqBD,EAAW,EAAE,CAAE5D,EAAQD,AAA7B,IAAI,CAAgCwK,MAAM,CAACN,EAAM,CAC5DvT,EAAS,EAAG+V,EAEhB,IAAK,IAAM7N,KAAMjE,CAAI,CAAC4F,EAAG,EAAI,EAAE,CAE3B7J,EAASwM,KAAKC,GAAG,CAACsJ,AADlBA,CAAAA,EAAQ1M,AAJG,IAAI,CAIA4R,SAAS,CAAC5R,AAJd,IAAI,CAIiBwK,MAAM,CAAC3L,EAAE,CAAC2B,EAAE,CAAE3B,EAAGnF,EAAQ,EAAGkB,EAAM4F,EAAE,EAC5C7J,MAAM,CAAG,EAAGA,GACpCkN,EAASN,IAAI,CAACmJ,GAElB,IAAM/G,EAAO,IAAI3F,AARF,IAAI,CAQKkX,SAAS,GAAGtT,IAAI,CAACpD,EAAI0J,EAAOrG,EAAUlN,EAAQ+C,EARvD,IAAI,CAQkEoK,GACrF,IAAK,IAAM4I,KAAS7I,EAChB6I,EAAMyK,UAAU,CAAGxR,EAQvB,OANA3F,AAZe,IAAI,CAYZ4F,OAAO,CAACD,EAAKnF,EAAE,CAAC,CAAGmF,EAC1B3F,AAbe,IAAI,CAaZ2R,QAAQ,CAACpO,IAAI,CAACoC,GACjB1F,IACAA,EAAM0F,IAAI,CAAGA,EACbA,EAAK1F,KAAK,CAAGA,GAEV0F,CACX,CAMAyR,SAAU,CACN,MAAO,CAAC,CAAC,IAAI,CAACC,SAAS,CAACC,QAAQ,AACpC,CACA1T,KAAK7K,CAAK,CAAEvC,CAAO,CAAE,CACjB,IAAMwJ,EAAS,IAAI,CAAE5G,EAAqB+U,GAAoB3X,EAAQuE,aAAa,CAAEvE,EAAQwC,WAAW,EAAGue,EAAkBjK,GAAuBtN,EAAQ,aAAc,AAAC9F,IACvK,IAAM1D,EAAU0D,EAAMO,WAAW,CAE7BkT,GAAsBnX,EAAQghB,gBAAgB,GAC9C,CAAC7J,GAAsBnX,EAAQyP,mBAAmB,IAClDzP,EAAQyP,mBAAmB,CAAGzP,EAAQghB,gBAAgB,CACtD,OAAOhhB,EAAQghB,gBAAgB,EAE/B7J,GAAsBnX,EAAQuE,aAAa,GAC3C,CAAC4S,GAAsBnX,EAAQoR,gBAAgB,IAC/CpR,EAAQoR,gBAAgB,CAAGpR,EAAQuE,aAAa,CAChD,OAAOvE,EAAQuE,aAAa,EAGhC,IAAM8L,EAAayH,GAAM9X,EAAQqQ,UAAU,EAAI,CAAC,EAChDrQ,CAAAA,EAAQ4U,MAAM,EAAEqB,QAAQ,AAAC/S,IACrBmN,EAAWtD,IAAI,CAAC4C,KAAK,CAACU,EAAYyH,GAAM5U,EAAMmN,UAAU,EAAI,CAAC,GACjE,GACA,IAAI,CAACwM,oBAAoB,CAAGxM,EAAWyM,IAAI,CAAC,AAACT,GAAOA,EAAG7L,OAAO,CAClE,GACA,KAAK,CAACpD,KAAK7K,EAAOvC,GAElB,OAAOwJ,EAAO+H,OAAO,CAErB/H,EAAOyX,cAAc,CAAClU,IAAI,CAACgU,GACvBvX,EAAOxJ,OAAO,CAACyP,mBAAmB,GAClCjG,EAAOyX,cAAc,CAAClU,IAAI,CAAC+J,GAAuBtN,EAAQ,QAASA,EAAO0X,kBAAkB,GAC5F1X,EAAOyX,cAAc,CAAClU,IAAI,CAAC+J,GAAuBtN,EAAQ,cAAe,SAAUxC,CAAC,EAChF,IAAMzE,EAAQiH,EAAOjH,KAAK,AACtBA,CAAAA,EAAMC,WAAW,EAEjBD,EAAMC,WAAW,CAACkC,gBAAgB,CAAC8E,EAAOmU,UAAU,CAAC3W,GAE7D,IACAwC,EAAOyX,cAAc,CAAClU,IAAI,CAAC+J,GAAuBtN,EAAQ,SAE1D,SAAUxC,CAAC,CAAExD,CAAM,EACf,IAAMhB,EAAc,IAAI,CAACD,KAAK,CAACC,WAAW,CACtCA,GAAewE,EAAEhH,OAAO,CAACwC,WAAW,EACpCA,EAAYmF,MAAM,CAACX,EAAEhH,OAAO,CAACwC,WAAW,EAE5C,IAAI,CAAC2e,oBAAoB,CAAG,IAAI,CAACtE,oBAAoB,AACzD,IACArT,EAAOyX,cAAc,CAAClU,IAAI,CAAC+J,GAAuBtN,EAAQ,UAAW,SAAuBxC,CAAC,EACzF,IAAMzE,EAAQ,IAAI,CAACA,KAAK,AACpBA,CAAAA,EAAMC,WAAW,EAAI,CAACwE,EAAEoa,mBAAmB,GAC3C7e,EAAMC,WAAW,CAACQ,OAAO,GACzBT,EAAMC,WAAW,CAAG,KAAK,EAEjC,KAECD,EAAMC,WAAW,EAClBD,CAAAA,EAAMC,WAAW,CAAG,IAl0E8BoB,EAk0EFrB,EAAOK,EAAkB,EAE7E4G,EAAOyX,cAAc,CAAClU,IAAI,CAAC+J,GAAuBvU,EAAMC,WAAW,CAAE,KAAM,SAAUwE,CAAC,EAClF,IAAMqa,EAAiB,IAAI,CAACne,KAAK,CAAG8D,EAAEK,QAAQ,CAC9C,IAAK,IAAIgB,EAAI,EAAGA,EAAIgZ,EAAgBhZ,IAChCmB,EAAOgW,OAAO,EAEtB,GACJ,CAKA0B,mBAAmBxd,CAAK,CAAE,CACtB,IAAqB+F,EAAQ/F,EAAM+F,KAAK,CAAEyV,EAAUzV,GAAOyV,QAEvDxH,GAAuBwH,KACvBzV,EAAMjB,QAAQ,CAAC,IACfgB,AAJW,IAAI,CAIR+V,WAAW,CAACL,EAAS,CAAA,EAAM,CAAEO,QAAS,OAAQ,GAE7D,CAKAtU,aAAa1B,CAAK,CAAEM,CAAK,CAAE,CACvB,IAAqB4J,EAAqB8D,GAAuBjO,AAAlD,IAAI,CAAqDmK,iBAAiB,EACrFnK,AADW,IAAI,CACRmK,iBAAiB,CACxB,CAAC,EAAIzQ,EAAQuG,GAASkK,CAAiB,CAAClK,EAAM0F,IAAI,CAACjM,KAAK,CAAC,EAAI,CAAC,EAAGlD,EAAU,IAAI,CAACA,OAAO,CAAEshB,EAAevX,GAAS/J,EAAQM,MAAM,EAAIN,EAAQM,MAAM,CAACyJ,EAAM,EAAI,CAAC,EAAGmF,EAAYzF,GAAOoE,gBAAkB,GAGzMjI,EAAO,CACH,OAAU,AAAC6D,GAASA,EAAM4H,WAAW,EACjCnO,EAAMmO,WAAW,EACjBiQ,EAAajQ,WAAW,EACxBrR,EAAQqR,WAAW,CACvB,eAAgBuG,GAAmBnO,GAASA,EAAM6H,WAAW,CAAEpO,EAAMoO,WAAW,CAAEgQ,EAAahQ,WAAW,CAAEtR,EAAQsR,WAAW,EAC/H,UAAa7H,GAAO8X,iBAChBre,EAAMqe,eAAe,EACrBD,EAAaC,eAAe,EAC5BvhB,EAAQuhB,eAAe,CAC3B,KAAQ9X,GAAOhJ,OAAS,IAAI,CAACA,KAAK,AACtC,EAqBA,OAnBIyO,AAAgD,KAAhDA,EAAUyL,OAAO,CAAC,2BAClB/U,EAAK1F,IAAI,CAAG,OACZ0F,CAAI,CAAC,eAAe,CAAG,GAGlBsJ,AAA8D,KAA9DA,EAAUyL,OAAO,CAAC,yCACvB/U,CAAI,CAAC,eAAe,CAAG0b,EAAa/P,OAAO,EAAIvR,EAAQuR,OAAO,EAAI,EAClE3L,EAAK4b,MAAM,CAAG,WAGTtS,AAAkD,KAAlDA,EAAUyL,OAAO,CAAC,4BACvB/U,EAAK1F,IAAI,CAAG,OAEP6J,GAASuX,EAAa7P,UAAU,EAErC7L,CAAAA,EAAK1F,IAAI,CAAGO,GAAMmF,EAAK1F,IAAI,EACtBoU,QAAQ,CAACgN,EAAa7P,UAAU,EAChCpS,GAAG,EAAC,EAENuG,CACX,CAKA6b,kBAAkBtS,CAAI,CAAEyE,CAAW,CAAEH,CAAU,CAAEC,CAAK,CAAEK,CAAQ,CAAE,CAC9D,IAAqBxR,EAAN,IAAI,EAAkBA,MAAOuR,EAASvR,GAAOvC,SAAS8T,OACrE,GAAI3E,EAAM,CACN,IAAMuS,EAAY/K,GAAuBxH,EAAM,CAC3C2E,OAAQA,EACRJ,MAAOA,EACPC,kBAAmBnK,AALZ,IAAI,CAKemK,iBAAiB,CAC3CC,YAAaA,EACbC,iBAAkBJ,EAClBjK,OARO,IAAI,CASXuK,SAAUA,CACd,GAAItK,EAAQD,AAVD,IAAI,CAUIwK,MAAM,CAAC7E,EAAK9G,CAAC,CAAC,CAC7BoB,IACAA,EAAMhJ,KAAK,CAAGihB,EAAUjhB,KAAK,CAC7BgJ,EAAMgK,UAAU,CAAGiO,EAAUjO,UAAU,EAE3C,IAAIpL,EAAI,GAER,IAAK,IAAM6N,KAAU/G,EAAK9B,QAAQ,EAAI,EAAE,CACpC7D,AAlBO,IAAI,CAkBJiY,iBAAiB,CAACvL,EAAOwL,EAAUjhB,KAAK,CAAEihB,EAAUjO,UAAU,CAAE,EAAEpL,EAAG8G,EAAK9B,QAAQ,CAACxI,MAAM,CAExG,CACJ,CACA8c,gBAAiB,CACb,IAAMnY,EAAS,IAAI,CACb,CAAEwK,OAAAA,CAAM,CAAEoE,MAAAA,CAAK,CAAEC,MAAAA,CAAK,CAAE,CAAG7O,EAC3BlC,EAAakC,EAAOjH,KAAK,CAAC+E,UAAU,CAOpCsa,EAAiB,AAACnY,GAAWnC,EAC/B,EACCkC,EAAO2B,YAAY,CAAC1B,EAAM,CAAC,eAAe,EAAI,EACnD,IAAK,IAAMA,KAASuK,EAAQ,CACxB,GAAM,CAAE6H,YAAaE,CAAM,CAAE5O,QAAAA,CAAO,CAAE,CAAG1D,EAAM0F,IAAI,CAEnD,GAAI4M,GAAU5O,EAAS,CACnB,GAAM,CAAEhN,OAAAA,CAAM,CAAE2C,MAAAA,CAAK,CAAE5B,EAAAA,CAAC,CAAEC,EAAAA,CAAC,CAAE,CAAG4a,EAAQ8F,EAAcD,EAAenY,GAAQqY,EAAS1J,EAAM2J,QAAQ,CAAC7gB,EAAG,CAAA,GAAO8gB,EAAU5J,EAAM2J,QAAQ,CAAC7gB,EAAI4B,EAAO,CAAA,GAAOmf,EAAS5J,EAAM0J,QAAQ,CAAC5gB,EAAG,CAAA,GAAO+gB,EAAU7J,EAAM0J,QAAQ,CAAC5gB,EAAIhB,EAAQ,CAAA,GAIjOgiB,EAAKL,AAAW,IAAXA,EACDD,EAAc,EACd3K,GAAMkB,EAAM2J,QAAQ,CAAC7gB,EAAG,CAAA,GAAO2gB,EAAa,CAAA,GAAOO,EAAKJ,IAAY5J,EAAM6D,GAAG,CAC7E7D,EAAM6D,GAAG,CAAG4F,EAAc,EAC1B3K,GAAMkB,EAAM2J,QAAQ,CAAC7gB,EAAI4B,EAAO,CAAA,GAAO+e,EAAa,CAAA,GAAOQ,EAAKJ,IAAW5J,EAAM4D,GAAG,CACpF5D,EAAM4D,GAAG,CAAG4F,EAAc,EAC1B3K,GAAMmB,EAAM0J,QAAQ,CAAC5gB,EAAG,CAAA,GAAO0gB,EAAa,CAAA,GAAOS,EAAKJ,AAAY,IAAZA,EACxDL,EAAc,EACd3K,GAAMmB,EAAM0J,QAAQ,CAAC5gB,EAAIhB,EAAQ,CAAA,GAAO0hB,EAAa,CAAA,GAEnD3T,EAAY,CACdhN,EAAGyL,KAAK8L,GAAG,CAAC0J,EAAIC,GAChBjhB,EAAGwL,KAAK8L,GAAG,CAAC4J,EAAIC,GAChBxf,MAAO6J,KAAK4V,GAAG,CAACH,EAAKD,GACrBhiB,OAAQwM,KAAK4V,GAAG,CAACD,EAAKD,EAC1B,CACA5Y,CAAAA,EAAM+Y,KAAK,CAAGtU,EAAUhN,CAAC,CAAIgN,EAAUpL,KAAK,CAAG,EAC/C2G,EAAMmG,KAAK,CAAG1B,EAAU/M,CAAC,CAAI+M,EAAU/N,MAAM,CAAG,EAChDsJ,EAAMyE,SAAS,CAAGA,CACtB,MAGI,OAAOzE,EAAM+Y,KAAK,CAClB,OAAO/Y,EAAMmG,KAAK,AAE1B,CACJ,CAkCA2P,YAAYvV,CAAE,CAAExG,CAAM,CAAEif,CAAc,CAAE,CAgCpCnL,GA/Be,IAAI,CA+Ba,cA/BCD,GAAqB,CAClDyG,UAAW9T,EACX0Y,eAAgBlZ,AAFL,IAAI,CAEQ6F,QAAQ,CAC/B7L,OAAQoU,GAAmBpU,EAAQ,CAAA,GACnCgG,OAJW,IAAI,AAKnB,EAAGiZ,GAce,SAAUE,CAAI,EAC5B,IAAMnZ,EAASmZ,EAAKnZ,MAAM,AAE1BA,CAAAA,EAAOoZ,cAAc,CAAGD,EAAKD,cAAc,CAC3ClZ,EAAO6F,QAAQ,CAAGsT,EAAK7E,SAAS,CAEhCtU,EAAOrF,OAAO,CAAG,CAAA,EACbwe,EAAKnf,MAAM,EACXgG,EAAOjH,KAAK,CAACiB,MAAM,EAE3B,EAGJ,CAOAgF,SAASuB,CAAK,CAAE,CACZ,IAAI,CAAC/J,OAAO,CAAC6iB,mBAAmB,CAAG,CAAA,EACnC,KAAK,CAACra,SAASuB,EAAO,CAAA,GACtB,IAAI,CAAC/J,OAAO,CAAC6iB,mBAAmB,CAAG,CAAA,CACvC,CACApN,cAAcC,CAAI,CAAE,CAChB,IAAqB1V,EAAUwJ,AAAhB,IAAI,CAAmBxJ,OAAO,CAAE4V,EAASpM,AAAzC,IAAI,CAA4C6F,QAAQ,CAAgCwG,EAAWC,AAA3BtM,AAAxE,IAAI,CAA2E4F,OAAO,AAAwB,CAACwG,EAAO,CAAEzE,EAAmB,AAAmC,WAAnC,OAAOnR,EAAQmR,eAAe,EACpLnR,EAAQmR,eAAe,CAChB9D,EAAW,EAAE,CAAE5D,EAAQD,AAFnB,IAAI,CAEsBwK,MAAM,CAAC0B,EAAKrN,CAAC,CAAC,CAEnD6E,EAAgB,EACpB,IAAK,IAAIgJ,KAASR,EAAKrI,QAAQ,CAC3B6I,EAAQ1M,AANG,IAAI,CAMAiM,aAAa,CAACS,GAC7B7I,EAASN,IAAI,CAACmJ,GACTA,EAAMuF,MAAM,EACbvO,CAAAA,GAAiBgJ,EAAME,GAAG,AAAD,EAIjC2B,GAAW1K,EAAU,CAACxO,EAAGikB,IAAO,AAACjkB,CAAAA,EAAEkkB,SAAS,EAAI,CAAA,EAAMD,CAAAA,EAAEC,SAAS,EAAI,CAAA,GAErE,IAAI3M,EAAMwB,GAAmBnO,GAAOiU,eAAgBjU,GAAOzJ,QAAQuK,MAAO2C,GAsB1E,OArBIzD,GACAA,CAAAA,EAAMc,KAAK,CAAG6L,CAAE,EAEhB3M,GAAO6F,SAAWtP,EAAQ+R,OAAO,EAAEG,iBACnCkE,CAAAA,GAAOpW,EAAQ+R,OAAO,CAACG,eAAe,AAAD,EAErCwD,EAAKiL,UAAU,EAAElX,OAAO6F,SAAW9F,AAtBxB,IAAI,CAsB2B6F,QAAQ,GAAKqG,EAAKpI,MAAM,EAClEoI,CAAAA,EAAKvI,OAAO,CAAG,CAAA,CAAI,EAEvBkK,GAAqB3B,EAAM,CACvBrI,SAAUA,EACVH,cAAeA,EAEfuO,OAAQ,CAAE7D,CAAAA,GAAmBnO,GAAO0D,QAAS,CAAA,IAAUiJ,EAAM,CAAC,EAC9D7G,OAAQmG,EAAKvI,OAAO,EAAI,CAACD,EACzBoC,QAAS7F,GAAO6F,QAChB0G,aAAeN,EAAKxS,KAAK,CAAIiO,CAAAA,EAAkB,EAAI0E,EAAS3S,KAAK,AAAD,EAChEqN,KAAMqH,GAAmBnO,GAAO8G,KAAM,IACtCwS,UAAWnL,GAAmBnO,GAAOsZ,UAAW,CAAC3M,GACjDA,IAAKA,CACT,GACOV,CACX,CACAsN,aAAa1V,CAAM,CAAED,CAAQ,CAAE,CAC3B,OAAO,IAAI,CAACyM,aAAa,CAAC,CAAA,EAAMxM,EAAQD,EAC5C,CACA4V,WAAW3V,CAAM,CAAED,CAAQ,CAAE,CACzB,OAAO,IAAI,CAAC2M,uBAAuB,CAAC,CAAA,EAAM1M,EAAQD,EACtD,CACA6V,MAAM5V,CAAM,CAAED,CAAQ,CAAE,CACpB,OAAO,IAAI,CAAC2M,uBAAuB,CAAC,CAAA,EAAO1M,EAAQD,EACvD,CACA8V,QAAQ7V,CAAM,CAAED,CAAQ,CAAE,CACtB,OAAO,IAAI,CAACyM,aAAa,CAAC,CAAA,EAAOxM,EAAQD,EAC7C,CACAjF,UAAUsN,CAAI,CAAE,CACZ,IAAMlM,EAAS,IAAI,CAAExJ,EAAUwJ,EAAOxJ,OAAO,CAAEojB,EAAgB,CAAC1N,EAEhEY,EAASO,GAA2BrN,GAAS6F,EAAUwM,EAAawH,EAAYjN,EAC3EV,GAASY,EAAOgN,UAAU,CAAC,wCAE5B,AAAC,CAAA,IAAI,CAACtP,MAAM,EAAI,EAAE,AAAD,EAAGiC,OAAO,CAAC,AAACxM,IACrBA,EAAM6F,OAAO,EACb7F,EAAMzG,OAAO,EAErB,GAEA,KAAK,CAACoF,YAENsN,EAAOlM,EAAOgX,OAAO,IAGzBhX,EAAOkM,IAAI,CAAGA,EAAOA,GAAQlM,EAAOkM,IAAI,CACxCrG,EAAW7F,EAAO4F,OAAO,CAACkH,EAAO,CAClB,KAAXA,GAAkBjH,IAClB7F,EAAO+V,WAAW,CAAC,GAAI,CAAA,GACvBjJ,EAAS9M,EAAO6F,QAAQ,CACxBA,EAAW7F,EAAO4F,OAAO,CAACkH,EAAO,EAEhCjH,EAAS5F,KAAK,EAAE6F,SACjB9F,CAAAA,EAAOmK,iBAAiB,CAAGiD,GAA8B,CACrDjC,KAAMtF,EAASnM,KAAK,CAAG,EACvB0R,OAAQ5U,EAAQ4U,MAAM,CACtBL,GAAImB,EAAKvV,MAAM,CACfsU,SAAU,CACNtD,gBAAiB3H,EAAOxJ,OAAO,CAACmR,eAAe,CAC/Cf,aAAcpQ,EAAQoQ,YAAY,AACtC,CACJ,EAAC,EAGL0C,EAAyBL,SAAS,CAACjJ,EAAO4F,OAAO,CAAC5F,EAAO6F,QAAQ,CAAC,CAAE,AAACF,IACjE,IAAMtD,EAAIsD,EAAK7B,MAAM,CACjBuF,EAAO,CAAA,EAKX,OAJA1D,EAAKhC,OAAO,CAAG,CAAA,EACXtB,CAAAA,GAAKA,AAAM,KAANA,CAAO,GACZgH,CAAAA,EAAOrJ,EAAO4F,OAAO,CAACvD,EAAE,AAAD,EAEpBgH,CACX,GAEAC,EAAyBL,SAAS,CAACjJ,EAAO4F,OAAO,CAAC5F,EAAO6F,QAAQ,CAAC,CAAChC,QAAQ,CAAE,AAACA,IAC1E,IAAIwF,EAAO,CAAA,EACX,IAAK,IAAMqD,KAAS7I,EAChB6I,EAAM/I,OAAO,CAAG,CAAA,EACZ+I,EAAM7I,QAAQ,CAACxI,MAAM,EACrBgO,CAAAA,EAAO,AAACA,CAAAA,GAAQ,EAAE,AAAD,EAAG0Q,MAAM,CAACrN,EAAM7I,QAAQ,CAAA,EAGjD,OAAOwF,CACX,GACArJ,EAAOiM,aAAa,CAACC,GAErBlM,EAAOgT,SAAS,CAAIhT,EAAO4O,KAAK,CAAC6D,GAAG,CAAGzS,EAAO6O,KAAK,CAAC4D,GAAG,CACvDzS,EAAO4F,OAAO,CAAC,GAAG,CAACyM,WAAW,CAAGA,EAAc,CAC3C3a,EAAG,EACHC,EAAG,EACH2B,MAtmCI,IAumCJ3C,OAvmCI,GAwmCR,EACAqJ,EAAO4F,OAAO,CAAC,GAAG,CAAC2M,MAAM,CAAGsH,EAAa1L,GAAoBkE,EAAa,CACtE/Y,MAAQ+Y,EAAY/Y,KAAK,CAAG0G,EAAOgT,SAAS,CAC5CzQ,UAAY/L,CAAAA,CAAAA,AAAoC,aAApCA,EAAQiR,uBAAuB,AAAc,EACzDmF,IAAKV,EAAKU,GAAG,AACjB,GAGI,CAAA,IAAI,CAACyG,oBAAoB,EAAI,IAAI,CAACsE,oBAAoB,AAAD,GACrD,IAAI,CAAClD,cAAc,GAEvBzU,EAAO6R,sBAAsB,CAAC3F,EAAM2N,GAE/B7Z,EAAOga,SAAS,EAChBxjB,EAAQoQ,YAAY,EACrB5G,EAAOiY,iBAAiB,CAACjY,EAAOkM,IAAI,EAGpC1V,EAAQyP,mBAAmB,EAAIJ,EAASwM,WAAW,GACnDzF,EAAM/G,EAASwM,WAAW,CAC1BrS,EAAO4O,KAAK,CAACqL,WAAW,CAACrN,EAAIlV,CAAC,CAAEkV,EAAIlV,CAAC,CAAGkV,EAAItT,KAAK,CAAE,CAAA,GACnD0G,EAAO6O,KAAK,CAACoL,WAAW,CAACrN,EAAIjV,CAAC,CAAEiV,EAAIjV,CAAC,CAAGiV,EAAIjW,MAAM,CAAE,CAAA,GACpDqJ,EAAO4O,KAAK,CAACsL,QAAQ,GACrBla,EAAO6O,KAAK,CAACqL,QAAQ,IAGzBla,EAAOmY,cAAc,GACjByB,GACA5Z,EAAO4Q,iBAAiB,EAEhC,CACJ,CACApB,GAAcxU,cAAc,CAAGmT,GAAoBjB,GAAclS,cAAc,CAx7CXuL,GAy7CpEsH,GAAqB2B,GAAcxZ,SAAS,CAAE,CAC1CmkB,YAAanN,GACbxL,aAAcM,EAA2BZ,aAAa,CAACM,YAAY,CACnEL,SAAU,aACViZ,YAAa,CAAA,EACbC,mBAAoB,CAAA,EACpBC,UAAWtN,GACXuN,aAAc,YACdlZ,eAAgB,CAAC,IAAK,IAAK,QAAS,aAAa,CACjDC,cAAe,CAAC,QAAS,aAAa,CACtCO,WA3pEuDwD,EA4pEvD6R,UAAWzT,EACXlC,cAAe,CAAC,QAAS,kBAAkB,CAC3CiZ,MAAOlR,CACX,GACAxH,EAA2BzH,OAAO,CAACmV,IACnClQ,IAA4Dmb,kBAAkB,CAAC,UAAWjL,IAoB1F,GAAM,CAAEkL,QAAAA,EAAO,CAAE,CAAItkB,IAGf,CAAEoC,UAAWmiB,EAA2B,CAAExV,SAAUyV,EAA0B,CAAEhiB,KAAMiiB,EAAsB,CAAEhR,eAAgBiR,EAAgC,CAAE,CAAI1kB,GAkEtKxB,EA7DOA,EA4FRA,GAAsBA,CAAAA,EAAoB,CAAC,CAAA,GA/BxBmmB,SAAS,CA1C3B,WACI,IAAMvkB,EAAU,IAAI,CAACA,OAAO,CAAEuC,EAAQ,IAAI,CAACA,KAAK,CAAEiiB,EAAc,EAAKxkB,CAAAA,EAAQykB,YAAY,EAAI,CAAA,EAAIC,EAAYniB,EAAMmiB,SAAS,CAAG,EAAIF,EAAaG,EAAapiB,EAAMoiB,UAAU,CAAG,EAAIH,EAAaI,EAAe5kB,EAAQ6kB,MAAM,CAAEC,EAAenY,KAAK8L,GAAG,CAACiM,EAAWC,GAAaI,EAAY/kB,EAAQ+kB,SAAS,CACzSC,EAAmBC,EAAOjlB,EAAQilB,IAAI,CAAEC,EAAYllB,EAAQklB,SAAS,EAAI,EAAG7c,EAAGkC,CAC/D,CAAA,UAAhB,OAAO0a,GACPA,CAAAA,EAAO3P,WAAW2P,EAAI,EAED,UAArB,OAAOC,GACPA,CAAAA,EAAY5P,WAAW4P,EAAS,EAEpC,IAAMC,EAAY,CACdd,GAAuBO,GAAc,CAAC,EAAE,CAAE,OAC1CP,GAAuBO,GAAc,CAAC,EAAE,CAAE,OAE1CP,GAAuBY,GAAQA,EAAO,EAAI,KAAK,EAAIjlB,EAAQilB,IAAI,CAAE,QACjEZ,GAAuBa,GAAaA,EAAY,EAAI,KAAK,EAAIllB,EAAQklB,SAAS,EAAI,EAAG,MACxF,CAMD,KAHI3iB,EAAM6iB,OAAO,EAAM,IAAI,YAAa3Z,KACpC0Z,CAAAA,CAAS,CAAC,EAAE,CAAG,CAAA,EAEd9c,EAAI,EAAGA,EAAI,EAAG,EAAEA,EACjBkC,EAAQ4a,CAAS,CAAC9c,EAAE,CACpB2c,EAAoB3c,EAAI,GAAMA,AAAM,IAANA,GAAW,KAAK+M,IAAI,CAAC7K,GAKnD4a,CAAS,CAAC9c,EAAE,CAAGic,GAAiC/Z,EAAO,CAACma,EAAWC,EAAYG,EAAcK,CAAS,CAAC,EAAE,CAAC,CAAC9c,EAAE,EAAK2c,CAAAA,EAAoBR,EAAc,CAAA,EAYxJ,OATIW,CAAS,CAAC,EAAE,CAAGA,CAAS,CAAC,EAAE,EAC3BA,CAAAA,CAAS,CAAC,EAAE,CAAGA,CAAS,CAAC,EAAE,AAAD,EAG1Bf,GAA2BW,IAC3BA,AAAY,EAAZA,EAAgBI,CAAS,CAAC,EAAE,EAAIJ,EAAY,GAC5CI,CAAAA,CAAS,CAAC,EAAE,CAAGA,CAAS,CAAC,EAAE,CAAGJ,AAAY,EAAZA,CAAY,EAE9CZ,GAA4B,IAAI,CAAE,iBAAkB,CAAEgB,UAAAA,CAAU,GACzDA,CACX,EA+BA/mB,EAAkBinB,qBAAqB,CAbvC,SAA+BC,CAAK,CAAEhM,CAAG,EACrC,IAAMiM,EAAanB,GAA2BkB,GAASA,EAAQ,EAC/DE,EAAY,AAACpB,GAA2B9K,IACpCA,EAAMiM,GAEN,AAACjM,EAAMiM,EAAc,IACrBjM,EACAiM,EAAa,IACjB,MAAO,CACHD,MAAOpB,GAAWqB,CAAAA,EAFc,GAEQ,EACxCjM,IAAK4K,GAAWsB,CAAAA,EAHgB,GAGI,CACxC,CACJ,EAQyB,IAAMC,GAA4BrnB,EAkCzD,CAAEoL,OAAQ,CAAEhK,UAAW,CAAE6L,WAAYqa,EAAK,CAAE,CAAE,CAAEtc,YAAa,CAAEuc,QAAS,CAAEnmB,UAAW,CAAE6L,WAAYua,EAA0B,CAAE,CAAE,CAAE,CAAE,CAAI9c,IAEzI,CAAEmO,aAAc4O,EAA0B,CAAE9jB,OAAQ+jB,EAAoB,CAAEC,KAAAA,EAAI,CAAE,CAAInmB,GAM1F,OAAMomB,WAAsBJ,GAMxBK,iBAAiBxe,CAAK,CAAE,CACpB,IAAMhC,EAAW,IAAI,CAAC+D,MAAM,CAACjH,KAAK,CAACkD,QAAQ,CAAEyI,EAAY,IAAI,CAACgY,aAAa,CAAEnH,EAAI7Q,EAAU6Q,CAAC,CAAGgH,GAAKte,EAAMzH,OAAO,EAAEoS,UAAY,GAC3HkT,EAAQpX,EAAUoX,KAAK,CAAEhM,EAAMpL,EAAUoL,GAAG,CAC1C6M,EAAQb,EAAQ,AAAChM,CAAAA,EAAMgM,CAAI,EAAK,EAClCc,EAAYD,EAAQ,GACpBA,EAAQ,CAACxZ,KAAK0Z,EAAE,EAChBF,EAAQxZ,KAAK0Z,EAAE,CAAEC,EAqCrB,OAnCIhB,IAAU,CAAC3Y,KAAK0Z,EAAE,CAAG,GACrBR,GAA2BvM,KAASuM,GAA2BlZ,AAAU,IAAVA,KAAK0Z,EAAE,IACtEf,EAAQ,CAAC3Y,KAAK0Z,EAAE,CAAG1Z,KAAK0Z,EAAE,CAAG,IAC7B/M,EAAM,CAAC3M,KAAK0Z,EAAE,CAAG,IACjBD,EAAY,CAAA,GAGZ9M,EAAMgM,EAAQ3Y,KAAK0Z,EAAE,GACrBD,EAAY,CAAA,EACZE,EAAe,CAAA,EAGVhN,EAAMgM,EAAS,EAAI3Y,KAAK0Z,EAAE,CAAG,MAC9Bf,GAAS,IACThM,GAAO,MAGX,IAAI,CAACiN,aAAa,EAClB,CAAA,IAAI,CAACA,aAAa,CAAG,IAAI,CAACA,aAAa,CAACvjB,OAAO,EAAC,EAGpD,IAAI,CAACujB,aAAa,CAAG9gB,EAChB+gB,GAAG,CAAC,CACLC,KAAM,CAAA,EACNC,QAASJ,GAAAA,CACb,GACK1gB,IAAI,CAAC,CACN0f,MAAQc,EAAYd,EAAQhM,EAC5BA,IAAM8M,EAAY9M,EAAMgM,EACxBqB,UAAW,CAACP,EACZllB,EAAGgN,EAAUhN,CAAC,CACdC,EAAG+M,EAAU/M,CAAC,CACd4d,EAAG,AAACA,CAAAA,EAAI7Q,EAAU0Y,MAAM,AAAD,EAAK,CAChC,GACK/gB,GAAG,CAACJ,EAASohB,IAAI,EACf,IAAI,CAACN,aAAa,AAC7B,CACAjc,SAAU,CACN,MAAO,CAAA,CACX,CACJ,CACAwb,GAAqBE,GAAcxmB,SAAS,CAAE,CAC1CqO,aAAc6X,GAAMlmB,SAAS,CAACqO,YAAY,CAC1CiZ,SAAUpB,GAAMlmB,SAAS,CAACsnB,QAAQ,CAClCte,SAAUkd,GAAMlmB,SAAS,CAACgJ,QAAQ,AACtC,GAwBA,GAAM,CAAEY,YAAa,CAAEuc,QAASoB,EAA+B,CAAE,CAAE,CAAIje,IAEjE,CAAE6F,SAAUqY,EAA0B,CAAE9T,SAAU+T,EAA0B,CAAE/kB,MAAOglB,EAAuB,CAAE,CAAItnB,IAqFxH,SAASunB,GAAMxS,CAAI,CAAEJ,CAAE,EACnB,IAAMM,EAAS,EAAE,CACjB,GAAImS,GAA2BrS,IAASqS,GAA2BzS,IAAOI,GAAQJ,EAC9E,IAAK,IAAIlM,EAAIsM,EAAMtM,GAAKkM,EAAIlM,IACxBwM,EAAO9H,IAAI,CAAC1E,GAGpB,OAAOwM,CACX,CAMA,IAAMuS,GAAoB,CACtBC,oBAjFJ,SAA6B/hB,CAAY,CAAEiI,CAAM,EAC7C,IAAM1B,EAAIob,GAA2B1Z,GAAUA,EAAS,CAAC,EACrDsH,EAAQyS,EAAc,EAAGC,EAAY3S,EAAQ4S,EAAmBC,EACpE,GAAIR,GAA2B3hB,GAAe,CAY1C,IAAK,IAAMpC,KAXX2R,EAASqS,GAAwB,CAAC,EAAG5hB,GAGrCsP,EAASuS,GAFFH,GAA2Bnb,EAAE8I,IAAI,EAAI9I,EAAE8I,IAAI,CAAG,EAChDqS,GAA2Bnb,EAAE0I,EAAE,EAAI1I,EAAE0I,EAAE,CAAG,GAE/CiT,EAAoBtoB,OAAOmP,IAAI,CAACwG,GAAQ2G,MAAM,CAAC,AAACxc,GAAS4V,AAAyB,KAAzBA,EAAO+F,OAAO,CAAC,CAAC3b,IACzEuoB,EAAaE,EAAgBT,GAA2Bnb,EAAE0b,UAAU,EAChE1b,EAAE0b,UAAU,CAAG,EAKC3S,GAAQ,CACxB,IAAM5U,EAAU6U,CAAM,CAAC3R,EAAM,CAAEwkB,EAAO1nB,EAAQ2nB,SAAS,CAACD,IAAI,CAAEnd,EAAQvK,EAAQ2nB,SAAS,CAACpd,KAAK,AACzFmd,AAAS,CAAA,WAATA,EACAJ,GAAe/c,EAEVmd,AAAS,eAATA,GACL1nB,EAAQ2nB,SAAS,CAAG,CAChBD,KAAM,SACNnd,MAAO,AAACA,EAAQ,IAAOgd,CAC3B,EACAE,GAAiBznB,EAAQ2nB,SAAS,CAACpd,KAAK,EAE1B,WAATmd,GACLD,CAAAA,GAAiBld,CAAI,CAE7B,CAEA,IAAK,IAAMrH,KAAS0R,EAAQ,CACxB,IAAM5U,EAAU6U,CAAM,CAAC3R,EAAM,CAC7B,GAAIlD,AAA2B,WAA3BA,EAAQ2nB,SAAS,CAACD,IAAI,CAAe,CACrC,IAAME,EAAS5nB,EAAQ2nB,SAAS,CAACpd,KAAK,AACtCsK,CAAAA,CAAM,CAAC3R,EAAM,CAACykB,SAAS,CAAG,CACtBD,KAAM,SACNnd,MAAO,AAACqd,EAASN,EAAeG,CACpC,CACJ,CACJ,CAGA,IAAK,IAAMvkB,KAASskB,EAChB3S,CAAM,CAAC3R,EAAM,CAACykB,SAAS,CAAG,CACtBpd,MAAO,EACPmd,KAAM,QACV,CAER,CACA,OAAO7S,CACX,EA8BIgT,kBA1BJ,SAA2B,CAAE3kB,MAAAA,CAAK,CAAE/C,OAAAA,CAAM,CAAE,EAIxC,MAAO,CAAEwU,KAFIzR,EAAQ,EAAIA,EAAQ,EAElBqR,GADJrR,EAAQ/C,CACD,CACtB,EAsBIgnB,MAAAA,GACA1U,UAAWsU,GAAgCvnB,SAAS,CAACwkB,KAAK,CAACvR,SAAS,AACxE,EAiZM,CAAEyR,QAAS4D,EAAgB,CAAE,CAAIloB,IACjC,CAAEiC,SAAUkmB,EAAiB,CAAE7lB,MAAO8lB,EAAc,CAAEC,UAAAA,EAAS,CAAEnmB,QAASomB,EAAgB,CAAEnmB,OAAQomB,EAAe,CAAE,CAAIvoB,IAyB/H,SAASwoB,GAAYC,CAAI,CAAEC,CAAe,EAEtCA,EAAkBN,GAAe,CAAA,EAAM,CACnC1X,QAAS,CAAA,EACTiY,WAAY,CACRC,GAAI,GACJC,YAAa,MACbC,WAAY,QAChB,CACJ,EAAGJ,GACH,IAAMpe,EAAM,IAAI,CAACzE,QAAQ,CAACyE,GAAG,CAAEye,EAAc,IAAI,CAACtnB,IAAI,EAAI,IAAI,CAAEunB,EAAWD,EAAYC,QAAQ,CAAE,CAAEL,WAAAA,CAAU,CAAEjY,QAAAA,CAAO,CAAE,CAAGgY,EAM3H,GALAD,EAAOA,GAASO,GAAYA,EAASP,IAAI,CAErCO,GACAA,EAASC,IAAI,GAEbR,GAAQ/X,EAAS,CACjB,IAAMuY,EAAOd,GAAkBY,EAAa,kBAAmB,AAAC3hB,IAC5D,GAAIqhB,GAAQ/X,EAAS,CAEjB,IAAIwY,EAAaT,EAAKziB,IAAI,CAAC,MACtBkjB,GACDT,EAAKziB,IAAI,CAAC,KAAMkjB,EAAab,MAGjC,IAAMc,EAAc,CAGhB7nB,EAAG,EACHC,EAAG,CACP,EACI+mB,GAAiBK,EAAWS,EAAE,IAC9BD,EAAYC,EAAE,CAAGT,EAAWS,EAAE,CAC9B,OAAOT,EAAWS,EAAE,EAEpBd,GAAiBK,EAAWC,EAAE,IAC9BO,EAAYP,EAAE,CAAGD,EAAWC,EAAE,CAC9B,OAAOD,EAAWC,EAAE,EAExBG,EAAY/iB,IAAI,CAACmjB,GAEjB,IAAI,CAACnjB,IAAI,CAAC,CAAEqjB,UAAW,EAAG,GACtB,IAAI,CAACC,GAAG,EACR,CAAA,IAAI,CAACA,GAAG,CAAG,IAAI,CAACA,GAAG,CAAClmB,OAAO,EAAC,EAGhC,IAAMqK,EAAWrG,EAAEmiB,KAAK,CAACC,KAAK,CAAC,EAC/BpiB,CAAAA,EAAEmiB,KAAK,CAACtkB,MAAM,CAAG,EACjBmC,EAAEmiB,KAAK,CAAC,EAAE,CAAG,CACTE,QAAS,WACTd,WAAYJ,GAAgBI,EAAY,CACpC,cAAeA,EAAWG,UAAU,CACpCze,KAAM,CAAC,EAAEC,EAAI,CAAC,EAAE4e,EAAW,CAAC,AAChC,GACAzb,SAAAA,CACJ,CACJ,CACJ,EAEAsb,CAAAA,EAAYC,QAAQ,CAAG,CAAEP,KAAAA,EAAMQ,KAAAA,CAAK,CACxC,MAEIF,EAAY/iB,IAAI,CAAC,CAAEojB,GAAI,EAAGR,GAAI,CAAE,GAChC,OAAOG,EAAYC,QAAQ,CAO/B,OALI,IAAI,CAACU,KAAK,GAEVX,EAAYY,SAAS,CAAG,GACxB,IAAI,CAAC9jB,QAAQ,CAAC+jB,SAAS,CAACb,IAErB,IAAI,AACf,CAWA,SAASc,GAAW/lB,CAAK,EACrB,IAAMiD,EAAOjD,EAAMiD,IAAI,CAAE+iB,EAAK,IAAI,CAAC5hB,OAAO,EAAE6hB,cAAc,YAC1D,GAAID,EAAI,CACJ,IAAME,EAAU,EAAE,CAAE,CAAE9G,EAAAA,CAAC,CAAEnX,EAAAA,CAAC,CAAE,CAAG,IAAI,CAAClG,QAAQ,CAACokB,WAAW,CAAC,IAAI,CAAC/hB,OAAO,EAAGgiB,EAAYne,EAAImX,EAAGiH,EAAmB,AAAIC,OAAO,gEAEtG,KAAMC,EAAQP,EAC5BQ,SAAS,CACTC,OAAO,CAACJ,EAAkB,IAC1BK,KAAK,CAAC,sCAAuCC,EAAaJ,EAAMplB,MAAM,CAIrEylB,EAAqB,CAACC,EAAWC,KACnC,GAAM,CAAEtpB,EAAAA,CAAC,CAAEC,EAAAA,CAAC,CAAE,CAAGqpB,EAAgBC,EAAW,AAACf,CAAAA,EAAGgB,iBAAiB,CAACH,GAAa,EAAC,EAAKzC,GAAkB6C,EAAShe,KAAKie,GAAG,CAACH,GAAWI,EAASle,KAAKme,GAAG,CAACL,GACtJ,MAAO,CACH,CACIvpB,EAAI4oB,EAAYa,EAChBxpB,EAAI2oB,EAAYe,EACnB,CACD,CACI3pB,EAAI4hB,EAAI6H,EACRxpB,EAAI2hB,EAAI+H,EACX,CACJ,AACL,EACA,IAAK,IAAIxiB,EAAI,EAAG0iB,EAAY,EAAGA,EAAYV,EAAYU,IAAa,CAChE,IAA+BC,EAAUC,AAA5BhB,CAAK,CAACc,EAAU,CAAiBlmB,MAAM,CACpD,IAAK,IAAIqmB,EAAgB,EAAGA,EAAgBF,EAASE,GAAiB,EAClE,GAAI,CACA,IAAMC,EAAgB9iB,EAClB6iB,EACAH,EAAY,CAACK,EAAOC,EAAM,CAAGf,EAAmBa,EAAczB,EAAG4B,sBAAsB,CAACH,GACxFD,AAAkB,CAAA,IAAlBA,GACAtB,EAAQ7c,IAAI,CAACse,GACbzB,EAAQ7c,IAAI,CAACqe,KAGK,IAAdL,GACAnB,EAAQ2B,OAAO,CAACF,GAEhBN,IAAcV,EAAa,GAC3BT,EAAQ7c,IAAI,CAACqe,GAGzB,CACA,MAAOpkB,EAAG,CAGN,KACJ,CAEJqB,GAAK2iB,EAAU,EACf,GAAI,CACA,IAAMG,EAAe9iB,EAAI0iB,EAAWS,EAAU9B,EAAG+B,oBAAoB,CAACN,GAAe,CAACC,EAAOC,EAAM,CAAGf,EAAmBa,EAAcK,GACvI5B,EAAQ2B,OAAO,CAACF,GAChBzB,EAAQ2B,OAAO,CAACH,EACpB,CACA,MAAOpkB,EAAG,CAGN,KACJ,CACJ,CAEI4iB,EAAQ/kB,MAAM,EACd+kB,EAAQ7c,IAAI,CAAC6c,CAAO,CAAC,EAAE,CAACR,KAAK,IAEjCziB,EAAKijB,OAAO,CAAGA,CACnB,CACA,OAAOjjB,CACX,CAWA,SAAS+kB,GAAahoB,CAAK,EACvB,IAAMyW,EAAezW,EAAMyW,YAAY,CAAE1Q,EAAQ/F,EAAM+F,KAAK,CAAE6e,EAAmBnO,CAAY,CAAC1Q,EAAMwR,YAAY,CAAG,WAAW,EAC1Hd,EAAayO,QAAQ,CACrBN,GAAmB,CAACnO,EAAa3Y,OAAO,GACxC,IAAI,CAAC4mB,WAAW,CAAC3e,EAAMwc,gBAAgB,GAAG,IAAI,GAAKxc,EAAME,OAAO,CAAE2e,GAC9D7e,EAAM8c,aAAa,EACnB,CAAC+B,EAAgBhY,OAAO,EAExB7G,CAAAA,EAAM8c,aAAa,CAAI9c,EAAM8c,aAAa,CAACvjB,OAAO,EAAE,EAGhE,CA8BA,GAAM,CAAEuhB,UAAAA,EAAS,CAAEc,sBAAAA,EAAqB,CAAE,CAAGI,GAEvC,CAAEjP,KAAMmV,EAAmB,CAAE,CAAI/rB,IAEjC,CAAEsJ,OAAQ0iB,EAA2B,CAAEjG,QAASkG,EAA4B,CAAE,CAAG,AAAC/iB,IAA6DM,WAAW,CAI1J,CAAEmK,SAAUuY,EAAuB,CAAEtX,gBAAiBuX,EAA8B,CAAEtW,cAAeuW,EAA4B,CAAE3V,aAAc4V,EAA2B,CAAE,CAAG3Y,GAIjL,CAAExR,QAASoqB,EAAsB,CAAE9U,MAAO+U,EAAoB,CAAEpqB,OAAQqqB,EAAqB,CAAEpqB,UAAWqqB,EAAwB,CAAE1d,SAAU2d,EAAuB,CAAEpZ,SAAUqZ,EAAuB,CAAEtqB,SAAUuqB,EAAuB,CAAEtqB,MAAOuqB,EAAoB,CAAE3U,MAAO4U,EAAoB,CAAE,CAAI9sB,IAGjT+sB,AApCiB,CAAA,CACb9oB,QATJ,SAAiB+oB,CAAe,EAC5B7E,GAAkB6E,EAAiB,eAAgBnD,IACnD1B,GAAkB6E,EAAiB,wBAAyBlB,IAC5D,IAAMmB,EAAkBD,EAAgBptB,SAAS,AAC5CqtB,CAAAA,EAAgBzE,WAAW,EAC5ByE,CAAAA,EAAgBzE,WAAW,CAAGA,EAAU,CAEhD,CAGA,CAAA,EAkCoBvkB,OAAO,CAAEoF,KAM7B,IAAM6jB,GAAU,IAAMngB,KAAK0Z,EAAE,CAgCvB0G,GAAc,SAAqB7rB,CAAC,CAAEC,CAAC,CAAEglB,CAAK,CAAE/T,CAAQ,EAC1D,MAAO,CACHlR,EAAGA,EAAKyL,KAAKie,GAAG,CAACzE,GAAS/T,EAC1BjR,EAAGA,EAAKwL,KAAKme,GAAG,CAAC3E,GAAS/T,CAC9B,CACJ,EA6NA,SAAS4a,GAAsB7d,CAAI,CAAEnP,CAAO,EACxC,IAAM8V,EAAc9V,EAAQ8V,WAAW,CAAExI,EAAS6B,EAAK7B,MAAM,CAAE+R,EAAa/R,EAASwI,CAAW,CAACxI,EAAO,CAAG,KAAK,EAAG9D,EAASxJ,EAAQwJ,MAAM,CAAEjH,EAAQiH,EAAOjH,KAAK,CAA0BkH,EAAQuK,AAAvBxK,EAAOwK,MAAM,AAAgB,CAAC7E,EAAK9G,CAAC,CAAC,CAAmEqZ,EAAYoK,GAAwB3c,EAAM,CACzT2E,OADuNtK,EAAOxJ,OAAO,CAAC8T,MAAM,EAAIvR,GAASA,EAAMvC,OAAO,CAAC8T,MAAM,CAE7QL,WAAYjK,EAAOiK,UAAU,CAC7BC,MAAO1T,EAAQ0T,KAAK,CACpBC,kBAAmB3T,EAAQ2T,iBAAiB,CAC5CC,YAAayL,GAAcA,EAAW5e,KAAK,CAC3CoT,iBAAkBwL,GAAcA,EAAW5L,UAAU,CACrDjK,OAAQxJ,EAAQwJ,MAAM,CACtBuK,SAAU/T,EAAQ+T,QAAQ,AAC9B,GASA,OARA5E,EAAK1O,KAAK,CAAGihB,EAAUjhB,KAAK,CAC5B0O,EAAKsE,UAAU,CAAGiO,EAAUjO,UAAU,CAClChK,IACAA,EAAMhJ,KAAK,CAAG0O,EAAK1O,KAAK,CACxBgJ,EAAMgK,UAAU,CAAGtE,EAAKsE,UAAU,CAElCtE,EAAK8d,MAAM,CAAG,AAAC9d,EAAKnF,EAAE,GAAKhK,EAAQ4V,MAAM,EAAInM,EAAMwjB,MAAM,EAEtD9d,CACX,CAMA,MAAM+d,WAAuBrB,GAMzB5R,eAAexQ,CAAK,CAAEyQ,CAAS,CAAEC,CAAY,CAAE,CAC3C,GAAIA,CAAAA,EAAayO,QAAQ,GAAIzO,EAAayO,QAAQ,CAACtY,OAAO,CAM1D,OADA4J,EAAUiT,MAAM,CAAG,CAAA,EACZ,KAAK,CAAClT,eAAetK,KAAK,CAAC,IAAI,CAAEb,UAC5C,CAKAX,QAAQf,CAAI,CAAE,CACV,IAIIQ,EAJErL,EAAQ,IAAI,CAACA,KAAK,CAAEsiB,EAAS,CAC/BtiB,EAAMmiB,SAAS,CAAG,EAClBniB,EAAMoiB,UAAU,CAAG,EACtB,CAAEyI,EAAW7qB,EAAM6qB,QAAQ,CAAE9pB,EAAUf,EAAMe,OAAO,CAAEkC,EAAQ,IAAI,CAACA,KAAK,CAGrE4H,GAEAQ,EAAU,CACNyf,WAAYxI,CAAM,CAAC,EAAE,CAAGuI,EACxBE,WAAYzI,CAAM,CAAC,EAAE,CAAGvhB,EACxBiqB,OAAQ,KACRC,OAAQ,KACR/C,SAAU,GACVlZ,QAAS,GACb,EACA/L,EAAMI,IAAI,CAACgI,KAIXA,EAAU,CACNyf,WAAYD,EACZE,WAAYhqB,EACZiqB,OAAQ,EACRC,OAAQ,EACR/C,SAAU,EACVlZ,QAAS,CACb,EACA/L,EAAM2I,OAAO,CAACP,EAAS,IAAI,CAAC5N,OAAO,CAAC0N,SAAS,EAErD,CACA8Q,YAAa,CACT,IAAMhV,EAAS,IAAI,CAAEmK,EAAoBnK,EAAOmK,iBAAiB,CAAE8Z,EAAYjkB,EAAOikB,SAAS,CAAEjoB,EAAQgE,EAAOhE,KAAK,CAAEmI,EAAcnE,EAAOmE,WAAW,CAAEiI,EAASpM,EAAO6F,QAAQ,CAAEuT,EAAiBpZ,EAAOoZ,cAAc,CAAExT,EAAU5F,EAAO4F,OAAO,CAAEse,EAAmBte,CAAO,CAACwT,EAAe,CAAE+K,EAAoBD,GAAoBA,EAAiBxf,SAAS,CAAE8F,EAASxK,EAAOwK,MAAM,CAAE4Z,EAAUpkB,EAAOqkB,kBAAkB,CAAEtrB,EAAQiH,EAAOjH,KAAK,CAAEurB,EAAevrB,GAASA,EAAMvC,OAAO,EAAIuC,EAAMvC,OAAO,CAACuC,KAAK,EAAI,CAAC,EAAGmL,EAAaqgB,AAxUzf,WAAb,OAwUghBD,EAAapgB,SAAS,EACriBogB,EAAapgB,SAAS,CACfyX,EAAY3b,EAAOqb,MAAM,CAAEA,EAAS,CAC3C3jB,EAAGikB,CAAS,CAAC,EAAE,CACfhkB,EAAGgkB,CAAS,CAAC,EAAE,AACnB,EAAGyB,EAASzB,CAAS,CAAC,EAAE,CAAG,EAAG1f,EAAW+D,EAAOjH,KAAK,CAACkD,QAAQ,CAAEuoB,EAAyB,CAAC,CAAEtgB,CAAAA,GACxFC,GACAiI,IAAWgN,GACXpZ,EAAOykB,eAAe,AAAD,EACrBC,EAAeC,EAAsB,CAAA,EAAOC,EAAY,CAAA,EAc5D,IAAK,IAAM3kB,KAbPukB,IACAxkB,EAAOykB,eAAe,CAACroB,IAAI,CAAC,CAAE2L,QAAS,CAAE,GACzC2c,EAAgB,WAEZC,EAAsB,CAAA,EAClBE,AAFM7kB,EAEJykB,eAAe,EACjBI,AAHM7kB,EAGJykB,eAAe,CAAC9f,OAAO,CAAC,CACtBoD,QAAS,EACTpH,WAAY,SAChB,EAER,GAEgB6J,GAAQ,CACxB,IACIsa,EAAe7gB,EADb0B,EAAO1F,EAAM0F,IAAI,CAAEjM,EAAQyQ,CAAiB,CAACxE,EAAKjM,KAAK,CAAC,CAAEgjB,EAAiBzc,EAAMyc,aAAa,EAAI,CAAC,EAAIqI,EAAQpf,EAAKjB,SAAS,EAAI,CAAC,EAAGf,EAAU,CAAC,CAAEgC,CAAAA,EAAKhC,OAAO,EAAIgC,EAAKjB,SAAS,AAAD,CAOrLqgB,CAAAA,EAAMte,YAAY,CAAGzG,EAAOxJ,OAAO,CAACiQ,YAAY,CAE5Cqe,EADA3gB,GAAeD,EACC8gB,AA9LhC,SAAsBD,CAAK,CAAEhhB,CAAM,EAC/B,IAAM9D,EAAQ8D,EAAO9D,KAAK,CAAEmkB,EAAUrgB,EAAOqgB,OAAO,CAAEhH,EAASrZ,EAAOqZ,MAAM,CAAEhR,EAASrI,EAAOqI,MAAM,CAAEgN,EAAiBrV,EAAOqV,cAAc,CAAEsD,EAAgB3Y,EAAO2Y,aAAa,CAAEuH,EAAYlgB,EAAOkgB,SAAS,CAAEE,EAAoBpgB,EAAOogB,iBAAiB,CAAExgB,EAAUI,EAAOJ,OAAO,CACpRwH,EAAO,CAAC,EAAGJ,EAAK,CAChB+E,IAAKiV,EAAMjV,GAAG,CACdgM,MAAOiJ,EAAMjJ,KAAK,CAClBsB,OAAQ2H,EAAM3H,MAAM,CACpB7H,EAAGwP,EAAMxP,CAAC,CACV7d,EAAGqtB,EAAMrtB,CAAC,CACVC,EAAGotB,EAAMptB,CAAC,AACd,EAgDA,OA/CIgM,EAEI,CAAC1D,EAAME,OAAO,EAAIgkB,GAiBlBhZ,CAAAA,CAfIA,EADAiB,IAAWnM,EAAMO,EAAE,CACZ,CACHsb,MAAOsI,EAAQtI,KAAK,CACpBhM,IAAKsU,EAAQtU,GAAG,AACpB,EAGO,AAACqU,EAAkBrU,GAAG,EAAIiV,EAAMjJ,KAAK,CAAI,CAC5CA,MAAOsI,EAAQtU,GAAG,CAClBA,IAAKsU,EAAQtU,GAAG,AACpB,EAAI,CACAgM,MAAOsI,EAAQtI,KAAK,CACpBhM,IAAKsU,EAAQtI,KAAK,AACtB,GAGCsB,MAAM,CAAGjS,EAAKoK,CAAC,CAAG6H,CAAK,EAK5Bnd,EAAME,OAAO,GACTiZ,IAAmBnZ,EAAMO,EAAE,CAC3BuK,EAAK,CACDqS,OAAQA,EACR7H,EAAG6H,CACP,EAEK6G,GACLlZ,CAAAA,EAAK,AAACkZ,EAAUnU,GAAG,EAAI4M,EAAcZ,KAAK,CACtC,CACIsB,OAAQA,EACR7H,EAAG6H,EACHtB,MAAOsI,EAAQtU,GAAG,CAClBA,IAAKsU,EAAQtU,GAAG,AACpB,EAAI,CACJsN,OAAQA,EACR7H,EAAG6H,EACHtB,MAAOsI,EAAQtI,KAAK,CACpBhM,IAAKsU,EAAQtI,KAAK,AACtB,CAAA,GAIL,CACH3Q,KAAMA,EACNJ,GAAIA,CACR,CACJ,EAiI6Cga,EAAO,CAChC1J,OAAQA,EACRpb,MAAOA,EACPmkB,QAASA,EACThH,OAAQA,EACRhR,OAAQA,EACRgN,eAAgBA,EAChBsD,cAAeA,EACfuH,UAAWA,EACXE,kBAAmBA,EACnBxgB,QAASA,CACb,GAIgB,CACZoH,GAAIga,EACJ5Z,KAAM,CAAC,CACX,EAEJyX,GAAsB3iB,EAAO,CACzByc,cAAeqI,EACfE,WAAY,CAACF,EAAM/L,KAAK,CAAE+L,EAAM3e,KAAK,CAAC,CACtCsP,QAASwP,AAtJzB,SAAoBjlB,CAAK,CAAEmM,CAAM,CAAEE,CAAW,MAEtCoJ,EAWJ,OAVK/P,AAFQ1F,EAAM0F,IAAI,CAEbI,MAAM,GAIR2P,EAFAtJ,IAAWnM,EAAMO,EAAE,CAET6L,AADCC,CAAW,CAACF,EAAO,CACXtI,MAAM,CAGf7D,EAAMO,EAAE,EAGnBkV,CACX,EAwIoCzV,EAAOmM,EAAQxG,GACnCmB,KAAM,GAAM9G,CAAAA,EAAM8G,IAAI,EAAI9G,EAAMO,EAAE,EAAIP,EAAMiK,KAAK,AAAD,EAChD8O,MAAO+L,EAAM/L,KAAK,CAClB5S,MAAO2e,EAAM3e,KAAK,CAClBrF,MAAO4E,EAAKiH,GAAG,CACf0I,SAAU3R,EACVwhB,OAAQ,CAACxhB,CACb,GACA1D,EAAM8U,SAAS,CAAGqQ,AAzW9B,SAAsBrhB,CAAM,EAExB,IAAM9D,EAAQ8D,EAAO9D,KAAK,CAAE8kB,EAAQhC,GAAwBhf,EAAOW,SAAS,EAAIX,EAAOW,SAAS,CAAG,CAAC,EAAG,CAAEoL,IAAAA,EAAM,CAAC,CAAE9G,OAAAA,EAAS,CAAC,CAAE8S,MAAAA,EAAQ,CAAC,CAAE,CAAGiJ,EAAOxY,EAAgBwW,GAAwBhf,EAAOwI,YAAY,EAC1MxI,EAAOwI,YAAY,CAAC1F,UAAU,CAC9B,CAAC,EAKOrQ,EAAUysB,GAFPC,GAAqBH,GAAwBhf,EAAOrK,KAAK,EACpEqK,EAAOrK,KAAK,CAACmN,UAAU,CACvB,CAAC,EAAE,CAAC,EAAE,CAA+C0F,GAAevV,EAAQR,EAAQQ,KAAK,CAAGR,EAAQQ,KAAK,EAAI,CAAC,EAAG,CAAEquB,eAAAA,EAAiB,CAAC,CAAEC,eAAAA,EAAiB,CAAC,CAAE,CAAGrlB,EAC9JslB,EAAatE,EAAUuE,EAAehvB,EAAQgvB,YAAY,CAAElsB,EAAQopB,GAAuB1rB,EAAMsC,KAAK,EACtGmsB,SAASzuB,EAAMsC,KAAK,EAAI,IAAK,IAAM,KAAK,EA8H5C,MA7HI,CAACwpB,GAAwBtsB,EAAQyqB,QAAQ,IACrCuE,CAAAA,AAAiB,SAAjBA,GAA2BA,AAAiB,aAAjBA,CAA0B,IACjDhvB,EAAQwB,OAAO,EACfwtB,AAAiB,aAAjBA,GAGAA,CAAAA,EAAe,MAAK,EAEpBH,EAAiB,GAAKC,EAAiBtc,GACvCuc,EAAc,EAEVtlB,EAAM8c,aAAa,EAAIyI,AAAiB,aAAjBA,GACvBhvB,CAAAA,EAAQ4oB,QAAQ,CAAG,CACftY,QAAS,CAAA,CACb,CAAA,EAIAgJ,EAAMgM,EAAQ3Y,KAAK0Z,EAAE,EACrBvjB,CAAAA,EAAQ0P,AAAS,GAATA,CAAW,GAGlBqc,EAAiB,GAAKC,EAAiB,IAAMtc,EAC9Cwc,AAAiB,aAAjBA,EACAhvB,EAAQ4oB,QAAQ,CAAG,CACftY,QAAS,CAAA,EACTiY,WAAY,CACRC,GAAI,CACR,CACJ,EAGAwG,EAAe,YAKfvlB,EAAMyQ,SAAS,EAAE0O,UACjBoG,AAAiB,aAAjBA,GACAhvB,CAAAA,EAAQ4oB,QAAQ,CAAG,CACftY,QAAS,CAAA,CACb,CAAA,EAEJ0e,EAAe,kBAGF,SAAjBA,GAA2BA,AAAiB,aAAjBA,IACvBvlB,EAAMyQ,SAAS,EAAE0O,UACjB5oB,CAAAA,EAAQ4oB,QAAQ,CAAG,CACftY,QAAS,CAAA,CACb,CAAA,EAEJye,EAAczV,EAAM,AAACA,CAAAA,EAAMgM,CAAI,EAAK,GAEpC0J,AAAiB,aAAjBA,EACAlsB,EAAQ6J,KAAK8L,GAAG,CAACjG,AAAS,IAATA,EAAc,AAACsc,CAAAA,EAAiBD,CAAa,EAAK,GAG/D,CAAC3C,GAAuBppB,IAAU0P,GAClC1P,CAAAA,EAAQ2G,AAAqB,IAArBA,EAAM0F,IAAI,CAACjM,KAAK,CAAS,EAAIsP,EAASA,CAAK,EAGtC,kBAAjBwc,IAMIF,EADM,GAENhsB,EAAQ,EAEHyrB,EAAM/b,MAAM,GACjBhS,EAAM6d,SAAS,CAAG1R,KAAK2R,KAAK,CAACuQ,EALvB,KAK8C,EAQpD/rB,EAAQ0P,EAJWqc,CAAAA,EATb,GAUFrc,AAAW7G,CAAAA,AAVT,GAUakjB,CAAa,EACvBC,CAAAA,EAAiBD,CAAa,EADnCrc,EAEA,CAAA,IAKZ1P,EAAQ6J,KAAKC,GAAG,CAAC,AAAC9J,CAAAA,GAAS,CAAA,EAAK,EAAK9C,CAAAA,EAAQI,OAAO,EAAI,CAAA,EAAI,GAC5DqqB,EAAW,AAAEsE,CAAAA,GAAe,CAAA,EAAKjC,GAAW,IACvB,aAAjBkC,GACAvE,CAAAA,GAAY,EAAC,EAGbA,EAAW,GACXA,GAAY,IAEPA,EAAW,KAChBA,CAAAA,GAAY,GAAE,EAElBzqB,EAAQyqB,QAAQ,CAAGA,GAEnBzqB,EAAQ4oB,QAAQ,GACZnf,AAA+B,IAA/BA,EAAMyc,aAAa,CAACU,MAAM,EAC1B5mB,EAAQ4oB,QAAQ,CAACtY,OAAO,EAExBtQ,EAAQyqB,QAAQ,CAAG,EAEnBzqB,EAAQ4oB,QAAQ,CAACtY,OAAO,CAAG,CAAA,EAE3BxN,EAAQ6J,KAAKC,GAAG,CAAC,AAAyB,EAAxBnD,EAAMyc,aAAa,CAACnH,CAAC,CACnC,EAAK/e,CAAAA,EAAQI,OAAO,EAAI,CAAA,EAAI,IAE3BqJ,EAAM8U,SAAS,EAAEqK,UACtB,CAACnf,EAAM8U,SAAS,CAACqK,QAAQ,CAACtY,OAAO,EACjC0e,AAAiB,aAAjBA,GAEAhvB,CAAAA,EAAQ4oB,QAAQ,CAACtY,OAAO,CAAG,CAAA,CAAG,EAE9BtQ,EAAQ4oB,QAAQ,CAACtY,OAAO,GAExBtQ,EAAQyqB,QAAQ,CAAG,EAEnB3nB,EAAQ6J,KAAKC,GAAG,CAAC,AAACkiB,CAAAA,EAAiBD,CAAa,EAAK,EACjD,EAAK7uB,CAAAA,EAAQI,OAAO,EAAI,CAAA,EAAI,GAChCI,EAAM0uB,UAAU,CAAG,WAG3B1uB,EAAMsC,KAAK,CAAGA,EAAQ,KACf9C,CACX,EA+N2C,CAC3ByJ,MAAOA,EACPvG,MAAOA,EACP6S,aAActM,EAAMzJ,OAAO,CAC3BkO,UAAWqgB,CACf,GACI,CAACH,GAAajhB,IACdihB,EAAY,CAAA,EACZ3gB,EAAaygB,GAEjBzkB,EAAMuF,IAAI,CAAC,CACPxB,kBAAmB8gB,EAAc/Z,EAAE,CACnC3G,QAASwe,GAAsBkC,EAAc3Z,IAAI,CAAG,CAACpS,EAAM+E,UAAU,EAAIkC,EAAO2B,YAAY,CAAC1B,EAAQA,EAAMuV,QAAQ,EAAI,WACvHvR,WAAYA,EACZjI,MAAOA,EACPC,SAAUA,EACVsI,UAAW,MACXG,UAAWqgB,CACf,EACJ,CAGIP,GAA0BI,GAC1B5kB,EAAOmE,WAAW,CAAG,CAAA,EACrBnE,EAAOxJ,OAAO,CAACqQ,UAAU,CAAC8e,KAAK,CAAG,CAAA,EAClCvD,GAA4BpsB,SAAS,CAACye,cAAc,CAACve,IAAI,CAAC8J,GAC1DA,EAAOmE,WAAW,CAAG,CAAA,EAGjBwgB,GACAD,KAIJtC,GAA4BpsB,SAAS,CAACye,cAAc,CAACve,IAAI,CAAC8J,GAE9DA,EAAOoZ,cAAc,CAAGhN,CAC5B,CAKA5E,gBAAgB1D,CAAM,CAAED,CAAQ,CAAErN,CAAO,CAAE,CACvC,IAAIulB,EAAajY,EAAOgY,KAAK,CACvB6B,EAAQ7Z,EAAOgM,GAAG,CAAGiM,EAAYtZ,EAAQqB,EAAO8I,GAAG,CAAElV,EAAIoM,EAAOpM,CAAC,CAAEC,EAAImM,EAAOnM,CAAC,CAAEqR,EAAU,AAACxS,GAC9FusB,GAAwBvsB,EAAQ2nB,SAAS,GACzC2E,GAAwBtsB,EAAQ2nB,SAAS,CAACpd,KAAK,EAC/CvK,EAAQ2nB,SAAS,CAACpd,KAAK,CACvB,EAAI6kB,EAAc9hB,EAAOyR,CAAC,CAAEsQ,EAAcD,EAAc5c,EAAQiS,EAAezkB,GAAWssB,GAAwBtsB,EAAQykB,YAAY,EACtIzkB,EAAQykB,YAAY,CACpB,EACJ,MAAO,AAACpX,CAAAA,GAAY,EAAE,AAAD,EAAGyH,MAAM,CAAC,CAACqL,EAAKjK,KACjC,IAA4C0X,EAAU0B,AAAlC,EAAIrjB,EAASiK,EAAME,GAAG,CAAyB+Q,EAAmDoI,EAAiBxC,GAAY7rB,EAAGC,EAA5DokB,EAAcqI,EAAU,EAAsDnJ,GAAe1I,EAAS,CAC5L7a,EAAGgV,EAAM+W,MAAM,CAAGsC,EAAeruB,CAAC,CAAGA,EACrCC,EAAG+U,EAAM+W,MAAM,CAAGsC,EAAepuB,CAAC,CAAGA,EACrCylB,OAAQwI,EACRrQ,EAAGsQ,EACH7c,OAAQA,EACR8S,MAAOC,EACPjM,IAAKiM,EAAaqI,CACtB,EAGA,OAFAzN,EAAIpT,IAAI,CAACgP,GACTwJ,EAAaxJ,EAAOzC,GAAG,CAChB6G,CACX,EAAG,EAAE,CACT,CACAZ,YAAYvV,CAAE,CAAExG,CAAM,CAAEif,CAAc,CAAE,CAEpC,GACAjZ,AAA6B,IAA7BA,AAFe,IAAI,CAEZ4F,OAAO,CAACpF,EAAG,CAAC9G,KAAK,EACpBsG,AAEgB,IAFhBA,AAHW,IAAI,CAGR2R,QAAQ,CACVK,MAAM,CAAC,AAACrM,GAASA,AAAe,IAAfA,EAAKjM,KAAK,EAC3B2B,MAAM,CAAQ,CACnB,GAAI2E,AAA0B,KAA1BA,AANO,IAAI,CAMJoZ,cAAc,CACrB,OAEJ5Y,EAAK,EACT,CACA,KAAK,CAACuV,YAAYvV,EAAIxG,EAAQif,EAClC,CAKA+M,aAAaliB,CAAM,CAAEmiB,CAAY,CAAE9b,CAAiB,CAAE,CAClD,IAAgC3T,EAAU2T,CAAiB,CAA7CrG,EAAOpK,KAAK,CAAG,EAAqC,CAElEmK,EAAWC,EAAOD,QAAQ,CAACmO,MAAM,CAAC,SAAUhd,CAAC,EACzC,OAAOA,EAAE2O,OAAO,AACpB,GACIwO,EAAiB,EAAE,CACvBA,EAAiB,IAAI,CAAC3K,eAAe,CAACye,EAAcpiB,EAAUrN,GAC9D,IAAIqI,EAAI,GACR,IAAK,IAAM6N,KAAS7I,EAAU,CAC1B,IAAM0O,EAASJ,CAAc,CAAC,EAAEtT,EAAE,CAAE8d,EAAQpK,EAAOuJ,KAAK,CAAI,AAACvJ,CAAAA,EAAOzC,GAAG,CAAGyC,EAAOuJ,KAAK,AAAD,EAAK,EAAI9S,EAASuJ,EAAO6K,MAAM,CAAI,AAAC7K,CAAAA,EAAOgD,CAAC,CAAGhD,EAAO6K,MAAM,AAAD,EAAK,EAAIgH,EAAW7R,EAAOzC,GAAG,CAAGyC,EAAOuJ,KAAK,CAAwDT,EAAU6K,AAAnD3T,AAAkB,IAAlBA,EAAO6K,MAAM,EAAUgH,EAL3N,KAMJ,CAAE1sB,EAAG6a,EAAO7a,CAAC,CAAEC,EAAG4a,EAAO5a,CAAC,AAAC,EAC3B4rB,GAAYhR,EAAO7a,CAAC,CAAE6a,EAAO5a,CAAC,CAAEglB,EAAO3T,GAAU4D,EAAOF,EAAME,GAAG,CAChEF,EAAMhJ,aAAa,CAAGgJ,EAAME,GAAG,CAC5BF,EAAMhJ,aAAa,CACnBgJ,EAAME,GAAG,CACbF,EAAMhJ,aAAa,AAEnB,CAAA,IAAI,CAAC8G,MAAM,CAACkC,EAAM7N,CAAC,CAAC,GACpB,IAAI,CAAC2L,MAAM,CAACkC,EAAM7N,CAAC,CAAC,CAACwmB,cAAc,CAAGjB,EAAU7R,EAAO6K,MAAM,CAC7D,IAAI,CAAC5S,MAAM,CAACkC,EAAM7N,CAAC,CAAC,CAACymB,cAAc,CAAGlB,EAAU7R,EAAOgD,CAAC,EAE5D7I,EAAMhI,SAAS,CAAGue,GAAqB1Q,EAAQ,CAC3CyG,MAAOqC,EAAO3jB,CAAC,CACf0O,MAAOiV,EAAO1jB,CAAC,AACnB,GACA+U,EAAM6F,MAAM,CAAG0Q,GAAqB1Q,EAAQ,CACxC3F,IAAKA,CACT,GAEIF,EAAM7I,QAAQ,CAACxI,MAAM,EACrB,IAAI,CAAC2qB,YAAY,CAACtZ,EAAOA,EAAM6F,MAAM,CAAEpI,EAE/C,CACJ,CACAvL,WAAY,CACR,IAAqBpI,EAAUwJ,AAAhB,IAAI,CAAmBxJ,OAAO,CAAEmlB,EAAY3b,AAA5C,IAAI,CAA+Cqb,MAAM,CAAGrb,AAA5D,IAAI,CAA+D+a,SAAS,GAAIqJ,EAAUpkB,AAA1F,IAAI,CAA6FqkB,kBAAkB,CAAGxI,GAAsBrlB,EAAQulB,UAAU,CAAEvlB,EAAQwlB,QAAQ,EAAG4J,EAAcjK,CAAS,CAAC,EAAE,CAAG,EAAGkK,EAAclK,CAAS,CAAC,EAAE,CAAG,EAE/P7O,EAAS2V,GAFM,IAAI,EAGfnW,EAActM,AAHH,IAAI,CAGM4F,OAAO,CAAEuE,EAAmBkC,EAAWC,GAAeA,CAAW,CAACQ,EAAO,CAAEqZ,EAAU,CAAC,CAC/GnmB,CAJe,IAAI,CAIZikB,SAAS,CAAG5X,GAAYA,EAAS3H,SAAS,CACjD1E,AALe,IAAI,CAKZomB,cAAc,GACrBvD,GANe,IAAI,CAMc,kBAEjC,IAAM3W,EAAOlM,AARE,IAAI,CAQCkM,IAAI,CAAGlM,AARZ,IAAI,CAQegX,OAAO,GAInCqP,EAAQrD,GAAwB3W,AADtCA,CAAAA,EAAWC,AADXA,CAAAA,EAActM,AAVC,IAAI,CAUE4F,OAAO,AAAD,CACL,CAACkH,EAAO,AAAD,EACkBhJ,MAAM,EAAIuI,EAASvI,MAAM,CAAG,GAAIwiB,EAAUha,CAAW,CAAC+Z,EAAM,CAAE,CAAElb,KAAAA,CAAI,CAAEJ,GAAAA,CAAE,CAAE,CAAGwb,AA/pCnE3I,GA+pC8FS,iBAAiB,CAAChS,GACzKlC,EAAoBoY,GAA+B,CAC/CpX,KAAAA,EACAC,OAAQpL,AAfG,IAAI,CAeAxJ,OAAO,CAAC4U,MAAM,CAC7BL,GAAAA,EACAE,SAAU,CACNrE,aAAcpQ,EAAQoQ,YAAY,CAClCC,WAAYrQ,EAAQqQ,UAAU,CAC9Bc,gBAAiBnR,EAAQmR,eAAe,CACxCwW,UAAW3nB,EAAQ2nB,SAAS,CAC5BlD,aAAczkB,EAAQykB,YAAY,AACtC,CACJ,GAGA9Q,EAAoBoc,AA9qCqC3I,GA8qCVC,mBAAmB,CAAC1T,EAAmB,CAClF4T,WA5B2Q8H,EAAcD,EA6BzRza,KAAAA,EACAJ,GAAAA,CACJ,GAGAyX,GAA6BtW,EAAM,CAC/BC,OAAQqX,GACRpX,OAAQU,EACRnF,gBAAiBnR,EAAQmR,eAAe,CACxCwC,kBAAmBA,EACnBmC,YAAaA,EACb9B,OAAQxK,AAxCG,IAAI,CAwCAwK,MAAM,CACrBxK,OAzCW,IAAI,AA0CnB,GACA,IAAMuS,EAASjG,CAAW,CAAC,GAAG,CAAC5H,SAAS,CAAG,CACvCoL,IAAKsU,EAAQtU,GAAG,CAChByF,EAAGqQ,EACH9J,MAAOsI,EAAQtI,KAAK,CACpBlP,IAAKP,EAASO,GAAG,CACjBlV,EAAGikB,CAAS,CAAC,EAAE,CACfhkB,EAAGgkB,CAAS,CAAC,EAAE,AACnB,EAKA,IAAK,IAAM1b,KAJX,IAAI,CAAC+lB,YAAY,CAACM,EAAS/T,EAAQpI,GAEnCnK,AArDe,IAAI,CAqDZmK,iBAAiB,CAAGA,EAEPnK,AAvDL,IAAI,CAuDQwK,MAAM,EACzB2b,CAAO,CAAClmB,EAAMO,EAAE,CAAC,EACjBmiB,GAAqB,GAAI,CAAA,EAAO3iB,AAzDzB,IAAI,CAyD4BjH,KAAK,EAGhDotB,CAAO,CAAClmB,EAAMO,EAAE,CAAC,CAAG,CAAA,CAI5B,CACJ,CAMAkjB,GAAe1oB,cAAc,CAAGioB,GAAqBZ,GAA6BrnB,cAAc,CAvpCjE,CAgI3BqgB,OAAQ,CAAC,MAAO,MAAM,CAMtBmL,KAAM,CAAA,EACN5f,aAAc,CAAA,EAQdmB,QAAS,EAMTlB,WAAY,CACR4f,aAAc,CAAA,EACdd,MAAO,CAAA,EAoBPH,aAAc,WACdxuB,MAAO,CAEHkQ,aAAc,UAClB,CACJ,EAQA4F,OAAQ,KAAK,EASbnF,gBAAiB,CAAA,EAWjBwW,UAAW,CAQPpd,MAAO,EAkBPmd,KAAM,QACV,EAsBAjD,aAAc,EAClB,GA05BA2H,GAAsBc,GAAe1tB,SAAS,CAAE,CAC5CoL,UAAW,EAAE,CACbqT,eAAgB0N,GAChBpH,UAAWA,GACX2L,YAAa,CAAA,EAEbC,iBAAkB,CAAA,EAClBhlB,aAAcygB,GAA4BpsB,SAAS,CAAC2L,YAAY,CAChEE,WAh2CyD2a,GAi2CzDtF,UAjtCJ,cAA2BzT,EAC3B,EAitCI+W,MAruC6DoD,EAsuCjE,GACAte,IAA4Dmb,kBAAkB,CAAC,WAAYiJ,IAe3F,IAAMkD,GAAKxwB,GACXwwB,CAAAA,GAAExsB,WAAW,CAAGwsB,GAAExsB,WAAW,EA9xIiCA,EA+xI9DwsB,GAAExsB,WAAW,CAACC,OAAO,CAACusB,GAAEC,KAAK,CAAED,GAAE5rB,cAAc,EAClB,IAAM8rB,GAAiB1wB,WAE3C0wB,MAAgBC,OAAO"}