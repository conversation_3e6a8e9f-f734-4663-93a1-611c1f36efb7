import*as t from"../highcharts.js";import"./exporting.js";var e={};e.n=t=>{var a=t&&t.__esModule?()=>t.default:()=>t;return e.d(a,{a:a}),a},e.d=(t,a)=>{for(var o in a)e.o(a,o)&&!e.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:a[o]})},e.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);let a=t.default;var o=e.n(a);let{isSafari:n,win:i,win:{document:r}}=o(),l=i.URL||i.webkitURL||i;function s(t){let e=t.replace(/filename=.*;/,"").match(/data:([^;]*)(;base64)?,([A-Z+\d\/]+)/i);if(e&&e.length>3&&i.atob&&i.ArrayBuffer&&i.Uint8Array&&i.Blob&&l.createObjectURL){let t=i.atob(e[3]),a=new i.ArrayBuffer(t.length),o=new i.Uint8Array(a);for(let e=0;e<o.length;++e)o[e]=t.charCodeAt(e);return l.createObjectURL(new i.Blob([o],{type:e[1]}))}}let h={dataURLtoBlob:s,downloadURL:function(t,e){let a=i.navigator,o=r.createElement("a");if("string"!=typeof t&&!(t instanceof String)&&a.msSaveOrOpenBlob){a.msSaveOrOpenBlob(t,e);return}if(t=""+t,a.userAgent.length>1e3)throw Error("Input too long");let l=/Edge\/\d+/.test(a.userAgent);if((n&&"string"==typeof t&&0===t.indexOf("data:application/pdf")||l||t.length>2e6)&&!(t=s(t)||""))throw Error("Failed to convert to blob");if(void 0!==o.download)o.href=t,o.download=e,r.body.appendChild(o),o.click(),r.body.removeChild(o);else try{if(!i.open(t,"chart"))throw Error("Failed to open window")}catch{i.location.href=t}}},c=t.default.AST;var d=e.n(c);let p={exporting:{csv:{annotations:{itemDelimiter:"; ",join:!1},columnHeaderFormatter:null,dateFormat:"%Y-%m-%d %H:%M:%S",decimalPoint:null,itemDelimiter:null,lineDelimiter:"\n"},showTable:!1,useMultiLevelHeaders:!0,useRowspanHeaders:!0,showExportInProgress:!0},lang:{downloadCSV:"Download CSV",downloadXLS:"Download XLS",exportData:{annotationHeader:"Annotations",categoryHeader:"Category",categoryDatetimeHeader:"DateTime"},viewData:"View data table",hideData:"Hide data table",exportInProgress:"Exporting..."}},{getOptions:u,setOptions:g}=o(),{downloadURL:m}=h,{doc:f,win:x}=o(),{addEvent:b,defined:y,extend:w,find:T,fireEvent:D,isNumber:v,pick:S}=o();function L(t){let e=!!this.options.exporting?.showExportInProgress,a=x.requestAnimationFrame||setTimeout;a(()=>{e&&this.showLoading(this.options.lang.exportInProgress),a(()=>{try{t.call(this)}finally{e&&this.hideLoading()}})})}function E(){L.call(this,()=>{let t=this.getCSV(!0);m(F(t,"text/csv")||"data:text/csv,\uFEFF"+encodeURIComponent(t),this.getFilename()+".csv")})}function A(){L.call(this,()=>{let t='<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head>\x3c!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>Ark1</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--\x3e<style>td{border:none;font-family: Calibri, sans-serif;} .number{mso-number-format:"0.00";} .text{ mso-number-format:"@";}</style><meta name=ProgId content=Excel.Sheet><meta charset=UTF-8></head><body>'+this.getTable(!0)+"</body></html>";m(F(t,"application/vnd.ms-excel")||"data:application/vnd.ms-excel;base64,"+x.btoa(unescape(encodeURIComponent(t))),this.getFilename()+".xls")})}function C(t){let e="",a=this.getDataRows(),o=this.options.exporting.csv,n=S(o.decimalPoint,","!==o.itemDelimiter&&t?1.1.toLocaleString()[1]:"."),i=S(o.itemDelimiter,","===n?";":","),r=o.lineDelimiter;return a.forEach((t,o)=>{let l="",s=t.length;for(;s--;)"string"==typeof(l=t[s])&&(l=`"${l}"`),"number"==typeof l&&"."!==n&&(l=l.toString().replace(".",n)),t[s]=l;t.length=a.length?a[0].length:0,e+=t.join(i),o<a.length-1&&(e+=r)}),e}function k(t){let e,a,o=this.hasParallelCoordinates,n=this.time,i=this.options.exporting&&this.options.exporting.csv||{},r=this.xAxis,l={},s=[],h=[],c=[],d=this.options.lang.exportData,p=d.categoryHeader,u=d.categoryDatetimeHeader,g=function(e,a,o){if(i.columnHeaderFormatter){let t=i.columnHeaderFormatter(e,a,o);if(!1!==t)return t}return e?e.bindAxes?t?{columnTitle:o>1?a:e.name,topLevelColumnTitle:e.name}:e.name+(o>1?" ("+a+")":""):e.options.title&&e.options.title.text||(e.dateTime?u:p):p},m=function(t,e,a){let o={},n={};return e.forEach(function(e){let i=(t.keyToAxis&&t.keyToAxis[e]||e)+"Axis",r=v(a)?t.chart[i][a]:t[i];o[e]=r&&r.categories||[],n[e]=r&&r.dateTime}),{categoryMap:o,dateTimeValueAxisMap:n}},f=function(t,e){let a=t.pointArrayMap||["y"];return t.data.some(t=>void 0!==t.y&&t.name)&&e&&!e.categories&&"name"!==t.exportKey?["x",...a]:a},x=[],b,w,L,E=0,A,C;for(A in this.series.forEach(function(e){let a=e.options.keys,s=e.xAxis,d=a||f(e,s),p=d.length,u=!e.requireSorting&&{},b=r.indexOf(s),y=m(e,d),w,D;if(!1!==e.options.includeInDataExport&&!e.options.isInternal&&!1!==e.visible){for(T(x,function(t){return t[0]===b})||x.push([b,E]),D=0;D<p;)L=g(e,d[D],d.length),c.push(L.columnTitle||L),t&&h.push(L.topLevelColumnTitle||L),D++;w={chart:e.chart,autoIncrement:e.autoIncrement,options:e.options,pointArrayMap:e.pointArrayMap,index:e.index},e.options.data.forEach(function(t,a){let r,h,c,g={series:w};o&&(y=m(e,d,a)),e.pointClass.prototype.applyOptions.apply(g,[t]);let f=e.data[a]&&e.data[a].name;if(r=(g.x??"")+","+f,D=0,(!s||"name"===e.exportKey||!o&&s&&s.hasNames&&f)&&(r=f),u&&(u[r]&&(r+="|"+a),u[r]=!0),l[r]){let t=`${r},${l[r].pointers[e.index]}`,a=r;l[r].pointers[e.index]&&(l[t]||(l[t]=[],l[t].xValues=[],l[t].pointers=[]),r=t),l[a].pointers[e.index]+=1}else{l[r]=[],l[r].xValues=[];let t=[];for(let a=0;a<e.chart.series.length;a++)t[a]=0;l[r].pointers=t,l[r].pointers[e.index]=1}for(l[r].x=g.x,l[r].name=f,l[r].xValues[b]=g.x;D<p;)h=d[D],c=e.pointClass.prototype.getNestedProperty.apply(g,[h]),l[r][E+D]=S(y.categoryMap[h][c],y.dateTimeValueAxisMap[h]?n.dateFormat(i.dateFormat,c):null,c),D++}),E+=D}}),l)Object.hasOwnProperty.call(l,A)&&s.push(l[A]);for(w=t?[h,c]:[c],E=x.length;E--;)e=x[E][0],a=x[E][1],b=r[e],s.sort(function(t,a){return t.xValues[e]-a.xValues[e]}),C=g(b),w[0].splice(a,0,C),t&&w[1]&&w[1].splice(a,0,C),s.forEach(function(t){let e=t.name;b&&!y(e)&&(b.dateTime?(t.x instanceof Date&&(t.x=t.x.getTime()),e=n.dateFormat(i.dateFormat,t.x)):e=b.categories?S(b.names[t.x],b.categories[t.x],t.x):t.x),t.splice(a,0,e)});return D(this,"exportData",{dataRows:w=w.concat(s)}),w}function O(t){let e=t=>{if(!t.tagName||"#text"===t.tagName)return t.textContent||"";let a=t.attributes,o=`<${t.tagName}`;return a&&Object.keys(a).forEach(t=>{let e=a[t];o+=` ${t}="${e}"`}),o+=">",o+=t.textContent||"",(t.children||[]).forEach(t=>{o+=e(t)}),o+=`</${t.tagName}>`};return e(this.getTableAST(t))}function R(t){let e=0,a=[],o=this.options,n=t?1.1.toLocaleString()[1]:".",i=S(o.exporting.useMultiLevelHeaders,!0),r=this.getDataRows(i),l=i?r.shift():null,s=r.shift(),h=function(t,e){let a=t.length;if(e.length!==a)return!1;for(;a--;)if(t[a]!==e[a])return!1;return!0},c=function(t,e,a,o){let i=S(o,""),r="highcharts-text"+(e?" "+e:"");return"number"==typeof i?(i=i.toString(),","===n&&(i=i.replace(".",n)),r="highcharts-number"):o||(r="highcharts-empty"),{tagName:t,attributes:a=w({class:r},a),textContent:i}},{tableCaption:d}=o.exporting||{};!1!==d&&a.push({tagName:"caption",attributes:{class:"highcharts-table-caption"},textContent:"string"==typeof d?d:o.title?.text||o.lang.chartTitle});for(let t=0,a=r.length;t<a;++t)r[t].length>e&&(e=r[t].length);a.push(function(t,e,a){let n=[],r=0,l=a||e&&e.length,s,d=0,p;if(i&&t&&e&&!h(t,e)){let a=[];for(;r<l;++r)if((s=t[r])===t[r+1])++d;else if(d)a.push(c("th","highcharts-table-topheading",{scope:"col",colspan:d+1},s)),d=0;else{s===e[r]?o.exporting.useRowspanHeaders?(p=2,delete e[r]):(p=1,e[r]=""):p=1;let t=c("th","highcharts-table-topheading",{scope:"col"},s);p>1&&t.attributes&&(t.attributes.valign="top",t.attributes.rowspan=p),a.push(t)}n.push({tagName:"tr",children:a})}if(e){let t=[];for(r=0,l=e.length;r<l;++r)void 0!==e[r]&&t.push(c("th",null,{scope:"col"},e[r]));n.push({tagName:"tr",children:t})}return{tagName:"thead",children:n}}(l,s,Math.max(e,s.length)));let p=[];r.forEach(function(t){let a=[];for(let o=0;o<e;o++)a.push(c(o?"td":"th",null,o?{}:{scope:"row"},t[o]));p.push({tagName:"tr",children:a})}),a.push({tagName:"tbody",children:p});let u={tree:{tagName:"table",id:`highcharts-data-table-${this.index}`,children:a}};return D(this,"aftergetTableAST",u),u.tree}function N(){this.toggleDataTable(!1)}function V(t){let e=(t=S(t,!this.isDataTableVisible))&&!this.dataTableDiv;if(e&&(this.dataTableDiv=f.createElement("div"),this.dataTableDiv.className="highcharts-data-table",this.renderTo.parentNode.insertBefore(this.dataTableDiv,this.renderTo.nextSibling)),this.dataTableDiv){let a=this.dataTableDiv.style,o=a.display;a.display=t?"block":"none",t?(this.dataTableDiv.innerHTML=d().emptyHTML,new(d())([this.getTableAST()]).addToDOM(this.dataTableDiv),D(this,"afterViewData",{element:this.dataTableDiv,wasHidden:e||o!==a.display})):D(this,"afterHideData")}this.isDataTableVisible=t;let a=this.exportDivElements,o=this.options.exporting,n=o&&o.buttons&&o.buttons.contextButton.menuItems,i=this.options.lang;if(o&&o.menuItemDefinitions&&i&&i.viewData&&i.hideData&&n&&a){let t=a[n.indexOf("viewData")];t&&d().setElementHTML(t,this.isDataTableVisible?i.hideData:i.viewData)}}function B(){this.toggleDataTable(!0)}function F(t,e){let a=x.navigator,o=x.URL||x.webkitURL||x;try{if(a.msSaveOrOpenBlob&&x.MSBlobBuilder){let e=new x.MSBlobBuilder;return e.append(t),e.getBlob("image/svg+xml")}return o.createObjectURL(new x.Blob(["\uFEFF"+t],{type:e}))}catch(t){}}function I(){let t=this,e=t.dataTableDiv,a=(t,e)=>t.children[e].textContent,o=(t,e)=>(o,n)=>{let i,r;return i=a(e?o:n,t),r=a(e?n:o,t),""===i||""===r||isNaN(i)||isNaN(r)?i.toString().localeCompare(r):i-r};if(e&&t.options.exporting&&t.options.exporting.allowTableSorting){let a=e.querySelector("thead tr");a&&a.childNodes.forEach(a=>{let n=a.closest("table");a.addEventListener("click",function(){let i=[...e.querySelectorAll("tr:not(thead tr)")],r=[...a.parentNode.children];i.sort(o(r.indexOf(a),t.ascendingOrderInTable=!t.ascendingOrderInTable)).forEach(t=>{n.appendChild(t)}),r.forEach(t=>{["highcharts-sort-ascending","highcharts-sort-descending"].forEach(e=>{t.classList.contains(e)&&t.classList.remove(e)})}),a.classList.add(t.ascendingOrderInTable?"highcharts-sort-ascending":"highcharts-sort-descending")})})}}function U(){this.options&&this.options.exporting&&this.options.exporting.showTable&&!this.options.chart.forExport&&this.viewData()}function H(){this.dataTableDiv?.remove()}e.d({},{});let M=o();M.dataURLtoBlob=M.dataURLtoBlob||h.dataURLtoBlob,M.downloadURL=M.downloadURL||h.downloadURL,({compose:function(t,e){let a=t.prototype;if(!a.getCSV){let o=u().exporting;b(t,"afterViewData",I),b(t,"render",U),b(t,"destroy",H),a.downloadCSV=E,a.downloadXLS=A,a.getCSV=C,a.getDataRows=k,a.getTable=O,a.getTableAST=R,a.hideData=N,a.toggleDataTable=V,a.viewData=B,o&&(w(o.menuItemDefinitions,{downloadCSV:{textKey:"downloadCSV",onclick:function(){this.downloadCSV()}},downloadXLS:{textKey:"downloadXLS",onclick:function(){this.downloadXLS()}},viewData:{textKey:"viewData",onclick:function(){L.call(this,this.toggleDataTable)}}}),o.buttons&&o.buttons.contextButton.menuItems&&o.buttons.contextButton.menuItems.push("separator","downloadCSV","downloadXLS","viewData")),g(p);let{arearange:n,gantt:i,map:r,mapbubble:l,treemap:s,xrange:h}=e.types;n&&(n.prototype.keyToAxis={low:"y",high:"y"}),i&&(i.prototype.exportKey="name",i.prototype.keyToAxis={start:"x",end:"x"}),r&&(r.prototype.exportKey="name"),l&&(l.prototype.exportKey="name"),s&&(s.prototype.exportKey="name"),h&&(h.prototype.keyToAxis={x2:"x"})}}}).compose(M.Chart,M.Series);let P=o();export{P as default};