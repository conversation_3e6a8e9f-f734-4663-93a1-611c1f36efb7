!function(t,i){"object"==typeof exports&&"object"==typeof module?module.exports=i(require("highcharts"),require("highcharts").Templating,require("highcharts").Series,require("highcharts").AST):"function"==typeof define&&define.amd?define("highcharts/modules/stock-tools",[["highcharts/highcharts"],["highcharts/highcharts","Templating"],["highcharts/highcharts","Series"],["highcharts/highcharts","AST"]],i):"object"==typeof exports?exports["highcharts/modules/stock-tools"]=i(require("highcharts"),require("highcharts").Templating,require("highcharts").Series,require("highcharts").AST):t.Highcharts=i(t.Highcharts,t.Highcharts.Templating,t.Highcharts.Series,t.Highcharts.AST)}(this,function(t,i,e,s){return function(){"use strict";var n,o,a,r={660:function(t){t.exports=s},820:function(t){t.exports=e},944:function(i){i.exports=t},984:function(t){t.exports=i}},l={};function c(t){var i=l[t];if(void 0!==i)return i.exports;var e=l[t]={exports:{}};return r[t](e,e.exports,c),e.exports}c.n=function(t){var i=t&&t.__esModule?function(){return t.default}:function(){return t};return c.d(i,{a:i}),i},c.d=function(t,i){for(var e in i)c.o(i,e)&&!c.o(t,e)&&Object.defineProperty(t,e,{enumerable:!0,get:i[e]})},c.o=function(t,i){return Object.prototype.hasOwnProperty.call(t,i)};var h={};c.d(h,{default:function(){return t3}});var p=c(944),d=c.n(p);(n=a||(a={})).compose=function(t){return t.navigation||(t.navigation=new o(t)),t},n.Additions=o=function(){function t(t){this.updates=[],this.chart=t}return t.prototype.addUpdate=function(t){this.chart.navigation.updates.push(t)},t.prototype.update=function(t,i){var e=this;this.updates.forEach(function(s){s.call(e.chart,t,i)})},t}();var u=a,g=c(984),v=c.n(g),y=d().defined,f=d().isNumber,m=d().pick,x={backgroundColor:"string",borderColor:"string",borderRadius:"string",color:"string",fill:"string",fontSize:"string",labels:"string",name:"string",stroke:"string",title:"string"},b=function(t){return t.filter(function(t){var i=t.axis.getExtremes(),e=i.min,s=i.max,n=m(t.axis.minPointOffset,0);return f(e)&&f(s)&&t.value>=e-n&&t.value<=s+n&&!t.axis.options.isInternal})[0]},A=function(t,i){var e=x[t],s=typeof i;return y(e)&&(s=e),({string:"text",number:"number",boolean:"checkbox"})[s]},k=d().isNumber,C=d().merge,w={lang:{navigation:{popup:{simpleShapes:"Simple shapes",lines:"Lines",circle:"Circle",ellipse:"Ellipse",rectangle:"Rectangle",label:"Label",shapeOptions:"Shape options",typeOptions:"Details",fill:"Fill",format:"Text",strokeWidth:"Line width",stroke:"Line color",title:"Title",name:"Name",labelOptions:"Label options",labels:"Labels",backgroundColor:"Background color",backgroundColors:"Background colors",borderColor:"Border color",borderRadius:"Border radius",borderWidth:"Border width",style:"Style",padding:"Padding",fontSize:"Font size",color:"Color",height:"Height",shapes:"Shape options"}}},navigation:{bindingsClassName:"highcharts-bindings-container",bindings:{circleAnnotation:{className:"highcharts-circle-annotation",start:function(t){var i,e=null===(i=this.chart.pointer)||void 0===i?void 0:i.getCoordinates(t),s=e&&b(e.xAxis),n=e&&b(e.yAxis),o=this.chart.options.navigation;if(s&&n)return this.chart.addAnnotation(C({langKey:"circle",type:"basicAnnotation",shapes:[{type:"circle",point:{x:s.value,y:n.value,xAxis:s.axis.index,yAxis:n.axis.index},r:5}]},o.annotationsOptions,o.bindings.circleAnnotation.annotationsOptions))},steps:[function(t,i){var e,s=i.options.shapes,n=s&&s[0]&&s[0].point||{};if(k(n.xAxis)&&k(n.yAxis)){var o=this.chart.inverted,a=this.chart.xAxis[n.xAxis].toPixels(n.x),r=this.chart.yAxis[n.yAxis].toPixels(n.y);e=Math.max(Math.sqrt(Math.pow(o?r-t.chartX:a-t.chartX,2)+Math.pow(o?a-t.chartY:r-t.chartY,2)),5)}i.update({shapes:[{r:e}]})}]},ellipseAnnotation:{className:"highcharts-ellipse-annotation",start:function(t){var i,e=null===(i=this.chart.pointer)||void 0===i?void 0:i.getCoordinates(t),s=e&&b(e.xAxis),n=e&&b(e.yAxis),o=this.chart.options.navigation;if(s&&n)return this.chart.addAnnotation(C({langKey:"ellipse",type:"basicAnnotation",shapes:[{type:"ellipse",xAxis:s.axis.index,yAxis:n.axis.index,points:[{x:s.value,y:n.value},{x:s.value,y:n.value}],ry:1}]},o.annotationsOptions,o.bindings.ellipseAnnotation.annotationsOptions))},steps:[function(t,i){var e=i.shapes[0],s=e.getAbsolutePosition(e.points[1]);e.translatePoint(t.chartX-s.x,t.chartY-s.y,1),e.redraw(!1)},function(t,i){var e=i.shapes[0],s=e.getAbsolutePosition(e.points[0]),n=e.getAbsolutePosition(e.points[1]),o=e.getDistanceFromLine(s,n,t.chartX,t.chartY),a=e.getYAxis(),r=Math.abs(a.toValue(0)-a.toValue(o));e.setYRadius(r),e.redraw(!1)}]},rectangleAnnotation:{className:"highcharts-rectangle-annotation",start:function(t){var i,e=null===(i=this.chart.pointer)||void 0===i?void 0:i.getCoordinates(t),s=e&&b(e.xAxis),n=e&&b(e.yAxis);if(s&&n){var o=s.value,a=n.value,r=s.axis.index,l=n.axis.index,c=this.chart.options.navigation;return this.chart.addAnnotation(C({langKey:"rectangle",type:"basicAnnotation",shapes:[{type:"path",points:[{xAxis:r,yAxis:l,x:o,y:a},{xAxis:r,yAxis:l,x:o,y:a},{xAxis:r,yAxis:l,x:o,y:a},{xAxis:r,yAxis:l,x:o,y:a},{command:"Z"}]}]},c.annotationsOptions,c.bindings.rectangleAnnotation.annotationsOptions))}},steps:[function(t,i){var e,s=i.options.shapes,n=s&&s[0]&&s[0].points||[],o=null===(e=this.chart.pointer)||void 0===e?void 0:e.getCoordinates(t),a=o&&b(o.xAxis),r=o&&b(o.yAxis);if(a&&r){var l=a.value,c=r.value;n[1].x=l,n[2].x=l,n[2].y=c,n[3].y=c,i.update({shapes:[{points:n}]})}}]},labelAnnotation:{className:"highcharts-label-annotation",start:function(t){var i,e=null===(i=this.chart.pointer)||void 0===i?void 0:i.getCoordinates(t),s=e&&b(e.xAxis),n=e&&b(e.yAxis),o=this.chart.options.navigation;if(s&&n)return this.chart.addAnnotation(C({langKey:"label",type:"basicAnnotation",labelOptions:{format:"{y:.2f}",overflow:"none",crop:!0},labels:[{point:{xAxis:s.axis.index,yAxis:n.axis.index,x:s.value,y:n.value}}]},o.annotationsOptions,o.bindings.labelAnnotation.annotationsOptions))}}},events:{},annotationsOptions:{animation:{defer:0}}}},O=d().setOptions,N=v().format,L=d().composed,T=d().doc,B=d().win,E=d().addEvent,S=d().attr,I=d().defined,z=d().fireEvent,W=d().isArray,P=d().isFunction,Y=d().isNumber,H=d().isObject,R=d().merge,M=d().objectEach,X=d().pick,U=d().pushUnique;function D(){this.chart.navigationBindings&&this.chart.navigationBindings.deselectAnnotation()}function F(){this.navigationBindings&&this.navigationBindings.destroy()}function K(){var t=this.options;t&&t.navigation&&t.navigation.bindings&&(this.navigationBindings=new G(this,t.navigation),this.navigationBindings.initEvents(),this.navigationBindings.initUpdate())}function V(){var t=this.navigationBindings,i="highcharts-disabled-btn";if(this&&t){var e=!1;if(this.series.forEach(function(t){!t.options.isInternal&&t.visible&&(e=!0)}),this.navigationBindings&&this.navigationBindings.container&&this.navigationBindings.container[0]){var s=this.navigationBindings.container[0];M(t.boundClassNames,function(t,n){var o=s.querySelectorAll("."+n);if(o)for(var a=0;a<o.length;a++){var r=o[a],l=r.className;"normal"===t.noDataState?-1!==l.indexOf(i)&&r.classList.remove(i):e?-1!==l.indexOf(i)&&r.classList.remove(i):-1===l.indexOf(i)&&(r.className+=" "+i)}})}}}function q(){this.deselectAnnotation()}function Z(){this.selectedButtonElement=null}function j(t){var i,e,s=t.prototype.defaultOptions.events&&t.prototype.defaultOptions.events.click;function n(t){var i=this,e=i.chart.navigationBindings,n=e.activeAnnotation;s&&s.call(i,t),n!==i?(e.deselectAnnotation(),e.activeAnnotation=i,i.setControlPointsVisibility(!0),z(e,"showPopup",{annotation:i,formType:"annotation-toolbar",options:e.annotationToFields(i),onSubmit:function(t){if("remove"===t.actionType)e.activeAnnotation=!1,e.chart.removeAnnotation(i);else{var s={};e.fieldsToOptions(t.fields,s),e.deselectAnnotation();var n=s.typeOptions;"measure"===i.options.type&&(n.crosshairY.enabled=0!==n.crosshairY.strokeWidth,n.crosshairX.enabled=0!==n.crosshairX.strokeWidth),i.update(s)}}})):z(e,"closePopup"),t.activeAnnotation=!0}R(!0,t.prototype.defaultOptions.events,{click:n,touchstart:function(t){i=t.touches[0].clientX,e=t.touches[0].clientY},touchend:function(t){i&&Math.sqrt(Math.pow(i-t.changedTouches[0].clientX,2)+Math.pow(e-t.changedTouches[0].clientY,2))>=4||n.call(this,t)}})}var G=function(){function t(t,i){this.boundClassNames=void 0,this.chart=t,this.options=i,this.eventsToUnbind=[],this.container=this.chart.container.getElementsByClassName(this.options.bindingsClassName||""),this.container.length||(this.container=T.getElementsByClassName(this.options.bindingsClassName||""))}return t.compose=function(i,e){U(L,"NavigationBindings")&&(E(i,"remove",D),j(i),M(i.types,function(t){j(t)}),E(e,"destroy",F),E(e,"load",K),E(e,"render",V),E(t,"closePopup",q),E(t,"deselectButton",Z),O(w))},t.prototype.getCoords=function(t){var i,e=null===(i=this.chart.pointer)||void 0===i?void 0:i.getCoordinates(t);return[e&&b(e.xAxis),e&&b(e.yAxis)]},t.prototype.initEvents=function(){var t=this,i=t.chart,e=t.container,s=t.options;t.boundClassNames={},M(s.bindings||{},function(i){t.boundClassNames[i.className]=i}),[].forEach.call(e,function(i){t.eventsToUnbind.push(E(i,"click",function(e){var s=t.getButtonEvents(i,e);s&&!s.button.classList.contains("highcharts-disabled-btn")&&t.bindingsButtonClick(s.button,s.events,e)}))}),M(s.events||{},function(i,e){P(i)&&t.eventsToUnbind.push(E(t,e,i,{passive:!1}))}),t.eventsToUnbind.push(E(i.container,"click",function(e){!i.cancelClick&&i.isInsidePlot(e.chartX-i.plotLeft,e.chartY-i.plotTop,{visiblePlotOnly:!0})&&t.bindingsChartClick(this,e)})),t.eventsToUnbind.push(E(i.container,d().isTouchDevice?"touchmove":"mousemove",function(i){t.bindingsContainerMouseMove(this,i)},d().isTouchDevice?{passive:!1}:void 0))},t.prototype.initUpdate=function(){var t=this;u.compose(this.chart).navigation.addUpdate(function(i){t.update(i)})},t.prototype.bindingsButtonClick=function(t,i,e){var s=this.chart,n=s.renderer.boxWrapper,o=!0;this.selectedButtonElement&&(this.selectedButtonElement.classList===t.classList&&(o=!1),z(this,"deselectButton",{button:this.selectedButtonElement}),this.nextEvent&&(this.currentUserDetails&&"annotations"===this.currentUserDetails.coll&&s.removeAnnotation(this.currentUserDetails),this.mouseMoveEvent=this.nextEvent=!1)),o?(this.selectedButton=i,this.selectedButtonElement=t,z(this,"selectButton",{button:t}),i.init&&i.init.call(this,t,e),(i.start||i.steps)&&s.renderer.boxWrapper.addClass("highcharts-draw-mode")):(s.stockTools&&t.classList.remove("highcharts-active"),n.removeClass("highcharts-draw-mode"),this.nextEvent=!1,this.mouseMoveEvent=!1,this.selectedButton=null)},t.prototype.bindingsChartClick=function(t,i){t=this.chart;var e=this.activeAnnotation,s=this.selectedButton,n=t.renderer.boxWrapper;e&&(e.cancelClick||i.activeAnnotation||!i.target.parentNode||function(t,i){var e=B.Element.prototype,s=e.matches||e.msMatchesSelector||e.webkitMatchesSelector,n=null;if(e.closest)n=e.closest.call(t,i);else do{if(s.call(t,i))return t;t=t.parentElement||t.parentNode}while(null!==t&&1===t.nodeType);return n}(i.target,".highcharts-popup")?e.cancelClick&&setTimeout(function(){e.cancelClick=!1},0):z(this,"closePopup")),s&&s.start&&(this.nextEvent?(this.nextEvent(i,this.currentUserDetails),this.steps&&(this.stepIndex++,s.steps[this.stepIndex]?this.mouseMoveEvent=this.nextEvent=s.steps[this.stepIndex]:(z(this,"deselectButton",{button:this.selectedButtonElement}),n.removeClass("highcharts-draw-mode"),s.end&&s.end.call(this,i,this.currentUserDetails),this.nextEvent=!1,this.mouseMoveEvent=!1,this.selectedButton=null))):(this.currentUserDetails=s.start.call(this,i),this.currentUserDetails&&s.steps?(this.stepIndex=0,this.steps=!0,this.mouseMoveEvent=this.nextEvent=s.steps[this.stepIndex]):(z(this,"deselectButton",{button:this.selectedButtonElement}),n.removeClass("highcharts-draw-mode"),this.steps=!1,this.selectedButton=null,s.end&&s.end.call(this,i,this.currentUserDetails))))},t.prototype.bindingsContainerMouseMove=function(t,i){this.mouseMoveEvent&&this.mouseMoveEvent(i,this.currentUserDetails)},t.prototype.fieldsToOptions=function(t,i){return M(t,function(t,e){var s=parseFloat(t),n=e.split("."),o=n.length-1;if(!Y(s)||t.match(/px|em/g)||e.match(/format/g)||(t=s),"undefined"!==t){var a=i;n.forEach(function(i,e){if("__proto__"!==i&&"constructor"!==i){var s=X(n[e+1],"");o===e?a[i]=t:(a[i]||(a[i]=s.match(/\d/g)?[]:{}),a=a[i])}})}}),i},t.prototype.deselectAnnotation=function(){this.activeAnnotation&&(this.activeAnnotation.setControlPointsVisibility(!1),this.activeAnnotation=!1)},t.prototype.annotationToFields=function(i){var e=i.options,s=t.annotationsEditable,n=s.nestedOptions,o=X(e.type,e.shapes&&e.shapes[0]&&e.shapes[0].type,e.labels&&e.labels[0]&&e.labels[0].type,"label"),a=t.annotationsNonEditable[e.langKey]||[],r={langKey:e.langKey,type:o};function l(t,e,s,o,r){var c;s&&I(t)&&-1===a.indexOf(e)&&((s.indexOf&&s.indexOf(e))>=0||s[e]||!0===s)&&(W(t)?(o[e]=[],t.forEach(function(t,i){H(t)?(o[e][i]={},M(t,function(t,s){l(t,s,n[e],o[e][i],e)})):l(t,0,n[e],o[e],e)})):H(t)?(c={},W(o)?(o.push(c),c[e]={},c=c[e]):o[e]=c,M(t,function(t,i){l(t,i,0===e?s:n[e],c,e)})):"format"===e?o[e]=[N(t,i.labels[0].points[0]).toString(),"text"]:W(o)?o.push([t,A(r,t)]):o[e]=[t,A(e,t)])}return M(e,function(t,i){"typeOptions"===i?(r[i]={},M(e[i],function(t,e){l(t,e,n,r[i],e)})):l(t,i,s[o],r,i)}),r},t.prototype.getClickedClassNames=function(t,i){for(var e,s=i.target,n=[];s&&s.tagName&&((e=S(s,"class"))&&(n=n.concat(e.split(" ").map(function(t){return[t,s]}))),(s=s.parentNode)!==t););return n},t.prototype.getButtonEvents=function(t,i){var e,s=this;return this.getClickedClassNames(t,i).forEach(function(t){s.boundClassNames[t[0]]&&!e&&(e={events:s.boundClassNames[t[0]],button:t[1]})}),e},t.prototype.update=function(t){this.options=R(!0,this.options,t),this.removeEvents(),this.initEvents()},t.prototype.removeEvents=function(){this.eventsToUnbind.forEach(function(t){return t()})},t.prototype.destroy=function(){this.removeEvents()},t.annotationsEditable={nestedOptions:{labelOptions:["style","format","backgroundColor"],labels:["style"],label:["style"],style:["fontSize","color"],background:["fill","strokeWidth","stroke"],innerBackground:["fill","strokeWidth","stroke"],outerBackground:["fill","strokeWidth","stroke"],shapeOptions:["fill","strokeWidth","stroke"],shapes:["fill","strokeWidth","stroke"],line:["strokeWidth","stroke"],backgroundColors:[!0],connector:["fill","strokeWidth","stroke"],crosshairX:["strokeWidth","stroke"],crosshairY:["strokeWidth","stroke"]},circle:["shapes"],ellipse:["shapes"],verticalLine:[],label:["labelOptions"],measure:["background","crosshairY","crosshairX"],fibonacci:[],tunnel:["background","line","height"],pitchfork:["innerBackground","outerBackground"],rect:["shapes"],crookedLine:[],basicAnnotation:["shapes","labelOptions"]},t.annotationsNonEditable={rectangle:["crosshairX","crosshairY","labelOptions"],ellipse:["labelOptions"],circle:["labelOptions"]},t}(),_=c(820),J=c.n(_),Q=d().getOptions,$=d().defined,tt=d().fireEvent,ti=d().isNumber,te=d().uniqueKey,ts=["apo","ad","aroon","aroonoscillator","atr","ao","cci","chaikin","cmf","cmo","disparityindex","dmi","dpo","linearRegressionAngle","linearRegressionIntercept","linearRegressionSlope","klinger","macd","mfi","momentum","natr","obv","ppo","roc","rsi","slowstochastic","stochastic","trix","williamsr"],tn=["ad","cmf","klinger","mfi","obv","vbp","vwap"];function to(t,i){var e,s,n,o,a=null===(e=i.pointer)||void 0===e?void 0:e.getCoordinates(t),r=Number.MAX_VALUE;if(i.navigationBindings&&a&&(s=b(a.xAxis),n=b(a.yAxis)),s&&n){var l=s.value,c=n.value;if(n.axis.series.forEach(function(i){if(i.points){var e=i.searchPoint(t,!0);e&&r>Math.abs(e.x-l)&&(r=Math.abs(e.x-l),o=e)}}),o&&o.x&&o.y)return{x:o.x,y:o.y,below:c<o.y,series:o.series,xAxis:o.series.xAxis.index||0,yAxis:o.series.yAxis.index||0}}}var ta={indicatorsWithAxes:ts,indicatorsWithVolume:tn,addFlagFromForm:function(t){return function(i){var e=this,s=e.chart,n=s.stockTools,o=to(i,s);if(o){var a={x:o.x,y:o.y},r={type:"flags",onSeries:o.series.id,shape:t,data:[a],xAxis:o.xAxis,yAxis:o.yAxis,point:{events:{click:function(){var t=this,i=t.options;tt(e,"showPopup",{point:t,formType:"annotation-toolbar",options:{langKey:"flags",type:"flags",title:[i.title,A("title",i.title)],name:[i.name,A("name",i.name)]},onSubmit:function(i){"remove"===i.actionType?t.remove():t.update(e.fieldsToOptions(i.fields,{}))}})}}}};n&&n.guiEnabled||s.addSeries(r),tt(e,"showPopup",{formType:"flag",options:{langKey:"flags",type:"flags",title:["A",A("label","A")],name:["Flag A",A("label","Flag A")]},onSubmit:function(t){e.fieldsToOptions(t.fields,r.data[0]),s.addSeries(r)}})}}},attractToPoint:to,getAssignedAxis:b,isNotNavigatorYAxis:function(t){return"highcharts-navigator-yaxis"!==t.userOptions.className},isPriceIndicatorEnabled:function(t){return t.some(function(t){return t.lastVisiblePrice||t.lastPrice})},manageIndicators:function(t){var i,e,s,n,o=this.chart,a={linkedTo:t.linkedTo,type:t.type};if("edit"===t.actionType)this.fieldsToOptions(t.fields,a),(n=o.get(t.seriesId))&&n.update(a,!1);else if("remove"===t.actionType){if((n=o.get(t.seriesId))&&(i=n.yAxis,n.linkedSeries&&n.linkedSeries.forEach(function(t){t.remove(!1)}),n.remove(!1),ts.indexOf(n.type)>=0)){var r={height:i.options.height,top:i.options.top};i.remove(!1),this.resizeYAxes(r)}}else a.id=te(),this.fieldsToOptions(t.fields,a),e=o.get(a.linkedTo),s=Q().plotOptions,void 0!==e&&e instanceof J()&&"sum"===e.getDGApproximation()&&!$(s&&s[a.type]&&s.dataGrouping&&s.dataGrouping.approximation)&&(a.dataGrouping={approximation:"sum"}),ts.indexOf(t.type)>=0?(a.yAxis=(i=o.addAxis({id:te(),offset:0,opposite:!0,title:{text:""},tickPixelInterval:40,showLastLabel:!1,labels:{align:"left",y:-2}},!1,!1)).options.id,this.resizeYAxes()):a.yAxis=o.get(t.linkedTo).options.yAxis,tn.indexOf(t.type)>=0&&(a.params.volumeSeriesID=o.series.filter(function(t){return"column"===t.options.type})[0].options.id),o.addSeries(a,!1);tt(this,"deselectButton",{button:this.selectedButtonElement}),o.redraw()},shallowArraysEqual:function(t,i){if(!$(t)||!$(i)||t.length!==i.length)return!1;for(var e=0;e<t.length;e++)if(t[e]!==i[e])return!1;return!0},updateHeight:function(t,i){var e=i.options.typeOptions,s=ti(e.yAxis)&&this.chart.yAxis[e.yAxis];s&&e.points&&i.update({typeOptions:{height:s.toValue(t[s.horiz?"chartX":"chartY"])-(e.points[1].y||0)}})},updateNthPoint:function(t){return function(i,e){var s=e.options.typeOptions,n=ti(s.xAxis)&&this.chart.xAxis[s.xAxis],o=ti(s.yAxis)&&this.chart.yAxis[s.yAxis];n&&o&&(s.points.forEach(function(e,s){s>=t&&(e.x=n.toValue(i[n.horiz?"chartX":"chartY"]),e.y=o.toValue(i[o.horiz?"chartX":"chartY"]))}),e.update({typeOptions:{points:s.points}}))}},updateRectSize:function(t,i){var e=i.chart,s=i.options.typeOptions,n=ti(s.xAxis)&&e.xAxis[s.xAxis],o=ti(s.yAxis)&&e.yAxis[s.yAxis];if(n&&o){var a=n.toValue(t[n.horiz?"chartX":"chartY"]),r=o.toValue(t[o.horiz?"chartX":"chartY"]),l=a-s.point.x,c=s.point.y-r;i.update({typeOptions:{background:{width:e.inverted?c:l,height:e.inverted?l:c}}})}}},tr=ta.addFlagFromForm,tl=ta.attractToPoint,tc=ta.isNotNavigatorYAxis,th=ta.isPriceIndicatorEnabled,tp=ta.manageIndicators,td=ta.updateHeight,tu=ta.updateNthPoint,tg=ta.updateRectSize,tv=d().fireEvent,ty=d().merge,tf={segment:{className:"highcharts-segment",start:function(t){var i=this.getCoords(t),e=i[0],s=i[1];if(e&&s){var n=this.chart.options.navigation,o=ty({langKey:"segment",type:"crookedLine",typeOptions:{xAxis:e.axis.index,yAxis:s.axis.index,points:[{x:e.value,y:s.value},{x:e.value,y:s.value}]}},n.annotationsOptions,n.bindings.segment.annotationsOptions);return this.chart.addAnnotation(o)}},steps:[tu(1)]},arrowSegment:{className:"highcharts-arrow-segment",start:function(t){var i=this.getCoords(t),e=i[0],s=i[1];if(e&&s){var n=this.chart.options.navigation,o=ty({langKey:"arrowSegment",type:"crookedLine",typeOptions:{line:{markerEnd:"arrow"},xAxis:e.axis.index,yAxis:s.axis.index,points:[{x:e.value,y:s.value},{x:e.value,y:s.value}]}},n.annotationsOptions,n.bindings.arrowSegment.annotationsOptions);return this.chart.addAnnotation(o)}},steps:[tu(1)]},ray:{className:"highcharts-ray",start:function(t){var i=this.getCoords(t),e=i[0],s=i[1];if(e&&s){var n=this.chart.options.navigation,o=ty({langKey:"ray",type:"infinityLine",typeOptions:{type:"ray",xAxis:e.axis.index,yAxis:s.axis.index,points:[{x:e.value,y:s.value},{x:e.value,y:s.value}]}},n.annotationsOptions,n.bindings.ray.annotationsOptions);return this.chart.addAnnotation(o)}},steps:[tu(1)]},arrowRay:{className:"highcharts-arrow-ray",start:function(t){var i=this.getCoords(t),e=i[0],s=i[1];if(e&&s){var n=this.chart.options.navigation,o=ty({langKey:"arrowRay",type:"infinityLine",typeOptions:{type:"ray",line:{markerEnd:"arrow"},xAxis:e.axis.index,yAxis:s.axis.index,points:[{x:e.value,y:s.value},{x:e.value,y:s.value}]}},n.annotationsOptions,n.bindings.arrowRay.annotationsOptions);return this.chart.addAnnotation(o)}},steps:[tu(1)]},infinityLine:{className:"highcharts-infinity-line",start:function(t){var i=this.getCoords(t),e=i[0],s=i[1];if(e&&s){var n=this.chart.options.navigation,o=ty({langKey:"infinityLine",type:"infinityLine",typeOptions:{type:"line",xAxis:e.axis.index,yAxis:s.axis.index,points:[{x:e.value,y:s.value},{x:e.value,y:s.value}]}},n.annotationsOptions,n.bindings.infinityLine.annotationsOptions);return this.chart.addAnnotation(o)}},steps:[tu(1)]},arrowInfinityLine:{className:"highcharts-arrow-infinity-line",start:function(t){var i=this.getCoords(t),e=i[0],s=i[1];if(e&&s){var n=this.chart.options.navigation,o=ty({langKey:"arrowInfinityLine",type:"infinityLine",typeOptions:{type:"line",line:{markerEnd:"arrow"},xAxis:e.axis.index,yAxis:s.axis.index,points:[{x:e.value,y:s.value},{x:e.value,y:s.value}]}},n.annotationsOptions,n.bindings.arrowInfinityLine.annotationsOptions);return this.chart.addAnnotation(o)}},steps:[tu(1)]},horizontalLine:{className:"highcharts-horizontal-line",start:function(t){var i=this.getCoords(t),e=i[0],s=i[1];if(e&&s){var n=this.chart.options.navigation,o=ty({langKey:"horizontalLine",type:"infinityLine",draggable:"y",typeOptions:{type:"horizontalLine",xAxis:e.axis.index,yAxis:s.axis.index,points:[{x:e.value,y:s.value}]}},n.annotationsOptions,n.bindings.horizontalLine.annotationsOptions);this.chart.addAnnotation(o)}}},verticalLine:{className:"highcharts-vertical-line",start:function(t){var i=this.getCoords(t),e=i[0],s=i[1];if(e&&s){var n=this.chart.options.navigation,o=ty({langKey:"verticalLine",type:"infinityLine",draggable:"x",typeOptions:{type:"verticalLine",xAxis:e.axis.index,yAxis:s.axis.index,points:[{x:e.value,y:s.value}]}},n.annotationsOptions,n.bindings.verticalLine.annotationsOptions);this.chart.addAnnotation(o)}}},crooked3:{className:"highcharts-crooked3",start:function(t){var i=this.getCoords(t),e=i[0],s=i[1];if(e&&s){var n=e.value,o=s.value,a=this.chart.options.navigation,r=ty({langKey:"crooked3",type:"crookedLine",typeOptions:{xAxis:e.axis.index,yAxis:s.axis.index,points:[{x:n,y:o},{x:n,y:o},{x:n,y:o}]}},a.annotationsOptions,a.bindings.crooked3.annotationsOptions);return this.chart.addAnnotation(r)}},steps:[tu(1),tu(2)]},crooked5:{className:"highcharts-crooked5",start:function(t){var i=this.getCoords(t),e=i[0],s=i[1];if(e&&s){var n=e.value,o=s.value,a=this.chart.options.navigation,r=ty({langKey:"crooked5",type:"crookedLine",typeOptions:{xAxis:e.axis.index,yAxis:s.axis.index,points:[{x:n,y:o},{x:n,y:o},{x:n,y:o},{x:n,y:o},{x:n,y:o}]}},a.annotationsOptions,a.bindings.crooked5.annotationsOptions);return this.chart.addAnnotation(r)}},steps:[tu(1),tu(2),tu(3),tu(4)]},elliott3:{className:"highcharts-elliott3",start:function(t){var i=this.getCoords(t),e=i[0],s=i[1];if(e&&s){var n=e.value,o=s.value,a=this.chart.options.navigation,r=ty({langKey:"elliott3",type:"elliottWave",typeOptions:{xAxis:e.axis.index,yAxis:s.axis.index,points:[{x:n,y:o},{x:n,y:o},{x:n,y:o},{x:n,y:o}]},labelOptions:{style:{color:"#666666"}}},a.annotationsOptions,a.bindings.elliott3.annotationsOptions);return this.chart.addAnnotation(r)}},steps:[tu(1),tu(2),tu(3)]},elliott5:{className:"highcharts-elliott5",start:function(t){var i=this.getCoords(t),e=i[0],s=i[1];if(e&&s){var n=e.value,o=s.value,a=this.chart.options.navigation,r=ty({langKey:"elliott5",type:"elliottWave",typeOptions:{xAxis:e.axis.index,yAxis:s.axis.index,points:[{x:n,y:o},{x:n,y:o},{x:n,y:o},{x:n,y:o},{x:n,y:o},{x:n,y:o}]},labelOptions:{style:{color:"#666666"}}},a.annotationsOptions,a.bindings.elliott5.annotationsOptions);return this.chart.addAnnotation(r)}},steps:[tu(1),tu(2),tu(3),tu(4),tu(5)]},measureX:{className:"highcharts-measure-x",start:function(t){var i=this.getCoords(t),e=i[0],s=i[1];if(e&&s){var n=e.value,o=s.value,a=this.chart.options.navigation,r=ty({langKey:"measure",type:"measure",typeOptions:{selectType:"x",xAxis:e.axis.index,yAxis:s.axis.index,point:{x:n,y:o},crosshairX:{strokeWidth:1,stroke:"#000000"},crosshairY:{enabled:!1,strokeWidth:0,stroke:"#000000"},background:{width:0,height:0,strokeWidth:0,stroke:"#ffffff"}},labelOptions:{style:{color:"#666666"}}},a.annotationsOptions,a.bindings.measureX.annotationsOptions);return this.chart.addAnnotation(r)}},steps:[tg]},measureY:{className:"highcharts-measure-y",start:function(t){var i=this.getCoords(t),e=i[0],s=i[1];if(e&&s){var n=e.value,o=s.value,a=this.chart.options.navigation,r=ty({langKey:"measure",type:"measure",typeOptions:{selectType:"y",xAxis:e.axis.index,yAxis:s.axis.index,point:{x:n,y:o},crosshairX:{enabled:!1,strokeWidth:0,stroke:"#000000"},crosshairY:{strokeWidth:1,stroke:"#000000"},background:{width:0,height:0,strokeWidth:0,stroke:"#ffffff"}},labelOptions:{style:{color:"#666666"}}},a.annotationsOptions,a.bindings.measureY.annotationsOptions);return this.chart.addAnnotation(r)}},steps:[tg]},measureXY:{className:"highcharts-measure-xy",start:function(t){var i=this.getCoords(t),e=i[0],s=i[1];if(e&&s){var n=e.value,o=s.value,a=this.chart.options.navigation,r=ty({langKey:"measure",type:"measure",typeOptions:{selectType:"xy",xAxis:e.axis.index,yAxis:s.axis.index,point:{x:n,y:o},background:{width:0,height:0,strokeWidth:0,stroke:"#ffffff"},crosshairX:{strokeWidth:1,stroke:"#000000"},crosshairY:{strokeWidth:1,stroke:"#000000"}},labelOptions:{style:{color:"#666666"}}},a.annotationsOptions,a.bindings.measureXY.annotationsOptions);return this.chart.addAnnotation(r)}},steps:[tg]},fibonacci:{className:"highcharts-fibonacci",start:function(t){var i=this.getCoords(t),e=i[0],s=i[1];if(e&&s){var n=e.value,o=s.value,a=this.chart.options.navigation,r=ty({langKey:"fibonacci",type:"fibonacci",typeOptions:{xAxis:e.axis.index,yAxis:s.axis.index,points:[{x:n,y:o},{x:n,y:o}]},labelOptions:{style:{color:"#666666"}}},a.annotationsOptions,a.bindings.fibonacci.annotationsOptions);return this.chart.addAnnotation(r)}},steps:[tu(1),td]},parallelChannel:{className:"highcharts-parallel-channel",start:function(t){var i=this.getCoords(t),e=i[0],s=i[1];if(e&&s){var n=e.value,o=s.value,a=this.chart.options.navigation,r=ty({langKey:"parallelChannel",type:"tunnel",typeOptions:{xAxis:e.axis.index,yAxis:s.axis.index,points:[{x:n,y:o},{x:n,y:o}]}},a.annotationsOptions,a.bindings.parallelChannel.annotationsOptions);return this.chart.addAnnotation(r)}},steps:[tu(1),td]},pitchfork:{className:"highcharts-pitchfork",start:function(t){var i=this.getCoords(t),e=i[0],s=i[1];if(e&&s){var n=e.value,o=s.value,a=this.chart.options.navigation,r=ty({langKey:"pitchfork",type:"pitchfork",typeOptions:{xAxis:e.axis.index,yAxis:s.axis.index,points:[{x:e.value,y:s.value,controlPoint:{style:{fill:"#f21313"}}},{x:n,y:o},{x:n,y:o}],innerBackground:{fill:"rgba(100, 170, 255, 0.8)"}},shapeOptions:{strokeWidth:2}},a.annotationsOptions,a.bindings.pitchfork.annotationsOptions);return this.chart.addAnnotation(r)}},steps:[tu(1),tu(2)]},verticalCounter:{className:"highcharts-vertical-counter",start:function(t){var i=tl(t,this.chart);if(i){this.verticalCounter=this.verticalCounter||0;var e=this.chart.options.navigation,s=ty({langKey:"verticalCounter",type:"verticalLine",typeOptions:{point:{x:i.x,y:i.y,xAxis:i.xAxis,yAxis:i.yAxis},label:{offset:i.below?40:-40,text:this.verticalCounter.toString()}},labelOptions:{style:{color:"#666666",fontSize:"0.7em"}},shapeOptions:{stroke:"rgba(0, 0, 0, 0.75)",strokeWidth:1}},e.annotationsOptions,e.bindings.verticalCounter.annotationsOptions),n=this.chart.addAnnotation(s);this.verticalCounter++,n.options.events.click.call(n,{})}}},timeCycles:{className:"highcharts-time-cycles",start:function(t){var i=tl(t,this.chart);if(i){var e=this.chart.options.navigation,s=ty({langKey:"timeCycles",type:"timeCycles",typeOptions:{xAxis:i.xAxis,yAxis:i.yAxis,points:[{x:i.x},{x:i.x}],line:{stroke:"rgba(0, 0, 0, 0.75)",fill:"transparent",strokeWidth:2}}},e.annotationsOptions,e.bindings.timeCycles.annotationsOptions),n=this.chart.addAnnotation(s);return n.options.events.click.call(n,{}),n}},steps:[tu(1)]},verticalLabel:{className:"highcharts-vertical-label",start:function(t){var i=tl(t,this.chart);if(i){var e=this.chart.options.navigation,s=ty({langKey:"verticalLabel",type:"verticalLine",typeOptions:{point:{x:i.x,y:i.y,xAxis:i.xAxis,yAxis:i.yAxis},label:{offset:i.below?40:-40}},labelOptions:{style:{color:"#666666",fontSize:"0.7em"}},shapeOptions:{stroke:"rgba(0, 0, 0, 0.75)",strokeWidth:1}},e.annotationsOptions,e.bindings.verticalLabel.annotationsOptions),n=this.chart.addAnnotation(s);n.options.events.click.call(n,{})}}},verticalArrow:{className:"highcharts-vertical-arrow",start:function(t){var i=tl(t,this.chart);if(i){var e=this.chart.options.navigation,s=ty({langKey:"verticalArrow",type:"verticalLine",typeOptions:{point:{x:i.x,y:i.y,xAxis:i.xAxis,yAxis:i.yAxis},label:{offset:i.below?40:-40,format:" "},connector:{fill:"none",stroke:i.below?"#f21313":"#06b535"}},shapeOptions:{stroke:"rgba(0, 0, 0, 0.75)",strokeWidth:1}},e.annotationsOptions,e.bindings.verticalArrow.annotationsOptions),n=this.chart.addAnnotation(s);n.options.events.click.call(n,{})}}},fibonacciTimeZones:{className:"highcharts-fibonacci-time-zones",start:function(t){var i=this.getCoords(t),e=i[0],s=i[1];if(e&&s){var n=this.chart.options.navigation,o=ty({type:"fibonacciTimeZones",langKey:"fibonacciTimeZones",typeOptions:{xAxis:e.axis.index,yAxis:s.axis.index,points:[{x:e.value}]}},n.annotationsOptions,n.bindings.fibonacciTimeZones.annotationsOptions);return this.chart.addAnnotation(o)}},steps:[function(t,i){var e=i.options.typeOptions.points,s=e&&e[0].x,n=this.getCoords(t),o=n[0],a=n[1];o&&a&&i.update({typeOptions:{xAxis:o.axis.index,yAxis:a.axis.index,points:[{x:s},{x:o.value}]}})}]},flagCirclepin:{className:"highcharts-flag-circlepin",start:tr("circlepin")},flagDiamondpin:{className:"highcharts-flag-diamondpin",start:tr("flag")},flagSquarepin:{className:"highcharts-flag-squarepin",start:tr("squarepin")},flagSimplepin:{className:"highcharts-flag-simplepin",start:tr("nopin")},zoomX:{className:"highcharts-zoom-x",init:function(t){this.chart.update({chart:{zooming:{type:"x"}}}),tv(this,"deselectButton",{button:t})}},zoomY:{className:"highcharts-zoom-y",init:function(t){this.chart.update({chart:{zooming:{type:"y"}}}),tv(this,"deselectButton",{button:t})}},zoomXY:{className:"highcharts-zoom-xy",init:function(t){this.chart.update({chart:{zooming:{type:"xy"}}}),tv(this,"deselectButton",{button:t})}},seriesTypeLine:{className:"highcharts-series-type-line",init:function(t){this.chart.series[0].update({type:"line",useOhlcData:!0}),tv(this,"deselectButton",{button:t})}},seriesTypeOhlc:{className:"highcharts-series-type-ohlc",init:function(t){this.chart.series[0].update({type:"ohlc"}),tv(this,"deselectButton",{button:t})}},seriesTypeCandlestick:{className:"highcharts-series-type-candlestick",init:function(t){this.chart.series[0].update({type:"candlestick"}),tv(this,"deselectButton",{button:t})}},seriesTypeHeikinAshi:{className:"highcharts-series-type-heikinashi",init:function(t){this.chart.series[0].update({type:"heikinashi"}),tv(this,"deselectButton",{button:t})}},seriesTypeHLC:{className:"highcharts-series-type-hlc",init:function(t){this.chart.series[0].update({type:"hlc",useOhlcData:!0}),tv(this,"deselectButton",{button:t})}},seriesTypeHollowCandlestick:{className:"highcharts-series-type-hollowcandlestick",init:function(t){this.chart.series[0].update({type:"hollowcandlestick"}),tv(this,"deselectButton",{button:t})}},fullScreen:{className:"highcharts-full-screen",noDataState:"normal",init:function(t){this.chart.fullscreen&&this.chart.fullscreen.toggle(),tv(this,"deselectButton",{button:t})}},currentPriceIndicator:{className:"highcharts-current-price-indicator",init:function(t){var i=this.chart,e=i.series,s=i.stockTools,n=th(i.series);s&&s.guiEnabled&&(e.forEach(function(t){t.update({lastPrice:{enabled:!n},lastVisiblePrice:{enabled:!n,label:{enabled:!0}}},!1)}),i.redraw()),tv(this,"deselectButton",{button:t})}},indicators:{className:"highcharts-indicators",init:function(){var t=this;tv(t,"showPopup",{formType:"indicators",options:{},onSubmit:function(i){tp.call(t,i)}})}},toggleAnnotations:{className:"highcharts-toggle-annotations",init:function(t){var i=this.chart,e=i.stockTools,s=e.getIconsURL();this.toggledAnnotations=!this.toggledAnnotations,(i.annotations||[]).forEach(function(t){t.setVisibility(!this.toggledAnnotations)},this),e&&e.guiEnabled&&(this.toggledAnnotations?t.firstChild.style["background-image"]='url("'+s+'annotations-hidden.svg")':t.firstChild.style["background-image"]='url("'+s+'annotations-visible.svg")'),tv(this,"deselectButton",{button:t})}},saveChart:{className:"highcharts-save-chart",noDataState:"normal",init:function(t){var i=this.chart,e=[],s=[],n=[],o=[];i.annotations.forEach(function(t,i){e[i]=t.userOptions}),i.series.forEach(function(t){t.is("sma")?s.push(t.userOptions):"flags"===t.type&&n.push(t.userOptions)}),i.yAxis.forEach(function(t){tc(t)&&o.push(t.options)}),d().win.localStorage.setItem("highcharts-chart",JSON.stringify({annotations:e,indicators:s,flags:n,yAxes:o})),tv(this,"deselectButton",{button:t})}}},tm={lang:{stockTools:{gui:{simpleShapes:"Simple shapes",lines:"Lines",crookedLines:"Crooked lines",measure:"Measure",advanced:"Advanced",toggleAnnotations:"Toggle annotations",verticalLabels:"Vertical labels",flags:"Flags",zoomChange:"Zoom change",typeChange:"Type change",saveChart:"Save chart",indicators:"Indicators",currentPriceIndicator:"Current Price Indicators",zoomX:"Zoom X",zoomY:"Zoom Y",zoomXY:"Zooom XY",fullScreen:"Fullscreen",typeOHLC:"OHLC",typeLine:"Line",typeCandlestick:"Candlestick",typeHLC:"HLC",typeHollowCandlestick:"Hollow Candlestick",typeHeikinAshi:"Heikin Ashi",circle:"Circle",ellipse:"Ellipse",label:"Label",rectangle:"Rectangle",flagCirclepin:"Flag circle",flagDiamondpin:"Flag diamond",flagSquarepin:"Flag square",flagSimplepin:"Flag simple",measureXY:"Measure XY",measureX:"Measure X",measureY:"Measure Y",segment:"Segment",arrowSegment:"Arrow segment",ray:"Ray",arrowRay:"Arrow ray",line:"Line",arrowInfinityLine:"Arrow line",horizontalLine:"Horizontal line",verticalLine:"Vertical line",infinityLine:"Infinity line",crooked3:"Crooked 3 line",crooked5:"Crooked 5 line",elliott3:"Elliott 3 line",elliott5:"Elliott 5 line",verticalCounter:"Vertical counter",verticalLabel:"Vertical label",verticalArrow:"Vertical arrow",fibonacci:"Fibonacci",fibonacciTimeZones:"Fibonacci Time Zones",pitchfork:"Pitchfork",parallelChannel:"Parallel channel",timeCycles:"Time Cycles"}},navigation:{popup:{circle:"Circle",ellipse:"Ellipse",rectangle:"Rectangle",label:"Label",segment:"Segment",arrowSegment:"Arrow segment",ray:"Ray",arrowRay:"Arrow ray",line:"Line",arrowInfinityLine:"Arrow line",horizontalLine:"Horizontal line",verticalLine:"Vertical line",crooked3:"Crooked 3 line",crooked5:"Crooked 5 line",elliott3:"Elliott 3 line",elliott5:"Elliott 5 line",verticalCounter:"Vertical counter",verticalLabel:"Vertical label",verticalArrow:"Vertical arrow",fibonacci:"Fibonacci",fibonacciTimeZones:"Fibonacci Time Zones",pitchfork:"Pitchfork",parallelChannel:"Parallel channel",infinityLine:"Infinity line",measure:"Measure",measureXY:"Measure XY",measureX:"Measure X",measureY:"Measure Y",timeCycles:"Time Cycles",flags:"Flags",addButton:"Add",saveButton:"Save",editButton:"Edit",removeButton:"Remove",series:"Series",volume:"Volume",connector:"Connector",innerBackground:"Inner background",outerBackground:"Outer background",crosshairX:"Crosshair X",crosshairY:"Crosshair Y",tunnel:"Tunnel",background:"Background",noFilterMatch:"No match",searchIndicators:"Search Indicators",clearFilter:"✕ clear filter",index:"Index",period:"Period",periods:"Periods",standardDeviation:"Standard deviation",periodTenkan:"Tenkan period",periodSenkouSpanB:"Senkou Span B period",periodATR:"ATR period",multiplierATR:"ATR multiplier",shortPeriod:"Short period",longPeriod:"Long period",signalPeriod:"Signal period",decimals:"Decimals",algorithm:"Algorithm",topBand:"Top band",bottomBand:"Bottom band",initialAccelerationFactor:"Initial acceleration factor",maxAccelerationFactor:"Max acceleration factor",increment:"Increment",multiplier:"Multiplier",ranges:"Ranges",highIndex:"High index",lowIndex:"Low index",deviation:"Deviation",xAxisUnit:"x-axis unit",factor:"Factor",fastAvgPeriod:"Fast average period",slowAvgPeriod:"Slow average period",average:"Average",indicatorAliases:{abands:["Acceleration Bands"],bb:["Bollinger Bands"],dema:["Double Exponential Moving Average"],ema:["Exponential Moving Average"],ikh:["Ichimoku Kinko Hyo"],keltnerchannels:["Keltner Channels"],linearRegression:["Linear Regression"],pivotpoints:["Pivot Points"],pc:["Price Channel"],priceenvelopes:["Price Envelopes"],psar:["Parabolic SAR"],sma:["Simple Moving Average"],supertrend:["Super Trend"],tema:["Triple Exponential Moving Average"],vbp:["Volume by Price"],vwap:["Volume Weighted Moving Average"],wma:["Weighted Moving Average"],zigzag:["Zig Zag"],apo:["Absolute price indicator"],ad:["Accumulation/Distribution"],aroon:["Aroon"],aroonoscillator:["Aroon oscillator"],atr:["Average True Range"],ao:["Awesome oscillator"],cci:["Commodity Channel Index"],chaikin:["Chaikin"],cmf:["Chaikin Money Flow"],cmo:["Chande Momentum Oscillator"],disparityindex:["Disparity Index"],dmi:["Directional Movement Index"],dpo:["Detrended price oscillator"],klinger:["Klinger Oscillator"],linearRegressionAngle:["Linear Regression Angle"],linearRegressionIntercept:["Linear Regression Intercept"],linearRegressionSlope:["Linear Regression Slope"],macd:["Moving Average Convergence Divergence"],mfi:["Money Flow Index"],momentum:["Momentum"],natr:["Normalized Average True Range"],obv:["On-Balance Volume"],ppo:["Percentage Price oscillator"],roc:["Rate of Change"],rsi:["Relative Strength Index"],slowstochastic:["Slow Stochastic"],stochastic:["Stochastic"],trix:["TRIX"],williamsr:["Williams %R"]}}}},stockTools:{gui:{enabled:!0,className:"highcharts-bindings-wrapper",toolbarClassName:"stocktools-toolbar",buttons:["indicators","separator","simpleShapes","lines","crookedLines","measure","advanced","toggleAnnotations","separator","verticalLabels","flags","separator","zoomChange","fullScreen","typeChange","separator","currentPriceIndicator","saveChart"],definitions:{separator:{elementType:"span",symbol:"separator.svg"},simpleShapes:{items:["label","circle","ellipse","rectangle"],circle:{symbol:"circle.svg"},ellipse:{symbol:"ellipse.svg"},rectangle:{symbol:"rectangle.svg"},label:{symbol:"label.svg"}},flags:{items:["flagCirclepin","flagDiamondpin","flagSquarepin","flagSimplepin"],flagSimplepin:{symbol:"flag-basic.svg"},flagDiamondpin:{symbol:"flag-diamond.svg"},flagSquarepin:{symbol:"flag-trapeze.svg"},flagCirclepin:{symbol:"flag-elipse.svg"}},lines:{items:["segment","arrowSegment","ray","arrowRay","line","arrowInfinityLine","horizontalLine","verticalLine"],segment:{symbol:"segment.svg"},arrowSegment:{symbol:"arrow-segment.svg"},ray:{symbol:"ray.svg"},arrowRay:{symbol:"arrow-ray.svg"},line:{symbol:"line.svg"},arrowInfinityLine:{symbol:"arrow-line.svg"},verticalLine:{symbol:"vertical-line.svg"},horizontalLine:{symbol:"horizontal-line.svg"}},crookedLines:{items:["elliott3","elliott5","crooked3","crooked5"],crooked3:{symbol:"crooked-3.svg"},crooked5:{symbol:"crooked-5.svg"},elliott3:{symbol:"elliott-3.svg"},elliott5:{symbol:"elliott-5.svg"}},verticalLabels:{items:["verticalCounter","verticalLabel","verticalArrow"],verticalCounter:{symbol:"vertical-counter.svg"},verticalLabel:{symbol:"vertical-label.svg"},verticalArrow:{symbol:"vertical-arrow.svg"}},advanced:{items:["fibonacci","fibonacciTimeZones","pitchfork","parallelChannel","timeCycles"],pitchfork:{symbol:"pitchfork.svg"},fibonacci:{symbol:"fibonacci.svg"},fibonacciTimeZones:{symbol:"fibonacci-timezone.svg"},parallelChannel:{symbol:"parallel-channel.svg"},timeCycles:{symbol:"time-cycles.svg"}},measure:{items:["measureXY","measureX","measureY"],measureX:{symbol:"measure-x.svg"},measureY:{symbol:"measure-y.svg"},measureXY:{symbol:"measure-xy.svg"}},toggleAnnotations:{symbol:"annotations-visible.svg"},currentPriceIndicator:{symbol:"current-price-show.svg"},indicators:{symbol:"indicators.svg"},zoomChange:{items:["zoomX","zoomY","zoomXY"],zoomX:{symbol:"zoom-x.svg"},zoomY:{symbol:"zoom-y.svg"},zoomXY:{symbol:"zoom-xy.svg"}},typeChange:{items:["typeOHLC","typeLine","typeCandlestick","typeHollowCandlestick","typeHLC","typeHeikinAshi"],typeOHLC:{symbol:"series-ohlc.svg"},typeLine:{symbol:"series-line.svg"},typeCandlestick:{symbol:"series-candlestick.svg"},typeHLC:{symbol:"series-hlc.svg"},typeHeikinAshi:{symbol:"series-heikin-ashi.svg"},typeHollowCandlestick:{symbol:"series-hollow-candlestick.svg"}},fullScreen:{symbol:"fullscreen.svg"},saveChart:{symbol:"save-chart.svg"}},visible:!0}}},tx=d().setOptions,tb=ta.isNotNavigatorYAxis,tA=ta.isPriceIndicatorEnabled,tk=d().correctFloat,tC=d().defined,tw=d().isNumber,tO=d().pick;function tN(t,i,e,s){var n,o,a,r=0;function l(t){return tC(t)&&!tw(t)&&t.match("%")}return s&&(a=tk(parseFloat(s.top)/100),o=tk(parseFloat(s.height)/100)),{positions:t.map(function(s,c){var h=tk(l(s.options.height)?parseFloat(s.options.height)/100:s.height/i),p=tk(l(s.options.top)?parseFloat(s.options.top)/100:(s.top-s.chart.plotTop)/i);return o?(p>a&&(p-=o),r=Math.max(r,(p||0)+(h||0))):(tw(h)||(h=t[c-1].series.every(function(t){return t.is("sma")})?n:e/100),tw(p)||(p=r),n=h,r=tk(Math.max(r,(p||0)+(h||0)))),{height:100*h,top:100*p}}),allAxesHeight:r}}function tL(t){var i=[];return t.forEach(function(e,s){var n=t[s+1];n?i[s]={enabled:!0,controlledAxis:{next:[tO(n.options.id,n.index)]}}:i[s]={enabled:!1}}),i}function tT(t,i,e,s){return t.forEach(function(n,o){var a=t[o-1];n.top=a?tk(a.height+a.top):0,e&&(n.height=tk(n.height+s*i))}),t}function tB(t){var i=this.chart,e=i.yAxis.filter(tb),s=i.plotHeight,n=this.getYAxisPositions(e,s,20,t),o=n.positions,a=n.allAxesHeight,r=this.getYAxisResizers(e);!t&&a<=tk(1)?o[o.length-1]={height:20,top:tk(100*a-20)}:o.forEach(function(t){t.height=t.height/(100*a)*100,t.top=t.top/(100*a)*100}),o.forEach(function(t,i){e[i].update({height:t.height+"%",top:t.top+"%",resize:r[i],offset:0},!1)})}var tE=c(660),tS=c.n(tE),tI=d().addEvent,tz=d().createElement,tW=d().css,tP=d().defined,tY=d().fireEvent,tH=d().getStyle,tR=d().isArray,tM=d().merge,tX=d().pick,tU=ta.shallowArraysEqual,tD=function(){function t(t,i,e){this.width=0,this.isDirty=!1,this.chart=e,this.options=t,this.lang=i,this.iconsURL=this.getIconsURL(),this.guiEnabled=t.enabled,this.visible=tX(t.visible,!0),this.guiClassName=t.className,this.toolbarClassName=t.toolbarClassName,this.eventsToUnbind=[],this.guiEnabled&&(this.createContainer(),this.createButtons(),this.showHideNavigation()),tY(this,"afterInit")}return t.prototype.createButtons=function(){var t=this,i=this.lang,e=this.options,s=this.toolbar,n=e.buttons,o=e.definitions,a=s.childNodes;this.buttonList=n,n.forEach(function(e){var n=t.addButton(s,o,e,i);t.eventsToUnbind.push(tI(n.buttonWrapper,"click",function(){return t.eraseActiveButtons(a,n.buttonWrapper)})),tR(o[e].items)&&t.addSubmenu(n,o[e])})},t.prototype.addSubmenu=function(t,i){var e=this,s=t.submenuArrow,n=t.buttonWrapper,o=tH(n,"width"),a=this.wrapper,r=this.listWrapper,l=this.toolbar.childNodes,c=this.submenu=tz("ul",{className:"highcharts-submenu-wrapper"},void 0,n);this.addSubmenuItems(n,i),this.eventsToUnbind.push(tI(s,"click",function(t){if(t.stopPropagation(),e.eraseActiveButtons(l,n),n.className.indexOf("highcharts-current")>=0)r.style.width=r.startWidth+"px",n.classList.remove("highcharts-current"),c.style.display="none";else{c.style.display="block";var i=c.offsetHeight-n.offsetHeight-3;c.offsetHeight+n.offsetTop>a.offsetHeight&&n.offsetTop>i||(i=0),tW(c,{top:-i+"px",left:o+3+"px"}),n.className+=" highcharts-current",r.startWidth=a.offsetWidth,r.style.width=r.startWidth+tH(r,"padding-left")+c.offsetWidth+3+"px"}}))},t.prototype.addSubmenuItems=function(t,i){var e,s=this,n=this,o=this.submenu,a=this.lang,r=this.listWrapper;i.items.forEach(function(l){e=s.addButton(o,i,l,a),s.eventsToUnbind.push(tI(e.mainButton,"click",function(){n.switchSymbol(this,t,!0),r.style.width=r.startWidth+"px",o.style.display="none"}))});var l=o.querySelectorAll("li > .highcharts-menu-item-btn")[0];this.switchSymbol(l,!1)},t.prototype.eraseActiveButtons=function(t,i,e){[].forEach.call(t,function(t){t!==i&&(t.classList.remove("highcharts-current"),t.classList.remove("highcharts-active"),(e=t.querySelectorAll(".highcharts-submenu-wrapper")).length>0&&(e[0].style.display="none"))})},t.prototype.addButton=function(i,e,s,n){void 0===n&&(n={});var o=e[s],a=o.items,r=t.prototype.classMapping,l=o.className||"",c=tz("li",{className:tX(r[s],"")+" "+l,title:n[s]||s},void 0,i),h=tz(o.elementType||"button",{className:"highcharts-menu-item-btn"},void 0,c);if(a&&a.length){var p=tz("button",{className:"highcharts-submenu-item-arrow highcharts-arrow-right"},void 0,c);return p.style.backgroundImage="url("+this.iconsURL+"arrow-bottom.svg)",{buttonWrapper:c,mainButton:h,submenuArrow:p}}return h.style.backgroundImage="url("+this.iconsURL+o.symbol+")",{buttonWrapper:c,mainButton:h}},t.prototype.addNavigation=function(){var t=this.wrapper;this.arrowWrapper=tz("div",{className:"highcharts-arrow-wrapper"}),this.arrowUp=tz("div",{className:"highcharts-arrow-up"},void 0,this.arrowWrapper),this.arrowUp.style.backgroundImage="url("+this.iconsURL+"arrow-right.svg)",this.arrowDown=tz("div",{className:"highcharts-arrow-down"},void 0,this.arrowWrapper),this.arrowDown.style.backgroundImage="url("+this.iconsURL+"arrow-right.svg)",t.insertBefore(this.arrowWrapper,t.childNodes[0]),this.scrollButtons()},t.prototype.scrollButtons=function(){var t=this.wrapper,i=this.toolbar,e=.1*t.offsetHeight,s=0;this.eventsToUnbind.push(tI(this.arrowUp,"click",function(){s>0&&(s-=e,i.style.marginTop=-s+"px")})),this.eventsToUnbind.push(tI(this.arrowDown,"click",function(){t.offsetHeight+s<=i.offsetHeight+e&&(s+=e,i.style.marginTop=-s+"px")}))},t.prototype.createContainer=function(){var t,i,e=this,s=this.chart,n=this.options,o=s.container,a=s.options.navigation,r=null==a?void 0:a.bindingsClassName,l=this,c=this.wrapper=tz("div",{className:"highcharts-stocktools-wrapper "+n.className+" "+r});o.appendChild(c),this.showHideBtn=tz("div",{className:"highcharts-toggle-toolbar highcharts-arrow-left"},void 0,c),this.eventsToUnbind.push(tI(this.showHideBtn,"click",function(){e.update({gui:{visible:!l.visible}})})),["mousedown","mousemove","click","touchstart"].forEach(function(t){tI(c,t,function(t){return t.stopPropagation()})}),tI(c,"mouseover",function(t){var i;return null===(i=s.pointer)||void 0===i?void 0:i.onContainerMouseLeave(t)}),this.toolbar=i=tz("ul",{className:"highcharts-stocktools-toolbar "+n.toolbarClassName}),this.listWrapper=t=tz("div",{className:"highcharts-menu-wrapper"}),c.insertBefore(t,c.childNodes[0]),t.insertBefore(i,t.childNodes[0]),this.showHideToolbar(),this.addNavigation()},t.prototype.showHideNavigation=function(){this.visible&&this.toolbar.offsetHeight>this.wrapper.offsetHeight-50?this.arrowWrapper.style.display="block":(this.toolbar.style.marginTop="0px",this.arrowWrapper.style.display="none")},t.prototype.showHideToolbar=function(){var t=this.wrapper,i=this.listWrapper,e=this.submenu,s=this.showHideBtn,n=this.visible;s.style.backgroundImage="url("+this.iconsURL+"arrow-right.svg)",n?(t.style.height="100%",i.classList.remove("highcharts-hide"),s.classList.remove("highcharts-arrow-right"),s.style.top=tH(i,"padding-top")+"px",s.style.left=t.offsetWidth+tH(i,"padding-left")+"px"):(e&&(e.style.display="none"),s.style.left="0px",n=this.visible=!1,i.classList.add("highcharts-hide"),s.classList.add("highcharts-arrow-right"),t.style.height=s.offsetHeight+"px")},t.prototype.switchSymbol=function(t,i){var e=t.parentNode,s=e.className,n=e.parentNode.parentNode;!(s.indexOf("highcharts-disabled-btn")>-1)&&(n.className="",s&&n.classList.add(s.trim()),n.querySelectorAll(".highcharts-menu-item-btn")[0].style.backgroundImage=t.style.backgroundImage,i&&this.toggleButtonActiveClass(n))},t.prototype.toggleButtonActiveClass=function(t){var i=t.classList;i.contains("highcharts-active")?i.remove("highcharts-active"):i.add("highcharts-active")},t.prototype.unselectAllButtons=function(t){var i=t.parentNode.querySelectorAll(".highcharts-active");[].forEach.call(i,function(i){i!==t&&i.classList.remove("highcharts-active")})},t.prototype.update=function(t,i){this.isDirty=!!t.gui.definitions,tM(!0,this.chart.options.stockTools,t),tM(!0,this.options,t.gui),this.visible=tX(this.options.visible&&this.options.enabled,!0),this.chart.navigationBindings&&this.chart.navigationBindings.update(),this.chart.isDirtyBox=!0,tX(i,!0)&&this.chart.redraw()},t.prototype.destroy=function(){var t=this.wrapper,i=t&&t.parentNode;this.eventsToUnbind.forEach(function(t){return t()}),i&&i.removeChild(t)},t.prototype.redraw=function(){if(this.options.enabled!==this.guiEnabled)this.handleGuiEnabledChange();else{if(!this.guiEnabled)return;this.updateClassNames(),this.updateButtons(),this.updateVisibility(),this.showHideNavigation(),this.showHideToolbar()}},t.prototype.handleGuiEnabledChange=function(){!1===this.options.enabled&&(this.destroy(),this.visible=!1),!0===this.options.enabled&&(this.createContainer(),this.createButtons()),this.guiEnabled=this.options.enabled},t.prototype.updateClassNames=function(){this.options.className!==this.guiClassName&&(this.guiClassName&&this.wrapper.classList.remove(this.guiClassName),this.options.className&&this.wrapper.classList.add(this.options.className),this.guiClassName=this.options.className),this.options.toolbarClassName!==this.toolbarClassName&&(this.toolbarClassName&&this.toolbar.classList.remove(this.toolbarClassName),this.options.toolbarClassName&&this.toolbar.classList.add(this.options.toolbarClassName),this.toolbarClassName=this.options.toolbarClassName)},t.prototype.updateButtons=function(){(!tU(this.options.buttons,this.buttonList)||this.isDirty)&&(this.toolbar.innerHTML=tS().emptyHTML,this.createButtons())},t.prototype.updateVisibility=function(){tP(this.options.visible)&&(this.visible=this.options.visible)},t.prototype.getIconsURL=function(){return this.chart.options.navigation.iconsURL||this.options.iconsURL||"https://code.highcharts.com/12.2.0/gfx/stock-icons/"},t}();tD.prototype.classMapping={circle:"highcharts-circle-annotation",ellipse:"highcharts-ellipse-annotation",rectangle:"highcharts-rectangle-annotation",label:"highcharts-label-annotation",segment:"highcharts-segment",arrowSegment:"highcharts-arrow-segment",ray:"highcharts-ray",arrowRay:"highcharts-arrow-ray",line:"highcharts-infinity-line",arrowInfinityLine:"highcharts-arrow-infinity-line",verticalLine:"highcharts-vertical-line",horizontalLine:"highcharts-horizontal-line",crooked3:"highcharts-crooked3",crooked5:"highcharts-crooked5",elliott3:"highcharts-elliott3",elliott5:"highcharts-elliott5",pitchfork:"highcharts-pitchfork",fibonacci:"highcharts-fibonacci",fibonacciTimeZones:"highcharts-fibonacci-time-zones",parallelChannel:"highcharts-parallel-channel",measureX:"highcharts-measure-x",measureY:"highcharts-measure-y",measureXY:"highcharts-measure-xy",timeCycles:"highcharts-time-cycles",verticalCounter:"highcharts-vertical-counter",verticalLabel:"highcharts-vertical-label",verticalArrow:"highcharts-vertical-arrow",currentPriceIndicator:"highcharts-current-price-indicator",indicators:"highcharts-indicators",flagCirclepin:"highcharts-flag-circlepin",flagDiamondpin:"highcharts-flag-diamondpin",flagSquarepin:"highcharts-flag-squarepin",flagSimplepin:"highcharts-flag-simplepin",zoomX:"highcharts-zoom-x",zoomY:"highcharts-zoom-y",zoomXY:"highcharts-zoom-xy",typeLine:"highcharts-series-type-line",typeOHLC:"highcharts-series-type-ohlc",typeHLC:"highcharts-series-type-hlc",typeCandlestick:"highcharts-series-type-candlestick",typeHollowCandlestick:"highcharts-series-type-hollowcandlestick",typeHeikinAshi:"highcharts-series-type-heikinashi",fullScreen:"highcharts-full-screen",toggleAnnotations:"highcharts-toggle-annotations",saveChart:"highcharts-save-chart",separator:"highcharts-separator"};var tF=d().setOptions,tK=d().addEvent,tV=d().getStyle,tq=d().merge,tZ=d().pick;function tj(t){var i=this.options,e=i.lang,s=tq(i.stockTools&&i.stockTools.gui,t&&t.gui),n=e&&e.stockTools&&e.stockTools.gui;this.stockTools=new tD(s,n,this),this.stockTools.guiEnabled&&(this.isDirtyBox=!0)}function tG(){this.setStockTools()}function t_(){this.stockTools&&(this.stockTools.redraw(),function(t){var i;if(null===(i=t.stockTools)||void 0===i?void 0:i.guiEnabled){var e=t.options.chart,s=t.stockTools.listWrapper,n=s&&(s.startWidth+tV(s,"padding-left")+tV(s,"padding-right")||s.offsetWidth);t.stockTools.width=n;var o=!1;if(n<t.plotWidth){var a=tZ(e.spacingLeft,e.spacing&&e.spacing[3],0)+n,r=a-t.spacingBox.x;t.spacingBox.x=a,t.spacingBox.width-=r,o=!0}else 0===n&&(o=!0);n!==t.stockTools.prevOffsetWidth&&(t.stockTools.prevOffsetWidth=n,o&&(t.isDirtyLegend=!0))}}(this))}function tJ(){this.stockTools&&this.stockTools.destroy()}function tQ(){var t,i=(null===(t=this.stockTools)||void 0===t?void 0:t.visible)&&this.stockTools.guiEnabled?this.stockTools.width:0;i&&i<this.plotWidth&&(this.plotLeft+=i,this.spacing[3]+=i)}function t$(){var t,i,e=this.stockTools,s=e&&e.toolbar&&e.toolbar.querySelector(".highcharts-current-price-indicator");e&&this.navigationBindings&&this.options.series&&s&&((null===(i=null===(t=this.navigationBindings.utils)||void 0===t?void 0:t.isPriceIndicatorEnabled)||void 0===i?void 0:i.call(t,this.series))?s.firstChild.style["background-image"]='url("'+e.getIconsURL()+'current-price-hide.svg")':s.firstChild.style["background-image"]='url("'+e.getIconsURL()+'current-price-show.svg")')}function t0(t){var i=this.chart.stockTools;if(i&&i.guiEnabled){var e=t.button;e.parentNode.className.indexOf("highcharts-submenu-wrapper")>=0&&(e=e.parentNode.parentNode),e.classList.remove("highcharts-active")}}function t1(t){var i=this.chart.stockTools;if(i&&i.guiEnabled){var e=t.button;i.unselectAllButtons(t.button),e.parentNode.className.indexOf("highcharts-submenu-wrapper")>=0&&(e=e.parentNode.parentNode),i.toggleButtonActiveClass(e)}}var t6=d();t6.NavigationBindings=t6.NavigationBindings||G,t6.Toolbar=tD,({compose:function(t){var i,e=t.prototype;(null===(i=e.utils)||void 0===i?void 0:i.manageIndicators)||(e.getYAxisPositions=tN,e.getYAxisResizers=tL,e.recalculateYAxisPositions=tT,e.resizeYAxes=tB,e.utils=e.utils||{},e.utils.indicatorsWithAxes=ta.indicatorsWithAxes,e.utils.indicatorsWithVolume=ta.indicatorsWithVolume,e.utils.getAssignedAxis=b,e.utils.isPriceIndicatorEnabled=tA,e.utils.manageIndicators=ta.manageIndicators,tx(tm),tx({navigation:{bindings:tf}}))}}).compose(t6.NavigationBindings),({compose:function(t,i){var e=t.prototype;e.setStockTools||(tK(t,"afterGetContainer",tG),tK(t,"beforeRedraw",t_),tK(t,"beforeRender",t_),tK(t,"destroy",tJ),tK(t,"getMargins",tQ,{order:0}),tK(t,"render",t$),e.setStockTools=tj,tK(i,"deselectButton",t0),tK(i,"selectButton",t1),tF(tm))}}).compose(t6.Chart,t6.NavigationBindings);var t3=d();return h.default}()});