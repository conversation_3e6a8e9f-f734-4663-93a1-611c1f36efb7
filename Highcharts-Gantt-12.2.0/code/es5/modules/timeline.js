!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("highcharts"),require("highcharts").SeriesRegistry,require("highcharts").Point):"function"==typeof define&&define.amd?define("highcharts/modules/timeline",[["highcharts/highcharts"],["highcharts/highcharts","SeriesRegistry"],["highcharts/highcharts","Point"]],e):"object"==typeof exports?exports["highcharts/modules/timeline"]=e(require("highcharts"),require("highcharts").SeriesRegistry,require("highcharts").Point):t.Highcharts=e(t.Highcharts,t.Highcharts.SeriesRegistry,t.Highcharts.Point)}(this,function(t,e,i){return function(){"use strict";var n,o,r={260:function(t){t.exports=i},512:function(t){t.exports=e},944:function(e){e.exports=t}},s={};function a(t){var e=s[t];if(void 0!==e)return e.exports;var i=s[t]={exports:{}};return r[t](i,i.exports,a),i.exports}a.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return a.d(e,{a:e}),e},a.d=function(t,e){for(var i in e)a.o(e,i)&&!a.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},a.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};var l={};a.d(l,{default:function(){return q}});var h=a(944),p=a.n(h),c=a(512),u=a.n(c),d=a(260),y=a.n(d),f=(n=function(t,e){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])})(t,e)},function(t,e){function i(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),g=u().seriesTypes,v=g.line.prototype.pointClass,x=g.pie.prototype.pointClass,b=p().defined,m=p().isNumber,w=p().merge,P=p().objectEach,O=p().pick,L=function(t){function e(e,i){var n,o=t.call(this,e,i)||this;return null!==(n=o.name)&&void 0!==n||(o.name=(i&&null!==i.y||!e.options.nullInteraction)&&"Event"||"Null"),o.y=1,o}return f(e,t),e.prototype.alignConnector=function(){var t=this.series,e=this.dataLabel,i=e.connector,n=e.options||{},o=n.connectorWidth||0,r=this.series.chart,s=i.getBBox(),a={x:s.x+(e.translateX||0),y:s.y+(e.translateY||0)};r.inverted?a.y-=o/2:a.x+=o/2,i[r.isInsidePlot(a.x,a.y)?"animate":"attr"]({d:this.getConnectorPath()}),i.addClass("highcharts-color-"+this.colorIndex),t.chart.styledMode||i.attr({stroke:n.connectorColor||this.color,"stroke-width":n.connectorWidth,opacity:e[b(e.newOpacity)?"newOpacity":"opacity"]})},e.prototype.drawConnector=function(){var t=this.dataLabel,e=this.series;t&&(t.connector||(t.connector=e.chart.renderer.path(this.getConnectorPath()).attr({zIndex:-1}).add(t)),this.series.chart.isInsidePlot(t.x||0,t.y||0)&&this.alignConnector())},e.prototype.getConnectorPath=function(){var t,e=this.plotX,i=void 0===e?0:e,n=this.plotY,o=void 0===n?0:n,r=this.series,s=this.dataLabel,a=r.chart,l=r.xAxis.len,h=a.inverted,p=h?"x2":"y2";if(s){var c=s.targetPosition,u=(s.alignAttr||s)[p[0]]<r.yAxis.len/2,d={x1:i,y1:o,x2:i,y2:m(c.y)?c.y:s.y};return h&&(d={x1:o,y1:l-i,x2:c.x||s.x,y2:l-i}),u&&(d[p]+=s[h?"width":"height"]||0),P(d,function(t,e){d[e]-=(s.alignAttr||s)[e[0]]}),a.renderer.crispLine([["M",d.x1,d.y1],["L",d.x2,d.y2]],(null===(t=s.options)||void 0===t?void 0:t.connectorWidth)||0)}return[]},e.prototype.isValid=function(){return null!==this.options.y||this.series.options.nullInteraction||!0},e.prototype.setState=function(){var e=t.prototype.setState;(!this.isNull||this.series.options.nullInteraction)&&e.apply(this,arguments)},e.prototype.setVisible=function(t,e){var i=this.series;e=O(e,i.options.ignoreHiddenPoint),x.prototype.setVisible.call(this,t,!1),i.processData(),e&&i.chart.redraw()},e.prototype.applyOptions=function(e,i){var n=this.isNull||null===e||null===e.y,o=this.series;i||(null==e?void 0:e.x)||(m(this.x)?i=this.x:m(null==o?void 0:o.xIncrement)&&(i=o.xIncrement||0,o.autoIncrement())),e=y().prototype.optionsToObject.call(this,null!=e?e:o.options.nullInteraction&&{y:0}||null);var r=t.prototype.applyOptions.call(this,e,i);return this.userDLOptions=w(this.userDLOptions,e.dataLabels),r.isNull=n,r},e}(v),A={colorByPoint:!0,stickyTracking:!1,ignoreHiddenPoint:!0,legendType:"point",lineWidth:4,tooltip:{headerFormat:'<span style="color:{point.color}">●</span> <span style="font-size: 0.8em"> {point.key}</span><br/>',pointFormat:"{point.description}"},states:{hover:{lineWidthPlus:0}},dataLabels:{enabled:!0,allowOverlap:!0,alternate:!0,backgroundColor:"#ffffff",borderWidth:1,borderColor:"#999999",borderRadius:3,color:"#333333",connectorWidth:1,distance:void 0,formatter:function(){var t;return(this.series.chart.styledMode?'<span class="highcharts-color-'+this.point.colorIndex+'">● </span>':'<span style="color:'+this.point.color+'">● </span>')+('<span class="highcharts-strong">'+(this.key||"")+"</span><br/>")+(this.label||"")},style:{textOutline:"none",fontWeight:"normal",fontSize:"0.8em",textAlign:"left"},shadow:!1,verticalAlign:"middle"},marker:{enabledThreshold:0,symbol:"square",radius:6,lineWidth:2,height:15},showInLegend:!1,colorKey:"x",legendSymbol:"rectangle"},C=(o=function(t,e){return(o=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),_=u().seriesTypes,k=_.column,I=_.line,M=p().addEvent,T=p().arrayMax,D=p().arrayMin,j=p().defined,S=p().extend,N=p().merge,W=p().pick,H=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return C(e,t),e.prototype.alignDataLabel=function(e,i,n,o){var r,s,a,l,h=this.chart.inverted,p=this.visibilityMap.filter(function(t){return!!t}),c=this.visiblePointsCount||0,u=p.indexOf(e),d=this.options.dataLabels,y=e.userDLOptions||{},f=d.alternate?u&&u!==c-1?2:1.5:1,g=Math.floor(this.xAxis.len/c),v=i.padding;e.visible&&(s=Math.abs(y.x||e.options.dataLabels.x),h?(a=(s-v)*2-(e.itemHeight||0)/2,l={width:W(null===(r=d.style)||void 0===r?void 0:r.width,""+.4*this.yAxis.len+"px"),textOverflow:(i.width||0)/a*(i.height||0)/2>g*f?"ellipsis":"none"}):l={width:(y.width||d.width||g*f-2*v)+"px"},i.css(l),this.chart.styledMode||i.shadow(d.shadow)),t.prototype.alignDataLabel.apply(this,arguments)},e.prototype.bindAxes=function(){t.prototype.bindAxes.call(this),this.xAxis.userOptions.type||(this.xAxis.categories=this.xAxis.hasNames=!0)},e.prototype.distributeDL=function(){var t,e=this.options.dataLabels,i=this.chart.inverted,n=1;if(e)for(var o=W(e.distance,i?20:100),r=0,s=this.points;r<s.length;r++){var a=s[r],l=((t={})[i?"x":"y"]=e.alternate&&n%2?-o:o,t);i&&(l.align=e.alternate&&n%2?"right":"left"),a.options.dataLabels=N(l,a.userDLOptions),n++}},e.prototype.generatePoints=function(){t.prototype.generatePoints.call(this);for(var e=this.points,i=e.length,n=this.getColumn("x"),o=0;o<i;++o){var r=n[o];e[o].applyOptions({x:r},r)}},e.prototype.getVisibilityMap=function(){var t=this.options.nullInteraction;return((this.data.length?this.data:this.options.data)||[]).map(function(e){return!!e&&!1!==e.visible&&(!e.isNull||!!t)&&e})},e.prototype.getXExtremes=function(t){var e=this,i=t.filter(function(t,i){return e.points[i].isValid()&&e.points[i].visible});return{min:D(i),max:T(i)}},e.prototype.init=function(){var e=this;t.prototype.init.apply(e,arguments),e.eventsToUnbind.push(M(e,"afterTranslate",function(){for(var t,i=Number.MAX_VALUE,n=0,o=e.points;n<o.length;n++){var r=o[n];r.isInside=r.isInside&&r.visible,r.visible&&(!r.isNull||e.options.nullInteraction)&&(j(t)&&(i=Math.min(i,Math.abs(r.plotX-t))),t=r.plotX)}e.closestPointRangePx=i})),e.eventsToUnbind.push(M(e,"drawDataLabels",function(){e.distributeDL()})),e.eventsToUnbind.push(M(e,"afterDrawDataLabels",function(){for(var t,i=0,n=e.points;i<n.length;i++){var o=n[i];(t=o.dataLabel)&&(t.animate=function(t){return this.targetPosition&&(this.targetPosition=t),this.renderer.Element.prototype.animate.apply(this,arguments)},t.targetPosition||(t.targetPosition={}),o.drawConnector())}})),e.eventsToUnbind.push(M(e.chart,"afterHideOverlappingLabel",function(){for(var t=0,i=e.points;t<i.length;t++){var n=i[t];n.dataLabel&&n.dataLabel.connector&&n.dataLabel.oldOpacity!==n.dataLabel.newOpacity&&n.alignConnector()}}))},e.prototype.markerAttribs=function(e,i){var n,o=this.options.marker,r=e.marker||{},s=r.symbol||o.symbol,a=W(r.width,o.width,this.closestPointRangePx),l=W(r.height,o.height),h=0;if(this.xAxis.dateTime)return t.prototype.markerAttribs.call(this,e,i);i&&(n=o.states[i]||{},h=W((r.states&&r.states[i]||{}).radius,n.radius,h+(n.radiusPlus||0))),e.hasImage=s&&0===s.indexOf("url");var p={x:Math.floor(e.plotX)-a/2-h/2,y:e.plotY-l/2-h/2,width:a+h,height:l+h};return this.chart.inverted?{y:p.x&&p.width&&this.xAxis.len-p.x-p.width,x:p.y&&p.y,width:p.height,height:p.width}:p},e.defaultOptions=N(I.defaultOptions,A),e}(I);M(H,"afterProcessData",function(){var t=this.getColumn("x"),e=0;this.visibilityMap=this.getVisibilityMap();for(var i=0,n=this.visibilityMap;i<n.length;i++)n[i]&&e++;this.visiblePointsCount=e,this.dataTable.setColumn("y",Array(t.length).fill(1))}),S(H.prototype,{drawTracker:k.prototype.drawTracker,pointClass:L,trackerGroups:["markerGroup","dataLabelsGroup"]}),u().registerSeriesType("timeline",H);var q=p();return l.default}()});