{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/annotations\n * @requires highcharts\n *\n * Annotations module\n *\n * (c) 2009-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"], require(\"highcharts\")[\"Templating\"], require(\"highcharts\")[\"AST\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/annotations\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"SeriesRegistry\"], [\"highcharts/highcharts\",\"Templating\"], [\"highcharts/highcharts\",\"AST\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/annotations\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"], require(\"highcharts\")[\"Templating\"], require(\"highcharts\")[\"AST\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"Templating\"], root[\"Highcharts\"][\"AST\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__984__, __WEBPACK_EXTERNAL_MODULE__660__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 660:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__660__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ }),\n\n/***/ 984:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__984__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ annotations_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es5/es-modules/Extensions/Annotations/AnnotationChart.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, erase = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).erase, find = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).find, fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, wrap = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).wrap;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Add an annotation to the chart after render time.\n *\n * @sample highcharts/annotations/add-annotation/\n *         Add annotation\n *\n * @function Highcharts.Chart#addAnnotation\n *\n * @param  {Highcharts.AnnotationsOptions} options\n *         The annotation options for the new, detailed annotation.\n *\n * @param {boolean} [redraw]\n *\n * @return {Highcharts.Annotation}\n *         The newly generated annotation.\n */\nfunction chartAddAnnotation(userOptions, redraw) {\n    var annotation = this.initAnnotation(userOptions);\n    this.options.annotations.push(annotation.options);\n    if (pick(redraw, true)) {\n        annotation.redraw();\n        annotation.graphic.attr({\n            opacity: 1\n        });\n    }\n    return annotation;\n}\n/**\n * @private\n */\nfunction chartCallback() {\n    var chart = this;\n    chart.plotBoxClip = this.renderer.clipRect(this.plotBox);\n    chart.controlPointsGroup = chart.renderer\n        .g('control-points')\n        .attr({ zIndex: 99 })\n        .clip(chart.plotBoxClip)\n        .add();\n    chart.options.annotations.forEach(function (annotationOptions, i) {\n        if (\n        // Verify that it has not been previously added in a responsive rule\n        !chart.annotations.some(function (annotation) {\n            return annotation.options === annotationOptions;\n        })) {\n            var annotation = chart.initAnnotation(annotationOptions);\n            chart.options.annotations[i] = annotation.options;\n        }\n    });\n    chart.drawAnnotations();\n    addEvent(chart, 'redraw', chart.drawAnnotations);\n    addEvent(chart, 'destroy', function () {\n        chart.plotBoxClip.destroy();\n        chart.controlPointsGroup.destroy();\n    });\n    addEvent(chart, 'exportData', function (event) {\n        var annotations = chart.annotations,\n            csvColumnHeaderFormatter = ((this.options.exporting &&\n                this.options.exporting.csv) ||\n                {}).columnHeaderFormatter, \n            // If second row doesn't have xValues\n            // then it is a title row thus multiple level header is in use.\n            multiLevelHeaders = !event.dataRows[1].xValues,\n            annotationHeader = (chart.options.lang &&\n                chart.options.lang.exportData &&\n                chart.options.lang.exportData.annotationHeader),\n            columnHeaderFormatter = function (index) {\n                var s;\n            if (csvColumnHeaderFormatter) {\n                s = csvColumnHeaderFormatter(index);\n                if (s !== false) {\n                    return s;\n                }\n            }\n            s = annotationHeader + ' ' + index;\n            if (multiLevelHeaders) {\n                return {\n                    columnTitle: s,\n                    topLevelColumnTitle: s\n                };\n            }\n            return s;\n        }, startRowLength = event.dataRows[0].length, annotationSeparator = (chart.options.exporting &&\n            chart.options.exporting.csv &&\n            chart.options.exporting.csv.annotations &&\n            chart.options.exporting.csv.annotations.itemDelimiter), joinAnnotations = (chart.options.exporting &&\n            chart.options.exporting.csv &&\n            chart.options.exporting.csv.annotations &&\n            chart.options.exporting.csv.annotations.join);\n        annotations.forEach(function (annotation) {\n            if (annotation.options.labelOptions &&\n                annotation.options.labelOptions.includeInDataExport) {\n                annotation.labels.forEach(function (label) {\n                    if (label.options.text) {\n                        var annotationText_1 = label.options.text;\n                        label.points.forEach(function (points) {\n                            var annotationX = points.x,\n                                xAxisIndex = points.series.xAxis ?\n                                    points.series.xAxis.index :\n                                    -1;\n                            var wasAdded = false;\n                            // Annotation not connected to any xAxis -\n                            // add new row.\n                            if (xAxisIndex === -1) {\n                                var n = event.dataRows[0].length,\n                                    newRow = new Array(n);\n                                for (var i = 0; i < n; ++i) {\n                                    newRow[i] = '';\n                                }\n                                newRow.push(annotationText_1);\n                                newRow.xValues = [];\n                                newRow.xValues[xAxisIndex] = annotationX;\n                                event.dataRows.push(newRow);\n                                wasAdded = true;\n                            }\n                            // Annotation placed on a exported data point\n                            // - add new column\n                            if (!wasAdded) {\n                                event.dataRows.forEach(function (row) {\n                                    if (!wasAdded &&\n                                        row.xValues &&\n                                        xAxisIndex !== void 0 &&\n                                        annotationX === row.xValues[xAxisIndex]) {\n                                        if (joinAnnotations &&\n                                            row.length > startRowLength) {\n                                            row[row.length - 1] += (annotationSeparator +\n                                                annotationText_1);\n                                        }\n                                        else {\n                                            row.push(annotationText_1);\n                                        }\n                                        wasAdded = true;\n                                    }\n                                });\n                            }\n                            // Annotation not placed on any exported data point,\n                            // but connected to the xAxis - add new row\n                            if (!wasAdded) {\n                                var n = event.dataRows[0].length,\n                                    newRow = new Array(n);\n                                for (var i = 0; i < n; ++i) {\n                                    newRow[i] = '';\n                                }\n                                newRow[0] = annotationX;\n                                newRow.push(annotationText_1);\n                                newRow.xValues = [];\n                                if (xAxisIndex !== void 0) {\n                                    newRow.xValues[xAxisIndex] = annotationX;\n                                }\n                                event.dataRows.push(newRow);\n                            }\n                        });\n                    }\n                });\n            }\n        });\n        var maxRowLen = 0;\n        event.dataRows.forEach(function (row) {\n            maxRowLen = Math.max(maxRowLen, row.length);\n        });\n        var newRows = maxRowLen - event.dataRows[0].length;\n        for (var i = 0; i < newRows; i++) {\n            var header = columnHeaderFormatter(i + 1);\n            if (multiLevelHeaders) {\n                event.dataRows[0].push(header.topLevelColumnTitle);\n                event.dataRows[1].push(header.columnTitle);\n            }\n            else {\n                event.dataRows[0].push(header);\n            }\n        }\n    });\n}\n/**\n * @private\n */\nfunction chartDrawAnnotations() {\n    this.plotBoxClip.attr(this.plotBox);\n    this.annotations.forEach(function (annotation) {\n        annotation.redraw();\n        annotation.graphic.animate({\n            opacity: 1\n        }, annotation.animationConfig);\n    });\n}\n/**\n * Remove an annotation from the chart.\n *\n * @function Highcharts.Chart#removeAnnotation\n *\n * @param {number|string|Highcharts.Annotation} idOrAnnotation\n *        The annotation's id or direct annotation object.\n */\nfunction chartRemoveAnnotation(idOrAnnotation) {\n    var annotations = this.annotations,\n        annotation = (idOrAnnotation.coll === 'annotations') ?\n            idOrAnnotation :\n            find(annotations,\n        function (annotation) {\n                return annotation.options.id === idOrAnnotation;\n        });\n    if (annotation) {\n        fireEvent(annotation, 'remove');\n        erase(this.options.annotations, annotation.options);\n        erase(annotations, annotation);\n        annotation.destroy();\n    }\n}\n/**\n * Create lookups initially\n * @private\n */\nfunction onChartAfterInit() {\n    var chart = this;\n    chart.annotations = [];\n    if (!this.options.annotations) {\n        this.options.annotations = [];\n    }\n}\n/**\n * @private\n */\nfunction wrapPointerOnContainerMouseDown(proceed) {\n    if (!this.chart.hasDraggedAnnotation) {\n        proceed.apply(this, Array.prototype.slice.call(arguments, 1));\n    }\n}\n/* *\n *\n *  Composition\n *\n * */\n/**\n * @private\n */\nvar AnnotationChart;\n(function (AnnotationChart) {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    function compose(AnnotationClass, ChartClass, PointerClass) {\n        var chartProto = ChartClass.prototype;\n        if (!chartProto.addAnnotation) {\n            var pointerProto = PointerClass.prototype;\n            addEvent(ChartClass, 'afterInit', onChartAfterInit);\n            chartProto.addAnnotation = chartAddAnnotation;\n            chartProto.callbacks.push(chartCallback);\n            chartProto.collectionsWithInit.annotations = [chartAddAnnotation];\n            chartProto.collectionsWithUpdate.push('annotations');\n            chartProto.drawAnnotations = chartDrawAnnotations;\n            chartProto.removeAnnotation = chartRemoveAnnotation;\n            chartProto.initAnnotation = function chartInitAnnotation(userOptions) {\n                var Constructor = (AnnotationClass.types[userOptions.type] ||\n                        AnnotationClass),\n                    annotation = new Constructor(this,\n                    userOptions);\n                this.annotations.push(annotation);\n                return annotation;\n            };\n            wrap(pointerProto, 'onContainerMouseDown', wrapPointerOnContainerMouseDown);\n        }\n    }\n    AnnotationChart.compose = compose;\n})(AnnotationChart || (AnnotationChart = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Annotations_AnnotationChart = (AnnotationChart);\n\n;// ./code/es5/es-modules/Extensions/Annotations/AnnotationDefaults.js\n/* *\n *\n *  Imports\n *\n * */\n\nvar defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined;\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A basic type of an annotation. It allows to add custom labels\n * or shapes. The items can be tied to points, axis coordinates\n * or chart pixel coordinates.\n *\n * @sample highcharts/annotations/basic/\n *         Basic annotations\n * @sample highcharts/demo/annotations/\n *         Advanced annotations\n * @sample highcharts/css/annotations\n *         Styled mode\n * @sample highcharts/annotations-advanced/controllable\n *         Controllable items\n * @sample {highstock} stock/annotations/fibonacci-retracements\n *         Custom annotation, Fibonacci retracement\n *\n * @type         {Array<*>}\n * @since        6.0.0\n * @requires     modules/annotations\n * @optionparent annotations\n */\nvar AnnotationDefaults = {\n    /**\n     * Sets an ID for an annotation. Can be user later when\n     * removing an annotation in [Chart#removeAnnotation(id)](\n     * /class-reference/Highcharts.Chart#removeAnnotation) method.\n     *\n     * @type      {number|string}\n     * @apioption annotations.id\n     */\n    /**\n     * Whether the annotation is visible.\n     *\n     * @sample highcharts/annotations/visible/\n     *         Set annotation visibility\n     */\n    visible: true,\n    /**\n     * Enable or disable the initial animation when a series is\n     * displayed for the `annotation`. The animation can also be set\n     * as a configuration object. Please note that this option only\n     * applies to the initial animation.\n     * For other animations, see [chart.animation](#chart.animation)\n     * and the animation parameter under the API methods.\n     * The following properties are supported:\n     *\n     * - `defer`: The animation delay time in milliseconds.\n     *\n     * @sample {highcharts} highcharts/annotations/defer/\n     *          Animation defer settings\n     * @type {boolean|Partial<Highcharts.AnimationOptionsObject>}\n     * @since 8.2.0\n     */\n    animation: {},\n    /**\n     * Whether to hide the part of the annotation\n     * that is outside the plot area.\n     *\n     * @sample highcharts/annotations/label-crop-overflow/\n     *         Crop line annotation\n     * @type  {boolean}\n     * @since 9.3.0\n     */\n    crop: true,\n    /**\n     * The animation delay time in milliseconds.\n     * Set to `0` renders annotation immediately.\n     * As `undefined` inherits defer time from the [series.animation.defer](#plotOptions.series.animation.defer).\n     *\n     * @type      {number}\n     * @since 8.2.0\n     * @apioption annotations.animation.defer\n     */\n    /**\n     * Allow an annotation to be draggable by a user. Possible\n     * values are `'x'`, `'xy'`, `'y'` and `''` (disabled).\n     *\n     * @sample highcharts/annotations/draggable/\n     *         Annotations draggable: 'xy'\n     *\n     * @type {Highcharts.AnnotationDraggableValue}\n     */\n    draggable: 'xy',\n    /**\n     * Options for annotation's labels. Each label inherits options\n     * from the labelOptions object. An option from the labelOptions\n     * can be overwritten by config for a specific label.\n     *\n     * @requires modules/annotations\n     */\n    labelOptions: {\n        /**\n         * The alignment of the annotation's label. If right,\n         * the right side of the label should be touching the point.\n         *\n         * @sample highcharts/annotations/label-position/\n         *         Set labels position\n         *\n         * @type {Highcharts.AlignValue}\n         */\n        align: 'center',\n        /**\n         * Whether to allow the annotation's labels to overlap.\n         * To make the labels less sensitive for overlapping,\n         * the can be set to 0.\n         *\n         * @sample highcharts/annotations/tooltip-like/\n         *         Hide overlapping labels\n         */\n        allowOverlap: false,\n        /**\n         * The background color or gradient for the annotation's\n         * label.\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         *\n         * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         */\n        backgroundColor: 'rgba(0, 0, 0, 0.75)',\n        /**\n         * The border color for the annotation's label.\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         *\n         * @type {Highcharts.ColorString}\n         */\n        borderColor: \"#000000\" /* Palette.neutralColor100 */,\n        /**\n         * The border radius in pixels for the annotation's label.\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         */\n        borderRadius: 3,\n        /**\n         * The border width in pixels for the annotation's label\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         */\n        borderWidth: 1,\n        /**\n         * A class name for styling by CSS.\n         *\n         * @sample highcharts/css/annotations\n         *         Styled mode annotations\n         *\n         * @since 6.0.5\n         */\n        className: 'highcharts-no-tooltip',\n        /**\n         * Whether to hide the annotation's label\n         * that is outside the plot area.\n         *\n         * @sample highcharts/annotations/label-crop-overflow/\n         *         Crop or justify labels\n         */\n        crop: false,\n        /**\n         * The label's pixel distance from the point.\n         *\n         * @sample highcharts/annotations/label-position/\n         *         Set labels position\n         *\n         * @type      {number}\n         * @apioption annotations.labelOptions.distance\n         */\n        /**\n         * A\n         * [format](https://www.highcharts.com/docs/chart-concepts/labels-and-string-formatting)\n         * string for the data label.\n         *\n         * @see [plotOptions.series.dataLabels.format](plotOptions.series.dataLabels.format.html)\n         *\n         * @sample highcharts/annotations/label-text/\n         *         Set labels text\n         *\n         * @type      {string}\n         * @apioption annotations.labelOptions.format\n         */\n        /**\n         * Alias for the format option.\n         *\n         * @see [format](annotations.labelOptions.format.html)\n         *\n         * @sample highcharts/annotations/label-text/\n         *         Set labels text\n         *\n         * @type      {string}\n         * @apioption annotations.labelOptions.text\n         */\n        /**\n         * Callback JavaScript function to format the annotation's\n         * label. Note that if a `format` or `text` are defined,\n         * the format or text take precedence and the formatter is\n         * ignored. `This` refers to a point object.\n         *\n         * @sample highcharts/annotations/label-text/\n         *         Set labels text\n         *\n         * @type    {Highcharts.FormatterCallbackFunction<Highcharts.Point>}\n         * @default function () { return defined(this.y) ? this.y : 'Annotation label'; }\n         */\n        formatter: function () {\n            return defined(this.y) ? '' + this.y : 'Annotation label';\n        },\n        /**\n         * Whether the annotation is visible in the exported data\n         * table.\n         *\n         * @sample highcharts/annotations/include-in-data-export/\n         *         Do not include in the data export\n         *\n         * @since 8.2.0\n         * @requires modules/export-data\n         */\n        includeInDataExport: true,\n        /**\n         * How to handle the annotation's label that flow outside\n         * the plot area. The justify option aligns the label inside\n         * the plot area.\n         *\n         * @sample highcharts/annotations/label-crop-overflow/\n         *         Crop or justify labels\n         *\n         * @validvalue [\"allow\", \"justify\"]\n         */\n        overflow: 'justify',\n        /**\n         * When either the borderWidth or the backgroundColor is\n         * set, this is the padding within the box.\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         */\n        padding: 5,\n        /**\n         * The shadow of the box. The shadow can be an object\n         * configuration containing `color`, `offsetX`, `offsetY`,\n         * `opacity` and `width`.\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         *\n         * @type {boolean|Highcharts.ShadowOptionsObject}\n         */\n        shadow: false,\n        /**\n         * The name of a symbol to use for the border around the\n         * label. Symbols are predefined functions on the Renderer\n         * object.\n         *\n         * @sample highcharts/annotations/shapes/\n         *         Available shapes for labels\n         */\n        shape: 'callout',\n        /**\n         * Styles for the annotation's label.\n         *\n         * @see [plotOptions.series.dataLabels.style](plotOptions.series.dataLabels.style.html)\n         *\n         * @sample highcharts/annotations/label-presentation/\n         *         Set labels graphic options\n         *\n         * @type {Highcharts.CSSObject}\n         */\n        style: {\n            /** @ignore */\n            fontSize: '0.7em',\n            /** @ignore */\n            fontWeight: 'normal',\n            /** @ignore */\n            color: 'contrast'\n        },\n        /**\n         * Whether to [use HTML](https://www.highcharts.com/docs/chart-concepts/labels-and-string-formatting#html)\n         * to render the annotation's label.\n         */\n        useHTML: false,\n        /**\n         * The vertical alignment of the annotation's label.\n         *\n         * @sample highcharts/annotations/label-position/\n         *         Set labels position\n         *\n         * @type {Highcharts.VerticalAlignValue}\n         */\n        verticalAlign: 'bottom',\n        /**\n         * The x position offset of the label relative to the point.\n         * Note that if a `distance` is defined, the distance takes\n         * precedence over `x` and `y` options.\n         *\n         * @sample highcharts/annotations/label-position/\n         *         Set labels position\n         */\n        x: 0,\n        /**\n         * The y position offset of the label relative to the point.\n         * Note that if a `distance` is defined, the distance takes\n         * precedence over `x` and `y` options.\n         *\n         * @sample highcharts/annotations/label-position/\n         *         Set labels position\n         */\n        y: -16\n    },\n    /**\n     * An array of labels for the annotation. For options that apply\n     * to multiple labels, they can be added to the\n     * [labelOptions](annotations.labelOptions.html).\n     *\n     * @type      {Array<*>}\n     * @extends   annotations.labelOptions\n     * @apioption annotations.labels\n     */\n    /**\n     * This option defines the point to which the label will be\n     * connected. It can be either the point which exists in the\n     * series - it is referenced by the point's id - or a new point\n     * with defined x, y properties and optionally axes.\n     *\n     * @sample highcharts/annotations/mock-point/\n     *         Attach annotation to a mock point\n     * @sample highcharts/annotations/mock-points/\n     *         Attach annotation to a mock point with different ways\n     *\n     * @declare   Highcharts.AnnotationMockPointOptionsObject\n     * @type      {\n     *               string|\n     *               Highcharts.AnnotationMockPointOptionsObject|\n     *               Highcharts.AnnotationMockPointFunction\n     *            }\n     * @requires  modules/annotations\n     * @apioption annotations.labels.point\n     */\n    /**\n     * An array of shapes for the annotation. For options that apply\n     * to multiple shapes, then can be added to the\n     * [shapeOptions](annotations.shapeOptions.html).\n     *\n     * @type      {Array<*>}\n     * @extends   annotations.shapeOptions\n     * @apioption annotations.shapes\n     */\n    /**\n     * This option defines the point to which the shape will be\n     * connected. It can be either the point which exists in the\n     * series - it is referenced by the point's id - or a new point\n     * with defined x, y properties and optionally axes.\n     *\n     * @sample highcharts/annotations/mock-points/\n     *         Attach annotation to a mock point with different ways\n     *\n     * @declare   Highcharts.AnnotationMockPointOptionsObject\n     * @type      {\n     *               string|\n     *               Highcharts.AnnotationMockPointOptionsObject|\n     *               Highcharts.AnnotationMockPointFunction\n     *            }\n     * @extends   annotations.labels.point\n     * @requires  modules/annotations\n     * @apioption annotations.shapes.point\n     */\n    /**\n     * An array of points for the shape\n     * or a callback function that returns that shape point.\n     *\n     * This option is available\n     * for shapes which can use multiple points such as path. A\n     * point can be either a point object or a point's id.\n     *\n     * @see [annotations.shapes.point](annotations.shapes.point.html)\n     *\n     * @type      {Array<Highcharts.AnnotationShapePointOptions>}\n     * @extends   annotations.labels.point\n     * @apioption annotations.shapes.points\n     */\n    /**\n     * The URL for an image to use as the annotation shape. Note,\n     * type has to be set to `'image'`.\n     *\n     * @see [annotations.shapes.type](annotations.shapes.type)\n     * @sample highcharts/annotations/shape-src/\n     *         Define a marker image url for annotations\n     *\n     * @type      {string}\n     * @apioption annotations.shapes.src\n     */\n    /**\n     * Id of the marker which will be drawn at the final vertex of\n     * the path. Custom markers can be defined in defs property.\n     *\n     * @see [defs.markers](defs.markers.html)\n     *\n     * @sample highcharts/annotations/custom-markers/\n     *         Define a custom marker for annotations\n     *\n     * @type      {string}\n     * @apioption annotations.shapes.markerEnd\n     */\n    /**\n     * Id of the marker which will be drawn at the first vertex of\n     * the path. Custom markers can be defined in defs property.\n     *\n     * @see [defs.markers](defs.markers.html)\n     *\n     * @sample {highcharts} highcharts/annotations/custom-markers/\n     *         Define a custom marker for annotations\n     *\n     * @type      {string}\n     * @apioption annotations.shapes.markerStart\n     */\n    /**\n     * Options for annotation's shapes. Each shape inherits options\n     * from the shapeOptions object. An option from the shapeOptions\n     * can be overwritten by config for a specific shape.\n     *\n     * @requires  modules/annotations\n     */\n    shapeOptions: {\n        /**\n         *\n         * The radius of the shape in y direction.\n         * Used for the ellipse.\n         *\n         * @sample highcharts/annotations/ellipse/\n         *         Ellipse annotation\n         *\n         * @type      {number}\n         * @apioption annotations.shapeOptions.ry\n         **/\n        /**\n         *\n         * The xAxis index to which the points should be attached.\n         * Used for the ellipse.\n         *\n         * @type      {number}\n         * @apioption annotations.shapeOptions.xAxis\n         **/\n        /**\n         * The yAxis index to which the points should be attached.\n         * Used for the ellipse.\n         *\n         * @type      {number}\n         * @apioption annotations.shapeOptions.yAxis\n         **/\n        /**\n         * The width of the shape.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         *\n         * @type      {number}\n         * @apioption annotations.shapeOptions.width\n         **/\n        /**\n         * The height of the shape.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         *\n         * @type      {number}\n         * @apioption annotations.shapeOptions.height\n         */\n        /**\n         * The type of the shape.\n         * Available options are circle, rect and ellipse.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         *\n         * @sample highcharts/annotations/ellipse/\n         *         Ellipse annotation\n         *\n         * @type      {string}\n         * @default   rect\n         * @apioption annotations.shapeOptions.type\n         */\n        /**\n         * The URL for an image to use as the annotation shape.\n         * Note, type has to be set to `'image'`.\n         *\n         * @see [annotations.shapeOptions.type](annotations.shapeOptions.type)\n         * @sample highcharts/annotations/shape-src/\n         *         Define a marker image url for annotations\n         *\n         * @type      {string}\n         * @apioption annotations.shapeOptions.src\n         */\n        /**\n         * Name of the dash style to use for the shape's stroke.\n         *\n         * @sample {highcharts} highcharts/plotoptions/series-dashstyle-all/\n         *         Possible values demonstrated\n         *\n         * @type      {Highcharts.DashStyleValue}\n         * @apioption annotations.shapeOptions.dashStyle\n         */\n        /**\n         * The color of the shape's stroke.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         *\n         * @type {Highcharts.ColorString}\n         */\n        stroke: 'rgba(0, 0, 0, 0.75)',\n        /**\n         * The pixel stroke width of the shape.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         */\n        strokeWidth: 1,\n        /**\n         * The color of the shape's fill.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         *\n         * @type {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         */\n        fill: 'rgba(0, 0, 0, 0.75)',\n        /**\n         * The radius of the shape.\n         *\n         * @sample highcharts/annotations/shape/\n         *         Basic shape annotation\n         */\n        r: 0,\n        /**\n         * Defines additional snapping area around an annotation\n         * making this annotation to focus. Defined in pixels.\n         */\n        snap: 2\n    },\n    /**\n     * Options for annotation's control points. Each control point\n     * inherits options from controlPointOptions object.\n     * Options from the controlPointOptions can be overwritten\n     * by options in a specific control point.\n     *\n     * @declare  Highcharts.AnnotationControlPointOptionsObject\n     * @requires modules/annotations\n     */\n    controlPointOptions: {\n        /**\n         * @type      {Highcharts.AnnotationControlPointPositionerFunction}\n         * @apioption annotations.controlPointOptions.positioner\n         */\n        /**\n         * @type {Highcharts.Dictionary<Function>}\n         */\n        events: {},\n        /**\n         * @type {Highcharts.SVGAttributes}\n         */\n        style: {\n            cursor: 'pointer',\n            fill: \"#ffffff\" /* Palette.backgroundColor */,\n            stroke: \"#000000\" /* Palette.neutralColor100 */,\n            'stroke-width': 2\n        },\n        height: 10,\n        symbol: 'circle',\n        visible: false,\n        width: 10\n    },\n    /**\n     * Event callback when annotation is added to the chart.\n     *\n     * @type      {Highcharts.EventCallbackFunction<Highcharts.Annotation>}\n     * @since     7.1.0\n     * @apioption annotations.events.add\n     */\n    /**\n     * Event callback when annotation is updated (e.g. drag and\n     * dropped or resized by control points).\n     *\n     * @type      {Highcharts.EventCallbackFunction<Highcharts.Annotation>}\n     * @since     7.1.0\n     * @apioption annotations.events.afterUpdate\n     */\n    /**\n     * Fires when the annotation is clicked.\n     *\n     * @type      {Highcharts.EventCallbackFunction<Highcharts.Annotation>}\n     * @since     7.1.0\n     * @apioption annotations.events.click\n     */\n    /**\n     * Fires when the annotation is dragged.\n     *\n     * @type      {Highcharts.EventCallbackFunction<Highcharts.Annotation>}\n     * @apioption annotations.events.drag\n     */\n    /**\n     * Event callback when annotation is removed from the chart.\n     *\n     * @type      {Highcharts.EventCallbackFunction<Highcharts.Annotation>}\n     * @since     7.1.0\n     * @apioption annotations.events.remove\n     */\n    /**\n     * Events available in annotations.\n     *\n     * @requires modules/annotations\n     */\n    events: {},\n    /**\n     * The Z index of the annotation.\n     */\n    zIndex: 6\n}; // Type options are expected but not set\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Annotations_AnnotationDefaults = (AnnotationDefaults);\n\n;// ./code/es5/es-modules/Extensions/Annotations/EventEmitter.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar doc = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).doc, isTouchDevice = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isTouchDevice;\n\nvar EventEmitter_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, EventEmitter_fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, objectEach = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).objectEach, EventEmitter_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, removeEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).removeEvent;\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n */\nvar EventEmitter = /** @class */ (function () {\n    function EventEmitter() {\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Add emitter events.\n     * @private\n     */\n    EventEmitter.prototype.addEvents = function () {\n        var emitter = this,\n            addMouseDownEvent = function (element) {\n                EventEmitter_addEvent(element,\n            isTouchDevice ? 'touchstart' : 'mousedown',\n            function (e) {\n                    emitter.onMouseDown(e);\n            }, { passive: false });\n        };\n        addMouseDownEvent(this.graphic.element);\n        (emitter.labels || []).forEach(function (label) {\n            if (label.options.useHTML &&\n                label.graphic.text &&\n                !label.graphic.text.foreignObject) {\n                // Mousedown event bound to HTML element (#13070).\n                addMouseDownEvent(label.graphic.text.element);\n            }\n        });\n        objectEach(emitter.options.events, function (event, type) {\n            var eventHandler = function (e) {\n                    var _a;\n                if (type !== 'click' || !emitter.cancelClick) {\n                    event.call(emitter, (_a = emitter.chart.pointer) === null || _a === void 0 ? void 0 : _a.normalize(e), emitter.target);\n                }\n            };\n            if ((emitter.nonDOMEvents || []).indexOf(type) === -1) {\n                EventEmitter_addEvent(emitter.graphic.element, type, eventHandler, { passive: false });\n                if (emitter.graphic.div) {\n                    EventEmitter_addEvent(emitter.graphic.div, type, eventHandler, { passive: false });\n                }\n            }\n            else {\n                EventEmitter_addEvent(emitter, type, eventHandler, { passive: false });\n            }\n        });\n        if (emitter.options.draggable) {\n            EventEmitter_addEvent(emitter, 'drag', emitter.onDrag);\n            if (!emitter.graphic.renderer.styledMode) {\n                var cssPointer_1 = {\n                        cursor: {\n                            x: 'ew-resize',\n                            y: 'ns-resize',\n                            xy: 'move'\n                        }[emitter.options.draggable]\n                    };\n                emitter.graphic.css(cssPointer_1);\n                (emitter.labels || []).forEach(function (label) {\n                    if (label.options.useHTML &&\n                        label.graphic.text &&\n                        !label.graphic.text.foreignObject) {\n                        label.graphic.text.css(cssPointer_1);\n                    }\n                });\n            }\n        }\n        if (!emitter.isUpdating) {\n            EventEmitter_fireEvent(emitter, 'add');\n        }\n    };\n    /**\n     * Destroy the event emitter.\n     */\n    EventEmitter.prototype.destroy = function () {\n        this.removeDocEvents();\n        removeEvent(this);\n        this.hcEvents = null;\n    };\n    /**\n     * Map mouse move event to the radians.\n     * @private\n     */\n    EventEmitter.prototype.mouseMoveToRadians = function (e, cx, cy) {\n        var prevDy = e.prevChartY - cy,\n            prevDx = e.prevChartX - cx,\n            dy = e.chartY - cy,\n            dx = e.chartX - cx,\n            temp;\n        if (this.chart.inverted) {\n            temp = prevDx;\n            prevDx = prevDy;\n            prevDy = temp;\n            temp = dx;\n            dx = dy;\n            dy = temp;\n        }\n        return Math.atan2(dy, dx) - Math.atan2(prevDy, prevDx);\n    };\n    /**\n     * Map mouse move to the scale factors.\n     * @private\n     */\n    EventEmitter.prototype.mouseMoveToScale = function (e, cx, cy) {\n        var prevDx = e.prevChartX - cx,\n            prevDy = e.prevChartY - cy,\n            dx = e.chartX - cx,\n            dy = e.chartY - cy;\n        var sx = (dx || 1) / (prevDx || 1), sy = (dy || 1) / (prevDy || 1);\n        if (this.chart.inverted) {\n            var temp = sy;\n            sy = sx;\n            sx = temp;\n        }\n        return {\n            x: sx,\n            y: sy\n        };\n    };\n    /**\n     * Map mouse move event to the distance between two following events.\n     * @private\n     */\n    EventEmitter.prototype.mouseMoveToTranslation = function (e) {\n        var dx = e.chartX - e.prevChartX,\n            dy = e.chartY - e.prevChartY,\n            temp;\n        if (this.chart.inverted) {\n            temp = dy;\n            dy = dx;\n            dx = temp;\n        }\n        return {\n            x: dx,\n            y: dy\n        };\n    };\n    /**\n     * Drag and drop event. All basic annotations should share this\n     * capability as well as the extended ones.\n     * @private\n     */\n    EventEmitter.prototype.onDrag = function (e) {\n        if (this.chart.isInsidePlot(e.chartX - this.chart.plotLeft, e.chartY - this.chart.plotTop, {\n            visiblePlotOnly: true\n        })) {\n            var translation_1 = this.mouseMoveToTranslation(e);\n            if (this.options.draggable === 'x') {\n                translation_1.y = 0;\n            }\n            if (this.options.draggable === 'y') {\n                translation_1.x = 0;\n            }\n            var emitter = this;\n            if (emitter.points.length) {\n                emitter.translate(translation_1.x, translation_1.y);\n            }\n            else {\n                emitter.shapes.forEach(function (shape) {\n                    return shape.translate(translation_1.x, translation_1.y);\n                });\n                emitter.labels.forEach(function (label) {\n                    return label.translate(translation_1.x, translation_1.y);\n                });\n            }\n            this.redraw(false);\n        }\n    };\n    /**\n     * Mouse down handler.\n     * @private\n     */\n    EventEmitter.prototype.onMouseDown = function (e) {\n        var _a;\n        if (e.preventDefault) {\n            e.preventDefault();\n        }\n        // On right click, do nothing:\n        if (e.button === 2) {\n            return;\n        }\n        var emitter = this,\n            pointer = emitter.chart.pointer, \n            // Using experimental property on event object to check if event was\n            // created by touch on screen on hybrid device (#18122)\n            firesTouchEvents = ((_a = e === null || e === void 0 ? void 0 : e.sourceCapabilities) === null || _a === void 0 ? void 0 : _a.firesTouchEvents) || false;\n        e = (pointer === null || pointer === void 0 ? void 0 : pointer.normalize(e)) || e;\n        var prevChartX = e.chartX,\n            prevChartY = e.chartY;\n        emitter.cancelClick = false;\n        emitter.chart.hasDraggedAnnotation = true;\n        emitter.removeDrag = EventEmitter_addEvent(doc, isTouchDevice || firesTouchEvents ? 'touchmove' : 'mousemove', function (e) {\n            emitter.hasDragged = true;\n            e = (pointer === null || pointer === void 0 ? void 0 : pointer.normalize(e)) || e;\n            e.prevChartX = prevChartX;\n            e.prevChartY = prevChartY;\n            EventEmitter_fireEvent(emitter, 'drag', e);\n            prevChartX = e.chartX;\n            prevChartY = e.chartY;\n        }, isTouchDevice || firesTouchEvents ? { passive: false } : void 0);\n        emitter.removeMouseUp = EventEmitter_addEvent(doc, isTouchDevice || firesTouchEvents ? 'touchend' : 'mouseup', function () {\n            // Sometimes the target is the annotation and sometimes its the\n            // controllable\n            var annotation = EventEmitter_pick(emitter.target && emitter.target.annotation,\n                emitter.target);\n            if (annotation) {\n                // Keep annotation selected after dragging control point\n                annotation.cancelClick = emitter.hasDragged;\n            }\n            emitter.cancelClick = emitter.hasDragged;\n            emitter.chart.hasDraggedAnnotation = false;\n            if (emitter.hasDragged) {\n                // ControlPoints vs Annotation:\n                EventEmitter_fireEvent(EventEmitter_pick(annotation, // #15952\n                emitter), 'afterUpdate');\n            }\n            emitter.hasDragged = false;\n            emitter.onMouseUp();\n        }, isTouchDevice || firesTouchEvents ? { passive: false } : void 0);\n    };\n    /**\n     * Mouse up handler.\n     */\n    EventEmitter.prototype.onMouseUp = function () {\n        this.removeDocEvents();\n    };\n    /**\n     * Remove emitter document events.\n     * @private\n     */\n    EventEmitter.prototype.removeDocEvents = function () {\n        if (this.removeDrag) {\n            this.removeDrag = this.removeDrag();\n        }\n        if (this.removeMouseUp) {\n            this.removeMouseUp = this.removeMouseUp();\n        }\n    };\n    return EventEmitter;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Annotations_EventEmitter = (EventEmitter);\n\n;// ./code/es5/es-modules/Extensions/Annotations/ControlPoint.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\nvar merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, ControlPoint_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Class\n *\n * */\n/**\n * A control point class which is a connection between controllable\n * transform methods and a user actions.\n *\n * @requires modules/annotations\n *\n * @class\n * @name Highcharts.AnnotationControlPoint\n *\n * @hideconstructor\n *\n * @param {Highcharts.Chart} chart\n * A chart instance.\n *\n * @param {Highcharts.AnnotationControllable} target\n * A controllable instance which is a target for a control point.\n *\n * @param {Highcharts.AnnotationControlPointOptionsObject} options\n * An options object.\n *\n * @param {number} [index]\n * Point index.\n */\nvar ControlPoint = /** @class */ (function (_super) {\n    __extends(ControlPoint, _super);\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    function ControlPoint(chart, target, options, index) {\n        var _this = _super.call(this) || this;\n        /**\n         * List of events for `annotation.options.events` that should not be\n         * added to `annotation.graphic` but to the `annotation`.\n         * @private\n         * @name Highcharts.AnnotationControlPoint#nonDOMEvents\n         * @type {Array<string>}\n         */\n        _this.nonDOMEvents = ['drag'];\n        _this.chart = chart;\n        _this.target = target;\n        _this.options = options;\n        _this.index = ControlPoint_pick(options.index, index);\n        return _this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Destroy the control point.\n     * @private\n     */\n    ControlPoint.prototype.destroy = function () {\n        _super.prototype.destroy.call(this);\n        if (this.graphic) {\n            this.graphic = this.graphic.destroy();\n        }\n        this.chart = null;\n        this.target = null;\n        this.options = null;\n    };\n    /**\n     * Redraw the control point.\n     * @private\n     * @param {boolean} [animation]\n     */\n    ControlPoint.prototype.redraw = function (animation) {\n        this.graphic[animation ? 'animate' : 'attr'](this.options.positioner.call(this, this.target));\n    };\n    /**\n     * Render the control point.\n     * @private\n     */\n    ControlPoint.prototype.render = function () {\n        var chart = this.chart,\n            options = this.options;\n        this.graphic = chart.renderer\n            .symbol(options.symbol, 0, 0, options.width, options.height)\n            .add(chart.controlPointsGroup)\n            .css(options.style);\n        this.setVisibility(options.visible);\n        // `npm test -- --tests \"@highcharts/highcharts/annotations-advanced/*\"`\n        this.addEvents();\n    };\n    /**\n     * Set the visibility of the control point.\n     *\n     * @function Highcharts.AnnotationControlPoint#setVisibility\n     *\n     * @param {boolean} visible\n     * Visibility of the control point.\n     *\n     */\n    ControlPoint.prototype.setVisibility = function (visible) {\n        this.graphic[visible ? 'show' : 'hide']();\n        this.options.visible = visible;\n    };\n    /**\n     * Update the control point.\n     *\n     * @function Highcharts.AnnotationControlPoint#update\n     *\n     * @param {Partial<Highcharts.AnnotationControlPointOptionsObject>} userOptions\n     * New options for the control point.\n     */\n    ControlPoint.prototype.update = function (userOptions) {\n        var chart = this.chart,\n            target = this.target,\n            index = this.index,\n            options = merge(true,\n            this.options,\n            userOptions);\n        this.destroy();\n        this.constructor(chart, target, options, index);\n        this.render(chart.controlPointsGroup);\n        this.redraw();\n    };\n    return ControlPoint;\n}(Annotations_EventEmitter));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Annotations_ControlPoint = (ControlPoint);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Callback to modify annotation's positioner controls.\n *\n * @callback Highcharts.AnnotationControlPointPositionerFunction\n * @param {Highcharts.AnnotationControlPoint} this\n * @param {Highcharts.AnnotationControllable} target\n * @return {Highcharts.PositionObject}\n */\n(''); // Keeps doclets above in JS file\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es5/es-modules/Extensions/Annotations/MockPoint.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar seriesProto = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).series.prototype;\n\nvar MockPoint_defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, MockPoint_fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent;\n/* *\n *\n *  Class\n *\n * */\n/**\n * A trimmed point object which imitates {@link Highchart.Point} class. It is\n * created when there is a need of pointing to some chart's position using axis\n * values or pixel values\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationMockPoint\n *\n * @hideconstructor\n *\n * @param {Highcharts.Chart} chart\n * The chart instance.\n *\n * @param {Highcharts.AnnotationControllable|null} target\n * The related controllable.\n *\n * @param {Highcharts.AnnotationMockPointOptionsObject|Function} options\n * The options object.\n */\nvar MockPoint = /** @class */ (function () {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    function MockPoint(chart, target, options) {\n        /* *\n         *\n         * Functions\n         *\n         * */\n        /**\n         * A flag indicating that a point is not the real one.\n         *\n         * @type {boolean}\n         * @default true\n         */\n        this.mock = true;\n        // Circular reference for formats and formatters\n        this.point = this;\n        /**\n         * A mock series instance imitating a real series from a real point.\n         *\n         * @name Annotation.AnnotationMockPoint#series\n         * @type {Highcharts.AnnotationMockSeries}\n         */\n        this.series = {\n            visible: true,\n            chart: chart,\n            getPlotBox: seriesProto.getPlotBox\n        };\n        /**\n         * @name Annotation.AnnotationMockPoint#target\n         * @type {Highcharts.AnnotationControllable|null}\n         */\n        this.target = target || null;\n        /**\n         * Options for the mock point.\n         *\n         * @name Annotation.AnnotationMockPoint#options\n         * @type {Highcharts.AnnotationsMockPointOptionsObject}\n         */\n        this.options = options;\n        /**\n         * If an xAxis is set it represents the point's value in terms of the\n         * xAxis.\n         *\n         * @name Annotation.AnnotationMockPoint#x\n         * @type {number|undefined}\n         */\n        /**\n         * If an yAxis is set it represents the point's value in terms of the\n         * yAxis.\n         *\n         * @name Annotation.AnnotationMockPoint#y\n         * @type {number|undefined}\n         */\n        /**\n         * It represents the point's pixel x coordinate relative to its plot\n         * box.\n         *\n         * @name Annotation.AnnotationMockPoint#plotX\n         * @type {number|undefined}\n         */\n        /**\n         * It represents the point's pixel y position relative to its plot box.\n         *\n         * @name Annotation.AnnotationMockPoint#plotY\n         * @type {number|undefined}\n         */\n        /**\n         * Whether the point is inside the plot box.\n         *\n         * @name Annotation.AnnotationMockPoint#isInside\n         * @type {boolean|undefined}\n         */\n        this.applyOptions(this.getOptions());\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Create a mock point from a real Highcharts point.\n     *\n     * @private\n     * @static\n     *\n     * @param {Highcharts.Point} point\n     *\n     * @return {Highcharts.AnnotationMockPoint}\n     * A mock point instance.\n     */\n    MockPoint.fromPoint = function (point) {\n        return new MockPoint(point.series.chart, null, {\n            x: point.x,\n            y: point.y,\n            xAxis: point.series.xAxis,\n            yAxis: point.series.yAxis\n        });\n    };\n    /**\n     * Get the pixel position from the point like object.\n     *\n     * @private\n     * @static\n     *\n     * @param {Highcharts.AnnotationPointType} point\n     *\n     * @param {boolean} [paneCoordinates]\n     *        Whether the pixel position should be relative\n     *\n     * @return {Highcharts.PositionObject} pixel position\n     */\n    MockPoint.pointToPixels = function (point, paneCoordinates) {\n        var series = point.series,\n            chart = series.chart;\n        var x = point.plotX || 0,\n            y = point.plotY || 0,\n            plotBox;\n        if (chart.inverted) {\n            if (point.mock) {\n                x = point.plotY;\n                y = point.plotX;\n            }\n            else {\n                x = chart.plotWidth - (point.plotY || 0);\n                y = chart.plotHeight - (point.plotX || 0);\n            }\n        }\n        if (series && !paneCoordinates) {\n            plotBox = series.getPlotBox();\n            x += plotBox.translateX;\n            y += plotBox.translateY;\n        }\n        return {\n            x: x,\n            y: y\n        };\n    };\n    /**\n     * Get fresh mock point options from the point like object.\n     *\n     * @private\n     * @static\n     *\n     * @param {Highcharts.AnnotationPointType} point\n     *\n     * @return {Highcharts.AnnotationMockPointOptionsObject}\n     * A mock point's options.\n     */\n    MockPoint.pointToOptions = function (point) {\n        return {\n            x: point.x,\n            y: point.y,\n            xAxis: point.series.xAxis,\n            yAxis: point.series.yAxis\n        };\n    };\n    /**\n     * Apply options for the point.\n     * @private\n     * @param {Highcharts.AnnotationMockPointOptionsObject} options\n     */\n    MockPoint.prototype.applyOptions = function (options) {\n        this.command = options.command;\n        this.setAxis(options, 'x');\n        this.setAxis(options, 'y');\n        this.refresh();\n    };\n    /**\n     * Get the point's options.\n     * @private\n     * @return {Highcharts.AnnotationMockPointOptionsObject}\n     * The mock point's options.\n     */\n    MockPoint.prototype.getOptions = function () {\n        return this.hasDynamicOptions() ?\n            this.options(this.target) :\n            this.options;\n    };\n    /**\n     * Check if the point has dynamic options.\n     * @private\n     * @return {boolean}\n     * A positive flag if the point has dynamic options.\n     */\n    MockPoint.prototype.hasDynamicOptions = function () {\n        return typeof this.options === 'function';\n    };\n    /**\n     * Check if the point is inside its pane.\n     * @private\n     * @return {boolean} A flag indicating whether the point is inside the pane.\n     */\n    MockPoint.prototype.isInsidePlot = function () {\n        var plotX = this.plotX,\n            plotY = this.plotY,\n            xAxis = this.series.xAxis,\n            yAxis = this.series.yAxis,\n            e = {\n                x: plotX,\n                y: plotY,\n                isInsidePlot: true,\n                options: {}\n            };\n        if (xAxis) {\n            e.isInsidePlot = MockPoint_defined(plotX) && plotX >= 0 && plotX <= xAxis.len;\n        }\n        if (yAxis) {\n            e.isInsidePlot =\n                e.isInsidePlot &&\n                    MockPoint_defined(plotY) &&\n                    plotY >= 0 && plotY <= yAxis.len;\n        }\n        MockPoint_fireEvent(this.series.chart, 'afterIsInsidePlot', e);\n        return e.isInsidePlot;\n    };\n    /**\n     * Refresh point values and coordinates based on its options.\n     * @private\n     */\n    MockPoint.prototype.refresh = function () {\n        var series = this.series,\n            xAxis = series.xAxis,\n            yAxis = series.yAxis,\n            options = this.getOptions();\n        if (xAxis) {\n            this.x = options.x;\n            this.plotX = xAxis.toPixels(options.x, true);\n        }\n        else {\n            this.x = void 0;\n            this.plotX = options.x;\n        }\n        if (yAxis) {\n            this.y = options.y;\n            this.plotY = yAxis.toPixels(options.y, true);\n        }\n        else {\n            this.y = null;\n            this.plotY = options.y;\n        }\n        this.isInside = this.isInsidePlot();\n    };\n    /**\n     * Refresh point options based on its plot coordinates.\n     * @private\n     */\n    MockPoint.prototype.refreshOptions = function () {\n        var series = this.series,\n            xAxis = series.xAxis,\n            yAxis = series.yAxis;\n        this.x = this.options.x = xAxis ?\n            this.options.x = xAxis.toValue(this.plotX, true) :\n            this.plotX;\n        this.y = this.options.y = yAxis ?\n            yAxis.toValue(this.plotY, true) :\n            this.plotY;\n    };\n    /**\n     * Rotate the point.\n     * @private\n     * @param {number} cx origin x rotation\n     * @param {number} cy origin y rotation\n     * @param {number} radians\n     */\n    MockPoint.prototype.rotate = function (cx, cy, radians) {\n        if (!this.hasDynamicOptions()) {\n            var cos = Math.cos(radians),\n                sin = Math.sin(radians),\n                x = this.plotX - cx,\n                y = this.plotY - cy,\n                tx = x * cos - y * sin,\n                ty = x * sin + y * cos;\n            this.plotX = tx + cx;\n            this.plotY = ty + cy;\n            this.refreshOptions();\n        }\n    };\n    /**\n     * Scale the point.\n     *\n     * @private\n     *\n     * @param {number} cx\n     * Origin x transformation.\n     *\n     * @param {number} cy\n     * Origin y transformation.\n     *\n     * @param {number} sx\n     * Scale factor x.\n     *\n     * @param {number} sy\n     * Scale factor y.\n     */\n    MockPoint.prototype.scale = function (cx, cy, sx, sy) {\n        if (!this.hasDynamicOptions()) {\n            var x = this.plotX * sx,\n                y = this.plotY * sy,\n                tx = (1 - sx) * cx,\n                ty = (1 - sy) * cy;\n            this.plotX = tx + x;\n            this.plotY = ty + y;\n            this.refreshOptions();\n        }\n    };\n    /**\n     * Set x or y axis.\n     * @private\n     * @param {Highcharts.AnnotationMockPointOptionsObject} options\n     * @param {string} xOrY\n     * 'x' or 'y' string literal\n     */\n    MockPoint.prototype.setAxis = function (options, xOrY) {\n        var axisName = (xOrY + 'Axis'),\n            axisOptions = options[axisName],\n            chart = this.series.chart;\n        this.series[axisName] =\n            typeof axisOptions === 'object' ?\n                axisOptions :\n                MockPoint_defined(axisOptions) ?\n                    (chart[axisName][axisOptions] ||\n                        // @todo v--- (axisName)[axisOptions] ?\n                        chart.get(axisOptions)) :\n                    null;\n    };\n    /**\n     * Transform the mock point to an anchor (relative position on the chart).\n     * @private\n     * @return {Array<number>}\n     * A quadruple of numbers which denotes x, y, width and height of the box\n     **/\n    MockPoint.prototype.toAnchor = function () {\n        var anchor = [this.plotX,\n            this.plotY, 0, 0];\n        if (this.series.chart.inverted) {\n            anchor[0] = this.plotY;\n            anchor[1] = this.plotX;\n        }\n        return anchor;\n    };\n    /**\n     * Translate the point.\n     *\n     * @private\n     *\n     * @param {number|undefined} cx\n     * Origin x transformation.\n     *\n     * @param {number|undefined} cy\n     * Origin y transformation.\n     *\n     * @param {number} dx\n     * Translation for x coordinate.\n     *\n     * @param {number} dy\n     * Translation for y coordinate.\n     **/\n    MockPoint.prototype.translate = function (_cx, _cy, dx, dy) {\n        if (!this.hasDynamicOptions()) {\n            this.plotX += dx;\n            this.plotY += dy;\n            this.refreshOptions();\n        }\n    };\n    return MockPoint;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Annotations_MockPoint = (MockPoint);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * @private\n * @interface Highcharts.AnnotationMockLabelOptionsObject\n */ /**\n* Point instance of the point.\n* @name Highcharts.AnnotationMockLabelOptionsObject#point\n* @type {Highcharts.AnnotationMockPoint}\n*/ /**\n* X value translated to x axis scale.\n* @name Highcharts.AnnotationMockLabelOptionsObject#x\n* @type {number|null}\n*/ /**\n* Y value translated to y axis scale.\n* @name Highcharts.AnnotationMockLabelOptionsObject#y\n* @type {number|null}\n*/\n/**\n * Object of shape point.\n *\n * @interface Highcharts.AnnotationMockPointOptionsObject\n */ /**\n* The x position of the point. Units can be either in axis\n* or chart pixel coordinates.\n*\n* @type      {number}\n* @name      Highcharts.AnnotationMockPointOptionsObject.x\n*/ /**\n* The y position of the point. Units can be either in axis\n* or chart pixel coordinates.\n*\n* @type      {number}\n* @name      Highcharts.AnnotationMockPointOptionsObject.y\n*/ /**\n* This number defines which xAxis the point is connected to.\n* It refers to either the axis id or the index of the axis in\n* the xAxis array. If the option is not configured or the axis\n* is not found the point's x coordinate refers to the chart\n* pixels.\n*\n* @type      {number|string|null}\n* @name      Highcharts.AnnotationMockPointOptionsObject.xAxis\n*/ /**\n* This number defines which yAxis the point is connected to.\n* It refers to either the axis id or the index of the axis in\n* the yAxis array. If the option is not configured or the axis\n* is not found the point's y coordinate refers to the chart\n* pixels.\n*\n* @type      {number|string|null}\n* @name      Highcharts.AnnotationMockPointOptionsObject.yAxis\n*/\n/**\n * Callback function that returns the annotation shape point.\n *\n * @callback Highcharts.AnnotationMockPointFunction\n *\n * @param  {Highcharts.Annotation} annotation\n *         An annotation instance.\n *\n * @return {Highcharts.AnnotationMockPointOptionsObject}\n *         Annotations shape point.\n */\n/**\n * A mock series instance imitating a real series from a real point.\n * @private\n * @interface Highcharts.AnnotationMockSeries\n */ /**\n* Whether a series is visible.\n* @name Highcharts.AnnotationMockSeries#visible\n* @type {boolean}\n*/ /**\n* A chart instance.\n* @name Highcharts.AnnotationMockSeries#chart\n* @type {Highcharts.Chart}\n*/ /**\n* @name Highcharts.AnnotationMockSeries#getPlotBox\n* @type {Function}\n*/\n/**\n * Indicates if this is a mock point for an annotation.\n * @name Highcharts.Point#mock\n * @type {boolean|undefined}\n */\n(''); // Keeps doclets above in JS file\n\n;// ./code/es5/es-modules/Extensions/Annotations/ControlTarget.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n/* *\n *\n *  Composition Namespace\n *\n * */\nvar ControlTarget;\n(function (ControlTarget) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Add control points.\n     * @private\n     */\n    function addControlPoints() {\n        var _this = this;\n        var controlPoints = this.controlPoints,\n            controlPointsOptions = this.options.controlPoints || [];\n        controlPointsOptions.forEach(function (controlPointOptions, i) {\n            var options = highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().merge(_this.options.controlPointOptions,\n                controlPointOptions);\n            if (!options.index) {\n                options.index = i;\n            }\n            controlPointsOptions[i] = options;\n            controlPoints.push(new Annotations_ControlPoint(_this.chart, _this, options));\n        });\n    }\n    /**\n     * Returns object which denotes anchor position - relative and absolute.\n     * @private\n     * @param {Highcharts.AnnotationPointType} point\n     * An annotation point.\n     *\n     * @return {Highcharts.AnnotationAnchorObject}\n     * An annotation anchor.\n     */\n    function anchor(point) {\n        var plotBox = point.series.getPlotBox(),\n            chart = point.series.chart,\n            box = point.mock ?\n                point.toAnchor() :\n                chart.tooltip &&\n                    chart.tooltip.getAnchor.call({\n                        chart: point.series.chart\n                    },\n            point) ||\n                    [0, 0, 0, 0],\n            anchor = {\n                x: box[0] + (this.options.x || 0),\n                y: box[1] + (this.options.y || 0),\n                height: box[2] || 0,\n                width: box[3] || 0\n            };\n        return {\n            relativePosition: anchor,\n            absolutePosition: highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().merge(anchor, {\n                x: anchor.x + (point.mock ? plotBox.translateX : chart.plotLeft),\n                y: anchor.y + (point.mock ? plotBox.translateY : chart.plotTop)\n            })\n        };\n    }\n    /**\n     * Adds shared functions to be used with targets of ControlPoint.\n     * @private\n     */\n    function compose(ControlTargetClass) {\n        var controlProto = ControlTargetClass.prototype;\n        if (!controlProto.addControlPoints) {\n            highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().merge(true, controlProto, {\n                addControlPoints: addControlPoints,\n                anchor: anchor,\n                destroyControlTarget: destroyControlTarget,\n                getPointsOptions: getPointsOptions,\n                linkPoints: linkPoints,\n                point: point,\n                redrawControlPoints: redrawControlPoints,\n                renderControlPoints: renderControlPoints,\n                transform: transform,\n                transformPoint: transformPoint,\n                translate: translate,\n                translatePoint: translatePoint\n            });\n        }\n    }\n    ControlTarget.compose = compose;\n    /**\n     * Destroy control points.\n     * @private\n     */\n    function destroyControlTarget() {\n        this.controlPoints.forEach(function (controlPoint) { return controlPoint.destroy(); });\n        this.chart = null;\n        this.controlPoints = null;\n        this.points = null;\n        this.options = null;\n        if (this.annotation) {\n            this.annotation = null;\n        }\n    }\n    /**\n     * Get the points options.\n     * @private\n     * @return {Array<Highcharts.PointOptionsObject>}\n     * An array of points' options.\n     */\n    function getPointsOptions() {\n        var options = this.options;\n        return (options.points ||\n            (options.point && highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().splat(options.point)));\n    }\n    /**\n     * Find point-like objects based on points options.\n     * @private\n     * @return {Array<Annotation.PointLike>}\n     *         An array of point-like objects.\n     */\n    function linkPoints() {\n        var pointsOptions = this.getPointsOptions(),\n            points = this.points,\n            len = (pointsOptions && pointsOptions.length) || 0;\n        var i,\n            point;\n        for (i = 0; i < len; i++) {\n            point = this.point(pointsOptions[i], points[i]);\n            if (!point) {\n                points.length = 0;\n                return;\n            }\n            if (point.mock) {\n                point.refresh();\n            }\n            points[i] = point;\n        }\n        return points;\n    }\n    /**\n     * Map point's options to a point-like object.\n     * @private\n     * @param {string|Function|Highcharts.AnnotationMockPointOptionsObject|Highcharts.AnnotationPointType} pointOptions\n     *        Point's options.\n     * @param {Highcharts.AnnotationPointType} point\n     *        A point-like instance.\n     * @return {Highcharts.AnnotationPointType|null}\n     *         If the point is found/set returns this point, otherwise null\n     */\n    function point(pointOptions, point) {\n        if (pointOptions && pointOptions.series) {\n            return pointOptions;\n        }\n        if (!point || point.series === null) {\n            if (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().isObject(pointOptions)) {\n                point = new Annotations_MockPoint(this.chart, this, pointOptions);\n            }\n            else if (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().isString(pointOptions)) {\n                point = this.chart.get(pointOptions) || null;\n            }\n            else if (typeof pointOptions === 'function') {\n                var pointConfig = pointOptions.call(point,\n                    this);\n                point = pointConfig.series ?\n                    pointConfig :\n                    new Annotations_MockPoint(this.chart, this, pointOptions);\n            }\n        }\n        return point;\n    }\n    /**\n     * Redraw control points.\n     * @private\n     */\n    function redrawControlPoints(animation) {\n        this.controlPoints.forEach(function (controlPoint) { return controlPoint.redraw(animation); });\n    }\n    /**\n     * Render control points.\n     * @private\n     */\n    function renderControlPoints() {\n        this.controlPoints.forEach(function (controlPoint) { return controlPoint.render(); });\n    }\n    /**\n     * Transform control points with a specific transformation.\n     * @private\n     * @param {string} transformation\n     *        A transformation name\n     * @param {number|null} cx\n     *        Origin x transformation\n     * @param {number|null} cy\n     *        Origin y transformation\n     * @param {number} p1\n     *        Param for the transformation\n     * @param {number} [p2]\n     *        Param for the transformation\n     */\n    function transform(transformation, cx, cy, p1, p2) {\n        var _this = this;\n        if (this.chart.inverted) {\n            var temp = cx;\n            cx = cy;\n            cy = temp;\n        }\n        this.points.forEach(function (_point, i) { return (_this.transformPoint(transformation, cx, cy, p1, p2, i)); }, this);\n    }\n    /**\n     * Transform a point with a specific transformation\n     * If a transformed point is a real point it is replaced with\n     * the mock point.\n     * @private\n     * @param {string} transformation\n     *        A transformation name\n     * @param {number|null} cx\n     *        Origin x transformation\n     * @param {number|null} cy\n     *        Origin y transformation\n     * @param {number} p1\n     *        Param for the transformation\n     * @param {number|undefined} p2\n     *        Param for the transformation\n     * @param {number} i\n     *        Index of the point\n     */\n    function transformPoint(transformation, cx, cy, p1, p2, i) {\n        var point = this.points[i];\n        if (!point.mock) {\n            point = this.points[i] = Annotations_MockPoint.fromPoint(point);\n        }\n        point[transformation](cx, cy, p1, p2);\n    }\n    /**\n     * Translate control points.\n     * @private\n     * @param {number} dx\n     *        Translation for x coordinate\n     * @param {number} dy\n     *        Translation for y coordinate\n     **/\n    function translate(dx, dy) {\n        this.transform('translate', null, null, dx, dy);\n    }\n    /**\n     * Translate a specific control point.\n     * @private\n     * @param {number} dx\n     *        Translation for x coordinate\n     * @param {number} dy\n     *        Translation for y coordinate\n     * @param {number} i\n     *        Index of the point\n     **/\n    function translatePoint(dx, dy, i) {\n        this.transformPoint('translate', null, null, dx, dy, i);\n    }\n})(ControlTarget || (ControlTarget = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Annotations_ControlTarget = (ControlTarget);\n\n;// ./code/es5/es-modules/Extensions/Annotations/Controllables/Controllable.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nvar Controllable_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n *  Class\n *\n * */\n/**\n * It provides methods for handling points, control points\n * and points transformations.\n * @private\n */\nvar Controllable = /** @class */ (function () {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    function Controllable(annotation, options, index, itemType) {\n        this.annotation = annotation;\n        this.chart = annotation.chart;\n        this.collection = (itemType === 'label' ? 'labels' : 'shapes');\n        this.controlPoints = [];\n        this.options = options;\n        this.points = [];\n        this.index = index;\n        this.itemType = itemType;\n        this.init(annotation, options, index);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Redirect attr usage on the controllable graphic element.\n     * @private\n     */\n    Controllable.prototype.attr = function () {\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        var _args = [];\n        for (\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        var _i = 0; \n            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n            _i < arguments.length; \n            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n            _i++) {\n                // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                _args[_i] = arguments[_i];\n        }\n        this.graphic.attr.apply(this.graphic, arguments);\n    };\n    /**\n     * Utility function for mapping item's options\n     * to element's attribute\n     * @private\n     * @param {Highcharts.AnnotationsLabelsOptions|Highcharts.AnnotationsShapesOptions} options\n     * @return {Highcharts.SVGAttributes}\n     *         Mapped options.\n     */\n    Controllable.prototype.attrsFromOptions = function (options) {\n        var map = this.constructor.attrsMap,\n            attrs = {},\n            styledMode = this.chart.styledMode;\n        var key,\n            mappedKey;\n        for (key in options) { // eslint-disable-line guard-for-in\n            mappedKey = map[key];\n            if (typeof map[key] !== 'undefined' &&\n                (!styledMode ||\n                    ['fill', 'stroke', 'stroke-width']\n                        .indexOf(mappedKey) === -1)) {\n                attrs[mappedKey] = options[key];\n            }\n        }\n        return attrs;\n    };\n    /**\n     * Destroy a controllable.\n     * @private\n     */\n    Controllable.prototype.destroy = function () {\n        if (this.graphic) {\n            this.graphic = this.graphic.destroy();\n        }\n        if (this.tracker) {\n            this.tracker = this.tracker.destroy();\n        }\n        this.destroyControlTarget();\n    };\n    /**\n     * Init the controllable\n     * @private\n     */\n    Controllable.prototype.init = function (annotation, options, index) {\n        this.annotation = annotation;\n        this.chart = annotation.chart;\n        this.options = options;\n        this.points = [];\n        this.controlPoints = [];\n        this.index = index;\n        this.linkPoints();\n        this.addControlPoints();\n    };\n    /**\n     * Redraw a controllable.\n     * @private\n     */\n    Controllable.prototype.redraw = function (animation) {\n        this.redrawControlPoints(animation);\n    };\n    /**\n     * Render a controllable.\n     * @private\n     */\n    Controllable.prototype.render = function (\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _parentGroup) {\n        if (this.options.className && this.graphic) {\n            this.graphic.addClass(this.options.className);\n        }\n        this.renderControlPoints();\n    };\n    /**\n     * Rotate a controllable.\n     * @private\n     * @param {number} cx\n     *        Origin x rotation\n     * @param {number} cy\n     *        Origin y rotation\n     * @param {number} radians\n     **/\n    Controllable.prototype.rotate = function (cx, cy, radians) {\n        this.transform('rotate', cx, cy, radians);\n    };\n    /**\n     * Scale a controllable.\n     * @private\n     * @param {number} cx\n     *        Origin x rotation\n     * @param {number} cy\n     *        Origin y rotation\n     * @param {number} sx\n     *        Scale factor x\n     * @param {number} sy\n     *        Scale factor y\n     */\n    Controllable.prototype.scale = function (cx, cy, sx, sy) {\n        this.transform('scale', cx, cy, sx, sy);\n    };\n    /**\n     * Set control points' visibility.\n     * @private\n     */\n    Controllable.prototype.setControlPointsVisibility = function (visible) {\n        this.controlPoints.forEach(function (controlPoint) {\n            controlPoint.setVisibility(visible);\n        });\n    };\n    /**\n     * Check if a controllable should be rendered/redrawn.\n     * @private\n     * @return {boolean}\n     *         Whether a controllable should be drawn.\n     */\n    Controllable.prototype.shouldBeDrawn = function () {\n        return !!this.points.length;\n    };\n    /**\n     * Translate shape within controllable item.\n     * Replaces `controllable.translate` method.\n     * @private\n     * @param {number} dx\n     *        Translation for x coordinate\n     * @param {number} dy\n     *        Translation for y coordinate\n     * @param {boolean|undefined} translateSecondPoint\n     *        If the shape has two points attached to it, this option allows you\n     *        to translate also the second point.\n     */\n    Controllable.prototype.translateShape = function (dx, dy, translateSecondPoint) {\n        var chart = this.annotation.chart, \n            // Annotation.options\n            shapeOptions = this.annotation.userOptions, \n            // Chart.options.annotations\n            annotationIndex = chart.annotations.indexOf(this.annotation),\n            chartOptions = chart.options.annotations[annotationIndex];\n        this.translatePoint(dx, dy, 0);\n        if (translateSecondPoint) {\n            this.translatePoint(dx, dy, 1);\n        }\n        // Options stored in:\n        // - chart (for exporting)\n        // - current config (for redraws)\n        chartOptions[this.collection][this.index]\n            .point = this.options.point;\n        shapeOptions[this.collection][this.index]\n            .point = this.options.point;\n    };\n    /**\n     * Update a controllable.\n     * @private\n     */\n    Controllable.prototype.update = function (newOptions) {\n        var annotation = this.annotation,\n            options = Controllable_merge(true,\n            this.options,\n            newOptions),\n            parentGroup = this.graphic.parentGroup,\n            Constructor = this.constructor;\n        this.destroy();\n        var newControllable = new Constructor(annotation,\n            options,\n            this.index,\n            this.itemType);\n        Controllable_merge(true, this, newControllable);\n        this.render(parentGroup);\n        this.redraw();\n    };\n    return Controllable;\n}());\nAnnotations_ControlTarget.compose(Controllable);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Controllables_Controllable = (Controllable);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * An object which denotes a controllable's anchor positions - relative and\n * absolute.\n *\n * @private\n * @interface Highcharts.AnnotationAnchorObject\n */ /**\n* Relative to the plot area position\n* @name Highcharts.AnnotationAnchorObject#relativePosition\n* @type {Highcharts.BBoxObject}\n*/ /**\n* Absolute position\n* @name Highcharts.AnnotationAnchorObject#absolutePosition\n* @type {Highcharts.BBoxObject}\n*/\n/**\n * @interface Highcharts.AnnotationControllable\n */ /**\n* @name Highcharts.AnnotationControllable#annotation\n* @type {Highcharts.Annotation}\n*/ /**\n* @name Highcharts.AnnotationControllable#chart\n* @type {Highcharts.Chart}\n*/ /**\n* @name Highcharts.AnnotationControllable#collection\n* @type {string}\n*/ /**\n* @private\n* @name Highcharts.AnnotationControllable#controlPoints\n* @type {Array<Highcharts.AnnotationControlPoint>}\n*/ /**\n* @name Highcharts.AnnotationControllable#points\n* @type {Array<Highcharts.Point>}\n*/\n(''); // Keeps doclets above in JS file\n\n;// ./code/es5/es-modules/Extensions/Annotations/Controllables/ControllableDefaults.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/**\n * Options for configuring markers for annotations.\n *\n * An example of the arrow marker:\n * <pre>\n * {\n *   arrow: {\n *     id: 'arrow',\n *     tagName: 'marker',\n *     refY: 5,\n *     refX: 5,\n *     markerWidth: 10,\n *     markerHeight: 10,\n *     children: [{\n *       tagName: 'path',\n *       attrs: {\n *         d: 'M 0 0 L 10 5 L 0 10 Z',\n *         'stroke-width': 0\n *       }\n *     }]\n *   }\n * }\n * </pre>\n *\n * @sample highcharts/annotations/custom-markers/\n *         Define a custom marker for annotations\n *\n * @sample highcharts/css/annotations-markers/\n *         Define markers in a styled mode\n *\n * @type         {Highcharts.Dictionary<Highcharts.ASTNode>}\n * @since        6.0.0\n * @optionparent defs\n */\nvar defaultMarkers = {\n    /**\n     * @type {Highcharts.ASTNode}\n     */\n    arrow: {\n        tagName: 'marker',\n        attributes: {\n            id: 'arrow',\n            refY: 5,\n            refX: 9,\n            markerWidth: 10,\n            markerHeight: 10\n        },\n        /**\n         * @type {Array<Highcharts.DefsOptions>}\n         */\n        children: [{\n                tagName: 'path',\n                attributes: {\n                    d: 'M 0 0 L 10 5 L 0 10 Z', // Triangle (used as an arrow)\n                    'stroke-width': 0\n                }\n            }]\n    },\n    /**\n     * @type {Highcharts.ASTNode}\n     */\n    'reverse-arrow': {\n        tagName: 'marker',\n        attributes: {\n            id: 'reverse-arrow',\n            refY: 5,\n            refX: 1,\n            markerWidth: 10,\n            markerHeight: 10\n        },\n        children: [{\n                tagName: 'path',\n                attributes: {\n                    // Reverse triangle (used as an arrow)\n                    d: 'M 0 5 L 10 0 L 10 10 Z',\n                    'stroke-width': 0\n                }\n            }]\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\nvar ControllableDefaults = {\n    defaultMarkers: defaultMarkers\n};\n/* harmony default export */ var Controllables_ControllableDefaults = (ControllableDefaults);\n\n;// ./code/es5/es-modules/Extensions/Annotations/Controllables/ControllablePath.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar ControllablePath_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\nvar ControllablePath_defaultMarkers = Controllables_ControllableDefaults.defaultMarkers;\n\n\nvar ControllablePath_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, ControllablePath_defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, ControllablePath_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, uniqueKey = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).uniqueKey;\n/* *\n *\n *  Constants\n *\n * */\nvar markerEndSetter = createMarkerSetter('marker-end');\nvar markerStartSetter = createMarkerSetter('marker-start');\n// See TRACKER_FILL in highcharts.js\nvar TRACKER_FILL = 'rgba(192,192,192,' + ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).svg ? 0.0001 : 0.002) + ')';\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction createMarkerSetter(markerType) {\n    return function (value) {\n        this.attr(markerType, 'url(#' + value + ')');\n    };\n}\n/**\n * @private\n */\nfunction onChartAfterGetContainer() {\n    this.options.defs = ControllablePath_merge(ControllablePath_defaultMarkers, this.options.defs || {});\n    ///  objectEach(this.options.defs, function (def): void {\n    //     const attributes = def.attributes;\n    //     if (\n    //         def.tagName === 'marker' &&\n    //         attributes &&\n    //         attributes.id &&\n    //         attributes.display !== 'none'\n    //     ) {\n    //         this.renderer.addMarker(attributes.id, def);\n    //     }\n    // }, this);\n}\n/**\n * @private\n */\nfunction svgRendererAddMarker(id, markerOptions) {\n    var options = { attributes: { id: id } };\n    var attrs = {\n            stroke: markerOptions.color || 'none',\n            fill: markerOptions.color || 'rgba(0, 0, 0, 0.75)'\n        };\n    options.children = (markerOptions.children &&\n        markerOptions.children.map(function (child) {\n            return ControllablePath_merge(attrs, child);\n        }));\n    var ast = ControllablePath_merge(true, {\n            attributes: {\n                markerWidth: 20,\n                markerHeight: 20,\n                refX: 0,\n                refY: 0,\n                orient: 'auto'\n            }\n        },\n        markerOptions,\n        options);\n    var marker = this.definition(ast);\n    marker.id = id;\n    return marker;\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * A controllable path class.\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationControllablePath\n *\n * @param {Highcharts.Annotation}\n * Related annotation.\n *\n * @param {Highcharts.AnnotationsShapeOptions} options\n * A path's options object.\n *\n * @param {number} index\n * Index of the path.\n */\nvar ControllablePath = /** @class */ (function (_super) {\n    ControllablePath_extends(ControllablePath, _super);\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    function ControllablePath(annotation, options, index) {\n        var _this = _super.call(this,\n            annotation,\n            options,\n            index, 'shape') || this;\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        _this.type = 'path';\n        return _this;\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    ControllablePath.compose = function (ChartClass, SVGRendererClass) {\n        var svgRendererProto = SVGRendererClass.prototype;\n        if (!svgRendererProto.addMarker) {\n            ControllablePath_addEvent(ChartClass, 'afterGetContainer', onChartAfterGetContainer);\n            svgRendererProto.addMarker = svgRendererAddMarker;\n        }\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Map the controllable path to 'd' path attribute.\n     *\n     * @return {Highcharts.SVGPathArray|null}\n     * A path's d attribute.\n     */\n    ControllablePath.prototype.toD = function () {\n        var dOption = this.options.d;\n        if (dOption) {\n            return typeof dOption === 'function' ?\n                dOption.call(this) :\n                dOption;\n        }\n        var points = this.points,\n            len = points.length,\n            d = [];\n        var showPath = len,\n            point = points[0],\n            position = showPath && this.anchor(point).absolutePosition,\n            pointIndex = 0,\n            command;\n        if (position) {\n            d.push(['M', position.x, position.y]);\n            while (++pointIndex < len && showPath) {\n                point = points[pointIndex];\n                command = point.command || 'L';\n                position = this.anchor(point).absolutePosition;\n                if (command === 'M') {\n                    d.push([command, position.x, position.y]);\n                }\n                else if (command === 'L') {\n                    d.push([command, position.x, position.y]);\n                }\n                else if (command === 'Z') {\n                    d.push([command]);\n                }\n                showPath = point.series.visible;\n            }\n        }\n        return (showPath && this.graphic ?\n            this.chart.renderer.crispLine(d, this.graphic.strokeWidth()) :\n            null);\n    };\n    ControllablePath.prototype.shouldBeDrawn = function () {\n        return _super.prototype.shouldBeDrawn.call(this) || !!this.options.d;\n    };\n    ControllablePath.prototype.render = function (parent) {\n        var options = this.options,\n            attrs = this.attrsFromOptions(options);\n        this.graphic = this.annotation.chart.renderer\n            .path([['M', 0, 0]])\n            .attr(attrs)\n            .add(parent);\n        this.tracker = this.annotation.chart.renderer\n            .path([['M', 0, 0]])\n            .addClass('highcharts-tracker-line')\n            .attr({\n            zIndex: 2\n        })\n            .add(parent);\n        if (!this.annotation.chart.styledMode) {\n            this.tracker.attr({\n                'stroke-linejoin': 'round', // #1225\n                stroke: TRACKER_FILL,\n                fill: TRACKER_FILL,\n                'stroke-width': this.graphic.strokeWidth() +\n                    options.snap * 2\n            });\n        }\n        _super.prototype.render.call(this);\n        extend(this.graphic, { markerStartSetter: markerStartSetter, markerEndSetter: markerEndSetter });\n        this.setMarkers(this);\n    };\n    ControllablePath.prototype.redraw = function (animation) {\n        if (this.graphic) {\n            var d = this.toD(),\n                action = animation ? 'animate' : 'attr';\n            if (d) {\n                this.graphic[action]({ d: d });\n                this.tracker[action]({ d: d });\n            }\n            else {\n                this.graphic.attr({ d: 'M 0 ' + -9e9 });\n                this.tracker.attr({ d: 'M 0 ' + -9e9 });\n            }\n            this.graphic.placed = this.tracker.placed = !!d;\n        }\n        _super.prototype.redraw.call(this, animation);\n    };\n    /**\n     * Set markers.\n     * @private\n     * @param {Highcharts.AnnotationControllablePath} item\n     */\n    ControllablePath.prototype.setMarkers = function (item) {\n        var itemOptions = item.options,\n            chart = item.chart,\n            defs = chart.options.defs,\n            fill = itemOptions.fill,\n            color = ControllablePath_defined(fill) && fill !== 'none' ?\n                fill :\n                itemOptions.stroke;\n        var setMarker = function (markerType) {\n                var markerId = itemOptions[markerType];\n            var def,\n                predefinedMarker,\n                key,\n                marker;\n            if (markerId) {\n                for (key in defs) { // eslint-disable-line guard-for-in\n                    def = defs[key];\n                    if ((markerId === (def.attributes && def.attributes.id) ||\n                        // Legacy, for\n                        // unit-tests/annotations/annotations-shapes\n                        markerId === def.id) &&\n                        def.tagName === 'marker') {\n                        predefinedMarker = def;\n                        break;\n                    }\n                }\n                if (predefinedMarker) {\n                    marker = item[markerType] = chart.renderer\n                        .addMarker((itemOptions.id || uniqueKey()) + '-' + markerId, ControllablePath_merge(predefinedMarker, { color: color }));\n                    item.attr(markerType, marker.getAttribute('id'));\n                }\n            }\n        };\n        ['markerStart', 'markerEnd']\n            .forEach(setMarker);\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    /**\n     * A map object which allows to map options attributes to element attributes\n     *\n     * @name Highcharts.AnnotationControllablePath.attrsMap\n     * @type {Highcharts.Dictionary<string>}\n     */\n    ControllablePath.attrsMap = {\n        dashStyle: 'dashstyle',\n        strokeWidth: 'stroke-width',\n        stroke: 'stroke',\n        fill: 'fill',\n        zIndex: 'zIndex'\n    };\n    return ControllablePath;\n}(Controllables_Controllable));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Controllables_ControllablePath = (ControllablePath);\n\n;// ./code/es5/es-modules/Extensions/Annotations/Controllables/ControllableRect.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar ControllableRect_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\n\nvar ControllableRect_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n *  Class\n *\n * */\n/**\n * A controllable rect class.\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationControllableRect\n *\n * @param {Highcharts.Annotation} annotation\n * An annotation instance.\n *\n * @param {Highcharts.AnnotationsShapeOptions} options\n * A rect's options.\n *\n * @param {number} index\n * Index of the rectangle\n */\nvar ControllableRect = /** @class */ (function (_super) {\n    ControllableRect_extends(ControllableRect, _super);\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    function ControllableRect(annotation, options, index) {\n        var _this = _super.call(this,\n            annotation,\n            options,\n            index, 'shape') || this;\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        _this.type = 'rect';\n        _this.translate = _super.prototype.translateShape;\n        return _this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    ControllableRect.prototype.render = function (parent) {\n        var attrs = this.attrsFromOptions(this.options);\n        this.graphic = this.annotation.chart.renderer\n            .rect(0, -9e9, 0, 0)\n            .attr(attrs)\n            .add(parent);\n        _super.prototype.render.call(this);\n    };\n    ControllableRect.prototype.redraw = function (animation) {\n        if (this.graphic) {\n            var position = this.anchor(this.points[0]).absolutePosition;\n            if (position) {\n                this.graphic[animation ? 'animate' : 'attr']({\n                    x: position.x,\n                    y: position.y,\n                    width: this.options.width,\n                    height: this.options.height\n                });\n            }\n            else {\n                this.attr({\n                    x: 0,\n                    y: -9e9\n                });\n            }\n            this.graphic.placed = Boolean(position);\n        }\n        _super.prototype.redraw.call(this, animation);\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    /**\n     * A map object which allows to map options attributes to element attributes\n     *\n     * @type {Annotation.ControllableRect.AttrsMap}\n     */\n    ControllableRect.attrsMap = ControllableRect_merge(Controllables_ControllablePath.attrsMap, {\n        width: 'width',\n        height: 'height'\n    });\n    return ControllableRect;\n}(Controllables_Controllable));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Controllables_ControllableRect = (ControllableRect);\n\n;// ./code/es5/es-modules/Extensions/Annotations/Controllables/ControllableCircle.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar ControllableCircle_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\n\nvar ControllableCircle_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n *  Class\n *\n * */\n/**\n * A controllable circle class.\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationControllableCircle\n *\n * @param {Highcharts.Annotation} annotation an annotation instance\n * @param {Highcharts.AnnotationsShapeOptions} options a shape's options\n * @param {number} index of the circle\n */\nvar ControllableCircle = /** @class */ (function (_super) {\n    ControllableCircle_extends(ControllableCircle, _super);\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    function ControllableCircle(annotation, options, index) {\n        var _this = _super.call(this,\n            annotation,\n            options,\n            index, 'shape') || this;\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        _this.type = 'circle';\n        _this.translate = _super.prototype.translateShape;\n        return _this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    ControllableCircle.prototype.redraw = function (animation) {\n        if (this.graphic) {\n            var position = this.anchor(this.points[0]).absolutePosition;\n            if (position) {\n                this.graphic[animation ? 'animate' : 'attr']({\n                    x: position.x,\n                    y: position.y,\n                    r: this.options.r\n                });\n            }\n            else {\n                this.graphic.attr({\n                    x: 0,\n                    y: -9e9\n                });\n            }\n            this.graphic.placed = !!position;\n        }\n        _super.prototype.redraw.call(this, animation);\n    };\n    /**\n     * @private\n     */\n    ControllableCircle.prototype.render = function (parent) {\n        var attrs = this.attrsFromOptions(this.options);\n        this.graphic = this.annotation.chart.renderer\n            .circle(0, -9e9, 0)\n            .attr(attrs)\n            .add(parent);\n        _super.prototype.render.call(this);\n    };\n    /**\n     * Set the radius.\n     * @private\n     * @param {number} r\n     *        A radius to be set\n     */\n    ControllableCircle.prototype.setRadius = function (r) {\n        this.options.r = r;\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    /**\n     * A map object which allows to map options attributes to element\n     * attributes.\n     *\n     * @name Highcharts.AnnotationControllableCircle.attrsMap\n     * @type {Highcharts.Dictionary<string>}\n     */\n    ControllableCircle.attrsMap = ControllableCircle_merge(Controllables_ControllablePath.attrsMap, { r: 'r' });\n    return ControllableCircle;\n}(Controllables_Controllable));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Controllables_ControllableCircle = (ControllableCircle);\n\n;// ./code/es5/es-modules/Extensions/Annotations/Controllables/ControllableEllipse.js\n/* *\n *\n * Author: Pawel Lysy\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar ControllableEllipse_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\n\nvar ControllableEllipse_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, ControllableEllipse_defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined;\n/* *\n *\n *  Class\n *\n * */\n/**\n * A controllable ellipse class.\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationControllableEllipse\n *\n * @param {Highcharts.Annotation} annotation an annotation instance\n * @param {Highcharts.AnnotationsShapeOptions} options a shape's options\n * @param {number} index of the Ellipse\n */\nvar ControllableEllipse = /** @class */ (function (_super) {\n    ControllableEllipse_extends(ControllableEllipse, _super);\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    function ControllableEllipse(annotation, options, index) {\n        var _this = _super.call(this,\n            annotation,\n            options,\n            index, 'shape') || this;\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        _this.type = 'ellipse';\n        return _this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    ControllableEllipse.prototype.init = function (annotation, options, index) {\n        if (ControllableEllipse_defined(options.yAxis)) {\n            options.points.forEach(function (point) {\n                point.yAxis = options.yAxis;\n            });\n        }\n        if (ControllableEllipse_defined(options.xAxis)) {\n            options.points.forEach(function (point) {\n                point.xAxis = options.xAxis;\n            });\n        }\n        _super.prototype.init.call(this, annotation, options, index);\n    };\n    /**\n     * Render the element\n     * @private\n     * @param parent\n     *        Parent SVG element.\n     */\n    ControllableEllipse.prototype.render = function (parent) {\n        this.graphic = this.annotation.chart.renderer.createElement('ellipse')\n            .attr(this.attrsFromOptions(this.options))\n            .add(parent);\n        _super.prototype.render.call(this);\n    };\n    /**\n     * Translate the points. Mostly used to handle dragging of the ellipse.\n     * @private\n     */\n    ControllableEllipse.prototype.translate = function (dx, dy) {\n        _super.prototype.translateShape.call(this, dx, dy, true);\n    };\n    /**\n     * Get the distance from the line to the point.\n     * @private\n     * @param point1\n     *        First point which is on the line\n     * @param point2\n     *        Second point\n     * @param x0\n     *        Point's x value from which you want to calculate the distance from\n     * @param y0\n     *        Point's y value from which you want to calculate the distance from\n     */\n    ControllableEllipse.prototype.getDistanceFromLine = function (point1, point2, x0, y0) {\n        return Math.abs((point2.y - point1.y) * x0 - (point2.x - point1.x) * y0 +\n            point2.x * point1.y - point2.y * point1.x) / Math.sqrt((point2.y - point1.y) * (point2.y - point1.y) +\n            (point2.x - point1.x) * (point2.x - point1.x));\n    };\n    /**\n     * The function calculates the svg attributes of the ellipse, and returns\n     * all parameters necessary to draw the ellipse.\n     * @private\n     * @param position\n     *        Absolute position of the first point in points array\n     * @param position2\n     *        Absolute position of the second point in points array\n     */\n    ControllableEllipse.prototype.getAttrs = function (position, position2) {\n        var x1 = position.x, y1 = position.y, x2 = position2.x, y2 = position2.y, cx = (x1 + x2) / 2, cy = (y1 + y2) / 2, rx = Math.sqrt((x1 - x2) * (x1 - x2) / 4 + (y1 - y2) * (y1 - y2) / 4), tan = (y2 - y1) / (x2 - x1);\n        var angle = Math.atan(tan) * 180 / Math.PI;\n        if (cx < x1) {\n            angle += 180;\n        }\n        var ry = this.getRY();\n        return { cx: cx, cy: cy, rx: rx, ry: ry, angle: angle };\n    };\n    /**\n     * Get the value of minor radius of the ellipse.\n     * @private\n     */\n    ControllableEllipse.prototype.getRY = function () {\n        var yAxis = this.getYAxis();\n        return ControllableEllipse_defined(yAxis) ?\n            Math.abs(yAxis.toPixels(this.options.ry) - yAxis.toPixels(0)) :\n            this.options.ry;\n    };\n    /**\n     * Get the yAxis object to which the ellipse is pinned.\n     * @private\n     */\n    ControllableEllipse.prototype.getYAxis = function () {\n        var yAxisIndex = this.options.yAxis;\n        return this.chart.yAxis[yAxisIndex];\n    };\n    /**\n     * Get the absolute coordinates of the MockPoint\n     * @private\n     * @param point\n     *        MockPoint that is added through options\n     */\n    ControllableEllipse.prototype.getAbsolutePosition = function (point) {\n        return this.anchor(point).absolutePosition;\n    };\n    /**\n     * Redraw the element\n     * @private\n     * @param animation\n     *        Display an animation\n     */\n    ControllableEllipse.prototype.redraw = function (animation) {\n        if (this.graphic) {\n            var position = this.getAbsolutePosition(this.points[0]),\n                position2 = this.getAbsolutePosition(this.points[1]),\n                attrs = this.getAttrs(position,\n                position2);\n            if (position) {\n                this.graphic[animation ? 'animate' : 'attr']({\n                    cx: attrs.cx,\n                    cy: attrs.cy,\n                    rx: attrs.rx,\n                    ry: attrs.ry,\n                    rotation: attrs.angle,\n                    rotationOriginX: attrs.cx,\n                    rotationOriginY: attrs.cy\n                });\n            }\n            else {\n                this.graphic.attr({\n                    x: 0,\n                    y: -9e9\n                });\n            }\n            this.graphic.placed = Boolean(position);\n        }\n        _super.prototype.redraw.call(this, animation);\n    };\n    /**\n     * Set the radius Y.\n     * @private\n     * @param {number} ry\n     *        A radius in y direction to be set\n     */\n    ControllableEllipse.prototype.setYRadius = function (ry) {\n        var shapes = this.annotation.userOptions.shapes;\n        this.options.ry = ry;\n        if (shapes && shapes[0]) {\n            shapes[0].ry = ry;\n            shapes[0].ry = ry;\n        }\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    /**\n     * A map object which allows to map options attributes to element\n     * attributes.\n     *\n     * @name Highcharts.AnnotationControllableEllipse.attrsMap\n     * @type {Highcharts.Dictionary<string>}\n     */\n    ControllableEllipse.attrsMap = ControllableEllipse_merge(Controllables_ControllablePath.attrsMap, {\n        ry: 'ry'\n    });\n    return ControllableEllipse;\n}(Controllables_Controllable));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Controllables_ControllableEllipse = (ControllableEllipse);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Templating\"],\"commonjs\":[\"highcharts\",\"Templating\"],\"commonjs2\":[\"highcharts\",\"Templating\"],\"root\":[\"Highcharts\",\"Templating\"]}\nvar highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_ = __webpack_require__(984);\nvar highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default = /*#__PURE__*/__webpack_require__.n(highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_);\n;// ./code/es5/es-modules/Extensions/Annotations/Controllables/ControllableLabel.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar ControllableLabel_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\nvar format = (highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default()).format;\n\n\nvar ControllableLabel_extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, getAlignFactor = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).getAlignFactor, isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, ControllableLabel_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * General symbol definition for labels with connector\n * @private\n */\nfunction symbolConnector(x, y, w, h, options) {\n    var anchorX = options && options.anchorX,\n        anchorY = options && options.anchorY;\n    var path,\n        yOffset,\n        lateral = w / 2;\n    if (isNumber(anchorX) && isNumber(anchorY)) {\n        path = [['M', anchorX, anchorY]];\n        // Prefer 45 deg connectors\n        yOffset = y - anchorY;\n        if (yOffset < 0) {\n            yOffset = -h - yOffset;\n        }\n        if (yOffset < w) {\n            lateral = anchorX < x + (w / 2) ? yOffset : w - yOffset;\n        }\n        // Anchor below label\n        if (anchorY > y + h) {\n            path.push(['L', x + lateral, y + h]);\n            // Anchor above label\n        }\n        else if (anchorY < y) {\n            path.push(['L', x + lateral, y]);\n            // Anchor left of label\n        }\n        else if (anchorX < x) {\n            path.push(['L', x, y + h / 2]);\n            // Anchor right of label\n        }\n        else if (anchorX > x + w) {\n            path.push(['L', x + w, y + h / 2]);\n        }\n    }\n    return path || [];\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * A controllable label class.\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationControllableLabel\n *\n * @param {Highcharts.Annotation} annotation\n * An annotation instance.\n * @param {Highcharts.AnnotationsLabelOptions} options\n * A label's options.\n * @param {number} index\n * Index of the label.\n */\nvar ControllableLabel = /** @class */ (function (_super) {\n    ControllableLabel_extends(ControllableLabel, _super);\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    function ControllableLabel(annotation, options, index) {\n        return _super.call(this, annotation, options, index, 'label') || this;\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Returns new aligned position based alignment options and box to align to.\n     * It is almost a one-to-one copy from SVGElement.prototype.align\n     * except it does not use and mutate an element\n     *\n     * @param {Highcharts.AnnotationAlignObject} alignOptions\n     *\n     * @param {Highcharts.BBoxObject} box\n     *\n     * @return {Highcharts.PositionObject}\n     * Aligned position.\n     */\n    ControllableLabel.alignedPosition = function (alignOptions, box) {\n        return {\n            x: Math.round((box.x || 0) + (alignOptions.x || 0) +\n                (box.width - (alignOptions.width || 0)) *\n                    getAlignFactor(alignOptions.align)),\n            y: Math.round((box.y || 0) + (alignOptions.y || 0) +\n                (box.height - (alignOptions.height || 0)) *\n                    getAlignFactor(alignOptions.verticalAlign))\n        };\n    };\n    ControllableLabel.compose = function (SVGRendererClass) {\n        var symbols = SVGRendererClass.prototype.symbols;\n        symbols.connector = symbolConnector;\n    };\n    /**\n     * Returns new alignment options for a label if the label is outside the\n     * plot area. It is almost a one-to-one copy from\n     * Series.prototype.justifyDataLabel except it does not mutate the label and\n     * it works with absolute instead of relative position.\n     */\n    ControllableLabel.justifiedOptions = function (chart, label, alignOptions, alignAttr) {\n        var align = alignOptions.align,\n            verticalAlign = alignOptions.verticalAlign,\n            padding = label.box ? 0 : (label.padding || 0),\n            bBox = label.getBBox(), \n            //\n            options = {\n                align: align,\n                verticalAlign: verticalAlign,\n                x: alignOptions.x,\n                y: alignOptions.y,\n                width: label.width,\n                height: label.height\n            }, \n            //\n            x = (alignAttr.x || 0) - chart.plotLeft,\n            y = (alignAttr.y || 0) - chart.plotTop;\n        var off;\n        // Off left\n        off = x + padding;\n        if (off < 0) {\n            if (align === 'right') {\n                options.align = 'left';\n            }\n            else {\n                options.x = (options.x || 0) - off;\n            }\n        }\n        // Off right\n        off = x + bBox.width - padding;\n        if (off > chart.plotWidth) {\n            if (align === 'left') {\n                options.align = 'right';\n            }\n            else {\n                options.x = (options.x || 0) + chart.plotWidth - off;\n            }\n        }\n        // Off top\n        off = y + padding;\n        if (off < 0) {\n            if (verticalAlign === 'bottom') {\n                options.verticalAlign = 'top';\n            }\n            else {\n                options.y = (options.y || 0) - off;\n            }\n        }\n        // Off bottom\n        off = y + bBox.height - padding;\n        if (off > chart.plotHeight) {\n            if (verticalAlign === 'top') {\n                options.verticalAlign = 'bottom';\n            }\n            else {\n                options.y = (options.y || 0) + chart.plotHeight - off;\n            }\n        }\n        return options;\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Translate the point of the label by deltaX and deltaY translations.\n     * The point is the label's anchor.\n     *\n     * @param {number} dx translation for x coordinate\n     * @param {number} dy translation for y coordinate\n     */\n    ControllableLabel.prototype.translatePoint = function (dx, dy) {\n        _super.prototype.translatePoint.call(this, dx, dy, 0);\n    };\n    /**\n     * Translate x and y position relative to the label's anchor.\n     *\n     * @param {number} dx translation for x coordinate\n     * @param {number} dy translation for y coordinate\n     */\n    ControllableLabel.prototype.translate = function (dx, dy) {\n        var chart = this.annotation.chart, \n            // Annotation.options\n            labelOptions = this.annotation.userOptions, \n            // Chart.options.annotations\n            annotationIndex = chart.annotations.indexOf(this.annotation),\n            chartAnnotations = chart.options.annotations,\n            chartOptions = chartAnnotations[annotationIndex];\n        if (chart.inverted) {\n            var temp = dx;\n            dx = dy;\n            dy = temp;\n        }\n        // Local options:\n        this.options.x += dx;\n        this.options.y += dy;\n        // Options stored in chart:\n        chartOptions[this.collection][this.index].x = this.options.x;\n        chartOptions[this.collection][this.index].y = this.options.y;\n        labelOptions[this.collection][this.index].x = this.options.x;\n        labelOptions[this.collection][this.index].y = this.options.y;\n    };\n    ControllableLabel.prototype.render = function (parent) {\n        var options = this.options,\n            attrs = this.attrsFromOptions(options),\n            style = options.style;\n        this.graphic = this.annotation.chart.renderer\n            .label('', 0, -9999, // #10055\n        options.shape, null, null, options.useHTML, null, 'annotation-label')\n            .attr(attrs)\n            .add(parent);\n        if (!this.annotation.chart.styledMode) {\n            if (style.color === 'contrast') {\n                style.color = this.annotation.chart.renderer.getContrast(ControllableLabel.shapesWithoutBackground.indexOf(options.shape) > -1 ? '#FFFFFF' : options.backgroundColor);\n            }\n            this.graphic\n                .css(options.style)\n                .shadow(options.shadow);\n        }\n        this.graphic.labelrank = options.labelrank;\n        _super.prototype.render.call(this);\n    };\n    ControllableLabel.prototype.redraw = function (animation) {\n        var options = this.options,\n            text = this.text || options.format || options.text,\n            label = this.graphic,\n            point = this.points[0];\n        if (!label) {\n            this.redraw(animation);\n            return;\n        }\n        label.attr({\n            text: text ?\n                format(String(text), point, this.annotation.chart) :\n                options.formatter.call(point, this)\n        });\n        var anchor = this.anchor(point);\n        var attrs = this.position(anchor);\n        if (attrs) {\n            label.alignAttr = attrs;\n            attrs.anchorX = anchor.absolutePosition.x;\n            attrs.anchorY = anchor.absolutePosition.y;\n            label[animation ? 'animate' : 'attr'](attrs);\n        }\n        else {\n            label.attr({\n                x: 0,\n                y: -9999 // #10055\n            });\n        }\n        label.placed = !!attrs;\n        _super.prototype.redraw.call(this, animation);\n    };\n    /**\n     * All basic shapes don't support alignTo() method except label.\n     * For a controllable label, we need to subtract translation from\n     * options.\n     */\n    ControllableLabel.prototype.anchor = function (\n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    _point) {\n        var anchor = _super.prototype.anchor.apply(this,\n            arguments),\n            x = this.options.x || 0,\n            y = this.options.y || 0;\n        anchor.absolutePosition.x -= x;\n        anchor.absolutePosition.y -= y;\n        anchor.relativePosition.x -= x;\n        anchor.relativePosition.y -= y;\n        return anchor;\n    };\n    /**\n     * Returns the label position relative to its anchor.\n     */\n    ControllableLabel.prototype.position = function (anchor) {\n        var item = this.graphic,\n            chart = this.annotation.chart,\n            tooltip = chart.tooltip,\n            point = this.points[0],\n            itemOptions = this.options,\n            anchorAbsolutePosition = anchor.absolutePosition,\n            anchorRelativePosition = anchor.relativePosition;\n        var itemPosition,\n            alignTo,\n            itemPosRelativeX,\n            itemPosRelativeY,\n            showItem = point.series.visible &&\n                Annotations_MockPoint.prototype.isInsidePlot.call(point);\n        if (item && showItem) {\n            var _a = item.width,\n                width = _a === void 0 ? 0 : _a,\n                _b = item.height,\n                height = _b === void 0 ? 0 : _b;\n            if (itemOptions.distance && tooltip) {\n                itemPosition = tooltip.getPosition.call({\n                    chart: chart,\n                    distance: ControllableLabel_pick(itemOptions.distance, 16),\n                    getPlayingField: tooltip.getPlayingField,\n                    pointer: tooltip.pointer\n                }, width, height, {\n                    plotX: anchorRelativePosition.x,\n                    plotY: anchorRelativePosition.y,\n                    negative: point.negative,\n                    ttBelow: point.ttBelow,\n                    h: (anchorRelativePosition.height ||\n                        anchorRelativePosition.width)\n                });\n            }\n            else if (itemOptions.positioner) {\n                itemPosition = itemOptions.positioner.call(this);\n            }\n            else {\n                alignTo = {\n                    x: anchorAbsolutePosition.x,\n                    y: anchorAbsolutePosition.y,\n                    width: 0,\n                    height: 0\n                };\n                itemPosition = ControllableLabel.alignedPosition(ControllableLabel_extend(itemOptions, {\n                    width: width,\n                    height: height\n                }), alignTo);\n                if (this.options.overflow === 'justify') {\n                    itemPosition = ControllableLabel.alignedPosition(ControllableLabel.justifiedOptions(chart, item, itemOptions, itemPosition), alignTo);\n                }\n            }\n            if (itemOptions.crop) {\n                itemPosRelativeX = itemPosition.x - chart.plotLeft;\n                itemPosRelativeY = itemPosition.y - chart.plotTop;\n                showItem =\n                    chart.isInsidePlot(itemPosRelativeX, itemPosRelativeY) &&\n                        chart.isInsidePlot(itemPosRelativeX + width, itemPosRelativeY + height);\n            }\n        }\n        return showItem ? itemPosition : null;\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    /**\n     * A map object which allows to map options attributes to element attributes\n     *\n     * @type {Highcharts.Dictionary<string>}\n     */\n    ControllableLabel.attrsMap = {\n        backgroundColor: 'fill',\n        borderColor: 'stroke',\n        borderWidth: 'stroke-width',\n        zIndex: 'zIndex',\n        borderRadius: 'r',\n        padding: 'padding'\n    };\n    /**\n     * Shapes which do not have background - the object is used for proper\n     * setting of the contrast color.\n     *\n     * @type {Array<string>}\n     */\n    ControllableLabel.shapesWithoutBackground = ['connector'];\n    return ControllableLabel;\n}(Controllables_Controllable));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Controllables_ControllableLabel = (ControllableLabel);\n\n;// ./code/es5/es-modules/Extensions/Annotations/Controllables/ControllableImage.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar ControllableImage_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * A controllable image class.\n *\n * @requires modules/annotations\n *\n * @private\n * @class\n * @name Highcharts.AnnotationControllableImage\n *\n * @param {Highcharts.Annotation} annotation\n * An annotation instance.\n *\n * @param {Highcharts.AnnotationsShapeOptions} options\n * A controllable's options.\n *\n * @param {number} index\n * Index of the image.\n */\nvar ControllableImage = /** @class */ (function (_super) {\n    ControllableImage_extends(ControllableImage, _super);\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    function ControllableImage(annotation, options, index) {\n        var _this = _super.call(this,\n            annotation,\n            options,\n            index, 'shape') || this;\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        _this.type = 'image';\n        _this.translate = _super.prototype.translateShape;\n        return _this;\n    }\n    ControllableImage.prototype.render = function (parent) {\n        var attrs = this.attrsFromOptions(this.options),\n            options = this.options;\n        this.graphic = this.annotation.chart.renderer\n            .image(options.src, 0, -9e9, options.width, options.height)\n            .attr(attrs)\n            .add(parent);\n        this.graphic.width = options.width;\n        this.graphic.height = options.height;\n        _super.prototype.render.call(this);\n    };\n    ControllableImage.prototype.redraw = function (animation) {\n        if (this.graphic) {\n            var anchor = this.anchor(this.points[0]),\n                position = Controllables_ControllableLabel.prototype.position.call(this,\n                anchor);\n            if (position) {\n                this.graphic[animation ? 'animate' : 'attr']({\n                    x: position.x,\n                    y: position.y\n                });\n            }\n            else {\n                this.graphic.attr({\n                    x: 0,\n                    y: -9e9\n                });\n            }\n            this.graphic.placed = Boolean(position);\n        }\n        _super.prototype.redraw.call(this, animation);\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    /**\n     * A map object which allows to map options attributes to element attributes\n     *\n     * @name Highcharts.AnnotationControllableImage.attrsMap\n     * @type {Highcharts.Dictionary<string>}\n     */\n    ControllableImage.attrsMap = {\n        width: 'width',\n        height: 'height',\n        zIndex: 'zIndex'\n    };\n    return ControllableImage;\n}(Controllables_Controllable));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Controllables_ControllableImage = (ControllableImage);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"AST\"],\"commonjs\":[\"highcharts\",\"AST\"],\"commonjs2\":[\"highcharts\",\"AST\"],\"root\":[\"Highcharts\",\"AST\"]}\nvar highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_ = __webpack_require__(660);\nvar highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default = /*#__PURE__*/__webpack_require__.n(highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_);\n;// ./code/es5/es-modules/Shared/BaseForm.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Imports\n *\n * */\n\n\nvar BaseForm_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, createElement = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).createElement;\n/* *\n *\n *  Class\n *\n * */\nvar BaseForm = /** @class */ (function () {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    function BaseForm(parentDiv, iconsURL) {\n        this.iconsURL = iconsURL;\n        this.container = this.createPopupContainer(parentDiv);\n        this.closeButton = this.addCloseButton();\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Create popup div container.\n     *\n     * @param {HTMLElement} parentDiv\n     * Parent div to attach popup.\n     *\n     * @param  {string} className\n     * Class name of the popup.\n     *\n     * @return {HTMLElement}\n     * Popup div.\n     */\n    BaseForm.prototype.createPopupContainer = function (parentDiv, className) {\n        if (className === void 0) { className = 'highcharts-popup highcharts-no-tooltip'; }\n        return createElement('div', { className: className }, void 0, parentDiv);\n    };\n    /**\n     * Create HTML element and attach click event to close popup.\n     *\n     * @param {string} className\n     * Class name of the close button.\n     *\n     * @return {HTMLElement}\n     * Close button.\n     */\n    BaseForm.prototype.addCloseButton = function (className) {\n        if (className === void 0) { className = 'highcharts-popup-close'; }\n        var popup = this,\n            iconsURL = this.iconsURL;\n        // Create close popup button.\n        var closeButton = createElement('button', { className: className },\n            void 0,\n            this.container);\n        closeButton.style['background-image'] = 'url(' +\n            (iconsURL.match(/png|svg|jpeg|jpg|gif/ig) ?\n                iconsURL : iconsURL + 'close.svg') + ')';\n        ['click', 'touchstart'].forEach(function (eventName) {\n            BaseForm_addEvent(closeButton, eventName, popup.closeButtonEvents.bind(popup));\n        });\n        // Close popup when press ESC\n        BaseForm_addEvent(document, 'keydown', function (event) {\n            if (event.code === 'Escape') {\n                popup.closeButtonEvents();\n            }\n        });\n        return closeButton;\n    };\n    /**\n     * Close button events.\n     * @return {void}\n     */\n    BaseForm.prototype.closeButtonEvents = function () {\n        this.closePopup();\n    };\n    /**\n     * Reset content of the current popup and show.\n     *\n     * @param {string} toolbarClass\n     * Class name of the toolbar which styles should be reset.\n     */\n    BaseForm.prototype.showPopup = function (toolbarClass) {\n        if (toolbarClass === void 0) { toolbarClass = 'highcharts-annotation-toolbar'; }\n        var popupDiv = this.container,\n            popupCloseButton = this.closeButton;\n        this.type = void 0;\n        // Reset content.\n        popupDiv.innerHTML = (highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default()).emptyHTML;\n        // Reset toolbar styles if exists.\n        if (popupDiv.className.indexOf(toolbarClass) >= 0) {\n            popupDiv.classList.remove(toolbarClass);\n            // Reset toolbar inline styles\n            popupDiv.removeAttribute('style');\n        }\n        // Add close button.\n        popupDiv.appendChild(popupCloseButton);\n        popupDiv.style.display = 'block';\n        popupDiv.style.height = '';\n    };\n    /**\n     * Hide popup.\n     */\n    BaseForm.prototype.closePopup = function () {\n        this.container.style.display = 'none';\n    };\n    return BaseForm;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Shared_BaseForm = (BaseForm);\n\n;// ./code/es5/es-modules/Extensions/Annotations/Popup/PopupAnnotations.js\n/* *\n *\n *  Popup generator for Stock tools\n *\n *  (c) 2009-2025 Sebastian Bochan\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar PopupAnnotations_doc = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).doc, isFirefox = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isFirefox;\n\nvar PopupAnnotations_createElement = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).createElement, isArray = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isArray, isObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isObject, PopupAnnotations_objectEach = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).objectEach, PopupAnnotations_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, stableSort = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).stableSort;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Create annotation simple form.\n * It contains fields with param names.\n * @private\n * @param {Highcharts.Chart} chart\n * Chart\n * @param {Object} options\n * Options\n * @param {Function} callback\n * On click callback\n * @param {boolean} [isInit]\n * If it is a form declared for init annotation\n */\nfunction addForm(chart, options, callback, isInit) {\n    if (!chart) {\n        return;\n    }\n    var popupDiv = this.container,\n        lang = this.lang;\n    // Create title of annotations\n    var lhsCol = PopupAnnotations_createElement('h2', {\n            className: 'highcharts-popup-main-title'\n        },\n        void 0,\n        popupDiv);\n    lhsCol.appendChild(PopupAnnotations_doc.createTextNode(lang[options.langKey] || options.langKey || ''));\n    // Left column\n    lhsCol = PopupAnnotations_createElement('div', {\n        className: ('highcharts-popup-lhs-col highcharts-popup-lhs-full')\n    }, void 0, popupDiv);\n    var bottomRow = PopupAnnotations_createElement('div', {\n            className: 'highcharts-popup-bottom-row'\n        },\n        void 0,\n        popupDiv);\n    addFormFields.call(this, lhsCol, chart, '', options, [], true);\n    this.addButton(bottomRow, isInit ?\n        (lang.addButton || 'Add') :\n        (lang.saveButton || 'Save'), isInit ? 'add' : 'save', popupDiv, callback);\n}\n/**\n * Create annotation simple form. It contains two buttons\n * (edit / remove) and text label.\n * @private\n * @param {Highcharts.Chart} - chart\n * @param {Highcharts.AnnotationsOptions} - options\n * @param {Function} - on click callback\n */\nfunction addToolbar(chart, options, callback) {\n    var _this = this;\n    var lang = this.lang,\n        popupDiv = this.container,\n        showForm = this.showForm,\n        toolbarClass = 'highcharts-annotation-toolbar';\n    // Set small size\n    if (popupDiv.className.indexOf(toolbarClass) === -1) {\n        popupDiv.className += ' ' + toolbarClass + ' highcharts-no-mousewheel';\n    }\n    // Set position\n    if (chart) {\n        popupDiv.style.top = chart.plotTop + 10 + 'px';\n    }\n    // Create label\n    var label = PopupAnnotations_createElement('p', {\n            className: 'highcharts-annotation-label'\n        },\n        void 0,\n        popupDiv);\n    label.setAttribute('aria-label', 'Annotation type');\n    label.appendChild(PopupAnnotations_doc.createTextNode(PopupAnnotations_pick(\n    // Advanced annotations:\n    lang[options.langKey] || options.langKey, \n    // Basic shapes:\n    options.shapes && options.shapes[0].type, '')));\n    // Add buttons\n    var button = this.addButton(popupDiv, lang.editButton || 'Edit', 'edit', popupDiv, function () {\n            showForm.call(_this, 'annotation-edit', chart, options, callback);\n    });\n    button.className += ' highcharts-annotation-edit-button';\n    button.style['background-image'] = 'url(' +\n        this.iconsURL + 'edit.svg)';\n    button = this.addButton(popupDiv, lang.removeButton || 'Remove', 'remove', popupDiv, callback);\n    button.className += ' highcharts-annotation-remove-button';\n    button.style['background-image'] = 'url(' +\n        this.iconsURL + 'destroy.svg)';\n}\n/**\n * Create annotation's form fields.\n * @private\n * @param {Highcharts.HTMLDOMElement} parentDiv\n * Div where inputs are placed\n * @param {Highcharts.Chart} chart\n * Chart\n * @param {string} parentNode\n * Name of parent to create chain of names\n * @param {Highcharts.AnnotationsOptions} options\n * Options\n * @param {Array<unknown>} storage\n * Array where all items are stored\n * @param {boolean} [isRoot]\n * Recursive flag for root\n */\nfunction addFormFields(parentDiv, chart, parentNode, options, storage, isRoot) {\n    var _this = this;\n    if (!chart) {\n        return;\n    }\n    var addInput = this.addInput,\n        lang = this.lang;\n    var parentFullName,\n        titleName;\n    PopupAnnotations_objectEach(options, function (value, option) {\n        // Create name like params.styles.fontSize\n        parentFullName = parentNode !== '' ? parentNode + '.' + option : option;\n        if (isObject(value)) {\n            if (\n            // Value is object of options\n            !isArray(value) ||\n                // Array of objects with params. i.e labels in Fibonacci\n                (isArray(value) && isObject(value[0]))) {\n                titleName = lang[option] || option;\n                if (!titleName.match(/\\d/g)) {\n                    storage.push([\n                        true,\n                        titleName,\n                        parentDiv\n                    ]);\n                }\n                addFormFields.call(_this, parentDiv, chart, parentFullName, value, storage, false);\n            }\n            else {\n                storage.push([\n                    _this,\n                    parentFullName,\n                    'annotation',\n                    parentDiv,\n                    value\n                ]);\n            }\n        }\n    });\n    if (isRoot) {\n        stableSort(storage, function (a) { return (a[1].match(/format/g) ? -1 : 1); });\n        if (isFirefox) {\n            storage.reverse(); // (#14691)\n        }\n        storage.forEach(function (genInput) {\n            if (genInput[0] === true) {\n                PopupAnnotations_createElement('span', {\n                    className: 'highcharts-annotation-title'\n                }, void 0, genInput[2]).appendChild(PopupAnnotations_doc.createTextNode(genInput[1]));\n            }\n            else {\n                genInput[4] = {\n                    value: genInput[4][0],\n                    type: genInput[4][1]\n                };\n                addInput.apply(genInput[0], genInput.splice(1));\n            }\n        });\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar PopupAnnotations = {\n    addForm: addForm,\n    addToolbar: addToolbar\n};\n/* harmony default export */ var Popup_PopupAnnotations = (PopupAnnotations);\n\n;// ./code/es5/es-modules/Extensions/Annotations/Popup/PopupIndicators.js\n/* *\n *\n *  Popup generator for Stock tools\n *\n *  (c) 2009-2025 Sebastian Bochan\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nvar PopupIndicators_doc = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).doc;\n\nvar seriesTypes = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nvar PopupIndicators_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, PopupIndicators_createElement = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).createElement, PopupIndicators_defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, PopupIndicators_isArray = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isArray, PopupIndicators_isObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isObject, PopupIndicators_objectEach = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).objectEach, PopupIndicators_stableSort = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).stableSort;\n/* *\n *\n *  Enums\n *\n * */\n/**\n * Enum for properties which should have dropdown list.\n * @private\n */\nvar DropdownProperties;\n(function (DropdownProperties) {\n    DropdownProperties[DropdownProperties[\"params.algorithm\"] = 0] = \"params.algorithm\";\n    DropdownProperties[DropdownProperties[\"params.average\"] = 1] = \"params.average\";\n})(DropdownProperties || (DropdownProperties = {}));\n/**\n * List of available algorithms for the specific indicator.\n * @private\n */\nvar dropdownParameters = {\n    'algorithm-pivotpoints': ['standard', 'fibonacci', 'camarilla'],\n    'average-disparityindex': ['sma', 'ema', 'dema', 'tema', 'wma']\n};\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Create two columns (divs) in HTML.\n * @private\n * @param {Highcharts.HTMLDOMElement} container\n * Container of columns\n * @return {Highcharts.Dictionary<Highcharts.HTMLDOMElement>}\n * Reference to two HTML columns (lhsCol, rhsCol)\n */\nfunction addColsContainer(container) {\n    // Left column\n    var lhsCol = PopupIndicators_createElement('div', {\n            className: 'highcharts-popup-lhs-col'\n        },\n        void 0,\n        container);\n    // Right column\n    var rhsCol = PopupIndicators_createElement('div', {\n            className: 'highcharts-popup-rhs-col'\n        },\n        void 0,\n        container);\n    // Wrapper content\n    PopupIndicators_createElement('div', {\n        className: 'highcharts-popup-rhs-col-wrapper'\n    }, void 0, rhsCol);\n    return {\n        lhsCol: lhsCol,\n        rhsCol: rhsCol\n    };\n}\n/**\n * Create indicator's form. It contains two tabs (ADD and EDIT) with\n * content.\n * @private\n */\nfunction PopupIndicators_addForm(chart, _options, callback) {\n    var lang = this.lang;\n    var buttonParentDiv;\n    if (!chart) {\n        return;\n    }\n    // Add tabs\n    this.tabs.init.call(this, chart);\n    // Get all tabs content divs\n    var tabsContainers = this.container\n            .querySelectorAll('.highcharts-tab-item-content');\n    // ADD tab\n    addColsContainer(tabsContainers[0]);\n    addSearchBox.call(this, chart, tabsContainers[0]);\n    addIndicatorList.call(this, chart, tabsContainers[0], 'add');\n    buttonParentDiv = tabsContainers[0]\n        .querySelectorAll('.highcharts-popup-rhs-col')[0];\n    this.addButton(buttonParentDiv, lang.addButton || 'add', 'add', buttonParentDiv, callback);\n    // EDIT tab\n    addColsContainer(tabsContainers[1]);\n    addIndicatorList.call(this, chart, tabsContainers[1], 'edit');\n    buttonParentDiv = tabsContainers[1]\n        .querySelectorAll('.highcharts-popup-rhs-col')[0];\n    this.addButton(buttonParentDiv, lang.saveButton || 'save', 'edit', buttonParentDiv, callback);\n    this.addButton(buttonParentDiv, lang.removeButton || 'remove', 'remove', buttonParentDiv, callback);\n}\n/**\n * Create typical inputs for chosen indicator. Fields are extracted from\n * defaultOptions (ADD mode) or current indicator (ADD mode). Two extra\n * fields are added:\n * - hidden input - contains indicator type (required for callback)\n * - select - list of series which can be linked with indicator\n * @private\n * @param {Highcharts.Chart} chart\n * Chart\n * @param {Highcharts.Series} series\n * Indicator\n * @param {string} seriesType\n * Indicator type like: sma, ema, etc.\n * @param {Highcharts.HTMLDOMElement} rhsColWrapper\n * Element where created HTML list is added\n */\nfunction PopupIndicators_addFormFields(chart, series, seriesType, rhsColWrapper) {\n    var fields = series.params || series.options.params;\n    // Reset current content\n    rhsColWrapper.innerHTML = (highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default()).emptyHTML;\n    // Create title (indicator name in the right column)\n    PopupIndicators_createElement('h3', {\n        className: 'highcharts-indicator-title'\n    }, void 0, rhsColWrapper).appendChild(PopupIndicators_doc.createTextNode(getNameType(series, seriesType).indicatorFullName));\n    // Input type\n    PopupIndicators_createElement('input', {\n        type: 'hidden',\n        name: 'highcharts-type-' + seriesType,\n        value: seriesType\n    }, void 0, rhsColWrapper);\n    // List all series with id\n    listAllSeries.call(this, seriesType, 'series', chart, rhsColWrapper, series, series.linkedParent && series.linkedParent.options.id);\n    if (fields.volumeSeriesID) {\n        listAllSeries.call(this, seriesType, 'volume', chart, rhsColWrapper, series, series.linkedParent && fields.volumeSeriesID);\n    }\n    // Add param fields\n    addParamInputs.call(this, chart, 'params', fields, seriesType, rhsColWrapper);\n}\n/**\n * Create HTML list of all indicators (ADD mode) or added indicators\n * (EDIT mode).\n *\n * @private\n *\n * @param {Highcharts.AnnotationChart} chart\n *        The chart object.\n *\n * @param {string} [optionName]\n *        Name of the option into which selection is being added.\n *\n * @param {HTMLDOMElement} [parentDiv]\n *        HTML parent element.\n *\n * @param {string} listType\n *        Type of list depending on the selected bookmark.\n *        Might be 'add' or 'edit'.\n *\n * @param {string|undefined} filter\n *        Applied filter string from the input.\n *        For the first iteration, it's an empty string.\n */\nfunction addIndicatorList(chart, parentDiv, listType, filter) {\n    /**\n     *\n     */\n    function selectIndicator(series, indicatorType) {\n        var button = rhsColWrapper.parentNode\n                .children[1];\n        PopupIndicators_addFormFields.call(popup, chart, series, indicatorType, rhsColWrapper);\n        if (button) {\n            button.style.display = 'block';\n        }\n        // Add hidden input with series.id\n        if (isEdit && series.options) {\n            PopupIndicators_createElement('input', {\n                type: 'hidden',\n                name: 'highcharts-id-' + indicatorType,\n                value: series.options.id\n            }, void 0, rhsColWrapper).setAttribute('highcharts-data-series-id', series.options.id);\n        }\n    }\n    var popup = this, lang = popup.lang, lhsCol = parentDiv.querySelectorAll('.highcharts-popup-lhs-col')[0], rhsCol = parentDiv.querySelectorAll('.highcharts-popup-rhs-col')[0], isEdit = listType === 'edit', series = (isEdit ?\n            chart.series : // EDIT mode\n            chart.options.plotOptions || {} // ADD mode\n        );\n    if (!chart && series) {\n        return;\n    }\n    var item,\n        filteredSeriesArray = [];\n    // Filter and sort the series.\n    if (!isEdit && !PopupIndicators_isArray(series)) {\n        // Apply filters only for the 'add' indicator list.\n        filteredSeriesArray = filterSeries.call(this, series, filter);\n    }\n    else if (PopupIndicators_isArray(series)) {\n        filteredSeriesArray = filterSeriesArray.call(this, series);\n    }\n    // Sort indicators alphabetically.\n    PopupIndicators_stableSort(filteredSeriesArray, function (a, b) {\n        var seriesAName = a.indicatorFullName.toLowerCase(),\n            seriesBName = b.indicatorFullName.toLowerCase();\n        return (seriesAName < seriesBName) ?\n            -1 : (seriesAName > seriesBName) ? 1 : 0;\n    });\n    // If the list exists remove it from the DOM\n    // in order to create a new one with different filters.\n    if (lhsCol.children[1]) {\n        lhsCol.children[1].remove();\n    }\n    // Create wrapper for list.\n    var indicatorList = PopupIndicators_createElement('ul', {\n            className: 'highcharts-indicator-list'\n        },\n        void 0,\n        lhsCol);\n    var rhsColWrapper = rhsCol.querySelectorAll('.highcharts-popup-rhs-col-wrapper')[0];\n    filteredSeriesArray.forEach(function (seriesSet) {\n        var indicatorFullName = seriesSet.indicatorFullName,\n            indicatorType = seriesSet.indicatorType,\n            series = seriesSet.series;\n        item = PopupIndicators_createElement('li', {\n            className: 'highcharts-indicator-list'\n        }, void 0, indicatorList);\n        var btn = PopupIndicators_createElement('button', {\n                className: 'highcharts-indicator-list-item',\n                textContent: indicatorFullName\n            },\n            void 0,\n            item);\n        ['click', 'touchstart'].forEach(function (eventName) {\n            PopupIndicators_addEvent(btn, eventName, function () {\n                selectIndicator(series, indicatorType);\n            });\n        });\n    });\n    // Select first item from the list\n    if (filteredSeriesArray.length > 0) {\n        var _a = filteredSeriesArray[0],\n            series_1 = _a.series,\n            indicatorType = _a.indicatorType;\n        selectIndicator(series_1, indicatorType);\n    }\n    else if (!isEdit) {\n        highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default().setElementHTML(rhsColWrapper.parentNode.children[0], lang.noFilterMatch || '');\n        rhsColWrapper.parentNode.children[1]\n            .style.display = 'none';\n    }\n}\n/**\n * Recurrent function which lists all fields, from params object and\n * create them as inputs. Each input has unique `data-name` attribute,\n * which keeps chain of fields i.e params.styles.fontSize.\n * @private\n * @param {Highcharts.Chart} chart\n * Chart\n * @param {string} parentNode\n * Name of parent to create chain of names\n * @param {Highcharts.PopupFieldsDictionary<string>} fields\n * Params which are based for input create\n * @param {string} type\n * Indicator type like: sma, ema, etc.\n * @param {Highcharts.HTMLDOMElement} parentDiv\n * Element where created HTML list is added\n */\nfunction addParamInputs(chart, parentNode, fields, type, parentDiv) {\n    var _this = this;\n    if (!chart) {\n        return;\n    }\n    var addInput = this.addInput;\n    PopupIndicators_objectEach(fields, function (value, fieldName) {\n        // Create name like params.styles.fontSize\n        var parentFullName = parentNode + '.' + fieldName;\n        if (PopupIndicators_defined(value) && // Skip if field is unnecessary, #15362\n            parentFullName) {\n            if (PopupIndicators_isObject(value)) {\n                // (15733) 'Periods' has an arrayed value. Label must be\n                // created here.\n                addInput.call(_this, parentFullName, type, parentDiv, {});\n                addParamInputs.call(_this, chart, parentFullName, value, type, parentDiv);\n            }\n            // If the option is listed in dropdown enum,\n            // add the selection box for it.\n            if (parentFullName in DropdownProperties) {\n                // Add selection boxes.\n                var selectBox = addSelection.call(_this,\n                    type,\n                    parentFullName,\n                    parentDiv);\n                // Add possible dropdown options.\n                addSelectionOptions.call(_this, chart, parentNode, selectBox, type, fieldName, value);\n            }\n            else if (\n            // Skip volume field which is created by addFormFields.\n            parentFullName !== 'params.volumeSeriesID' &&\n                !PopupIndicators_isArray(value) // Skip params declared in array.\n            ) {\n                addInput.call(_this, parentFullName, type, parentDiv, {\n                    value: value,\n                    type: 'number'\n                } // All inputs are text type\n                );\n            }\n        }\n    });\n}\n/**\n * Add searchbox HTML element and its' label.\n *\n * @private\n *\n * @param {Highcharts.AnnotationChart} chart\n *        The chart object.\n *\n * @param {HTMLDOMElement} parentDiv\n *        HTML parent element.\n */\nfunction addSearchBox(chart, parentDiv) {\n    var popup = this, lhsCol = parentDiv.querySelectorAll('.highcharts-popup-lhs-col')[0], options = 'searchIndicators', inputAttributes = {\n            value: '',\n            type: 'text',\n            htmlFor: 'search-indicators',\n            labelClassName: 'highcharts-input-search-indicators-label'\n        }, clearFilterText = this.lang.clearFilter, inputWrapper = PopupIndicators_createElement('div', {\n            className: 'highcharts-input-wrapper'\n        }, void 0, lhsCol);\n    var handleInputChange = function (inputText) {\n            // Apply some filters.\n            addIndicatorList.call(popup,\n        chart,\n        popup.container, 'add',\n        inputText);\n    };\n    // Add input field with the label and button.\n    var input = this.addInput(options, 'input', inputWrapper, inputAttributes), button = PopupIndicators_createElement('a', {\n            textContent: clearFilterText\n        }, void 0, inputWrapper);\n    input.classList.add('highcharts-input-search-indicators');\n    button.classList.add('clear-filter-button');\n    // Add input change events.\n    PopupIndicators_addEvent(input, 'input', function () {\n        handleInputChange(this.value);\n        // Show clear filter button.\n        if (this.value.length) {\n            button.style.display = 'inline-block';\n        }\n        else {\n            button.style.display = 'none';\n        }\n    });\n    // Add clear filter click event.\n    ['click', 'touchstart'].forEach(function (eventName) {\n        PopupIndicators_addEvent(button, eventName, function () {\n            // Clear the input.\n            input.value = '';\n            handleInputChange('');\n            // Hide clear filter button- no longer necessary.\n            button.style.display = 'none';\n        });\n    });\n}\n/**\n * Add selection HTML element and its' label.\n *\n * @private\n *\n * @param {string} indicatorType\n * Type of the indicator i.e. sma, ema...\n *\n * @param {string} [optionName]\n * Name of the option into which selection is being added.\n *\n * @param {HTMLDOMElement} [parentDiv]\n * HTML parent element.\n */\nfunction addSelection(indicatorType, optionName, parentDiv) {\n    var optionParamList = optionName.split('.'), labelText = optionParamList[optionParamList.length - 1], selectName = 'highcharts-' + optionName + '-type-' + indicatorType, lang = this.lang;\n    // Add a label for the selection box.\n    PopupIndicators_createElement('label', {\n        htmlFor: selectName\n    }, null, parentDiv).appendChild(PopupIndicators_doc.createTextNode(lang[labelText] || optionName));\n    // Create a selection box.\n    var selectBox = PopupIndicators_createElement('select', {\n            name: selectName,\n            className: 'highcharts-popup-field',\n            id: 'highcharts-select-' + optionName\n        },\n        null,\n        parentDiv);\n    selectBox.setAttribute('id', 'highcharts-select-' + optionName);\n    return selectBox;\n}\n/**\n * Get and add selection options.\n *\n * @private\n *\n * @param {Highcharts.AnnotationChart} chart\n *        The chart object.\n *\n * @param {string} [optionName]\n *        Name of the option into which selection is being added.\n *\n * @param {HTMLSelectElement} [selectBox]\n *        HTML select box element to which the options are being added.\n *\n * @param {string|undefined} indicatorType\n *        Type of the indicator i.e. sma, ema...\n *\n * @param {string|undefined} parameterName\n *        Name of the parameter which should be applied.\n *\n * @param {string|undefined} selectedOption\n *        Default value in dropdown.\n */\nfunction addSelectionOptions(chart, optionName, selectBox, indicatorType, parameterName, selectedOption, currentSeries) {\n    // Get and apply selection options for the possible series.\n    if (optionName === 'series' || optionName === 'volume') {\n        // List all series which have id - mandatory for indicator.\n        chart.series.forEach(function (series) {\n            var seriesOptions = series.options,\n                seriesName = seriesOptions.name ||\n                    seriesOptions.params ?\n                    series.name :\n                    seriesOptions.id || '';\n            if (seriesOptions.id !== 'highcharts-navigator-series' &&\n                seriesOptions.id !== (currentSeries &&\n                    currentSeries.options &&\n                    currentSeries.options.id)) {\n                if (!PopupIndicators_defined(selectedOption) &&\n                    optionName === 'volume' &&\n                    series.type === 'column') {\n                    selectedOption = seriesOptions.id;\n                }\n                PopupIndicators_createElement('option', {\n                    value: seriesOptions.id\n                }, void 0, selectBox).appendChild(PopupIndicators_doc.createTextNode(seriesName));\n            }\n        });\n    }\n    else if (indicatorType && parameterName) {\n        // Get and apply options for the possible parameters.\n        var dropdownKey = parameterName + '-' + indicatorType,\n            parameterOption = dropdownParameters[dropdownKey];\n        parameterOption.forEach(function (element) {\n            PopupIndicators_createElement('option', {\n                value: element\n            }, void 0, selectBox).appendChild(PopupIndicators_doc.createTextNode(element));\n        });\n    }\n    // Add the default dropdown value if defined.\n    if (PopupIndicators_defined(selectedOption)) {\n        selectBox.value = selectedOption;\n    }\n}\n/**\n * Filter object of series which are not indicators.\n * If the filter string exists, check against it.\n *\n * @private\n *\n * @param {Highcharts.FilteredSeries} series\n *        All series are available in the plotOptions.\n *\n * @param {string|undefined} filter\n *        Applied filter string from the input.\n *        For the first iteration, it's an empty string.\n *\n * @return {Array<Highcharts.FilteredSeries>} filteredSeriesArray\n *         Returns array of filtered series based on filter string.\n */\nfunction filterSeries(series, filter) {\n    var popup = this,\n        lang = popup.chart && popup.chart.options.lang,\n        indicatorAliases = lang &&\n            lang.navigation &&\n            lang.navigation.popup &&\n            lang.navigation.popup.indicatorAliases,\n        filteredSeriesArray = [];\n    var filteredSeries;\n    PopupIndicators_objectEach(series, function (series, value) {\n        var seriesOptions = series && series.options;\n        // Allow only indicators.\n        if (series.params || seriesOptions &&\n            seriesOptions.params) {\n            var _a = getNameType(series,\n                value),\n                indicatorFullName = _a.indicatorFullName,\n                indicatorType = _a.indicatorType;\n            if (filter) {\n                // Replace invalid characters.\n                var validFilter = filter.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n                var regex = new RegExp(validFilter, 'i'),\n                    alias = indicatorAliases &&\n                        indicatorAliases[indicatorType] &&\n                        indicatorAliases[indicatorType].join(' ') || '';\n                if (indicatorFullName.match(regex) ||\n                    alias.match(regex)) {\n                    filteredSeries = {\n                        indicatorFullName: indicatorFullName,\n                        indicatorType: indicatorType,\n                        series: series\n                    };\n                    filteredSeriesArray.push(filteredSeries);\n                }\n            }\n            else {\n                filteredSeries = {\n                    indicatorFullName: indicatorFullName,\n                    indicatorType: indicatorType,\n                    series: series\n                };\n                filteredSeriesArray.push(filteredSeries);\n            }\n        }\n    });\n    return filteredSeriesArray;\n}\n/**\n * Filter an array of series and map its names and types.\n *\n * @private\n *\n * @param {Highcharts.FilteredSeries} series\n *        All series that are available in the plotOptions.\n *\n * @return {Array<Highcharts.FilteredSeries>} filteredSeriesArray\n *         Returns array of filtered series based on filter string.\n */\nfunction filterSeriesArray(series) {\n    var filteredSeriesArray = [];\n    // Allow only indicators.\n    series.forEach(function (series) {\n        if (series.is('sma')) {\n            filteredSeriesArray.push({\n                indicatorFullName: series.name,\n                indicatorType: series.type,\n                series: series\n            });\n        }\n    });\n    return filteredSeriesArray;\n}\n/**\n * Get amount of indicators added to chart.\n * @private\n * @return {number} - Amount of indicators\n */\nfunction getAmount() {\n    var counter = 0;\n    this.series.forEach(function (serie) {\n        if (serie.params ||\n            serie.options.params) {\n            counter++;\n        }\n    });\n    return counter;\n}\n/**\n * Extract full name and type of requested indicator.\n *\n * @private\n *\n * @param {Highcharts.Series} series\n * Series which name is needed(EDITmode - defaultOptions.series,\n * ADDmode - indicator series).\n *\n * @param {string} [indicatorType]\n * Type of the indicator i.e. sma, ema...\n *\n * @return {Highcharts.Dictionary<string>}\n * Full name and series type.\n */\nfunction getNameType(series, indicatorType) {\n    var options = series.options;\n    // Add mode\n    var seriesName = (seriesTypes[indicatorType] &&\n            seriesTypes[indicatorType].prototype.nameBase) ||\n            indicatorType.toUpperCase(),\n        seriesType = indicatorType;\n    // Edit\n    if (options && options.type) {\n        seriesType = series.options.type;\n        seriesName = series.name;\n    }\n    return {\n        indicatorFullName: seriesName,\n        indicatorType: seriesType\n    };\n}\n/**\n * Create the selection box for the series,\n * add options and apply the default one.\n *\n * @private\n *\n * @param {string} indicatorType\n *        Type of the indicator i.e. sma, ema...\n *\n * @param {string} [optionName]\n *        Name of the option into which selection is being added.\n *\n * @param {Highcharts.AnnotationChart} chart\n *        The chart object.\n *\n * @param {HTMLDOMElement} [parentDiv]\n *        HTML parent element.\n *\n * @param {string|undefined} selectedOption\n *        Default value in dropdown.\n */\nfunction listAllSeries(indicatorType, optionName, chart, parentDiv, currentSeries, selectedOption) {\n    var popup = this;\n    // Won't work without the chart.\n    if (!chart) {\n        return;\n    }\n    // Add selection boxes.\n    var selectBox = addSelection.call(popup,\n        indicatorType,\n        optionName,\n        parentDiv);\n    // Add possible dropdown options.\n    addSelectionOptions.call(popup, chart, optionName, selectBox, void 0, void 0, void 0, currentSeries);\n    // Add the default dropdown value if defined.\n    if (PopupIndicators_defined(selectedOption)) {\n        selectBox.value = selectedOption;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar PopupIndicators = {\n    addForm: PopupIndicators_addForm,\n    getAmount: getAmount\n};\n/* harmony default export */ var Popup_PopupIndicators = (PopupIndicators);\n\n;// ./code/es5/es-modules/Extensions/Annotations/Popup/PopupTabs.js\n/* *\n *\n *  Popup generator for Stock tools\n *\n *  (c) 2009-2025 Sebastian Bochan\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar PopupTabs_doc = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).doc;\n\nvar PopupTabs_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, PopupTabs_createElement = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).createElement;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Create tab content\n * @private\n * @return {HTMLDOMElement} - created HTML tab-content element\n */\nfunction addContentItem() {\n    var popupDiv = this.container;\n    return PopupTabs_createElement('div', {\n        // #12100\n        className: 'highcharts-tab-item-content highcharts-no-mousewheel'\n    }, void 0, popupDiv);\n}\n/**\n * Create tab menu item\n * @private\n * @param {string} tabName\n * `add` or `edit`\n * @param {number} [disableTab]\n * Disable tab when 0\n * @return {Highcharts.HTMLDOMElement}\n * Created HTML tab-menu element\n */\nfunction addMenuItem(tabName, disableTab) {\n    var popupDiv = this.container,\n        lang = this.lang;\n    var className = 'highcharts-tab-item';\n    if (disableTab === 0) {\n        className += ' highcharts-tab-disabled';\n    }\n    // Tab 1\n    var menuItem = PopupTabs_createElement('button', {\n            className: className\n        },\n        void 0,\n        popupDiv);\n    menuItem.appendChild(PopupTabs_doc.createTextNode(lang[tabName + 'Button'] || tabName));\n    menuItem.setAttribute('highcharts-data-tab-type', tabName);\n    return menuItem;\n}\n/**\n * Set all tabs as invisible.\n * @private\n */\nfunction deselectAll() {\n    var popupDiv = this.container,\n        tabs = popupDiv\n            .querySelectorAll('.highcharts-tab-item'),\n        tabsContent = popupDiv\n            .querySelectorAll('.highcharts-tab-item-content');\n    for (var i = 0; i < tabs.length; i++) {\n        tabs[i].classList.remove('highcharts-tab-item-active');\n        tabsContent[i].classList.remove('highcharts-tab-item-show');\n    }\n}\n/**\n * Init tabs. Create tab menu items, tabs containers\n * @private\n * @param {Highcharts.Chart} chart\n * Reference to current chart\n */\nfunction init(chart) {\n    if (!chart) {\n        return;\n    }\n    var indicatorsCount = this.indicators.getAmount.call(chart);\n    // Create menu items\n    var firstTab = addMenuItem.call(this, 'add'); // Run by default\n        addMenuItem.call(this, 'edit',\n        indicatorsCount);\n    // Create tabs containers\n    addContentItem.call(this);\n    addContentItem.call(this);\n    switchTabs.call(this, indicatorsCount);\n    // Activate first tab\n    selectTab.call(this, firstTab, 0);\n}\n/**\n * Set tab as visible\n * @private\n * @param {globals.Element} - current tab\n * @param {number} - Index of tab in menu\n */\nfunction selectTab(tab, index) {\n    var allTabs = this.container\n            .querySelectorAll('.highcharts-tab-item-content');\n    tab.className += ' highcharts-tab-item-active';\n    allTabs[index].className += ' highcharts-tab-item-show';\n}\n/**\n * Add click event to each tab\n * @private\n * @param {number} disableTab\n * Disable tab when 0\n */\nfunction switchTabs(disableTab) {\n    var popup = this,\n        popupDiv = this.container,\n        tabs = popupDiv.querySelectorAll('.highcharts-tab-item');\n    tabs.forEach(function (tab, i) {\n        if (disableTab === 0 &&\n            tab.getAttribute('highcharts-data-tab-type') === 'edit') {\n            return;\n        }\n        ['click', 'touchstart'].forEach(function (eventName) {\n            PopupTabs_addEvent(tab, eventName, function () {\n                // Reset class on other elements\n                deselectAll.call(popup);\n                selectTab.call(popup, this, i);\n            });\n        });\n    });\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar PopupTabs = {\n    init: init\n};\n/* harmony default export */ var Popup_PopupTabs = (PopupTabs);\n\n;// ./code/es5/es-modules/Extensions/Annotations/Popup/Popup.js\n/* *\n *\n *  Popup generator for Stock tools\n *\n *  (c) 2009-2025 Sebastian Bochan\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar Popup_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\nvar Popup_doc = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).doc;\n\nvar getOptions = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).getOptions;\n\n\n\n\nvar Popup_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, Popup_createElement = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).createElement, Popup_extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, Popup_fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, Popup_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Get values from all inputs and selections then create JSON.\n *\n * @private\n *\n * @param {Highcharts.HTMLDOMElement} parentDiv\n * The container where inputs and selections are created.\n *\n * @param {string} type\n * Type of the popup bookmark (add|edit|remove).\n */\nfunction getFields(parentDiv, type) {\n    var inputList = Array.prototype.slice.call(parentDiv.querySelectorAll('input')), selectList = Array.prototype.slice.call(parentDiv.querySelectorAll('select')), optionSeries = '#highcharts-select-series > option:checked', optionVolume = '#highcharts-select-volume > option:checked', linkedTo = parentDiv.querySelectorAll(optionSeries)[0], volumeTo = parentDiv.querySelectorAll(optionVolume)[0];\n    var fieldsOutput = {\n            actionType: type,\n            linkedTo: linkedTo && linkedTo.getAttribute('value') || '',\n            fields: {}\n        };\n    inputList.forEach(function (input) {\n        var param = input.getAttribute('highcharts-data-name'), seriesId = input.getAttribute('highcharts-data-series-id');\n        // Params\n        if (seriesId) {\n            fieldsOutput.seriesId = input.value;\n        }\n        else if (param) {\n            fieldsOutput.fields[param] = input.value;\n        }\n        else {\n            // Type like sma / ema\n            fieldsOutput.type = input.value;\n        }\n    });\n    selectList.forEach(function (select) {\n        var id = select.id;\n        // Get inputs only for the parameters, not for series and volume.\n        if (id !== 'highcharts-select-series' &&\n            id !== 'highcharts-select-volume') {\n            var parameter = id.split('highcharts-select-')[1];\n            fieldsOutput.fields[parameter] = select.value;\n        }\n    });\n    if (volumeTo) {\n        fieldsOutput.fields['params.volumeSeriesID'] = volumeTo\n            .getAttribute('value') || '';\n    }\n    return fieldsOutput;\n}\n/* *\n *\n *  Class\n *\n * */\nvar Popup = /** @class */ (function (_super) {\n    Popup_extends(Popup, _super);\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    function Popup(parentDiv, iconsURL, chart) {\n        var _this = _super.call(this,\n            parentDiv,\n            iconsURL) || this;\n        _this.chart = chart;\n        _this.lang = (getOptions().lang.navigation || {}).popup || {};\n        Popup_addEvent(_this.container, 'mousedown', function () {\n            var activeAnnotation = chart &&\n                    chart.navigationBindings &&\n                    chart.navigationBindings.activeAnnotation;\n            if (activeAnnotation) {\n                activeAnnotation.cancelClick = true;\n                var unbind_1 = Popup_addEvent(Popup_doc, 'click',\n                    function () {\n                        setTimeout(function () {\n                            activeAnnotation.cancelClick = false;\n                    }, 0);\n                    unbind_1();\n                });\n            }\n        });\n        return _this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Create input with label.\n     *\n     * @private\n     *\n     * @param {string} option\n     *        Chain of fields i.e params.styles.fontSize separated by the dot.\n     *\n     * @param {string} indicatorType\n     *        Type of the indicator i.e. sma, ema...\n     *\n     * @param {HTMLDOMElement} parentDiv\n     *        HTML parent element.\n     *\n     * @param {Highcharts.InputAttributes} inputAttributes\n     *        Attributes of the input.\n     *\n     * @return {HTMLInputElement}\n     *         Return created input element.\n     */\n    Popup.prototype.addInput = function (option, indicatorType, parentDiv, inputAttributes) {\n        var optionParamList = option.split('.'), optionName = optionParamList[optionParamList.length - 1], lang = this.lang, inputName = 'highcharts-' + indicatorType + '-' + Popup_pick(inputAttributes.htmlFor, optionName);\n        if (!optionName.match(/^\\d+$/)) {\n            // Add label\n            Popup_createElement('label', {\n                htmlFor: inputName,\n                className: inputAttributes.labelClassName\n            }, void 0, parentDiv).appendChild(Popup_doc.createTextNode(lang[optionName] || optionName));\n        }\n        // Add input\n        var input = Popup_createElement('input', {\n                name: inputName,\n                value: inputAttributes.value,\n                type: inputAttributes.type,\n                className: 'highcharts-popup-field'\n            },\n            void 0,\n            parentDiv);\n        input.setAttribute('highcharts-data-name', option);\n        return input;\n    };\n    Popup.prototype.closeButtonEvents = function () {\n        if (this.chart) {\n            var navigationBindings = this.chart.navigationBindings;\n            Popup_fireEvent(navigationBindings, 'closePopup');\n            if (navigationBindings &&\n                navigationBindings.selectedButtonElement) {\n                Popup_fireEvent(navigationBindings, 'deselectButton', { button: navigationBindings.selectedButtonElement });\n            }\n        }\n        else {\n            _super.prototype.closeButtonEvents.call(this);\n        }\n    };\n    /**\n     * Create button.\n     * @private\n     * @param {Highcharts.HTMLDOMElement} parentDiv\n     * Container where elements should be added\n     * @param {string} label\n     * Text placed as button label\n     * @param {string} type\n     * add | edit | remove\n     * @param {Function} callback\n     * On click callback\n     * @param {Highcharts.HTMLDOMElement} fieldsDiv\n     * Container where inputs are generated\n     * @return {Highcharts.HTMLDOMElement}\n     * HTML button\n     */\n    Popup.prototype.addButton = function (parentDiv, label, type, fieldsDiv, callback) {\n        var _this = this;\n        var button = Popup_createElement('button',\n            void 0,\n            void 0,\n            parentDiv);\n        button.appendChild(Popup_doc.createTextNode(label));\n        if (callback) {\n            ['click', 'touchstart'].forEach(function (eventName) {\n                Popup_addEvent(button, eventName, function () {\n                    _this.closePopup();\n                    return callback(getFields(fieldsDiv, type));\n                });\n            });\n        }\n        return button;\n    };\n    /**\n     * Create content and show popup.\n     * @private\n     * @param {string} - type of popup i.e indicators\n     * @param {Highcharts.Chart} - chart\n     * @param {Highcharts.AnnotationsOptions} - options\n     * @param {Function} - on click callback\n     */\n    Popup.prototype.showForm = function (type, chart, options, callback) {\n        if (!chart) {\n            return;\n        }\n        // Show blank popup\n        this.showPopup();\n        // Indicator form\n        if (type === 'indicators') {\n            this.indicators.addForm.call(this, chart, options, callback);\n        }\n        // Annotation small toolbar\n        if (type === 'annotation-toolbar') {\n            this.annotations.addToolbar.call(this, chart, options, callback);\n        }\n        // Annotation edit form\n        if (type === 'annotation-edit') {\n            this.annotations.addForm.call(this, chart, options, callback);\n        }\n        // Flags form - add / edit\n        if (type === 'flag') {\n            this.annotations.addForm.call(this, chart, options, callback, true);\n        }\n        this.type = type;\n        // Explicit height is needed to make inner elements scrollable\n        this.container.style.height = this.container.offsetHeight + 'px';\n    };\n    return Popup;\n}(Shared_BaseForm));\nPopup_extend(Popup.prototype, {\n    annotations: Popup_PopupAnnotations,\n    indicators: Popup_PopupIndicators,\n    tabs: Popup_PopupTabs\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Popup_Popup = (Popup);\n\n;// ./code/es5/es-modules/Extensions/Annotations/Popup/PopupComposition.js\n/* *\n *\n *  Popup generator for Stock tools\n *\n *  (c) 2009-2025 Sebastian Bochan\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar composed = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).composed;\n\n\nvar PopupComposition_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, pushUnique = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pushUnique, PopupComposition_wrap = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).wrap;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction compose(NagivationBindingsClass, PointerClass) {\n    if (pushUnique(composed, 'Popup')) {\n        PopupComposition_addEvent(NagivationBindingsClass, 'closePopup', onNavigationBindingsClosePopup);\n        PopupComposition_addEvent(NagivationBindingsClass, 'showPopup', onNavigationBindingsShowPopup);\n        PopupComposition_wrap(PointerClass.prototype, 'onContainerMouseDown', wrapPointerOnContainerMouserDown);\n    }\n}\n/**\n * @private\n */\nfunction onNavigationBindingsClosePopup() {\n    if (this.popup) {\n        this.popup.closePopup();\n    }\n}\n/**\n * @private\n */\nfunction onNavigationBindingsShowPopup(config) {\n    if (!this.popup) {\n        // Add popup to main container\n        this.popup = new Popup_Popup(this.chart.container, (this.chart.options.navigation.iconsURL ||\n            (this.chart.options.stockTools &&\n                this.chart.options.stockTools.gui.iconsURL) ||\n            'https://code.highcharts.com/12.2.0/gfx/stock-icons/'), this.chart);\n    }\n    this.popup.showForm(config.formType, this.chart, config.options, config.onSubmit);\n}\n/**\n * `onContainerMouseDown` blocks internal popup events, due to e.preventDefault.\n * Related issue #4606\n * @private\n */\nfunction wrapPointerOnContainerMouserDown(proceed, e) {\n    // Elements is not in popup\n    if (!this.inClass(e.target, 'highcharts-popup')) {\n        proceed.apply(this, Array.prototype.slice.call(arguments, 1));\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar PopupComposition = {\n    compose: compose\n};\n/* harmony default export */ var Popup_PopupComposition = (PopupComposition);\n\n;// ./code/es5/es-modules/Extensions/Annotations/Annotation.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar Annotation_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar getDeferredAnimation = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).getDeferredAnimation;\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar destroyObjectProperties = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).destroyObjectProperties, Annotation_erase = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).erase, Annotation_fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, Annotation_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, Annotation_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, splat = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).splat;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Hide or show annotation attached to points.\n * @private\n */\nfunction adjustVisibility(item) {\n    var label = item.graphic,\n        hasVisiblePoints = item.points.some(function (point) { return (point.series.visible !== false &&\n            point.visible !== false); });\n    if (label) {\n        if (!hasVisiblePoints) {\n            label.hide();\n        }\n        else if (label.visibility === 'hidden') {\n            label.show();\n        }\n    }\n}\n/**\n * @private\n */\nfunction getLabelsAndShapesOptions(baseOptions, newOptions) {\n    var mergedOptions = {};\n    ['labels', 'shapes'].forEach(function (name) {\n        var someBaseOptions = baseOptions[name],\n            newOptionsValue = newOptions[name];\n        if (someBaseOptions) {\n            if (newOptionsValue) {\n                mergedOptions[name] = splat(newOptionsValue).map(function (basicOptions, i) {\n                    return Annotation_merge(someBaseOptions[i], basicOptions);\n                });\n            }\n            else {\n                mergedOptions[name] = baseOptions[name];\n            }\n        }\n    });\n    return mergedOptions;\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * An annotation class which serves as a container for items like labels or\n * shapes. Created items are positioned on the chart either by linking them to\n * existing points or created mock points\n *\n * @requires modules/annotations\n *\n * @class\n * @name Highcharts.Annotation\n *\n * @param {Highcharts.Chart} chart\n *        A chart instance\n * @param {Highcharts.AnnotationsOptions} userOptions\n *        The annotation options\n */\nvar Annotation = /** @class */ (function (_super) {\n    Annotation_extends(Annotation, _super);\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    function Annotation(chart, userOptions) {\n        var _this = _super.call(this) || this;\n        _this.coll = 'annotations';\n        /**\n         * The chart that the annotation belongs to.\n         *\n         * @name Highcharts.Annotation#chart\n         * @type {Highcharts.Chart}\n         */\n        _this.chart = chart;\n        /**\n         * The array of points which defines the annotation.\n         * @private\n         * @name Highcharts.Annotation#points\n         * @type {Array<Highcharts.Point>}\n         */\n        _this.points = [];\n        /**\n         * The array of control points.\n         * @private\n         * @name Highcharts.Annotation#controlPoints\n         * @type {Array<Annotation.ControlPoint>}\n         */\n        _this.controlPoints = [];\n        _this.coll = 'annotations';\n        _this.index = -1;\n        /**\n         * The array of labels which belong to the annotation.\n         * @private\n         * @name Highcharts.Annotation#labels\n         * @type {Array<Highcharts.AnnotationLabelType>}\n         */\n        _this.labels = [];\n        /**\n         * The array of shapes which belong to the annotation.\n         * @private\n         * @name Highcharts.Annotation#shapes\n         * @type {Array<Highcharts.AnnotationShapeType>}\n         */\n        _this.shapes = [];\n        /**\n         * The options for the annotations.\n         *\n         * @name Highcharts.Annotation#options\n         * @type {Highcharts.AnnotationsOptions}\n         */\n        _this.options = Annotation_merge(_this.defaultOptions, userOptions);\n        /**\n         * The user options for the annotations.\n         *\n         * @name Highcharts.Annotation#userOptions\n         * @type {Highcharts.AnnotationsOptions}\n         */\n        _this.userOptions = userOptions;\n        // Handle labels and shapes - those are arrays\n        // Merging does not work with arrays (stores reference)\n        var labelsAndShapes = getLabelsAndShapesOptions(_this.options,\n            userOptions);\n        _this.options.labels = labelsAndShapes.labels;\n        _this.options.shapes = labelsAndShapes.shapes;\n        /**\n         * The callback that reports to the overlapping labels logic which\n         * labels it should account for.\n         * @private\n         * @name Highcharts.Annotation#labelCollector\n         * @type {Function}\n         */\n        /**\n         * The group svg element.\n         *\n         * @name Highcharts.Annotation#group\n         * @type {Highcharts.SVGElement}\n         */\n        /**\n         * The group svg element of the annotation's shapes.\n         *\n         * @name Highcharts.Annotation#shapesGroup\n         * @type {Highcharts.SVGElement}\n         */\n        /**\n         * The group svg element of the annotation's labels.\n         *\n         * @name Highcharts.Annotation#labelsGroup\n         * @type {Highcharts.SVGElement}\n         */\n        _this.init(chart, _this.options);\n        return _this;\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    Annotation.compose = function (ChartClass, NavigationBindingsClass, PointerClass, SVGRendererClass) {\n        Annotations_AnnotationChart.compose(Annotation, ChartClass, PointerClass);\n        Controllables_ControllableLabel.compose(SVGRendererClass);\n        Controllables_ControllablePath.compose(ChartClass, SVGRendererClass);\n        NavigationBindingsClass.compose(Annotation, ChartClass);\n        Popup_PopupComposition.compose(NavigationBindingsClass, PointerClass);\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    Annotation.prototype.addClipPaths = function () {\n        this.setClipAxes();\n        if (this.clipXAxis &&\n            this.clipYAxis &&\n            this.options.crop // #15399\n        ) {\n            this.clipRect = this.chart.renderer.clipRect(this.getClipBox());\n        }\n    };\n    /**\n     * @private\n     */\n    Annotation.prototype.addLabels = function () {\n        var _this = this;\n        var labelsOptions = (this.options.labels || []);\n        labelsOptions.forEach(function (labelOptions, i) {\n            var label = _this.initLabel(labelOptions,\n                i);\n            Annotation_merge(true, labelsOptions[i], label.options);\n        });\n    };\n    /**\n     * @private\n     */\n    Annotation.prototype.addShapes = function () {\n        var _this = this;\n        var shapes = this.options.shapes || [];\n        shapes.forEach(function (shapeOptions, i) {\n            var shape = _this.initShape(shapeOptions,\n                i);\n            Annotation_merge(true, shapes[i], shape.options);\n        });\n    };\n    /**\n     * Destroy the annotation. This function does not touch the chart\n     * that the annotation belongs to (all annotations are kept in\n     * the chart.annotations array) - it is recommended to use\n     * {@link Highcharts.Chart#removeAnnotation} instead.\n     * @private\n     */\n    Annotation.prototype.destroy = function () {\n        var chart = this.chart,\n            destroyItem = function (item) {\n                item.destroy();\n        };\n        this.labels.forEach(destroyItem);\n        this.shapes.forEach(destroyItem);\n        this.clipXAxis = null;\n        this.clipYAxis = null;\n        Annotation_erase(chart.labelCollectors, this.labelCollector);\n        _super.prototype.destroy.call(this);\n        this.destroyControlTarget();\n        destroyObjectProperties(this, chart);\n    };\n    /**\n     * Destroy a single item.\n     * @private\n     */\n    Annotation.prototype.destroyItem = function (item) {\n        // Erase from shapes or labels array\n        Annotation_erase(this[item.itemType + 's'], item);\n        item.destroy();\n    };\n    /**\n     * @private\n     */\n    Annotation.prototype.getClipBox = function () {\n        if (this.clipXAxis && this.clipYAxis) {\n            return {\n                x: this.clipXAxis.left,\n                y: this.clipYAxis.top,\n                width: this.clipXAxis.width,\n                height: this.clipYAxis.height\n            };\n        }\n    };\n    /**\n     * Initialize the annotation properties.\n     * @private\n     */\n    Annotation.prototype.initProperties = function (chart, userOptions) {\n        this.setOptions(userOptions);\n        var labelsAndShapes = getLabelsAndShapesOptions(this.options,\n            userOptions);\n        this.options.labels = labelsAndShapes.labels;\n        this.options.shapes = labelsAndShapes.shapes;\n        this.chart = chart;\n        this.points = [];\n        this.controlPoints = [];\n        this.coll = 'annotations';\n        this.userOptions = userOptions;\n        this.labels = [];\n        this.shapes = [];\n    };\n    /**\n     * Initialize the annotation.\n     * @private\n     */\n    Annotation.prototype.init = function (_annotationOrChart, _userOptions, index) {\n        if (index === void 0) { index = this.index; }\n        var chart = this.chart,\n            animOptions = this.options.animation;\n        this.index = index;\n        this.linkPoints();\n        this.addControlPoints();\n        this.addShapes();\n        this.addLabels();\n        this.setLabelCollector();\n        this.animationConfig = getDeferredAnimation(chart, animOptions);\n    };\n    /**\n     * Initialisation of a single label\n     * @private\n     */\n    Annotation.prototype.initLabel = function (labelOptions, index) {\n        var options = Annotation_merge(this.options.labelOptions, {\n                controlPointOptions: this.options.controlPointOptions\n            },\n            labelOptions),\n            label = new Controllables_ControllableLabel(this,\n            options,\n            index);\n        label.itemType = 'label';\n        this.labels.push(label);\n        return label;\n    };\n    /**\n     * Initialisation of a single shape\n     * @private\n     * @param {Object} shapeOptions\n     * a config object for a single shape\n     * @param {number} index\n     * annotation may have many shapes, this is the shape's index saved in\n     * shapes.index.\n     */\n    Annotation.prototype.initShape = function (shapeOptions, index) {\n        var options = Annotation_merge(this.options.shapeOptions, {\n                controlPointOptions: this.options.controlPointOptions\n            },\n            shapeOptions),\n            shape = new (Annotation.shapesMap[options.type])(this,\n            options,\n            index);\n        shape.itemType = 'shape';\n        this.shapes.push(shape);\n        return shape;\n    };\n    /**\n     * @private\n     */\n    Annotation.prototype.redraw = function (animation) {\n        this.linkPoints();\n        if (!this.graphic) {\n            this.render();\n        }\n        if (this.clipRect) {\n            this.clipRect.animate(this.getClipBox());\n        }\n        this.redrawItems(this.shapes, animation);\n        this.redrawItems(this.labels, animation);\n        this.redrawControlPoints(animation);\n    };\n    /**\n     * Redraw a single item.\n     * @private\n     */\n    Annotation.prototype.redrawItem = function (item, animation) {\n        item.linkPoints();\n        if (!item.shouldBeDrawn()) {\n            this.destroyItem(item);\n        }\n        else {\n            if (!item.graphic) {\n                this.renderItem(item);\n            }\n            item.redraw(Annotation_pick(animation, true) && item.graphic.placed);\n            if (item.points.length) {\n                adjustVisibility(item);\n            }\n        }\n    };\n    /**\n     * @private\n     */\n    Annotation.prototype.redrawItems = function (items, animation) {\n        var i = items.length;\n        // Needs a backward loop. Labels/shapes array might be modified due to\n        // destruction of the item\n        while (i--) {\n            this.redrawItem(items[i], animation);\n        }\n    };\n    /**\n     * See {@link Highcharts.Chart#removeAnnotation}.\n     * @private\n     */\n    Annotation.prototype.remove = function () {\n        // Let chart.update() remove annotations on demand\n        return this.chart.removeAnnotation(this);\n    };\n    /**\n     * @private\n     */\n    Annotation.prototype.render = function () {\n        var renderer = this.chart.renderer;\n        this.graphic = renderer\n            .g('annotation')\n            .attr({\n            opacity: 0,\n            zIndex: this.options.zIndex,\n            visibility: this.options.visible ?\n                'inherit' :\n                'hidden'\n        })\n            .add();\n        this.shapesGroup = renderer\n            .g('annotation-shapes')\n            .add(this.graphic);\n        if (this.options.crop) { // #15399\n            this.shapesGroup.clip(this.chart.plotBoxClip);\n        }\n        this.labelsGroup = renderer\n            .g('annotation-labels')\n            .attr({\n            // `hideOverlappingLabels` requires translation\n            translateX: 0,\n            translateY: 0\n        })\n            .add(this.graphic);\n        this.addClipPaths();\n        if (this.clipRect) {\n            this.graphic.clip(this.clipRect);\n        }\n        // Render shapes and labels before adding events (#13070).\n        this.renderItems(this.shapes);\n        this.renderItems(this.labels);\n        this.addEvents();\n        this.renderControlPoints();\n    };\n    /**\n     * @private\n     */\n    Annotation.prototype.renderItem = function (item) {\n        item.render(item.itemType === 'label' ?\n            this.labelsGroup :\n            this.shapesGroup);\n    };\n    /**\n     * @private\n     */\n    Annotation.prototype.renderItems = function (items) {\n        var i = items.length;\n        while (i--) {\n            this.renderItem(items[i]);\n        }\n    };\n    /**\n     * @private\n     */\n    Annotation.prototype.setClipAxes = function () {\n        var xAxes = this.chart.xAxis,\n            yAxes = this.chart.yAxis,\n            linkedAxes = (this.options.labels || [])\n                .concat(this.options.shapes || [])\n                .reduce(function (axes,\n            labelOrShape) {\n                var point = labelOrShape &&\n                    (labelOrShape.point ||\n                        (labelOrShape.points && labelOrShape.points[0]));\n            return [\n                xAxes[point && point.xAxis] || axes[0],\n                yAxes[point && point.yAxis] || axes[1]\n            ];\n        }, []);\n        this.clipXAxis = linkedAxes[0];\n        this.clipYAxis = linkedAxes[1];\n    };\n    /**\n     * @private\n     */\n    Annotation.prototype.setControlPointsVisibility = function (visible) {\n        var setItemControlPointsVisibility = function (item) {\n                item.setControlPointsVisibility(visible);\n        };\n        this.controlPoints.forEach(function (controlPoint) {\n            controlPoint.setVisibility(visible);\n        });\n        this.shapes.forEach(setItemControlPointsVisibility);\n        this.labels.forEach(setItemControlPointsVisibility);\n    };\n    /**\n     * @private\n     */\n    Annotation.prototype.setLabelCollector = function () {\n        var annotation = this;\n        annotation.labelCollector = function () {\n            return annotation.labels.reduce(function (labels, label) {\n                if (!label.options.allowOverlap) {\n                    labels.push(label.graphic);\n                }\n                return labels;\n            }, []);\n        };\n        annotation.chart.labelCollectors.push(annotation.labelCollector);\n    };\n    /**\n     * Set an annotation options.\n     * @private\n     * @param {Highcharts.AnnotationsOptions} userOptions\n     *        User options for an annotation\n     */\n    Annotation.prototype.setOptions = function (userOptions) {\n        this.options = Annotation_merge(this.defaultOptions, userOptions);\n    };\n    /**\n     * Set the annotation's visibility.\n     * @private\n     * @param {boolean} [visible]\n     * Whether to show or hide an annotation. If the param is omitted, the\n     * annotation's visibility is toggled.\n     */\n    Annotation.prototype.setVisibility = function (visible) {\n        var options = this.options,\n            navigation = this.chart.navigationBindings,\n            visibility = Annotation_pick(visible, !options.visible);\n        this.graphic.attr('visibility', visibility ? 'inherit' : 'hidden');\n        if (!visibility) {\n            var setItemControlPointsVisibility = function (item) {\n                    item.setControlPointsVisibility(visibility);\n            };\n            this.shapes.forEach(setItemControlPointsVisibility);\n            this.labels.forEach(setItemControlPointsVisibility);\n            if (navigation.activeAnnotation === this &&\n                navigation.popup &&\n                navigation.popup.type === 'annotation-toolbar') {\n                Annotation_fireEvent(navigation, 'closePopup');\n            }\n        }\n        options.visible = visibility;\n    };\n    /**\n     * Updates an annotation.\n     *\n     * @function Highcharts.Annotation#update\n     *\n     * @param {Partial<Highcharts.AnnotationsOptions>} userOptions\n     *        New user options for the annotation.\n     *\n     */\n    Annotation.prototype.update = function (userOptions, redraw) {\n        var chart = this.chart,\n            labelsAndShapes = getLabelsAndShapesOptions(this.userOptions,\n            userOptions),\n            userOptionsIndex = chart.annotations.indexOf(this),\n            options = Annotation_merge(true,\n            this.userOptions,\n            userOptions);\n        options.labels = labelsAndShapes.labels;\n        options.shapes = labelsAndShapes.shapes;\n        this.destroy();\n        this.initProperties(chart, options);\n        this.init(chart, options);\n        // Update options in chart options, used in exporting (#9767, #21507):\n        chart.options.annotations[userOptionsIndex] = this.options;\n        this.isUpdating = true;\n        if (Annotation_pick(redraw, true)) {\n            chart.drawAnnotations();\n        }\n        Annotation_fireEvent(this, 'afterUpdate');\n        this.isUpdating = false;\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    /**\n     * @private\n     */\n    Annotation.ControlPoint = Annotations_ControlPoint;\n    /**\n     * @private\n     */\n    Annotation.MockPoint = Annotations_MockPoint;\n    /**\n     * An object uses for mapping between a shape type and a constructor.\n     * To add a new shape type extend this object with type name as a key\n     * and a constructor as its value.\n     *\n     * @private\n     */\n    Annotation.shapesMap = {\n        'rect': Controllables_ControllableRect,\n        'circle': Controllables_ControllableCircle,\n        'ellipse': Controllables_ControllableEllipse,\n        'path': Controllables_ControllablePath,\n        'image': Controllables_ControllableImage\n    };\n    /**\n     * @private\n     */\n    Annotation.types = {};\n    return Annotation;\n}(Annotations_EventEmitter));\nAnnotation.prototype.defaultOptions = Annotations_AnnotationDefaults;\n/**\n * List of events for `annotation.options.events` that should not be\n * added to `annotation.graphic` but to the `annotation`.\n *\n * @private\n * @type {Array<string>}\n */\nAnnotation.prototype.nonDOMEvents = ['add', 'afterUpdate', 'drag', 'remove'];\nAnnotations_ControlTarget.compose(Annotation);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Annotations_Annotation = (Annotation);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Possible directions for draggable annotations. An empty string (`''`)\n * makes the annotation undraggable.\n *\n * @typedef {''|'x'|'xy'|'y'} Highcharts.AnnotationDraggableValue\n * @requires modules/annotations\n */\n/**\n * @private\n * @typedef {\n *          Highcharts.AnnotationControllableCircle|\n *          Highcharts.AnnotationControllableImage|\n *          Highcharts.AnnotationControllablePath|\n *          Highcharts.AnnotationControllableRect\n *     } Highcharts.AnnotationShapeType\n * @requires modules/annotations\n */\n/**\n * @private\n * @typedef {\n *          Highcharts.AnnotationControllableLabel\n *     } Highcharts.AnnotationLabelType\n * @requires modules/annotations\n */\n/**\n * A point-like object, a mock point or a point used in series.\n * @private\n * @typedef {\n *          Highcharts.AnnotationMockPoint|\n *          Highcharts.Point\n *     } Highcharts.AnnotationPointType\n * @requires modules/annotations\n */\n/**\n * Shape point as string, object or function.\n *\n * @typedef {\n *          string|\n *          Highcharts.AnnotationMockPointOptionsObject|\n *          Highcharts.AnnotationMockPointFunction\n *     } Highcharts.AnnotationShapePointOptions\n */\n(''); // Keeps doclets above in JS file\n\n;// ./code/es5/es-modules/Core/Chart/ChartNavigationComposition.js\n/**\n *\n *  (c) 2010-2025 Paweł Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Composition\n *\n * */\nvar ChartNavigationComposition;\n(function (ChartNavigationComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * @private\n     */\n    function compose(chart) {\n        if (!chart.navigation) {\n            chart.navigation = new Additions(chart);\n        }\n        return chart;\n    }\n    ChartNavigationComposition.compose = compose;\n    /* *\n     *\n     *  Class\n     *\n     * */\n    /**\n     * Initializes `chart.navigation` object which delegates `update()` methods\n     * to all other common classes (used in exporting and navigationBindings).\n     * @private\n     */\n    var Additions = /** @class */ (function () {\n            /* *\n             *\n             *  Constructor\n             *\n             * */\n            function Additions(chart) {\n                this.updates = [];\n            this.chart = chart;\n        }\n        /* *\n         *\n         *  Functions\n         *\n         * */\n        /**\n         * Registers an `update()` method in the `chart.navigation` object.\n         *\n         * @private\n         * @param {UpdateFunction} updateFn\n         * The `update()` method that will be called in `chart.update()`.\n         */\n        Additions.prototype.addUpdate = function (updateFn) {\n            this.chart.navigation.updates.push(updateFn);\n        };\n        /**\n         * @private\n         */\n        Additions.prototype.update = function (options, redraw) {\n            var _this = this;\n            this.updates.forEach(function (updateFn) {\n                updateFn.call(_this.chart, options, redraw);\n            });\n        };\n        return Additions;\n    }());\n    ChartNavigationComposition.Additions = Additions;\n})(ChartNavigationComposition || (ChartNavigationComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Chart_ChartNavigationComposition = (ChartNavigationComposition);\n\n;// ./code/es5/es-modules/Extensions/Annotations/NavigationBindingsUtilities.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar NavigationBindingsUtilities_defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, NavigationBindingsUtilities_isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, NavigationBindingsUtilities_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Constants\n *\n * */\n/**\n * Define types for editable fields per annotation. There is no need to define\n * numbers, because they won't change their type to string.\n * @private\n */\nvar annotationsFieldsTypes = {\n    backgroundColor: 'string',\n    borderColor: 'string',\n    borderRadius: 'string',\n    color: 'string',\n    fill: 'string',\n    fontSize: 'string',\n    labels: 'string',\n    name: 'string',\n    stroke: 'string',\n    title: 'string'\n};\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Returns the first xAxis or yAxis that was clicked with its value.\n *\n * @private\n *\n * @param {Array<Highcharts.PointerAxisCoordinateObject>} coords\n *        All the chart's x or y axes with a current pointer's axis value.\n *\n * @return {Highcharts.PointerAxisCoordinateObject}\n *         Object with a first found axis and its value that pointer\n *         is currently pointing.\n */\nfunction getAssignedAxis(coords) {\n    return coords.filter(function (coord) {\n        var extremes = coord.axis.getExtremes(),\n            axisMin = extremes.min,\n            axisMax = extremes.max, \n            // Correct axis edges when axis has series\n            // with pointRange (like column)\n            minPointOffset = NavigationBindingsUtilities_pick(coord.axis.minPointOffset, 0);\n        return NavigationBindingsUtilities_isNumber(axisMin) && NavigationBindingsUtilities_isNumber(axisMax) &&\n            coord.value >= (axisMin - minPointOffset) &&\n            coord.value <= (axisMax + minPointOffset) &&\n            // Don't count navigator axis\n            !coord.axis.options.isInternal;\n    })[0]; // If the axes overlap, return the first axis that was found.\n}\n/**\n * Get field type according to value\n *\n * @private\n *\n * @param {'boolean'|'number'|'string'} value\n * Atomic type (one of: string, number, boolean)\n *\n * @return {'checkbox'|'number'|'text'}\n * Field type (one of: text, number, checkbox)\n */\nfunction getFieldType(key, value) {\n    var predefinedType = annotationsFieldsTypes[key];\n    var fieldType = typeof value;\n    if (NavigationBindingsUtilities_defined(predefinedType)) {\n        fieldType = predefinedType;\n    }\n    return {\n        'string': 'text',\n        'number': 'number',\n        'boolean': 'checkbox'\n    }[fieldType];\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar NavigationBindingUtilities = {\n    annotationsFieldsTypes: annotationsFieldsTypes,\n    getAssignedAxis: getAssignedAxis,\n    getFieldType: getFieldType\n};\n/* harmony default export */ var NavigationBindingsUtilities = (NavigationBindingUtilities);\n\n;// ./code/es5/es-modules/Extensions/Annotations/NavigationBindingsDefaults.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar NavigationBindingsDefaults_getAssignedAxis = NavigationBindingsUtilities.getAssignedAxis;\n\nvar NavigationBindingsDefaults_isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, NavigationBindingsDefaults_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n *  Constants\n *\n * */\n/**\n * @optionparent lang\n */\nvar lang = {\n    /**\n     * Configure the Popup strings in the chart. Requires the\n     * `annotations.js` or `annotations-advanced.js` module to be\n     * loaded.\n     * @since   7.0.0\n     * @product highcharts highstock\n     */\n    navigation: {\n        /**\n         * Translations for all field names used in popup.\n         *\n         * @product highcharts highstock\n         */\n        popup: {\n            simpleShapes: 'Simple shapes',\n            lines: 'Lines',\n            circle: 'Circle',\n            ellipse: 'Ellipse',\n            rectangle: 'Rectangle',\n            label: 'Label',\n            shapeOptions: 'Shape options',\n            typeOptions: 'Details',\n            fill: 'Fill',\n            format: 'Text',\n            strokeWidth: 'Line width',\n            stroke: 'Line color',\n            title: 'Title',\n            name: 'Name',\n            labelOptions: 'Label options',\n            labels: 'Labels',\n            backgroundColor: 'Background color',\n            backgroundColors: 'Background colors',\n            borderColor: 'Border color',\n            borderRadius: 'Border radius',\n            borderWidth: 'Border width',\n            style: 'Style',\n            padding: 'Padding',\n            fontSize: 'Font size',\n            color: 'Color',\n            height: 'Height',\n            shapes: 'Shape options'\n        }\n    }\n};\n/**\n * @optionparent navigation\n * @product      highcharts highstock\n */\nvar navigation = {\n    /**\n     * A CSS class name where all bindings will be attached to. Multiple\n     * charts on the same page should have separate class names to prevent\n     * duplicating events.\n     *\n     * Default value of versions < 7.0.4 `highcharts-bindings-wrapper`\n     *\n     * @since     7.0.0\n     * @type      {string}\n     */\n    bindingsClassName: 'highcharts-bindings-container',\n    /**\n     * Bindings definitions for custom HTML buttons. Each binding implements\n     * simple event-driven interface:\n     *\n     * - `className`: classname used to bind event to\n     *\n     * - `init`: initial event, fired on button click\n     *\n     * - `start`: fired on first click on a chart\n     *\n     * - `steps`: array of sequential events fired one after another on each\n     *   of users clicks\n     *\n     * - `end`: last event to be called after last step event\n     *\n     * @type         {Highcharts.Dictionary<Highcharts.NavigationBindingsOptionsObject>|*}\n     *\n     * @sample {highstock} stock/stocktools/stocktools-thresholds\n     *               Custom bindings\n     * @sample {highcharts} highcharts/annotations/bindings/\n     *               Simple binding\n     * @sample {highcharts} highcharts/annotations/bindings-custom-annotation/\n     *               Custom annotation binding\n     *\n     * @since        7.0.0\n     * @requires     modules/annotations\n     * @product      highcharts highstock\n     */\n    bindings: {\n        /**\n         * A circle annotation bindings. Includes `start` and one event in\n         * `steps` array.\n         *\n         * @type    {Highcharts.NavigationBindingsOptionsObject}\n         * @default {\"className\": \"highcharts-circle-annotation\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n         */\n        circleAnnotation: {\n            /** @ignore-option */\n            className: 'highcharts-circle-annotation',\n            /** @ignore-option */\n            start: function (e) {\n                var _a;\n                var coords = (_a = this.chart.pointer) === null || _a === void 0 ? void 0 : _a.getCoordinates(e),\n                    coordsX = coords && NavigationBindingsDefaults_getAssignedAxis(coords.xAxis),\n                    coordsY = coords && NavigationBindingsDefaults_getAssignedAxis(coords.yAxis),\n                    navigation = this.chart.options.navigation;\n                // Exit if clicked out of axes area\n                if (!coordsX || !coordsY) {\n                    return;\n                }\n                return this.chart.addAnnotation(NavigationBindingsDefaults_merge({\n                    langKey: 'circle',\n                    type: 'basicAnnotation',\n                    shapes: [{\n                            type: 'circle',\n                            point: {\n                                x: coordsX.value,\n                                y: coordsY.value,\n                                xAxis: coordsX.axis.index,\n                                yAxis: coordsY.axis.index\n                            },\n                            r: 5\n                        }]\n                }, navigation.annotationsOptions, navigation.bindings.circleAnnotation\n                    .annotationsOptions));\n            },\n            /** @ignore-option */\n            steps: [\n                function (e, annotation) {\n                    var shapes = annotation.options.shapes,\n                        mockPointOpts = ((shapes && shapes[0] && shapes[0].point) ||\n                            {});\n                    var distance;\n                    if (NavigationBindingsDefaults_isNumber(mockPointOpts.xAxis) &&\n                        NavigationBindingsDefaults_isNumber(mockPointOpts.yAxis)) {\n                        var inverted = this.chart.inverted,\n                            x = this.chart.xAxis[mockPointOpts.xAxis]\n                                .toPixels(mockPointOpts.x),\n                            y = this.chart.yAxis[mockPointOpts.yAxis]\n                                .toPixels(mockPointOpts.y);\n                        distance = Math.max(Math.sqrt(Math.pow(inverted ? y - e.chartX : x - e.chartX, 2) +\n                            Math.pow(inverted ? x - e.chartY : y - e.chartY, 2)), 5);\n                    }\n                    annotation.update({\n                        shapes: [{\n                                r: distance\n                            }]\n                    });\n                }\n            ]\n        },\n        /**\n         * A ellipse annotation bindings. Includes `start` and two events in\n         * `steps` array. First updates the second point, responsible for a\n         * rx width, and second updates the ry width.\n         *\n         * @type    {Highcharts.NavigationBindingsOptionsObject}\n         * @default {\"className\": \"highcharts-ellipse-annotation\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n         */\n        ellipseAnnotation: {\n            className: 'highcharts-ellipse-annotation',\n            start: function (e) {\n                var _a;\n                var coords = (_a = this.chart.pointer) === null || _a === void 0 ? void 0 : _a.getCoordinates(e),\n                    coordsX = coords && NavigationBindingsDefaults_getAssignedAxis(coords.xAxis),\n                    coordsY = coords && NavigationBindingsDefaults_getAssignedAxis(coords.yAxis),\n                    navigation = this.chart.options.navigation;\n                if (!coordsX || !coordsY) {\n                    return;\n                }\n                return this.chart.addAnnotation(NavigationBindingsDefaults_merge({\n                    langKey: 'ellipse',\n                    type: 'basicAnnotation',\n                    shapes: [\n                        {\n                            type: 'ellipse',\n                            xAxis: coordsX.axis.index,\n                            yAxis: coordsY.axis.index,\n                            points: [{\n                                    x: coordsX.value,\n                                    y: coordsY.value\n                                }, {\n                                    x: coordsX.value,\n                                    y: coordsY.value\n                                }],\n                            ry: 1\n                        }\n                    ]\n                }, navigation.annotationsOptions, navigation.bindings.ellipseAnnotation\n                    .annotationsOptions));\n            },\n            steps: [\n                function (e, annotation) {\n                    var target = annotation.shapes[0],\n                        position = target.getAbsolutePosition(target.points[1]);\n                    target.translatePoint(e.chartX - position.x, e.chartY - position.y, 1);\n                    target.redraw(false);\n                },\n                function (e, annotation) {\n                    var target = annotation.shapes[0],\n                        position = target.getAbsolutePosition(target.points[0]),\n                        position2 = target.getAbsolutePosition(target.points[1]),\n                        newR = target.getDistanceFromLine(position,\n                        position2,\n                        e.chartX,\n                        e.chartY),\n                        yAxis = target.getYAxis(),\n                        newRY = Math.abs(yAxis.toValue(0) - yAxis.toValue(newR));\n                    target.setYRadius(newRY);\n                    target.redraw(false);\n                }\n            ]\n        },\n        /**\n         * A rectangle annotation bindings. Includes `start` and one event\n         * in `steps` array.\n         *\n         * @type    {Highcharts.NavigationBindingsOptionsObject}\n         * @default {\"className\": \"highcharts-rectangle-annotation\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n         */\n        rectangleAnnotation: {\n            /** @ignore-option */\n            className: 'highcharts-rectangle-annotation',\n            /** @ignore-option */\n            start: function (e) {\n                var _a;\n                var coords = (_a = this.chart.pointer) === null || _a === void 0 ? void 0 : _a.getCoordinates(e),\n                    coordsX = coords && NavigationBindingsDefaults_getAssignedAxis(coords.xAxis),\n                    coordsY = coords && NavigationBindingsDefaults_getAssignedAxis(coords.yAxis);\n                // Exit if clicked out of axes area\n                if (!coordsX || !coordsY) {\n                    return;\n                }\n                var x = coordsX.value,\n                    y = coordsY.value,\n                    xAxis = coordsX.axis.index,\n                    yAxis = coordsY.axis.index,\n                    navigation = this.chart.options.navigation;\n                return this.chart.addAnnotation(NavigationBindingsDefaults_merge({\n                    langKey: 'rectangle',\n                    type: 'basicAnnotation',\n                    shapes: [{\n                            type: 'path',\n                            points: [\n                                { xAxis: xAxis, yAxis: yAxis, x: x, y: y },\n                                { xAxis: xAxis, yAxis: yAxis, x: x, y: y },\n                                { xAxis: xAxis, yAxis: yAxis, x: x, y: y },\n                                { xAxis: xAxis, yAxis: yAxis, x: x, y: y },\n                                { command: 'Z' }\n                            ]\n                        }]\n                }, navigation\n                    .annotationsOptions, navigation\n                    .bindings\n                    .rectangleAnnotation\n                    .annotationsOptions));\n            },\n            /** @ignore-option */\n            steps: [\n                function (e, annotation) {\n                    var _a;\n                    var shapes = annotation.options.shapes,\n                        points = ((shapes && shapes[0] && shapes[0].points) ||\n                            []),\n                        coords = (_a = this.chart.pointer) === null || _a === void 0 ? void 0 : _a.getCoordinates(e),\n                        coordsX = coords && NavigationBindingsDefaults_getAssignedAxis(coords.xAxis),\n                        coordsY = coords && NavigationBindingsDefaults_getAssignedAxis(coords.yAxis);\n                    if (coordsX && coordsY) {\n                        var x = coordsX.value,\n                            y = coordsY.value;\n                        // Top right point\n                        points[1].x = x;\n                        // Bottom right point (cursor position)\n                        points[2].x = x;\n                        points[2].y = y;\n                        // Bottom left\n                        points[3].y = y;\n                        annotation.update({\n                            shapes: [{\n                                    points: points\n                                }]\n                        });\n                    }\n                }\n            ]\n        },\n        /**\n         * A label annotation bindings. Includes `start` event only.\n         *\n         * @type    {Highcharts.NavigationBindingsOptionsObject}\n         * @default {\"className\": \"highcharts-label-annotation\", \"start\": function() {}, \"steps\": [function() {}], \"annotationsOptions\": {}}\n         */\n        labelAnnotation: {\n            /** @ignore-option */\n            className: 'highcharts-label-annotation',\n            /** @ignore-option */\n            start: function (e) {\n                var _a;\n                var coords = (_a = this.chart.pointer) === null || _a === void 0 ? void 0 : _a.getCoordinates(e),\n                    coordsX = coords && NavigationBindingsDefaults_getAssignedAxis(coords.xAxis),\n                    coordsY = coords && NavigationBindingsDefaults_getAssignedAxis(coords.yAxis),\n                    navigation = this.chart.options.navigation;\n                // Exit if clicked out of axes area\n                if (!coordsX || !coordsY) {\n                    return;\n                }\n                return this.chart.addAnnotation(NavigationBindingsDefaults_merge({\n                    langKey: 'label',\n                    type: 'basicAnnotation',\n                    labelOptions: {\n                        format: '{y:.2f}',\n                        overflow: 'none',\n                        crop: true\n                    },\n                    labels: [{\n                            point: {\n                                xAxis: coordsX.axis.index,\n                                yAxis: coordsY.axis.index,\n                                x: coordsX.value,\n                                y: coordsY.value\n                            }\n                        }]\n                }, navigation\n                    .annotationsOptions, navigation\n                    .bindings\n                    .labelAnnotation\n                    .annotationsOptions));\n            }\n        }\n    },\n    /**\n     * Path where Highcharts will look for icons. Change this to use icons\n     * from a different server.\n     *\n     * @type      {string}\n     * @default   https://code.highcharts.com/12.2.0/gfx/stock-icons/\n     * @since     7.1.3\n     * @apioption navigation.iconsURL\n     */\n    /**\n     * A `showPopup` event. Fired when selecting for example an annotation.\n     *\n     * @type      {Function}\n     * @apioption navigation.events.showPopup\n     */\n    /**\n     * A `closePopup` event. Fired when Popup should be hidden, for example\n     * when clicking on an annotation again.\n     *\n     * @type      {Function}\n     * @apioption navigation.events.closePopup\n     */\n    /**\n     * Event fired on a button click.\n     *\n     * @type      {Function}\n     * @sample    highcharts/annotations/gui/\n     *            Change icon in a dropddown on event\n     * @sample    highcharts/annotations/gui-buttons/\n     *            Change button class on event\n     * @apioption navigation.events.selectButton\n     */\n    /**\n     * Event fired when button state should change, for example after\n     * adding an annotation.\n     *\n     * @type      {Function}\n     * @sample    highcharts/annotations/gui/\n     *            Change icon in a dropddown on event\n     * @sample    highcharts/annotations/gui-buttons/\n     *            Change button class on event\n     * @apioption navigation.events.deselectButton\n     */\n    /**\n     * Events to communicate between Stock Tools and custom GUI.\n     *\n     * @since        7.0.0\n     * @product      highcharts highstock\n     * @optionparent navigation.events\n     */\n    events: {},\n    /**\n     * Additional options to be merged into all annotations.\n     *\n     * @sample stock/stocktools/navigation-annotation-options\n     *         Set red color of all line annotations\n     *\n     * @type      {Highcharts.AnnotationsOptions}\n     * @extends   annotations\n     * @exclude   crookedLine, elliottWave, fibonacci, infinityLine,\n     *            measure, pitchfork, tunnel, verticalLine, basicAnnotation\n     * @requires     modules/annotations\n     * @apioption navigation.annotationsOptions\n     */\n    annotationsOptions: {\n        animation: {\n            defer: 0\n        }\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\nvar NavigationBindingDefaults = {\n    lang: lang,\n    navigation: navigation\n};\n/* harmony default export */ var NavigationBindingsDefaults = (NavigationBindingDefaults);\n\n;// ./code/es5/es-modules/Extensions/Annotations/NavigationBindings.js\n/* *\n *\n *  (c) 2009-2025 Highsoft, Black Label\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nvar setOptions = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).setOptions;\n\nvar NavigationBindings_format = (highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default()).format;\n\nvar NavigationBindings_composed = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).composed, NavigationBindings_doc = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).doc, win = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).win;\n\n\nvar NavigationBindings_getAssignedAxis = NavigationBindingsUtilities.getAssignedAxis, NavigationBindings_getFieldType = NavigationBindingsUtilities.getFieldType;\n\nvar NavigationBindings_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, attr = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).attr, NavigationBindings_defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, NavigationBindings_fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, NavigationBindings_isArray = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isArray, isFunction = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isFunction, NavigationBindings_isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, NavigationBindings_isObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isObject, NavigationBindings_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, NavigationBindings_objectEach = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).objectEach, NavigationBindings_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, NavigationBindings_pushUnique = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pushUnique;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * IE 9-11 polyfill for Element.closest():\n * @private\n */\nfunction closestPolyfill(el, s) {\n    var ElementProto = win.Element.prototype,\n        elementMatches = ElementProto.matches ||\n            ElementProto.msMatchesSelector ||\n            ElementProto.webkitMatchesSelector;\n    var ret = null;\n    if (ElementProto.closest) {\n        ret = ElementProto.closest.call(el, s);\n    }\n    else {\n        do {\n            if (elementMatches.call(el, s)) {\n                return el;\n            }\n            el = el.parentElement || el.parentNode;\n        } while (el !== null && el.nodeType === 1);\n    }\n    return ret;\n}\n/**\n * @private\n */\nfunction onAnnotationRemove() {\n    if (this.chart.navigationBindings) {\n        this.chart.navigationBindings.deselectAnnotation();\n    }\n}\n/**\n * @private\n */\nfunction onChartDestroy() {\n    if (this.navigationBindings) {\n        this.navigationBindings.destroy();\n    }\n}\n/**\n * @private\n */\nfunction onChartLoad() {\n    var options = this.options;\n    if (options && options.navigation && options.navigation.bindings) {\n        this.navigationBindings = new NavigationBindings(this, options.navigation);\n        this.navigationBindings.initEvents();\n        this.navigationBindings.initUpdate();\n    }\n}\n/**\n * @private\n */\nfunction onChartRender() {\n    var navigationBindings = this.navigationBindings,\n        disabledClassName = 'highcharts-disabled-btn';\n    if (this && navigationBindings) {\n        // Check if the buttons should be enabled/disabled based on\n        // visible series.\n        var buttonsEnabled_1 = false;\n        this.series.forEach(function (series) {\n            if (!series.options.isInternal && series.visible) {\n                buttonsEnabled_1 = true;\n            }\n        });\n        if (this.navigationBindings &&\n            this.navigationBindings.container &&\n            this.navigationBindings.container[0]) {\n            var container_1 = this.navigationBindings.container[0];\n            NavigationBindings_objectEach(navigationBindings.boundClassNames, function (value, key) {\n                // Get the HTML element corresponding to the className taken\n                // from StockToolsBindings.\n                var buttonNode = container_1.querySelectorAll('.' + key);\n                if (buttonNode) {\n                    for (var i = 0; i < buttonNode.length; i++) {\n                        var button = buttonNode[i],\n                            cls = button.className;\n                        if (value.noDataState === 'normal') {\n                            // If button has noDataState: 'normal', and has\n                            // disabledClassName, remove this className.\n                            if (cls.indexOf(disabledClassName) !== -1) {\n                                button.classList.remove(disabledClassName);\n                            }\n                        }\n                        else if (!buttonsEnabled_1) {\n                            if (cls.indexOf(disabledClassName) === -1) {\n                                button.className += ' ' + disabledClassName;\n                            }\n                        }\n                        else {\n                            // Enable all buttons by deleting the className.\n                            if (cls.indexOf(disabledClassName) !== -1) {\n                                button.classList.remove(disabledClassName);\n                            }\n                        }\n                    }\n                }\n            });\n        }\n    }\n}\n/**\n * @private\n */\nfunction NavigationBindings_onNavigationBindingsClosePopup() {\n    this.deselectAnnotation();\n}\n/**\n * @private\n */\nfunction onNavigationBindingsDeselectButton() {\n    this.selectedButtonElement = null;\n}\n/**\n * Show edit-annotation form:\n * @private\n */\nfunction selectableAnnotation(annotationType) {\n    var originalClick = annotationType.prototype.defaultOptions.events &&\n            annotationType.prototype.defaultOptions.events.click;\n    /**\n     * Select and show popup\n     * @private\n     */\n    function selectAndShowPopup(eventArguments) {\n        var annotation = this,\n            navigation = annotation.chart.navigationBindings,\n            prevAnnotation = navigation.activeAnnotation;\n        if (originalClick) {\n            originalClick.call(annotation, eventArguments);\n        }\n        if (prevAnnotation !== annotation) {\n            // Select current:\n            navigation.deselectAnnotation();\n            navigation.activeAnnotation = annotation;\n            annotation.setControlPointsVisibility(true);\n            NavigationBindings_fireEvent(navigation, 'showPopup', {\n                annotation: annotation,\n                formType: 'annotation-toolbar',\n                options: navigation.annotationToFields(annotation),\n                onSubmit: function (data) {\n                    if (data.actionType === 'remove') {\n                        navigation.activeAnnotation = false;\n                        navigation.chart.removeAnnotation(annotation);\n                    }\n                    else {\n                        var config = {};\n                        navigation.fieldsToOptions(data.fields, config);\n                        navigation.deselectAnnotation();\n                        var typeOptions = config.typeOptions;\n                        if (annotation.options.type === 'measure') {\n                            // Manually disable crooshars according to\n                            // stroke width of the shape:\n                            typeOptions.crosshairY.enabled = (typeOptions.crosshairY\n                                .strokeWidth !== 0);\n                            typeOptions.crosshairX.enabled = (typeOptions.crosshairX\n                                .strokeWidth !== 0);\n                        }\n                        annotation.update(config);\n                    }\n                }\n            });\n        }\n        else {\n            // Deselect current:\n            NavigationBindings_fireEvent(navigation, 'closePopup');\n        }\n        // Let bubble event to chart.click:\n        eventArguments.activeAnnotation = true;\n    }\n    // #18276, show popup on touchend, but not on touchmove\n    var touchStartX,\n        touchStartY;\n    /**\n     *\n     */\n    function saveCoords(e) {\n        touchStartX = e.touches[0].clientX;\n        touchStartY = e.touches[0].clientY;\n    }\n    /**\n     *\n     */\n    function checkForTouchmove(e) {\n        var hasMoved = touchStartX ? Math.sqrt(Math.pow(touchStartX - e.changedTouches[0].clientX, 2) +\n                Math.pow(touchStartY - e.changedTouches[0].clientY, 2)) >= 4 : false;\n        if (!hasMoved) {\n            selectAndShowPopup.call(this, e);\n        }\n    }\n    NavigationBindings_merge(true, annotationType.prototype.defaultOptions.events, {\n        click: selectAndShowPopup,\n        touchstart: saveCoords,\n        touchend: checkForTouchmove\n    });\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n */\nvar NavigationBindings = /** @class */ (function () {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    function NavigationBindings(chart, options) {\n        this.boundClassNames = void 0;\n        this.chart = chart;\n        this.options = options;\n        this.eventsToUnbind = [];\n        this.container =\n            this.chart.container.getElementsByClassName(this.options.bindingsClassName || '');\n        if (!this.container.length) {\n            this.container = NavigationBindings_doc.getElementsByClassName(this.options.bindingsClassName || '');\n        }\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    NavigationBindings.compose = function (AnnotationClass, ChartClass) {\n        if (NavigationBindings_pushUnique(NavigationBindings_composed, 'NavigationBindings')) {\n            NavigationBindings_addEvent(AnnotationClass, 'remove', onAnnotationRemove);\n            // Basic shapes:\n            selectableAnnotation(AnnotationClass);\n            // Advanced annotations:\n            NavigationBindings_objectEach(AnnotationClass.types, function (annotationType) {\n                selectableAnnotation(annotationType);\n            });\n            NavigationBindings_addEvent(ChartClass, 'destroy', onChartDestroy);\n            NavigationBindings_addEvent(ChartClass, 'load', onChartLoad);\n            NavigationBindings_addEvent(ChartClass, 'render', onChartRender);\n            NavigationBindings_addEvent(NavigationBindings, 'closePopup', NavigationBindings_onNavigationBindingsClosePopup);\n            NavigationBindings_addEvent(NavigationBindings, 'deselectButton', onNavigationBindingsDeselectButton);\n            setOptions(NavigationBindingsDefaults);\n        }\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    NavigationBindings.prototype.getCoords = function (e) {\n        var _a;\n        var coords = (_a = this.chart.pointer) === null || _a === void 0 ? void 0 : _a.getCoordinates(e);\n        return [\n            coords && NavigationBindings_getAssignedAxis(coords.xAxis),\n            coords && NavigationBindings_getAssignedAxis(coords.yAxis)\n        ];\n    };\n    /**\n     * Init all events connected to NavigationBindings.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#initEvents\n     */\n    NavigationBindings.prototype.initEvents = function () {\n        var navigation = this,\n            chart = navigation.chart,\n            bindingsContainer = navigation.container,\n            options = navigation.options;\n        // Shorthand object for getting events for buttons:\n        navigation.boundClassNames = {};\n        NavigationBindings_objectEach((options.bindings || {}), function (value) {\n            navigation.boundClassNames[value.className] = value;\n        });\n        // Handle multiple containers with the same class names:\n        [].forEach.call(bindingsContainer, function (subContainer) {\n            navigation.eventsToUnbind.push(NavigationBindings_addEvent(subContainer, 'click', function (event) {\n                var bindings = navigation.getButtonEvents(subContainer,\n                    event);\n                if (bindings &&\n                    (!bindings.button.classList\n                        .contains('highcharts-disabled-btn'))) {\n                    navigation.bindingsButtonClick(bindings.button, bindings.events, event);\n                }\n            }));\n        });\n        NavigationBindings_objectEach((options.events || {}), function (callback, eventName) {\n            if (isFunction(callback)) {\n                navigation.eventsToUnbind.push(NavigationBindings_addEvent(navigation, eventName, callback, { passive: false }));\n            }\n        });\n        navigation.eventsToUnbind.push(NavigationBindings_addEvent(chart.container, 'click', function (e) {\n            if (!chart.cancelClick &&\n                chart.isInsidePlot(e.chartX - chart.plotLeft, e.chartY - chart.plotTop, {\n                    visiblePlotOnly: true\n                })) {\n                navigation.bindingsChartClick(this, e);\n            }\n        }));\n        navigation.eventsToUnbind.push(NavigationBindings_addEvent(chart.container, (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isTouchDevice ? 'touchmove' : 'mousemove', function (e) {\n            navigation.bindingsContainerMouseMove(this, e);\n        }, (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isTouchDevice ? { passive: false } : void 0));\n    };\n    /**\n     * Common chart.update() delegation, shared between bindings and exporting.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#initUpdate\n     */\n    NavigationBindings.prototype.initUpdate = function () {\n        var navigation = this;\n        Chart_ChartNavigationComposition\n            .compose(this.chart).navigation\n            .addUpdate(function (options) {\n            navigation.update(options);\n        });\n    };\n    /**\n     * Hook for click on a button, method selects/unselects buttons,\n     * then calls `bindings.init` callback.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#bindingsButtonClick\n     *\n     * @param {Highcharts.HTMLDOMElement} [button]\n     *        Clicked button\n     *\n     * @param {Object} events\n     *        Events passed down from bindings (`init`, `start`, `step`, `end`)\n     *\n     * @param {Highcharts.PointerEventObject} clickEvent\n     *        Browser's click event\n     */\n    NavigationBindings.prototype.bindingsButtonClick = function (button, events, clickEvent) {\n        var navigation = this,\n            chart = navigation.chart,\n            svgContainer = chart.renderer.boxWrapper;\n        var shouldEventBeFired = true;\n        if (navigation.selectedButtonElement) {\n            if (navigation.selectedButtonElement.classList === button.classList) {\n                shouldEventBeFired = false;\n            }\n            NavigationBindings_fireEvent(navigation, 'deselectButton', { button: navigation.selectedButtonElement });\n            if (navigation.nextEvent) {\n                // Remove in-progress annotations adders:\n                if (navigation.currentUserDetails &&\n                    navigation.currentUserDetails.coll === 'annotations') {\n                    chart.removeAnnotation(navigation.currentUserDetails);\n                }\n                navigation.mouseMoveEvent = navigation.nextEvent = false;\n            }\n        }\n        if (shouldEventBeFired) {\n            navigation.selectedButton = events;\n            navigation.selectedButtonElement = button;\n            NavigationBindings_fireEvent(navigation, 'selectButton', { button: button });\n            // Call \"init\" event, for example to open modal window\n            if (events.init) {\n                events.init.call(navigation, button, clickEvent);\n            }\n            if (events.start || events.steps) {\n                chart.renderer.boxWrapper.addClass('highcharts-draw-mode');\n            }\n        }\n        else {\n            chart.stockTools && button.classList.remove('highcharts-active');\n            svgContainer.removeClass('highcharts-draw-mode');\n            navigation.nextEvent = false;\n            navigation.mouseMoveEvent = false;\n            navigation.selectedButton = null;\n        }\n    };\n    /**\n     * Hook for click on a chart, first click on a chart calls `start` event,\n     * then on all subsequent clicks iterate over `steps` array.\n     * When finished, calls `end` event.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#bindingsChartClick\n     *\n     * @param {Highcharts.Chart} chart\n     *        Chart that click was performed on.\n     *\n     * @param {Highcharts.PointerEventObject} clickEvent\n     *        Browser's click event.\n     */\n    NavigationBindings.prototype.bindingsChartClick = function (chart, clickEvent) {\n        chart = this.chart;\n        var navigation = this,\n            activeAnnotation = navigation.activeAnnotation,\n            selectedButton = navigation.selectedButton,\n            svgContainer = chart.renderer.boxWrapper;\n        if (activeAnnotation) {\n            // Click outside popups, should close them and deselect the\n            // annotation\n            if (!activeAnnotation.cancelClick && // #15729\n                !clickEvent.activeAnnotation &&\n                // Element could be removed in the child action, e.g. button\n                clickEvent.target.parentNode &&\n                // TO DO: Polyfill for IE11?\n                !closestPolyfill(clickEvent.target, '.highcharts-popup')) {\n                NavigationBindings_fireEvent(navigation, 'closePopup');\n            }\n            else if (activeAnnotation.cancelClick) {\n                // Reset cancelClick after the other event handlers have run\n                setTimeout(function () {\n                    activeAnnotation.cancelClick = false;\n                }, 0);\n            }\n        }\n        if (!selectedButton || !selectedButton.start) {\n            return;\n        }\n        if (!navigation.nextEvent) {\n            // Call init method:\n            navigation.currentUserDetails = selectedButton.start.call(navigation, clickEvent);\n            // If steps exists (e.g. Annotations), bind them:\n            if (navigation.currentUserDetails && selectedButton.steps) {\n                navigation.stepIndex = 0;\n                navigation.steps = true;\n                navigation.mouseMoveEvent = navigation.nextEvent =\n                    selectedButton.steps[navigation.stepIndex];\n            }\n            else {\n                NavigationBindings_fireEvent(navigation, 'deselectButton', { button: navigation.selectedButtonElement });\n                svgContainer.removeClass('highcharts-draw-mode');\n                navigation.steps = false;\n                navigation.selectedButton = null;\n                // First click is also the last one:\n                if (selectedButton.end) {\n                    selectedButton.end.call(navigation, clickEvent, navigation.currentUserDetails);\n                }\n            }\n        }\n        else {\n            navigation.nextEvent(clickEvent, navigation.currentUserDetails);\n            if (navigation.steps) {\n                navigation.stepIndex++;\n                if (selectedButton.steps[navigation.stepIndex]) {\n                    // If we have more steps, bind them one by one:\n                    navigation.mouseMoveEvent = navigation.nextEvent = selectedButton.steps[navigation.stepIndex];\n                }\n                else {\n                    NavigationBindings_fireEvent(navigation, 'deselectButton', { button: navigation.selectedButtonElement });\n                    svgContainer.removeClass('highcharts-draw-mode');\n                    // That was the last step, call end():\n                    if (selectedButton.end) {\n                        selectedButton.end.call(navigation, clickEvent, navigation.currentUserDetails);\n                    }\n                    navigation.nextEvent = false;\n                    navigation.mouseMoveEvent = false;\n                    navigation.selectedButton = null;\n                }\n            }\n        }\n    };\n    /**\n     * Hook for mouse move on a chart's container. It calls current step.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#bindingsContainerMouseMove\n     *\n     * @param {Highcharts.HTMLDOMElement} container\n     *        Chart's container.\n     *\n     * @param {global.Event} moveEvent\n     *        Browser's move event.\n     */\n    NavigationBindings.prototype.bindingsContainerMouseMove = function (_container, moveEvent) {\n        if (this.mouseMoveEvent) {\n            this.mouseMoveEvent(moveEvent, this.currentUserDetails);\n        }\n    };\n    /**\n     * Translate fields (e.g. `params.period` or `marker.styles.color`) to\n     * Highcharts options object (e.g. `{ params: { period } }`).\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#fieldsToOptions<T>\n     *\n     * @param {Highcharts.Dictionary<string>} fields\n     *        Fields from popup form.\n     *\n     * @param {T} config\n     *        Default config to be modified.\n     *\n     * @return {T}\n     *         Modified config\n     */\n    NavigationBindings.prototype.fieldsToOptions = function (fields, config) {\n        NavigationBindings_objectEach(fields, function (value, field) {\n            var parsedValue = parseFloat(value),\n                path = field.split('.'),\n                pathLength = path.length - 1;\n            // If it's a number (not \"format\" options), parse it:\n            if (NavigationBindings_isNumber(parsedValue) &&\n                !value.match(/px|em/g) &&\n                !field.match(/format/g)) {\n                value = parsedValue;\n            }\n            // Remove values like 0\n            if (value !== 'undefined') {\n                var parent_1 = config;\n                path.forEach(function (name, index) {\n                    if (name !== '__proto__' && name !== 'constructor') {\n                        var nextName = NavigationBindings_pick(path[index + 1], '');\n                        if (pathLength === index) {\n                            // Last index, put value:\n                            parent_1[name] = value;\n                        }\n                        else if (!parent_1[name]) {\n                            // Create middle property:\n                            parent_1[name] = nextName.match(/\\d/g) ?\n                                [] :\n                                {};\n                            parent_1 = parent_1[name];\n                        }\n                        else {\n                            // Jump into next property\n                            parent_1 = parent_1[name];\n                        }\n                    }\n                });\n            }\n        });\n        return config;\n    };\n    /**\n     * Shorthand method to deselect an annotation.\n     *\n     * @function Highcharts.NavigationBindings#deselectAnnotation\n     */\n    NavigationBindings.prototype.deselectAnnotation = function () {\n        if (this.activeAnnotation) {\n            this.activeAnnotation.setControlPointsVisibility(false);\n            this.activeAnnotation = false;\n        }\n    };\n    /**\n     * Generates API config for popup in the same format as options for\n     * Annotation object.\n     *\n     * @function Highcharts.NavigationBindings#annotationToFields\n     *\n     * @param {Highcharts.Annotation} annotation\n     *        Annotations object\n     *\n     * @return {Highcharts.Dictionary<string>}\n     *         Annotation options to be displayed in popup box\n     */\n    NavigationBindings.prototype.annotationToFields = function (annotation) {\n        var options = annotation.options,\n            editables = NavigationBindings.annotationsEditable,\n            nestedEditables = editables.nestedOptions,\n            type = NavigationBindings_pick(options.type,\n            options.shapes && options.shapes[0] &&\n                options.shapes[0].type,\n            options.labels && options.labels[0] &&\n                options.labels[0].type, 'label'),\n            nonEditables = NavigationBindings.annotationsNonEditable[options.langKey] || [],\n            visualOptions = {\n                langKey: options.langKey,\n                type: type\n            };\n        /**\n         * Nested options traversing. Method goes down to the options and copies\n         * allowed options (with values) to new object, which is last parameter:\n         * \"parent\".\n         *\n         * @private\n         *\n         * @param {*} option\n         *        Atomic type or object/array\n         *\n         * @param {string} key\n         *        Option name, for example \"visible\" or \"x\", \"y\"\n         *\n         * @param {Object} parentEditables\n         *        Editables from NavigationBindings.annotationsEditable\n         *\n         * @param {Object} parent\n         *        Where new options will be assigned\n         */\n        function traverse(option, key, parentEditables, parent, parentKey) {\n            var nextParent;\n            if (parentEditables &&\n                NavigationBindings_defined(option) &&\n                nonEditables.indexOf(key) === -1 &&\n                ((parentEditables.indexOf &&\n                    parentEditables.indexOf(key)) >= 0 ||\n                    parentEditables[key] || // Nested array\n                    parentEditables === true // Simple array\n                )) {\n                // Roots:\n                if (NavigationBindings_isArray(option)) {\n                    parent[key] = [];\n                    option.forEach(function (arrayOption, i) {\n                        if (!NavigationBindings_isObject(arrayOption)) {\n                            // Simple arrays, e.g. [String, Number, Boolean]\n                            traverse(arrayOption, 0, nestedEditables[key], parent[key], key);\n                        }\n                        else {\n                            // Advanced arrays, e.g. [Object, Object]\n                            parent[key][i] = {};\n                            NavigationBindings_objectEach(arrayOption, function (nestedOption, nestedKey) {\n                                traverse(nestedOption, nestedKey, nestedEditables[key], parent[key][i], key);\n                            });\n                        }\n                    });\n                }\n                else if (NavigationBindings_isObject(option)) {\n                    nextParent = {};\n                    if (NavigationBindings_isArray(parent)) {\n                        parent.push(nextParent);\n                        nextParent[key] = {};\n                        nextParent = nextParent[key];\n                    }\n                    else {\n                        parent[key] = nextParent;\n                    }\n                    NavigationBindings_objectEach(option, function (nestedOption, nestedKey) {\n                        traverse(nestedOption, nestedKey, key === 0 ?\n                            parentEditables :\n                            nestedEditables[key], nextParent, key);\n                    });\n                }\n                else {\n                    // Leaf:\n                    if (key === 'format') {\n                        parent[key] = [\n                            NavigationBindings_format(option, annotation.labels[0].points[0]).toString(),\n                            'text'\n                        ];\n                    }\n                    else if (NavigationBindings_isArray(parent)) {\n                        parent.push([option, NavigationBindings_getFieldType(parentKey, option)]);\n                    }\n                    else {\n                        parent[key] = [option, NavigationBindings_getFieldType(key, option)];\n                    }\n                }\n            }\n        }\n        NavigationBindings_objectEach(options, function (option, key) {\n            if (key === 'typeOptions') {\n                visualOptions[key] = {};\n                NavigationBindings_objectEach(options[key], function (typeOption, typeKey) {\n                    traverse(typeOption, typeKey, nestedEditables, visualOptions[key], typeKey);\n                });\n            }\n            else {\n                traverse(option, key, editables[type], visualOptions, key);\n            }\n        });\n        return visualOptions;\n    };\n    /**\n     * Get all class names for all parents in the element. Iterates until finds\n     * main container.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#getClickedClassNames\n     *\n     * @param {Highcharts.HTMLDOMElement} container\n     * Container that event is bound to.\n     *\n     * @param {global.Event} event\n     * Browser's event.\n     *\n     * @return {Array<Array<string, Highcharts.HTMLDOMElement>>}\n     * Array of class names with corresponding elements\n     */\n    NavigationBindings.prototype.getClickedClassNames = function (container, event) {\n        var element = event.target,\n            classNames = [],\n            elemClassName;\n        while (element && element.tagName) {\n            elemClassName = attr(element, 'class');\n            if (elemClassName) {\n                classNames = classNames.concat(elemClassName\n                    .split(' ')\n                    // eslint-disable-next-line no-loop-func\n                    .map(function (name) { return ([name, element]); }));\n            }\n            element = element.parentNode;\n            if (element === container) {\n                return classNames;\n            }\n        }\n        return classNames;\n    };\n    /**\n     * Get events bound to a button. It's a custom event delegation to find all\n     * events connected to the element.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#getButtonEvents\n     *\n     * @param {Highcharts.HTMLDOMElement} container\n     *        Container that event is bound to.\n     *\n     * @param {global.Event} event\n     *        Browser's event.\n     *\n     * @return {Object}\n     *         Object with events (init, start, steps, and end)\n     */\n    NavigationBindings.prototype.getButtonEvents = function (container, event) {\n        var navigation = this,\n            classNames = this.getClickedClassNames(container,\n            event);\n        var bindings;\n        classNames.forEach(function (className) {\n            if (navigation.boundClassNames[className[0]] && !bindings) {\n                bindings = {\n                    events: navigation.boundClassNames[className[0]],\n                    button: className[1]\n                };\n            }\n        });\n        return bindings;\n    };\n    /**\n     * Bindings are just events, so the whole update process is simply\n     * removing old events and adding new ones.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#update\n     */\n    NavigationBindings.prototype.update = function (options) {\n        this.options = NavigationBindings_merge(true, this.options, options);\n        this.removeEvents();\n        this.initEvents();\n    };\n    /**\n     * Remove all events created in the navigation.\n     *\n     * @private\n     * @function Highcharts.NavigationBindings#removeEvents\n     */\n    NavigationBindings.prototype.removeEvents = function () {\n        this.eventsToUnbind.forEach(function (unbinder) { return unbinder(); });\n    };\n    /**\n     * @private\n     * @function Highcharts.NavigationBindings#destroy\n     */\n    NavigationBindings.prototype.destroy = function () {\n        this.removeEvents();\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    // Define which options from annotations should show up in edit box:\n    NavigationBindings.annotationsEditable = {\n        // `typeOptions` are always available\n        // Nested and shared options:\n        nestedOptions: {\n            labelOptions: ['style', 'format', 'backgroundColor'],\n            labels: ['style'],\n            label: ['style'],\n            style: ['fontSize', 'color'],\n            background: ['fill', 'strokeWidth', 'stroke'],\n            innerBackground: ['fill', 'strokeWidth', 'stroke'],\n            outerBackground: ['fill', 'strokeWidth', 'stroke'],\n            shapeOptions: ['fill', 'strokeWidth', 'stroke'],\n            shapes: ['fill', 'strokeWidth', 'stroke'],\n            line: ['strokeWidth', 'stroke'],\n            backgroundColors: [true],\n            connector: ['fill', 'strokeWidth', 'stroke'],\n            crosshairX: ['strokeWidth', 'stroke'],\n            crosshairY: ['strokeWidth', 'stroke']\n        },\n        // Simple shapes:\n        circle: ['shapes'],\n        ellipse: ['shapes'],\n        verticalLine: [],\n        label: ['labelOptions'],\n        // Measure\n        measure: ['background', 'crosshairY', 'crosshairX'],\n        // Others:\n        fibonacci: [],\n        tunnel: ['background', 'line', 'height'],\n        pitchfork: ['innerBackground', 'outerBackground'],\n        rect: ['shapes'],\n        // Crooked lines, elliots, arrows etc:\n        crookedLine: [],\n        basicAnnotation: ['shapes', 'labelOptions']\n    };\n    // Define non editable fields per annotation, for example Rectangle inherits\n    // options from Measure, but crosshairs are not available\n    NavigationBindings.annotationsNonEditable = {\n        rectangle: ['crosshairX', 'crosshairY', 'labelOptions'],\n        ellipse: ['labelOptions'],\n        circle: ['labelOptions']\n    };\n    return NavigationBindings;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Annotations_NavigationBindings = (NavigationBindings);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * A config object for navigation bindings in annotations.\n *\n * @interface Highcharts.NavigationBindingsOptionsObject\n */ /**\n* ClassName of the element for a binding.\n* @name Highcharts.NavigationBindingsOptionsObject#className\n* @type {string|undefined}\n*/ /**\n* Last event to be fired after last step event.\n* @name Highcharts.NavigationBindingsOptionsObject#end\n* @type {Function|undefined}\n*/ /**\n* Initial event, fired on a button click.\n* @name Highcharts.NavigationBindingsOptionsObject#init\n* @type {Function|undefined}\n*/ /**\n* Event fired on first click on a chart.\n* @name Highcharts.NavigationBindingsOptionsObject#start\n* @type {Function|undefined}\n*/ /**\n* Last event to be fired after last step event. Array of step events to be\n* called sequentially after each user click.\n* @name Highcharts.NavigationBindingsOptionsObject#steps\n* @type {Array<Function>|undefined}\n*/\n(''); // Keeps doclets above in JS file\n\n;// ./code/es5/es-modules/masters/modules/annotations.js\n\n\n\n\n\nvar G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nG.Annotation = G.Annotation || Annotations_Annotation;\nG.NavigationBindings = G.NavigationBindings || Annotations_NavigationBindings;\nG.Annotation.compose(G.Chart, G.NavigationBindings, G.Pointer, G.SVGRenderer);\n/* harmony default export */ var annotations_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__984__", "__WEBPACK_EXTERNAL_MODULE__660__", "extendStatics", "DropdownProperties", "ChartNavigationComposition", "Additions", "AnnotationChart", "ControlTarget", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "annotations_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "addEvent", "erase", "find", "fireEvent", "pick", "wrap", "chartAddAnnotation", "userOptions", "redraw", "annotation", "initAnnotation", "options", "annotations", "push", "graphic", "attr", "opacity", "chartCallback", "chart", "plotBoxClip", "renderer", "clipRect", "plotBox", "controlPointsGroup", "g", "zIndex", "clip", "add", "for<PERSON>ach", "annotationOptions", "i", "some", "drawAnnotations", "destroy", "event", "csvColumnHeaderFormatter", "exporting", "csv", "columnHeaderFormatter", "multiLevelHeaders", "dataRows", "xValues", "annotationHeader", "lang", "exportData", "startRowLength", "length", "annotationSeparator", "itemDelimiter", "joinAnnotations", "join", "labelOptions", "includeInDataExport", "labels", "label", "text", "annotationText_1", "points", "annotationX", "x", "xAxisIndex", "series", "xAxis", "index", "wasAdded", "newRow", "Array", "row", "maxRowLen", "Math", "max", "newRows", "header", "s", "columnTitle", "topLevelColumnTitle", "chartDrawAnnotations", "animate", "animationConfig", "chartRemoveAnnotation", "idOrAnnotation", "coll", "id", "onChartAfterInit", "wrapPointerOnContainerMouseDown", "proceed", "hasDraggedAnnotation", "apply", "slice", "arguments", "compose", "AnnotationClass", "ChartClass", "PointerClass", "chartProto", "addAnnotation", "pointer<PERSON><PERSON><PERSON>", "callbacks", "collectionsWithInit", "collectionsWithUpdate", "removeAnnotation", "types", "type", "Annotations_AnnotationChart", "defined", "doc", "isTouchDevice", "EventEmitter_addEvent", "EventEmitter_fireEvent", "objectEach", "EventEmitter_pick", "removeEvent", "EventEmitter", "addEvents", "emitter", "addMouseDownEvent", "element", "e", "onMouseDown", "passive", "useHTML", "foreignObject", "events", "<PERSON><PERSON><PERSON><PERSON>", "_a", "cancelClick", "pointer", "normalize", "target", "nonDOMEvents", "indexOf", "div", "draggable", "onDrag", "styledMode", "cssPointer_1", "cursor", "y", "xy", "css", "isUpdating", "removeDocEvents", "hcEvents", "mouseMoveToRadians", "cx", "cy", "temp", "prevDy", "prevChartY", "prevDx", "prevChartX", "dy", "chartY", "dx", "chartX", "inverted", "atan2", "mouseMoveToScale", "sx", "sy", "mouseMoveToTranslation", "isInsidePlot", "plotLeft", "plotTop", "visiblePlotOnly", "translation_1", "translate", "shapes", "shape", "preventDefault", "button", "firesTouchEvents", "sourceCapabilities", "removeDrag", "hasDragged", "removeMouseUp", "onMouseUp", "__extends", "b", "setPrototypeOf", "__proto__", "p", "__", "constructor", "create", "merge", "ControlPoint_pick", "ControlPoint", "_super", "_this", "animation", "positioner", "render", "symbol", "width", "height", "style", "setVisibility", "visible", "update", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "seriesProto", "MockPoint_defined", "MockPoint_fireEvent", "MockPoint", "mock", "point", "getPlotBox", "applyOptions", "getOptions", "fromPoint", "yAxis", "pointToPixels", "paneCoordinates", "plotX", "plotY", "plot<PERSON>id<PERSON>", "plotHeight", "translateX", "translateY", "pointToOptions", "command", "setAxis", "refresh", "hasDynamicOptions", "len", "toPixels", "isInside", "refreshOptions", "toValue", "rotate", "radians", "cos", "sin", "tx", "ty", "scale", "xOrY", "axisName", "axisOptions", "toAnchor", "anchor", "_cx", "_cy", "addControlPoints", "controlPoints", "controlPointsOptions", "controlPointOptions", "box", "tooltip", "getAnchor", "relativePosition", "absolutePosition", "destroyControlTarget", "controlPoint", "getPointsOptions", "splat", "linkPoints", "pointsOptions", "pointOptions", "isObject", "isString", "pointConfig", "redrawControlPoints", "renderControlPoints", "transform", "transformation", "p1", "p2", "_point", "transformPoint", "Annotations_MockPoint", "translatePoint", "ControlTargetClass", "controlProto", "Annotations_ControlTarget", "Controllable_merge", "Controllable", "itemType", "collection", "init", "_args", "_i", "attrsFromOptions", "<PERSON><PERSON><PERSON>", "map", "attrsMap", "attrs", "tracker", "_parentGroup", "className", "addClass", "setControlPointsVisibility", "shouldBeDrawn", "translateShape", "translateSecondPoint", "shapeOptions", "annotationIndex", "chartOptions", "newOptions", "parentGroup", "<PERSON><PERSON><PERSON><PERSON>", "arrow", "tagName", "attributes", "refY", "refX", "marker<PERSON>id<PERSON>", "markerHeight", "children", "ControllablePath_extends", "TypeError", "String", "ControllablePath_addEvent", "ControllablePath_defined", "extend", "ControllablePath_merge", "<PERSON><PERSON><PERSON>", "markerEndSetter", "createMarkerSetter", "markerStartSetter", "TRACKER_FILL", "svg", "markerType", "value", "onChartAfterGetContainer", "defs", "svgRendererAddMarker", "markerOptions", "stroke", "color", "fill", "child", "ast", "orient", "marker", "ControllablePath", "SVGRendererClass", "svgRendererProto", "add<PERSON><PERSON><PERSON>", "toD", "dOption", "showPath", "position", "pointIndex", "crispLine", "strokeWidth", "parent", "path", "snap", "setMarkers", "action", "placed", "item", "itemOptions", "def", "predefined<PERSON>ark<PERSON>", "markerId", "getAttribute", "dashStyle", "ControllableRect_extends", "ControllableRect_merge", "ControllableRect", "rect", "Boolean", "Controllables_ControllablePath", "ControllableCircle_extends", "ControllableCircle_merge", "ControllableCircle", "r", "circle", "setRadius", "ControllableEllipse_extends", "ControllableEllipse_merge", "ControllableEllipse_defined", "ControllableEllipse", "createElement", "getDistanceFromLine", "point1", "point2", "x0", "y0", "abs", "sqrt", "getAttrs", "position2", "x1", "y1", "x2", "y2", "rx", "angle", "atan", "PI", "ry", "getRY", "getYAxis", "yAxisIndex", "getAbsolutePosition", "rotation", "rotationOriginX", "rotationOriginY", "setYRadius", "highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_", "highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default", "ControllableLabel_extends", "format", "ControllableLabel_extend", "getAlignFactor", "isNumber", "ControllableLabel_pick", "symbolConnector", "w", "h", "yOffset", "anchorX", "anchorY", "lateral", "ControllableLabel", "alignedPosition", "alignOptions", "round", "align", "verticalAlign", "symbols", "connector", "justifiedOptions", "alignAttr", "off", "padding", "bBox", "getBBox", "chartAnnotations", "getContrast", "shapesWithoutBackground", "backgroundColor", "shadow", "labelrank", "formatter", "itemPosition", "alignTo", "itemPosRelativeX", "itemPosRelativeY", "anchorAbsolutePosition", "anchorRelativePosition", "showItem", "_b", "distance", "getPosition", "getPlayingField", "negative", "ttBelow", "overflow", "crop", "borderColor", "borderWidth", "borderRadius", "ControllableImage_extends", "ControllableImage", "image", "src", "Controllables_ControllableLabel", "highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_", "highcharts_AST_commonjs_highcharts_AST_commonjs2_highcharts_AST_root_Highcharts_AST_default", "BaseForm_addEvent", "BaseForm", "parentDiv", "iconsURL", "container", "createPopupContainer", "closeButton", "addCloseButton", "popup", "match", "eventName", "closeButtonEvents", "bind", "document", "code", "closePopup", "showPopup", "toolbarClass", "popupDiv", "popupClose<PERSON><PERSON>on", "innerHTML", "emptyHTML", "classList", "remove", "removeAttribute", "append<PERSON><PERSON><PERSON>", "display", "PopupAnnotations_doc", "isFirefox", "PopupAnnotations_createElement", "isArray", "PopupAnnotations_objectEach", "PopupAnnotations_pick", "stableSort", "addFormFields", "parentNode", "storage", "isRoot", "parentFullName", "<PERSON><PERSON><PERSON>", "addInput", "option", "reverse", "genInput", "createTextNode", "splice", "PopupIndicators_doc", "seriesTypes", "PopupIndicators_addEvent", "PopupIndicators_createElement", "PopupIndicators_defined", "PopupIndicators_isArray", "PopupIndicators_isObject", "PopupIndicators_objectEach", "PopupIndicators_stableSort", "dropdownParameters", "addColsContainer", "lhsCol", "rhsCol", "PopupIndicators_addFormFields", "seriesType", "rhsColWrapper", "fields", "params", "getNameType", "indicatorFullName", "name", "listAllSeries", "linkedParent", "volumeSeriesID", "addParamInputs", "addIndicatorList", "listType", "filter", "selectIndicator", "indicatorType", "isEdit", "setAttribute", "querySelectorAll", "plotOptions", "filteredSeriesArray", "filterSeriesArray", "filterSeries", "seriesAName", "toLowerCase", "seriesBName", "indicatorList", "seriesSet", "btn", "textContent", "setElementHTML", "noFilterMatch", "fieldName", "selectBox", "addSelection", "addSelectionOptions", "addSearchBox", "clearFilterText", "clearFilter", "inputWrapper", "handleInputChange", "inputText", "input", "htmlFor", "labelClassName", "optionName", "optionParamList", "split", "labelText", "selectName", "parameterName", "selectedOption", "currentSeries", "seriesOptions", "seriesName", "parameterOption", "filteredSeries", "indicatorAliases", "navigation", "regex", "RegExp", "replace", "alias", "is", "nameBase", "toUpperCase", "PopupTabs_doc", "PopupTabs_addEvent", "PopupTabs_createElement", "addContentItem", "addMenuItem", "tabName", "disableTab", "menuItem", "deselectAll", "tabs", "tabsContent", "selectTab", "tab", "allTabs", "switchTabs", "Popup_extends", "Popup_doc", "Popup_addEvent", "Popup_createElement", "Popup_extend", "Popup_fireEvent", "Popup_pick", "Popup", "activeAnnotation", "navigationBindings", "unbind_1", "setTimeout", "inputAttributes", "inputName", "selectedButtonElement", "addButton", "fieldsDiv", "callback", "inputList", "selectList", "linkedTo", "volumeTo", "fieldsOutput", "actionType", "param", "seriesId", "select", "parameter", "showForm", "indicators", "addForm", "addToolbar", "offsetHeight", "isInit", "lang<PERSON><PERSON>", "bottomRow", "saveButton", "top", "edit<PERSON><PERSON><PERSON>", "removeButton", "_options", "buttonParentDiv", "tabsContainers", "getAmount", "counter", "serie", "indicatorsCount", "firstTab", "composed", "PopupComposition_addEvent", "pushUnique", "PopupComposition_wrap", "onNavigationBindingsClosePopup", "onNavigationBindingsShowPopup", "config", "stockTools", "gui", "formType", "onSubmit", "wrapPointerOnContainerMouserDown", "inClass", "NagivationBindingsClass", "Annotation_extends", "getDeferredAnimation", "destroyObjectProperties", "Annotation_erase", "Annotation_fireEvent", "Annotation_merge", "Annotation_pick", "getLabelsAndShapesOptions", "baseOptions", "mergedOptions", "someBaseOptions", "newOptionsValue", "basicOptions", "Annotation", "defaultOptions", "labelsAndShapes", "NavigationBindingsClass", "Popup_PopupComposition", "addClipPaths", "setClipAxes", "clipXAxis", "clipYAxis", "getClipBox", "addLabels", "labelsOptions", "initLabel", "addShapes", "initShape", "destroyItem", "labelCollectors", "labelCollector", "left", "initProperties", "setOptions", "_annotation<PERSON>r<PERSON>hart", "_userOptions", "animOptions", "set<PERSON>abelCollector", "shapesMap", "redrawItems", "redrawItem", "renderItem", "hasVisiblePoints", "visibility", "show", "hide", "items", "shapesGroup", "labelsGroup", "renderItems", "xAxes", "yAxes", "linkedAxes", "concat", "reduce", "axes", "labelOrShape", "setItemControlPointsVisibility", "allowOverlap", "userOptionsIndex", "fontSize", "fontWeight", "updates", "addUpdate", "updateFn", "Chart_ChartNavigationComposition", "NavigationBindingsUtilities_defined", "NavigationBindingsUtilities_isNumber", "NavigationBindingsUtilities_pick", "annotationsFieldsTypes", "title", "coords", "coord", "extremes", "axis", "getExtremes", "axisMin", "min", "axisMax", "minPointOffset", "isInternal", "NavigationBindingsDefaults_isNumber", "NavigationBindingsDefaults_merge", "NavigationBindingsDefaults", "simpleShapes", "lines", "ellipse", "rectangle", "typeOptions", "backgroundColors", "bindingsClassName", "bindings", "circleAnnotation", "start", "getCoordinates", "coordsX", "NavigationBindingsDefaults_getAssignedAxis", "coordsY", "annotationsOptions", "steps", "mockPointOpts", "pow", "ellipseAnnotation", "newR", "newRY", "rectangleAnnotation", "labelAnnotation", "defer", "NavigationBindings_format", "NavigationBindings_composed", "NavigationBindings_doc", "win", "NavigationBindings_getFieldType", "predefinedType", "fieldType", "NavigationBindings_addEvent", "NavigationBindings_defined", "NavigationBindings_fireEvent", "NavigationBindings_isArray", "isFunction", "NavigationBindings_isNumber", "NavigationBindings_isObject", "NavigationBindings_merge", "NavigationBindings_objectEach", "NavigationBindings_pick", "NavigationBindings_pushUnique", "onAnnotationRemove", "deselectAnnotation", "onChartDestroy", "onChartLoad", "NavigationBindings", "initEvents", "initUpdate", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disabledClassName", "buttonsEnabled_1", "container_1", "boundClassNames", "buttonNode", "cls", "noDataState", "NavigationBindings_onNavigationBindingsClosePopup", "onNavigationBindingsDeselectButton", "selectableAnnotation", "annotationType", "touchStartX", "touchStartY", "originalClick", "click", "selectAndShowPopup", "eventArguments", "prevAnnotation", "annotationToFields", "data", "fieldsToOptions", "crosshairY", "enabled", "crosshairX", "touchstart", "touches", "clientX", "clientY", "touchend", "changedTouches", "eventsToUnbind", "getElementsByClassName", "getCoords", "NavigationBindings_getAssignedAxis", "bindingsContainer", "subContainer", "getButtonEvents", "contains", "bindingsButtonClick", "bindingsChartClick", "bindingsContainerMouseMove", "clickEvent", "svgContainer", "boxWrapper", "shouldEventBeFired", "nextEvent", "currentUserDetails", "mouseMoveEvent", "<PERSON><PERSON><PERSON><PERSON>", "removeClass", "closestPolyfill", "el", "ElementProto", "Element", "elementMatches", "matches", "msMatchesSelector", "webkitMatchesSelector", "ret", "closest", "parentElement", "nodeType", "stepIndex", "end", "_container", "moveEvent", "field", "parsedValue", "parseFloat", "<PERSON><PERSON><PERSON><PERSON>", "parent_1", "nextName", "editables", "annotationsEditable", "nestedEditables", "nestedOptions", "nonEditables", "annotationsNonEditable", "visualOptions", "traverse", "parentEditables", "parent<PERSON><PERSON>", "nextParent", "arrayOption", "nestedOption", "nested<PERSON><PERSON>", "toString", "typeOption", "typeKey", "getClickedClassNames", "elemClassName", "classNames", "removeEvents", "unbinder", "background", "innerBackground", "outerBackground", "line", "verticalLine", "measure", "<PERSON><PERSON><PERSON><PERSON>", "tunnel", "pitchfork", "crookedLine", "basicAnnotation", "G", "Chart", "Pointer", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "CAWA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,CAAEA,QAAQ,cAAc,UAAa,CAAEA,QAAQ,cAAc,GAAM,EACnJ,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,iCAAkC,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,iBAAiB,CAAE,CAAC,wBAAwB,aAAa,CAAE,CAAC,wBAAwB,MAAM,CAAC,CAAEJ,GACpL,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,iCAAiC,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,CAAEA,QAAQ,cAAc,UAAa,CAAEA,QAAQ,cAAc,GAAM,EAErLJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,UAAa,CAAEA,EAAK,UAAa,CAAC,GAAM,CACpJ,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,EACxJ,OAAgB,AAAC,WACP,aACA,IAyxCFC,EAuzCAA,EA4TAA,EAmIAA,EA6IAA,EAoPAA,EA+ZAA,EAkeGC,EAywBHD,EAsVAA,EA8rBGE,EAgCHC,EAjvLJC,EA6mDAC,EAs0EAJ,EA6xDAC,EA7iMUI,EAAuB,CAE/B,IACC,SAASd,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,SAASP,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,EAEA,IACC,SAASJ,CAAM,EAEtBA,EAAOD,OAAO,CAAGO,CAEX,CAEI,EAGIS,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAanB,OAAO,CAG5B,IAAIC,EAASe,CAAwB,CAACE,EAAS,CAAG,CAGjDlB,QAAS,CAAC,CACX,EAMA,OAHAe,CAAmB,CAACG,EAAS,CAACjB,EAAQA,EAAOD,OAAO,CAAEiB,GAG/ChB,EAAOD,OAAO,AACtB,CAMCiB,EAAoBI,CAAC,CAAG,SAASpB,CAAM,EACtC,IAAIqB,EAASrB,GAAUA,EAAOsB,UAAU,CACvC,WAAa,OAAOtB,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAgB,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASxB,CAAO,CAAE0B,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAAC5B,EAAS2B,IAC5EE,OAAOC,cAAc,CAAC9B,EAAS2B,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,EAAiB,CAChE,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAajHE,EAAW,AAACD,IAA+EC,QAAQ,CAAEC,EAAQ,AAACF,IAA+EE,KAAK,CAAEC,EAAO,AAACH,IAA+EG,IAAI,CAAEC,EAAY,AAACJ,IAA+EI,SAAS,CAAEC,EAAO,AAACL,IAA+EK,IAAI,CAAEC,EAAO,AAACN,IAA+EM,IAAI,CAsBpkB,SAASC,EAAmBC,CAAW,CAAEC,CAAM,EAC3C,IAAIC,EAAa,IAAI,CAACC,cAAc,CAACH,GAQrC,OAPA,IAAI,CAACI,OAAO,CAACC,WAAW,CAACC,IAAI,CAACJ,EAAWE,OAAO,EAC5CP,EAAKI,EAAQ,CAAA,KACbC,EAAWD,MAAM,GACjBC,EAAWK,OAAO,CAACC,IAAI,CAAC,CACpBC,QAAS,CACb,IAEGP,CACX,CAIA,SAASQ,IACL,IAAIC,EAAQ,IAAI,AAChBA,CAAAA,EAAMC,WAAW,CAAG,IAAI,CAACC,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAACC,OAAO,EACvDJ,EAAMK,kBAAkB,CAAGL,EAAME,QAAQ,CACpCI,CAAC,CAAC,kBACFT,IAAI,CAAC,CAAEU,OAAQ,EAAG,GAClBC,IAAI,CAACR,EAAMC,WAAW,EACtBQ,GAAG,GACRT,EAAMP,OAAO,CAACC,WAAW,CAACgB,OAAO,CAAC,SAAUC,CAAiB,CAAEC,CAAC,EAC5D,GAEA,CAACZ,EAAMN,WAAW,CAACmB,IAAI,CAAC,SAAUtB,CAAU,EACxC,OAAOA,EAAWE,OAAO,GAAKkB,CAClC,GAAI,CACA,IAAIpB,EAAaS,EAAMR,cAAc,CAACmB,EACtCX,CAAAA,EAAMP,OAAO,CAACC,WAAW,CAACkB,EAAE,CAAGrB,EAAWE,OAAO,AACrD,CACJ,GACAO,EAAMc,eAAe,GACrBhC,EAASkB,EAAO,SAAUA,EAAMc,eAAe,EAC/ChC,EAASkB,EAAO,UAAW,WACvBA,EAAMC,WAAW,CAACc,OAAO,GACzBf,EAAMK,kBAAkB,CAACU,OAAO,EACpC,GACAjC,EAASkB,EAAO,aAAc,SAAUgB,CAAK,EACzC,IAAItB,EAAcM,EAAMN,WAAW,CAC/BuB,EAA2B,AAAC,CAAA,AAAC,IAAI,CAACxB,OAAO,CAACyB,SAAS,EAC/C,IAAI,CAACzB,OAAO,CAACyB,SAAS,CAACC,GAAG,EAC1B,CAAC,CAAA,EAAGC,qBAAqB,CAG7BC,EAAoB,CAACL,EAAMM,QAAQ,CAAC,EAAE,CAACC,OAAO,CAC9CC,EAAoBxB,EAAMP,OAAO,CAACgC,IAAI,EAClCzB,EAAMP,OAAO,CAACgC,IAAI,CAACC,UAAU,EAC7B1B,EAAMP,OAAO,CAACgC,IAAI,CAACC,UAAU,CAACF,gBAAgB,CAiBnDG,EAAiBX,EAAMM,QAAQ,CAAC,EAAE,CAACM,MAAM,CAAEC,EAAuB7B,EAAMP,OAAO,CAACyB,SAAS,EACxFlB,EAAMP,OAAO,CAACyB,SAAS,CAACC,GAAG,EAC3BnB,EAAMP,OAAO,CAACyB,SAAS,CAACC,GAAG,CAACzB,WAAW,EACvCM,EAAMP,OAAO,CAACyB,SAAS,CAACC,GAAG,CAACzB,WAAW,CAACoC,aAAa,CAAGC,EAAmB/B,EAAMP,OAAO,CAACyB,SAAS,EAClGlB,EAAMP,OAAO,CAACyB,SAAS,CAACC,GAAG,EAC3BnB,EAAMP,OAAO,CAACyB,SAAS,CAACC,GAAG,CAACzB,WAAW,EACvCM,EAAMP,OAAO,CAACyB,SAAS,CAACC,GAAG,CAACzB,WAAW,CAACsC,IAAI,CAChDtC,EAAYgB,OAAO,CAAC,SAAUnB,CAAU,EAChCA,EAAWE,OAAO,CAACwC,YAAY,EAC/B1C,EAAWE,OAAO,CAACwC,YAAY,CAACC,mBAAmB,EACnD3C,EAAW4C,MAAM,CAACzB,OAAO,CAAC,SAAU0B,CAAK,EACrC,GAAIA,EAAM3C,OAAO,CAAC4C,IAAI,CAAE,CACpB,IAAIC,EAAmBF,EAAM3C,OAAO,CAAC4C,IAAI,CACzCD,EAAMG,MAAM,CAAC7B,OAAO,CAAC,SAAU6B,CAAM,EACjC,IAAIC,EAAcD,EAAOE,CAAC,CACtBC,EAAaH,EAAOI,MAAM,CAACC,KAAK,CAC5BL,EAAOI,MAAM,CAACC,KAAK,CAACC,KAAK,CACzB,GACJC,EAAW,CAAA,EAGf,GAAIJ,AAAe,KAAfA,EAAmB,CAGnB,IAAK,IAFDjF,EAAIuD,EAAMM,QAAQ,CAAC,EAAE,CAACM,MAAM,CAC5BmB,EAAS,AAAIC,MAAMvF,GACdmD,EAAI,EAAGA,EAAInD,EAAG,EAAEmD,EACrBmC,CAAM,CAACnC,EAAE,CAAG,GAEhBmC,EAAOpD,IAAI,CAAC2C,GACZS,EAAOxB,OAAO,CAAG,EAAE,CACnBwB,EAAOxB,OAAO,CAACmB,EAAW,CAAGF,EAC7BxB,EAAMM,QAAQ,CAAC3B,IAAI,CAACoD,GACpBD,EAAW,CAAA,CACf,CAuBA,GApBKA,GACD9B,EAAMM,QAAQ,CAACZ,OAAO,CAAC,SAAUuC,CAAG,EAC5B,CAACH,GACDG,EAAI1B,OAAO,EACXmB,AAAe,KAAK,IAApBA,GACAF,IAAgBS,EAAI1B,OAAO,CAACmB,EAAW,GACnCX,GACAkB,EAAIrB,MAAM,CAAGD,EACbsB,CAAG,CAACA,EAAIrB,MAAM,CAAG,EAAE,EAAKC,EACpBS,EAGJW,EAAItD,IAAI,CAAC2C,GAEbQ,EAAW,CAAA,EAEnB,GAIA,CAACA,EAAU,CAGX,IAAK,IAFDrF,EAAIuD,EAAMM,QAAQ,CAAC,EAAE,CAACM,MAAM,CAC5BmB,EAAS,AAAIC,MAAMvF,GACdmD,EAAI,EAAGA,EAAInD,EAAG,EAAEmD,EACrBmC,CAAM,CAACnC,EAAE,CAAG,EAEhBmC,CAAAA,CAAM,CAAC,EAAE,CAAGP,EACZO,EAAOpD,IAAI,CAAC2C,GACZS,EAAOxB,OAAO,CAAG,EAAE,CACA,KAAK,IAApBmB,GACAK,CAAAA,EAAOxB,OAAO,CAACmB,EAAW,CAAGF,CAAU,EAE3CxB,EAAMM,QAAQ,CAAC3B,IAAI,CAACoD,EACxB,CACJ,EACJ,CACJ,EAER,GACA,IAAIG,EAAY,EAChBlC,EAAMM,QAAQ,CAACZ,OAAO,CAAC,SAAUuC,CAAG,EAChCC,EAAYC,KAAKC,GAAG,CAACF,EAAWD,EAAIrB,MAAM,CAC9C,GAEA,IAAK,IADDyB,EAAUH,EAAYlC,EAAMM,QAAQ,CAAC,EAAE,CAACM,MAAM,CACzChB,EAAI,EAAGA,EAAIyC,EAASzC,IAAK,CAC9B,IAAI0C,EAASlC,AAhGW,SAAUyB,CAAK,EACnC,IAAIU,SACR,AAAItC,GAEIsC,AAAM,CAAA,IADVA,CAAAA,EAAItC,EAAyB4B,EAAK,EAEvBU,GAGfA,EAAI/B,EAAmB,IAAMqB,EACzBxB,GACO,CACHmC,YAAaD,EACbE,oBAAqBF,CACzB,EAEGA,CACX,EAgFuC3C,EAAI,GACnCS,GACAL,EAAMM,QAAQ,CAAC,EAAE,CAAC3B,IAAI,CAAC2D,EAAOG,mBAAmB,EACjDzC,EAAMM,QAAQ,CAAC,EAAE,CAAC3B,IAAI,CAAC2D,EAAOE,WAAW,GAGzCxC,EAAMM,QAAQ,CAAC,EAAE,CAAC3B,IAAI,CAAC2D,EAE/B,CACJ,EACJ,CAIA,SAASI,IACL,IAAI,CAACzD,WAAW,CAACJ,IAAI,CAAC,IAAI,CAACO,OAAO,EAClC,IAAI,CAACV,WAAW,CAACgB,OAAO,CAAC,SAAUnB,CAAU,EACzCA,EAAWD,MAAM,GACjBC,EAAWK,OAAO,CAAC+D,OAAO,CAAC,CACvB7D,QAAS,CACb,EAAGP,EAAWqE,eAAe,CACjC,EACJ,CASA,SAASC,EAAsBC,CAAc,EACzC,IAAIpE,EAAc,IAAI,CAACA,WAAW,CAC9BH,EAAa,AAACuE,AAAwB,gBAAxBA,EAAeC,IAAI,CAC7BD,EACA9E,EAAKU,EACT,SAAUH,CAAU,EACZ,OAAOA,EAAWE,OAAO,CAACuE,EAAE,GAAKF,CACzC,GACAvE,IACAN,EAAUM,EAAY,UACtBR,EAAM,IAAI,CAACU,OAAO,CAACC,WAAW,CAAEH,EAAWE,OAAO,EAClDV,EAAMW,EAAaH,GACnBA,EAAWwB,OAAO,GAE1B,CAKA,SAASkD,IAELjE,AADY,IAAI,CACVN,WAAW,CAAG,EAAE,CACjB,IAAI,CAACD,OAAO,CAACC,WAAW,EACzB,CAAA,IAAI,CAACD,OAAO,CAACC,WAAW,CAAG,EAAE,AAAD,CAEpC,CAIA,SAASwE,EAAgCC,CAAO,EACvC,IAAI,CAACnE,KAAK,CAACoE,oBAAoB,EAChCD,EAAQE,KAAK,CAAC,IAAI,CAAErB,MAAMzE,SAAS,CAAC+F,KAAK,CAAC7F,IAAI,CAAC8F,UAAW,GAElE,CAyCItH,AACDA,CAAAA,GAAoBA,CAAAA,EAAkB,CAAC,CAAA,CAAC,EADvBuH,OAAO,CAtBvB,SAAiBC,CAAe,CAAEC,CAAU,CAAEC,CAAY,EACtD,IAAIC,EAAaF,EAAWnG,SAAS,CACrC,GAAI,CAACqG,EAAWC,aAAa,CAAE,CAC3B,IAAIC,EAAeH,EAAapG,SAAS,CACzCO,EAAS4F,EAAY,YAAaT,GAClCW,EAAWC,aAAa,CAAGzF,EAC3BwF,EAAWG,SAAS,CAACpF,IAAI,CAACI,GAC1B6E,EAAWI,mBAAmB,CAACtF,WAAW,CAAG,CAACN,EAAmB,CACjEwF,EAAWK,qBAAqB,CAACtF,IAAI,CAAC,eACtCiF,EAAW9D,eAAe,CAAG4C,EAC7BkB,EAAWM,gBAAgB,CAAGrB,EAC9Be,EAAWpF,cAAc,CAAG,SAA6BH,CAAW,EAChE,IAEIE,EAAa,GAFEkF,CAAAA,EAAgBU,KAAK,CAAC9F,EAAY+F,IAAI,CAAC,EAClDX,CAAc,EACW,IAAI,CACjCpF,GAEJ,OADA,IAAI,CAACK,WAAW,CAACC,IAAI,CAACJ,GACfA,CACX,EACAJ,EAAK2F,EAAc,uBAAwBZ,EAC/C,CACJ,EAQyB,IAAImB,EAA+BpI,EAS5DqI,EAAU,AAACzG,IAA+EyG,OAAO,CAkoBjGC,EAAM,AAAC1G,IAA+E0G,GAAG,CAAEC,EAAgB,AAAC3G,IAA+E2G,aAAa,CAExMC,EAAwB,AAAC5G,IAA+EC,QAAQ,CAAE4G,EAAyB,AAAC7G,IAA+EI,SAAS,CAAE0G,EAAa,AAAC9G,IAA+E8G,UAAU,CAAEC,EAAoB,AAAC/G,IAA+EK,IAAI,CAAE2G,EAAc,AAAChH,IAA+EgH,WAAW,CASliBC,EAA8B,WAC9B,SAASA,IACT,CA4OA,OAlOAA,EAAavH,SAAS,CAACwH,SAAS,CAAG,WAC/B,IAAIC,EAAU,IAAI,CACdC,EAAoB,SAAUC,CAAO,EACjCT,EAAsBS,EAC1BV,EAAgB,aAAe,YAC/B,SAAUW,CAAC,EACHH,EAAQI,WAAW,CAACD,EAC5B,EAAG,CAAEE,QAAS,CAAA,CAAM,EACxB,EA2BA,GA1BAJ,EAAkB,IAAI,CAACrG,OAAO,CAACsG,OAAO,EACtC,AAACF,CAAAA,EAAQ7D,MAAM,EAAI,EAAE,AAAD,EAAGzB,OAAO,CAAC,SAAU0B,CAAK,EACtCA,EAAM3C,OAAO,CAAC6G,OAAO,EACrBlE,EAAMxC,OAAO,CAACyC,IAAI,EAClB,CAACD,EAAMxC,OAAO,CAACyC,IAAI,CAACkE,aAAa,EAEjCN,EAAkB7D,EAAMxC,OAAO,CAACyC,IAAI,CAAC6D,OAAO,CAEpD,GACAP,EAAWK,EAAQvG,OAAO,CAAC+G,MAAM,CAAE,SAAUxF,CAAK,CAAEoE,CAAI,EACpD,IAAIqB,EAAe,SAAUN,CAAC,EACtB,IAAIO,CACK,CAAA,UAATtB,GAAqBY,EAAQW,WAAW,EACxC3F,EAAMvC,IAAI,CAACuH,EAAS,AAAiC,OAAhCU,CAAAA,EAAKV,EAAQhG,KAAK,CAAC4G,OAAO,AAAD,GAAeF,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGG,SAAS,CAACV,GAAIH,EAAQc,MAAM,CAE7H,CACI,AAA+C,CAAA,KAA/C,AAACd,CAAAA,EAAQe,YAAY,EAAI,EAAE,AAAD,EAAGC,OAAO,CAAC5B,IACrCK,EAAsBO,EAAQpG,OAAO,CAACsG,OAAO,CAAEd,EAAMqB,EAAc,CAAEJ,QAAS,CAAA,CAAM,GAChFL,EAAQpG,OAAO,CAACqH,GAAG,EACnBxB,EAAsBO,EAAQpG,OAAO,CAACqH,GAAG,CAAE7B,EAAMqB,EAAc,CAAEJ,QAAS,CAAA,CAAM,IAIpFZ,EAAsBO,EAASZ,EAAMqB,EAAc,CAAEJ,QAAS,CAAA,CAAM,EAE5E,GACIL,EAAQvG,OAAO,CAACyH,SAAS,GACzBzB,EAAsBO,EAAS,OAAQA,EAAQmB,MAAM,EACjD,CAACnB,EAAQpG,OAAO,CAACM,QAAQ,CAACkH,UAAU,EAAE,CACtC,IAAIC,EAAe,CACXC,OAAQ,CACJ7E,EAAG,YACH8E,EAAG,YACHC,GAAI,MACR,CAAC,CAACxB,EAAQvG,OAAO,CAACyH,SAAS,CAAC,AAChC,EACJlB,EAAQpG,OAAO,CAAC6H,GAAG,CAACJ,GACpB,AAACrB,CAAAA,EAAQ7D,MAAM,EAAI,EAAE,AAAD,EAAGzB,OAAO,CAAC,SAAU0B,CAAK,EACtCA,EAAM3C,OAAO,CAAC6G,OAAO,EACrBlE,EAAMxC,OAAO,CAACyC,IAAI,EAClB,CAACD,EAAMxC,OAAO,CAACyC,IAAI,CAACkE,aAAa,EACjCnE,EAAMxC,OAAO,CAACyC,IAAI,CAACoF,GAAG,CAACJ,EAE/B,EACJ,CAECrB,EAAQ0B,UAAU,EACnBhC,EAAuBM,EAAS,MAExC,EAIAF,EAAavH,SAAS,CAACwC,OAAO,CAAG,WAC7B,IAAI,CAAC4G,eAAe,GACpB9B,EAAY,IAAI,EAChB,IAAI,CAAC+B,QAAQ,CAAG,IACpB,EAKA9B,EAAavH,SAAS,CAACsJ,kBAAkB,CAAG,SAAU1B,CAAC,CAAE2B,CAAE,CAAEC,CAAE,EAC3D,IAIIC,EAJAC,EAAS9B,EAAE+B,UAAU,CAAGH,EACxBI,EAAShC,EAAEiC,UAAU,CAAGN,EACxBO,EAAKlC,EAAEmC,MAAM,CAAGP,EAChBQ,EAAKpC,EAAEqC,MAAM,CAAGV,EAUpB,OARI,IAAI,CAAC9H,KAAK,CAACyI,QAAQ,GACnBT,EAAOG,EACPA,EAASF,EACTA,EAASD,EACTA,EAAOO,EACPA,EAAKF,EACLA,EAAKL,GAEF7E,KAAKuF,KAAK,CAACL,EAAIE,GAAMpF,KAAKuF,KAAK,CAACT,EAAQE,EACnD,EAKArC,EAAavH,SAAS,CAACoK,gBAAgB,CAAG,SAAUxC,CAAC,CAAE2B,CAAE,CAAEC,CAAE,EACzD,IAAII,EAAShC,EAAEiC,UAAU,CAAGN,EACxBG,EAAS9B,EAAE+B,UAAU,CAAGH,EACxBQ,EAAKpC,EAAEqC,MAAM,CAAGV,EAChBO,EAAKlC,EAAEmC,MAAM,CAAGP,EAChBa,EAAK,AAACL,CAAAA,GAAM,CAAA,EAAMJ,CAAAA,GAAU,CAAA,EAAIU,EAAK,AAACR,CAAAA,GAAM,CAAA,EAAMJ,CAAAA,GAAU,CAAA,EAChE,GAAI,IAAI,CAACjI,KAAK,CAACyI,QAAQ,CAAE,CACrB,IAAIT,EAAOa,EACXA,EAAKD,EACLA,EAAKZ,CACT,CACA,MAAO,CACHvF,EAAGmG,EACHrB,EAAGsB,CACP,CACJ,EAKA/C,EAAavH,SAAS,CAACuK,sBAAsB,CAAG,SAAU3C,CAAC,EACvD,IAEI6B,EAFAO,EAAKpC,EAAEqC,MAAM,CAAGrC,EAAEiC,UAAU,CAC5BC,EAAKlC,EAAEmC,MAAM,CAAGnC,EAAE+B,UAAU,CAOhC,OALI,IAAI,CAAClI,KAAK,CAACyI,QAAQ,GACnBT,EAAOK,EACPA,EAAKE,EACLA,EAAKP,GAEF,CACHvF,EAAG8F,EACHhB,EAAGc,CACP,CACJ,EAMAvC,EAAavH,SAAS,CAAC4I,MAAM,CAAG,SAAUhB,CAAC,EACvC,GAAI,IAAI,CAACnG,KAAK,CAAC+I,YAAY,CAAC5C,EAAEqC,MAAM,CAAG,IAAI,CAACxI,KAAK,CAACgJ,QAAQ,CAAE7C,EAAEmC,MAAM,CAAG,IAAI,CAACtI,KAAK,CAACiJ,OAAO,CAAE,CACvFC,gBAAiB,CAAA,CACrB,GAAI,CACA,IAAIC,EAAgB,IAAI,CAACL,sBAAsB,CAAC3C,EACjB,CAAA,MAA3B,IAAI,CAAC1G,OAAO,CAACyH,SAAS,EACtBiC,CAAAA,EAAc5B,CAAC,CAAG,CAAA,EAES,MAA3B,IAAI,CAAC9H,OAAO,CAACyH,SAAS,EACtBiC,CAAAA,EAAc1G,CAAC,CAAG,CAAA,EAGlBuD,AADU,IAAI,CACNzD,MAAM,CAACX,MAAM,CACrBoE,AAFU,IAAI,CAENoD,SAAS,CAACD,EAAc1G,CAAC,CAAE0G,EAAc5B,CAAC,GAGlDvB,AALU,IAAI,CAKNqD,MAAM,CAAC3I,OAAO,CAAC,SAAU4I,CAAK,EAClC,OAAOA,EAAMF,SAAS,CAACD,EAAc1G,CAAC,CAAE0G,EAAc5B,CAAC,CAC3D,GACAvB,AARU,IAAI,CAQN7D,MAAM,CAACzB,OAAO,CAAC,SAAU0B,CAAK,EAClC,OAAOA,EAAMgH,SAAS,CAACD,EAAc1G,CAAC,CAAE0G,EAAc5B,CAAC,CAC3D,IAEJ,IAAI,CAACjI,MAAM,CAAC,CAAA,EAChB,CACJ,EAKAwG,EAAavH,SAAS,CAAC6H,WAAW,CAAG,SAAUD,CAAC,EAM5C,GAJIA,EAAEoD,cAAc,EAChBpD,EAAEoD,cAAc,GAGhBpD,AAAa,IAAbA,EAAEqD,MAAM,EAGZ,IARI9C,EAQAV,EAAU,IAAI,CACdY,EAAUZ,EAAQhG,KAAK,CAAC4G,OAAO,CAG/B6C,EAAmB,AAAC,CAAA,AAAsE,OAArE/C,CAAAA,EAAKP,MAAAA,EAA6B,KAAK,EAAIA,EAAEuD,kBAAkB,AAAD,GAAehD,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG+C,gBAAgB,AAAD,GAAM,CAAA,EAEnJrB,EAAajC,AADjBA,CAAAA,EAAI,AAACS,CAAAA,MAAAA,EAAyC,KAAK,EAAIA,EAAQC,SAAS,CAACV,EAAC,GAAMA,CAAAA,EAC7DqC,MAAM,CACrBN,EAAa/B,EAAEmC,MAAM,AACzBtC,CAAAA,EAAQW,WAAW,CAAG,CAAA,EACtBX,EAAQhG,KAAK,CAACoE,oBAAoB,CAAG,CAAA,EACrC4B,EAAQ2D,UAAU,CAAGlE,EAAsBF,EAAKC,GAAiBiE,EAAmB,YAAc,YAAa,SAAUtD,CAAC,EACtHH,EAAQ4D,UAAU,CAAG,CAAA,EAErBzD,AADAA,CAAAA,EAAI,AAACS,CAAAA,MAAAA,EAAyC,KAAK,EAAIA,EAAQC,SAAS,CAACV,EAAC,GAAMA,CAAAA,EAC9EiC,UAAU,CAAGA,EACfjC,EAAE+B,UAAU,CAAGA,EACfxC,EAAuBM,EAAS,OAAQG,GACxCiC,EAAajC,EAAEqC,MAAM,CACrBN,EAAa/B,EAAEmC,MAAM,AACzB,EAAG9C,GAAiBiE,EAAmB,CAAEpD,QAAS,CAAA,CAAM,EAAI,KAAK,GACjEL,EAAQ6D,aAAa,CAAGpE,EAAsBF,EAAKC,GAAiBiE,EAAmB,WAAa,UAAW,WAG3G,IAAIlK,EAAaqG,EAAkBI,EAAQc,MAAM,EAAId,EAAQc,MAAM,CAACvH,UAAU,CAC1EyG,EAAQc,MAAM,EACdvH,GAEAA,CAAAA,EAAWoH,WAAW,CAAGX,EAAQ4D,UAAU,AAAD,EAE9C5D,EAAQW,WAAW,CAAGX,EAAQ4D,UAAU,CACxC5D,EAAQhG,KAAK,CAACoE,oBAAoB,CAAG,CAAA,EACjC4B,EAAQ4D,UAAU,EAElBlE,EAAuBE,EAAkBrG,EACzCyG,GAAU,eAEdA,EAAQ4D,UAAU,CAAG,CAAA,EACrB5D,EAAQ8D,SAAS,EACrB,EAAGtE,GAAiBiE,EAAmB,CAAEpD,QAAS,CAAA,CAAM,EAAI,KAAK,GACrE,EAIAP,EAAavH,SAAS,CAACuL,SAAS,CAAG,WAC/B,IAAI,CAACnC,eAAe,EACxB,EAKA7B,EAAavH,SAAS,CAACoJ,eAAe,CAAG,WACjC,IAAI,CAACgC,UAAU,EACf,CAAA,IAAI,CAACA,UAAU,CAAG,IAAI,CAACA,UAAU,EAAC,EAElC,IAAI,CAACE,aAAa,EAClB,CAAA,IAAI,CAACA,aAAa,CAAG,IAAI,CAACA,aAAa,EAAC,CAEhD,EACO/D,CACX,IAeIiE,GACIlN,EAAgB,SAAUe,CAAC,CAC3BoM,CAAC,EAMD,MAAOnN,AALHA,CAAAA,EAAgBoB,OAAOgM,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAalH,OAAS,SAAUpF,CAAC,CAC1DoM,CAAC,EAAIpM,EAAEsM,SAAS,CAAGF,CAAG,GACd,SAAUpM,CAAC,CACnBoM,CAAC,EAAI,IAAK,IAAIG,KAAKH,EAAOA,EAAExL,cAAc,CAAC2L,IAAIvM,CAAAA,CAAC,CAACuM,EAAE,CAAGH,CAAC,CAACG,EAAE,AAAD,CAAG,CAAA,EACvCvM,EAAGoM,EAC5B,EACO,SAAUpM,CAAC,CAAEoM,CAAC,EAEjB,SAASI,IAAO,IAAI,CAACC,WAAW,CAAGzM,CAAG,CADtCf,EAAce,EAAGoM,GAEjBpM,EAAEW,SAAS,CAAGyL,AAAM,OAANA,EAAa/L,OAAOqM,MAAM,CAACN,GAAMI,CAAAA,EAAG7L,SAAS,CAAGyL,EAAEzL,SAAS,CAAE,IAAI6L,CAAG,CACtF,GAIAG,EAAQ,AAAC1L,IAA+E0L,KAAK,CAAEC,EAAoB,AAAC3L,IAA+EK,IAAI,CA6BvMuL,EAA8B,SAAUC,CAAM,EAO9C,SAASD,EAAazK,CAAK,CAAE8G,CAAM,CAAErH,CAAO,CAAEoD,CAAK,EAC/C,IAAI8H,EAAQD,EAAOjM,IAAI,CAAC,IAAI,GAAK,IAAI,CAarC,OALAkM,EAAM5D,YAAY,CAAG,CAAC,OAAO,CAC7B4D,EAAM3K,KAAK,CAAGA,EACd2K,EAAM7D,MAAM,CAAGA,EACf6D,EAAMlL,OAAO,CAAGA,EAChBkL,EAAM9H,KAAK,CAAG2H,EAAkB/K,EAAQoD,KAAK,CAAEA,GACxC8H,CACX,CA2EA,OAhGAZ,EAAUU,EAAcC,GA+BxBD,EAAalM,SAAS,CAACwC,OAAO,CAAG,WAC7B2J,EAAOnM,SAAS,CAACwC,OAAO,CAACtC,IAAI,CAAC,IAAI,EAC9B,IAAI,CAACmB,OAAO,EACZ,CAAA,IAAI,CAACA,OAAO,CAAG,IAAI,CAACA,OAAO,CAACmB,OAAO,EAAC,EAExC,IAAI,CAACf,KAAK,CAAG,KACb,IAAI,CAAC8G,MAAM,CAAG,KACd,IAAI,CAACrH,OAAO,CAAG,IACnB,EAMAgL,EAAalM,SAAS,CAACe,MAAM,CAAG,SAAUsL,CAAS,EAC/C,IAAI,CAAChL,OAAO,CAACgL,EAAY,UAAY,OAAO,CAAC,IAAI,CAACnL,OAAO,CAACoL,UAAU,CAACpM,IAAI,CAAC,IAAI,CAAE,IAAI,CAACqI,MAAM,EAC/F,EAKA2D,EAAalM,SAAS,CAACuM,MAAM,CAAG,WAC5B,IAAI9K,EAAQ,IAAI,CAACA,KAAK,CAClBP,EAAU,IAAI,CAACA,OAAO,AAC1B,CAAA,IAAI,CAACG,OAAO,CAAGI,EAAME,QAAQ,CACxB6K,MAAM,CAACtL,EAAQsL,MAAM,CAAE,EAAG,EAAGtL,EAAQuL,KAAK,CAAEvL,EAAQwL,MAAM,EAC1DxK,GAAG,CAACT,EAAMK,kBAAkB,EAC5BoH,GAAG,CAAChI,EAAQyL,KAAK,EACtB,IAAI,CAACC,aAAa,CAAC1L,EAAQ2L,OAAO,EAElC,IAAI,CAACrF,SAAS,EAClB,EAUA0E,EAAalM,SAAS,CAAC4M,aAAa,CAAG,SAAUC,CAAO,EACpD,IAAI,CAACxL,OAAO,CAACwL,EAAU,OAAS,OAAO,GACvC,IAAI,CAAC3L,OAAO,CAAC2L,OAAO,CAAGA,CAC3B,EASAX,EAAalM,SAAS,CAAC8M,MAAM,CAAG,SAAUhM,CAAW,EACjD,IAAIW,EAAQ,IAAI,CAACA,KAAK,CAClB8G,EAAS,IAAI,CAACA,MAAM,CACpBjE,EAAQ,IAAI,CAACA,KAAK,CAClBpD,EAAU8K,EAAM,CAAA,EAChB,IAAI,CAAC9K,OAAO,CACZJ,GACJ,IAAI,CAAC0B,OAAO,GACZ,IAAI,CAACsJ,WAAW,CAACrK,EAAO8G,EAAQrH,EAASoD,GACzC,IAAI,CAACiI,MAAM,CAAC9K,EAAMK,kBAAkB,EACpC,IAAI,CAACf,MAAM,EACf,EACOmL,CACX,EA1J6D3E,GAiLzDwF,EAAmIjO,EAAoB,KACvJkO,EAAuJlO,EAAoBI,CAAC,CAAC6N,GAS7KE,GAAc,AAACD,IAA2I5I,MAAM,CAACpE,SAAS,CAE1KkN,GAAoB,AAAC5M,IAA+EyG,OAAO,CAAEoG,GAAsB,AAAC7M,IAA+EI,SAAS,CA4B5N0M,GAA2B,WAM3B,SAASA,EAAU3L,CAAK,CAAE8G,CAAM,CAAErH,CAAO,EAYrC,IAAI,CAACmM,IAAI,CAAG,CAAA,EAEZ,IAAI,CAACC,KAAK,CAAG,IAAI,CAOjB,IAAI,CAAClJ,MAAM,CAAG,CACVyI,QAAS,CAAA,EACTpL,MAAOA,EACP8L,WAAYN,GAAYM,UAAU,AACtC,EAKA,IAAI,CAAChF,MAAM,CAAGA,GAAU,KAOxB,IAAI,CAACrH,OAAO,CAAGA,EAkCf,IAAI,CAACsM,YAAY,CAAC,IAAI,CAACC,UAAU,GACrC,CAmSA,OAlRAL,EAAUM,SAAS,CAAG,SAAUJ,CAAK,EACjC,OAAO,IAAIF,EAAUE,EAAMlJ,MAAM,CAAC3C,KAAK,CAAE,KAAM,CAC3CyC,EAAGoJ,EAAMpJ,CAAC,CACV8E,EAAGsE,EAAMtE,CAAC,CACV3E,MAAOiJ,EAAMlJ,MAAM,CAACC,KAAK,CACzBsJ,MAAOL,EAAMlJ,MAAM,CAACuJ,KAAK,AAC7B,EACJ,EAcAP,EAAUQ,aAAa,CAAG,SAAUN,CAAK,CAAEO,CAAe,EACtD,IAIIhM,EAJAuC,EAASkJ,EAAMlJ,MAAM,CACrB3C,EAAQ2C,EAAO3C,KAAK,CACpByC,EAAIoJ,EAAMQ,KAAK,EAAI,EACnB9E,EAAIsE,EAAMS,KAAK,EAAI,EAiBvB,OAfItM,EAAMyI,QAAQ,GACVoD,EAAMD,IAAI,EACVnJ,EAAIoJ,EAAMS,KAAK,CACf/E,EAAIsE,EAAMQ,KAAK,GAGf5J,EAAIzC,EAAMuM,SAAS,CAAIV,CAAAA,EAAMS,KAAK,EAAI,CAAA,EACtC/E,EAAIvH,EAAMwM,UAAU,CAAIX,CAAAA,EAAMQ,KAAK,EAAI,CAAA,IAG3C1J,GAAU,CAACyJ,IAEX3J,GAAKrC,AADLA,CAAAA,EAAUuC,EAAOmJ,UAAU,EAAC,EACfW,UAAU,CACvBlF,GAAKnH,EAAQsM,UAAU,EAEpB,CACHjK,EAAGA,EACH8E,EAAGA,CACP,CACJ,EAYAoE,EAAUgB,cAAc,CAAG,SAAUd,CAAK,EACtC,MAAO,CACHpJ,EAAGoJ,EAAMpJ,CAAC,CACV8E,EAAGsE,EAAMtE,CAAC,CACV3E,MAAOiJ,EAAMlJ,MAAM,CAACC,KAAK,CACzBsJ,MAAOL,EAAMlJ,MAAM,CAACuJ,KAAK,AAC7B,CACJ,EAMAP,EAAUpN,SAAS,CAACwN,YAAY,CAAG,SAAUtM,CAAO,EAChD,IAAI,CAACmN,OAAO,CAAGnN,EAAQmN,OAAO,CAC9B,IAAI,CAACC,OAAO,CAACpN,EAAS,KACtB,IAAI,CAACoN,OAAO,CAACpN,EAAS,KACtB,IAAI,CAACqN,OAAO,EAChB,EAOAnB,EAAUpN,SAAS,CAACyN,UAAU,CAAG,WAC7B,OAAO,IAAI,CAACe,iBAAiB,GACzB,IAAI,CAACtN,OAAO,CAAC,IAAI,CAACqH,MAAM,EACxB,IAAI,CAACrH,OAAO,AACpB,EAOAkM,EAAUpN,SAAS,CAACwO,iBAAiB,CAAG,WACpC,MAAO,AAAwB,YAAxB,OAAO,IAAI,CAACtN,OAAO,AAC9B,EAMAkM,EAAUpN,SAAS,CAACwK,YAAY,CAAG,WAC/B,IAAIsD,EAAQ,IAAI,CAACA,KAAK,CAClBC,EAAQ,IAAI,CAACA,KAAK,CAClB1J,EAAQ,IAAI,CAACD,MAAM,CAACC,KAAK,CACzBsJ,EAAQ,IAAI,CAACvJ,MAAM,CAACuJ,KAAK,CACzB/F,EAAI,CACA1D,EAAG4J,EACH9E,EAAG+E,EACHvD,aAAc,CAAA,EACdtJ,QAAS,CAAC,CACd,EAWJ,OAVImD,GACAuD,CAAAA,EAAE4C,YAAY,CAAG0C,GAAkBY,IAAUA,GAAS,GAAKA,GAASzJ,EAAMoK,GAAG,AAAD,EAE5Ed,GACA/F,CAAAA,EAAE4C,YAAY,CACV5C,EAAE4C,YAAY,EACV0C,GAAkBa,IAClBA,GAAS,GAAKA,GAASJ,EAAMc,GAAG,AAAD,EAE3CtB,GAAoB,IAAI,CAAC/I,MAAM,CAAC3C,KAAK,CAAE,oBAAqBmG,GACrDA,EAAE4C,YAAY,AACzB,EAKA4C,EAAUpN,SAAS,CAACuO,OAAO,CAAG,WAC1B,IAAInK,EAAS,IAAI,CAACA,MAAM,CACpBC,EAAQD,EAAOC,KAAK,CACpBsJ,EAAQvJ,EAAOuJ,KAAK,CACpBzM,EAAU,IAAI,CAACuM,UAAU,GACzBpJ,GACA,IAAI,CAACH,CAAC,CAAGhD,EAAQgD,CAAC,CAClB,IAAI,CAAC4J,KAAK,CAAGzJ,EAAMqK,QAAQ,CAACxN,EAAQgD,CAAC,CAAE,CAAA,KAGvC,IAAI,CAACA,CAAC,CAAG,KAAK,EACd,IAAI,CAAC4J,KAAK,CAAG5M,EAAQgD,CAAC,EAEtByJ,GACA,IAAI,CAAC3E,CAAC,CAAG9H,EAAQ8H,CAAC,CAClB,IAAI,CAAC+E,KAAK,CAAGJ,EAAMe,QAAQ,CAACxN,EAAQ8H,CAAC,CAAE,CAAA,KAGvC,IAAI,CAACA,CAAC,CAAG,KACT,IAAI,CAAC+E,KAAK,CAAG7M,EAAQ8H,CAAC,EAE1B,IAAI,CAAC2F,QAAQ,CAAG,IAAI,CAACnE,YAAY,EACrC,EAKA4C,EAAUpN,SAAS,CAAC4O,cAAc,CAAG,WACjC,IAAIxK,EAAS,IAAI,CAACA,MAAM,CACpBC,EAAQD,EAAOC,KAAK,CACpBsJ,EAAQvJ,EAAOuJ,KAAK,AACxB,CAAA,IAAI,CAACzJ,CAAC,CAAG,IAAI,CAAChD,OAAO,CAACgD,CAAC,CAAGG,EACtB,IAAI,CAACnD,OAAO,CAACgD,CAAC,CAAGG,EAAMwK,OAAO,CAAC,IAAI,CAACf,KAAK,CAAE,CAAA,GAC3C,IAAI,CAACA,KAAK,CACd,IAAI,CAAC9E,CAAC,CAAG,IAAI,CAAC9H,OAAO,CAAC8H,CAAC,CAAG2E,EACtBA,EAAMkB,OAAO,CAAC,IAAI,CAACd,KAAK,CAAE,CAAA,GAC1B,IAAI,CAACA,KAAK,AAClB,EAQAX,EAAUpN,SAAS,CAAC8O,MAAM,CAAG,SAAUvF,CAAE,CAAEC,CAAE,CAAEuF,CAAO,EAClD,GAAI,CAAC,IAAI,CAACP,iBAAiB,GAAI,CAC3B,IAAIQ,EAAMpK,KAAKoK,GAAG,CAACD,GACfE,EAAMrK,KAAKqK,GAAG,CAACF,GACf7K,EAAI,IAAI,CAAC4J,KAAK,CAAGvE,EACjBP,EAAI,IAAI,CAAC+E,KAAK,CAAGvE,CAGrB,CAAA,IAAI,CAACsE,KAAK,CAAGoB,AAFJhL,EAAI8K,EAAMhG,EAAIiG,EAEL1F,EAClB,IAAI,CAACwE,KAAK,CAAGoB,AAFJjL,EAAI+K,EAAMjG,EAAIgG,EAELxF,EAClB,IAAI,CAACoF,cAAc,EACvB,CACJ,EAkBAxB,EAAUpN,SAAS,CAACoP,KAAK,CAAG,SAAU7F,CAAE,CAAEC,CAAE,CAAEa,CAAE,CAAEC,CAAE,EAChD,GAAI,CAAC,IAAI,CAACkE,iBAAiB,GAAI,CAC3B,IAAItK,EAAI,IAAI,CAAC4J,KAAK,CAAGzD,EACjBrB,EAAI,IAAI,CAAC+E,KAAK,CAAGzD,CAGrB,CAAA,IAAI,CAACwD,KAAK,CAAGoB,AAFH,CAAA,EAAI7E,CAAC,EAAKd,EAEFrF,EAClB,IAAI,CAAC6J,KAAK,CAAGoB,AAFH,CAAA,EAAI7E,CAAC,EAAKd,EAEFR,EAClB,IAAI,CAAC4F,cAAc,EACvB,CACJ,EAQAxB,EAAUpN,SAAS,CAACsO,OAAO,CAAG,SAAUpN,CAAO,CAAEmO,CAAI,EACjD,IAAIC,EAAYD,EAAO,OACnBE,EAAcrO,CAAO,CAACoO,EAAS,CAC/B7N,EAAQ,IAAI,CAAC2C,MAAM,CAAC3C,KAAK,AAC7B,CAAA,IAAI,CAAC2C,MAAM,CAACkL,EAAS,CACjB,AAAuB,UAAvB,OAAOC,EACHA,EACArC,GAAkBqC,GACb9N,CAAK,CAAC6N,EAAS,CAACC,EAAY,EAEzB9N,EAAM5B,GAAG,CAAC0P,GACd,IAChB,EAOAnC,EAAUpN,SAAS,CAACwP,QAAQ,CAAG,WAC3B,IAAIC,EAAS,CAAC,IAAI,CAAC3B,KAAK,CACpB,IAAI,CAACC,KAAK,CAAE,EAAG,EAAE,CAKrB,OAJI,IAAI,CAAC3J,MAAM,CAAC3C,KAAK,CAACyI,QAAQ,GAC1BuF,CAAM,CAAC,EAAE,CAAG,IAAI,CAAC1B,KAAK,CACtB0B,CAAM,CAAC,EAAE,CAAG,IAAI,CAAC3B,KAAK,EAEnB2B,CACX,EAkBArC,EAAUpN,SAAS,CAAC6K,SAAS,CAAG,SAAU6E,CAAG,CAAEC,CAAG,CAAE3F,CAAE,CAAEF,CAAE,EACjD,IAAI,CAAC0E,iBAAiB,KACvB,IAAI,CAACV,KAAK,EAAI9D,EACd,IAAI,CAAC+D,KAAK,EAAIjE,EACd,IAAI,CAAC8E,cAAc,GAE3B,EACOxB,CACX,KAiHA,AAAC,SAAUzO,CAAa,EAepB,SAASiR,IACL,IAAIxD,EAAQ,IAAI,CACZyD,EAAgB,IAAI,CAACA,aAAa,CAClCC,EAAuB,IAAI,CAAC5O,OAAO,CAAC2O,aAAa,EAAI,EAAE,CAC3DC,EAAqB3N,OAAO,CAAC,SAAU4N,CAAmB,CAAE1N,CAAC,EACzD,IAAInB,EAAUZ,IAA8E0L,KAAK,CAACI,EAAMlL,OAAO,CAAC6O,mBAAmB,CAC/HA,EACC7O,CAAAA,EAAQoD,KAAK,EACdpD,CAAAA,EAAQoD,KAAK,CAAGjC,CAAAA,EAEpByN,CAAoB,CAACzN,EAAE,CAAGnB,EAC1B2O,EAAczO,IAAI,CAAC,IAtjB8B8K,EAsjBDE,EAAM3K,KAAK,CAAE2K,EAAOlL,GACxE,EACJ,CAUA,SAASuO,EAAOnC,CAAK,EACjB,IAAIzL,EAAUyL,EAAMlJ,MAAM,CAACmJ,UAAU,GACjC9L,EAAQ6L,EAAMlJ,MAAM,CAAC3C,KAAK,CAC1BuO,EAAM1C,EAAMD,IAAI,CACZC,EAAMkC,QAAQ,GACd/N,EAAMwO,OAAO,EACTxO,EAAMwO,OAAO,CAACC,SAAS,CAAChQ,IAAI,CAAC,CACzBuB,MAAO6L,EAAMlJ,MAAM,CAAC3C,KAAK,AAC7B,EACR6L,IACQ,CAAC,EAAG,EAAG,EAAG,EAAE,CACpBmC,EAAS,CACLvL,EAAG8L,CAAG,CAAC,EAAE,CAAI,CAAA,IAAI,CAAC9O,OAAO,CAACgD,CAAC,EAAI,CAAA,EAC/B8E,EAAGgH,CAAG,CAAC,EAAE,CAAI,CAAA,IAAI,CAAC9O,OAAO,CAAC8H,CAAC,EAAI,CAAA,EAC/B0D,OAAQsD,CAAG,CAAC,EAAE,EAAI,EAClBvD,MAAOuD,CAAG,CAAC,EAAE,EAAI,CACrB,EACJ,MAAO,CACHG,iBAAkBV,EAClBW,iBAAkB9P,IAA8E0L,KAAK,CAACyD,EAAQ,CAC1GvL,EAAGuL,EAAOvL,CAAC,CAAIoJ,CAAAA,EAAMD,IAAI,CAAGxL,EAAQqM,UAAU,CAAGzM,EAAMgJ,QAAQ,AAAD,EAC9DzB,EAAGyG,EAAOzG,CAAC,CAAIsE,CAAAA,EAAMD,IAAI,CAAGxL,EAAQsM,UAAU,CAAG1M,EAAMiJ,OAAO,AAAD,CACjE,EACJ,CACJ,CA6BA,SAAS2F,IACL,IAAI,CAACR,aAAa,CAAC1N,OAAO,CAAC,SAAUmO,CAAY,EAAI,OAAOA,EAAa9N,OAAO,EAAI,GACpF,IAAI,CAACf,KAAK,CAAG,KACb,IAAI,CAACoO,aAAa,CAAG,KACrB,IAAI,CAAC7L,MAAM,CAAG,KACd,IAAI,CAAC9C,OAAO,CAAG,KACX,IAAI,CAACF,UAAU,EACf,CAAA,IAAI,CAACA,UAAU,CAAG,IAAG,CAE7B,CAOA,SAASuP,IACL,IAAIrP,EAAU,IAAI,CAACA,OAAO,CAC1B,OAAQA,EAAQ8C,MAAM,EACjB9C,EAAQoM,KAAK,EAAIhN,IAA8EkQ,KAAK,CAACtP,EAAQoM,KAAK,CAC3H,CAOA,SAASmD,IACL,IAGIpO,EACAiL,EAJAoD,EAAgB,IAAI,CAACH,gBAAgB,GACrCvM,EAAS,IAAI,CAACA,MAAM,CACpByK,EAAM,AAACiC,GAAiBA,EAAcrN,MAAM,EAAK,EAGrD,IAAKhB,EAAI,EAAGA,EAAIoM,EAAKpM,IAAK,CAEtB,GAAI,CADJiL,CAAAA,EAAQ,IAAI,CAACA,KAAK,CAACoD,CAAa,CAACrO,EAAE,CAAE2B,CAAM,CAAC3B,EAAE,CAAA,EAClC,CACR2B,EAAOX,MAAM,CAAG,EAChB,MACJ,CACIiK,EAAMD,IAAI,EACVC,EAAMiB,OAAO,GAEjBvK,CAAM,CAAC3B,EAAE,CAAGiL,CAChB,CACA,OAAOtJ,CACX,CAWA,SAASsJ,EAAMqD,CAAY,CAAErD,CAAK,EAC9B,GAAIqD,GAAgBA,EAAavM,MAAM,CACnC,OAAOuM,EAEX,GAAI,CAACrD,GAASA,AAAiB,OAAjBA,EAAMlJ,MAAM,EACtB,GAAI9D,IAA8EsQ,QAAQ,CAACD,GACvFrD,EAAQ,IApQkCF,GAoQR,IAAI,CAAC3L,KAAK,CAAE,IAAI,CAAEkP,QAEnD,GAAIrQ,IAA8EuQ,QAAQ,CAACF,GAC5FrD,EAAQ,IAAI,CAAC7L,KAAK,CAAC5B,GAAG,CAAC8Q,IAAiB,UAEvC,GAAI,AAAwB,YAAxB,OAAOA,EAA6B,CACzC,IAAIG,EAAcH,EAAazQ,IAAI,CAACoN,EAChC,IAAI,EACRA,EAAQwD,EAAY1M,MAAM,CACtB0M,EACA,IA9QsC1D,GA8QZ,IAAI,CAAC3L,KAAK,CAAE,IAAI,CAAEkP,EACpD,EAEJ,OAAOrD,CACX,CAKA,SAASyD,EAAoB1E,CAAS,EAClC,IAAI,CAACwD,aAAa,CAAC1N,OAAO,CAAC,SAAUmO,CAAY,EAAI,OAAOA,EAAavP,MAAM,CAACsL,EAAY,EAChG,CAKA,SAAS2E,IACL,IAAI,CAACnB,aAAa,CAAC1N,OAAO,CAAC,SAAUmO,CAAY,EAAI,OAAOA,EAAa/D,MAAM,EAAI,EACvF,CAeA,SAAS0E,EAAUC,CAAc,CAAE3H,CAAE,CAAEC,CAAE,CAAE2H,CAAE,CAAEC,CAAE,EAC7C,IAAIhF,EAAQ,IAAI,CAChB,GAAI,IAAI,CAAC3K,KAAK,CAACyI,QAAQ,CAAE,CACrB,IAAIT,EAAOF,EACXA,EAAKC,EACLA,EAAKC,CACT,CACA,IAAI,CAACzF,MAAM,CAAC7B,OAAO,CAAC,SAAUkP,CAAM,CAAEhP,CAAC,EAAI,OAAQ+J,EAAMkF,cAAc,CAACJ,EAAgB3H,EAAIC,EAAI2H,EAAIC,EAAI/O,EAAK,EAAG,IAAI,CACxH,CAmBA,SAASiP,EAAeJ,CAAc,CAAE3H,CAAE,CAAEC,CAAE,CAAE2H,CAAE,CAAEC,CAAE,CAAE/O,CAAC,EACrD,IAAIiL,EAAQ,IAAI,CAACtJ,MAAM,CAAC3B,EAAE,AACrBiL,CAAAA,EAAMD,IAAI,EACXC,CAAAA,EAAQ,IAAI,CAACtJ,MAAM,CAAC3B,EAAE,CAAGkP,AA7UqBnE,GA6UCM,SAAS,CAACJ,EAAK,EAElEA,CAAK,CAAC4D,EAAe,CAAC3H,EAAIC,EAAI2H,EAAIC,EACtC,CASA,SAASvG,EAAUb,CAAE,CAAEF,CAAE,EACrB,IAAI,CAACmH,SAAS,CAAC,YAAa,KAAM,KAAMjH,EAAIF,EAChD,CAWA,SAAS0H,EAAexH,CAAE,CAAEF,CAAE,CAAEzH,CAAC,EAC7B,IAAI,CAACiP,cAAc,CAAC,YAAa,KAAM,KAAMtH,EAAIF,EAAIzH,EACzD,CAvKA1D,EAAcsH,OAAO,CAnBrB,SAAiBwL,CAAkB,EAC/B,IAAIC,EAAeD,EAAmBzR,SAAS,AAC1C0R,CAAAA,EAAa9B,gBAAgB,EAC9BtP,IAA8E0L,KAAK,CAAC,CAAA,EAAM0F,EAAc,CACpG9B,iBAAkBA,EAClBH,OAAQA,EACRY,qBAAsBA,EACtBE,iBAAkBA,EAClBE,WAAYA,EACZnD,MAAOA,EACPyD,oBAAqBA,EACrBC,oBAAqBA,EACrBC,UAAWA,EACXK,eAAgBA,EAChBzG,UAAWA,EACX2G,eAAgBA,CACpB,EAER,CAyKJ,EAAG7S,GAAkBA,CAAAA,EAAgB,CAAC,CAAA,GAMT,IAAIgT,GAA6BhT,EAW1DiT,GAAqB,AAACtR,IAA+E0L,KAAK,CAW1G6F,GAA8B,WAM9B,SAASA,EAAa7Q,CAAU,CAAEE,CAAO,CAAEoD,CAAK,CAAEwN,CAAQ,EACtD,IAAI,CAAC9Q,UAAU,CAAGA,EAClB,IAAI,CAACS,KAAK,CAAGT,EAAWS,KAAK,CAC7B,IAAI,CAACsQ,UAAU,CAAID,AAAa,UAAbA,EAAuB,SAAW,SACrD,IAAI,CAACjC,aAAa,CAAG,EAAE,CACvB,IAAI,CAAC3O,OAAO,CAAGA,EACf,IAAI,CAAC8C,MAAM,CAAG,EAAE,CAChB,IAAI,CAACM,KAAK,CAAGA,EACb,IAAI,CAACwN,QAAQ,CAAGA,EAChB,IAAI,CAACE,IAAI,CAAChR,EAAYE,EAASoD,EACnC,CAgMA,OAtLAuN,EAAa7R,SAAS,CAACsB,IAAI,CAAG,WAG1B,IAEA,IAHI2Q,EAAQ,EAAE,CAGVC,EAAK,EAELA,EAAKlM,UAAU3C,MAAM,CAErB6O,IAEID,CAAK,CAACC,EAAG,CAAGlM,SAAS,CAACkM,EAAG,CAEjC,IAAI,CAAC7Q,OAAO,CAACC,IAAI,CAACwE,KAAK,CAAC,IAAI,CAACzE,OAAO,CAAE2E,UAC1C,EASA6L,EAAa7R,SAAS,CAACmS,gBAAgB,CAAG,SAAUjR,CAAO,EACvD,IAGI1B,EACA4S,EAJAC,EAAM,IAAI,CAACvG,WAAW,CAACwG,QAAQ,CAC/BC,EAAQ,CAAC,EACT1J,EAAa,IAAI,CAACpH,KAAK,CAACoH,UAAU,CAGtC,IAAKrJ,KAAO0B,EACRkR,EAAYC,CAAG,CAAC7S,EAAI,CACI,KAAA,IAAb6S,CAAG,CAAC7S,EAAI,EACd,AAACqJ,GACE,AAC4B,KAD5B,CAAC,OAAQ,SAAU,eAAe,CAC7BJ,OAAO,CAAC2J,IACjBG,CAAAA,CAAK,CAACH,EAAU,CAAGlR,CAAO,CAAC1B,EAAI,AAAD,EAGtC,OAAO+S,CACX,EAKAV,EAAa7R,SAAS,CAACwC,OAAO,CAAG,WACzB,IAAI,CAACnB,OAAO,EACZ,CAAA,IAAI,CAACA,OAAO,CAAG,IAAI,CAACA,OAAO,CAACmB,OAAO,EAAC,EAEpC,IAAI,CAACgQ,OAAO,EACZ,CAAA,IAAI,CAACA,OAAO,CAAG,IAAI,CAACA,OAAO,CAAChQ,OAAO,EAAC,EAExC,IAAI,CAAC6N,oBAAoB,EAC7B,EAKAwB,EAAa7R,SAAS,CAACgS,IAAI,CAAG,SAAUhR,CAAU,CAAEE,CAAO,CAAEoD,CAAK,EAC9D,IAAI,CAACtD,UAAU,CAAGA,EAClB,IAAI,CAACS,KAAK,CAAGT,EAAWS,KAAK,CAC7B,IAAI,CAACP,OAAO,CAAGA,EACf,IAAI,CAAC8C,MAAM,CAAG,EAAE,CAChB,IAAI,CAAC6L,aAAa,CAAG,EAAE,CACvB,IAAI,CAACvL,KAAK,CAAGA,EACb,IAAI,CAACmM,UAAU,GACf,IAAI,CAACb,gBAAgB,EACzB,EAKAiC,EAAa7R,SAAS,CAACe,MAAM,CAAG,SAAUsL,CAAS,EAC/C,IAAI,CAAC0E,mBAAmB,CAAC1E,EAC7B,EAKAwF,EAAa7R,SAAS,CAACuM,MAAM,CAAG,SAEhCkG,CAAY,EACJ,IAAI,CAACvR,OAAO,CAACwR,SAAS,EAAI,IAAI,CAACrR,OAAO,EACtC,IAAI,CAACA,OAAO,CAACsR,QAAQ,CAAC,IAAI,CAACzR,OAAO,CAACwR,SAAS,EAEhD,IAAI,CAAC1B,mBAAmB,EAC5B,EAUAa,EAAa7R,SAAS,CAAC8O,MAAM,CAAG,SAAUvF,CAAE,CAAEC,CAAE,CAAEuF,CAAO,EACrD,IAAI,CAACkC,SAAS,CAAC,SAAU1H,EAAIC,EAAIuF,EACrC,EAaA8C,EAAa7R,SAAS,CAACoP,KAAK,CAAG,SAAU7F,CAAE,CAAEC,CAAE,CAAEa,CAAE,CAAEC,CAAE,EACnD,IAAI,CAAC2G,SAAS,CAAC,QAAS1H,EAAIC,EAAIa,EAAIC,EACxC,EAKAuH,EAAa7R,SAAS,CAAC4S,0BAA0B,CAAG,SAAU/F,CAAO,EACjE,IAAI,CAACgD,aAAa,CAAC1N,OAAO,CAAC,SAAUmO,CAAY,EAC7CA,EAAa1D,aAAa,CAACC,EAC/B,EACJ,EAOAgF,EAAa7R,SAAS,CAAC6S,aAAa,CAAG,WACnC,MAAO,CAAC,CAAC,IAAI,CAAC7O,MAAM,CAACX,MAAM,AAC/B,EAaAwO,EAAa7R,SAAS,CAAC8S,cAAc,CAAG,SAAU9I,CAAE,CAAEF,CAAE,CAAEiJ,CAAoB,EAC1E,IAAItR,EAAQ,IAAI,CAACT,UAAU,CAACS,KAAK,CAE7BuR,EAAe,IAAI,CAAChS,UAAU,CAACF,WAAW,CAE1CmS,EAAkBxR,EAAMN,WAAW,CAACsH,OAAO,CAAC,IAAI,CAACzH,UAAU,EAC3DkS,EAAezR,EAAMP,OAAO,CAACC,WAAW,CAAC8R,EAAgB,CAC7D,IAAI,CAACzB,cAAc,CAACxH,EAAIF,EAAI,GACxBiJ,GACA,IAAI,CAACvB,cAAc,CAACxH,EAAIF,EAAI,GAKhCoJ,CAAY,CAAC,IAAI,CAACnB,UAAU,CAAC,CAAC,IAAI,CAACzN,KAAK,CAAC,CACpCgJ,KAAK,CAAG,IAAI,CAACpM,OAAO,CAACoM,KAAK,CAC/B0F,CAAY,CAAC,IAAI,CAACjB,UAAU,CAAC,CAAC,IAAI,CAACzN,KAAK,CAAC,CACpCgJ,KAAK,CAAG,IAAI,CAACpM,OAAO,CAACoM,KAAK,AACnC,EAKAuE,EAAa7R,SAAS,CAAC8M,MAAM,CAAG,SAAUqG,CAAU,EAChD,IAAInS,EAAa,IAAI,CAACA,UAAU,CAC5BE,EAAU0Q,GAAmB,CAAA,EAC7B,IAAI,CAAC1Q,OAAO,CACZiS,GACAC,EAAc,IAAI,CAAC/R,OAAO,CAAC+R,WAAW,CACtCC,EAAc,IAAI,CAACvH,WAAW,CAClC,IAAI,CAACtJ,OAAO,GAKZoP,GAAmB,CAAA,EAAM,IAAI,CAJP,IAAIyB,EAAYrS,EAClCE,EACA,IAAI,CAACoD,KAAK,CACV,IAAI,CAACwN,QAAQ,GAEjB,IAAI,CAACvF,MAAM,CAAC6G,GACZ,IAAI,CAACrS,MAAM,EACf,EACO8Q,CACX,IACAF,GAA0B1L,OAAO,CAAC4L,IA4IlC,OAnDqB,CAIjByB,MAAO,CACHC,QAAS,SACTC,WAAY,CACR/N,GAAI,QACJgO,KAAM,EACNC,KAAM,EACNC,YAAa,GACbC,aAAc,EAClB,EAIAC,SAAU,CAAC,CACHN,QAAS,OACTC,WAAY,CACRnU,EAAG,wBACH,eAAgB,CACpB,CACJ,EAAE,AACV,EAIA,gBAAiB,CACbkU,QAAS,SACTC,WAAY,CACR/N,GAAI,gBACJgO,KAAM,EACNC,KAAM,EACNC,YAAa,GACbC,aAAc,EAClB,EACAC,SAAU,CAAC,CACHN,QAAS,OACTC,WAAY,CAERnU,EAAG,yBACH,eAAgB,CACpB,CACJ,EAAE,AACV,CACJ,EAkBIyU,IACIxV,EAAgB,SAAUe,CAAC,CAC3BoM,CAAC,EAOD,MAAOnN,AANHA,CAAAA,EAAgBoB,OAAOgM,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAalH,OAAS,SAAUpF,CAAC,CAC1DoM,CAAC,EAAIpM,EAAEsM,SAAS,CAAGF,CAAG,GACd,SAAUpM,CAAC,CACnBoM,CAAC,EAAI,IAAK,IAAIG,KAAKH,EAAO/L,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACuL,EAC/DG,IAAIvM,CAAAA,CAAC,CAACuM,EAAE,CAAGH,CAAC,CAACG,EAAE,AAAD,CAAG,CAAA,EACIvM,EAAGoM,EAC5B,EACO,SAAUpM,CAAC,CAAEoM,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIsI,UAAU,uBAAyBC,OAAOvI,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACC,WAAW,CAAGzM,CAAG,CADtCf,EAAce,EAAGoM,GAEjBpM,EAAEW,SAAS,CAAGyL,AAAM,OAANA,EAAa/L,OAAOqM,MAAM,CAACN,GAAMI,CAAAA,EAAG7L,SAAS,CAAGyL,EAAEzL,SAAS,CAAE,IAAI6L,CAAG,CACtF,GAOAoI,GAA4B,AAAC3T,IAA+EC,QAAQ,CAAE2T,GAA2B,AAAC5T,IAA+EyG,OAAO,CAAEoN,GAAS,AAAC7T,IAA+E6T,MAAM,CAAEC,GAAyB,AAAC9T,IAA+E0L,KAAK,CAAEqI,GAAY,AAAC/T,IAA+E+T,SAAS,CAMhiBC,GAAkBC,GAAmB,cACrCC,GAAoBD,GAAmB,gBAEvCE,GAAe,oBAAuB,CAAA,AAACnU,IAA+EoU,GAAG,CAAG,KAAS,IAAI,EAAK,IASlJ,SAASH,GAAmBI,CAAU,EAClC,OAAO,SAAUC,CAAK,EAClB,IAAI,CAACtT,IAAI,CAACqT,EAAY,QAAUC,EAAQ,IAC5C,CACJ,CAIA,SAASC,KACL,IAAI,CAAC3T,OAAO,CAAC4T,IAAI,CAAGV,MAAwD,IAAI,CAAClT,OAAO,CAAC4T,IAAI,EAAI,CAAC,EAYtG,CAIA,SAASC,GAAqBtP,CAAE,CAAEuP,CAAa,EAC3C,IAAI9T,EAAU,CAAEsS,WAAY,CAAE/N,GAAIA,CAAG,CAAE,EACnC8M,EAAQ,CACJ0C,OAAQD,EAAcE,KAAK,EAAI,OAC/BC,KAAMH,EAAcE,KAAK,EAAI,qBACjC,CACJhU,CAAAA,EAAQ2S,QAAQ,CAAImB,EAAcnB,QAAQ,EACtCmB,EAAcnB,QAAQ,CAACxB,GAAG,CAAC,SAAU+C,CAAK,EACtC,OAAOhB,GAAuB7B,EAAO6C,EACzC,GACJ,IAAIC,EAAMjB,GAAuB,CAAA,EAAM,CAC/BZ,WAAY,CACRG,YAAa,GACbC,aAAc,GACdF,KAAM,EACND,KAAM,EACN6B,OAAQ,MACZ,CACJ,EACAN,EACA9T,GACAqU,EAAS,IAAI,CAAChW,UAAU,CAAC8V,GAE7B,OADAE,EAAO9P,EAAE,CAAGA,EACL8P,CACX,CAwBA,IAAIC,GAAkC,SAAUrJ,CAAM,EAOlD,SAASqJ,EAAiBxU,CAAU,CAAEE,CAAO,CAAEoD,CAAK,EAChD,IAAI8H,EAAQD,EAAOjM,IAAI,CAAC,IAAI,CACxBc,EACAE,EACAoD,EAAO,UAAY,IAAI,CAO3B,OADA8H,EAAMvF,IAAI,CAAG,OACNuF,CACX,CAsKA,OAxLA0H,GAAyB0B,EAAkBrJ,GAwB3CqJ,EAAiBvP,OAAO,CAAG,SAAUE,CAAU,CAAEsP,CAAgB,EAC7D,IAAIC,EAAmBD,EAAiBzV,SAAS,AAC5C0V,CAAAA,EAAiBC,SAAS,GAC3B1B,GAA0B9N,EAAY,oBAAqB0O,IAC3Da,EAAiBC,SAAS,CAAGZ,GAErC,EAYAS,EAAiBxV,SAAS,CAAC4V,GAAG,CAAG,WAC7B,IAAIC,EAAU,IAAI,CAAC3U,OAAO,CAAC7B,CAAC,CAC5B,GAAIwW,EACA,MAAO,AAAmB,YAAnB,OAAOA,EACVA,EAAQ3V,IAAI,CAAC,IAAI,EACjB2V,EAER,IAOIxH,EAPArK,EAAS,IAAI,CAACA,MAAM,CACpByK,EAAMzK,EAAOX,MAAM,CACnBhE,EAAI,EAAE,CACNyW,EAAWrH,EACXnB,EAAQtJ,CAAM,CAAC,EAAE,CACjB+R,EAAWD,GAAY,IAAI,CAACrG,MAAM,CAACnC,GAAO8C,gBAAgB,CAC1D4F,EAAa,EAEjB,GAAID,EAEA,IADA1W,EAAE+B,IAAI,CAAC,CAAC,IAAK2U,EAAS7R,CAAC,CAAE6R,EAAS/M,CAAC,CAAC,EAC7B,EAAEgN,EAAavH,GAAOqH,GAEzBzH,EAAUf,AADVA,CAAAA,EAAQtJ,CAAM,CAACgS,EAAW,AAAD,EACT3H,OAAO,EAAI,IAC3B0H,EAAW,IAAI,CAACtG,MAAM,CAACnC,GAAO8C,gBAAgB,CAC1C/B,AAAY,MAAZA,EACAhP,EAAE+B,IAAI,CAAC,CAACiN,EAAS0H,EAAS7R,CAAC,CAAE6R,EAAS/M,CAAC,CAAC,EAEnCqF,AAAY,MAAZA,EACLhP,EAAE+B,IAAI,CAAC,CAACiN,EAAS0H,EAAS7R,CAAC,CAAE6R,EAAS/M,CAAC,CAAC,EAEvB,MAAZqF,GACLhP,EAAE+B,IAAI,CAAC,CAACiN,EAAQ,EAEpByH,EAAWxI,EAAMlJ,MAAM,CAACyI,OAAO,CAGvC,OAAQiJ,GAAY,IAAI,CAACzU,OAAO,CAC5B,IAAI,CAACI,KAAK,CAACE,QAAQ,CAACsU,SAAS,CAAC5W,EAAG,IAAI,CAACgC,OAAO,CAAC6U,WAAW,IACzD,IACR,EACAV,EAAiBxV,SAAS,CAAC6S,aAAa,CAAG,WACvC,OAAO1G,EAAOnM,SAAS,CAAC6S,aAAa,CAAC3S,IAAI,CAAC,IAAI,GAAK,CAAC,CAAC,IAAI,CAACgB,OAAO,CAAC7B,CAAC,AACxE,EACAmW,EAAiBxV,SAAS,CAACuM,MAAM,CAAG,SAAU4J,CAAM,EAChD,IAAIjV,EAAU,IAAI,CAACA,OAAO,CACtBqR,EAAQ,IAAI,CAACJ,gBAAgB,CAACjR,EAClC,CAAA,IAAI,CAACG,OAAO,CAAG,IAAI,CAACL,UAAU,CAACS,KAAK,CAACE,QAAQ,CACxCyU,IAAI,CAAC,CAAC,CAAC,IAAK,EAAG,EAAE,CAAC,EAClB9U,IAAI,CAACiR,GACLrQ,GAAG,CAACiU,GACT,IAAI,CAAC3D,OAAO,CAAG,IAAI,CAACxR,UAAU,CAACS,KAAK,CAACE,QAAQ,CACxCyU,IAAI,CAAC,CAAC,CAAC,IAAK,EAAG,EAAE,CAAC,EAClBzD,QAAQ,CAAC,2BACTrR,IAAI,CAAC,CACNU,OAAQ,CACZ,GACKE,GAAG,CAACiU,GACJ,IAAI,CAACnV,UAAU,CAACS,KAAK,CAACoH,UAAU,EACjC,IAAI,CAAC2J,OAAO,CAAClR,IAAI,CAAC,CACd,kBAAmB,QACnB2T,OAAQR,GACRU,KAAMV,GACN,eAAgB,IAAI,CAACpT,OAAO,CAAC6U,WAAW,GACpChV,AAAe,EAAfA,EAAQmV,IAAI,AACpB,GAEJlK,EAAOnM,SAAS,CAACuM,MAAM,CAACrM,IAAI,CAAC,IAAI,EACjCiU,GAAO,IAAI,CAAC9S,OAAO,CAAE,CAAEmT,kBAAmBA,GAAmBF,gBAAiBA,EAAgB,GAC9F,IAAI,CAACgC,UAAU,CAAC,IAAI,CACxB,EACAd,EAAiBxV,SAAS,CAACe,MAAM,CAAG,SAAUsL,CAAS,EACnD,GAAI,IAAI,CAAChL,OAAO,CAAE,CACd,IAAIhC,EAAI,IAAI,CAACuW,GAAG,GACZW,EAASlK,EAAY,UAAY,OACjChN,GACA,IAAI,CAACgC,OAAO,CAACkV,EAAO,CAAC,CAAElX,EAAGA,CAAE,GAC5B,IAAI,CAACmT,OAAO,CAAC+D,EAAO,CAAC,CAAElX,EAAGA,CAAE,KAG5B,IAAI,CAACgC,OAAO,CAACC,IAAI,CAAC,CAAEjC,EAAG,iBAAc,GACrC,IAAI,CAACmT,OAAO,CAAClR,IAAI,CAAC,CAAEjC,EAAG,iBAAc,IAEzC,IAAI,CAACgC,OAAO,CAACmV,MAAM,CAAG,IAAI,CAAChE,OAAO,CAACgE,MAAM,CAAG,CAAC,CAACnX,CAClD,CACA8M,EAAOnM,SAAS,CAACe,MAAM,CAACb,IAAI,CAAC,IAAI,CAAEmM,EACvC,EAMAmJ,EAAiBxV,SAAS,CAACsW,UAAU,CAAG,SAAUG,CAAI,EAClD,IAAIC,EAAcD,EAAKvV,OAAO,CAC1BO,EAAQgV,EAAKhV,KAAK,CAClBqT,EAAOrT,EAAMP,OAAO,CAAC4T,IAAI,CACzBK,EAAOuB,EAAYvB,IAAI,CACvBD,EAAQhB,GAAyBiB,IAASA,AAAS,SAATA,EACtCA,EACAuB,EAAYzB,MAAM,CA0B1B,CAAC,cAAe,YAAY,CACvB9S,OAAO,CA1BI,SAAUwS,CAAU,EAC5B,IACAgC,EACAC,EACApX,EACA+V,EAJIsB,EAAWH,CAAW,CAAC/B,EAAW,CAK1C,GAAIkC,EAAU,CACV,IAAKrX,KAAOsV,EAER,GAAI,AAAC+B,CAAAA,IAAcF,CAAAA,AADnBA,CAAAA,EAAM7B,CAAI,CAACtV,EAAI,AAAD,EACSgU,UAAU,EAAImD,EAAInD,UAAU,CAAC/N,EAAE,AAAD,GAGjDoR,IAAaF,EAAIlR,EAAE,AAAD,GAClBkR,AAAgB,WAAhBA,EAAIpD,OAAO,CAAe,CAC1BqD,EAAmBD,EACnB,KACJ,CAEAC,IACArB,EAASkB,CAAI,CAAC9B,EAAW,CAAGlT,EAAME,QAAQ,CACrCgU,SAAS,CAAC,AAACe,CAAAA,EAAYjR,EAAE,EAAI4O,IAAU,EAAK,IAAMwC,EAAUzC,GAAuBwC,EAAkB,CAAE1B,MAAOA,CAAM,IACzHuB,EAAKnV,IAAI,CAACqT,EAAYY,EAAOuB,YAAY,CAAC,OAElD,CACJ,EAGJ,EAYAtB,EAAiBlD,QAAQ,CAAG,CACxByE,UAAW,YACXb,YAAa,eACbjB,OAAQ,SACRE,KAAM,OACNnT,OAAQ,QACZ,EACOwT,CACX,EA/b+D3D,IA8c3DmF,IACI1Y,EAAgB,SAAUe,CAAC,CAC3BoM,CAAC,EAOD,MAAOnN,AANHA,CAAAA,EAAgBoB,OAAOgM,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAalH,OAAS,SAAUpF,CAAC,CAC1DoM,CAAC,EAAIpM,EAAEsM,SAAS,CAAGF,CAAG,GACd,SAAUpM,CAAC,CACnBoM,CAAC,EAAI,IAAK,IAAIG,KAAKH,EAAO/L,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACuL,EAC/DG,IAAIvM,CAAAA,CAAC,CAACuM,EAAE,CAAGH,CAAC,CAACG,EAAE,AAAD,CAAG,CAAA,EACIvM,EAAGoM,EAC5B,EACO,SAAUpM,CAAC,CAAEoM,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIsI,UAAU,uBAAyBC,OAAOvI,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACC,WAAW,CAAGzM,CAAG,CADtCf,EAAce,EAAGoM,GAEjBpM,EAAEW,SAAS,CAAGyL,AAAM,OAANA,EAAa/L,OAAOqM,MAAM,CAACN,GAAMI,CAAAA,EAAG7L,SAAS,CAAGyL,EAAEzL,SAAS,CAAE,IAAI6L,CAAG,CACtF,GAKAoL,GAAyB,AAAC3W,IAA+E0L,KAAK,CAwB9GkL,GAAkC,SAAU/K,CAAM,EAOlD,SAAS+K,EAAiBlW,CAAU,CAAEE,CAAO,CAAEoD,CAAK,EAChD,IAAI8H,EAAQD,EAAOjM,IAAI,CAAC,IAAI,CACxBc,EACAE,EACAoD,EAAO,UAAY,IAAI,CAQ3B,OAFA8H,EAAMvF,IAAI,CAAG,OACbuF,EAAMvB,SAAS,CAAGsB,EAAOnM,SAAS,CAAC8S,cAAc,CAC1C1G,CACX,CAiDA,OApEA4K,GAAyBE,EAAkB/K,GAyB3C+K,EAAiBlX,SAAS,CAACuM,MAAM,CAAG,SAAU4J,CAAM,EAChD,IAAI5D,EAAQ,IAAI,CAACJ,gBAAgB,CAAC,IAAI,CAACjR,OAAO,CAC9C,CAAA,IAAI,CAACG,OAAO,CAAG,IAAI,CAACL,UAAU,CAACS,KAAK,CAACE,QAAQ,CACxCwV,IAAI,CAAC,EAAG,KAAM,EAAG,GACjB7V,IAAI,CAACiR,GACLrQ,GAAG,CAACiU,GACThK,EAAOnM,SAAS,CAACuM,MAAM,CAACrM,IAAI,CAAC,IAAI,CACrC,EACAgX,EAAiBlX,SAAS,CAACe,MAAM,CAAG,SAAUsL,CAAS,EACnD,GAAI,IAAI,CAAChL,OAAO,CAAE,CACd,IAAI0U,EAAW,IAAI,CAACtG,MAAM,CAAC,IAAI,CAACzL,MAAM,CAAC,EAAE,EAAEoM,gBAAgB,CACvD2F,EACA,IAAI,CAAC1U,OAAO,CAACgL,EAAY,UAAY,OAAO,CAAC,CACzCnI,EAAG6R,EAAS7R,CAAC,CACb8E,EAAG+M,EAAS/M,CAAC,CACbyD,MAAO,IAAI,CAACvL,OAAO,CAACuL,KAAK,CACzBC,OAAQ,IAAI,CAACxL,OAAO,CAACwL,MAAM,AAC/B,GAGA,IAAI,CAACpL,IAAI,CAAC,CACN4C,EAAG,EACH8E,EAAG,IACP,GAEJ,IAAI,CAAC3H,OAAO,CAACmV,MAAM,CAAGY,CAAAA,CAAQrB,CAClC,CACA5J,EAAOnM,SAAS,CAACe,MAAM,CAACb,IAAI,CAAC,IAAI,CAAEmM,EACvC,EAWA6K,EAAiB5E,QAAQ,CAAG2E,GAAuBI,AAxHY7B,GAwHmBlD,QAAQ,CAAE,CACxF7F,MAAO,QACPC,OAAQ,QACZ,GACOwK,CACX,EAlkB+DrF,IAilB3DyF,IACIhZ,EAAgB,SAAUe,CAAC,CAC3BoM,CAAC,EAOD,MAAOnN,AANHA,CAAAA,EAAgBoB,OAAOgM,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAalH,OAAS,SAAUpF,CAAC,CAC1DoM,CAAC,EAAIpM,EAAEsM,SAAS,CAAGF,CAAG,GACd,SAAUpM,CAAC,CACnBoM,CAAC,EAAI,IAAK,IAAIG,KAAKH,EAAO/L,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACuL,EAC/DG,IAAIvM,CAAAA,CAAC,CAACuM,EAAE,CAAGH,CAAC,CAACG,EAAE,AAAD,CAAG,CAAA,EACIvM,EAAGoM,EAC5B,EACO,SAAUpM,CAAC,CAAEoM,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIsI,UAAU,uBAAyBC,OAAOvI,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACC,WAAW,CAAGzM,CAAG,CADtCf,EAAce,EAAGoM,GAEjBpM,EAAEW,SAAS,CAAGyL,AAAM,OAANA,EAAa/L,OAAOqM,MAAM,CAACN,GAAMI,CAAAA,EAAG7L,SAAS,CAAGyL,EAAEzL,SAAS,CAAE,IAAI6L,CAAG,CACtF,GAKA0L,GAA2B,AAACjX,IAA+E0L,KAAK,CAmBhHwL,GAAoC,SAAUrL,CAAM,EAOpD,SAASqL,EAAmBxW,CAAU,CAAEE,CAAO,CAAEoD,CAAK,EAClD,IAAI8H,EAAQD,EAAOjM,IAAI,CAAC,IAAI,CACxBc,EACAE,EACAoD,EAAO,UAAY,IAAI,CAQ3B,OAFA8H,EAAMvF,IAAI,CAAG,SACbuF,EAAMvB,SAAS,CAAGsB,EAAOnM,SAAS,CAAC8S,cAAc,CAC1C1G,CACX,CA8DA,OAjFAkL,GAA2BE,EAAoBrL,GA4B/CqL,EAAmBxX,SAAS,CAACe,MAAM,CAAG,SAAUsL,CAAS,EACrD,GAAI,IAAI,CAAChL,OAAO,CAAE,CACd,IAAI0U,EAAW,IAAI,CAACtG,MAAM,CAAC,IAAI,CAACzL,MAAM,CAAC,EAAE,EAAEoM,gBAAgB,CACvD2F,EACA,IAAI,CAAC1U,OAAO,CAACgL,EAAY,UAAY,OAAO,CAAC,CACzCnI,EAAG6R,EAAS7R,CAAC,CACb8E,EAAG+M,EAAS/M,CAAC,CACbyO,EAAG,IAAI,CAACvW,OAAO,CAACuW,CAAC,AACrB,GAGA,IAAI,CAACpW,OAAO,CAACC,IAAI,CAAC,CACd4C,EAAG,EACH8E,EAAG,IACP,GAEJ,IAAI,CAAC3H,OAAO,CAACmV,MAAM,CAAG,CAAC,CAACT,CAC5B,CACA5J,EAAOnM,SAAS,CAACe,MAAM,CAACb,IAAI,CAAC,IAAI,CAAEmM,EACvC,EAIAmL,EAAmBxX,SAAS,CAACuM,MAAM,CAAG,SAAU4J,CAAM,EAClD,IAAI5D,EAAQ,IAAI,CAACJ,gBAAgB,CAAC,IAAI,CAACjR,OAAO,CAC9C,CAAA,IAAI,CAACG,OAAO,CAAG,IAAI,CAACL,UAAU,CAACS,KAAK,CAACE,QAAQ,CACxC+V,MAAM,CAAC,EAAG,KAAM,GAChBpW,IAAI,CAACiR,GACLrQ,GAAG,CAACiU,GACThK,EAAOnM,SAAS,CAACuM,MAAM,CAACrM,IAAI,CAAC,IAAI,CACrC,EAOAsX,EAAmBxX,SAAS,CAAC2X,SAAS,CAAG,SAAUF,CAAC,EAChD,IAAI,CAACvW,OAAO,CAACuW,CAAC,CAAGA,CACrB,EAaAD,EAAmBlF,QAAQ,CAAGiF,GAAyBF,AAtQQ7B,GAsQuBlD,QAAQ,CAAE,CAAEmF,EAAG,GAAI,GAClGD,CACX,EA7sB+D3F,IA8tB3D+F,IACItZ,EAAgB,SAAUe,CAAC,CAC3BoM,CAAC,EAOD,MAAOnN,AANHA,CAAAA,EAAgBoB,OAAOgM,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAalH,OAAS,SAAUpF,CAAC,CAC1DoM,CAAC,EAAIpM,EAAEsM,SAAS,CAAGF,CAAG,GACd,SAAUpM,CAAC,CACnBoM,CAAC,EAAI,IAAK,IAAIG,KAAKH,EAAO/L,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACuL,EAC/DG,IAAIvM,CAAAA,CAAC,CAACuM,EAAE,CAAGH,CAAC,CAACG,EAAE,AAAD,CAAG,CAAA,EACIvM,EAAGoM,EAC5B,EACO,SAAUpM,CAAC,CAAEoM,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIsI,UAAU,uBAAyBC,OAAOvI,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACC,WAAW,CAAGzM,CAAG,CADtCf,EAAce,EAAGoM,GAEjBpM,EAAEW,SAAS,CAAGyL,AAAM,OAANA,EAAa/L,OAAOqM,MAAM,CAACN,GAAMI,CAAAA,EAAG7L,SAAS,CAAGyL,EAAEzL,SAAS,CAAE,IAAI6L,CAAG,CACtF,GAKAgM,GAA4B,AAACvX,IAA+E0L,KAAK,CAAE8L,GAA8B,AAACxX,IAA+EyG,OAAO,CAmBxOgR,GAAqC,SAAU5L,CAAM,EAOrD,SAAS4L,EAAoB/W,CAAU,CAAEE,CAAO,CAAEoD,CAAK,EACnD,IAAI8H,EAAQD,EAAOjM,IAAI,CAAC,IAAI,CACxBc,EACAE,EACAoD,EAAO,UAAY,IAAI,CAO3B,OADA8H,EAAMvF,IAAI,CAAG,UACNuF,CACX,CAqKA,OAvLAwL,GAA4BG,EAAqB5L,GA2BjD4L,EAAoB/X,SAAS,CAACgS,IAAI,CAAG,SAAUhR,CAAU,CAAEE,CAAO,CAAEoD,CAAK,EACjEwT,GAA4B5W,EAAQyM,KAAK,GACzCzM,EAAQ8C,MAAM,CAAC7B,OAAO,CAAC,SAAUmL,CAAK,EAClCA,EAAMK,KAAK,CAAGzM,EAAQyM,KAAK,AAC/B,GAEAmK,GAA4B5W,EAAQmD,KAAK,GACzCnD,EAAQ8C,MAAM,CAAC7B,OAAO,CAAC,SAAUmL,CAAK,EAClCA,EAAMjJ,KAAK,CAAGnD,EAAQmD,KAAK,AAC/B,GAEJ8H,EAAOnM,SAAS,CAACgS,IAAI,CAAC9R,IAAI,CAAC,IAAI,CAAEc,EAAYE,EAASoD,EAC1D,EAOAyT,EAAoB/X,SAAS,CAACuM,MAAM,CAAG,SAAU4J,CAAM,EACnD,IAAI,CAAC9U,OAAO,CAAG,IAAI,CAACL,UAAU,CAACS,KAAK,CAACE,QAAQ,CAACqW,aAAa,CAAC,WACvD1W,IAAI,CAAC,IAAI,CAAC6Q,gBAAgB,CAAC,IAAI,CAACjR,OAAO,GACvCgB,GAAG,CAACiU,GACThK,EAAOnM,SAAS,CAACuM,MAAM,CAACrM,IAAI,CAAC,IAAI,CACrC,EAKA6X,EAAoB/X,SAAS,CAAC6K,SAAS,CAAG,SAAUb,CAAE,CAAEF,CAAE,EACtDqC,EAAOnM,SAAS,CAAC8S,cAAc,CAAC5S,IAAI,CAAC,IAAI,CAAE8J,EAAIF,EAAI,CAAA,EACvD,EAaAiO,EAAoB/X,SAAS,CAACiY,mBAAmB,CAAG,SAAUC,CAAM,CAAEC,CAAM,CAAEC,CAAE,CAAEC,CAAE,EAChF,OAAOzT,KAAK0T,GAAG,CAAC,AAACH,CAAAA,EAAOnP,CAAC,CAAGkP,EAAOlP,CAAC,AAADA,EAAKoP,EAAK,AAACD,CAAAA,EAAOjU,CAAC,CAAGgU,EAAOhU,CAAC,AAADA,EAAKmU,EACjEF,EAAOjU,CAAC,CAAGgU,EAAOlP,CAAC,CAAGmP,EAAOnP,CAAC,CAAGkP,EAAOhU,CAAC,EAAIU,KAAK2T,IAAI,CAAC,AAACJ,CAAAA,EAAOnP,CAAC,CAAGkP,EAAOlP,CAAC,AAADA,EAAMmP,CAAAA,EAAOnP,CAAC,CAAGkP,EAAOlP,CAAC,AAADA,EAClG,AAACmP,CAAAA,EAAOjU,CAAC,CAAGgU,EAAOhU,CAAC,AAADA,EAAMiU,CAAAA,EAAOjU,CAAC,CAAGgU,EAAOhU,CAAC,AAADA,EACnD,EAUA6T,EAAoB/X,SAAS,CAACwY,QAAQ,CAAG,SAAUzC,CAAQ,CAAE0C,CAAS,EAClE,IAAIC,EAAK3C,EAAS7R,CAAC,CAAEyU,EAAK5C,EAAS/M,CAAC,CAAE4P,EAAKH,EAAUvU,CAAC,CAAE2U,EAAKJ,EAAUzP,CAAC,CAAEO,EAAK,AAACmP,CAAAA,EAAKE,CAAC,EAAK,EAAuBE,EAAKlU,KAAK2T,IAAI,CAAC,AAACG,CAAAA,EAAKE,CAAC,EAAMF,CAAAA,EAAKE,CAAC,EAAK,EAAI,AAACD,CAAAA,EAAKE,CAAC,EAAMF,CAAAA,EAAKE,CAAC,EAAK,GACjLE,EAAQnU,AAAiB,IAAjBA,KAAKoU,IAAI,CAD0K,AAACH,CAAAA,EAAKF,CAAC,EAAMC,CAAAA,EAAKF,CAAC,GAC/K9T,KAAKqU,EAAE,CAK1C,OAJI1P,EAAKmP,GACLK,CAAAA,GAAS,GAAE,EAGR,CAAExP,GAAIA,EAAIC,GANkF,AAACmP,CAAAA,EAAKE,CAAC,EAAK,EAMtFC,GAAIA,EAAII,GADxB,IAAI,CAACC,KAAK,GACsBJ,MAAOA,CAAM,CAC1D,EAKAhB,EAAoB/X,SAAS,CAACmZ,KAAK,CAAG,WAClC,IAAIxL,EAAQ,IAAI,CAACyL,QAAQ,GACzB,OAAOtB,GAA4BnK,GAC/B/I,KAAK0T,GAAG,CAAC3K,EAAMe,QAAQ,CAAC,IAAI,CAACxN,OAAO,CAACgY,EAAE,EAAIvL,EAAMe,QAAQ,CAAC,IAC1D,IAAI,CAACxN,OAAO,CAACgY,EAAE,AACvB,EAKAnB,EAAoB/X,SAAS,CAACoZ,QAAQ,CAAG,WACrC,IAAIC,EAAa,IAAI,CAACnY,OAAO,CAACyM,KAAK,CACnC,OAAO,IAAI,CAAClM,KAAK,CAACkM,KAAK,CAAC0L,EAAW,AACvC,EAOAtB,EAAoB/X,SAAS,CAACsZ,mBAAmB,CAAG,SAAUhM,CAAK,EAC/D,OAAO,IAAI,CAACmC,MAAM,CAACnC,GAAO8C,gBAAgB,AAC9C,EAOA2H,EAAoB/X,SAAS,CAACe,MAAM,CAAG,SAAUsL,CAAS,EACtD,GAAI,IAAI,CAAChL,OAAO,CAAE,CACd,IAAI0U,EAAW,IAAI,CAACuD,mBAAmB,CAAC,IAAI,CAACtV,MAAM,CAAC,EAAE,EAClDyU,EAAY,IAAI,CAACa,mBAAmB,CAAC,IAAI,CAACtV,MAAM,CAAC,EAAE,EACnDuO,EAAQ,IAAI,CAACiG,QAAQ,CAACzC,EACtB0C,GACA1C,EACA,IAAI,CAAC1U,OAAO,CAACgL,EAAY,UAAY,OAAO,CAAC,CACzC9C,GAAIgJ,EAAMhJ,EAAE,CACZC,GAAI+I,EAAM/I,EAAE,CACZsP,GAAIvG,EAAMuG,EAAE,CACZI,GAAI3G,EAAM2G,EAAE,CACZK,SAAUhH,EAAMwG,KAAK,CACrBS,gBAAiBjH,EAAMhJ,EAAE,CACzBkQ,gBAAiBlH,EAAM/I,EAAE,AAC7B,GAGA,IAAI,CAACnI,OAAO,CAACC,IAAI,CAAC,CACd4C,EAAG,EACH8E,EAAG,IACP,GAEJ,IAAI,CAAC3H,OAAO,CAACmV,MAAM,CAAGY,CAAAA,CAAQrB,CAClC,CACA5J,EAAOnM,SAAS,CAACe,MAAM,CAACb,IAAI,CAAC,IAAI,CAAEmM,EACvC,EAOA0L,EAAoB/X,SAAS,CAAC0Z,UAAU,CAAG,SAAUR,CAAE,EACnD,IAAIpO,EAAS,IAAI,CAAC9J,UAAU,CAACF,WAAW,CAACgK,MAAM,AAC/C,CAAA,IAAI,CAAC5J,OAAO,CAACgY,EAAE,CAAGA,EACdpO,GAAUA,CAAM,CAAC,EAAE,GACnBA,CAAM,CAAC,EAAE,CAACoO,EAAE,CAAGA,EACfpO,CAAM,CAAC,EAAE,CAACoO,EAAE,CAAGA,EAEvB,EAaAnB,EAAoBzF,QAAQ,CAAGuF,GAA0BR,AAvfM7B,GAufyBlD,QAAQ,CAAE,CAC9F4G,GAAI,IACR,GACOnB,CACX,EAh8B+DlG,IAy8B3D8H,GAAmH7a,EAAoB,KACvI8a,GAAuI9a,EAAoBI,CAAC,CAACya,IAQ7JE,IACIvb,EAAgB,SAAUe,CAAC,CAC3BoM,CAAC,EAOD,MAAOnN,AANHA,CAAAA,EAAgBoB,OAAOgM,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAalH,OAAS,SAAUpF,CAAC,CAC1DoM,CAAC,EAAIpM,EAAEsM,SAAS,CAAGF,CAAG,GACd,SAAUpM,CAAC,CACnBoM,CAAC,EAAI,IAAK,IAAIG,KAAKH,EAAO/L,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACuL,EAC/DG,IAAIvM,CAAAA,CAAC,CAACuM,EAAE,CAAGH,CAAC,CAACG,EAAE,AAAD,CAAG,CAAA,EACIvM,EAAGoM,EAC5B,EACO,SAAUpM,CAAC,CAAEoM,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIsI,UAAU,uBAAyBC,OAAOvI,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACC,WAAW,CAAGzM,CAAG,CADtCf,EAAce,EAAGoM,GAEjBpM,EAAEW,SAAS,CAAGyL,AAAM,OAANA,EAAa/L,OAAOqM,MAAM,CAACN,GAAMI,CAAAA,EAAG7L,SAAS,CAAGyL,EAAEzL,SAAS,CAAE,IAAI6L,CAAG,CACtF,GAIAiO,GAAS,AAACF,KAA2HE,MAAM,CAG3IC,GAA2B,AAACzZ,IAA+E6T,MAAM,CAAE6F,GAAiB,AAAC1Z,IAA+E0Z,cAAc,CAAEC,GAAW,AAAC3Z,IAA+E2Z,QAAQ,CAAEC,GAAyB,AAAC5Z,IAA+EK,IAAI,CAU1b,SAASwZ,GAAgBjW,CAAC,CAAE8E,CAAC,CAAEoR,CAAC,CAAEC,CAAC,CAAEnZ,CAAO,EACxC,IAEIkV,EACAkE,EAHAC,EAAUrZ,GAAWA,EAAQqZ,OAAO,CACpCC,EAAUtZ,GAAWA,EAAQsZ,OAAO,CAGpCC,EAAUL,EAAI,EA4BlB,OA3BIH,GAASM,IAAYN,GAASO,KAC9BpE,EAAO,CAAC,CAAC,IAAKmE,EAASC,EAAQ,CAAC,CAEhCF,CAAAA,EAAUtR,EAAIwR,CAAM,EACN,GACVF,CAAAA,EAAU,CAACD,EAAIC,CAAM,EAErBA,EAAUF,GACVK,CAAAA,EAAUF,EAAUrW,EAAKkW,EAAI,EAAKE,EAAUF,EAAIE,CAAM,EAGtDE,EAAUxR,EAAIqR,EACdjE,EAAKhV,IAAI,CAAC,CAAC,IAAK8C,EAAIuW,EAASzR,EAAIqR,EAAE,EAG9BG,EAAUxR,EACfoN,EAAKhV,IAAI,CAAC,CAAC,IAAK8C,EAAIuW,EAASzR,EAAE,EAG1BuR,EAAUrW,EACfkS,EAAKhV,IAAI,CAAC,CAAC,IAAK8C,EAAG8E,EAAIqR,EAAI,EAAE,EAGxBE,EAAUrW,EAAIkW,GACnBhE,EAAKhV,IAAI,CAAC,CAAC,IAAK8C,EAAIkW,EAAGpR,EAAIqR,EAAI,EAAE,GAGlCjE,GAAQ,EAAE,AACrB,CAsBA,IAAIsE,GAAmC,SAAUvO,CAAM,EAOnD,SAASuO,EAAkB1Z,CAAU,CAAEE,CAAO,CAAEoD,CAAK,EACjD,OAAO6H,EAAOjM,IAAI,CAAC,IAAI,CAAEc,EAAYE,EAASoD,EAAO,UAAY,IAAI,AACzE,CA4SA,OApTAuV,GAA0Ba,EAAmBvO,GA0B7CuO,EAAkBC,eAAe,CAAG,SAAUC,CAAY,CAAE5K,CAAG,EAC3D,MAAO,CACH9L,EAAGU,KAAKiW,KAAK,CAAC,AAAC7K,CAAAA,EAAI9L,CAAC,EAAI,CAAA,EAAM0W,CAAAA,EAAa1W,CAAC,EAAI,CAAA,EAC5C,AAAC8L,CAAAA,EAAIvD,KAAK,CAAImO,CAAAA,EAAanO,KAAK,EAAI,CAAA,CAAC,EACjCuN,GAAeY,EAAaE,KAAK,GACzC9R,EAAGpE,KAAKiW,KAAK,CAAC,AAAC7K,CAAAA,EAAIhH,CAAC,EAAI,CAAA,EAAM4R,CAAAA,EAAa5R,CAAC,EAAI,CAAA,EAC5C,AAACgH,CAAAA,EAAItD,MAAM,CAAIkO,CAAAA,EAAalO,MAAM,EAAI,CAAA,CAAC,EACnCsN,GAAeY,EAAaG,aAAa,EACrD,CACJ,EACAL,EAAkBzU,OAAO,CAAG,SAAUwP,CAAgB,EAElDuF,AADcvF,EAAiBzV,SAAS,CAACgb,OAAO,CACxCC,SAAS,CAAGd,EACxB,EAOAO,EAAkBQ,gBAAgB,CAAG,SAAUzZ,CAAK,CAAEoC,CAAK,CAAE+W,CAAY,CAAEO,CAAS,EAChF,IAgBIC,EAhBAN,EAAQF,EAAaE,KAAK,CAC1BC,EAAgBH,EAAaG,aAAa,CAC1CM,EAAUxX,EAAMmM,GAAG,CAAG,EAAKnM,EAAMwX,OAAO,EAAI,EAC5CC,EAAOzX,EAAM0X,OAAO,GAEpBra,EAAU,CACN4Z,MAAOA,EACPC,cAAeA,EACf7W,EAAG0W,EAAa1W,CAAC,CACjB8E,EAAG4R,EAAa5R,CAAC,CACjByD,MAAO5I,EAAM4I,KAAK,CAClBC,OAAQ7I,EAAM6I,MAAM,AACxB,EAEAxI,EAAI,AAACiX,CAAAA,EAAUjX,CAAC,EAAI,CAAA,EAAKzC,EAAMgJ,QAAQ,CACvCzB,EAAI,AAACmS,CAAAA,EAAUnS,CAAC,EAAI,CAAA,EAAKvH,EAAMiJ,OAAO,CA0C1C,MAvCA0Q,CAAAA,EAAMlX,EAAImX,CAAM,EACN,IACFP,AAAU,UAAVA,EACA5Z,EAAQ4Z,KAAK,CAAG,OAGhB5Z,EAAQgD,CAAC,CAAG,AAAChD,CAAAA,EAAQgD,CAAC,EAAI,CAAA,EAAKkX,GAIvCA,CAAAA,EAAMlX,EAAIoX,EAAK7O,KAAK,CAAG4O,CAAM,EACnB5Z,EAAMuM,SAAS,GACjB8M,AAAU,SAAVA,EACA5Z,EAAQ4Z,KAAK,CAAG,QAGhB5Z,EAAQgD,CAAC,CAAG,AAAChD,CAAAA,EAAQgD,CAAC,EAAI,CAAA,EAAKzC,EAAMuM,SAAS,CAAGoN,GAIzDA,CAAAA,EAAMpS,EAAIqS,CAAM,EACN,IACFN,AAAkB,WAAlBA,EACA7Z,EAAQ6Z,aAAa,CAAG,MAGxB7Z,EAAQ8H,CAAC,CAAG,AAAC9H,CAAAA,EAAQ8H,CAAC,EAAI,CAAA,EAAKoS,GAIvCA,CAAAA,EAAMpS,EAAIsS,EAAK5O,MAAM,CAAG2O,CAAM,EACpB5Z,EAAMwM,UAAU,GAClB8M,AAAkB,QAAlBA,EACA7Z,EAAQ6Z,aAAa,CAAG,SAGxB7Z,EAAQ8H,CAAC,CAAG,AAAC9H,CAAAA,EAAQ8H,CAAC,EAAI,CAAA,EAAKvH,EAAMwM,UAAU,CAAGmN,GAGnDla,CACX,EAaAwZ,EAAkB1a,SAAS,CAACwR,cAAc,CAAG,SAAUxH,CAAE,CAAEF,CAAE,EACzDqC,EAAOnM,SAAS,CAACwR,cAAc,CAACtR,IAAI,CAAC,IAAI,CAAE8J,EAAIF,EAAI,EACvD,EAOA4Q,EAAkB1a,SAAS,CAAC6K,SAAS,CAAG,SAAUb,CAAE,CAAEF,CAAE,EACpD,IAAIrI,EAAQ,IAAI,CAACT,UAAU,CAACS,KAAK,CAE7BiC,EAAe,IAAI,CAAC1C,UAAU,CAACF,WAAW,CAE1CmS,EAAkBxR,EAAMN,WAAW,CAACsH,OAAO,CAAC,IAAI,CAACzH,UAAU,EAE3DkS,EAAesI,AADI/Z,EAAMP,OAAO,CAACC,WAAW,AACb,CAAC8R,EAAgB,CACpD,GAAIxR,EAAMyI,QAAQ,CAAE,CAChB,IAAIT,EAAOO,EACXA,EAAKF,EACLA,EAAKL,CACT,CAEA,IAAI,CAACvI,OAAO,CAACgD,CAAC,EAAI8F,EAClB,IAAI,CAAC9I,OAAO,CAAC8H,CAAC,EAAIc,EAElBoJ,CAAY,CAAC,IAAI,CAACnB,UAAU,CAAC,CAAC,IAAI,CAACzN,KAAK,CAAC,CAACJ,CAAC,CAAG,IAAI,CAAChD,OAAO,CAACgD,CAAC,CAC5DgP,CAAY,CAAC,IAAI,CAACnB,UAAU,CAAC,CAAC,IAAI,CAACzN,KAAK,CAAC,CAAC0E,CAAC,CAAG,IAAI,CAAC9H,OAAO,CAAC8H,CAAC,CAC5DtF,CAAY,CAAC,IAAI,CAACqO,UAAU,CAAC,CAAC,IAAI,CAACzN,KAAK,CAAC,CAACJ,CAAC,CAAG,IAAI,CAAChD,OAAO,CAACgD,CAAC,CAC5DR,CAAY,CAAC,IAAI,CAACqO,UAAU,CAAC,CAAC,IAAI,CAACzN,KAAK,CAAC,CAAC0E,CAAC,CAAG,IAAI,CAAC9H,OAAO,CAAC8H,CAAC,AAChE,EACA0R,EAAkB1a,SAAS,CAACuM,MAAM,CAAG,SAAU4J,CAAM,EACjD,IAAIjV,EAAU,IAAI,CAACA,OAAO,CACtBqR,EAAQ,IAAI,CAACJ,gBAAgB,CAACjR,GAC9ByL,EAAQzL,EAAQyL,KAAK,AACzB,CAAA,IAAI,CAACtL,OAAO,CAAG,IAAI,CAACL,UAAU,CAACS,KAAK,CAACE,QAAQ,CACxCkC,KAAK,CAAC,GAAI,EAAG,MAClB3C,EAAQ6J,KAAK,CAAE,KAAM,KAAM7J,EAAQ6G,OAAO,CAAE,KAAM,oBAC7CzG,IAAI,CAACiR,GACLrQ,GAAG,CAACiU,GACJ,IAAI,CAACnV,UAAU,CAACS,KAAK,CAACoH,UAAU,GACb,aAAhB8D,EAAMuI,KAAK,EACXvI,CAAAA,EAAMuI,KAAK,CAAG,IAAI,CAAClU,UAAU,CAACS,KAAK,CAACE,QAAQ,CAAC8Z,WAAW,CAACf,EAAkBgB,uBAAuB,CAACjT,OAAO,CAACvH,EAAQ6J,KAAK,EAAI,GAAK,UAAY7J,EAAQya,eAAe,CAAA,EAExK,IAAI,CAACta,OAAO,CACP6H,GAAG,CAAChI,EAAQyL,KAAK,EACjBiP,MAAM,CAAC1a,EAAQ0a,MAAM,GAE9B,IAAI,CAACva,OAAO,CAACwa,SAAS,CAAG3a,EAAQ2a,SAAS,CAC1C1P,EAAOnM,SAAS,CAACuM,MAAM,CAACrM,IAAI,CAAC,IAAI,CACrC,EACAwa,EAAkB1a,SAAS,CAACe,MAAM,CAAG,SAAUsL,CAAS,EACpD,IAAInL,EAAU,IAAI,CAACA,OAAO,CACtB4C,EAAO,IAAI,CAACA,IAAI,EAAI5C,EAAQ4Y,MAAM,EAAI5Y,EAAQ4C,IAAI,CAClDD,EAAQ,IAAI,CAACxC,OAAO,CACpBiM,EAAQ,IAAI,CAACtJ,MAAM,CAAC,EAAE,CAC1B,GAAI,CAACH,EAAO,CACR,IAAI,CAAC9C,MAAM,CAACsL,GACZ,MACJ,CACAxI,EAAMvC,IAAI,CAAC,CACPwC,KAAMA,EACFgW,GAAO9F,OAAOlQ,GAAOwJ,EAAO,IAAI,CAACtM,UAAU,CAACS,KAAK,EACjDP,EAAQ4a,SAAS,CAAC5b,IAAI,CAACoN,EAAO,IAAI,CAC1C,GACA,IAAImC,EAAS,IAAI,CAACA,MAAM,CAACnC,GACrBiF,EAAQ,IAAI,CAACwD,QAAQ,CAACtG,GACtB8C,GACA1O,EAAMsX,SAAS,CAAG5I,EAClBA,EAAMgI,OAAO,CAAG9K,EAAOW,gBAAgB,CAAClM,CAAC,CACzCqO,EAAMiI,OAAO,CAAG/K,EAAOW,gBAAgB,CAACpH,CAAC,CACzCnF,CAAK,CAACwI,EAAY,UAAY,OAAO,CAACkG,IAGtC1O,EAAMvC,IAAI,CAAC,CACP4C,EAAG,EACH8E,EAAG,KACP,GAEJnF,EAAM2S,MAAM,CAAG,CAAC,CAACjE,EACjBpG,EAAOnM,SAAS,CAACe,MAAM,CAACb,IAAI,CAAC,IAAI,CAAEmM,EACvC,EAMAqO,EAAkB1a,SAAS,CAACyP,MAAM,CAAG,SAErC4B,CAAM,EACF,IAAI5B,EAAStD,EAAOnM,SAAS,CAACyP,MAAM,CAAC3J,KAAK,CAAC,IAAI,CAC3CE,WACA9B,EAAI,IAAI,CAAChD,OAAO,CAACgD,CAAC,EAAI,EACtB8E,EAAI,IAAI,CAAC9H,OAAO,CAAC8H,CAAC,EAAI,EAK1B,OAJAyG,EAAOW,gBAAgB,CAAClM,CAAC,EAAIA,EAC7BuL,EAAOW,gBAAgB,CAACpH,CAAC,EAAIA,EAC7ByG,EAAOU,gBAAgB,CAACjM,CAAC,EAAIA,EAC7BuL,EAAOU,gBAAgB,CAACnH,CAAC,EAAIA,EACtByG,CACX,EAIAiL,EAAkB1a,SAAS,CAAC+V,QAAQ,CAAG,SAAUtG,CAAM,EACnD,IAOIsM,EACAC,EACAC,EACAC,EAVAzF,EAAO,IAAI,CAACpV,OAAO,CACnBI,EAAQ,IAAI,CAACT,UAAU,CAACS,KAAK,CAC7BwO,EAAUxO,EAAMwO,OAAO,CACvB3C,EAAQ,IAAI,CAACtJ,MAAM,CAAC,EAAE,CACtB0S,EAAc,IAAI,CAACxV,OAAO,CAC1Bib,EAAyB1M,EAAOW,gBAAgB,CAChDgM,EAAyB3M,EAAOU,gBAAgB,CAKhDkM,EAAW/O,EAAMlJ,MAAM,CAACyI,OAAO,EAC3B0E,AAp3D0CnE,GAo3DpBpN,SAAS,CAACwK,YAAY,CAACtK,IAAI,CAACoN,GAC1D,GAAImJ,GAAQ4F,EAAU,CAClB,IAAIlU,EAAKsO,EAAKhK,KAAK,CACfA,EAAQtE,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EAC5BmU,EAAK7F,EAAK/J,MAAM,CAChBA,EAAS4P,AAAO,KAAK,IAAZA,EAAgB,EAAIA,CAC7B5F,CAAAA,EAAY6F,QAAQ,EAAItM,EACxB8L,EAAe9L,EAAQuM,WAAW,CAACtc,IAAI,CAAC,CACpCuB,MAAOA,EACP8a,SAAUrC,GAAuBxD,EAAY6F,QAAQ,CAAE,IACvDE,gBAAiBxM,EAAQwM,eAAe,CACxCpU,QAAS4H,EAAQ5H,OAAO,AAC5B,EAAGoE,EAAOC,EAAQ,CACdoB,MAAOsO,EAAuBlY,CAAC,CAC/B6J,MAAOqO,EAAuBpT,CAAC,CAC/B0T,SAAUpP,EAAMoP,QAAQ,CACxBC,QAASrP,EAAMqP,OAAO,CACtBtC,EAAI+B,EAAuB1P,MAAM,EAC7B0P,EAAuB3P,KAAK,AACpC,GAEKiK,EAAYpK,UAAU,CAC3ByP,EAAerF,EAAYpK,UAAU,CAACpM,IAAI,CAAC,IAAI,GAG/C8b,EAAU,CACN9X,EAAGiY,EAAuBjY,CAAC,CAC3B8E,EAAGmT,EAAuBnT,CAAC,CAC3ByD,MAAO,EACPC,OAAQ,CACZ,EACAqP,EAAerB,EAAkBC,eAAe,CAACZ,GAAyBrD,EAAa,CACnFjK,MAAOA,EACPC,OAAQA,CACZ,GAAIsP,GAC0B,YAA1B,IAAI,CAAC9a,OAAO,CAAC0b,QAAQ,EACrBb,CAAAA,EAAerB,EAAkBC,eAAe,CAACD,EAAkBQ,gBAAgB,CAACzZ,EAAOgV,EAAMC,EAAaqF,GAAeC,EAAO,GAGxItF,EAAYmG,IAAI,GAChBZ,EAAmBF,EAAa7X,CAAC,CAAGzC,EAAMgJ,QAAQ,CAClDyR,EAAmBH,EAAa/S,CAAC,CAAGvH,EAAMiJ,OAAO,CACjD2R,EACI5a,EAAM+I,YAAY,CAACyR,EAAkBC,IACjCza,EAAM+I,YAAY,CAACyR,EAAmBxP,EAAOyP,EAAmBxP,GAEhF,CACA,OAAO2P,EAAWN,EAAe,IACrC,EAWArB,EAAkBpI,QAAQ,CAAG,CACzBqJ,gBAAiB,OACjBmB,YAAa,SACbC,YAAa,eACb/a,OAAQ,SACRgb,aAAc,IACd3B,QAAS,SACb,EAOAX,EAAkBgB,uBAAuB,CAAG,CAAC,YAAY,CAClDhB,CACX,EAl2C+D7I,IAi3C3DoL,IACI3e,EAAgB,SAAUe,CAAC,CAC3BoM,CAAC,EAOD,MAAOnN,AANHA,CAAAA,EAAgBoB,OAAOgM,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAalH,OAAS,SAAUpF,CAAC,CAC1DoM,CAAC,EAAIpM,EAAEsM,SAAS,CAAGF,CAAG,GACd,SAAUpM,CAAC,CACnBoM,CAAC,EAAI,IAAK,IAAIG,KAAKH,EAAO/L,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACuL,EAC/DG,IAAIvM,CAAAA,CAAC,CAACuM,EAAE,CAAGH,CAAC,CAACG,EAAE,AAAD,CAAG,CAAA,EACIvM,EAAGoM,EAC5B,EACO,SAAUpM,CAAC,CAAEoM,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIsI,UAAU,uBAAyBC,OAAOvI,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACC,WAAW,CAAGzM,CAAG,CADtCf,EAAce,EAAGoM,GAEjBpM,EAAEW,SAAS,CAAGyL,AAAM,OAANA,EAAa/L,OAAOqM,MAAM,CAACN,GAAMI,CAAAA,EAAG7L,SAAS,CAAGyL,EAAEzL,SAAS,CAAE,IAAI6L,CAAG,CACtF,GA2BAqR,GAAmC,SAAU/Q,CAAM,EAOnD,SAAS+Q,EAAkBlc,CAAU,CAAEE,CAAO,CAAEoD,CAAK,EACjD,IAAI8H,EAAQD,EAAOjM,IAAI,CAAC,IAAI,CACxBc,EACAE,EACAoD,EAAO,UAAY,IAAI,CAQ3B,OAFA8H,EAAMvF,IAAI,CAAG,QACbuF,EAAMvB,SAAS,CAAGsB,EAAOnM,SAAS,CAAC8S,cAAc,CAC1C1G,CACX,CAiDA,OApEA6Q,GAA0BC,EAAmB/Q,GAoB7C+Q,EAAkBld,SAAS,CAACuM,MAAM,CAAG,SAAU4J,CAAM,EACjD,IAAI5D,EAAQ,IAAI,CAACJ,gBAAgB,CAAC,IAAI,CAACjR,OAAO,EAC1CA,EAAU,IAAI,CAACA,OAAO,AAC1B,CAAA,IAAI,CAACG,OAAO,CAAG,IAAI,CAACL,UAAU,CAACS,KAAK,CAACE,QAAQ,CACxCwb,KAAK,CAACjc,EAAQkc,GAAG,CAAE,EAAG,KAAMlc,EAAQuL,KAAK,CAAEvL,EAAQwL,MAAM,EACzDpL,IAAI,CAACiR,GACLrQ,GAAG,CAACiU,GACT,IAAI,CAAC9U,OAAO,CAACoL,KAAK,CAAGvL,EAAQuL,KAAK,CAClC,IAAI,CAACpL,OAAO,CAACqL,MAAM,CAAGxL,EAAQwL,MAAM,CACpCP,EAAOnM,SAAS,CAACuM,MAAM,CAACrM,IAAI,CAAC,IAAI,CACrC,EACAgd,EAAkBld,SAAS,CAACe,MAAM,CAAG,SAAUsL,CAAS,EACpD,GAAI,IAAI,CAAChL,OAAO,CAAE,CACd,IAAIoO,EAAS,IAAI,CAACA,MAAM,CAAC,IAAI,CAACzL,MAAM,CAAC,EAAE,EACnC+R,EAAWsH,AAxFyC3C,GAwFT1a,SAAS,CAAC+V,QAAQ,CAAC7V,IAAI,CAAC,IAAI,CACvEuP,GACAsG,EACA,IAAI,CAAC1U,OAAO,CAACgL,EAAY,UAAY,OAAO,CAAC,CACzCnI,EAAG6R,EAAS7R,CAAC,CACb8E,EAAG+M,EAAS/M,CAAC,AACjB,GAGA,IAAI,CAAC3H,OAAO,CAACC,IAAI,CAAC,CACd4C,EAAG,EACH8E,EAAG,IACP,GAEJ,IAAI,CAAC3H,OAAO,CAACmV,MAAM,CAAGY,CAAAA,CAAQrB,CAClC,CACA5J,EAAOnM,SAAS,CAACe,MAAM,CAACb,IAAI,CAAC,IAAI,CAAEmM,EACvC,EAYA6Q,EAAkB5K,QAAQ,CAAG,CACzB7F,MAAO,QACPC,OAAQ,SACR1K,OAAQ,QACZ,EACOkb,CACX,EAn+C+DrL,IA4+C3DyL,GAAuFxe,EAAoB,KAC3Gye,GAA2Gze,EAAoBI,CAAC,CAACoe,IAmBjIE,GAAoB,AAACld,IAA+EC,QAAQ,CAAEyX,GAAgB,AAAC1X,IAA+E0X,aAAa,CAM3NyF,GAA0B,WAM1B,SAASA,EAASC,CAAS,CAAEC,CAAQ,EACjC,IAAI,CAACA,QAAQ,CAAGA,EAChB,IAAI,CAACC,SAAS,CAAG,IAAI,CAACC,oBAAoB,CAACH,GAC3C,IAAI,CAACI,WAAW,CAAG,IAAI,CAACC,cAAc,EAC1C,CA0FA,OAxEAN,EAASzd,SAAS,CAAC6d,oBAAoB,CAAG,SAAUH,CAAS,CAAEhL,CAAS,EAEpE,OADkB,KAAK,IAAnBA,GAAwBA,CAAAA,EAAY,wCAAuC,EACxEsF,GAAc,MAAO,CAAEtF,UAAWA,CAAU,EAAG,KAAK,EAAGgL,EAClE,EAUAD,EAASzd,SAAS,CAAC+d,cAAc,CAAG,SAAUrL,CAAS,EACjC,KAAK,IAAnBA,GAAwBA,CAAAA,EAAY,wBAAuB,EAC/D,IAAIsL,EAAQ,IAAI,CACZL,EAAW,IAAI,CAACA,QAAQ,CAExBG,EAAc9F,GAAc,SAAU,CAAEtF,UAAWA,CAAU,EAC7D,KAAK,EACL,IAAI,CAACkL,SAAS,EAalB,OAZAE,EAAYnR,KAAK,CAAC,mBAAmB,CAAG,OACnCgR,CAAAA,EAASM,KAAK,CAAC,0BACZN,EAAWA,EAAW,WAAU,EAAK,IAC7C,CAAC,QAAS,aAAa,CAACxb,OAAO,CAAC,SAAU+b,CAAS,EAC/CV,GAAkBM,EAAaI,EAAWF,EAAMG,iBAAiB,CAACC,IAAI,CAACJ,GAC3E,GAEAR,GAAkBa,SAAU,UAAW,SAAU5b,CAAK,EAC/B,WAAfA,EAAM6b,IAAI,EACVN,EAAMG,iBAAiB,EAE/B,GACOL,CACX,EAKAL,EAASzd,SAAS,CAACme,iBAAiB,CAAG,WACnC,IAAI,CAACI,UAAU,EACnB,EAOAd,EAASzd,SAAS,CAACwe,SAAS,CAAG,SAAUC,CAAY,EAC5B,KAAK,IAAtBA,GAA2BA,CAAAA,EAAe,+BAA8B,EAC5E,IAAIC,EAAW,IAAI,CAACd,SAAS,CACzBe,EAAmB,IAAI,CAACb,WAAW,AACvC,CAAA,IAAI,CAACjX,IAAI,CAAG,KAAK,EAEjB6X,EAASE,SAAS,CAAG,AAACrB,KAA+FsB,SAAS,CAE1HH,EAAShM,SAAS,CAACjK,OAAO,CAACgW,IAAiB,IAC5CC,EAASI,SAAS,CAACC,MAAM,CAACN,GAE1BC,EAASM,eAAe,CAAC,UAG7BN,EAASO,WAAW,CAACN,GACrBD,EAAS/R,KAAK,CAACuS,OAAO,CAAG,QACzBR,EAAS/R,KAAK,CAACD,MAAM,CAAG,EAC5B,EAIA+Q,EAASzd,SAAS,CAACue,UAAU,CAAG,WAC5B,IAAI,CAACX,SAAS,CAACjR,KAAK,CAACuS,OAAO,CAAG,MACnC,EACOzB,CACX,IAsBI0B,GAAuB,AAAC7e,IAA+E0G,GAAG,CAAEoY,GAAY,AAAC9e,IAA+E8e,SAAS,CAEjNC,GAAiC,AAAC/e,IAA+E0X,aAAa,CAAEsH,GAAU,AAAChf,IAA+Egf,OAAO,CAAE1O,GAAW,AAACtQ,IAA+EsQ,QAAQ,CAAE2O,GAA8B,AAACjf,IAA+E8G,UAAU,CAAEoY,GAAwB,AAAClf,IAA+EK,IAAI,CAAE8e,GAAa,AAACnf,IAA+Emf,UAAU,CA4G3pB,SAASC,GAAchC,CAAS,CAAEjc,CAAK,CAAEke,CAAU,CAAEze,CAAO,CAAE0e,CAAO,CAAEC,CAAM,EACzE,IAMIC,EACAC,EAPA3T,EAAQ,IAAI,CAChB,GAAK3K,GAGL,IAAIue,EAAW,IAAI,CAACA,QAAQ,CACxB9c,EAAO,IAAI,CAACA,IAAI,CAGpBqc,GAA4Bre,EAAS,SAAU0T,CAAK,CAAEqL,CAAM,EAExDH,EAAiBH,AAAe,KAAfA,EAAoBA,EAAa,IAAMM,EAASA,EAC7DrP,GAASgE,KAGT,CAAC0K,GAAQ1K,IAEJ0K,GAAQ1K,IAAUhE,GAASgE,CAAK,CAAC,EAAE,GAE/BmL,AADLA,CAAAA,EAAY7c,CAAI,CAAC+c,EAAO,EAAIA,CAAK,EAClBhC,KAAK,CAAC,QACjB2B,EAAQxe,IAAI,CAAC,CACT,CAAA,EACA2e,EACArC,EACH,EAELgC,GAAcxf,IAAI,CAACkM,EAAOsR,EAAWjc,EAAOqe,EAAgBlL,EAAOgL,EAAS,CAAA,IAG5EA,EAAQxe,IAAI,CAAC,CACTgL,EACA0T,EACA,aACApC,EACA9I,EACH,EAGb,GACIiL,IACAJ,GAAWG,EAAS,SAAUtgB,CAAC,EAAI,OAAQA,CAAC,CAAC,EAAE,CAAC2e,KAAK,CAAC,WAAa,GAAK,CAAI,GACxEmB,IACAQ,EAAQM,OAAO,GAEnBN,EAAQzd,OAAO,CAAC,SAAUge,CAAQ,EAC1BA,AAAgB,CAAA,IAAhBA,CAAQ,CAAC,EAAE,CACXd,GAA+B,OAAQ,CACnC3M,UAAW,6BACf,EAAG,KAAK,EAAGyN,CAAQ,CAAC,EAAE,EAAElB,WAAW,CAACE,GAAqBiB,cAAc,CAACD,CAAQ,CAAC,EAAE,IAGnFA,CAAQ,CAAC,EAAE,CAAG,CACVvL,MAAOuL,CAAQ,CAAC,EAAE,CAAC,EAAE,CACrBtZ,KAAMsZ,CAAQ,CAAC,EAAE,CAAC,EAAE,AACxB,EACAH,EAASla,KAAK,CAACqa,CAAQ,CAAC,EAAE,CAAEA,EAASE,MAAM,CAAC,IAEpD,IAER,CA2BA,IAAIC,GAAsB,AAAChgB,IAA+E0G,GAAG,CAEzGuZ,GAAc,AAACvT,IAA2IuT,WAAW,CAErKC,GAA2B,AAAClgB,IAA+EC,QAAQ,CAAEkgB,GAAgC,AAACngB,IAA+E0X,aAAa,CAAE0I,GAA0B,AAACpgB,IAA+EyG,OAAO,CAAE4Z,GAA0B,AAACrgB,IAA+Egf,OAAO,CAAEsB,GAA2B,AAACtgB,IAA+EsQ,QAAQ,CAAEiQ,GAA6B,AAACvgB,IAA+E8G,UAAU,CAAE0Z,GAA6B,AAACxgB,IAA+Emf,UAAU,AAY/zBlhB,EADOA,EAGRA,GAAuBA,CAAAA,EAAqB,CAAC,CAAA,EAF1B,CAACA,CAAkB,CAAC,mBAAmB,CAAG,EAAE,CAAG,mBACjEA,CAAkB,CAACA,CAAkB,CAAC,iBAAiB,CAAG,EAAE,CAAG,iBAMnE,IAAIwiB,GAAqB,CACrB,wBAAyB,CAAC,WAAY,YAAa,YAAY,CAC/D,yBAA0B,CAAC,MAAO,MAAO,OAAQ,OAAQ,MAAM,AACnE,EAcA,SAASC,GAAiBpD,CAAS,EAE/B,IAAIqD,EAASR,GAA8B,MAAO,CAC1C/N,UAAW,0BACf,EACA,KAAK,EACLkL,GAEAsD,EAAST,GAA8B,MAAO,CAC1C/N,UAAW,0BACf,EACA,KAAK,EACLkL,GAKJ,OAHA6C,GAA8B,MAAO,CACjC/N,UAAW,kCACf,EAAG,KAAK,EAAGwO,GACJ,CACHD,OAAQA,EACRC,OAAQA,CACZ,CACJ,CAgDA,SAASC,GAA8B1f,CAAK,CAAE2C,CAAM,CAAEgd,CAAU,CAAEC,CAAa,EAC3E,IAAIC,EAASld,EAAOmd,MAAM,EAAInd,EAAOlD,OAAO,CAACqgB,MAAM,AAEnDF,CAAAA,EAAczC,SAAS,CAAG,AAACrB,KAA+FsB,SAAS,CAEnI4B,GAA8B,KAAM,CAChC/N,UAAW,4BACf,EAAG,KAAK,EAAG2O,GAAepC,WAAW,CAACqB,GAAoBF,cAAc,CAACoB,GAAYpd,EAAQgd,GAAYK,iBAAiB,GAE1HhB,GAA8B,QAAS,CACnC5Z,KAAM,SACN6a,KAAM,mBAAqBN,EAC3BxM,MAAOwM,CACX,EAAG,KAAK,EAAGC,GAEXM,GAAczhB,IAAI,CAAC,IAAI,CAAEkhB,EAAY,SAAU3f,EAAO4f,EAAejd,EAAQA,EAAOwd,YAAY,EAAIxd,EAAOwd,YAAY,CAAC1gB,OAAO,CAACuE,EAAE,EAC9H6b,EAAOO,cAAc,EACrBF,GAAczhB,IAAI,CAAC,IAAI,CAAEkhB,EAAY,SAAU3f,EAAO4f,EAAejd,EAAQA,EAAOwd,YAAY,EAAIN,EAAOO,cAAc,EAG7HC,GAAe5hB,IAAI,CAAC,IAAI,CAAEuB,EAAO,SAAU6f,EAAQF,EAAYC,EACnE,CAwBA,SAASU,GAAiBtgB,CAAK,CAAEic,CAAS,CAAEsE,CAAQ,CAAEC,CAAM,EAIxD,SAASC,EAAgB9d,CAAM,CAAE+d,CAAa,EAC1C,IAAIlX,EAASoW,EAAc1B,UAAU,CAC5B9L,QAAQ,CAAC,EAAE,CACpBsN,GAA8BjhB,IAAI,CAAC8d,EAAOvc,EAAO2C,EAAQ+d,EAAed,GACpEpW,GACAA,CAAAA,EAAO0B,KAAK,CAACuS,OAAO,CAAG,OAAM,EAG7BkD,GAAUhe,EAAOlD,OAAO,EACxBuf,GAA8B,QAAS,CACnC5Z,KAAM,SACN6a,KAAM,iBAAmBS,EACzBvN,MAAOxQ,EAAOlD,OAAO,CAACuE,EAAE,AAC5B,EAAG,KAAK,EAAG4b,GAAegB,YAAY,CAAC,4BAA6Bje,EAAOlD,OAAO,CAACuE,EAAE,CAE7F,CACA,IAAIuY,EAAQ,IAAI,CAAE9a,EAAO8a,EAAM9a,IAAI,CAAE+d,EAASvD,EAAU4E,gBAAgB,CAAC,4BAA4B,CAAC,EAAE,CAAEpB,EAASxD,EAAU4E,gBAAgB,CAAC,4BAA4B,CAAC,EAAE,CAAEF,EAASJ,AAAa,SAAbA,EAAqB5d,EAAUge,EAC/M3gB,EAAM2C,MAAM,CACZ3C,EAAMP,OAAO,CAACqhB,WAAW,EAAI,CAAC,EAEtC,GAAI,AAAC9gB,IAAS2C,GAGd,IAAIqS,EACA+L,EAAsB,EAAE,AAExB,CAACJ,GAAWzB,GAAwBvc,GAI/Buc,GAAwBvc,IAC7Boe,CAAAA,EAAsBC,GAAkBviB,IAAI,CAAC,IAAI,CAAEkE,EAAM,EAHzDoe,EAAsBE,GAAaxiB,IAAI,CAAC,IAAI,CAAEkE,EAAQ6d,GAM1DnB,GAA2B0B,EAAqB,SAAUljB,CAAC,CAAEmM,CAAC,EAC1D,IAAIkX,EAAcrjB,EAAEmiB,iBAAiB,CAACmB,WAAW,GAC7CC,EAAcpX,EAAEgW,iBAAiB,CAACmB,WAAW,GACjD,OAAO,AAACD,EAAcE,EAClB,GAAK,CAACF,CAAAA,EAAcE,CAAU,CACtC,GAGI5B,EAAOpN,QAAQ,CAAC,EAAE,EAClBoN,EAAOpN,QAAQ,CAAC,EAAE,CAACkL,MAAM,GAG7B,IAAI+D,EAAgBrC,GAA8B,KAAM,CAChD/N,UAAW,2BACf,EACA,KAAK,EACLuO,GACAI,EAAgBH,EAAOoB,gBAAgB,CAAC,oCAAoC,CAAC,EAAE,CAqBnF,GApBAE,EAAoBrgB,OAAO,CAAC,SAAU4gB,CAAS,EAC3C,IAAItB,EAAoBsB,EAAUtB,iBAAiB,CAC/CU,EAAgBY,EAAUZ,aAAa,CACvC/d,EAAS2e,EAAU3e,MAAM,CAC7BqS,EAAOgK,GAA8B,KAAM,CACvC/N,UAAW,2BACf,EAAG,KAAK,EAAGoQ,GACX,IAAIE,EAAMvC,GAA8B,SAAU,CAC1C/N,UAAW,iCACXuQ,YAAaxB,CACjB,EACA,KAAK,EACLhL,GACJ,CAAC,QAAS,aAAa,CAACtU,OAAO,CAAC,SAAU+b,CAAS,EAC/CsC,GAAyBwC,EAAK9E,EAAW,WACrCgE,EAAgB9d,EAAQ+d,EAC5B,EACJ,EACJ,GAEIK,EAAoBnf,MAAM,CAAG,EAAG,CAChC,IAAI8E,EAAKqa,CAAmB,CAAC,EAAE,CAG/BN,EAFe/Z,EAAG/D,MAAM,CACJ+D,EAAGga,aAAa,CAExC,MACUC,IACN7E,KAA8F2F,cAAc,CAAC7B,EAAc1B,UAAU,CAAC9L,QAAQ,CAAC,EAAE,CAAE3Q,EAAKigB,aAAa,EAAI,IACzK9B,EAAc1B,UAAU,CAAC9L,QAAQ,CAAC,EAAE,CAC/BlH,KAAK,CAACuS,OAAO,CAAG,QAE7B,CAiBA,SAAS4C,GAAergB,CAAK,CAAEke,CAAU,CAAE2B,CAAM,CAAEza,CAAI,CAAE6W,CAAS,EAC9D,IAAItR,EAAQ,IAAI,CAChB,GAAK3K,GAGL,IAAIue,EAAW,IAAI,CAACA,QAAQ,CAC5Ba,GAA2BS,EAAQ,SAAU1M,CAAK,CAAEwO,CAAS,EAEzD,IAAItD,EAAiBH,EAAa,IAAMyD,EACxC,GAAI1C,GAAwB9L,IACxBkL,GASA,GARIc,GAAyBhM,KAGzBoL,EAAS9f,IAAI,CAACkM,EAAO0T,EAAgBjZ,EAAM6W,EAAW,CAAC,GACvDoE,GAAe5hB,IAAI,CAACkM,EAAO3K,EAAOqe,EAAgBlL,EAAO/N,EAAM6W,IAI/DoC,KAAkBvhB,EAAoB,CAEtC,IAAI8kB,EAAYC,GAAapjB,IAAI,CAACkM,EAC9BvF,EACAiZ,EACApC,GAEJ6F,GAAoBrjB,IAAI,CAACkM,EAAO3K,EAAOke,EAAY0D,EAAWxc,EAAMuc,EAAWxO,EACnF,KAGmB,0BAAnBkL,GACKa,GAAwB/L,IAEzBoL,EAAS9f,IAAI,CAACkM,EAAO0T,EAAgBjZ,EAAM6W,EAAW,CAClD9I,MAAOA,EACP/N,KAAM,QACV,GAIZ,GACJ,CAYA,SAAS2c,GAAa/hB,CAAK,CAAEic,CAAS,EAClC,IAAIM,EAAQ,IAAI,CAAEiD,EAASvD,EAAU4E,gBAAgB,CAAC,4BAA4B,CAAC,EAAE,CAK9EmB,EAAkB,IAAI,CAACvgB,IAAI,CAACwgB,WAAW,CAAEC,EAAelD,GAA8B,MAAO,CAC5F/N,UAAW,0BACf,EAAG,KAAK,EAAGuO,GACX2C,EAAoB,SAAUC,CAAS,EAEnC9B,GAAiB7hB,IAAI,CAAC8d,EAC1Bvc,EACAuc,EAAMJ,SAAS,CAAE,MACjBiG,EACJ,EAEIC,EAAQ,IAAI,CAAC9D,QAAQ,CAhBwE,mBAgB9D,QAAS2D,EAhB2F,CAC/H/O,MAAO,GACP/N,KAAM,OACNkd,QAAS,oBACTC,eAAgB,0CACpB,GAWwE/Y,EAASwV,GAA8B,IAAK,CAChHwC,YAAaQ,CACjB,EAAG,KAAK,EAAGE,GACfG,EAAMhF,SAAS,CAAC5c,GAAG,CAAC,sCACpB+I,EAAO6T,SAAS,CAAC5c,GAAG,CAAC,uBAErBse,GAAyBsD,EAAO,QAAS,WACrCF,EAAkB,IAAI,CAAChP,KAAK,EAExB,IAAI,CAACA,KAAK,CAACvR,MAAM,CACjB4H,EAAO0B,KAAK,CAACuS,OAAO,CAAG,eAGvBjU,EAAO0B,KAAK,CAACuS,OAAO,CAAG,MAE/B,GAEA,CAAC,QAAS,aAAa,CAAC/c,OAAO,CAAC,SAAU+b,CAAS,EAC/CsC,GAAyBvV,EAAQiT,EAAW,WAExC4F,EAAMlP,KAAK,CAAG,GACdgP,EAAkB,IAElB3Y,EAAO0B,KAAK,CAACuS,OAAO,CAAG,MAC3B,EACJ,EACJ,CAeA,SAASoE,GAAanB,CAAa,CAAE8B,CAAU,CAAEvG,CAAS,EACtD,IAAIwG,EAAkBD,EAAWE,KAAK,CAAC,KAAMC,EAAYF,CAAe,CAACA,EAAgB7gB,MAAM,CAAG,EAAE,CAAEghB,EAAa,cAAgBJ,EAAa,SAAW9B,EAAejf,EAAO,IAAI,CAACA,IAAI,CAE1Lud,GAA8B,QAAS,CACnCsD,QAASM,CACb,EAAG,KAAM3G,GAAWuB,WAAW,CAACqB,GAAoBF,cAAc,CAACld,CAAI,CAACkhB,EAAU,EAAIH,IAEtF,IAAIZ,EAAY5C,GAA8B,SAAU,CAChDiB,KAAM2C,EACN3R,UAAW,yBACXjN,GAAI,qBAAuBwe,CAC/B,EACA,KACAvG,GAEJ,OADA2F,EAAUhB,YAAY,CAAC,KAAM,qBAAuB4B,GAC7CZ,CACX,CAwBA,SAASE,GAAoB9hB,CAAK,CAAEwiB,CAAU,CAAEZ,CAAS,CAAElB,CAAa,CAAEmC,CAAa,CAAEC,CAAc,CAAEC,CAAa,EAE9GP,AAAe,WAAfA,GAA2BA,AAAe,WAAfA,EAE3BxiB,EAAM2C,MAAM,CAACjC,OAAO,CAAC,SAAUiC,CAAM,EACjC,IAAIqgB,EAAgBrgB,EAAOlD,OAAO,CAC9BwjB,EAAaD,EAAc/C,IAAI,EAC3B+C,EAAclD,MAAM,CACpBnd,EAAOsd,IAAI,CACX+C,EAAchf,EAAE,EAAI,EACH,CAAA,gCAArBgf,EAAchf,EAAE,EAChBgf,EAAchf,EAAE,GAAM+e,CAAAA,GAClBA,EAActjB,OAAO,EACrBsjB,EAActjB,OAAO,CAACuE,EAAE,AAAD,IACtBib,GAAwB6D,IACzBN,AAAe,WAAfA,GACA7f,AAAgB,WAAhBA,EAAOyC,IAAI,EACX0d,CAAAA,EAAiBE,EAAchf,EAAE,AAAD,EAEpCgb,GAA8B,SAAU,CACpC7L,MAAO6P,EAAchf,EAAE,AAC3B,EAAG,KAAK,EAAG4d,GAAWpE,WAAW,CAACqB,GAAoBF,cAAc,CAACsE,IAE7E,GAEKvC,GAAiBmC,GAItBK,AADsB5D,EAAkB,CADtBuD,EAAgB,IAAMnC,EACa,CACrChgB,OAAO,CAAC,SAAUwF,CAAO,EACrC8Y,GAA8B,SAAU,CACpC7L,MAAOjN,CACX,EAAG,KAAK,EAAG0b,GAAWpE,WAAW,CAACqB,GAAoBF,cAAc,CAACzY,GACzE,GAGA+Y,GAAwB6D,IACxBlB,CAAAA,EAAUzO,KAAK,CAAG2P,CAAa,CAEvC,CAiBA,SAAS7B,GAAate,CAAM,CAAE6d,CAAM,EAChC,IAOI2C,EANA1hB,EAAO8a,AADC,IAAI,CACCvc,KAAK,EAAIuc,AADd,IAAI,CACgBvc,KAAK,CAACP,OAAO,CAACgC,IAAI,CAC9C2hB,EAAmB3hB,GACfA,EAAK4hB,UAAU,EACf5hB,EAAK4hB,UAAU,CAAC9G,KAAK,EACrB9a,EAAK4hB,UAAU,CAAC9G,KAAK,CAAC6G,gBAAgB,CAC1CrC,EAAsB,EAAE,CAsC5B,OApCA3B,GAA2Bzc,EAAQ,SAAUA,CAAM,CAAEwQ,CAAK,EACtD,IAAI6P,EAAgBrgB,GAAUA,EAAOlD,OAAO,CAE5C,GAAIkD,EAAOmd,MAAM,EAAIkD,GACjBA,EAAclD,MAAM,CAAE,CACtB,IAAIpZ,EAAKqZ,GAAYpd,EACjBwQ,GACA6M,EAAoBtZ,EAAGsZ,iBAAiB,CACxCU,EAAgBha,EAAGga,aAAa,CACpC,GAAIF,EAAQ,CAGR,IAAI8C,EAAQ,AAAIC,OADE/C,EAAOgD,OAAO,CAAC,sBAAuB,QACpB,KAChCC,EAAQL,GACJA,CAAgB,CAAC1C,EAAc,EAC/B0C,CAAgB,CAAC1C,EAAc,CAAC1e,IAAI,CAAC,MAAQ,GACjDge,CAAAA,EAAkBxD,KAAK,CAAC8G,IACxBG,EAAMjH,KAAK,CAAC8G,EAAK,IACjBH,EAAiB,CACbnD,kBAAmBA,EACnBU,cAAeA,EACf/d,OAAQA,CACZ,EACAoe,EAAoBphB,IAAI,CAACwjB,GAEjC,MAEIA,EAAiB,CACbnD,kBAAmBA,EACnBU,cAAeA,EACf/d,OAAQA,CACZ,EACAoe,EAAoBphB,IAAI,CAACwjB,EAEjC,CACJ,GACOpC,CACX,CAYA,SAASC,GAAkBre,CAAM,EAC7B,IAAIoe,EAAsB,EAAE,CAW5B,OATApe,EAAOjC,OAAO,CAAC,SAAUiC,CAAM,EACvBA,EAAO+gB,EAAE,CAAC,QACV3C,EAAoBphB,IAAI,CAAC,CACrBqgB,kBAAmBrd,EAAOsd,IAAI,CAC9BS,cAAe/d,EAAOyC,IAAI,CAC1BzC,OAAQA,CACZ,EAER,GACOoe,CACX,CA+BA,SAAShB,GAAYpd,CAAM,CAAE+d,CAAa,EACtC,IAAIjhB,EAAUkD,EAAOlD,OAAO,CAExBwjB,EAAa,AAACnE,EAAW,CAAC4B,EAAc,EACpC5B,EAAW,CAAC4B,EAAc,CAACniB,SAAS,CAAColB,QAAQ,EAC7CjD,EAAckD,WAAW,GAC7BjE,EAAae,EAMjB,OAJIjhB,GAAWA,EAAQ2F,IAAI,GACvBua,EAAahd,EAAOlD,OAAO,CAAC2F,IAAI,CAChC6d,EAAatgB,EAAOsd,IAAI,EAErB,CACHD,kBAAmBiD,EACnBvC,cAAef,CACnB,CACJ,CAsBA,SAASO,GAAcQ,CAAa,CAAE8B,CAAU,CAAExiB,CAAK,CAAEic,CAAS,CAAE8G,CAAa,CAAED,CAAc,EAG7F,GAAK9iB,GAIL,IAAI4hB,EAAYC,GAAapjB,IAAI,CANrB,IAAI,CAOZiiB,EACA8B,EACAvG,GAEJ6F,GAAoBrjB,IAAI,CAXZ,IAAI,CAWgBuB,EAAOwiB,EAAYZ,EAAW,KAAK,EAAG,KAAK,EAAG,KAAK,EAAGmB,GAElF9D,GAAwB6D,IACxBlB,CAAAA,EAAUzO,KAAK,CAAG2P,CAAa,EAEvC,CA0BA,IAAIe,GAAgB,AAAChlB,IAA+E0G,GAAG,CAEnGue,GAAqB,AAACjlB,IAA+EC,QAAQ,CAAEilB,GAA0B,AAACllB,IAA+E0X,aAAa,CAW1O,SAASyN,KAEL,OAAOD,GAAwB,MAAO,CAElC9S,UAAW,sDACf,EAAG,KAAK,EAJO,IAAI,CAACkL,SAAS,CAKjC,CAWA,SAAS8H,GAAYC,CAAO,CAAEC,CAAU,EACpC,IAAIlH,EAAW,IAAI,CAACd,SAAS,CACzB1a,EAAO,IAAI,CAACA,IAAI,CAChBwP,EAAY,qBACG,CAAA,IAAfkT,GACAlT,CAAAA,GAAa,0BAAyB,EAG1C,IAAImT,EAAWL,GAAwB,SAAU,CACzC9S,UAAWA,CACf,EACA,KAAK,EACLgM,GAGJ,OAFAmH,EAAS5G,WAAW,CAACqG,GAAclF,cAAc,CAACld,CAAI,CAACyiB,EAAU,SAAS,EAAIA,IAC9EE,EAASxD,YAAY,CAAC,2BAA4BsD,GAC3CE,CACX,CAKA,SAASC,KAML,IAAK,IALDpH,EAAW,IAAI,CAACd,SAAS,CACzBmI,EAAOrH,EACF4D,gBAAgB,CAAC,wBACtB0D,EAActH,EACT4D,gBAAgB,CAAC,gCACjBjgB,EAAI,EAAGA,EAAI0jB,EAAK1iB,MAAM,CAAEhB,IAC7B0jB,CAAI,CAAC1jB,EAAE,CAACyc,SAAS,CAACC,MAAM,CAAC,8BACzBiH,CAAW,CAAC3jB,EAAE,CAACyc,SAAS,CAACC,MAAM,CAAC,2BAExC,CA6BA,SAASkH,GAAUC,CAAG,CAAE5hB,CAAK,EACzB,IAAI6hB,EAAU,IAAI,CAACvI,SAAS,CACnB0E,gBAAgB,CAAC,+BAC1B4D,CAAAA,EAAIxT,SAAS,EAAI,8BACjByT,CAAO,CAAC7hB,EAAM,CAACoO,SAAS,EAAI,2BAChC,CAOA,SAAS0T,GAAWR,CAAU,EAC1B,IAAI5H,EAAQ,IAAI,CAGhB+H,AADWrH,AADI,IAAI,CAACd,SAAS,CACT0E,gBAAgB,CAAC,wBAChCngB,OAAO,CAAC,SAAU+jB,CAAG,CAAE7jB,CAAC,EACrBujB,CAAAA,AAAe,IAAfA,GACAM,AAAiD,SAAjDA,EAAIpP,YAAY,CAAC,2BAAqC,GAG1D,CAAC,QAAS,aAAa,CAAC3U,OAAO,CAAC,SAAU+b,CAAS,EAC/CqH,GAAmBW,EAAKhI,EAAW,WAE/B4H,GAAY5lB,IAAI,CAAC8d,GACjBiI,GAAU/lB,IAAI,CAAC8d,EAAO,IAAI,CAAE3b,EAChC,EACJ,EACJ,EACJ,CAwBA,IAAIgkB,IACI/nB,EAAgB,SAAUe,CAAC,CAC3BoM,CAAC,EAOD,MAAOnN,AANHA,CAAAA,EAAgBoB,OAAOgM,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAalH,OAAS,SAAUpF,CAAC,CAC1DoM,CAAC,EAAIpM,EAAEsM,SAAS,CAAGF,CAAG,GACd,SAAUpM,CAAC,CACnBoM,CAAC,EAAI,IAAK,IAAIG,KAAKH,EAAO/L,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACuL,EAC/DG,IAAIvM,CAAAA,CAAC,CAACuM,EAAE,CAAGH,CAAC,CAACG,EAAE,AAAD,CAAG,CAAA,EACIvM,EAAGoM,EAC5B,EACO,SAAUpM,CAAC,CAAEoM,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIsI,UAAU,uBAAyBC,OAAOvI,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACC,WAAW,CAAGzM,CAAG,CADtCf,EAAce,EAAGoM,GAEjBpM,EAAEW,SAAS,CAAGyL,AAAM,OAANA,EAAa/L,OAAOqM,MAAM,CAACN,GAAMI,CAAAA,EAAG7L,SAAS,CAAGyL,EAAEzL,SAAS,CAAE,IAAI6L,CAAG,CACtF,GAIAya,GAAY,AAAChmB,IAA+E0G,GAAG,CAE/FyG,GAAa,AAACnN,IAA+EmN,UAAU,CAKvG8Y,GAAiB,AAACjmB,IAA+EC,QAAQ,CAAEimB,GAAsB,AAAClmB,IAA+E0X,aAAa,CAAEyO,GAAe,AAACnmB,IAA+E6T,MAAM,CAAEuS,GAAkB,AAACpmB,IAA+EI,SAAS,CAAEimB,GAAa,AAACrmB,IAA+EK,IAAI,CA0DrhBimB,GAAuB,SAAUza,CAAM,EAOvC,SAASya,EAAMlJ,CAAS,CAAEC,CAAQ,CAAElc,CAAK,EACrC,IAAI2K,EAAQD,EAAOjM,IAAI,CAAC,IAAI,CACxBwd,EACAC,IAAa,IAAI,CAkBrB,OAjBAvR,EAAM3K,KAAK,CAAGA,EACd2K,EAAMlJ,IAAI,CAAG,AAACuK,CAAAA,KAAavK,IAAI,CAAC4hB,UAAU,EAAI,CAAC,CAAA,EAAG9G,KAAK,EAAI,CAAC,EAC5DuI,GAAena,EAAMwR,SAAS,CAAE,YAAa,WACzC,IAAIiJ,EAAmBplB,GACfA,EAAMqlB,kBAAkB,EACxBrlB,EAAMqlB,kBAAkB,CAACD,gBAAgB,CACjD,GAAIA,EAAkB,CAClBA,EAAiBze,WAAW,CAAG,CAAA,EAC/B,IAAI2e,EAAWR,GAAeD,GAAW,QACrC,WACIU,WAAW,WACPH,EAAiBze,WAAW,CAAG,CAAA,CACvC,EAAG,GACH2e,GACJ,EACJ,CACJ,GACO3a,CACX,CA+HA,OA3JAia,GAAcO,EAAOza,GAsDrBya,EAAM5mB,SAAS,CAACggB,QAAQ,CAAG,SAAUC,CAAM,CAAEkC,CAAa,CAAEzE,CAAS,CAAEuJ,CAAe,EAClF,IAAI/C,EAAkBjE,EAAOkE,KAAK,CAAC,KAAMF,EAAaC,CAAe,CAACA,EAAgB7gB,MAAM,CAAG,EAAE,CAAEH,EAAO,IAAI,CAACA,IAAI,CAAEgkB,EAAY,cAAgB/E,EAAgB,IAAMwE,GAAWM,EAAgBlD,OAAO,CAAEE,GACtMA,EAAWhG,KAAK,CAAC,UAElBuI,GAAoB,QAAS,CACzBzC,QAASmD,EACTxU,UAAWuU,EAAgBjD,cAAc,AAC7C,EAAG,KAAK,EAAGtG,GAAWuB,WAAW,CAACqH,GAAUlG,cAAc,CAACld,CAAI,CAAC+gB,EAAW,EAAIA,IAGnF,IAAIH,EAAQ0C,GAAoB,QAAS,CACjC9E,KAAMwF,EACNtS,MAAOqS,EAAgBrS,KAAK,CAC5B/N,KAAMogB,EAAgBpgB,IAAI,CAC1B6L,UAAW,wBACf,EACA,KAAK,EACLgL,GAEJ,OADAoG,EAAMzB,YAAY,CAAC,uBAAwBpC,GACpC6D,CACX,EACA8C,EAAM5mB,SAAS,CAACme,iBAAiB,CAAG,WAChC,GAAI,IAAI,CAAC1c,KAAK,CAAE,CACZ,IAAIqlB,EAAqB,IAAI,CAACrlB,KAAK,CAACqlB,kBAAkB,CACtDJ,GAAgBI,EAAoB,cAChCA,GACAA,EAAmBK,qBAAqB,EACxCT,GAAgBI,EAAoB,iBAAkB,CAAE7b,OAAQ6b,EAAmBK,qBAAqB,AAAC,EAEjH,MAEIhb,EAAOnM,SAAS,CAACme,iBAAiB,CAACje,IAAI,CAAC,IAAI,CAEpD,EAiBA0mB,EAAM5mB,SAAS,CAAConB,SAAS,CAAG,SAAU1J,CAAS,CAAE7Z,CAAK,CAAEgD,CAAI,CAAEwgB,CAAS,CAAEC,CAAQ,EAC7E,IAAIlb,EAAQ,IAAI,CACZnB,EAASub,GAAoB,SAC7B,KAAK,EACL,KAAK,EACL9I,GAUJ,OATAzS,EAAOgU,WAAW,CAACqH,GAAUlG,cAAc,CAACvc,IACxCyjB,GACA,CAAC,QAAS,aAAa,CAACnlB,OAAO,CAAC,SAAU+b,CAAS,EAC/CqI,GAAetb,EAAQiT,EAAW,eA1J1CqJ,EAA6EC,EAAyMC,EAAwDC,EAC9UC,EA2JY,OADAvb,EAAMmS,UAAU,GACT+I,GA5JnBC,EAAY9iB,MAAMzE,SAAS,CAAC+F,KAAK,CAAC7F,IAAI,CAACwd,AA4JD2J,EA5JW/E,gBAAgB,CAAC,UAAWkF,EAAa/iB,MAAMzE,SAAS,CAAC+F,KAAK,CAAC7F,IAAI,CAACwd,AA4J/E2J,EA5JyF/E,gBAAgB,CAAC,WAAsImF,EAAW/J,AA4J3P2J,EA5JqQ/E,gBAAgB,CAAhJ,6CAA8J,CAAC,EAAE,CAAEoF,EAAWhK,AA4JnT2J,EA5J6T/E,gBAAgB,CAA3I,6CAAyJ,CAAC,EAAE,CACpYqF,EAAe,CACXC,WA0J6C/gB,EAzJ7C4gB,SAAUA,GAAYA,EAAS3Q,YAAY,CAAC,UAAY,GACxDwK,OAAQ,CAAC,CACb,EACJiG,EAAUplB,OAAO,CAAC,SAAU2hB,CAAK,EAC7B,IAAI+D,EAAQ/D,EAAMhN,YAAY,CAAC,wBAAoCgN,EAAMhN,YAAY,CAAC,6BAGlF6Q,EAAaG,QAAQ,CAAGhE,EAAMlP,KAAK,CAE9BiT,EACLF,EAAarG,MAAM,CAACuG,EAAM,CAAG/D,EAAMlP,KAAK,CAIxC+S,EAAa9gB,IAAI,CAAGid,EAAMlP,KAAK,AAEvC,GACA4S,EAAWrlB,OAAO,CAAC,SAAU4lB,CAAM,EAC/B,IAAItiB,EAAKsiB,EAAOtiB,EAAE,CAElB,GAAIA,AAAO,6BAAPA,GACAA,AAAO,6BAAPA,EAAmC,CACnC,IAAIuiB,EAAYviB,EAAG0e,KAAK,CAAC,qBAAqB,CAAC,EAAE,AACjDwD,CAAAA,EAAarG,MAAM,CAAC0G,EAAU,CAAGD,EAAOnT,KAAK,AACjD,CACJ,GACI8S,GACAC,CAAAA,EAAarG,MAAM,CAAC,wBAAwB,CAAGoG,EAC1C5Q,YAAY,CAAC,UAAY,EAAC,EAE5B6Q,GA4HK,EACJ,GAEG1c,CACX,EASA2b,EAAM5mB,SAAS,CAACioB,QAAQ,CAAG,SAAUphB,CAAI,CAAEpF,CAAK,CAAEP,CAAO,CAAEomB,CAAQ,EAC1D7lB,IAIL,IAAI,CAAC+c,SAAS,GAED,eAAT3X,GACA,IAAI,CAACqhB,UAAU,CAACC,OAAO,CAACjoB,IAAI,CAAC,IAAI,CAAEuB,EAAOP,EAASomB,GAG1C,uBAATzgB,GACA,IAAI,CAAC1F,WAAW,CAACinB,UAAU,CAACloB,IAAI,CAAC,IAAI,CAAEuB,EAAOP,EAASomB,GAG9C,oBAATzgB,GACA,IAAI,CAAC1F,WAAW,CAACgnB,OAAO,CAACjoB,IAAI,CAAC,IAAI,CAAEuB,EAAOP,EAASomB,GAG3C,SAATzgB,GACA,IAAI,CAAC1F,WAAW,CAACgnB,OAAO,CAACjoB,IAAI,CAAC,IAAI,CAAEuB,EAAOP,EAASomB,EAAU,CAAA,GAElE,IAAI,CAACzgB,IAAI,CAAGA,EAEZ,IAAI,CAAC+W,SAAS,CAACjR,KAAK,CAACD,MAAM,CAAG,IAAI,CAACkR,SAAS,CAACyK,YAAY,CAAG,KAChE,EACOzB,CACX,EA9tCoDnJ,IA+tCpDgJ,GAAaG,GAAM5mB,SAAS,CAAE,CAC1BmB,YAjiCmB,CACnBgnB,QA3JJ,SAAiB1mB,CAAK,CAAEP,CAAO,CAAEomB,CAAQ,CAAEgB,CAAM,EAC7C,GAAK7mB,GAGL,IAAIid,EAAW,IAAI,CAACd,SAAS,CACzB1a,EAAO,IAAI,CAACA,IAAI,CAEhB+d,EAAS5B,GAA+B,KAAM,CAC1C3M,UAAW,6BACf,EACA,KAAK,EACLgM,GACJuC,EAAOhC,WAAW,CAACE,GAAqBiB,cAAc,CAACld,CAAI,CAAChC,EAAQqnB,OAAO,CAAC,EAAIrnB,EAAQqnB,OAAO,EAAI,KAEnGtH,EAAS5B,GAA+B,MAAO,CAC3C3M,UAAY,oDAChB,EAAG,KAAK,EAAGgM,GACX,IAAI8J,EAAYnJ,GAA+B,MAAO,CAC9C3M,UAAW,6BACf,EACA,KAAK,EACLgM,GACJgB,GAAcxf,IAAI,CAAC,IAAI,CAAE+gB,EAAQxf,EAAO,GAAIP,EAAS,EAAE,CAAE,CAAA,GACzD,IAAI,CAACkmB,SAAS,CAACoB,EAAWF,EACrBplB,EAAKkkB,SAAS,EAAI,MAClBlkB,EAAKulB,UAAU,EAAI,OAASH,EAAS,MAAQ,OAAQ5J,EAAU4I,GACxE,EAkIIc,WAzHJ,SAAoB3mB,CAAK,CAAEP,CAAO,CAAEomB,CAAQ,EACxC,IAAIlb,EAAQ,IAAI,CACZlJ,EAAO,IAAI,CAACA,IAAI,CAChBwb,EAAW,IAAI,CAACd,SAAS,CACzBqK,EAAW,IAAI,CAACA,QAAQ,CACxBxJ,EAAe,+BAE8B,CAAA,KAA7CC,EAAShM,SAAS,CAACjK,OAAO,CAACgW,IAC3BC,CAAAA,EAAShM,SAAS,EAAI,IAAM+L,EAAe,2BAA0B,EAGrEhd,GACAid,CAAAA,EAAS/R,KAAK,CAAC+b,GAAG,CAAGjnB,EAAMiJ,OAAO,CAAG,GAAK,IAAG,EAGjD,IAAI7G,EAAQwb,GAA+B,IAAK,CACxC3M,UAAW,6BACf,EACA,KAAK,EACLgM,GACJ7a,EAAMwe,YAAY,CAAC,aAAc,mBACjCxe,EAAMob,WAAW,CAACE,GAAqBiB,cAAc,CAACZ,GAEtDtc,CAAI,CAAChC,EAAQqnB,OAAO,CAAC,EAAIrnB,EAAQqnB,OAAO,CAExCrnB,EAAQ4J,MAAM,EAAI5J,EAAQ4J,MAAM,CAAC,EAAE,CAACjE,IAAI,CAAE,MAE1C,IAAIoE,EAAS,IAAI,CAACmc,SAAS,CAAC1I,EAAUxb,EAAKylB,UAAU,EAAI,OAAQ,OAAQjK,EAAU,WAC3EuJ,EAAS/nB,IAAI,CAACkM,EAAO,kBAAmB3K,EAAOP,EAASomB,EAChE,EACArc,CAAAA,EAAOyH,SAAS,EAAI,qCACpBzH,EAAO0B,KAAK,CAAC,mBAAmB,CAAG,OAC/B,IAAI,CAACgR,QAAQ,CAAG,YACpB1S,EAAS,IAAI,CAACmc,SAAS,CAAC1I,EAAUxb,EAAK0lB,YAAY,EAAI,SAAU,SAAUlK,EAAU4I,GACrFrc,EAAOyH,SAAS,EAAI,uCACpBzH,EAAO0B,KAAK,CAAC,mBAAmB,CAAG,OAC/B,IAAI,CAACgR,QAAQ,CAAG,cACxB,CAqFA,EA+hCIuK,WAzZkB,CAClBC,QAljBJ,SAAiC1mB,CAAK,CAAEonB,CAAQ,CAAEvB,CAAQ,EACtD,IACIwB,EADA5lB,EAAO,IAAI,CAACA,IAAI,CAEpB,GAAKzB,GAIL,IAAI,CAACskB,IAAI,CAAC/T,IAAI,CAAC9R,IAAI,CAAC,IAAI,CAAEuB,GAE1B,IAAIsnB,EAAiB,IAAI,CAACnL,SAAS,CAC1B0E,gBAAgB,CAAC,gCAE1BtB,GAAiB+H,CAAc,CAAC,EAAE,EAClCvF,GAAatjB,IAAI,CAAC,IAAI,CAAEuB,EAAOsnB,CAAc,CAAC,EAAE,EAChDhH,GAAiB7hB,IAAI,CAAC,IAAI,CAAEuB,EAAOsnB,CAAc,CAAC,EAAE,CAAE,OACtDD,EAAkBC,CAAc,CAAC,EAAE,CAC9BzG,gBAAgB,CAAC,4BAA4B,CAAC,EAAE,CACrD,IAAI,CAAC8E,SAAS,CAAC0B,EAAiB5lB,EAAKkkB,SAAS,EAAI,MAAO,MAAO0B,EAAiBxB,GAEjFtG,GAAiB+H,CAAc,CAAC,EAAE,EAClChH,GAAiB7hB,IAAI,CAAC,IAAI,CAAEuB,EAAOsnB,CAAc,CAAC,EAAE,CAAE,QACtDD,EAAkBC,CAAc,CAAC,EAAE,CAC9BzG,gBAAgB,CAAC,4BAA4B,CAAC,EAAE,CACrD,IAAI,CAAC8E,SAAS,CAAC0B,EAAiB5lB,EAAKulB,UAAU,EAAI,OAAQ,OAAQK,EAAiBxB,GACpF,IAAI,CAACF,SAAS,CAAC0B,EAAiB5lB,EAAK0lB,YAAY,EAAI,SAAU,SAAUE,EAAiBxB,GAC9F,EA0hBI0B,UAxFJ,WACI,IAAIC,EAAU,EAOd,OANA,IAAI,CAAC7kB,MAAM,CAACjC,OAAO,CAAC,SAAU+mB,CAAK,EAC3BA,CAAAA,EAAM3H,MAAM,EACZ2H,EAAMhoB,OAAO,CAACqgB,MAAM,AAAD,GACnB0H,GAER,GACOA,CACX,CAgFA,EAuZIlD,KAzQY,CACZ/T,KA1DJ,SAAcvQ,CAAK,EACf,GAAKA,GAGL,IAAI0nB,EAAkB,IAAI,CAACjB,UAAU,CAACc,SAAS,CAAC9oB,IAAI,CAACuB,GAEjD2nB,EAAW1D,GAAYxlB,IAAI,CAAC,IAAI,CAAE,OAClCwlB,GAAYxlB,IAAI,CAAC,IAAI,CAAE,OACvBipB,GAEJ1D,GAAevlB,IAAI,CAAC,IAAI,EACxBulB,GAAevlB,IAAI,CAAC,IAAI,EACxBkmB,GAAWlmB,IAAI,CAAC,IAAI,CAAEipB,GAEtBlD,GAAU/lB,IAAI,CAAC,IAAI,CAAEkpB,EAAU,GACnC,CA4CA,CAwQA,GAsBA,IAAIC,GAAW,AAAC/oB,IAA+E+oB,QAAQ,CAGnGC,GAA4B,AAAChpB,IAA+EC,QAAQ,CAAEgpB,GAAa,AAACjpB,IAA+EipB,UAAU,CAAEC,GAAwB,AAAClpB,IAA+EM,IAAI,CAmB/U,SAAS6oB,KACD,IAAI,CAACzL,KAAK,EACV,IAAI,CAACA,KAAK,CAACO,UAAU,EAE7B,CAIA,SAASmL,GAA8BC,CAAM,EACpC,IAAI,CAAC3L,KAAK,EAEX,CAAA,IAAI,CAACA,KAAK,CAAG,IAjD2B4I,GAiDX,IAAI,CAACnlB,KAAK,CAACmc,SAAS,CAAG,IAAI,CAACnc,KAAK,CAACP,OAAO,CAAC4jB,UAAU,CAACnH,QAAQ,EACrF,IAAI,CAAClc,KAAK,CAACP,OAAO,CAAC0oB,UAAU,EAC1B,IAAI,CAACnoB,KAAK,CAACP,OAAO,CAAC0oB,UAAU,CAACC,GAAG,CAAClM,QAAQ,EAC9C,sDAAwD,IAAI,CAAClc,KAAK,CAAA,EAE1E,IAAI,CAACuc,KAAK,CAACiK,QAAQ,CAAC0B,EAAOG,QAAQ,CAAE,IAAI,CAACroB,KAAK,CAAEkoB,EAAOzoB,OAAO,CAAEyoB,EAAOI,QAAQ,CACpF,CAMA,SAASC,GAAiCpkB,CAAO,CAAEgC,CAAC,EAE3C,IAAI,CAACqiB,OAAO,CAACriB,EAAEW,MAAM,CAAE,qBACxB3C,EAAQE,KAAK,CAAC,IAAI,CAAErB,MAAMzE,SAAS,CAAC+F,KAAK,CAAC7F,IAAI,CAAC8F,UAAW,GAElE,CAS6B,OA/C7B,SAAiBkkB,CAAuB,CAAE9jB,CAAY,EAC9CmjB,GAAWF,GAAU,WACrBC,GAA0BY,EAAyB,aAAcT,IACjEH,GAA0BY,EAAyB,YAAaR,IAChEF,GAAsBpjB,EAAapG,SAAS,CAAE,uBAAwBgqB,IAE9E,EAsDIG,IACI7rB,EAAgB,SAAUe,CAAC,CAC3BoM,CAAC,EAOD,MAAOnN,AANHA,CAAAA,EAAgBoB,OAAOgM,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAalH,OAAS,SAAUpF,CAAC,CAC1DoM,CAAC,EAAIpM,EAAEsM,SAAS,CAAGF,CAAG,GACd,SAAUpM,CAAC,CACnBoM,CAAC,EAAI,IAAK,IAAIG,KAAKH,EAAO/L,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACuL,EAC/DG,IAAIvM,CAAAA,CAAC,CAACuM,EAAE,CAAGH,CAAC,CAACG,EAAE,AAAD,CAAG,CAAA,EACIvM,EAAGoM,EAC5B,EACO,SAAUpM,CAAC,CAAEoM,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIsI,UAAU,uBAAyBC,OAAOvI,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACC,WAAW,CAAGzM,CAAG,CADtCf,EAAce,EAAGoM,GAEjBpM,EAAEW,SAAS,CAAGyL,AAAM,OAANA,EAAa/L,OAAOqM,MAAM,CAACN,GAAMI,CAAAA,EAAG7L,SAAS,CAAGyL,EAAEzL,SAAS,CAAE,IAAI6L,CAAG,CACtF,GAGAue,GAAuB,AAAC9pB,IAA+E8pB,oBAAoB,CAe3HC,GAA0B,AAAC/pB,IAA+E+pB,uBAAuB,CAAEC,GAAmB,AAAChqB,IAA+EE,KAAK,CAAE+pB,GAAuB,AAACjqB,IAA+EI,SAAS,CAAE8pB,GAAmB,AAAClqB,IAA+E0L,KAAK,CAAEye,GAAkB,AAACnqB,IAA+EK,IAAI,CAAE6P,GAAQ,AAAClQ,IAA+EkQ,KAAK,CA0BlpB,SAASka,GAA0BC,CAAW,CAAExX,CAAU,EACtD,IAAIyX,EAAgB,CAAC,EAerB,MAdA,CAAC,SAAU,SAAS,CAACzoB,OAAO,CAAC,SAAUuf,CAAI,EACvC,IAAImJ,EAAkBF,CAAW,CAACjJ,EAAK,CACnCoJ,EAAkB3X,CAAU,CAACuO,EAAK,CAClCmJ,IACIC,EACAF,CAAa,CAAClJ,EAAK,CAAGlR,GAAMsa,GAAiBzY,GAAG,CAAC,SAAU0Y,CAAY,CAAE1oB,CAAC,EACtE,OAAOmoB,GAAiBK,CAAe,CAACxoB,EAAE,CAAE0oB,EAChD,GAGAH,CAAa,CAAClJ,EAAK,CAAGiJ,CAAW,CAACjJ,EAAK,CAGnD,GACOkJ,CACX,CAqBA,IAAII,GAA4B,SAAU7e,CAAM,EAO5C,SAAS6e,EAAWvpB,CAAK,CAAEX,CAAW,EAClC,IAAIsL,EAAQD,EAAOjM,IAAI,CAAC,IAAI,GAAK,IAAI,AACrCkM,CAAAA,EAAM5G,IAAI,CAAG,cAOb4G,EAAM3K,KAAK,CAAGA,EAOd2K,EAAMpI,MAAM,CAAG,EAAE,CAOjBoI,EAAMyD,aAAa,CAAG,EAAE,CACxBzD,EAAM5G,IAAI,CAAG,cACb4G,EAAM9H,KAAK,CAAG,GAOd8H,EAAMxI,MAAM,CAAG,EAAE,CAOjBwI,EAAMtB,MAAM,CAAG,EAAE,CAOjBsB,EAAMlL,OAAO,CAAGspB,GAAiBpe,EAAM6e,cAAc,CAAEnqB,GAOvDsL,EAAMtL,WAAW,CAAGA,EAGpB,IAAIoqB,EAAkBR,GAA0Bte,EAAMlL,OAAO,CACzDJ,GA6BJ,OA5BAsL,EAAMlL,OAAO,CAAC0C,MAAM,CAAGsnB,EAAgBtnB,MAAM,CAC7CwI,EAAMlL,OAAO,CAAC4J,MAAM,CAAGogB,EAAgBpgB,MAAM,CA0B7CsB,EAAM4F,IAAI,CAACvQ,EAAO2K,EAAMlL,OAAO,EACxBkL,CACX,CA0aA,OAvgBA+d,GAAmBa,EAAY7e,GAsG/B6e,EAAW/kB,OAAO,CAAG,SAAUE,CAAU,CAAEglB,CAAuB,CAAE/kB,CAAY,CAAEqP,CAAgB,EAC9F3O,EAA4Bb,OAAO,CAAC+kB,EAAY7kB,EAAYC,GAC5DiX,AAtxD4D3C,GAsxD5BzU,OAAO,CAACwP,GACxC4B,AA1rF2D7B,GA0rF5BvP,OAAO,CAACE,EAAYsP,GACnD0V,EAAwBllB,OAAO,CAAC+kB,EAAY7kB,GAC5CilB,GAA+BD,EAAyB/kB,EAC5D,EASA4kB,EAAWhrB,SAAS,CAACqrB,YAAY,CAAG,WAChC,IAAI,CAACC,WAAW,GACZ,IAAI,CAACC,SAAS,EACd,IAAI,CAACC,SAAS,EACd,IAAI,CAACtqB,OAAO,CAAC2b,IAAI,EAEjB,CAAA,IAAI,CAACjb,QAAQ,CAAG,IAAI,CAACH,KAAK,CAACE,QAAQ,CAACC,QAAQ,CAAC,IAAI,CAAC6pB,UAAU,GAAE,CAEtE,EAIAT,EAAWhrB,SAAS,CAAC0rB,SAAS,CAAG,WAC7B,IAAItf,EAAQ,IAAI,CACZuf,EAAiB,IAAI,CAACzqB,OAAO,CAAC0C,MAAM,EAAI,EAAE,CAC9C+nB,EAAcxpB,OAAO,CAAC,SAAUuB,CAAY,CAAErB,CAAC,EAC3C,IAAIwB,EAAQuI,EAAMwf,SAAS,CAACloB,EACxBrB,GACJmoB,GAAiB,CAAA,EAAMmB,CAAa,CAACtpB,EAAE,CAAEwB,EAAM3C,OAAO,CAC1D,EACJ,EAIA8pB,EAAWhrB,SAAS,CAAC6rB,SAAS,CAAG,WAC7B,IAAIzf,EAAQ,IAAI,CACZtB,EAAS,IAAI,CAAC5J,OAAO,CAAC4J,MAAM,EAAI,EAAE,CACtCA,EAAO3I,OAAO,CAAC,SAAU6Q,CAAY,CAAE3Q,CAAC,EACpC,IAAI0I,EAAQqB,EAAM0f,SAAS,CAAC9Y,EACxB3Q,GACJmoB,GAAiB,CAAA,EAAM1f,CAAM,CAACzI,EAAE,CAAE0I,EAAM7J,OAAO,CACnD,EACJ,EAQA8pB,EAAWhrB,SAAS,CAACwC,OAAO,CAAG,WAC3B,IAAIf,EAAQ,IAAI,CAACA,KAAK,CAClBsqB,EAAc,SAAUtV,CAAI,EACxBA,EAAKjU,OAAO,EACpB,EACA,IAAI,CAACoB,MAAM,CAACzB,OAAO,CAAC4pB,GACpB,IAAI,CAACjhB,MAAM,CAAC3I,OAAO,CAAC4pB,GACpB,IAAI,CAACR,SAAS,CAAG,KACjB,IAAI,CAACC,SAAS,CAAG,KACjBlB,GAAiB7oB,EAAMuqB,eAAe,CAAE,IAAI,CAACC,cAAc,EAC3D9f,EAAOnM,SAAS,CAACwC,OAAO,CAACtC,IAAI,CAAC,IAAI,EAClC,IAAI,CAACmQ,oBAAoB,GACzBga,GAAwB,IAAI,CAAE5oB,EAClC,EAKAupB,EAAWhrB,SAAS,CAAC+rB,WAAW,CAAG,SAAUtV,CAAI,EAE7C6T,GAAiB,IAAI,CAAC7T,EAAK3E,QAAQ,CAAG,IAAI,CAAE2E,GAC5CA,EAAKjU,OAAO,EAChB,EAIAwoB,EAAWhrB,SAAS,CAACyrB,UAAU,CAAG,WAC9B,GAAI,IAAI,CAACF,SAAS,EAAI,IAAI,CAACC,SAAS,CAChC,MAAO,CACHtnB,EAAG,IAAI,CAACqnB,SAAS,CAACW,IAAI,CACtBljB,EAAG,IAAI,CAACwiB,SAAS,CAAC9C,GAAG,CACrBjc,MAAO,IAAI,CAAC8e,SAAS,CAAC9e,KAAK,CAC3BC,OAAQ,IAAI,CAAC8e,SAAS,CAAC9e,MAAM,AACjC,CAER,EAKAse,EAAWhrB,SAAS,CAACmsB,cAAc,CAAG,SAAU1qB,CAAK,CAAEX,CAAW,EAC9D,IAAI,CAACsrB,UAAU,CAACtrB,GAChB,IAAIoqB,EAAkBR,GAA0B,IAAI,CAACxpB,OAAO,CACxDJ,EACJ,CAAA,IAAI,CAACI,OAAO,CAAC0C,MAAM,CAAGsnB,EAAgBtnB,MAAM,CAC5C,IAAI,CAAC1C,OAAO,CAAC4J,MAAM,CAAGogB,EAAgBpgB,MAAM,CAC5C,IAAI,CAACrJ,KAAK,CAAGA,EACb,IAAI,CAACuC,MAAM,CAAG,EAAE,CAChB,IAAI,CAAC6L,aAAa,CAAG,EAAE,CACvB,IAAI,CAACrK,IAAI,CAAG,cACZ,IAAI,CAAC1E,WAAW,CAAGA,EACnB,IAAI,CAAC8C,MAAM,CAAG,EAAE,CAChB,IAAI,CAACkH,MAAM,CAAG,EAAE,AACpB,EAKAkgB,EAAWhrB,SAAS,CAACgS,IAAI,CAAG,SAAUqa,CAAkB,CAAEC,CAAY,CAAEhoB,CAAK,EAC3D,KAAK,IAAfA,GAAoBA,CAAAA,EAAQ,IAAI,CAACA,KAAK,AAAD,EACzC,IAAI7C,EAAQ,IAAI,CAACA,KAAK,CAClB8qB,EAAc,IAAI,CAACrrB,OAAO,CAACmL,SAAS,AACxC,CAAA,IAAI,CAAC/H,KAAK,CAAGA,EACb,IAAI,CAACmM,UAAU,GACf,IAAI,CAACb,gBAAgB,GACrB,IAAI,CAACic,SAAS,GACd,IAAI,CAACH,SAAS,GACd,IAAI,CAACc,iBAAiB,GACtB,IAAI,CAACnnB,eAAe,CAAG+kB,GAAqB3oB,EAAO8qB,EACvD,EAKAvB,EAAWhrB,SAAS,CAAC4rB,SAAS,CAAG,SAAUloB,CAAY,CAAEY,CAAK,EAC1D,IAIIT,EAAQ,IA15DgD6W,GA05DZ,IAAI,CAJtC8P,GAAiB,IAAI,CAACtpB,OAAO,CAACwC,YAAY,CAAE,CAClDqM,oBAAqB,IAAI,CAAC7O,OAAO,CAAC6O,mBAAmB,AACzD,EACArM,GAGAY,GAGJ,OAFAT,EAAMiO,QAAQ,CAAG,QACjB,IAAI,CAAClO,MAAM,CAACxC,IAAI,CAACyC,GACVA,CACX,EAUAmnB,EAAWhrB,SAAS,CAAC8rB,SAAS,CAAG,SAAU9Y,CAAY,CAAE1O,CAAK,EAC1D,IAAIpD,EAAUspB,GAAiB,IAAI,CAACtpB,OAAO,CAAC8R,YAAY,CAAE,CAClDjD,oBAAqB,IAAI,CAAC7O,OAAO,CAAC6O,mBAAmB,AACzD,EACAiD,GACAjI,EAAQ,IAAKigB,EAAWyB,SAAS,CAACvrB,EAAQ2F,IAAI,CAAC,CAAE,IAAI,CACrD3F,EACAoD,GAGJ,OAFAyG,EAAM+G,QAAQ,CAAG,QACjB,IAAI,CAAChH,MAAM,CAAC1J,IAAI,CAAC2J,GACVA,CACX,EAIAigB,EAAWhrB,SAAS,CAACe,MAAM,CAAG,SAAUsL,CAAS,EAC7C,IAAI,CAACoE,UAAU,GACV,IAAI,CAACpP,OAAO,EACb,IAAI,CAACkL,MAAM,GAEX,IAAI,CAAC3K,QAAQ,EACb,IAAI,CAACA,QAAQ,CAACwD,OAAO,CAAC,IAAI,CAACqmB,UAAU,IAEzC,IAAI,CAACiB,WAAW,CAAC,IAAI,CAAC5hB,MAAM,CAAEuB,GAC9B,IAAI,CAACqgB,WAAW,CAAC,IAAI,CAAC9oB,MAAM,CAAEyI,GAC9B,IAAI,CAAC0E,mBAAmB,CAAC1E,EAC7B,EAKA2e,EAAWhrB,SAAS,CAAC2sB,UAAU,CAAG,SAAUlW,CAAI,CAAEpK,CAAS,EAEvD,GADAoK,EAAKhG,UAAU,GACVgG,EAAK5D,aAAa,GAQnB,CAAA,GAJK4D,EAAKpV,OAAO,EACb,IAAI,CAACurB,UAAU,CAACnW,GAEpBA,EAAK1V,MAAM,CAAC0pB,GAAgBpe,EAAW,CAAA,IAASoK,EAAKpV,OAAO,CAACmV,MAAM,EAC/DC,EAAKzS,MAAM,CAACX,MAAM,CAAE,KA3V5BQ,EACAgpB,EADAhpB,EAAQ4S,AA4ViBA,EA5VZpV,OAAO,CACpBwrB,EAAmBpW,AA2VMA,EA3VDzS,MAAM,CAAC1B,IAAI,CAAC,SAAUgL,CAAK,EAAI,MAAQA,AAAyB,CAAA,IAAzBA,EAAMlJ,MAAM,CAACyI,OAAO,EAC/ES,AAAkB,CAAA,IAAlBA,EAAMT,OAAO,AAAa,GAC9BhJ,IACKgpB,EAGyB,WAArBhpB,EAAMipB,UAAU,EACrBjpB,EAAMkpB,IAAI,GAHVlpB,EAAMmpB,IAAI,GAwVV,CAAA,MATA,IAAI,CAACjB,WAAW,CAACtV,EAWzB,EAIAuU,EAAWhrB,SAAS,CAAC0sB,WAAW,CAAG,SAAUO,CAAK,CAAE5gB,CAAS,EAIzD,IAHA,IAAIhK,EAAI4qB,EAAM5pB,MAAM,CAGbhB,KACH,IAAI,CAACsqB,UAAU,CAACM,CAAK,CAAC5qB,EAAE,CAAEgK,EAElC,EAKA2e,EAAWhrB,SAAS,CAAC+e,MAAM,CAAG,WAE1B,OAAO,IAAI,CAACtd,KAAK,CAACkF,gBAAgB,CAAC,IAAI,CAC3C,EAIAqkB,EAAWhrB,SAAS,CAACuM,MAAM,CAAG,WAC1B,IAAI5K,EAAW,IAAI,CAACF,KAAK,CAACE,QAAQ,AAClC,CAAA,IAAI,CAACN,OAAO,CAAGM,EACVI,CAAC,CAAC,cACFT,IAAI,CAAC,CACNC,QAAS,EACTS,OAAQ,IAAI,CAACd,OAAO,CAACc,MAAM,CAC3B8qB,WAAY,IAAI,CAAC5rB,OAAO,CAAC2L,OAAO,CAC5B,UACA,QACR,GACK3K,GAAG,GACR,IAAI,CAACgrB,WAAW,CAAGvrB,EACdI,CAAC,CAAC,qBACFG,GAAG,CAAC,IAAI,CAACb,OAAO,EACjB,IAAI,CAACH,OAAO,CAAC2b,IAAI,EACjB,IAAI,CAACqQ,WAAW,CAACjrB,IAAI,CAAC,IAAI,CAACR,KAAK,CAACC,WAAW,EAEhD,IAAI,CAACyrB,WAAW,CAAGxrB,EACdI,CAAC,CAAC,qBACFT,IAAI,CAAC,CAEN4M,WAAY,EACZC,WAAY,CAChB,GACKjM,GAAG,CAAC,IAAI,CAACb,OAAO,EACrB,IAAI,CAACgqB,YAAY,GACb,IAAI,CAACzpB,QAAQ,EACb,IAAI,CAACP,OAAO,CAACY,IAAI,CAAC,IAAI,CAACL,QAAQ,EAGnC,IAAI,CAACwrB,WAAW,CAAC,IAAI,CAACtiB,MAAM,EAC5B,IAAI,CAACsiB,WAAW,CAAC,IAAI,CAACxpB,MAAM,EAC5B,IAAI,CAAC4D,SAAS,GACd,IAAI,CAACwJ,mBAAmB,EAC5B,EAIAga,EAAWhrB,SAAS,CAAC4sB,UAAU,CAAG,SAAUnW,CAAI,EAC5CA,EAAKlK,MAAM,CAACkK,AAAkB,UAAlBA,EAAK3E,QAAQ,CACrB,IAAI,CAACqb,WAAW,CAChB,IAAI,CAACD,WAAW,CACxB,EAIAlC,EAAWhrB,SAAS,CAACotB,WAAW,CAAG,SAAUH,CAAK,EAE9C,IADA,IAAI5qB,EAAI4qB,EAAM5pB,MAAM,CACbhB,KACH,IAAI,CAACuqB,UAAU,CAACK,CAAK,CAAC5qB,EAAE,CAEhC,EAIA2oB,EAAWhrB,SAAS,CAACsrB,WAAW,CAAG,WAC/B,IAAI+B,EAAQ,IAAI,CAAC5rB,KAAK,CAAC4C,KAAK,CACxBipB,EAAQ,IAAI,CAAC7rB,KAAK,CAACkM,KAAK,CACxB4f,EAAa,AAAC,CAAA,IAAI,CAACrsB,OAAO,CAAC0C,MAAM,EAAI,EAAE,AAAD,EACjC4pB,MAAM,CAAC,IAAI,CAACtsB,OAAO,CAAC4J,MAAM,EAAI,EAAE,EAChC2iB,MAAM,CAAC,SAAUC,CAAI,CAC1BC,CAAY,EACR,IAAIrgB,EAAQqgB,GACPA,CAAAA,EAAargB,KAAK,EACdqgB,EAAa3pB,MAAM,EAAI2pB,EAAa3pB,MAAM,CAAC,EAAE,EAC1D,MAAO,CACHqpB,CAAK,CAAC/f,GAASA,EAAMjJ,KAAK,CAAC,EAAIqpB,CAAI,CAAC,EAAE,CACtCJ,CAAK,CAAChgB,GAASA,EAAMK,KAAK,CAAC,EAAI+f,CAAI,CAAC,EAAE,CACzC,AACL,EAAG,EAAE,CACL,CAAA,IAAI,CAACnC,SAAS,CAAGgC,CAAU,CAAC,EAAE,CAC9B,IAAI,CAAC/B,SAAS,CAAG+B,CAAU,CAAC,EAAE,AAClC,EAIAvC,EAAWhrB,SAAS,CAAC4S,0BAA0B,CAAG,SAAU/F,CAAO,EAC/D,IAAI+gB,EAAiC,SAAUnX,CAAI,EAC3CA,EAAK7D,0BAA0B,CAAC/F,EACxC,EACA,IAAI,CAACgD,aAAa,CAAC1N,OAAO,CAAC,SAAUmO,CAAY,EAC7CA,EAAa1D,aAAa,CAACC,EAC/B,GACA,IAAI,CAAC/B,MAAM,CAAC3I,OAAO,CAACyrB,GACpB,IAAI,CAAChqB,MAAM,CAACzB,OAAO,CAACyrB,EACxB,EAIA5C,EAAWhrB,SAAS,CAACwsB,iBAAiB,CAAG,WACrC,IAAIxrB,EAAa,IAAI,AACrBA,CAAAA,EAAWirB,cAAc,CAAG,WACxB,OAAOjrB,EAAW4C,MAAM,CAAC6pB,MAAM,CAAC,SAAU7pB,CAAM,CAAEC,CAAK,EAInD,OAHKA,EAAM3C,OAAO,CAAC2sB,YAAY,EAC3BjqB,EAAOxC,IAAI,CAACyC,EAAMxC,OAAO,EAEtBuC,CACX,EAAG,EAAE,CACT,EACA5C,EAAWS,KAAK,CAACuqB,eAAe,CAAC5qB,IAAI,CAACJ,EAAWirB,cAAc,CACnE,EAOAjB,EAAWhrB,SAAS,CAACosB,UAAU,CAAG,SAAUtrB,CAAW,EACnD,IAAI,CAACI,OAAO,CAAGspB,GAAiB,IAAI,CAACS,cAAc,CAAEnqB,EACzD,EAQAkqB,EAAWhrB,SAAS,CAAC4M,aAAa,CAAG,SAAUC,CAAO,EAClD,IAAI3L,EAAU,IAAI,CAACA,OAAO,CACtB4jB,EAAa,IAAI,CAACrjB,KAAK,CAACqlB,kBAAkB,CAC1CgG,EAAarC,GAAgB5d,EAAS,CAAC3L,EAAQ2L,OAAO,EAE1D,GADA,IAAI,CAACxL,OAAO,CAACC,IAAI,CAAC,aAAcwrB,EAAa,UAAY,UACrD,CAACA,EAAY,CACb,IAAIc,EAAiC,SAAUnX,CAAI,EAC3CA,EAAK7D,0BAA0B,CAACka,EACxC,EACA,IAAI,CAAChiB,MAAM,CAAC3I,OAAO,CAACyrB,GACpB,IAAI,CAAChqB,MAAM,CAACzB,OAAO,CAACyrB,GAChB9I,EAAW+B,gBAAgB,GAAK,IAAI,EACpC/B,EAAW9G,KAAK,EAChB8G,AAA0B,uBAA1BA,EAAW9G,KAAK,CAACnX,IAAI,EACrB0jB,GAAqBzF,EAAY,aAEzC,CACA5jB,EAAQ2L,OAAO,CAAGigB,CACtB,EAUA9B,EAAWhrB,SAAS,CAAC8M,MAAM,CAAG,SAAUhM,CAAW,CAAEC,CAAM,EACvD,IAAIU,EAAQ,IAAI,CAACA,KAAK,CAClBypB,EAAkBR,GAA0B,IAAI,CAAC5pB,WAAW,CAC5DA,GACAgtB,EAAmBrsB,EAAMN,WAAW,CAACsH,OAAO,CAAC,IAAI,EACjDvH,EAAUspB,GAAiB,CAAA,EAC3B,IAAI,CAAC1pB,WAAW,CAChBA,EACJI,CAAAA,EAAQ0C,MAAM,CAAGsnB,EAAgBtnB,MAAM,CACvC1C,EAAQ4J,MAAM,CAAGogB,EAAgBpgB,MAAM,CACvC,IAAI,CAACtI,OAAO,GACZ,IAAI,CAAC2pB,cAAc,CAAC1qB,EAAOP,GAC3B,IAAI,CAAC8Q,IAAI,CAACvQ,EAAOP,GAEjBO,EAAMP,OAAO,CAACC,WAAW,CAAC2sB,EAAiB,CAAG,IAAI,CAAC5sB,OAAO,CAC1D,IAAI,CAACiI,UAAU,CAAG,CAAA,EACdshB,GAAgB1pB,EAAQ,CAAA,IACxBU,EAAMc,eAAe,GAEzBgoB,GAAqB,IAAI,CAAE,eAC3B,IAAI,CAACphB,UAAU,CAAG,CAAA,CACtB,EASA6hB,EAAW9e,YAAY,CAphJkCA,EAwhJzD8e,EAAW5d,SAAS,CAvmIkCA,GA+mItD4d,EAAWyB,SAAS,CAAG,CACnB,KA38F2DvV,GA48F3D,OAj0F6DM,GAk0F7D,QA/kF8DO,GAglF9D,KAjlG2DvC,GAklG3D,MA9iE4D0H,EA+iEhE,EAIA8N,EAAWpkB,KAAK,CAAG,CAAC,EACbokB,CACX,EA5sJ6DzjB,EA6sJ7DyjB,CAAAA,GAAWhrB,SAAS,CAACirB,cAAc,CApjLV,CAerBpe,QAAS,CAAA,EAiBTR,UAAW,CAAC,EAUZwQ,KAAM,CAAA,EAmBNlU,UAAW,KAQXjF,aAAc,CAUVoX,MAAO,SASP+S,aAAc,CAAA,EAUdlS,gBAAiB,sBASjBmB,YAAa,UAObE,aAAc,EAOdD,YAAa,EASbrK,UAAW,wBAQXmK,KAAM,CAAA,EA8CNf,UAAW,WACP,OAAO/U,EAAQ,IAAI,CAACiC,CAAC,EAAI,GAAK,IAAI,CAACA,CAAC,CAAG,kBAC3C,EAWArF,oBAAqB,CAAA,EAWrBiZ,SAAU,UAQVvB,QAAS,EAWTO,OAAQ,CAAA,EASR7Q,MAAO,UAWP4B,MAAO,CAEHohB,SAAU,QAEVC,WAAY,SAEZ9Y,MAAO,UACX,EAKAnN,QAAS,CAAA,EASTgT,cAAe,SASf7W,EAAG,EASH8E,EAAG,GACP,EAkHAgK,aAAc,CAuFViC,OAAQ,sBAORiB,YAAa,EASbf,KAAM,sBAONsC,EAAG,EAKHpB,KAAM,CACV,EAUAtG,oBAAqB,CAQjB9H,OAAQ,CAAC,EAIT0E,MAAO,CACH5D,OAAQ,UACRoM,KAAM,UACNF,OAAQ,UACR,eAAgB,CACpB,EACAvI,OAAQ,GACRF,OAAQ,SACRK,QAAS,CAAA,EACTJ,MAAO,EACX,EAyCAxE,OAAQ,CAAC,EAITjG,OAAQ,CACZ,EAy+JAgpB,GAAWhrB,SAAS,CAACwI,YAAY,CAAG,CAAC,MAAO,cAAe,OAAQ,SAAS,CAC5EmJ,GAA0B1L,OAAO,CAAC+kB,IA8F9BxsB,CArBOA,EAqERA,GAA+BA,CAAAA,EAA6B,CAAC,CAAA,GAhDjCyH,OAAO,CANlC,SAAiBxE,CAAK,EAIlB,OAHKA,EAAMqjB,UAAU,EACjBrjB,CAAAA,EAAMqjB,UAAU,CAAG,IAAIrmB,EAAUgD,EAAK,EAEnCA,CACX,EAgDAjD,EAA2BC,SAAS,CApChCA,EAA2B,WAMvB,SAASA,EAAUgD,CAAK,EACpB,IAAI,CAACwsB,OAAO,CAAG,EAAE,CACrB,IAAI,CAACxsB,KAAK,CAAGA,CACjB,CAyBA,OAZAhD,EAAUuB,SAAS,CAACkuB,SAAS,CAAG,SAAUC,CAAQ,EAC9C,IAAI,CAAC1sB,KAAK,CAACqjB,UAAU,CAACmJ,OAAO,CAAC7sB,IAAI,CAAC+sB,EACvC,EAIA1vB,EAAUuB,SAAS,CAAC8M,MAAM,CAAG,SAAU5L,CAAO,CAAEH,CAAM,EAClD,IAAIqL,EAAQ,IAAI,CAChB,IAAI,CAAC6hB,OAAO,CAAC9rB,OAAO,CAAC,SAAUgsB,CAAQ,EACnCA,EAASjuB,IAAI,CAACkM,EAAM3K,KAAK,CAAEP,EAASH,EACxC,EACJ,EACOtC,CACX,IAQyB,IAAI2vB,GAAoC5vB,EAcjE6vB,GAAsC,AAAC/tB,IAA+EyG,OAAO,CAAEunB,GAAuC,AAAChuB,IAA+E2Z,QAAQ,CAAEsU,GAAmC,AAACjuB,IAA+EK,IAAI,CAWvX6tB,GAAyB,CACzB7S,gBAAiB,SACjBmB,YAAa,SACbE,aAAc,SACd9H,MAAO,SACPC,KAAM,SACN4Y,SAAU,SACVnqB,OAAQ,SACR8d,KAAM,SACNzM,OAAQ,SACRwZ,MAAO,QACX,KAkBA,SAAyBC,CAAM,EAC3B,OAAOA,EAAOzM,MAAM,CAAC,SAAU0M,CAAK,EAChC,IAAIC,EAAWD,EAAME,IAAI,CAACC,WAAW,GACjCC,EAAUH,EAASI,GAAG,CACtBC,EAAUL,EAAS/pB,GAAG,CAGtBqqB,EAAiBX,GAAiCI,EAAME,IAAI,CAACK,cAAc,CAAE,GACjF,OAAOZ,GAAqCS,IAAYT,GAAqCW,IACzFN,EAAM/Z,KAAK,EAAKma,EAAUG,GAC1BP,EAAM/Z,KAAK,EAAKqa,EAAUC,GAE1B,CAACP,EAAME,IAAI,CAAC3tB,OAAO,CAACiuB,UAAU,AACtC,EAAE,CAAC,EAAE,AACT,EAkDIC,GAAsC,AAAC9uB,IAA+E2Z,QAAQ,CAAEoV,GAAmC,AAAC/uB,IAA+E0L,KAAK,CAma3NsjB,GAJD,CAC5BpsB,KAvZO,CAQP4hB,WAAY,CAMR9G,MAAO,CACHuR,aAAc,gBACdC,MAAO,QACP9X,OAAQ,SACR+X,QAAS,UACTC,UAAW,YACX7rB,MAAO,QACPmP,aAAc,gBACd2c,YAAa,UACbxa,KAAM,OACN2E,OAAQ,OACR5D,YAAa,aACbjB,OAAQ,aACRwZ,MAAO,QACP/M,KAAM,OACNhe,aAAc,gBACdE,OAAQ,SACR+X,gBAAiB,mBACjBiU,iBAAkB,oBAClB9S,YAAa,eACbE,aAAc,gBACdD,YAAa,eACbpQ,MAAO,QACP0O,QAAS,UACT0S,SAAU,YACV7Y,MAAO,QACPxI,OAAQ,SACR5B,OAAQ,eACZ,CACJ,CACJ,EA4WIga,WAvWa,CAWb+K,kBAAmB,gCA6BnBC,SAAU,CAQNC,iBAAkB,CAEdrd,UAAW,+BAEXsd,MAAO,SAAUpoB,CAAC,EAEd,IADIO,EACAumB,EAAS,AAA8B,OAA7BvmB,CAAAA,EAAK,IAAI,CAAC1G,KAAK,CAAC4G,OAAO,AAAD,GAAeF,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG8nB,cAAc,CAACroB,GAC1FsoB,EAAUxB,GAAUyB,GAA2CzB,EAAOrqB,KAAK,EAC3E+rB,EAAU1B,GAAUyB,GAA2CzB,EAAO/gB,KAAK,EAC3EmX,EAAa,IAAI,CAACrjB,KAAK,CAACP,OAAO,CAAC4jB,UAAU,CAE9C,GAAI,AAACoL,GAAYE,EAGjB,OAAO,IAAI,CAAC3uB,KAAK,CAAC6E,aAAa,CAAC+oB,GAAiC,CAC7D9G,QAAS,SACT1hB,KAAM,kBACNiE,OAAQ,CAAC,CACDjE,KAAM,SACNyG,MAAO,CACHpJ,EAAGgsB,EAAQtb,KAAK,CAChB5L,EAAGonB,EAAQxb,KAAK,CAChBvQ,MAAO6rB,EAAQrB,IAAI,CAACvqB,KAAK,CACzBqJ,MAAOyiB,EAAQvB,IAAI,CAACvqB,KAAK,AAC7B,EACAmT,EAAG,CACP,EAAE,AACV,EAAGqN,EAAWuL,kBAAkB,CAAEvL,EAAWgL,QAAQ,CAACC,gBAAgB,CACjEM,kBAAkB,EAC3B,EAEAC,MAAO,CACH,SAAU1oB,CAAC,CAAE5G,CAAU,EACnB,IAGIub,EAHAzR,EAAS9J,EAAWE,OAAO,CAAC4J,MAAM,CAClCylB,EAAiB,AAACzlB,GAAUA,CAAM,CAAC,EAAE,EAAIA,CAAM,CAAC,EAAE,CAACwC,KAAK,EACpD,CAAC,EAET,GAAI8hB,GAAoCmB,EAAclsB,KAAK,GACvD+qB,GAAoCmB,EAAc5iB,KAAK,EAAG,CAC1D,IAAIzD,EAAW,IAAI,CAACzI,KAAK,CAACyI,QAAQ,CAC9BhG,EAAI,IAAI,CAACzC,KAAK,CAAC4C,KAAK,CAACksB,EAAclsB,KAAK,CAAC,CACpCqK,QAAQ,CAAC6hB,EAAcrsB,CAAC,EAC7B8E,EAAI,IAAI,CAACvH,KAAK,CAACkM,KAAK,CAAC4iB,EAAc5iB,KAAK,CAAC,CACpCe,QAAQ,CAAC6hB,EAAcvnB,CAAC,EACjCuT,EAAW3X,KAAKC,GAAG,CAACD,KAAK2T,IAAI,CAAC3T,KAAK4rB,GAAG,CAACtmB,EAAWlB,EAAIpB,EAAEqC,MAAM,CAAG/F,EAAI0D,EAAEqC,MAAM,CAAE,GAC3ErF,KAAK4rB,GAAG,CAACtmB,EAAWhG,EAAI0D,EAAEmC,MAAM,CAAGf,EAAIpB,EAAEmC,MAAM,CAAE,IAAK,EAC9D,CACA/I,EAAW8L,MAAM,CAAC,CACdhC,OAAQ,CAAC,CACD2M,EAAG8E,CACP,EAAE,AACV,EACJ,EACH,AACL,EASAkU,kBAAmB,CACf/d,UAAW,gCACXsd,MAAO,SAAUpoB,CAAC,EAEd,IADIO,EACAumB,EAAS,AAA8B,OAA7BvmB,CAAAA,EAAK,IAAI,CAAC1G,KAAK,CAAC4G,OAAO,AAAD,GAAeF,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG8nB,cAAc,CAACroB,GAC1FsoB,EAAUxB,GAAUyB,GAA2CzB,EAAOrqB,KAAK,EAC3E+rB,EAAU1B,GAAUyB,GAA2CzB,EAAO/gB,KAAK,EAC3EmX,EAAa,IAAI,CAACrjB,KAAK,CAACP,OAAO,CAAC4jB,UAAU,CAC9C,GAAI,AAACoL,GAAYE,EAGjB,OAAO,IAAI,CAAC3uB,KAAK,CAAC6E,aAAa,CAAC+oB,GAAiC,CAC7D9G,QAAS,UACT1hB,KAAM,kBACNiE,OAAQ,CACJ,CACIjE,KAAM,UACNxC,MAAO6rB,EAAQrB,IAAI,CAACvqB,KAAK,CACzBqJ,MAAOyiB,EAAQvB,IAAI,CAACvqB,KAAK,CACzBN,OAAQ,CAAC,CACDE,EAAGgsB,EAAQtb,KAAK,CAChB5L,EAAGonB,EAAQxb,KAAK,AACpB,EAAG,CACC1Q,EAAGgsB,EAAQtb,KAAK,CAChB5L,EAAGonB,EAAQxb,KAAK,AACpB,EAAE,CACNsE,GAAI,CACR,EACH,AACL,EAAG4L,EAAWuL,kBAAkB,CAAEvL,EAAWgL,QAAQ,CAACW,iBAAiB,CAClEJ,kBAAkB,EAC3B,EACAC,MAAO,CACH,SAAU1oB,CAAC,CAAE5G,CAAU,EACnB,IAAIuH,EAASvH,EAAW8J,MAAM,CAAC,EAAE,CAC7BiL,EAAWxN,EAAO+Q,mBAAmB,CAAC/Q,EAAOvE,MAAM,CAAC,EAAE,EAC1DuE,EAAOiJ,cAAc,CAAC5J,EAAEqC,MAAM,CAAG8L,EAAS7R,CAAC,CAAE0D,EAAEmC,MAAM,CAAGgM,EAAS/M,CAAC,CAAE,GACpET,EAAOxH,MAAM,CAAC,CAAA,EAClB,EACA,SAAU6G,CAAC,CAAE5G,CAAU,EACnB,IAAIuH,EAASvH,EAAW8J,MAAM,CAAC,EAAE,CAC7BiL,EAAWxN,EAAO+Q,mBAAmB,CAAC/Q,EAAOvE,MAAM,CAAC,EAAE,EACtDyU,EAAYlQ,EAAO+Q,mBAAmB,CAAC/Q,EAAOvE,MAAM,CAAC,EAAE,EACvD0sB,EAAOnoB,EAAO0P,mBAAmB,CAAClC,EAClC0C,EACA7Q,EAAEqC,MAAM,CACRrC,EAAEmC,MAAM,EACR4D,EAAQpF,EAAO6Q,QAAQ,GACvBuX,EAAQ/rB,KAAK0T,GAAG,CAAC3K,EAAMkB,OAAO,CAAC,GAAKlB,EAAMkB,OAAO,CAAC6hB,IACtDnoB,EAAOmR,UAAU,CAACiX,GAClBpoB,EAAOxH,MAAM,CAAC,CAAA,EAClB,EACH,AACL,EAQA6vB,oBAAqB,CAEjBle,UAAW,kCAEXsd,MAAO,SAAUpoB,CAAC,EAEd,IADIO,EACAumB,EAAS,AAA8B,OAA7BvmB,CAAAA,EAAK,IAAI,CAAC1G,KAAK,CAAC4G,OAAO,AAAD,GAAeF,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG8nB,cAAc,CAACroB,GAC1FsoB,EAAUxB,GAAUyB,GAA2CzB,EAAOrqB,KAAK,EAC3E+rB,EAAU1B,GAAUyB,GAA2CzB,EAAO/gB,KAAK,EAE/E,GAAI,AAACuiB,GAAYE,GAGjB,IAAIlsB,EAAIgsB,EAAQtb,KAAK,CACjB5L,EAAIonB,EAAQxb,KAAK,CACjBvQ,EAAQ6rB,EAAQrB,IAAI,CAACvqB,KAAK,CAC1BqJ,EAAQyiB,EAAQvB,IAAI,CAACvqB,KAAK,CAC1BwgB,EAAa,IAAI,CAACrjB,KAAK,CAACP,OAAO,CAAC4jB,UAAU,CAC9C,OAAO,IAAI,CAACrjB,KAAK,CAAC6E,aAAa,CAAC+oB,GAAiC,CAC7D9G,QAAS,YACT1hB,KAAM,kBACNiE,OAAQ,CAAC,CACDjE,KAAM,OACN7C,OAAQ,CACJ,CAAEK,MAAOA,EAAOsJ,MAAOA,EAAOzJ,EAAGA,EAAG8E,EAAGA,CAAE,EACzC,CAAE3E,MAAOA,EAAOsJ,MAAOA,EAAOzJ,EAAGA,EAAG8E,EAAGA,CAAE,EACzC,CAAE3E,MAAOA,EAAOsJ,MAAOA,EAAOzJ,EAAGA,EAAG8E,EAAGA,CAAE,EACzC,CAAE3E,MAAOA,EAAOsJ,MAAOA,EAAOzJ,EAAGA,EAAG8E,EAAGA,CAAE,EACzC,CAAEqF,QAAS,GAAI,EAClB,AACL,EAAE,AACV,EAAGyW,EACEuL,kBAAkB,CAAEvL,EACpBgL,QAAQ,CACRc,mBAAmB,CACnBP,kBAAkB,GAC3B,EAEAC,MAAO,CACH,SAAU1oB,CAAC,CAAE5G,CAAU,EAEnB,IADImH,EACA2C,EAAS9J,EAAWE,OAAO,CAAC4J,MAAM,CAClC9G,EAAU,AAAC8G,GAAUA,CAAM,CAAC,EAAE,EAAIA,CAAM,CAAC,EAAE,CAAC9G,MAAM,EAC9C,EAAE,CACN0qB,EAAS,AAA8B,OAA7BvmB,CAAAA,EAAK,IAAI,CAAC1G,KAAK,CAAC4G,OAAO,AAAD,GAAeF,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG8nB,cAAc,CAACroB,GAC1FsoB,EAAUxB,GAAUyB,GAA2CzB,EAAOrqB,KAAK,EAC3E+rB,EAAU1B,GAAUyB,GAA2CzB,EAAO/gB,KAAK,EAC/E,GAAIuiB,GAAWE,EAAS,CACpB,IAAIlsB,EAAIgsB,EAAQtb,KAAK,CACjB5L,EAAIonB,EAAQxb,KAAK,AAErB5Q,CAAAA,CAAM,CAAC,EAAE,CAACE,CAAC,CAAGA,EAEdF,CAAM,CAAC,EAAE,CAACE,CAAC,CAAGA,EACdF,CAAM,CAAC,EAAE,CAACgF,CAAC,CAAGA,EAEdhF,CAAM,CAAC,EAAE,CAACgF,CAAC,CAAGA,EACdhI,EAAW8L,MAAM,CAAC,CACdhC,OAAQ,CAAC,CACD9G,OAAQA,CACZ,EAAE,AACV,EACJ,CACJ,EACH,AACL,EAOA6sB,gBAAiB,CAEbne,UAAW,8BAEXsd,MAAO,SAAUpoB,CAAC,EAEd,IADIO,EACAumB,EAAS,AAA8B,OAA7BvmB,CAAAA,EAAK,IAAI,CAAC1G,KAAK,CAAC4G,OAAO,AAAD,GAAeF,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG8nB,cAAc,CAACroB,GAC1FsoB,EAAUxB,GAAUyB,GAA2CzB,EAAOrqB,KAAK,EAC3E+rB,EAAU1B,GAAUyB,GAA2CzB,EAAO/gB,KAAK,EAC3EmX,EAAa,IAAI,CAACrjB,KAAK,CAACP,OAAO,CAAC4jB,UAAU,CAE9C,GAAI,AAACoL,GAAYE,EAGjB,OAAO,IAAI,CAAC3uB,KAAK,CAAC6E,aAAa,CAAC+oB,GAAiC,CAC7D9G,QAAS,QACT1hB,KAAM,kBACNnD,aAAc,CACVoW,OAAQ,UACR8C,SAAU,OACVC,KAAM,CAAA,CACV,EACAjZ,OAAQ,CAAC,CACD0J,MAAO,CACHjJ,MAAO6rB,EAAQrB,IAAI,CAACvqB,KAAK,CACzBqJ,MAAOyiB,EAAQvB,IAAI,CAACvqB,KAAK,CACzBJ,EAAGgsB,EAAQtb,KAAK,CAChB5L,EAAGonB,EAAQxb,KAAK,AACpB,CACJ,EAAE,AACV,EAAGkQ,EACEuL,kBAAkB,CAAEvL,EACpBgL,QAAQ,CACRe,eAAe,CACfR,kBAAkB,EAC3B,CACJ,CACJ,EAmDApoB,OAAQ,CAAC,EAcTooB,mBAAoB,CAChBhkB,UAAW,CACPykB,MAAO,CACX,CACJ,CACJ,CASA,EAgBI1E,GAAa,AAAC9rB,IAA+E8rB,UAAU,CAEvG2E,GAA4B,AAACnX,KAA2HE,MAAM,CAE9JkX,GAA8B,AAAC1wB,IAA+E+oB,QAAQ,CAAE4H,GAAyB,AAAC3wB,IAA+E0G,GAAG,CAAEkqB,GAAM,AAAC5wB,IAA+E4wB,GAAG,CAG7OC,GA/dtF,SAAsB3xB,CAAG,CAAEoV,CAAK,EAC5B,IAAIwc,EAAiB5C,EAAsB,CAAChvB,EAAI,CAC5C6xB,EAAY,OAAOzc,EAIvB,OAHIyZ,GAAoC+C,IACpCC,CAAAA,EAAYD,CAAa,EAEtB,CAAA,CACH,OAAU,OACV,OAAU,SACV,QAAW,UACf,CAAA,CAAC,CAACC,EAAU,AAChB,EAsdIC,GAA8B,AAAChxB,IAA+EC,QAAQ,CAAEe,GAAO,AAAChB,IAA+EgB,IAAI,CAAEiwB,GAA6B,AAACjxB,IAA+EyG,OAAO,CAAEyqB,GAA+B,AAAClxB,IAA+EI,SAAS,CAAE+wB,GAA6B,AAACnxB,IAA+Egf,OAAO,CAAEoS,GAAa,AAACpxB,IAA+EoxB,UAAU,CAAEC,GAA8B,AAACrxB,IAA+E2Z,QAAQ,CAAE2X,GAA8B,AAACtxB,IAA+EsQ,QAAQ,CAAEihB,GAA2B,AAACvxB,IAA+E0L,KAAK,CAAE8lB,GAAgC,AAACxxB,IAA+E8G,UAAU,CAAE2qB,GAA0B,AAACzxB,IAA+EK,IAAI,CAAEqxB,GAAgC,AAAC1xB,IAA+EipB,UAAU,CAgCh3C,SAAS0I,KACD,IAAI,CAACxwB,KAAK,CAACqlB,kBAAkB,EAC7B,IAAI,CAACrlB,KAAK,CAACqlB,kBAAkB,CAACoL,kBAAkB,EAExD,CAIA,SAASC,KACD,IAAI,CAACrL,kBAAkB,EACvB,IAAI,CAACA,kBAAkB,CAACtkB,OAAO,EAEvC,CAIA,SAAS4vB,KACL,IAAIlxB,EAAU,IAAI,CAACA,OAAO,CACtBA,GAAWA,EAAQ4jB,UAAU,EAAI5jB,EAAQ4jB,UAAU,CAACgL,QAAQ,GAC5D,IAAI,CAAChJ,kBAAkB,CAAG,IAAIuL,GAAmB,IAAI,CAAEnxB,EAAQ4jB,UAAU,EACzE,IAAI,CAACgC,kBAAkB,CAACwL,UAAU,GAClC,IAAI,CAACxL,kBAAkB,CAACyL,UAAU,GAE1C,CAIA,SAASC,KACL,IAAI1L,EAAqB,IAAI,CAACA,kBAAkB,CAC5C2L,EAAoB,0BACxB,GAAI,IAAI,EAAI3L,EAAoB,CAG5B,IAAI4L,EAAmB,CAAA,EAMvB,GALA,IAAI,CAACtuB,MAAM,CAACjC,OAAO,CAAC,SAAUiC,CAAM,EAC5B,CAACA,EAAOlD,OAAO,CAACiuB,UAAU,EAAI/qB,EAAOyI,OAAO,EAC5C6lB,CAAAA,EAAmB,CAAA,CAAG,CAE9B,GACI,IAAI,CAAC5L,kBAAkB,EACvB,IAAI,CAACA,kBAAkB,CAAClJ,SAAS,EACjC,IAAI,CAACkJ,kBAAkB,CAAClJ,SAAS,CAAC,EAAE,CAAE,CACtC,IAAI+U,EAAc,IAAI,CAAC7L,kBAAkB,CAAClJ,SAAS,CAAC,EAAE,CACtDkU,GAA8BhL,EAAmB8L,eAAe,CAAE,SAAUhe,CAAK,CAAEpV,CAAG,EAGlF,IAAIqzB,EAAaF,EAAYrQ,gBAAgB,CAAC,IAAM9iB,GACpD,GAAIqzB,EACA,IAAK,IAAIxwB,EAAI,EAAGA,EAAIwwB,EAAWxvB,MAAM,CAAEhB,IAAK,CACxC,IAAI4I,EAAS4nB,CAAU,CAACxwB,EAAE,CACtBywB,EAAM7nB,EAAOyH,SAAS,AACtBkC,AAAsB,CAAA,WAAtBA,EAAMme,WAAW,CAGsB,KAAnCD,EAAIrqB,OAAO,CAACgqB,IACZxnB,EAAO6T,SAAS,CAACC,MAAM,CAAC0T,GAGtBC,EAOiC,KAAnCI,EAAIrqB,OAAO,CAACgqB,IACZxnB,EAAO6T,SAAS,CAACC,MAAM,CAAC0T,GAPW,KAAnCK,EAAIrqB,OAAO,CAACgqB,IACZxnB,CAAAA,EAAOyH,SAAS,EAAI,IAAM+f,CAAgB,CAStD,CAER,EACJ,CACJ,CACJ,CAIA,SAASO,KACL,IAAI,CAACd,kBAAkB,EAC3B,CAIA,SAASe,KACL,IAAI,CAAC9L,qBAAqB,CAAG,IACjC,CAKA,SAAS+L,GAAqBC,CAAc,EACxC,IAqDIC,EACAC,EAtDAC,EAAgBH,EAAenzB,SAAS,CAACirB,cAAc,CAAChjB,MAAM,EAC1DkrB,EAAenzB,SAAS,CAACirB,cAAc,CAAChjB,MAAM,CAACsrB,KAAK,CAK5D,SAASC,EAAmBC,CAAc,EACtC,IAAIzyB,EAAa,IAAI,CACjB8jB,EAAa9jB,EAAWS,KAAK,CAACqlB,kBAAkB,CAChD4M,EAAiB5O,EAAW+B,gBAAgB,CAC5CyM,GACAA,EAAcpzB,IAAI,CAACc,EAAYyyB,GAE/BC,IAAmB1yB,GAEnB8jB,EAAWoN,kBAAkB,GAC7BpN,EAAW+B,gBAAgB,CAAG7lB,EAC9BA,EAAW4R,0BAA0B,CAAC,CAAA,GACtC4e,GAA6B1M,EAAY,YAAa,CAClD9jB,WAAYA,EACZ8oB,SAAU,qBACV5oB,QAAS4jB,EAAW6O,kBAAkB,CAAC3yB,GACvC+oB,SAAU,SAAU6J,CAAI,EACpB,GAAIA,AAAoB,WAApBA,EAAKhM,UAAU,CACf9C,EAAW+B,gBAAgB,CAAG,CAAA,EAC9B/B,EAAWrjB,KAAK,CAACkF,gBAAgB,CAAC3F,OAEjC,CACD,IAAI2oB,EAAS,CAAC,EACd7E,EAAW+O,eAAe,CAACD,EAAKtS,MAAM,CAAEqI,GACxC7E,EAAWoN,kBAAkB,GAC7B,IAAIvC,EAAchG,EAAOgG,WAAW,AACJ,CAAA,YAA5B3uB,EAAWE,OAAO,CAAC2F,IAAI,GAGvB8oB,EAAYmE,UAAU,CAACC,OAAO,CAAIpE,AACb,IADaA,EAAYmE,UAAU,CACnD5d,WAAW,CAChByZ,EAAYqE,UAAU,CAACD,OAAO,CAAIpE,AACb,IADaA,EAAYqE,UAAU,CACnD9d,WAAW,EAEpBlV,EAAW8L,MAAM,CAAC6c,EACtB,CACJ,CACJ,IAIA6H,GAA6B1M,EAAY,cAG7C2O,EAAe5M,gBAAgB,CAAG,CAAA,CACtC,CAqBAgL,GAAyB,CAAA,EAAMsB,EAAenzB,SAAS,CAACirB,cAAc,CAAChjB,MAAM,CAAE,CAC3EsrB,MAAOC,EACPS,WAhBJ,SAAoBrsB,CAAC,EACjBwrB,EAAcxrB,EAAEssB,OAAO,CAAC,EAAE,CAACC,OAAO,CAClCd,EAAczrB,EAAEssB,OAAO,CAAC,EAAE,CAACE,OAAO,AACtC,EAcIC,SAVJ,SAA2BzsB,CAAC,EACTwrB,GAAcxuB,KAAK2T,IAAI,CAAC3T,KAAK4rB,GAAG,CAAC4C,EAAcxrB,EAAE0sB,cAAc,CAAC,EAAE,CAACH,OAAO,CAAE,GACnFvvB,KAAK4rB,GAAG,CAAC6C,EAAczrB,EAAE0sB,cAAc,CAAC,EAAE,CAACF,OAAO,CAAE,KAAO,GAE/DZ,EAAmBtzB,IAAI,CAAC,IAAI,CAAE0H,EAEtC,CAKA,EACJ,CASA,IAAIyqB,GAAoC,WAMpC,SAASA,EAAmB5wB,CAAK,CAAEP,CAAO,EACtC,IAAI,CAAC0xB,eAAe,CAAG,KAAK,EAC5B,IAAI,CAACnxB,KAAK,CAAGA,EACb,IAAI,CAACP,OAAO,CAAGA,EACf,IAAI,CAACqzB,cAAc,CAAG,EAAE,CACxB,IAAI,CAAC3W,SAAS,CACV,IAAI,CAACnc,KAAK,CAACmc,SAAS,CAAC4W,sBAAsB,CAAC,IAAI,CAACtzB,OAAO,CAAC2uB,iBAAiB,EAAI,IAC7E,IAAI,CAACjS,SAAS,CAACva,MAAM,EACtB,CAAA,IAAI,CAACua,SAAS,CAAGqT,GAAuBuD,sBAAsB,CAAC,IAAI,CAACtzB,OAAO,CAAC2uB,iBAAiB,EAAI,GAAE,CAE3G,CAgkBA,OA1jBAwC,EAAmBpsB,OAAO,CAAG,SAAUC,CAAe,CAAEC,CAAU,EAC1D6rB,GAA8BhB,GAA6B,wBAC3DM,GAA4BprB,EAAiB,SAAU+rB,IAEvDiB,GAAqBhtB,GAErB4rB,GAA8B5rB,EAAgBU,KAAK,CAAE,SAAUusB,CAAc,EACzED,GAAqBC,EACzB,GACA7B,GAA4BnrB,EAAY,UAAWgsB,IACnDb,GAA4BnrB,EAAY,OAAQisB,IAChDd,GAA4BnrB,EAAY,SAAUqsB,IAClDlB,GAA4Be,EAAoB,aAAcW,IAC9D1B,GAA4Be,EAAoB,iBAAkBY,IAClE7G,GAAWkD,IAEnB,EAMA+C,EAAmBryB,SAAS,CAACy0B,SAAS,CAAG,SAAU7sB,CAAC,EAEhD,IADIO,EACAumB,EAAS,AAA8B,OAA7BvmB,CAAAA,EAAK,IAAI,CAAC1G,KAAK,CAAC4G,OAAO,AAAD,GAAeF,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG8nB,cAAc,CAACroB,GAC9F,MAAO,CACH8mB,GAAUgG,GAAmChG,EAAOrqB,KAAK,EACzDqqB,GAAUgG,GAAmChG,EAAO/gB,KAAK,EAC5D,AACL,EAOA0kB,EAAmBryB,SAAS,CAACsyB,UAAU,CAAG,WACtC,IAAIxN,EAAa,IAAI,CACjBrjB,EAAQqjB,EAAWrjB,KAAK,CACxBkzB,EAAoB7P,EAAWlH,SAAS,CACxC1c,EAAU4jB,EAAW5jB,OAAO,AAEhC4jB,CAAAA,EAAW8N,eAAe,CAAG,CAAC,EAC9Bd,GAA+B5wB,EAAQ4uB,QAAQ,EAAI,CAAC,EAAI,SAAUlb,CAAK,EACnEkQ,EAAW8N,eAAe,CAAChe,EAAMlC,SAAS,CAAC,CAAGkC,CAClD,GAEA,EAAE,CAACzS,OAAO,CAACjC,IAAI,CAACy0B,EAAmB,SAAUC,CAAY,EACrD9P,EAAWyP,cAAc,CAACnzB,IAAI,CAACkwB,GAA4BsD,EAAc,QAAS,SAAUnyB,CAAK,EAC7F,IAAIqtB,EAAWhL,EAAW+P,eAAe,CAACD,EACtCnyB,GACAqtB,GACC,CAACA,EAAS7kB,MAAM,CAAC6T,SAAS,CACtBgW,QAAQ,CAAC,4BACdhQ,EAAWiQ,mBAAmB,CAACjF,EAAS7kB,MAAM,CAAE6kB,EAAS7nB,MAAM,CAAExF,EAEzE,GACJ,GACAqvB,GAA+B5wB,EAAQ+G,MAAM,EAAI,CAAC,EAAI,SAAUqf,CAAQ,CAAEpJ,CAAS,EAC3EwT,GAAWpK,IACXxC,EAAWyP,cAAc,CAACnzB,IAAI,CAACkwB,GAA4BxM,EAAY5G,EAAWoJ,EAAU,CAAExf,QAAS,CAAA,CAAM,GAErH,GACAgd,EAAWyP,cAAc,CAACnzB,IAAI,CAACkwB,GAA4B7vB,EAAMmc,SAAS,CAAE,QAAS,SAAUhW,CAAC,EACxF,CAACnG,EAAM2G,WAAW,EAClB3G,EAAM+I,YAAY,CAAC5C,EAAEqC,MAAM,CAAGxI,EAAMgJ,QAAQ,CAAE7C,EAAEmC,MAAM,CAAGtI,EAAMiJ,OAAO,CAAE,CACpEC,gBAAiB,CAAA,CACrB,IACAma,EAAWkQ,kBAAkB,CAAC,IAAI,CAAEptB,EAE5C,IACAkd,EAAWyP,cAAc,CAACnzB,IAAI,CAACkwB,GAA4B7vB,EAAMmc,SAAS,CAAE,AAACtd,IAA+E2G,aAAa,CAAG,YAAc,YAAa,SAAUW,CAAC,EAC9Mkd,EAAWmQ,0BAA0B,CAAC,IAAI,CAAErtB,EAChD,EAAG,AAACtH,IAA+E2G,aAAa,CAAG,CAAEa,QAAS,CAAA,CAAM,EAAI,KAAK,GACjI,EAOAuqB,EAAmBryB,SAAS,CAACuyB,UAAU,CAAG,WACtC,IAAIzN,EAAa,IAAI,CACrBsJ,GACKnoB,OAAO,CAAC,IAAI,CAACxE,KAAK,EAAEqjB,UAAU,CAC9BoJ,SAAS,CAAC,SAAUhtB,CAAO,EAC5B4jB,EAAWhY,MAAM,CAAC5L,EACtB,EACJ,EAiBAmxB,EAAmBryB,SAAS,CAAC+0B,mBAAmB,CAAG,SAAU9pB,CAAM,CAAEhD,CAAM,CAAEitB,CAAU,EACnF,IACIzzB,EAAQqjB,AADK,IAAI,CACErjB,KAAK,CACxB0zB,EAAe1zB,EAAME,QAAQ,CAACyzB,UAAU,CACxCC,EAAqB,CAAA,CACrBvQ,CAJa,IAAI,CAINqC,qBAAqB,GAC5BrC,AALS,IAAI,CAKFqC,qBAAqB,CAACrI,SAAS,GAAK7T,EAAO6T,SAAS,EAC/DuW,CAAAA,EAAqB,CAAA,CAAI,EAE7B7D,GARa,IAAI,CAQwB,iBAAkB,CAAEvmB,OAAQ6Z,AARxD,IAAI,CAQ+DqC,qBAAqB,AAAC,GAClGrC,AATS,IAAI,CASFwQ,SAAS,GAEhBxQ,AAXK,IAAI,CAWEyQ,kBAAkB,EAC7BzQ,AAAuC,gBAAvCA,AAZK,IAAI,CAYEyQ,kBAAkB,CAAC/vB,IAAI,EAClC/D,EAAMkF,gBAAgB,CAACme,AAblB,IAAI,CAayByQ,kBAAkB,EAExDzQ,AAfS,IAAI,CAeF0Q,cAAc,CAAG1Q,AAfnB,IAAI,CAe0BwQ,SAAS,CAAG,CAAA,IAGvDD,GACAvQ,AAnBa,IAAI,CAmBN2Q,cAAc,CAAGxtB,EAC5B6c,AApBa,IAAI,CAoBNqC,qBAAqB,CAAGlc,EACnCumB,GArBa,IAAI,CAqBwB,eAAgB,CAAEvmB,OAAQA,CAAO,GAEtEhD,EAAO+J,IAAI,EACX/J,EAAO+J,IAAI,CAAC9R,IAAI,CAxBP,IAAI,CAwBgB+K,EAAQiqB,GAErCjtB,CAAAA,EAAO+nB,KAAK,EAAI/nB,EAAOqoB,KAAK,AAAD,GAC3B7uB,EAAME,QAAQ,CAACyzB,UAAU,CAACziB,QAAQ,CAAC,0BAIvClR,EAAMmoB,UAAU,EAAI3e,EAAO6T,SAAS,CAACC,MAAM,CAAC,qBAC5CoW,EAAaO,WAAW,CAAC,wBACzB5Q,AAjCa,IAAI,CAiCNwQ,SAAS,CAAG,CAAA,EACvBxQ,AAlCa,IAAI,CAkCN0Q,cAAc,CAAG,CAAA,EAC5B1Q,AAnCa,IAAI,CAmCN2Q,cAAc,CAAG,KAEpC,EAeApD,EAAmBryB,SAAS,CAACg1B,kBAAkB,CAAG,SAAUvzB,CAAK,CAAEyzB,CAAU,EACzEzzB,EAAQ,IAAI,CAACA,KAAK,CAClB,IACIolB,EAAmB/B,AADN,IAAI,CACa+B,gBAAgB,CAC9C4O,EAAiB3Q,AAFJ,IAAI,CAEW2Q,cAAc,CAC1CN,EAAe1zB,EAAME,QAAQ,CAACyzB,UAAU,CACxCvO,IAGI,AAACA,EAAiBze,WAAW,EAC5B8sB,EAAWrO,gBAAgB,GAE5BqO,EAAW3sB,MAAM,CAACoX,UAAU,EAE3BgW,AA1YjB,SAAyBC,CAAE,CAAE5wB,CAAC,EAC1B,IAAI6wB,EAAe3E,GAAI4E,OAAO,CAAC91B,SAAS,CACpC+1B,EAAiBF,EAAaG,OAAO,EACjCH,EAAaI,iBAAiB,EAC9BJ,EAAaK,qBAAqB,CACtCC,EAAM,KACV,GAAIN,EAAaO,OAAO,CACpBD,EAAMN,EAAaO,OAAO,CAACl2B,IAAI,CAAC01B,EAAI5wB,QAGpC,EAAG,CACC,GAAI+wB,EAAe71B,IAAI,CAAC01B,EAAI5wB,GACxB,OAAO4wB,EAEXA,EAAKA,EAAGS,aAAa,EAAIT,EAAGjW,UAAU,AAC1C,OAASiW,AAAO,OAAPA,GAAeA,AAAgB,IAAhBA,EAAGU,QAAQ,CAAQ,CAE/C,OAAOH,CACX,EAwXiCjB,EAAW3sB,MAAM,CAAE,qBAG/Bse,EAAiBze,WAAW,EAEjC4e,WAAW,WACPH,EAAiBze,WAAW,CAAG,CAAA,CACnC,EAAG,GANHopB,GAbS,IAAI,CAa4B,eAS5CiE,GAAmBA,EAAezF,KAAK,GAGvClL,AAzBY,IAAI,CAyBLwQ,SAAS,EAsBrBxQ,AA/Ca,IAAI,CA+CNwQ,SAAS,CAACJ,EAAYpQ,AA/CpB,IAAI,CA+C2ByQ,kBAAkB,EAC1DzQ,AAhDS,IAAI,CAgDFwL,KAAK,GAChBxL,AAjDS,IAAI,CAiDFyR,SAAS,GAChBd,EAAenF,KAAK,CAACxL,AAlDhB,IAAI,CAkDuByR,SAAS,CAAC,CAE1CzR,AApDK,IAAI,CAoDE0Q,cAAc,CAAG1Q,AApDvB,IAAI,CAoD8BwQ,SAAS,CAAGG,EAAenF,KAAK,CAACxL,AApDnE,IAAI,CAoD0EyR,SAAS,CAAC,EAG7F/E,GAvDK,IAAI,CAuDgC,iBAAkB,CAAEvmB,OAAQ6Z,AAvDhE,IAAI,CAuDuEqC,qBAAqB,AAAC,GACtGgO,EAAaO,WAAW,CAAC,wBAErBD,EAAee,GAAG,EAClBf,EAAee,GAAG,CAACt2B,IAAI,CA3DtB,IAAI,CA2D+Bg1B,EAAYpQ,AA3D/C,IAAI,CA2DsDyQ,kBAAkB,EAEjFzQ,AA7DK,IAAI,CA6DEwQ,SAAS,CAAG,CAAA,EACvBxQ,AA9DK,IAAI,CA8DE0Q,cAAc,CAAG,CAAA,EAC5B1Q,AA/DK,IAAI,CA+DE2Q,cAAc,CAAG,SApCpC3Q,AA3Ba,IAAI,CA2BNyQ,kBAAkB,CAAGE,EAAezF,KAAK,CAAC9vB,IAAI,CA3B5C,IAAI,CA2BqDg1B,GAElEpQ,AA7BS,IAAI,CA6BFyQ,kBAAkB,EAAIE,EAAenF,KAAK,EACrDxL,AA9BS,IAAI,CA8BFyR,SAAS,CAAG,EACvBzR,AA/BS,IAAI,CA+BFwL,KAAK,CAAG,CAAA,EACnBxL,AAhCS,IAAI,CAgCF0Q,cAAc,CAAG1Q,AAhCnB,IAAI,CAgC0BwQ,SAAS,CAC5CG,EAAenF,KAAK,CAACxL,AAjChB,IAAI,CAiCuByR,SAAS,CAAC,GAG9C/E,GApCS,IAAI,CAoC4B,iBAAkB,CAAEvmB,OAAQ6Z,AApC5D,IAAI,CAoCmEqC,qBAAqB,AAAC,GACtGgO,EAAaO,WAAW,CAAC,wBACzB5Q,AAtCS,IAAI,CAsCFwL,KAAK,CAAG,CAAA,EACnBxL,AAvCS,IAAI,CAuCF2Q,cAAc,CAAG,KAExBA,EAAee,GAAG,EAClBf,EAAee,GAAG,CAACt2B,IAAI,CA1ClB,IAAI,CA0C2Bg1B,EAAYpQ,AA1C3C,IAAI,CA0CkDyQ,kBAAkB,IAyB7F,EAaAlD,EAAmBryB,SAAS,CAACi1B,0BAA0B,CAAG,SAAUwB,CAAU,CAAEC,CAAS,EACjF,IAAI,CAAClB,cAAc,EACnB,IAAI,CAACA,cAAc,CAACkB,EAAW,IAAI,CAACnB,kBAAkB,CAE9D,EAiBAlD,EAAmBryB,SAAS,CAAC6zB,eAAe,CAAG,SAAUvS,CAAM,CAAEqI,CAAM,EAoCnE,OAnCAmI,GAA8BxQ,EAAQ,SAAU1M,CAAK,CAAE+hB,CAAK,EACxD,IAAIC,EAAcC,WAAWjiB,GACzBwB,EAAOugB,EAAMxS,KAAK,CAAC,KACnB2S,EAAa1gB,EAAK/S,MAAM,CAAG,EAQ/B,IANIsuB,GAA4BiF,IAC3BhiB,EAAMqJ,KAAK,CAAC,WACZ0Y,EAAM1Y,KAAK,CAAC,YACbrJ,CAAAA,EAAQgiB,CAAU,EAGlBhiB,AAAU,cAAVA,EAAuB,CACvB,IAAImiB,EAAWpN,EACfvT,EAAKjU,OAAO,CAAC,SAAUuf,CAAI,CAAEpd,CAAK,EAC9B,GAAIod,AAAS,cAATA,GAAwBA,AAAS,gBAATA,EAAwB,CAChD,IAAIsV,EAAWjF,GAAwB3b,CAAI,CAAC9R,EAAQ,EAAE,CAAE,GACpDwyB,CAAAA,IAAexyB,EAEfyyB,CAAQ,CAACrV,EAAK,CAAG9M,GAEXmiB,CAAQ,CAACrV,EAAK,EAEpBqV,CAAAA,CAAQ,CAACrV,EAAK,CAAGsV,EAAS/Y,KAAK,CAAC,OAC5B,EAAE,CACF,CAAC,CAAA,EAKL8Y,EAAWA,CAAQ,CAACrV,EAAK,CAEjC,CACJ,EACJ,CACJ,GACOiI,CACX,EAMA0I,EAAmBryB,SAAS,CAACkyB,kBAAkB,CAAG,WAC1C,IAAI,CAACrL,gBAAgB,GACrB,IAAI,CAACA,gBAAgB,CAACjU,0BAA0B,CAAC,CAAA,GACjD,IAAI,CAACiU,gBAAgB,CAAG,CAAA,EAEhC,EAaAwL,EAAmBryB,SAAS,CAAC2zB,kBAAkB,CAAG,SAAU3yB,CAAU,EAClE,IAAIE,EAAUF,EAAWE,OAAO,CAC5B+1B,EAAY5E,EAAmB6E,mBAAmB,CAClDC,EAAkBF,EAAUG,aAAa,CACzCvwB,EAAOkrB,GAAwB7wB,EAAQ2F,IAAI,CAC3C3F,EAAQ4J,MAAM,EAAI5J,EAAQ4J,MAAM,CAAC,EAAE,EAC/B5J,EAAQ4J,MAAM,CAAC,EAAE,CAACjE,IAAI,CAC1B3F,EAAQ0C,MAAM,EAAI1C,EAAQ0C,MAAM,CAAC,EAAE,EAC/B1C,EAAQ0C,MAAM,CAAC,EAAE,CAACiD,IAAI,CAAE,SAC5BwwB,EAAehF,EAAmBiF,sBAAsB,CAACp2B,EAAQqnB,OAAO,CAAC,EAAI,EAAE,CAC/EgP,EAAgB,CACZhP,QAASrnB,EAAQqnB,OAAO,CACxB1hB,KAAMA,CACV,EAoBJ,SAAS2wB,EAASvX,CAAM,CAAEzgB,CAAG,CAAEi4B,CAAe,CAAEthB,CAAM,CAAEuhB,CAAS,EAC7D,IAAIC,EACAF,GACAlG,GAA2BtR,IAC3BoX,AAA8B,KAA9BA,EAAa5uB,OAAO,CAACjJ,IACpB,CAAA,AAACi4B,CAAAA,EAAgBhvB,OAAO,EACrBgvB,EAAgBhvB,OAAO,CAACjJ,EAAG,GAAM,GACjCi4B,CAAe,CAACj4B,EAAI,EACpBi4B,AAAoB,CAAA,IAApBA,CAAuB,IAGvBhG,GAA2BxR,IAC3B9J,CAAM,CAAC3W,EAAI,CAAG,EAAE,CAChBygB,EAAO9d,OAAO,CAAC,SAAUy1B,CAAW,CAAEv1B,CAAC,EAC9BuvB,GAA4BgG,IAM7BzhB,CAAM,CAAC3W,EAAI,CAAC6C,EAAE,CAAG,CAAC,EAClByvB,GAA8B8F,EAAa,SAAUC,CAAY,CAAEC,CAAS,EACxEN,EAASK,EAAcC,EAAWX,CAAe,CAAC33B,EAAI,CAAE2W,CAAM,CAAC3W,EAAI,CAAC6C,EAAE,CAAE7C,EAC5E,IAPAg4B,EAASI,EAAa,EAAGT,CAAe,CAAC33B,EAAI,CAAE2W,CAAM,CAAC3W,EAAI,CAAEA,EASpE,IAEKoyB,GAA4B3R,IACjC0X,EAAa,CAAC,EACVlG,GAA2Btb,IAC3BA,EAAO/U,IAAI,CAACu2B,GACZA,CAAU,CAACn4B,EAAI,CAAG,CAAC,EACnBm4B,EAAaA,CAAU,CAACn4B,EAAI,EAG5B2W,CAAM,CAAC3W,EAAI,CAAGm4B,EAElB7F,GAA8B7R,EAAQ,SAAU4X,CAAY,CAAEC,CAAS,EACnEN,EAASK,EAAcC,EAAWt4B,AAAQ,IAARA,EAC9Bi4B,EACAN,CAAe,CAAC33B,EAAI,CAAEm4B,EAAYn4B,EAC1C,IAIIA,AAAQ,WAARA,EACA2W,CAAM,CAAC3W,EAAI,CAAG,CACVuxB,GAA0B9Q,EAAQjf,EAAW4C,MAAM,CAAC,EAAE,CAACI,MAAM,CAAC,EAAE,EAAE+zB,QAAQ,GAC1E,OACH,CAEItG,GAA2Btb,GAChCA,EAAO/U,IAAI,CAAC,CAAC6e,EAAQkR,GAAgCuG,EAAWzX,GAAQ,EAGxE9J,CAAM,CAAC3W,EAAI,CAAG,CAACygB,EAAQkR,GAAgC3xB,EAAKygB,GAAQ,CAIpF,CAYA,OAXA6R,GAA8B5wB,EAAS,SAAU+e,CAAM,CAAEzgB,CAAG,EACpDA,AAAQ,gBAARA,GACA+3B,CAAa,CAAC/3B,EAAI,CAAG,CAAC,EACtBsyB,GAA8B5wB,CAAO,CAAC1B,EAAI,CAAE,SAAUw4B,CAAU,CAAEC,CAAO,EACrET,EAASQ,EAAYC,EAASd,EAAiBI,CAAa,CAAC/3B,EAAI,CAAEy4B,EACvE,IAGAT,EAASvX,EAAQzgB,EAAKy3B,CAAS,CAACpwB,EAAK,CAAE0wB,EAAe/3B,EAE9D,GACO+3B,CACX,EAiBAlF,EAAmBryB,SAAS,CAACk4B,oBAAoB,CAAG,SAAUta,CAAS,CAAEnb,CAAK,EAI1E,IAHA,IAEI01B,EAFAxwB,EAAUlF,EAAM8F,MAAM,CACtB6vB,EAAa,EAAE,CAEZzwB,GAAWA,EAAQ4L,OAAO,GAC7B4kB,CAAAA,EAAgB72B,GAAKqG,EAAS,QAAO,GAEjCywB,CAAAA,EAAaA,EAAW5K,MAAM,CAAC2K,EAC1BhU,KAAK,CAAC,KAEN9R,GAAG,CAAC,SAAUqP,CAAI,EAAI,MAAQ,CAACA,EAAM/Z,EAAQ,AAAG,GAAE,EAGvDA,AADJA,CAAAA,EAAUA,EAAQgY,UAAU,AAAD,IACX/B,KAIpB,OAAOwa,CACX,EAiBA/F,EAAmBryB,SAAS,CAAC60B,eAAe,CAAG,SAAUjX,CAAS,CAAEnb,CAAK,EACrE,IAGIqtB,EAHAhL,EAAa,IAAI,CAYrB,OARAsT,AAHiB,IAAI,CAACF,oBAAoB,CAACta,EACvCnb,GAEON,OAAO,CAAC,SAAUuQ,CAAS,EAC9BoS,EAAW8N,eAAe,CAAClgB,CAAS,CAAC,EAAE,CAAC,EAAI,CAACod,GAC7CA,CAAAA,EAAW,CACP7nB,OAAQ6c,EAAW8N,eAAe,CAAClgB,CAAS,CAAC,EAAE,CAAC,CAChDzH,OAAQyH,CAAS,CAAC,EAAE,AACxB,CAAA,CAER,GACOod,CACX,EAQAuC,EAAmBryB,SAAS,CAAC8M,MAAM,CAAG,SAAU5L,CAAO,EACnD,IAAI,CAACA,OAAO,CAAG2wB,GAAyB,CAAA,EAAM,IAAI,CAAC3wB,OAAO,CAAEA,GAC5D,IAAI,CAACm3B,YAAY,GACjB,IAAI,CAAC/F,UAAU,EACnB,EAOAD,EAAmBryB,SAAS,CAACq4B,YAAY,CAAG,WACxC,IAAI,CAAC9D,cAAc,CAACpyB,OAAO,CAAC,SAAUm2B,CAAQ,EAAI,OAAOA,GAAY,EACzE,EAKAjG,EAAmBryB,SAAS,CAACwC,OAAO,CAAG,WACnC,IAAI,CAAC61B,YAAY,EACrB,EAOAhG,EAAmB6E,mBAAmB,CAAG,CAGrCE,cAAe,CACX1zB,aAAc,CAAC,QAAS,SAAU,kBAAkB,CACpDE,OAAQ,CAAC,QAAQ,CACjBC,MAAO,CAAC,QAAQ,CAChB8I,MAAO,CAAC,WAAY,QAAQ,CAC5B4rB,WAAY,CAAC,OAAQ,cAAe,SAAS,CAC7CC,gBAAiB,CAAC,OAAQ,cAAe,SAAS,CAClDC,gBAAiB,CAAC,OAAQ,cAAe,SAAS,CAClDzlB,aAAc,CAAC,OAAQ,cAAe,SAAS,CAC/ClI,OAAQ,CAAC,OAAQ,cAAe,SAAS,CACzC4tB,KAAM,CAAC,cAAe,SAAS,CAC/B9I,iBAAkB,CAAC,CAAA,EAAK,CACxB3U,UAAW,CAAC,OAAQ,cAAe,SAAS,CAC5C+Y,WAAY,CAAC,cAAe,SAAS,CACrCF,WAAY,CAAC,cAAe,SAAS,AACzC,EAEApc,OAAQ,CAAC,SAAS,CAClB+X,QAAS,CAAC,SAAS,CACnBkJ,aAAc,EAAE,CAChB90B,MAAO,CAAC,eAAe,CAEvB+0B,QAAS,CAAC,aAAc,aAAc,aAAa,CAEnDC,UAAW,EAAE,CACbC,OAAQ,CAAC,aAAc,OAAQ,SAAS,CACxCC,UAAW,CAAC,kBAAmB,kBAAkB,CACjD5hB,KAAM,CAAC,SAAS,CAEhB6hB,YAAa,EAAE,CACfC,gBAAiB,CAAC,SAAU,eAAe,AAC/C,EAGA5G,EAAmBiF,sBAAsB,CAAG,CACxC5H,UAAW,CAAC,aAAc,aAAc,eAAe,CACvDD,QAAS,CAAC,eAAe,CACzB/X,OAAQ,CAAC,eAAe,AAC5B,EACO2a,CACX,IA8CI6G,GAAK54B,GACT44B,CAAAA,GAAElO,UAAU,CAAGkO,GAAElO,UAAU,EAjhDgCA,GAkhD3DkO,GAAE7G,kBAAkB,CAAG6G,GAAE7G,kBAAkB,EA1CwBA,GA2CnE6G,GAAElO,UAAU,CAAC/kB,OAAO,CAACizB,GAAEC,KAAK,CAAED,GAAE7G,kBAAkB,CAAE6G,GAAEE,OAAO,CAAEF,GAAEG,WAAW,EAC/C,IAAIj5B,GAAoBE,IAG3C,OADYH,EAAoB,OAAU,AAE3C,GAET"}