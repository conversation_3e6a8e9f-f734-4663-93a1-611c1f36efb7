{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts Gantt JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/current-date-indicator\n * @requires highcharts\n *\n * CurrentDateIndicator\n *\n * (c) 2010-2025 Lars A. <PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/current-date-indicator\", [[\"highcharts/highcharts\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/current-date-indicator\"] = factory(require(\"highcharts\"));\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ current_date_indicator_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es5/es-modules/Extensions/CurrentDateIndication.js\n/* *\n *\n *  (c) 2016-2025 Highsoft AS\n *\n *  Author: Lars A. V. Cabrera\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar composed = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).composed;\n\nvar addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, pushUnique = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pushUnique, wrap = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).wrap;\n/* *\n *\n *  Constants\n *\n * */\n/**\n * Show an indicator on the axis for the current date and time. Can be a\n * boolean or a configuration object similar to\n * [xAxis.plotLines](#xAxis.plotLines).\n *\n * @sample gantt/current-date-indicator/demo\n *         Current date indicator enabled\n * @sample gantt/current-date-indicator/object-config\n *         Current date indicator with custom options\n *\n * @declare   Highcharts.CurrentDateIndicatorOptions\n * @type      {boolean|CurrentDateIndicatorOptions}\n * @default   true\n * @extends   xAxis.plotLines\n * @excluding value\n * @product   gantt\n * @apioption xAxis.currentDateIndicator\n */\nvar defaultOptions = {\n    color: \"#ccd3ff\" /* Palette.highlightColor20 */,\n    width: 2,\n    /**\n     * @declare Highcharts.AxisCurrentDateIndicatorLabelOptions\n     */\n    label: {\n        /**\n         * Format of the label. This options is passed as the first argument to\n         * [dateFormat](/class-reference/Highcharts.Time#dateFormat) function.\n         *\n         * @type      {string|Intl.DateTimeFormatOptions}\n         * @product   gantt\n         * @apioption xAxis.currentDateIndicator.label.format\n         */\n        format: '%[abdYHM]',\n        formatter: function (value, format) {\n            return this.axis.chart.time.dateFormat(format || '', value, true);\n        },\n        rotation: 0,\n        /**\n         * @type {Highcharts.CSSObject}\n         */\n        style: {\n            /** @internal */\n            fontSize: '0.7em'\n        }\n    }\n};\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction compose(AxisClass, PlotLineOrBandClass) {\n    if (pushUnique(composed, 'CurrentDateIndication')) {\n        addEvent(AxisClass, 'afterSetOptions', onAxisAfterSetOptions);\n        addEvent(PlotLineOrBandClass, 'render', onPlotLineOrBandRender);\n        wrap(PlotLineOrBandClass.prototype, 'getLabelText', wrapPlotLineOrBandGetLabelText);\n    }\n}\n/**\n * @private\n */\nfunction onAxisAfterSetOptions() {\n    var options = this.options,\n        cdiOptions = options.currentDateIndicator;\n    if (cdiOptions) {\n        var plotLineOptions = typeof cdiOptions === 'object' ?\n                merge(defaultOptions,\n            cdiOptions) :\n                merge(defaultOptions);\n        plotLineOptions.value = Date.now();\n        plotLineOptions.className = 'highcharts-current-date-indicator';\n        if (!options.plotLines) {\n            options.plotLines = [];\n        }\n        options.plotLines.push(plotLineOptions);\n    }\n}\n/**\n * @private\n */\nfunction onPlotLineOrBandRender() {\n    // If the label already exists, update its text\n    if (this.label) {\n        this.label.attr({\n            text: this.getLabelText(this.options.label)\n        });\n    }\n}\n/**\n * @private\n */\nfunction wrapPlotLineOrBandGetLabelText(defaultMethod, defaultLabelOptions) {\n    var options = this.options;\n    if (options &&\n        options.className &&\n        options.className.indexOf('highcharts-current-date-indicator') !== -1 &&\n        options.label &&\n        typeof options.label.formatter === 'function') {\n        options.value = Date.now();\n        return options.label.formatter\n            .call(this, options.value, options.label.format);\n    }\n    return defaultMethod.call(this, defaultLabelOptions);\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar CurrentDateIndication = {\n    compose: compose\n};\n/* harmony default export */ var Extensions_CurrentDateIndication = (CurrentDateIndication);\n\n;// ./code/es5/es-modules/masters/modules/current-date-indicator.js\n\n\n\n\nvar G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nExtensions_CurrentDateIndication.compose(G.Axis, G.PlotLineOrBand);\n/* harmony default export */ var current_date_indicator_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "current_date_indicator_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "composed", "addEvent", "merge", "pushUnique", "wrap", "defaultOptions", "color", "width", "label", "format", "formatter", "value", "axis", "chart", "time", "dateFormat", "rotation", "style", "fontSize", "onAxisAfterSetOptions", "options", "cdiOptions", "currentDateIndicator", "plotLineOptions", "Date", "now", "className", "plotLines", "push", "onPlotLineOrBandRender", "attr", "text", "getLabelText", "wrapPlotLineOrBandGetLabelText", "defaultMethod", "defaultLabelOptions", "indexOf", "G", "Extensions_CurrentDateIndication", "compose", "AxisClass", "PlotLineOrBandClass", "Axis", "PlotLineOrBand"], "mappings": "CAWA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,eAC1B,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,4CAA6C,CAAC,CAAC,wBAAwB,CAAC,CAAEJ,GAC1E,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,4CAA4C,CAAGD,EAAQG,QAAQ,eAEvEJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CACjD,EAAG,IAAI,CAAE,SAASO,CAAgC,EAClD,OAAgB,AAAC,WACP,aACA,IAAIC,EAAuB,CAE/B,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGIE,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaV,OAAO,CAG5B,IAAIC,EAASM,CAAwB,CAACE,EAAS,CAAG,CAGjDT,QAAS,CAAC,CACX,EAMA,OAHAM,CAAmB,CAACG,EAAS,CAACR,EAAQA,EAAOD,OAAO,CAAEQ,GAG/CP,EAAOD,OAAO,AACtB,CAMCQ,EAAoBI,CAAC,CAAG,SAASX,CAAM,EACtC,IAAIY,EAASZ,GAAUA,EAAOa,UAAU,CACvC,WAAa,OAAOb,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAO,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASf,CAAO,CAAEiB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACnB,EAASkB,IAC5EE,OAAOC,cAAc,CAACrB,EAASkB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,CAA4B,CAC3E,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAejHE,EAAW,AAACD,IAA+EC,QAAQ,CAEnGC,EAAW,AAACF,IAA+EE,QAAQ,CAAEC,EAAQ,AAACH,IAA+EG,KAAK,CAAEC,EAAa,AAACJ,IAA+EI,UAAU,CAAEC,EAAO,AAACL,IAA+EK,IAAI,CAwBxYC,EAAiB,CACjBC,MAAO,UACPC,MAAO,EAIPC,MAAO,CASHC,OAAQ,YACRC,UAAW,SAAUC,CAAK,CAAEF,CAAM,EAC9B,OAAO,IAAI,CAACG,IAAI,CAACC,KAAK,CAACC,IAAI,CAACC,UAAU,CAACN,GAAU,GAAIE,EAAO,CAAA,EAChE,EACAK,SAAU,EAIVC,MAAO,CAEHC,SAAU,OACd,CACJ,CACJ,EAmBA,SAASC,IACL,IAAIC,EAAU,IAAI,CAACA,OAAO,CACtBC,EAAaD,EAAQE,oBAAoB,CAC7C,GAAID,EAAY,CACZ,IAAIE,EAAkB,AAAsB,UAAtB,OAAOF,EACrBnB,EAAMG,EACVgB,GACInB,EAAMG,EACdkB,CAAAA,EAAgBZ,KAAK,CAAGa,KAAKC,GAAG,GAChCF,EAAgBG,SAAS,CAAG,oCACvBN,EAAQO,SAAS,EAClBP,CAAAA,EAAQO,SAAS,CAAG,EAAE,AAAD,EAEzBP,EAAQO,SAAS,CAACC,IAAI,CAACL,EAC3B,CACJ,CAIA,SAASM,IAED,IAAI,CAACrB,KAAK,EACV,IAAI,CAACA,KAAK,CAACsB,IAAI,CAAC,CACZC,KAAM,IAAI,CAACC,YAAY,CAAC,IAAI,CAACZ,OAAO,CAACZ,KAAK,CAC9C,EAER,CAIA,SAASyB,EAA+BC,CAAa,CAAEC,CAAmB,EACtE,IAAIf,EAAU,IAAI,CAACA,OAAO,QAC1B,AAAIA,GACAA,EAAQM,SAAS,EACjBN,AAAmE,KAAnEA,EAAQM,SAAS,CAACU,OAAO,CAAC,sCAC1BhB,EAAQZ,KAAK,EACb,AAAmC,YAAnC,OAAOY,EAAQZ,KAAK,CAACE,SAAS,EAC9BU,EAAQT,KAAK,CAAGa,KAAKC,GAAG,GACjBL,EAAQZ,KAAK,CAACE,SAAS,CACzBf,IAAI,CAAC,IAAI,CAAEyB,EAAQT,KAAK,CAAES,EAAQZ,KAAK,CAACC,MAAM,GAEhDyB,EAAcvC,IAAI,CAAC,IAAI,CAAEwC,EACpC,CAgBA,IAAIE,EAAKtC,IACTuC,AAX4B,CAAA,CACxBC,QA3DJ,SAAiBC,CAAS,CAAEC,CAAmB,EACvCtC,EAAWH,EAAU,2BACrBC,EAASuC,EAAW,kBAAmBrB,GACvClB,EAASwC,EAAqB,SAAUZ,GACxCzB,EAAKqC,EAAoBhD,SAAS,CAAE,eAAgBwC,GAE5D,CAsDA,CAAA,EASiCM,OAAO,CAACF,EAAEK,IAAI,CAAEL,EAAEM,cAAc,EACpC,IAAI9C,EAA+BE,IAGtD,OADYH,EAAoB,OAAU,AAE3C,GAET"}