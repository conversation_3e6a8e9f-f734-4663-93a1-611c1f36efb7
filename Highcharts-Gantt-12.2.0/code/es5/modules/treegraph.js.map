{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * Treegraph chart series type\n * @module highcharts/modules/treegraph\n * @requires highcharts\n * @requires highcharts/modules/treemap\n *\n *  (c) 2010-2025 Pawel Lysy Grzegorz Blachlinski\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"], require(\"highcharts\")[\"SVGRenderer\"], require(\"highcharts\")[\"Point\"], require(\"highcharts\")[\"Color\"], require(\"highcharts\")[\"SVGElement\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/treegraph\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"SeriesRegistry\"], [\"highcharts/highcharts\",\"SVGRenderer\"], [\"highcharts/highcharts\",\"Point\"], [\"highcharts/highcharts\",\"Color\"], [\"highcharts/highcharts\",\"SVGElement\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/treegraph\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"], require(\"highcharts\")[\"SVGRenderer\"], require(\"highcharts\")[\"Point\"], require(\"highcharts\")[\"Color\"], require(\"highcharts\")[\"SVGElement\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"SVGRenderer\"], root[\"Highcharts\"][\"Point\"], root[\"Highcharts\"][\"Color\"], root[\"Highcharts\"][\"SVGElement\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__540__, __WEBPACK_EXTERNAL_MODULE__260__, __WEBPACK_EXTERNAL_MODULE__620__, __WEBPACK_EXTERNAL_MODULE__28__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 28:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__28__;\n\n/***/ }),\n\n/***/ 260:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__260__;\n\n/***/ }),\n\n/***/ 512:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 540:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__540__;\n\n/***/ }),\n\n/***/ 620:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__620__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ treegraph_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es5/es-modules/Series/PathUtilities.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar getLinkPath = {\n    'default': getDefaultPath,\n    straight: getStraightPath,\n    curved: getCurvedPath\n};\n/**\n *\n */\nfunction getDefaultPath(pathParams) {\n    var x1 = pathParams.x1,\n        y1 = pathParams.y1,\n        x2 = pathParams.x2,\n        y2 = pathParams.y2,\n        _a = pathParams.width,\n        width = _a === void 0 ? 0 : _a,\n        _b = pathParams.inverted,\n        inverted = _b === void 0 ? false : _b,\n        radius = pathParams.radius,\n        parentVisible = pathParams.parentVisible;\n    var path = [\n            ['M',\n        x1,\n        y1],\n            ['L',\n        x1,\n        y1],\n            ['C',\n        x1,\n        y1,\n        x1,\n        y2,\n        x1,\n        y2],\n            ['L',\n        x1,\n        y2],\n            ['C',\n        x1,\n        y1,\n        x1,\n        y2,\n        x1,\n        y2],\n            ['L',\n        x1,\n        y2]\n        ];\n    return parentVisible ?\n        applyRadius([\n            ['M', x1, y1],\n            ['L', x1 + width * (inverted ? -0.5 : 0.5), y1],\n            ['L', x1 + width * (inverted ? -0.5 : 0.5), y2],\n            ['L', x2, y2]\n        ], radius) :\n        path;\n}\n/**\n *\n */\nfunction getStraightPath(pathParams) {\n    var x1 = pathParams.x1,\n        y1 = pathParams.y1,\n        x2 = pathParams.x2,\n        y2 = pathParams.y2,\n        _a = pathParams.width,\n        width = _a === void 0 ? 0 : _a,\n        _b = pathParams.inverted,\n        inverted = _b === void 0 ? false : _b,\n        parentVisible = pathParams.parentVisible;\n    return parentVisible ? [\n        ['M', x1, y1],\n        ['L', x1 + width * (inverted ? -1 : 1), y2],\n        ['L', x2, y2]\n    ] : [\n        ['M', x1, y1],\n        ['L', x1, y2],\n        ['L', x1, y2]\n    ];\n}\n/**\n *\n */\nfunction getCurvedPath(pathParams) {\n    var x1 = pathParams.x1,\n        y1 = pathParams.y1,\n        x2 = pathParams.x2,\n        y2 = pathParams.y2,\n        _a = pathParams.offset,\n        offset = _a === void 0 ? 0 : _a,\n        _b = pathParams.width,\n        width = _b === void 0 ? 0 : _b,\n        _c = pathParams.inverted,\n        inverted = _c === void 0 ? false : _c,\n        parentVisible = pathParams.parentVisible;\n    return parentVisible ?\n        [\n            ['M', x1, y1],\n            [\n                'C',\n                x1 + offset,\n                y1,\n                x1 - offset + width * (inverted ? -1 : 1),\n                y2,\n                x1 + width * (inverted ? -1 : 1),\n                y2\n            ],\n            ['L', x2, y2]\n        ] :\n        [\n            ['M', x1, y1],\n            ['C', x1, y1, x1, y2, x1, y2],\n            ['L', x2, y2]\n        ];\n}\n/**\n * General function to apply corner radius to a path\n * @private\n */\nfunction applyRadius(path, r) {\n    var d = [];\n    for (var i = 0; i < path.length; i++) {\n        var x = path[i][1];\n        var y = path[i][2];\n        if (typeof x === 'number' && typeof y === 'number') {\n            // MoveTo\n            if (i === 0) {\n                d.push(['M', x, y]);\n            }\n            else if (i === path.length - 1) {\n                d.push(['L', x, y]);\n                // CurveTo\n            }\n            else if (r) {\n                var prevSeg = path[i - 1];\n                var nextSeg = path[i + 1];\n                if (prevSeg && nextSeg) {\n                    var x1 = prevSeg[1],\n                        y1 = prevSeg[2],\n                        x2 = nextSeg[1],\n                        y2 = nextSeg[2];\n                    // Only apply to breaks\n                    if (typeof x1 === 'number' &&\n                        typeof x2 === 'number' &&\n                        typeof y1 === 'number' &&\n                        typeof y2 === 'number' &&\n                        x1 !== x2 &&\n                        y1 !== y2) {\n                        var directionX = x1 < x2 ? 1 : -1,\n                            directionY = y1 < y2 ? 1 : -1;\n                        d.push([\n                            'L',\n                            x - directionX * Math.min(Math.abs(x - x1), r),\n                            y - directionY * Math.min(Math.abs(y - y1), r)\n                        ], [\n                            'C',\n                            x,\n                            y,\n                            x,\n                            y,\n                            x + directionX * Math.min(Math.abs(x - x2), r),\n                            y + directionY * Math.min(Math.abs(y - y2), r)\n                        ]);\n                    }\n                }\n                // LineTo\n            }\n            else {\n                d.push(['L', x, y]);\n            }\n        }\n    }\n    return d;\n}\nvar PathUtilities = {\n    applyRadius: applyRadius,\n    getLinkPath: getLinkPath\n};\n/* harmony default export */ var Series_PathUtilities = (PathUtilities);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SVGRenderer\"],\"commonjs\":[\"highcharts\",\"SVGRenderer\"],\"commonjs2\":[\"highcharts\",\"SVGRenderer\"],\"root\":[\"Highcharts\",\"SVGRenderer\"]}\nvar highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_ = __webpack_require__(540);\nvar highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default = /*#__PURE__*/__webpack_require__.n(highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_);\n;// ./code/es5/es-modules/Series/Treegraph/TreegraphNode.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy Grzegorz Blachlinski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar TreemapNode = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.treemap.prototype.NodeClass;\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n */\nvar TreegraphNode = /** @class */ (function (_super) {\n    __extends(TreegraphNode, _super);\n    function TreegraphNode() {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        var _this = _super !== null && _super.apply(this,\n            arguments) || this;\n        _this.mod = 0;\n        _this.shift = 0;\n        _this.change = 0;\n        _this.children = [];\n        _this.preX = 0;\n        _this.hidden = false;\n        _this.wasVisited = false;\n        _this.collapsed = false;\n        return _this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Get the next left node which is either first child or thread.\n     *\n     * @return {TreegraphNode|undefined}\n     *         Next left node child or thread.\n     */\n    TreegraphNode.prototype.nextLeft = function () {\n        return this.getLeftMostChild() || this.thread;\n    };\n    /**\n     * Get the next right node which is either last child or thread.\n     *\n     * @return {TreegraphNode|undefined}\n     *         Next right node child or thread.\n     */\n    TreegraphNode.prototype.nextRight = function () {\n        return this.getRightMostChild() || this.thread;\n    };\n    /**\n     * Return the left one of the greatest uncommon ancestors of a\n     * leftInternal node and it's right neighbor.\n     *\n     * @param {TreegraphNode} leftIntNode\n     * @param {TreegraphNode} defaultAncestor\n     * @return {TreegraphNode}\n     *         Left one of the greatest uncommon ancestors of a leftInternal\n     *         node and it's right neighbor.\n     *\n     */\n    TreegraphNode.prototype.getAncestor = function (leftIntNode, defaultAncestor) {\n        var leftAnc = leftIntNode.ancestor;\n        if (leftAnc.children[0] === this.children[0]) {\n            return leftIntNode.ancestor;\n        }\n        return defaultAncestor;\n    };\n    /**\n     * Get node's first sibling, which is not hidden.\n     *\n     * @return {TreegraphNode|undefined}\n     *         First sibling of the node which is not hidden or undefined, if it\n     *         does not exists.\n     */\n    TreegraphNode.prototype.getLeftMostSibling = function () {\n        var parent = this.getParent();\n        if (parent) {\n            for (var _i = 0, _a = parent.children; _i < _a.length; _i++) {\n                var child = _a[_i];\n                if (child && child.point.visible) {\n                    return child;\n                }\n            }\n        }\n    };\n    /**\n     * Check if the node is a leaf (if it has any children).\n     *\n     * @return {boolean}\n     *         If the node has no visible children return true.\n     */\n    TreegraphNode.prototype.hasChildren = function () {\n        var children = this.children;\n        for (var i = 0; i < children.length; i++) {\n            if (children[i].point.visible) {\n                return true;\n            }\n        }\n        return false;\n    };\n    /**\n     * Get node's left sibling (if it exists).\n     *\n     * @return {TreegraphNode|undefined}\n     *         Left sibling of the node\n     */\n    TreegraphNode.prototype.getLeftSibling = function () {\n        var parent = this.getParent();\n        if (parent) {\n            var children = parent.children;\n            for (var i = this.relativeXPosition - 1; i >= 0; i--) {\n                if (children[i] && children[i].point.visible) {\n                    return children[i];\n                }\n            }\n        }\n    };\n    /**\n     * Get the node's first child (if it exists).\n     *\n     * @return {TreegraphNode|undefined}\n     *         Node's first child which isn't hidden.\n     */\n    TreegraphNode.prototype.getLeftMostChild = function () {\n        var children = this.children;\n        for (var i = 0; i < children.length; i++) {\n            if (children[i].point.visible) {\n                return children[i];\n            }\n        }\n    };\n    /**\n     * Get the node's last child (if it exists).\n     *\n     * @return {TreegraphNode|undefined}\n     *         Node's last child which isn't hidden.\n     */\n    TreegraphNode.prototype.getRightMostChild = function () {\n        var children = this.children;\n        for (var i = children.length - 1; i >= 0; i--) {\n            if (children[i].point.visible) {\n                return children[i];\n            }\n        }\n    };\n    /**\n     * Get the parent of current node or return undefined for root of the\n     * tree.\n     *\n     * @return {TreegraphNode|undefined}\n     *         Node's parent or undefined for root.\n     */\n    TreegraphNode.prototype.getParent = function () {\n        return this.parentNode;\n    };\n    /**\n     * Get node's first child which is not hidden.\n     *\n     * @return {TreegraphNode|undefined}\n     *         First child.\n     */\n    TreegraphNode.prototype.getFirstChild = function () {\n        var children = this.children;\n        for (var i = 0; i < children.length; i++) {\n            if (children[i].point.visible) {\n                return children[i];\n            }\n        }\n    };\n    return TreegraphNode;\n}(TreemapNode));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Treegraph_TreegraphNode = (TreegraphNode);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Point\"],\"commonjs\":[\"highcharts\",\"Point\"],\"commonjs2\":[\"highcharts\",\"Point\"],\"root\":[\"Highcharts\",\"Point\"]}\nvar highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_ = __webpack_require__(260);\nvar highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default = /*#__PURE__*/__webpack_require__.n(highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_);\n;// ./code/es5/es-modules/Series/Treegraph/TreegraphPoint.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy Grzegorz Blachlinski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar TreegraphPoint_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\nvar TreemapPoint = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.treemap.prototype.pointClass;\n\nvar addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n */\nvar TreegraphPoint = /** @class */ (function (_super) {\n    TreegraphPoint_extends(TreegraphPoint, _super);\n    function TreegraphPoint() {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        var _this = _super !== null && _super.apply(this,\n            arguments) || this;\n        _this.dataLabelOnHidden = true;\n        _this.isLink = false;\n        _this.setState = (highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default()).prototype.setState;\n        return _this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    TreegraphPoint.prototype.draw = function () {\n        _super.prototype.draw.apply(this, arguments);\n        // Run animation of hiding/showing of the point.\n        var graphic = this.graphic;\n        if (graphic) {\n            graphic.animate({\n                visibility: this.visible ? 'inherit' : 'hidden'\n            });\n        }\n        this.renderCollapseButton();\n    };\n    TreegraphPoint.prototype.renderCollapseButton = function () {\n        var point = this,\n            series = point.series,\n            parentGroup = point.graphic && point.graphic.parentGroup,\n            levelOptions = series.mapOptionsToLevel[point.node.level || 0] || {},\n            btnOptions = merge(series.options.collapseButton,\n            levelOptions.collapseButton,\n            point.options.collapseButton),\n            width = btnOptions.width,\n            height = btnOptions.height,\n            shape = btnOptions.shape,\n            style = btnOptions.style,\n            padding = 2,\n            chart = this.series.chart,\n            calculatedOpacity = (point.visible &&\n                (point.collapsed ||\n                    !btnOptions.onlyOnHover ||\n                    point.state === 'hover')) ? 1 : 0;\n        if (!point.shapeArgs) {\n            return;\n        }\n        this.collapseButtonOptions = btnOptions;\n        if (!point.collapseButton) {\n            if (!point.node.children.length || !btnOptions.enabled) {\n                return;\n            }\n            var _a = this.getCollapseBtnPosition(btnOptions), x = _a.x, y = _a.y, fill = (btnOptions.fillColor ||\n                    point.color ||\n                    \"#cccccc\" /* Palette.neutralColor20 */);\n            point.collapseButton = chart.renderer\n                .label(point.collapsed ? '+' : '-', x, y, shape)\n                .attr({\n                height: height - 2 * padding,\n                width: width - 2 * padding,\n                padding: padding,\n                fill: fill,\n                rotation: chart.inverted ? 90 : 0,\n                rotationOriginX: width / 2,\n                rotationOriginY: height / 2,\n                stroke: btnOptions.lineColor || \"#ffffff\" /* Palette.backgroundColor */,\n                'stroke-width': btnOptions.lineWidth,\n                'text-align': 'center',\n                align: 'center',\n                zIndex: 1,\n                opacity: calculatedOpacity,\n                visibility: point.visible ? 'inherit' : 'hidden'\n            })\n                .addClass('highcharts-tracker')\n                .addClass('highcharts-collapse-button')\n                .removeClass('highcharts-no-tooltip')\n                .css(merge({\n                color: typeof fill === 'string' ?\n                    chart.renderer.getContrast(fill) :\n                    \"#333333\" /* Palette.neutralColor80 */\n            }, style))\n                .add(parentGroup);\n            point.collapseButton.element.point = point;\n        }\n        else {\n            if (!point.node.children.length || !btnOptions.enabled) {\n                point.collapseButton.destroy();\n                delete point.collapseButton;\n            }\n            else {\n                var _b = this.getCollapseBtnPosition(btnOptions),\n                    x = _b.x,\n                    y = _b.y;\n                point.collapseButton\n                    .attr({\n                    text: point.collapsed ? '+' : '-',\n                    rotation: chart.inverted ? 90 : 0,\n                    rotationOriginX: width / 2,\n                    rotationOriginY: height / 2,\n                    visibility: point.visible ? 'inherit' : 'hidden'\n                })\n                    .animate({\n                    x: x,\n                    y: y,\n                    opacity: calculatedOpacity\n                });\n            }\n        }\n    };\n    TreegraphPoint.prototype.toggleCollapse = function (state) {\n        var series = this.series;\n        this.update({\n            collapsed: state !== null && state !== void 0 ? state : !this.collapsed\n        }, false, void 0, false);\n        fireEvent(series, 'toggleCollapse');\n        series.redraw();\n    };\n    TreegraphPoint.prototype.destroy = function () {\n        if (this.collapseButton) {\n            this.collapseButton.destroy();\n            delete this.collapseButton;\n            this.collapseButton = void 0;\n        }\n        if (this.linkToParent) {\n            this.linkToParent.destroy();\n            delete this.linkToParent;\n        }\n        _super.prototype.destroy.apply(this, arguments);\n    };\n    TreegraphPoint.prototype.getCollapseBtnPosition = function (btnOptions) {\n        var point = this,\n            chart = point.series.chart,\n            inverted = chart.inverted,\n            btnWidth = btnOptions.width,\n            btnHeight = btnOptions.height,\n            _a = point.shapeArgs || {},\n            _b = _a.x,\n            x = _b === void 0 ? 0 : _b,\n            _c = _a.y,\n            y = _c === void 0 ? 0 : _c,\n            _d = _a.width,\n            width = _d === void 0 ? 0 : _d,\n            _e = _a.height,\n            height = _e === void 0 ? 0 : _e;\n        return {\n            x: x +\n                btnOptions.x +\n                (inverted ? -btnHeight * 0.3 : width + btnWidth * -0.3),\n            y: y + height / 2 - btnHeight / 2 + btnOptions.y\n        };\n    };\n    return TreegraphPoint;\n}(TreemapPoint));\naddEvent(TreegraphPoint, 'mouseOut', function () {\n    var btn = this.collapseButton,\n        btnOptions = this.collapseButtonOptions;\n    if (btn && (btnOptions === null || btnOptions === void 0 ? void 0 : btnOptions.onlyOnHover) && !this.collapsed) {\n        btn.animate({ opacity: 0 });\n    }\n});\naddEvent(TreegraphPoint, 'mouseOver', function () {\n    var _a,\n        _b;\n    if (this.collapseButton && this.visible) {\n        this.collapseButton.animate({ opacity: 1 }, (_b = (_a = this.series.options.states) === null || _a === void 0 ? void 0 : _a.hover) === null || _b === void 0 ? void 0 : _b.animation);\n    }\n});\n// Handle showing and hiding of the points\naddEvent(TreegraphPoint, 'click', function () {\n    this.toggleCollapse();\n});\n/* *\n *\n *  Export Default\n *\n * */\n/* harmony default export */ var Treegraph_TreegraphPoint = (TreegraphPoint);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Color\"],\"commonjs\":[\"highcharts\",\"Color\"],\"commonjs2\":[\"highcharts\",\"Color\"],\"root\":[\"Highcharts\",\"Color\"]}\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_ = __webpack_require__(620);\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default = /*#__PURE__*/__webpack_require__.n(highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_);\n;// ./code/es5/es-modules/Series/TreeUtilities.js\n/* *\n *\n *  (c) 2014-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nvar extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, isArray = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isArray, isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, isObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isObject, TreeUtilities_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, relativeLength = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).relativeLength;\n/* *\n *\n *  Functions\n *\n * */\n/* eslint-disable valid-jsdoc */\n/**\n * @private\n */\nfunction getColor(node, options) {\n    var index = options.index,\n        mapOptionsToLevel = options.mapOptionsToLevel,\n        parentColor = options.parentColor,\n        parentColorIndex = options.parentColorIndex,\n        series = options.series,\n        colors = options.colors,\n        siblings = options.siblings,\n        points = series.points,\n        chartOptionsChart = series.chart.options.chart;\n    var getColorByPoint,\n        point,\n        level,\n        colorByPoint,\n        colorIndexByPoint,\n        color,\n        colorIndex;\n    /**\n     * @private\n     */\n    var variateColor = function (color) {\n            var colorVariation = level && level.colorVariation;\n        if (colorVariation &&\n            colorVariation.key === 'brightness' &&\n            index &&\n            siblings) {\n            return highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default().parse(color).brighten(colorVariation.to * (index / siblings)).get();\n        }\n        return color;\n    };\n    if (node) {\n        point = points[node.i];\n        level = mapOptionsToLevel[node.level] || {};\n        getColorByPoint = point && level.colorByPoint;\n        if (getColorByPoint) {\n            colorIndexByPoint = point.index % (colors ?\n                colors.length :\n                chartOptionsChart.colorCount);\n            colorByPoint = colors && colors[colorIndexByPoint];\n        }\n        // Select either point color, level color or inherited color.\n        if (!series.chart.styledMode) {\n            color = pick(point && point.options.color, level && level.color, colorByPoint, parentColor && variateColor(parentColor), series.color);\n        }\n        colorIndex = pick(point && point.options.colorIndex, level && level.colorIndex, colorIndexByPoint, parentColorIndex, options.colorIndex);\n    }\n    return {\n        color: color,\n        colorIndex: colorIndex\n    };\n}\n/**\n * Creates a map from level number to its given options.\n *\n * @private\n *\n * @param {Object} params\n * Object containing parameters.\n * - `defaults` Object containing default options. The default options are\n *   merged with the userOptions to get the final options for a specific\n *   level.\n * - `from` The lowest level number.\n * - `levels` User options from series.levels.\n * - `to` The highest level number.\n *\n * @return {Highcharts.Dictionary<object>|null}\n * Returns a map from level number to its given options.\n */\nfunction getLevelOptions(params) {\n    var result = {};\n    var defaults,\n        converted,\n        i,\n        from,\n        to,\n        levels;\n    if (isObject(params)) {\n        from = isNumber(params.from) ? params.from : 1;\n        levels = params.levels;\n        converted = {};\n        defaults = isObject(params.defaults) ? params.defaults : {};\n        if (isArray(levels)) {\n            converted = levels.reduce(function (obj, item) {\n                var level,\n                    levelIsConstant,\n                    options;\n                if (isObject(item) && isNumber(item.level)) {\n                    options = TreeUtilities_merge({}, item);\n                    levelIsConstant = pick(options.levelIsConstant, defaults.levelIsConstant);\n                    // Delete redundant properties.\n                    delete options.levelIsConstant;\n                    delete options.level;\n                    // Calculate which level these options apply to.\n                    level = item.level + (levelIsConstant ? 0 : from - 1);\n                    if (isObject(obj[level])) {\n                        TreeUtilities_merge(true, obj[level], options); // #16329\n                    }\n                    else {\n                        obj[level] = options;\n                    }\n                }\n                return obj;\n            }, {});\n        }\n        to = isNumber(params.to) ? params.to : 1;\n        for (i = 0; i <= to; i++) {\n            result[i] = TreeUtilities_merge({}, defaults, isObject(converted[i]) ? converted[i] : {});\n        }\n    }\n    return result;\n}\n/**\n * @private\n * @todo Combine buildTree and buildNode with setTreeValues\n * @todo Remove logic from Treemap and make it utilize this mixin.\n */\nfunction setTreeValues(tree, options) {\n    var before = options.before,\n        idRoot = options.idRoot,\n        mapIdToNode = options.mapIdToNode,\n        nodeRoot = mapIdToNode[idRoot],\n        levelIsConstant = (options.levelIsConstant !== false),\n        points = options.points,\n        point = points[tree.i],\n        optionsPoint = point && point.options || {},\n        children = [];\n    var childrenTotal = 0;\n    tree.levelDynamic = tree.level - (levelIsConstant ? 0 : nodeRoot.level);\n    tree.name = pick(point && point.name, '');\n    tree.visible = (idRoot === tree.id ||\n        options.visible === true);\n    if (typeof before === 'function') {\n        tree = before(tree, options);\n    }\n    // First give the children some values\n    tree.children.forEach(function (child, i) {\n        var newOptions = extend({},\n            options);\n        extend(newOptions, {\n            index: i,\n            siblings: tree.children.length,\n            visible: tree.visible\n        });\n        child = setTreeValues(child, newOptions);\n        children.push(child);\n        if (child.visible) {\n            childrenTotal += child.val;\n        }\n    });\n    // Set the values\n    var value = pick(optionsPoint.value,\n        childrenTotal);\n    tree.visible = value >= 0 && (childrenTotal > 0 || tree.visible);\n    tree.children = children;\n    tree.childrenTotal = childrenTotal;\n    tree.isLeaf = tree.visible && !childrenTotal;\n    tree.val = value;\n    return tree;\n}\n/**\n * Update the rootId property on the series. Also makes sure that it is\n * accessible to exporting.\n *\n * @private\n *\n * @param {Object} series\n * The series to operate on.\n *\n * @return {string}\n * Returns the resulting rootId after update.\n */\nfunction updateRootId(series) {\n    var rootId,\n        options;\n    if (isObject(series)) {\n        // Get the series options.\n        options = isObject(series.options) ? series.options : {};\n        // Calculate the rootId.\n        rootId = pick(series.rootNode, options.rootId, '');\n        // Set rootId on series.userOptions to pick it up in exporting.\n        if (isObject(series.userOptions)) {\n            series.userOptions.rootId = rootId;\n        }\n        // Set rootId on series to pick it up on next update.\n        series.rootNode = rootId;\n    }\n    return rootId;\n}\n/**\n * Get the node width, which relies on the plot width and the nodeDistance\n * option.\n *\n * @private\n */\nfunction getNodeWidth(series, columnCount) {\n    var chart = series.chart,\n        options = series.options,\n        _a = options.nodeDistance,\n        nodeDistance = _a === void 0 ? 0 : _a,\n        _b = options.nodeWidth,\n        nodeWidth = _b === void 0 ? 0 : _b,\n        _c = chart.plotSizeX,\n        plotSizeX = _c === void 0 ? 1 : _c;\n    // Node width auto means they are evenly distributed along the width of\n    // the plot area\n    if (nodeWidth === 'auto') {\n        if (typeof nodeDistance === 'string' && /%$/.test(nodeDistance)) {\n            var fraction = parseFloat(nodeDistance) / 100,\n                total = columnCount + fraction * (columnCount - 1);\n            return plotSizeX / total;\n        }\n        var nDistance = Number(nodeDistance);\n        return ((plotSizeX + nDistance) /\n            (columnCount || 1)) - nDistance;\n    }\n    return relativeLength(nodeWidth, plotSizeX);\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar TreeUtilities = {\n    getColor: getColor,\n    getLevelOptions: getLevelOptions,\n    getNodeWidth: getNodeWidth,\n    setTreeValues: setTreeValues,\n    updateRootId: updateRootId\n};\n/* harmony default export */ var Series_TreeUtilities = (TreeUtilities);\n\n;// ./code/es5/es-modules/Series/Treegraph/TreegraphLink.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy Grzegorz Blachlinski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar TreegraphLink_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\nvar TreegraphLink_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, TreegraphLink_extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend;\n\nvar ColumnPoint = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.column.prototype.pointClass;\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n */\nvar LinkPoint = /** @class */ (function (_super) {\n    TreegraphLink_extends(LinkPoint, _super);\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    function LinkPoint(series, options, x, point) {\n        var _this = _super.call(this,\n            series,\n            options,\n            x) || this;\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        _this.dataLabelOnNull = true;\n        _this.formatPrefix = 'link';\n        _this.isLink = true;\n        _this.node = {};\n        _this.formatPrefix = 'link';\n        _this.dataLabelOnNull = true;\n        if (point) {\n            _this.fromNode = point.node.parentNode.point;\n            _this.visible = point.visible;\n            _this.toNode = point;\n            _this.id = _this.toNode.id + '-' + _this.fromNode.id;\n        }\n        return _this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    LinkPoint.prototype.update = function (options, redraw, animation, runEvent) {\n        var oldOptions = {\n                id: this.id,\n                formatPrefix: this.formatPrefix\n            };\n        highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default().prototype.update.call(this, options, this.isLink ? false : redraw, // Hold the redraw for nodes\n        animation, runEvent);\n        this.visible = this.toNode.visible;\n        TreegraphLink_extend(this, oldOptions);\n        if (TreegraphLink_pick(redraw, true)) {\n            this.series.chart.redraw(animation);\n        }\n    };\n    return LinkPoint;\n}(ColumnPoint));\n/* *\n *\n *  Export Default\n *\n * */\n/* harmony default export */ var TreegraphLink = (LinkPoint);\n\n;// ./code/es5/es-modules/Series/Treegraph/TreegraphLayout.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy Grzegorz Blachlinski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n */\nvar TreegraphLayout = /** @class */ (function () {\n    function TreegraphLayout() {\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Create dummy node, which allows to manually set the level of the node.\n     *\n     * @param {TreegraphNode} parent\n     *        Parent node, to which the dummyNode should be connected.\n     * @param {TreegraphNode} child\n     *        Child node, which should be connected to dummyNode.\n     * @param {number} gapSize\n     *        Remaining gap size.\n     *\n     * @return {TreegraphNode}\n     *         DummyNode as a parent of nodes, which column changes.\n     */\n    TreegraphLayout.createDummyNode = function (parent, child, gapSize) {\n        // Initialise dummy node.\n        var dummyNode = new Treegraph_TreegraphNode();\n        dummyNode.id = parent.id + '-' + gapSize;\n        dummyNode.ancestor = parent;\n        // Add connection from new node to the previous points.\n        // First connection to itself.\n        dummyNode.children.push(child);\n        dummyNode.parent = parent.id;\n        dummyNode.parentNode = parent;\n        dummyNode.point = child.point;\n        dummyNode.level = child.level - gapSize;\n        dummyNode.relativeXPosition = child.relativeXPosition;\n        dummyNode.visible = child.visible;\n        // Then connection from parent to dummyNode.\n        parent.children[child.relativeXPosition] = dummyNode;\n        child.oldParentNode = parent;\n        child.relativeXPosition = 0;\n        // Then connection from child to dummyNode.\n        child.parentNode = dummyNode;\n        child.parent = dummyNode.id;\n        return dummyNode;\n    };\n    /**\n     * Walker algorithm of positioning the nodes in the treegraph improved by\n     * Buchheim to run in the linear time. Basic algorithm consists of post\n     * order traversal, which starts from going bottom up (first walk), and then\n     * pre order traversal top to bottom (second walk) where adding all of the\n     * modifiers is performed.\n     * link to the paper: http://dirk.jivas.de/papers/buchheim02improving.pdf\n     *\n     * @param {TreegraphSeries} series the Treegraph series\n     */\n    TreegraphLayout.prototype.calculatePositions = function (series) {\n        var treeLayout = this;\n        var nodes = series.nodeList;\n        this.resetValues(nodes);\n        var root = series.tree;\n        if (root) {\n            treeLayout.calculateRelativeX(root, 0);\n            treeLayout.beforeLayout(nodes);\n            treeLayout.firstWalk(root);\n            treeLayout.secondWalk(root, -root.preX);\n            treeLayout.afterLayout(nodes);\n        }\n    };\n    /**\n     * Create dummyNodes as parents for nodes, which column is changed.\n     *\n     * @param {Array<TreegraphNode>} nodes\n     *        All of the nodes.\n     */\n    TreegraphLayout.prototype.beforeLayout = function (nodes) {\n        for (var _i = 0, nodes_1 = nodes; _i < nodes_1.length; _i++) {\n            var node = nodes_1[_i];\n            for (var _a = 0, _b = node.children; _a < _b.length; _a++) {\n                var child = _b[_a];\n                // Support for children placed in distant columns.\n                if (child && child.level - node.level > 1) {\n                    // For further columns treat the nodes as a\n                    // single parent-child pairs till the column is achieved.\n                    var gapSize = child.level - node.level - 1;\n                    // Parent -> dummyNode -> child\n                    while (gapSize > 0) {\n                        child = TreegraphLayout.createDummyNode(node, child, gapSize);\n                        gapSize--;\n                    }\n                }\n            }\n        }\n    };\n    /**\n     * Reset the calculated values from the previous run.\n     * @param {TreegraphNode[]} nodes all of the nodes.\n     */\n    TreegraphLayout.prototype.resetValues = function (nodes) {\n        for (var _i = 0, nodes_2 = nodes; _i < nodes_2.length; _i++) {\n            var node = nodes_2[_i];\n            node.mod = 0;\n            node.ancestor = node;\n            node.shift = 0;\n            node.thread = void 0;\n            node.change = 0;\n            node.preX = 0;\n        }\n    };\n    /**\n     * Assigns the value to each node, which indicates, what is his sibling\n     * number.\n     *\n     * @param {TreegraphNode} node\n     *        Root node\n     * @param {number} index\n     *        Index to which the nodes position should be set\n     */\n    TreegraphLayout.prototype.calculateRelativeX = function (node, index) {\n        var treeLayout = this,\n            children = node.children;\n        for (var i = 0, iEnd = children.length; i < iEnd; ++i) {\n            treeLayout.calculateRelativeX(children[i], i);\n        }\n        node.relativeXPosition = index;\n    };\n    /**\n     * Recursive post order traversal of the tree, where the initial position\n     * of the nodes is calculated.\n     *\n     * @param {TreegraphNode} node\n     *        The node for which the position should be calculated.\n     */\n    TreegraphLayout.prototype.firstWalk = function (node) {\n        var treeLayout = this, \n            // Arbitrary value used to position nodes in respect to each other.\n            siblingDistance = 1;\n        var leftSibling;\n        // If the node is a leaf, set it's position based on the left siblings.\n        if (!node.hasChildren()) {\n            leftSibling = node.getLeftSibling();\n            if (leftSibling) {\n                node.preX = leftSibling.preX + siblingDistance;\n                node.mod = node.preX;\n            }\n            else {\n                node.preX = 0;\n            }\n        }\n        else {\n            // If the node has children, perform the recursive first walk for\n            // its children, and then calculate its shift in the apportion\n            // function (most crucial part of the algorithm).\n            var defaultAncestor = node.getLeftMostChild();\n            for (var _i = 0, _a = node.children; _i < _a.length; _i++) {\n                var child = _a[_i];\n                treeLayout.firstWalk(child);\n                defaultAncestor = treeLayout.apportion(child, defaultAncestor);\n            }\n            treeLayout.executeShifts(node);\n            var leftChild = node.getLeftMostChild(),\n                rightChild = node.getRightMostChild(), \n                // Set the position of the parent as a middle point of its\n                // children and move it by the value of the leftSibling (if it\n                // exists).\n                midPoint = (leftChild.preX + rightChild.preX) / 2;\n            leftSibling = node.getLeftSibling();\n            if (leftSibling) {\n                node.preX = leftSibling.preX + siblingDistance;\n                node.mod = node.preX - midPoint;\n            }\n            else {\n                node.preX = midPoint;\n            }\n        }\n    };\n    /**\n     * Pre order traversal of the tree, which sets the final xPosition of the\n     * node as its preX value and sum of all if it's parents' modifiers.\n     *\n     * @param {TreegraphNode} node\n     *        The node, for which the final position should be calculated.\n     * @param {number} modSum\n     *        The sum of modifiers of all of the parents.\n     */\n    TreegraphLayout.prototype.secondWalk = function (node, modSum) {\n        var treeLayout = this;\n        // When the chart is not inverted we want the tree to be positioned from\n        // left to right with root node close to the chart border, this is why\n        // x and y positions are switched.\n        node.yPosition = node.preX + modSum;\n        node.xPosition = node.level;\n        for (var _i = 0, _a = node.children; _i < _a.length; _i++) {\n            var child = _a[_i];\n            treeLayout.secondWalk(child, modSum + node.mod);\n        }\n    };\n    /**\n     *  Shift all children of the current node from right to left.\n     *\n     * @param {TreegraphNode} node\n     *        The parent node.\n     */\n    TreegraphLayout.prototype.executeShifts = function (node) {\n        var shift = 0,\n            change = 0;\n        for (var i = node.children.length - 1; i >= 0; i--) {\n            var childNode = node.children[i];\n            childNode.preX += shift;\n            childNode.mod += shift;\n            change += childNode.change;\n            shift += childNode.shift + change;\n        }\n    };\n    /**\n     * The core of the algorithm. The new subtree is combined with the previous\n     * subtrees. Threads are used to traverse the inside and outside contours of\n     * the left and right subtree up to the highest common level. The vertecies\n     * are left(right)Int(Out)node where Int means internal and Out means\n     * outernal. For summing up the modifiers along the contour we use the\n     * `left(right)Int(Out)mod` variable. Whenever two nodes of the inside\n     * contours are in conflict we commute the left one of the greatest uncommon\n     * ancestors using the getAncestor function and we call the moveSubtree\n     * method to shift the subtree and prepare the shifts of smaller subtrees.\n     * Finally we add a new thread (if necessary) and we adjust ancestor of\n     * right outernal node or defaultAncestor.\n     *\n     * @param {TreegraphNode} node\n     * @param {TreegraphNode} defaultAncestor\n     *        The default ancestor of the passed node.\n     */\n    TreegraphLayout.prototype.apportion = function (node, defaultAncestor) {\n        var treeLayout = this,\n            leftSibling = node.getLeftSibling();\n        if (leftSibling) {\n            var rightIntNode = node,\n                rightOutNode = node,\n                leftIntNode = leftSibling,\n                leftOutNode = rightIntNode.getLeftMostSibling(),\n                rightIntMod = rightIntNode.mod,\n                rightOutMod = rightOutNode.mod,\n                leftIntMod = leftIntNode.mod,\n                leftOutMod = leftOutNode.mod;\n            while (leftIntNode &&\n                leftIntNode.nextRight() &&\n                rightIntNode &&\n                rightIntNode.nextLeft()) {\n                leftIntNode = leftIntNode.nextRight();\n                leftOutNode = leftOutNode.nextLeft();\n                rightIntNode = rightIntNode.nextLeft();\n                rightOutNode = rightOutNode.nextRight();\n                rightOutNode.ancestor = node;\n                var siblingDistance = 1,\n                    shift = leftIntNode.preX +\n                        leftIntMod -\n                        (rightIntNode.preX + rightIntMod) +\n                        siblingDistance;\n                if (shift > 0) {\n                    treeLayout.moveSubtree(node.getAncestor(leftIntNode, defaultAncestor), node, shift);\n                    rightIntMod += shift;\n                    rightOutMod += shift;\n                }\n                leftIntMod += leftIntNode.mod;\n                rightIntMod += rightIntNode.mod;\n                leftOutMod += leftOutNode.mod;\n                rightOutMod += rightOutNode.mod;\n            }\n            if (leftIntNode &&\n                leftIntNode.nextRight() &&\n                !rightOutNode.nextRight()) {\n                rightOutNode.thread = leftIntNode.nextRight();\n                rightOutNode.mod += leftIntMod - rightOutMod;\n            }\n            if (rightIntNode &&\n                rightIntNode.nextLeft() &&\n                !leftOutNode.nextLeft()) {\n                leftOutNode.thread = rightIntNode.nextLeft();\n                leftOutNode.mod += rightIntMod - leftOutMod;\n            }\n            defaultAncestor = node;\n        }\n        return defaultAncestor;\n    };\n    /**\n     * Shifts the subtree from leftNode to rightNode.\n     *\n     * @param {TreegraphNode} leftNode\n     * @param {TreegraphNode} rightNode\n     * @param {number} shift\n     *        The value, by which the subtree should be moved.\n     */\n    TreegraphLayout.prototype.moveSubtree = function (leftNode, rightNode, shift) {\n        var subtrees = rightNode.relativeXPosition - leftNode.relativeXPosition;\n        rightNode.change -= shift / subtrees;\n        rightNode.shift += shift;\n        rightNode.preX += shift;\n        rightNode.mod += shift;\n        leftNode.change += shift / subtrees;\n    };\n    /**\n     * Clear values created in a beforeLayout.\n     *\n     * @param {TreegraphNode[]} nodes\n     *        All of the nodes of the Treegraph Series.\n     */\n    TreegraphLayout.prototype.afterLayout = function (nodes) {\n        for (var _i = 0, nodes_3 = nodes; _i < nodes_3.length; _i++) {\n            var node = nodes_3[_i];\n            if (node.oldParentNode) {\n                // Restore default connections\n                node.relativeXPosition = node.parentNode.relativeXPosition;\n                node.parent = node.oldParentNode.parent;\n                node.parentNode = node.oldParentNode;\n                // Delete dummyNode\n                delete node.oldParentNode.children[node.relativeXPosition];\n                node.oldParentNode.children[node.relativeXPosition] = node;\n                node.oldParentNode = void 0;\n            }\n        }\n    };\n    return TreegraphLayout;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Treegraph_TreegraphLayout = (TreegraphLayout);\n\n;// ./code/es5/es-modules/Series/Treegraph/TreegraphSeriesDefaults.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy Grzegorz Blachlinski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n/**\n * A treegraph series is a diagram, which shows a relation between ancestors\n * and descendants with a clear parent - child relation.\n * The best examples of the dataStructures, which best reflect this chart\n * are e.g. genealogy tree or directory structure.\n *\n * TODO change back the demo path\n * @sample highcharts/demo/treegraph-chart\n *         Treegraph Chart\n *\n * @extends      plotOptions.treemap\n * @excluding    layoutAlgorithm, dashStyle, linecap, lineWidth,\n *               negativeColor, threshold, zones, zoneAxis, colorAxis,\n *               colorKey, compare, dataGrouping, endAngle, gapSize, gapUnit,\n *               ignoreHiddenPoint, innerSize, joinBy, legendType, linecap,\n *               minSize, navigatorOptions, pointRange, allowTraversingTree,\n *               alternateStartingDirection, borderRadius, breadcrumbs,\n *               interactByLeaf, layoutStartingDirection, levelIsConstant,\n *               lineWidth, negativeColor, nodes, sortIndex, zoneAxis,\n *               zones, cluster\n *\n * @product      highcharts\n * @since 10.3.0\n * @requires     modules/treemap\n * @requires     modules/treegraph\n * @optionparent plotOptions.treegraph\n */\nvar TreegraphSeriesDefaults = {\n    /**\n     * Flips the positions of the nodes of a treegraph along the\n     * horizontal axis (vertical if chart is inverted).\n     *\n     * @sample highcharts/series-treegraph/reversed-nodes\n     *         Treegraph series with reversed nodes.\n     *\n     * @type    {boolean}\n     * @default false\n     * @product highcharts\n     * @since 10.3.0\n     */\n    reversed: false,\n    /**\n     * @extends   plotOptions.series.marker\n     * @excluding enabled, enabledThreshold\n     */\n    marker: {\n        radius: 10,\n        lineWidth: 0,\n        symbol: 'circle',\n        fillOpacity: 1,\n        states: {}\n    },\n    link: {\n        /**\n         * Modifier of the shape of the curved link. Works best for\n         * values between 0 and 1, where 0 is a straight line, and 1 is\n         * a shape close to the default one.\n         *\n         * @type      {number}\n         * @default   0.5\n         * @product   highcharts\n         * @since 10.3.0\n         * @apioption series.treegraph.link.curveFactor\n         */\n        /**\n         * The color of the links between nodes.\n         *\n         * @type {Highcharts.ColorString}\n         * @private\n         */\n        color: \"#666666\" /* Palette.neutralColor60 */,\n        /**\n         * The line width of the links connecting nodes, in pixels.\n         * @type {number}\n         *\n         * @private\n         */\n        lineWidth: 1,\n        /**\n         * Radius for the rounded corners of the links between nodes.\n         * Works for `default` link type.\n         *\n         * @private\n         */\n        radius: 10,\n        cursor: 'default',\n        /**\n         * Type of the link shape.\n         *\n         * @sample   highcharts/series-treegraph/link-types\n         *           Different link types\n         *\n         * @type {'default' | 'curved' | 'straight'}\n         * @product highcharts\n         *\n         */\n        type: 'curved'\n    },\n    /**\n     * Options applied to collapse Button. The collape button is the\n     * small button which indicates, that the node is collapsable.\n     */\n    collapseButton: {\n        /**\n         * Whether the button should be visible only when the node is\n         * hovered. When set to true, the button is hidden for nodes,\n         * which are not collapsed, and shown for the collapsed ones.\n         */\n        onlyOnHover: true,\n        /**\n         * Whether the button should be visible.\n         */\n        enabled: true,\n        /**\n         * The line width of the button in pixels\n         */\n        lineWidth: 1,\n        /**\n         * Offset of the button in the x direction.\n         */\n        x: 0,\n        /**\n         * Offset of the button in the y direction.\n         */\n        y: 0,\n        /**\n         * Height of the button.\n         */\n        height: 18,\n        /**\n         * Width of the button.\n         */\n        width: 18,\n        /**\n         * The symbol of the collapse button.\n         */\n        shape: 'circle',\n        /**\n         * CSS styles for the collapse button.\n         *\n         * In styled mode, the collapse button style is given in the\n         * `.highcharts-collapse-button` class.\n         */\n        style: {\n            cursor: 'pointer',\n            fontWeight: 'bold',\n            fontSize: '1em'\n        }\n    },\n    /**\n     * Whether the treegraph series should fill the entire plot area in the X\n     * axis direction, even when there are collapsed points.\n     *\n     * @sample  highcharts/series-treegraph/fillspace\n     *          Fill space demonstrated\n     *\n     * @product highcharts\n     */\n    fillSpace: false,\n    /**\n     * @extends plotOptions.series.tooltip\n     * @excluding clusterFormat\n     */\n    tooltip: {\n        /**\n         * The HTML of the point's line in the tooltip. Variables are\n         * enclosed by curly brackets. Available variables are\n         * `point.id`,  `point.fromNode.id`, `point.toNode.id`,\n         * `series.name`, `series.color` and other properties on the\n         * same form. Furthermore, This can also be overridden for each\n         * series, which makes it a good hook for displaying units. In\n         * styled mode, the dot is colored by a class name rather than\n         * the point color.\n         *\n         * @type {string}\n         * @since 10.3.0\n         * @product highcharts\n         */\n        linkFormat: '{point.fromNode.id} \\u2192 {point.toNode.id}',\n        pointFormat: '{point.id}'\n        /**\n         * A callback function for formatting the HTML output for a\n         * single link in the tooltip. Like the `linkFormat` string,\n         * but with more flexibility.\n         *\n         * @type {Highcharts.FormatterCallbackFunction.<Highcharts.Point>}\n         * @apioption series.treegraph.tooltip.linkFormatter\n         *\n         */\n    },\n    /**\n     * Options for the data labels appearing on top of the nodes and\n     * links. For treegraph charts, data labels are visible for the\n     * nodes by default, but hidden for links. This is controlled by\n     * modifying the `nodeFormat`, and the `format` that applies to\n     * links and is an empty string by default.\n     *\n     * @declare Highcharts.SeriesTreegraphDataLabelsOptionsObject\n     */\n    dataLabels: {\n        defer: true,\n        /**\n         * Options for a _link_ label text which should follow link\n         * connection. Border and background are disabled for a label\n         * that follows a path.\n         *\n         * **Note:** Only SVG-based renderer supports this option.\n         * Setting `useHTML` to true will disable this option.\n         *\n         * @sample highcharts/series-treegraph/link-text-path\n         *         Treegraph series with link text path dataLabels.\n         *\n         * @extends plotOptions.treegraph.dataLabels.textPath\n         * @since 10.3.0\n         */\n        linkTextPath: {\n            attributes: {\n                startOffset: '50%'\n            }\n        },\n        enabled: true,\n        linkFormatter: function () { return ''; },\n        padding: 5,\n        style: {\n            textOverflow: 'none'\n        }\n    },\n    /**\n     * The distance between nodes in a tree graph in the longitudinal direction.\n     * The longitudinal direction means the direction that the chart flows - in\n     * a horizontal chart the distance is horizontal, in an inverted chart\n     * (vertical), the distance is vertical.\n     *\n     * If a number is given, it denotes pixels. If a percentage string is given,\n     * the distance is a percentage of the rendered node width. A `nodeDistance`\n     * of `100%` will render equal widths for the nodes and the gaps between\n     * them.\n     *\n     * This option applies only when the `nodeWidth` option is `auto`, making\n     * the node width respond to the number of columns.\n     *\n     * @since 11.4.0\n     * @sample highcharts/series-treegraph/node-distance\n     *         Node distance of 100% means equal to node width\n     * @type   {number|string}\n     */\n    nodeDistance: 30,\n    /**\n     * The pixel width of each node in a, or the height in case the chart is\n     * inverted. For tree graphs, the node width is only applied if the marker\n     * symbol is `rect`, otherwise the `marker` sizing options apply.\n     *\n     * Can be a number or a percentage string, or `auto`. If `auto`, the nodes\n     * are sized to fill up the plot area in the longitudinal direction,\n     * regardless of the number of levels.\n     *\n     * @since 11.4.0\n     * @see    [treegraph.nodeDistance](#nodeDistance)\n     * @sample highcharts/series-treegraph/node-distance\n     *         Node width is auto and combined with node distance\n     *\n     * @type {number|string}\n     */\n    nodeWidth: void 0\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Treegraph_TreegraphSeriesDefaults = (TreegraphSeriesDefaults);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SVGElement\"],\"commonjs\":[\"highcharts\",\"SVGElement\"],\"commonjs2\":[\"highcharts\",\"SVGElement\"],\"root\":[\"Highcharts\",\"SVGElement\"]}\nvar highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_ = __webpack_require__(28);\nvar highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default = /*#__PURE__*/__webpack_require__.n(highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_);\n;// ./code/es5/es-modules/Extensions/TextPath.js\n/* *\n *\n *  Highcharts module with textPath functionality.\n *\n *  (c) 2009-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nvar deg2rad = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).deg2rad;\nvar TextPath_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, TextPath_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, uniqueKey = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).uniqueKey, defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, TextPath_extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend;\n/**\n * Set a text path for a `text` or `label` element, allowing the text to\n * flow along a path.\n *\n * In order to unset the path for an existing element, call `setTextPath`\n * with `{ enabled: false }` as the second argument.\n *\n * Text path support is not bundled into `highcharts.js`, and requires the\n * `modules/textpath.js` file. However, it is included in the script files of\n * those series types that use it by default\n *\n * @sample highcharts/members/renderer-textpath/ Text path demonstrated\n *\n * @function Highcharts.SVGElement#setTextPath\n *\n * @param {Highcharts.SVGElement|undefined} path\n *        Path to follow. If undefined, it allows changing options for the\n *        existing path.\n *\n * @param {Highcharts.DataLabelsTextPathOptionsObject} textPathOptions\n *        Options.\n *\n * @return {Highcharts.SVGElement} Returns the SVGElement for chaining.\n */\nfunction setTextPath(path, textPathOptions) {\n    var _this = this;\n    // Defaults\n    textPathOptions = TextPath_merge(true, {\n        enabled: true,\n        attributes: {\n            dy: -5,\n            startOffset: '50%',\n            textAnchor: 'middle'\n        }\n    }, textPathOptions);\n    var url = this.renderer.url,\n        textWrapper = this.text || this,\n        textPath = textWrapper.textPath,\n        attributes = textPathOptions.attributes,\n        enabled = textPathOptions.enabled;\n    path = path || (textPath && textPath.path);\n    // Remove previously added event\n    if (textPath) {\n        textPath.undo();\n    }\n    if (path && enabled) {\n        var undo = TextPath_addEvent(textWrapper, 'afterModifyTree',\n            function (e) {\n                if (path && enabled) {\n                    // Set ID for the path\n                    var textPathId = path.attr('id');\n                if (!textPathId) {\n                    path.attr('id', textPathId = uniqueKey());\n                }\n                // Set attributes for the <text>\n                var textAttribs = {\n                        // `dx`/`dy` options must by set on <text> (parent), the\n                        // rest should be set on <textPath>\n                        x: 0,\n                        y: 0\n                    };\n                if (defined(attributes.dx)) {\n                    textAttribs.dx = attributes.dx;\n                    delete attributes.dx;\n                }\n                if (defined(attributes.dy)) {\n                    textAttribs.dy = attributes.dy;\n                    delete attributes.dy;\n                }\n                textWrapper.attr(textAttribs);\n                // Handle label properties\n                _this.attr({ transform: '' });\n                if (_this.box) {\n                    _this.box = _this.box.destroy();\n                }\n                // Wrap the nodes in a textPath\n                var children = e.nodes.slice(0);\n                e.nodes.length = 0;\n                e.nodes[0] = {\n                    tagName: 'textPath',\n                    attributes: TextPath_extend(attributes, {\n                        'text-anchor': attributes.textAnchor,\n                        href: \"\" + url + \"#\".concat(textPathId)\n                    }),\n                    children: children\n                };\n            }\n        });\n        // Set the reference\n        textWrapper.textPath = { path: path, undo: undo };\n    }\n    else {\n        textWrapper.attr({ dx: 0, dy: 0 });\n        delete textWrapper.textPath;\n    }\n    if (this.added) {\n        // Rebuild text after added\n        textWrapper.textCache = '';\n        this.renderer.buildText(textWrapper);\n    }\n    return this;\n}\n/**\n * Attach a polygon to a bounding box if the element contains a textPath.\n *\n * @function Highcharts.SVGElement#setPolygon\n *\n * @param {any} event\n *        An event containing a bounding box object\n *\n * @return {Highcharts.BBoxObject} Returns the bounding box object.\n */\nfunction setPolygon(event) {\n    var _a;\n    var bBox = event.bBox,\n        tp = (_a = this.element) === null || _a === void 0 ? void 0 : _a.querySelector('textPath');\n    if (tp) {\n        var polygon = [], _b = this.renderer.fontMetrics(this.element), b_1 = _b.b, h = _b.h, descender_1 = h - b_1, lineCleanerRegex = new RegExp('(<tspan>|' +\n                '<tspan(?!\\\\sclass=\"highcharts-br\")[^>]*>|' +\n                '<\\\\/tspan>)', 'g'), lines = tp\n                .innerHTML\n                .replace(lineCleanerRegex, '')\n                .split(/<tspan class=\"highcharts-br\"[^>]*>/), numOfLines = lines.length;\n        // Calculate top and bottom coordinates for\n        // either the start or the end of a single\n        // character, and append it to the polygon.\n        var appendTopAndBottom = function (charIndex,\n            positionOfChar) {\n                var x = positionOfChar.x,\n            y = positionOfChar.y,\n            rotation = (tp.getRotationOfChar(charIndex) - 90) * deg2rad,\n            cosRot = Math.cos(rotation),\n            sinRot = Math.sin(rotation);\n            return [\n                [\n                    x - descender_1 * cosRot,\n                    y - descender_1 * sinRot\n                ],\n                [\n                    x + b_1 * cosRot,\n                    y + b_1 * sinRot\n                ]\n            ];\n        };\n        for (var i = 0, lineIndex = 0; lineIndex < numOfLines; lineIndex++) {\n            var line = lines[lineIndex],\n                lineLen = line.length;\n            for (var lineCharIndex = 0; lineCharIndex < lineLen; lineCharIndex += 5) {\n                try {\n                    var srcCharIndex = (i +\n                            lineCharIndex +\n                            lineIndex),\n                        _c = appendTopAndBottom(srcCharIndex,\n                        tp.getStartPositionOfChar(srcCharIndex)),\n                        lower = _c[0],\n                        upper = _c[1];\n                    if (lineCharIndex === 0) {\n                        polygon.push(upper);\n                        polygon.push(lower);\n                    }\n                    else {\n                        if (lineIndex === 0) {\n                            polygon.unshift(upper);\n                        }\n                        if (lineIndex === numOfLines - 1) {\n                            polygon.push(lower);\n                        }\n                    }\n                }\n                catch (e) {\n                    // Safari fails on getStartPositionOfChar even if the\n                    // character is within the `textContent.length`\n                    break;\n                }\n            }\n            i += lineLen - 1;\n            try {\n                var srcCharIndex = i + lineIndex,\n                    charPos = tp.getEndPositionOfChar(srcCharIndex),\n                    _d = appendTopAndBottom(srcCharIndex,\n                    charPos),\n                    lower = _d[0],\n                    upper = _d[1];\n                polygon.unshift(upper);\n                polygon.unshift(lower);\n            }\n            catch (e) {\n                // Safari fails on getStartPositionOfChar even if the character\n                // is within the `textContent.length`\n                break;\n            }\n        }\n        // Close it\n        if (polygon.length) {\n            polygon.push(polygon[0].slice());\n        }\n        bBox.polygon = polygon;\n    }\n    return bBox;\n}\n/**\n * Draw text along a textPath for a dataLabel.\n *\n * @function Highcharts.SVGElement#setTextPath\n *\n * @param {any} event\n *        An event containing label options\n *\n * @return {void}\n */\nfunction drawTextPath(event) {\n    var _a;\n    var labelOptions = event.labelOptions,\n        point = event.point,\n        textPathOptions = (labelOptions[point.formatPrefix + 'TextPath'] ||\n            labelOptions.textPath);\n    if (textPathOptions && !labelOptions.useHTML) {\n        this.setTextPath(((_a = point.getDataLabelPath) === null || _a === void 0 ? void 0 : _a.call(point, this)) || point.graphic, textPathOptions);\n        if (point.dataLabelPath &&\n            !textPathOptions.enabled) {\n            // Clean the DOM\n            point.dataLabelPath = (point.dataLabelPath.destroy());\n        }\n    }\n}\nfunction compose(SVGElementClass) {\n    TextPath_addEvent(SVGElementClass, 'afterGetBBox', setPolygon);\n    TextPath_addEvent(SVGElementClass, 'beforeAddingDataLabel', drawTextPath);\n    var svgElementProto = SVGElementClass.prototype;\n    if (!svgElementProto.setTextPath) {\n        svgElementProto.setTextPath = setTextPath;\n    }\n}\nvar TextPath = {\n    compose: compose\n};\n/* harmony default export */ var Extensions_TextPath = (TextPath);\n\n;// ./code/es5/es-modules/Series/Treegraph/TreegraphSeries.js\n/* *\n *\n *  (c) 2010-2025 Pawel Lysy Grzegorz Blachlinski\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar TreegraphSeries_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar TreegraphSeries_getLinkPath = Series_PathUtilities.getLinkPath;\n\nvar seriesProto = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).series.prototype, _a = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes, TreemapSeries = _a.treemap, ColumnSeries = _a.column;\n\nvar symbols = (highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default()).prototype.symbols;\n\n\n\nvar TreegraphSeries_getLevelOptions = Series_TreeUtilities.getLevelOptions, TreegraphSeries_getNodeWidth = Series_TreeUtilities.getNodeWidth;\n\nvar arrayMax = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).arrayMax, crisp = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).crisp, TreegraphSeries_extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, TreegraphSeries_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, TreegraphSeries_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, TreegraphSeries_relativeLength = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).relativeLength, splat = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).splat;\n\n\n\n\n\nExtensions_TextPath.compose((highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default()));\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Treegraph series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.treegraph\n *\n * @augments Highcharts.Series\n */\nvar TreegraphSeries = /** @class */ (function (_super) {\n    TreegraphSeries_extends(TreegraphSeries, _super);\n    function TreegraphSeries() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        var _this = _super !== null && _super.apply(this,\n            arguments) || this;\n        _this.nodeList = [];\n        _this.links = [];\n        return _this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    TreegraphSeries.prototype.init = function () {\n        _super.prototype.init.apply(this, arguments);\n        this.layoutAlgorythm = new Treegraph_TreegraphLayout();\n        // Register the link data labels in the label collector for overlap\n        // detection.\n        var series = this,\n            collectors = this.chart.labelCollectors,\n            collectorFunc = function () {\n                var linkLabels = [];\n            // Check links for overlap\n            if (series.options.dataLabels &&\n                !splat(series.options.dataLabels)[0].allowOverlap) {\n                for (var _i = 0, _a = (series.links || []); _i < _a.length; _i++) {\n                    var link = _a[_i];\n                    if (link.dataLabel) {\n                        linkLabels.push(link.dataLabel);\n                    }\n                }\n            }\n            return linkLabels;\n        };\n        // Only add the collector function if it is not present\n        if (!collectors.some(function (f) { return f.name === 'collectorFunc'; })) {\n            collectors.push(collectorFunc);\n        }\n    };\n    /**\n     * Calculate `a` and `b` parameters of linear transformation, where\n     * `finalPosition = a * calculatedPosition + b`.\n     *\n     * @return {LayoutModifiers} `a` and `b` parameter for x and y direction.\n     */\n    TreegraphSeries.prototype.getLayoutModifiers = function () {\n        var _this = this;\n        var chart = this.chart,\n            series = this,\n            plotSizeX = chart.plotSizeX,\n            plotSizeY = chart.plotSizeY,\n            columnCount = arrayMax(this.points.map(function (p) { return p.node.xPosition; }));\n        var minX = Infinity,\n            maxX = -Infinity,\n            minY = Infinity,\n            maxY = -Infinity,\n            maxXSize = 0,\n            minXSize = 0,\n            maxYSize = 0,\n            minYSize = 0;\n        this.points.forEach(function (point) {\n            var _a;\n            // When fillSpace is on, stop the layout calculation when the hidden\n            // points are reached. (#19038)\n            if (_this.options.fillSpace && !point.visible) {\n                return;\n            }\n            var node = point.node,\n                level = series.mapOptionsToLevel[point.node.level] || {},\n                markerOptions = TreegraphSeries_merge(_this.options.marker,\n                level.marker,\n                point.options.marker),\n                nodeWidth = (_a = markerOptions.width) !== null && _a !== void 0 ? _a : TreegraphSeries_getNodeWidth(_this,\n                columnCount),\n                radius = TreegraphSeries_relativeLength(markerOptions.radius || 0,\n                Math.min(plotSizeX,\n                plotSizeY)),\n                symbol = markerOptions.symbol,\n                nodeSizeY = (symbol === 'circle' || !markerOptions.height) ?\n                    radius * 2 :\n                    TreegraphSeries_relativeLength(markerOptions.height,\n                plotSizeY),\n                nodeSizeX = symbol === 'circle' || !nodeWidth ?\n                    radius * 2 :\n                    TreegraphSeries_relativeLength(nodeWidth,\n                plotSizeX);\n            node.nodeSizeX = nodeSizeX;\n            node.nodeSizeY = nodeSizeY;\n            var lineWidth;\n            if (node.xPosition <= minX) {\n                minX = node.xPosition;\n                lineWidth = markerOptions.lineWidth || 0;\n                minXSize = Math.max(nodeSizeX + lineWidth, minXSize);\n            }\n            if (node.xPosition >= maxX) {\n                maxX = node.xPosition;\n                lineWidth = markerOptions.lineWidth || 0;\n                maxXSize = Math.max(nodeSizeX + lineWidth, maxXSize);\n            }\n            if (node.yPosition <= minY) {\n                minY = node.yPosition;\n                lineWidth = markerOptions.lineWidth || 0;\n                minYSize = Math.max(nodeSizeY + lineWidth, minYSize);\n            }\n            if (node.yPosition >= maxY) {\n                maxY = node.yPosition;\n                lineWidth = markerOptions.lineWidth || 0;\n                maxYSize = Math.max(nodeSizeY + lineWidth, maxYSize);\n            }\n        });\n        // Calculate the values of linear transformation, which will later be\n        // applied as `nodePosition = a * x + b` for each direction.\n        var ay = maxY === minY ?\n                1 :\n                (plotSizeY - (minYSize + maxYSize) / 2) / (maxY - minY), by = maxY === minY ? plotSizeY / 2 : -ay * minY + minYSize / 2, ax = maxX === minX ?\n                1 :\n                (plotSizeX - (maxXSize + maxXSize) / 2) / (maxX - minX), bx = maxX === minX ? plotSizeX / 2 : -ax * minX + minXSize / 2;\n        return { ax: ax, bx: bx, ay: ay, by: by };\n    };\n    TreegraphSeries.prototype.getLinks = function () {\n        var _this = this;\n        var series = this;\n        var links = [];\n        this.data.forEach(function (point) {\n            var levelOptions = series.mapOptionsToLevel[point.node.level || 0] || {};\n            if (point.node.parent) {\n                var pointOptions = TreegraphSeries_merge(levelOptions,\n                    point.options);\n                if (!point.linkToParent || point.linkToParent.destroyed) {\n                    var link = new series.LinkClass(series,\n                        pointOptions,\n                        void 0,\n                        point);\n                    point.linkToParent = link;\n                }\n                else {\n                    // #19552\n                    point.collapsed = TreegraphSeries_pick(point.collapsed, (_this.mapOptionsToLevel[point.node.level] || {}).collapsed);\n                    point.linkToParent.visible =\n                        point.linkToParent.toNode.visible;\n                }\n                point.linkToParent.index = links.push(point.linkToParent) - 1;\n            }\n            else {\n                if (point.linkToParent) {\n                    series.links.splice(point.linkToParent.index);\n                    point.linkToParent.destroy();\n                    delete point.linkToParent;\n                }\n            }\n        });\n        return links;\n    };\n    TreegraphSeries.prototype.buildTree = function (id, index, level, list, parent) {\n        var point = this.points[index];\n        level = (point && point.level) || level;\n        return _super.prototype.buildTree.call(this, id, index, level, list, parent);\n    };\n    TreegraphSeries.prototype.markerAttribs = function () {\n        // The super Series.markerAttribs returns { width: NaN, height: NaN },\n        // so just disable this for now.\n        return {};\n    };\n    TreegraphSeries.prototype.setCollapsedStatus = function (node, visibility) {\n        var _this = this;\n        var point = node.point;\n        if (point) {\n            // Take the level options into account.\n            point.collapsed = TreegraphSeries_pick(point.collapsed, (this.mapOptionsToLevel[node.level] || {}).collapsed);\n            point.visible = visibility;\n            visibility = visibility === false ? false : !point.collapsed;\n        }\n        node.children.forEach(function (childNode) {\n            _this.setCollapsedStatus(childNode, visibility);\n        });\n    };\n    TreegraphSeries.prototype.drawTracker = function () {\n        ColumnSeries.prototype.drawTracker.apply(this, arguments);\n        ColumnSeries.prototype.drawTracker.call(this, this.links);\n    };\n    /**\n     * Run pre-translation by generating the nodeColumns.\n     * @private\n     */\n    TreegraphSeries.prototype.translate = function () {\n        var _this = this;\n        var series = this,\n            options = series.options;\n        // NOTE: updateRootId modifies series.\n        var rootId = Series_TreeUtilities.updateRootId(series),\n            rootNode;\n        // Call prototype function\n        seriesProto.translate.call(series);\n        var tree = series.tree = series.getTree();\n        rootNode = series.nodeMap[rootId];\n        if (rootId !== '' && (!rootNode || !rootNode.children.length)) {\n            series.setRootNode('', false);\n            rootId = series.rootNode;\n            rootNode = series.nodeMap[rootId];\n        }\n        series.mapOptionsToLevel = TreegraphSeries_getLevelOptions({\n            from: rootNode.level + 1,\n            levels: options.levels,\n            to: tree.height,\n            defaults: {\n                levelIsConstant: series.options.levelIsConstant,\n                colorByPoint: options.colorByPoint\n            }\n        });\n        this.setCollapsedStatus(tree, true);\n        series.links = series.getLinks();\n        series.setTreeValues(tree);\n        this.layoutAlgorythm.calculatePositions(series);\n        series.layoutModifier = this.getLayoutModifiers();\n        this.points.forEach(function (point) {\n            _this.translateNode(point);\n        });\n        this.points.forEach(function (point) {\n            if (point.linkToParent) {\n                _this.translateLink(point.linkToParent);\n            }\n        });\n        if (!options.colorByPoint) {\n            series.setColorRecursive(series.tree);\n        }\n    };\n    TreegraphSeries.prototype.translateLink = function (link) {\n        var _a,\n            _b,\n            _c,\n            _d,\n            _e;\n        var fromNode = link.fromNode,\n            toNode = link.toNode,\n            linkWidth = ((_a = this.options.link) === null || _a === void 0 ? void 0 : _a.lineWidth) || 0,\n            factor = TreegraphSeries_pick((_b = this.options.link) === null || _b === void 0 ? void 0 : _b.curveFactor, 0.5),\n            type = TreegraphSeries_pick((_c = link.options.link) === null || _c === void 0 ? void 0 : _c.type, (_d = this.options.link) === null || _d === void 0 ? void 0 : _d.type, 'default');\n        if (fromNode.shapeArgs && toNode.shapeArgs) {\n            var fromNodeWidth = (fromNode.shapeArgs.width || 0),\n                inverted = this.chart.inverted,\n                y1 = crisp((fromNode.shapeArgs.y || 0) +\n                    (fromNode.shapeArgs.height || 0) / 2,\n                linkWidth),\n                y2 = crisp((toNode.shapeArgs.y || 0) +\n                    (toNode.shapeArgs.height || 0) / 2,\n                linkWidth);\n            var x1 = crisp((fromNode.shapeArgs.x || 0) + fromNodeWidth,\n                linkWidth),\n                x2 = crisp(toNode.shapeArgs.x || 0,\n                linkWidth);\n            if (inverted) {\n                x1 -= fromNodeWidth;\n                x2 += (toNode.shapeArgs.width || 0);\n            }\n            var diff = toNode.node.xPosition - fromNode.node.xPosition;\n            link.shapeType = 'path';\n            var fullWidth = Math.abs(x2 - x1) + fromNodeWidth,\n                width = (fullWidth / diff) - fromNodeWidth,\n                offset = width * factor * (inverted ? -1 : 1);\n            var xMiddle = crisp((x2 + x1) / 2,\n                linkWidth);\n            link.plotX = xMiddle;\n            link.plotY = y2;\n            link.shapeArgs = {\n                d: TreegraphSeries_getLinkPath[type]({\n                    x1: x1,\n                    y1: y1,\n                    x2: x2,\n                    y2: y2,\n                    width: width,\n                    offset: offset,\n                    inverted: inverted,\n                    parentVisible: toNode.visible,\n                    radius: (_e = this.options.link) === null || _e === void 0 ? void 0 : _e.radius\n                })\n            };\n            link.dlBox = {\n                x: (x1 + x2) / 2,\n                y: (y1 + y2) / 2,\n                height: linkWidth,\n                width: 0\n            };\n            link.tooltipPos = inverted ? [\n                (this.chart.plotSizeY || 0) - link.dlBox.y,\n                (this.chart.plotSizeX || 0) - link.dlBox.x\n            ] : [\n                link.dlBox.x,\n                link.dlBox.y\n            ];\n        }\n    };\n    /**\n     * Private method responsible for adjusting the dataLabel options for each\n     * node-point individually.\n     */\n    TreegraphSeries.prototype.drawNodeLabels = function (points) {\n        var _a;\n        var _b,\n            _c,\n            _d;\n        var series = this,\n            mapOptionsToLevel = series.mapOptionsToLevel;\n        var options,\n            level;\n        for (var _i = 0, points_1 = points; _i < points_1.length; _i++) {\n            var point = points_1[_i];\n            level = mapOptionsToLevel[point.node.level];\n            // Set options to new object to avoid problems with scope\n            options = { style: {} };\n            // If options for level exists, include them as well\n            if (level && level.dataLabels) {\n                options = TreegraphSeries_merge(options, level.dataLabels);\n                series.hasDataLabels = function () { return true; };\n            }\n            // Set dataLabel width to the width of the point shape.\n            if (point.shapeArgs &&\n                series.options.dataLabels) {\n                var css = {};\n                var _e = point.shapeArgs,\n                    _f = _e.width,\n                    width = _f === void 0 ? 0 : _f,\n                    _g = _e.height,\n                    height = _g === void 0 ? 0 : _g;\n                if (series.chart.inverted) {\n                    _a = [height, width], width = _a[0], height = _a[1];\n                }\n                if (!((_b = splat(series.options.dataLabels)[0].style) === null || _b === void 0 ? void 0 : _b.width)) {\n                    css.width = \"\" + width + \"px\";\n                }\n                if (!((_c = splat(series.options.dataLabels)[0].style) === null || _c === void 0 ? void 0 : _c.lineClamp)) {\n                    css.lineClamp = Math.floor(height / 16);\n                }\n                TreegraphSeries_extend(options.style, css);\n                (_d = point.dataLabel) === null || _d === void 0 ? void 0 : _d.css(css);\n            }\n            // Merge custom options with point options\n            point.dlOptions = TreegraphSeries_merge(options, point.options.dataLabels);\n        }\n        seriesProto.drawDataLabels.call(this, points);\n    };\n    /**\n     * Override alignDataLabel so that position is always calculated and the\n     * label is faded in and out instead of hidden/shown when collapsing and\n     * expanding nodes.\n     */\n    TreegraphSeries.prototype.alignDataLabel = function (point, dataLabel) {\n        var visible = point.visible;\n        // Force position calculation and visibility\n        point.visible = true;\n        _super.prototype.alignDataLabel.apply(this, arguments);\n        // Fade in or out\n        dataLabel.animate({\n            opacity: visible === false ? 0 : 1\n        }, void 0, function () {\n            // Hide data labels that belong to hidden points (#18891)\n            visible || dataLabel.hide();\n        });\n        // Reset\n        point.visible = visible;\n    };\n    /**\n     * Treegraph has two separate collecions of nodes and lines,\n     * render dataLabels for both sets.\n     */\n    TreegraphSeries.prototype.drawDataLabels = function () {\n        if (this.options.dataLabels) {\n            this.options.dataLabels = splat(this.options.dataLabels);\n            // Render node labels.\n            this.drawNodeLabels(this.points);\n            // Render link labels.\n            seriesProto.drawDataLabels.call(this, this.links);\n        }\n    };\n    TreegraphSeries.prototype.destroy = function () {\n        // Links must also be destroyed.\n        if (this.links) {\n            for (var _i = 0, _a = this.links; _i < _a.length; _i++) {\n                var link = _a[_i];\n                link.destroy();\n            }\n            this.links.length = 0;\n        }\n        return seriesProto.destroy.apply(this, arguments);\n    };\n    /**\n     * Return the presentational attributes.\n     * @private\n     */\n    TreegraphSeries.prototype.pointAttribs = function (point, state) {\n        var series = this,\n            levelOptions = point &&\n                series.mapOptionsToLevel[point.node.level || 0] || {},\n            options = point && point.options,\n            stateOptions = (levelOptions.states &&\n                levelOptions.states[state]) ||\n                {};\n        if (point) {\n            point.options.marker = TreegraphSeries_merge(series.options.marker, levelOptions.marker, point.options.marker);\n        }\n        var linkColor = TreegraphSeries_pick(stateOptions && stateOptions.link && stateOptions.link.color,\n            options && options.link && options.link.color,\n            levelOptions && levelOptions.link && levelOptions.link.color,\n            series.options.link && series.options.link.color),\n            linkLineWidth = TreegraphSeries_pick(stateOptions && stateOptions.link &&\n                stateOptions.link.lineWidth,\n            options && options.link && options.link.lineWidth,\n            levelOptions && levelOptions.link &&\n                levelOptions.link.lineWidth,\n            series.options.link && series.options.link.lineWidth),\n            attribs = seriesProto.pointAttribs.call(series,\n            point,\n            state);\n        if (point) {\n            if (point.isLink) {\n                attribs.stroke = linkColor;\n                attribs['stroke-width'] = linkLineWidth;\n                delete attribs.fill;\n            }\n            if (!point.visible) {\n                attribs.opacity = 0;\n            }\n        }\n        return attribs;\n    };\n    TreegraphSeries.prototype.drawPoints = function () {\n        TreemapSeries.prototype.drawPoints.apply(this, arguments);\n        ColumnSeries.prototype.drawPoints.call(this, this.links);\n    };\n    /**\n     * Run translation operations for one node.\n     * @private\n     */\n    TreegraphSeries.prototype.translateNode = function (point) {\n        var chart = this.chart,\n            node = point.node,\n            plotSizeY = chart.plotSizeY,\n            plotSizeX = chart.plotSizeX, \n            // Get the layout modifiers which are common for all nodes.\n            _a = this.layoutModifier,\n            ax = _a.ax,\n            bx = _a.bx,\n            ay = _a.ay,\n            by = _a.by,\n            x = ax * node.xPosition + bx,\n            y = ay * node.yPosition + by,\n            level = this.mapOptionsToLevel[node.level] || {},\n            markerOptions = TreegraphSeries_merge(this.options.marker,\n            level.marker,\n            point.options.marker),\n            symbol = markerOptions.symbol,\n            height = node.nodeSizeY,\n            width = node.nodeSizeX,\n            reversed = this.options.reversed,\n            nodeX = node.x = (chart.inverted ?\n                plotSizeX - width / 2 - x :\n                x - width / 2),\n            nodeY = node.y = (!reversed ?\n                plotSizeY - y - height / 2 :\n                y - height / 2),\n            borderRadius = TreegraphSeries_pick(point.options.borderRadius,\n            level.borderRadius,\n            this.options.borderRadius),\n            symbolFn = symbols[symbol || 'circle'];\n        if (symbolFn === void 0) {\n            point.hasImage = true;\n            point.shapeType = 'image';\n            point.imageUrl = symbol.match(/^url\\((.*?)\\)$/)[1];\n        }\n        else {\n            point.shapeType = 'path';\n        }\n        if (!point.visible && point.linkToParent) {\n            var parentNode = point.linkToParent.fromNode;\n            if (parentNode) {\n                var parentShapeArgs = parentNode.shapeArgs || {},\n                    _b = parentShapeArgs.x,\n                    x_1 = _b === void 0 ? 0 : _b,\n                    _c = parentShapeArgs.y,\n                    y_1 = _c === void 0 ? 0 : _c,\n                    _d = parentShapeArgs.width,\n                    width_1 = _d === void 0 ? 0 : _d,\n                    _e = parentShapeArgs.height,\n                    height_1 = _e === void 0 ? 0 : _e;\n                if (!point.shapeArgs) {\n                    point.shapeArgs = {};\n                }\n                if (!point.hasImage) {\n                    TreegraphSeries_extend(point.shapeArgs, {\n                        d: symbolFn(x_1, y_1, width_1, height_1, borderRadius ? { r: borderRadius } : void 0)\n                    });\n                }\n                TreegraphSeries_extend(point.shapeArgs, { x: x_1, y: y_1 });\n                point.plotX = parentNode.plotX;\n                point.plotY = parentNode.plotY;\n            }\n        }\n        else {\n            point.plotX = nodeX;\n            point.plotY = nodeY;\n            point.shapeArgs = {\n                x: nodeX,\n                y: nodeY,\n                width: width,\n                height: height,\n                cursor: !point.node.isLeaf ? 'pointer' : 'default'\n            };\n            if (!point.hasImage) {\n                point.shapeArgs.d = symbolFn(nodeX, nodeY, width, height, borderRadius ? { r: borderRadius } : void 0);\n            }\n        }\n        // Set the anchor position for tooltip.\n        point.tooltipPos = chart.inverted ?\n            [plotSizeY - nodeY - height / 2, plotSizeX - nodeX - width / 2] :\n            [nodeX + width / 2, nodeY];\n    };\n    TreegraphSeries.defaultOptions = TreegraphSeries_merge(TreemapSeries.defaultOptions, Treegraph_TreegraphSeriesDefaults);\n    return TreegraphSeries;\n}(TreemapSeries));\nTreegraphSeries_extend(TreegraphSeries.prototype, {\n    pointClass: Treegraph_TreegraphPoint,\n    NodeClass: Treegraph_TreegraphNode,\n    LinkClass: TreegraphLink\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('treegraph', TreegraphSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Treegraph_TreegraphSeries = ((/* unused pure expression or super */ null && (TreegraphSeries)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `treegraph` series. If the [type](#series.treegraph.type)\n * option is not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.treegraph\n * @exclude   allowDrillToNode, boostBlending, boostThreshold, curveFactor,\n * centerInCategory, connectEnds, connectNulls, colorAxis, colorKey,\n * dataSorting, dragDrop, findNearestPointBy, getExtremesFromAll, groupPadding,\n * headers, layout, nodePadding, nodeSizeBy, pointInterval, pointIntervalUnit,\n * pointPlacement, pointStart, relativeXValue, softThreshold, stack, stacking,\n * step, traverseUpButton, xAxis, yAxis, zoneAxis, zones\n * @product   highcharts\n * @requires  modules/treemap\n * @requires  modules/treegraph\n * @apioption series.treegraph\n */\n/**\n * @extends   plotOptions.series.marker\n * @excluding enabled, enabledThreshold\n * @apioption series.treegraph.marker\n */\n/**\n * @type      {Highcharts.SeriesTreegraphDataLabelsOptionsObject|Array<Highcharts.SeriesTreegraphDataLabelsOptionsObject>}\n * @product   highcharts\n * @apioption series.treegraph.data.dataLabels\n */\n/**\n * @sample highcharts/series-treegraph/level-options\n *          Treegraph chart with level options applied\n *\n * @type      {Array<*>}\n * @excluding layoutStartingDirection, layoutAlgorithm\n * @apioption series.treegraph.levels\n */\n/**\n * Set collapsed status for nodes level-wise.\n * @type {boolean}\n * @apioption series.treegraph.levels.collapsed\n */\n/**\n * Set marker options for nodes at the level.\n * @extends   series.treegraph.marker\n * @apioption series.treegraph.levels.marker\n */\n/**\n * An array of data points for the series. For the `treegraph` series type,\n * points can be given in the following ways:\n *\n * 1. The array of arrays, with `keys` property, which defines how the fields in\n *     array should be interpreted\n *    ```js\n *       keys: ['id', 'parent'],\n *       data: [\n *           ['Category1'],\n *           ['Category1', 'Category2']\n *       ]\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the\n *    series' [turboThreshold](#series.area.turboThreshold),\n *    this option is not available.\n *    The data of the treegraph series needs to be formatted in such a way, that\n *    there are no circular dependencies on the nodes\n *\n *  ```js\n *     data: [{\n *         id: 'Category1'\n *     }, {\n *         id: 'Category1',\n *         parent: 'Category2',\n *     }]\n *  ```\n *\n * @type      {Array<*>}\n * @extends   series.treemap.data\n * @product   highcharts\n * @excluding outgoing, weight, value\n * @apioption series.treegraph.data\n */\n/**\n * Options used for button, which toggles the collapse status of the node.\n *\n *\n * @apioption series.treegraph.data.collapseButton\n */\n/**\n * If point's children should be initially hidden\n *\n * @sample highcharts/series-treegraph/level-options\n *          Treegraph chart with initially hidden children\n *\n * @type {boolean}\n * @apioption series.treegraph.data.collapsed\n */\n''; // Gets doclets above into transpiled version\n\n;// ./code/es5/es-modules/masters/modules/treegraph.js\n\n\n\n\n/* harmony default export */ var treegraph_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__540__", "__WEBPACK_EXTERNAL_MODULE__260__", "__WEBPACK_EXTERNAL_MODULE__620__", "__WEBPACK_EXTERNAL_MODULE__28__", "extendStatics", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "treegraph_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_", "highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default", "__extends", "b", "setPrototypeOf", "__proto__", "Array", "p", "__", "constructor", "create", "TreegraphNode", "_super", "_this", "apply", "arguments", "mod", "shift", "change", "children", "preX", "hidden", "wasVisited", "collapsed", "nextLeft", "getLeftMostChild", "thread", "nextRight", "getRightMostChild", "getAncestor", "leftIntNode", "defaultAncestor", "leftAnc", "ancestor", "getLeftMostSibling", "parent", "getParent", "_i", "_a", "length", "child", "point", "visible", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i", "getLeftSibling", "relativeXPosition", "parentNode", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "seriesTypes", "treemap", "NodeClass", "highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_", "highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default", "TreegraphPoint_extends", "TypeError", "String", "TreemapPoint", "pointClass", "addEvent", "fireEvent", "merge", "TreegraphPoint", "dataLabelOnHidden", "isLink", "setState", "draw", "graphic", "animate", "visibility", "renderCollapseButton", "series", "parentGroup", "levelOptions", "mapOptionsToLevel", "node", "level", "btnOptions", "options", "collapseButton", "width", "height", "shape", "style", "chart", "calculatedOpacity", "onlyOnHover", "state", "shapeArgs", "collapseButtonOptions", "enabled", "_b", "getCollapseBtnPosition", "x", "y", "attr", "text", "rotation", "inverted", "rotationOriginX", "rotationOriginY", "opacity", "destroy", "fill", "fillColor", "color", "renderer", "label", "padding", "stroke", "lineColor", "lineWidth", "align", "zIndex", "addClass", "removeClass", "css", "getContrast", "add", "element", "toggleCollapse", "update", "redraw", "linkToParent", "btnWidth", "btnHeight", "_c", "_d", "_e", "btn", "states", "hover", "animation", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_", "isArray", "extend", "isNumber", "isObject", "TreeUtilities_merge", "pick", "<PERSON><PERSON><PERSON><PERSON>", "params", "defaults", "converted", "from", "to", "levels", "result", "reduce", "item", "levelIsConstant", "columnCount", "nodeDistance", "nodeWidth", "plotSizeX", "test", "fraction", "parseFloat", "nDistance", "Number", "rootId", "rootNode", "userOptions", "TreegraphLink_extends", "TreegraphLink_pick", "TreegraphLink_extend", "LinkPoint", "dataLabelOnNull", "formatPrefix", "fromNode", "toNode", "id", "runEvent", "oldOptions", "column", "TreegraphLayout", "createDummyNode", "gapSize", "dummy<PERSON>ode", "push", "oldParentNode", "calculatePositions", "nodes", "nodeList", "resetValues", "tree", "treeLayout", "calculateRelativeX", "beforeLayout", "firstWalk", "secondWalk", "afterLayout", "nodes_1", "nodes_2", "index", "iEnd", "leftSibling", "apportion", "executeShifts", "leftChild", "<PERSON><PERSON><PERSON><PERSON>", "midPoint", "modSum", "yPosition", "xPosition", "childNode", "rightIntNode", "rightOutNode", "leftOutNode", "rightIntMod", "rightOutMod", "leftIntMod", "leftOutMod", "moveSubtree", "leftNode", "rightNode", "subtrees", "nodes_3", "Treegraph_TreegraphSeriesDefaults", "reversed", "marker", "radius", "symbol", "fillOpacity", "link", "cursor", "type", "fontWeight", "fontSize", "fillSpace", "tooltip", "linkFormat", "pointFormat", "dataLabels", "defer", "linkTextPath", "attributes", "startOffset", "linkFormatter", "textOverflow", "highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_", "highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default", "deg2rad", "TextPath_addEvent", "TextPath_merge", "<PERSON><PERSON><PERSON>", "defined", "TextPath_extend", "setTextPath", "path", "textPathOptions", "dy", "textAnchor", "url", "textWrapper", "textPath", "undo", "e", "textPathId", "textAttribs", "dx", "transform", "box", "slice", "tagName", "href", "concat", "added", "textCache", "buildText", "setPolygon", "event", "bBox", "tp", "querySelector", "polygon", "fontMetrics", "b_1", "descender_1", "h", "lineCleanerRegex", "RegExp", "lines", "innerHTML", "replace", "split", "numOfLines", "appendTopAndBottom", "charIndex", "positionOfChar", "getRotationOfChar", "cosRot", "Math", "cos", "sinRot", "sin", "lineIndex", "lineLen", "line", "lineCharIndex", "srcCharIndex", "getStartPositionOfChar", "lower", "upper", "unshift", "char<PERSON><PERSON>", "getEndPositionOfChar", "drawTextPath", "labelOptions", "useHTML", "getDataLabelPath", "dataLabelPath", "TreegraphSeries_extends", "TreegraphSeries_getLinkPath", "pathParams", "x1", "y1", "x2", "y2", "parentVisible", "applyRadius", "r", "prevSeg", "nextSeg", "directionX", "directionY", "min", "abs", "straight", "curved", "offset", "seriesProto", "TreemapSeries", "ColumnSeries", "symbols", "arrayMax", "crisp", "TreegraphSeries_extend", "TreegraphSeries_merge", "TreegraphSeries_pick", "TreegraphSeries_relativeLength", "splat", "Extensions_TextPath", "compose", "SVGElementClass", "svgElementProto", "TreegraphSeries", "links", "init", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "collectors", "labelCollectors", "some", "f", "name", "linkLabels", "allowOverlap", "dataLabel", "getLayoutModifiers", "plotSizeY", "points", "map", "minX", "Infinity", "maxX", "minY", "maxY", "maxXSize", "minXSize", "maxYSize", "minYSize", "for<PERSON>ach", "markerOptions", "TreegraphSeries_getNodeWidth", "nodeSizeY", "nodeSizeX", "max", "ay", "by", "ax", "bx", "getLinks", "data", "pointOptions", "destroyed", "LinkClass", "splice", "buildTree", "list", "markerAttribs", "setCollapsedStatus", "drawTracker", "translate", "Series_TreeUtilities", "getTree", "nodeMap", "setRootNode", "TreegraphSeries_getLevelOptions", "colorByPoint", "setTreeValues", "layoutModifier", "translateNode", "translateLink", "setColorRecursive", "linkWidth", "factor", "curveFactor", "fromNodeWidth", "diff", "shapeType", "plotX", "plotY", "dlBox", "tooltipPos", "drawNodeLabels", "points_1", "hasDataLabels", "_f", "_g", "lineClamp", "floor", "dlOptions", "drawDataLabels", "alignDataLabel", "hide", "pointAttribs", "stateOptions", "linkColor", "linkLineWidth", "attribs", "drawPoints", "nodeX", "nodeY", "borderRadius", "symbolFn", "hasImage", "imageUrl", "match", "parentShapeArgs", "x_1", "y_1", "<PERSON><PERSON><PERSON><PERSON>", "defaultOptions", "registerSeriesType"], "mappings": "CAWA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,CAAEA,QAAQ,cAAc,WAAc,CAAEA,QAAQ,cAAc,KAAQ,CAAEA,QAAQ,cAAc,KAAQ,CAAEA,QAAQ,cAAc,UAAa,EAC3N,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,+BAAgC,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,iBAAiB,CAAE,CAAC,wBAAwB,cAAc,CAAE,CAAC,wBAAwB,QAAQ,CAAE,CAAC,wBAAwB,QAAQ,CAAE,CAAC,wBAAwB,aAAa,CAAC,CAAEJ,GAChQ,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,+BAA+B,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,CAAEA,QAAQ,cAAc,WAAc,CAAEA,QAAQ,cAAc,KAAQ,CAAEA,QAAQ,cAAc,KAAQ,CAAEA,QAAQ,cAAc,UAAa,EAE3PJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,WAAc,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,UAAa,CACtN,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,CAAEC,CAA+B,EAC3N,OAAgB,AAAC,WACP,aACA,IA+TFC,EAqNAA,EAseAA,EAo+BAA,EA99DMC,EAAuB,CAE/B,GACC,SAASX,CAAM,EAEtBA,EAAOD,OAAO,CAAGU,CAEX,EAEA,IACC,SAAST,CAAM,EAEtBA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,SAASP,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,SAASN,CAAM,EAEtBA,EAAOD,OAAO,CAAGS,CAEX,EAEA,IACC,SAASR,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGIQ,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAahB,OAAO,CAG5B,IAAIC,EAASY,CAAwB,CAACE,EAAS,CAAG,CAGjDf,QAAS,CAAC,CACX,EAMA,OAHAY,CAAmB,CAACG,EAAS,CAACd,EAAQA,EAAOD,OAAO,CAAEc,GAG/Cb,EAAOD,OAAO,AACtB,CAMCc,EAAoBI,CAAC,CAAG,SAASjB,CAAM,EACtC,IAAIkB,EAASlB,GAAUA,EAAOmB,UAAU,CACvC,WAAa,OAAOnB,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAa,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASrB,CAAO,CAAEuB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACzB,EAASwB,IAC5EE,OAAOC,cAAc,CAAC3B,EAASwB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,EAAe,CAC9D,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAgMjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAE7KE,EAAuH3B,EAAoB,KAC3I4B,EAA2I5B,EAAoBI,CAAC,CAACuB,GAYjKE,GACIhC,EAAgB,SAAUU,CAAC,CAC3BuB,CAAC,EAMD,MAAOjC,AALHA,CAAAA,EAAgBe,OAAOmB,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAU1B,CAAC,CAC1DuB,CAAC,EAAIvB,EAAEyB,SAAS,CAAGF,CAAG,GACd,SAAUvB,CAAC,CACnBuB,CAAC,EAAI,IAAK,IAAII,KAAKJ,EAAOA,EAAEX,cAAc,CAACe,IAAI3B,CAAAA,CAAC,CAAC2B,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,CAAG,CAAA,EACvC3B,EAAGuB,EAC5B,EACO,SAAUvB,CAAC,CAAEuB,CAAC,EAEjB,SAASK,IAAO,IAAI,CAACC,WAAW,CAAG7B,CAAG,CADtCV,EAAcU,EAAGuB,GAEjBvB,EAAEW,SAAS,CAAGY,AAAM,OAANA,EAAalB,OAAOyB,MAAM,CAACP,GAAMK,CAAAA,EAAGjB,SAAS,CAAGY,EAAEZ,SAAS,CAAE,IAAIiB,CAAG,CACtF,GAaAG,EAA+B,SAAUC,CAAM,EAE/C,SAASD,IAML,IAAIE,EAAQD,AAAW,OAAXA,GAAmBA,EAAOE,KAAK,CAAC,IAAI,CAC5CC,YAAc,IAAI,CAStB,OARAF,EAAMG,GAAG,CAAG,EACZH,EAAMI,KAAK,CAAG,EACdJ,EAAMK,MAAM,CAAG,EACfL,EAAMM,QAAQ,CAAG,EAAE,CACnBN,EAAMO,IAAI,CAAG,EACbP,EAAMQ,MAAM,CAAG,CAAA,EACfR,EAAMS,UAAU,CAAG,CAAA,EACnBT,EAAMU,SAAS,CAAG,CAAA,EACXV,CACX,CAgJA,OAlKAX,EAAUS,EAAeC,GA8BzBD,EAAcpB,SAAS,CAACiC,QAAQ,CAAG,WAC/B,OAAO,IAAI,CAACC,gBAAgB,IAAM,IAAI,CAACC,MAAM,AACjD,EAOAf,EAAcpB,SAAS,CAACoC,SAAS,CAAG,WAChC,OAAO,IAAI,CAACC,iBAAiB,IAAM,IAAI,CAACF,MAAM,AAClD,EAYAf,EAAcpB,SAAS,CAACsC,WAAW,CAAG,SAAUC,CAAW,CAAEC,CAAe,SAExE,AAAIC,AADUF,EAAYG,QAAQ,CACtBd,QAAQ,CAAC,EAAE,GAAK,IAAI,CAACA,QAAQ,CAAC,EAAE,CACjCW,EAAYG,QAAQ,CAExBF,CACX,EAQApB,EAAcpB,SAAS,CAAC2C,kBAAkB,CAAG,WACzC,IAAIC,EAAS,IAAI,CAACC,SAAS,GAC3B,GAAID,EACA,IAAK,IAAIE,EAAK,EAAGC,EAAKH,EAAOhB,QAAQ,CAAEkB,EAAKC,EAAGC,MAAM,CAAEF,IAAM,CACzD,IAAIG,EAAQF,CAAE,CAACD,EAAG,CAClB,GAAIG,GAASA,EAAMC,KAAK,CAACC,OAAO,CAC5B,OAAOF,CAEf,CAER,EAOA7B,EAAcpB,SAAS,CAACoD,WAAW,CAAG,WAElC,IAAK,IADDxB,EAAW,IAAI,CAACA,QAAQ,CACnByB,EAAI,EAAGA,EAAIzB,EAASoB,MAAM,CAAEK,IACjC,GAAIzB,CAAQ,CAACyB,EAAE,CAACH,KAAK,CAACC,OAAO,CACzB,MAAO,CAAA,EAGf,MAAO,CAAA,CACX,EAOA/B,EAAcpB,SAAS,CAACsD,cAAc,CAAG,WACrC,IAAIV,EAAS,IAAI,CAACC,SAAS,GAC3B,GAAID,EAEA,CAAA,IAAK,IADDhB,EAAWgB,EAAOhB,QAAQ,CACrByB,EAAI,IAAI,CAACE,iBAAiB,CAAG,EAAGF,GAAK,EAAGA,IAC7C,GAAIzB,CAAQ,CAACyB,EAAE,EAAIzB,CAAQ,CAACyB,EAAE,CAACH,KAAK,CAACC,OAAO,CACxC,OAAOvB,CAAQ,CAACyB,EAAE,AAE1B,CAER,EAOAjC,EAAcpB,SAAS,CAACkC,gBAAgB,CAAG,WAEvC,IAAK,IADDN,EAAW,IAAI,CAACA,QAAQ,CACnByB,EAAI,EAAGA,EAAIzB,EAASoB,MAAM,CAAEK,IACjC,GAAIzB,CAAQ,CAACyB,EAAE,CAACH,KAAK,CAACC,OAAO,CACzB,OAAOvB,CAAQ,CAACyB,EAAE,AAG9B,EAOAjC,EAAcpB,SAAS,CAACqC,iBAAiB,CAAG,WAExC,IAAK,IADDT,EAAW,IAAI,CAACA,QAAQ,CACnByB,EAAIzB,EAASoB,MAAM,CAAG,EAAGK,GAAK,EAAGA,IACtC,GAAIzB,CAAQ,CAACyB,EAAE,CAACH,KAAK,CAACC,OAAO,CACzB,OAAOvB,CAAQ,CAACyB,EAAE,AAG9B,EAQAjC,EAAcpB,SAAS,CAAC6C,SAAS,CAAG,WAChC,OAAO,IAAI,CAACW,UAAU,AAC1B,EAOApC,EAAcpB,SAAS,CAACyD,aAAa,CAAG,WAEpC,IAAK,IADD7B,EAAW,IAAI,CAACA,QAAQ,CACnByB,EAAI,EAAGA,EAAIzB,EAASoB,MAAM,CAAEK,IACjC,GAAIzB,CAAQ,CAACyB,EAAE,CAACH,KAAK,CAACC,OAAO,CACzB,OAAOvB,CAAQ,CAACyB,EAAE,AAG9B,EACOjC,CACX,EA9KkB,AAACZ,IAA2IkD,WAAW,CAACC,OAAO,CAAC3D,SAAS,CAAC4D,SAAS,EAuLjMC,EAA+F/E,EAAoB,KACnHgF,EAAmHhF,EAAoBI,CAAC,CAAC2E,GAYzIE,GACIpF,EAAgB,SAAUU,CAAC,CAC3BuB,CAAC,EAOD,MAAOjC,AANHA,CAAAA,EAAgBe,OAAOmB,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAU1B,CAAC,CAC1DuB,CAAC,EAAIvB,EAAEyB,SAAS,CAAGF,CAAG,GACd,SAAUvB,CAAC,CACnBuB,CAAC,EAAI,IAAK,IAAII,KAAKJ,EAAOlB,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACU,EAC/DI,IAAI3B,CAAAA,CAAC,CAAC2B,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,CAAG,CAAA,EACI3B,EAAGuB,EAC5B,EACO,SAAUvB,CAAC,CAAEuB,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIoD,UAAU,uBAAyBC,OAAOrD,GAAK,iCAE7D,SAASK,IAAO,IAAI,CAACC,WAAW,CAAG7B,CAAG,CADtCV,EAAcU,EAAGuB,GAEjBvB,EAAEW,SAAS,CAAGY,AAAM,OAANA,EAAalB,OAAOyB,MAAM,CAACP,GAAMK,CAAAA,EAAGjB,SAAS,CAAGY,EAAEZ,SAAS,CAAE,IAAIiB,CAAG,CACtF,GAIAiD,EAAe,AAAC1D,IAA2IkD,WAAW,CAACC,OAAO,CAAC3D,SAAS,CAACmE,UAAU,CAEnMC,EAAW,AAAC9D,IAA+E8D,QAAQ,CAAEC,EAAY,AAAC/D,IAA+E+D,SAAS,CAAEC,EAAQ,AAAChE,IAA+EgE,KAAK,CAUzSC,EAAgC,SAAUlD,CAAM,EAEhD,SAASkD,IAML,IAAIjD,EAAQD,AAAW,OAAXA,GAAmBA,EAAOE,KAAK,CAAC,IAAI,CAC5CC,YAAc,IAAI,CAItB,OAHAF,EAAMkD,iBAAiB,CAAG,CAAA,EAC1BlD,EAAMmD,MAAM,CAAG,CAAA,EACfnD,EAAMoD,QAAQ,CAAG,AAACZ,IAAuG9D,SAAS,CAAC0E,QAAQ,CACpIpD,CACX,CA8IA,OA3JAyC,EAAuBQ,EAAgBlD,GAmBvCkD,EAAevE,SAAS,CAAC2E,IAAI,CAAG,WAC5BtD,EAAOrB,SAAS,CAAC2E,IAAI,CAACpD,KAAK,CAAC,IAAI,CAAEC,WAElC,IAAIoD,EAAU,IAAI,CAACA,OAAO,CACtBA,GACAA,EAAQC,OAAO,CAAC,CACZC,WAAY,IAAI,CAAC3B,OAAO,CAAG,UAAY,QAC3C,GAEJ,IAAI,CAAC4B,oBAAoB,EAC7B,EACAR,EAAevE,SAAS,CAAC+E,oBAAoB,CAAG,WAC5C,IACIC,EAAS9B,AADD,IAAI,CACG8B,MAAM,CACrBC,EAAc/B,AAFN,IAAI,CAEQ0B,OAAO,EAAI1B,AAFvB,IAAI,CAEyB0B,OAAO,CAACK,WAAW,CACxDC,EAAeF,EAAOG,iBAAiB,CAACjC,AAHhC,IAAI,CAGkCkC,IAAI,CAACC,KAAK,EAAI,EAAE,EAAI,CAAC,EACnEC,EAAahB,EAAMU,EAAOO,OAAO,CAACC,cAAc,CAChDN,EAAaM,cAAc,CAC3BtC,AANQ,IAAI,CAMNqC,OAAO,CAACC,cAAc,EAC5BC,EAAQH,EAAWG,KAAK,CACxBC,EAASJ,EAAWI,MAAM,CAC1BC,EAAQL,EAAWK,KAAK,CACxBC,EAAQN,EAAWM,KAAK,CAExBC,EAAQ,IAAI,CAACb,MAAM,CAACa,KAAK,CACzBC,EAAoB,AAAC5C,AAbb,IAAI,CAaeC,OAAO,EAC7BD,CAAAA,AAdG,IAAI,CAcDlB,SAAS,EACZ,CAACsD,EAAWS,WAAW,EACvB7C,AAAgB,UAAhBA,AAhBA,IAAI,CAgBE8C,KAAK,AAAW,EAAM,EAAI,EAC5C,GAAK9C,AAjBO,IAAI,CAiBL+C,SAAS,EAIpB,GADA,IAAI,CAACC,qBAAqB,CAAGZ,EACxBpC,AArBO,IAAI,CAqBLsC,cAAc,EAqCrB,GAAI,AAACtC,AA1DG,IAAI,CA0DDkC,IAAI,CAACxD,QAAQ,CAACoB,MAAM,EAAKsC,EAAWa,OAAO,CAIjD,CACD,IAAIC,EAAK,IAAI,CAACC,sBAAsB,CAACf,GACjCgB,EAAIF,EAAGE,CAAC,CACRC,EAAIH,EAAGG,CAAC,CACZrD,AAlEI,IAAI,CAkEFsC,cAAc,CACfgB,IAAI,CAAC,CACNC,KAAMvD,AApEN,IAAI,CAoEQlB,SAAS,CAAG,IAAM,IAC9B0E,SAAUb,AAAiB,KAAjBA,EAAMc,QAAQ,CACxBC,gBAAiBnB,EAAQ,EACzBoB,gBAAiBnB,EAAS,EAC1BZ,WAAY5B,AAxEZ,IAAI,CAwEcC,OAAO,CAAG,UAAY,QAC5C,GACK0B,OAAO,CAAC,CACTyB,EAAGA,EACHC,EAAGA,EACHO,QAAShB,CACb,EACJ,MApBI5C,AA3DI,IAAI,CA2DFsC,cAAc,CAACuB,OAAO,GAC5B,OAAO7D,AA5DH,IAAI,CA4DKsC,cAAc,KAvCR,CACvB,GAAI,CAACtC,AAtBG,IAAI,CAsBDkC,IAAI,CAACxD,QAAQ,CAACoB,MAAM,EAAI,CAACsC,EAAWa,OAAO,CAClD,OAEJ,IAAIpD,EAAK,IAAI,CAACsD,sBAAsB,CAACf,GAAagB,EAAIvD,EAAGuD,CAAC,CAAEC,EAAIxD,EAAGwD,CAAC,CAAES,EAAQ1B,EAAW2B,SAAS,EAC1F/D,AA1BA,IAAI,CA0BEgE,KAAK,EACX,SACRhE,CA5BQ,IAAI,CA4BNsC,cAAc,CAAGK,EAAMsB,QAAQ,CAChCC,KAAK,CAAClE,AA7BH,IAAI,CA6BKlB,SAAS,CAAG,IAAM,IAAKsE,EAAGC,EAAGZ,GACzCa,IAAI,CAAC,CACNd,OAAQA,EAAS,EACjBD,MAAOA,EAAQ,EACf4B,QAtBM,EAuBNL,KAAMA,EACNN,SAAUb,AAAiB,KAAjBA,EAAMc,QAAQ,CACxBC,gBAAiBnB,EAAQ,EACzBoB,gBAAiBnB,EAAS,EAC1B4B,OAAQhC,EAAWiC,SAAS,EAAI,UAChC,eAAgBjC,EAAWkC,SAAS,CACpC,aAAc,SACdC,MAAO,SACPC,OAAQ,EACRZ,QAAShB,EACThB,WAAY5B,AA5CR,IAAI,CA4CUC,OAAO,CAAG,UAAY,QAC5C,GACKwE,QAAQ,CAAC,sBACTA,QAAQ,CAAC,8BACTC,WAAW,CAAC,yBACZC,GAAG,CAACvD,EAAM,CACX4C,MAAO,AAAgB,UAAhB,OAAOF,EACVnB,EAAMsB,QAAQ,CAACW,WAAW,CAACd,GAC3B,SACR,EAAGpB,IACEmC,GAAG,CAAC9C,GACT/B,AAvDQ,IAAI,CAuDNsC,cAAc,CAACwC,OAAO,CAAC9E,KAAK,CAvD1B,IAAI,AAwDhB,EAyBJ,EACAqB,EAAevE,SAAS,CAACiI,cAAc,CAAG,SAAUjC,CAAK,EACrD,IAAIhB,EAAS,IAAI,CAACA,MAAM,CACxB,IAAI,CAACkD,MAAM,CAAC,CACRlG,UAAWgE,MAAAA,EAAqCA,EAAQ,CAAC,IAAI,CAAChE,SAAS,AAC3E,EAAG,CAAA,EAAO,KAAK,EAAG,CAAA,GAClBqC,EAAUW,EAAQ,kBAClBA,EAAOmD,MAAM,EACjB,EACA5D,EAAevE,SAAS,CAAC+G,OAAO,CAAG,WAC3B,IAAI,CAACvB,cAAc,GACnB,IAAI,CAACA,cAAc,CAACuB,OAAO,GAC3B,OAAO,IAAI,CAACvB,cAAc,CAC1B,IAAI,CAACA,cAAc,CAAG,KAAK,GAE3B,IAAI,CAAC4C,YAAY,GACjB,IAAI,CAACA,YAAY,CAACrB,OAAO,GACzB,OAAO,IAAI,CAACqB,YAAY,EAE5B/G,EAAOrB,SAAS,CAAC+G,OAAO,CAACxF,KAAK,CAAC,IAAI,CAAEC,UACzC,EACA+C,EAAevE,SAAS,CAACqG,sBAAsB,CAAG,SAAUf,CAAU,EAClE,IAEIqB,EAAWd,AADH3C,AADA,IAAI,CACE8B,MAAM,CAACa,KAAK,CACTc,QAAQ,CACzB0B,EAAW/C,EAAWG,KAAK,CAC3B6C,EAAYhD,EAAWI,MAAM,CAC7B3C,EAAKG,AALG,IAAI,CAKD+C,SAAS,EAAI,CAAC,EACzBG,EAAKrD,EAAGuD,CAAC,CAETiC,EAAKxF,EAAGwD,CAAC,CAETiC,EAAKzF,EAAG0C,KAAK,CAEbgD,EAAK1F,EAAG2C,MAAM,CAElB,MAAO,CACHY,EAAGA,AARCF,CAAAA,AAAO,KAAK,IAAZA,EAAgB,EAAIA,CAAC,EASrBd,EAAWgB,CAAC,CACXK,CAAAA,EAAW,CAAA,CAAA,AAAa,GAAZ2B,CAAc,EAAI7C,AAN3B+C,CAAAA,AAAO,KAAK,IAAZA,EAAgB,EAAIA,CAAC,EAMcH,AAAW,IAAXA,CAAc,EACzD9B,EAAGA,AATCgC,CAAAA,AAAO,KAAK,IAAZA,EAAgB,EAAIA,CAAC,EASlB7C,AALE+C,CAAAA,AAAO,KAAK,IAAZA,EAAgB,EAAIA,CAAC,EAKd,EAAIH,EAAY,EAAIhD,EAAWiB,CAAC,AACpD,CACJ,EACOhC,CACX,EAAEL,GACFE,EAASG,EAAgB,WAAY,WACjC,IAAImE,EAAM,IAAI,CAAClD,cAAc,CACzBF,EAAa,IAAI,CAACY,qBAAqB,CACvCwC,GAAQpD,CAAAA,MAAAA,EAA+C,KAAK,EAAIA,EAAWS,WAAW,AAAD,GAAM,CAAC,IAAI,CAAC/D,SAAS,EAC1G0G,EAAI7D,OAAO,CAAC,CAAEiC,QAAS,CAAE,EAEjC,GACA1C,EAASG,EAAgB,YAAa,WAClC,IAAIxB,EACAqD,CACA,CAAA,IAAI,CAACZ,cAAc,EAAI,IAAI,CAACrC,OAAO,EACnC,IAAI,CAACqC,cAAc,CAACX,OAAO,CAAC,CAAEiC,QAAS,CAAE,EAAG,AAA2F,OAA1FV,CAAAA,EAAK,AAAsC,OAArCrD,CAAAA,EAAK,IAAI,CAACiC,MAAM,CAACO,OAAO,CAACoD,MAAM,AAAD,GAAe5F,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG6F,KAAK,AAAD,GAAexC,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGyC,SAAS,CAE5L,GAEAzE,EAASG,EAAgB,QAAS,WAC9B,IAAI,CAAC0D,cAAc,EACvB,GASA,IAAIa,EAA+FhK,EAAoB,KAiBlBiK,GAAxF,AAACzI,IAA+E0I,MAAM,CAAY,AAAC1I,IAA+EyI,OAAO,EAAEE,EAAW,AAAC3I,IAA+E2I,QAAQ,CAAEC,EAAW,AAAC5I,IAA+E4I,QAAQ,CAAEC,EAAsB,AAAC7I,IAA+EgE,KAAK,CAAE8E,EAAO,AAAC9I,IAA+E8I,IAAI,CAAEC,EAAiB,AAAC/I,IAA+E+I,cAAc,GA8E3sB,SAAyBC,CAAM,EAC3B,IACIC,EACAC,EACAnG,EACAoG,EACAC,EACAC,EANAC,EAAS,CAAC,EAOd,GAAIV,EAASI,GA6BT,IA5BAG,EAAOR,EAASK,EAAOG,IAAI,EAAIH,EAAOG,IAAI,CAAG,EAC7CE,EAASL,EAAOK,MAAM,CACtBH,EAAY,CAAC,EACbD,EAAWL,EAASI,EAAOC,QAAQ,EAAID,EAAOC,QAAQ,CAAG,CAAC,EACtDR,EAAQY,IACRH,CAAAA,EAAYG,EAAOE,MAAM,CAAC,SAAU/J,CAAG,CAAEgK,CAAI,EACzC,IAAIzE,EACA0E,EACAxE,EAgBJ,OAfI2D,EAASY,IAASb,EAASa,EAAKzE,KAAK,IAErC0E,EAAkBX,EAAK7D,AADvBA,CAAAA,EAAU4D,EAAoB,CAAC,EAAGW,EAAI,EACPC,eAAe,CAAER,EAASQ,eAAe,EAExE,OAAOxE,EAAQwE,eAAe,CAC9B,OAAOxE,EAAQF,KAAK,CAGhB6D,EAASpJ,CAAG,CADhBuF,EAAQyE,EAAKzE,KAAK,CAAI0E,CAAAA,EAAkB,EAAIN,EAAO,CAAA,EAC5B,EACnBN,EAAoB,CAAA,EAAMrJ,CAAG,CAACuF,EAAM,CAAEE,GAGtCzF,CAAG,CAACuF,EAAM,CAAGE,GAGdzF,CACX,EAAG,CAAC,EAAC,EAET4J,EAAKT,EAASK,EAAOI,EAAE,EAAIJ,EAAOI,EAAE,CAAG,EAClCrG,EAAI,EAAGA,GAAKqG,EAAIrG,IACjBuG,CAAM,CAACvG,EAAE,CAAG8F,EAAoB,CAAC,EAAGI,EAAUL,EAASM,CAAS,CAACnG,EAAE,EAAImG,CAAS,CAACnG,EAAE,CAAG,CAAC,GAG/F,OAAOuG,CACX,IAoFA,SAAsB5E,CAAM,CAAEgF,CAAW,EACrC,IAAInE,EAAQb,EAAOa,KAAK,CACpBN,EAAUP,EAAOO,OAAO,CACxBxC,EAAKwC,EAAQ0E,YAAY,CACzBA,EAAelH,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EACnCqD,EAAKb,EAAQ2E,SAAS,CACtBA,EAAY9D,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EAChCmC,EAAK1C,EAAMsE,SAAS,CACpBA,EAAY5B,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EAGpC,GAAI2B,AAAc,SAAdA,EAAsB,CACtB,GAAI,AAAwB,UAAxB,OAAOD,GAA6B,KAAKG,IAAI,CAACH,GAG9C,OAAOE,EADKH,CAAAA,EAAcK,AADXC,WAAWL,GAAgB,IACJD,CAAAA,EAAc,CAAA,CAAC,EAGzD,IAAIO,EAAYC,OAAOP,GACvB,MAAO,AAAEE,CAAAA,EAAYI,CAAQ,EACxBP,CAAAA,GAAe,CAAA,EAAMO,CAC9B,CACA,OAAOlB,EAAea,EAAWC,EACrC,IA7CA,SAAsBnF,CAAM,EACxB,IAAIyF,EACAlF,EAaJ,OAZI2D,EAASlE,KAETO,EAAU2D,EAASlE,EAAOO,OAAO,EAAIP,EAAOO,OAAO,CAAG,CAAC,EAEvDkF,EAASrB,EAAKpE,EAAO0F,QAAQ,CAAEnF,EAAQkF,MAAM,CAAE,IAE3CvB,EAASlE,EAAO2F,WAAW,GAC3B3F,CAAAA,EAAO2F,WAAW,CAACF,MAAM,CAAGA,CAAK,EAGrCzF,EAAO0F,QAAQ,CAAGD,GAEfA,CACX,EAuDIG,GACIjM,EAAgB,SAAUU,CAAC,CAC3BuB,CAAC,EAOD,MAAOjC,AANHA,CAAAA,EAAgBe,OAAOmB,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAU1B,CAAC,CAC1DuB,CAAC,EAAIvB,EAAEyB,SAAS,CAAGF,CAAG,GACd,SAAUvB,CAAC,CACnBuB,CAAC,EAAI,IAAK,IAAII,KAAKJ,EAAOlB,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACU,EAC/DI,IAAI3B,CAAAA,CAAC,CAAC2B,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,CAAG,CAAA,EACI3B,EAAGuB,EAC5B,EACO,SAAUvB,CAAC,CAAEuB,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIoD,UAAU,uBAAyBC,OAAOrD,GAAK,iCAE7D,SAASK,IAAO,IAAI,CAACC,WAAW,CAAG7B,CAAG,CADtCV,EAAcU,EAAGuB,GAEjBvB,EAAEW,SAAS,CAAGY,AAAM,OAANA,EAAalB,OAAOyB,MAAM,CAACP,GAAMK,CAAAA,EAAGjB,SAAS,CAAGY,EAAEZ,SAAS,CAAE,IAAIiB,CAAG,CACtF,GAIA4J,EAAqB,AAACvK,IAA+E8I,IAAI,CAAE0B,EAAuB,AAACxK,IAA+E0I,MAAM,CAYxN+B,EAA2B,SAAU1J,CAAM,EAO3C,SAAS0J,EAAU/F,CAAM,CAAEO,CAAO,CAAEe,CAAC,CAAEpD,CAAK,EACxC,IAAI5B,EAAQD,EAAOnB,IAAI,CAAC,IAAI,CACxB8E,EACAO,EACAe,IAAM,IAAI,CAkBd,OAZAhF,EAAM0J,eAAe,CAAG,CAAA,EACxB1J,EAAM2J,YAAY,CAAG,OACrB3J,EAAMmD,MAAM,CAAG,CAAA,EACfnD,EAAM8D,IAAI,CAAG,CAAC,EACd9D,EAAM2J,YAAY,CAAG,OACrB3J,EAAM0J,eAAe,CAAG,CAAA,EACpB9H,IACA5B,EAAM4J,QAAQ,CAAGhI,EAAMkC,IAAI,CAAC5B,UAAU,CAACN,KAAK,CAC5C5B,EAAM6B,OAAO,CAAGD,EAAMC,OAAO,CAC7B7B,EAAM6J,MAAM,CAAGjI,EACf5B,EAAM8J,EAAE,CAAG9J,EAAM6J,MAAM,CAACC,EAAE,CAAG,IAAM9J,EAAM4J,QAAQ,CAACE,EAAE,EAEjD9J,CACX,CAmBA,OAhDAsJ,EAAsBG,EAAW1J,GAmCjC0J,EAAU/K,SAAS,CAACkI,MAAM,CAAG,SAAU3C,CAAO,CAAE4C,CAAM,CAAEU,CAAS,CAAEwC,CAAQ,EACvE,IAAIC,EAAa,CACTF,GAAI,IAAI,CAACA,EAAE,CACXH,aAAc,IAAI,CAACA,YAAY,AACnC,EACJnH,IAAsG9D,SAAS,CAACkI,MAAM,CAAChI,IAAI,CAAC,IAAI,CAAEqF,EAAS,CAAA,IAAI,CAACd,MAAM,EAAW0D,EACjKU,EAAWwC,GACX,IAAI,CAAClI,OAAO,CAAG,IAAI,CAACgI,MAAM,CAAChI,OAAO,CAClC2H,EAAqB,IAAI,CAAEQ,GACvBT,EAAmB1C,EAAQ,CAAA,IAC3B,IAAI,CAACnD,MAAM,CAACa,KAAK,CAACsC,MAAM,CAACU,EAEjC,EACOkC,CACX,EA5DkB,AAACvK,IAA2IkD,WAAW,CAAC6H,MAAM,CAACvL,SAAS,CAACmE,UAAU,EAyFjMqH,EAAiC,WACjC,SAASA,IACT,CA4TA,OAzSAA,EAAgBC,eAAe,CAAG,SAAU7I,CAAM,CAAEK,CAAK,CAAEyI,CAAO,EAE9D,IAAIC,EAAY,IA7nBoCvK,EAgpBpD,OAlBAuK,EAAUP,EAAE,CAAGxI,EAAOwI,EAAE,CAAG,IAAMM,EACjCC,EAAUjJ,QAAQ,CAAGE,EAGrB+I,EAAU/J,QAAQ,CAACgK,IAAI,CAAC3I,GACxB0I,EAAU/I,MAAM,CAAGA,EAAOwI,EAAE,CAC5BO,EAAUnI,UAAU,CAAGZ,EACvB+I,EAAUzI,KAAK,CAAGD,EAAMC,KAAK,CAC7ByI,EAAUtG,KAAK,CAAGpC,EAAMoC,KAAK,CAAGqG,EAChCC,EAAUpI,iBAAiB,CAAGN,EAAMM,iBAAiB,CACrDoI,EAAUxI,OAAO,CAAGF,EAAME,OAAO,CAEjCP,EAAOhB,QAAQ,CAACqB,EAAMM,iBAAiB,CAAC,CAAGoI,EAC3C1I,EAAM4I,aAAa,CAAGjJ,EACtBK,EAAMM,iBAAiB,CAAG,EAE1BN,EAAMO,UAAU,CAAGmI,EACnB1I,EAAML,MAAM,CAAG+I,EAAUP,EAAE,CACpBO,CACX,EAWAH,EAAgBxL,SAAS,CAAC8L,kBAAkB,CAAG,SAAU9G,CAAM,EAE3D,IAAI+G,EAAQ/G,EAAOgH,QAAQ,CAC3B,IAAI,CAACC,WAAW,CAACF,GACjB,IAAIjO,EAAOkH,EAAOkH,IAAI,CAClBpO,IACAqO,AALa,IAAI,CAKNC,kBAAkB,CAACtO,EAAM,GACpCqO,AANa,IAAI,CAMNE,YAAY,CAACN,GACxBI,AAPa,IAAI,CAONG,SAAS,CAACxO,GACrBqO,AARa,IAAI,CAQNI,UAAU,CAACzO,EAAM,CAACA,EAAK+D,IAAI,EACtCsK,AATa,IAAI,CASNK,WAAW,CAACT,GAE/B,EAOAP,EAAgBxL,SAAS,CAACqM,YAAY,CAAG,SAAUN,CAAK,EACpD,IAAK,IAAIjJ,EAAK,EAAoBA,EAAK2J,AAAZV,EAAoB/I,MAAM,CAAEF,IAEnD,IAAK,IADDsC,EAAOqH,AADYV,CACL,CAACjJ,EAAG,CACbC,EAAK,EAAGqD,EAAKhB,EAAKxD,QAAQ,CAAEmB,EAAKqD,EAAGpD,MAAM,CAAED,IAAM,CACvD,IAAIE,EAAQmD,CAAE,CAACrD,EAAG,CAElB,GAAIE,GAASA,EAAMoC,KAAK,CAAGD,EAAKC,KAAK,CAAG,EAKpC,IAFA,IAAIqG,EAAUzI,EAAMoC,KAAK,CAAGD,EAAKC,KAAK,CAAG,EAElCqG,EAAU,GACbzI,EAAQuI,EAAgBC,eAAe,CAACrG,EAAMnC,EAAOyI,GACrDA,GAGZ,CAER,EAKAF,EAAgBxL,SAAS,CAACiM,WAAW,CAAG,SAAUF,CAAK,EACnD,IAAK,IAAIjJ,EAAK,EAAoBA,EAAK4J,AAAZX,EAAoB/I,MAAM,CAAEF,IAAM,CACzD,IAAIsC,EAAOsH,AADYX,CACL,CAACjJ,EAAG,AACtBsC,CAAAA,EAAK3D,GAAG,CAAG,EACX2D,EAAK1C,QAAQ,CAAG0C,EAChBA,EAAK1D,KAAK,CAAG,EACb0D,EAAKjD,MAAM,CAAG,KAAK,EACnBiD,EAAKzD,MAAM,CAAG,EACdyD,EAAKvD,IAAI,CAAG,CAChB,CACJ,EAUA2J,EAAgBxL,SAAS,CAACoM,kBAAkB,CAAG,SAAUhH,CAAI,CAAEuH,CAAK,EAGhE,IAAK,IADD/K,EAAWwD,EAAKxD,QAAQ,CACnByB,EAAI,EAAGuJ,EAAOhL,EAASoB,MAAM,CAAEK,EAAIuJ,EAAM,EAAEvJ,EAChD8I,AAHa,IAAI,CAGNC,kBAAkB,CAACxK,CAAQ,CAACyB,EAAE,CAAEA,EAE/C+B,CAAAA,EAAK7B,iBAAiB,CAAGoJ,CAC7B,EAQAnB,EAAgBxL,SAAS,CAACsM,SAAS,CAAG,SAAUlH,CAAI,EAChD,IAGIyH,EAEJ,GAAKzH,EAAKhC,WAAW,GAUhB,CAKD,IAAK,IADDZ,EAAkB4C,EAAKlD,gBAAgB,GAClCY,EAAK,EAAGC,EAAKqC,EAAKxD,QAAQ,CAAEkB,EAAKC,EAAGC,MAAM,CAAEF,IAAM,CACvD,IAAIG,EAAQF,CAAE,CAACD,EAAG,CAClBqJ,AAtBS,IAAI,CAsBFG,SAAS,CAACrJ,GACrBT,EAAkB2J,AAvBT,IAAI,CAuBgBW,SAAS,CAAC7J,EAAOT,EAClD,CACA2J,AAzBa,IAAI,CAyBNY,aAAa,CAAC3H,GACzB,IAAI4H,EAAY5H,EAAKlD,gBAAgB,GACjC+K,EAAa7H,EAAK/C,iBAAiB,GAInC6K,EAAW,AAACF,CAAAA,EAAUnL,IAAI,CAAGoL,EAAWpL,IAAI,AAAD,EAAK,EACpDgL,CAAAA,EAAczH,EAAK9B,cAAc,EAAC,GAE9B8B,EAAKvD,IAAI,CAAGgL,EAAYhL,IAAI,CAhCd,EAiCduD,EAAK3D,GAAG,CAAG2D,EAAKvD,IAAI,CAAGqL,GAGvB9H,EAAKvD,IAAI,CAAGqL,CAEpB,KAlCIL,CAAAA,EAAczH,EAAK9B,cAAc,EAAC,GAE9B8B,EAAKvD,IAAI,CAAGgL,EAAYhL,IAAI,CANd,EAOduD,EAAK3D,GAAG,CAAG2D,EAAKvD,IAAI,EAGpBuD,EAAKvD,IAAI,CAAG,CA6BxB,EAUA2J,EAAgBxL,SAAS,CAACuM,UAAU,CAAG,SAAUnH,CAAI,CAAE+H,CAAM,EAKzD/H,EAAKgI,SAAS,CAAGhI,EAAKvD,IAAI,CAAGsL,EAC7B/H,EAAKiI,SAAS,CAAGjI,EAAKC,KAAK,CAC3B,IAAK,IAAIvC,EAAK,EAAGC,EAAKqC,EAAKxD,QAAQ,CAAEkB,EAAKC,EAAGC,MAAM,CAAEF,IAAM,CACvD,IAAIG,EAAQF,CAAE,CAACD,EAAG,CAClBqJ,AARa,IAAI,CAQNI,UAAU,CAACtJ,EAAOkK,EAAS/H,EAAK3D,GAAG,CAClD,CACJ,EAOA+J,EAAgBxL,SAAS,CAAC+M,aAAa,CAAG,SAAU3H,CAAI,EAGpD,IAAK,IAFD1D,EAAQ,EACRC,EAAS,EACJ0B,EAAI+B,EAAKxD,QAAQ,CAACoB,MAAM,CAAG,EAAGK,GAAK,EAAGA,IAAK,CAChD,IAAIiK,EAAYlI,EAAKxD,QAAQ,CAACyB,EAAE,AAChCiK,CAAAA,EAAUzL,IAAI,EAAIH,EAClB4L,EAAU7L,GAAG,EAAIC,EACjBC,GAAU2L,EAAU3L,MAAM,CAC1BD,GAAS4L,EAAU5L,KAAK,CAAGC,CAC/B,CACJ,EAkBA6J,EAAgBxL,SAAS,CAAC8M,SAAS,CAAG,SAAU1H,CAAI,CAAE5C,CAAe,EACjE,IACIqK,EAAczH,EAAK9B,cAAc,GACrC,GAAIuJ,EAAa,CASb,IARA,IAAIU,EAAenI,EACfoI,EAAepI,EACf7C,EAAcsK,EACdY,EAAcF,EAAa5K,kBAAkB,GAC7C+K,EAAcH,EAAa9L,GAAG,CAC9BkM,EAAcH,EAAa/L,GAAG,CAC9BmM,EAAarL,EAAYd,GAAG,CAC5BoM,EAAaJ,EAAYhM,GAAG,CACzBc,GACHA,EAAYH,SAAS,IACrBmL,GACAA,EAAatL,QAAQ,IAAI,CACzBM,EAAcA,EAAYH,SAAS,GACnCqL,EAAcA,EAAYxL,QAAQ,GAClCsL,EAAeA,EAAatL,QAAQ,GAEpCuL,AADAA,CAAAA,EAAeA,EAAapL,SAAS,EAAC,EACzBM,QAAQ,CAAG0C,EACxB,IACI1D,EAAQa,EAAYV,IAAI,CACpB+L,EACCL,CAAAA,EAAa1L,IAAI,CAAG6L,CAAU,EAHjB,EAKlBhM,EAAQ,IACRyK,AA1BK,IAAI,CA0BE2B,WAAW,CAAC1I,EAAK9C,WAAW,CAACC,EAAaC,GAAkB4C,EAAM1D,GAC7EgM,GAAehM,EACfiM,GAAejM,GAEnBkM,GAAcrL,EAAYd,GAAG,CAC7BiM,GAAeH,EAAa9L,GAAG,CAC/BoM,GAAcJ,EAAYhM,GAAG,CAC7BkM,GAAeH,EAAa/L,GAAG,AACnC,CACIc,GACAA,EAAYH,SAAS,IACrB,CAACoL,EAAapL,SAAS,KACvBoL,EAAarL,MAAM,CAAGI,EAAYH,SAAS,GAC3CoL,EAAa/L,GAAG,EAAImM,EAAaD,GAEjCJ,GACAA,EAAatL,QAAQ,IACrB,CAACwL,EAAYxL,QAAQ,KACrBwL,EAAYtL,MAAM,CAAGoL,EAAatL,QAAQ,GAC1CwL,EAAYhM,GAAG,EAAIiM,EAAcG,GAErCrL,EAAkB4C,CACtB,CACA,OAAO5C,CACX,EASAgJ,EAAgBxL,SAAS,CAAC8N,WAAW,CAAG,SAAUC,CAAQ,CAAEC,CAAS,CAAEtM,CAAK,EACxE,IAAIuM,EAAWD,EAAUzK,iBAAiB,CAAGwK,EAASxK,iBAAiB,AACvEyK,CAAAA,EAAUrM,MAAM,EAAID,EAAQuM,EAC5BD,EAAUtM,KAAK,EAAIA,EACnBsM,EAAUnM,IAAI,EAAIH,EAClBsM,EAAUvM,GAAG,EAAIC,EACjBqM,EAASpM,MAAM,EAAID,EAAQuM,CAC/B,EAOAzC,EAAgBxL,SAAS,CAACwM,WAAW,CAAG,SAAUT,CAAK,EACnD,IAAK,IAAIjJ,EAAK,EAAoBA,EAAKoL,AAAZnC,EAAoB/I,MAAM,CAAEF,IAAM,CACzD,IAAIsC,EAAO8I,AADYnC,CACL,CAACjJ,EAAG,AAClBsC,CAAAA,EAAKyG,aAAa,GAElBzG,EAAK7B,iBAAiB,CAAG6B,EAAK5B,UAAU,CAACD,iBAAiB,CAC1D6B,EAAKxC,MAAM,CAAGwC,EAAKyG,aAAa,CAACjJ,MAAM,CACvCwC,EAAK5B,UAAU,CAAG4B,EAAKyG,aAAa,CAEpC,OAAOzG,EAAKyG,aAAa,CAACjK,QAAQ,CAACwD,EAAK7B,iBAAiB,CAAC,CAC1D6B,EAAKyG,aAAa,CAACjK,QAAQ,CAACwD,EAAK7B,iBAAiB,CAAC,CAAG6B,EACtDA,EAAKyG,aAAa,CAAG,KAAK,EAElC,CACJ,EACOL,CACX,IAsSiC2C,EAnPH,CAa1BC,SAAU,CAAA,EAKVC,OAAQ,CACJC,OAAQ,GACR9G,UAAW,EACX+G,OAAQ,SACRC,YAAa,EACb7F,OAAQ,CAAC,CACb,EACA8F,KAAM,CAkBFvH,MAAO,UAOPM,UAAW,EAOX8G,OAAQ,GACRI,OAAQ,UAWRC,KAAM,QACV,EAKAnJ,eAAgB,CAMZO,YAAa,CAAA,EAIbI,QAAS,CAAA,EAITqB,UAAW,EAIXlB,EAAG,EAIHC,EAAG,EAIHb,OAAQ,GAIRD,MAAO,GAIPE,MAAO,SAOPC,MAAO,CACH8I,OAAQ,UACRE,WAAY,OACZC,SAAU,KACd,CACJ,EAUAC,UAAW,CAAA,EAKXC,QAAS,CAeLC,WAAY,0CACZC,YAAa,YAUjB,EAUAC,WAAY,CACRC,MAAO,CAAA,EAePC,aAAc,CACVC,WAAY,CACRC,YAAa,KACjB,CACJ,EACAnJ,QAAS,CAAA,EACToJ,cAAe,WAAc,MAAO,EAAI,EACxClI,QAAS,EACTzB,MAAO,CACH4J,aAAc,MAClB,CACJ,EAoBAvF,aAAc,GAiBdC,UAAW,KAAK,CACpB,EASIuF,EAAmH3Q,EAAoB,IACvI4Q,EAAuI5Q,EAAoBI,CAAC,CAACuQ,GAgB7JE,EAAU,AAACrP,IAA+EqP,OAAO,CACjGC,EAAoB,AAACtP,IAA+E8D,QAAQ,CAAEyL,EAAiB,AAACvP,IAA+EgE,KAAK,CAAEwL,EAAY,AAACxP,IAA+EwP,SAAS,CAAEC,EAAU,AAACzP,IAA+EyP,OAAO,CAAEC,EAAkB,AAAC1P,IAA+E0I,MAAM,CAyB5gB,SAASiH,GAAYC,CAAI,CAAEC,CAAe,EACtC,IAAI7O,EAAQ,IAAI,CAEhB6O,EAAkBN,EAAe,CAAA,EAAM,CACnC1J,QAAS,CAAA,EACTkJ,WAAY,CACRe,GAAI,GACJd,YAAa,MACbe,WAAY,QAChB,CACJ,EAAGF,GACH,IAAIG,EAAM,IAAI,CAACnJ,QAAQ,CAACmJ,GAAG,CACvBC,EAAc,IAAI,CAAC9J,IAAI,EAAI,IAAI,CAC/B+J,EAAWD,EAAYC,QAAQ,CAC/BnB,EAAac,EAAgBd,UAAU,CACvClJ,EAAUgK,EAAgBhK,OAAO,CAMrC,GALA+J,EAAOA,GAASM,GAAYA,EAASN,IAAI,CAErCM,GACAA,EAASC,IAAI,GAEbP,GAAQ/J,EAAS,CACjB,IAAIsK,EAAOb,EAAkBW,EAAa,kBACtC,SAAUG,CAAC,EACP,GAAIR,GAAQ/J,EAAS,CAEjB,IAAIwK,EAAaT,EAAK1J,IAAI,CAAC,MAC1BmK,GACDT,EAAK1J,IAAI,CAAC,KAAMmK,EAAab,KAGjC,IAAIc,EAAc,CAGVtK,EAAG,EACHC,EAAG,CACP,EACAwJ,EAAQV,EAAWwB,EAAE,IACrBD,EAAYC,EAAE,CAAGxB,EAAWwB,EAAE,CAC9B,OAAOxB,EAAWwB,EAAE,EAEpBd,EAAQV,EAAWe,EAAE,IACrBQ,EAAYR,EAAE,CAAGf,EAAWe,EAAE,CAC9B,OAAOf,EAAWe,EAAE,EAExBG,EAAY/J,IAAI,CAACoK,GAEjBtP,EAAMkF,IAAI,CAAC,CAAEsK,UAAW,EAAG,GACvBxP,EAAMyP,GAAG,EACTzP,CAAAA,EAAMyP,GAAG,CAAGzP,EAAMyP,GAAG,CAAChK,OAAO,EAAC,EAGlC,IAAInF,EAAW8O,EAAE3E,KAAK,CAACiF,KAAK,CAAC,EAC7BN,CAAAA,EAAE3E,KAAK,CAAC/I,MAAM,CAAG,EACjB0N,EAAE3E,KAAK,CAAC,EAAE,CAAG,CACTkF,QAAS,WACT5B,WAAYW,EAAgBX,EAAY,CACpC,cAAeA,EAAWgB,UAAU,CACpCa,KAAM,GAAKZ,EAAM,IAAIa,MAAM,CAACR,EAChC,GACA/O,SAAUA,CACd,CACJ,CACJ,EAEA2O,CAAAA,EAAYC,QAAQ,CAAG,CAAEN,KAAMA,EAAMO,KAAMA,CAAK,CACpD,MAEIF,EAAY/J,IAAI,CAAC,CAAEqK,GAAI,EAAGT,GAAI,CAAE,GAChC,OAAOG,EAAYC,QAAQ,CAO/B,OALI,IAAI,CAACY,KAAK,GAEVb,EAAYc,SAAS,CAAG,GACxB,IAAI,CAAClK,QAAQ,CAACmK,SAAS,CAACf,IAErB,IAAI,AACf,CAWA,SAASgB,GAAWC,CAAK,EAErB,IADIzO,EACA0O,EAAOD,EAAMC,IAAI,CACjBC,EAAK,AAAwB,OAAvB3O,CAAAA,EAAK,IAAI,CAACiF,OAAO,AAAD,GAAejF,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG4O,aAAa,CAAC,YACnF,GAAID,EAAI,CA4BJ,IAAK,IA3BDE,EAAU,EAAE,CAAExL,EAAK,IAAI,CAACe,QAAQ,CAAC0K,WAAW,CAAC,IAAI,CAAC7J,OAAO,EAAG8J,EAAM1L,EAAGxF,CAAC,CAAYmR,EAAcC,AAApB5L,EAAG4L,CAAC,CAAoBF,EAAKG,EAAmB,AAAIC,OAAO,gEAEpH,KAAMC,EAAQT,EAC5BU,SAAS,CACTC,OAAO,CAACJ,EAAkB,IAC1BK,KAAK,CAAC,sCAAuCC,EAAaJ,EAAMnP,MAAM,CAI3EwP,EAAqB,SAAUC,CAAS,CACxCC,CAAc,EACV,IAAIpM,EAAIoM,EAAepM,CAAC,CAC5BC,EAAImM,EAAenM,CAAC,CACpBG,EAAW,AAACgL,CAAAA,EAAGiB,iBAAiB,CAACF,GAAa,EAAC,EAAK9C,EACpDiD,EAASC,KAAKC,GAAG,CAACpM,GAClBqM,EAASF,KAAKG,GAAG,CAACtM,GAClB,MAAO,CACH,CACIJ,EAAIyL,EAAca,EAClBrM,EAAIwL,EAAcgB,EACrB,CACD,CACIzM,EAAIwL,EAAMc,EACVrM,EAAIuL,EAAMiB,EACb,CACJ,AACL,EACS1P,EAAI,EAAG4P,EAAY,EAAGA,EAAYV,EAAYU,IAAa,CAGhE,IAAK,IADDC,EAAUC,AADHhB,CAAK,CAACc,EAAU,CACRjQ,MAAM,CAChBoQ,EAAgB,EAAGA,EAAgBF,EAASE,GAAiB,EAClE,GAAI,CACA,IAAIC,EAAgBhQ,EACZ+P,EACAH,EACJ1K,EAAKiK,EAAmBa,EACxB3B,EAAG4B,sBAAsB,CAACD,IAC1BE,EAAQhL,CAAE,CAAC,EAAE,CACbiL,EAAQjL,CAAE,CAAC,EAAE,AACb6K,AAAkB,CAAA,IAAlBA,GACAxB,EAAQhG,IAAI,CAAC4H,GACb5B,EAAQhG,IAAI,CAAC2H,KAGK,IAAdN,GACArB,EAAQ6B,OAAO,CAACD,GAEhBP,IAAcV,EAAa,GAC3BX,EAAQhG,IAAI,CAAC2H,GAGzB,CACA,MAAO7C,EAAG,CAGN,KACJ,CAEJrN,GAAK6P,EAAU,EACf,GAAI,CACA,IAAIG,EAAehQ,EAAI4P,EACnBS,EAAUhC,EAAGiC,oBAAoB,CAACN,GAClC7K,EAAKgK,EAAmBa,EACxBK,GACAH,EAAQ/K,CAAE,CAAC,EAAE,CACbgL,EAAQhL,CAAE,CAAC,EAAE,CACjBoJ,EAAQ6B,OAAO,CAACD,GAChB5B,EAAQ6B,OAAO,CAACF,EACpB,CACA,MAAO7C,EAAG,CAGN,KACJ,CACJ,CAEIkB,EAAQ5O,MAAM,EACd4O,EAAQhG,IAAI,CAACgG,CAAO,CAAC,EAAE,CAACZ,KAAK,IAEjCS,EAAKG,OAAO,CAAGA,CACnB,CACA,OAAOH,CACX,CAWA,SAASmC,GAAapC,CAAK,EAEvB,IADIzO,EACA8Q,EAAerC,EAAMqC,YAAY,CACjC3Q,EAAQsO,EAAMtO,KAAK,CACnBiN,EAAmB0D,CAAY,CAAC3Q,EAAM+H,YAAY,CAAG,WAAW,EAC5D4I,EAAarD,QAAQ,CACzBL,GAAmB,CAAC0D,EAAaC,OAAO,GACxC,IAAI,CAAC7D,WAAW,CAAC,AAAC,CAAA,AAAkC,OAAjClN,CAAAA,EAAKG,EAAM6Q,gBAAgB,AAAD,GAAehR,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG7C,IAAI,CAACgD,EAAO,IAAI,CAAA,GAAMA,EAAM0B,OAAO,CAAEuL,GACzHjN,EAAM8Q,aAAa,EACnB,CAAC7D,EAAgBhK,OAAO,EAExBjD,CAAAA,EAAM8Q,aAAa,CAAI9Q,EAAM8Q,aAAa,CAACjN,OAAO,EAAE,EAGhE,CAyBA,IAAIkN,IACItV,EAAgB,SAAUU,CAAC,CAC3BuB,CAAC,EAOD,MAAOjC,AANHA,CAAAA,EAAgBe,OAAOmB,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAU1B,CAAC,CAC1DuB,CAAC,EAAIvB,EAAEyB,SAAS,CAAGF,CAAG,GACd,SAAUvB,CAAC,CACnBuB,CAAC,EAAI,IAAK,IAAII,KAAKJ,EAAOlB,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACU,EAC/DI,IAAI3B,CAAAA,CAAC,CAAC2B,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,CAAG,CAAA,EACI3B,EAAGuB,EAC5B,EACO,SAAUvB,CAAC,CAAEuB,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIoD,UAAU,uBAAyBC,OAAOrD,GAAK,iCAE7D,SAASK,IAAO,IAAI,CAACC,WAAW,CAAG7B,CAAG,CADtCV,EAAcU,EAAGuB,GAEjBvB,EAAEW,SAAS,CAAGY,AAAM,OAANA,EAAalB,OAAOyB,MAAM,CAACP,GAAMK,CAAAA,EAAGjB,SAAS,CAAGY,EAAEZ,SAAS,CAAE,IAAIiB,CAAG,CACtF,GAGAiT,GAv3Dc,CACd,QAOJ,SAAwBC,CAAU,EAC9B,IAAIC,EAAKD,EAAWC,EAAE,CAClBC,EAAKF,EAAWE,EAAE,CAClBC,EAAKH,EAAWG,EAAE,CAClBC,EAAKJ,EAAWI,EAAE,CAClBxR,EAAKoR,EAAW1O,KAAK,CACrBA,EAAQ1C,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EAC5BqD,EAAK+N,EAAWxN,QAAQ,CACxBA,EAAWP,AAAO,KAAK,IAAZA,GAAwBA,EACnCkI,EAAS6F,EAAW7F,MAAM,CAC1BkG,EAAgBL,EAAWK,aAAa,CACxCtE,EAAO,CACH,CAAC,IACLkE,EACAC,EAAG,CACC,CAAC,IACLD,EACAC,EAAG,CACC,CAAC,IACLD,EACAC,EACAD,EACAG,EACAH,EACAG,EAAG,CACC,CAAC,IACLH,EACAG,EAAG,CACC,CAAC,IACLH,EACAC,EACAD,EACAG,EACAH,EACAG,EAAG,CACC,CAAC,IACLH,EACAG,EAAG,CACF,CACL,OAAOC,EACHC,AAsER,SAAqBvE,CAAI,CAAEwE,CAAC,EAExB,IAAK,IADDrV,EAAI,EAAE,CACDgE,EAAI,EAAGA,EAAI6M,EAAKlN,MAAM,CAAEK,IAAK,CAClC,IAAIiD,EAAI4J,CAAI,CAAC7M,EAAE,CAAC,EAAE,CACdkD,EAAI2J,CAAI,CAAC7M,EAAE,CAAC,EAAE,CAClB,GAAI,AAAa,UAAb,OAAOiD,GAAkB,AAAa,UAAb,OAAOC,GAEhC,GAAIlD,AAAM,IAANA,EACAhE,EAAEuM,IAAI,CAAC,CAAC,IAAKtF,EAAGC,EAAE,OAEjB,GAAIlD,IAAM6M,EAAKlN,MAAM,CAAG,EACzB3D,EAAEuM,IAAI,CAAC,CAAC,IAAKtF,EAAGC,EAAE,OAGjB,GAAImO,EAAG,CACR,IAAIC,EAAUzE,CAAI,CAAC7M,EAAI,EAAE,CACrBuR,EAAU1E,CAAI,CAAC7M,EAAI,EAAE,CACzB,GAAIsR,GAAWC,EAAS,CACpB,IAAIR,EAAKO,CAAO,CAAC,EAAE,CACfN,EAAKM,CAAO,CAAC,EAAE,CACfL,EAAKM,CAAO,CAAC,EAAE,CACfL,EAAKK,CAAO,CAAC,EAAE,CAEnB,GAAI,AAAc,UAAd,OAAOR,GACP,AAAc,UAAd,OAAOE,GACP,AAAc,UAAd,OAAOD,GACP,AAAc,UAAd,OAAOE,GACPH,IAAOE,GACPD,IAAOE,EAAI,CACX,IAAIM,EAAaT,EAAKE,EAAK,EAAI,GAC3BQ,EAAaT,EAAKE,EAAK,EAAI,GAC/BlV,EAAEuM,IAAI,CAAC,CACH,IACAtF,EAAIuO,EAAahC,KAAKkC,GAAG,CAAClC,KAAKmC,GAAG,CAAC1O,EAAI8N,GAAKM,GAC5CnO,EAAIuO,EAAajC,KAAKkC,GAAG,CAAClC,KAAKmC,GAAG,CAACzO,EAAI8N,GAAKK,GAC/C,CAAE,CACC,IACApO,EACAC,EACAD,EACAC,EACAD,EAAIuO,EAAahC,KAAKkC,GAAG,CAAClC,KAAKmC,GAAG,CAAC1O,EAAIgO,GAAKI,GAC5CnO,EAAIuO,EAAajC,KAAKkC,GAAG,CAAClC,KAAKmC,GAAG,CAACzO,EAAIgO,GAAKG,GAC/C,CACL,CACJ,CAEJ,MAEIrV,EAAEuM,IAAI,CAAC,CAAC,IAAKtF,EAAGC,EAAE,EAG9B,CACA,OAAOlH,CACX,EA5HoB,CACR,CAAC,IAAK+U,EAAIC,EAAG,CACb,CAAC,IAAKD,EAAK3O,EAASkB,CAAAA,EAAW,IAAO,EAAE,EAAI0N,EAAG,CAC/C,CAAC,IAAKD,EAAK3O,EAASkB,CAAAA,EAAW,IAAO,EAAE,EAAI4N,EAAG,CAC/C,CAAC,IAAKD,EAAIC,EAAG,CAChB,CAAEjG,GACH4B,CACR,EArDI+E,SAyDJ,SAAyBd,CAAU,EAC/B,IAAIC,EAAKD,EAAWC,EAAE,CAClBC,EAAKF,EAAWE,EAAE,CAClBC,EAAKH,EAAWG,EAAE,CAClBC,EAAKJ,EAAWI,EAAE,CAClBxR,EAAKoR,EAAW1O,KAAK,CAErBW,EAAK+N,EAAWxN,QAAQ,CAG5B,OAAO6N,AADaL,EAAWK,aAAa,CACrB,CACnB,CAAC,IAAKJ,EAAIC,EAAG,CACb,CAAC,IAAKD,EAAK3O,AANH1C,CAAAA,AAAO,KAAK,IAAZA,EAAgB,EAAIA,CAAC,EAMT4D,CAAAA,AAJTP,AAAO,KAAK,IAAZA,GAAwBA,EAIJ,GAAK,CAAA,EAAImO,EAAG,CAC3C,CAAC,IAAKD,EAAIC,EAAG,CAChB,CAAG,CACA,CAAC,IAAKH,EAAIC,EAAG,CACb,CAAC,IAAKD,EAAIG,EAAG,CACb,CAAC,IAAKH,EAAIG,EAAG,CAChB,AACL,EA3EIW,OA+EJ,SAAuBf,CAAU,EAC7B,IAAIC,EAAKD,EAAWC,EAAE,CAClBC,EAAKF,EAAWE,EAAE,CAClBC,EAAKH,EAAWG,EAAE,CAClBC,EAAKJ,EAAWI,EAAE,CAClBxR,EAAKoR,EAAWgB,MAAM,CACtBA,EAASpS,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EAC7BqD,EAAK+N,EAAW1O,KAAK,CACrBA,EAAQW,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EAC5BmC,EAAK4L,EAAWxN,QAAQ,CACxBA,EAAW4B,AAAO,KAAK,IAAZA,GAAwBA,EAEvC,OAAOiM,AADaL,EAAWK,aAAa,CAExC,CACI,CAAC,IAAKJ,EAAIC,EAAG,CACb,CACI,IACAD,EAAKe,EACLd,EACAD,EAAKe,EAAS1P,EAASkB,CAAAA,EAAW,GAAK,CAAA,EACvC4N,EACAH,EAAK3O,EAASkB,CAAAA,EAAW,GAAK,CAAA,EAC9B4N,EACH,CACD,CAAC,IAAKD,EAAIC,EAAG,CAChB,CACD,CACI,CAAC,IAAKH,EAAIC,EAAG,CACb,CAAC,IAAKD,EAAIC,EAAID,EAAIG,EAAIH,EAAIG,EAAG,CAC7B,CAAC,IAAKD,EAAIC,EAAG,CAChB,AACT,CA7GA,EAq3DIa,GAAc,AAAC5U,IAA2IwE,MAAM,CAAChF,SAAS,CAAE+C,GAAK,AAACvC,IAA2IkD,WAAW,CAAE2R,GAAgBtS,GAAGY,OAAO,CAAE2R,GAAevS,GAAGwI,MAAM,CAE9XgK,GAAU,AAAC7U,IAA+HV,SAAS,CAACuV,OAAO,CAM3JC,GAAW,AAAClV,IAA+EkV,QAAQ,CAAEC,GAAQ,AAACnV,IAA+EmV,KAAK,CAAEC,GAAyB,AAACpV,IAA+E0I,MAAM,CAAE2M,GAAwB,AAACrV,IAA+EgE,KAAK,CAAEsR,GAAuB,AAACtV,IAA+E8I,IAAI,CAAEyM,GAAiC,AAACvV,IAA+E+I,cAAc,CAAEyM,GAAQ,AAACxV,IAA+EwV,KAAK,CAMnvBC,AApDe,CAAA,CACXC,QATJ,SAAiBC,CAAe,EAC5BrG,EAAkBqG,EAAiB,eAAgB1E,IACnD3B,EAAkBqG,EAAiB,wBAAyBrC,IAC5D,IAAIsC,EAAkBD,EAAgBjW,SAAS,AAC1CkW,CAAAA,EAAgBjG,WAAW,EAC5BiG,CAAAA,EAAgBjG,WAAW,CAAGA,EAAU,CAEhD,CAGA,CAAA,EAkDoB+F,OAAO,CAAEtG,KAe7B,IAAIyG,GAAiC,SAAU9U,CAAM,EAEjD,SAAS8U,IAML,IAAI7U,EAAQD,AAAW,OAAXA,GAAmBA,EAAOE,KAAK,CAAC,IAAI,CAC5CC,YAAc,IAAI,CAGtB,OAFAF,EAAM0K,QAAQ,CAAG,EAAE,CACnB1K,EAAM8U,KAAK,CAAG,EAAE,CACT9U,CACX,CA6fA,OAzgBA2S,GAAwBkC,EAAiB9U,GAkBzC8U,EAAgBnW,SAAS,CAACqW,IAAI,CAAG,WAC7BhV,EAAOrB,SAAS,CAACqW,IAAI,CAAC9U,KAAK,CAAC,IAAI,CAAEC,WAClC,IAAI,CAAC8U,eAAe,CAAG,IAvnB+B9K,EA0nBtD,IAAIxG,EAAS,IAAI,CACbuR,EAAa,IAAI,CAAC1Q,KAAK,CAAC2Q,eAAe,CAgBtCD,EAAWE,IAAI,CAAC,SAAUC,CAAC,EAAI,MAAOA,AAAW,kBAAXA,EAAEC,IAAI,AAAsB,IACnEJ,EAAW3K,IAAI,CAhBC,WACZ,IAAIgL,EAAa,EAAE,CAEvB,GAAI5R,EAAOO,OAAO,CAAC2J,UAAU,EACzB,CAAC4G,GAAM9Q,EAAOO,OAAO,CAAC2J,UAAU,CAAC,CAAC,EAAE,CAAC2H,YAAY,CACjD,IAAK,IAAI/T,EAAK,EAAGC,EAAMiC,EAAOoR,KAAK,EAAI,EAAE,CAAGtT,EAAKC,EAAGC,MAAM,CAAEF,IAAM,CAC9D,IAAI2L,EAAO1L,CAAE,CAACD,EAAG,AACb2L,CAAAA,EAAKqI,SAAS,EACdF,EAAWhL,IAAI,CAAC6C,EAAKqI,SAAS,CAEtC,CAEJ,OAAOF,CACX,EAKJ,EAOAT,EAAgBnW,SAAS,CAAC+W,kBAAkB,CAAG,WAC3C,IAAIzV,EAAQ,IAAI,CACZuE,EAAQ,IAAI,CAACA,KAAK,CAClBb,EAAS,IAAI,CACbmF,EAAYtE,EAAMsE,SAAS,CAC3B6M,EAAYnR,EAAMmR,SAAS,CAC3BhN,EAAcwL,GAAS,IAAI,CAACyB,MAAM,CAACC,GAAG,CAAC,SAAUlW,CAAC,EAAI,OAAOA,EAAEoE,IAAI,CAACiI,SAAS,AAAE,IAC/E8J,EAAOC,IACPC,EAAO,CAACD,IACRE,EAAOF,IACPG,EAAO,CAACH,IACRI,EAAW,EACXC,EAAW,EACXC,EAAW,EACXC,EAAW,EACf,IAAI,CAACV,MAAM,CAACW,OAAO,CAAC,SAAU1U,CAAK,EAI/B,GAAI5B,CAAAA,EAAMiE,OAAO,CAACuJ,SAAS,EAAK5L,EAAMC,OAAO,EAG7C,IANIJ,EA2BAyE,EArBApC,EAAOlC,EAAMkC,IAAI,CACjBC,EAAQL,EAAOG,iBAAiB,CAACjC,EAAMkC,IAAI,CAACC,KAAK,CAAC,EAAI,CAAC,EACvDwS,EAAgBlC,GAAsBrU,EAAMiE,OAAO,CAAC8I,MAAM,CAC1DhJ,EAAMgJ,MAAM,CACZnL,EAAMqC,OAAO,CAAC8I,MAAM,EACpBnE,EAAY,AAA+B,OAA9BnH,CAAAA,EAAK8U,EAAcpS,KAAK,AAAD,GAAe1C,AAAO,KAAK,IAAZA,EAAgBA,EAAK+U,EAA6BxW,EACrG0I,GACAsE,EAASuH,GAA+BgC,EAAcvJ,MAAM,EAAI,EAChEuE,KAAKkC,GAAG,CAAC5K,EACT6M,IACAzI,EAASsJ,EAActJ,MAAM,CAC7BwJ,EAAY,AAACxJ,AAAW,WAAXA,GAAwBsJ,EAAcnS,MAAM,CAErDmQ,GAA+BgC,EAAcnS,MAAM,CACvDsR,GAFI1I,AAAS,EAATA,EAGJ0J,EAAYzJ,AAAW,WAAXA,GAAwBrE,EAEhC2L,GAA+B3L,EACnCC,GAFImE,AAAS,EAATA,CAGRlJ,CAAAA,EAAK4S,SAAS,CAAGA,EACjB5S,EAAK2S,SAAS,CAAGA,EAEb3S,EAAKiI,SAAS,EAAI8J,IAClBA,EAAO/R,EAAKiI,SAAS,CAErBoK,EAAW5E,KAAKoF,GAAG,CAACD,EADRH,CAAAA,EAAcrQ,SAAS,EAAI,CAAA,EACIiQ,IAE3CrS,EAAKiI,SAAS,EAAIgK,IAClBA,EAAOjS,EAAKiI,SAAS,CAErBmK,EAAW3E,KAAKoF,GAAG,CAACD,EADRH,CAAAA,EAAcrQ,SAAS,EAAI,CAAA,EACIgQ,IAE3CpS,EAAKgI,SAAS,EAAIkK,IAClBA,EAAOlS,EAAKgI,SAAS,CAErBuK,EAAW9E,KAAKoF,GAAG,CAACF,EADRF,CAAAA,EAAcrQ,SAAS,EAAI,CAAA,EACImQ,IAE3CvS,EAAKgI,SAAS,EAAImK,IAClBA,EAAOnS,EAAKgI,SAAS,CAErBsK,EAAW7E,KAAKoF,GAAG,CAACF,EADRF,CAAAA,EAAcrQ,SAAS,EAAI,CAAA,EACIkQ,IAEnD,GAGA,IAAIQ,EAAKX,IAASD,EACV,EACA,AAACN,CAAAA,EAAY,AAACW,CAAAA,EAAWD,CAAO,EAAK,CAAA,EAAMH,CAAAA,EAAOD,CAAG,EAAIa,EAAKZ,IAASD,EAAON,EAAY,EAAI,CAACkB,EAAKZ,EAAOK,EAAW,EAAGS,EAAKf,IAASF,EACvI,EACA,AAAChN,CAAAA,EAAY,AAACqN,CAAAA,EAAWA,CAAO,EAAK,CAAA,EAAMH,CAAAA,EAAOF,CAAG,EAAIkB,EAAKhB,IAASF,EAAOhN,EAAY,EAAI,CAACiO,EAAKjB,EAAOM,EAAW,EAC9H,MAAO,CAAEW,GAAIA,EAAIC,GAAIA,EAAIH,GAAIA,EAAIC,GAAIA,CAAG,CAC5C,EACAhC,EAAgBnW,SAAS,CAACsY,QAAQ,CAAG,WACjC,IAAIhX,EAAQ,IAAI,CACZ0D,EAAS,IAAI,CACboR,EAAQ,EAAE,CA6Bd,OA5BA,IAAI,CAACmC,IAAI,CAACX,OAAO,CAAC,SAAU1U,CAAK,EAC7B,IAAIgC,EAAeF,EAAOG,iBAAiB,CAACjC,EAAMkC,IAAI,CAACC,KAAK,EAAI,EAAE,EAAI,CAAC,EACvE,GAAInC,EAAMkC,IAAI,CAACxC,MAAM,CAAE,CACnB,IAAI4V,EAAe7C,GAAsBzQ,EACrChC,EAAMqC,OAAO,EACjB,GAAI,CAACrC,EAAMkF,YAAY,EAAIlF,EAAMkF,YAAY,CAACqQ,SAAS,CAAE,CACrD,IAAIhK,EAAO,IAAIzJ,EAAO0T,SAAS,CAAC1T,EAC5BwT,EACA,KAAK,EACLtV,EACJA,CAAAA,EAAMkF,YAAY,CAAGqG,CACzB,MAGIvL,EAAMlB,SAAS,CAAG4T,GAAqB1S,EAAMlB,SAAS,CAAE,AAACV,CAAAA,EAAM6D,iBAAiB,CAACjC,EAAMkC,IAAI,CAACC,KAAK,CAAC,EAAI,CAAC,CAAA,EAAGrD,SAAS,EACnHkB,EAAMkF,YAAY,CAACjF,OAAO,CACtBD,EAAMkF,YAAY,CAAC+C,MAAM,CAAChI,OAAO,AAEzCD,CAAAA,EAAMkF,YAAY,CAACuE,KAAK,CAAGyJ,EAAMxK,IAAI,CAAC1I,EAAMkF,YAAY,EAAI,CAChE,MAEQlF,EAAMkF,YAAY,GAClBpD,EAAOoR,KAAK,CAACuC,MAAM,CAACzV,EAAMkF,YAAY,CAACuE,KAAK,EAC5CzJ,EAAMkF,YAAY,CAACrB,OAAO,GAC1B,OAAO7D,EAAMkF,YAAY,CAGrC,GACOgO,CACX,EACAD,EAAgBnW,SAAS,CAAC4Y,SAAS,CAAG,SAAUxN,CAAE,CAAEuB,CAAK,CAAEtH,CAAK,CAAEwT,CAAI,CAAEjW,CAAM,EAC1E,IAAIM,EAAQ,IAAI,CAAC+T,MAAM,CAACtK,EAAM,CAE9B,OADAtH,EAAQ,AAACnC,GAASA,EAAMmC,KAAK,EAAKA,EAC3BhE,EAAOrB,SAAS,CAAC4Y,SAAS,CAAC1Y,IAAI,CAAC,IAAI,CAAEkL,EAAIuB,EAAOtH,EAAOwT,EAAMjW,EACzE,EACAuT,EAAgBnW,SAAS,CAAC8Y,aAAa,CAAG,WAGtC,MAAO,CAAC,CACZ,EACA3C,EAAgBnW,SAAS,CAAC+Y,kBAAkB,CAAG,SAAU3T,CAAI,CAAEN,CAAU,EACrE,IAAIxD,EAAQ,IAAI,CACZ4B,EAAQkC,EAAKlC,KAAK,CAClBA,IAEAA,EAAMlB,SAAS,CAAG4T,GAAqB1S,EAAMlB,SAAS,CAAE,AAAC,CAAA,IAAI,CAACmD,iBAAiB,CAACC,EAAKC,KAAK,CAAC,EAAI,CAAC,CAAA,EAAGrD,SAAS,EAC5GkB,EAAMC,OAAO,CAAG2B,EAChBA,EAAaA,AAAe,CAAA,IAAfA,GAA+B,CAAC5B,EAAMlB,SAAS,EAEhEoD,EAAKxD,QAAQ,CAACgW,OAAO,CAAC,SAAUtK,CAAS,EACrChM,EAAMyX,kBAAkB,CAACzL,EAAWxI,EACxC,EACJ,EACAqR,EAAgBnW,SAAS,CAACgZ,WAAW,CAAG,WACpC1D,GAAatV,SAAS,CAACgZ,WAAW,CAACzX,KAAK,CAAC,IAAI,CAAEC,WAC/C8T,GAAatV,SAAS,CAACgZ,WAAW,CAAC9Y,IAAI,CAAC,IAAI,CAAE,IAAI,CAACkW,KAAK,CAC5D,EAKAD,EAAgBnW,SAAS,CAACiZ,SAAS,CAAG,WAClC,IAKIvO,EALApJ,EAAQ,IAAI,CAEZiE,EAAUP,AADD,IAAI,CACIO,OAAO,CAExBkF,EAASyO,EAHA,IAAI,EAMjB9D,GAAY6D,SAAS,CAAC/Y,IAAI,CANb,IAAI,EAOjB,IAAIgM,EAAOlH,AAPE,IAAI,CAOCkH,IAAI,CAAGlH,AAPZ,IAAI,CAOemU,OAAO,GACvCzO,EAAW1F,AARE,IAAI,CAQCoU,OAAO,CAAC3O,EAAO,CAClB,KAAXA,GAAkB,AAACC,GAAaA,EAAS9I,QAAQ,CAACoB,MAAM,GACxDgC,AAVS,IAAI,CAUNqU,WAAW,CAAC,GAAI,CAAA,GACvB5O,EAASzF,AAXA,IAAI,CAWG0F,QAAQ,CACxBA,EAAW1F,AAZF,IAAI,CAYKoU,OAAO,CAAC3O,EAAO,EAErCzF,AAda,IAAI,CAcVG,iBAAiB,CAAGmU,EAAgC,CACvD7P,KAAMiB,EAASrF,KAAK,CAAG,EACvBsE,OAAQpE,EAAQoE,MAAM,CACtBD,GAAIwC,EAAKxG,MAAM,CACf6D,SAAU,CACNQ,gBAAiB/E,AAnBZ,IAAI,CAmBeO,OAAO,CAACwE,eAAe,CAC/CwP,aAAchU,EAAQgU,YAAY,AACtC,CACJ,GACA,IAAI,CAACR,kBAAkB,CAAC7M,EAAM,CAAA,GAC9BlH,AAxBa,IAAI,CAwBVoR,KAAK,CAAGpR,AAxBF,IAAI,CAwBKsT,QAAQ,GAC9BtT,AAzBa,IAAI,CAyBVwU,aAAa,CAACtN,GACrB,IAAI,CAACoK,eAAe,CAACxK,kBAAkB,CA1B1B,IAAI,EA2BjB9G,AA3Ba,IAAI,CA2BVyU,cAAc,CAAG,IAAI,CAAC1C,kBAAkB,GAC/C,IAAI,CAACE,MAAM,CAACW,OAAO,CAAC,SAAU1U,CAAK,EAC/B5B,EAAMoY,aAAa,CAACxW,EACxB,GACA,IAAI,CAAC+T,MAAM,CAACW,OAAO,CAAC,SAAU1U,CAAK,EAC3BA,EAAMkF,YAAY,EAClB9G,EAAMqY,aAAa,CAACzW,EAAMkF,YAAY,CAE9C,GACK7C,EAAQgU,YAAY,EACrBvU,AArCS,IAAI,CAqCN4U,iBAAiB,CAAC5U,AArChB,IAAI,CAqCmBkH,IAAI,CAE5C,EACAiK,EAAgBnW,SAAS,CAAC2Z,aAAa,CAAG,SAAUlL,CAAI,EAMpD,IALI1L,EACAqD,EACAmC,EACAC,EACAC,EACAyC,EAAWuD,EAAKvD,QAAQ,CACxBC,EAASsD,EAAKtD,MAAM,CACpB0O,EAAY,AAAC,CAAA,AAA6B,OAA5B9W,CAAAA,EAAK,IAAI,CAACwC,OAAO,CAACkJ,IAAI,AAAD,GAAe1L,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGyE,SAAS,AAAD,GAAM,EAC5FsS,EAASlE,GAAqB,AAA6B,OAA5BxP,CAAAA,EAAK,IAAI,CAACb,OAAO,CAACkJ,IAAI,AAAD,GAAerI,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG2T,WAAW,CAAE,IAC5GpL,EAAOiH,GAAqB,AAA6B,OAA5BrN,CAAAA,EAAKkG,EAAKlJ,OAAO,CAACkJ,IAAI,AAAD,GAAelG,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGoG,IAAI,CAAE,AAA6B,OAA5BnG,CAAAA,EAAK,IAAI,CAACjD,OAAO,CAACkJ,IAAI,AAAD,GAAejG,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGmG,IAAI,CAAE,WAC9K,GAAIzD,EAASjF,SAAS,EAAIkF,EAAOlF,SAAS,CAAE,CACxC,IAAI+T,EAAiB9O,EAASjF,SAAS,CAACR,KAAK,EAAI,EAC7CkB,EAAW,IAAI,CAACd,KAAK,CAACc,QAAQ,CAC9B0N,EAAKoB,GAAM,AAACvK,CAAAA,EAASjF,SAAS,CAACM,CAAC,EAAI,CAAA,EAChC,AAAC2E,CAAAA,EAASjF,SAAS,CAACP,MAAM,EAAI,CAAA,EAAK,EACvCmU,GACAtF,EAAKkB,GAAM,AAACtK,CAAAA,EAAOlF,SAAS,CAACM,CAAC,EAAI,CAAA,EAC9B,AAAC4E,CAAAA,EAAOlF,SAAS,CAACP,MAAM,EAAI,CAAA,EAAK,EACrCmU,GACAzF,EAAKqB,GAAM,AAACvK,CAAAA,EAASjF,SAAS,CAACK,CAAC,EAAI,CAAA,EAAK0T,EACzCH,GACAvF,EAAKmB,GAAMtK,EAAOlF,SAAS,CAACK,CAAC,EAAI,EACjCuT,GACAlT,IACAyN,GAAM4F,EACN1F,GAAOnJ,EAAOlF,SAAS,CAACR,KAAK,EAAI,GAErC,IAAIwU,EAAO9O,EAAO/F,IAAI,CAACiI,SAAS,CAAGnC,EAAS9F,IAAI,CAACiI,SAAS,AAC1DoB,CAAAA,EAAKyL,SAAS,CAAG,OACjB,IACIzU,EAAQ,AADIoN,CAAAA,KAAKmC,GAAG,CAACV,EAAKF,GAAM4F,CAAY,EACvBC,EAAQD,CAIjCvL,CAAAA,EAAK0L,KAAK,CAFI1E,GAAM,AAACnB,CAAAA,EAAKF,CAAC,EAAK,EAC5ByF,GAEJpL,EAAK2L,KAAK,CAAG7F,EACb9F,EAAKxI,SAAS,CAAG,CACb5G,EAAG6U,EAA2B,CAACvF,EAAK,CAAC,CACjCyF,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJC,GAAIA,EACJ9O,MAAOA,EACP0P,OAZK1P,EAAQqU,EAAUnT,CAAAA,EAAW,GAAK,CAAA,EAavCA,SAAUA,EACV6N,cAAerJ,EAAOhI,OAAO,CAC7BmL,OAAQ,AAA6B,OAA5B7F,CAAAA,EAAK,IAAI,CAAClD,OAAO,CAACkJ,IAAI,AAAD,GAAehG,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG6F,MAAM,AACnF,EACJ,EACAG,EAAK4L,KAAK,CAAG,CACT/T,EAAG,AAAC8N,CAAAA,EAAKE,CAAC,EAAK,EACf/N,EAAG,AAAC8N,CAAAA,EAAKE,CAAC,EAAK,EACf7O,OAAQmU,EACRpU,MAAO,CACX,EACAgJ,EAAK6L,UAAU,CAAG3T,EAAW,CACzB,AAAC,CAAA,IAAI,CAACd,KAAK,CAACmR,SAAS,EAAI,CAAA,EAAKvI,EAAK4L,KAAK,CAAC9T,CAAC,CAC1C,AAAC,CAAA,IAAI,CAACV,KAAK,CAACsE,SAAS,EAAI,CAAA,EAAKsE,EAAK4L,KAAK,CAAC/T,CAAC,CAC7C,CAAG,CACAmI,EAAK4L,KAAK,CAAC/T,CAAC,CACZmI,EAAK4L,KAAK,CAAC9T,CAAC,CACf,AACL,CACJ,EAKA4P,EAAgBnW,SAAS,CAACua,cAAc,CAAG,SAAUtD,CAAM,EASvD,IAAK,IARDlU,EACAqD,EACAmC,EACAC,EAGAjD,EACAF,EAFAF,EAAoBH,AADX,IAAI,CACcG,iBAAiB,CAGvCrC,EAAK,EAAsBA,EAAK0X,AAAbvD,EAAsBjU,MAAM,CAAEF,IAAM,CAC5D,IAAII,EAAQsX,AADYvD,CACJ,CAACnU,EAAG,CAUxB,GATAuC,EAAQF,CAAiB,CAACjC,EAAMkC,IAAI,CAACC,KAAK,CAAC,CAE3CE,EAAU,CAAEK,MAAO,CAAC,CAAE,EAElBP,GAASA,EAAM6J,UAAU,GACzB3J,EAAUoQ,GAAsBpQ,EAASF,EAAM6J,UAAU,EACzDlK,AAZK,IAAI,CAYFyV,aAAa,CAAG,WAAc,MAAO,CAAA,CAAM,GAGlDvX,EAAM+C,SAAS,EACfjB,AAhBK,IAAI,CAgBFO,OAAO,CAAC2J,UAAU,CAAE,CAC3B,IAAIrH,EAAM,CAAC,EACPY,EAAKvF,EAAM+C,SAAS,CACpByU,EAAKjS,EAAGhD,KAAK,CACbA,EAAQiV,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EAC5BC,EAAKlS,EAAG/C,MAAM,CACdA,EAASiV,AAAO,KAAK,IAAZA,EAAgB,EAAIA,CAC7B3V,CAvBC,IAAI,CAuBEa,KAAK,CAACc,QAAQ,EACrB5D,CAAAA,AAAsB0C,EAAQ1C,AAA9BA,CAAAA,EAAK,CAAC2C,EAAQD,EAAM,AAAD,CAAa,CAAC,EAAE,CAAEC,EAAS3C,CAAE,CAAC,EAAE,AAAD,EAEhD,CAAA,AAAqD,OAApDqD,CAAAA,EAAK0P,GAAM9Q,AA1Bb,IAAI,CA0BgBO,OAAO,CAAC2J,UAAU,CAAC,CAAC,EAAE,CAACtJ,KAAK,AAAD,GAAeQ,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGX,KAAK,AAAD,GAC/FoC,CAAAA,EAAIpC,KAAK,CAAG,GAAKA,EAAQ,IAAG,EAE1B,CAAA,AAAqD,OAApD8C,CAAAA,EAAKuN,GAAM9Q,AA7Bb,IAAI,CA6BgBO,OAAO,CAAC2J,UAAU,CAAC,CAAC,EAAE,CAACtJ,KAAK,AAAD,GAAe2C,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGqS,SAAS,AAAD,GACnG/S,CAAAA,EAAI+S,SAAS,CAAG/H,KAAKgI,KAAK,CAACnV,EAAS,GAAE,EAE1CgQ,GAAuBnQ,EAAQK,KAAK,CAAEiC,GACtC,AAA2B,OAA1BW,CAAAA,EAAKtF,EAAM4T,SAAS,AAAD,GAAetO,AAAO,KAAK,IAAZA,GAAyBA,EAAGX,GAAG,CAACA,EACvE,CAEA3E,EAAM4X,SAAS,CAAGnF,GAAsBpQ,EAASrC,EAAMqC,OAAO,CAAC2J,UAAU,CAC7E,CACAkG,GAAY2F,cAAc,CAAC7a,IAAI,CAAC,IAAI,CAAE+W,EAC1C,EAMAd,EAAgBnW,SAAS,CAACgb,cAAc,CAAG,SAAU9X,CAAK,CAAE4T,CAAS,EACjE,IAAI3T,EAAUD,EAAMC,OAAO,AAE3BD,CAAAA,EAAMC,OAAO,CAAG,CAAA,EAChB9B,EAAOrB,SAAS,CAACgb,cAAc,CAACzZ,KAAK,CAAC,IAAI,CAAEC,WAE5CsV,EAAUjS,OAAO,CAAC,CACdiC,QAAS3D,CAAAA,CAAAA,AAAY,CAAA,IAAZA,CAAgB,CAC7B,EAAG,KAAK,EAAG,WAEPA,GAAW2T,EAAUmE,IAAI,EAC7B,GAEA/X,EAAMC,OAAO,CAAGA,CACpB,EAKAgT,EAAgBnW,SAAS,CAAC+a,cAAc,CAAG,WACnC,IAAI,CAACxV,OAAO,CAAC2J,UAAU,GACvB,IAAI,CAAC3J,OAAO,CAAC2J,UAAU,CAAG4G,GAAM,IAAI,CAACvQ,OAAO,CAAC2J,UAAU,EAEvD,IAAI,CAACqL,cAAc,CAAC,IAAI,CAACtD,MAAM,EAE/B7B,GAAY2F,cAAc,CAAC7a,IAAI,CAAC,IAAI,CAAE,IAAI,CAACkW,KAAK,EAExD,EACAD,EAAgBnW,SAAS,CAAC+G,OAAO,CAAG,WAEhC,GAAI,IAAI,CAACqP,KAAK,CAAE,CACZ,IAAK,IAAItT,EAAK,EAAGC,EAAK,IAAI,CAACqT,KAAK,CAAEtT,EAAKC,EAAGC,MAAM,CAAEF,IAE9C2L,AADW1L,CAAE,CAACD,EAAG,CACZiE,OAAO,EAEhB,CAAA,IAAI,CAACqP,KAAK,CAACpT,MAAM,CAAG,CACxB,CACA,OAAOoS,GAAYrO,OAAO,CAACxF,KAAK,CAAC,IAAI,CAAEC,UAC3C,EAKA2U,EAAgBnW,SAAS,CAACkb,YAAY,CAAG,SAAUhY,CAAK,CAAE8C,CAAK,EAC3D,IACId,EAAehC,GACX8B,AAFK,IAAI,CAEFG,iBAAiB,CAACjC,EAAMkC,IAAI,CAACC,KAAK,EAAI,EAAE,EAAI,CAAC,EACxDE,EAAUrC,GAASA,EAAMqC,OAAO,CAChC4V,EAAe,AAACjW,EAAayD,MAAM,EAC/BzD,EAAayD,MAAM,CAAC3C,EAAM,EAC1B,CAAC,EACL9C,GACAA,CAAAA,EAAMqC,OAAO,CAAC8I,MAAM,CAAGsH,GAAsB3Q,AARpC,IAAI,CAQuCO,OAAO,CAAC8I,MAAM,CAAEnJ,EAAamJ,MAAM,CAAEnL,EAAMqC,OAAO,CAAC8I,MAAM,CAAA,EAEjH,IAAI+M,EAAYxF,GAAqBuF,GAAgBA,EAAa1M,IAAI,EAAI0M,EAAa1M,IAAI,CAACvH,KAAK,CAC7F3B,GAAWA,EAAQkJ,IAAI,EAAIlJ,EAAQkJ,IAAI,CAACvH,KAAK,CAC7ChC,GAAgBA,EAAauJ,IAAI,EAAIvJ,EAAauJ,IAAI,CAACvH,KAAK,CAC5DlC,AAbS,IAAI,CAaNO,OAAO,CAACkJ,IAAI,EAAIzJ,AAbd,IAAI,CAaiBO,OAAO,CAACkJ,IAAI,CAACvH,KAAK,EAChDmU,EAAgBzF,GAAqBuF,GAAgBA,EAAa1M,IAAI,EAClE0M,EAAa1M,IAAI,CAACjH,SAAS,CAC/BjC,GAAWA,EAAQkJ,IAAI,EAAIlJ,EAAQkJ,IAAI,CAACjH,SAAS,CACjDtC,GAAgBA,EAAauJ,IAAI,EAC7BvJ,EAAauJ,IAAI,CAACjH,SAAS,CAC/BxC,AAnBS,IAAI,CAmBNO,OAAO,CAACkJ,IAAI,EAAIzJ,AAnBd,IAAI,CAmBiBO,OAAO,CAACkJ,IAAI,CAACjH,SAAS,EACpD8T,EAAUlG,GAAY8F,YAAY,CAAChb,IAAI,CApB9B,IAAI,CAqBbgD,EACA8C,GAWJ,OAVI9C,IACIA,EAAMuB,MAAM,GACZ6W,EAAQhU,MAAM,CAAG8T,EACjBE,CAAO,CAAC,eAAe,CAAGD,EAC1B,OAAOC,EAAQtU,IAAI,EAElB9D,EAAMC,OAAO,EACdmY,CAAAA,EAAQxU,OAAO,CAAG,CAAA,GAGnBwU,CACX,EACAnF,EAAgBnW,SAAS,CAACub,UAAU,CAAG,WACnClG,GAAcrV,SAAS,CAACub,UAAU,CAACha,KAAK,CAAC,IAAI,CAAEC,WAC/C8T,GAAatV,SAAS,CAACub,UAAU,CAACrb,IAAI,CAAC,IAAI,CAAE,IAAI,CAACkW,KAAK,CAC3D,EAKAD,EAAgBnW,SAAS,CAAC0Z,aAAa,CAAG,SAAUxW,CAAK,EACrD,IAAI2C,EAAQ,IAAI,CAACA,KAAK,CAClBT,EAAOlC,EAAMkC,IAAI,CACjB4R,EAAYnR,EAAMmR,SAAS,CAC3B7M,EAAYtE,EAAMsE,SAAS,CAE3BpH,EAAK,IAAI,CAAC0W,cAAc,CACxBrB,EAAKrV,EAAGqV,EAAE,CACVC,EAAKtV,EAAGsV,EAAE,CACVH,EAAKnV,EAAGmV,EAAE,CACVC,EAAKpV,EAAGoV,EAAE,CACV7R,EAAI8R,EAAKhT,EAAKiI,SAAS,CAAGgL,EAC1B9R,EAAI2R,EAAK9S,EAAKgI,SAAS,CAAG+K,EAC1B9S,EAAQ,IAAI,CAACF,iBAAiB,CAACC,EAAKC,KAAK,CAAC,EAAI,CAAC,EAI/CkJ,EAASsJ,AAHOlC,GAAsB,IAAI,CAACpQ,OAAO,CAAC8I,MAAM,CACzDhJ,EAAMgJ,MAAM,CACZnL,EAAMqC,OAAO,CAAC8I,MAAM,EACGE,MAAM,CAC7B7I,EAASN,EAAK2S,SAAS,CACvBtS,EAAQL,EAAK4S,SAAS,CACtB5J,EAAW,IAAI,CAAC7I,OAAO,CAAC6I,QAAQ,CAChCoN,EAAQpW,EAAKkB,CAAC,CAAIT,EAAMc,QAAQ,CAC5BwD,EAAY1E,EAAQ,EAAIa,EACxBA,EAAIb,EAAQ,EAChBgW,EAAQrW,EAAKmB,CAAC,CAAI,AAAC6H,EAEf7H,EAAIb,EAAS,EADbsR,EAAYzQ,EAAIb,EAAS,EAE7BgW,EAAe9F,GAAqB1S,EAAMqC,OAAO,CAACmW,YAAY,CAC9DrW,EAAMqW,YAAY,CAClB,IAAI,CAACnW,OAAO,CAACmW,YAAY,EACzBC,EAAWpG,EAAO,CAAChH,GAAU,SAAS,CAS1C,GARIoN,AAAa,KAAK,IAAlBA,GACAzY,EAAM0Y,QAAQ,CAAG,CAAA,EACjB1Y,EAAMgX,SAAS,CAAG,QAClBhX,EAAM2Y,QAAQ,CAAGtN,EAAOuN,KAAK,CAAC,iBAAiB,CAAC,EAAE,EAGlD5Y,EAAMgX,SAAS,CAAG,OAElB,CAAChX,EAAMC,OAAO,EAAID,EAAMkF,YAAY,CAAE,CACtC,IAAI5E,EAAaN,EAAMkF,YAAY,CAAC8C,QAAQ,CAC5C,GAAI1H,EAAY,CACZ,IAAIuY,EAAkBvY,EAAWyC,SAAS,EAAI,CAAC,EAC3CG,EAAK2V,EAAgBzV,CAAC,CACtB0V,EAAM5V,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EAC1BmC,EAAKwT,EAAgBxV,CAAC,CACtB0V,EAAM1T,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EAC1BC,EAAKuT,EAAgBtW,KAAK,CAE1BgD,EAAKsT,EAAgBrW,MAAM,AAE1BxC,CAAAA,EAAM+C,SAAS,EAChB/C,CAAAA,EAAM+C,SAAS,CAAG,CAAC,CAAA,EAElB/C,EAAM0Y,QAAQ,EACflG,GAAuBxS,EAAM+C,SAAS,CAAE,CACpC5G,EAAGsc,EAASK,EAAKC,EARXzT,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EAEnBC,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EAMciT,EAAe,CAAEhH,EAAGgH,CAAa,EAAI,KAAK,EACvF,GAEJhG,GAAuBxS,EAAM+C,SAAS,CAAE,CAAEK,EAAG0V,EAAKzV,EAAG0V,CAAI,GACzD/Y,EAAMiX,KAAK,CAAG3W,EAAW2W,KAAK,CAC9BjX,EAAMkX,KAAK,CAAG5W,EAAW4W,KAAK,AAClC,CACJ,MAEIlX,EAAMiX,KAAK,CAAGqB,EACdtY,EAAMkX,KAAK,CAAGqB,EACdvY,EAAM+C,SAAS,CAAG,CACdK,EAAGkV,EACHjV,EAAGkV,EACHhW,MAAOA,EACPC,OAAQA,EACRgJ,OAAQ,AAACxL,EAAMkC,IAAI,CAAC8W,MAAM,CAAe,UAAZ,SACjC,EACKhZ,EAAM0Y,QAAQ,EACf1Y,CAAAA,EAAM+C,SAAS,CAAC5G,CAAC,CAAGsc,EAASH,EAAOC,EAAOhW,EAAOC,EAAQgW,EAAe,CAAEhH,EAAGgH,CAAa,EAAI,KAAK,EAAC,CAI7GxY,CAAAA,EAAMoX,UAAU,CAAGzU,EAAMc,QAAQ,CAC7B,CAACqQ,EAAYyE,EAAQ/V,EAAS,EAAGyE,EAAYqR,EAAQ/V,EAAQ,EAAE,CAC/D,CAAC+V,EAAQ/V,EAAQ,EAAGgW,EAAM,AAClC,EACAtF,EAAgBgG,cAAc,CAAGxG,GAAsBN,GAAc8G,cAAc,CAAEhO,GAC9EgI,CACX,EAAEd,IACFK,GAAuBS,GAAgBnW,SAAS,CAAE,CAC9CmE,WApzDyDI,EAqzDzDX,UA3hEwDxC,EA4hExDsX,UA78C8C3N,CA88ClD,GACAvK,IAA0I4b,kBAAkB,CAAC,YAAajG,IAkH7I,IAAI/V,GAAkBE,IAGzC,OADYH,EAAoB,OAAU,AAE3C,GAET"}