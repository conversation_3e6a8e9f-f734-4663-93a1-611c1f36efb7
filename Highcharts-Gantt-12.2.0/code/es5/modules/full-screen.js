!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("highcharts"),require("highcharts").AST):"function"==typeof define&&define.amd?define("highcharts/modules/full-screen",[["highcharts/highcharts"],["highcharts/highcharts","AST"]],t):"object"==typeof exports?exports["highcharts/modules/full-screen"]=t(require("highcharts"),require("highcharts").AST):e.Highcharts=t(e.Highcharts,e.Highcharts.AST)}(this,function(e,t){return function(){"use strict";var n={660:function(e){e.exports=t},944:function(t){t.exports=e}},r={};function i(e){var t=r[e];if(void 0!==t)return t.exports;var s=r[e]={exports:{}};return n[e](s,s.exports,i),s.exports}i.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return i.d(t,{a:t}),t},i.d=function(e,t){for(var n in t)i.o(t,n)&&!i.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},i.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)};var s={};i.d(s,{default:function(){return v}});var o=i(944),u=i.n(o),l=i(660),c=i.n(l),h=u().composed,a=u().addEvent,p=u().fireEvent,f=u().pushUnique;function g(){this.fullscreen=new d(this)}var d=function(){function e(e){this.chart=e,this.isOpen=!1;var t=e.renderTo;!this.browserProps&&("function"==typeof t.requestFullscreen?this.browserProps={fullscreenChange:"fullscreenchange",requestFullscreen:"requestFullscreen",exitFullscreen:"exitFullscreen"}:t.mozRequestFullScreen?this.browserProps={fullscreenChange:"mozfullscreenchange",requestFullscreen:"mozRequestFullScreen",exitFullscreen:"mozCancelFullScreen"}:t.webkitRequestFullScreen?this.browserProps={fullscreenChange:"webkitfullscreenchange",requestFullscreen:"webkitRequestFullScreen",exitFullscreen:"webkitExitFullscreen"}:t.msRequestFullscreen&&(this.browserProps={fullscreenChange:"MSFullscreenChange",requestFullscreen:"msRequestFullscreen",exitFullscreen:"msExitFullscreen"}))}return e.compose=function(e){f(h,"Fullscreen")&&a(e,"beforeRender",g)},e.prototype.close=function(){var e=this,t=e.chart,n=t.options.chart;p(t,"fullscreenClose",null,function(){e.isOpen&&e.browserProps&&t.container.ownerDocument instanceof Document&&t.container.ownerDocument[e.browserProps.exitFullscreen](),e.unbindFullscreenEvent&&(e.unbindFullscreenEvent=e.unbindFullscreenEvent()),t.setSize(e.origWidth,e.origHeight,!1),e.origWidth=void 0,e.origHeight=void 0,n.width=e.origWidthOption,n.height=e.origHeightOption,e.origWidthOption=void 0,e.origHeightOption=void 0,e.isOpen=!1,e.setButtonText()})},e.prototype.open=function(){var e=this,t=e.chart,n=t.options.chart;p(t,"fullscreenOpen",null,function(){if(n&&(e.origWidthOption=n.width,e.origHeightOption=n.height),e.origWidth=t.chartWidth,e.origHeight=t.chartHeight,e.browserProps){var r=a(t.container.ownerDocument,e.browserProps.fullscreenChange,function(){e.isOpen?(e.isOpen=!1,e.close()):(t.setSize(null,null,!1),e.isOpen=!0,e.setButtonText())}),i=a(t,"destroy",r);e.unbindFullscreenEvent=function(){r(),i()};var s=t.renderTo[e.browserProps.requestFullscreen]();s&&s.catch(function(){alert("Full screen is not supported inside a frame.")})}})},e.prototype.setButtonText=function(){var e=this.chart,t=e.exportDivElements,n=e.options.exporting,r=n&&n.buttons&&n.buttons.contextButton.menuItems,i=e.options.lang;if(n&&n.menuItemDefinitions&&i&&i.exitFullscreen&&i.viewFullscreen&&r&&t){var s=t[r.indexOf("viewFullscreen")];s&&c().setElementHTML(s,this.isOpen?i.exitFullscreen:n.menuItemDefinitions.viewFullscreen.text||i.viewFullscreen)}},e.prototype.toggle=function(){this.isOpen?this.close():this.open()},e}(),F=u();F.Fullscreen=F.Fullscreen||d,F.Fullscreen.compose(F.Chart);var v=u();return s.default}()});