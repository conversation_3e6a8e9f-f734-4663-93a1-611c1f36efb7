{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/variwide\n * @requires highcharts\n *\n * Highcharts variwide module\n *\n * (c) 2010-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/variwide\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"SeriesRegistry\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/variwide\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ variwide_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es5/es-modules/Series/Variwide/VariwideComposition.js\n/* *\n *\n *  Highcharts variwide module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar composed = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).composed;\n\nvar addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, pushUnique = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pushUnique, wrap = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).wrap;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction compose(AxisClass, TickClass) {\n    if (pushUnique(composed, 'Variwide')) {\n        var tickProto = TickClass.prototype;\n        addEvent(AxisClass, 'afterDrawCrosshair', onAxisAfterDrawCrosshair);\n        addEvent(AxisClass, 'afterRender', onAxisAfterRender);\n        addEvent(TickClass, 'afterGetPosition', onTickAfterGetPosition);\n        tickProto.postTranslate = tickPostTranslate;\n        wrap(tickProto, 'getLabelPosition', wrapTickGetLabelPosition);\n    }\n}\n/**\n * Same width as the category (#8083)\n * @private\n */\nfunction onAxisAfterDrawCrosshair(e) {\n    var _a;\n    if (this.variwide && this.cross) {\n        this.cross.attr('stroke-width', (_a = e.point) === null || _a === void 0 ? void 0 : _a.crosshairWidth);\n    }\n}\n/**\n * On a vertical axis, apply anti-collision logic to the labels.\n * @private\n */\nfunction onAxisAfterRender() {\n    var axis = this;\n    if (this.variwide) {\n        this.chart.labelCollectors.push(function () {\n            return axis.tickPositions\n                .filter(function (pos) { return !!axis.ticks[pos].label; })\n                .map(function (pos, i) {\n                var label = axis.ticks[pos].label;\n                label.labelrank = axis.zData[i];\n                return label;\n            });\n        });\n    }\n}\n/**\n * @private\n */\nfunction onTickAfterGetPosition(e) {\n    var axis = this.axis,\n        xOrY = axis.horiz ? 'x' : 'y';\n    if (axis.variwide) {\n        this[xOrY + 'Orig'] = e.pos[xOrY];\n        this.postTranslate(e.pos, xOrY, this.pos);\n    }\n}\n/**\n * @private\n */\nfunction tickPostTranslate(xy, xOrY, index) {\n    var axis = this.axis;\n    var pos = xy[xOrY] - axis.pos;\n    if (!axis.horiz) {\n        pos = axis.len - pos;\n    }\n    pos = axis.series[0].postTranslate(index, pos);\n    if (!axis.horiz) {\n        pos = axis.len - pos;\n    }\n    xy[xOrY] = axis.pos + pos;\n}\n/**\n * @private\n */\nfunction wrapTickGetLabelPosition(proceed, _x, _y, _label, horiz, \n/* eslint-disable @typescript-eslint/no-unused-vars */\n_labelOptions, _tickmarkOffset, _index\n/* eslint-enable @typescript-eslint/no-unused-vars */\n) {\n    var args = Array.prototype.slice.call(arguments, 1),\n        xOrY = horiz ? 'x' : 'y';\n    // Replace the x with the original x\n    if (this.axis.variwide &&\n        typeof this[xOrY + 'Orig'] === 'number') {\n        args[horiz ? 0 : 1] = this[xOrY + 'Orig'];\n    }\n    var xy = proceed.apply(this,\n        args);\n    // Post-translate\n    if (this.axis.variwide && this.axis.categories) {\n        this.postTranslate(xy, xOrY, this.pos);\n    }\n    return xy;\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar VariwideComposition = {\n    compose: compose\n};\n/* harmony default export */ var Variwide_VariwideComposition = (VariwideComposition);\n\n;// ./code/es5/es-modules/Series/Variwide/VariwidePoint.js\n/* *\n *\n *  Highcharts variwide module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar ColumnPoint = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.column.prototype.pointClass;\n\nvar isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber;\n/* *\n *\n *  Class\n *\n * */\nvar VariwidePoint = /** @class */ (function (_super) {\n    __extends(VariwidePoint, _super);\n    function VariwidePoint() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    VariwidePoint.prototype.isValid = function () {\n        return isNumber(this.y) && isNumber(this.z);\n    };\n    return VariwidePoint;\n}(ColumnPoint));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Variwide_VariwidePoint = (VariwidePoint);\n\n;// ./code/es5/es-modules/Series/Variwide/VariwideSeriesDefaults.js\n/* *\n *\n *  Highcharts variwide module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A variwide chart (related to marimekko chart) is a column chart with a\n * variable width expressing a third dimension.\n *\n * @sample {highcharts} highcharts/demo/variwide/\n *         Variwide chart\n * @sample {highcharts} highcharts/series-variwide/inverted/\n *         Inverted variwide chart\n * @sample {highcharts} highcharts/series-variwide/datetime/\n *         Variwide columns on a datetime axis\n *\n * @extends      plotOptions.column\n * @since        6.0.0\n * @product      highcharts\n * @excluding    boostThreshold, crisp, depth, edgeColor, edgeWidth,\n *               groupZPadding, boostBlending\n * @requires     modules/variwide\n * @optionparent plotOptions.variwide\n */\nvar VariwideSeriesDefaults = {\n    /**\n     * In a variwide chart, the point padding is 0 in order to express the\n     * horizontal stacking of items.\n     */\n    pointPadding: 0,\n    /**\n     * In a variwide chart, the group padding is 0 in order to express the\n     * horizontal stacking of items.\n     */\n    groupPadding: 0\n};\n/**\n * A `variwide` series. If the [type](#series.variwide.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.variwide\n * @excluding boostThreshold, boostBlending\n * @product   highcharts\n * @requires  modules/variwide\n * @apioption series.variwide\n */\n/**\n * An array of data points for the series. For the `variwide` series type,\n * points can be given in the following ways:\n *\n * 1. An array of arrays with 3 or 2 values. In this case, the values correspond\n *    to `x,y,z`. If the first value is a string, it is applied as the name of\n *    the point, and the `x` value is inferred. The `x` value can also be\n *    omitted, in which case the inner arrays should be of length 2. Then the\n *    `x` value is automatically calculated, either starting at 0 and\n *    incremented by 1, or from `pointStart` and `pointInterval` given in the\n *    series options.\n *    ```js\n *       data: [\n *           [0, 1, 2],\n *           [1, 5, 5],\n *           [2, 0, 2]\n *       ]\n *    ```\n *\n * 2. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.variwide.turboThreshold), this option is not\n *    available.\n *    ```js\n *       data: [{\n *           x: 1,\n *           y: 1,\n *           z: 1,\n *           name: \"Point2\",\n *           color: \"#00FF00\"\n *       }, {\n *           x: 1,\n *           y: 5,\n *           z: 4,\n *           name: \"Point1\",\n *           color: \"#FF00FF\"\n *       }]\n *    ```\n *\n * @sample {highcharts} highcharts/series/data-array-of-arrays/\n *         Arrays of numeric x and y\n * @sample {highcharts} highcharts/series/data-array-of-arrays-datetime/\n *         Arrays of datetime x and y\n * @sample {highcharts} highcharts/series/data-array-of-name-value/\n *         Arrays of point.name and y\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<Array<(number|string),number>|Array<(number|string),number,number>|*>}\n * @extends   series.line.data\n * @excluding marker\n * @product   highcharts\n * @apioption series.variwide.data\n */\n/**\n * The relative width for each column. On a category axis, the widths are\n * distributed so they sum up to the X axis length. On linear and datetime axes,\n * the columns will be laid out from the X value and Z units along the axis.\n *\n * @type      {number}\n * @product   highcharts\n * @apioption series.variwide.data.z\n */\n''; // Adds doclets above to transpiled file\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Variwide_VariwideSeriesDefaults = (VariwideSeriesDefaults);\n\n;// ./code/es5/es-modules/Series/Variwide/VariwideSeries.js\n/* *\n *\n *  Highcharts variwide module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar VariwideSeries_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar ColumnSeries = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.column;\n\n\n\n\nvar VariwideSeries_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, crisp = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).crisp, extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.variwide\n *\n * @augments Highcharts.Series\n */\nvar VariwideSeries = /** @class */ (function (_super) {\n    VariwideSeries_extends(VariwideSeries, _super);\n    function VariwideSeries() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     * Functions\n     *\n     * */\n    VariwideSeries.prototype.processData = function (force) {\n        this.totalZ = 0;\n        this.relZ = [];\n        highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().seriesTypes.column.prototype.processData.call(this, force);\n        var zData = this.getColumn('z');\n        (this.xAxis.reversed ?\n            zData.slice().reverse() :\n            zData).forEach(function (z, i) {\n            this.relZ[i] = this.totalZ;\n            this.totalZ += z;\n        }, this);\n        if (this.xAxis.categories) {\n            this.xAxis.variwide = true;\n            this.xAxis.zData = zData; // Used for label rank\n        }\n        return;\n    };\n    /**\n     * Translate an x value inside a given category index into the distorted\n     * axis translation.\n     *\n     * @private\n     * @function Highcharts.Series#postTranslate\n     *\n     * @param {number} index\n     *        The category index\n     *\n     * @param {number} x\n     *        The X pixel position in undistorted axis pixels\n     *\n     * @param {Highcharts.Point} point\n     *        For crosshairWidth for every point\n     *\n     * @return {number}\n     *         Distorted X position\n     */\n    VariwideSeries.prototype.postTranslate = function (index, x, point) {\n        var axis = this.xAxis, relZ = this.relZ, i = axis.reversed ? relZ.length - index : index, goRight = axis.reversed ? -1 : 1, minPx = axis.toPixels(axis.reversed ?\n                (axis.dataMax || 0) + axis.pointRange :\n                (axis.dataMin || 0)), maxPx = axis.toPixels(axis.reversed ?\n                (axis.dataMin || 0) :\n                (axis.dataMax || 0) + axis.pointRange), len = Math.abs(maxPx - minPx), totalZ = this.totalZ, left = this.chart.inverted ?\n                maxPx - (this.chart.plotTop - goRight * axis.minPixelPadding) :\n                minPx - this.chart.plotLeft - goRight * axis.minPixelPadding, linearSlotLeft = i / relZ.length * len, linearSlotRight = (i + goRight) / relZ.length * len, slotLeft = (pick(relZ[i], totalZ) / totalZ) * len, slotRight = (pick(relZ[i + goRight], totalZ) / totalZ) * len, xInsideLinearSlot = (x - (left + linearSlotLeft));\n        // Set crosshairWidth for every point (#8173)\n        if (point) {\n            point.crosshairWidth = slotRight - slotLeft;\n        }\n        return left + slotLeft +\n            xInsideLinearSlot * (slotRight - slotLeft) /\n                (linearSlotRight - linearSlotLeft);\n    };\n    /* eslint-enable valid-jsdoc */\n    VariwideSeries.prototype.translate = function () {\n        // Temporarily disable crisping when computing original shapeArgs\n        this.crispOption = this.options.crisp;\n        this.options.crisp = false;\n        _super.prototype.translate.call(this);\n        // Reset option\n        this.options.crisp = this.crispOption;\n    };\n    /**\n     * Function that corrects stack labels positions\n     * @private\n     */\n    VariwideSeries.prototype.correctStackLabels = function () {\n        var series = this,\n            options = series.options,\n            yAxis = series.yAxis;\n        var pointStack,\n            pointWidth,\n            stack,\n            xValue;\n        for (var _i = 0, _a = series.points; _i < _a.length; _i++) {\n            var point = _a[_i];\n            xValue = point.x;\n            pointWidth = point.shapeArgs.width;\n            stack = yAxis.stacking.stacks[(series.negStacks &&\n                point.y < (options.startFromThreshold ?\n                    0 :\n                    options.threshold) ?\n                '-' :\n                '') + series.stackKey];\n            if (stack) {\n                pointStack = stack[xValue];\n                if (pointStack && !point.isNull) {\n                    pointStack.setOffset(-(pointWidth / 2) || 0, pointWidth || 0, void 0, void 0, point.plotX, series.xAxis);\n                }\n            }\n        }\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    VariwideSeries.compose = Variwide_VariwideComposition.compose;\n    VariwideSeries.defaultOptions = merge(ColumnSeries.defaultOptions, Variwide_VariwideSeriesDefaults);\n    return VariwideSeries;\n}(ColumnSeries));\n// Extend translation by distorting X position based on Z.\nVariwideSeries_addEvent(VariwideSeries, 'afterColumnTranslate', function () {\n    // Temporarily disable crisping when computing original shapeArgs\n    var xAxis = this.xAxis,\n        inverted = this.chart.inverted;\n    var i = -1;\n    // Distort the points to reflect z dimension\n    for (var _i = 0, _a = this.points; _i < _a.length; _i++) {\n        var point = _a[_i];\n        ++i;\n        var shapeArgs = point.shapeArgs || {},\n            _b = shapeArgs.x,\n            x = _b === void 0 ? 0 : _b,\n            _c = shapeArgs.width,\n            width = _c === void 0 ? 0 : _c,\n            _d = point.plotX,\n            plotX = _d === void 0 ? 0 : _d,\n            tooltipPos = point.tooltipPos,\n            _e = point.z,\n            z = _e === void 0 ? 0 : _e;\n        var left = void 0,\n            right = void 0;\n        if (xAxis.variwide) {\n            left = this.postTranslate(i, x, point);\n            right = this.postTranslate(i, x + width);\n            // For linear or datetime axes, the variwide column should start with X\n            // and extend Z units, without modifying the axis.\n        }\n        else {\n            left = plotX;\n            right = xAxis.translate(point.x + z, false, false, false, true);\n        }\n        if (this.crispOption) {\n            left = crisp(left, this.borderWidth);\n            right = crisp(right, this.borderWidth);\n        }\n        shapeArgs.x = left;\n        shapeArgs.width = Math.max(right - left, 1);\n        // Crosshair position (#8083)\n        point.plotX = (left + right) / 2;\n        // Adjust the tooltip position\n        if (tooltipPos) {\n            if (!inverted) {\n                tooltipPos[0] = shapeArgs.x + shapeArgs.width / 2;\n            }\n            else {\n                tooltipPos[1] = xAxis.len - shapeArgs.x - shapeArgs.width / 2;\n            }\n        }\n    }\n    if (this.options.stacking) {\n        this.correctStackLabels();\n    }\n}, { order: 2 });\nextend(VariwideSeries.prototype, {\n    irregularWidths: true,\n    keysAffectYAxis: ['y'],\n    pointArrayMap: ['y', 'z'],\n    parallelArrays: ['x', 'y', 'z'],\n    pointClass: Variwide_VariwidePoint\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('variwide', VariwideSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Variwide_VariwideSeries = (VariwideSeries);\n\n;// ./code/es5/es-modules/masters/modules/variwide.js\n\n\n\n\nvar G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nVariwide_VariwideSeries.compose(G.Axis, G.Tick);\n/* harmony default export */ var variwide_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "extendStatics", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "variwide_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "composed", "addEvent", "pushUnique", "wrap", "onAxisAfterDrawCrosshair", "e", "_a", "variwide", "cross", "attr", "point", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onAxisAfterRender", "axis", "chart", "labelCollectors", "push", "tickPositions", "filter", "pos", "ticks", "label", "map", "i", "labelrank", "zData", "onTickAfterGetPosition", "xOrY", "horiz", "postTranslate", "tickPostTranslate", "xy", "index", "len", "series", "wrapTickGetLabelPosition", "proceed", "_x", "_y", "_label", "_labelOptions", "_tickmarkOffset", "_index", "args", "Array", "slice", "arguments", "apply", "categories", "AxisClass", "TickClass", "tick<PERSON>roto", "__extends", "b", "setPrototypeOf", "__proto__", "p", "__", "constructor", "create", "ColumnPoint", "seriesTypes", "column", "pointClass", "isNumber", "VariwidePoint", "_super", "<PERSON><PERSON><PERSON><PERSON>", "y", "z", "Variwide_VariwideSeriesDefaults", "pointPadding", "groupPadding", "VariwideSeries_extends", "TypeError", "String", "ColumnSeries", "VariwideSeries_addEvent", "crisp", "extend", "merge", "pick", "VariwideSeries", "processData", "force", "totalZ", "relZ", "getColumn", "xAxis", "reversed", "reverse", "for<PERSON>ach", "x", "length", "goRight", "minPx", "toPixels", "dataMax", "pointRange", "dataMin", "maxPx", "Math", "abs", "left", "inverted", "plotTop", "minPixelPadding", "plotLeft", "linearSlotLeft", "linearSlotRight", "slotLeft", "slotRight", "xInsideLinearSlot", "translate", "crispOption", "options", "correct<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pointStack", "pointWidth", "stack", "xValue", "yAxis", "_i", "points", "shapeArgs", "width", "stacking", "stacks", "negStacks", "startFromThreshold", "threshold", "<PERSON><PERSON><PERSON>", "isNull", "setOffset", "plotX", "compose", "defaultOptions", "_b", "_c", "_d", "tooltipPos", "_e", "right", "borderWidth", "max", "order", "irregularWidths", "keysAffectYAxis", "pointArrayMap", "parallelArrays", "registerSeriesType", "G", "Variwide_VariwideSeries", "Axis", "Tick"], "mappings": "CAWA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,EAChF,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,8BAA+B,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,iBAAiB,CAAC,CAAEJ,GACxG,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,8BAA8B,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,EAE/GJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,EACpF,OAAgB,AAAC,WACP,aACA,IA8NFC,EA+LAA,EA7ZMC,EAAuB,CAE/B,IACC,SAASP,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,SAASb,CAAM,EACtC,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,WAAa,OAAOf,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASjB,CAAO,CAAEmB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,CAAc,CAC7D,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAe7KE,EAAW,AAACH,IAA+EG,QAAQ,CAEnGC,EAAW,AAACJ,IAA+EI,QAAQ,CAAEC,EAAa,AAACL,IAA+EK,UAAU,CAAEC,EAAO,AAACN,IAA+EM,IAAI,CAuB7S,SAASC,EAAyBC,CAAC,EAC/B,IAAIC,CACA,CAAA,IAAI,CAACC,QAAQ,EAAI,IAAI,CAACC,KAAK,EAC3B,IAAI,CAACA,KAAK,CAACC,IAAI,CAAC,eAAgB,AAAmB,OAAlBH,CAAAA,EAAKD,EAAEK,KAAK,AAAD,GAAeJ,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGK,cAAc,CAE7G,CAKA,SAASC,IACL,IAAIC,EAAO,IAAI,AACX,CAAA,IAAI,CAACN,QAAQ,EACb,IAAI,CAACO,KAAK,CAACC,eAAe,CAACC,IAAI,CAAC,WAC5B,OAAOH,EAAKI,aAAa,CACpBC,MAAM,CAAC,SAAUC,CAAG,EAAI,MAAO,CAAC,CAACN,EAAKO,KAAK,CAACD,EAAI,CAACE,KAAK,AAAE,GACxDC,GAAG,CAAC,SAAUH,CAAG,CAAEI,CAAC,EACrB,IAAIF,EAAQR,EAAKO,KAAK,CAACD,EAAI,CAACE,KAAK,CAEjC,OADAA,EAAMG,SAAS,CAAGX,EAAKY,KAAK,CAACF,EAAE,CACxBF,CACX,EACJ,EAER,CAIA,SAASK,EAAuBrB,CAAC,EAC7B,IAAIQ,EAAO,IAAI,CAACA,IAAI,CAChBc,EAAOd,EAAKe,KAAK,CAAG,IAAM,GAC1Bf,CAAAA,EAAKN,QAAQ,GACb,IAAI,CAACoB,EAAO,OAAO,CAAGtB,EAAEc,GAAG,CAACQ,EAAK,CACjC,IAAI,CAACE,aAAa,CAACxB,EAAEc,GAAG,CAAEQ,EAAM,IAAI,CAACR,GAAG,EAEhD,CAIA,SAASW,EAAkBC,CAAE,CAAEJ,CAAI,CAAEK,CAAK,EACtC,IAAInB,EAAO,IAAI,CAACA,IAAI,CAChBM,EAAMY,CAAE,CAACJ,EAAK,CAAGd,EAAKM,GAAG,AACxBN,CAAAA,EAAKe,KAAK,EACXT,CAAAA,EAAMN,EAAKoB,GAAG,CAAGd,CAAE,EAEvBA,EAAMN,EAAKqB,MAAM,CAAC,EAAE,CAACL,aAAa,CAACG,EAAOb,GACrCN,EAAKe,KAAK,EACXT,CAAAA,EAAMN,EAAKoB,GAAG,CAAGd,CAAE,EAEvBY,CAAE,CAACJ,EAAK,CAAGd,EAAKM,GAAG,CAAGA,CAC1B,CAIA,SAASgB,EAAyBC,CAAO,CAAEC,CAAE,CAAEC,CAAE,CAAEC,CAAM,CAAEX,CAAK,CAEhEY,CAAa,CAAEC,CAAe,CAAEC,CAAM,EAGlC,IAAIC,EAAOC,MAAMrD,SAAS,CAACsD,KAAK,CAACpD,IAAI,CAACqD,UAAW,GAC7CnB,EAAOC,EAAQ,IAAM,GAErB,CAAA,IAAI,CAACf,IAAI,CAACN,QAAQ,EAClB,AAA+B,UAA/B,OAAO,IAAI,CAACoB,EAAO,OAAO,EAC1BgB,CAAAA,CAAI,CAACf,EAAAA,EAAc,CAAG,IAAI,CAACD,EAAO,OAAO,AAAD,EAE5C,IAAII,EAAKK,EAAQW,KAAK,CAAC,IAAI,CACvBJ,GAKJ,OAHI,IAAI,CAAC9B,IAAI,CAACN,QAAQ,EAAI,IAAI,CAACM,IAAI,CAACmC,UAAU,EAC1C,IAAI,CAACnB,aAAa,CAACE,EAAIJ,EAAM,IAAI,CAACR,GAAG,EAElCY,CACX,CAS6B,MA/F7B,SAAiBkB,CAAS,CAAEC,CAAS,EACjC,GAAIhD,EAAWF,EAAU,YAAa,CAClC,IAAImD,EAAYD,EAAU3D,SAAS,CACnCU,EAASgD,EAAW,qBAAsB7C,GAC1CH,EAASgD,EAAW,cAAerC,GACnCX,EAASiD,EAAW,mBAAoBxB,GACxCyB,EAAUtB,aAAa,CAAGC,EAC1B3B,EAAKgD,EAAW,mBAAoBhB,EACxC,CACJ,EAqGIiB,GACIlF,EAAgB,SAAUU,CAAC,CAC3ByE,CAAC,EAMD,MAAOnF,AALHA,CAAAA,EAAgBe,OAAOqE,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaX,OAAS,SAAUhE,CAAC,CAC1DyE,CAAC,EAAIzE,EAAE2E,SAAS,CAAGF,CAAG,GACd,SAAUzE,CAAC,CACnByE,CAAC,EAAI,IAAK,IAAIG,KAAKH,EAAOA,EAAE7D,cAAc,CAACgE,IAAI5E,CAAAA,CAAC,CAAC4E,EAAE,CAAGH,CAAC,CAACG,EAAE,AAAD,CAAG,CAAA,EACvC5E,EAAGyE,EAC5B,EACO,SAAUzE,CAAC,CAAEyE,CAAC,EAEjB,SAASI,IAAO,IAAI,CAACC,WAAW,CAAG9E,CAAG,CADtCV,EAAcU,EAAGyE,GAEjBzE,EAAEW,SAAS,CAAG8D,AAAM,OAANA,EAAapE,OAAO0E,MAAM,CAACN,GAAMI,CAAAA,EAAGlE,SAAS,CAAG8D,EAAE9D,SAAS,CAAE,IAAIkE,CAAG,CACtF,GAGAG,EAAc,AAAC7D,IAA2I8D,WAAW,CAACC,MAAM,CAACvE,SAAS,CAACwE,UAAU,CAEjMC,EAAW,AAACnE,IAA+EmE,QAAQ,CAMnGC,EAA+B,SAAUC,CAAM,EAE/C,SAASD,IACL,OAAOC,AAAW,OAAXA,GAAmBA,EAAOnB,KAAK,CAAC,IAAI,CAAED,YAAc,IAAI,AACnE,CASA,OAZAM,EAAUa,EAAeC,GASzBD,EAAc1E,SAAS,CAAC4E,OAAO,CAAG,WAC9B,OAAOH,EAAS,IAAI,CAACI,CAAC,GAAKJ,EAAS,IAAI,CAACK,CAAC,CAC9C,EACOJ,CACX,EAAEL,GAyI+BU,EA5FJ,CAKzBC,aAAc,EAKdC,aAAc,CAClB,EAgGIC,GACIvG,EAAgB,SAAUU,CAAC,CAC3ByE,CAAC,EAOD,MAAOnF,AANHA,CAAAA,EAAgBe,OAAOqE,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaX,OAAS,SAAUhE,CAAC,CAC1DyE,CAAC,EAAIzE,EAAE2E,SAAS,CAAGF,CAAG,GACd,SAAUzE,CAAC,CACnByE,CAAC,EAAI,IAAK,IAAIG,KAAKH,EAAOpE,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC4D,EAC/DG,IAAI5E,CAAAA,CAAC,CAAC4E,EAAE,CAAGH,CAAC,CAACG,EAAE,AAAD,CAAG,CAAA,EACI5E,EAAGyE,EAC5B,EACO,SAAUzE,CAAC,CAAEyE,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIqB,UAAU,uBAAyBC,OAAOtB,GAAK,iCAE7D,SAASI,IAAO,IAAI,CAACC,WAAW,CAAG9E,CAAG,CADtCV,EAAcU,EAAGyE,GAEjBzE,EAAEW,SAAS,CAAG8D,AAAM,OAANA,EAAapE,OAAO0E,MAAM,CAACN,GAAMI,CAAAA,EAAGlE,SAAS,CAAG8D,EAAE9D,SAAS,CAAE,IAAIkE,CAAG,CACtF,GAGAmB,EAAe,AAAC7E,IAA2I8D,WAAW,CAACC,MAAM,CAK7Ke,EAA0B,AAAChF,IAA+EI,QAAQ,CAAE6E,EAAQ,AAACjF,IAA+EiF,KAAK,CAAEC,EAAS,AAAClF,IAA+EkF,MAAM,CAAEC,EAAQ,AAACnF,IAA+EmF,KAAK,CAAEC,EAAO,AAACpF,IAA+EoF,IAAI,CAa9eC,EAAgC,SAAUhB,CAAM,EAEhD,SAASgB,IACL,OAAOhB,AAAW,OAAXA,GAAmBA,EAAOnB,KAAK,CAAC,IAAI,CAAED,YAAc,IAAI,AACnE,CAwGA,OA3GA2B,EAAuBS,EAAgBhB,GASvCgB,EAAe3F,SAAS,CAAC4F,WAAW,CAAG,SAAUC,CAAK,EAClD,IAAI,CAACC,MAAM,CAAG,EACd,IAAI,CAACC,IAAI,CAAG,EAAE,CACdvF,IAA0I8D,WAAW,CAACC,MAAM,CAACvE,SAAS,CAAC4F,WAAW,CAAC1F,IAAI,CAAC,IAAI,CAAE2F,GAC9L,IAAI3D,EAAQ,IAAI,CAAC8D,SAAS,CAAC,KAC3B,AAAC,CAAA,IAAI,CAACC,KAAK,CAACC,QAAQ,CAChBhE,EAAMoB,KAAK,GAAG6C,OAAO,GACrBjE,CAAI,EAAGkE,OAAO,CAAC,SAAUtB,CAAC,CAAE9C,CAAC,EAC7B,IAAI,CAAC+D,IAAI,CAAC/D,EAAE,CAAG,IAAI,CAAC8D,MAAM,CAC1B,IAAI,CAACA,MAAM,EAAIhB,CACnB,EAAG,IAAI,EACH,IAAI,CAACmB,KAAK,CAACxC,UAAU,GACrB,IAAI,CAACwC,KAAK,CAACjF,QAAQ,CAAG,CAAA,EACtB,IAAI,CAACiF,KAAK,CAAC/D,KAAK,CAAGA,EAG3B,EAoBAyD,EAAe3F,SAAS,CAACsC,aAAa,CAAG,SAAUG,CAAK,CAAE4D,CAAC,CAAElF,CAAK,EAC9D,IAAIG,EAAO,IAAI,CAAC2E,KAAK,CAAEF,EAAO,IAAI,CAACA,IAAI,CAAE/D,EAAIV,EAAK4E,QAAQ,CAAGH,EAAKO,MAAM,CAAG7D,EAAQA,EAAO8D,EAAUjF,EAAK4E,QAAQ,CAAG,GAAK,EAAGM,EAAQlF,EAAKmF,QAAQ,CAACnF,EAAK4E,QAAQ,CACvJ,AAAC5E,CAAAA,EAAKoF,OAAO,EAAI,CAAA,EAAKpF,EAAKqF,UAAU,CACpCrF,EAAKsF,OAAO,EAAI,GAAKC,EAAQvF,EAAKmF,QAAQ,CAACnF,EAAK4E,QAAQ,CACxD5E,EAAKsF,OAAO,EAAI,EACjB,AAACtF,CAAAA,EAAKoF,OAAO,EAAI,CAAA,EAAKpF,EAAKqF,UAAU,EAAGjE,EAAMoE,KAAKC,GAAG,CAACF,EAAQL,GAAQV,EAAS,IAAI,CAACA,MAAM,CAAEkB,EAAO,IAAI,CAACzF,KAAK,CAAC0F,QAAQ,CACvHJ,EAAS,CAAA,IAAI,CAACtF,KAAK,CAAC2F,OAAO,CAAGX,EAAUjF,EAAK6F,eAAe,AAAD,EAC3DX,EAAQ,IAAI,CAACjF,KAAK,CAAC6F,QAAQ,CAAGb,EAAUjF,EAAK6F,eAAe,CAAEE,EAAiBrF,EAAI+D,EAAKO,MAAM,CAAG5D,EAAK4E,EAAkB,AAACtF,CAAAA,EAAIuE,CAAM,EAAKR,EAAKO,MAAM,CAAG5D,EAAK6E,EAAW,AAAC7B,EAAKK,CAAI,CAAC/D,EAAE,CAAE8D,GAAUA,EAAUpD,EAAK8E,EAAY,AAAC9B,EAAKK,CAAI,CAAC/D,EAAIuE,EAAQ,CAAET,GAAUA,EAAUpD,EAK/Q,OAHIvB,GACAA,CAAAA,EAAMC,cAAc,CAAGoG,EAAYD,CAAO,EAEvCP,EAAOO,EACVE,AANqSpB,CAAAA,EAAKW,CAAAA,EAAOK,CAAa,CAAC,EAM1SG,CAAAA,EAAYD,CAAO,EACnCD,CAAAA,EAAkBD,CAAa,CAC5C,EAEA1B,EAAe3F,SAAS,CAAC0H,SAAS,CAAG,WAEjC,IAAI,CAACC,WAAW,CAAG,IAAI,CAACC,OAAO,CAACrC,KAAK,CACrC,IAAI,CAACqC,OAAO,CAACrC,KAAK,CAAG,CAAA,EACrBZ,EAAO3E,SAAS,CAAC0H,SAAS,CAACxH,IAAI,CAAC,IAAI,EAEpC,IAAI,CAAC0H,OAAO,CAACrC,KAAK,CAAG,IAAI,CAACoC,WAAW,AACzC,EAKAhC,EAAe3F,SAAS,CAAC6H,kBAAkB,CAAG,WAQ1C,IAAK,IAJDC,EACAC,EACAC,EACAC,EALAL,EAAUjF,AADD,IAAI,CACIiF,OAAO,CACxBM,EAAQvF,AAFC,IAAI,CAEEuF,KAAK,CAKfC,EAAK,EAAGpH,EAAK4B,AAPT,IAAI,CAOYyF,MAAM,CAAED,EAAKpH,EAAGuF,MAAM,CAAE6B,IAAM,CACvD,IAAIhH,EAAQJ,CAAE,CAACoH,EAAG,CAClBF,EAAS9G,EAAMkF,CAAC,CAChB0B,EAAa5G,EAAMkH,SAAS,CAACC,KAAK,CAClCN,CAAAA,EAAQE,EAAMK,QAAQ,CAACC,MAAM,CAAC,AAAC7F,CAAAA,AAXtB,IAAI,CAWyB8F,SAAS,EAC3CtH,EAAM0D,CAAC,CAAI+C,CAAAA,EAAQc,kBAAkB,CACjC,EACAd,EAAQe,SAAS,AAAD,EACpB,IACA,EAAC,EAAKhG,AAhBD,IAAI,CAgBIiG,QAAQ,CAAC,AAAD,GAGjBd,AADJA,CAAAA,EAAaE,CAAK,CAACC,EAAO,AAAD,GACP,CAAC9G,EAAM0H,MAAM,EAC3Bf,EAAWgB,SAAS,CAAC,CAAEf,CAAAA,EAAa,CAAA,GAAM,EAAGA,GAAc,EAAG,KAAK,EAAG,KAAK,EAAG5G,EAAM4H,KAAK,CAAEpG,AApB1F,IAAI,CAoB6FsD,KAAK,CAGnH,CACJ,EAMAN,EAAeqD,OAAO,GACtBrD,EAAesD,cAAc,CAAGxD,EAAMJ,EAAa4D,cAAc,CAAElE,GAC5DY,CACX,EAAEN,GAEFC,EAAwBK,EAAgB,uBAAwB,WAM5D,IAAK,IAJDM,EAAQ,IAAI,CAACA,KAAK,CAClBgB,EAAW,IAAI,CAAC1F,KAAK,CAAC0F,QAAQ,CAC9BjF,EAAI,GAECmG,EAAK,EAAGpH,EAAK,IAAI,CAACqH,MAAM,CAAED,EAAKpH,EAAGuF,MAAM,CAAE6B,IAAM,CACrD,IAAIhH,EAAQJ,CAAE,CAACoH,EAAG,AAClB,GAAEnG,EACF,IAAIqG,EAAYlH,EAAMkH,SAAS,EAAI,CAAC,EAChCa,EAAKb,EAAUhC,CAAC,CAChBA,EAAI6C,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EACxBC,EAAKd,EAAUC,KAAK,CACpBA,EAAQa,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EAC5BC,EAAKjI,EAAM4H,KAAK,CAChBA,EAAQK,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EAC5BC,EAAalI,EAAMkI,UAAU,CAC7BC,EAAKnI,EAAM2D,CAAC,CACZA,EAAIwE,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EACxBtC,EAAO,KAAK,EACZuC,EAAQ,KAAK,CACbtD,CAAAA,EAAMjF,QAAQ,EACdgG,EAAO,IAAI,CAAC1E,aAAa,CAACN,EAAGqE,EAAGlF,GAChCoI,EAAQ,IAAI,CAACjH,aAAa,CAACN,EAAGqE,EAAIiC,KAKlCtB,EAAO+B,EACPQ,EAAQtD,EAAMyB,SAAS,CAACvG,EAAMkF,CAAC,CAAGvB,EAAG,CAAA,EAAO,CAAA,EAAO,CAAA,EAAO,CAAA,IAE1D,IAAI,CAAC6C,WAAW,GAChBX,EAAOzB,EAAMyB,EAAM,IAAI,CAACwC,WAAW,EACnCD,EAAQhE,EAAMgE,EAAO,IAAI,CAACC,WAAW,GAEzCnB,EAAUhC,CAAC,CAAGW,EACdqB,EAAUC,KAAK,CAAGxB,KAAK2C,GAAG,CAACF,EAAQvC,EAAM,GAEzC7F,EAAM4H,KAAK,CAAG,AAAC/B,CAAAA,EAAOuC,CAAI,EAAK,EAE3BF,IACKpC,EAIDoC,CAAU,CAAC,EAAE,CAAGpD,EAAMvD,GAAG,CAAG2F,EAAUhC,CAAC,CAAGgC,EAAUC,KAAK,CAAG,EAH5De,CAAU,CAAC,EAAE,CAAGhB,EAAUhC,CAAC,CAAGgC,EAAUC,KAAK,CAAG,EAM5D,CACI,IAAI,CAACV,OAAO,CAACW,QAAQ,EACrB,IAAI,CAACV,kBAAkB,EAE/B,EAAG,CAAE6B,MAAO,CAAE,GACdlE,EAAOG,EAAe3F,SAAS,CAAE,CAC7B2J,gBAAiB,CAAA,EACjBC,gBAAiB,CAAC,IAAI,CACtBC,cAAe,CAAC,IAAK,IAAI,CACzBC,eAAgB,CAAC,IAAK,IAAK,IAAI,CAC/BtF,WAjWuDE,CAkW3D,GACAlE,IAA0IuJ,kBAAkB,CAAC,WAAYpE,GAazK,IAAIqE,EAAK1J,IACT2J,AAR4DtE,EAQpCqD,OAAO,CAACgB,EAAEE,IAAI,CAAEF,EAAEG,IAAI,EACjB,IAAI/J,EAAiBE,IAGxC,OADYH,EAAoB,OAAU,AAE3C,GAET"}