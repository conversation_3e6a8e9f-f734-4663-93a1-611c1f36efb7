!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("highcharts")):"function"==typeof define&&define.amd?define("highcharts/modules/textpath",[["highcharts/highcharts"]],e):"object"==typeof exports?exports["highcharts/modules/textpath"]=e(require("highcharts")):t.Highcharts=e(t.Highcharts)}(this,function(t){return function(){"use strict";var e={944:function(e){e.exports=t}},r={};function a(t){var n=r[t];if(void 0!==n)return n.exports;var o=r[t]={exports:{}};return e[t](o,o.exports,a),o.exports}a.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return a.d(e,{a:e}),e},a.d=function(t,e){for(var r in e)a.o(e,r)&&!a.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},a.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};var n={};a.d(n,{default:function(){return v}});var o=a(944),h=a.n(o),i=h().deg2rad,s=h().addEvent,d=h().merge,u=h().uniqueKey,c=h().defined,f=h().extend;function l(t,e){var r=this;e=d(!0,{enabled:!0,attributes:{dy:-5,startOffset:"50%",textAnchor:"middle"}},e);var a=this.renderer.url,n=this.text||this,o=n.textPath,h=e.attributes,i=e.enabled;if(t=t||o&&o.path,o&&o.undo(),t&&i){var l=s(n,"afterModifyTree",function(e){if(t&&i){var o=t.attr("id");o||t.attr("id",o=u());var s={x:0,y:0};c(h.dx)&&(s.dx=h.dx,delete h.dx),c(h.dy)&&(s.dy=h.dy,delete h.dy),n.attr(s),r.attr({transform:""}),r.box&&(r.box=r.box.destroy());var d=e.nodes.slice(0);e.nodes.length=0,e.nodes[0]={tagName:"textPath",attributes:f(h,{"text-anchor":h.textAnchor,href:""+a+"#".concat(o)}),children:d}}});n.textPath={path:t,undo:l}}else n.attr({dx:0,dy:0}),delete n.textPath;return this.added&&(n.textCache="",this.renderer.buildText(n)),this}function x(t){var e,r=t.bBox,a=null===(e=this.element)||void 0===e?void 0:e.querySelector("textPath");if(a){for(var n=[],o=this.renderer.fontMetrics(this.element),h=o.b,s=o.h-h,d=RegExp('(<tspan>|<tspan(?!\\sclass="highcharts-br")[^>]*>|<\\/tspan>)',"g"),u=a.innerHTML.replace(d,"").split(/<tspan class="highcharts-br"[^>]*>/),c=u.length,f=function(t,e){var r=e.x,n=e.y,o=(a.getRotationOfChar(t)-90)*i,d=Math.cos(o),u=Math.sin(o);return[[r-s*d,n-s*u],[r+h*d,n+h*u]]},l=0,x=0;x<c;x++){for(var p=u[x].length,b=0;b<p;b+=5)try{var v=l+b+x,g=f(v,a.getStartPositionOfChar(v)),y=g[0],P=g[1];0===b?(n.push(P),n.push(y)):(0===x&&n.unshift(P),x===c-1&&n.push(y))}catch(t){break}l+=p-1;try{var v=l+x,m=a.getEndPositionOfChar(v),T=f(v,m),y=T[0],P=T[1];n.unshift(P),n.unshift(y)}catch(t){break}}n.length&&n.push(n[0].slice()),r.polygon=n}return r}function p(t){var e,r=t.labelOptions,a=t.point,n=r[a.formatPrefix+"TextPath"]||r.textPath;n&&!r.useHTML&&(this.setTextPath((null===(e=a.getDataLabelPath)||void 0===e?void 0:e.call(a,this))||a.graphic,n),a.dataLabelPath&&!n.enabled&&(a.dataLabelPath=a.dataLabelPath.destroy()))}var b=h();b.TextPath={compose:function(t){s(t,"afterGetBBox",x),s(t,"beforeAddingDataLabel",p);var e=t.prototype;e.setTextPath||(e.setTextPath=l)}},b.TextPath.compose(b.SVGElement);var v=h();return n.default}()});