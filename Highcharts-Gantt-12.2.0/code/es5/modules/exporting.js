!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("highcharts"),require("highcharts").AST,require("highcharts").Chart):"function"==typeof define&&define.amd?define("highcharts/modules/exporting",[["highcharts/highcharts"],["highcharts/highcharts","AST"],["highcharts/highcharts","Chart"]],t):"object"==typeof exports?exports["highcharts/modules/exporting"]=t(require("highcharts"),require("highcharts").AST,require("highcharts").Chart):e.Highcharts=t(e.Highcharts,e.Highcharts.AST,e.Highcharts.Chart)}(this,function(e,t,n){return function(){"use strict";var i,o,r,s,a,l={660:function(e){e.exports=t},944:function(t){t.exports=e},960:function(e){e.exports=n}},c={};function u(e){var t=c[e];if(void 0!==t)return t.exports;var n=c[e]={exports:{}};return l[e](n,n.exports,u),n.exports}u.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return u.d(t,{a:t}),t},u.d=function(e,t){for(var n in t)u.o(t,n)&&!u.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:t[n]})},u.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)};var p={};u.d(p,{default:function(){return Q}});var h=u(944),d=u.n(h),f=u(660),g=u.n(f);u(960),(i=r||(r={})).compose=function(e){return e.navigation||(e.navigation=new o(e)),e},i.Additions=o=function(){function e(e){this.updates=[],this.chart=e}return e.prototype.addUpdate=function(e){this.chart.navigation.updates.push(e)},e.prototype.update=function(e,t){var n=this;this.updates.forEach(function(i){i.call(n.chart,e,t)})},e}();var m=r,v=d().isTouchDevice,x={exporting:{allowTableSorting:!0,type:"image/png",url:"https://export-svg.highcharts.com?v=".concat(d().version),pdfFont:{normal:void 0,bold:void 0,bolditalic:void 0,italic:void 0},printMaxWidth:780,scale:2,buttons:{contextButton:{className:"highcharts-contextbutton",menuClassName:"highcharts-contextmenu",symbol:"menu",titleKey:"contextButtonTitle",menuItems:["viewFullscreen","printChart","separator","downloadPNG","downloadJPEG","downloadSVG"]}},menuItemDefinitions:{viewFullscreen:{textKey:"viewFullscreen",onclick:function(){this.fullscreen&&this.fullscreen.toggle()}},printChart:{textKey:"printChart",onclick:function(){this.print()}},separator:{separator:!0},downloadPNG:{textKey:"downloadPNG",onclick:function(){this.exportChart()}},downloadJPEG:{textKey:"downloadJPEG",onclick:function(){this.exportChart({type:"image/jpeg"})}},downloadPDF:{textKey:"downloadPDF",onclick:function(){this.exportChart({type:"application/pdf"})}},downloadSVG:{textKey:"downloadSVG",onclick:function(){this.exportChart({type:"image/svg+xml"})}}}},lang:{viewFullscreen:"View in full screen",exitFullscreen:"Exit from full screen",printChart:"Print chart",downloadPNG:"Download PNG image",downloadJPEG:"Download JPEG image",downloadPDF:"Download PDF document",downloadSVG:"Download SVG vector image",contextButtonTitle:"Chart context menu"},navigation:{buttonOptions:{symbolSize:14,symbolX:14.5,symbolY:13.5,align:"right",buttonSpacing:5,height:28,y:-5,verticalAlign:"top",width:28,symbolFill:"#666666",symbolStroke:"#666666",symbolStrokeWidth:3,theme:{fill:"#ffffff",padding:5,stroke:"none","stroke-linecap":"round"}},menuStyle:{border:"none",borderRadius:"3px",background:"#ffffff",padding:"0.5em"},menuItemStyle:{background:"none",borderRadius:"3px",color:"#333333",padding:"0.5em",fontSize:v?"0.9em":"0.8em",transition:"background 250ms, color 250ms"},menuItemHoverStyle:{background:"#f2f2f2"}}};!function(e){var t=[];function n(e,t,n,i){return[["M",e,t+2.5],["L",e+n,t+2.5],["M",e,t+i/2+.5],["L",e+n,t+i/2+.5],["M",e,t+i-1.5],["L",e+n,t+i-1.5]]}function i(e,t,n,i){var o=i/3-2,r=[];return r.concat(this.circle(n-o,t,o,o),this.circle(n-o,t+o+4,o,o),this.circle(n-o,t+2*(o+4),o,o))}e.compose=function(e){if(-1===t.indexOf(e)){t.push(e);var o=e.prototype.symbols;o.menu=n,o.menuball=i.bind(o)}}}(s||(s={}));var y=s,b=d().composed,w=d().addEvent,S=d().fireEvent,E=d().pushUnique;function O(){this.fullscreen=new C(this)}var C=function(){function e(e){this.chart=e,this.isOpen=!1;var t=e.renderTo;!this.browserProps&&("function"==typeof t.requestFullscreen?this.browserProps={fullscreenChange:"fullscreenchange",requestFullscreen:"requestFullscreen",exitFullscreen:"exitFullscreen"}:t.mozRequestFullScreen?this.browserProps={fullscreenChange:"mozfullscreenchange",requestFullscreen:"mozRequestFullScreen",exitFullscreen:"mozCancelFullScreen"}:t.webkitRequestFullScreen?this.browserProps={fullscreenChange:"webkitfullscreenchange",requestFullscreen:"webkitRequestFullScreen",exitFullscreen:"webkitExitFullscreen"}:t.msRequestFullscreen&&(this.browserProps={fullscreenChange:"MSFullscreenChange",requestFullscreen:"msRequestFullscreen",exitFullscreen:"msExitFullscreen"}))}return e.compose=function(e){E(b,"Fullscreen")&&w(e,"beforeRender",O)},e.prototype.close=function(){var e=this,t=e.chart,n=t.options.chart;S(t,"fullscreenClose",null,function(){e.isOpen&&e.browserProps&&t.container.ownerDocument instanceof Document&&t.container.ownerDocument[e.browserProps.exitFullscreen](),e.unbindFullscreenEvent&&(e.unbindFullscreenEvent=e.unbindFullscreenEvent()),t.setSize(e.origWidth,e.origHeight,!1),e.origWidth=void 0,e.origHeight=void 0,n.width=e.origWidthOption,n.height=e.origHeightOption,e.origWidthOption=void 0,e.origHeightOption=void 0,e.isOpen=!1,e.setButtonText()})},e.prototype.open=function(){var e=this,t=e.chart,n=t.options.chart;S(t,"fullscreenOpen",null,function(){if(n&&(e.origWidthOption=n.width,e.origHeightOption=n.height),e.origWidth=t.chartWidth,e.origHeight=t.chartHeight,e.browserProps){var i=w(t.container.ownerDocument,e.browserProps.fullscreenChange,function(){e.isOpen?(e.isOpen=!1,e.close()):(t.setSize(null,null,!1),e.isOpen=!0,e.setButtonText())}),o=w(t,"destroy",i);e.unbindFullscreenEvent=function(){i(),o()};var r=t.renderTo[e.browserProps.requestFullscreen]();r&&r.catch(function(){alert("Full screen is not supported inside a frame.")})}})},e.prototype.setButtonText=function(){var e=this.chart,t=e.exportDivElements,n=e.options.exporting,i=n&&n.buttons&&n.buttons.contextButton.menuItems,o=e.options.lang;if(n&&n.menuItemDefinitions&&o&&o.exitFullscreen&&o.viewFullscreen&&i&&t){var r=t[i.indexOf("viewFullscreen")];r&&g().setElementHTML(r,this.isOpen?o.exitFullscreen:n.menuItemDefinitions.viewFullscreen.text||o.viewFullscreen)}},e.prototype.toggle=function(){this.isOpen?this.close():this.open()},e}(),T=function(){return(T=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},P=d().win,F=d().discardElement,k=d().objectEach,M={ajax:function(e){var t,n={json:"application/json",xml:"application/xml",text:"text/plain",octet:"application/octet-stream"},i=new XMLHttpRequest;function o(t,n){e.error&&e.error(t,n)}if(!e.url)return!1;i.open((e.type||"get").toUpperCase(),e.url,!0),(null===(t=e.headers)||void 0===t?void 0:t["Content-Type"])||i.setRequestHeader("Content-Type",n[e.dataType||"json"]||n.text),k(e.headers,function(e,t){i.setRequestHeader(t,e)}),e.responseType&&(i.responseType=e.responseType),i.onreadystatechange=function(){var t,n;if(4===i.readyState){if(200===i.status){if("blob"!==e.responseType&&(n=i.responseText,"json"===e.dataType))try{n=JSON.parse(n)}catch(e){if(e instanceof Error)return o(i,e)}return null===(t=e.success)||void 0===t?void 0:t.call(e,n,i)}o(i,i.responseText)}},e.data&&"string"!=typeof e.data&&(e.data=JSON.stringify(e.data)),i.send(e.data)},getJSON:function(e,t){M.ajax({url:e,success:t,dataType:"json",headers:{"Content-Type":"text/plain"}})},post:function(e,t,n){var i=new P.FormData;k(t,function(e,t){i.append(t,e)}),i.append("b64","true");var o=t.filename,r=t.type;return P.fetch(e,T({method:"POST",body:i},n)).then(function(e){e.ok&&e.text().then(function(e){var t=document.createElement("a");t.href="data:".concat(r,";base64,").concat(e),t.download=o,t.click(),F(t)})})}},N=function(){return(N=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)},H=d().defaultOptions,D=d().doc,G=d().SVG_NS,q=d().win,j=d().addEvent,I=d().css,W=d().createElement,A=d().discardElement,V=d().extend,R=d().find,L=d().fireEvent,z=d().isObject,K=d().merge,$=d().objectEach,B=d().pick,J=d().removeEvent,U=d().splat,_=d().uniqueKey;!function(e){var t,n=[/-/,/^(clipPath|cssText|d|height|width)$/,/^font$/,/[lL]ogical(Width|Height)$/,/^parentRule$/,/^(cssRules|ownerRules)$/,/perspective/,/TapHighlightColor/,/^transition/,/^length$/,/^\d+$/],i=["fill","stroke","strokeLinecap","strokeLinejoin","strokeWidth","textAnchor","x","y"];e.inlineAllowlist=[];var o=["clipPath","defs","desc"];function r(e){var t,n,i=this,o=i.renderer,r=K(i.options.navigation.buttonOptions,e),s=r.onclick,a=r.menuItems,l=r.symbolSize||12;if(i.btnCount||(i.btnCount=0),i.exportDivElements||(i.exportDivElements=[],i.exportSVGElements=[]),!1!==r.enabled&&r.theme){var c=i.styledMode?{}:r.theme;s?n=function(e){e&&e.stopPropagation(),s.call(i,e)}:a&&(n=function(e){e&&e.stopPropagation(),i.contextMenu(u.menuClassName,a,u.translateX||0,u.translateY||0,u.width||0,u.height||0,u),u.setState(2)}),r.text&&r.symbol?c.paddingLeft=B(c.paddingLeft,30):r.text||V(c,{width:r.width,height:r.height,padding:0});var u=o.button(r.text,0,0,n,c,void 0,void 0,void 0,void 0,r.useHTML).addClass(e.className).attr({title:B(i.options.lang[r._titleKey||r.titleKey],"")});u.menuClassName=e.menuClassName||"highcharts-menu-"+i.btnCount++,r.symbol&&(t=o.symbol(r.symbol,Math.round((r.symbolX||0)-l/2),Math.round((r.symbolY||0)-l/2),l,l,{width:l,height:l}).addClass("highcharts-button-symbol").attr({zIndex:1}).add(u),i.styledMode||t.attr({stroke:r.symbolStroke,fill:r.symbolFill,"stroke-width":r.symbolStrokeWidth||1})),u.add(i.exportingGroup).align(V(r,{width:u.width,x:B(r.x,i.buttonOffset)}),!0,"spacingBox"),i.buttonOffset+=((u.width||0)+r.buttonSpacing)*("right"===r.align?-1:1),i.exportSVGElements.push(u,t)}}function s(){if(this.printReverseInfo){var e=this.printReverseInfo,n=e.childNodes,i=e.origDisplay,o=e.resetParams;this.moveContainers(this.renderTo),[].forEach.call(n,function(e,t){1===e.nodeType&&(e.style.display=i[t]||"")}),this.isPrinting=!1,o&&this.setSize.apply(this,o),delete this.printReverseInfo,t=void 0,L(this,"afterPrint")}}function a(){var e,t=D.body,n=this.options.exporting.printMaxWidth,i={childNodes:t.childNodes,origDisplay:[],resetParams:void 0};this.isPrinting=!0,null===(e=this.pointer)||void 0===e||e.reset(void 0,0),L(this,"beforePrint"),n&&this.chartWidth>n&&(i.resetParams=[this.options.chart.width,void 0,!1],this.setSize(n,void 0,!1)),[].forEach.call(i.childNodes,function(e,t){1===e.nodeType&&(i.origDisplay[t]=e.style.display,e.style.display="none")}),this.moveContainers(t),this.printReverseInfo=i}function l(e){e.renderExporting(),j(e,"redraw",e.renderExporting),j(e,"destroy",e.destroyExport)}function c(e,t,n,i,o,r,s){var a,l,c,u=this,p=u.options.navigation,h=u.chartWidth,f=u.chartHeight,m="cache-"+e,v=Math.max(o,r),x=u[m];x||(u.exportContextMenu=u[m]=x=W("div",{className:e},N({position:"absolute",zIndex:1e3,padding:v+"px",pointerEvents:"auto"},u.renderer.style),(null===(a=u.scrollablePlotArea)||void 0===a?void 0:a.fixedDiv)||u.container),c=W("ul",{className:"highcharts-menu"},u.styledMode?{}:{listStyle:"none",margin:0,padding:0},x),u.styledMode||I(c,V({MozBoxShadow:"3px 3px 10px #888",WebkitBoxShadow:"3px 3px 10px #888",boxShadow:"3px 3px 10px #888"},p.menuStyle)),x.hideMenu=function(){I(x,{display:"none"}),s&&s.setState(0),u.openMenu=!1,I(u.renderTo,{overflow:"hidden"}),I(u.container,{overflow:"hidden"}),d().clearTimeout(x.hideTimer),L(u,"exportMenuHidden")},u.exportEvents.push(j(x,"mouseleave",function(){x.hideTimer=q.setTimeout(x.hideMenu,500)}),j(x,"mouseenter",function(){d().clearTimeout(x.hideTimer)}),j(D,"mouseup",function(t){var n;(null===(n=u.pointer)||void 0===n?void 0:n.inClass(t.target,e))||x.hideMenu()}),j(x,"click",function(){u.openMenu&&x.hideMenu()})),t.forEach(function(e){if("string"==typeof e&&(e=u.options.exporting.menuItemDefinitions[e]),z(e,!0)){var t=void 0;e.separator?t=W("hr",void 0,void 0,c):("viewData"===e.textKey&&u.isDataTableVisible&&(e.textKey="hideData"),t=W("li",{className:"highcharts-menu-item",onclick:function(t){t&&t.stopPropagation(),x.hideMenu(),"string"!=typeof e&&e.onclick&&e.onclick.apply(u,arguments)}},void 0,c),g().setElementHTML(t,e.text||u.options.lang[e.textKey]),u.styledMode||(t.onmouseover=function(){I(this,p.menuItemHoverStyle)},t.onmouseout=function(){I(this,p.menuItemStyle)},I(t,V({cursor:"pointer"},p.menuItemStyle||{})))),u.exportDivElements.push(t)}}),u.exportDivElements.push(c,x),u.exportMenuWidth=x.offsetWidth,u.exportMenuHeight=x.offsetHeight);var y={display:"block"};n+(u.exportMenuWidth||0)>h?y.right=h-n-o-v+"px":y.left=n-v+"px",i+r+(u.exportMenuHeight||0)>f&&(null===(l=s.alignOptions)||void 0===l?void 0:l.verticalAlign)!=="top"?y.bottom=f-i-v+"px":y.top=i+r-v+"px",I(x,y),I(u.renderTo,{overflow:""}),I(u.container,{overflow:""}),u.openMenu=!0,L(u,"exportMenuShown")}function u(e){var t,n=e?e.target:this,i=n.exportSVGElements,o=n.exportDivElements,r=n.exportEvents;i&&(i.forEach(function(e,o){e&&(e.onclick=e.ontouchstart=null,n[t="cache-"+e.menuClassName]&&delete n[t],i[o]=e.destroy())}),i.length=0),n.exportingGroup&&(n.exportingGroup.destroy(),delete n.exportingGroup),o&&(o.forEach(function(e,t){e&&(d().clearTimeout(e.hideTimer),J(e,"mouseleave"),o[t]=e.onmouseout=e.onmouseover=e.ontouchstart=e.onclick=null,A(e))}),o.length=0),r&&(r.forEach(function(e){e()}),r.length=0)}function p(e,t){var n=this.getSVGForExport(e,t);e=K(this.options.exporting,e),M.post(e.url,{filename:e.filename?e.filename.replace(/\//g,"-"):this.getFilename(),type:e.type,width:e.width,scale:e.scale,svg:n},e.fetchOptions)}function h(e){return e&&this.inlineStyles(),this.resolveCSSVariables(),this.container.innerHTML}function f(){var e=this.userOptions.title&&this.userOptions.title.text,t=this.options.exporting.filename;return t?t.replace(/\//g,"-"):("string"==typeof e&&(t=e.toLowerCase().replace(/<\/?[^>]+(>|$)/g,"").replace(/[\s_]+/g,"-").replace(/[^a-z\d\-]/g,"").replace(/^[\-]+/g,"").replace(/[\-]+/g,"-").substr(0,24).replace(/[\-]+$/g,"")),(!t||t.length<5)&&(t="chart"),t)}function v(e){var t,n,i,o=K(this.options,e);o.plotOptions=K(this.userOptions.plotOptions,e&&e.plotOptions),o.time=K(this.userOptions.time,e&&e.time);var r=W("div",null,{position:"absolute",top:"-9999em",width:this.chartWidth+"px",height:this.chartHeight+"px"},D.body),s=this.renderTo.style.width,a=this.renderTo.style.height,l=o.exporting.sourceWidth||o.chart.width||/px$/.test(s)&&parseInt(s,10)||(o.isGantt?800:600),c=o.exporting.sourceHeight||o.chart.height||/px$/.test(a)&&parseInt(a,10)||400;V(o.chart,{animation:!1,renderTo:r,forExport:!0,renderer:"SVGRenderer",width:l,height:c}),o.exporting.enabled=!1,delete o.data,o.series=[],this.series.forEach(function(e){(i=K(e.userOptions,{animation:!1,enableMouseTracking:!1,showCheckbox:!1,visible:e.visible})).isInternal||o.series.push(i)});var u={};this.axes.forEach(function(e){e.userOptions.internalKey||(e.userOptions.internalKey=_()),e.options.isInternal||(u[e.coll]||(u[e.coll]=!0,o[e.coll]=[]),o[e.coll].push(K(e.userOptions,{visible:e.visible,type:e.type,uniqueNames:e.uniqueNames})))}),o.colorAxis=this.userOptions.colorAxis;var p=new this.constructor(o,this.callback);return e&&["xAxis","yAxis","series"].forEach(function(t){var n={};e[t]&&(n[t]=e[t],p.update(n))}),this.axes.forEach(function(t){var n=R(p.axes,function(e){return e.options.internalKey===t.userOptions.internalKey});if(n){var i=t.getExtremes(),o=U((null==e?void 0:e[t.coll])||{})[0],r="min"in o?o.min:i.userMin,s="max"in o?o.max:i.userMax;(void 0!==r&&r!==n.min||void 0!==s&&s!==n.max)&&n.setExtremes(null!=r?r:void 0,null!=s?s:void 0,!0,!1)}}),n=p.getChartHTML(this.styledMode||(null===(t=o.exporting)||void 0===t?void 0:t.applyStyleSheets)),L(this,"getSVG",{chartCopy:p}),n=this.sanitizeSVG(n,o),o=null,p.destroy(),A(r),n}function b(e,t){var n=this.options.exporting;return this.getSVG(K({chart:{borderRadius:0}},n.chartOptions,t,{exporting:{sourceWidth:e&&e.sourceWidth||n.sourceWidth,sourceHeight:e&&e.sourceHeight||n.sourceHeight}}))}function w(){var t,r=e.inlineAllowlist,s={},a=D.createElement("iframe");I(a,{width:"1px",height:"1px",visibility:"hidden"}),D.body.appendChild(a);var l=a.contentWindow&&a.contentWindow.document;l&&l.body.appendChild(l.createElementNS(G,"svg")),!function e(a){var c,u,p,h,f,g,m={};if(l&&1===a.nodeType&&-1===o.indexOf(a.nodeName)){if(c=q.getComputedStyle(a,null),u="svg"===a.nodeName?{}:q.getComputedStyle(a.parentNode,null),!s[a.nodeName]){t=l.getElementsByTagName("svg")[0],p=l.createElementNS(a.namespaceURI,a.nodeName),t.appendChild(p);var v=q.getComputedStyle(p,null),x={};for(var y in v)y.length<1e3&&"string"==typeof v[y]&&!/^\d+$/.test(y)&&(x[y]=v[y]);s[a.nodeName]=x,"text"===a.nodeName&&delete s.text.fill,t.removeChild(p)}for(var b in c)(d().isFirefox||d().isMS||d().isSafari||Object.hasOwnProperty.call(c,b))&&function(e,t){if(h=f=!1,r.length){for(g=r.length;g--&&!f;)f=r[g].test(t);h=!f}for("transform"===t&&"none"===e&&(h=!0),g=n.length;g--&&!h;){if(t.length>1e3)throw Error("Input too long");h=n[g].test(t)||"function"==typeof e}!h&&(u[t]!==e||"svg"===a.nodeName)&&s[a.nodeName][t]!==e&&(i&&-1===i.indexOf(t)?m[t]=e:e&&a.setAttribute(t.replace(/[A-Z]/g,function(e){return"-"+e.toLowerCase()}),e))}(c[b],b);if(I(a,m),"svg"===a.nodeName&&a.setAttribute("stroke-width","1px"),"text"===a.nodeName)return;[].forEach.call(a.children||a.childNodes,e)}}(this.container.querySelector("svg")),t.parentNode.removeChild(t),a.parentNode.removeChild(a)}function S(){var e=this.container.querySelectorAll("*"),t=["color","fill","stop-color","stroke"];Array.from(e).forEach(function(e){t.forEach(function(t){var n=e.getAttribute(t);(null==n?void 0:n.includes("var("))&&e.setAttribute(t,getComputedStyle(e).getPropertyValue(t))})})}function E(e){var t=this.scrollablePlotArea;(t?[t.fixedDiv,t.scrollingContainer]:[this.container]).forEach(function(t){e.appendChild(t)})}function O(){var e=this,t=function(t,n,i){e.isDirtyExporting=!0,K(!0,e.options[t],n),B(i,!0)&&e.redraw()};e.exporting={update:function(e,n){t("exporting",e,n)}},m.compose(e).navigation.addUpdate(function(e,n){t("navigation",e,n)})}function T(e){var t,n,i,o,r=e.alignTo,s=e.key,a=e.textPxLength,l=this.options.exporting,c=K(null===(t=this.options.navigation)||void 0===t?void 0:t.buttonOptions,null===(n=null==l?void 0:l.buttons)||void 0===n?void 0:n.contextButton),u=c.align,p=c.buttonSpacing,h=c.verticalAlign,d=c.width,f=r.width-a,g=(void 0===d?0:d)+(void 0===p?0:p);(null===(i=null==l?void 0:l.enabled)||void 0===i||i)&&"title"===s&&"right"===u&&"top"===h&&f<2*g&&(f<g?r.width-=g:(null===(o=this.title)||void 0===o?void 0:o.alignValue)!=="left"&&(r.x-=g-f/2))}function P(){var e=this;!e.isPrinting&&(t=e,d().isSafari||e.beforePrint(),setTimeout(function(){q.focus(),q.print(),d().isSafari||setTimeout(function(){e.afterPrint()},1e3)},1))}function F(){var e=this,t=e.options.exporting,n=t.buttons,i=e.isDirtyExporting||!e.exportSVGElements;e.buttonOffset=0,e.isDirtyExporting&&e.destroyExport(),i&&!1!==t.enabled&&(e.exportEvents=[],e.exportingGroup=e.exportingGroup||e.renderer.g("exporting-group").attr({zIndex:3}).add(),$(n,function(t){e.addButton(t)}),e.isDirtyExporting=!1)}function k(e,t){var n,i=e.indexOf("</svg>")+6,o=e.indexOf("<foreignObject")>-1,r=e.substr(i);return e=e.substr(0,i),o?e=e.replace(/(<(?:img|br).*?(?=\>))>/g,"$1 />"):r&&(null===(n=null==t?void 0:t.exporting)||void 0===n?void 0:n.allowHTML)&&(r='<foreignObject x="0" y="0" width="'+t.chart.width+'" height="'+t.chart.height+'"><body xmlns="http://www.w3.org/1999/xhtml">'+r.replace(/(<(?:img|br).*?(?=\>))>/g,"$1 />")+"</body></foreignObject>",e=e.replace("</svg>",r+"</svg>")),e=e.replace(/zIndex="[^"]+"/g,"").replace(/symbolName="[^"]+"/g,"").replace(/jQuery\d+="[^"]+"/g,"").replace(/url\(("|&quot;)(.*?)("|&quot;)\;?\)/g,"url($2)").replace(/url\([^#]+#/g,"url(#").replace(/<svg /,'<svg xmlns:xlink="http://www.w3.org/1999/xlink" ').replace(/ (NS\d+\:)?href=/g," xlink:href=").replace(/\n+/g," ").replace(/&nbsp;/g,"\xa0").replace(/&shy;/g,"\xad")}e.compose=function(e,n){y.compose(n),C.compose(e);var i=e.prototype;i.exportChart||(i.afterPrint=s,i.exportChart=p,i.inlineStyles=w,i.print=P,i.sanitizeSVG=k,i.getChartHTML=h,i.getSVG=v,i.getSVGForExport=b,i.getFilename=f,i.moveContainers=E,i.beforePrint=a,i.contextMenu=c,i.addButton=r,i.destroyExport=u,i.renderExporting=F,i.resolveCSSVariables=S,i.callbacks.push(l),j(e,"init",O),j(e,"layOutTitle",T),d().isSafari&&q.matchMedia("print").addListener(function(e){t&&(e.matches?t.beforePrint():t.afterPrint())}),H.exporting=K(x.exporting,H.exporting),H.lang=K(x.lang,H.lang),H.navigation=K(x.navigation,H.navigation))}}(a||(a={}));var X=a,Y=d();Y.HttpUtilities=Y.HttpUtilities||M,Y.ajax=Y.HttpUtilities.ajax,Y.getJSON=Y.HttpUtilities.getJSON,Y.post=Y.HttpUtilities.post,X.compose(Y.Chart,Y.Renderer);var Q=d();return p.default}()});