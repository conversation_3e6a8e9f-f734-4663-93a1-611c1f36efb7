{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highmaps JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/tilemap\n * @requires highcharts\n * @requires highcharts/modules/map\n *\n * Tilemap module\n *\n * (c) 2010-2025 Highsoft AS\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"], require(\"highcharts\")[\"Color\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/tilemap\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"SeriesRegistry\"], [\"highcharts/highcharts\",\"Color\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/tilemap\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"], require(\"highcharts\")[\"Color\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"Color\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__620__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 620:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__620__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ tilemap_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Color\"],\"commonjs\":[\"highcharts\",\"Color\"],\"commonjs2\":[\"highcharts\",\"Color\"],\"root\":[\"Highcharts\",\"Color\"]}\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_ = __webpack_require__(620);\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default = /*#__PURE__*/__webpack_require__.n(highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_);\n;// ./code/es5/es-modules/Core/Axis/Color/ColorAxisComposition.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar color = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default()).parse;\n\nvar addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, splat = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).splat;\n/* *\n *\n *  Composition\n *\n * */\nvar ColorAxisComposition;\n(function (ColorAxisComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Variables\n     *\n     * */\n    var ColorAxisConstructor;\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    function compose(ColorAxisClass, ChartClass, FxClass, LegendClass, SeriesClass) {\n        var chartProto = ChartClass.prototype,\n            fxProto = FxClass.prototype,\n            seriesProto = SeriesClass.prototype;\n        if (!chartProto.collectionsWithUpdate.includes('colorAxis')) {\n            ColorAxisConstructor = ColorAxisClass;\n            chartProto.collectionsWithUpdate.push('colorAxis');\n            chartProto.collectionsWithInit.colorAxis = [\n                chartProto.addColorAxis\n            ];\n            addEvent(ChartClass, 'afterCreateAxes', onChartAfterCreateAxes);\n            wrapChartCreateAxis(ChartClass);\n            fxProto.fillSetter = wrapFxFillSetter;\n            fxProto.strokeSetter = wrapFxStrokeSetter;\n            addEvent(LegendClass, 'afterGetAllItems', onLegendAfterGetAllItems);\n            addEvent(LegendClass, 'afterColorizeItem', onLegendAfterColorizeItem);\n            addEvent(LegendClass, 'afterUpdate', onLegendAfterUpdate);\n            extend(seriesProto, {\n                optionalAxis: 'colorAxis',\n                translateColors: seriesTranslateColors\n            });\n            extend(seriesProto.pointClass.prototype, {\n                setVisible: pointSetVisible\n            });\n            addEvent(SeriesClass, 'afterTranslate', onSeriesAfterTranslate, { order: 1 });\n            addEvent(SeriesClass, 'bindAxes', onSeriesBindAxes);\n        }\n    }\n    ColorAxisComposition.compose = compose;\n    /**\n     * Extend the chart createAxes method to also make the color axis.\n     * @private\n     */\n    function onChartAfterCreateAxes() {\n        var _this = this;\n        var userOptions = this.userOptions;\n        this.colorAxis = [];\n        // If a `colorAxis` config is present in the user options (not in a\n        // theme), instanciate it.\n        if (userOptions.colorAxis) {\n            userOptions.colorAxis = splat(userOptions.colorAxis);\n            userOptions.colorAxis.map(function (axisOptions) { return (new ColorAxisConstructor(_this, axisOptions)); });\n        }\n    }\n    /**\n     * Add the color axis. This also removes the axis' own series to prevent\n     * them from showing up individually.\n     * @private\n     */\n    function onLegendAfterGetAllItems(e) {\n        var _this = this;\n        var colorAxes = this.chart.colorAxis || [],\n            destroyItem = function (item) {\n                var i = e.allItems.indexOf(item);\n            if (i !== -1) {\n                // #15436\n                _this.destroyItem(e.allItems[i]);\n                e.allItems.splice(i, 1);\n            }\n        };\n        var colorAxisItems = [],\n            options,\n            i;\n        colorAxes.forEach(function (colorAxis) {\n            options = colorAxis.options;\n            if (options === null || options === void 0 ? void 0 : options.showInLegend) {\n                // Data classes\n                if (options.dataClasses && options.visible) {\n                    colorAxisItems = colorAxisItems.concat(colorAxis.getDataClassLegendSymbols());\n                    // Gradient legend\n                }\n                else if (options.visible) {\n                    // Add this axis on top\n                    colorAxisItems.push(colorAxis);\n                }\n                // If dataClasses are defined or showInLegend option is not set\n                // to true, do not add color axis' series to legend.\n                colorAxis.series.forEach(function (series) {\n                    if (!series.options.showInLegend || options.dataClasses) {\n                        if (series.options.legendType === 'point') {\n                            series.points.forEach(function (point) {\n                                destroyItem(point);\n                            });\n                        }\n                        else {\n                            destroyItem(series);\n                        }\n                    }\n                });\n            }\n        });\n        i = colorAxisItems.length;\n        while (i--) {\n            e.allItems.unshift(colorAxisItems[i]);\n        }\n    }\n    /**\n     * @private\n     */\n    function onLegendAfterColorizeItem(e) {\n        if (e.visible && e.item.legendColor) {\n            e.item.legendItem.symbol.attr({\n                fill: e.item.legendColor\n            });\n        }\n    }\n    /**\n     * Updates in the legend need to be reflected in the color axis. (#6888)\n     * @private\n     */\n    function onLegendAfterUpdate(e) {\n        var _a;\n        (_a = this.chart.colorAxis) === null || _a === void 0 ? void 0 : _a.forEach(function (colorAxis) {\n            colorAxis.update({}, e.redraw);\n        });\n    }\n    /**\n     * Calculate and set colors for points.\n     * @private\n     */\n    function onSeriesAfterTranslate() {\n        var _a;\n        if (((_a = this.chart.colorAxis) === null || _a === void 0 ? void 0 : _a.length) ||\n            this.colorAttribs) {\n            this.translateColors();\n        }\n    }\n    /**\n     * Add colorAxis to series axisTypes.\n     * @private\n     */\n    function onSeriesBindAxes() {\n        var axisTypes = this.axisTypes;\n        if (!axisTypes) {\n            this.axisTypes = ['colorAxis'];\n        }\n        else if (axisTypes.indexOf('colorAxis') === -1) {\n            axisTypes.push('colorAxis');\n        }\n    }\n    /**\n     * Set the visibility of a single point\n     * @private\n     * @function Highcharts.colorPointMixin.setVisible\n     * @param {boolean} visible\n     */\n    function pointSetVisible(vis) {\n        var point = this,\n            method = vis ? 'show' : 'hide';\n        point.visible = point.options.visible = Boolean(vis);\n        // Show and hide associated elements\n        ['graphic', 'dataLabel'].forEach(function (key) {\n            if (point[key]) {\n                point[key][method]();\n            }\n        });\n        this.series.buildKDTree(); // Rebuild kdtree #13195\n    }\n    ColorAxisComposition.pointSetVisible = pointSetVisible;\n    /**\n     * In choropleth maps, the color is a result of the value, so this needs\n     * translation too\n     * @private\n     * @function Highcharts.colorSeriesMixin.translateColors\n     */\n    function seriesTranslateColors() {\n        var series = this,\n            points = this.getPointsCollection(), // #17945\n            nullColor = this.options.nullColor,\n            colorAxis = this.colorAxis,\n            colorKey = this.colorKey;\n        points.forEach(function (point) {\n            var value = point.getNestedProperty(colorKey),\n                color = point.options.color || (point.isNull || point.value === null ?\n                    nullColor :\n                    (colorAxis && typeof value !== 'undefined') ?\n                        colorAxis.toColor(value,\n                point) :\n                        point.color || series.color);\n            if (color && point.color !== color) {\n                point.color = color;\n                if (series.options.legendType === 'point' &&\n                    point.legendItem &&\n                    point.legendItem.label) {\n                    series.chart.legend.colorizeItem(point, point.visible);\n                }\n            }\n        });\n    }\n    /**\n     * @private\n     */\n    function wrapChartCreateAxis(ChartClass) {\n        var superCreateAxis = ChartClass.prototype.createAxis;\n        ChartClass.prototype.createAxis = function (type, options) {\n            var chart = this;\n            if (type !== 'colorAxis') {\n                return superCreateAxis.apply(chart, arguments);\n            }\n            var axis = new ColorAxisConstructor(chart,\n                merge(options.axis, {\n                    index: chart[type].length,\n                    isX: false\n                }));\n            chart.isDirtyLegend = true;\n            // Clear before 'bindAxes' (#11924)\n            chart.axes.forEach(function (axis) {\n                axis.series = [];\n            });\n            chart.series.forEach(function (series) {\n                series.bindAxes();\n                series.isDirtyData = true;\n            });\n            if (pick(options.redraw, true)) {\n                chart.redraw(options.animation);\n            }\n            return axis;\n        };\n    }\n    /**\n     * Handle animation of the color attributes directly.\n     * @private\n     */\n    function wrapFxFillSetter() {\n        this.elem.attr('fill', color(this.start).tweenTo(color(this.end), this.pos), void 0, true);\n    }\n    /**\n     * Handle animation of the color attributes directly.\n     * @private\n     */\n    function wrapFxStrokeSetter() {\n        this.elem.attr('stroke', color(this.start).tweenTo(color(this.end), this.pos), void 0, true);\n    }\n})(ColorAxisComposition || (ColorAxisComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Color_ColorAxisComposition = (ColorAxisComposition);\n\n;// ./code/es5/es-modules/Series/Tilemap/TilemapPoint.js\n/* *\n *\n *  Tilemaps module\n *\n *  (c) 2010-2025 Highsoft AS\n *  Author: Øystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\nvar Point = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).series.prototype.pointClass, HeatmapPoint = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.heatmap.prototype.pointClass;\n\nvar TilemapPoint_extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend;\n/* *\n *\n *  Class\n *\n * */\nvar TilemapPoint = /** @class */ (function (_super) {\n    __extends(TilemapPoint, _super);\n    function TilemapPoint() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     * @function Highcharts.Point#haloPath\n     */\n    TilemapPoint.prototype.haloPath = function () {\n        return this.series.tileShape.haloPath.apply(this, arguments);\n    };\n    return TilemapPoint;\n}(HeatmapPoint));\nTilemapPoint_extend(TilemapPoint.prototype, {\n    setState: Point.prototype.setState,\n    setVisible: Color_ColorAxisComposition.pointSetVisible\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Tilemap_TilemapPoint = (TilemapPoint);\n\n;// ./code/es5/es-modules/Series/Tilemap/TilemapSeriesDefaults.js\n/* *\n *\n *  Tilemaps module\n *\n *  (c) 2010-2025 Highsoft AS\n *  Author: Øystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A tilemap series is a type of heatmap where the tile shapes are\n * configurable.\n *\n * @sample highcharts/demo/honeycomb-usa/\n *         Honeycomb tilemap, USA\n * @sample maps/plotoptions/honeycomb-brazil/\n *         Honeycomb tilemap, Brazil\n * @sample maps/plotoptions/honeycomb-china/\n *         Honeycomb tilemap, China\n * @sample maps/plotoptions/honeycomb-europe/\n *         Honeycomb tilemap, Europe\n * @sample maps/demo/circlemap-africa/\n *         Circlemap tilemap, Africa\n * @sample maps/demo/diamondmap\n *         Diamondmap tilemap\n *\n * @extends      plotOptions.heatmap\n * @since        6.0.0\n * @excluding    jitter, joinBy, shadow, allAreas, mapData, marker, data,\n *               dataSorting, boostThreshold, boostBlending\n * @product      highcharts highmaps\n * @requires     modules/tilemap\n * @optionparent plotOptions.tilemap\n */\nvar TilemapSeriesDefaults = {\n    // Remove marker from tilemap default options, as it was before\n    // heatmap refactoring.\n    marker: null,\n    states: {\n        hover: {\n            halo: {\n                enabled: true,\n                size: 2,\n                opacity: 0.5,\n                attributes: {\n                    zIndex: 3\n                }\n            }\n        }\n    },\n    /**\n     * The padding between points in the tilemap.\n     *\n     * @sample maps/plotoptions/tilemap-pointpadding\n     *         Point padding on tiles\n     */\n    pointPadding: 2,\n    /**\n     * The column size - how many X axis units each column in the tilemap\n     * should span. Works as in [Heatmaps](#plotOptions.heatmap.colsize).\n     *\n     * @sample {highcharts} maps/demo/heatmap/\n     *         One day\n     * @sample {highmaps} maps/demo/heatmap/\n     *         One day\n     *\n     * @type      {number}\n     * @default   1\n     * @product   highcharts highmaps\n     * @apioption plotOptions.tilemap.colsize\n     */\n    /**\n     * The row size - how many Y axis units each tilemap row should span.\n     * Analogous to [colsize](#plotOptions.tilemap.colsize).\n     *\n     * @sample {highcharts} maps/demo/heatmap/\n     *         1 by default\n     * @sample {highmaps} maps/demo/heatmap/\n     *         1 by default\n     *\n     * @type      {number}\n     * @default   1\n     * @product   highcharts highmaps\n     * @apioption plotOptions.tilemap.rowsize\n     */\n    /**\n     * The shape of the tiles in the tilemap. Possible values are `hexagon`,\n     * `circle`, `diamond`, and `square`.\n     *\n     * @sample maps/demo/circlemap-africa\n     *         Circular tile shapes\n     * @sample maps/demo/diamondmap\n     *         Diamond tile shapes\n     *\n     * @type {Highcharts.TilemapShapeValue}\n     */\n    tileShape: 'hexagon'\n};\n/**\n * A `tilemap` series. If the [type](#series.tilemap.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.tilemap\n * @excluding allAreas, dataParser, dataURL, joinBy, mapData, marker,\n *            pointRange, shadow, stack, dataSorting, boostThreshold,\n *            boostBlending\n * @product   highcharts highmaps\n * @requires  modules/tilemap\n * @apioption series.tilemap\n */\n/**\n * An array of data points for the series. For the `tilemap` series\n * type, points can be given in the following ways:\n *\n * 1. An array of arrays with 3 or 2 values. In this case, the values correspond\n *    to `x,y,value`. If the first value is a string, it is applied as the name\n *    of the point, and the `x` value is inferred. The `x` value can also be\n *    omitted, in which case the inner arrays should be of length 2\\. Then the\n *    `x` value is automatically calculated, either starting at 0 and\n *    incremented by 1, or from `pointStart` and `pointInterval` given in the\n *    series options.\n *    ```js\n *    data: [\n *        [0, 9, 7],\n *        [1, 10, 4],\n *        [2, 6, 3]\n *    ]\n *    ```\n *\n * 2. An array of objects with named values. The objects are point configuration\n *    objects as seen below. If the total number of data points exceeds the\n *    series' [turboThreshold](#series.tilemap.turboThreshold), this option is\n *    not available.\n *    ```js\n *    data: [{\n *        x: 1,\n *        y: 3,\n *        value: 10,\n *        name: \"Point2\",\n *        color: \"#00FF00\"\n *    }, {\n *        x: 1,\n *        y: 7,\n *        value: 10,\n *        name: \"Point1\",\n *        color: \"#FF00FF\"\n *    }]\n *    ```\n *\n * Note that for some [tileShapes](#plotOptions.tilemap.tileShape) the grid\n * coordinates are offset.\n *\n * @sample maps/series/tilemap-gridoffset\n *         Offset grid coordinates\n * @sample {highcharts} highcharts/series/data-array-of-arrays/\n *         Arrays of numeric x and y\n * @sample {highcharts} highcharts/series/data-array-of-arrays-datetime/\n *         Arrays of datetime x and y\n * @sample {highcharts} highcharts/series/data-array-of-name-value/\n *         Arrays of point.name and y\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<Array<(number|string),number>|Array<(number|string),number,number>|*>}\n * @extends   series.heatmap.data\n * @excluding marker\n * @product   highcharts highmaps\n * @apioption series.tilemap.data\n */\n/**\n * The color of the point. In tilemaps the point color is rarely set\n * explicitly, as we use the color to denote the `value`. Options for\n * this are set in the [colorAxis](#colorAxis) configuration.\n *\n * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n * @product   highcharts highmaps\n * @apioption series.tilemap.data.color\n */\n/**\n * The x coordinate of the point.\n *\n * Note that for some [tileShapes](#plotOptions.tilemap.tileShape) the grid\n * coordinates are offset.\n *\n * @sample maps/series/tilemap-gridoffset\n *         Offset grid coordinates\n *\n * @type      {number}\n * @product   highcharts highmaps\n * @apioption series.tilemap.data.x\n */\n/**\n * The y coordinate of the point.\n *\n * Note that for some [tileShapes](#plotOptions.tilemap.tileShape) the grid\n * coordinates are offset.\n *\n * @sample maps/series/tilemap-gridoffset\n *         Offset grid coordinates\n *\n * @type      {number}\n * @product   highcharts highmaps\n * @apioption series.tilemap.data.y\n */\n''; // Keeps doclets above detached\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Tilemap_TilemapSeriesDefaults = (TilemapSeriesDefaults);\n\n;// ./code/es5/es-modules/Series/Tilemap/TilemapShapes.js\n/* *\n *\n *  Tilemaps module\n *\n *  (c) 2010-2025 Highsoft AS\n *  Author: Øystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar noop = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).noop;\n\nvar _a = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes, HeatmapSeries = _a.heatmap, ScatterSeries = _a.scatter;\n\nvar clamp = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).clamp, TilemapShapes_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Utility func to get padding definition from tile size division\n * @private\n */\nfunction tilePaddingFromTileSize(series, xDiv, yDiv) {\n    var options = series.options;\n    return {\n        xPad: (options.colsize || 1) / -xDiv,\n        yPad: (options.rowsize || 1) / -yDiv\n    };\n}\n/* *\n *\n *  Registry\n *\n * */\n/**\n * Map of shape types.\n * @private\n */\nvar TilemapShapes = {\n    // Hexagon shape type.\n    hexagon: {\n        alignDataLabel: ScatterSeries.prototype.alignDataLabel,\n        getSeriesPadding: function (series) {\n            return tilePaddingFromTileSize(series, 3, 2);\n        },\n        haloPath: function (size) {\n            if (!size) {\n                return [];\n            }\n            var hexagon = this.tileEdges;\n            return [\n                ['M', hexagon.x2 - size, hexagon.y1 + size],\n                ['L', hexagon.x3 + size, hexagon.y1 + size],\n                ['L', hexagon.x4 + size * 1.5, hexagon.y2],\n                ['L', hexagon.x3 + size, hexagon.y3 - size],\n                ['L', hexagon.x2 - size, hexagon.y3 - size],\n                ['L', hexagon.x1 - size * 1.5, hexagon.y2],\n                ['Z']\n            ];\n        },\n        translate: function () {\n            var _a;\n            var series = this, options = series.options, xAxis = series.xAxis, yAxis = series.yAxis, seriesPointPadding = options.pointPadding || 0, xPad = (options.colsize || 1) / 3, yPad = (options.rowsize || 1) / 2;\n            var yShift;\n            series.generatePoints();\n            for (var _i = 0, _b = series.points; _i < _b.length; _i++) {\n                var point = _b[_i];\n                var x1 = clamp(Math.floor(xAxis.len -\n                        xAxis.translate(point.x - xPad * 2, 0, 1, 0, 1)), -xAxis.len, 2 * xAxis.len),\n                    x2 = clamp(Math.floor(xAxis.len -\n                        xAxis.translate(point.x - xPad, 0, 1, 0, 1)), -xAxis.len, 2 * xAxis.len),\n                    x3 = clamp(Math.floor(xAxis.len -\n                        xAxis.translate(point.x + xPad, 0, 1, 0, 1)), -xAxis.len, 2 * xAxis.len),\n                    x4 = clamp(Math.floor(xAxis.len -\n                        xAxis.translate(point.x + xPad * 2, 0, 1, 0, 1)), -xAxis.len, 2 * xAxis.len),\n                    y1 = clamp(Math.floor(yAxis.translate(point.y - yPad, 0, 1, 0, 1)), -yAxis.len, 2 * yAxis.len),\n                    y2 = clamp(Math.floor(yAxis.translate(point.y, 0, 1, 0, 1)), -yAxis.len, 2 * yAxis.len),\n                    y3 = clamp(Math.floor(yAxis.translate(point.y + yPad, 0, 1, 0, 1)), -yAxis.len, 2 * yAxis.len);\n                var pointPadding = (_a = point.pointPadding) !== null && _a !== void 0 ? _a : seriesPointPadding, \n                    // We calculate the point padding of the midpoints to\n                    // preserve the angles of the shape.\n                    midPointPadding = pointPadding *\n                        Math.abs(x2 - x1) / Math.abs(y3 - y2),\n                    xMidPadding = xAxis.reversed ?\n                        -midPointPadding : midPointPadding,\n                    xPointPadding = xAxis.reversed ?\n                        -pointPadding : pointPadding,\n                    yPointPadding = yAxis.reversed ?\n                        -pointPadding : pointPadding;\n                // Shift y-values for every second grid column\n                if (point.x % 2) {\n                    yShift = yShift || Math.round(Math.abs(y3 - y1) / 2) *\n                        // We have to reverse the shift for reversed y-axes\n                        (yAxis.reversed ? -1 : 1);\n                    y1 += yShift;\n                    y2 += yShift;\n                    y3 += yShift;\n                }\n                // Set plotX and plotY for use in K-D-Tree and more\n                point.plotX = point.clientX = (x2 + x3) / 2;\n                point.plotY = y2;\n                // Apply point padding to translated coordinates\n                x1 += xMidPadding + xPointPadding;\n                x2 += xPointPadding;\n                x3 -= xPointPadding;\n                x4 -= xMidPadding + xPointPadding;\n                y1 -= yPointPadding;\n                y3 += yPointPadding;\n                // Store points for halo creation\n                point.tileEdges = {\n                    x1: x1, x2: x2, x3: x3, x4: x4, y1: y1, y2: y2, y3: y3\n                };\n                // Finally set the shape for this point\n                point.shapeType = 'path';\n                point.shapeArgs = {\n                    d: [\n                        ['M', x2, y1],\n                        ['L', x3, y1],\n                        ['L', x4, y2],\n                        ['L', x3, y3],\n                        ['L', x2, y3],\n                        ['L', x1, y2],\n                        ['Z']\n                    ]\n                };\n            }\n            series.translateColors();\n        }\n    },\n    // Diamond shape type.\n    diamond: {\n        alignDataLabel: ScatterSeries.prototype.alignDataLabel,\n        getSeriesPadding: function (series) {\n            return tilePaddingFromTileSize(series, 2, 2);\n        },\n        haloPath: function (size) {\n            if (!size) {\n                return [];\n            }\n            var diamond = this.tileEdges;\n            return [\n                ['M', diamond.x2, diamond.y1 + size],\n                ['L', diamond.x3 + size, diamond.y2],\n                ['L', diamond.x2, diamond.y3 - size],\n                ['L', diamond.x1 - size, diamond.y2],\n                ['Z']\n            ];\n        },\n        translate: function () {\n            var series = this,\n                options = series.options,\n                xAxis = series.xAxis,\n                yAxis = series.yAxis,\n                seriesPointPadding = options.pointPadding || 0,\n                xPad = (options.colsize || 1),\n                yPad = (options.rowsize || 1) / 2;\n            var yShift;\n            series.generatePoints();\n            for (var _i = 0, _a = series.points; _i < _a.length; _i++) {\n                var point = _a[_i];\n                var x1 = clamp(Math.round(xAxis.len -\n                        xAxis.translate(point.x - xPad, 0, 1, 0, 0)), -xAxis.len, 2 * xAxis.len),\n                    x3 = clamp(Math.round(xAxis.len -\n                        xAxis.translate(point.x + xPad, 0, 1, 0, 0)), -xAxis.len, 2 * xAxis.len),\n                    y1 = clamp(Math.round(yAxis.translate(point.y - yPad, 0, 1, 0, 0)), -yAxis.len, 2 * yAxis.len),\n                    y2 = clamp(Math.round(yAxis.translate(point.y, 0, 1, 0, 0)), -yAxis.len, 2 * yAxis.len),\n                    y3 = clamp(Math.round(yAxis.translate(point.y + yPad, 0, 1, 0, 0)), -yAxis.len, 2 * yAxis.len);\n                var x2 = clamp(Math.round(xAxis.len -\n                        xAxis.translate(point.x, 0, 1, 0, 0)), -xAxis.len, 2 * xAxis.len),\n                    pointPadding = TilemapShapes_pick(point.pointPadding,\n                    seriesPointPadding), \n                    // We calculate the point padding of the midpoints to\n                    // preserve the angles of the shape.\n                    midPointPadding = pointPadding *\n                        Math.abs(x2 - x1) / Math.abs(y3 - y2),\n                    xPointPadding = xAxis.reversed ?\n                        -midPointPadding : midPointPadding,\n                    yPointPadding = yAxis.reversed ?\n                        -pointPadding : pointPadding;\n                // Shift y-values for every second grid column\n                // We have to reverse the shift for reversed y-axes\n                if (point.x % 2) {\n                    yShift = Math.abs(y3 - y1) / 2 * (yAxis.reversed ? -1 : 1);\n                    y1 += yShift;\n                    y2 += yShift;\n                    y3 += yShift;\n                }\n                // Set plotX and plotY for use in K-D-Tree and more\n                point.plotX = point.clientX = x2;\n                point.plotY = y2;\n                // Apply point padding to translated coordinates\n                x1 += xPointPadding;\n                x3 -= xPointPadding;\n                y1 -= yPointPadding;\n                y3 += yPointPadding;\n                // Store points for halo creation\n                point.tileEdges = {\n                    x1: x1, x2: x2, x3: x3, y1: y1, y2: y2, y3: y3\n                };\n                // Set this point's shape parameters\n                point.shapeType = 'path';\n                point.shapeArgs = {\n                    d: [\n                        ['M', x2, y1],\n                        ['L', x3, y2],\n                        ['L', x2, y3],\n                        ['L', x1, y2],\n                        ['Z']\n                    ]\n                };\n            }\n            series.translateColors();\n        }\n    },\n    // Circle shape type.\n    circle: {\n        alignDataLabel: ScatterSeries.prototype.alignDataLabel,\n        getSeriesPadding: function (series) {\n            return tilePaddingFromTileSize(series, 2, 2);\n        },\n        haloPath: function (size) {\n            return ScatterSeries.prototype.pointClass.prototype.haloPath\n                .call(this, size + (size && this.radius));\n        },\n        translate: function () {\n            var series = this,\n                options = series.options,\n                xAxis = series.xAxis,\n                yAxis = series.yAxis,\n                seriesPointPadding = options.pointPadding || 0,\n                yRadius = (options.rowsize || 1) / 2,\n                colsize = (options.colsize || 1);\n            var colsizePx,\n                yRadiusPx,\n                xRadiusPx,\n                radius,\n                forceNextRadiusCompute = false;\n            series.generatePoints();\n            for (var _i = 0, _a = series.points; _i < _a.length; _i++) {\n                var point = _a[_i];\n                var x = clamp(Math.round(xAxis.len -\n                        xAxis.translate(point.x, 0, 1, 0, 0)), -xAxis.len, 2 * xAxis.len);\n                var pointPadding = seriesPointPadding,\n                    hasPerPointPadding = false,\n                    y = clamp(Math.round(yAxis.translate(point.y, 0, 1, 0, 0)), -yAxis.len, 2 * yAxis.len);\n                // If there is point padding defined on a single point, add it\n                if (typeof point.pointPadding !== 'undefined') {\n                    pointPadding = point.pointPadding;\n                    hasPerPointPadding = true;\n                    forceNextRadiusCompute = true;\n                }\n                // Find radius if not found already.\n                // Use the smallest one (x vs y) to avoid overlap.\n                // Note that the radius will be recomputed for each series.\n                // Ideal (max) x radius is dependent on y radius:\n                /*\n                                * (circle 2)\n\n                                        * (circle 3)\n                                        |    yRadiusPx\n                    (circle 1)    *-------|\n                                 colsizePx\n\n                    The distance between circle 1 and 3 (and circle 2 and 3) is\n                    2r, which is the hypotenuse of the triangle created by\n                    colsizePx and yRadiusPx. If the distance between circle 2\n                    and circle 1 is less than 2r, we use half of that distance\n                    instead (yRadiusPx).\n                */\n                if (!radius || forceNextRadiusCompute) {\n                    colsizePx = Math.abs(clamp(Math.floor(xAxis.len -\n                        xAxis.translate(point.x + colsize, 0, 1, 0, 0)), -xAxis.len, 2 * xAxis.len) - x);\n                    yRadiusPx = Math.abs(clamp(Math.floor(yAxis.translate(point.y + yRadius, 0, 1, 0, 0)), -yAxis.len, 2 * yAxis.len) - y);\n                    xRadiusPx = Math.floor(Math.sqrt((colsizePx * colsizePx + yRadiusPx * yRadiusPx)) / 2);\n                    radius = Math.min(colsizePx, xRadiusPx, yRadiusPx) - pointPadding;\n                    // If we have per point padding we need to always compute\n                    // the radius for this point and the next. If we used to\n                    // have per point padding but don't anymore, don't force\n                    // compute next radius.\n                    if (forceNextRadiusCompute && !hasPerPointPadding) {\n                        forceNextRadiusCompute = false;\n                    }\n                }\n                // Shift y-values for every second grid column.\n                // Note that we always use the optimal y axis radius for this.\n                // Also note: We have to reverse the shift for reversed y-axes.\n                if (point.x % 2) {\n                    y += yRadiusPx * (yAxis.reversed ? -1 : 1);\n                }\n                // Set plotX and plotY for use in K-D-Tree and more\n                point.plotX = point.clientX = x;\n                point.plotY = y;\n                // Save radius for halo\n                point.radius = radius;\n                // Set this point's shape parameters\n                point.shapeType = 'circle';\n                point.shapeArgs = {\n                    x: x,\n                    y: y,\n                    r: radius\n                };\n            }\n            series.translateColors();\n        }\n    },\n    // Square shape type.\n    square: {\n        alignDataLabel: HeatmapSeries.prototype.alignDataLabel,\n        translate: HeatmapSeries.prototype.translate,\n        getSeriesPadding: noop,\n        haloPath: HeatmapSeries.prototype.pointClass.prototype.haloPath\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Tilemap_TilemapShapes = (TilemapShapes);\n\n;// ./code/es5/es-modules/Series/Tilemap/TilemapSeries.js\n/* *\n *\n *  Tilemaps module\n *\n *  (c) 2010-2025 Highsoft AS\n *  Author: Øystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar TilemapSeries_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar composed = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).composed, TilemapSeries_noop = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).noop;\n\nvar TilemapSeries_a = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes, ColumnSeries = TilemapSeries_a.column, TilemapSeries_HeatmapSeries = TilemapSeries_a.heatmap, TilemapSeries_ScatterSeries = TilemapSeries_a.scatter;\n\n\n\n\nvar TilemapSeries_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, TilemapSeries_extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, TilemapSeries_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, pushUnique = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pushUnique;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Extension to add pixel padding for series. Uses getSeriesPixelPadding on each\n * series and adds the largest padding required. If no series has this function\n * defined, we add nothing.\n * @private\n */\nfunction onAxisAfterSetAxisTranslation() {\n    if (this.recomputingForTilemap || this.coll === 'colorAxis') {\n        return;\n    }\n    var axis = this, \n        // Find which series' padding to use\n        seriesPadding = axis.series\n            .map(function (series) {\n            return series.getSeriesPixelPadding &&\n                series.getSeriesPixelPadding(axis);\n    })\n        .reduce(function (a, b) {\n        return (a && a.padding) > (b && b.padding) ?\n            a :\n            b;\n    }, void 0) ||\n        {\n            padding: 0,\n            axisLengthFactor: 1\n        }, lengthPadding = Math.round(seriesPadding.padding * seriesPadding.axisLengthFactor);\n    // Don't waste time on this if we're not adding extra padding\n    if (seriesPadding.padding) {\n        // Recompute translation with new axis length now (minus padding)\n        axis.len -= lengthPadding;\n        axis.recomputingForTilemap = true;\n        axis.setAxisTranslation();\n        delete axis.recomputingForTilemap;\n        axis.minPixelPadding += seriesPadding.padding;\n        axis.len += lengthPadding;\n    }\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.tilemap\n *\n * @augments Highcharts.Series\n */\nvar TilemapSeries = /** @class */ (function (_super) {\n    TilemapSeries_extends(TilemapSeries, _super);\n    function TilemapSeries() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    TilemapSeries.compose = function (AxisClass) {\n        if (pushUnique(composed, 'TilemapSeries')) {\n            TilemapSeries_addEvent(AxisClass, 'afterSetAxisTranslation', onAxisAfterSetAxisTranslation);\n        }\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Use the shape's defined data label alignment function.\n     * @private\n     */\n    TilemapSeries.prototype.alignDataLabel = function () {\n        return this.tileShape.alignDataLabel.apply(this, arguments);\n    };\n    TilemapSeries.prototype.drawPoints = function () {\n        // In styled mode, use CSS, otherwise the fill used in the style\n        // sheet will take precedence over the fill attribute.\n        ColumnSeries.prototype.drawPoints.call(this);\n        for (var _i = 0, _a = this.points; _i < _a.length; _i++) {\n            var point = _a[_i];\n            if (point.graphic) {\n                point.graphic[this.chart.styledMode ? 'css' : 'animate'](this.colorAttribs(point));\n            }\n        }\n    };\n    /**\n     * Get metrics for padding of axis for this series.\n     * @private\n     */\n    TilemapSeries.prototype.getSeriesPixelPadding = function (axis) {\n        var isX = axis.isXAxis,\n            padding = this.tileShape.getSeriesPadding(this);\n        // If the shape type does not require padding, return no-op padding\n        if (!padding) {\n            return {\n                padding: 0,\n                axisLengthFactor: 1\n            };\n        }\n        // Use translate to compute how far outside the points we\n        // draw, and use this difference as padding.\n        var coord1 = Math.round(axis.translate(isX ?\n                padding.xPad * 2 :\n                padding.yPad, 0, 1, 0, 1));\n        var coord2 = Math.round(axis.translate(isX ? padding.xPad : 0, 0, 1, 0, 1));\n        return {\n            padding: (axis.single ? // If there is only one tick adjust padding #18647\n                Math.abs(coord1 - coord2) / 2 :\n                Math.abs(coord1 - coord2)) || 0,\n            // Offset the yAxis length to compensate for shift. Setting the\n            // length factor to 2 would add the same margin to max as min.\n            // Now we only add a slight bit of the min margin to max, as we\n            // don't actually draw outside the max bounds. For the xAxis we\n            // draw outside on both sides so we add the same margin to min\n            // and max.\n            axisLengthFactor: isX ? 2 : 1.1\n        };\n    };\n    /**\n     * Set tile shape object on series.\n     * @private\n     */\n    TilemapSeries.prototype.setOptions = function () {\n        // Call original function\n        var ret = _super.prototype.setOptions.apply(this,\n            arguments);\n        this.tileShape = Tilemap_TilemapShapes[ret.tileShape];\n        return ret;\n    };\n    /**\n     * Use translate from tileShape.\n     * @private\n     */\n    TilemapSeries.prototype.translate = function () {\n        return this.tileShape.translate.apply(this, arguments);\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    TilemapSeries.defaultOptions = TilemapSeries_merge(TilemapSeries_HeatmapSeries.defaultOptions, Tilemap_TilemapSeriesDefaults);\n    return TilemapSeries;\n}(TilemapSeries_HeatmapSeries));\nTilemapSeries_extend(TilemapSeries.prototype, {\n    // Revert the noop on getSymbol.\n    getSymbol: TilemapSeries_noop,\n    // Use drawPoints, markerAttribs, pointAttribs methods from the old\n    // heatmap implementation.\n    // TODO: Consider standardizing heatmap and tilemap into more\n    // consistent form.\n    markerAttribs: TilemapSeries_ScatterSeries.prototype.markerAttribs,\n    pointAttribs: ColumnSeries.prototype.pointAttribs,\n    pointClass: Tilemap_TilemapPoint\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('tilemap', TilemapSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Tilemap_TilemapSeries = (TilemapSeries);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * @typedef {\"circle\"|\"diamond\"|\"hexagon\"|\"square\"} Highcharts.TilemapShapeValue\n */\n''; // Keeps doclets above in JS file\n\n;// ./code/es5/es-modules/masters/modules/tilemap.js\n\n\n\n\nvar G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nTilemap_TilemapSeries.compose(G.Axis);\n/* harmony default export */ var tilemap_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__620__", "extendStatics", "ColorAxisComposition", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "tilemap_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_", "color", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default", "parse", "addEvent", "extend", "merge", "pick", "splat", "ColorAxisConstructor", "onChartAfterCreateAxes", "_this", "userOptions", "colorAxis", "map", "axisOptions", "onLegendAfterGetAllItems", "e", "options", "i", "colorAxes", "chart", "destroyItem", "item", "allItems", "indexOf", "splice", "colorAxisItems", "for<PERSON>ach", "showInLegend", "dataClasses", "visible", "concat", "getDataClassLegendSymbols", "push", "series", "legendType", "points", "point", "length", "unshift", "onLegendAfterColorizeItem", "legendColor", "legendItem", "symbol", "attr", "fill", "onLegendAfterUpdate", "_a", "update", "redraw", "onSeriesAfterTranslate", "colorAttribs", "translateColors", "onSeriesBindAxes", "axisTypes", "pointSetVisible", "vis", "method", "Boolean", "buildKDTree", "seriesTranslateColors", "getPointsCollection", "nullColor", "colorKey", "value", "getNestedProperty", "isNull", "toColor", "label", "legend", "colorizeItem", "wrapFxFillSetter", "elem", "start", "tweenTo", "end", "pos", "wrapFxStrokeSetter", "compose", "ColorAxisClass", "ChartClass", "FxClass", "LegendClass", "SeriesClass", "superCreateAxis", "chartProto", "fxProto", "seriesProto", "collectionsWithUpdate", "includes", "collectionsWithInit", "addColorAxis", "createAxis", "type", "apply", "arguments", "axis", "index", "isX", "isDirtyLegend", "axes", "bindAxes", "isDirtyData", "animation", "fillSetter", "strokeSetter", "optionalAxis", "pointClass", "setVisible", "order", "Color_ColorAxisComposition", "__extends", "b", "setPrototypeOf", "__proto__", "Array", "p", "__", "constructor", "create", "Point", "HeatmapPoint", "seriesTypes", "heatmap", "TilemapPoint_extend", "TilemapPoint", "_super", "haloPath", "tileShape", "setState", "Tilemap_TilemapSeriesDefaults", "marker", "states", "hover", "halo", "enabled", "size", "opacity", "attributes", "zIndex", "pointPadding", "noop", "HeatmapSeries", "ScatterSeries", "scatter", "clamp", "TilemapShapes_pick", "tilePaddingFromTileSize", "xDiv", "yDiv", "xPad", "colsize", "yPad", "rowsize", "TilemapShapes", "hexagon", "alignDataLabel", "getSeriesPadding", "tileEdges", "x2", "y1", "x3", "x4", "y2", "y3", "x1", "translate", "yShift", "xAxis", "yAxis", "seriesPointPadding", "generatePoints", "_i", "_b", "Math", "floor", "len", "x", "y", "midPointPadding", "abs", "xMidPadding", "reversed", "xPointPadding", "yPointPadding", "round", "plotX", "clientX", "plotY", "shapeType", "shapeArgs", "diamond", "circle", "radius", "colsizePx", "yRadiusPx", "xRadiusPx", "yRadius", "forceNextRadiusCompute", "hasPerPointPadding", "sqrt", "min", "r", "square", "TilemapSeries_extends", "TypeError", "String", "composed", "TilemapSeries_noop", "TilemapSeries_a", "ColumnSeries", "column", "TilemapSeries_HeatmapSeries", "TilemapSeries_ScatterSeries", "TilemapSeries_addEvent", "TilemapSeries_extend", "TilemapSeries_merge", "pushUnique", "onAxisAfterSetAxisTranslation", "recomputingForTilemap", "coll", "seriesPadding", "getSeriesPixelPadding", "reduce", "padding", "axisLengthFactor", "lengthPadding", "setAxisTranslation", "minPixelPadding", "TilemapSeries", "AxisClass", "drawPoints", "graphic", "styledMode", "isXAxis", "coord1", "coord2", "single", "setOptions", "ret", "Tilemap_TilemapShapes", "defaultOptions", "getSymbol", "markerAttribs", "pointAttribs", "registerSeriesType", "G", "Tilemap_TilemapSeries", "Axis"], "mappings": "CAYA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,CAAEA,QAAQ,cAAc,KAAQ,EAChH,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,6BAA8B,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,iBAAiB,CAAE,CAAC,wBAAwB,QAAQ,CAAC,CAAEJ,GAC1I,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,6BAA6B,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,CAAEA,QAAQ,cAAc,KAAQ,EAE9IJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,KAAQ,CACpH,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,EACtH,OAAgB,AAAC,WACP,aACA,IAyYFC,EA2mBAA,EAh4BJC,EApHUC,EAAuB,CAE/B,IACC,SAAST,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,SAASN,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGIM,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAad,OAAO,CAG5B,IAAIC,EAASU,CAAwB,CAACE,EAAS,CAAG,CAGjDb,QAAS,CAAC,CACX,EAMA,OAHAU,CAAmB,CAACG,EAAS,CAACZ,EAAQA,EAAOD,OAAO,CAAEY,GAG/CX,EAAOD,OAAO,AACtB,CAMCY,EAAoBI,CAAC,CAAG,SAASf,CAAM,EACtC,IAAIgB,EAAShB,GAAUA,EAAOiB,UAAU,CACvC,WAAa,OAAOjB,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAW,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASnB,CAAO,CAAEqB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACvB,EAASsB,IAC5EE,OAAOC,cAAc,CAACzB,EAASsB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,CAAa,CAC5D,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAE7KE,EAA+F3B,EAAoB,KAcnH4B,EAAQ,AAACC,AAb0G7B,EAAoBI,CAAC,CAACuB,KAazBG,KAAK,CAErHC,EAAW,AAACP,IAA+EO,QAAQ,CAAEC,EAAS,AAACR,IAA+EQ,MAAM,CAAEC,EAAQ,AAACT,IAA+ES,KAAK,CAAEC,EAAO,AAACV,IAA+EU,IAAI,CAAEC,EAAQ,AAACX,IAA+EW,KAAK,EAOne,AAAC,SAAUtC,CAAoB,EAW3B,IAAIuC,EA0CJ,SAASC,IACL,IAAIC,EAAQ,IAAI,CACZC,EAAc,IAAI,CAACA,WAAW,AAClC,CAAA,IAAI,CAACC,SAAS,CAAG,EAAE,CAGfD,EAAYC,SAAS,GACrBD,EAAYC,SAAS,CAAGL,EAAMI,EAAYC,SAAS,EACnDD,EAAYC,SAAS,CAACC,GAAG,CAAC,SAAUC,CAAW,EAAI,OAAQ,IAAIN,EAAqBE,EAAOI,EAAe,GAElH,CAMA,SAASC,EAAyBC,CAAC,EAC/B,IAWIC,EACAC,EAZAR,EAAQ,IAAI,CACZS,EAAY,IAAI,CAACC,KAAK,CAACR,SAAS,EAAI,EAAE,CACtCS,EAAc,SAAUC,CAAI,EACxB,IAAIJ,EAAIF,EAAEO,QAAQ,CAACC,OAAO,CAACF,EACrB,CAAA,KAANJ,IAEAR,EAAMW,WAAW,CAACL,EAAEO,QAAQ,CAACL,EAAE,EAC/BF,EAAEO,QAAQ,CAACE,MAAM,CAACP,EAAG,GAE7B,EACIQ,EAAiB,EAAE,CAgCvB,IA7BAP,EAAUQ,OAAO,CAAC,SAAUf,CAAS,EAE7BK,CAAAA,MADJA,CAAAA,EAAUL,EAAUK,OAAO,AAAD,EACmB,KAAK,EAAIA,EAAQW,YAAY,AAAD,IAEjEX,EAAQY,WAAW,EAAIZ,EAAQa,OAAO,CACtCJ,EAAiBA,EAAeK,MAAM,CAACnB,EAAUoB,yBAAyB,IAGrEf,EAAQa,OAAO,EAEpBJ,EAAeO,IAAI,CAACrB,GAIxBA,EAAUsB,MAAM,CAACP,OAAO,CAAC,SAAUO,CAAM,EACjC,CAAA,CAACA,EAAOjB,OAAO,CAACW,YAAY,EAAIX,EAAQY,WAAW,AAAD,IAC9CK,AAA8B,UAA9BA,EAAOjB,OAAO,CAACkB,UAAU,CACzBD,EAAOE,MAAM,CAACT,OAAO,CAAC,SAAUU,CAAK,EACjChB,EAAYgB,EAChB,GAGAhB,EAAYa,GAGxB,GAER,GACAhB,EAAIQ,EAAeY,MAAM,CAClBpB,KACHF,EAAEO,QAAQ,CAACgB,OAAO,CAACb,CAAc,CAACR,EAAE,CAE5C,CAIA,SAASsB,EAA0BxB,CAAC,EAC5BA,EAAEc,OAAO,EAAId,EAAEM,IAAI,CAACmB,WAAW,EAC/BzB,EAAEM,IAAI,CAACoB,UAAU,CAACC,MAAM,CAACC,IAAI,CAAC,CAC1BC,KAAM7B,EAAEM,IAAI,CAACmB,WAAW,AAC5B,EAER,CAKA,SAASK,EAAoB9B,CAAC,EAC1B,IAAI+B,CACJ,AAAgC,QAA/BA,CAAAA,EAAK,IAAI,CAAC3B,KAAK,CAACR,SAAS,AAAD,GAAemC,AAAO,KAAK,IAAZA,GAAyBA,EAAGpB,OAAO,CAAC,SAAUf,CAAS,EAC3FA,EAAUoC,MAAM,CAAC,CAAC,EAAGhC,EAAEiC,MAAM,CACjC,EACJ,CAKA,SAASC,IACL,IAAIH,EACA,CAAA,AAAC,CAAA,AAAgC,OAA/BA,CAAAA,EAAK,IAAI,CAAC3B,KAAK,CAACR,SAAS,AAAD,GAAemC,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGT,MAAM,AAAD,GAC1E,IAAI,CAACa,YAAY,AAAD,GAChB,IAAI,CAACC,eAAe,EAE5B,CAKA,SAASC,IACL,IAAIC,EAAY,IAAI,CAACA,SAAS,CACzBA,EAGuC,KAAnCA,EAAU9B,OAAO,CAAC,cACvB8B,EAAUrB,IAAI,CAAC,aAHf,IAAI,CAACqB,SAAS,CAAG,CAAC,YAAY,AAKtC,CAOA,SAASC,EAAgBC,CAAG,EACxB,IAAInB,EAAQ,IAAI,CACZoB,EAASD,EAAM,OAAS,MAC5BnB,CAAAA,EAAMP,OAAO,CAAGO,EAAMpB,OAAO,CAACa,OAAO,CAAG4B,CAAAA,CAAQF,EAEhD,CAAC,UAAW,YAAY,CAAC7B,OAAO,CAAC,SAAU7C,CAAG,EACtCuD,CAAK,CAACvD,EAAI,EACVuD,CAAK,CAACvD,EAAI,CAAC2E,EAAO,EAE1B,GACA,IAAI,CAACvB,MAAM,CAACyB,WAAW,EAC3B,CAQA,SAASC,IACL,IAAI1B,EAAS,IAAI,CACbE,EAAS,IAAI,CAACyB,mBAAmB,GACjCC,EAAY,IAAI,CAAC7C,OAAO,CAAC6C,SAAS,CAClClD,EAAY,IAAI,CAACA,SAAS,CAC1BmD,EAAW,IAAI,CAACA,QAAQ,CAC5B3B,EAAOT,OAAO,CAAC,SAAUU,CAAK,EAC1B,IAAI2B,EAAQ3B,EAAM4B,iBAAiB,CAACF,GAChC/D,EAAQqC,EAAMpB,OAAO,CAACjB,KAAK,EAAKqC,CAAAA,EAAM6B,MAAM,EAAI7B,AAAgB,OAAhBA,EAAM2B,KAAK,CACvDF,EACA,AAAClD,GAAa,AAAiB,KAAA,IAAVoD,EACjBpD,EAAUuD,OAAO,CAACH,EAC1B3B,GACQA,EAAMrC,KAAK,EAAIkC,EAAOlC,KAAK,AAAD,EAClCA,GAASqC,EAAMrC,KAAK,GAAKA,IACzBqC,EAAMrC,KAAK,CAAGA,EACoB,UAA9BkC,EAAOjB,OAAO,CAACkB,UAAU,EACzBE,EAAMK,UAAU,EAChBL,EAAMK,UAAU,CAAC0B,KAAK,EACtBlC,EAAOd,KAAK,CAACiD,MAAM,CAACC,YAAY,CAACjC,EAAOA,EAAMP,OAAO,EAGjE,EACJ,CAmCA,SAASyC,IACL,IAAI,CAACC,IAAI,CAAC5B,IAAI,CAAC,OAAQ5C,EAAM,IAAI,CAACyE,KAAK,EAAEC,OAAO,CAAC1E,EAAM,IAAI,CAAC2E,GAAG,EAAG,IAAI,CAACC,GAAG,EAAG,KAAK,EAAG,CAAA,EACzF,CAKA,SAASC,IACL,IAAI,CAACL,IAAI,CAAC5B,IAAI,CAAC,SAAU5C,EAAM,IAAI,CAACyE,KAAK,EAAEC,OAAO,CAAC1E,EAAM,IAAI,CAAC2E,GAAG,EAAG,IAAI,CAACC,GAAG,EAAG,KAAK,EAAG,CAAA,EAC3F,CA5MA3G,EAAqB6G,OAAO,CA5B5B,SAAiBC,CAAc,CAAEC,CAAU,CAAEC,CAAO,CAAEC,CAAW,CAAEC,CAAW,EAC1E,IA+LyBH,EACrBI,EAhMAC,EAAaL,EAAW1F,SAAS,CACjCgG,EAAUL,EAAQ3F,SAAS,CAC3BiG,EAAcJ,EAAY7F,SAAS,CAClC+F,EAAWG,qBAAqB,CAACC,QAAQ,CAAC,eAC3CjF,EAAuBuE,EACvBM,EAAWG,qBAAqB,CAACvD,IAAI,CAAC,aACtCoD,EAAWK,mBAAmB,CAAC9E,SAAS,CAAG,CACvCyE,EAAWM,YAAY,CAC1B,CACDxF,EAAS6E,EAAY,kBAAmBvE,GAuLxC2E,EAAkBJ,CADGA,EArLDA,GAsLS1F,SAAS,CAACsG,UAAU,CACrDZ,EAAW1F,SAAS,CAACsG,UAAU,CAAG,SAAUC,CAAI,CAAE5E,CAAO,EAErD,GAAI4E,AAAS,cAATA,EACA,OAAOT,EAAgBU,KAAK,CAFpB,IAAI,CAEwBC,WAExC,IAAIC,EAAO,IAAIxF,EAJH,IAAI,CAKZH,EAAMY,EAAQ+E,IAAI,CAAE,CAChBC,MAAO7E,AANH,IAAI,AAMI,CAACyE,EAAK,CAACvD,MAAM,CACzB4D,IAAK,CAAA,CACT,IAaJ,OAZA9E,AATY,IAAI,CASV+E,aAAa,CAAG,CAAA,EAEtB/E,AAXY,IAAI,CAWVgF,IAAI,CAACzE,OAAO,CAAC,SAAUqE,CAAI,EAC7BA,EAAK9D,MAAM,CAAG,EAAE,AACpB,GACAd,AAdY,IAAI,CAcVc,MAAM,CAACP,OAAO,CAAC,SAAUO,CAAM,EACjCA,EAAOmE,QAAQ,GACfnE,EAAOoE,WAAW,CAAG,CAAA,CACzB,GACIhG,EAAKW,EAAQgC,MAAM,CAAE,CAAA,IACrB7B,AAnBQ,IAAI,CAmBN6B,MAAM,CAAChC,EAAQsF,SAAS,EAE3BP,CACX,EA7MIV,EAAQkB,UAAU,CAAGjC,EACrBe,EAAQmB,YAAY,CAAG5B,EACvB1E,EAAS+E,EAAa,mBAAoBnE,GAC1CZ,EAAS+E,EAAa,oBAAqB1C,GAC3CrC,EAAS+E,EAAa,cAAepC,GACrC1C,EAAOmF,EAAa,CAChBmB,aAAc,YACdtD,gBAAiBQ,CACrB,GACAxD,EAAOmF,EAAYoB,UAAU,CAACrH,SAAS,CAAE,CACrCsH,WAAYrD,CAChB,GACApD,EAASgF,EAAa,iBAAkBjC,EAAwB,CAAE2D,MAAO,CAAE,GAC3E1G,EAASgF,EAAa,WAAY9B,GAE1C,EAmIApF,EAAqBsF,eAAe,CAAGA,CA2E3C,EAAGtF,GAAyBA,CAAAA,EAAuB,CAAC,CAAA,GAMvB,IAAI6I,EAA8B7I,EAgB3D8I,GACI/I,EAAgB,SAAUW,CAAC,CAC3BqI,CAAC,EAMD,MAAOhJ,AALHA,CAAAA,EAAgBgB,OAAOiI,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAUxI,CAAC,CAC1DqI,CAAC,EAAIrI,EAAEuI,SAAS,CAAGF,CAAG,GACd,SAAUrI,CAAC,CACnBqI,CAAC,EAAI,IAAK,IAAII,KAAKJ,EAAOA,EAAEzH,cAAc,CAAC6H,IAAIzI,CAAAA,CAAC,CAACyI,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,CAAG,CAAA,EACvCzI,EAAGqI,EAC5B,EACO,SAAUrI,CAAC,CAAEqI,CAAC,EAEjB,SAASK,IAAO,IAAI,CAACC,WAAW,CAAG3I,CAAG,CADtCX,EAAcW,EAAGqI,GAEjBrI,EAAEW,SAAS,CAAG0H,AAAM,OAANA,EAAahI,OAAOuI,MAAM,CAACP,GAAMK,CAAAA,EAAG/H,SAAS,CAAG0H,EAAE1H,SAAS,CAAE,IAAI+H,CAAG,CACtF,GAIAG,EAAQ,AAAC1H,IAA2IoC,MAAM,CAAC5C,SAAS,CAACqH,UAAU,CAAEc,EAAe,AAAC3H,IAA2I4H,WAAW,CAACC,OAAO,CAACrI,SAAS,CAACqH,UAAU,CAEpXiB,EAAsB,AAAChI,IAA+EQ,MAAM,CAM5GyH,EAA8B,SAAUC,CAAM,EAE9C,SAASD,IACL,OAAOC,AAAW,OAAXA,GAAmBA,EAAOhC,KAAK,CAAC,IAAI,CAAEC,YAAc,IAAI,AACnE,CAaA,OAhBAgB,EAAUc,EAAcC,GAaxBD,EAAavI,SAAS,CAACyI,QAAQ,CAAG,WAC9B,OAAO,IAAI,CAAC7F,MAAM,CAAC8F,SAAS,CAACD,QAAQ,CAACjC,KAAK,CAAC,IAAI,CAAEC,UACtD,EACO8B,CACX,EAAEJ,GACFG,EAAoBC,EAAavI,SAAS,CAAE,CACxC2I,SAAUT,EAAMlI,SAAS,CAAC2I,QAAQ,CAClCrB,WAAYE,EAA2BvD,eAAe,AAC1D,GAoO6B,IAAI2E,EAhLL,CAGxBC,OAAQ,KACRC,OAAQ,CACJC,MAAO,CACHC,KAAM,CACFC,QAAS,CAAA,EACTC,KAAM,EACNC,QAAS,GACTC,WAAY,CACRC,OAAQ,CACZ,CACJ,CACJ,CACJ,EAOAC,aAAc,EAwCdZ,UAAW,SACf,EAkIIa,EAAO,AAACjJ,IAA+EiJ,IAAI,CAE3F9F,EAAK,AAACjD,IAA2I4H,WAAW,CAAEoB,EAAgB/F,EAAG4E,OAAO,CAAEoB,EAAgBhG,EAAGiG,OAAO,CAEpNC,EAAQ,AAACrJ,IAA+EqJ,KAAK,CAAEC,EAAqB,AAACtJ,IAA+EU,IAAI,CAU5M,SAAS6I,EAAwBjH,CAAM,CAAEkH,CAAI,CAAEC,CAAI,EAC/C,IAAIpI,EAAUiB,EAAOjB,OAAO,CAC5B,MAAO,CACHqI,KAAM,CAAA,CAAA,AAACrI,CAAAA,EAAQsI,OAAO,EAAI,CAAA,EAAMH,CAAG,EACnCI,KAAM,CAAA,CAAA,AAACvI,CAAAA,EAAQwI,OAAO,EAAI,CAAA,EAAMJ,CAAG,CACvC,CACJ,CAUA,IAAIK,EAAgB,CAEhBC,QAAS,CACLC,eAAgBb,EAAczJ,SAAS,CAACsK,cAAc,CACtDC,iBAAkB,SAAU3H,CAAM,EAC9B,OAAOiH,EAAwBjH,EAAQ,EAAG,EAC9C,EACA6F,SAAU,SAAUS,CAAI,EACpB,GAAI,CAACA,EACD,MAAO,EAAE,CAEb,IAAImB,EAAU,IAAI,CAACG,SAAS,CAC5B,MAAO,CACH,CAAC,IAAKH,EAAQI,EAAE,CAAGvB,EAAMmB,EAAQK,EAAE,CAAGxB,EAAK,CAC3C,CAAC,IAAKmB,EAAQM,EAAE,CAAGzB,EAAMmB,EAAQK,EAAE,CAAGxB,EAAK,CAC3C,CAAC,IAAKmB,EAAQO,EAAE,CAAG1B,AAAO,IAAPA,EAAYmB,EAAQQ,EAAE,CAAC,CAC1C,CAAC,IAAKR,EAAQM,EAAE,CAAGzB,EAAMmB,EAAQS,EAAE,CAAG5B,EAAK,CAC3C,CAAC,IAAKmB,EAAQI,EAAE,CAAGvB,EAAMmB,EAAQS,EAAE,CAAG5B,EAAK,CAC3C,CAAC,IAAKmB,EAAQU,EAAE,CAAG7B,AAAO,IAAPA,EAAYmB,EAAQQ,EAAE,CAAC,CAC1C,CAAC,IAAI,CACR,AACL,EACAG,UAAW,WAEP,IADIvH,EAEAwH,EADetJ,EAAUiB,AAAhB,IAAI,CAAmBjB,OAAO,CAAEuJ,EAAQtI,AAAxC,IAAI,CAA2CsI,KAAK,CAAEC,EAAQvI,AAA9D,IAAI,CAAiEuI,KAAK,CAAEC,EAAqBzJ,EAAQ2H,YAAY,EAAI,EAAGU,EAAO,AAACrI,CAAAA,EAAQsI,OAAO,EAAI,CAAA,EAAK,EAAGC,EAAO,AAACvI,CAAAA,EAAQwI,OAAO,EAAI,CAAA,EAAK,EAE5MvH,AAFa,IAAI,CAEVyI,cAAc,GACrB,IAAK,IAAIC,EAAK,EAAGC,EAAK3I,AAHT,IAAI,CAGYE,MAAM,CAAEwI,EAAKC,EAAGvI,MAAM,CAAEsI,IAAM,CACvD,IAAIvI,EAAQwI,CAAE,CAACD,EAAG,CACdP,EAAKpB,EAAM6B,KAAKC,KAAK,CAACP,EAAMQ,GAAG,CAC3BR,EAAMF,SAAS,CAACjI,EAAM4I,CAAC,CAAG3B,AAAO,EAAPA,EAAU,EAAG,EAAG,EAAG,IAAK,CAACkB,EAAMQ,GAAG,CAAE,EAAIR,EAAMQ,GAAG,EAC/EjB,EAAKd,EAAM6B,KAAKC,KAAK,CAACP,EAAMQ,GAAG,CAC3BR,EAAMF,SAAS,CAACjI,EAAM4I,CAAC,CAAG3B,EAAM,EAAG,EAAG,EAAG,IAAK,CAACkB,EAAMQ,GAAG,CAAE,EAAIR,EAAMQ,GAAG,EAC3Ef,EAAKhB,EAAM6B,KAAKC,KAAK,CAACP,EAAMQ,GAAG,CAC3BR,EAAMF,SAAS,CAACjI,EAAM4I,CAAC,CAAG3B,EAAM,EAAG,EAAG,EAAG,IAAK,CAACkB,EAAMQ,GAAG,CAAE,EAAIR,EAAMQ,GAAG,EAC3Ed,EAAKjB,EAAM6B,KAAKC,KAAK,CAACP,EAAMQ,GAAG,CAC3BR,EAAMF,SAAS,CAACjI,EAAM4I,CAAC,CAAG3B,AAAO,EAAPA,EAAU,EAAG,EAAG,EAAG,IAAK,CAACkB,EAAMQ,GAAG,CAAE,EAAIR,EAAMQ,GAAG,EAC/EhB,EAAKf,EAAM6B,KAAKC,KAAK,CAACN,EAAMH,SAAS,CAACjI,EAAM6I,CAAC,CAAG1B,EAAM,EAAG,EAAG,EAAG,IAAK,CAACiB,EAAMO,GAAG,CAAE,EAAIP,EAAMO,GAAG,EAC7Fb,EAAKlB,EAAM6B,KAAKC,KAAK,CAACN,EAAMH,SAAS,CAACjI,EAAM6I,CAAC,CAAE,EAAG,EAAG,EAAG,IAAK,CAACT,EAAMO,GAAG,CAAE,EAAIP,EAAMO,GAAG,EACtFZ,EAAKnB,EAAM6B,KAAKC,KAAK,CAACN,EAAMH,SAAS,CAACjI,EAAM6I,CAAC,CAAG1B,EAAM,EAAG,EAAG,EAAG,IAAK,CAACiB,EAAMO,GAAG,CAAE,EAAIP,EAAMO,GAAG,EAC7FpC,EAAe,AAA8B,OAA7B7F,CAAAA,EAAKV,EAAMuG,YAAY,AAAD,GAAe7F,AAAO,KAAK,IAAZA,EAAgBA,EAAK2H,EAG1ES,EAAkBvC,EACdkC,KAAKM,GAAG,CAACrB,EAAKM,GAAMS,KAAKM,GAAG,CAAChB,EAAKD,GACtCkB,EAAcb,EAAMc,QAAQ,CACxB,CAACH,EAAkBA,EACvBI,EAAgBf,EAAMc,QAAQ,CAC1B,CAAC1C,EAAeA,EACpB4C,EAAgBf,EAAMa,QAAQ,CAC1B,CAAC1C,EAAeA,CAEpBvG,CAAAA,EAAM4I,CAAC,CAAG,IACVV,EAASA,GAAUO,KAAKW,KAAK,CAACX,KAAKM,GAAG,CAAChB,EAAKJ,GAAM,GAE7CS,CAAAA,EAAMa,QAAQ,CAAG,GAAK,CAAA,EAC3BtB,GAAMO,EACNJ,GAAMI,EACNH,GAAMG,GAGVlI,EAAMqJ,KAAK,CAAGrJ,EAAMsJ,OAAO,CAAG,AAAC5B,CAAAA,EAAKE,CAAC,EAAK,EAC1C5H,EAAMuJ,KAAK,CAAGzB,EASd9H,EAAMyH,SAAS,CAAG,CACdO,GARJA,GAAMgB,EAAcE,EAQRxB,GAPZA,GAAMwB,EAOctB,GANpBA,GAAMsB,EAMsBrB,GAL5BA,GAAMmB,EAAcE,EAKgBvB,GAJpCA,GAAMwB,EAIsCrB,GAAIA,EAAIC,GAHpDA,GAAMoB,CAIN,EAEAnJ,EAAMwJ,SAAS,CAAG,OAClBxJ,EAAMyJ,SAAS,CAAG,CACdnN,EAAG,CACC,CAAC,IAAKoL,EAAIC,EAAG,CACb,CAAC,IAAKC,EAAID,EAAG,CACb,CAAC,IAAKE,EAAIC,EAAG,CACb,CAAC,IAAKF,EAAIG,EAAG,CACb,CAAC,IAAKL,EAAIK,EAAG,CACb,CAAC,IAAKC,EAAIF,EAAG,CACb,CAAC,IAAI,CACR,AACL,CACJ,CACAjI,AAhEa,IAAI,CAgEVkB,eAAe,EAC1B,CACJ,EAEA2I,QAAS,CACLnC,eAAgBb,EAAczJ,SAAS,CAACsK,cAAc,CACtDC,iBAAkB,SAAU3H,CAAM,EAC9B,OAAOiH,EAAwBjH,EAAQ,EAAG,EAC9C,EACA6F,SAAU,SAAUS,CAAI,EACpB,GAAI,CAACA,EACD,MAAO,EAAE,CAEb,IAAIuD,EAAU,IAAI,CAACjC,SAAS,CAC5B,MAAO,CACH,CAAC,IAAKiC,EAAQhC,EAAE,CAAEgC,EAAQ/B,EAAE,CAAGxB,EAAK,CACpC,CAAC,IAAKuD,EAAQ9B,EAAE,CAAGzB,EAAMuD,EAAQ5B,EAAE,CAAC,CACpC,CAAC,IAAK4B,EAAQhC,EAAE,CAAEgC,EAAQ3B,EAAE,CAAG5B,EAAK,CACpC,CAAC,IAAKuD,EAAQ1B,EAAE,CAAG7B,EAAMuD,EAAQ5B,EAAE,CAAC,CACpC,CAAC,IAAI,CACR,AACL,EACAG,UAAW,WACP,IAOIC,EANAtJ,EAAUiB,AADD,IAAI,CACIjB,OAAO,CACxBuJ,EAAQtI,AAFC,IAAI,CAEEsI,KAAK,CACpBC,EAAQvI,AAHC,IAAI,CAGEuI,KAAK,CACpBC,EAAqBzJ,EAAQ2H,YAAY,EAAI,EAC7CU,EAAQrI,EAAQsI,OAAO,EAAI,EAC3BC,EAAO,AAACvI,CAAAA,EAAQwI,OAAO,EAAI,CAAA,EAAK,EAEpCvH,AARa,IAAI,CAQVyI,cAAc,GACrB,IAAK,IAAIC,EAAK,EAAG7H,EAAKb,AATT,IAAI,CASYE,MAAM,CAAEwI,EAAK7H,EAAGT,MAAM,CAAEsI,IAAM,CACvD,IAAIvI,EAAQU,CAAE,CAAC6H,EAAG,CACdP,EAAKpB,EAAM6B,KAAKW,KAAK,CAACjB,EAAMQ,GAAG,CAC3BR,EAAMF,SAAS,CAACjI,EAAM4I,CAAC,CAAG3B,EAAM,EAAG,EAAG,EAAG,IAAK,CAACkB,EAAMQ,GAAG,CAAE,EAAIR,EAAMQ,GAAG,EAC3Ef,EAAKhB,EAAM6B,KAAKW,KAAK,CAACjB,EAAMQ,GAAG,CAC3BR,EAAMF,SAAS,CAACjI,EAAM4I,CAAC,CAAG3B,EAAM,EAAG,EAAG,EAAG,IAAK,CAACkB,EAAMQ,GAAG,CAAE,EAAIR,EAAMQ,GAAG,EAC3EhB,EAAKf,EAAM6B,KAAKW,KAAK,CAAChB,EAAMH,SAAS,CAACjI,EAAM6I,CAAC,CAAG1B,EAAM,EAAG,EAAG,EAAG,IAAK,CAACiB,EAAMO,GAAG,CAAE,EAAIP,EAAMO,GAAG,EAC7Fb,EAAKlB,EAAM6B,KAAKW,KAAK,CAAChB,EAAMH,SAAS,CAACjI,EAAM6I,CAAC,CAAE,EAAG,EAAG,EAAG,IAAK,CAACT,EAAMO,GAAG,CAAE,EAAIP,EAAMO,GAAG,EACtFZ,EAAKnB,EAAM6B,KAAKW,KAAK,CAAChB,EAAMH,SAAS,CAACjI,EAAM6I,CAAC,CAAG1B,EAAM,EAAG,EAAG,EAAG,IAAK,CAACiB,EAAMO,GAAG,CAAE,EAAIP,EAAMO,GAAG,EAC7FjB,EAAKd,EAAM6B,KAAKW,KAAK,CAACjB,EAAMQ,GAAG,CAC3BR,EAAMF,SAAS,CAACjI,EAAM4I,CAAC,CAAE,EAAG,EAAG,EAAG,IAAK,CAACT,EAAMQ,GAAG,CAAE,EAAIR,EAAMQ,GAAG,EACpEpC,EAAeM,EAAmB7G,EAAMuG,YAAY,CACpD8B,GAGAS,EAAkBvC,EACdkC,KAAKM,GAAG,CAACrB,EAAKM,GAAMS,KAAKM,GAAG,CAAChB,EAAKD,GACtCoB,EAAgBf,EAAMc,QAAQ,CAC1B,CAACH,EAAkBA,EACvBK,EAAgBf,EAAMa,QAAQ,CAC1B,CAAC1C,EAAeA,CAGpBvG,CAAAA,EAAM4I,CAAC,CAAG,IACVV,EAASO,KAAKM,GAAG,CAAChB,EAAKJ,GAAM,EAAKS,CAAAA,EAAMa,QAAQ,CAAG,GAAK,CAAA,EACxDtB,GAAMO,EACNJ,GAAMI,EACNH,GAAMG,GAGVlI,EAAMqJ,KAAK,CAAGrJ,EAAMsJ,OAAO,CAAG5B,EAC9B1H,EAAMuJ,KAAK,CAAGzB,EAOd9H,EAAMyH,SAAS,CAAG,CACdO,GANJA,GAAMkB,EAMMxB,GAAIA,EAAIE,GALpBA,GAAMsB,EAKsBvB,GAJ5BA,GAAMwB,EAI8BrB,GAAIA,EAAIC,GAH5CA,GAAMoB,CAIN,EAEAnJ,EAAMwJ,SAAS,CAAG,OAClBxJ,EAAMyJ,SAAS,CAAG,CACdnN,EAAG,CACC,CAAC,IAAKoL,EAAIC,EAAG,CACb,CAAC,IAAKC,EAAIE,EAAG,CACb,CAAC,IAAKJ,EAAIK,EAAG,CACb,CAAC,IAAKC,EAAIF,EAAG,CACb,CAAC,IAAI,CACR,AACL,CACJ,CACAjI,AA9Da,IAAI,CA8DVkB,eAAe,EAC1B,CACJ,EAEA4I,OAAQ,CACJpC,eAAgBb,EAAczJ,SAAS,CAACsK,cAAc,CACtDC,iBAAkB,SAAU3H,CAAM,EAC9B,OAAOiH,EAAwBjH,EAAQ,EAAG,EAC9C,EACA6F,SAAU,SAAUS,CAAI,EACpB,OAAOO,EAAczJ,SAAS,CAACqH,UAAU,CAACrH,SAAS,CAACyI,QAAQ,CACvDvI,IAAI,CAAC,IAAI,CAAEgJ,EAAQA,CAAAA,GAAQ,IAAI,CAACyD,MAAM,AAAD,EAC9C,EACA3B,UAAW,WACP,IAOI4B,EACAC,EACAC,EACAH,EATAhL,EAAUiB,AADD,IAAI,CACIjB,OAAO,CACxBuJ,EAAQtI,AAFC,IAAI,CAEEsI,KAAK,CACpBC,EAAQvI,AAHC,IAAI,CAGEuI,KAAK,CACpBC,EAAqBzJ,EAAQ2H,YAAY,EAAI,EAC7CyD,EAAU,AAACpL,CAAAA,EAAQwI,OAAO,EAAI,CAAA,EAAK,EACnCF,EAAWtI,EAAQsI,OAAO,EAAI,EAK9B+C,EAAyB,CAAA,EAC7BpK,AAZa,IAAI,CAYVyI,cAAc,GACrB,IAAK,IAAIC,EAAK,EAAG7H,EAAKb,AAbT,IAAI,CAaYE,MAAM,CAAEwI,EAAK7H,EAAGT,MAAM,CAAEsI,IAAM,CACvD,IAAIvI,EAAQU,CAAE,CAAC6H,EAAG,CACdK,EAAIhC,EAAM6B,KAAKW,KAAK,CAACjB,EAAMQ,GAAG,CAC1BR,EAAMF,SAAS,CAACjI,EAAM4I,CAAC,CAAE,EAAG,EAAG,EAAG,IAAK,CAACT,EAAMQ,GAAG,CAAE,EAAIR,EAAMQ,GAAG,EACpEpC,EAAe8B,EACf6B,EAAqB,CAAA,EACrBrB,EAAIjC,EAAM6B,KAAKW,KAAK,CAAChB,EAAMH,SAAS,CAACjI,EAAM6I,CAAC,CAAE,EAAG,EAAG,EAAG,IAAK,CAACT,EAAMO,GAAG,CAAE,EAAIP,EAAMO,GAAG,CAEvD,MAAA,IAAvB3I,EAAMuG,YAAY,GACzBA,EAAevG,EAAMuG,YAAY,CACjC2D,EAAqB,CAAA,EACrBD,EAAyB,CAAA,GAoBzB,CAAA,CAACL,GAAUK,CAAqB,IAIhCF,EAAYtB,KAAKC,KAAK,CAACD,KAAK0B,IAAI,CAAEN,AAHlCA,CAAAA,EAAYpB,KAAKM,GAAG,CAACnC,EAAM6B,KAAKC,KAAK,CAACP,EAAMQ,GAAG,CAC3CR,EAAMF,SAAS,CAACjI,EAAM4I,CAAC,CAAG1B,EAAS,EAAG,EAAG,EAAG,IAAK,CAACiB,EAAMQ,GAAG,CAAE,EAAIR,EAAMQ,GAAG,EAAIC,EAAC,EAErCiB,EAAYC,AAD1DA,CAAAA,EAAYrB,KAAKM,GAAG,CAACnC,EAAM6B,KAAKC,KAAK,CAACN,EAAMH,SAAS,CAACjI,EAAM6I,CAAC,CAAGmB,EAAS,EAAG,EAAG,EAAG,IAAK,CAAC5B,EAAMO,GAAG,CAAE,EAAIP,EAAMO,GAAG,EAAIE,EAAC,EAC/CiB,GAAc,GACpFF,EAASnB,KAAK2B,GAAG,CAACP,EAAWE,EAAWD,GAAavD,EAKjD0D,GAA0B,CAACC,GAC3BD,CAAAA,EAAyB,CAAA,CAAI,GAMjCjK,EAAM4I,CAAC,CAAG,GACVC,CAAAA,GAAKiB,EAAa1B,CAAAA,EAAMa,QAAQ,CAAG,GAAK,CAAA,CAAC,EAG7CjJ,EAAMqJ,KAAK,CAAGrJ,EAAMsJ,OAAO,CAAGV,EAC9B5I,EAAMuJ,KAAK,CAAGV,EAEd7I,EAAM4J,MAAM,CAAGA,EAEf5J,EAAMwJ,SAAS,CAAG,SAClBxJ,EAAMyJ,SAAS,CAAG,CACdb,EAAGA,EACHC,EAAGA,EACHwB,EAAGT,CACP,CACJ,CACA/J,AA7Ea,IAAI,CA6EVkB,eAAe,EAC1B,CACJ,EAEAuJ,OAAQ,CACJ/C,eAAgBd,EAAcxJ,SAAS,CAACsK,cAAc,CACtDU,UAAWxB,EAAcxJ,SAAS,CAACgL,SAAS,CAC5CT,iBAAkBhB,EAClBd,SAAUe,EAAcxJ,SAAS,CAACqH,UAAU,CAACrH,SAAS,CAACyI,QAAQ,AACnE,CACJ,EAsBI6E,GACI5O,EAAgB,SAAUW,CAAC,CAC3BqI,CAAC,EAOD,MAAOhJ,AANHA,CAAAA,EAAgBgB,OAAOiI,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAUxI,CAAC,CAC1DqI,CAAC,EAAIrI,EAAEuI,SAAS,CAAGF,CAAG,GACd,SAAUrI,CAAC,CACnBqI,CAAC,EAAI,IAAK,IAAII,KAAKJ,EAAOhI,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACwH,EAC/DI,IAAIzI,CAAAA,CAAC,CAACyI,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,CAAG,CAAA,EACIzI,EAAGqI,EAC5B,EACO,SAAUrI,CAAC,CAAEqI,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAI6F,UAAU,uBAAyBC,OAAO9F,GAAK,iCAE7D,SAASK,IAAO,IAAI,CAACC,WAAW,CAAG3I,CAAG,CADtCX,EAAcW,EAAGqI,GAEjBrI,EAAEW,SAAS,CAAG0H,AAAM,OAANA,EAAahI,OAAOuI,MAAM,CAACP,GAAMK,CAAAA,EAAG/H,SAAS,CAAG0H,EAAE1H,SAAS,CAAE,IAAI+H,CAAG,CACtF,GAGA0F,EAAW,AAACnN,IAA+EmN,QAAQ,CAAEC,EAAqB,AAACpN,IAA+EiJ,IAAI,CAE9MoE,EAAkB,AAACnN,IAA2I4H,WAAW,CAAEwF,EAAeD,EAAgBE,MAAM,CAAEC,EAA8BH,EAAgBtF,OAAO,CAAE0F,EAA8BJ,EAAgBjE,OAAO,CAK9TsE,EAAyB,AAAC1N,IAA+EO,QAAQ,CAAEoN,EAAuB,AAAC3N,IAA+EQ,MAAM,CAAEoN,EAAsB,AAAC5N,IAA+ES,KAAK,CAAEoN,EAAa,AAAC7N,IAA+E6N,UAAU,CAY1b,SAASC,IACL,GAAI,CAAA,IAAI,CAACC,qBAAqB,EAAI,AAAc,cAAd,IAAI,CAACC,IAAI,EAG3C,IAAI5H,EAAO,IAAI,CAEX6H,EAAgB7H,EAAK9D,MAAM,CACtBrB,GAAG,CAAC,SAAUqB,CAAM,EACrB,OAAOA,EAAO4L,qBAAqB,EAC/B5L,EAAO4L,qBAAqB,CAAC9H,EACzC,GACK+H,MAAM,CAAC,SAAUnP,CAAC,CAAEoI,CAAC,EACtB,MAAO,AAACpI,CAAAA,GAAKA,EAAEoP,OAAO,AAAD,EAAMhH,CAAAA,GAAKA,EAAEgH,OAAO,AAAD,EACpCpP,EACAoI,CACR,EAAG,KAAK,IACJ,CACIgH,QAAS,EACTC,iBAAkB,CACtB,EAAGC,EAAgBpD,KAAKW,KAAK,CAACoC,EAAcG,OAAO,CAAGH,EAAcI,gBAAgB,CAEpFJ,CAAAA,EAAcG,OAAO,GAErBhI,EAAKgF,GAAG,EAAIkD,EACZlI,EAAK2H,qBAAqB,CAAG,CAAA,EAC7B3H,EAAKmI,kBAAkB,GACvB,OAAOnI,EAAK2H,qBAAqB,CACjC3H,EAAKoI,eAAe,EAAIP,EAAcG,OAAO,CAC7ChI,EAAKgF,GAAG,EAAIkD,GAEpB,CAaA,IAAIG,EAA+B,SAAUvG,CAAM,EAE/C,SAASuG,IACL,OAAOvG,AAAW,OAAXA,GAAmBA,EAAOhC,KAAK,CAAC,IAAI,CAAEC,YAAc,IAAI,AACnE,CA2FA,OA9FA6G,EAAsByB,EAAevG,GASrCuG,EAAcvJ,OAAO,CAAG,SAAUwJ,CAAS,EACnCb,EAAWV,EAAU,kBACrBO,EAAuBgB,EAAW,0BAA2BZ,EAErE,EAUAW,EAAc/O,SAAS,CAACsK,cAAc,CAAG,WACrC,OAAO,IAAI,CAAC5B,SAAS,CAAC4B,cAAc,CAAC9D,KAAK,CAAC,IAAI,CAAEC,UACrD,EACAsI,EAAc/O,SAAS,CAACiP,UAAU,CAAG,WAGjCrB,EAAa5N,SAAS,CAACiP,UAAU,CAAC/O,IAAI,CAAC,IAAI,EAC3C,IAAK,IAAIoL,EAAK,EAAG7H,EAAK,IAAI,CAACX,MAAM,CAAEwI,EAAK7H,EAAGT,MAAM,CAAEsI,IAAM,CACrD,IAAIvI,EAAQU,CAAE,CAAC6H,EAAG,AACdvI,CAAAA,EAAMmM,OAAO,EACbnM,EAAMmM,OAAO,CAAC,IAAI,CAACpN,KAAK,CAACqN,UAAU,CAAG,MAAQ,UAAU,CAAC,IAAI,CAACtL,YAAY,CAACd,GAEnF,CACJ,EAKAgM,EAAc/O,SAAS,CAACwO,qBAAqB,CAAG,SAAU9H,CAAI,EAC1D,IAAIE,EAAMF,EAAK0I,OAAO,CAClBV,EAAU,IAAI,CAAChG,SAAS,CAAC6B,gBAAgB,CAAC,IAAI,EAElD,GAAI,CAACmE,EACD,MAAO,CACHA,QAAS,EACTC,iBAAkB,CACtB,EAIJ,IAAIU,EAAS7D,KAAKW,KAAK,CAACzF,EAAKsE,SAAS,CAACpE,EAC/B8H,AAAe,EAAfA,EAAQ1E,IAAI,CACZ0E,EAAQxE,IAAI,CAAE,EAAG,EAAG,EAAG,IAC3BoF,EAAS9D,KAAKW,KAAK,CAACzF,EAAKsE,SAAS,CAACpE,EAAM8H,EAAQ1E,IAAI,CAAG,EAAG,EAAG,EAAG,EAAG,IACxE,MAAO,CACH0E,QAAS,AAAChI,CAAAA,EAAK6I,MAAM,CACjB/D,KAAKM,GAAG,CAACuD,EAASC,GAAU,EAC5B9D,KAAKM,GAAG,CAACuD,EAASC,EAAM,GAAM,EAOlCX,iBAAkB/H,EAAM,EAAI,GAChC,CACJ,EAKAmI,EAAc/O,SAAS,CAACwP,UAAU,CAAG,WAEjC,IAAIC,EAAMjH,EAAOxI,SAAS,CAACwP,UAAU,CAAChJ,KAAK,CAAC,IAAI,CAC5CC,WAEJ,OADA,IAAI,CAACiC,SAAS,CAAGgH,AAjLiCtF,CAiLZ,CAACqF,EAAI/G,SAAS,CAAC,CAC9C+G,CACX,EAKAV,EAAc/O,SAAS,CAACgL,SAAS,CAAG,WAChC,OAAO,IAAI,CAACtC,SAAS,CAACsC,SAAS,CAACxE,KAAK,CAAC,IAAI,CAAEC,UAChD,EAMAsI,EAAcY,cAAc,CAAGzB,EAAoBJ,EAA4B6B,cAAc,CAAE/G,GACxFmG,CACX,EAAEjB,GACFG,EAAqBc,EAAc/O,SAAS,CAAE,CAE1C4P,UAAWlC,EAKXmC,cAAe9B,EAA4B/N,SAAS,CAAC6P,aAAa,CAClEC,aAAclC,EAAa5N,SAAS,CAAC8P,YAAY,CACjDzI,WAjvBqDkB,CAkvBzD,GACA/H,IAA0IuP,kBAAkB,CAAC,UAAWhB,GAsBxK,IAAIiB,EAAK1P,IACT2P,AAjB0DlB,EAiBpCvJ,OAAO,CAACwK,EAAEE,IAAI,EACP,IAAI9P,EAAgBE,IAGvC,OADYH,EAAoB,OAAU,AAE3C,GAET"}