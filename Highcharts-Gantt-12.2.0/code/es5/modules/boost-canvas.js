!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("highcharts"),require("highcharts").Color):"function"==typeof define&&define.amd?define("highcharts/modules/boost-canvas",[["highcharts/highcharts"],["highcharts/highcharts","Color"]],e):"object"==typeof exports?exports["highcharts/modules/boost-canvas"]=e(require("highcharts"),require("highcharts").Color):t.Highcharts=e(t.Highcharts,t.Highcharts.Color)}(this,function(t,e){return function(){"use strict";var i,o,r,n,s,a={620:function(t){t.exports=e},944:function(e){e.exports=t}},l={};function h(t){var e=l[t];if(void 0!==e)return e.exports;var i=l[t]={exports:{}};return a[t](i,i.exports,h),i.exports}h.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return h.d(e,{a:e}),e},h.d=function(t,e){for(var i in e)h.o(e,i)&&!h.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},h.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};var u={};h.d(u,{default:function(){return tI}});var d=h(944),c=h.n(d),f=["area","areaspline","arearange","column","columnrange","bar","line","scatter","heatmap","bubble","treemap"],g={};f.forEach(function(t){g[t]=!0}),c().composed;var p=c().addEvent,m=c().pick;function v(t){var e=t.series,i=t.boost=t.boost||{},o=t.options.boost||{},r=m(o.seriesThreshold,50);if(e.length>=r)return!0;if(1===e.length)return!1;var n=o.allowForce;if(void 0===n){n=!0;for(var s=0,a=t.xAxis;s<a.length;s++){var l=a[s];if(m(l.min,-1/0)>m(l.dataMin,-1/0)||m(l.max,1/0)<m(l.dataMax,1/0)){n=!1;break}}}if(void 0!==i.forceChartBoost){if(n)return i.forceChartBoost;i.forceChartBoost=void 0}for(var h,u=0,d=0,c=0;c<e.length;c++){var f=e[c];0!==(h=f.options).boostThreshold&&!1!==f.visible&&"heatmap"!==f.type&&(g[f.type]&&++u,function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];var i=-Number.MAX_VALUE;return t.forEach(function(t){if(null!=t&&void 0!==t.length&&t.length>0)return i=t.length,!0}),i}(f.getColumn("x",!0),h.data,f.points)>=(h.boostThreshold||Number.MAX_VALUE)&&++d)}return i.forceChartBoost=n&&(u===e.length&&d===u||d>5),i.forceChartBoost}c().pushUnique;var b=function(t,e){var i=t.navigator,o={x:t.plotLeft,y:t.plotTop,width:t.plotWidth,height:t.plotHeight};if(i&&t.inverted?(o.width+=i.top+i.height,i.opposite||(o.x=i.left)):i&&!t.inverted&&(o.height=i.top+i.height-t.plotTop),e.is){var r=e.xAxis,n=e.yAxis;if(o=t.getClipBox(e),t.inverted){var s=o.width;o.width=o.height,o.height=s,o.x=n.pos,o.y=r.pos}else o.x=r.pos,o.y=n.pos}if(e===t){var a=t.inverted?t.xAxis:t.yAxis;a.length<=1&&(o.y=Math.min(a[0].pos,o.y),o.height=a[0].pos-t.plotTop+a[0].len)}return o},x=h(620),A=h.n(x),y={area:"LINES",arearange:"LINES",areaspline:"LINES",column:"LINES",columnrange:"LINES",bar:"LINES",line:"LINE_STRIP",scatter:"POINTS",heatmap:"TRIANGLES",treemap:"TRIANGLES",bubble:"POINTS"},P=c().clamp,T=c().error,C=c().pick,M=function(){function t(t){if(this.errors=[],this.uLocations={},this.gl=t,t&&!this.createShader())return}return t.prototype.bind=function(){this.gl&&this.shaderProgram&&this.gl.useProgram(this.shaderProgram)},t.prototype.createShader=function(){var t=this,e=this.stringToProgram("#version 100\n#define LN10 2.302585092994046\nprecision highp float;\nattribute vec4 aVertexPosition;\nattribute vec4 aColor;\nvarying highp vec2 position;\nvarying highp vec4 vColor;\nuniform mat4 uPMatrix;\nuniform float pSize;\nuniform float translatedThreshold;\nuniform bool hasThreshold;\nuniform bool skipTranslation;\nuniform float xAxisTrans;\nuniform float xAxisMin;\nuniform float xAxisMinPad;\nuniform float xAxisPointRange;\nuniform float xAxisLen;\nuniform bool  xAxisPostTranslate;\nuniform float xAxisOrdinalSlope;\nuniform float xAxisOrdinalOffset;\nuniform float xAxisPos;\nuniform bool  xAxisCVSCoord;\nuniform bool  xAxisIsLog;\nuniform bool  xAxisReversed;\nuniform float yAxisTrans;\nuniform float yAxisMin;\nuniform float yAxisMinPad;\nuniform float yAxisPointRange;\nuniform float yAxisLen;\nuniform bool  yAxisPostTranslate;\nuniform float yAxisOrdinalSlope;\nuniform float yAxisOrdinalOffset;\nuniform float yAxisPos;\nuniform bool  yAxisCVSCoord;\nuniform bool  yAxisIsLog;\nuniform bool  yAxisReversed;\nuniform bool  isBubble;\nuniform bool  bubbleSizeByArea;\nuniform float bubbleZMin;\nuniform float bubbleZMax;\nuniform float bubbleZThreshold;\nuniform float bubbleMinSize;\nuniform float bubbleMaxSize;\nuniform bool  bubbleSizeAbs;\nuniform bool  isInverted;\nfloat bubbleRadius(){\nfloat value = aVertexPosition.w;\nfloat zMax = bubbleZMax;\nfloat zMin = bubbleZMin;\nfloat radius = 0.0;\nfloat pos = 0.0;\nfloat zRange = zMax - zMin;\nif (bubbleSizeAbs){\nvalue = value - bubbleZThreshold;\nzMax = max(zMax - bubbleZThreshold, zMin - bubbleZThreshold);\nzMin = 0.0;\n}\nif (value < zMin){\nradius = bubbleZMin / 2.0 - 1.0;\n} else {\npos = zRange > 0.0 ? (value - zMin) / zRange : 0.5;\nif (bubbleSizeByArea && pos > 0.0){\npos = sqrt(pos);\n}\nradius = ceil(bubbleMinSize + pos * (bubbleMaxSize - bubbleMinSize)) / 2.0;\n}\nreturn radius * 2.0;\n}\nfloat translate(float val,\nfloat pointPlacement,\nfloat localA,\nfloat localMin,\nfloat minPixelPadding,\nfloat pointRange,\nfloat len,\nbool  cvsCoord,\nbool  isLog,\nbool  reversed\n){\nfloat sign = 1.0;\nfloat cvsOffset = 0.0;\nif (cvsCoord) {\nsign *= -1.0;\ncvsOffset = len;\n}\nif (isLog) {\nval = log(val) / LN10;\n}\nif (reversed) {\nsign *= -1.0;\ncvsOffset -= sign * len;\n}\nreturn sign * (val - localMin) * localA + cvsOffset + \n(sign * minPixelPadding);\n}\nfloat xToPixels(float value) {\nif (skipTranslation){\nreturn value;// + xAxisPos;\n}\nreturn translate(value, 0.0, xAxisTrans, xAxisMin, xAxisMinPad, xAxisPointRange, xAxisLen, xAxisCVSCoord, xAxisIsLog, xAxisReversed);// + xAxisPos;\n}\nfloat yToPixels(float value, float checkTreshold) {\nfloat v;\nif (skipTranslation){\nv = value;// + yAxisPos;\n} else {\nv = translate(value, 0.0, yAxisTrans, yAxisMin, yAxisMinPad, yAxisPointRange, yAxisLen, yAxisCVSCoord, yAxisIsLog, yAxisReversed);// + yAxisPos;\nif (v > yAxisLen) {\nv = yAxisLen;\n}\n}\nif (checkTreshold > 0.0 && hasThreshold) {\nv = min(v, translatedThreshold);\n}\nreturn v;\n}\nvoid main(void) {\nif (isBubble){\ngl_PointSize = bubbleRadius();\n} else {\ngl_PointSize = pSize;\n}\nvColor = aColor;\nif (skipTranslation && isInverted) {\ngl_Position = uPMatrix * vec4(aVertexPosition.y + yAxisPos, aVertexPosition.x + xAxisPos, 0.0, 1.0);\n} else if (isInverted) {\ngl_Position = uPMatrix * vec4(yToPixels(aVertexPosition.y, aVertexPosition.z) + yAxisPos, xToPixels(aVertexPosition.x) + xAxisPos, 0.0, 1.0);\n} else {\ngl_Position = uPMatrix * vec4(xToPixels(aVertexPosition.x) + xAxisPos, yToPixels(aVertexPosition.y, aVertexPosition.z) + yAxisPos, 0.0, 1.0);\n}\n}","vertex"),i=this.stringToProgram("precision highp float;\nuniform vec4 fillColor;\nvarying highp vec2 position;\nvarying highp vec4 vColor;\nuniform sampler2D uSampler;\nuniform bool isCircle;\nuniform bool hasColor;\nvoid main(void) {\nvec4 col = fillColor;\nvec4 tcol = texture2D(uSampler, gl_PointCoord.st);\nif (hasColor) {\ncol = vColor;\n}\nif (isCircle) {\ncol *= tcol;\nif (tcol.r < 0.0) {\ndiscard;\n} else {\ngl_FragColor = col;\n}\n} else {\ngl_FragColor = col;\n}\n}","fragment"),o=function(e){return t.gl.getUniformLocation(t.shaderProgram,e)};return e&&i?(this.shaderProgram=this.gl.createProgram(),this.gl.attachShader(this.shaderProgram,e),this.gl.attachShader(this.shaderProgram,i),this.gl.linkProgram(this.shaderProgram),this.gl.getProgramParameter(this.shaderProgram,this.gl.LINK_STATUS))?(this.gl.useProgram(this.shaderProgram),this.gl.bindAttribLocation(this.shaderProgram,0,"aVertexPosition"),this.pUniform=o("uPMatrix"),this.psUniform=o("pSize"),this.fcUniform=o("fillColor"),this.isBubbleUniform=o("isBubble"),this.bubbleSizeAbsUniform=o("bubbleSizeAbs"),this.bubbleSizeAreaUniform=o("bubbleSizeByArea"),this.uSamplerUniform=o("uSampler"),this.skipTranslationUniform=o("skipTranslation"),this.isCircleUniform=o("isCircle"),this.isInverted=o("isInverted"),!0):(this.errors.push(this.gl.getProgramInfoLog(this.shaderProgram)),this.handleErrors(),this.shaderProgram=!1,!1):(this.shaderProgram=!1,this.handleErrors(),!1)},t.prototype.handleErrors=function(){this.errors.length&&T("[highcharts boost] shader error - "+this.errors.join("\n"))},t.prototype.stringToProgram=function(t,e){var i=this.gl.createShader("vertex"===e?this.gl.VERTEX_SHADER:this.gl.FRAGMENT_SHADER);return(this.gl.shaderSource(i,t),this.gl.compileShader(i),this.gl.getShaderParameter(i,this.gl.COMPILE_STATUS))?i:(this.errors.push("when compiling "+e+" shader:\n"+this.gl.getShaderInfoLog(i)),!1)},t.prototype.destroy=function(){this.gl&&this.shaderProgram&&(this.gl.deleteProgram(this.shaderProgram),this.shaderProgram=!1)},t.prototype.fillColorUniform=function(){return this.fcUniform},t.prototype.getProgram=function(){return this.shaderProgram},t.prototype.pointSizeUniform=function(){return this.psUniform},t.prototype.perspectiveUniform=function(){return this.pUniform},t.prototype.reset=function(){this.gl&&this.shaderProgram&&(this.gl.uniform1i(this.isBubbleUniform,0),this.gl.uniform1i(this.isCircleUniform,0))},t.prototype.setBubbleUniforms=function(t,e,i,o){void 0===o&&(o=1);var r=t.options,n=Number.MAX_VALUE,s=-Number.MAX_VALUE;if(this.gl&&this.shaderProgram&&t.is("bubble")){var a=t.getPxExtremes();n=C(r.zMin,P(e,!1===r.displayNegative?r.zThreshold:-Number.MAX_VALUE,n)),s=C(r.zMax,Math.max(s,i)),this.gl.uniform1i(this.isBubbleUniform,1),this.gl.uniform1i(this.isCircleUniform,1),this.gl.uniform1i(this.bubbleSizeAreaUniform,"width"!==t.options.sizeBy),this.gl.uniform1i(this.bubbleSizeAbsUniform,t.options.sizeByAbsoluteValue),this.setUniform("bubbleMinSize",a.minPxSize*o),this.setUniform("bubbleMaxSize",a.maxPxSize*o),this.setUniform("bubbleZMin",n),this.setUniform("bubbleZMax",s),this.setUniform("bubbleZThreshold",t.options.zThreshold)}},t.prototype.setColor=function(t){this.gl&&this.shaderProgram&&this.gl.uniform4f(this.fcUniform,t[0]/255,t[1]/255,t[2]/255,t[3])},t.prototype.setDrawAsCircle=function(t){this.gl&&this.shaderProgram&&this.gl.uniform1i(this.isCircleUniform,+!!t)},t.prototype.setInverted=function(t){this.gl&&this.shaderProgram&&this.gl.uniform1i(this.isInverted,t)},t.prototype.setPMatrix=function(t){this.gl&&this.shaderProgram&&this.gl.uniformMatrix4fv(this.pUniform,!1,t)},t.prototype.setPointSize=function(t){this.gl&&this.shaderProgram&&this.gl.uniform1f(this.psUniform,t)},t.prototype.setSkipTranslation=function(t){this.gl&&this.shaderProgram&&this.gl.uniform1i(this.skipTranslationUniform,+(!0===t))},t.prototype.setTexture=function(t){this.gl&&this.shaderProgram&&this.gl.uniform1i(this.uSamplerUniform,t)},t.prototype.setUniform=function(t,e){if(this.gl&&this.shaderProgram){var i=this.uLocations[t]=this.uLocations[t]||this.gl.getUniformLocation(this.shaderProgram,t);this.gl.uniform1f(i,e)}},t}(),S=function(){function t(t,e,i){this.buffer=!1,this.iterator=0,this.preAllocated=!1,this.vertAttribute=!1,this.components=i||2,this.dataComponents=i,this.gl=t,this.shader=e}return t.prototype.allocate=function(t){this.iterator=-1,this.preAllocated=new Float32Array(4*t)},t.prototype.bind=function(){if(!this.buffer)return!1;this.gl.vertexAttribPointer(this.vertAttribute,this.components,this.gl.FLOAT,!1,0,0)},t.prototype.build=function(t,e,i){var o;return(this.data=t||[],this.data&&0!==this.data.length||this.preAllocated)?(this.components=i||this.components,this.buffer&&this.gl.deleteBuffer(this.buffer),this.preAllocated||(o=new Float32Array(this.data)),this.buffer=this.gl.createBuffer(),this.gl.bindBuffer(this.gl.ARRAY_BUFFER,this.buffer),this.gl.bufferData(this.gl.ARRAY_BUFFER,this.preAllocated||o,this.gl.STATIC_DRAW),this.vertAttribute=this.gl.getAttribLocation(this.shader.getProgram(),e),this.gl.enableVertexAttribArray(this.vertAttribute),o=!1,!0):(this.destroy(),!1)},t.prototype.destroy=function(){this.buffer&&(this.gl.deleteBuffer(this.buffer),this.buffer=!1,this.vertAttribute=!1),this.iterator=0,this.components=this.dataComponents||2,this.data=[]},t.prototype.push=function(t,e,i,o){this.preAllocated&&(this.preAllocated[++this.iterator]=t,this.preAllocated[++this.iterator]=e,this.preAllocated[++this.iterator]=i,this.preAllocated[++this.iterator]=o)},t.prototype.render=function(t,e,i){var o=this.preAllocated?this.preAllocated.length:this.data.length;return!!this.buffer&&!!o&&((!t||t>o||t<0)&&(t=0),(!e||e>o)&&(e=o),!(t>=e)&&(i=i||"POINTS",this.gl.drawArrays(this.gl[i],t/this.components,(e-t)/this.components),!0))},t}(),E=A().parse,k=c().doc,w=c().win,R=c().isNumber,U=c().isObject,L=c().merge,N=c().objectEach,_=c().pick,z={column:!0,columnrange:!0,bar:!0,area:!0,areaspline:!0,arearange:!0},D={scatter:!0,bubble:!0},I=["webgl","experimental-webgl","moz-webgl","webkit-3d"],G=function(){function t(t){this.data=[],this.height=0,this.isInited=!1,this.markerData=[],this.series=[],this.textureHandles={},this.width=0,this.postRenderCallback=t,this.settings={pointSize:1,lineWidth:1,fillColor:"#AA00AA",useAlpha:!0,usePreallocated:!1,useGPUTranslations:!1,debug:{timeRendering:!1,timeSeriesProcessing:!1,timeSetup:!1,timeBufferCopy:!1,timeKDTree:!1,showSkipSummary:!1}}}return t.orthoMatrix=function(t,e){return[2/t,0,0,0,0,-(2/e),0,0,0,0,-2,0,-1,1,-1,1]},t.seriesPointCount=function(t){var e,i,o;return t.boosted?(e=!!t.options.stacking,i=(t.getColumn("x").length?t.getColumn("x"):void 0)||t.options.xData||t.getColumn("x",!0),o=(e?t.data:i||t.options.data).length,"treemap"===t.type?o*=12:"heatmap"===t.type?o*=6:z[t.type]&&(o*=2),o):0},t.prototype.getPixelRatio=function(){return this.settings.pixelRatio||w.devicePixelRatio||1},t.prototype.setOptions=function(t){"pixelRatio"in t||(t.pixelRatio=1),L(!0,this.settings,t)},t.prototype.allocateBuffer=function(e){var i=this.vbuffer,o=0;this.settings.usePreallocated&&(e.series.forEach(function(e){e.boosted&&(o+=t.seriesPointCount(e))}),i&&i.allocate(o))},t.prototype.allocateBufferForSingleSeries=function(e){var i=this.vbuffer,o=0;this.settings.usePreallocated&&(e.boosted&&(o=t.seriesPointCount(e)),i&&i.allocate(o))},t.prototype.clear=function(){var t=this.gl;t&&t.clear(t.COLOR_BUFFER_BIT|t.DEPTH_BUFFER_BIT)},t.prototype.pushSeriesData=function(t,e){var i,o,r,n,s,a,l,h,u,d,c=this,f=this.data,g=this.settings,p=this.vbuffer,m=t.pointArrayMap&&"low,high"===t.pointArrayMap.join(","),v=t.chart,b=t.options,x=t.sorted,A=t.xAxis,y=t.yAxis,P=!!b.stacking,T=b.data,C=t.xAxis.getExtremes(),M=C.min-(t.xAxis.minPointOffset||0),S=C.max+(t.xAxis.minPointOffset||0),k=t.yAxis.getExtremes(),w=k.min-(t.yAxis.minPointOffset||0),R=k.max+(t.yAxis.minPointOffset||0),L=(t.getColumn("x").length?t.getColumn("x"):void 0)||b.xData||t.getColumn("x",!0),N=(t.getColumn("y").length?t.getColumn("y"):void 0)||b.yData||t.getColumn("y",!0),_=(t.getColumn("z").length?t.getColumn("z"):void 0)||b.zData||t.getColumn("z",!0),D=!L||0===L.length,I=b.connectNulls,G=t.points||!1,O=P?t.data:L||T,B={x:Number.MAX_VALUE,y:0},V={x:-Number.MAX_VALUE,y:0},X=void 0===v.index,F=z[t.type],H=b.zoneAxis||"y",Y=b.zones||!1,W=b.threshold,j=this.getPixelRatio(),q=t.chart.plotWidth,K=!1,Z=!1,Q=0,J=!1,$=-1,tt=!1,te=!1,ti=!1,to=!1,tr=!1,tn=!1,ts=!0,ta=!0,tl=!1,th=!1,tu=0;if(!b.boostData||!(b.boostData.length>0)){b.gapSize&&(th="value"!==b.gapUnit?b.gapSize*t.closestPointRange:b.gapSize),Y&&(d=[],Y.forEach(function(t,e){if(t.color){var i=E(t.color).rgba;i[0]/=255,i[1]/=255,i[2]/=255,d[e]=i,tl||void 0!==t.value||(tl=i)}}),tl||(tl=E(t.pointAttribs&&t.pointAttribs().fill||t.color).rgba,tl[0]/=255,tl[1]/=255,tl[2]/=255)),v.inverted&&(q=t.chart.plotHeight),t.closestPointRangePx=Number.MAX_VALUE;var td=function(t){t&&(e.colorData.push(t[0]),e.colorData.push(t[1]),e.colorData.push(t[2]),e.colorData.push(t[3]))},tc=function(t,i,o,r,n){void 0===r&&(r=1),td(n),1!==j&&(!g.useGPUTranslations||e.skipTranslation)&&(t*=j,i*=j,r*=j),g.usePreallocated&&p?(p.push(t,i,+!!o,r),tu+=4):(f.push(t),f.push(i),f.push(o?j:0),f.push(r))},tf=function(){e.segments.length&&(e.segments[e.segments.length-1].to=f.length||tu)},tg=function(){(!e.segments.length||e.segments[e.segments.length-1].from!==(f.length||tu))&&(tf(),e.segments.push({from:f.length||tu}))},tp=function(t,e,i,o,r){td(r),tc(t+i,e),td(r),tc(t,e),td(r),tc(t,e+o),td(r),tc(t,e+o),td(r),tc(t+i,e+o),td(r),tc(t+i,e)};if(tg(),G&&G.length>0){e.skipTranslation=!0,e.drawMode="TRIANGLES",G[0].node&&G[0].node.levelDynamic&&G.sort(function(t,e){if(t.node){if(t.node.levelDynamic>e.node.levelDynamic)return 1;if(t.node.levelDynamic<e.node.levelDynamic)return -1}return 0}),G.forEach(function(e){var i,o,r=e.plotY;if(void 0!==r&&!isNaN(r)&&null!==e.y&&e.shapeArgs){var s=e.shapeArgs,a=s.x,l=void 0===a?0:a,h=s.y,u=void 0===h?0:h,d=s.width,c=void 0===d?0:d,f=s.height,g=void 0===f?0:f;i=(o=v.styledMode?e.series.colorAttribs(e):o=e.series.pointAttribs(e))["stroke-width"]||0,tr=E(o.fill).rgba,tr[0]/=255,tr[1]/=255,tr[2]/=255,t.is("treemap")&&(i=i||1,n=E(o.stroke).rgba,n[0]/=255,n[1]/=255,n[2]/=255,tp(l,u,c,g,n),i/=2),t.is("heatmap")&&v.inverted&&(l=A.len-l,u=y.len-u,c=-c,g=-g),tp(l+i,u+i,c-2*i,g-2*i,tr)}}),tf();return}for(;$<O.length-1&&"break"!==function(){if(void 0===(l=O[++$]))return"continue";if(X)return"break";var n,c=T&&T[$];return(!D&&U(c,!0)&&c.color&&(tr=E(c.color).rgba,tr[0]/=255,tr[1]/=255,tr[2]/=255),D?(s=l[0],a=l[1],O[$+1]&&(te=O[$+1][0]),O[$-1]&&(tt=O[$-1][0]),l.length>=3&&(h=l[2],l[2]>e.zMax&&(e.zMax=l[2]),l[2]<e.zMin&&(e.zMin=l[2]))):(s=l,a=null==N?void 0:N[$],O[$+1]&&(te=O[$+1]),O[$-1]&&(tt=O[$-1]),_&&_.length&&(h=_[$],_[$]>e.zMax&&(e.zMax=_[$]),_[$]<e.zMin&&(e.zMin=_[$]))),I||null!==s&&null!==a)?(te&&te>=M&&te<=S&&(ti=!0),tt&&tt>=M&&tt<=S&&(to=!0),m?(D&&(a=l.slice(1,3)),u=null===(i=t.getColumn("low",!0))||void 0===i?void 0:i[$],a=(null===(o=t.getColumn("high",!0))||void 0===o?void 0:o[$])||0):P&&(s=l.x,u=(a=l.stackY)-l.y),null!=w&&null!=R&&(ts=a>=w&&a<=R),x||ts)?(s>S&&V.x<S&&(V.x=s,V.y=a),s<M&&B.x>M&&(B.x=s,B.y=a),null===a&&I)?"continue":null!==a&&(ts||!(O.length>1)||ti||to)?((x&&(te>=M||s>=M)&&(tt<=S||s<=S)||!x&&s>=M&&s<=S)&&(tn=!0),tn||ti||to)?(th&&s-tt>th&&tg(),Y&&(Y.some(function(t,e){var i=Y[e-1];return"x"===H?void 0!==t.value&&s<=t.value&&(d[e]&&(!i||s>=i.value)&&(n=d[e]),!0):void 0!==t.value&&a<=t.value&&(d[e]&&(!i||a>=i.value)&&(n=d[e]),!0)}),tr=n||tl||tr),!g.useGPUTranslations&&(e.skipTranslation=!0,s=A.toPixels(s,!0),a=y.toPixels(a,!0),s>q&&"POINTS"===e.drawMode))?"continue":(e.hasMarkers&&tn&&!1!==K&&(t.closestPointRangePx=Math.min(t.closestPointRangePx,Math.abs(s-K))),!g.useGPUTranslations&&!g.usePreallocated&&K&&1>Math.abs(s-K)&&Z&&1>Math.abs(a-Z))?(g.debug.showSkipSummary&&++Q,"continue"):void(F&&(r=u||0,(!1===u||void 0===u)&&(r=a<0?a:0),(m||P)&&!y.logarithmic||(r=Math.max(null===W?w:W,w)),g.useGPUTranslations||(r=y.toPixels(r,!0)),tc(s,r,0,0,tr)),b.step&&!ta&&tc(s,Z,0,2,tr),tc(s,a,0,"bubble"===t.type?h||1:2,tr),K=s,Z=a,J=!0,ta=!1):"continue":(tg(),"continue"):"continue":(tg(),"continue")}(););g.debug.showSkipSummary&&console.log("skipped points:",Q);var tm=function(t,i){if(g.useGPUTranslations||(e.skipTranslation=!0,t.x=A.toPixels(t.x,!0),t.y=y.toPixels(t.y,!0)),i){c.data=[t.x,t.y,0,2].concat(c.data);return}tc(t.x,t.y,0,2)};!J&&!1!==I&&"line_strip"===t.drawMode&&(B.x<Number.MAX_VALUE&&tm(B,!0),V.x>-Number.MAX_VALUE&&tm(V)),tf()}},t.prototype.pushSeries=function(t){var e=this.markerData,i=this.series,o=this.settings;i.length>0&&i[i.length-1].hasMarkers&&(i[i.length-1].markerTo=e.length),o.debug.timeSeriesProcessing&&console.time("building "+t.type+" series");var r={segments:[],markerFrom:e.length,colorData:[],series:t,zMin:Number.MAX_VALUE,zMax:-Number.MAX_VALUE,hasMarkers:!!t.options.marker&&!1!==t.options.marker.enabled,showMarkers:!0,drawMode:y[t.type]||"LINE_STRIP"};t.index>=i.length?i.push(r):i[t.index]=r,this.pushSeriesData(t,r),o.debug.timeSeriesProcessing&&console.timeEnd("building "+t.type+" series")},t.prototype.flush=function(){var t=this.vbuffer;this.data=[],this.markerData=[],this.series=[],t&&t.destroy()},t.prototype.setXAxis=function(t){var e=this.shader;if(e){var i=this.getPixelRatio();e.setUniform("xAxisTrans",t.transA*i),e.setUniform("xAxisMin",t.min),e.setUniform("xAxisMinPad",t.minPixelPadding*i),e.setUniform("xAxisPointRange",t.pointRange),e.setUniform("xAxisLen",t.len*i),e.setUniform("xAxisPos",t.pos*i),e.setUniform("xAxisCVSCoord",!t.horiz),e.setUniform("xAxisIsLog",!!t.logarithmic),e.setUniform("xAxisReversed",!!t.reversed)}},t.prototype.setYAxis=function(t){var e=this.shader;if(e){var i=this.getPixelRatio();e.setUniform("yAxisTrans",t.transA*i),e.setUniform("yAxisMin",t.min),e.setUniform("yAxisMinPad",t.minPixelPadding*i),e.setUniform("yAxisPointRange",t.pointRange),e.setUniform("yAxisLen",t.len*i),e.setUniform("yAxisPos",t.pos*i),e.setUniform("yAxisCVSCoord",!t.horiz),e.setUniform("yAxisIsLog",!!t.logarithmic),e.setUniform("yAxisReversed",!!t.reversed)}},t.prototype.setThreshold=function(t,e){var i=this.shader;i&&(i.setUniform("hasThreshold",t),i.setUniform("translatedThreshold",e))},t.prototype.renderChart=function(e){var i=this,o=this.gl,r=this.settings,n=this.shader,s=this.vbuffer,a=this.getPixelRatio();if(!e)return!1;this.width=e.chartWidth*a,this.height=e.chartHeight*a;var l=this.height,h=this.width;if(!o||!n||!h||!l)return!1;r.debug.timeRendering&&console.time("gl rendering"),o.canvas.width=h,o.canvas.height=l,n.bind(),o.viewport(0,0,h,l),n.setPMatrix(t.orthoMatrix(h,l)),r.lineWidth>1&&!c().isMS&&o.lineWidth(r.lineWidth),s&&(s.build(this.data,"aVertexPosition",4),s.bind()),n.setInverted(e.inverted),this.series.forEach(function(t,l){var h,u,d,c,f,g,p=t.series.options,m=p.marker,v=void 0!==p.lineWidth?p.lineWidth:1,b=p.threshold,x=R(b),y=t.series.yAxis.getThreshold(b),P=_(p.marker?p.marker.enabled:null,!!t.series.xAxis.isRadial||null,t.series.closestPointRangePx>2*((p.marker?p.marker.radius:10)||10)),T=i.textureHandles[m&&m.symbol||t.series.symbol]||i.textureHandles.circle,C=[];if(0!==t.segments.length&&t.segments[0].from!==t.segments[0].to){if(T.isReady&&(o.bindTexture(o.TEXTURE_2D,T.handle),n.setTexture(T.handle)),e.styledMode?t.series.markerGroup===(null===(h=t.series.chart.boost)||void 0===h?void 0:h.markerGroup)?(delete t.series.markerGroup,t.series.markerGroup=t.series.plotGroup("markerGroup","markers","visible",1,e.seriesGroup).addClass("highcharts-tracker"),g=t.series.markerGroup.getStyle("fill"),t.series.markerGroup.destroy(),t.series.markerGroup=null===(u=t.series.chart.boost)||void 0===u?void 0:u.markerGroup):g=null===(d=t.series.markerGroup)||void 0===d?void 0:d.getStyle("fill"):(g="POINTS"===t.drawMode&&t.series.pointAttribs&&t.series.pointAttribs().fill||t.series.color,p.colorByPoint&&(g=t.series.chart.options.colors[l])),t.series.fillOpacity&&p.fillOpacity&&(g=new(A())(g).setOpacity(_(p.fillOpacity,1)).get()),C=E(g).rgba,r.useAlpha||(C[3]=1),"add"===p.boostBlending?(o.blendFunc(o.SRC_ALPHA,o.ONE),o.blendEquation(o.FUNC_ADD)):"mult"===p.boostBlending||"multiply"===p.boostBlending?o.blendFunc(o.DST_COLOR,o.ZERO):"darken"===p.boostBlending?(o.blendFunc(o.ONE,o.ONE),o.blendEquation(o.FUNC_MIN)):o.blendFuncSeparate(o.SRC_ALPHA,o.ONE_MINUS_SRC_ALPHA,o.ONE,o.ONE_MINUS_SRC_ALPHA),n.reset(),t.colorData.length>0?(n.setUniform("hasColor",1),(f=new S(o,n)).build(Array(t.segments[0].from).concat(t.colorData),"aColor",4),f.bind()):(n.setUniform("hasColor",0),o.disableVertexAttribArray(o.getAttribLocation(n.getProgram(),"aColor"))),n.setColor(C),i.setXAxis(t.series.xAxis),i.setYAxis(t.series.yAxis),i.setThreshold(x,y),"POINTS"===t.drawMode&&n.setPointSize(2*_(p.marker&&p.marker.radius,.5)*a),n.setSkipTranslation(t.skipTranslation),"bubble"===t.series.type&&n.setBubbleUniforms(t.series,t.zMin,t.zMax,a),n.setDrawAsCircle(D[t.series.type]||!1),s){if(v>0||"LINE_STRIP"!==t.drawMode)for(c=0;c<t.segments.length;c++)s.render(t.segments[c].from,t.segments[c].to,t.drawMode);if(t.hasMarkers&&P)for(n.setPointSize(2*_(p.marker&&p.marker.radius,5)*a),n.setDrawAsCircle(!0),c=0;c<t.segments.length;c++)s.render(t.segments[c].from,t.segments[c].to,"POINTS")}}}),r.debug.timeRendering&&console.timeEnd("gl rendering"),this.postRenderCallback&&this.postRenderCallback(this),this.flush()},t.prototype.render=function(t){var e=this;if(this.clear(),t.renderer.forExport)return this.renderChart(t);this.isInited?this.renderChart(t):setTimeout(function(){e.render(t)},1)},t.prototype.setSize=function(e,i){var o=this.shader;o&&(this.width!==e||this.height!==i)&&(this.width=e,this.height=i,o.bind(),o.setPMatrix(t.orthoMatrix(e,i)))},t.prototype.init=function(t,e){var i=this,o=this.settings;if(this.isInited=!1,!t)return!1;o.debug.timeSetup&&console.time("gl setup");for(var r=0;r<I.length&&(this.gl=t.getContext(I[r],{}),!this.gl);++r);var n=this.gl;if(!n)return!1;e||this.flush(),n.enable(n.BLEND),n.blendFunc(n.SRC_ALPHA,n.ONE_MINUS_SRC_ALPHA),n.disable(n.DEPTH_TEST),n.depthFunc(n.LESS);var s=this.shader=new M(n);if(!s)return!1;this.vbuffer=new S(n,s);var a=function(t,e){var o={isReady:!1,texture:k.createElement("canvas"),handle:n.createTexture()},r=o.texture.getContext("2d");i.textureHandles[t]=o,o.texture.width=512,o.texture.height=512,r.mozImageSmoothingEnabled=!1,r.webkitImageSmoothingEnabled=!1,r.msImageSmoothingEnabled=!1,r.imageSmoothingEnabled=!1,r.strokeStyle="rgba(255, 255, 255, 0)",r.fillStyle="#FFF",e(r);try{n.activeTexture(n.TEXTURE0),n.bindTexture(n.TEXTURE_2D,o.handle),n.texImage2D(n.TEXTURE_2D,0,n.RGBA,n.RGBA,n.UNSIGNED_BYTE,o.texture),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_WRAP_S,n.CLAMP_TO_EDGE),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_WRAP_T,n.CLAMP_TO_EDGE),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_MAG_FILTER,n.LINEAR),n.texParameteri(n.TEXTURE_2D,n.TEXTURE_MIN_FILTER,n.LINEAR),n.bindTexture(n.TEXTURE_2D,null),o.isReady=!0}catch(t){}};return a("circle",function(t){t.beginPath(),t.arc(256,256,256,0,2*Math.PI),t.stroke(),t.fill()}),a("square",function(t){t.fillRect(0,0,512,512)}),a("diamond",function(t){t.beginPath(),t.moveTo(256,0),t.lineTo(512,256),t.lineTo(256,512),t.lineTo(0,256),t.lineTo(256,0),t.fill()}),a("triangle",function(t){t.beginPath(),t.moveTo(0,512),t.lineTo(256,0),t.lineTo(512,512),t.lineTo(0,512),t.fill()}),a("triangle-down",function(t){t.beginPath(),t.moveTo(0,0),t.lineTo(256,512),t.lineTo(512,0),t.lineTo(0,0),t.fill()}),this.isInited=!0,o.debug.timeSetup&&console.timeEnd("gl setup"),!0},t.prototype.destroy=function(){var t=this.gl,e=this.shader,i=this.vbuffer;this.flush(),i&&i.destroy(),e&&e.destroy(),t&&(N(this.textureHandles,function(e){e.handle&&t.deleteTexture(e.handle)}),t.canvas.width=1,t.canvas.height=1)},t}(),O=function(t,e,i){if(i||2==arguments.length)for(var o,r=0,n=e.length;r<n;r++)!o&&r in e||(o||(o=Array.prototype.slice.call(e,0,r)),o[r]=e[r]);return t.concat(o||Array.prototype.slice.call(e))};(i=o||(o={})).setLength=function(t,e,i){return Array.isArray(t)?(t.length=e,t):t[i?"subarray":"slice"](0,e)},i.splice=function(t,e,i,o,r){if(void 0===r&&(r=[]),Array.isArray(t))return Array.isArray(r)||(r=Array.from(r)),{removed:t.splice.apply(t,O([e,i],r,!1)),array:t};var n=Object.getPrototypeOf(t).constructor,s=t[o?"subarray":"slice"](e,e+i),a=new n(t.length-i+r.length);return a.set(t.subarray(0,e),0),a.set(r,e),a.set(t.subarray(e+i),e+r.length),{removed:s,array:a}};var B=o,V=B.setLength,X=B.splice,F=c().fireEvent,H=c().objectEach,Y=c().uniqueKey,W=function(){function t(t){void 0===t&&(t={});var e=this;this.autoId=!t.id,this.columns={},this.id=t.id||Y(),this.modified=this,this.rowCount=0,this.versionTag=Y();var i=0;H(t.columns||{},function(t,o){e.columns[o]=t.slice(),i=Math.max(i,t.length)}),this.applyRowCount(i)}return t.prototype.applyRowCount=function(t){var e=this;this.rowCount=t,H(this.columns,function(i,o){i.length!==t&&(e.columns[o]=V(i,t))})},t.prototype.deleteRows=function(t,e){var i=this;if(void 0===e&&(e=1),e>0&&t<this.rowCount){var o=0;H(this.columns,function(r,n){i.columns[n]=X(r,t,e).array,o=r.length}),this.rowCount=o}F(this,"afterDeleteRows",{rowIndex:t,rowCount:e}),this.versionTag=Y()},t.prototype.getColumn=function(t,e){return this.columns[t]},t.prototype.getColumns=function(t,e){var i=this;return(t||Object.keys(this.columns)).reduce(function(t,e){return t[e]=i.columns[e],t},{})},t.prototype.getRow=function(t,e){var i=this;return(e||Object.keys(this.columns)).map(function(e){var o;return null===(o=i.columns[e])||void 0===o?void 0:o[t]})},t.prototype.setColumn=function(t,e,i,o){var r;void 0===e&&(e=[]),void 0===i&&(i=0),this.setColumns(((r={})[t]=e,r),i,o)},t.prototype.setColumns=function(t,e,i){var o=this,r=this.rowCount;H(t,function(t,e){o.columns[e]=t.slice(),r=t.length}),this.applyRowCount(r),(null==i?void 0:i.silent)||(F(this,"afterSetColumns"),this.versionTag=Y())},t.prototype.setRow=function(t,e,i,o){void 0===e&&(e=this.rowCount);var r=this.columns,n=i?this.rowCount+1:e+1;H(t,function(t,s){var a=r[s]||(null==o?void 0:o.addColumns)!==!1&&Array(n);a&&(i?a=X(a,e,0,!0,[t]).array:a[e]=t,r[s]=a)}),n>this.rowCount&&this.applyRowCount(n),(null==o?void 0:o.silent)||(F(this,"afterSetRows"),this.versionTag=Y())},t}(),j=c().getOptions,q=c().composed,K=c().doc,Z=c().noop,Q=c().win,J=c().addEvent,$=c().destroyObjectProperties,tt=c().error,te=c().extend,ti=c().fireEvent,to=c().isArray,tr=c().isNumber,tn=c().pick,ts=c().pushUnique,ta=c().wrap,tl=c().defined;function th(t,e){var i=e.boost;t&&i&&i.target&&i.canvas&&!v(e.chart)&&t.allocateBufferForSingleSeries(e)}function tu(t){return tn(t&&t.options&&t.options.boost&&t.options.boost.enabled,!0)}function td(t,e){var i,o,r,s=t.constructor,a=t.seriesGroup||e.group,l=t.chartWidth,h=t.chartHeight,u=t,d="undefined"!=typeof SVGForeignObjectElement,c=!1;v(t)?u=t:(u=e,c=!!((null===(i=e.options.events)||void 0===i?void 0:i.click)||(null===(r=null===(o=e.options.point)||void 0===o?void 0:o.events)||void 0===r?void 0:r.click)));var f=u.boost=u.boost||{};if(d=!1,n||(n=K.createElement("canvas")),!f.target&&(f.canvas=n,t.renderer.forExport||!d?(u.renderTarget=f.target=t.renderer.image("",0,0,l,h).addClass("highcharts-boost-canvas").add(a),f.clear=function(){f.target.attr({href:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII="})},f.copy=function(){f.resize(),f.target.attr({href:f.canvas.toDataURL("image/png")})}):(f.targetFo=t.renderer.createElement("foreignObject").add(a),u.renderTarget=f.target=K.createElement("canvas"),f.targetCtx=f.target.getContext("2d"),f.targetFo.element.appendChild(f.target),f.clear=function(){f.target.width=f.canvas.width,f.target.height=f.canvas.height},f.copy=function(){f.target.width=f.canvas.width,f.target.height=f.canvas.height,f.targetCtx.drawImage(f.canvas,0,0)}),f.resize=function(){var e,i;l=t.chartWidth,h=t.chartHeight,(f.targetFo||f.target).attr({x:0,y:0,width:l,height:h}).css({pointerEvents:c?void 0:"none",mixedBlendMode:"normal",opacity:1}).addClass(c?"highcharts-tracker":""),u instanceof s&&(null===(i=null===(e=u.boost)||void 0===e?void 0:e.markerGroup)||void 0===i||i.translate(t.plotLeft,t.plotTop))},f.clipRect=t.renderer.clipRect(),(f.targetFo||f.target).attr({zIndex:e.options.zIndex}),u instanceof s&&(u.boost.markerGroup=u.renderer.g().add(a).translate(e.xAxis.pos,e.yAxis.pos))),f.canvas.width=l,f.canvas.height=h,f.clipRect){var g=b(t,u),p=g.width===t.clipBox.width&&g.height===t.clipBox.height?a:f.targetFo||f.target;f.clipRect.attr(g),null==p||p.clip(f.clipRect)}return f.resize(),f.clear(),!f.wgl&&(f.wgl=new G(function(t){t.settings.debug.timeBufferCopy&&console.time("buffer copy"),f.copy(),t.settings.debug.timeBufferCopy&&console.timeEnd("buffer copy")}),f.wgl.init(f.canvas)||tt("[highcharts boost] - unable to init WebGL renderer"),f.wgl.setOptions(t.options.boost||{}),u instanceof s&&f.wgl.allocateBuffer(t)),f.wgl.setSize(l,h),f.wgl}function tc(t){var e=t.points;if(e){var i=void 0,o=void 0;for(o=0;o<e.length;o+=1)(i=e[o])&&i.destroyElements&&i.destroyElements()}["graph","area","tracker"].forEach(function(e){var i=t[e];i&&(t[e]=i.destroy())});for(var r=0,n=t.zones;r<n.length;r++)$(n[r],void 0,!0)}function tf(t,e,i,o,r,n){for(var s=(r=r||0)+(o=o||3e3),a=!0;a&&r<s&&r<t.length;)a=e(t[r],r),++r;a&&(r<t.length?n?tf(t,e,i,o,r,n):Q.requestAnimationFrame?Q.requestAnimationFrame(function(){tf(t,e,i,o,r)}):setTimeout(tf,0,t,e,i,o,r):i&&i())}function tg(t,e){var i=t.options,o=t.dataTable.modified.rowCount,r=t.xAxis&&t.xAxis.options,n=t.yAxis&&t.yAxis.options,s=t.colorAxis&&t.colorAxis.options;return o>(i.boostThreshold||Number.MAX_VALUE)&&tr(n.min)&&tr(n.max)&&(!e||tr(r.min)&&tr(r.max))&&(!s||tr(s.min)&&tr(s.max))}var tp=function(t,e){return!t.forceCrop&&(v(t.chart)||(e?e.length:0)>=(t.options.boostThreshold||Number.MAX_VALUE))};function tm(){var t=this,e=t.chart;e.boost&&e.boost.markerGroup===t.markerGroup&&(t.markerGroup=null),e.hoverPoints&&(e.hoverPoints=e.hoverPoints.filter(function(e){return e.series===t})),e.hoverPoint&&e.hoverPoint.series===t&&(e.hoverPoint=null)}function tv(){var t=this.boost;t&&t.canvas&&t.target&&(t.wgl&&t.wgl.clear(),t.clear&&t.clear())}function tb(t){var e=t.boost;e&&e.canvas&&e.target&&e.wgl&&!v(t.chart)&&e.wgl.render(t.chart)}function tx(t,e){var i,o=t.options,r=t.xAxis,n=t.pointClass;if(e instanceof n)return e;var s=t.is("scatter"),a=(s&&t.getColumn("x",!0).length?t.getColumn("x",!0):void 0)||(t.getColumn("x").length?t.getColumn("x"):void 0)||o.xData||t.getColumn("x",!0)||!1,l=t.getColumn("y",!0)||o.yData||!1,h=new n(t,s&&a&&l?[a[e.i],l[e.i]]:(to(t.options.data)?t.options.data:[])[e.i],a?a[e.i]:void 0);return h.category=tn(r.categories?r.categories[h.x]:h.x,h.x),h.key=null!==(i=h.name)&&void 0!==i?i:h.category,h.dist=e.dist,h.distX=e.distX,h.plotX=e.plotX,h.plotY=e.plotY,h.index=e.i,h.percentage=e.percentage,h.isInside=t.isPointInside(h),h}function tA(t){var e=this.options,i=this.xAxis,o=this.yAxis;if(!this.isDirty&&!i.isDirty&&!o.isDirty&&!t)return!1;this.yAxis.setTickInterval();var r=e.boostThreshold||0,n=e.cropThreshold,s=this.getColumn("x"),a=i.getExtremes(),l=null!==(p=a.max)&&void 0!==p?p:Number.MAX_VALUE,h=null!==(m=a.min)&&void 0!==m?m:-Number.MAX_VALUE,u=this.getColumn("y"),d=o.getExtremes(),c=null!==(v=d.max)&&void 0!==v?v:Number.MAX_VALUE,f=null!==(b=d.min)&&void 0!==b?b:-Number.MAX_VALUE;if(!this.boosted&&i.old&&o.old&&h>=(null!==(x=i.old.min)&&void 0!==x?x:-Number.MAX_VALUE)&&l<=(null!==(A=i.old.max)&&void 0!==A?A:Number.MAX_VALUE)&&f>=(null!==(y=o.old.min)&&void 0!==y?y:-Number.MAX_VALUE)&&c<=(null!==(P=o.old.max)&&void 0!==P?P:Number.MAX_VALUE))return this.dataTable.modified.setColumns({x:s,y:u}),!0;var g=this.dataTable.rowCount;if(!r||g<r||n&&!this.forceCrop&&!this.getExtremesFromAll&&!e.getExtremesFromAll&&g<n)return this.dataTable.modified.setColumns({x:s,y:u}),!0;for(var p,m,v,b,x,A,y,P,T,C,M=[],S=[],E=[],k=!(tr(a.max)||tr(a.min)),w=!(tr(d.max)||tr(d.min)),R=!1,U=s[0],L=s[0],N=null==u?void 0:u[0],_=null==u?void 0:u[0],z=0,D=s.length;z<D;++z)T=s[z],C=null==u?void 0:u[z],T>=h&&T<=l&&C>=f&&C<=c?(M.push({x:T,y:C}),S.push(T),E.push(C),k&&(U=Math.max(U,T),L=Math.min(L,T)),w&&(N=Math.max(N,C),_=Math.min(_,C))):R=!0;return k&&(i.dataMax=Math.max(U,i.dataMax||0),i.dataMin=Math.min(L,i.dataMin||0)),w&&(o.dataMax=Math.max(N,o.dataMax||0),o.dataMin=Math.min(_,o.dataMin||0)),this.cropped=R,this.cropStart=0,R&&this.dataTable.modified===this.dataTable&&(this.dataTable.modified=new W),this.dataTable.modified.setColumns({x:S,y:E}),tp(this,S)||(this.processedData=M),!0}function ty(){var t,e,i,o,n,s=this,a=this.options||{},l=this.chart,h=l.boost,u=this.boost,d=this.xAxis,c=this.yAxis,f=a.xData||this.getColumn("x",!0),g=a.yData||this.getColumn("y",!0),p=this.getColumn("low",!0),m=this.getColumn("high",!0),b=this.processedData||a.data,x=d.getExtremes(),A=x.min-(d.minPointOffset||0),y=x.max+(d.minPointOffset||0),P=c.getExtremes(),T=P.min-(c.minPointOffset||0),C=P.max+(c.minPointOffset||0),M={},S=!!this.sampling,E=a.enableMouseTracking,k=a.threshold,w=this.pointArrayMap&&"low,high"===this.pointArrayMap.join(","),R=!!a.stacking,U=this.cropStart||0,L=this.requireSorting,N=!f,_="x"===a.findNearestPointBy,z=(this.getColumn("x").length?this.getColumn("x"):void 0)||this.options.xData||this.getColumn("x",!0),D=tn(a.lineWidth,1),I=a.nullInteraction&&T,G=!1,O=c.getThreshold(k);if(!d.isPanning&&!c.isPanning){if(G=td(l,this),l.boosted=!0,this.visible){(this.points||this.graph)&&tc(this),v(l)?(this.markerGroup&&this.markerGroup!==(null==h?void 0:h.markerGroup)&&this.markerGroup.destroy(),this.markerGroup=null==h?void 0:h.markerGroup,u&&u.target&&(this.renderTarget=u.target=u.target.destroy())):(this.markerGroup===(null==h?void 0:h.markerGroup)&&(this.markerGroup=void 0),this.markerGroup=this.plotGroup("markerGroup","markers","visible",1,l.seriesGroup).addClass("highcharts-tracker"));var B=this.points=[],V=function(t,e,i,o){var n=!!z&&z[U+i],s=function(t){l.inverted&&(t=d.len-t,e=c.len-e),B.push({destroy:Z,x:n,clientX:t,plotX:t,plotY:e,i:U+i,percentage:o})};t=Math.ceil(t),r=_?t:t+","+e,E&&(M[r]?n===z[z.length-1]&&(B.length--,s(t)):(M[r]=!0,s(t)))};this.buildKDTree=Z,ti(this,"renderCanvas"),this.is("line")&&D>1&&(null==u?void 0:u.target)&&h&&!h.lineWidthFilter&&(h.lineWidthFilter=l.renderer.definition({tagName:"filter",children:[{tagName:"feMorphology",attributes:{operator:"dilate",radius:.25*D}}],attributes:{id:"linewidth"}}),u.target.attr({filter:"url(#linewidth)"})),G&&(th(G,this),G.pushSeries(this),tb(this));var X=G.settings;l.renderer.forExport||(X.debug.timeKDTree&&console.time("kd tree building"),tf(R?this.data.slice(U):f||b,function(r,s){var a,h,u,f,v,b,x,P=void 0===l.index,M=!1,E=!0;return!tl(r)||(!P&&(N?(u=r[0],f=r[1]):(u=r,f=null!==(h=null!==(a=g[s])&&void 0!==a?a:I)&&void 0!==h?h:null),w?(N&&(f=r.slice(1,3)),M=p[s],f=m[s]):R&&(u=r.x,M=(f=r.stackY)-r.y,x=r.percentage),L||(E=(f||0)>=T&&f<=C),null!==f&&u>=A&&u<=y&&E&&(v=d.toPixels(u,!0),S?((void 0===o||v===t)&&(w||(M=f),(void 0===n||f>i)&&(i=f,n=s),(void 0===o||M<e)&&(e=M,o=s)),_&&v===t||(void 0!==o&&(b=c.toPixels(i,!0),O=c.toPixels(e,!0),V(v,b,n,x),O!==b&&V(v,O,o,x)),o=n=void 0,t=v)):V(v,b=Math.ceil(c.toPixels(f,!0)),s,x))),!P)},function(){ti(s,"renderedCanvas"),delete s.buildKDTree,s.options&&s.buildKDTree(),X.debug.timeKDTree&&console.timeEnd("kd tree building")}))}}}function tP(t){var e=!0;if(this.chart.options&&this.chart.options.boost&&(e=void 0===this.chart.options.boost.enabled||this.chart.options.boost.enabled),!e||!this.boosted)return t.call(this);this.chart.boosted=!0;var i=td(this.chart,this);i&&(th(i,this),i.pushSeries(this)),tb(this)}function tT(t){if(this.boosted){if(tg(this))return{};if(this.xAxis.isPanning||this.yAxis.isPanning)return this}return t.apply(this,[].slice.call(arguments,1))}function tC(t){var e,i,o,r=this.options.data;if(tu(this.chart)&&g[this.type]){var n=this.is("scatter")&&!this.is("bubble")&&!this.is("treemap")&&!this.is("heatmap");if(!tp(this,r)||n||this.is("treemap")||this.options.stacking||!tg(this,!0)){if(this.boosted&&((null===(e=this.xAxis)||void 0===e?void 0:e.isPanning)||(null===(i=this.yAxis)||void 0===i?void 0:i.isPanning)))return;n&&"treegrid"!==this.yAxis.type?tA.call(this,arguments[1]):t.apply(this,[].slice.call(arguments,1)),r=this.getColumn("x",!0)}if(this.boosted=tp(this,r),this.boosted){var s=void 0;null===(o=this.options.data)||void 0===o||!o.length||tr(s=this.getFirstValidPoint(this.options.data))||to(s)||this.is("treemap")||tt(12,!1,this.chart),function(t){t.boost=t.boost||{getPoint:function(e){return tx(t,e)}};var e,i=t.boost.altered=[];if(["allowDG","directTouch","stickyTracking"].forEach(function(e){i.push({prop:e,val:t[e],own:Object.hasOwnProperty.call(t,e)})}),t.allowDG=!1,t.directTouch=!1,t.stickyTracking=!0,t.finishedAnimating=!0,t.labelBySeries&&(t.labelBySeries=t.labelBySeries.destroy()),t.is("scatter")&&!t.is("treemap")&&t.data.length){for(var o=0,r=t.data;o<r.length;o++){var n=r[o];null===(e=null==n?void 0:n.destroy)||void 0===e||e.call(n)}t.data.length=0,t.points.length=0,delete t.processedData}}(this)}else!function(t){var e,i=t.boost,o=t.chart,r=o.boost;if(null==r?void 0:r.markerGroup){r.markerGroup.destroy(),r.markerGroup=void 0;for(var n=0,s=o.series;n<s.length;n++){var a=s[n];a.markerGroup=void 0,a.markerGroup=a.plotGroup("markerGroup","markers","visible",1,o.seriesGroup).addClass("highcharts-tracker")}}i&&((i.altered||[]).forEach(function(e){e.own?t[e.prop]=e.val:delete t[e.prop]}),i.clear&&i.clear()),null===(e=o.seriesGroup||t.group)||void 0===e||e.clip()}(this)}else t.apply(this,[].slice.call(arguments,1))}function tM(t){var e=t.apply(this,[].slice.call(arguments,1));return this.boost&&e?this.boost.getPoint(e):e}var tS=A().parse,tE=c().doc,tk=c().noop,tw=c().addEvent,tR=c().fireEvent,tU=c().isNumber,tL=c().merge,tN=c().pick,t_=c().wrap;!function(t){var e,i,o="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=";function r(t,e,i,o,r){r&&e!==r.clientX&&(t.moveTo(r.clientX,r.yBottom),t.lineTo(r.clientX,r.plotY),t.lineTo(e,i),t.lineTo(e,o))}function n(t,e,i,o,r){t.moveTo(e,i),t.arc(e,i,this.radii&&this.radii[r],0,2*Math.PI,!1)}function s(t,e,i,o){t.rect(e-1,i,1,o-i)}function a(){this.boost&&this.boost.copy&&this.boost.copy()}function l(){var t=this.boost||{};t.target&&t.target.attr({href:o}),t.canvas&&t.canvas.getContext("2d").clearRect(0,0,t.canvas.width,t.canvas.height)}function h(){v(this.chart)?this.boost&&this.boost.clear&&this.boost.clear():this.boost&&this.boost.copy?this.boost.copy():this.chart.boost&&this.chart.boost.copy&&this.chart.boost.copy()}function u(t,e,i){t.lineTo(e,i)}function d(){var t,e=this.chart,i=v(e)?e:this,r=i===e?e.seriesGroup:e.seriesGroup||this.group,n=e.chartWidth,s=e.chartHeight,a=function(t,e,i,o,r,n,s){t.call(this,i,e,o,r,n,s)},l=i.boost=i.boost||{};return t=l.targetCtx,l.canvas||(l.canvas=tE.createElement("canvas"),l.target=e.renderer.image("",0,0,n,s).addClass("highcharts-boost-canvas").add(r),t=l.targetCtx=l.canvas.getContext("2d"),e.inverted&&["moveTo","lineTo","rect","arc"].forEach(function(e){t_(t,e,a)}),l.copy=function(){l.target.attr({href:l.canvas.toDataURL("image/png")})},l.clear=function(){t.clearRect(0,0,l.canvas.width,l.canvas.height),i===l.target&&l.target.attr({href:o})},l.clipRect=e.renderer.clipRect(),l.target.clip(l.clipRect)),l.canvas.width!==n&&(l.canvas.width=n),l.canvas.height!==s&&(l.canvas.height=s),l.target.attr({x:0,y:0,width:n,height:s,style:"pointer-events: none",href:o}),l.clipRect&&l.clipRect.attr(b(e,i)),t}function f(){var t=this,e=t.options,r=t.chart,n=t.xAxis,s=t.yAxis,a=r.options.boost||{},l={timeRendering:a.timeRendering||!1,timeSeriesProcessing:a.timeSeriesProcessing||!1,timeSetup:a.timeSetup||!1},h=t.getColumn("x",!0),u=t.getColumn("y",!0),d=e.data,f=n.getExtremes(),g=f.min,p=f.max,m=s.getExtremes(),v=m.min,b=m.max,x={},y=!!t.sampling,P=e.marker&&e.marker.radius,T=t.cvsStrokeBatch||1e3,C=e.enableMouseTracking,M=e.threshold,S=tU(M),E=s.getThreshold(M),k=t.fill,w=t.pointArrayMap&&"low,high"===t.pointArrayMap.join(","),R=!!e.stacking,U=t.cropStart||0,L=r.options.loading,N=t.requireSorting,_=e.connectNulls,z=!h,D=R?t.data:h||d,I=t.fillOpacity?A().parse(t.color).setOpacity(tN(e.fillOpacity,.75)).get():t.color,G="x"===e.findNearestPointBy,O=this.boost||{},B=t.cvsDrawPoint,V=e.lineWidth?t.cvsLineTo:void 0,X=P&&P<=1?t.cvsMarkerSquare:t.cvsMarkerCircle;O.target&&O.target.attr({href:o}),(t.points||t.graph)&&tc(t),t.plotGroup("group","series",t.visible?"visible":"hidden",e.zIndex,r.seriesGroup),t.markerGroup=t.group,tw(t,"destroy",function(){t.markerGroup=null});var F=this.points=[],H=this.getContext();if(t.buildKDTree=tk,O.clear&&O.clear(),t.visible){d.length>99999&&(r.options.loading=tL(L,{labelStyle:{backgroundColor:tS("#ffffff").setOpacity(.75).get(),padding:"1em",borderRadius:"0.5em"},style:{backgroundColor:"none",opacity:1}}),c().clearTimeout(i),r.showLoading("Drawing..."),r.options.loading=L),l.timeRendering&&console.time("canvas rendering");var Y,W,j,q,K,Z,Q,J,$=0,tt=E,te=function(){k?(H.fillStyle=I,H.fill()):(H.strokeStyle=t.color,H.lineWidth=e.lineWidth,H.stroke())},ti=function(e,i,o,n){0===$&&(H.beginPath(),V&&(H.lineJoin="round")),r.scroller&&"highcharts-navigator-series"===t.options.className?(i+=r.scroller.top,o&&(o+=r.scroller.top)):i+=r.plotTop,e+=r.plotLeft,j?H.moveTo(e,i):B?B(H,e,i,o,W):V?V(H,e,i):X&&X.call(t,H,e,i,P,n),($+=1)===T&&(te(),$=0),W={clientX:e,plotY:i,yBottom:o}},to=(this.getColumn("x").length?this.getColumn("x"):void 0)||this.options.xData||!!this.getColumn("x",!0).length&&this.getColumn("x",!0),tr=function(t,e,i){J=G?t:t+","+e,C&&!x[J]&&(x[J]=!0,r.inverted&&(t=n.len-t,e=s.len-e),F.push({x:!!to&&to[U+i],clientX:t,plotX:t,plotY:e,i:U+i}))};tf(D,function(e,i){var o,a,l,h,d,c,f=void 0===r.index,m=!1,x=!1,A=NaN,P=NaN,T=!0;return!f&&(z?(o=e[0],a=e[1],D[i+1]&&(A=D[i+1][0]),D[i-1]&&(P=D[i-1][0])):(o=e,a=u[i],D[i+1]&&(A=D[i+1]),D[i-1]&&(P=D[i-1])),A&&A>=g&&A<=p&&(m=!0),P&&P>=g&&P<=p&&(x=!0),w?(z&&(a=e.slice(1,3)),c=a[0],a=a[1]):R&&(o=e.x,c=(a=e.stackY)-e.y),d=null===a,N||(T=a>=v&&a<=b),!d&&(o>=g&&o<=p&&T||m||x)&&(l=Math.round(n.toPixels(o,!0)),y?((void 0===Z||l===Y)&&(w||(c=a),(void 0===Q||a>K)&&(K=a,Q=i),(void 0===Z||c<q)&&(q=c,Z=i)),l!==Y&&(void 0!==Z&&(h=s.toPixels(K,!0),tt=s.toPixels(q,!0),ti(l,S?Math.min(h,E):h,S?Math.max(tt,E):tt,i),tr(l,h,Q),tt!==h&&tr(l,tt,Z)),Z=Q=void 0,Y=l)):(ti(l,h=Math.round(s.toPixels(a,!0)),tt,i),tr(l,h,i))),j=d&&!_,i%5e4==0&&(t.boost&&t.boost.copy?t.boost.copy():t.chart.boost&&t.chart.boost.copy&&t.chart.boost.copy())),!f},function(){var e=r.loadingDiv,o=r.loadingShown;te(),t.canvasToSVG(),l.timeRendering&&console.timeEnd("canvas rendering"),tR(t,"renderedCanvas"),o&&(e.style.transition="opacity 250ms",e.opacity=0,r.loadingShown=!1,i=setTimeout(function(){e.parentNode&&e.parentNode.removeChild(e),r.loadingDiv=r.loadingSpan=null},250)),delete t.buildKDTree,t.buildKDTree()},r.renderer.forExport?Number.MAX_VALUE:void 0)}}function g(t,e,i,o){t.moveTo(e,i),t.arc(e,i,o,0,2*Math.PI,!1)}function p(t,e,i,o){t.rect(e-o,i-o,2*o,2*o)}function m(){var t=this.chart,e=this.getContext(),i=this.chart.inverted,o=this.xAxis,r=this.yAxis;e?(this.points.forEach(function(n){var s,a=n.plotY;if(void 0!==a&&!isNaN(a)&&null!==n.y&&e){var l=n.shapeArgs||{},h=l.x,u=void 0===h?0:h,d=l.y,c=void 0===d?0:d,f=l.width,g=void 0===f?0:f,p=l.height,m=void 0===p?0:p;e.fillStyle=(t.styledMode?n.series.colorAttribs(n):n.series.pointAttribs(n)).fill,i?e.fillRect(r.len-c+o.left,o.len-u+r.top,-m,-g):e.fillRect(u+o.left,c+r.top,g,m)}}),this.canvasToSVG()):this.chart.showLoading("Your browser doesn't support HTML5 canvas, <br>please use a modern browser")}t.compose=function(t,e,i){var o=e.prototype;if(!o.renderCanvas){var c=i.area,v=i.bubble,b=i.column,x=i.heatmap,A=i.scatter;if(t.prototype.callbacks.push(function(t){tw(t,"predraw",l),tw(t,"render",a)}),o.canvasToSVG=h,o.cvsLineTo=u,o.getContext=d,o.renderCanvas=f,c){var y=c.prototype;y.cvsDrawPoint=r,y.fill=!0,y.fillOpacity=!0,y.sampling=!0}if(v){var P=v.prototype;P.cvsMarkerCircle=n,P.cvsStrokeBatch=1}if(b){var T=b.prototype;T.cvsDrawPoint=s,T.fill=!0,T.sampling=!0}if(x&&t_(x.prototype,"drawPoints",m),A){var C=A.prototype;C.cvsMarkerCircle=g,C.cvsMarkerSquare=p,C.fill=!0}}}}(s||(s={}));var tz=s,tD=c();tD.initCanvasBoost=function(){tz.compose(tD.Chart,tD.Series,tD.seriesTypes)};var tI=c();return u.default}()});