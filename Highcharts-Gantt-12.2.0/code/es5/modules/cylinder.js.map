{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/cylinder\n * @requires highcharts\n * @requires highcharts/highcharts-3d\n *\n * Highcharts cylinder module\n *\n * (c) 2010-2025 Kacper Madej\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"Color\"], require(\"highcharts\")[\"RendererRegistry\"], require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/cylinder\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"Color\"], [\"highcharts/highcharts\",\"RendererRegistry\"], [\"highcharts/highcharts\",\"SeriesRegistry\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/cylinder\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"Color\"], require(\"highcharts\")[\"RendererRegistry\"], require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Color\"], root[\"Highcharts\"][\"RendererRegistry\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__620__, __WEBPACK_EXTERNAL_MODULE__608__, __WEBPACK_EXTERNAL_MODULE__512__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 608:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__608__;\n\n/***/ }),\n\n/***/ 620:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__620__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ cylinder_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es5/es-modules/Core/Math3D.js\n/* *\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar deg2rad = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).deg2rad;\n\nvar pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Functions\n *\n * */\n/* eslint-disable max-len */\n/**\n * Apply 3-D rotation\n * Euler Angles (XYZ):\n *     cosA = cos(Alfa|Roll)\n *     cosB = cos(Beta|Pitch)\n *     cosG = cos(Gamma|Yaw)\n *\n * Composite rotation:\n * |          cosB * cosG             |           cosB * sinG            |    -sinB    |\n * | sinA * sinB * cosG - cosA * sinG | sinA * sinB * sinG + cosA * cosG | sinA * cosB |\n * | cosA * sinB * cosG + sinA * sinG | cosA * sinB * sinG - sinA * cosG | cosA * cosB |\n *\n * Now, Gamma/Yaw is not used (angle=0), so we assume cosG = 1 and sinG = 0, so\n * we get:\n * |     cosB    |   0    |   - sinB    |\n * | sinA * sinB |  cosA  | sinA * cosB |\n * | cosA * sinB | - sinA | cosA * cosB |\n *\n * But in browsers, y is reversed, so we get sinA => -sinA. The general result\n * is:\n * |      cosB     |   0    |    - sinB     |     | x |     | px |\n * | - sinA * sinB |  cosA  | - sinA * cosB |  x  | y |  =  | py |\n * |  cosA * sinB  |  sinA  |  cosA * cosB  |     | z |     | pz |\n *\n * @private\n * @function rotate3D\n */\n/* eslint-enable max-len */\n/**\n * Rotates the position as defined in angles.\n * @private\n * @param {number} x\n *        X coordinate\n * @param {number} y\n *        Y coordinate\n * @param {number} z\n *        Z coordinate\n * @param {Highcharts.Rotation3DObject} angles\n *        Rotation angles\n * @return {Highcharts.Position3DObject}\n *         Rotated position\n */\nfunction rotate3D(x, y, z, angles) {\n    return {\n        x: angles.cosB * x - angles.sinB * z,\n        y: -angles.sinA * angles.sinB * x + angles.cosA * y -\n            angles.cosB * angles.sinA * z,\n        z: angles.cosA * angles.sinB * x + angles.sinA * y +\n            angles.cosA * angles.cosB * z\n    };\n}\n/**\n * Transforms a given array of points according to the angles in chart.options.\n *\n * @private\n * @function Highcharts.perspective\n *\n * @param {Array<Highcharts.Position3DObject>} points\n * The array of points\n *\n * @param {Highcharts.Chart} chart\n * The chart\n *\n * @param {boolean} [insidePlotArea]\n * Whether to verify that the points are inside the plotArea\n *\n * @param {boolean} [useInvertedPersp]\n * Whether to use inverted perspective in calculations\n *\n * @return {Array<Highcharts.Position3DObject>}\n * An array of transformed points\n *\n * @requires highcharts-3d\n */\nfunction perspective(points, chart, insidePlotArea, useInvertedPersp) {\n    var options3d = chart.options.chart.options3d, \n        /* The useInvertedPersp argument is used for inverted charts with\n         * already inverted elements,\n        such as dataLabels or tooltip positions.\n         */\n        inverted = pick(useInvertedPersp,\n        insidePlotArea ? chart.inverted : false),\n        origin = {\n            x: chart.plotWidth / 2,\n            y: chart.plotHeight / 2,\n            z: options3d.depth / 2,\n            vd: pick(options3d.depth, 1) * pick(options3d.viewDistance, 0)\n        },\n        scale = chart.scale3d || 1,\n        beta = deg2rad * options3d.beta * (inverted ? -1 : 1),\n        alpha = deg2rad * options3d.alpha * (inverted ? -1 : 1),\n        angles = {\n            cosA: Math.cos(alpha),\n            cosB: Math.cos(-beta),\n            sinA: Math.sin(alpha),\n            sinB: Math.sin(-beta)\n        };\n    if (!insidePlotArea) {\n        origin.x += chart.plotLeft;\n        origin.y += chart.plotTop;\n    }\n    // Transform each point\n    return points.map(function (point) {\n        var rotated = rotate3D((inverted ? point.y : point.x) - origin.x, (inverted ? point.x : point.y) - origin.y, (point.z || 0) - origin.z,\n            angles), \n            // Apply perspective\n            coordinate = perspective3D(rotated,\n            origin,\n            origin.vd);\n        // Apply translation\n        coordinate.x = coordinate.x * scale + origin.x;\n        coordinate.y = coordinate.y * scale + origin.y;\n        coordinate.z = rotated.z * scale + origin.z;\n        return {\n            x: (inverted ? coordinate.y : coordinate.x),\n            y: (inverted ? coordinate.x : coordinate.y),\n            z: coordinate.z\n        };\n    });\n}\n/**\n * Perspective3D function is available in global Highcharts scope because is\n * needed also outside of perspective() function (#8042).\n * @private\n * @function Highcharts.perspective3D\n *\n * @param {Highcharts.Position3DObject} coordinate\n * 3D position\n *\n * @param {Highcharts.Position3DObject} origin\n * 3D root position\n *\n * @param {number} distance\n * Perspective distance\n *\n * @return {Highcharts.PositionObject}\n * Perspective 3D Position\n *\n * @requires highcharts-3d\n */\nfunction perspective3D(coordinate, origin, distance) {\n    var projection = ((distance > 0) &&\n            (distance < Number.POSITIVE_INFINITY)) ?\n            distance / (coordinate.z + origin.z + distance) :\n            1;\n    return {\n        x: coordinate.x * projection,\n        y: coordinate.y * projection\n    };\n}\n/**\n * Calculate a distance from camera to points - made for calculating zIndex of\n * scatter points.\n *\n * @private\n * @function Highcharts.pointCameraDistance\n *\n * @param {Highcharts.Dictionary<number>} coordinates\n * Coordinates of the specific point\n *\n * @param {Highcharts.Chart} chart\n * Related chart\n *\n * @return {number}\n * Distance from camera to point\n *\n * @requires highcharts-3d\n */\nfunction pointCameraDistance(coordinates, chart) {\n    var options3d = chart.options.chart.options3d,\n        cameraPosition = {\n            x: chart.plotWidth / 2,\n            y: chart.plotHeight / 2,\n            z: pick(options3d.depth, 1) * pick(options3d.viewDistance, 0) +\n                options3d.depth\n        }, \n        // Added support for objects with plotX or x coordinates.\n        distance = Math.sqrt(Math.pow(cameraPosition.x - pick(coordinates.plotX,\n        coordinates.x), 2) +\n            Math.pow(cameraPosition.y - pick(coordinates.plotY,\n        coordinates.y), 2) +\n            Math.pow(cameraPosition.z - pick(coordinates.plotZ,\n        coordinates.z), 2));\n    return distance;\n}\n/**\n * Calculate area of a 2D polygon using Shoelace algorithm\n * https://en.wikipedia.org/wiki/Shoelace_formula\n *\n * @private\n * @function Highcharts.shapeArea\n *\n * @param {Array<Highcharts.PositionObject>} vertexes\n * 2D Polygon\n *\n * @return {number}\n * Calculated area\n *\n * @requires highcharts-3d\n */\nfunction shapeArea(vertexes) {\n    var area = 0,\n        i,\n        j;\n    for (i = 0; i < vertexes.length; i++) {\n        j = (i + 1) % vertexes.length;\n        area += vertexes[i].x * vertexes[j].y - vertexes[j].x * vertexes[i].y;\n    }\n    return area / 2;\n}\n/**\n * Calculate area of a 3D polygon after perspective projection\n *\n * @private\n * @function Highcharts.shapeArea3d\n *\n * @param {Array<Highcharts.Position3DObject>} vertexes\n * 3D Polygon\n *\n * @param {Highcharts.Chart} chart\n * Related chart\n *\n * @param {boolean} [insidePlotArea]\n * Whether to verify that the points are inside the plotArea\n *\n * @return {number}\n * Calculated area\n *\n * @requires highcharts-3d\n */\nfunction shapeArea3D(vertexes, chart, insidePlotArea) {\n    return shapeArea(perspective(vertexes, chart, insidePlotArea));\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar Math3D = {\n    perspective: perspective,\n    perspective3D: perspective3D,\n    pointCameraDistance: pointCameraDistance,\n    shapeArea: shapeArea,\n    shapeArea3D: shapeArea3D\n};\n/* harmony default export */ var Core_Math3D = (Math3D);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Color\"],\"commonjs\":[\"highcharts\",\"Color\"],\"commonjs2\":[\"highcharts\",\"Color\"],\"root\":[\"Highcharts\",\"Color\"]}\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_ = __webpack_require__(620);\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default = /*#__PURE__*/__webpack_require__.n(highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"RendererRegistry\"],\"commonjs\":[\"highcharts\",\"RendererRegistry\"],\"commonjs2\":[\"highcharts\",\"RendererRegistry\"],\"root\":[\"Highcharts\",\"RendererRegistry\"]}\nvar highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_ = __webpack_require__(608);\nvar highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_);\n;// ./code/es5/es-modules/Series/Cylinder/SVGElement3DCylinder.js\n/* *\n *\n *  Highcharts cylinder - a 3D series\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Kacper Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar color = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default()).parse;\n\nvar SVGElement3D = highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_default().getRendererType().prototype.Element3D;\n/* *\n *\n *  Class\n *\n * */\nvar SVGElement3DCylinder = /** @class */ (function (_super) {\n    __extends(SVGElement3DCylinder, _super);\n    function SVGElement3DCylinder() {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        var _this = _super !== null && _super.apply(this,\n            arguments) || this;\n        _this.parts = ['top', 'bottom', 'front', 'back'];\n        _this.pathType = 'cylinder';\n        return _this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    SVGElement3DCylinder.prototype.fillSetter = function (fill) {\n        this.singleSetterForParts('fill', null, {\n            front: fill,\n            back: fill,\n            top: color(fill).brighten(0.1).get(),\n            bottom: color(fill).brighten(-0.1).get()\n        });\n        // Fill for animation getter (#6776)\n        this.color = this.fill = fill;\n        return this;\n    };\n    return SVGElement3DCylinder;\n}(SVGElement3D));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Cylinder_SVGElement3DCylinder = (SVGElement3DCylinder);\n\n;// ./code/es5/es-modules/Series/Cylinder/CylinderComposition.js\n/* *\n *\n *  Highcharts cylinder - a 3D series\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Kacper Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar charts = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).charts, CylinderComposition_deg2rad = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).deg2rad;\n\nvar CylinderComposition_perspective = Core_Math3D.perspective;\n\n\nvar extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, CylinderComposition_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Functions\n *\n * */\n/**\n *\n */\nfunction compose(SVGRendererClass) {\n    var rendererProto = SVGRendererClass.prototype;\n    if (!rendererProto.cylinder) {\n        rendererProto.Element3D.types.cylinder = Cylinder_SVGElement3DCylinder;\n        extend(rendererProto, {\n            cylinder: rendererCylinder,\n            cylinderPath: rendererCylinderPath,\n            getCurvedPath: rendererGetCurvedPath,\n            getCylinderBack: rendererGetCylinderBack,\n            getCylinderEnd: rendererGetCylinderEnd,\n            getCylinderFront: rendererGetCylinderFront\n        });\n    }\n}\n/**\n * Check if a path is simplified. The simplified path contains only lineTo\n * segments, whereas non-simplified contain curves.\n * @private\n */\nfunction isSimplified(path) {\n    return !path.some(function (seg) { return seg[0] === 'C'; });\n}\n/** @private */\nfunction rendererCylinder(shapeArgs) {\n    return this.element3d('cylinder', shapeArgs);\n}\n/**\n * Generates paths and zIndexes.\n * @private\n */\nfunction rendererCylinderPath(shapeArgs) {\n    var renderer = this,\n        chart = charts[renderer.chartIndex], \n        // Decide zIndexes of parts based on cuboid logic, for consistency.\n        cuboidData = this.cuboidPath(shapeArgs),\n        isTopFirst = !cuboidData.isTop,\n        isFronFirst = !cuboidData.isFront,\n        top = renderer.getCylinderEnd(chart,\n        shapeArgs),\n        bottom = renderer.getCylinderEnd(chart,\n        shapeArgs,\n        true);\n    return {\n        front: renderer.getCylinderFront(top, bottom),\n        back: renderer.getCylinderBack(top, bottom),\n        top: top,\n        bottom: bottom,\n        zIndexes: {\n            top: isTopFirst ? 3 : 0,\n            bottom: isTopFirst ? 0 : 3,\n            front: isFronFirst ? 2 : 1,\n            back: isFronFirst ? 1 : 2,\n            group: cuboidData.zIndexes.group\n        }\n    };\n}\n/**\n * Returns curved path in format of:\n * [ M, x, y, ...[C, cp1x, cp2y, cp2x, cp2y, epx, epy]*n_times ]\n * (cp - control point, ep - end point)\n * @private\n */\nfunction rendererGetCurvedPath(points) {\n    var path = [['M',\n        points[0].x,\n        points[0].y]],\n        limit = points.length - 2;\n    for (var i = 1; i < limit; i += 3) {\n        path.push([\n            'C',\n            points[i].x, points[i].y,\n            points[i + 1].x, points[i + 1].y,\n            points[i + 2].x, points[i + 2].y\n        ]);\n    }\n    return path;\n}\n/**\n * Returns cylinder Back path.\n * @private\n */\nfunction rendererGetCylinderBack(topPath, bottomPath) {\n    var path = [];\n    if (isSimplified(topPath)) {\n        var move = topPath[0],\n            line2 = topPath[2];\n        if (move[0] === 'M' && line2[0] === 'L') {\n            path.push(['M', line2[1], line2[2]]);\n            path.push(topPath[3]);\n            // End at start\n            path.push(['L', move[1], move[2]]);\n        }\n    }\n    else {\n        if (topPath[2][0] === 'C') {\n            path.push(['M', topPath[2][5], topPath[2][6]]);\n        }\n        path.push(topPath[3], topPath[4]);\n    }\n    if (isSimplified(bottomPath)) {\n        var move = bottomPath[0];\n        if (move[0] === 'M') {\n            path.push(['L', move[1], move[2]]);\n            path.push(bottomPath[3]);\n            path.push(bottomPath[2]);\n        }\n    }\n    else {\n        var curve2 = bottomPath[2],\n            curve3 = bottomPath[3],\n            curve4 = bottomPath[4];\n        if (curve2[0] === 'C' && curve3[0] === 'C' && curve4[0] === 'C') {\n            path.push(['L', curve4[5], curve4[6]]);\n            path.push([\n                'C',\n                curve4[3],\n                curve4[4],\n                curve4[1],\n                curve4[2],\n                curve3[5],\n                curve3[6]\n            ]);\n            path.push([\n                'C',\n                curve3[3],\n                curve3[4],\n                curve3[1],\n                curve3[2],\n                curve2[5],\n                curve2[6]\n            ]);\n        }\n    }\n    path.push(['Z']);\n    return path;\n}\n/**\n * Returns cylinder path for top or bottom.\n * @private\n */\nfunction rendererGetCylinderEnd(chart, shapeArgs, isBottom) {\n    var _a = shapeArgs.width, width = _a === void 0 ? 0 : _a, _b = shapeArgs.height, height = _b === void 0 ? 0 : _b, _c = shapeArgs.alphaCorrection, alphaCorrection = _c === void 0 ? 0 : _c, \n        // A half of the smaller one out of width or depth (optional, because\n        // there's no depth for a funnel that reuses the code)\n        depth = CylinderComposition_pick(shapeArgs.depth, width, 0), radius = Math.min(width, depth) / 2, \n        // Approximated longest diameter\n        angleOffset = CylinderComposition_deg2rad * (chart.options.chart.options3d.beta - 90 +\n            alphaCorrection), \n        // Could be top or bottom of the cylinder\n        y = (shapeArgs.y || 0) + (isBottom ? height : 0), \n        // Use cubic Bezier curve to draw a circle in x,z (y is constant).\n        // More math. at spencermortensen.com/articles/bezier-circle/\n        c = 0.5519 * radius, centerX = width / 2 + (shapeArgs.x || 0), centerZ = depth / 2 + (shapeArgs.z || 0), \n        // Points could be generated in a loop, but readability will plummet\n        points = [{\n                x: 0,\n                y: y,\n                z: radius\n            }, {\n                x: c,\n                y: y,\n                z: radius\n            }, {\n                x: radius,\n                y: y,\n                z: c\n            }, {\n                x: radius,\n                y: y,\n                z: 0\n            }, {\n                x: radius,\n                y: y,\n                z: -c\n            }, {\n                x: c,\n                y: y,\n                z: -radius\n            }, {\n                x: 0,\n                y: y,\n                z: -radius\n            }, {\n                x: -c,\n                y: y,\n                z: -radius\n            }, {\n                x: -radius,\n                y: y,\n                z: -c\n            }, {\n                x: -radius,\n                y: y,\n                z: 0\n            }, {\n                x: -radius,\n                y: y,\n                z: c\n            }, {\n                x: -c,\n                y: y,\n                z: radius\n            }, {\n                x: 0,\n                y: y,\n                z: radius\n            }], cosTheta = Math.cos(angleOffset), sinTheta = Math.sin(angleOffset);\n    var path,\n        x,\n        z;\n    // Rotate to match chart's beta and translate to the shape center\n    for (var _i = 0, points_1 = points; _i < points_1.length; _i++) {\n        var point = points_1[_i];\n        x = point.x;\n        z = point.z;\n        point.x = (x * cosTheta - z * sinTheta) + centerX;\n        point.z = (z * cosTheta + x * sinTheta) + centerZ;\n    }\n    var perspectivePoints = CylinderComposition_perspective(points,\n        chart,\n        true);\n    // Check for sub-pixel curve issue, compare front and back edges\n    if (Math.abs(perspectivePoints[3].y - perspectivePoints[9].y) < 2.5 &&\n        Math.abs(perspectivePoints[0].y - perspectivePoints[6].y) < 2.5) {\n        // Use simplified shape\n        path = this.toLinePath([\n            perspectivePoints[0],\n            perspectivePoints[3],\n            perspectivePoints[6],\n            perspectivePoints[9]\n        ], true);\n    }\n    else {\n        // Or default curved path to imitate ellipse (2D circle)\n        path = this.getCurvedPath(perspectivePoints);\n    }\n    return path;\n}\n/**\n * Returns cylinder Front path.\n * @private\n */\nfunction rendererGetCylinderFront(topPath, bottomPath) {\n    var path = topPath.slice(0, 3);\n    if (isSimplified(bottomPath)) {\n        var move = bottomPath[0];\n        if (move[0] === 'M') {\n            path.push(bottomPath[2]);\n            path.push(bottomPath[1]);\n            path.push(['L', move[1], move[2]]);\n        }\n    }\n    else {\n        var move = bottomPath[0],\n            curve1 = bottomPath[1],\n            curve2 = bottomPath[2];\n        if (move[0] === 'M' && curve1[0] === 'C' && curve2[0] === 'C') {\n            path.push(['L', curve2[5], curve2[6]]);\n            path.push([\n                'C',\n                curve2[3],\n                curve2[4],\n                curve2[1],\n                curve2[2],\n                curve1[5],\n                curve1[6]\n            ]);\n            path.push([\n                'C',\n                curve1[3],\n                curve1[4],\n                curve1[1],\n                curve1[2],\n                move[1],\n                move[2]\n            ]);\n        }\n    }\n    path.push(['Z']);\n    return path;\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar CylinderComposition = {\n    compose: compose\n};\n/* harmony default export */ var Cylinder_CylinderComposition = (CylinderComposition);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es5/es-modules/Series/Cylinder/CylinderPoint.js\n/* *\n *\n *  Highcharts cylinder - a 3D series\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Kacper Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar CylinderPoint_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar ColumnPoint = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.column.prototype.pointClass;\n\nvar CylinderPoint_extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend;\n/* *\n *\n *  Class\n *\n * */\nvar CylinderPoint = /** @class */ (function (_super) {\n    CylinderPoint_extends(CylinderPoint, _super);\n    function CylinderPoint() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    return CylinderPoint;\n}(ColumnPoint));\nCylinderPoint_extend(CylinderPoint.prototype, {\n    shapeType: 'cylinder'\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Cylinder_CylinderPoint = (CylinderPoint);\n\n;// ./code/es5/es-modules/Series/Cylinder/CylinderSeriesDefaults.js\n/* *\n *\n *  Highcharts cylinder - a 3D series\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Kacper Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A cylinder graph is a variation of a 3d column graph. The cylinder graph\n * features cylindrical points.\n *\n * @sample {highcharts} highcharts/demo/cylinder/\n *         Cylinder graph\n *\n * @extends      plotOptions.column\n * @since        7.0.0\n * @product      highcharts\n * @excluding    allAreas, boostThreshold, colorAxis, compare, compareBase,\n *               dragDrop, boostBlending\n * @requires     modules/cylinder\n * @optionparent plotOptions.cylinder\n */\nvar CylinderSeriesDefaults = {};\n/**\n * A `cylinder` series. If the [type](#series.cylinder.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.cylinder\n * @since     7.0.0\n * @product   highcharts\n * @excluding allAreas, boostThreshold, colorAxis, compare, compareBase,\n *            boostBlending\n * @requires  modules/cylinder\n * @apioption series.cylinder\n */\n/**\n * An array of data points for the series. For the `cylinder` series type,\n * points can be given in the following ways:\n *\n * 1. An array of numerical values. In this case, the numerical values will be\n *    interpreted as `y` options. The `x` values will be automatically\n *    calculated, either starting at 0 and incremented by 1, or from\n *    `pointStart` and `pointInterval` given in the series options. If the axis\n *    has categories, these will be used. Example:\n *    ```js\n *    data: [0, 5, 3, 5]\n *    ```\n *\n * 2. An array of arrays with 2 values. In this case, the values correspond to\n *    `x,y`. If the first value is a string, it is applied as the name of the\n *    point, and the `x` value is inferred.\n *    ```js\n *    data: [\n *        [0, 0],\n *        [1, 8],\n *        [2, 9]\n *    ]\n *    ```\n *\n * 3. An array of objects with named values. The following snippet shows only a\n *    few settings, see the complete options set below. If the total number of\n *    data points exceeds the series'\n *    [turboThreshold](#series.cylinder.turboThreshold), this option is not\n *    available.\n *\n *    ```js\n *    data: [{\n *        x: 1,\n *        y: 2,\n *        name: \"Point2\",\n *        color: \"#00FF00\"\n *    }, {\n *        x: 1,\n *        y: 4,\n *        name: \"Point1\",\n *        color: \"#FF00FF\"\n *    }]\n *    ```\n *\n * @sample {highcharts} highcharts/chart/reflow-true/\n *         Numerical values\n * @sample {highcharts} highcharts/series/data-array-of-arrays/\n *         Arrays of numeric x and y\n * @sample {highcharts} highcharts/series/data-array-of-arrays-datetime/\n *         Arrays of datetime x and y\n * @sample {highcharts} highcharts/series/data-array-of-name-value/\n *         Arrays of point.name and y\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<number|Array<(number|string),(number|null)>|null|*>}\n * @extends   series.column.data\n * @product   highcharts highstock\n * @apioption series.cylinder.data\n */\n''; // Detaches doclets above\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Cylinder_CylinderSeriesDefaults = (CylinderSeriesDefaults);\n\n;// ./code/es5/es-modules/Series/Cylinder/CylinderSeries.js\n/* *\n *\n *  Highcharts cylinder - a 3D series\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Kacper Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar CylinderSeries_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\n\n\nvar ColumnSeries = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.column;\n\nvar CylinderSeries_extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n *  Class\n *\n * */\n/**\n * The cylinder series type.\n *\n * @requires highcharts-3d\n * @requires modules/cylinder\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.cylinder\n *\n * @augments Highcharts.Series\n */\nvar CylinderSeries = /** @class */ (function (_super) {\n    CylinderSeries_extends(CylinderSeries, _super);\n    function CylinderSeries() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    CylinderSeries.compose = Cylinder_CylinderComposition.compose;\n    CylinderSeries.defaultOptions = merge(ColumnSeries.defaultOptions, Cylinder_CylinderSeriesDefaults);\n    return CylinderSeries;\n}(ColumnSeries));\nCylinderSeries_extend(CylinderSeries.prototype, {\n    pointClass: Cylinder_CylinderPoint\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('cylinder', CylinderSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Cylinder_CylinderSeries = (CylinderSeries);\n\n;// ./code/es5/es-modules/masters/modules/cylinder.js\n\n\n\n\n\nCylinder_CylinderSeries.compose(highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_default().getRendererType());\n/* harmony default export */ var cylinder_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__620__", "__WEBPACK_EXTERNAL_MODULE__608__", "__WEBPACK_EXTERNAL_MODULE__512__", "extendStatics", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "cylinder_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "deg2rad", "pick", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default", "highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_", "highcharts_RendererRegistry_commonjs_highcharts_RendererRegistry_commonjs2_highcharts_RendererRegistry_root_Highcharts_RendererRegistry_default", "__extends", "b", "setPrototypeOf", "__proto__", "Array", "p", "__", "constructor", "create", "color", "parse", "SVGElement3DC<PERSON>inder", "_super", "_this", "apply", "arguments", "parts", "pathType", "fillSetter", "fill", "singleSetterForParts", "front", "back", "top", "brighten", "bottom", "getRendererType", "Element3D", "charts", "CylinderComposition_deg2rad", "extend", "CylinderComposition_pick", "isSimplified", "path", "some", "seg", "rendererCylinder", "shapeArgs", "element3d", "rendererCylinderPath", "chart", "renderer", "chartIndex", "cuboidData", "cuboidPath", "isTopFirst", "isTop", "isFronFirst", "isFront", "getCylinderEnd", "getCylinderFront", "getCylinderBack", "zIndexes", "group", "rendererGetCurvedPath", "points", "x", "y", "limit", "length", "i", "push", "rendererGetCylinderBack", "topPath", "bottomPath", "move", "line2", "curve2", "curve3", "curve4", "rendererGetCylinderEnd", "isBottom", "insidePlotArea", "useInvertedPersp", "options3d", "inverted", "origin", "scale", "beta", "alpha", "angles", "z", "_a", "width", "_b", "height", "_c", "alphaCorrection", "depth", "radius", "Math", "min", "angleOffset", "options", "c", "centerX", "centerZ", "cosTheta", "cos", "sinTheta", "sin", "_i", "points_1", "point", "perspectivePoints", "plot<PERSON>id<PERSON>", "plotHeight", "vd", "viewDistance", "scale3d", "cosA", "cosB", "sinA", "sinB", "plotLeft", "plotTop", "map", "rotated", "coordinate", "perspective3D", "distance", "projection", "Number", "POSITIVE_INFINITY", "abs", "to<PERSON><PERSON><PERSON><PERSON>", "get<PERSON><PERSON>ved<PERSON><PERSON>", "rendererGetCylinderFront", "slice", "curve1", "SVGRendererClass", "rendererProto", "cylinder", "types", "cylinderPath", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "CylinderPoint_extends", "TypeError", "String", "ColumnPoint", "seriesTypes", "column", "pointClass", "CylinderPoint_extend", "CylinderPoint", "shapeType", "Cylinder_CylinderSeriesDefaults", "CylinderSeries_extends", "ColumnSeries", "CylinderSeries_extend", "merge", "CylinderSeries", "compose", "defaultOptions", "registerSeriesType", "Cylinder_CylinderSeries"], "mappings": "CAYA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,KAAQ,CAAEA,QAAQ,cAAc,gBAAmB,CAAEA,QAAQ,cAAc,cAAiB,EAC3J,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,8BAA+B,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,QAAQ,CAAE,CAAC,wBAAwB,mBAAmB,CAAE,CAAC,wBAAwB,iBAAiB,CAAC,CAAEJ,GACzL,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,8BAA8B,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,KAAQ,CAAEA,QAAQ,cAAc,gBAAmB,CAAEA,QAAQ,cAAc,cAAiB,EAE1LJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,gBAAmB,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAC5J,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,EACxJ,OAAgB,AAAC,WACP,aACA,IAmYFC,EAmZAA,EAgLAA,EAt8BMC,EAAuB,CAE/B,IACC,SAAST,CAAM,EAEtBA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,SAASP,CAAM,EAEtBA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,SAASN,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGIM,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAad,OAAO,CAG5B,IAAIC,EAASU,CAAwB,CAACE,EAAS,CAAG,CAGjDb,QAAS,CAAC,CACX,EAMA,OAHAU,CAAmB,CAACG,EAAS,CAACZ,EAAQA,EAAOD,OAAO,CAAEY,GAG/CX,EAAOD,OAAO,AACtB,CAMCY,EAAoBI,CAAC,CAAG,SAASf,CAAM,EACtC,IAAIgB,EAAShB,GAAUA,EAAOiB,UAAU,CACvC,WAAa,OAAOjB,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAW,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASnB,CAAO,CAAEqB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACvB,EAASsB,IAC5EE,OAAOC,cAAc,CAACzB,EAASsB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,CAAc,CAC7D,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAajHE,EAAU,AAACD,IAA+EC,OAAO,CAEjGC,EAAO,AAACF,IAA+EE,IAAI,CA+P3FC,EAA+F3B,EAAoB,KACnH4B,EAAmH5B,EAAoBI,CAAC,CAACuB,GAEzIE,EAA2I7B,EAAoB,KAC/J8B,EAA+J9B,EAAoBI,CAAC,CAACyB,GAgBrLE,GACIlC,EAAgB,SAAUU,CAAC,CAC3ByB,CAAC,EAMD,MAAOnC,AALHA,CAAAA,EAAgBe,OAAOqB,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAU5B,CAAC,CAC1DyB,CAAC,EAAIzB,EAAE2B,SAAS,CAAGF,CAAG,GACd,SAAUzB,CAAC,CACnByB,CAAC,EAAI,IAAK,IAAII,KAAKJ,EAAOA,EAAEb,cAAc,CAACiB,IAAI7B,CAAAA,CAAC,CAAC6B,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,CAAG,CAAA,EACvC7B,EAAGyB,EAC5B,EACO,SAAUzB,CAAC,CAAEyB,CAAC,EAEjB,SAASK,IAAO,IAAI,CAACC,WAAW,CAAG/B,CAAG,CADtCV,EAAcU,EAAGyB,GAEjBzB,EAAEW,SAAS,CAAGc,AAAM,OAANA,EAAapB,OAAO2B,MAAM,CAACP,GAAMK,CAAAA,EAAGnB,SAAS,CAAGc,EAAEd,SAAS,CAAE,IAAImB,CAAG,CACtF,GAGAG,EAAQ,AAACZ,IAAuGa,KAAK,CAQrHC,EAAsC,SAAUC,CAAM,EAEtD,SAASD,IAML,IAAIE,EAAQD,AAAW,OAAXA,GAAmBA,EAAOE,KAAK,CAAC,IAAI,CAC5CC,YAAc,IAAI,CAGtB,OAFAF,EAAMG,KAAK,CAAG,CAAC,MAAO,SAAU,QAAS,OAAO,CAChDH,EAAMI,QAAQ,CAAG,WACVJ,CACX,CAiBA,OA7BAb,EAAUW,EAAsBC,GAkBhCD,EAAqBxB,SAAS,CAAC+B,UAAU,CAAG,SAAUC,CAAI,EAStD,OARA,IAAI,CAACC,oBAAoB,CAAC,OAAQ,KAAM,CACpCC,MAAOF,EACPG,KAAMH,EACNI,IAAKd,EAAMU,GAAMK,QAAQ,CAAC,IAAKxC,GAAG,GAClCyC,OAAQhB,EAAMU,GAAMK,QAAQ,CAAC,KAAMxC,GAAG,EAC1C,GAEA,IAAI,CAACyB,KAAK,CAAG,IAAI,CAACU,IAAI,CAAGA,EAClB,IAAI,AACf,EACOR,CACX,EArCmBZ,IAAkJ2B,eAAe,GAAGvC,SAAS,CAACwC,SAAS,EA6DtMC,EAAS,AAACnC,IAA+EmC,MAAM,CAAEC,EAA8B,AAACpC,IAA+EC,OAAO,CAKtNoC,EAAS,AAACrC,IAA+EqC,MAAM,CAAEC,EAA2B,AAACtC,IAA+EE,IAAI,CA4BpN,SAASqC,EAAaC,CAAI,EACtB,MAAO,CAACA,EAAKC,IAAI,CAAC,SAAUC,CAAG,EAAI,MAAOA,AAAW,MAAXA,CAAG,CAAC,EAAE,AAAU,EAC9D,CAEA,SAASC,EAAiBC,CAAS,EAC/B,OAAO,IAAI,CAACC,SAAS,CAAC,WAAYD,EACtC,CAKA,SAASE,EAAqBF,CAAS,EACnC,IACIG,EAAQZ,CAAM,CAACa,AADJ,IAAI,CACSC,UAAU,CAAC,CAEnCC,EAAa,IAAI,CAACC,UAAU,CAACP,GAC7BQ,EAAa,CAACF,EAAWG,KAAK,CAC9BC,EAAc,CAACJ,EAAWK,OAAO,CACjCzB,EAAMkB,AANK,IAAI,CAMAQ,cAAc,CAACT,EAC9BH,GACAZ,EAASgB,AARE,IAAI,CAQGQ,cAAc,CAACT,EACjCH,EACA,CAAA,GACJ,MAAO,CACHhB,MAAOoB,AAZI,IAAI,CAYCS,gBAAgB,CAAC3B,EAAKE,GACtCH,KAAMmB,AAbK,IAAI,CAaAU,eAAe,CAAC5B,EAAKE,GACpCF,IAAKA,EACLE,OAAQA,EACR2B,SAAU,CACN7B,IAAKsB,AAAa,IAAbA,EACLpB,OAAQoB,AAAiB,GAAjBA,EACRxB,MAAO0B,EAAc,EAAI,EACzBzB,KAAMyB,EAAc,EAAI,EACxBM,MAAOV,EAAWS,QAAQ,CAACC,KAAK,AACpC,CACJ,CACJ,CAOA,SAASC,EAAsBC,CAAM,EAKjC,IAAK,IAJDtB,EAAO,CAAC,CAAC,IACTsB,CAAM,CAAC,EAAE,CAACC,CAAC,CACXD,CAAM,CAAC,EAAE,CAACE,CAAC,CAAC,CAAC,CACbC,EAAQH,EAAOI,MAAM,CAAG,EACnBC,EAAI,EAAGA,EAAIF,EAAOE,GAAK,EAC5B3B,EAAK4B,IAAI,CAAC,CACN,IACAN,CAAM,CAACK,EAAE,CAACJ,CAAC,CAAED,CAAM,CAACK,EAAE,CAACH,CAAC,CACxBF,CAAM,CAACK,EAAI,EAAE,CAACJ,CAAC,CAAED,CAAM,CAACK,EAAI,EAAE,CAACH,CAAC,CAChCF,CAAM,CAACK,EAAI,EAAE,CAACJ,CAAC,CAAED,CAAM,CAACK,EAAI,EAAE,CAACH,CAAC,CACnC,EAEL,OAAOxB,CACX,CAKA,SAAS6B,EAAwBC,CAAO,CAAEC,CAAU,EAChD,IAAI/B,EAAO,EAAE,CACb,GAAID,EAAa+B,GAAU,CACvB,IAAIE,EAAOF,CAAO,CAAC,EAAE,CACjBG,EAAQH,CAAO,CAAC,EAAE,AACN,CAAA,MAAZE,CAAI,CAAC,EAAE,EAAYC,AAAa,MAAbA,CAAK,CAAC,EAAE,GAC3BjC,EAAK4B,IAAI,CAAC,CAAC,IAAKK,CAAK,CAAC,EAAE,CAAEA,CAAK,CAAC,EAAE,CAAC,EACnCjC,EAAK4B,IAAI,CAACE,CAAO,CAAC,EAAE,EAEpB9B,EAAK4B,IAAI,CAAC,CAAC,IAAKI,CAAI,CAAC,EAAE,CAAEA,CAAI,CAAC,EAAE,CAAC,EAEzC,KAE0B,MAAlBF,CAAO,CAAC,EAAE,CAAC,EAAE,EACb9B,EAAK4B,IAAI,CAAC,CAAC,IAAKE,CAAO,CAAC,EAAE,CAAC,EAAE,CAAEA,CAAO,CAAC,EAAE,CAAC,EAAE,CAAC,EAEjD9B,EAAK4B,IAAI,CAACE,CAAO,CAAC,EAAE,CAAEA,CAAO,CAAC,EAAE,EAEpC,GAAI/B,EAAagC,GAAa,CAC1B,IAAIC,EAAOD,CAAU,CAAC,EAAE,AACR,CAAA,MAAZC,CAAI,CAAC,EAAE,GACPhC,EAAK4B,IAAI,CAAC,CAAC,IAAKI,CAAI,CAAC,EAAE,CAAEA,CAAI,CAAC,EAAE,CAAC,EACjChC,EAAK4B,IAAI,CAACG,CAAU,CAAC,EAAE,EACvB/B,EAAK4B,IAAI,CAACG,CAAU,CAAC,EAAE,EAE/B,KACK,CACD,IAAIG,EAASH,CAAU,CAAC,EAAE,CACtBI,EAASJ,CAAU,CAAC,EAAE,CACtBK,EAASL,CAAU,CAAC,EAAE,AACR,CAAA,MAAdG,CAAM,CAAC,EAAE,EAAYC,AAAc,MAAdA,CAAM,CAAC,EAAE,EAAYC,AAAc,MAAdA,CAAM,CAAC,EAAE,GACnDpC,EAAK4B,IAAI,CAAC,CAAC,IAAKQ,CAAM,CAAC,EAAE,CAAEA,CAAM,CAAC,EAAE,CAAC,EACrCpC,EAAK4B,IAAI,CAAC,CACN,IACAQ,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACTD,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACZ,EACDnC,EAAK4B,IAAI,CAAC,CACN,IACAO,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACTD,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACZ,EAET,CAEA,OADAlC,EAAK4B,IAAI,CAAC,CAAC,IAAI,EACR5B,CACX,CAKA,SAASqC,EAAuB9B,CAAK,CAAEH,CAAS,CAAEkC,CAAQ,EAuEtD,IAAK,IAnfYhB,EAAQf,EAAOgC,EAAgBC,EAC5CC,EAKAC,EAEAC,EAMAC,EACAC,EACAC,EACAC,EA8dA/C,EACAuB,EACAyB,EApEAC,EAAK7C,EAAU8C,KAAK,CAAEA,EAAQD,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EAAIE,EAAK/C,EAAUgD,MAAM,CAAmCC,EAAKjD,EAAUkD,eAAe,CAG5IC,EAAQzD,EAAyBM,EAAUmD,KAAK,CAAEL,EAAO,GAAIM,EAASC,KAAKC,GAAG,CAACR,EAAOK,GAAS,EAE/FI,EAAc/D,EAA+BW,CAAAA,EAAMqD,OAAO,CAACrD,KAAK,CAACkC,SAAS,CAACI,IAAI,CAAG,GAL8EQ,CAAAA,AAAO,KAAK,IAAZA,EAAgB,EAAIA,CAAC,CAMnK,EAElB7B,EAAI,AAACpB,CAAAA,EAAUoB,CAAC,EAAI,CAAA,EAAMc,CAAAA,EAR4Da,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EAQ5D,CAAA,EAG9CU,EAAI,MAASL,EAAQM,EAAUZ,EAAQ,EAAK9C,CAAAA,EAAUmB,CAAC,EAAI,CAAA,EAAIwC,EAAUR,EAAQ,EAAKnD,CAAAA,EAAU4C,CAAC,EAAI,CAAA,EAErG1B,EAAS,CAAC,CACFC,EAAG,EACHC,EAAGA,EACHwB,EAAGQ,CACP,EAAG,CACCjC,EAAGsC,EACHrC,EAAGA,EACHwB,EAAGQ,CACP,EAAG,CACCjC,EAAGiC,EACHhC,EAAGA,EACHwB,EAAGa,CACP,EAAG,CACCtC,EAAGiC,EACHhC,EAAGA,EACHwB,EAAG,CACP,EAAG,CACCzB,EAAGiC,EACHhC,EAAGA,EACHwB,EAAG,CAACa,CACR,EAAG,CACCtC,EAAGsC,EACHrC,EAAGA,EACHwB,EAAG,CAACQ,CACR,EAAG,CACCjC,EAAG,EACHC,EAAGA,EACHwB,EAAG,CAACQ,CACR,EAAG,CACCjC,EAAG,CAACsC,EACJrC,EAAGA,EACHwB,EAAG,CAACQ,CACR,EAAG,CACCjC,EAAG,CAACiC,EACJhC,EAAGA,EACHwB,EAAG,CAACa,CACR,EAAG,CACCtC,EAAG,CAACiC,EACJhC,EAAGA,EACHwB,EAAG,CACP,EAAG,CACCzB,EAAG,CAACiC,EACJhC,EAAGA,EACHwB,EAAGa,CACP,EAAG,CACCtC,EAAG,CAACsC,EACJrC,EAAGA,EACHwB,EAAGQ,CACP,EAAG,CACCjC,EAAG,EACHC,EAAGA,EACHwB,EAAGQ,CACP,EAAE,CAAEQ,EAAWP,KAAKQ,GAAG,CAACN,GAAcO,EAAWT,KAAKU,GAAG,CAACR,GAKzDS,EAAK,EAAsBA,EAAKC,AAAb/C,EAAsBI,MAAM,CAAE0C,IAAM,CAC5D,IAAIE,EAAQD,AADY/C,CACJ,CAAC8C,EAAG,CACxB7C,EAAI+C,EAAM/C,CAAC,CACXyB,EAAIsB,EAAMtB,CAAC,CACXsB,EAAM/C,CAAC,CAAG,AAACA,EAAIyC,EAAWhB,EAAIkB,EAAYJ,EAC1CQ,EAAMtB,CAAC,CAAG,AAACA,EAAIgB,EAAWzC,EAAI2C,EAAYH,CAC9C,CA5EA,IA6EIQ,GA1fajD,EA0fuCA,EA1fxBiB,EA4f5B,CAAA,EA3fAE,EAAYlC,CADSA,EA2frBA,GA1fkBqD,OAAO,CAACrD,KAAK,CAACkC,SAAS,CAKzCC,EAAWhF,EANiC8E,KAAAA,EAO5CD,EAAAA,GAAiBhC,EAAMmC,QAAQ,EAC/BC,EAAS,CACLpB,EAAGhB,EAAMiE,SAAS,CAAG,EACrBhD,EAAGjB,EAAMkE,UAAU,CAAG,EACtBzB,EAAGP,EAAUc,KAAK,CAAG,EACrBmB,GAAIhH,EAAK+E,EAAUc,KAAK,CAAE,GAAK7F,EAAK+E,EAAUkC,YAAY,CAAE,EAChE,EACA/B,EAAQrC,EAAMqE,OAAO,EAAI,EACzB/B,EAAOpF,EAAUgF,EAAUI,IAAI,CAAIH,CAAAA,EAAW,GAAK,CAAA,EAEnDK,EAAS,CACL8B,KAAMpB,KAAKQ,GAAG,CAFlBnB,EAAQrF,EAAUgF,EAAUK,KAAK,CAAIJ,CAAAA,EAAW,GAAK,CAAA,GAGjDoC,KAAMrB,KAAKQ,GAAG,CAAC,CAACpB,GAChBkC,KAAMtB,KAAKU,GAAG,CAACrB,GACfkC,KAAMvB,KAAKU,GAAG,CAAC,CAACtB,EACpB,EACCN,IACDI,EAAOpB,CAAC,EAAIhB,EAAM0E,QAAQ,CAC1BtC,EAAOnB,CAAC,EAAIjB,EAAM2E,OAAO,EAGtB5D,EAAO6D,GAAG,CAAC,SAAUb,CAAK,EAC7B,IA7DU/C,EAAGC,EAAGwB,EA6DZoC,GA7DM7D,EA6Da,AAACmB,CAAAA,EAAW4B,EAAM9C,CAAC,CAAG8C,EAAM/C,CAAC,AAADA,EAAKoB,EAAOpB,CAAC,CA7DnDC,EA6DqD,AAACkB,CAAAA,EAAW4B,EAAM/C,CAAC,CAAG+C,EAAM9C,CAAC,AAADA,EAAKmB,EAAOnB,CAAC,CA7D3FwB,EA6D6F,AAACsB,CAAAA,EAAMtB,CAAC,EAAI,CAAA,EAAKL,EAAOK,CAAC,CA5DnI,CACHzB,EAAGwB,AA4DCA,EA5DM+B,IAAI,CAAGvD,EAAIwB,AA4DjBA,EA5DwBiC,IAAI,CAAGhC,EACnCxB,EAAG,CAACuB,AA2DAA,EA3DOgC,IAAI,CAAGhC,AA2DdA,EA3DqBiC,IAAI,CAAGzD,EAAIwB,AA2DhCA,EA3DuC8B,IAAI,CAAGrD,EAC9CuB,AA0DAA,EA1DO+B,IAAI,CAAG/B,AA0DdA,EA1DqBgC,IAAI,CAAG/B,EAChCA,EAAGD,AAyDCA,EAzDM8B,IAAI,CAAG9B,AAyDbA,EAzDoBiC,IAAI,CAAGzD,EAAIwB,AAyD/BA,EAzDsCgC,IAAI,CAAGvD,EAC7CuB,AAwDAA,EAxDO8B,IAAI,CAAG9B,AAwDdA,EAxDqB+B,IAAI,CAAG9B,CACpC,GAyDQqC,EAAaC,AAkCzB,SAAuBD,CAAU,CAAE1C,CAAM,CAAE4C,CAAQ,EAC/C,IAAIC,EAAa,AAAC,AAACD,EAAW,GACrBA,EAAWE,OAAOC,iBAAiB,CACpCH,EAAYF,CAAAA,EAAWrC,CAAC,CAAGL,EAAOK,CAAC,CAAGuC,CAAO,EAC7C,EACR,MAAO,CACHhE,EAAG8D,EAAW9D,CAAC,CAAGiE,EAClBhE,EAAG6D,EAAW7D,CAAC,CAAGgE,CACtB,CACJ,EA3CuCJ,EAC3BzC,EACAA,EAAO+B,EAAE,EAKb,OAHAW,EAAW9D,CAAC,CAAG8D,EAAW9D,CAAC,CAAGqB,EAAQD,EAAOpB,CAAC,CAC9C8D,EAAW7D,CAAC,CAAG6D,EAAW7D,CAAC,CAAGoB,EAAQD,EAAOnB,CAAC,CAC9C6D,EAAWrC,CAAC,CAAGoC,EAAQpC,CAAC,CAAGJ,EAAQD,EAAOK,CAAC,CACpC,CACHzB,EAAImB,EAAW2C,EAAW7D,CAAC,CAAG6D,EAAW9D,CAAC,CAC1CC,EAAIkB,EAAW2C,EAAW9D,CAAC,CAAG8D,EAAW7D,CAAC,CAC1CwB,EAAGqC,EAAWrC,CAAC,AACnB,CACJ,IAgeA,OAdIS,AAA4D,IAA5DA,KAAKkC,GAAG,CAACpB,CAAiB,CAAC,EAAE,CAAC/C,CAAC,CAAG+C,CAAiB,CAAC,EAAE,CAAC/C,CAAC,GACxDiC,AAA4D,IAA5DA,KAAKkC,GAAG,CAACpB,CAAiB,CAAC,EAAE,CAAC/C,CAAC,CAAG+C,CAAiB,CAAC,EAAE,CAAC/C,CAAC,EAEjD,IAAI,CAACoE,UAAU,CAAC,CACnBrB,CAAiB,CAAC,EAAE,CACpBA,CAAiB,CAAC,EAAE,CACpBA,CAAiB,CAAC,EAAE,CACpBA,CAAiB,CAAC,EAAE,CACvB,CAAE,CAAA,GAII,IAAI,CAACsB,aAAa,CAACtB,EAGlC,CAKA,SAASuB,EAAyBhE,CAAO,CAAEC,CAAU,EACjD,IAAI/B,EAAO8B,EAAQiE,KAAK,CAAC,EAAG,GAC5B,GAAIhG,EAAagC,GAAa,CAC1B,IAAIC,EAAOD,CAAU,CAAC,EAAE,AACR,CAAA,MAAZC,CAAI,CAAC,EAAE,GACPhC,EAAK4B,IAAI,CAACG,CAAU,CAAC,EAAE,EACvB/B,EAAK4B,IAAI,CAACG,CAAU,CAAC,EAAE,EACvB/B,EAAK4B,IAAI,CAAC,CAAC,IAAKI,CAAI,CAAC,EAAE,CAAEA,CAAI,CAAC,EAAE,CAAC,EAEzC,KACK,CACD,IAAIA,EAAOD,CAAU,CAAC,EAAE,CACpBiE,EAASjE,CAAU,CAAC,EAAE,CACtBG,EAASH,CAAU,CAAC,EAAE,AACV,CAAA,MAAZC,CAAI,CAAC,EAAE,EAAYgE,AAAc,MAAdA,CAAM,CAAC,EAAE,EAAY9D,AAAc,MAAdA,CAAM,CAAC,EAAE,GACjDlC,EAAK4B,IAAI,CAAC,CAAC,IAAKM,CAAM,CAAC,EAAE,CAAEA,CAAM,CAAC,EAAE,CAAC,EACrClC,EAAK4B,IAAI,CAAC,CACN,IACAM,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACT8D,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACZ,EACDhG,EAAK4B,IAAI,CAAC,CACN,IACAoE,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACTA,CAAM,CAAC,EAAE,CACThE,CAAI,CAAC,EAAE,CACPA,CAAI,CAAC,EAAE,CACV,EAET,CAEA,OADAhC,EAAK4B,IAAI,CAAC,CAAC,IAAI,EACR5B,CACX,CAS6B,MAjS7B,SAAiBiG,CAAgB,EAC7B,IAAIC,EAAgBD,EAAiB/I,SAAS,AACzCgJ,CAAAA,EAAcC,QAAQ,GACvBD,EAAcxG,SAAS,CAAC0G,KAAK,CAACD,QAAQ,CAnCoBzH,EAoC1DmB,EAAOqG,EAAe,CAClBC,SAAUhG,EACVkG,aAAc/F,EACduF,cAAexE,EACfH,gBAAiBW,EACjBb,eAAgBqB,EAChBpB,iBAAkB6E,CACtB,GAER,EAuRIQ,EAAmItK,EAAoB,KACvJuK,EAAuJvK,EAAoBI,CAAC,CAACkK,GAgB7KE,GACI3K,EAAgB,SAAUU,CAAC,CAC3ByB,CAAC,EAOD,MAAOnC,AANHA,CAAAA,EAAgBe,OAAOqB,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAU5B,CAAC,CAC1DyB,CAAC,EAAIzB,EAAE2B,SAAS,CAAGF,CAAG,GACd,SAAUzB,CAAC,CACnByB,CAAC,EAAI,IAAK,IAAII,KAAKJ,EAAOpB,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACY,EAC/DI,IAAI7B,CAAAA,CAAC,CAAC6B,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,CAAG,CAAA,EACI7B,EAAGyB,EAC5B,EACO,SAAUzB,CAAC,CAAEyB,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIyI,UAAU,uBAAyBC,OAAO1I,GAAK,iCAE7D,SAASK,IAAO,IAAI,CAACC,WAAW,CAAG/B,CAAG,CADtCV,EAAcU,EAAGyB,GAEjBzB,EAAEW,SAAS,CAAGc,AAAM,OAANA,EAAapB,OAAO2B,MAAM,CAACP,GAAMK,CAAAA,EAAGnB,SAAS,CAAGc,EAAEd,SAAS,CAAE,IAAImB,CAAG,CACtF,GAGAsI,EAAc,AAACJ,IAA2IK,WAAW,CAACC,MAAM,CAAC3J,SAAS,CAAC4J,UAAU,CAEjMC,EAAuB,AAACvJ,IAA+EqC,MAAM,CAM7GmH,EAA+B,SAAUrI,CAAM,EAE/C,SAASqI,IACL,OAAOrI,AAAW,OAAXA,GAAmBA,EAAOE,KAAK,CAAC,IAAI,CAAEC,YAAc,IAAI,AACnE,CACA,OAJA0H,EAAsBQ,EAAerI,GAI9BqI,CACX,EAAEL,GACFI,EAAqBC,EAAc9J,SAAS,CAAE,CAC1C+J,UAAW,UACf,GA0H6B,IAAIC,EA/EJ,CAAC,EAgG1BC,GACItL,EAAgB,SAAUU,CAAC,CAC3ByB,CAAC,EAOD,MAAOnC,AANHA,CAAAA,EAAgBe,OAAOqB,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAU5B,CAAC,CAC1DyB,CAAC,EAAIzB,EAAE2B,SAAS,CAAGF,CAAG,GACd,SAAUzB,CAAC,CACnByB,CAAC,EAAI,IAAK,IAAII,KAAKJ,EAAOpB,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACY,EAC/DI,IAAI7B,CAAAA,CAAC,CAAC6B,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,CAAG,CAAA,EACI7B,EAAGyB,EAC5B,EACO,SAAUzB,CAAC,CAAEyB,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIyI,UAAU,uBAAyBC,OAAO1I,GAAK,iCAE7D,SAASK,IAAO,IAAI,CAACC,WAAW,CAAG/B,CAAG,CADtCV,EAAcU,EAAGyB,GAEjBzB,EAAEW,SAAS,CAAGc,AAAM,OAANA,EAAapB,OAAO2B,MAAM,CAACP,GAAMK,CAAAA,EAAGnB,SAAS,CAAGc,EAAEd,SAAS,CAAE,IAAImB,CAAG,CACtF,GAMA+I,EAAe,AAACb,IAA2IK,WAAW,CAACC,MAAM,CAE7KQ,EAAwB,AAAC7J,IAA+EqC,MAAM,CAAEyH,EAAQ,AAAC9J,IAA+E8J,KAAK,CAkB7MC,EAAgC,SAAU5I,CAAM,EAEhD,SAAS4I,IACL,OAAO5I,AAAW,OAAXA,GAAmBA,EAAOE,KAAK,CAAC,IAAI,CAAEC,YAAc,IAAI,AACnE,CAQA,OAXAqI,EAAuBI,EAAgB5I,GASvC4I,EAAeC,OAAO,GACtBD,EAAeE,cAAc,CAAGH,EAAMF,EAAaK,cAAc,CAAEP,GAC5DK,CACX,EAAEH,GACFC,EAAsBE,EAAerK,SAAS,CAAE,CAC5C4J,WA/LuDE,CAgM3D,GACAT,IAA0ImB,kBAAkB,CAAC,WAAYH,GAczKI,AAR4DJ,EAQpCC,OAAO,CAAC1J,IAAkJ2B,eAAe,IACpK,IAAInC,EAAiBE,IAGxC,OADYH,EAAoB,OAAU,AAE3C,GAET"}