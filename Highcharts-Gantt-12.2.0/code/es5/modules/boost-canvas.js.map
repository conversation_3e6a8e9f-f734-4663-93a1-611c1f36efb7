{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/boost-canvas\n * @requires highcharts\n *\n * Boost module\n *\n * (c) 2010-2025 Highsoft AS\n * Author: Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"Color\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/boost-canvas\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"Color\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/boost-canvas\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"Color\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Color\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__620__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 620:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__620__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ boost_canvas_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es5/es-modules/Extensions/Boost/Boostables.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n// These are the series we allow boosting for.\nvar Boostables = [\n    'area',\n    'areaspline',\n    'arearange',\n    'column',\n    'columnrange',\n    'bar',\n    'line',\n    'scatter',\n    'heatmap',\n    'bubble',\n    'treemap'\n];\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Boost_Boostables = (Boostables);\n\n;// ./code/es5/es-modules/Extensions/Boost/BoostableMap.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Imports\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\n// These are the series we allow boosting for.\nvar BoostableMap = {};\nBoost_Boostables.forEach(function (item) {\n    BoostableMap[item] = true;\n});\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Boost_BoostableMap = (BoostableMap);\n\n;// ./code/es5/es-modules/Extensions/Boost/BoostChart.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nvar composed = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).composed;\n\nvar addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, pushUnique = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pushUnique;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction compose(ChartClass, wglMode) {\n    if (wglMode && pushUnique(composed, 'Boost.Chart')) {\n        ChartClass.prototype.callbacks.push(onChartCallback);\n    }\n    return ChartClass;\n}\n/**\n * Get the clip rectangle for a target, either a series or the chart.\n * For the chart, we need to consider the maximum extent of its Y axes,\n * in case of Highcharts Stock panes and navigator.\n *\n * @private\n * @function Highcharts.Chart#getBoostClipRect\n */\nfunction getBoostClipRect(chart, target) {\n    var navigator = chart.navigator;\n    var clipBox = {\n            x: chart.plotLeft,\n            y: chart.plotTop,\n            width: chart.plotWidth,\n            height: chart.plotHeight\n        };\n    if (navigator && chart.inverted) { // #17820, #20936\n        clipBox.width += navigator.top + navigator.height;\n        if (!navigator.opposite) {\n            clipBox.x = navigator.left;\n        }\n    }\n    else if (navigator && !chart.inverted) {\n        clipBox.height = navigator.top + navigator.height - chart.plotTop;\n    }\n    // Clipping of individual series (#11906, #19039).\n    if (target.is) {\n        var _a = target,\n            xAxis = _a.xAxis,\n            yAxis = _a.yAxis;\n        clipBox = chart.getClipBox(target);\n        if (chart.inverted) {\n            var lateral = clipBox.width;\n            clipBox.width = clipBox.height;\n            clipBox.height = lateral;\n            clipBox.x = yAxis.pos;\n            clipBox.y = xAxis.pos;\n        }\n        else {\n            clipBox.x = xAxis.pos;\n            clipBox.y = yAxis.pos;\n        }\n    }\n    if (target === chart) {\n        var verticalAxes = chart.inverted ? chart.xAxis : chart.yAxis; // #14444\n            if (verticalAxes.length <= 1) {\n                clipBox.y = Math.min(verticalAxes[0].pos,\n            clipBox.y);\n            clipBox.height = (verticalAxes[0].pos -\n                chart.plotTop +\n                verticalAxes[0].len);\n        }\n    }\n    return clipBox;\n}\n/**\n * Returns true if the chart is in series boost mode.\n * @private\n * @param {Highcharts.Chart} chart\n * Chart to check.\n * @return {boolean}\n * `true` if the chart is in series boost mode.\n */\nfunction isChartSeriesBoosting(chart) {\n    var allSeries = chart.series,\n        boost = chart.boost = chart.boost || {},\n        boostOptions = chart.options.boost || {},\n        threshold = pick(boostOptions.seriesThreshold, 50);\n    if (allSeries.length >= threshold) {\n        return true;\n    }\n    if (allSeries.length === 1) {\n        return false;\n    }\n    var allowBoostForce = boostOptions.allowForce;\n    if (typeof allowBoostForce === 'undefined') {\n        allowBoostForce = true;\n        for (var _i = 0, _a = chart.xAxis; _i < _a.length; _i++) {\n            var axis = _a[_i];\n            if (pick(axis.min, -Infinity) > pick(axis.dataMin, -Infinity) ||\n                pick(axis.max, Infinity) < pick(axis.dataMax, Infinity)) {\n                allowBoostForce = false;\n                break;\n            }\n        }\n    }\n    if (typeof boost.forceChartBoost !== 'undefined') {\n        if (allowBoostForce) {\n            return boost.forceChartBoost;\n        }\n        boost.forceChartBoost = void 0;\n    }\n    // If there are more than five series currently boosting,\n    // we should boost the whole chart to avoid running out of webgl contexts.\n    var canBoostCount = 0,\n        needBoostCount = 0,\n        seriesOptions;\n    for (var _b = 0, allSeries_1 = allSeries; _b < allSeries_1.length; _b++) {\n        var series = allSeries_1[_b];\n        seriesOptions = series.options;\n        // Don't count series with boostThreshold set to 0\n        // See #8950\n        // Also don't count if the series is hidden.\n        // See #9046\n        if (seriesOptions.boostThreshold === 0 ||\n            series.visible === false) {\n            continue;\n        }\n        // Don't count heatmap series as they are handled differently.\n        // In the future we should make the heatmap/treemap path compatible\n        // with forcing. See #9636.\n        if (series.type === 'heatmap') {\n            continue;\n        }\n        if (Boost_BoostableMap[series.type]) {\n            ++canBoostCount;\n        }\n        if (patientMax(series.getColumn('x', true), seriesOptions.data, \n        /// series.xData,\n        series.points) >= (seriesOptions.boostThreshold || Number.MAX_VALUE)) {\n            ++needBoostCount;\n        }\n    }\n    boost.forceChartBoost = allowBoostForce && ((\n    // Even when the series that need a boost are less than or equal\n    // to 5, force a chart boost when all series are to be boosted.\n    // See #18815\n    canBoostCount === allSeries.length &&\n        needBoostCount === canBoostCount) ||\n        needBoostCount > 5);\n    return boost.forceChartBoost;\n}\n/**\n * Take care of the canvas blitting\n * @private\n */\nfunction onChartCallback(chart) {\n    /**\n     * Convert chart-level canvas to image.\n     * @private\n     */\n    function canvasToSVG() {\n        if (chart.boost &&\n            chart.boost.wgl &&\n            isChartSeriesBoosting(chart)) {\n            chart.boost.wgl.render(chart);\n        }\n    }\n    /**\n     * Clear chart-level canvas.\n     * @private\n     */\n    function preRender() {\n        var _a,\n            _b;\n        // Reset force state\n        chart.boost = chart.boost || {};\n        chart.boost.forceChartBoost = void 0;\n        chart.boosted = false;\n        // Clear the canvas\n        if (!chart.axes.some(function (axis) { return axis.isPanning; })) {\n            (_b = (_a = chart.boost).clear) === null || _b === void 0 ? void 0 : _b.call(_a);\n        }\n        if (chart.boost.canvas &&\n            chart.boost.wgl &&\n            isChartSeriesBoosting(chart)) {\n            // Allocate\n            chart.boost.wgl.allocateBuffer(chart);\n        }\n        // See #6518 + #6739\n        if (chart.boost.markerGroup &&\n            chart.xAxis &&\n            chart.xAxis.length > 0 &&\n            chart.yAxis &&\n            chart.yAxis.length > 0) {\n            chart.boost.markerGroup.translate(chart.xAxis[0].pos, chart.yAxis[0].pos);\n        }\n    }\n    addEvent(chart, 'predraw', preRender);\n    // Use the load event rather than redraw, otherwise user load events will\n    // fire too early (#18755)\n    addEvent(chart, 'load', canvasToSVG, { order: -1 });\n    addEvent(chart, 'redraw', canvasToSVG);\n    var prevX = -1;\n    var prevY = -1;\n    addEvent(chart.pointer, 'afterGetHoverData', function (e) {\n        var _a;\n        var series = (_a = e.hoverPoint) === null || _a === void 0 ? void 0 : _a.series;\n        chart.boost = chart.boost || {};\n        if (chart.boost.markerGroup && series) {\n            var xAxis = chart.inverted ? series.yAxis : series.xAxis;\n            var yAxis = chart.inverted ? series.xAxis : series.yAxis;\n            if ((xAxis && xAxis.pos !== prevX) ||\n                (yAxis && yAxis.pos !== prevY)) {\n                // #21176: If the axis is changed, hide teh halo without\n                // animation  to prevent flickering of halos sharing the\n                // same marker group\n                chart.series.forEach(function (s) {\n                    var _a;\n                    (_a = s.halo) === null || _a === void 0 ? void 0 : _a.hide();\n                });\n                // #10464: Keep the marker group position in sync with the\n                // position of the hovered series axes since there is only\n                // one shared marker group when boosting.\n                chart.boost.markerGroup.translate(xAxis.pos, yAxis.pos);\n                prevX = xAxis.pos;\n                prevY = yAxis.pos;\n            }\n        }\n    });\n}\n/**\n * Tolerant max() function.\n *\n * @private\n * @param {...Array<Array<unknown>>} args\n * Max arguments\n * @return {number}\n * Max value\n */\nfunction patientMax() {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n    }\n    var r = -Number.MAX_VALUE;\n    args.forEach(function (t) {\n        if (typeof t !== 'undefined' &&\n            t !== null &&\n            typeof t.length !== 'undefined') {\n            if (t.length > 0) {\n                r = t.length;\n                return true;\n            }\n        }\n    });\n    return r;\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar BoostChart = {\n    compose: compose,\n    getBoostClipRect: getBoostClipRect,\n    isChartSeriesBoosting: isChartSeriesBoosting\n};\n/* harmony default export */ var Boost_BoostChart = (BoostChart);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Color\"],\"commonjs\":[\"highcharts\",\"Color\"],\"commonjs2\":[\"highcharts\",\"Color\"],\"root\":[\"Highcharts\",\"Color\"]}\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_ = __webpack_require__(620);\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default = /*#__PURE__*/__webpack_require__.n(highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_);\n;// ./code/es5/es-modules/Extensions/Boost/WGLDrawMode.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Constants\n *\n * */\nvar WGLDrawMode = {\n    'area': 'LINES',\n    'arearange': 'LINES',\n    'areaspline': 'LINES',\n    'column': 'LINES',\n    'columnrange': 'LINES',\n    'bar': 'LINES',\n    'line': 'LINE_STRIP',\n    'scatter': 'POINTS',\n    'heatmap': 'TRIANGLES',\n    'treemap': 'TRIANGLES',\n    'bubble': 'POINTS'\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Boost_WGLDrawMode = (WGLDrawMode);\n\n;// ./code/es5/es-modules/Extensions/Boost/WGLShader.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar clamp = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).clamp, error = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).error, WGLShader_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Constants\n *\n * */\nvar fragmentShader = [\n    /* eslint-disable max-len, @typescript-eslint/indent */\n    'precision highp float;',\n    'uniform vec4 fillColor;',\n    'varying highp vec2 position;',\n    'varying highp vec4 vColor;',\n    'uniform sampler2D uSampler;',\n    'uniform bool isCircle;',\n    'uniform bool hasColor;',\n    // 'vec4 toColor(float value, vec2 point) {',\n    //     'return vec4(0.0, 0.0, 0.0, 0.0);',\n    // '}',\n    'void main(void) {',\n    'vec4 col = fillColor;',\n    'vec4 tcol = texture2D(uSampler, gl_PointCoord.st);',\n    'if (hasColor) {',\n    'col = vColor;',\n    '}',\n    'if (isCircle) {',\n    'col *= tcol;',\n    'if (tcol.r < 0.0) {',\n    'discard;',\n    '} else {',\n    'gl_FragColor = col;',\n    '}',\n    '} else {',\n    'gl_FragColor = col;',\n    '}',\n    '}'\n    /* eslint-enable max-len, @typescript-eslint/indent */\n].join('\\n');\nvar vertexShader = [\n    /* eslint-disable max-len, @typescript-eslint/indent */\n    '#version 100',\n    '#define LN10 2.302585092994046',\n    'precision highp float;',\n    'attribute vec4 aVertexPosition;',\n    'attribute vec4 aColor;',\n    'varying highp vec2 position;',\n    'varying highp vec4 vColor;',\n    'uniform mat4 uPMatrix;',\n    'uniform float pSize;',\n    'uniform float translatedThreshold;',\n    'uniform bool hasThreshold;',\n    'uniform bool skipTranslation;',\n    'uniform float xAxisTrans;',\n    'uniform float xAxisMin;',\n    'uniform float xAxisMinPad;',\n    'uniform float xAxisPointRange;',\n    'uniform float xAxisLen;',\n    'uniform bool  xAxisPostTranslate;',\n    'uniform float xAxisOrdinalSlope;',\n    'uniform float xAxisOrdinalOffset;',\n    'uniform float xAxisPos;',\n    'uniform bool  xAxisCVSCoord;',\n    'uniform bool  xAxisIsLog;',\n    'uniform bool  xAxisReversed;',\n    'uniform float yAxisTrans;',\n    'uniform float yAxisMin;',\n    'uniform float yAxisMinPad;',\n    'uniform float yAxisPointRange;',\n    'uniform float yAxisLen;',\n    'uniform bool  yAxisPostTranslate;',\n    'uniform float yAxisOrdinalSlope;',\n    'uniform float yAxisOrdinalOffset;',\n    'uniform float yAxisPos;',\n    'uniform bool  yAxisCVSCoord;',\n    'uniform bool  yAxisIsLog;',\n    'uniform bool  yAxisReversed;',\n    'uniform bool  isBubble;',\n    'uniform bool  bubbleSizeByArea;',\n    'uniform float bubbleZMin;',\n    'uniform float bubbleZMax;',\n    'uniform float bubbleZThreshold;',\n    'uniform float bubbleMinSize;',\n    'uniform float bubbleMaxSize;',\n    'uniform bool  bubbleSizeAbs;',\n    'uniform bool  isInverted;',\n    'float bubbleRadius(){',\n    'float value = aVertexPosition.w;',\n    'float zMax = bubbleZMax;',\n    'float zMin = bubbleZMin;',\n    'float radius = 0.0;',\n    'float pos = 0.0;',\n    'float zRange = zMax - zMin;',\n    'if (bubbleSizeAbs){',\n    'value = value - bubbleZThreshold;',\n    'zMax = max(zMax - bubbleZThreshold, zMin - bubbleZThreshold);',\n    'zMin = 0.0;',\n    '}',\n    'if (value < zMin){',\n    'radius = bubbleZMin / 2.0 - 1.0;',\n    '} else {',\n    'pos = zRange > 0.0 ? (value - zMin) / zRange : 0.5;',\n    'if (bubbleSizeByArea && pos > 0.0){',\n    'pos = sqrt(pos);',\n    '}',\n    'radius = ceil(bubbleMinSize + pos * (bubbleMaxSize - bubbleMinSize)) / 2.0;',\n    '}',\n    'return radius * 2.0;',\n    '}',\n    'float translate(float val,',\n    'float pointPlacement,',\n    'float localA,',\n    'float localMin,',\n    'float minPixelPadding,',\n    'float pointRange,',\n    'float len,',\n    'bool  cvsCoord,',\n    'bool  isLog,',\n    'bool  reversed',\n    '){',\n    'float sign = 1.0;',\n    'float cvsOffset = 0.0;',\n    'if (cvsCoord) {',\n    'sign *= -1.0;',\n    'cvsOffset = len;',\n    '}',\n    'if (isLog) {',\n    'val = log(val) / LN10;',\n    '}',\n    'if (reversed) {',\n    'sign *= -1.0;',\n    'cvsOffset -= sign * len;',\n    '}',\n    'return sign * (val - localMin) * localA + cvsOffset + ',\n    '(sign * minPixelPadding);', // ' + localA * pointPlacement * pointRange;',\n    '}',\n    'float xToPixels(float value) {',\n    'if (skipTranslation){',\n    'return value;// + xAxisPos;',\n    '}',\n    'return translate(value, 0.0, xAxisTrans, xAxisMin, xAxisMinPad, xAxisPointRange, xAxisLen, xAxisCVSCoord, xAxisIsLog, xAxisReversed);// + xAxisPos;',\n    '}',\n    'float yToPixels(float value, float checkTreshold) {',\n    'float v;',\n    'if (skipTranslation){',\n    'v = value;// + yAxisPos;',\n    '} else {',\n    'v = translate(value, 0.0, yAxisTrans, yAxisMin, yAxisMinPad, yAxisPointRange, yAxisLen, yAxisCVSCoord, yAxisIsLog, yAxisReversed);// + yAxisPos;',\n    'if (v > yAxisLen) {',\n    'v = yAxisLen;',\n    '}',\n    '}',\n    'if (checkTreshold > 0.0 && hasThreshold) {',\n    'v = min(v, translatedThreshold);',\n    '}',\n    'return v;',\n    '}',\n    'void main(void) {',\n    'if (isBubble){',\n    'gl_PointSize = bubbleRadius();',\n    '} else {',\n    'gl_PointSize = pSize;',\n    '}',\n    // 'gl_PointSize = 10.0;',\n    'vColor = aColor;',\n    'if (skipTranslation && isInverted) {',\n    // If we get translated values from JS, just swap them (x, y)\n    'gl_Position = uPMatrix * vec4(aVertexPosition.y + yAxisPos, aVertexPosition.x + xAxisPos, 0.0, 1.0);',\n    '} else if (isInverted) {',\n    // But when calculating pixel positions directly,\n    // swap axes and values (x, y)\n    'gl_Position = uPMatrix * vec4(yToPixels(aVertexPosition.y, aVertexPosition.z) + yAxisPos, xToPixels(aVertexPosition.x) + xAxisPos, 0.0, 1.0);',\n    '} else {',\n    'gl_Position = uPMatrix * vec4(xToPixels(aVertexPosition.x) + xAxisPos, yToPixels(aVertexPosition.y, aVertexPosition.z) + yAxisPos, 0.0, 1.0);',\n    '}',\n    // 'gl_Position = uPMatrix * vec4(aVertexPosition.x, aVertexPosition.y, 0.0, 1.0);',\n    '}'\n    /* eslint-enable max-len, @typescript-eslint/indent */\n].join('\\n');\n/* *\n *\n *  Class\n *\n * */\n/* eslint-disable valid-jsdoc */\n/**\n * A static shader mimicing axis translation functions found in Core/Axis\n *\n * @private\n *\n * @param {WebGLContext} gl\n * the context in which the shader is active\n */\nvar WGLShader = /** @class */ (function () {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    function WGLShader(gl) {\n        // Error stack\n        this.errors = [];\n        this.uLocations = {};\n        this.gl = gl;\n        if (gl && !this.createShader()) {\n            return void 0;\n        }\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Bind the shader.\n     * This makes the shader the active one until another one is bound,\n     * or until 0 is bound.\n     * @private\n     */\n    WGLShader.prototype.bind = function () {\n        if (this.gl && this.shaderProgram) {\n            this.gl.useProgram(this.shaderProgram);\n        }\n    };\n    /**\n     * Create the shader.\n     * Loads the shader program statically defined above\n     * @private\n     */\n    WGLShader.prototype.createShader = function () {\n        var _this = this;\n        var v = this.stringToProgram(vertexShader, 'vertex'), f = this.stringToProgram(fragmentShader, 'fragment'), uloc = function (n) { return (_this.gl.getUniformLocation(_this.shaderProgram, n)); };\n        if (!v || !f) {\n            this.shaderProgram = false;\n            this.handleErrors();\n            return false;\n        }\n        this.shaderProgram = this.gl.createProgram();\n        this.gl.attachShader(this.shaderProgram, v);\n        this.gl.attachShader(this.shaderProgram, f);\n        this.gl.linkProgram(this.shaderProgram);\n        if (!this.gl.getProgramParameter(this.shaderProgram, this.gl.LINK_STATUS)) {\n            this.errors.push(this.gl.getProgramInfoLog(this.shaderProgram));\n            this.handleErrors();\n            this.shaderProgram = false;\n            return false;\n        }\n        this.gl.useProgram(this.shaderProgram);\n        this.gl.bindAttribLocation(this.shaderProgram, 0, 'aVertexPosition');\n        this.pUniform = uloc('uPMatrix');\n        this.psUniform = uloc('pSize');\n        this.fcUniform = uloc('fillColor');\n        this.isBubbleUniform = uloc('isBubble');\n        this.bubbleSizeAbsUniform = uloc('bubbleSizeAbs');\n        this.bubbleSizeAreaUniform = uloc('bubbleSizeByArea');\n        this.uSamplerUniform = uloc('uSampler');\n        this.skipTranslationUniform = uloc('skipTranslation');\n        this.isCircleUniform = uloc('isCircle');\n        this.isInverted = uloc('isInverted');\n        return true;\n    };\n    /**\n     * Handle errors accumulated in errors stack\n     * @private\n     */\n    WGLShader.prototype.handleErrors = function () {\n        if (this.errors.length) {\n            error('[highcharts boost] shader error - ' +\n                this.errors.join('\\n'));\n        }\n    };\n    /**\n     * String to shader program\n     * @private\n     * @param {string} str\n     * Program source\n     * @param {string} type\n     * Program type: either `vertex` or `fragment`\n     */\n    WGLShader.prototype.stringToProgram = function (str, type) {\n        var shader = this.gl.createShader(type === 'vertex' ? this.gl.VERTEX_SHADER : this.gl.FRAGMENT_SHADER);\n        this.gl.shaderSource(shader, str);\n        this.gl.compileShader(shader);\n        if (!this.gl.getShaderParameter(shader, this.gl.COMPILE_STATUS)) {\n            this.errors.push('when compiling ' +\n                type +\n                ' shader:\\n' +\n                this.gl.getShaderInfoLog(shader));\n            return false;\n        }\n        return shader;\n    };\n    /**\n     * Destroy the shader\n     * @private\n     */\n    WGLShader.prototype.destroy = function () {\n        if (this.gl && this.shaderProgram) {\n            this.gl.deleteProgram(this.shaderProgram);\n            this.shaderProgram = false;\n        }\n    };\n    WGLShader.prototype.fillColorUniform = function () {\n        return this.fcUniform;\n    };\n    /**\n     * Get the shader program handle\n     * @private\n     * @return {WebGLProgram}\n     * The handle for the program\n     */\n    WGLShader.prototype.getProgram = function () {\n        return this.shaderProgram;\n    };\n    WGLShader.prototype.pointSizeUniform = function () {\n        return this.psUniform;\n    };\n    WGLShader.prototype.perspectiveUniform = function () {\n        return this.pUniform;\n    };\n    /**\n     * Flush\n     * @private\n     */\n    WGLShader.prototype.reset = function () {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform1i(this.isBubbleUniform, 0);\n            this.gl.uniform1i(this.isCircleUniform, 0);\n        }\n    };\n    /**\n     * Set bubble uniforms\n     * @private\n     * @param {Highcharts.Series} series\n     * Series to use\n     */\n    WGLShader.prototype.setBubbleUniforms = function (series, zCalcMin, zCalcMax, pixelRatio) {\n        if (pixelRatio === void 0) { pixelRatio = 1; }\n        var seriesOptions = series.options;\n        var zMin = Number.MAX_VALUE,\n            zMax = -Number.MAX_VALUE;\n        if (this.gl && this.shaderProgram && series.is('bubble')) {\n            var pxSizes = series.getPxExtremes();\n            zMin = WGLShader_pick(seriesOptions.zMin, clamp(zCalcMin, seriesOptions.displayNegative === false ?\n                seriesOptions.zThreshold : -Number.MAX_VALUE, zMin));\n            zMax = WGLShader_pick(seriesOptions.zMax, Math.max(zMax, zCalcMax));\n            this.gl.uniform1i(this.isBubbleUniform, 1);\n            this.gl.uniform1i(this.isCircleUniform, 1);\n            this.gl.uniform1i(this.bubbleSizeAreaUniform, (series.options.sizeBy !== 'width'));\n            this.gl.uniform1i(this.bubbleSizeAbsUniform, series.options\n                .sizeByAbsoluteValue);\n            this.setUniform('bubbleMinSize', pxSizes.minPxSize * pixelRatio);\n            this.setUniform('bubbleMaxSize', pxSizes.maxPxSize * pixelRatio);\n            this.setUniform('bubbleZMin', zMin);\n            this.setUniform('bubbleZMax', zMax);\n            this.setUniform('bubbleZThreshold', series.options.zThreshold);\n        }\n    };\n    /**\n     * Set the Color uniform.\n     * @private\n     * @param {Array<number>} color\n     * Array with RGBA values.\n     */\n    WGLShader.prototype.setColor = function (color) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform4f(this.fcUniform, color[0] / 255.0, color[1] / 255.0, color[2] / 255.0, color[3]);\n        }\n    };\n    /**\n     * Enable/disable circle drawing\n     * @private\n     */\n    WGLShader.prototype.setDrawAsCircle = function (flag) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform1i(this.isCircleUniform, flag ? 1 : 0);\n        }\n    };\n    /**\n     * Set if inversion state\n     * @private\n     * @param {number} flag\n     * Inversion flag\n     */\n    WGLShader.prototype.setInverted = function (flag) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform1i(this.isInverted, flag);\n        }\n    };\n    /**\n     * Set the perspective matrix\n     * @private\n     * @param {Float32List} m\n     * Matrix 4 x 4\n     */\n    WGLShader.prototype.setPMatrix = function (m) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniformMatrix4fv(this.pUniform, false, m);\n        }\n    };\n    /**\n     * Set the point size.\n     * @private\n     * @param {number} p\n     * Point size\n     */\n    WGLShader.prototype.setPointSize = function (p) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform1f(this.psUniform, p);\n        }\n    };\n    /**\n     * Set skip translation\n     * @private\n     */\n    WGLShader.prototype.setSkipTranslation = function (flag) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform1i(this.skipTranslationUniform, flag === true ? 1 : 0);\n        }\n    };\n    /**\n     * Set the active texture\n     * @private\n     * @param {number} texture\n     * Texture to activate\n     */\n    WGLShader.prototype.setTexture = function (texture) {\n        if (this.gl && this.shaderProgram) {\n            this.gl.uniform1i(this.uSamplerUniform, texture);\n        }\n    };\n    /**\n     * Set a uniform value.\n     * This uses a hash map to cache uniform locations.\n     * @private\n     * @param {string} name\n     * Name of the uniform to set.\n     * @param {number} val\n     * Value to set\n     */\n    WGLShader.prototype.setUniform = function (name, val) {\n        if (this.gl && this.shaderProgram) {\n            var u = this.uLocations[name] = (this.uLocations[name] ||\n                    this.gl.getUniformLocation(this.shaderProgram,\n                name));\n            this.gl.uniform1f(u, val);\n        }\n    };\n    return WGLShader;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Boost_WGLShader = (WGLShader);\n\n;// ./code/es5/es-modules/Extensions/Boost/WGLVertexBuffer.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Class\n *\n * */\n/**\n * Vertex Buffer abstraction.\n * A vertex buffer is a set of vertices which are passed to the GPU\n * in a single call.\n *\n * @private\n * @class\n * @name WGLVertexBuffer\n *\n * @param {WebGLContext} gl\n * Context in which to create the buffer.\n * @param {WGLShader} shader\n * Shader to use.\n */\nvar WGLVertexBuffer = /** @class */ (function () {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    function WGLVertexBuffer(gl, shader, dataComponents\n    /* , type */\n    ) {\n        /* *\n         *\n         *  Properties\n         *\n         * */\n        this.buffer = false;\n        this.iterator = 0;\n        this.preAllocated = false;\n        this.vertAttribute = false;\n        this.components = dataComponents || 2;\n        this.dataComponents = dataComponents;\n        this.gl = gl;\n        this.shader = shader;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Note about pre-allocated buffers:\n     *     - This is slower for charts with many series\n     * @private\n     */\n    WGLVertexBuffer.prototype.allocate = function (size) {\n        this.iterator = -1;\n        this.preAllocated = new Float32Array(size * 4);\n    };\n    /**\n     * Bind the buffer\n     * @private\n     */\n    WGLVertexBuffer.prototype.bind = function () {\n        if (!this.buffer) {\n            return false;\n        }\n        /// gl.bindAttribLocation(shader.program(), 0, 'aVertexPosition');\n        // gl.enableVertexAttribArray(vertAttribute);\n        // gl.bindBuffer(gl.ARRAY_BUFFER, buffer);\n        this.gl.vertexAttribPointer(this.vertAttribute, this.components, this.gl.FLOAT, false, 0, 0);\n        /// gl.enableVertexAttribArray(vertAttribute);\n    };\n    /**\n     * Build the buffer\n     * @private\n     * @param {Array<number>} dataIn\n     * Zero padded array of indices\n     * @param {string} attrib\n     * Name of the Attribute to bind the buffer to\n     * @param {number} dataComponents\n     * Number of components per. indice\n     */\n    WGLVertexBuffer.prototype.build = function (dataIn, attrib, dataComponents) {\n        var farray;\n        this.data = dataIn || [];\n        if ((!this.data || this.data.length === 0) && !this.preAllocated) {\n            /// console.error('trying to render empty vbuffer');\n            this.destroy();\n            return false;\n        }\n        this.components = dataComponents || this.components;\n        if (this.buffer) {\n            this.gl.deleteBuffer(this.buffer);\n        }\n        if (!this.preAllocated) {\n            farray = new Float32Array(this.data);\n        }\n        this.buffer = this.gl.createBuffer();\n        this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.buffer);\n        this.gl.bufferData(this.gl.ARRAY_BUFFER, this.preAllocated || farray, this.gl.STATIC_DRAW);\n        /// gl.bindAttribLocation(shader.program(), 0, 'aVertexPosition');\n        this.vertAttribute = this.gl\n            .getAttribLocation(this.shader.getProgram(), attrib);\n        this.gl.enableVertexAttribArray(this.vertAttribute);\n        // Trigger cleanup\n        farray = false;\n        return true;\n    };\n    /**\n     * @private\n     */\n    WGLVertexBuffer.prototype.destroy = function () {\n        if (this.buffer) {\n            this.gl.deleteBuffer(this.buffer);\n            this.buffer = false;\n            this.vertAttribute = false;\n        }\n        this.iterator = 0;\n        this.components = this.dataComponents || 2;\n        this.data = [];\n    };\n    /**\n     * Adds data to the pre-allocated buffer.\n     * @private\n     * @param {number} x\n     * X data\n     * @param {number} y\n     * Y data\n     * @param {number} a\n     * A data\n     * @param {number} b\n     * B data\n     */\n    WGLVertexBuffer.prototype.push = function (x, y, a, b) {\n        if (this.preAllocated) { // && iterator <= preAllocated.length - 4) {\n            this.preAllocated[++this.iterator] = x;\n            this.preAllocated[++this.iterator] = y;\n            this.preAllocated[++this.iterator] = a;\n            this.preAllocated[++this.iterator] = b;\n        }\n    };\n    /**\n     * Render the buffer\n     *\n     * @private\n     * @param {number} from\n     * Start indice.\n     * @param {number} to\n     * End indice.\n     * @param {WGLDrawModeValue} drawMode\n     * Draw mode.\n     */\n    WGLVertexBuffer.prototype.render = function (from, to, drawMode) {\n        var length = this.preAllocated ?\n                this.preAllocated.length : this.data.length;\n        if (!this.buffer) {\n            return false;\n        }\n        if (!length) {\n            return false;\n        }\n        if (!from || from > length || from < 0) {\n            from = 0;\n        }\n        if (!to || to > length) {\n            to = length;\n        }\n        if (from >= to) {\n            return false;\n        }\n        drawMode = drawMode || 'POINTS';\n        this.gl.drawArrays(this.gl[drawMode], from / this.components, (to - from) / this.components);\n        return true;\n    };\n    return WGLVertexBuffer;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Boost_WGLVertexBuffer = (WGLVertexBuffer);\n\n;// ./code/es5/es-modules/Extensions/Boost/WGLRenderer.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar color = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default()).parse;\n\nvar doc = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).doc, win = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).win;\n\nvar isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, isObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isObject, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, objectEach = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).objectEach, WGLRenderer_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n\n\n\n/* *\n *\n *  Constants\n *\n * */\n// Things to draw as \"rectangles\" (i.e lines)\nvar asBar = {\n    'column': true,\n    'columnrange': true,\n    'bar': true,\n    'area': true,\n    'areaspline': true,\n    'arearange': true\n};\nvar asCircle = {\n    'scatter': true,\n    'bubble': true\n};\nvar contexts = [\n    'webgl',\n    'experimental-webgl',\n    'moz-webgl',\n    'webkit-3d'\n];\n/* *\n *\n *  Class\n *\n * */\n/* eslint-disable valid-jsdoc */\n/**\n * Main renderer. Used to render series.\n *\n * Notes to self:\n * - May be able to build a point map by rendering to a separate canvas and\n *   encoding values in the color data.\n * - Need to figure out a way to transform the data quicker\n *\n * @private\n *\n * @param {Function} postRenderCallback\n */\nvar WGLRenderer = /** @class */ (function () {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    function WGLRenderer(postRenderCallback) {\n        /**\n         * The data to render - array of coordinates.\n         * Repeating sequence of [x, y, checkThreshold, pointSize].\n         */\n        this.data = [];\n        // Height of our viewport in pixels\n        this.height = 0;\n        // Is it inited?\n        this.isInited = false;\n        // The marker data\n        this.markerData = [];\n        // The series stack\n        this.series = [];\n        // Texture handles\n        this.textureHandles = {};\n        // Width of our viewport in pixels\n        this.width = 0;\n        this.postRenderCallback = postRenderCallback;\n        this.settings = {\n            pointSize: 1,\n            lineWidth: 1,\n            fillColor: '#AA00AA',\n            useAlpha: true,\n            usePreallocated: false,\n            useGPUTranslations: false,\n            debug: {\n                timeRendering: false,\n                timeSeriesProcessing: false,\n                timeSetup: false,\n                timeBufferCopy: false,\n                timeKDTree: false,\n                showSkipSummary: false\n            }\n        };\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Returns an orthographic perspective matrix\n     * @private\n     * @param {number} width\n     * the width of the viewport in pixels\n     * @param {number} height\n     * the height of the viewport in pixels\n     */\n    WGLRenderer.orthoMatrix = function (width, height) {\n        var near = 0,\n            far = 1;\n        return [\n            2 / width, 0, 0, 0,\n            0, -(2 / height), 0, 0,\n            0, 0, -2 / (far - near), 0,\n            -1, 1, -(far + near) / (far - near), 1\n        ];\n    };\n    /**\n     * @private\n     */\n    WGLRenderer.seriesPointCount = function (series) {\n        var isStacked,\n            xData,\n            s;\n        if (series.boosted) {\n            isStacked = !!series.options.stacking;\n            xData = ((series.getColumn('x').length ?\n                series.getColumn('x') :\n                void 0) ||\n                series.options.xData ||\n                series.getColumn('x', true));\n            s = (isStacked ? series.data : (xData || series.options.data))\n                .length;\n            if (series.type === 'treemap') {\n                s *= 12;\n            }\n            else if (series.type === 'heatmap') {\n                s *= 6;\n            }\n            else if (asBar[series.type]) {\n                s *= 2;\n            }\n            return s;\n        }\n        return 0;\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    WGLRenderer.prototype.getPixelRatio = function () {\n        return this.settings.pixelRatio || win.devicePixelRatio || 1;\n    };\n    /**\n     * @private\n     */\n    WGLRenderer.prototype.setOptions = function (options) {\n        // The pixelRatio defaults to 1. This is an antipattern, we should\n        // refactor the Boost options to include an object of default options as\n        // base for the merge, like other components.\n        if (!('pixelRatio' in options)) {\n            options.pixelRatio = 1;\n        }\n        merge(true, this.settings, options);\n    };\n    /**\n     * Allocate a float buffer to fit all series\n     * @private\n     */\n    WGLRenderer.prototype.allocateBuffer = function (chart) {\n        var vbuffer = this.vbuffer;\n        var s = 0;\n        if (!this.settings.usePreallocated) {\n            return;\n        }\n        chart.series.forEach(function (series) {\n            if (series.boosted) {\n                s += WGLRenderer.seriesPointCount(series);\n            }\n        });\n        vbuffer && vbuffer.allocate(s);\n    };\n    /**\n     * @private\n     */\n    WGLRenderer.prototype.allocateBufferForSingleSeries = function (series) {\n        var vbuffer = this.vbuffer;\n        var s = 0;\n        if (!this.settings.usePreallocated) {\n            return;\n        }\n        if (series.boosted) {\n            s = WGLRenderer.seriesPointCount(series);\n        }\n        vbuffer && vbuffer.allocate(s);\n    };\n    /**\n     * Clear the depth and color buffer\n     * @private\n     */\n    WGLRenderer.prototype.clear = function () {\n        var gl = this.gl;\n        gl && gl.clear(gl.COLOR_BUFFER_BIT | gl.DEPTH_BUFFER_BIT);\n    };\n    /**\n     * Push data for a single series\n     * This calculates additional vertices and transforms the data to be\n     * aligned correctly in memory\n     * @private\n     */\n    WGLRenderer.prototype.pushSeriesData = function (series, inst) {\n        var _this = this;\n        var _a,\n            _b;\n        var data = this.data, settings = this.settings, vbuffer = this.vbuffer, isRange = (series.pointArrayMap &&\n                series.pointArrayMap.join(',') === 'low,high'), chart = series.chart, options = series.options, sorted = series.sorted, xAxis = series.xAxis, yAxis = series.yAxis, isStacked = !!options.stacking, rawData = options.data, xExtremes = series.xAxis.getExtremes(), \n            // Taking into account the offset of the min point #19497\n            xMin = xExtremes.min - (series.xAxis.minPointOffset || 0), xMax = xExtremes.max + (series.xAxis.minPointOffset || 0), yExtremes = series.yAxis.getExtremes(), yMin = yExtremes.min - (series.yAxis.minPointOffset || 0), yMax = yExtremes.max + (series.yAxis.minPointOffset || 0), xData = (series.getColumn('x').length ? series.getColumn('x') : void 0) || options.xData || series.getColumn('x', true), yData = (series.getColumn('y').length ? series.getColumn('y') : void 0) || options.yData || series.getColumn('y', true), zData = (series.getColumn('z').length ? series.getColumn('z') : void 0) || options.zData || series.getColumn('z', true), useRaw = !xData || xData.length === 0, \n            /// threshold = options.threshold,\n            // yBottom = chart.yAxis[0].getThreshold(threshold),\n            // hasThreshold = isNumber(threshold),\n            // colorByPoint = series.options.colorByPoint,\n            // This is required for color by point, so make sure this is\n            // uncommented if enabling that\n            // colorIndex = 0,\n            // Required for color axis support\n            // caxis,\n            connectNulls = options.connectNulls, \n            // For some reason eslint/TypeScript don't pick up that this is\n            // actually used: --- bre1470: it is never read, just set\n            // maxVal: (number|undefined), // eslint-disable-line no-unused-vars\n            points = series.points || false, sdata = isStacked ? series.data : (xData || rawData), closestLeft = { x: Number.MAX_VALUE, y: 0 }, closestRight = { x: -Number.MAX_VALUE, y: 0 }, cullXThreshold = 1, cullYThreshold = 1, chartDestroyed = typeof chart.index === 'undefined', drawAsBar = asBar[series.type], zoneAxis = options.zoneAxis || 'y', zones = options.zones || false, threshold = options.threshold, pixelRatio = this.getPixelRatio();\n        var plotWidth = series.chart.plotWidth,\n            lastX = false,\n            lastY = false,\n            minVal,\n            scolor, \n            //\n            skipped = 0,\n            hadPoints = false, \n            // The following are used in the builder while loop\n            x,\n            y,\n            d,\n            z,\n            i = -1,\n            px = false,\n            nx = false,\n            low,\n            nextInside = false,\n            prevInside = false,\n            pcolor = false,\n            isXInside = false,\n            isYInside = true,\n            firstPoint = true,\n            zoneColors,\n            zoneDefColor = false,\n            gapSize = false,\n            vlen = 0;\n        if (options.boostData && options.boostData.length > 0) {\n            return;\n        }\n        if (options.gapSize) {\n            gapSize = options.gapUnit !== 'value' ?\n                options.gapSize * series.closestPointRange :\n                options.gapSize;\n        }\n        if (zones) {\n            zoneColors = [];\n            zones.forEach(function (zone, i) {\n                if (zone.color) {\n                    var zoneColor = color(zone.color).rgba;\n                    zoneColor[0] /= 255.0;\n                    zoneColor[1] /= 255.0;\n                    zoneColor[2] /= 255.0;\n                    zoneColors[i] = zoneColor;\n                    if (!zoneDefColor && typeof zone.value === 'undefined') {\n                        zoneDefColor = zoneColor;\n                    }\n                }\n            });\n            if (!zoneDefColor) {\n                var seriesColor = ((series.pointAttribs && series.pointAttribs().fill) ||\n                        series.color);\n                zoneDefColor = color(seriesColor).rgba;\n                zoneDefColor[0] /= 255.0;\n                zoneDefColor[1] /= 255.0;\n                zoneDefColor[2] /= 255.0;\n            }\n        }\n        if (chart.inverted) {\n            plotWidth = series.chart.plotHeight;\n        }\n        series.closestPointRangePx = Number.MAX_VALUE;\n        /**\n         * Push color to color buffer - need to do this per vertex.\n         * @private\n         */\n        var pushColor = function (color) {\n                if (color) {\n                    inst.colorData.push(color[0]);\n                inst.colorData.push(color[1]);\n                inst.colorData.push(color[2]);\n                inst.colorData.push(color[3]);\n            }\n        };\n        /**\n         * Push a vertice to the data buffer.\n         * @private\n         */\n        var vertice = function (x,\n            y,\n            checkTreshold,\n            pointSize,\n            color) {\n                if (pointSize === void 0) { pointSize = 1; }\n                pushColor(color);\n            // Correct for pixel ratio\n            if (pixelRatio !== 1 && (!settings.useGPUTranslations ||\n                inst.skipTranslation)) {\n                x *= pixelRatio;\n                y *= pixelRatio;\n                pointSize *= pixelRatio;\n            }\n            if (settings.usePreallocated && vbuffer) {\n                vbuffer.push(x, y, checkTreshold ? 1 : 0, pointSize);\n                vlen += 4;\n            }\n            else {\n                data.push(x);\n                data.push(y);\n                data.push(checkTreshold ? pixelRatio : 0);\n                data.push(pointSize);\n            }\n        };\n        /**\n         * @private\n         */\n        var closeSegment = function () {\n                if (inst.segments.length) {\n                    inst.segments[inst.segments.length - 1].to = data.length || vlen;\n            }\n        };\n        /**\n         * Create a new segment for the current set.\n         * @private\n         */\n        var beginSegment = function () {\n                // Insert a segment on the series.\n                // A segment is just a start indice.\n                // When adding a segment, if one exists from before, it should\n                // set the previous segment's end\n                if (inst.segments.length &&\n                    inst.segments[inst.segments.length - 1].from === (data.length || vlen)) {\n                    return;\n            }\n            closeSegment();\n            inst.segments.push({\n                from: data.length || vlen\n            });\n        };\n        /**\n         * Push a rectangle to the data buffer.\n         * @private\n         */\n        var pushRect = function (x,\n            y,\n            w,\n            h,\n            color) {\n                pushColor(color);\n            vertice(x + w, y);\n            pushColor(color);\n            vertice(x, y);\n            pushColor(color);\n            vertice(x, y + h);\n            pushColor(color);\n            vertice(x, y + h);\n            pushColor(color);\n            vertice(x + w, y + h);\n            pushColor(color);\n            vertice(x + w, y);\n        };\n        // Create the first segment\n        beginSegment();\n        // Special case for point shapes\n        if (points && points.length > 0) {\n            // If we're doing points, we assume that the points are already\n            // translated, so we skip the shader translation.\n            inst.skipTranslation = true;\n            // Force triangle draw mode\n            inst.drawMode = 'TRIANGLES';\n            // We don't have a z component in the shader, so we need to sort.\n            if (points[0].node && points[0].node.levelDynamic) {\n                points.sort(function (a, b) {\n                    if (a.node) {\n                        if (a.node.levelDynamic >\n                            b.node.levelDynamic) {\n                            return 1;\n                        }\n                        if (a.node.levelDynamic <\n                            b.node.levelDynamic) {\n                            return -1;\n                        }\n                    }\n                    return 0;\n                });\n            }\n            points.forEach(function (point) {\n                var plotY = point.plotY;\n                var swidth,\n                    pointAttr;\n                if (typeof plotY !== 'undefined' &&\n                    !isNaN(plotY) &&\n                    point.y !== null &&\n                    point.shapeArgs) {\n                    var _a = point.shapeArgs,\n                        _b = _a.x,\n                        x_1 = _b === void 0 ? 0 : _b,\n                        _c = _a.y,\n                        y_1 = _c === void 0 ? 0 : _c,\n                        _d = _a.width,\n                        width = _d === void 0 ? 0 : _d,\n                        _e = _a.height,\n                        height = _e === void 0 ? 0 : _e;\n                    pointAttr = chart.styledMode ?\n                        point.series\n                            .colorAttribs(point) :\n                        pointAttr = point.series.pointAttribs(point);\n                    swidth = pointAttr['stroke-width'] || 0;\n                    // Handle point colors\n                    pcolor = color(pointAttr.fill).rgba;\n                    pcolor[0] /= 255.0;\n                    pcolor[1] /= 255.0;\n                    pcolor[2] /= 255.0;\n                    // So there are two ways of doing this. Either we can\n                    // create a rectangle of two triangles, or we can do a\n                    // point and use point size. Latter is faster, but\n                    // only supports squares. So we're doing triangles.\n                    // We could also use one color per. vertice to get\n                    // better color interpolation.\n                    // If there's stroking, we do an additional rect\n                    if (series.is('treemap')) {\n                        swidth = swidth || 1;\n                        scolor = color(pointAttr.stroke).rgba;\n                        scolor[0] /= 255.0;\n                        scolor[1] /= 255.0;\n                        scolor[2] /= 255.0;\n                        pushRect(x_1, y_1, width, height, scolor);\n                        swidth /= 2;\n                    }\n                    // } else {\n                    //     swidth = 0;\n                    // }\n                    // Fixes issues with inverted heatmaps (see #6981). The root\n                    // cause is that the coordinate system is flipped. In other\n                    // words, instead of [0,0] being top-left, it's\n                    // bottom-right. This causes a vertical and horizontal flip\n                    // in the resulting image, making it rotated 180 degrees.\n                    if (series.is('heatmap') && chart.inverted) {\n                        x_1 = xAxis.len - x_1;\n                        y_1 = yAxis.len - y_1;\n                        width = -width;\n                        height = -height;\n                    }\n                    pushRect(x_1 + swidth, y_1 + swidth, width - (swidth * 2), height - (swidth * 2), pcolor);\n                }\n            });\n            closeSegment();\n            return;\n        }\n        var _loop_1 = function () {\n                d = sdata[++i];\n            if (typeof d === 'undefined') {\n                return \"continue\";\n            }\n            /// px = x = y = z = nx = low = false;\n            // chartDestroyed = typeof chart.index === 'undefined';\n            // nextInside = prevInside = pcolor = isXInside = isYInside = false;\n            // drawAsBar = asBar[series.type];\n            if (chartDestroyed) {\n                return \"break\";\n            }\n            // Uncomment this to enable color by point.\n            // This currently left disabled as the charts look really ugly\n            // when enabled and there's a lot of points.\n            // Leaving in for the future (tm).\n            // if (colorByPoint) {\n            //     colorIndex = ++colorIndex %\n            //         series.chart.options.colors.length;\n            //     pcolor = toRGBAFast(series.chart.options.colors[colorIndex]);\n            //     pcolor[0] /= 255.0;\n            //     pcolor[1] /= 255.0;\n            //     pcolor[2] /= 255.0;\n            // }\n            // Handle the point.color option (#5999)\n            var pointOptions = rawData && rawData[i];\n            if (!useRaw && isObject(pointOptions, true)) {\n                if (pointOptions.color) {\n                    pcolor = color(pointOptions.color).rgba;\n                    pcolor[0] /= 255.0;\n                    pcolor[1] /= 255.0;\n                    pcolor[2] /= 255.0;\n                }\n            }\n            if (useRaw) {\n                x = d[0];\n                y = d[1];\n                if (sdata[i + 1]) {\n                    nx = sdata[i + 1][0];\n                }\n                if (sdata[i - 1]) {\n                    px = sdata[i - 1][0];\n                }\n                if (d.length >= 3) {\n                    z = d[2];\n                    if (d[2] > inst.zMax) {\n                        inst.zMax = d[2];\n                    }\n                    if (d[2] < inst.zMin) {\n                        inst.zMin = d[2];\n                    }\n                }\n            }\n            else {\n                x = d;\n                y = yData === null || yData === void 0 ? void 0 : yData[i];\n                if (sdata[i + 1]) {\n                    nx = sdata[i + 1];\n                }\n                if (sdata[i - 1]) {\n                    px = sdata[i - 1];\n                }\n                if (zData && zData.length) {\n                    z = zData[i];\n                    if (zData[i] > inst.zMax) {\n                        inst.zMax = zData[i];\n                    }\n                    if (zData[i] < inst.zMin) {\n                        inst.zMin = zData[i];\n                    }\n                }\n            }\n            if (!connectNulls && (x === null || y === null)) {\n                beginSegment();\n                return \"continue\";\n            }\n            if (nx && nx >= xMin && nx <= xMax) {\n                nextInside = true;\n            }\n            if (px && px >= xMin && px <= xMax) {\n                prevInside = true;\n            }\n            if (isRange) {\n                if (useRaw) {\n                    y = d.slice(1, 3);\n                }\n                low = (_a = series.getColumn('low', true)) === null || _a === void 0 ? void 0 : _a[i];\n                y = ((_b = series.getColumn('high', true)) === null || _b === void 0 ? void 0 : _b[i]) || 0;\n            }\n            else if (isStacked) {\n                x = d.x;\n                y = d.stackY;\n                low = y - d.y;\n            }\n            if (yMin !== null &&\n                typeof yMin !== 'undefined' &&\n                yMax !== null &&\n                typeof yMax !== 'undefined') {\n                isYInside = y >= yMin && y <= yMax;\n            }\n            // Do not render points outside the zoomed range (#19701)\n            if (!sorted && !isYInside) {\n                return \"continue\";\n            }\n            if (x > xMax && closestRight.x < xMax) {\n                closestRight.x = x;\n                closestRight.y = y;\n            }\n            if (x < xMin && closestLeft.x > xMin) {\n                closestLeft.x = x;\n                closestLeft.y = y;\n            }\n            if (y === null && connectNulls) {\n                return \"continue\";\n            }\n            // Cull points outside the extremes\n            // Continue if `sdata` has only one point as `nextInside` asserts\n            // whether the next point exists and will thus be false. (#22194)\n            if (y === null || (!isYInside && sdata.length > 1 &&\n                !nextInside && !prevInside)) {\n                beginSegment();\n                return \"continue\";\n            }\n            // The first point before and first after extremes should be\n            // rendered (#9962, 19701)\n            // Make sure series with a single point are rendered (#21897)\n            if (sorted && ((nx >= xMin || x >= xMin) &&\n                (px <= xMax || x <= xMax)) ||\n                !sorted && ((x >= xMin) && (x <= xMax))) {\n                isXInside = true;\n            }\n            if (!isXInside && !nextInside && !prevInside) {\n                return \"continue\";\n            }\n            if (gapSize && x - px > gapSize) {\n                beginSegment();\n            }\n            // Note: Boost requires that zones are sorted!\n            if (zones) {\n                var zoneColor_1;\n                zones.some(function (// eslint-disable-line no-loop-func\n                zone, i) {\n                    var last = zones[i - 1];\n                    if (zoneAxis === 'x') {\n                        if (typeof zone.value !== 'undefined' &&\n                            x <= zone.value) {\n                            if (zoneColors[i] &&\n                                (!last || x >= last.value)) {\n                                zoneColor_1 = zoneColors[i];\n                            }\n                            return true;\n                        }\n                        return false;\n                    }\n                    if (typeof zone.value !== 'undefined' && y <= zone.value) {\n                        if (zoneColors[i] &&\n                            (!last || y >= last.value)) {\n                            zoneColor_1 = zoneColors[i];\n                        }\n                        return true;\n                    }\n                    return false;\n                });\n                pcolor = zoneColor_1 || zoneDefColor || pcolor;\n            }\n            // Skip translations - temporary floating point fix\n            if (!settings.useGPUTranslations) {\n                inst.skipTranslation = true;\n                x = xAxis.toPixels(x, true);\n                y = yAxis.toPixels(y, true);\n                // Make sure we're not drawing outside of the chart area.\n                // See #6594. Update: this is no longer required as far as I\n                // can tell. Leaving in for git blame in case there are edge\n                // cases I've not found. Having this in breaks #10246.\n                // if (y > plotHeight) {\n                // y = plotHeight;\n                // }\n                if (x > plotWidth) {\n                    // If this is rendered as a point, just skip drawing it\n                    // entirely, as we're not dependant on lineTo'ing to it.\n                    // See #8197\n                    if (inst.drawMode === 'POINTS') {\n                        return \"continue\";\n                    }\n                    // Having this here will clamp markers and make the angle\n                    // of the last line wrong. See 9166.\n                    // x = plotWidth;\n                }\n            }\n            // No markers on out of bounds things.\n            // Out of bound things are shown if and only if the next\n            // or previous point is inside the rect.\n            if (inst.hasMarkers && isXInside) {\n                /// x = Highcharts.correctFloat(\n                //     Math.min(Math.max(-1e5, xAxis.translate(\n                //         x,\n                //         0,\n                //         0,\n                //         0,\n                //         1,\n                //         0.5,\n                //         false\n                //     )), 1e5)\n                // );\n                if (lastX !== false) {\n                    series.closestPointRangePx = Math.min(series.closestPointRangePx, Math.abs(x - lastX));\n                }\n            }\n            // If the last _drawn_ point is closer to this point than the\n            // threshold, skip it. Shaves off 20-100ms in processing.\n            if (!settings.useGPUTranslations &&\n                !settings.usePreallocated &&\n                (lastX && Math.abs(x - lastX) < cullXThreshold) &&\n                (lastY && Math.abs(y - lastY) < cullYThreshold)) {\n                if (settings.debug.showSkipSummary) {\n                    ++skipped;\n                }\n                return \"continue\";\n            }\n            if (drawAsBar) {\n                minVal = low || 0;\n                if (low === false || typeof low === 'undefined') {\n                    if (y < 0) {\n                        minVal = y;\n                    }\n                    else {\n                        minVal = 0;\n                    }\n                }\n                if ((!isRange && !isStacked) ||\n                    yAxis.logarithmic // #16850\n                ) {\n                    minVal = Math.max(threshold === null ? yMin : threshold, // #5268\n                    yMin); // #8731\n                }\n                if (!settings.useGPUTranslations) {\n                    minVal = yAxis.toPixels(minVal, true);\n                }\n                // Need to add an extra point here\n                vertice(x, minVal, 0, 0, pcolor);\n            }\n            // Do step line if enabled.\n            // Draws an additional point at the old Y at the new X.\n            // See #6976.\n            if (options.step && !firstPoint) {\n                vertice(x, lastY, 0, 2, pcolor);\n            }\n            vertice(x, y, 0, series.type === 'bubble' ? (z || 1) : 2, pcolor);\n            // Uncomment this to support color axis.\n            // if (caxis) {\n            //     pcolor = color(caxis.toColor(y)).rgba;\n            //     inst.colorData.push(color[0] / 255.0);\n            //     inst.colorData.push(color[1] / 255.0);\n            //     inst.colorData.push(color[2] / 255.0);\n            //     inst.colorData.push(color[3]);\n            // }\n            lastX = x;\n            lastY = y;\n            hadPoints = true;\n            firstPoint = false;\n        };\n        // Extract color axis\n        // (chart.axes || []).forEach((a): void => {\n        //     if (H.ColorAxis && a instanceof H.ColorAxis) {\n        //         caxis = a;\n        //     }\n        // });\n        while (i < sdata.length - 1) {\n            var state_1 = _loop_1();\n            if (state_1 === \"break\")\n                break;\n        }\n        if (settings.debug.showSkipSummary) {\n            console.log('skipped points:', skipped); // eslint-disable-line no-console\n        }\n        var pushSupplementPoint = function (point,\n            atStart) {\n                if (!settings.useGPUTranslations) {\n                    inst.skipTranslation = true;\n                point.x = xAxis.toPixels(point.x, true);\n                point.y = yAxis.toPixels(point.y, true);\n            }\n            // We should only do this for lines, and we should ignore markers\n            // since there's no point here that would have a marker.\n            if (atStart) {\n                _this.data = [point.x, point.y, 0, 2].concat(_this.data);\n                return;\n            }\n            vertice(point.x, point.y, 0, 2);\n        };\n        if (!hadPoints &&\n            connectNulls !== false &&\n            series.drawMode === 'line_strip') {\n            if (closestLeft.x < Number.MAX_VALUE) {\n                // We actually need to push this *before* the complete buffer.\n                pushSupplementPoint(closestLeft, true);\n            }\n            if (closestRight.x > -Number.MAX_VALUE) {\n                pushSupplementPoint(closestRight);\n            }\n        }\n        closeSegment();\n    };\n    /**\n     * Push a series to the renderer\n     * If we render the series immediately, we don't have to loop later\n     * @private\n     * @param {Highchart.Series} s\n     * The series to push.\n     */\n    WGLRenderer.prototype.pushSeries = function (s) {\n        var markerData = this.markerData,\n            series = this.series,\n            settings = this.settings;\n        if (series.length > 0) {\n            if (series[series.length - 1].hasMarkers) {\n                series[series.length - 1].markerTo = markerData.length;\n            }\n        }\n        if (settings.debug.timeSeriesProcessing) {\n            console.time('building ' + s.type + ' series'); // eslint-disable-line no-console\n        }\n        var obj = {\n                segments: [],\n                markerFrom: markerData.length,\n                // Push RGBA values to this array to use per. point coloring.\n                // It should be 0-padded, so each component should be pushed in\n                // succession.\n                colorData: [],\n                series: s,\n                zMin: Number.MAX_VALUE,\n                zMax: -Number.MAX_VALUE,\n                hasMarkers: s.options.marker ?\n                    s.options.marker.enabled !== false :\n                    false,\n                showMarkers: true,\n                drawMode: Boost_WGLDrawMode[s.type] || 'LINE_STRIP'\n            };\n        if (s.index >= series.length) {\n            series.push(obj);\n        }\n        else {\n            series[s.index] = obj;\n        }\n        // Add the series data to our buffer(s)\n        this.pushSeriesData(s, obj);\n        if (settings.debug.timeSeriesProcessing) {\n            console.timeEnd('building ' + s.type + ' series'); // eslint-disable-line no-console\n        }\n    };\n    /**\n     * Flush the renderer.\n     * This removes pushed series and vertices.\n     * Should be called after clearing and before rendering\n     * @private\n     */\n    WGLRenderer.prototype.flush = function () {\n        var vbuffer = this.vbuffer;\n        this.data = [];\n        this.markerData = [];\n        this.series = [];\n        if (vbuffer) {\n            vbuffer.destroy();\n        }\n    };\n    /**\n     * Pass x-axis to shader\n     * @private\n     * @param {Highcharts.Axis} axis\n     * The x-axis.\n     */\n    WGLRenderer.prototype.setXAxis = function (axis) {\n        var shader = this.shader;\n        if (!shader) {\n            return;\n        }\n        var pixelRatio = this.getPixelRatio();\n        shader.setUniform('xAxisTrans', axis.transA * pixelRatio);\n        shader.setUniform('xAxisMin', axis.min);\n        shader.setUniform('xAxisMinPad', axis.minPixelPadding * pixelRatio);\n        shader.setUniform('xAxisPointRange', axis.pointRange);\n        shader.setUniform('xAxisLen', axis.len * pixelRatio);\n        shader.setUniform('xAxisPos', axis.pos * pixelRatio);\n        shader.setUniform('xAxisCVSCoord', (!axis.horiz));\n        shader.setUniform('xAxisIsLog', (!!axis.logarithmic));\n        shader.setUniform('xAxisReversed', (!!axis.reversed));\n    };\n    /**\n     * Pass y-axis to shader\n     * @private\n     * @param {Highcharts.Axis} axis\n     * The y-axis.\n     */\n    WGLRenderer.prototype.setYAxis = function (axis) {\n        var shader = this.shader;\n        if (!shader) {\n            return;\n        }\n        var pixelRatio = this.getPixelRatio();\n        shader.setUniform('yAxisTrans', axis.transA * pixelRatio);\n        shader.setUniform('yAxisMin', axis.min);\n        shader.setUniform('yAxisMinPad', axis.minPixelPadding * pixelRatio);\n        shader.setUniform('yAxisPointRange', axis.pointRange);\n        shader.setUniform('yAxisLen', axis.len * pixelRatio);\n        shader.setUniform('yAxisPos', axis.pos * pixelRatio);\n        shader.setUniform('yAxisCVSCoord', (!axis.horiz));\n        shader.setUniform('yAxisIsLog', (!!axis.logarithmic));\n        shader.setUniform('yAxisReversed', (!!axis.reversed));\n    };\n    /**\n     * Set the translation threshold\n     * @private\n     * @param {boolean} has\n     * Has threshold flag.\n     * @param {numbe} translation\n     * The threshold.\n     */\n    WGLRenderer.prototype.setThreshold = function (has, translation) {\n        var shader = this.shader;\n        if (!shader) {\n            return;\n        }\n        shader.setUniform('hasThreshold', has);\n        shader.setUniform('translatedThreshold', translation);\n    };\n    /**\n     * Render the data\n     * This renders all pushed series.\n     * @private\n     */\n    WGLRenderer.prototype.renderChart = function (chart) {\n        var _this = this;\n        var gl = this.gl,\n            settings = this.settings,\n            shader = this.shader,\n            vbuffer = this.vbuffer;\n        var pixelRatio = this.getPixelRatio();\n        if (chart) {\n            this.width = chart.chartWidth * pixelRatio;\n            this.height = chart.chartHeight * pixelRatio;\n        }\n        else {\n            return false;\n        }\n        var height = this.height,\n            width = this.width;\n        if (!gl || !shader || !width || !height) {\n            return false;\n        }\n        if (settings.debug.timeRendering) {\n            console.time('gl rendering'); // eslint-disable-line no-console\n        }\n        gl.canvas.width = width;\n        gl.canvas.height = height;\n        shader.bind();\n        gl.viewport(0, 0, width, height);\n        shader.setPMatrix(WGLRenderer.orthoMatrix(width, height));\n        if (settings.lineWidth > 1 && !(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isMS) {\n            gl.lineWidth(settings.lineWidth);\n        }\n        if (vbuffer) {\n            vbuffer.build(this.data, 'aVertexPosition', 4);\n            vbuffer.bind();\n        }\n        shader.setInverted(chart.inverted);\n        // Render the series\n        this.series.forEach(function (s, si) {\n            var _a,\n                _b,\n                _c;\n            var options = s.series.options,\n                shapeOptions = options.marker,\n                lineWidth = (typeof options.lineWidth !== 'undefined' ?\n                    options.lineWidth :\n                    1),\n                threshold = options.threshold,\n                hasThreshold = isNumber(threshold),\n                yBottom = s.series.yAxis.getThreshold(threshold),\n                translatedThreshold = yBottom,\n                showMarkers = WGLRenderer_pick(options.marker ? options.marker.enabled : null,\n                s.series.xAxis.isRadial ? true : null,\n                s.series.closestPointRangePx >\n                    2 * ((options.marker ?\n                        options.marker.radius :\n                        10) || 10)),\n                shapeTexture = _this.textureHandles[(shapeOptions && shapeOptions.symbol) ||\n                    s.series.symbol] || _this.textureHandles.circle;\n            var sindex,\n                cbuffer,\n                fillColor,\n                scolor = [];\n            if (s.segments.length === 0 ||\n                s.segments[0].from === s.segments[0].to) {\n                return;\n            }\n            if (shapeTexture.isReady) {\n                gl.bindTexture(gl.TEXTURE_2D, shapeTexture.handle);\n                shader.setTexture(shapeTexture.handle);\n            }\n            if (chart.styledMode) {\n                if (s.series.markerGroup === ((_a = s.series.chart.boost) === null || _a === void 0 ? void 0 : _a.markerGroup)) {\n                    // Create a temporary markerGroup to get the fill color\n                    delete s.series.markerGroup;\n                    s.series.markerGroup = s.series.plotGroup('markerGroup', 'markers', 'visible', 1, chart.seriesGroup).addClass('highcharts-tracker');\n                    fillColor = s.series.markerGroup.getStyle('fill');\n                    s.series.markerGroup.destroy();\n                    s.series.markerGroup = (_b = s.series.chart.boost) === null || _b === void 0 ? void 0 : _b.markerGroup;\n                }\n                else {\n                    fillColor = (_c = s.series.markerGroup) === null || _c === void 0 ? void 0 : _c.getStyle('fill');\n                }\n            }\n            else {\n                fillColor =\n                    (s.drawMode === 'POINTS' && // #14260\n                        s.series.pointAttribs &&\n                        s.series.pointAttribs().fill) ||\n                        s.series.color;\n                if (options.colorByPoint) {\n                    fillColor = s.series.chart.options.colors[si];\n                }\n            }\n            if (s.series.fillOpacity && options.fillOpacity) {\n                fillColor = new (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default())(fillColor).setOpacity(WGLRenderer_pick(options.fillOpacity, 1.0)).get();\n            }\n            scolor = color(fillColor).rgba;\n            if (!settings.useAlpha) {\n                scolor[3] = 1.0;\n            }\n            // Blending\n            if (options.boostBlending === 'add') {\n                gl.blendFunc(gl.SRC_ALPHA, gl.ONE);\n                gl.blendEquation(gl.FUNC_ADD);\n            }\n            else if (options.boostBlending === 'mult' ||\n                options.boostBlending === 'multiply') {\n                gl.blendFunc(gl.DST_COLOR, gl.ZERO);\n            }\n            else if (options.boostBlending === 'darken') {\n                gl.blendFunc(gl.ONE, gl.ONE);\n                gl.blendEquation(gl.FUNC_MIN);\n            }\n            else {\n                /// gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);\n                // gl.blendEquation(gl.FUNC_ADD);\n                gl.blendFuncSeparate(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA, gl.ONE, gl.ONE_MINUS_SRC_ALPHA);\n            }\n            shader.reset();\n            // If there are entries in the colorData buffer, build and bind it.\n            if (s.colorData.length > 0) {\n                shader.setUniform('hasColor', 1);\n                cbuffer = new Boost_WGLVertexBuffer(gl, shader);\n                cbuffer.build(\n                // The color array attribute for vertex is assigned from 0,\n                // so it needs to be shifted to be applied to further\n                // segments. #18858\n                Array(s.segments[0].from).concat(s.colorData), 'aColor', 4);\n                cbuffer.bind();\n            }\n            else {\n                // Set the hasColor uniform to false (0) when the series\n                // contains no colorData buffer points. #18858\n                shader.setUniform('hasColor', 0);\n                // #15869, a buffer with fewer points might already be bound by\n                // a different series/chart causing out of range errors\n                gl.disableVertexAttribArray(gl.getAttribLocation(shader.getProgram(), 'aColor'));\n            }\n            // Set series specific uniforms\n            shader.setColor(scolor);\n            _this.setXAxis(s.series.xAxis);\n            _this.setYAxis(s.series.yAxis);\n            _this.setThreshold(hasThreshold, translatedThreshold);\n            if (s.drawMode === 'POINTS') {\n                shader.setPointSize(WGLRenderer_pick(options.marker && options.marker.radius, 0.5) * 2 * pixelRatio);\n            }\n            // If set to true, the toPixels translations in the shader\n            // is skipped, i.e it's assumed that the value is a pixel coord.\n            shader.setSkipTranslation(s.skipTranslation);\n            if (s.series.type === 'bubble') {\n                shader.setBubbleUniforms(s.series, s.zMin, s.zMax, pixelRatio);\n            }\n            shader.setDrawAsCircle(asCircle[s.series.type] || false);\n            if (!vbuffer) {\n                return;\n            }\n            // Do the actual rendering\n            // If the line width is < 0, skip rendering of the lines. See #7833.\n            if (lineWidth > 0 || s.drawMode !== 'LINE_STRIP') {\n                for (sindex = 0; sindex < s.segments.length; sindex++) {\n                    vbuffer.render(s.segments[sindex].from, s.segments[sindex].to, s.drawMode);\n                }\n            }\n            if (s.hasMarkers && showMarkers) {\n                shader.setPointSize(WGLRenderer_pick(options.marker && options.marker.radius, 5) * 2 * pixelRatio);\n                shader.setDrawAsCircle(true);\n                for (sindex = 0; sindex < s.segments.length; sindex++) {\n                    vbuffer.render(s.segments[sindex].from, s.segments[sindex].to, 'POINTS');\n                }\n            }\n        });\n        if (settings.debug.timeRendering) {\n            console.timeEnd('gl rendering'); // eslint-disable-line no-console\n        }\n        if (this.postRenderCallback) {\n            this.postRenderCallback(this);\n        }\n        this.flush();\n    };\n    /**\n     * Render the data when ready\n     * @private\n     */\n    WGLRenderer.prototype.render = function (chart) {\n        var _this = this;\n        this.clear();\n        if (chart.renderer.forExport) {\n            return this.renderChart(chart);\n        }\n        if (this.isInited) {\n            this.renderChart(chart);\n        }\n        else {\n            setTimeout(function () {\n                _this.render(chart);\n            }, 1);\n        }\n    };\n    /**\n     * Set the viewport size in pixels\n     * Creates an orthographic perspective matrix and applies it.\n     * @private\n     */\n    WGLRenderer.prototype.setSize = function (width, height) {\n        var shader = this.shader;\n        // Skip if there's no change, or if we have no valid shader\n        if (!shader || (this.width === width && this.height === height)) {\n            return;\n        }\n        this.width = width;\n        this.height = height;\n        shader.bind();\n        shader.setPMatrix(WGLRenderer.orthoMatrix(width, height));\n    };\n    /**\n     * Init OpenGL\n     * @private\n     */\n    WGLRenderer.prototype.init = function (canvas, noFlush) {\n        var _this = this;\n        var settings = this.settings;\n        this.isInited = false;\n        if (!canvas) {\n            return false;\n        }\n        if (settings.debug.timeSetup) {\n            console.time('gl setup'); // eslint-disable-line no-console\n        }\n        for (var i = 0; i < contexts.length; ++i) {\n            this.gl = canvas.getContext(contexts[i], {\n            //    /premultipliedAlpha: false\n            });\n            if (this.gl) {\n                break;\n            }\n        }\n        var gl = this.gl;\n        if (gl) {\n            if (!noFlush) {\n                this.flush();\n            }\n        }\n        else {\n            return false;\n        }\n        gl.enable(gl.BLEND);\n        /// gl.blendFunc(gl.SRC_ALPHA, gl.ONE);\n        gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);\n        gl.disable(gl.DEPTH_TEST);\n        /// gl.depthMask(gl.FALSE);\n        gl.depthFunc(gl.LESS);\n        var shader = this.shader = new Boost_WGLShader(gl);\n        if (!shader) {\n            // We need to abort, there's no shader context\n            return false;\n        }\n        this.vbuffer = new Boost_WGLVertexBuffer(gl, shader);\n        var createTexture = function (name,\n            fn) {\n                var props = {\n                    isReady: false,\n                    texture: doc.createElement('canvas'),\n                    handle: gl.createTexture()\n                },\n            ctx = props.texture.getContext('2d');\n            _this.textureHandles[name] = props;\n            props.texture.width = 512;\n            props.texture.height = 512;\n            ctx.mozImageSmoothingEnabled = false;\n            ctx.webkitImageSmoothingEnabled = false;\n            ctx.msImageSmoothingEnabled = false;\n            ctx.imageSmoothingEnabled = false;\n            ctx.strokeStyle = 'rgba(255, 255, 255, 0)';\n            ctx.fillStyle = '#FFF';\n            fn(ctx);\n            try {\n                gl.activeTexture(gl.TEXTURE0);\n                gl.bindTexture(gl.TEXTURE_2D, props.handle);\n                /// gl.pixelStorei(gl.UNPACK_PREMULTIPLY_ALPHA_WEBGL, true);\n                gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, props.texture);\n                gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE);\n                gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE);\n                gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, gl.LINEAR);\n                gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, gl.LINEAR);\n                /// gl.generateMipmap(gl.TEXTURE_2D);\n                gl.bindTexture(gl.TEXTURE_2D, null);\n                props.isReady = true;\n            }\n            catch (e) {\n                // Silent error\n            }\n        };\n        // Circle shape\n        createTexture('circle', function (ctx) {\n            ctx.beginPath();\n            ctx.arc(256, 256, 256, 0, 2 * Math.PI);\n            ctx.stroke();\n            ctx.fill();\n        });\n        // Square shape\n        createTexture('square', function (ctx) {\n            ctx.fillRect(0, 0, 512, 512);\n        });\n        // Diamond shape\n        createTexture('diamond', function (ctx) {\n            ctx.beginPath();\n            ctx.moveTo(256, 0);\n            ctx.lineTo(512, 256);\n            ctx.lineTo(256, 512);\n            ctx.lineTo(0, 256);\n            ctx.lineTo(256, 0);\n            ctx.fill();\n        });\n        // Triangle shape\n        createTexture('triangle', function (ctx) {\n            ctx.beginPath();\n            ctx.moveTo(0, 512);\n            ctx.lineTo(256, 0);\n            ctx.lineTo(512, 512);\n            ctx.lineTo(0, 512);\n            ctx.fill();\n        });\n        // Triangle shape (rotated)\n        createTexture('triangle-down', function (ctx) {\n            ctx.beginPath();\n            ctx.moveTo(0, 0);\n            ctx.lineTo(256, 512);\n            ctx.lineTo(512, 0);\n            ctx.lineTo(0, 0);\n            ctx.fill();\n        });\n        this.isInited = true;\n        if (settings.debug.timeSetup) {\n            console.timeEnd('gl setup'); // eslint-disable-line no-console\n        }\n        return true;\n    };\n    /**\n     * @private\n     * @todo use it\n     */\n    WGLRenderer.prototype.destroy = function () {\n        var gl = this.gl,\n            shader = this.shader,\n            vbuffer = this.vbuffer;\n        this.flush();\n        if (vbuffer) {\n            vbuffer.destroy();\n        }\n        if (shader) {\n            shader.destroy();\n        }\n        if (gl) {\n            objectEach(this.textureHandles, function (texture) {\n                if (texture.handle) {\n                    gl.deleteTexture(texture.handle);\n                }\n            });\n            gl.canvas.width = 1;\n            gl.canvas.height = 1;\n        }\n    };\n    return WGLRenderer;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Boost_WGLRenderer = (WGLRenderer);\n\n;// ./code/es5/es-modules/Data/ColumnUtils.js\n/* *\n *\n *  (c) 2020-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\nvar __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\n/**\n * Utility functions for columns that can be either arrays or typed arrays.\n * @private\n */\nvar ColumnUtils;\n(function (ColumnUtils) {\n    /* *\n    *\n    *  Declarations\n    *\n    * */\n    /* *\n    *\n    * Functions\n    *\n    * */\n    /**\n     * Sets the length of the column array.\n     *\n     * @param {DataTable.Column} column\n     * Column to be modified.\n     *\n     * @param {number} length\n     * New length of the column.\n     *\n     * @param {boolean} asSubarray\n     * If column is a typed array, return a subarray instead of a new array. It\n     * is faster `O(1)`, but the entire buffer will be kept in memory until all\n     * views to it are destroyed. Default is `false`.\n     *\n     * @return {DataTable.Column}\n     * Modified column.\n     *\n     * @private\n     */\n    function setLength(column, length, asSubarray) {\n        if (Array.isArray(column)) {\n            column.length = length;\n            return column;\n        }\n        return column[asSubarray ? 'subarray' : 'slice'](0, length);\n    }\n    ColumnUtils.setLength = setLength;\n    /**\n     * Splices a column array.\n     *\n     * @param {DataTable.Column} column\n     * Column to be modified.\n     *\n     * @param {number} start\n     * Index at which to start changing the array.\n     *\n     * @param {number} deleteCount\n     * An integer indicating the number of old array elements to remove.\n     *\n     * @param {boolean} removedAsSubarray\n     * If column is a typed array, return a subarray instead of a new array. It\n     * is faster `O(1)`, but the entire buffer will be kept in memory until all\n     * views to it are destroyed. Default is `true`.\n     *\n     * @param {Array<number>|TypedArray} items\n     * The elements to add to the array, beginning at the start index. If you\n     * don't specify any elements, `splice()` will only remove elements from the\n     * array.\n     *\n     * @return {SpliceResult}\n     * Object containing removed elements and the modified column.\n     *\n     * @private\n     */\n    function splice(column, start, deleteCount, removedAsSubarray, items) {\n        if (items === void 0) { items = []; }\n        if (Array.isArray(column)) {\n            if (!Array.isArray(items)) {\n                items = Array.from(items);\n            }\n            return {\n                removed: column.splice.apply(column, __spreadArray([start, deleteCount], items, false)),\n                array: column\n            };\n        }\n        var Constructor = Object.getPrototypeOf(column)\n                .constructor;\n        var removed = column[removedAsSubarray ? 'subarray' : 'slice'](start,\n            start + deleteCount);\n        var newLength = column.length - deleteCount + items.length;\n        var result = new Constructor(newLength);\n        result.set(column.subarray(0, start), 0);\n        result.set(items, start);\n        result.set(column.subarray(start + deleteCount), start + items.length);\n        return {\n            removed: removed,\n            array: result\n        };\n    }\n    ColumnUtils.splice = splice;\n})(ColumnUtils || (ColumnUtils = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Data_ColumnUtils = (ColumnUtils);\n\n;// ./code/es5/es-modules/Data/DataTableCore.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Gøran Slettemark\n *  - Torstein Hønsi\n *\n * */\n\n\nvar setLength = Data_ColumnUtils.setLength, splice = Data_ColumnUtils.splice;\n\nvar fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, DataTableCore_objectEach = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).objectEach, uniqueKey = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).uniqueKey;\n/* *\n *\n *  Class\n *\n * */\n/**\n * Class to manage columns and rows in a table structure. It provides methods\n * to add, remove, and manipulate columns and rows, as well as to retrieve data\n * from specific cells.\n *\n * @class\n * @name Highcharts.DataTable\n *\n * @param {Highcharts.DataTableOptions} [options]\n * Options to initialize the new DataTable instance.\n */\nvar DataTableCore = /** @class */ (function () {\n    /**\n     * Constructs an instance of the DataTable class.\n     *\n     * @example\n     * const dataTable = new Highcharts.DataTableCore({\n     *   columns: {\n     *     year: [2020, 2021, 2022, 2023],\n     *     cost: [11, 13, 12, 14],\n     *     revenue: [12, 15, 14, 18]\n     *   }\n     * });\n\n     *\n     * @param {Highcharts.DataTableOptions} [options]\n     * Options to initialize the new DataTable instance.\n     */\n    function DataTableCore(options) {\n        if (options === void 0) { options = {}; }\n        var _this = this;\n        /**\n         * Whether the ID was automatic generated or given in the constructor.\n         *\n         * @name Highcharts.DataTable#autoId\n         * @type {boolean}\n         */\n        this.autoId = !options.id;\n        this.columns = {};\n        /**\n         * ID of the table for identification purposes.\n         *\n         * @name Highcharts.DataTable#id\n         * @type {string}\n         */\n        this.id = (options.id || uniqueKey());\n        this.modified = this;\n        this.rowCount = 0;\n        this.versionTag = uniqueKey();\n        var rowCount = 0;\n        DataTableCore_objectEach(options.columns || {}, function (column, columnName) {\n            _this.columns[columnName] = column.slice();\n            rowCount = Math.max(rowCount, column.length);\n        });\n        this.applyRowCount(rowCount);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Applies a row count to the table by setting the `rowCount` property and\n     * adjusting the length of all columns.\n     *\n     * @private\n     * @param {number} rowCount The new row count.\n     */\n    DataTableCore.prototype.applyRowCount = function (rowCount) {\n        var _this = this;\n        this.rowCount = rowCount;\n        DataTableCore_objectEach(this.columns, function (column, columnName) {\n            if (column.length !== rowCount) {\n                _this.columns[columnName] = setLength(column, rowCount);\n            }\n        });\n    };\n    /**\n     * Delete rows. Simplified version of the full\n     * `DataTable.deleteRows` method.\n     *\n     * @param {number} rowIndex\n     * The start row index\n     *\n     * @param {number} [rowCount=1]\n     * The number of rows to delete\n     *\n     * @return {void}\n     *\n     * @emits #afterDeleteRows\n     */\n    DataTableCore.prototype.deleteRows = function (rowIndex, rowCount) {\n        var _this = this;\n        if (rowCount === void 0) { rowCount = 1; }\n        if (rowCount > 0 && rowIndex < this.rowCount) {\n            var length_1 = 0;\n            DataTableCore_objectEach(this.columns, function (column, columnName) {\n                _this.columns[columnName] =\n                    splice(column, rowIndex, rowCount).array;\n                length_1 = column.length;\n            });\n            this.rowCount = length_1;\n        }\n        fireEvent(this, 'afterDeleteRows', { rowIndex: rowIndex, rowCount: rowCount });\n        this.versionTag = uniqueKey();\n    };\n    /**\n     * Fetches the given column by the canonical column name. Simplified version\n     * of the full `DataTable.getRow` method, always returning by reference.\n     *\n     * @param {string} columnName\n     * Name of the column to get.\n     *\n     * @return {Highcharts.DataTableColumn|undefined}\n     * A copy of the column, or `undefined` if not found.\n     */\n    DataTableCore.prototype.getColumn = function (columnName, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    asReference) {\n        return this.columns[columnName];\n    };\n    /**\n     * Retrieves all or the given columns. Simplified version of the full\n     * `DataTable.getColumns` method, always returning by reference.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names to retrieve.\n     *\n     * @return {Highcharts.DataTableColumnCollection}\n     * Collection of columns. If a requested column was not found, it is\n     * `undefined`.\n     */\n    DataTableCore.prototype.getColumns = function (columnNames, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    asReference) {\n        var _this = this;\n        return (columnNames || Object.keys(this.columns)).reduce(function (columns, columnName) {\n            columns[columnName] = _this.columns[columnName];\n            return columns;\n        }, {});\n    };\n    /**\n     * Retrieves the row at a given index.\n     *\n     * @param {number} rowIndex\n     * Row index to retrieve. First row has index 0.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names to retrieve.\n     *\n     * @return {Record<string, number|string|undefined>|undefined}\n     * Returns the row values, or `undefined` if not found.\n     */\n    DataTableCore.prototype.getRow = function (rowIndex, columnNames) {\n        var _this = this;\n        return (columnNames || Object.keys(this.columns)).map(function (key) { var _a; return (_a = _this.columns[key]) === null || _a === void 0 ? void 0 : _a[rowIndex]; });\n    };\n    /**\n     * Sets cell values for a column. Will insert a new column, if not found.\n     *\n     * @param {string} columnName\n     * Column name to set.\n     *\n     * @param {Highcharts.DataTableColumn} [column]\n     * Values to set in the column.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to change. (Default: 0)\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #setColumns\n     * @emits #afterSetColumns\n     */\n    DataTableCore.prototype.setColumn = function (columnName, column, rowIndex, eventDetail) {\n        var _a;\n        if (column === void 0) { column = []; }\n        if (rowIndex === void 0) { rowIndex = 0; }\n        this.setColumns((_a = {}, _a[columnName] = column, _a), rowIndex, eventDetail);\n    };\n    /**\n     * Sets cell values for multiple columns. Will insert new columns, if not\n     * found. Simplified version of the full `DataTableCore.setColumns`, limited\n     * to full replacement of the columns (undefined `rowIndex`).\n     *\n     * @param {Highcharts.DataTableColumnCollection} columns\n     * Columns as a collection, where the keys are the column names.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to change. Ignored in the `DataTableCore`, as it\n     * always replaces the full column.\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #setColumns\n     * @emits #afterSetColumns\n     */\n    DataTableCore.prototype.setColumns = function (columns, rowIndex, eventDetail) {\n        var _this = this;\n        var rowCount = this.rowCount;\n        DataTableCore_objectEach(columns, function (column, columnName) {\n            _this.columns[columnName] = column.slice();\n            rowCount = column.length;\n        });\n        this.applyRowCount(rowCount);\n        if (!(eventDetail === null || eventDetail === void 0 ? void 0 : eventDetail.silent)) {\n            fireEvent(this, 'afterSetColumns');\n            this.versionTag = uniqueKey();\n        }\n    };\n    /**\n     * Sets cell values of a row. Will insert a new row if no index was\n     * provided, or if the index is higher than the total number of table rows.\n     * A simplified version of the full `DateTable.setRow`, limited to objects.\n     *\n     * @param {Record<string, number|string|undefined>} row\n     * Cell values to set.\n     *\n     * @param {number} [rowIndex]\n     * Index of the row to set. Leave `undefined` to add as a new row.\n     *\n     * @param {boolean} [insert]\n     * Whether to insert the row at the given index, or to overwrite the row.\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #afterSetRows\n     */\n    DataTableCore.prototype.setRow = function (row, rowIndex, insert, eventDetail) {\n        if (rowIndex === void 0) { rowIndex = this.rowCount; }\n        var columns = this.columns,\n            indexRowCount = insert ? this.rowCount + 1 : rowIndex + 1;\n        DataTableCore_objectEach(row, function (cellValue, columnName) {\n            var column = columns[columnName] ||\n                    (eventDetail === null || eventDetail === void 0 ? void 0 : eventDetail.addColumns) !== false && new Array(indexRowCount);\n            if (column) {\n                if (insert) {\n                    column = splice(column, rowIndex, 0, true, [cellValue]).array;\n                }\n                else {\n                    column[rowIndex] = cellValue;\n                }\n                columns[columnName] = column;\n            }\n        });\n        if (indexRowCount > this.rowCount) {\n            this.applyRowCount(indexRowCount);\n        }\n        if (!(eventDetail === null || eventDetail === void 0 ? void 0 : eventDetail.silent)) {\n            fireEvent(this, 'afterSetRows');\n            this.versionTag = uniqueKey();\n        }\n    };\n    return DataTableCore;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Data_DataTableCore = (DataTableCore);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * A typed array.\n * @typedef {Int8Array|Uint8Array|Uint8ClampedArray|Int16Array|Uint16Array|Int32Array|Uint32Array|Float32Array|Float64Array} Highcharts.TypedArray\n * //**\n * A column of values in a data table.\n * @typedef {Array<boolean|null|number|string|undefined>|Highcharts.TypedArray} Highcharts.DataTableColumn\n */ /**\n* A collection of data table columns defined by a object where the key is the\n* column name and the value is an array of the column values.\n* @typedef {Record<string, Highcharts.DataTableColumn>} Highcharts.DataTableColumnCollection\n*/\n/**\n * Options for the `DataTable` or `DataTableCore` classes.\n * @interface Highcharts.DataTableOptions\n */ /**\n* The column options for the data table. The columns are defined by an object\n* where the key is the column ID and the value is an array of the column\n* values.\n*\n* @name Highcharts.DataTableOptions.columns\n* @type {Highcharts.DataTableColumnCollection|undefined}\n*/ /**\n* Custom ID to identify the new DataTable instance.\n*\n* @name Highcharts.DataTableOptions.id\n* @type {string|undefined}\n*/\n(''); // Keeps doclets above in JS file\n\n;// ./code/es5/es-modules/Extensions/Boost/BoostSeries.js\n/* *\n *\n *  (c) 2019-2025 Highsoft AS\n *\n *  Boost module: stripped-down renderer for higher performance\n *\n *  License: highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nvar BoostSeries_getBoostClipRect = Boost_BoostChart.getBoostClipRect, BoostSeries_isChartSeriesBoosting = Boost_BoostChart.isChartSeriesBoosting;\n\nvar getOptions = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).getOptions;\n\nvar BoostSeries_composed = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).composed, BoostSeries_doc = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).doc, noop = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).noop, BoostSeries_win = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).win;\n\nvar BoostSeries_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, destroyObjectProperties = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).destroyObjectProperties, BoostSeries_error = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).error, extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, BoostSeries_fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, isArray = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isArray, BoostSeries_isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, BoostSeries_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, BoostSeries_pushUnique = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pushUnique, wrap = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).wrap, defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined;\n\n\n/* *\n *\n *  Constants\n *\n * */\nvar CHUNK_SIZE = 3000;\n/* *\n *\n *  Variables\n *\n * */\nvar index, mainCanvas;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction allocateIfNotSeriesBoosting(renderer, series) {\n    var boost = series.boost;\n    if (renderer &&\n        boost &&\n        boost.target &&\n        boost.canvas &&\n        !BoostSeries_isChartSeriesBoosting(series.chart)) {\n        renderer.allocateBufferForSingleSeries(series);\n    }\n}\n/**\n * Return true if ths boost.enabled option is true\n *\n * @private\n * @param {Highcharts.Chart} chart\n * The chart\n * @return {boolean}\n * True, if boost is enabled.\n */\nfunction boostEnabled(chart) {\n    return BoostSeries_pick((chart &&\n        chart.options &&\n        chart.options.boost &&\n        chart.options.boost.enabled), true);\n}\n/**\n * @private\n */\nfunction BoostSeries_compose(SeriesClass, seriesTypes, PointClass, wglMode) {\n    if (BoostSeries_pushUnique(BoostSeries_composed, 'Boost.Series')) {\n        var plotOptions_1 = getOptions().plotOptions,\n            seriesProto_1 = SeriesClass.prototype;\n        BoostSeries_addEvent(SeriesClass, 'destroy', onSeriesDestroy);\n        BoostSeries_addEvent(SeriesClass, 'hide', onSeriesHide);\n        if (wglMode) {\n            seriesProto_1.renderCanvas = seriesRenderCanvas;\n        }\n        wrap(seriesProto_1, 'getExtremes', wrapSeriesGetExtremes);\n        wrap(seriesProto_1, 'processData', wrapSeriesProcessData);\n        wrap(seriesProto_1, 'searchPoint', wrapSeriesSearchPoint);\n        [\n            'translate',\n            'generatePoints',\n            'drawTracker',\n            'drawPoints',\n            'render'\n        ].forEach(function (method) {\n            return wrapSeriesFunctions(seriesProto_1, seriesTypes, method);\n        });\n        wrap(PointClass.prototype, 'firePointEvent', function (proceed, type, e) {\n            var _a,\n                _b;\n            if (type === 'click' && this.series.boosted) {\n                var point = e.point;\n                if ((point.dist || point.distX) >= ((_b = (_a = point.series.options.marker) === null || _a === void 0 ? void 0 : _a.radius) !== null && _b !== void 0 ? _b : 10)) {\n                    return;\n                }\n            }\n            return proceed.apply(this, [].slice.call(arguments, 1));\n        });\n        // Set default options\n        Boost_Boostables.forEach(function (type) {\n            var typePlotOptions = plotOptions_1[type];\n            if (typePlotOptions) {\n                typePlotOptions.boostThreshold = 5000;\n                typePlotOptions.boostData = [];\n                seriesTypes[type].prototype.fillOpacity = true;\n            }\n        });\n        if (wglMode) {\n            var AreaSeries = seriesTypes.area,\n                AreaSplineSeries = seriesTypes.areaspline,\n                BubbleSeries = seriesTypes.bubble,\n                ColumnSeries = seriesTypes.column,\n                HeatmapSeries = seriesTypes.heatmap,\n                ScatterSeries = seriesTypes.scatter,\n                TreemapSeries = seriesTypes.treemap;\n            if (AreaSeries) {\n                extend(AreaSeries.prototype, {\n                    fill: true,\n                    fillOpacity: true,\n                    sampling: true\n                });\n            }\n            if (AreaSplineSeries) {\n                extend(AreaSplineSeries.prototype, {\n                    fill: true,\n                    fillOpacity: true,\n                    sampling: true\n                });\n            }\n            if (BubbleSeries) {\n                var bubbleProto_1 = BubbleSeries.prototype;\n                // By default, the bubble series does not use the KD-tree, so\n                // force it to.\n                delete bubbleProto_1.buildKDTree;\n                // SeriesTypes.bubble.prototype.directTouch = false;\n                // Needed for markers to work correctly\n                wrap(bubbleProto_1, 'markerAttribs', function (proceed) {\n                    if (this.boosted) {\n                        return false;\n                    }\n                    return proceed.apply(this, [].slice.call(arguments, 1));\n                });\n            }\n            if (ColumnSeries) {\n                extend(ColumnSeries.prototype, {\n                    fill: true,\n                    sampling: true\n                });\n            }\n            if (ScatterSeries) {\n                ScatterSeries.prototype.fill = true;\n            }\n            // We need to handle heatmaps separately, since we can't perform the\n            // size/color calculations in the shader easily.\n            // @todo This likely needs future optimization.\n            [HeatmapSeries, TreemapSeries].forEach(function (SC) {\n                if (SC) {\n                    wrap(SC.prototype, 'drawPoints', wrapSeriesDrawPoints);\n                }\n            });\n        }\n    }\n    return SeriesClass;\n}\n/**\n * Create a canvas + context and attach it to the target\n *\n * @private\n * @function createAndAttachRenderer\n *\n * @param {Highcharts.Chart} chart\n * the chart\n *\n * @param {Highcharts.Series} series\n * the series\n *\n * @return {Highcharts.BoostGLRenderer}\n * the canvas renderer\n */\nfunction createAndAttachRenderer(chart, series) {\n    var _a,\n        _b,\n        _c;\n    var ChartClass = chart.constructor,\n        targetGroup = chart.seriesGroup || series.group,\n        alpha = 1;\n    var width = chart.chartWidth,\n        height = chart.chartHeight,\n        target = chart,\n        foSupported = typeof SVGForeignObjectElement !== 'undefined',\n        hasClickHandler = false;\n    if (BoostSeries_isChartSeriesBoosting(chart)) {\n        target = chart;\n    }\n    else {\n        target = series;\n        hasClickHandler = Boolean(((_a = series.options.events) === null || _a === void 0 ? void 0 : _a.click) ||\n            ((_c = (_b = series.options.point) === null || _b === void 0 ? void 0 : _b.events) === null || _c === void 0 ? void 0 : _c.click));\n    }\n    var boost = target.boost =\n            target.boost ||\n                {};\n    // Support for foreignObject is flimsy as best.\n    // IE does not support it, and Chrome has a bug which messes up\n    // the canvas draw order.\n    // As such, we force the Image fallback for now, but leaving the\n    // actual Canvas path in-place in case this changes in the future.\n    foSupported = false;\n    if (!mainCanvas) {\n        mainCanvas = BoostSeries_doc.createElement('canvas');\n    }\n    if (!boost.target) {\n        boost.canvas = mainCanvas;\n        // Fall back to image tag if foreignObject isn't supported,\n        // or if we're exporting.\n        if (chart.renderer.forExport || !foSupported) {\n            target.renderTarget = boost.target = chart.renderer.image('', 0, 0, width, height)\n                .addClass('highcharts-boost-canvas')\n                .add(targetGroup);\n            boost.clear = function () {\n                boost.target.attr({\n                    // Insert a blank pixel (#17182)\n                    /* eslint-disable-next-line max-len*/\n                    href: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII='\n                });\n            };\n            boost.copy = function () {\n                boost.resize();\n                boost.target.attr({\n                    href: boost.canvas.toDataURL('image/png')\n                });\n            };\n        }\n        else {\n            boost.targetFo = chart.renderer\n                .createElement('foreignObject')\n                .add(targetGroup);\n            target.renderTarget = boost.target =\n                BoostSeries_doc.createElement('canvas');\n            boost.targetCtx = boost.target.getContext('2d');\n            boost.targetFo.element.appendChild(boost.target);\n            boost.clear = function () {\n                boost.target.width = boost.canvas.width;\n                boost.target.height = boost.canvas.height;\n            };\n            boost.copy = function () {\n                boost.target.width = boost.canvas.width;\n                boost.target.height = boost.canvas.height;\n                boost.targetCtx.drawImage(boost.canvas, 0, 0);\n            };\n        }\n        boost.resize = function () {\n            var _a,\n                _b;\n            width = chart.chartWidth;\n            height = chart.chartHeight;\n            (boost.targetFo || boost.target)\n                .attr({\n                x: 0,\n                y: 0,\n                width: width,\n                height: height\n            })\n                .css({\n                pointerEvents: hasClickHandler ? void 0 : 'none',\n                mixedBlendMode: 'normal',\n                opacity: alpha\n            })\n                .addClass(hasClickHandler ? 'highcharts-tracker' : '');\n            if (target instanceof ChartClass) {\n                (_b = (_a = target.boost) === null || _a === void 0 ? void 0 : _a.markerGroup) === null || _b === void 0 ? void 0 : _b.translate(chart.plotLeft, chart.plotTop);\n            }\n        };\n        boost.clipRect = chart.renderer.clipRect();\n        (boost.targetFo || boost.target)\n            .attr({\n            // Set the z index of the boost target to that of the last\n            // series using it. This logic is not perfect, as it will not\n            // handle interleaved series with boost enabled or disabled. But\n            // it will cover the most common use case of one or more\n            // successive boosted or non-boosted series (#9819).\n            zIndex: series.options.zIndex\n        });\n        if (target instanceof ChartClass) {\n            target.boost.markerGroup = target.renderer\n                .g()\n                .add(targetGroup)\n                .translate(series.xAxis.pos, series.yAxis.pos);\n        }\n    }\n    boost.canvas.width = width;\n    boost.canvas.height = height;\n    if (boost.clipRect) {\n        var box = BoostSeries_getBoostClipRect(chart,\n            target), \n            // When using panes, the image itself must be clipped. When not\n            // using panes, it is better to clip the target group, because then\n            // we preserve clipping on touch- and mousewheel zoom preview.\n            clippedElement = (box.width === chart.clipBox.width &&\n                box.height === chart.clipBox.height) ? targetGroup :\n                (boost.targetFo || boost.target);\n        boost.clipRect.attr(box);\n        clippedElement === null || clippedElement === void 0 ? void 0 : clippedElement.clip(boost.clipRect);\n    }\n    boost.resize();\n    boost.clear();\n    if (!boost.wgl) {\n        boost.wgl = new Boost_WGLRenderer(function (wgl) {\n            if (wgl.settings.debug.timeBufferCopy) {\n                console.time('buffer copy'); // eslint-disable-line no-console\n            }\n            boost.copy();\n            if (wgl.settings.debug.timeBufferCopy) {\n                console.timeEnd('buffer copy'); // eslint-disable-line no-console\n            }\n        });\n        if (!boost.wgl.init(boost.canvas)) {\n            // The OGL renderer couldn't be inited. This likely means a shader\n            // error as we wouldn't get to this point if there was no WebGL\n            // support.\n            BoostSeries_error('[highcharts boost] - unable to init WebGL renderer');\n        }\n        boost.wgl.setOptions(chart.options.boost || {});\n        if (target instanceof ChartClass) {\n            boost.wgl.allocateBuffer(chart);\n        }\n    }\n    boost.wgl.setSize(width, height);\n    return boost.wgl;\n}\n/**\n * If implemented in the core, parts of this can probably be\n * shared with other similar methods in Highcharts.\n * @private\n * @function Highcharts.Series#destroyGraphics\n */\nfunction destroyGraphics(series) {\n    var points = series.points;\n    if (points) {\n        var point = void 0,\n            i = void 0;\n        for (i = 0; i < points.length; i = i + 1) {\n            point = points[i];\n            if (point && point.destroyElements) {\n                point.destroyElements(); // #7557\n            }\n        }\n    }\n    ['graph', 'area', 'tracker'].forEach(function (prop) {\n        var seriesProp = series[prop];\n        if (seriesProp) {\n            series[prop] = seriesProp.destroy();\n        }\n    });\n    for (var _i = 0, _a = series.zones; _i < _a.length; _i++) {\n        var zone = _a[_i];\n        destroyObjectProperties(zone, void 0, true);\n    }\n}\n/**\n * An \"async\" foreach loop. Uses a setTimeout to keep the loop from blocking the\n * UI thread.\n *\n * @private\n * @param {Array<unknown>} arr\n * The array to loop through.\n * @param {Function} fn\n * The callback to call for each item.\n * @param {Function} finalFunc\n * The callback to call when done.\n * @param {number} [chunkSize]\n * The number of iterations per timeout.\n * @param {number} [i]\n * The current index.\n * @param {boolean} [noTimeout]\n * Set to true to skip timeouts.\n */\nfunction eachAsync(arr, fn, finalFunc, chunkSize, i, noTimeout) {\n    i = i || 0;\n    chunkSize = chunkSize || CHUNK_SIZE;\n    var threshold = i + chunkSize;\n    var proceed = true;\n    while (proceed && i < threshold && i < arr.length) {\n        proceed = fn(arr[i], i);\n        ++i;\n    }\n    if (proceed) {\n        if (i < arr.length) {\n            if (noTimeout) {\n                eachAsync(arr, fn, finalFunc, chunkSize, i, noTimeout);\n            }\n            else if (BoostSeries_win.requestAnimationFrame) {\n                // If available, do requestAnimationFrame - shaves off a few ms\n                BoostSeries_win.requestAnimationFrame(function () {\n                    eachAsync(arr, fn, finalFunc, chunkSize, i);\n                });\n            }\n            else {\n                setTimeout(eachAsync, 0, arr, fn, finalFunc, chunkSize, i);\n            }\n        }\n        else if (finalFunc) {\n            finalFunc();\n        }\n    }\n}\n/**\n * Enter boost mode and apply boost-specific properties.\n * @private\n * @function Highcharts.Series#enterBoost\n */\nfunction enterBoost(series) {\n    var _a;\n    series.boost = series.boost || {\n        // Faster than a series bind:\n        getPoint: (function (bp) { return getPoint(series, bp); })\n    };\n    var alteredByBoost = series.boost.altered = [];\n    // Save the original values, including whether it was an own\n    // property or inherited from the prototype.\n    ['allowDG', 'directTouch', 'stickyTracking'].forEach(function (prop) {\n        alteredByBoost.push({\n            prop: prop,\n            val: series[prop],\n            own: Object.hasOwnProperty.call(series, prop)\n        });\n    });\n    series.allowDG = false;\n    series.directTouch = false;\n    series.stickyTracking = true;\n    // Prevent animation when zooming in on boosted series(#13421).\n    series.finishedAnimating = true;\n    // Hide series label if any\n    if (series.labelBySeries) {\n        series.labelBySeries = series.labelBySeries.destroy();\n    }\n    // Destroy existing points after zoom out\n    if (series.is('scatter') &&\n        !series.is('treemap') &&\n        series.data.length) {\n        for (var _i = 0, _b = series.data; _i < _b.length; _i++) {\n            var point = _b[_i];\n            (_a = point === null || point === void 0 ? void 0 : point.destroy) === null || _a === void 0 ? void 0 : _a.call(point);\n        }\n        series.data.length = 0;\n        series.points.length = 0;\n        delete series.processedData;\n    }\n}\n/**\n * Exit from boost mode and restore non-boost properties.\n * @private\n * @function Highcharts.Series#exitBoost\n */\nfunction exitBoost(series) {\n    var _a;\n    var boost = series.boost,\n        chart = series.chart,\n        chartBoost = chart.boost;\n    if (chartBoost === null || chartBoost === void 0 ? void 0 : chartBoost.markerGroup) {\n        chartBoost.markerGroup.destroy();\n        chartBoost.markerGroup = void 0;\n        for (var _i = 0, _b = chart.series; _i < _b.length; _i++) {\n            var s = _b[_i];\n            s.markerGroup = void 0;\n            s.markerGroup = s.plotGroup('markerGroup', 'markers', 'visible', 1, chart.seriesGroup).addClass('highcharts-tracker');\n        }\n    }\n    // Reset instance properties and/or delete instance properties and go back\n    // to prototype\n    if (boost) {\n        (boost.altered || []).forEach(function (setting) {\n            if (setting.own) {\n                series[setting.prop] = setting.val;\n            }\n            else {\n                // Revert to prototype\n                delete series[setting.prop];\n            }\n        });\n        // Clear previous run\n        if (boost.clear) {\n            boost.clear();\n        }\n    }\n    // #21106, clean up boost clipping on the series groups.\n    (_a = (chart.seriesGroup || series.group)) === null || _a === void 0 ? void 0 : _a.clip();\n}\n/**\n * @private\n * @function Highcharts.Series#hasExtremes\n */\nfunction hasExtremes(series, checkX) {\n    var options = series.options,\n        dataLength = series.dataTable.modified.rowCount,\n        xAxis = series.xAxis && series.xAxis.options,\n        yAxis = series.yAxis && series.yAxis.options,\n        colorAxis = series.colorAxis && series.colorAxis.options;\n    return dataLength > (options.boostThreshold || Number.MAX_VALUE) &&\n        // Defined yAxis extremes\n        BoostSeries_isNumber(yAxis.min) &&\n        BoostSeries_isNumber(yAxis.max) &&\n        // Defined (and required) xAxis extremes\n        (!checkX ||\n            (BoostSeries_isNumber(xAxis.min) && BoostSeries_isNumber(xAxis.max))) &&\n        // Defined (e.g. heatmap) colorAxis extremes\n        (!colorAxis ||\n            (BoostSeries_isNumber(colorAxis.min) && BoostSeries_isNumber(colorAxis.max)));\n}\n/**\n * Used multiple times. In processData first on this.options.data, the second\n * time it runs the check again after processedXData is built.\n * If the data is going to be grouped, the series shouldn't be boosted.\n * @private\n */\nvar getSeriesBoosting = function (series, data) {\n    // Check if will be grouped.\n    if (series.forceCrop) {\n        return false;\n    }\n    return (BoostSeries_isChartSeriesBoosting(series.chart) ||\n        ((data ? data.length : 0) >=\n            (series.options.boostThreshold || Number.MAX_VALUE)));\n};\n/**\n * Extend series.destroy to also remove the fake k-d-tree points (#5137).\n * Normally this is handled by Series.destroy that calls Point.destroy,\n * but the fake search points are not registered like that.\n * @private\n */\nfunction onSeriesDestroy() {\n    var series = this,\n        chart = series.chart;\n    if (chart.boost &&\n        chart.boost.markerGroup === series.markerGroup) {\n        series.markerGroup = null;\n    }\n    if (chart.hoverPoints) {\n        chart.hoverPoints = chart.hoverPoints.filter(function (point) {\n            return point.series === series;\n        });\n    }\n    if (chart.hoverPoint && chart.hoverPoint.series === series) {\n        chart.hoverPoint = null;\n    }\n}\n/**\n * @private\n */\nfunction onSeriesHide() {\n    var boost = this.boost;\n    if (boost && boost.canvas && boost.target) {\n        if (boost.wgl) {\n            boost.wgl.clear();\n        }\n        if (boost.clear) {\n            boost.clear();\n        }\n    }\n}\n/**\n * Performs the actual render if the renderer is\n * attached to the series.\n * @private\n */\nfunction renderIfNotSeriesBoosting(series) {\n    var boost = series.boost;\n    if (boost &&\n        boost.canvas &&\n        boost.target &&\n        boost.wgl &&\n        !BoostSeries_isChartSeriesBoosting(series.chart)) {\n        boost.wgl.render(series.chart);\n    }\n}\n/**\n * Return a full Point object based on the index.\n * The boost module uses stripped point objects for performance reasons.\n * @private\n * @param {object|Highcharts.Point} boostPoint\n *        A stripped-down point object\n * @return {Highcharts.Point}\n *         A Point object as per https://api.highcharts.com/highcharts#Point\n */\nfunction getPoint(series, boostPoint) {\n    var _a;\n    var seriesOptions = series.options,\n        xAxis = series.xAxis,\n        PointClass = series.pointClass;\n    if (boostPoint instanceof PointClass) {\n        return boostPoint;\n    }\n    var isScatter = series.is('scatter'), xData = ((isScatter && series.getColumn('x', true).length ?\n            series.getColumn('x', true) :\n            void 0) ||\n            (series.getColumn('x').length ? series.getColumn('x') : void 0) ||\n            seriesOptions.xData ||\n            series.getColumn('x', true) ||\n            false), yData = (series.getColumn('y', true) ||\n            seriesOptions.yData ||\n            false), point = new PointClass(series, (isScatter && xData && yData) ?\n            [xData[boostPoint.i], yData[boostPoint.i]] :\n            (isArray(series.options.data) ? series.options.data : [])[boostPoint.i], xData ? xData[boostPoint.i] : void 0);\n    point.category = BoostSeries_pick(xAxis.categories ?\n        xAxis.categories[point.x] :\n        point.x, // @todo simplify\n    point.x);\n    point.key = (_a = point.name) !== null && _a !== void 0 ? _a : point.category;\n    point.dist = boostPoint.dist;\n    point.distX = boostPoint.distX;\n    point.plotX = boostPoint.plotX;\n    point.plotY = boostPoint.plotY;\n    point.index = boostPoint.i;\n    point.percentage = boostPoint.percentage;\n    point.isInside = series.isPointInside(point);\n    return point;\n}\n/**\n * @private\n */\nfunction scatterProcessData(force) {\n    var _a,\n        _b,\n        _c,\n        _d,\n        _e,\n        _f,\n        _g,\n        _h;\n    var series = this,\n        options = series.options,\n        xAxis = series.xAxis,\n        yAxis = series.yAxis;\n    // Process only on changes\n    if (!series.isDirty &&\n        !xAxis.isDirty &&\n        !yAxis.isDirty &&\n        !force) {\n        return false;\n    }\n    // Required to get tick-based zoom ranges that take options into account\n    // like `minPadding`, `maxPadding`, `startOnTick`, `endOnTick`.\n    series.yAxis.setTickInterval();\n    var boostThreshold = options.boostThreshold || 0, cropThreshold = options.cropThreshold, xData = series.getColumn('x'), xExtremes = xAxis.getExtremes(), xMax = (_a = xExtremes.max) !== null && _a !== void 0 ? _a : Number.MAX_VALUE, xMin = (_b = xExtremes.min) !== null && _b !== void 0 ? _b : -Number.MAX_VALUE, yData = series.getColumn('y'), yExtremes = yAxis.getExtremes(), yMax = (_c = yExtremes.max) !== null && _c !== void 0 ? _c : Number.MAX_VALUE, yMin = (_d = yExtremes.min) !== null && _d !== void 0 ? _d : -Number.MAX_VALUE;\n    // Skip processing in non-boost zoom\n    if (!series.boosted &&\n        xAxis.old &&\n        yAxis.old &&\n        xMin >= ((_e = xAxis.old.min) !== null && _e !== void 0 ? _e : -Number.MAX_VALUE) &&\n        xMax <= ((_f = xAxis.old.max) !== null && _f !== void 0 ? _f : Number.MAX_VALUE) &&\n        yMin >= ((_g = yAxis.old.min) !== null && _g !== void 0 ? _g : -Number.MAX_VALUE) &&\n        yMax <= ((_h = yAxis.old.max) !== null && _h !== void 0 ? _h : Number.MAX_VALUE)) {\n        series.dataTable.modified.setColumns({\n            x: xData,\n            y: yData\n        });\n        return true;\n    }\n    // Without thresholds just assign data\n    var dataLength = series.dataTable.rowCount;\n    if (!boostThreshold ||\n        dataLength < boostThreshold ||\n        (cropThreshold &&\n            !series.forceCrop &&\n            !series.getExtremesFromAll &&\n            !options.getExtremesFromAll &&\n            dataLength < cropThreshold)) {\n        series.dataTable.modified.setColumns({\n            x: xData,\n            y: yData\n        });\n        return true;\n    }\n    // Filter unsorted scatter data for ranges\n    var processedData = [],\n        processedXData = [],\n        processedYData = [],\n        xRangeNeeded = !(BoostSeries_isNumber(xExtremes.max) || BoostSeries_isNumber(xExtremes.min)),\n        yRangeNeeded = !(BoostSeries_isNumber(yExtremes.max) || BoostSeries_isNumber(yExtremes.min));\n    var cropped = false,\n        x,\n        xDataMax = xData[0],\n        xDataMin = xData[0],\n        y,\n        yDataMax = yData === null || yData === void 0 ? void 0 : yData[0],\n        yDataMin = yData === null || yData === void 0 ? void 0 : yData[0];\n    for (var i = 0, iEnd = xData.length; i < iEnd; ++i) {\n        x = xData[i];\n        y = yData === null || yData === void 0 ? void 0 : yData[i];\n        if (x >= xMin && x <= xMax &&\n            y >= yMin && y <= yMax) {\n            processedData.push({ x: x, y: y });\n            processedXData.push(x);\n            processedYData.push(y);\n            if (xRangeNeeded) {\n                xDataMax = Math.max(xDataMax, x);\n                xDataMin = Math.min(xDataMin, x);\n            }\n            if (yRangeNeeded) {\n                yDataMax = Math.max(yDataMax, y);\n                yDataMin = Math.min(yDataMin, y);\n            }\n        }\n        else {\n            cropped = true;\n        }\n    }\n    if (xRangeNeeded) {\n        xAxis.dataMax = Math.max(xDataMax, xAxis.dataMax || 0);\n        xAxis.dataMin = Math.min(xDataMin, xAxis.dataMin || 0);\n    }\n    if (yRangeNeeded) {\n        yAxis.dataMax = Math.max(yDataMax, yAxis.dataMax || 0);\n        yAxis.dataMin = Math.min(yDataMin, yAxis.dataMin || 0);\n    }\n    // Set properties as base processData\n    series.cropped = cropped;\n    series.cropStart = 0;\n    // For boosted points rendering\n    if (cropped && series.dataTable.modified === series.dataTable) {\n        // Calling setColumns with cropped data must be done on a new instance\n        // to avoid modification of the original (complete) data\n        series.dataTable.modified = new Data_DataTableCore();\n    }\n    series.dataTable.modified.setColumns({\n        x: processedXData,\n        y: processedYData\n    });\n    if (!getSeriesBoosting(series, processedXData)) {\n        series.processedData = processedData; // For un-boosted points rendering\n    }\n    return true;\n}\n/**\n * @private\n * @function Highcharts.Series#renderCanvas\n */\nfunction seriesRenderCanvas() {\n    var _this = this;\n    var options = this.options || {}, chart = this.chart, chartBoost = chart.boost, seriesBoost = this.boost, xAxis = this.xAxis, yAxis = this.yAxis, xData = options.xData || this.getColumn('x', true), yData = options.yData || this.getColumn('y', true), lowData = this.getColumn('low', true), highData = this.getColumn('high', true), rawData = this.processedData || options.data, xExtremes = xAxis.getExtremes(), \n        // Taking into account the offset of the min point #19497\n        xMin = xExtremes.min - (xAxis.minPointOffset || 0), xMax = xExtremes.max + (xAxis.minPointOffset || 0), yExtremes = yAxis.getExtremes(), yMin = yExtremes.min - (yAxis.minPointOffset || 0), yMax = yExtremes.max + (yAxis.minPointOffset || 0), pointTaken = {}, sampling = !!this.sampling, enableMouseTracking = options.enableMouseTracking, threshold = options.threshold, isRange = this.pointArrayMap &&\n            this.pointArrayMap.join(',') === 'low,high', isStacked = !!options.stacking, cropStart = this.cropStart || 0, requireSorting = this.requireSorting, useRaw = !xData, compareX = options.findNearestPointBy === 'x', xDataFull = ((this.getColumn('x').length ?\n            this.getColumn('x') :\n            void 0) ||\n            this.options.xData ||\n            this.getColumn('x', true)), lineWidth = BoostSeries_pick(options.lineWidth, 1), nullYSubstitute = options.nullInteraction && yMin;\n    var renderer = false,\n        lastClientX,\n        yBottom = yAxis.getThreshold(threshold),\n        minVal,\n        maxVal,\n        minI,\n        maxI;\n    // When touch-zooming or mouse-panning, re-rendering the canvas would not\n    // perform fast enough. Instead, let the axes redraw, but not the series.\n    // The series is scale-translated in an event handler for an approximate\n    // preview.\n    if (xAxis.isPanning || yAxis.isPanning) {\n        return;\n    }\n    // Get or create the renderer\n    renderer = createAndAttachRenderer(chart, this);\n    chart.boosted = true;\n    if (!this.visible) {\n        return;\n    }\n    // If we are zooming out from SVG mode, destroy the graphics\n    if (this.points || this.graph) {\n        destroyGraphics(this);\n    }\n    // If we're rendering per. series we should create the marker groups\n    // as usual.\n    if (!BoostSeries_isChartSeriesBoosting(chart)) {\n        // If all series were boosting, but are not anymore\n        // restore private markerGroup\n        if (this.markerGroup === (chartBoost === null || chartBoost === void 0 ? void 0 : chartBoost.markerGroup)) {\n            this.markerGroup = void 0;\n        }\n        this.markerGroup = this.plotGroup('markerGroup', 'markers', 'visible', 1, chart.seriesGroup).addClass('highcharts-tracker');\n    }\n    else {\n        // If series has a private markerGroup, remove that\n        // and use common markerGroup\n        if (this.markerGroup &&\n            this.markerGroup !== (chartBoost === null || chartBoost === void 0 ? void 0 : chartBoost.markerGroup)) {\n            this.markerGroup.destroy();\n        }\n        // Use a single group for the markers\n        this.markerGroup = chartBoost === null || chartBoost === void 0 ? void 0 : chartBoost.markerGroup;\n        // When switching from chart boosting mode, destroy redundant\n        // series boosting targets\n        if (seriesBoost && seriesBoost.target) {\n            this.renderTarget =\n                seriesBoost.target =\n                    seriesBoost.target.destroy();\n        }\n    }\n    var points = this.points = [],\n        addKDPoint = function (clientX,\n        plotY,\n        i,\n        percentage) {\n            var x = xDataFull ? xDataFull[cropStart + i] : false,\n        pushPoint = function (plotX) {\n                if (chart.inverted) {\n                    plotX = xAxis.len - plotX;\n                plotY = yAxis.len - plotY;\n            }\n            points.push({\n                destroy: noop,\n                x: x,\n                clientX: plotX,\n                plotX: plotX,\n                plotY: plotY,\n                i: cropStart + i,\n                percentage: percentage\n            });\n        };\n        // We need to do ceil on the clientX to make things\n        // snap to pixel values. The renderer will frequently\n        // draw stuff on \"sub-pixels\".\n        clientX = Math.ceil(clientX);\n        // Shaves off about 60ms compared to repeated concatenation\n        index = compareX ? clientX : clientX + ',' + plotY;\n        // The k-d tree requires series points.\n        // Reduce the amount of points, since the time to build the\n        // tree increases exponentially.\n        if (enableMouseTracking) {\n            if (!pointTaken[index]) {\n                pointTaken[index] = true;\n                pushPoint(clientX);\n            }\n            else if (x === xDataFull[xDataFull.length - 1]) {\n                // If the last point is on the same pixel as the last\n                // tracked point, swap them. (#18856)\n                points.length--;\n                pushPoint(clientX);\n            }\n        }\n    };\n    // Do not start building while drawing\n    this.buildKDTree = noop;\n    BoostSeries_fireEvent(this, 'renderCanvas');\n    if (this.is('line') &&\n        lineWidth > 1 &&\n        (seriesBoost === null || seriesBoost === void 0 ? void 0 : seriesBoost.target) &&\n        chartBoost &&\n        !chartBoost.lineWidthFilter) {\n        chartBoost.lineWidthFilter = chart.renderer.definition({\n            tagName: 'filter',\n            children: [\n                {\n                    tagName: 'feMorphology',\n                    attributes: {\n                        operator: 'dilate',\n                        radius: 0.25 * lineWidth\n                    }\n                }\n            ],\n            attributes: { id: 'linewidth' }\n        });\n        seriesBoost.target.attr({\n            filter: 'url(#linewidth)'\n        });\n    }\n    if (renderer) {\n        allocateIfNotSeriesBoosting(renderer, this);\n        renderer.pushSeries(this);\n        // Perform the actual renderer if we're on series level\n        renderIfNotSeriesBoosting(this);\n    }\n    /**\n     * This builds the KD-tree\n     * @private\n     */\n    function processPoint(d, i) {\n        var _a,\n            _b;\n        var chartDestroyed = typeof chart.index === 'undefined';\n        var x,\n            y,\n            clientX,\n            plotY,\n            percentage,\n            low = false,\n            isYInside = true;\n        if (!defined(d)) {\n            return true;\n        }\n        if (!chartDestroyed) {\n            if (useRaw) {\n                x = d[0];\n                y = d[1];\n            }\n            else {\n                x = d;\n                y = (_b = (_a = yData[i]) !== null && _a !== void 0 ? _a : nullYSubstitute) !== null && _b !== void 0 ? _b : null;\n            }\n            // Resolve low and high for range series\n            if (isRange) {\n                if (useRaw) {\n                    y = d.slice(1, 3);\n                }\n                low = lowData[i];\n                y = highData[i];\n            }\n            else if (isStacked) {\n                x = d.x;\n                y = d.stackY;\n                low = y - d.y;\n                percentage = d.percentage;\n            }\n            // Optimize for scatter zooming\n            if (!requireSorting) {\n                isYInside = (y || 0) >= yMin && y <= yMax;\n            }\n            if (y !== null && x >= xMin && x <= xMax && isYInside) {\n                clientX = xAxis.toPixels(x, true);\n                if (sampling) {\n                    if (typeof minI === 'undefined' ||\n                        clientX === lastClientX) {\n                        if (!isRange) {\n                            low = y;\n                        }\n                        if (typeof maxI === 'undefined' ||\n                            y > maxVal) {\n                            maxVal = y;\n                            maxI = i;\n                        }\n                        if (typeof minI === 'undefined' ||\n                            low < minVal) {\n                            minVal = low;\n                            minI = i;\n                        }\n                    }\n                    // Add points and reset\n                    if (!compareX || clientX !== lastClientX) {\n                        // `maxI` is number too:\n                        if (typeof minI !== 'undefined') {\n                            plotY =\n                                yAxis.toPixels(maxVal, true);\n                            yBottom =\n                                yAxis.toPixels(minVal, true);\n                            addKDPoint(clientX, plotY, maxI, percentage);\n                            if (yBottom !== plotY) {\n                                addKDPoint(clientX, yBottom, minI, percentage);\n                            }\n                        }\n                        minI = maxI = void 0;\n                        lastClientX = clientX;\n                    }\n                }\n                else {\n                    plotY = Math.ceil(yAxis.toPixels(y, true));\n                    addKDPoint(clientX, plotY, i, percentage);\n                }\n            }\n        }\n        return !chartDestroyed;\n    }\n    /**\n     * @private\n     */\n    var boostOptions = renderer.settings,\n        doneProcessing = function () {\n            BoostSeries_fireEvent(_this, 'renderedCanvas');\n        // Go back to prototype, ready to build\n        delete _this.buildKDTree;\n        // Check that options exist, as async processing\n        // could mean the series is removed at this point (#19895)\n        if (_this.options) {\n            _this.buildKDTree();\n        }\n        if (boostOptions.debug.timeKDTree) {\n            console.timeEnd('kd tree building'); // eslint-disable-line no-console\n        }\n    };\n    // Loop over the points to build the k-d tree - skip this if\n    // exporting\n    if (!chart.renderer.forExport) {\n        if (boostOptions.debug.timeKDTree) {\n            console.time('kd tree building'); // eslint-disable-line no-console\n        }\n        eachAsync(isStacked ?\n            this.data.slice(cropStart) :\n            (xData || rawData), processPoint, doneProcessing);\n    }\n}\n/**\n * Used for treemap|heatmap.drawPoints\n * @private\n */\nfunction wrapSeriesDrawPoints(proceed) {\n    var enabled = true;\n    if (this.chart.options && this.chart.options.boost) {\n        enabled = typeof this.chart.options.boost.enabled === 'undefined' ?\n            true :\n            this.chart.options.boost.enabled;\n    }\n    if (!enabled || !this.boosted) {\n        return proceed.call(this);\n    }\n    this.chart.boosted = true;\n    // Make sure we have a valid OGL context\n    var renderer = createAndAttachRenderer(this.chart,\n        this);\n    if (renderer) {\n        allocateIfNotSeriesBoosting(renderer, this);\n        renderer.pushSeries(this);\n    }\n    renderIfNotSeriesBoosting(this);\n}\n/**\n * Override a bunch of methods the same way. If the number of points is\n * below the threshold, run the original method. If not, check for a\n * canvas version or do nothing.\n *\n * Note that we're not overriding any of these for heatmaps.\n */\nfunction wrapSeriesFunctions(seriesProto, seriesTypes, method) {\n    /**\n     * @private\n     */\n    function branch(proceed) {\n        var letItPass = this.options.stacking &&\n                (method === 'translate' || method === 'generatePoints');\n        if (!this.boosted ||\n            letItPass ||\n            !boostEnabled(this.chart) ||\n            this.type === 'heatmap' ||\n            this.type === 'treemap' ||\n            !Boost_BoostableMap[this.type] ||\n            this.options.boostThreshold === 0) {\n            proceed.call(this);\n            // Run canvas version of method, like renderCanvas(), if it exists\n        }\n        else if (method === 'render' && this.renderCanvas) {\n            this.renderCanvas();\n        }\n    }\n    wrap(seriesProto, method, branch);\n    // Special case for some types, when translate method is already wrapped\n    if (method === 'translate') {\n        for (var _i = 0, _a = [\n            'column',\n            'arearange',\n            'columnrange',\n            'heatmap',\n            'treemap'\n        ]; _i < _a.length; _i++) {\n            var type = _a[_i];\n            if (seriesTypes[type]) {\n                wrap(seriesTypes[type].prototype, method, branch);\n            }\n        }\n    }\n}\n/**\n * Do not compute extremes when min and max are set. If we use this in the\n * core, we can add the hook to hasExtremes to the methods directly.\n * @private\n */\nfunction wrapSeriesGetExtremes(proceed) {\n    if (this.boosted) {\n        if (hasExtremes(this)) {\n            return {};\n        }\n        if (this.xAxis.isPanning || this.yAxis.isPanning) {\n            // Do not re-compute the extremes during panning, because looping\n            // the data is expensive. The `this` contains the `dataMin` and\n            // `dataMax` to use.\n            return this;\n        }\n    }\n    return proceed.apply(this, [].slice.call(arguments, 1));\n}\n/**\n * If the series is a heatmap or treemap, or if the series is not boosting\n * do the default behaviour. Otherwise, process if the series has no\n * extremes.\n * @private\n */\nfunction wrapSeriesProcessData(proceed) {\n    var _a,\n        _b,\n        _c;\n    var dataToMeasure = this.options.data;\n    if (boostEnabled(this.chart) && Boost_BoostableMap[this.type]) {\n        var series = this, \n            // Flag for code that should run for ScatterSeries and its\n            // subclasses, apart from the enlisted exceptions.\n            isScatter = series.is('scatter') &&\n                !series.is('bubble') &&\n                !series.is('treemap') &&\n                !series.is('heatmap');\n        // If there are no extremes given in the options, we also need to\n        // process the data to read the data extremes. If this is a heatmap,\n        // do default behaviour.\n        if (\n        // First pass with options.data:\n        !getSeriesBoosting(series, dataToMeasure) ||\n            isScatter ||\n            series.is('treemap') ||\n            // Use processedYData for the stack (#7481):\n            series.options.stacking ||\n            !hasExtremes(series, true)) {\n            // Do nothing until the panning stops\n            if (series.boosted && (((_a = series.xAxis) === null || _a === void 0 ? void 0 : _a.isPanning) || ((_b = series.yAxis) === null || _b === void 0 ? void 0 : _b.isPanning))) {\n                return;\n            }\n            // Extra check for zoomed scatter data\n            if (isScatter && series.yAxis.type !== 'treegrid') {\n                scatterProcessData.call(series, arguments[1]);\n            }\n            else {\n                proceed.apply(series, [].slice.call(arguments, 1));\n            }\n            dataToMeasure = series.getColumn('x', true);\n        }\n        // Set the isBoosting flag, second pass with processedXData to\n        // see if we have zoomed.\n        series.boosted = getSeriesBoosting(series, dataToMeasure);\n        // Enter or exit boost mode\n        if (series.boosted) {\n            // Force turbo-mode:\n            var firstPoint = void 0;\n            if ((_c = series.options.data) === null || _c === void 0 ? void 0 : _c.length) {\n                firstPoint = series.getFirstValidPoint(series.options.data);\n                if (!BoostSeries_isNumber(firstPoint) &&\n                    !isArray(firstPoint) &&\n                    !series.is('treemap')) {\n                    BoostSeries_error(12, false, series.chart);\n                }\n            }\n            enterBoost(series);\n        }\n        else {\n            exitBoost(series);\n        }\n        // The series type is not boostable\n    }\n    else {\n        proceed.apply(this, [].slice.call(arguments, 1));\n    }\n}\n/**\n * Return a point instance from the k-d-tree\n * @private\n */\nfunction wrapSeriesSearchPoint(proceed) {\n    var result = proceed.apply(this,\n        [].slice.call(arguments, 1));\n    if (this.boost && result) {\n        return this.boost.getPoint(result);\n    }\n    return result;\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar BoostSeries = {\n    compose: BoostSeries_compose,\n    destroyGraphics: destroyGraphics,\n    eachAsync: eachAsync,\n    getPoint: getPoint\n};\n/* harmony default export */ var Boost_BoostSeries = (BoostSeries);\n\n;// ./code/es5/es-modules/Extensions/BoostCanvas.js\n/* *\n *\n *  License: www.highcharts.com/license\n *  Author: Torstein Honsi, Christer Vasseng\n *\n *  This module serves as a fallback for the Boost module in IE9 and IE10. Newer\n *  browsers support WebGL which is faster.\n *\n *  It is recommended to include this module in conditional comments targeting\n *  IE9 and IE10.\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar BoostCanvas_getBoostClipRect = Boost_BoostChart.getBoostClipRect, BoostCanvas_isChartSeriesBoosting = Boost_BoostChart.isChartSeriesBoosting;\n\nvar BoostCanvas_destroyGraphics = Boost_BoostSeries.destroyGraphics;\n\nvar BoostCanvas_color = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default()).parse;\n\nvar BoostCanvas_doc = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).doc, BoostCanvas_noop = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).noop;\n\nvar BoostCanvas_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, BoostCanvas_fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, BoostCanvas_isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, BoostCanvas_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, BoostCanvas_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, BoostCanvas_wrap = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).wrap;\n/* *\n *\n *  Namespace\n *\n * */\nvar BoostCanvas;\n(function (BoostCanvas) {\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    // Use a blank pixel for clearing canvas (#17182)\n    var b64BlankPixel = ('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAw' +\n            'CAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=');\n    var CHUNK_SIZE = 50000;\n    /* *\n     *\n     *  Variables\n     *\n     * */\n    var ChartConstructor;\n    var destroyLoadingDiv;\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    function areaCvsDrawPoint(ctx, clientX, plotY, yBottom, lastPoint) {\n        if (lastPoint && clientX !== lastPoint.clientX) {\n            ctx.moveTo(lastPoint.clientX, lastPoint.yBottom);\n            ctx.lineTo(lastPoint.clientX, lastPoint.plotY);\n            ctx.lineTo(clientX, plotY);\n            ctx.lineTo(clientX, yBottom);\n        }\n    }\n    /**\n     * @private\n     */\n    function bubbleCvsMarkerCircle(ctx, clientX, plotY, r, i) {\n        ctx.moveTo(clientX, plotY);\n        ctx.arc(clientX, plotY, this.radii && this.radii[i], 0, 2 * Math.PI, false);\n    }\n    /**\n     * @private\n     */\n    function columnCvsDrawPoint(ctx, clientX, plotY, yBottom) {\n        ctx.rect(clientX - 1, plotY, 1, yBottom - plotY);\n    }\n    /**\n     * @private\n     */\n    function compose(ChartClass, SeriesClass, seriesTypes) {\n        var seriesProto = SeriesClass.prototype;\n        if (!seriesProto.renderCanvas) {\n            var AreaSeries_1 = seriesTypes.area,\n                BubbleSeries_1 = seriesTypes.bubble,\n                ColumnSeries_1 = seriesTypes.column,\n                HeatmapSeries_1 = seriesTypes.heatmap,\n                ScatterSeries_1 = seriesTypes.scatter;\n            ChartConstructor = ChartClass;\n            ChartClass.prototype.callbacks.push(function (chart) {\n                BoostCanvas_addEvent(chart, 'predraw', onChartClear);\n                BoostCanvas_addEvent(chart, 'render', onChartCanvasToSVG);\n            });\n            seriesProto.canvasToSVG = seriesCanvasToSVG;\n            seriesProto.cvsLineTo = seriesCvsLineTo;\n            seriesProto.getContext = seriesGetContext;\n            seriesProto.renderCanvas = seriesRenderCanvas;\n            if (AreaSeries_1) {\n                var areaProto = AreaSeries_1.prototype;\n                areaProto.cvsDrawPoint = areaCvsDrawPoint;\n                areaProto.fill = true;\n                areaProto.fillOpacity = true;\n                areaProto.sampling = true;\n            }\n            if (BubbleSeries_1) {\n                var bubbleProto = BubbleSeries_1.prototype;\n                bubbleProto.cvsMarkerCircle = bubbleCvsMarkerCircle;\n                bubbleProto.cvsStrokeBatch = 1;\n            }\n            if (ColumnSeries_1) {\n                var columnProto = ColumnSeries_1.prototype;\n                columnProto.cvsDrawPoint = columnCvsDrawPoint;\n                columnProto.fill = true;\n                columnProto.sampling = true;\n            }\n            if (HeatmapSeries_1) {\n                var heatmapProto = HeatmapSeries_1.prototype;\n                BoostCanvas_wrap(heatmapProto, 'drawPoints', wrapHeatmapDrawPoints);\n            }\n            if (ScatterSeries_1) {\n                var scatterProto = ScatterSeries_1.prototype;\n                scatterProto.cvsMarkerCircle = scatterCvsMarkerCircle;\n                scatterProto.cvsMarkerSquare = scatterCvsMarkerSquare;\n                scatterProto.fill = true;\n            }\n        }\n    }\n    BoostCanvas.compose = compose;\n    /**\n     * @private\n     */\n    function onChartCanvasToSVG() {\n        if (this.boost && this.boost.copy) {\n            this.boost.copy();\n        }\n    }\n    /**\n     * @private\n     */\n    function onChartClear() {\n        var boost = this.boost || {};\n        if (boost.target) {\n            boost.target.attr({ href: b64BlankPixel });\n        }\n        if (boost.canvas) {\n            boost.canvas.getContext('2d').clearRect(0, 0, boost.canvas.width, boost.canvas.height);\n        }\n    }\n    /**\n     * Draw the canvas image inside an SVG image\n     *\n     * @private\n     * @function Highcharts.Series#canvasToSVG\n     */\n    function seriesCanvasToSVG() {\n        if (!BoostCanvas_isChartSeriesBoosting(this.chart)) {\n            if (this.boost && this.boost.copy) {\n                this.boost.copy();\n            }\n            else if (this.chart.boost && this.chart.boost.copy) {\n                this.chart.boost.copy();\n            }\n        }\n        else if (this.boost && this.boost.clear) {\n            this.boost.clear();\n        }\n    }\n    /**\n     * @private\n     */\n    function seriesCvsLineTo(ctx, clientX, plotY) {\n        ctx.lineTo(clientX, plotY);\n    }\n    /**\n     * Create a hidden canvas to draw the graph on. The contents is later\n     * copied over to an SVG image element.\n     *\n     * @private\n     * @function Highcharts.Series#getContext\n     */\n    function seriesGetContext() {\n        var chart = this.chart,\n            target = BoostCanvas_isChartSeriesBoosting(chart) ? chart : this,\n            targetGroup = (target === chart ?\n                chart.seriesGroup :\n                chart.seriesGroup || this.group),\n            width = chart.chartWidth,\n            height = chart.chartHeight,\n            swapXY = function (proceed,\n            x,\n            y,\n            a,\n            b,\n            c,\n            d) {\n                proceed.call(this,\n            y,\n            x,\n            a,\n            b,\n            c,\n            d);\n        };\n        var ctx;\n        var boost = target.boost =\n                target.boost ||\n                    {};\n        ctx = boost.targetCtx;\n        if (!boost.canvas) {\n            boost.canvas = BoostCanvas_doc.createElement('canvas');\n            boost.target = chart.renderer\n                .image('', 0, 0, width, height)\n                .addClass('highcharts-boost-canvas')\n                .add(targetGroup);\n            ctx = boost.targetCtx =\n                boost.canvas.getContext('2d');\n            if (chart.inverted) {\n                ['moveTo', 'lineTo', 'rect', 'arc'].forEach(function (fn) {\n                    BoostCanvas_wrap(ctx, fn, swapXY);\n                });\n            }\n            boost.copy = function () {\n                boost.target.attr({\n                    href: boost.canvas.toDataURL('image/png')\n                });\n            };\n            boost.clear = function () {\n                ctx.clearRect(0, 0, boost.canvas.width, boost.canvas.height);\n                if (target === boost.target) {\n                    boost.target.attr({\n                        href: b64BlankPixel\n                    });\n                }\n            };\n            boost.clipRect = chart.renderer.clipRect();\n            boost.target.clip(boost.clipRect);\n        }\n        else if (!(target instanceof ChartConstructor)) {\n            ///  ctx.clearRect(0, 0, width, height);\n        }\n        if (boost.canvas.width !== width) {\n            boost.canvas.width = width;\n        }\n        if (boost.canvas.height !== height) {\n            boost.canvas.height = height;\n        }\n        boost.target.attr({\n            x: 0,\n            y: 0,\n            width: width,\n            height: height,\n            style: 'pointer-events: none',\n            href: b64BlankPixel\n        });\n        if (boost.clipRect) {\n            boost.clipRect.attr(BoostCanvas_getBoostClipRect(chart, target));\n        }\n        return ctx;\n    }\n    /**\n     * @private\n     */\n    function seriesRenderCanvas() {\n        var series = this, options = series.options, chart = series.chart, xAxis = series.xAxis, yAxis = series.yAxis, activeBoostSettings = chart.options.boost || {}, boostSettings = {\n                timeRendering: activeBoostSettings.timeRendering || false,\n                timeSeriesProcessing: activeBoostSettings.timeSeriesProcessing || false,\n                timeSetup: activeBoostSettings.timeSetup || false\n            }, xData = series.getColumn('x', true), yData = series.getColumn('y', true), rawData = options.data, xExtremes = xAxis.getExtremes(), xMin = xExtremes.min, xMax = xExtremes.max, yExtremes = yAxis.getExtremes(), yMin = yExtremes.min, yMax = yExtremes.max, pointTaken = {}, sampling = !!series.sampling, r = options.marker && options.marker.radius, strokeBatch = series.cvsStrokeBatch || 1000, enableMouseTracking = options.enableMouseTracking, threshold = options.threshold, hasThreshold = BoostCanvas_isNumber(threshold), translatedThreshold = yAxis.getThreshold(threshold), doFill = series.fill, isRange = (series.pointArrayMap &&\n                series.pointArrayMap.join(',') === 'low,high'), isStacked = !!options.stacking, cropStart = series.cropStart || 0, loadingOptions = chart.options.loading, requireSorting = series.requireSorting, connectNulls = options.connectNulls, useRaw = !xData, sdata = (isStacked ?\n                series.data :\n                (xData || rawData)), fillColor = (series.fillOpacity ?\n                highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default().parse(series.color).setOpacity(BoostCanvas_pick(options.fillOpacity, 0.75)).get() :\n                series.color), compareX = options.findNearestPointBy === 'x', boost = this.boost || {}, cvsDrawPoint = series.cvsDrawPoint, cvsLineTo = options.lineWidth ? series.cvsLineTo : void 0, cvsMarker = (r && r <= 1 ?\n                series.cvsMarkerSquare :\n                series.cvsMarkerCircle);\n        if (boost.target) {\n            boost.target.attr({ href: b64BlankPixel });\n        }\n        // If we are zooming out from SVG mode, destroy the graphics\n        if (series.points || series.graph) {\n            BoostCanvas_destroyGraphics(series);\n        }\n        // The group\n        series.plotGroup('group', 'series', series.visible ? 'visible' : 'hidden', options.zIndex, chart.seriesGroup);\n        series.markerGroup = series.group;\n        BoostCanvas_addEvent(series, 'destroy', function () {\n            // Prevent destroy twice\n            series.markerGroup = null;\n        });\n        var points = this.points = [],\n            ctx = this.getContext();\n        series.buildKDTree = BoostCanvas_noop; // Do not start building while drawing\n        if (boost.clear) {\n            boost.clear();\n        }\n        // If (series.canvas) {\n        //     ctx.clearRect(\n        //         0,\n        //         0,\n        //         series.canvas.width,\n        //         series.canvas.height\n        //     );\n        // }\n        if (!series.visible) {\n            return;\n        }\n        // Display a loading indicator\n        if (rawData.length > 99999) {\n            chart.options.loading = BoostCanvas_merge(loadingOptions, {\n                labelStyle: {\n                    backgroundColor: BoostCanvas_color(\"#ffffff\" /* Palette.backgroundColor */).setOpacity(0.75).get(),\n                    padding: '1em',\n                    borderRadius: '0.5em'\n                },\n                style: {\n                    backgroundColor: 'none',\n                    opacity: 1\n                }\n            });\n            highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().clearTimeout(destroyLoadingDiv);\n            chart.showLoading('Drawing...');\n            chart.options.loading = loadingOptions; // Reset\n        }\n        if (boostSettings.timeRendering) {\n            console.time('canvas rendering'); // eslint-disable-line no-console\n        }\n        // Loop variables\n        var c = 0,\n            lastClientX,\n            lastPoint,\n            yBottom = translatedThreshold,\n            wasNull,\n            minVal,\n            maxVal,\n            minI,\n            maxI,\n            index;\n        // Loop helpers\n        var stroke = function () {\n                if (doFill) {\n                    ctx.fillStyle = fillColor;\n                ctx.fill();\n            }\n            else {\n                ctx.strokeStyle = series.color;\n                ctx.lineWidth = options.lineWidth;\n                ctx.stroke();\n            }\n        }, \n        //\n        drawPoint = function (clientX, plotY, yBottom, i) {\n            if (c === 0) {\n                ctx.beginPath();\n                if (cvsLineTo) {\n                    ctx.lineJoin = 'round';\n                }\n            }\n            if (chart.scroller &&\n                series.options.className ===\n                    'highcharts-navigator-series') {\n                plotY += chart.scroller.top;\n                if (yBottom) {\n                    yBottom += chart.scroller.top;\n                }\n            }\n            else {\n                plotY += chart.plotTop;\n            }\n            clientX += chart.plotLeft;\n            if (wasNull) {\n                ctx.moveTo(clientX, plotY);\n            }\n            else {\n                if (cvsDrawPoint) {\n                    cvsDrawPoint(ctx, clientX, plotY, yBottom, lastPoint);\n                }\n                else if (cvsLineTo) {\n                    cvsLineTo(ctx, clientX, plotY);\n                }\n                else if (cvsMarker) {\n                    cvsMarker.call(series, ctx, clientX, plotY, r, i);\n                }\n            }\n            // We need to stroke the line for every 1000 pixels. It will\n            // crash the browser memory use if we stroke too\n            // infrequently.\n            c = c + 1;\n            if (c === strokeBatch) {\n                stroke();\n                c = 0;\n            }\n            // Area charts need to keep track of the last point\n            lastPoint = {\n                clientX: clientX,\n                plotY: plotY,\n                yBottom: yBottom\n            };\n        }, xDataFull = ((this.getColumn('x').length ? this.getColumn('x') : void 0) ||\n            this.options.xData ||\n            (this.getColumn('x', true).length ?\n                this.getColumn('x', true) :\n                false)), \n        //\n        addKDPoint = function (clientX, plotY, i) {\n            // Shaves off about 60ms compared to repeated concatenation\n            index = compareX ? clientX : clientX + ',' + plotY;\n            // The k-d tree requires series points.\n            // Reduce the amount of points, since the time to build the\n            // tree increases exponentially.\n            if (enableMouseTracking && !pointTaken[index]) {\n                pointTaken[index] = true;\n                if (chart.inverted) {\n                    clientX = xAxis.len - clientX;\n                    plotY = yAxis.len - plotY;\n                }\n                points.push({\n                    x: xDataFull ?\n                        xDataFull[cropStart + i] :\n                        false,\n                    clientX: clientX,\n                    plotX: clientX,\n                    plotY: plotY,\n                    i: cropStart + i\n                });\n            }\n        };\n        // Loop over the points\n        Boost_BoostSeries.eachAsync(sdata, function (d, i) {\n            var chartDestroyed = typeof chart.index === 'undefined';\n            var x,\n                y,\n                clientX,\n                plotY,\n                isNull,\n                low,\n                isNextInside = false,\n                isPrevInside = false,\n                nx = NaN,\n                px = NaN,\n                isYInside = true;\n            if (!chartDestroyed) {\n                if (useRaw) {\n                    x = d[0];\n                    y = d[1];\n                    if (sdata[i + 1]) {\n                        nx = sdata[i + 1][0];\n                    }\n                    if (sdata[i - 1]) {\n                        px = sdata[i - 1][0];\n                    }\n                }\n                else {\n                    x = d;\n                    y = yData[i];\n                    if (sdata[i + 1]) {\n                        nx = sdata[i + 1];\n                    }\n                    if (sdata[i - 1]) {\n                        px = sdata[i - 1];\n                    }\n                }\n                if (nx && nx >= xMin && nx <= xMax) {\n                    isNextInside = true;\n                }\n                if (px && px >= xMin && px <= xMax) {\n                    isPrevInside = true;\n                }\n                // Resolve low and high for range series\n                if (isRange) {\n                    if (useRaw) {\n                        y = d.slice(1, 3);\n                    }\n                    low = y[0];\n                    y = y[1];\n                }\n                else if (isStacked) {\n                    x = d.x;\n                    y = d.stackY;\n                    low = y - d.y;\n                }\n                isNull = y === null;\n                // Optimize for scatter zooming\n                if (!requireSorting) {\n                    isYInside = y >= yMin && y <= yMax;\n                }\n                if (!isNull &&\n                    ((x >= xMin && x <= xMax && isYInside) ||\n                        (isNextInside || isPrevInside))) {\n                    clientX = Math.round(xAxis.toPixels(x, true));\n                    if (sampling) {\n                        if (typeof minI === 'undefined' ||\n                            clientX === lastClientX) {\n                            if (!isRange) {\n                                low = y;\n                            }\n                            if (typeof maxI === 'undefined' || y > maxVal) {\n                                maxVal = y;\n                                maxI = i;\n                            }\n                            if (typeof minI === 'undefined' ||\n                                low < minVal) {\n                                minVal = low;\n                                minI = i;\n                            }\n                        }\n                        // Add points and reset\n                        if (clientX !== lastClientX) {\n                            // `maxI` also a number:\n                            if (typeof minI !== 'undefined') {\n                                plotY = yAxis.toPixels(maxVal, true);\n                                yBottom = yAxis.toPixels(minVal, true);\n                                drawPoint(clientX, hasThreshold ?\n                                    Math.min(plotY, translatedThreshold) : plotY, hasThreshold ?\n                                    Math.max(yBottom, translatedThreshold) : yBottom, i);\n                                addKDPoint(clientX, plotY, maxI);\n                                if (yBottom !== plotY) {\n                                    addKDPoint(clientX, yBottom, minI);\n                                }\n                            }\n                            minI = maxI = void 0;\n                            lastClientX = clientX;\n                        }\n                    }\n                    else {\n                        plotY = Math.round(yAxis.toPixels(y, true));\n                        drawPoint(clientX, plotY, yBottom, i);\n                        addKDPoint(clientX, plotY, i);\n                    }\n                }\n                wasNull = isNull && !connectNulls;\n                if (i % CHUNK_SIZE === 0) {\n                    if (series.boost &&\n                        series.boost.copy) {\n                        series.boost.copy();\n                    }\n                    else if (series.chart.boost &&\n                        series.chart.boost.copy) {\n                        series.chart.boost.copy();\n                    }\n                }\n            }\n            return !chartDestroyed;\n        }, function () {\n            var loadingDiv = chart.loadingDiv,\n                loadingShown = chart.loadingShown;\n            stroke();\n            // If (series.boostCopy || series.chart.boostCopy) {\n            //     (series.boostCopy || series.chart.boostCopy)();\n            // }\n            series.canvasToSVG();\n            if (boostSettings.timeRendering) {\n                console.timeEnd('canvas rendering'); // eslint-disable-line no-console\n            }\n            BoostCanvas_fireEvent(series, 'renderedCanvas');\n            // Do not use chart.hideLoading, as it runs JS animation and\n            // will be blocked by buildKDTree. CSS animation looks good, but\n            // then it must be deleted in timeout. If we add the module to\n            // core, change hideLoading so we can skip this block.\n            if (loadingShown) {\n                loadingDiv.style.transition = 'opacity 250ms';\n                loadingDiv.opacity = 0;\n                chart.loadingShown = false;\n                destroyLoadingDiv = setTimeout(function () {\n                    if (loadingDiv.parentNode) { // In exporting it is falsy\n                        loadingDiv.parentNode.removeChild(loadingDiv);\n                    }\n                    chart.loadingDiv = chart.loadingSpan = null;\n                }, 250);\n            }\n            // Go back to prototype, ready to build\n            delete series.buildKDTree;\n            series.buildKDTree();\n            // Don't do async on export, the exportChart, getSVGForExport and\n            // getSVG methods are not chained for it.\n        }, chart.renderer.forExport ? Number.MAX_VALUE : void 0);\n    }\n    /**\n     * @private\n     */\n    function scatterCvsMarkerCircle(ctx, clientX, plotY, r) {\n        ctx.moveTo(clientX, plotY);\n        ctx.arc(clientX, plotY, r, 0, 2 * Math.PI, false);\n    }\n    /**\n     * Rect is twice as fast as arc, should be used for small markers.\n     * @private\n     */\n    function scatterCvsMarkerSquare(ctx, clientX, plotY, r) {\n        ctx.rect(clientX - r, plotY - r, r * 2, r * 2);\n    }\n    /**\n     * @private\n     */\n    function wrapHeatmapDrawPoints() {\n        var chart = this.chart,\n            ctx = this.getContext(),\n            inverted = this.chart.inverted,\n            xAxis = this.xAxis,\n            yAxis = this.yAxis;\n        if (ctx) {\n            // Draw the columns\n            this.points.forEach(function (point) {\n                var plotY = point.plotY;\n                var pointAttr;\n                if (typeof plotY !== 'undefined' &&\n                    !isNaN(plotY) &&\n                    point.y !== null &&\n                    ctx) {\n                    var _a = point.shapeArgs || {},\n                        _b = _a.x,\n                        x = _b === void 0 ? 0 : _b,\n                        _c = _a.y,\n                        y = _c === void 0 ? 0 : _c,\n                        _d = _a.width,\n                        width = _d === void 0 ? 0 : _d,\n                        _e = _a.height,\n                        height = _e === void 0 ? 0 : _e;\n                    if (!chart.styledMode) {\n                        pointAttr = point.series.pointAttribs(point);\n                    }\n                    else {\n                        pointAttr = point.series.colorAttribs(point);\n                    }\n                    ctx.fillStyle = pointAttr.fill;\n                    if (inverted) {\n                        ctx.fillRect(yAxis.len - y + xAxis.left, xAxis.len - x + yAxis.top, -height, -width);\n                    }\n                    else {\n                        ctx.fillRect(x + xAxis.left, y + yAxis.top, width, height);\n                    }\n                }\n            });\n            this.canvasToSVG();\n        }\n        else {\n            this.chart.showLoading('Your browser doesn\\'t support HTML5 canvas, <br>' +\n                'please use a modern browser');\n        }\n    }\n})(BoostCanvas || (BoostCanvas = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Extensions_BoostCanvas = (BoostCanvas);\n\n;// ./code/es5/es-modules/masters/modules/boost-canvas.js\n\n\n\n\nvar G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/**\n * Initialize the canvas boost.\n *\n * @function Highcharts.initCanvasBoost\n */\nG.initCanvasBoost = function () {\n    Extensions_BoostCanvas.compose(G.Chart, G.Series, G.seriesTypes);\n};\n/* harmony default export */ var boost_canvas_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__620__", "Column<PERSON><PERSON><PERSON>", "index", "mainCanvas", "BoostCanvas", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "boost_canvas_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "Boost_Boostables", "BoostableMap", "for<PERSON>ach", "item", "composed", "addEvent", "pick", "isChartSeriesBoosting", "chart", "allSeries", "series", "boost", "boostOptions", "options", "threshold", "seriesThreshold", "length", "allowBoostForce", "allowForce", "_i", "_a", "xAxis", "axis", "min", "Infinity", "dataMin", "max", "dataMax", "forceChartBoost", "seriesOptions", "canBoostCount", "needBoostCount", "_b", "allSeries_1", "boostThreshold", "visible", "type", "Boost_BoostableMap", "patientMax", "args", "arguments", "r", "Number", "MAX_VALUE", "t", "getColumn", "data", "points", "pushUnique", "target", "navigator", "clipBox", "x", "plotLeft", "y", "plotTop", "width", "plot<PERSON>id<PERSON>", "height", "plotHeight", "inverted", "top", "opposite", "left", "is", "yAxis", "getClipBox", "lateral", "pos", "verticalAxes", "Math", "len", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default", "Boost_WGLDrawMode", "clamp", "error", "WG<PERSON><PERSON><PERSON>_pick", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "gl", "errors", "uLocations", "createShader", "bind", "shaderProgram", "useProgram", "_this", "v", "stringToProgram", "f", "uloc", "getUniformLocation", "createProgram", "<PERSON><PERSON><PERSON><PERSON>", "linkProgram", "getProgramParameter", "LINK_STATUS", "bindAttribLocation", "pUniform", "psUniform", "fcUniform", "isBubbleUniform", "bubbleSizeAbsUniform", "bubbleSizeAreaUniform", "uSamplerUniform", "skipTranslationUniform", "isCircleUniform", "isInverted", "push", "getProgramInfoLog", "handleErrors", "join", "str", "shader", "VERTEX_SHADER", "FRAGMENT_SHADER", "shaderSource", "compileShader", "getShaderParameter", "COMPILE_STATUS", "getShaderInfoLog", "destroy", "deleteProgram", "fillColorUniform", "getProgram", "pointSizeUniform", "perspectiveUniform", "reset", "uniform1i", "setBubbleUniforms", "zCalcMin", "zCalcMax", "pixelRatio", "zMin", "zMax", "pxSizes", "getPxExtremes", "displayNegative", "zThreshold", "sizeBy", "sizeByAbsoluteValue", "setUniform", "minPxSize", "maxPxSize", "setColor", "color", "uniform4f", "setDrawAsCircle", "flag", "setInverted", "setPMatrix", "m", "uniformMatrix4fv", "setPointSize", "p", "uniform1f", "setSkipTranslation", "setTexture", "texture", "name", "val", "u", "WGLVertexBuffer", "dataComponents", "buffer", "iterator", "preAllocated", "vertAttribute", "components", "allocate", "size", "Float32Array", "vertexAttribPointer", "FLOAT", "build", "dataIn", "attrib", "farray", "deleteBuffer", "createBuffer", "<PERSON><PERSON><PERSON><PERSON>", "ARRAY_BUFFER", "bufferData", "STATIC_DRAW", "getAttribLocation", "enableVertexAttribArray", "b", "render", "from", "to", "drawMode", "drawArrays", "parse", "doc", "win", "isNumber", "isObject", "merge", "objectEach", "WG<PERSON>enderer_pick", "asBar", "asCircle", "contexts", "WG<PERSON><PERSON><PERSON>", "postRender<PERSON>allback", "isInited", "markerData", "textureHandles", "settings", "pointSize", "lineWidth", "fillColor", "useAlpha", "usePreallocated", "useGPUTranslations", "debug", "timeRendering", "timeSeriesProcessing", "timeSetup", "timeBufferCopy", "timeKDTree", "showSkipSummary", "orthoMatrix", "seriesPointCount", "isStacked", "xData", "s", "boosted", "stacking", "getPixelRatio", "devicePixelRatio", "setOptions", "allocate<PERSON><PERSON><PERSON>", "vbuffer", "allocateBufferForSingleSeries", "clear", "COLOR_BUFFER_BIT", "DEPTH_BUFFER_BIT", "pushSeriesData", "inst", "minVal", "scolor", "z", "low", "zoneColors", "isRange", "pointArrayMap", "sorted", "rawData", "xExtremes", "getExtremes", "xMin", "minPointOffset", "xMax", "yExtremes", "yMin", "yMax", "yData", "zData", "useRaw", "connectNulls", "sdata", "closestLeft", "closestRight", "chartDestroyed", "drawAsBar", "zoneAxis", "zones", "lastX", "lastY", "skipped", "hadPoints", "i", "px", "nx", "nextInside", "prevInside", "pcolor", "isXInside", "isYInside", "firstPoint", "zoneDefColor", "gapSize", "vlen", "boostData", "gapUnit", "closestPointRange", "zone", "zoneColor", "rgba", "value", "pointAttribs", "fill", "closestPointRangePx", "pushColor", "colorData", "vertice", "checkTreshold", "skipTranslation", "closeSegment", "segments", "beginSegment", "pushRect", "w", "h", "node", "levelDynamic", "sort", "point", "swidth", "pointAttr", "plotY", "isNaN", "shapeArgs", "x_1", "_c", "y_1", "_d", "_e", "styledMode", "colorAttribs", "stroke", "state_1", "_loop_1", "zoneColor_1", "pointOptions", "slice", "stackY", "some", "last", "toPixels", "hasMark<PERSON>", "abs", "logarithmic", "step", "console", "log", "pushSupplementPoint", "atStart", "concat", "pushSeries", "markerTo", "time", "markerFrom", "marker", "enabled", "showMarkers", "timeEnd", "flush", "setXAxis", "transA", "minPixelPadding", "pointRange", "horiz", "reversed", "setYAxis", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "has", "translation", "<PERSON><PERSON><PERSON>", "chartWidth", "chartHeight", "canvas", "viewport", "isMS", "si", "sindex", "cbuffer", "shapeOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yBottom", "get<PERSON><PERSON><PERSON>old", "isRadial", "radius", "shapeTexture", "symbol", "circle", "isReady", "bindTexture", "TEXTURE_2D", "handle", "markerGroup", "plotGroup", "seriesGroup", "addClass", "getStyle", "colorByPoint", "colors", "fillOpacity", "setOpacity", "boostBlending", "blendFunc", "SRC_ALPHA", "ONE", "blendEquation", "FUNC_ADD", "DST_COLOR", "ZERO", "FUNC_MIN", "blendFuncSeparate", "ONE_MINUS_SRC_ALPHA", "Array", "disableVertexAttribArray", "renderer", "forExport", "setTimeout", "setSize", "init", "noFlush", "getContext", "enable", "BLEND", "disable", "DEPTH_TEST", "depthFunc", "LESS", "createTexture", "fn", "props", "createElement", "ctx", "mozImageSmoothingEnabled", "webkitImageSmoothingEnabled", "msImageSmoothingEnabled", "imageSmoothingEnabled", "strokeStyle", "fillStyle", "activeTexture", "TEXTURE0", "texImage2D", "RGBA", "UNSIGNED_BYTE", "texParameteri", "TEXTURE_WRAP_S", "CLAMP_TO_EDGE", "TEXTURE_WRAP_T", "TEXTURE_MAG_FILTER", "LINEAR", "TEXTURE_MIN_FILTER", "e", "beginPath", "arc", "PI", "fillRect", "moveTo", "lineTo", "deleteTexture", "__spread<PERSON><PERSON>y", "pack", "ar", "l", "<PERSON><PERSON><PERSON><PERSON>", "column", "as<PERSON><PERSON><PERSON><PERSON>", "isArray", "splice", "start", "deleteCount", "removedAsSubarray", "items", "removed", "apply", "array", "<PERSON><PERSON><PERSON><PERSON>", "getPrototypeOf", "constructor", "result", "set", "subarray", "Data_ColumnUtils", "fireEvent", "DataTableCore_objectEach", "<PERSON><PERSON><PERSON>", "DataTableCore", "autoId", "id", "columns", "modified", "rowCount", "versionTag", "columnName", "applyRowCount", "deleteRows", "rowIndex", "length_1", "asReference", "getColumns", "columnNames", "keys", "reduce", "getRow", "map", "setColumn", "eventDetail", "setColumns", "silent", "setRow", "row", "insert", "indexRowCount", "cellValue", "addColumns", "getOptions", "BoostSeries_composed", "BoostSeries_doc", "noop", "BoostSeries_win", "BoostSeries_addEvent", "destroyObjectProperties", "BoostSeries_error", "extend", "BoostSeries_fireEvent", "BoostSeries_isNumber", "BoostSeries_pick", "BoostSeries_pushUnique", "wrap", "defined", "allocateIfNotSeriesBoosting", "BoostSeries_isChartSeriesBoosting", "boostEnabled", "createAndAttachRenderer", "ChartClass", "targetGroup", "group", "foSupported", "SVGForeignObjectElement", "hasClickHandler", "Boolean", "events", "click", "renderTarget", "image", "add", "attr", "href", "copy", "resize", "toDataURL", "targetFo", "targetCtx", "element", "append<PERSON><PERSON><PERSON>", "drawImage", "css", "pointerEvents", "mixedBlendMode", "opacity", "translate", "clipRect", "zIndex", "g", "box", "BoostSeries_getBoostClipRect", "clippedElement", "clip", "wgl", "destroyGraphics", "destroyElements", "seriesProp", "eachAsync", "arr", "finalFunc", "chunkSize", "noTimeout", "proceed", "requestAnimationFrame", "hasExtremes", "checkX", "dataLength", "dataTable", "colorAxis", "getSeriesBoosting", "forceCrop", "onSeriesDestroy", "hoverPoints", "filter", "hoverPoint", "onSeriesHide", "renderIfNotSeriesBoosting", "getPoint", "boostPoint", "PointClass", "pointClass", "isScatter", "category", "categories", "dist", "distX", "plotX", "percentage", "isInside", "isPointInside", "scatterProcessData", "force", "isDirty", "setTickInterval", "cropThreshold", "old", "_f", "_g", "_h", "getExtremesFromAll", "processedData", "processedXData", "processedYData", "xRangeNeeded", "y<PERSON>ange<PERSON><PERSON>ed", "cropped", "xDataMax", "xDataMin", "yDataMax", "yDataMin", "iEnd", "cropStart", "seriesRenderCanvas", "lastClientX", "maxVal", "minI", "maxI", "chartBoost", "seriesBoost", "lowData", "highData", "pointTaken", "sampling", "enableMouseTracking", "requireSorting", "compareX", "findNearestPointBy", "xDataFull", "nullYSubstitute", "nullInteraction", "isPanning", "graph", "addKDPoint", "clientX", "pushPoint", "ceil", "buildKDTree", "lineWidthFilter", "tagName", "children", "attributes", "operator", "wrapSeriesDrawPoints", "wrapSeriesGetExtremes", "wrapSeriesProcessData", "dataToMeasure", "getFirstValidPoint", "enterBoost", "bp", "alteredByBoost", "altered", "own", "allowDG", "directTouch", "stickyTracking", "finishedAnimating", "labelBySeries", "exitBoost", "setting", "wrapSeriesSearchPoint", "BoostCanvas_color", "BoostCanvas_doc", "BoostCanvas_noop", "BoostCanvas_addEvent", "BoostCanvas_fireEvent", "BoostCanvas_isNumber", "BoostCanvas_merge", "BoostCanvas_pick", "BoostCanvas_wrap", "ChartConstructor", "destroyLoadingDiv", "b64BlankPixel", "areaCvsDrawPoint", "lastPoint", "bubbleCvsMarkerCircle", "radii", "columnCvsDrawPoint", "rect", "onChartCanvasToSVG", "onChartClear", "clearRect", "seriesCanvasToSVG", "BoostCanvas_isChartSeriesBoosting", "seriesCvsLineTo", "seriesGetContext", "swapXY", "c", "style", "BoostCanvas_getBoostClipRect", "activeBoostSettings", "boostSettings", "strokeBatch", "cvsStrokeBatch", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "doFill", "loadingOptions", "loading", "cvsDrawPoint", "cvsLineTo", "cvs<PERSON><PERSON><PERSON>", "cvsMarkerSquare", "cvsMarkerCircle", "BoostCanvas_destroyGraphics", "labelStyle", "backgroundColor", "padding", "borderRadius", "clearTimeout", "showLoading", "<PERSON><PERSON><PERSON>", "drawPoint", "lineJoin", "scroller", "className", "Boost_BoostSeries", "isNull", "isNextInside", "isPrevInside", "NaN", "round", "loadingDiv", "loadingShown", "canvasToSVG", "transition", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "loadingSpan", "scatterCvsMarkerCircle", "scatterCvsMarkerSquare", "wrapHeatmapDrawPoints", "compose", "SeriesClass", "seriesTypes", "seriesProto", "renderCanvas", "AreaSeries_1", "area", "BubbleSeries_1", "bubble", "ColumnSeries_1", "HeatmapSeries_1", "heatmap", "ScatterSeries_1", "scatter", "callbacks", "areaProto", "bubbleProto", "columnProto", "scatterProto", "Extensions_BoostCanvas", "G", "initCanvasBoost", "Chart", "Series"], "mappings": "CAYA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,KAAQ,EACvE,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,kCAAmC,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,QAAQ,CAAC,CAAEJ,GACnG,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,kCAAkC,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,KAAQ,EAE1GJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAC9E,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,EACpF,OAAgB,AAAC,WACP,aACA,IA05ECC,EADPA,EA2cAC,EAAOC,EAqpCPC,EAz/HUC,EAAuB,CAE/B,IACC,SAASV,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGIO,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaf,OAAO,CAG5B,IAAIC,EAASW,CAAwB,CAACE,EAAS,CAAG,CAGjDd,QAAS,CAAC,CACX,EAMA,OAHAW,CAAmB,CAACG,EAAS,CAACb,EAAQA,EAAOD,OAAO,CAAEa,GAG/CZ,EAAOD,OAAO,AACtB,CAMCa,EAAoBI,CAAC,CAAG,SAAShB,CAAM,EACtC,IAAIiB,EAASjB,GAAUA,EAAOkB,UAAU,CACvC,WAAa,OAAOlB,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAY,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASpB,CAAO,CAAEsB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACxB,EAASuB,IAC5EE,OAAOC,cAAc,CAAC1B,EAASuB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,EAAkB,CACjE,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAsCpFE,EAlBhB,CACb,OACA,aACA,YACA,SACA,cACA,MACA,OACA,UACA,UACA,SACA,UACH,CAiCGC,EAAe,CAAC,EACpBD,EAAiBE,OAAO,CAAC,SAAUC,CAAI,EACnCF,CAAY,CAACE,EAAK,CAAG,CAAA,CACzB,GAuBe,AAACJ,IAA+EK,QAAQ,CAAvG,IAEIC,EAAW,AAACN,IAA+EM,QAAQ,CAAEC,EAAO,AAACP,IAA+EO,IAAI,CA8EpM,SAASC,EAAsBC,CAAK,EAChC,IAAIC,EAAYD,EAAME,MAAM,CACxBC,EAAQH,EAAMG,KAAK,CAAGH,EAAMG,KAAK,EAAI,CAAC,EACtCC,EAAeJ,EAAMK,OAAO,CAACF,KAAK,EAAI,CAAC,EACvCG,EAAYR,EAAKM,EAAaG,eAAe,CAAE,IACnD,GAAIN,EAAUO,MAAM,EAAIF,EACpB,MAAO,CAAA,EAEX,GAAIL,AAAqB,IAArBA,EAAUO,MAAM,CAChB,MAAO,CAAA,EAEX,IAAIC,EAAkBL,EAAaM,UAAU,CAC7C,GAAI,AAA2B,KAAA,IAApBD,EAAiC,CACxCA,EAAkB,CAAA,EAClB,IAAK,IAAIE,EAAK,EAAGC,EAAKZ,EAAMa,KAAK,CAAEF,EAAKC,EAAGJ,MAAM,CAAEG,IAAM,CACrD,IAAIG,EAAOF,CAAE,CAACD,EAAG,CACjB,GAAIb,EAAKgB,EAAKC,GAAG,CAAE,CAACC,KAAYlB,EAAKgB,EAAKG,OAAO,CAAE,CAACD,MAChDlB,EAAKgB,EAAKI,GAAG,CAAEF,KAAYlB,EAAKgB,EAAKK,OAAO,CAAEH,KAAW,CACzDP,EAAkB,CAAA,EAClB,KACJ,CACJ,CACJ,CACA,GAAI,AAAiC,KAAA,IAA1BN,EAAMiB,eAAe,CAAkB,CAC9C,GAAIX,EACA,OAAON,EAAMiB,eAAe,AAEhCjB,CAAAA,EAAMiB,eAAe,CAAG,KAAK,CACjC,CAMA,IAAK,IADDC,EAFAC,EAAgB,EAChBC,EAAiB,EAEZC,EAAK,EAA4BA,EAAKC,AAAhBxB,EAA4BO,MAAM,CAAEgB,IAAM,CACrE,IAAItB,EAASuB,AADcxB,CACH,CAACuB,EAAG,AAMS,CAAA,IAAjCH,AALJA,CAAAA,EAAgBnB,EAAOG,OAAO,AAAD,EAKXqB,cAAc,EAC5BxB,AAAmB,CAAA,IAAnBA,EAAOyB,OAAO,EAMdzB,AAAgB,YAAhBA,EAAO0B,IAAI,GAGXC,AApJ2CpC,CAoJzB,CAACS,EAAO0B,IAAI,CAAC,EAC/B,EAAEN,EAEFQ,AAuGZ,WAEI,IAAK,IADDC,EAAO,EAAE,CACJpB,EAAK,EAAGA,EAAKqB,UAAUxB,MAAM,CAAEG,IACpCoB,CAAI,CAACpB,EAAG,CAAGqB,SAAS,CAACrB,EAAG,CAF5B,IAIIsB,EAAI,CAACC,OAAOC,SAAS,CAWzB,OAVAJ,EAAKrC,OAAO,CAAC,SAAU0C,CAAC,EACpB,GAAI,MAAOA,GAEP,AAAoB,KAAA,IAAbA,EAAE5B,MAAM,EACX4B,EAAE5B,MAAM,CAAG,EAEX,OADAyB,EAAIG,EAAE5B,MAAM,CACL,CAAA,CAGnB,GACOyB,CACX,EAxHuB/B,EAAOmC,SAAS,CAAC,IAAK,CAAA,GAAOhB,EAAciB,IAAI,CAE9DpC,EAAOqC,MAAM,GAAMlB,CAAAA,EAAcK,cAAc,EAAIQ,OAAOC,SAAS,AAAD,GAC9D,EAAEZ,EAEV,CAQA,OAPApB,EAAMiB,eAAe,CAAGX,GAAoB,CAAA,AAI5Ca,IAAkBrB,EAAUO,MAAM,EAC9Be,IAAmBD,GACnBC,EAAiB,CAAA,EACdpB,EAAMiB,eAAe,AAChC,CAlJmN,AAAC7B,IAA+EiD,UAAU,CAuQhR,MAhP7B,SAA0BxC,CAAK,CAAEyC,CAAM,EACnC,IAAIC,EAAY1C,EAAM0C,SAAS,CAC3BC,EAAU,CACNC,EAAG5C,EAAM6C,QAAQ,CACjBC,EAAG9C,EAAM+C,OAAO,CAChBC,MAAOhD,EAAMiD,SAAS,CACtBC,OAAQlD,EAAMmD,UAAU,AAC5B,EAWJ,GAVIT,GAAa1C,EAAMoD,QAAQ,EAC3BT,EAAQK,KAAK,EAAIN,EAAUW,GAAG,CAAGX,EAAUQ,MAAM,CAC5CR,EAAUY,QAAQ,EACnBX,CAAAA,EAAQC,CAAC,CAAGF,EAAUa,IAAI,AAAD,GAGxBb,GAAa,CAAC1C,EAAMoD,QAAQ,EACjCT,CAAAA,EAAQO,MAAM,CAAGR,EAAUW,GAAG,CAAGX,EAAUQ,MAAM,CAAGlD,EAAM+C,OAAO,AAAD,EAGhEN,EAAOe,EAAE,CAAE,CACX,IACI3C,EAAQD,AADH6B,EACM5B,KAAK,CAChB4C,EAAQ7C,AAFH6B,EAEMgB,KAAK,CAEpB,GADAd,EAAU3C,EAAM0D,UAAU,CAACjB,GACvBzC,EAAMoD,QAAQ,CAAE,CAChB,IAAIO,EAAUhB,EAAQK,KAAK,AAC3BL,CAAAA,EAAQK,KAAK,CAAGL,EAAQO,MAAM,CAC9BP,EAAQO,MAAM,CAAGS,EACjBhB,EAAQC,CAAC,CAAGa,EAAMG,GAAG,CACrBjB,EAAQG,CAAC,CAAGjC,EAAM+C,GAAG,AACzB,MAEIjB,EAAQC,CAAC,CAAG/B,EAAM+C,GAAG,CACrBjB,EAAQG,CAAC,CAAGW,EAAMG,GAAG,AAE7B,CACA,GAAInB,IAAWzC,EAAO,CAClB,IAAI6D,EAAe7D,EAAMoD,QAAQ,CAAGpD,EAAMa,KAAK,CAAGb,EAAMyD,KAAK,AACrDI,CAAAA,EAAarD,MAAM,EAAI,IACvBmC,EAAQG,CAAC,CAAGgB,KAAK/C,GAAG,CAAC8C,CAAY,CAAC,EAAE,CAACD,GAAG,CAC5CjB,EAAQG,CAAC,EACTH,EAAQO,MAAM,CAAIW,CAAY,CAAC,EAAE,CAACD,GAAG,CACjC5D,EAAM+C,OAAO,CACbc,CAAY,CAAC,EAAE,CAACE,GAAG,CAE/B,CACA,OAAOpB,CACX,EAqMIqB,EAA+FjG,EAAoB,KACnHkG,EAAmHlG,EAAoBI,CAAC,CAAC6F,GAqC5GE,EAlBf,CACd,KAAQ,QACR,UAAa,QACb,WAAc,QACd,OAAU,QACV,YAAe,QACf,IAAO,QACP,KAAQ,aACR,QAAW,SACX,QAAW,YACX,QAAW,YACX,OAAU,QACd,EAsBIC,EAAQ,AAAC5E,IAA+E4E,KAAK,CAAEC,EAAQ,AAAC7E,IAA+E6E,KAAK,CAAEC,EAAiB,AAAC9E,IAA+EO,IAAI,CA+LnSwE,EAA2B,WAM3B,SAASA,EAAUC,CAAE,EAKjB,GAHA,IAAI,CAACC,MAAM,CAAG,EAAE,CAChB,IAAI,CAACC,UAAU,CAAG,CAAC,EACnB,IAAI,CAACF,EAAE,CAAGA,EACNA,GAAM,CAAC,IAAI,CAACG,YAAY,GACxB,MAER,CAiPA,OArOAJ,EAAUrF,SAAS,CAAC0F,IAAI,CAAG,WACnB,IAAI,CAACJ,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAACM,UAAU,CAAC,IAAI,CAACD,aAAa,CAE7C,EAMAN,EAAUrF,SAAS,CAACyF,YAAY,CAAG,WAC/B,IAAII,EAAQ,IAAI,CACZC,EAAI,IAAI,CAACC,eAAe,CAhMjB,ygHAgMgC,UAAWC,EAAI,IAAI,CAACD,eAAe,CA/NjE,+bA+NkF,YAAaE,EAAO,SAAU/G,CAAC,EAAI,OAAQ2G,EAAMP,EAAE,CAACY,kBAAkB,CAACL,EAAMF,aAAa,CAAEzG,EAAK,SAChM,AAAI,AAAC4G,GAAME,GAKX,IAAI,CAACL,aAAa,CAAG,IAAI,CAACL,EAAE,CAACa,aAAa,GAC1C,IAAI,CAACb,EAAE,CAACc,YAAY,CAAC,IAAI,CAACT,aAAa,CAAEG,GACzC,IAAI,CAACR,EAAE,CAACc,YAAY,CAAC,IAAI,CAACT,aAAa,CAAEK,GACzC,IAAI,CAACV,EAAE,CAACe,WAAW,CAAC,IAAI,CAACV,aAAa,EACjC,IAAI,CAACL,EAAE,CAACgB,mBAAmB,CAAC,IAAI,CAACX,aAAa,CAAE,IAAI,CAACL,EAAE,CAACiB,WAAW,IAMxE,IAAI,CAACjB,EAAE,CAACM,UAAU,CAAC,IAAI,CAACD,aAAa,EACrC,IAAI,CAACL,EAAE,CAACkB,kBAAkB,CAAC,IAAI,CAACb,aAAa,CAAE,EAAG,mBAClD,IAAI,CAACc,QAAQ,CAAGR,EAAK,YACrB,IAAI,CAACS,SAAS,CAAGT,EAAK,SACtB,IAAI,CAACU,SAAS,CAAGV,EAAK,aACtB,IAAI,CAACW,eAAe,CAAGX,EAAK,YAC5B,IAAI,CAACY,oBAAoB,CAAGZ,EAAK,iBACjC,IAAI,CAACa,qBAAqB,CAAGb,EAAK,oBAClC,IAAI,CAACc,eAAe,CAAGd,EAAK,YAC5B,IAAI,CAACe,sBAAsB,CAAGf,EAAK,mBACnC,IAAI,CAACgB,eAAe,CAAGhB,EAAK,YAC5B,IAAI,CAACiB,UAAU,CAAGjB,EAAK,cAChB,CAAA,IAjBH,IAAI,CAACV,MAAM,CAAC4B,IAAI,CAAC,IAAI,CAAC7B,EAAE,CAAC8B,iBAAiB,CAAC,IAAI,CAACzB,aAAa,GAC7D,IAAI,CAAC0B,YAAY,GACjB,IAAI,CAAC1B,aAAa,CAAG,CAAA,EACd,CAAA,IAZP,IAAI,CAACA,aAAa,CAAG,CAAA,EACrB,IAAI,CAAC0B,YAAY,GACV,CAAA,EAyBf,EAKAhC,EAAUrF,SAAS,CAACqH,YAAY,CAAG,WAC3B,IAAI,CAAC9B,MAAM,CAAChE,MAAM,EAClB4D,EAAM,qCACF,IAAI,CAACI,MAAM,CAAC+B,IAAI,CAAC,MAE7B,EASAjC,EAAUrF,SAAS,CAAC+F,eAAe,CAAG,SAAUwB,CAAG,CAAE5E,CAAI,EACrD,IAAI6E,EAAS,IAAI,CAAClC,EAAE,CAACG,YAAY,CAAC9C,AAAS,WAATA,EAAoB,IAAI,CAAC2C,EAAE,CAACmC,aAAa,CAAG,IAAI,CAACnC,EAAE,CAACoC,eAAe,QAGrG,CAFA,IAAI,CAACpC,EAAE,CAACqC,YAAY,CAACH,EAAQD,GAC7B,IAAI,CAACjC,EAAE,CAACsC,aAAa,CAACJ,GACjB,IAAI,CAAClC,EAAE,CAACuC,kBAAkB,CAACL,EAAQ,IAAI,CAAClC,EAAE,CAACwC,cAAc,GAOvDN,GANH,IAAI,CAACjC,MAAM,CAAC4B,IAAI,CAAC,kBACbxE,EACA,aACA,IAAI,CAAC2C,EAAE,CAACyC,gBAAgB,CAACP,IACtB,CAAA,EAGf,EAKAnC,EAAUrF,SAAS,CAACgI,OAAO,CAAG,WACtB,IAAI,CAAC1C,EAAE,EAAI,IAAI,CAACK,aAAa,GAC7B,IAAI,CAACL,EAAE,CAAC2C,aAAa,CAAC,IAAI,CAACtC,aAAa,EACxC,IAAI,CAACA,aAAa,CAAG,CAAA,EAE7B,EACAN,EAAUrF,SAAS,CAACkI,gBAAgB,CAAG,WACnC,OAAO,IAAI,CAACvB,SAAS,AACzB,EAOAtB,EAAUrF,SAAS,CAACmI,UAAU,CAAG,WAC7B,OAAO,IAAI,CAACxC,aAAa,AAC7B,EACAN,EAAUrF,SAAS,CAACoI,gBAAgB,CAAG,WACnC,OAAO,IAAI,CAAC1B,SAAS,AACzB,EACArB,EAAUrF,SAAS,CAACqI,kBAAkB,CAAG,WACrC,OAAO,IAAI,CAAC5B,QAAQ,AACxB,EAKApB,EAAUrF,SAAS,CAACsI,KAAK,CAAG,WACpB,IAAI,CAAChD,EAAE,EAAI,IAAI,CAACK,aAAa,GAC7B,IAAI,CAACL,EAAE,CAACiD,SAAS,CAAC,IAAI,CAAC3B,eAAe,CAAE,GACxC,IAAI,CAACtB,EAAE,CAACiD,SAAS,CAAC,IAAI,CAACtB,eAAe,CAAE,GAEhD,EAOA5B,EAAUrF,SAAS,CAACwI,iBAAiB,CAAG,SAAUvH,CAAM,CAAEwH,CAAQ,CAAEC,CAAQ,CAAEC,CAAU,EACjE,KAAK,IAApBA,GAAyBA,CAAAA,EAAa,CAAA,EAC1C,IAAIvG,EAAgBnB,EAAOG,OAAO,CAC9BwH,EAAO3F,OAAOC,SAAS,CACvB2F,EAAO,CAAC5F,OAAOC,SAAS,CAC5B,GAAI,IAAI,CAACoC,EAAE,EAAI,IAAI,CAACK,aAAa,EAAI1E,EAAOsD,EAAE,CAAC,UAAW,CACtD,IAAIuE,EAAU7H,EAAO8H,aAAa,GAClCH,EAAOxD,EAAehD,EAAcwG,IAAI,CAAE1D,EAAMuD,EAAUrG,AAAkC,CAAA,IAAlCA,EAAc4G,eAAe,CACnF5G,EAAc6G,UAAU,CAAG,CAAChG,OAAOC,SAAS,CAAE0F,IAClDC,EAAOzD,EAAehD,EAAcyG,IAAI,CAAEhE,KAAK5C,GAAG,CAAC4G,EAAMH,IACzD,IAAI,CAACpD,EAAE,CAACiD,SAAS,CAAC,IAAI,CAAC3B,eAAe,CAAE,GACxC,IAAI,CAACtB,EAAE,CAACiD,SAAS,CAAC,IAAI,CAACtB,eAAe,CAAE,GACxC,IAAI,CAAC3B,EAAE,CAACiD,SAAS,CAAC,IAAI,CAACzB,qBAAqB,CAAG7F,AAA0B,UAA1BA,EAAOG,OAAO,CAAC8H,MAAM,EACpE,IAAI,CAAC5D,EAAE,CAACiD,SAAS,CAAC,IAAI,CAAC1B,oBAAoB,CAAE5F,EAAOG,OAAO,CACtD+H,mBAAmB,EACxB,IAAI,CAACC,UAAU,CAAC,gBAAiBN,EAAQO,SAAS,CAAGV,GACrD,IAAI,CAACS,UAAU,CAAC,gBAAiBN,EAAQQ,SAAS,CAAGX,GACrD,IAAI,CAACS,UAAU,CAAC,aAAcR,GAC9B,IAAI,CAACQ,UAAU,CAAC,aAAcP,GAC9B,IAAI,CAACO,UAAU,CAAC,mBAAoBnI,EAAOG,OAAO,CAAC6H,UAAU,CACjE,CACJ,EAOA5D,EAAUrF,SAAS,CAACuJ,QAAQ,CAAG,SAAUC,CAAK,EACtC,IAAI,CAAClE,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAACmE,SAAS,CAAC,IAAI,CAAC9C,SAAS,CAAE6C,CAAK,CAAC,EAAE,CAAG,IAAOA,CAAK,CAAC,EAAE,CAAG,IAAOA,CAAK,CAAC,EAAE,CAAG,IAAOA,CAAK,CAAC,EAAE,CAExG,EAKAnE,EAAUrF,SAAS,CAAC0J,eAAe,CAAG,SAAUC,CAAI,EAC5C,IAAI,CAACrE,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAACiD,SAAS,CAAC,IAAI,CAACtB,eAAe,CAAE0C,GAAAA,EAEhD,EAOAtE,EAAUrF,SAAS,CAAC4J,WAAW,CAAG,SAAUD,CAAI,EACxC,IAAI,CAACrE,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAACiD,SAAS,CAAC,IAAI,CAACrB,UAAU,CAAEyC,EAE3C,EAOAtE,EAAUrF,SAAS,CAAC6J,UAAU,CAAG,SAAUC,CAAC,EACpC,IAAI,CAACxE,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAACyE,gBAAgB,CAAC,IAAI,CAACtD,QAAQ,CAAE,CAAA,EAAOqD,EAEvD,EAOAzE,EAAUrF,SAAS,CAACgK,YAAY,CAAG,SAAUC,CAAC,EACtC,IAAI,CAAC3E,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAAC4E,SAAS,CAAC,IAAI,CAACxD,SAAS,CAAEuD,EAE1C,EAKA5E,EAAUrF,SAAS,CAACmK,kBAAkB,CAAG,SAAUR,CAAI,EAC/C,IAAI,CAACrE,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAACiD,SAAS,CAAC,IAAI,CAACvB,sBAAsB,CAAE2C,CAAAA,CAAAA,AAAS,CAAA,IAATA,CAAY,EAEnE,EAOAtE,EAAUrF,SAAS,CAACoK,UAAU,CAAG,SAAUC,CAAO,EAC1C,IAAI,CAAC/E,EAAE,EAAI,IAAI,CAACK,aAAa,EAC7B,IAAI,CAACL,EAAE,CAACiD,SAAS,CAAC,IAAI,CAACxB,eAAe,CAAEsD,EAEhD,EAUAhF,EAAUrF,SAAS,CAACoJ,UAAU,CAAG,SAAUkB,CAAI,CAAEC,CAAG,EAChD,GAAI,IAAI,CAACjF,EAAE,EAAI,IAAI,CAACK,aAAa,CAAE,CAC/B,IAAI6E,EAAI,IAAI,CAAChF,UAAU,CAAC8E,EAAK,CAAI,IAAI,CAAC9E,UAAU,CAAC8E,EAAK,EAC9C,IAAI,CAAChF,EAAE,CAACY,kBAAkB,CAAC,IAAI,CAACP,aAAa,CACjD2E,GACJ,IAAI,CAAChF,EAAE,CAAC4E,SAAS,CAACM,EAAGD,EACzB,CACJ,EACOlF,CACX,IAwCIoF,EAAiC,WAMjC,SAASA,EAAgBnF,CAAE,CAAEkC,CAAM,CAAEkD,CAAc,EAQ/C,IAAI,CAACC,MAAM,CAAG,CAAA,EACd,IAAI,CAACC,QAAQ,CAAG,EAChB,IAAI,CAACC,YAAY,CAAG,CAAA,EACpB,IAAI,CAACC,aAAa,CAAG,CAAA,EACrB,IAAI,CAACC,UAAU,CAAGL,GAAkB,EACpC,IAAI,CAACA,cAAc,CAAGA,EACtB,IAAI,CAACpF,EAAE,CAAGA,EACV,IAAI,CAACkC,MAAM,CAAGA,CAClB,CAmIA,OAxHAiD,EAAgBzK,SAAS,CAACgL,QAAQ,CAAG,SAAUC,CAAI,EAC/C,IAAI,CAACL,QAAQ,CAAG,GAChB,IAAI,CAACC,YAAY,CAAG,IAAIK,aAAaD,AAAO,EAAPA,EACzC,EAKAR,EAAgBzK,SAAS,CAAC0F,IAAI,CAAG,WAC7B,GAAI,CAAC,IAAI,CAACiF,MAAM,CACZ,MAAO,CAAA,EAKX,IAAI,CAACrF,EAAE,CAAC6F,mBAAmB,CAAC,IAAI,CAACL,aAAa,CAAE,IAAI,CAACC,UAAU,CAAE,IAAI,CAACzF,EAAE,CAAC8F,KAAK,CAAE,CAAA,EAAO,EAAG,EAE9F,EAWAX,EAAgBzK,SAAS,CAACqL,KAAK,CAAG,SAAUC,CAAM,CAAEC,CAAM,CAAEb,CAAc,EACtE,IAAIc,QAEJ,CADA,IAAI,CAACnI,IAAI,CAAGiI,GAAU,EAAE,CACpB,AAAE,IAAI,CAACjI,IAAI,EAAI,AAAqB,IAArB,IAAI,CAACA,IAAI,CAAC9B,MAAM,EAAY,IAAI,CAACsJ,YAAY,GAKhE,IAAI,CAACE,UAAU,CAAGL,GAAkB,IAAI,CAACK,UAAU,CAC/C,IAAI,CAACJ,MAAM,EACX,IAAI,CAACrF,EAAE,CAACmG,YAAY,CAAC,IAAI,CAACd,MAAM,EAE/B,IAAI,CAACE,YAAY,EAClBW,CAAAA,EAAS,IAAIN,aAAa,IAAI,CAAC7H,IAAI,CAAA,EAEvC,IAAI,CAACsH,MAAM,CAAG,IAAI,CAACrF,EAAE,CAACoG,YAAY,GAClC,IAAI,CAACpG,EAAE,CAACqG,UAAU,CAAC,IAAI,CAACrG,EAAE,CAACsG,YAAY,CAAE,IAAI,CAACjB,MAAM,EACpD,IAAI,CAACrF,EAAE,CAACuG,UAAU,CAAC,IAAI,CAACvG,EAAE,CAACsG,YAAY,CAAE,IAAI,CAACf,YAAY,EAAIW,EAAQ,IAAI,CAAClG,EAAE,CAACwG,WAAW,EAEzF,IAAI,CAAChB,aAAa,CAAG,IAAI,CAACxF,EAAE,CACvByG,iBAAiB,CAAC,IAAI,CAACvE,MAAM,CAACW,UAAU,GAAIoD,GACjD,IAAI,CAACjG,EAAE,CAAC0G,uBAAuB,CAAC,IAAI,CAAClB,aAAa,EAElDU,EAAS,CAAA,EACF,CAAA,IAnBH,IAAI,CAACxD,OAAO,GACL,CAAA,EAmBf,EAIAyC,EAAgBzK,SAAS,CAACgI,OAAO,CAAG,WAC5B,IAAI,CAAC2C,MAAM,GACX,IAAI,CAACrF,EAAE,CAACmG,YAAY,CAAC,IAAI,CAACd,MAAM,EAChC,IAAI,CAACA,MAAM,CAAG,CAAA,EACd,IAAI,CAACG,aAAa,CAAG,CAAA,GAEzB,IAAI,CAACF,QAAQ,CAAG,EAChB,IAAI,CAACG,UAAU,CAAG,IAAI,CAACL,cAAc,EAAI,EACzC,IAAI,CAACrH,IAAI,CAAG,EAAE,AAClB,EAaAoH,EAAgBzK,SAAS,CAACmH,IAAI,CAAG,SAAUxD,CAAC,CAAEE,CAAC,CAAEvE,CAAC,CAAE2M,CAAC,EAC7C,IAAI,CAACpB,YAAY,GACjB,IAAI,CAACA,YAAY,CAAC,EAAE,IAAI,CAACD,QAAQ,CAAC,CAAGjH,EACrC,IAAI,CAACkH,YAAY,CAAC,EAAE,IAAI,CAACD,QAAQ,CAAC,CAAG/G,EACrC,IAAI,CAACgH,YAAY,CAAC,EAAE,IAAI,CAACD,QAAQ,CAAC,CAAGtL,EACrC,IAAI,CAACuL,YAAY,CAAC,EAAE,IAAI,CAACD,QAAQ,CAAC,CAAGqB,EAE7C,EAYAxB,EAAgBzK,SAAS,CAACkM,MAAM,CAAG,SAAUC,CAAI,CAAEC,CAAE,CAAEC,CAAQ,EAC3D,IAAI9K,EAAS,IAAI,CAACsJ,YAAY,CACtB,IAAI,CAACA,YAAY,CAACtJ,MAAM,CAAG,IAAI,CAAC8B,IAAI,CAAC9B,MAAM,OACnD,EAAK,IAAI,CAACoJ,MAAM,IAGXpJ,IAGD,CAAA,CAAC4K,GAAQA,EAAO5K,GAAU4K,EAAO,CAAA,GACjCA,CAAAA,EAAO,CAAA,EAEP,CAAA,CAACC,GAAMA,EAAK7K,CAAK,GACjB6K,CAAAA,EAAK7K,CAAK,GAEV4K,CAAAA,GAAQC,CAAC,IAGbC,EAAWA,GAAY,SACvB,IAAI,CAAC/G,EAAE,CAACgH,UAAU,CAAC,IAAI,CAAChH,EAAE,CAAC+G,EAAS,CAAEF,EAAO,IAAI,CAACpB,UAAU,CAAE,AAACqB,CAAAA,EAAKD,CAAG,EAAK,IAAI,CAACpB,UAAU,EACpF,CAAA,GACX,EACON,CACX,IAsBIjB,EAAQ,AAACxE,IAAuGuH,KAAK,CAErHC,EAAM,AAAClM,IAA+EkM,GAAG,CAAEC,EAAM,AAACnM,IAA+EmM,GAAG,CAEpLC,EAAW,AAACpM,IAA+EoM,QAAQ,CAAEC,EAAW,AAACrM,IAA+EqM,QAAQ,CAAEC,EAAQ,AAACtM,IAA+EsM,KAAK,CAAEC,EAAa,AAACvM,IAA+EuM,UAAU,CAAEC,EAAmB,AAACxM,IAA+EO,IAAI,CAUzfkM,EAAQ,CACR,OAAU,CAAA,EACV,YAAe,CAAA,EACf,IAAO,CAAA,EACP,KAAQ,CAAA,EACR,WAAc,CAAA,EACd,UAAa,CAAA,CACjB,EACIC,EAAW,CACX,QAAW,CAAA,EACX,OAAU,CAAA,CACd,EACIC,EAAW,CACX,QACA,qBACA,YACA,YACH,CAmBGC,EAA6B,WAM7B,SAASA,EAAYC,CAAkB,EAKnC,IAAI,CAAC9J,IAAI,CAAG,EAAE,CAEd,IAAI,CAACY,MAAM,CAAG,EAEd,IAAI,CAACmJ,QAAQ,CAAG,CAAA,EAEhB,IAAI,CAACC,UAAU,CAAG,EAAE,CAEpB,IAAI,CAACpM,MAAM,CAAG,EAAE,CAEhB,IAAI,CAACqM,cAAc,CAAG,CAAC,EAEvB,IAAI,CAACvJ,KAAK,CAAG,EACb,IAAI,CAACoJ,kBAAkB,CAAGA,EAC1B,IAAI,CAACI,QAAQ,CAAG,CACZC,UAAW,EACXC,UAAW,EACXC,UAAW,UACXC,SAAU,CAAA,EACVC,gBAAiB,CAAA,EACjBC,mBAAoB,CAAA,EACpBC,MAAO,CACHC,cAAe,CAAA,EACfC,qBAAsB,CAAA,EACtBC,UAAW,CAAA,EACXC,eAAgB,CAAA,EAChBC,WAAY,CAAA,EACZC,gBAAiB,CAAA,CACrB,CACJ,CACJ,CAqpCA,OAvoCAlB,EAAYmB,WAAW,CAAG,SAAUtK,CAAK,CAAEE,CAAM,EAG7C,MAAO,CACH,EAAIF,EAAO,EAAG,EAAG,EACjB,EAAG,CAAE,CAAA,EAAIE,CAAK,EAAI,EAAG,EACrB,EAAG,EAAG,GAAmB,EACzB,GAAI,EAAG,GAA8B,EACxC,AACL,EAIAiJ,EAAYoB,gBAAgB,CAAG,SAAUrN,CAAM,EAC3C,IAAIsN,EACAC,EACAC,SACJ,AAAIxN,EAAOyN,OAAO,EACdH,EAAY,CAAC,CAACtN,EAAOG,OAAO,CAACuN,QAAQ,CACrCH,EAAS,AAACvN,CAAAA,EAAOmC,SAAS,CAAC,KAAK7B,MAAM,CAClCN,EAAOmC,SAAS,CAAC,KACjB,KAAK,CAAA,GACLnC,EAAOG,OAAO,CAACoN,KAAK,EACpBvN,EAAOmC,SAAS,CAAC,IAAK,CAAA,GAC1BqL,EAAI,AAACF,CAAAA,EAAYtN,EAAOoC,IAAI,CAAImL,GAASvN,EAAOG,OAAO,CAACiC,IAAI,EACvD9B,MAAM,CACPN,AAAgB,YAAhBA,EAAO0B,IAAI,CACX8L,GAAK,GAEAxN,AAAgB,YAAhBA,EAAO0B,IAAI,CAChB8L,GAAK,EAEA1B,CAAK,CAAC9L,EAAO0B,IAAI,CAAC,EACvB8L,CAAAA,GAAK,CAAA,EAEFA,GAEJ,CACX,EASAvB,EAAYlN,SAAS,CAAC4O,aAAa,CAAG,WAClC,OAAO,IAAI,CAACrB,QAAQ,CAAC5E,UAAU,EAAI8D,EAAIoC,gBAAgB,EAAI,CAC/D,EAIA3B,EAAYlN,SAAS,CAAC8O,UAAU,CAAG,SAAU1N,CAAO,EAI1C,eAAgBA,GAClBA,CAAAA,EAAQuH,UAAU,CAAG,CAAA,EAEzBiE,EAAM,CAAA,EAAM,IAAI,CAACW,QAAQ,CAAEnM,EAC/B,EAKA8L,EAAYlN,SAAS,CAAC+O,cAAc,CAAG,SAAUhO,CAAK,EAClD,IAAIiO,EAAU,IAAI,CAACA,OAAO,CACtBP,EAAI,CACH,CAAA,IAAI,CAAClB,QAAQ,CAACK,eAAe,GAGlC7M,EAAME,MAAM,CAACR,OAAO,CAAC,SAAUQ,CAAM,EAC7BA,EAAOyN,OAAO,EACdD,CAAAA,GAAKvB,EAAYoB,gBAAgB,CAACrN,EAAM,CAEhD,GACA+N,GAAWA,EAAQhE,QAAQ,CAACyD,GAChC,EAIAvB,EAAYlN,SAAS,CAACiP,6BAA6B,CAAG,SAAUhO,CAAM,EAClE,IAAI+N,EAAU,IAAI,CAACA,OAAO,CACtBP,EAAI,CACH,CAAA,IAAI,CAAClB,QAAQ,CAACK,eAAe,GAG9B3M,EAAOyN,OAAO,EACdD,CAAAA,EAAIvB,EAAYoB,gBAAgB,CAACrN,EAAM,EAE3C+N,GAAWA,EAAQhE,QAAQ,CAACyD,GAChC,EAKAvB,EAAYlN,SAAS,CAACkP,KAAK,CAAG,WAC1B,IAAI5J,EAAK,IAAI,CAACA,EAAE,AAChBA,CAAAA,GAAMA,EAAG4J,KAAK,CAAC5J,EAAG6J,gBAAgB,CAAG7J,EAAG8J,gBAAgB,CAC5D,EAOAlC,EAAYlN,SAAS,CAACqP,cAAc,CAAG,SAAUpO,CAAM,CAAEqO,CAAI,EACzD,IACI3N,EACAY,EAsBAgN,EACAC,EAKA7L,EACAE,EACAxE,EACAoQ,EAIAC,EAOAC,EA5CA9J,EAAQ,IAAI,CAGZxC,EAAO,IAAI,CAACA,IAAI,CAAEkK,EAAW,IAAI,CAACA,QAAQ,CAAEyB,EAAU,IAAI,CAACA,OAAO,CAAEY,EAAW3O,EAAO4O,aAAa,EAC/F5O,AAAmC,aAAnCA,EAAO4O,aAAa,CAACvI,IAAI,CAAC,KAAsBvG,EAAQE,EAAOF,KAAK,CAAEK,EAAUH,EAAOG,OAAO,CAAE0O,EAAS7O,EAAO6O,MAAM,CAAElO,EAAQX,EAAOW,KAAK,CAAE4C,EAAQvD,EAAOuD,KAAK,CAAE+J,EAAY,CAAC,CAACnN,EAAQuN,QAAQ,CAAEoB,EAAU3O,EAAQiC,IAAI,CAAE2M,EAAY/O,EAAOW,KAAK,CAACqO,WAAW,GAEpQC,EAAOF,EAAUlO,GAAG,CAAIb,CAAAA,EAAOW,KAAK,CAACuO,cAAc,EAAI,CAAA,EAAIC,EAAOJ,EAAU/N,GAAG,CAAIhB,CAAAA,EAAOW,KAAK,CAACuO,cAAc,EAAI,CAAA,EAAIE,EAAYpP,EAAOuD,KAAK,CAACyL,WAAW,GAAIK,EAAOD,EAAUvO,GAAG,CAAIb,CAAAA,EAAOuD,KAAK,CAAC2L,cAAc,EAAI,CAAA,EAAII,EAAOF,EAAUpO,GAAG,CAAIhB,CAAAA,EAAOuD,KAAK,CAAC2L,cAAc,EAAI,CAAA,EAAI3B,EAAQ,AAACvN,CAAAA,EAAOmC,SAAS,CAAC,KAAK7B,MAAM,CAAGN,EAAOmC,SAAS,CAAC,KAAO,KAAK,CAAA,GAAMhC,EAAQoN,KAAK,EAAIvN,EAAOmC,SAAS,CAAC,IAAK,CAAA,GAAOoN,EAAQ,AAACvP,CAAAA,EAAOmC,SAAS,CAAC,KAAK7B,MAAM,CAAGN,EAAOmC,SAAS,CAAC,KAAO,KAAK,CAAA,GAAMhC,EAAQoP,KAAK,EAAIvP,EAAOmC,SAAS,CAAC,IAAK,CAAA,GAAOqN,EAAQ,AAACxP,CAAAA,EAAOmC,SAAS,CAAC,KAAK7B,MAAM,CAAGN,EAAOmC,SAAS,CAAC,KAAO,KAAK,CAAA,GAAMhC,EAAQqP,KAAK,EAAIxP,EAAOmC,SAAS,CAAC,IAAK,CAAA,GAAOsN,EAAS,CAAClC,GAASA,AAAiB,IAAjBA,EAAMjN,MAAM,CAU9pBoP,EAAevP,EAAQuP,YAAY,CAInCrN,EAASrC,EAAOqC,MAAM,EAAI,CAAA,EAAOsN,EAAQrC,EAAYtN,EAAOoC,IAAI,CAAImL,GAASuB,EAAUc,EAAc,CAAElN,EAAGV,OAAOC,SAAS,CAAEW,EAAG,CAAE,EAAGiN,EAAe,CAAEnN,EAAG,CAACV,OAAOC,SAAS,CAAEW,EAAG,CAAE,EAA2CkN,EAAiB,AAAuB,KAAA,IAAhBhQ,EAAMtC,KAAK,CAAkBuS,EAAYjE,CAAK,CAAC9L,EAAO0B,IAAI,CAAC,CAAEsO,EAAW7P,EAAQ6P,QAAQ,EAAI,IAAKC,EAAQ9P,EAAQ8P,KAAK,EAAI,CAAA,EAAO7P,EAAYD,EAAQC,SAAS,CAAEsH,EAAa,IAAI,CAACiG,aAAa,GAClb5K,EAAY/C,EAAOF,KAAK,CAACiD,SAAS,CAClCmN,EAAQ,CAAA,EACRC,EAAQ,CAAA,EAIRC,EAAU,EACVC,EAAY,CAAA,EAMZC,EAAI,GACJC,GAAK,CAAA,EACLC,GAAK,CAAA,EAELC,GAAa,CAAA,EACbC,GAAa,CAAA,EACbC,GAAS,CAAA,EACTC,GAAY,CAAA,EACZC,GAAY,CAAA,EACZC,GAAa,CAAA,EAEbC,GAAe,CAAA,EACfC,GAAU,CAAA,EACVC,GAAO,EACX,GAAI9Q,CAAAA,EAAQ+Q,SAAS,GAAI/Q,CAAAA,EAAQ+Q,SAAS,CAAC5Q,MAAM,CAAG,CAAA,GAGhDH,EAAQ6Q,OAAO,EACfA,CAAAA,GAAU7Q,AAAoB,UAApBA,EAAQgR,OAAO,CACrBhR,EAAQ6Q,OAAO,CAAGhR,EAAOoR,iBAAiB,CAC1CjR,EAAQ6Q,OAAO,AAAD,EAElBf,IACAvB,EAAa,EAAE,CACfuB,EAAMzQ,OAAO,CAAC,SAAU6R,CAAI,CAAEf,CAAC,EAC3B,GAAIe,EAAK9I,KAAK,CAAE,CACZ,IAAI+I,EAAY/I,EAAM8I,EAAK9I,KAAK,EAAEgJ,IAAI,AACtCD,CAAAA,CAAS,CAAC,EAAE,EAAI,IAChBA,CAAS,CAAC,EAAE,EAAI,IAChBA,CAAS,CAAC,EAAE,EAAI,IAChB5C,CAAU,CAAC4B,EAAE,CAAGgB,EACXP,IAAgB,AAAsB,KAAA,IAAfM,EAAKG,KAAK,EAClCT,CAAAA,GAAeO,CAAQ,CAE/B,CACJ,GACKP,KAGDA,GAAexI,EAFI,AAACvI,EAAOyR,YAAY,EAAIzR,EAAOyR,YAAY,GAAGC,IAAI,EAC7D1R,EAAOuI,KAAK,EACcgJ,IAAI,CACtCR,EAAY,CAAC,EAAE,EAAI,IACnBA,EAAY,CAAC,EAAE,EAAI,IACnBA,EAAY,CAAC,EAAE,EAAI,MAGvBjR,EAAMoD,QAAQ,EACdH,CAAAA,EAAY/C,EAAOF,KAAK,CAACmD,UAAU,AAAD,EAEtCjD,EAAO2R,mBAAmB,CAAG3P,OAAOC,SAAS,CAK7C,IAAI2P,GAAY,SAAUrJ,CAAK,EACnBA,IACA8F,EAAKwD,SAAS,CAAC3L,IAAI,CAACqC,CAAK,CAAC,EAAE,EAChC8F,EAAKwD,SAAS,CAAC3L,IAAI,CAACqC,CAAK,CAAC,EAAE,EAC5B8F,EAAKwD,SAAS,CAAC3L,IAAI,CAACqC,CAAK,CAAC,EAAE,EAC5B8F,EAAKwD,SAAS,CAAC3L,IAAI,CAACqC,CAAK,CAAC,EAAE,EAEpC,EAKIuJ,GAAU,SAAUpP,CAAC,CACrBE,CAAC,CACDmP,CAAa,CACbxF,CAAS,CACThE,CAAK,EACiB,KAAK,IAAnBgE,GAAwBA,CAAAA,EAAY,CAAA,EACxCqF,GAAUrJ,GAEK,IAAfb,GAAqB,CAAA,CAAC4E,EAASM,kBAAkB,EACjDyB,EAAK2D,eAAe,AAAD,IACnBtP,GAAKgF,EACL9E,GAAK8E,EACL6E,GAAa7E,GAEb4E,EAASK,eAAe,EAAIoB,GAC5BA,EAAQ7H,IAAI,CAACxD,EAAGE,EAAGmP,GAAAA,EAAuBxF,GAC1C0E,IAAQ,IAGR7O,EAAK8D,IAAI,CAACxD,GACVN,EAAK8D,IAAI,CAACtD,GACVR,EAAK8D,IAAI,CAAC6L,EAAgBrK,EAAa,GACvCtF,EAAK8D,IAAI,CAACqG,GAElB,EAII0F,GAAe,WACP5D,EAAK6D,QAAQ,CAAC5R,MAAM,EACpB+N,CAAAA,EAAK6D,QAAQ,CAAC7D,EAAK6D,QAAQ,CAAC5R,MAAM,CAAG,EAAE,CAAC6K,EAAE,CAAG/I,EAAK9B,MAAM,EAAI2Q,EAAG,CAE3E,EAKIkB,GAAe,WAKP9D,CAAAA,CAAAA,EAAK6D,QAAQ,CAAC5R,MAAM,EACpB+N,EAAK6D,QAAQ,CAAC7D,EAAK6D,QAAQ,CAAC5R,MAAM,CAAG,EAAE,CAAC4K,IAAI,GAAM9I,CAAAA,EAAK9B,MAAM,EAAI2Q,EAAG,CAAC,IAG7EgB,KACA5D,EAAK6D,QAAQ,CAAChM,IAAI,CAAC,CACfgF,KAAM9I,EAAK9B,MAAM,EAAI2Q,EACzB,GACJ,EAKImB,GAAW,SAAU1P,CAAC,CACtBE,CAAC,CACDyP,CAAC,CACDC,CAAC,CACD/J,CAAK,EACDqJ,GAAUrJ,GACduJ,GAAQpP,EAAI2P,EAAGzP,GACfgP,GAAUrJ,GACVuJ,GAAQpP,EAAGE,GACXgP,GAAUrJ,GACVuJ,GAAQpP,EAAGE,EAAI0P,GACfV,GAAUrJ,GACVuJ,GAAQpP,EAAGE,EAAI0P,GACfV,GAAUrJ,GACVuJ,GAAQpP,EAAI2P,EAAGzP,EAAI0P,GACnBV,GAAUrJ,GACVuJ,GAAQpP,EAAI2P,EAAGzP,EACnB,EAIA,GAFAuP,KAEI9P,GAAUA,EAAO/B,MAAM,CAAG,EAAG,CAG7B+N,EAAK2D,eAAe,CAAG,CAAA,EAEvB3D,EAAKjD,QAAQ,CAAG,YAEZ/I,CAAM,CAAC,EAAE,CAACkQ,IAAI,EAAIlQ,CAAM,CAAC,EAAE,CAACkQ,IAAI,CAACC,YAAY,EAC7CnQ,EAAOoQ,IAAI,CAAC,SAAUpU,CAAC,CAAE2M,CAAC,EACtB,GAAI3M,EAAEkU,IAAI,CAAE,CACR,GAAIlU,EAAEkU,IAAI,CAACC,YAAY,CACnBxH,EAAEuH,IAAI,CAACC,YAAY,CACnB,OAAO,EAEX,GAAInU,EAAEkU,IAAI,CAACC,YAAY,CACnBxH,EAAEuH,IAAI,CAACC,YAAY,CACnB,OAAO,EAEf,CACA,OAAO,CACX,GAEJnQ,EAAO7C,OAAO,CAAC,SAAUkT,CAAK,EAC1B,IACIC,EACAC,EAFAC,EAAQH,EAAMG,KAAK,CAGvB,GAAI,AAAiB,KAAA,IAAVA,GACP,CAACC,MAAMD,IACPH,AAAY,OAAZA,EAAM9P,CAAC,EACP8P,EAAMK,SAAS,CAAE,CACjB,IAAIrS,EAAKgS,EAAMK,SAAS,CACpBzR,EAAKZ,EAAGgC,CAAC,CACTsQ,EAAM1R,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EAC1B2R,EAAKvS,EAAGkC,CAAC,CACTsQ,EAAMD,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EAC1BE,EAAKzS,EAAGoC,KAAK,CACbA,EAAQqQ,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EAC5BC,EAAK1S,EAAGsC,MAAM,CACdA,EAASoQ,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EAKjCT,EAASC,AAJTA,CAAAA,EAAY9S,EAAMuT,UAAU,CACxBX,EAAM1S,MAAM,CACPsT,YAAY,CAACZ,GAClBE,EAAYF,EAAM1S,MAAM,CAACyR,YAAY,CAACiB,EAAK,CAC7B,CAAC,eAAe,EAAI,EAEtC/B,GAASpI,EAAMqK,EAAUlB,IAAI,EAAEH,IAAI,CACnCZ,EAAM,CAAC,EAAE,EAAI,IACbA,EAAM,CAAC,EAAE,EAAI,IACbA,EAAM,CAAC,EAAE,EAAI,IAQT3Q,EAAOsD,EAAE,CAAC,aACVqP,EAASA,GAAU,EACnBpE,EAAShG,EAAMqK,EAAUW,MAAM,EAAEhC,IAAI,CACrChD,CAAM,CAAC,EAAE,EAAI,IACbA,CAAM,CAAC,EAAE,EAAI,IACbA,CAAM,CAAC,EAAE,EAAI,IACb6D,GAASY,EAAKE,EAAKpQ,EAAOE,EAAQuL,GAClCoE,GAAU,GAUV3S,EAAOsD,EAAE,CAAC,YAAcxD,EAAMoD,QAAQ,GACtC8P,EAAMrS,EAAMkD,GAAG,CAAGmP,EAClBE,EAAM3P,EAAMM,GAAG,CAAGqP,EAClBpQ,EAAQ,CAACA,EACTE,EAAS,CAACA,GAEdoP,GAASY,EAAML,EAAQO,EAAMP,EAAQ7P,EAAS6P,AAAS,EAATA,EAAa3P,EAAU2P,AAAS,EAATA,EAAahC,GACtF,CACJ,GACAsB,KACA,MACJ,CA4QA,KAAO3B,EAAIX,EAAMrP,MAAM,CAAG,GAElBkT,AAAY,UADFC,AA5QJ,WAEV,GAAI,AAAa,KAAA,IADbrV,CAAAA,EAAIuR,CAAK,CAAC,EAAEW,EAAE,AAAD,EAEb,MAAO,WAMX,GAAIR,EACA,MAAO,QAeX,IAkHQ4D,EAlHJC,EAAe7E,GAAWA,CAAO,CAACwB,EAAE,OA+CxC,CA9CI,CAACb,GAAU/D,EAASiI,EAAc,CAAA,IAC9BA,EAAapL,KAAK,GAClBoI,GAASpI,EAAMoL,EAAapL,KAAK,EAAEgJ,IAAI,CACvCZ,EAAM,CAAC,EAAE,EAAI,IACbA,EAAM,CAAC,EAAE,EAAI,IACbA,EAAM,CAAC,EAAE,EAAI,KAGjBlB,GACA/M,EAAItE,CAAC,CAAC,EAAE,CACRwE,EAAIxE,CAAC,CAAC,EAAE,CACJuR,CAAK,CAACW,EAAI,EAAE,EACZE,CAAAA,GAAKb,CAAK,CAACW,EAAI,EAAE,CAAC,EAAE,AAAD,EAEnBX,CAAK,CAACW,EAAI,EAAE,EACZC,CAAAA,GAAKZ,CAAK,CAACW,EAAI,EAAE,CAAC,EAAE,AAAD,EAEnBlS,EAAEkC,MAAM,EAAI,IACZkO,EAAIpQ,CAAC,CAAC,EAAE,CACJA,CAAC,CAAC,EAAE,CAAGiQ,EAAKzG,IAAI,EAChByG,CAAAA,EAAKzG,IAAI,CAAGxJ,CAAC,CAAC,EAAE,AAAD,EAEfA,CAAC,CAAC,EAAE,CAAGiQ,EAAK1G,IAAI,EAChB0G,CAAAA,EAAK1G,IAAI,CAAGvJ,CAAC,CAAC,EAAE,AAAD,KAKvBsE,EAAItE,EACJwE,EAAI2M,MAAAA,EAAqC,KAAK,EAAIA,CAAK,CAACe,EAAE,CACtDX,CAAK,CAACW,EAAI,EAAE,EACZE,CAAAA,GAAKb,CAAK,CAACW,EAAI,EAAE,AAAD,EAEhBX,CAAK,CAACW,EAAI,EAAE,EACZC,CAAAA,GAAKZ,CAAK,CAACW,EAAI,EAAE,AAAD,EAEhBd,GAASA,EAAMlP,MAAM,GACrBkO,EAAIgB,CAAK,CAACc,EAAE,CACRd,CAAK,CAACc,EAAE,CAAGjC,EAAKzG,IAAI,EACpByG,CAAAA,EAAKzG,IAAI,CAAG4H,CAAK,CAACc,EAAE,AAAD,EAEnBd,CAAK,CAACc,EAAE,CAAGjC,EAAK1G,IAAI,EACpB0G,CAAAA,EAAK1G,IAAI,CAAG6H,CAAK,CAACc,EAAE,AAAD,IAI3B,AAACZ,GAAiBhN,AAAM,OAANA,GAAcE,AAAM,OAANA,IAIhC4N,IAAMA,IAAMvB,GAAQuB,IAAMrB,GAC1BsB,CAAAA,GAAa,CAAA,CAAG,EAEhBF,IAAMA,IAAMtB,GAAQsB,IAAMpB,GAC1BuB,CAAAA,GAAa,CAAA,CAAG,EAEhB/B,GACIc,GACA7M,CAAAA,EAAIxE,EAAEwV,KAAK,CAAC,EAAG,EAAC,EAEpBnF,EAAM,AAAyC,OAAxC/N,CAAAA,EAAKV,EAAOmC,SAAS,CAAC,MAAO,CAAA,EAAI,GAAezB,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,CAAE,CAAC4P,EAAE,CACrF1N,EAAI,AAAC,CAAA,AAA0C,OAAzCtB,CAAAA,EAAKtB,EAAOmC,SAAS,CAAC,OAAQ,CAAA,EAAI,GAAeb,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,CAAE,CAACgP,EAAE,AAAD,GAAM,GAErFhD,IACL5K,EAAItE,EAAEsE,CAAC,CAEP+L,EAAM7L,AADNA,CAAAA,EAAIxE,EAAEyV,MAAM,AAAD,EACDzV,EAAEwE,CAAC,QAEbyM,GAGA,MADAC,GAEAuB,CAAAA,GAAYjO,GAAKyM,GAAQzM,GAAK0M,CAAG,EAGjC,AAACT,GAAWgC,KAGZnO,EAAIyM,GAAQU,EAAanN,CAAC,CAAGyM,IAC7BU,EAAanN,CAAC,CAAGA,EACjBmN,EAAajN,CAAC,CAAGA,GAEjBF,EAAIuM,GAAQW,EAAYlN,CAAC,CAAGuM,IAC5BW,EAAYlN,CAAC,CAAGA,EAChBkN,EAAYhN,CAAC,CAAGA,GAEhBA,AAAM,OAANA,GAAc8M,GACP,WAKP9M,AAAM,OAANA,GAAe,CAAA,AAACiO,KAAalB,CAAAA,EAAMrP,MAAM,CAAG,CAAA,GAC3CmQ,IAAeC,EAAS,GAOzB7B,CAAAA,GAAY2B,CAAAA,IAAMvB,GAAQvM,GAAKuM,CAAG,GACjCsB,CAAAA,IAAMpB,GAAQzM,GAAKyM,CAAG,GACvB,CAACN,GAAYnM,GAAKuM,GAAUvM,GAAKyM,CAAK,GACtCyB,CAAAA,GAAY,CAAA,CAAG,EAEf,AAACA,IAAcH,IAAeC,KAG9BM,IAAWtO,EAAI6N,GAAKS,IACpBmB,KAGAlC,IAEAA,EAAM6D,IAAI,CAAC,SACXzC,CAAI,CAAEf,CAAC,EACH,IAAIyD,EAAO9D,CAAK,CAACK,EAAI,EAAE,OACvB,AAAIN,AAAa,MAAbA,EACA,AAA0B,KAAA,IAAfqB,EAAKG,KAAK,EACjB9O,GAAK2O,EAAKG,KAAK,GACX9C,CAAU,CAAC4B,EAAE,EACZ,CAAA,CAACyD,GAAQrR,GAAKqR,EAAKvC,KAAK,AAAD,GACxBkC,CAAAA,EAAchF,CAAU,CAAC4B,EAAE,AAAD,EAEvB,CAAA,GAIf,AAA0B,KAAA,IAAfe,EAAKG,KAAK,EAAoB5O,GAAKyO,EAAKG,KAAK,GAChD9C,CAAU,CAAC4B,EAAE,EACZ,CAAA,CAACyD,GAAQnR,GAAKmR,EAAKvC,KAAK,AAAD,GACxBkC,CAAAA,EAAchF,CAAU,CAAC4B,EAAE,AAAD,EAEvB,CAAA,EAGf,GACAK,GAAS+C,GAAe3C,IAAgBJ,IAGxC,CAACrE,EAASM,kBAAkB,GAC5ByB,EAAK2D,eAAe,CAAG,CAAA,EACvBtP,EAAI/B,EAAMqT,QAAQ,CAACtR,EAAG,CAAA,GACtBE,EAAIW,EAAMyQ,QAAQ,CAACpR,EAAG,CAAA,GAQlBF,EAAIK,GAIAsL,AAAkB,WAAlBA,EAAKjD,QAAQ,GACN,YAUfiD,EAAK4F,UAAU,EAAIrD,IAYfV,AAAU,CAAA,IAAVA,GACAlQ,CAAAA,EAAO2R,mBAAmB,CAAG/N,KAAK/C,GAAG,CAACb,EAAO2R,mBAAmB,CAAE/N,KAAKsQ,GAAG,CAACxR,EAAIwN,GAAM,EAKzF,CAAC5D,EAASM,kBAAkB,EAC5B,CAACN,EAASK,eAAe,EACxBuD,GAAStM,AApcsL,EAoctLA,KAAKsQ,GAAG,CAACxR,EAAIwN,IACtBC,GAASvM,AArc0M,EAqc1MA,KAAKsQ,GAAG,CAACtR,EAAIuN,KACnB7D,EAASO,KAAK,CAACM,eAAe,EAC9B,EAAEiD,EAEC,iBAEPL,IACAzB,EAASG,GAAO,EACZA,CAAAA,AAAQ,CAAA,IAARA,GAAiB,AAAe,KAAA,IAARA,CAAkB,IAEtCH,EADA1L,EAAI,EACKA,EAGA,GAGZ,CAAA,AAAC+L,GAAYrB,CAAQ,IACtB/J,EAAM4Q,WAAW,EAEjB7F,CAAAA,EAAS1K,KAAK5C,GAAG,CAACZ,AAAc,OAAdA,EAAqBiP,EAAOjP,EAC9CiP,EAAI,EAEH/C,EAASM,kBAAkB,EAC5B0B,CAAAA,EAAS/K,EAAMyQ,QAAQ,CAAC1F,EAAQ,CAAA,EAAI,EAGxCwD,GAAQpP,EAAG4L,EAAQ,EAAG,EAAGqC,KAKzBxQ,EAAQiU,IAAI,EAAI,CAACtD,IACjBgB,GAAQpP,EAAGyN,EAAO,EAAG,EAAGQ,IAE5BmB,GAAQpP,EAAGE,EAAG,EAAG5C,AAAgB,WAAhBA,EAAO0B,IAAI,CAAiB8M,GAAK,EAAK,EAAGmC,IAS1DT,EAAQxN,EACRyN,EAAQvN,EACRyN,EAAY,CAAA,EACZS,GAAa,CAAA,GA/HF,YAZPqB,KACO,YAnBA,YA7BPA,KACO,WA0Lf,MAYI7F,EAASO,KAAK,CAACM,eAAe,EAC9BkH,QAAQC,GAAG,CAAC,kBAAmBlE,GAEnC,IAAImE,GAAsB,SAAU7B,CAAK,CACrC8B,CAAO,EAQP,GAPSlI,EAASM,kBAAkB,GAC5ByB,EAAK2D,eAAe,CAAG,CAAA,EAC3BU,EAAMhQ,CAAC,CAAG/B,EAAMqT,QAAQ,CAACtB,EAAMhQ,CAAC,CAAE,CAAA,GAClCgQ,EAAM9P,CAAC,CAAGW,EAAMyQ,QAAQ,CAACtB,EAAM9P,CAAC,CAAE,CAAA,IAIlC4R,EAAS,CACT5P,EAAMxC,IAAI,CAAG,CAACsQ,EAAMhQ,CAAC,CAAEgQ,EAAM9P,CAAC,CAAE,EAAG,EAAE,CAAC6R,MAAM,CAAC7P,EAAMxC,IAAI,EACvD,MACJ,CACA0P,GAAQY,EAAMhQ,CAAC,CAAEgQ,EAAM9P,CAAC,CAAE,EAAG,EACjC,CACI,EAACyN,GACDX,AAAiB,CAAA,IAAjBA,GACA1P,AAAoB,eAApBA,EAAOoL,QAAQ,GACXwE,EAAYlN,CAAC,CAAGV,OAAOC,SAAS,EAEhCsS,GAAoB3E,EAAa,CAAA,GAEjCC,EAAanN,CAAC,CAAG,CAACV,OAAOC,SAAS,EAClCsS,GAAoB1E,IAG5BoC,KACJ,EAQAhG,EAAYlN,SAAS,CAAC2V,UAAU,CAAG,SAAUlH,CAAC,EAC1C,IAAIpB,EAAa,IAAI,CAACA,UAAU,CAC5BpM,EAAS,IAAI,CAACA,MAAM,CACpBsM,EAAW,IAAI,CAACA,QAAQ,AACxBtM,CAAAA,EAAOM,MAAM,CAAG,GACZN,CAAM,CAACA,EAAOM,MAAM,CAAG,EAAE,CAAC2T,UAAU,EACpCjU,CAAAA,CAAM,CAACA,EAAOM,MAAM,CAAG,EAAE,CAACqU,QAAQ,CAAGvI,EAAW9L,MAAM,AAAD,EAGzDgM,EAASO,KAAK,CAACE,oBAAoB,EACnCsH,QAAQO,IAAI,CAAC,YAAcpH,EAAE9L,IAAI,CAAG,WAExC,IAAI7C,EAAM,CACFqT,SAAU,EAAE,CACZ2C,WAAYzI,EAAW9L,MAAM,CAI7BuR,UAAW,EAAE,CACb7R,OAAQwN,EACR7F,KAAM3F,OAAOC,SAAS,CACtB2F,KAAM,CAAC5F,OAAOC,SAAS,CACvBgS,WAAYzG,EAAAA,EAAErN,OAAO,CAAC2U,MAAM,EACxBtH,AAA6B,CAAA,IAA7BA,EAAErN,OAAO,CAAC2U,MAAM,CAACC,OAAO,CAE5BC,YAAa,CAAA,EACb5J,SAAUpH,CAAiB,CAACwJ,EAAE9L,IAAI,CAAC,EAAI,YAC3C,CACA8L,CAAAA,EAAEhQ,KAAK,EAAIwC,EAAOM,MAAM,CACxBN,EAAOkG,IAAI,CAACrH,GAGZmB,CAAM,CAACwN,EAAEhQ,KAAK,CAAC,CAAGqB,EAGtB,IAAI,CAACuP,cAAc,CAACZ,EAAG3O,GACnByN,EAASO,KAAK,CAACE,oBAAoB,EACnCsH,QAAQY,OAAO,CAAC,YAAczH,EAAE9L,IAAI,CAAG,UAE/C,EAOAuK,EAAYlN,SAAS,CAACmW,KAAK,CAAG,WAC1B,IAAInH,EAAU,IAAI,CAACA,OAAO,AAC1B,CAAA,IAAI,CAAC3L,IAAI,CAAG,EAAE,CACd,IAAI,CAACgK,UAAU,CAAG,EAAE,CACpB,IAAI,CAACpM,MAAM,CAAG,EAAE,CACZ+N,GACAA,EAAQhH,OAAO,EAEvB,EAOAkF,EAAYlN,SAAS,CAACoW,QAAQ,CAAG,SAAUvU,CAAI,EAC3C,IAAI2F,EAAS,IAAI,CAACA,MAAM,CACxB,GAAKA,GAGL,IAAImB,EAAa,IAAI,CAACiG,aAAa,GACnCpH,EAAO4B,UAAU,CAAC,aAAcvH,EAAKwU,MAAM,CAAG1N,GAC9CnB,EAAO4B,UAAU,CAAC,WAAYvH,EAAKC,GAAG,EACtC0F,EAAO4B,UAAU,CAAC,cAAevH,EAAKyU,eAAe,CAAG3N,GACxDnB,EAAO4B,UAAU,CAAC,kBAAmBvH,EAAK0U,UAAU,EACpD/O,EAAO4B,UAAU,CAAC,WAAYvH,EAAKiD,GAAG,CAAG6D,GACzCnB,EAAO4B,UAAU,CAAC,WAAYvH,EAAK8C,GAAG,CAAGgE,GACzCnB,EAAO4B,UAAU,CAAC,gBAAkB,CAACvH,EAAK2U,KAAK,EAC/ChP,EAAO4B,UAAU,CAAC,aAAe,CAAC,CAACvH,EAAKuT,WAAW,EACnD5N,EAAO4B,UAAU,CAAC,gBAAkB,CAAC,CAACvH,EAAK4U,QAAQ,EACvD,EAOAvJ,EAAYlN,SAAS,CAAC0W,QAAQ,CAAG,SAAU7U,CAAI,EAC3C,IAAI2F,EAAS,IAAI,CAACA,MAAM,CACxB,GAAKA,GAGL,IAAImB,EAAa,IAAI,CAACiG,aAAa,GACnCpH,EAAO4B,UAAU,CAAC,aAAcvH,EAAKwU,MAAM,CAAG1N,GAC9CnB,EAAO4B,UAAU,CAAC,WAAYvH,EAAKC,GAAG,EACtC0F,EAAO4B,UAAU,CAAC,cAAevH,EAAKyU,eAAe,CAAG3N,GACxDnB,EAAO4B,UAAU,CAAC,kBAAmBvH,EAAK0U,UAAU,EACpD/O,EAAO4B,UAAU,CAAC,WAAYvH,EAAKiD,GAAG,CAAG6D,GACzCnB,EAAO4B,UAAU,CAAC,WAAYvH,EAAK8C,GAAG,CAAGgE,GACzCnB,EAAO4B,UAAU,CAAC,gBAAkB,CAACvH,EAAK2U,KAAK,EAC/ChP,EAAO4B,UAAU,CAAC,aAAe,CAAC,CAACvH,EAAKuT,WAAW,EACnD5N,EAAO4B,UAAU,CAAC,gBAAkB,CAAC,CAACvH,EAAK4U,QAAQ,EACvD,EASAvJ,EAAYlN,SAAS,CAAC2W,YAAY,CAAG,SAAUC,CAAG,CAAEC,CAAW,EAC3D,IAAIrP,EAAS,IAAI,CAACA,MAAM,CACnBA,IAGLA,EAAO4B,UAAU,CAAC,eAAgBwN,GAClCpP,EAAO4B,UAAU,CAAC,sBAAuByN,GAC7C,EAMA3J,EAAYlN,SAAS,CAAC8W,WAAW,CAAG,SAAU/V,CAAK,EAC/C,IAAI8E,EAAQ,IAAI,CACZP,EAAK,IAAI,CAACA,EAAE,CACZiI,EAAW,IAAI,CAACA,QAAQ,CACxB/F,EAAS,IAAI,CAACA,MAAM,CACpBwH,EAAU,IAAI,CAACA,OAAO,CACtBrG,EAAa,IAAI,CAACiG,aAAa,GACnC,IAAI7N,EAKA,MAAO,CAAA,CAJP,CAAA,IAAI,CAACgD,KAAK,CAAGhD,EAAMgW,UAAU,CAAGpO,EAChC,IAAI,CAAC1E,MAAM,CAAGlD,EAAMiW,WAAW,CAAGrO,EAKtC,IAAI1E,EAAS,IAAI,CAACA,MAAM,CACpBF,EAAQ,IAAI,CAACA,KAAK,CACtB,GAAI,CAACuB,GAAM,CAACkC,GAAU,CAACzD,GAAS,CAACE,EAC7B,MAAO,CAAA,CAEPsJ,CAAAA,EAASO,KAAK,CAACC,aAAa,EAC5BuH,QAAQO,IAAI,CAAC,gBAEjBvQ,EAAG2R,MAAM,CAAClT,KAAK,CAAGA,EAClBuB,EAAG2R,MAAM,CAAChT,MAAM,CAAGA,EACnBuD,EAAO9B,IAAI,GACXJ,EAAG4R,QAAQ,CAAC,EAAG,EAAGnT,EAAOE,GACzBuD,EAAOqC,UAAU,CAACqD,EAAYmB,WAAW,CAACtK,EAAOE,IAC7CsJ,EAASE,SAAS,CAAG,GAAK,CAAC,AAACnN,IAA+E6W,IAAI,EAC/G7R,EAAGmI,SAAS,CAACF,EAASE,SAAS,EAE/BuB,IACAA,EAAQ3D,KAAK,CAAC,IAAI,CAAChI,IAAI,CAAE,kBAAmB,GAC5C2L,EAAQtJ,IAAI,IAEhB8B,EAAOoC,WAAW,CAAC7I,EAAMoD,QAAQ,EAEjC,IAAI,CAAClD,MAAM,CAACR,OAAO,CAAC,SAAUgO,CAAC,CAAE2I,CAAE,EAI/B,IAHIzV,EACAY,EACA2R,EAkBAmD,EACAC,EACA5J,EAnBAtM,EAAUqN,EAAExN,MAAM,CAACG,OAAO,CAC1BmW,EAAenW,EAAQ2U,MAAM,CAC7BtI,EAAa,AAA6B,KAAA,IAAtBrM,EAAQqM,SAAS,CACjCrM,EAAQqM,SAAS,CACjB,EACJpM,EAAYD,EAAQC,SAAS,CAC7BmW,EAAe9K,EAASrL,GACxBoW,EAAUhJ,EAAExN,MAAM,CAACuD,KAAK,CAACkT,YAAY,CAACrW,GAEtC4U,EAAcnJ,EAAiB1L,EAAQ2U,MAAM,CAAG3U,EAAQ2U,MAAM,CAACC,OAAO,CAAG,KACzEvH,EAAAA,EAAExN,MAAM,CAACW,KAAK,CAAC+V,QAAQ,EAAU,KACjClJ,EAAExN,MAAM,CAAC2R,mBAAmB,CACxB,EAAK,CAAA,AAACxR,CAAAA,EAAQ2U,MAAM,CAChB3U,EAAQ2U,MAAM,CAAC6B,MAAM,CACrB,EAAC,GAAM,EAAC,GAChBC,EAAehS,EAAMyH,cAAc,CAAC,AAACiK,GAAgBA,EAAaO,MAAM,EACpErJ,EAAExN,MAAM,CAAC6W,MAAM,CAAC,EAAIjS,EAAMyH,cAAc,CAACyK,MAAM,CAInDvI,EAAS,EAAE,CACf,GAAIf,AAAsB,IAAtBA,EAAE0E,QAAQ,CAAC5R,MAAM,EACjBkN,EAAE0E,QAAQ,CAAC,EAAE,CAAChH,IAAI,GAAKsC,EAAE0E,QAAQ,CAAC,EAAE,CAAC/G,EAAE,CA0F3C,CAAA,GAvFIyL,EAAaG,OAAO,GACpB1S,EAAG2S,WAAW,CAAC3S,EAAG4S,UAAU,CAAEL,EAAaM,MAAM,EACjD3Q,EAAO4C,UAAU,CAACyN,EAAaM,MAAM,GAErCpX,EAAMuT,UAAU,CACZ7F,EAAExN,MAAM,CAACmX,WAAW,GAAM,CAAA,AAAgC,OAA/BzW,CAAAA,EAAK8M,EAAExN,MAAM,CAACF,KAAK,CAACG,KAAK,AAAD,GAAeS,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGyW,WAAW,AAAD,GAExG,OAAO3J,EAAExN,MAAM,CAACmX,WAAW,CAC3B3J,EAAExN,MAAM,CAACmX,WAAW,CAAG3J,EAAExN,MAAM,CAACoX,SAAS,CAAC,cAAe,UAAW,UAAW,EAAGtX,EAAMuX,WAAW,EAAEC,QAAQ,CAAC,sBAC9G7K,EAAYe,EAAExN,MAAM,CAACmX,WAAW,CAACI,QAAQ,CAAC,QAC1C/J,EAAExN,MAAM,CAACmX,WAAW,CAACpQ,OAAO,GAC5ByG,EAAExN,MAAM,CAACmX,WAAW,CAAG,AAAgC,OAA/B7V,CAAAA,EAAKkM,EAAExN,MAAM,CAACF,KAAK,CAACG,KAAK,AAAD,GAAeqB,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG6V,WAAW,EAGtG1K,EAAY,AAAgC,OAA/BwG,CAAAA,EAAKzF,EAAExN,MAAM,CAACmX,WAAW,AAAD,GAAelE,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGsE,QAAQ,CAAC,SAI7F9K,EACI,AAAgB,WAAfe,EAAEpC,QAAQ,EACPoC,EAAExN,MAAM,CAACyR,YAAY,EACrBjE,EAAExN,MAAM,CAACyR,YAAY,GAAGC,IAAI,EAC5BlE,EAAExN,MAAM,CAACuI,KAAK,CAClBpI,EAAQqX,YAAY,EACpB/K,CAAAA,EAAYe,EAAExN,MAAM,CAACF,KAAK,CAACK,OAAO,CAACsX,MAAM,CAACtB,EAAG,AAAD,GAGhD3I,EAAExN,MAAM,CAAC0X,WAAW,EAAIvX,EAAQuX,WAAW,EAC3CjL,CAAAA,EAAY,GAAK1I,CAAAA,GAAoG,EAAG0I,GAAWkL,UAAU,CAAC9L,EAAiB1L,EAAQuX,WAAW,CAAE,IAAM9Y,GAAG,EAAC,EAElM2P,EAAShG,EAAMkE,GAAW8E,IAAI,CACzBjF,EAASI,QAAQ,EAClB6B,CAAAA,CAAM,CAAC,EAAE,CAAG,CAAE,EAGdpO,AAA0B,QAA1BA,EAAQyX,aAAa,EACrBvT,EAAGwT,SAAS,CAACxT,EAAGyT,SAAS,CAAEzT,EAAG0T,GAAG,EACjC1T,EAAG2T,aAAa,CAAC3T,EAAG4T,QAAQ,GAEvB9X,AAA0B,SAA1BA,EAAQyX,aAAa,EAC1BzX,AAA0B,aAA1BA,EAAQyX,aAAa,CACrBvT,EAAGwT,SAAS,CAACxT,EAAG6T,SAAS,CAAE7T,EAAG8T,IAAI,EAE7BhY,AAA0B,WAA1BA,EAAQyX,aAAa,EAC1BvT,EAAGwT,SAAS,CAACxT,EAAG0T,GAAG,CAAE1T,EAAG0T,GAAG,EAC3B1T,EAAG2T,aAAa,CAAC3T,EAAG+T,QAAQ,GAK5B/T,EAAGgU,iBAAiB,CAAChU,EAAGyT,SAAS,CAAEzT,EAAGiU,mBAAmB,CAAEjU,EAAG0T,GAAG,CAAE1T,EAAGiU,mBAAmB,EAE7F/R,EAAOc,KAAK,GAERmG,EAAEqE,SAAS,CAACvR,MAAM,CAAG,GACrBiG,EAAO4B,UAAU,CAAC,WAAY,GAE9BkO,AADAA,CAAAA,EAAU,IAhhCgC7M,EAghCNnF,EAAIkC,EAAM,EACtC6D,KAAK,CAIbmO,MAAM/K,EAAE0E,QAAQ,CAAC,EAAE,CAAChH,IAAI,EAAEuJ,MAAM,CAACjH,EAAEqE,SAAS,EAAG,SAAU,GACzDwE,EAAQ5R,IAAI,KAKZ8B,EAAO4B,UAAU,CAAC,WAAY,GAG9B9D,EAAGmU,wBAAwB,CAACnU,EAAGyG,iBAAiB,CAACvE,EAAOW,UAAU,GAAI,YAG1EX,EAAO+B,QAAQ,CAACiG,GAChB3J,EAAMuQ,QAAQ,CAAC3H,EAAExN,MAAM,CAACW,KAAK,EAC7BiE,EAAM6Q,QAAQ,CAACjI,EAAExN,MAAM,CAACuD,KAAK,EAC7BqB,EAAM8Q,YAAY,CAACa,EA7FOC,GA8FP,WAAfhJ,EAAEpC,QAAQ,EACV7E,EAAOwC,YAAY,CAAC8C,AAAiE,EAAjEA,EAAiB1L,EAAQ2U,MAAM,EAAI3U,EAAQ2U,MAAM,CAAC6B,MAAM,CAAE,IAAWjP,GAI7FnB,EAAO2C,kBAAkB,CAACsE,EAAEwE,eAAe,EACrB,WAAlBxE,EAAExN,MAAM,CAAC0B,IAAI,EACb6E,EAAOgB,iBAAiB,CAACiG,EAAExN,MAAM,CAAEwN,EAAE7F,IAAI,CAAE6F,EAAE5F,IAAI,CAAEF,GAEvDnB,EAAOkC,eAAe,CAACsD,CAAQ,CAACyB,EAAExN,MAAM,CAAC0B,IAAI,CAAC,EAAI,CAAA,GAC7CqM,GAKL,GAAIvB,EAAY,GAAKgB,AAAe,eAAfA,EAAEpC,QAAQ,CAC3B,IAAKgL,EAAS,EAAGA,EAAS5I,EAAE0E,QAAQ,CAAC5R,MAAM,CAAE8V,IACzCrI,EAAQ9C,MAAM,CAACuC,EAAE0E,QAAQ,CAACkE,EAAO,CAAClL,IAAI,CAAEsC,EAAE0E,QAAQ,CAACkE,EAAO,CAACjL,EAAE,CAAEqC,EAAEpC,QAAQ,EAGjF,GAAIoC,EAAEyG,UAAU,EAAIe,EAGhB,IAFAzO,EAAOwC,YAAY,CAAC8C,AAA+D,EAA/DA,EAAiB1L,EAAQ2U,MAAM,EAAI3U,EAAQ2U,MAAM,CAAC6B,MAAM,CAAE,GAASjP,GACvFnB,EAAOkC,eAAe,CAAC,CAAA,GAClB2N,EAAS,EAAGA,EAAS5I,EAAE0E,QAAQ,CAAC5R,MAAM,CAAE8V,IACzCrI,EAAQ9C,MAAM,CAACuC,EAAE0E,QAAQ,CAACkE,EAAO,CAAClL,IAAI,CAAEsC,EAAE0E,QAAQ,CAACkE,EAAO,CAACjL,EAAE,CAAE,UAZvE,CAeJ,GACImB,EAASO,KAAK,CAACC,aAAa,EAC5BuH,QAAQY,OAAO,CAAC,gBAEhB,IAAI,CAAC/I,kBAAkB,EACvB,IAAI,CAACA,kBAAkB,CAAC,IAAI,EAEhC,IAAI,CAACgJ,KAAK,EACd,EAKAjJ,EAAYlN,SAAS,CAACkM,MAAM,CAAG,SAAUnL,CAAK,EAC1C,IAAI8E,EAAQ,IAAI,CAEhB,GADA,IAAI,CAACqJ,KAAK,GACNnO,EAAM2Y,QAAQ,CAACC,SAAS,CACxB,OAAO,IAAI,CAAC7C,WAAW,CAAC/V,EAExB,CAAA,IAAI,CAACqM,QAAQ,CACb,IAAI,CAAC0J,WAAW,CAAC/V,GAGjB6Y,WAAW,WACP/T,EAAMqG,MAAM,CAACnL,EACjB,EAAG,EAEX,EAMAmM,EAAYlN,SAAS,CAAC6Z,OAAO,CAAG,SAAU9V,CAAK,CAAEE,CAAM,EACnD,IAAIuD,EAAS,IAAI,CAACA,MAAM,CAEnBA,GAAW,CAAA,IAAI,CAACzD,KAAK,GAAKA,GAAS,IAAI,CAACE,MAAM,GAAKA,CAAK,IAG7D,IAAI,CAACF,KAAK,CAAGA,EACb,IAAI,CAACE,MAAM,CAAGA,EACduD,EAAO9B,IAAI,GACX8B,EAAOqC,UAAU,CAACqD,EAAYmB,WAAW,CAACtK,EAAOE,IACrD,EAKAiJ,EAAYlN,SAAS,CAAC8Z,IAAI,CAAG,SAAU7C,CAAM,CAAE8C,CAAO,EAClD,IAAIlU,EAAQ,IAAI,CACZ0H,EAAW,IAAI,CAACA,QAAQ,CAE5B,GADA,IAAI,CAACH,QAAQ,CAAG,CAAA,EACZ,CAAC6J,EACD,MAAO,CAAA,CAEP1J,CAAAA,EAASO,KAAK,CAACG,SAAS,EACxBqH,QAAQO,IAAI,CAAC,YAEjB,IAAK,IAAItE,EAAI,EAAGA,EAAItE,EAAS1L,MAAM,GAC/B,IAAI,CAAC+D,EAAE,CAAG2R,EAAO+C,UAAU,CAAC/M,CAAQ,CAACsE,EAAE,CAAE,CAEzC,IACI,IAAI,CAACjM,EAAE,EAJsB,EAAEiM,GAQvC,IAAIjM,EAAK,IAAI,CAACA,EAAE,CAChB,IAAIA,EAMA,MAAO,CAAA,EALFyU,GACD,IAAI,CAAC5D,KAAK,GAMlB7Q,EAAG2U,MAAM,CAAC3U,EAAG4U,KAAK,EAElB5U,EAAGwT,SAAS,CAACxT,EAAGyT,SAAS,CAAEzT,EAAGiU,mBAAmB,EACjDjU,EAAG6U,OAAO,CAAC7U,EAAG8U,UAAU,EAExB9U,EAAG+U,SAAS,CAAC/U,EAAGgV,IAAI,EACpB,IAAI9S,EAAS,IAAI,CAACA,MAAM,CAAG,IAn1CiBnC,EAm1CGC,GAC/C,GAAI,CAACkC,EAED,MAAO,CAAA,CAEX,CAAA,IAAI,CAACwH,OAAO,CAAG,IAtpCmCvE,EAspCTnF,EAAIkC,GAC7C,IAAI+S,EAAgB,SAAUjQ,CAAI,CAC9BkQ,CAAE,EACE,IAAIC,EAAQ,CACRzC,QAAS,CAAA,EACT3N,QAASmC,EAAIkO,aAAa,CAAC,UAC3BvC,OAAQ7S,EAAGiV,aAAa,EAC5B,EACJI,EAAMF,EAAMpQ,OAAO,CAAC2P,UAAU,CAAC,KAC/BnU,CAAAA,EAAMyH,cAAc,CAAChD,EAAK,CAAGmQ,EAC7BA,EAAMpQ,OAAO,CAACtG,KAAK,CAAG,IACtB0W,EAAMpQ,OAAO,CAACpG,MAAM,CAAG,IACvB0W,EAAIC,wBAAwB,CAAG,CAAA,EAC/BD,EAAIE,2BAA2B,CAAG,CAAA,EAClCF,EAAIG,uBAAuB,CAAG,CAAA,EAC9BH,EAAII,qBAAqB,CAAG,CAAA,EAC5BJ,EAAIK,WAAW,CAAG,yBAClBL,EAAIM,SAAS,CAAG,OAChBT,EAAGG,GACH,GAAI,CACArV,EAAG4V,aAAa,CAAC5V,EAAG6V,QAAQ,EAC5B7V,EAAG2S,WAAW,CAAC3S,EAAG4S,UAAU,CAAEuC,EAAMtC,MAAM,EAE1C7S,EAAG8V,UAAU,CAAC9V,EAAG4S,UAAU,CAAE,EAAG5S,EAAG+V,IAAI,CAAE/V,EAAG+V,IAAI,CAAE/V,EAAGgW,aAAa,CAAEb,EAAMpQ,OAAO,EACjF/E,EAAGiW,aAAa,CAACjW,EAAG4S,UAAU,CAAE5S,EAAGkW,cAAc,CAAElW,EAAGmW,aAAa,EACnEnW,EAAGiW,aAAa,CAACjW,EAAG4S,UAAU,CAAE5S,EAAGoW,cAAc,CAAEpW,EAAGmW,aAAa,EACnEnW,EAAGiW,aAAa,CAACjW,EAAG4S,UAAU,CAAE5S,EAAGqW,kBAAkB,CAAErW,EAAGsW,MAAM,EAChEtW,EAAGiW,aAAa,CAACjW,EAAG4S,UAAU,CAAE5S,EAAGuW,kBAAkB,CAAEvW,EAAGsW,MAAM,EAEhEtW,EAAG2S,WAAW,CAAC3S,EAAG4S,UAAU,CAAE,MAC9BuC,EAAMzC,OAAO,CAAG,CAAA,CACpB,CACA,MAAO8D,EAAG,CAEV,CACJ,EA4CA,OA1CAvB,EAAc,SAAU,SAAUI,CAAG,EACjCA,EAAIoB,SAAS,GACbpB,EAAIqB,GAAG,CAAC,IAAK,IAAK,IAAK,EAAG,EAAInX,KAAKoX,EAAE,EACrCtB,EAAInG,MAAM,GACVmG,EAAIhI,IAAI,EACZ,GAEA4H,EAAc,SAAU,SAAUI,CAAG,EACjCA,EAAIuB,QAAQ,CAAC,EAAG,EAAG,IAAK,IAC5B,GAEA3B,EAAc,UAAW,SAAUI,CAAG,EAClCA,EAAIoB,SAAS,GACbpB,EAAIwB,MAAM,CAAC,IAAK,GAChBxB,EAAIyB,MAAM,CAAC,IAAK,KAChBzB,EAAIyB,MAAM,CAAC,IAAK,KAChBzB,EAAIyB,MAAM,CAAC,EAAG,KACdzB,EAAIyB,MAAM,CAAC,IAAK,GAChBzB,EAAIhI,IAAI,EACZ,GAEA4H,EAAc,WAAY,SAAUI,CAAG,EACnCA,EAAIoB,SAAS,GACbpB,EAAIwB,MAAM,CAAC,EAAG,KACdxB,EAAIyB,MAAM,CAAC,IAAK,GAChBzB,EAAIyB,MAAM,CAAC,IAAK,KAChBzB,EAAIyB,MAAM,CAAC,EAAG,KACdzB,EAAIhI,IAAI,EACZ,GAEA4H,EAAc,gBAAiB,SAAUI,CAAG,EACxCA,EAAIoB,SAAS,GACbpB,EAAIwB,MAAM,CAAC,EAAG,GACdxB,EAAIyB,MAAM,CAAC,IAAK,KAChBzB,EAAIyB,MAAM,CAAC,IAAK,GAChBzB,EAAIyB,MAAM,CAAC,EAAG,GACdzB,EAAIhI,IAAI,EACZ,GACA,IAAI,CAACvF,QAAQ,CAAG,CAAA,EACZG,EAASO,KAAK,CAACG,SAAS,EACxBqH,QAAQY,OAAO,CAAC,YAEb,CAAA,CACX,EAKAhJ,EAAYlN,SAAS,CAACgI,OAAO,CAAG,WAC5B,IAAI1C,EAAK,IAAI,CAACA,EAAE,CACZkC,EAAS,IAAI,CAACA,MAAM,CACpBwH,EAAU,IAAI,CAACA,OAAO,CAC1B,IAAI,CAACmH,KAAK,GACNnH,GACAA,EAAQhH,OAAO,GAEfR,GACAA,EAAOQ,OAAO,GAEd1C,IACAuH,EAAW,IAAI,CAACS,cAAc,CAAE,SAAUjD,CAAO,EACzCA,EAAQ8N,MAAM,EACd7S,EAAG+W,aAAa,CAAChS,EAAQ8N,MAAM,CAEvC,GACA7S,EAAG2R,MAAM,CAAClT,KAAK,CAAG,EAClBuB,EAAG2R,MAAM,CAAChT,MAAM,CAAG,EAE3B,EACOiJ,CACX,IAqBIoP,EAA0D,SAAUlQ,CAAE,CAAED,CAAI,CAAEoQ,CAAI,EAClF,GAAIA,GAAQxZ,AAAqB,GAArBA,UAAUxB,MAAM,CAAQ,IAAK,IAA4Bib,EAAxBjL,EAAI,EAAGkL,EAAItQ,EAAK5K,MAAM,CAAMgQ,EAAIkL,EAAGlL,KACxEiL,GAAQjL,KAAKpF,IACRqQ,GAAIA,CAAAA,EAAKhD,MAAMxZ,SAAS,CAAC6U,KAAK,CAAC3U,IAAI,CAACiM,EAAM,EAAGoF,EAAC,EACnDiL,CAAE,CAACjL,EAAE,CAAGpF,CAAI,CAACoF,EAAE,EAGvB,OAAOnF,EAAGsJ,MAAM,CAAC8G,GAAMhD,MAAMxZ,SAAS,CAAC6U,KAAK,CAAC3U,IAAI,CAACiM,GACtD,CA2CI3N,EArCOA,EA2FRA,GAAgBA,CAAAA,EAAc,CAAC,CAAA,GAtDlBke,SAAS,CAPrB,SAAmBC,CAAM,CAAEpb,CAAM,CAAEqb,CAAU,SACzC,AAAIpD,MAAMqD,OAAO,CAACF,IACdA,EAAOpb,MAAM,CAAGA,EACTob,GAEJA,CAAM,CAACC,EAAa,WAAa,QAAQ,CAAC,EAAGrb,EACxD,EAsDA/C,EAAYse,MAAM,CAzBlB,SAAgBH,CAAM,CAAEI,CAAK,CAAEC,CAAW,CAAEC,CAAiB,CAAEC,CAAK,EAEhE,GADc,KAAK,IAAfA,GAAoBA,CAAAA,EAAQ,EAAE,AAAD,EAC7B1D,MAAMqD,OAAO,CAACF,GAId,OAHKnD,MAAMqD,OAAO,CAACK,IACfA,CAAAA,EAAQ1D,MAAMrN,IAAI,CAAC+Q,EAAK,EAErB,CACHC,QAASR,EAAOG,MAAM,CAACM,KAAK,CAACT,EAAQL,EAAc,CAACS,EAAOC,EAAY,CAAEE,EAAO,CAAA,IAChFG,MAAOV,CACX,EAEJ,IAAIW,EAAc5d,OAAO6d,cAAc,CAACZ,GAC/Ba,WAAW,CAChBL,EAAUR,CAAM,CAACM,EAAoB,WAAa,QAAQ,CAACF,EAC3DA,EAAQC,GAERS,EAAS,IAAIH,EADDX,EAAOpb,MAAM,CAAGyb,EAAcE,EAAM3b,MAAM,EAK1D,OAHAkc,EAAOC,GAAG,CAACf,EAAOgB,QAAQ,CAAC,EAAGZ,GAAQ,GACtCU,EAAOC,GAAG,CAACR,EAAOH,GAClBU,EAAOC,GAAG,CAACf,EAAOgB,QAAQ,CAACZ,EAAQC,GAAcD,EAAQG,EAAM3b,MAAM,EAC9D,CACH4b,QAASA,EACTE,MAAOI,CACX,CACJ,EAQyB,IAAIG,EAAoBpf,EAmBjDke,EAAYkB,EAAiBlB,SAAS,CAAEI,EAASc,EAAiBd,MAAM,CAExEe,EAAY,AAACvd,IAA+Eud,SAAS,CAAEC,EAA2B,AAACxd,IAA+EuM,UAAU,CAAEkR,EAAY,AAACzd,IAA+Eyd,SAAS,CAiBnUC,EAA+B,WAiB/B,SAASA,EAAc5c,CAAO,EACV,KAAK,IAAjBA,GAAsBA,CAAAA,EAAU,CAAC,CAAA,EACrC,IAAIyE,EAAQ,IAAI,AAOhB,CAAA,IAAI,CAACoY,MAAM,CAAG,CAAC7c,EAAQ8c,EAAE,CACzB,IAAI,CAACC,OAAO,CAAG,CAAC,EAOhB,IAAI,CAACD,EAAE,CAAI9c,EAAQ8c,EAAE,EAAIH,IACzB,IAAI,CAACK,QAAQ,CAAG,IAAI,CACpB,IAAI,CAACC,QAAQ,CAAG,EAChB,IAAI,CAACC,UAAU,CAAGP,IAClB,IAAIM,EAAW,EACfP,EAAyB1c,EAAQ+c,OAAO,EAAI,CAAC,EAAG,SAAUxB,CAAM,CAAE4B,CAAU,EACxE1Y,EAAMsY,OAAO,CAACI,EAAW,CAAG5B,EAAO9H,KAAK,GACxCwJ,EAAWxZ,KAAK5C,GAAG,CAACoc,EAAU1B,EAAOpb,MAAM,CAC/C,GACA,IAAI,CAACid,aAAa,CAACH,EACvB,CAyMA,OA5LAL,EAAche,SAAS,CAACwe,aAAa,CAAG,SAAUH,CAAQ,EACtD,IAAIxY,EAAQ,IAAI,AAChB,CAAA,IAAI,CAACwY,QAAQ,CAAGA,EAChBP,EAAyB,IAAI,CAACK,OAAO,CAAE,SAAUxB,CAAM,CAAE4B,CAAU,EAC3D5B,EAAOpb,MAAM,GAAK8c,GAClBxY,CAAAA,EAAMsY,OAAO,CAACI,EAAW,CAAG7B,EAAUC,EAAQ0B,EAAQ,CAE9D,EACJ,EAeAL,EAAche,SAAS,CAACye,UAAU,CAAG,SAAUC,CAAQ,CAAEL,CAAQ,EAC7D,IAAIxY,EAAQ,IAAI,CAEhB,GADiB,KAAK,IAAlBwY,GAAuBA,CAAAA,EAAW,CAAA,EAClCA,EAAW,GAAKK,EAAW,IAAI,CAACL,QAAQ,CAAE,CAC1C,IAAIM,EAAW,EACfb,EAAyB,IAAI,CAACK,OAAO,CAAE,SAAUxB,CAAM,CAAE4B,CAAU,EAC/D1Y,EAAMsY,OAAO,CAACI,EAAW,CACrBzB,EAAOH,EAAQ+B,EAAUL,GAAUhB,KAAK,CAC5CsB,EAAWhC,EAAOpb,MAAM,AAC5B,GACA,IAAI,CAAC8c,QAAQ,CAAGM,CACpB,CACAd,EAAU,IAAI,CAAE,kBAAmB,CAAEa,SAAUA,EAAUL,SAAUA,CAAS,GAC5E,IAAI,CAACC,UAAU,CAAGP,GACtB,EAWAC,EAAche,SAAS,CAACoD,SAAS,CAAG,SAAUmb,CAAU,CAExDK,CAAW,EACP,OAAO,IAAI,CAACT,OAAO,CAACI,EAAW,AACnC,EAYAP,EAAche,SAAS,CAAC6e,UAAU,CAAG,SAAUC,CAAW,CAE1DF,CAAW,EACP,IAAI/Y,EAAQ,IAAI,CAChB,MAAO,AAACiZ,CAAAA,GAAepf,OAAOqf,IAAI,CAAC,IAAI,CAACZ,OAAO,CAAA,EAAGa,MAAM,CAAC,SAAUb,CAAO,CAAEI,CAAU,EAElF,OADAJ,CAAO,CAACI,EAAW,CAAG1Y,EAAMsY,OAAO,CAACI,EAAW,CACxCJ,CACX,EAAG,CAAC,EACR,EAaAH,EAAche,SAAS,CAACif,MAAM,CAAG,SAAUP,CAAQ,CAAEI,CAAW,EAC5D,IAAIjZ,EAAQ,IAAI,CAChB,MAAO,AAACiZ,CAAAA,GAAepf,OAAOqf,IAAI,CAAC,IAAI,CAACZ,OAAO,CAAA,EAAGe,GAAG,CAAC,SAAU1f,CAAG,EAAI,IAAImC,EAAI,OAAO,AAA8B,OAA7BA,CAAAA,EAAKkE,EAAMsY,OAAO,CAAC3e,EAAI,AAAD,GAAemC,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,CAAE,CAAC+c,EAAS,AAAE,EACvK,EAmBAV,EAAche,SAAS,CAACmf,SAAS,CAAG,SAAUZ,CAAU,CAAE5B,CAAM,CAAE+B,CAAQ,CAAEU,CAAW,EACnF,IAAIzd,CACW,MAAK,IAAhBgb,GAAqBA,CAAAA,EAAS,EAAE,AAAD,EAClB,KAAK,IAAlB+B,GAAuBA,CAAAA,EAAW,CAAA,EACtC,IAAI,CAACW,UAAU,CAAE1d,CAAAA,AAASA,CAATA,EAAK,CAAC,CAAA,CAAK,CAAC4c,EAAW,CAAG5B,EAAQhb,CAAC,EAAI+c,EAAUU,EACtE,EAmBApB,EAAche,SAAS,CAACqf,UAAU,CAAG,SAAUlB,CAAO,CAAEO,CAAQ,CAAEU,CAAW,EACzE,IAAIvZ,EAAQ,IAAI,CACZwY,EAAW,IAAI,CAACA,QAAQ,CAC5BP,EAAyBK,EAAS,SAAUxB,CAAM,CAAE4B,CAAU,EAC1D1Y,EAAMsY,OAAO,CAACI,EAAW,CAAG5B,EAAO9H,KAAK,GACxCwJ,EAAW1B,EAAOpb,MAAM,AAC5B,GACA,IAAI,CAACid,aAAa,CAACH,GACbe,CAAAA,MAAAA,EAAiD,KAAK,EAAIA,EAAYE,MAAM,AAAD,IAC7EzB,EAAU,IAAI,CAAE,mBAChB,IAAI,CAACS,UAAU,CAAGP,IAE1B,EAoBAC,EAAche,SAAS,CAACuf,MAAM,CAAG,SAAUC,CAAG,CAAEd,CAAQ,CAAEe,CAAM,CAAEL,CAAW,EACxD,KAAK,IAAlBV,GAAuBA,CAAAA,EAAW,IAAI,CAACL,QAAQ,AAAD,EAClD,IAAIF,EAAU,IAAI,CAACA,OAAO,CACtBuB,EAAgBD,EAAS,IAAI,CAACpB,QAAQ,CAAG,EAAIK,EAAW,EAC5DZ,EAAyB0B,EAAK,SAAUG,CAAS,CAAEpB,CAAU,EACzD,IAAI5B,EAASwB,CAAO,CAACI,EAAW,EACxB,AAACa,CAAAA,MAAAA,EAAiD,KAAK,EAAIA,EAAYQ,UAAU,AAAD,IAAO,CAAA,GAAS,AAAIpG,MAAMkG,GAC9G/C,IACI8C,EACA9C,EAASG,EAAOH,EAAQ+B,EAAU,EAAG,CAAA,EAAM,CAACiB,EAAU,EAAEtC,KAAK,CAG7DV,CAAM,CAAC+B,EAAS,CAAGiB,EAEvBxB,CAAO,CAACI,EAAW,CAAG5B,EAE9B,GACI+C,EAAgB,IAAI,CAACrB,QAAQ,EAC7B,IAAI,CAACG,aAAa,CAACkB,GAEjBN,CAAAA,MAAAA,EAAiD,KAAK,EAAIA,EAAYE,MAAM,AAAD,IAC7EzB,EAAU,IAAI,CAAE,gBAChB,IAAI,CAACS,UAAU,CAAGP,IAE1B,EACOC,CACX,IA2DI6B,EAAa,AAACvf,IAA+Euf,UAAU,CAEvGC,EAAuB,AAACxf,IAA+EK,QAAQ,CAAEof,EAAkB,AAACzf,IAA+EkM,GAAG,CAAEwT,EAAO,AAAC1f,IAA+E0f,IAAI,CAAEC,EAAkB,AAAC3f,IAA+EmM,GAAG,CAE1ZyT,EAAuB,AAAC5f,IAA+EM,QAAQ,CAAEuf,EAA0B,AAAC7f,IAA+E6f,uBAAuB,CAAEC,GAAoB,AAAC9f,IAA+E6E,KAAK,CAAEkb,GAAS,AAAC/f,IAA+E+f,MAAM,CAAEC,GAAwB,AAAChgB,IAA+Eud,SAAS,CAAEhB,GAAU,AAACvc,IAA+Euc,OAAO,CAAE0D,GAAuB,AAACjgB,IAA+EoM,QAAQ,CAAE8T,GAAmB,AAAClgB,IAA+EO,IAAI,CAAE4f,GAAyB,AAACngB,IAA+EiD,UAAU,CAAEmd,GAAO,AAACpgB,IAA+EogB,IAAI,CAAEC,GAAU,AAACrgB,IAA+EqgB,OAAO,CAuBvqC,SAASC,GAA4BlH,CAAQ,CAAEzY,CAAM,EACjD,IAAIC,EAAQD,EAAOC,KAAK,CACpBwY,GACAxY,GACAA,EAAMsC,MAAM,EACZtC,EAAM+V,MAAM,EACZ,CAAC4J,AA/7EkB/f,EA+7EgBG,EAAOF,KAAK,GAC/C2Y,EAASzK,6BAA6B,CAAChO,EAE/C,CAUA,SAAS6f,GAAa/f,CAAK,EACvB,OAAOyf,GAAkBzf,GACrBA,EAAMK,OAAO,EACbL,EAAMK,OAAO,CAACF,KAAK,EACnBH,EAAMK,OAAO,CAACF,KAAK,CAAC8U,OAAO,CAAG,CAAA,EACtC,CAqHA,SAAS+K,GAAwBhgB,CAAK,CAAEE,CAAM,EAI1C,IAHIU,EACAY,EACA2R,EACA8M,EAAajgB,EAAMyc,WAAW,CAC9ByD,EAAclgB,EAAMuX,WAAW,EAAIrX,EAAOigB,KAAK,CAE/Cnd,EAAQhD,EAAMgW,UAAU,CACxB9S,EAASlD,EAAMiW,WAAW,CAC1BxT,EAASzC,EACTogB,EAAc,AAAmC,aAAnC,OAAOC,wBACrBC,EAAkB,CAAA,EAClBR,AAllFmB/f,EAklFeC,GAClCyC,EAASzC,GAGTyC,EAASvC,EACTogB,EAAkBC,CAAAA,CAAQ,CAAA,AAAC,CAAA,AAAiC,OAAhC3f,CAAAA,EAAKV,EAAOG,OAAO,CAACmgB,MAAM,AAAD,GAAe5f,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG6f,KAAK,AAAD,GAC/F,CAAA,AAAsF,OAArFtN,CAAAA,EAAK,AAAgC,OAA/B3R,CAAAA,EAAKtB,EAAOG,OAAO,CAACuS,KAAK,AAAD,GAAepR,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGgf,MAAM,AAAD,GAAerN,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGsN,KAAK,AAAD,CAAC,GAExI,IAAItgB,EAAQsC,EAAOtC,KAAK,CAChBsC,EAAOtC,KAAK,EACR,CAAC,EA2Fb,GArFAigB,EAAc,CAAA,EACTziB,GACDA,CAAAA,EAAaqhB,EAAgBrF,aAAa,CAAC,SAAQ,EAEnD,CAACxZ,EAAMsC,MAAM,GACbtC,EAAM+V,MAAM,CAAGvY,EAGXqC,EAAM2Y,QAAQ,CAACC,SAAS,EAAI,CAACwH,GAC7B3d,EAAOie,YAAY,CAAGvgB,EAAMsC,MAAM,CAAGzC,EAAM2Y,QAAQ,CAACgI,KAAK,CAAC,GAAI,EAAG,EAAG3d,EAAOE,GACtEsU,QAAQ,CAAC,2BACToJ,GAAG,CAACV,GACT/f,EAAMgO,KAAK,CAAG,WACVhO,EAAMsC,MAAM,CAACoe,IAAI,CAAC,CAGdC,KAAM,oHACV,EACJ,EACA3gB,EAAM4gB,IAAI,CAAG,WACT5gB,EAAM6gB,MAAM,GACZ7gB,EAAMsC,MAAM,CAACoe,IAAI,CAAC,CACdC,KAAM3gB,EAAM+V,MAAM,CAAC+K,SAAS,CAAC,YACjC,EACJ,IAGA9gB,EAAM+gB,QAAQ,CAAGlhB,EAAM2Y,QAAQ,CAC1BgB,aAAa,CAAC,iBACdiH,GAAG,CAACV,GACTzd,EAAOie,YAAY,CAAGvgB,EAAMsC,MAAM,CAC9Buc,EAAgBrF,aAAa,CAAC,UAClCxZ,EAAMghB,SAAS,CAAGhhB,EAAMsC,MAAM,CAACwW,UAAU,CAAC,MAC1C9Y,EAAM+gB,QAAQ,CAACE,OAAO,CAACC,WAAW,CAAClhB,EAAMsC,MAAM,EAC/CtC,EAAMgO,KAAK,CAAG,WACVhO,EAAMsC,MAAM,CAACO,KAAK,CAAG7C,EAAM+V,MAAM,CAAClT,KAAK,CACvC7C,EAAMsC,MAAM,CAACS,MAAM,CAAG/C,EAAM+V,MAAM,CAAChT,MAAM,AAC7C,EACA/C,EAAM4gB,IAAI,CAAG,WACT5gB,EAAMsC,MAAM,CAACO,KAAK,CAAG7C,EAAM+V,MAAM,CAAClT,KAAK,CACvC7C,EAAMsC,MAAM,CAACS,MAAM,CAAG/C,EAAM+V,MAAM,CAAChT,MAAM,CACzC/C,EAAMghB,SAAS,CAACG,SAAS,CAACnhB,EAAM+V,MAAM,CAAE,EAAG,EAC/C,GAEJ/V,EAAM6gB,MAAM,CAAG,WACX,IAAIpgB,EACAY,EACJwB,EAAQhD,EAAMgW,UAAU,CACxB9S,EAASlD,EAAMiW,WAAW,CAC1B,AAAC9V,CAAAA,EAAM+gB,QAAQ,EAAI/gB,EAAMsC,MAAM,AAAD,EACzBoe,IAAI,CAAC,CACNje,EAAG,EACHE,EAAG,EACHE,MAAOA,EACPE,OAAQA,CACZ,GACKqe,GAAG,CAAC,CACLC,cAAelB,EAAkB,KAAK,EAAI,OAC1CmB,eAAgB,SAChBC,QAjFA,CAkFJ,GACKlK,QAAQ,CAAC8I,EAAkB,qBAAuB,IACnD7d,aAAkBwd,GAClB,CAAA,AAAmF,OAAlFze,CAAAA,EAAK,AAAwB,OAAvBZ,CAAAA,EAAK6B,EAAOtC,KAAK,AAAD,GAAeS,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGyW,WAAW,AAAD,GAAe7V,AAAO,KAAK,IAAZA,GAAyBA,EAAGmgB,SAAS,CAAC3hB,EAAM6C,QAAQ,CAAE7C,EAAM+C,OAAO,CAAA,CAEtK,EACA5C,EAAMyhB,QAAQ,CAAG5hB,EAAM2Y,QAAQ,CAACiJ,QAAQ,GACxC,AAACzhB,CAAAA,EAAM+gB,QAAQ,EAAI/gB,EAAMsC,MAAM,AAAD,EACzBoe,IAAI,CAAC,CAMNgB,OAAQ3hB,EAAOG,OAAO,CAACwhB,MAAM,AACjC,GACIpf,aAAkBwd,GAClBxd,CAAAA,EAAOtC,KAAK,CAACkX,WAAW,CAAG5U,EAAOkW,QAAQ,CACrCmJ,CAAC,GACDlB,GAAG,CAACV,GACJyB,SAAS,CAACzhB,EAAOW,KAAK,CAAC+C,GAAG,CAAE1D,EAAOuD,KAAK,CAACG,GAAG,CAAA,GAGzDzD,EAAM+V,MAAM,CAAClT,KAAK,CAAGA,EACrB7C,EAAM+V,MAAM,CAAChT,MAAM,CAAGA,EAClB/C,EAAMyhB,QAAQ,CAAE,CAChB,IAAIG,EAAMC,EAA6BhiB,EACnCyC,GAIAwf,EAAiB,AAACF,EAAI/e,KAAK,GAAKhD,EAAM2C,OAAO,CAACK,KAAK,EAC/C+e,EAAI7e,MAAM,GAAKlD,EAAM2C,OAAO,CAACO,MAAM,CAAIgd,EACtC/f,EAAM+gB,QAAQ,EAAI/gB,EAAMsC,MAAM,CACvCtC,EAAMyhB,QAAQ,CAACf,IAAI,CAACkB,GACpBE,MAAAA,GAAgEA,EAAeC,IAAI,CAAC/hB,EAAMyhB,QAAQ,CACtG,CAyBA,OAxBAzhB,EAAM6gB,MAAM,GACZ7gB,EAAMgO,KAAK,GACP,CAAChO,EAAMgiB,GAAG,GACVhiB,EAAMgiB,GAAG,CAAG,IA7vBkChW,EA6vBZ,SAAUgW,CAAG,EACvCA,EAAI3V,QAAQ,CAACO,KAAK,CAACI,cAAc,EACjCoH,QAAQO,IAAI,CAAC,eAEjB3U,EAAM4gB,IAAI,GACNoB,EAAI3V,QAAQ,CAACO,KAAK,CAACI,cAAc,EACjCoH,QAAQY,OAAO,CAAC,cAExB,GACKhV,EAAMgiB,GAAG,CAACpJ,IAAI,CAAC5Y,EAAM+V,MAAM,GAI5BmJ,GAAkB,sDAEtBlf,EAAMgiB,GAAG,CAACpU,UAAU,CAAC/N,EAAMK,OAAO,CAACF,KAAK,EAAI,CAAC,GACzCsC,aAAkBwd,GAClB9f,EAAMgiB,GAAG,CAACnU,cAAc,CAAChO,IAGjCG,EAAMgiB,GAAG,CAACrJ,OAAO,CAAC9V,EAAOE,GAClB/C,EAAMgiB,GAAG,AACpB,CAOA,SAASC,GAAgBliB,CAAM,EAC3B,IAAIqC,EAASrC,EAAOqC,MAAM,CAC1B,GAAIA,EAAQ,CACR,IAAIqQ,EAAQ,KAAK,EACbpC,EAAI,KAAK,EACb,IAAKA,EAAI,EAAGA,EAAIjO,EAAO/B,MAAM,CAAEgQ,GAAQ,EACnCoC,CAAAA,EAAQrQ,CAAM,CAACiO,EAAE,AAAD,GACHoC,EAAMyP,eAAe,EAC9BzP,EAAMyP,eAAe,EAGjC,CACA,CAAC,QAAS,OAAQ,UAAU,CAAC3iB,OAAO,CAAC,SAAUV,CAAI,EAC/C,IAAIsjB,EAAapiB,CAAM,CAAClB,EAAK,CACzBsjB,GACApiB,CAAAA,CAAM,CAAClB,EAAK,CAAGsjB,EAAWrb,OAAO,EAAC,CAE1C,GACA,IAAK,IAAItG,EAAK,EAAGC,EAAKV,EAAOiQ,KAAK,CAAExP,EAAKC,EAAGJ,MAAM,CAAEG,IAEhDye,EADWxe,CAAE,CAACD,EAAG,CACa,KAAK,EAAG,CAAA,EAE9C,CAmBA,SAAS4hB,GAAUC,CAAG,CAAE/I,CAAE,CAAEgJ,CAAS,CAAEC,CAAS,CAAElS,CAAC,CAAEmS,CAAS,EAK1D,IAFA,IAAIriB,EAAYkQ,AAFhBA,CAAAA,EAAIA,GAAK,CAAA,EACTkS,CAAAA,EAAYA,GApWC,GAoWqB,EAE9BE,EAAU,CAAA,EACPA,GAAWpS,EAAIlQ,GAAakQ,EAAIgS,EAAIhiB,MAAM,EAC7CoiB,EAAUnJ,EAAG+I,CAAG,CAAChS,EAAE,CAAEA,GACrB,EAAEA,EAEFoS,IACIpS,EAAIgS,EAAIhiB,MAAM,CACVmiB,EACAJ,GAAUC,EAAK/I,EAAIgJ,EAAWC,EAAWlS,EAAGmS,GAEvCzD,EAAgB2D,qBAAqB,CAE1C3D,EAAgB2D,qBAAqB,CAAC,WAClCN,GAAUC,EAAK/I,EAAIgJ,EAAWC,EAAWlS,EAC7C,GAGAqI,WAAW0J,GAAW,EAAGC,EAAK/I,EAAIgJ,EAAWC,EAAWlS,GAGvDiS,GACLA,IAGZ,CAuFA,SAASK,GAAY5iB,CAAM,CAAE6iB,CAAM,EAC/B,IAAI1iB,EAAUH,EAAOG,OAAO,CACxB2iB,EAAa9iB,EAAO+iB,SAAS,CAAC5F,QAAQ,CAACC,QAAQ,CAC/Czc,EAAQX,EAAOW,KAAK,EAAIX,EAAOW,KAAK,CAACR,OAAO,CAC5CoD,EAAQvD,EAAOuD,KAAK,EAAIvD,EAAOuD,KAAK,CAACpD,OAAO,CAC5C6iB,EAAYhjB,EAAOgjB,SAAS,EAAIhjB,EAAOgjB,SAAS,CAAC7iB,OAAO,CAC5D,OAAO2iB,EAAc3iB,CAAAA,EAAQqB,cAAc,EAAIQ,OAAOC,SAAS,AAAD,GAE1Dqd,GAAqB/b,EAAM1C,GAAG,GAC9Bye,GAAqB/b,EAAMvC,GAAG,GAE7B,CAAA,CAAC6hB,GACGvD,GAAqB3e,EAAME,GAAG,GAAKye,GAAqB3e,EAAMK,GAAG,CAAC,GAEtE,CAAA,CAACgiB,GACG1D,GAAqB0D,EAAUniB,GAAG,GAAKye,GAAqB0D,EAAUhiB,GAAG,CAAC,CACvF,CAOA,IAAIiiB,GAAoB,SAAUjjB,CAAM,CAAEoC,CAAI,QAE1C,CAAIpC,EAAOkjB,SAAS,EAGZtD,CAAAA,AA35Fe/f,EA25FmBG,EAAOF,KAAK,GACjD,AAACsC,CAAAA,EAAOA,EAAK9B,MAAM,CAAG,CAAA,GAClBN,CAAAA,EAAOG,OAAO,CAACqB,cAAc,EAAIQ,OAAOC,SAAS,AAAD,CAAE,CAC/D,EAOA,SAASkhB,KACL,IAAInjB,EAAS,IAAI,CACbF,EAAQE,EAAOF,KAAK,AACpBA,CAAAA,EAAMG,KAAK,EACXH,EAAMG,KAAK,CAACkX,WAAW,GAAKnX,EAAOmX,WAAW,EAC9CnX,CAAAA,EAAOmX,WAAW,CAAG,IAAG,EAExBrX,EAAMsjB,WAAW,EACjBtjB,CAAAA,EAAMsjB,WAAW,CAAGtjB,EAAMsjB,WAAW,CAACC,MAAM,CAAC,SAAU3Q,CAAK,EACxD,OAAOA,EAAM1S,MAAM,GAAKA,CAC5B,EAAC,EAEDF,EAAMwjB,UAAU,EAAIxjB,EAAMwjB,UAAU,CAACtjB,MAAM,GAAKA,GAChDF,CAAAA,EAAMwjB,UAAU,CAAG,IAAG,CAE9B,CAIA,SAASC,KACL,IAAItjB,EAAQ,IAAI,CAACA,KAAK,CAClBA,GAASA,EAAM+V,MAAM,EAAI/V,EAAMsC,MAAM,GACjCtC,EAAMgiB,GAAG,EACThiB,EAAMgiB,GAAG,CAAChU,KAAK,GAEfhO,EAAMgO,KAAK,EACXhO,EAAMgO,KAAK,GAGvB,CAMA,SAASuV,GAA0BxjB,CAAM,EACrC,IAAIC,EAAQD,EAAOC,KAAK,CACpBA,GACAA,EAAM+V,MAAM,EACZ/V,EAAMsC,MAAM,EACZtC,EAAMgiB,GAAG,EACT,CAACrC,AA98FkB/f,EA88FgBG,EAAOF,KAAK,GAC/CG,EAAMgiB,GAAG,CAAChX,MAAM,CAACjL,EAAOF,KAAK,CAErC,CAUA,SAAS2jB,GAASzjB,CAAM,CAAE0jB,CAAU,EAEhC,IADIhjB,EACAS,EAAgBnB,EAAOG,OAAO,CAC9BQ,EAAQX,EAAOW,KAAK,CACpBgjB,EAAa3jB,EAAO4jB,UAAU,CAClC,GAAIF,aAAsBC,EACtB,OAAOD,EAEX,IAAIG,EAAY7jB,EAAOsD,EAAE,CAAC,WAAYiK,EAAS,AAACsW,CAAAA,GAAa7jB,EAAOmC,SAAS,CAAC,IAAK,CAAA,GAAM7B,MAAM,CACvFN,EAAOmC,SAAS,CAAC,IAAK,CAAA,GACtB,KAAK,CAAA,GACJnC,CAAAA,EAAOmC,SAAS,CAAC,KAAK7B,MAAM,CAAGN,EAAOmC,SAAS,CAAC,KAAO,KAAK,CAAA,GAC7DhB,EAAcoM,KAAK,EACnBvN,EAAOmC,SAAS,CAAC,IAAK,CAAA,IACtB,CAAA,EAAQoN,EAASvP,EAAOmC,SAAS,CAAC,IAAK,CAAA,IACvChB,EAAcoO,KAAK,EACnB,CAAA,EAAQmD,EAAQ,IAAIiR,EAAW3jB,EAAQ,AAAC6jB,GAAatW,GAASgC,EAC9D,CAAChC,CAAK,CAACmW,EAAWpT,CAAC,CAAC,CAAEf,CAAK,CAACmU,EAAWpT,CAAC,CAAC,CAAC,CAC1C,AAACsL,CAAAA,GAAQ5b,EAAOG,OAAO,CAACiC,IAAI,EAAIpC,EAAOG,OAAO,CAACiC,IAAI,CAAG,EAAE,AAAD,CAAE,CAACshB,EAAWpT,CAAC,CAAC,CAAE/C,EAAQA,CAAK,CAACmW,EAAWpT,CAAC,CAAC,CAAG,KAAK,GAapH,OAZAoC,EAAMoR,QAAQ,CAAGvE,GAAiB5e,EAAMojB,UAAU,CAC9CpjB,EAAMojB,UAAU,CAACrR,EAAMhQ,CAAC,CAAC,CACzBgQ,EAAMhQ,CAAC,CACXgQ,EAAMhQ,CAAC,EACPgQ,EAAMnU,GAAG,CAAG,AAAsB,OAArBmC,CAAAA,EAAKgS,EAAMrJ,IAAI,AAAD,GAAe3I,AAAO,KAAK,IAAZA,EAAgBA,EAAKgS,EAAMoR,QAAQ,CAC7EpR,EAAMsR,IAAI,CAAGN,EAAWM,IAAI,CAC5BtR,EAAMuR,KAAK,CAAGP,EAAWO,KAAK,CAC9BvR,EAAMwR,KAAK,CAAGR,EAAWQ,KAAK,CAC9BxR,EAAMG,KAAK,CAAG6Q,EAAW7Q,KAAK,CAC9BH,EAAMlV,KAAK,CAAGkmB,EAAWpT,CAAC,CAC1BoC,EAAMyR,UAAU,CAAGT,EAAWS,UAAU,CACxCzR,EAAM0R,QAAQ,CAAGpkB,EAAOqkB,aAAa,CAAC3R,GAC/BA,CACX,CAIA,SAAS4R,GAAmBC,CAAK,EAS7B,IACIpkB,EAAUH,AADD,IAAI,CACIG,OAAO,CACxBQ,EAAQX,AAFC,IAAI,CAEEW,KAAK,CACpB4C,EAAQvD,AAHC,IAAI,CAGEuD,KAAK,CAExB,GAAI,CAACvD,AALQ,IAAI,CAKLwkB,OAAO,EACf,CAAC7jB,EAAM6jB,OAAO,EACd,CAACjhB,EAAMihB,OAAO,EACd,CAACD,EACD,MAAO,CAAA,EAIXvkB,AAba,IAAI,CAaVuD,KAAK,CAACkhB,eAAe,GAC5B,IAAIjjB,EAAiBrB,EAAQqB,cAAc,EAAI,EAAGkjB,EAAgBvkB,EAAQukB,aAAa,CAAEnX,EAAQvN,AAdpF,IAAI,CAcuFmC,SAAS,CAAC,KAAM4M,EAAYpO,EAAMqO,WAAW,GAAIG,EAAO,AAAyB,OAAxBzO,CAAAA,EAAKqO,EAAU/N,GAAG,AAAD,GAAeN,AAAO,KAAK,IAAZA,EAAgBA,EAAKsB,OAAOC,SAAS,CAAEgN,EAAO,AAAyB,OAAxB3N,CAAAA,EAAKyN,EAAUlO,GAAG,AAAD,GAAeS,AAAO,KAAK,IAAZA,EAAgBA,EAAK,CAACU,OAAOC,SAAS,CAAEsN,EAAQvP,AAdnT,IAAI,CAcsTmC,SAAS,CAAC,KAAMiN,EAAY7L,EAAMyL,WAAW,GAAIM,EAAO,AAAyB,OAAxB2D,CAAAA,EAAK7D,EAAUpO,GAAG,AAAD,GAAeiS,AAAO,KAAK,IAAZA,EAAgBA,EAAKjR,OAAOC,SAAS,CAAEoN,EAAO,AAAyB,OAAxB8D,CAAAA,EAAK/D,EAAUvO,GAAG,AAAD,GAAesS,AAAO,KAAK,IAAZA,EAAgBA,EAAK,CAACnR,OAAOC,SAAS,CAErhB,GAAI,CAACjC,AAhBQ,IAAI,CAgBLyN,OAAO,EACf9M,EAAMgkB,GAAG,EACTphB,EAAMohB,GAAG,EACT1V,GAAS,CAAA,AAAyB,OAAxBmE,CAAAA,EAAKzS,EAAMgkB,GAAG,CAAC9jB,GAAG,AAAD,GAAeuS,AAAO,KAAK,IAAZA,EAAgBA,EAAK,CAACpR,OAAOC,SAAS,AAAD,GAC/EkN,GAAS,CAAA,AAAyB,OAAxByV,CAAAA,EAAKjkB,EAAMgkB,GAAG,CAAC3jB,GAAG,AAAD,GAAe4jB,AAAO,KAAK,IAAZA,EAAgBA,EAAK5iB,OAAOC,SAAS,AAAD,GAC9EoN,GAAS,CAAA,AAAyB,OAAxBwV,CAAAA,EAAKthB,EAAMohB,GAAG,CAAC9jB,GAAG,AAAD,GAAegkB,AAAO,KAAK,IAAZA,EAAgBA,EAAK,CAAC7iB,OAAOC,SAAS,AAAD,GAC/EqN,GAAS,CAAA,AAAyB,OAAxBwV,CAAAA,EAAKvhB,EAAMohB,GAAG,CAAC3jB,GAAG,AAAD,GAAe8jB,AAAO,KAAK,IAAZA,EAAgBA,EAAK9iB,OAAOC,SAAS,AAAD,EAK9E,OAJAjC,AAvBS,IAAI,CAuBN+iB,SAAS,CAAC5F,QAAQ,CAACiB,UAAU,CAAC,CACjC1b,EAAG6K,EACH3K,EAAG2M,CACP,GACO,CAAA,EAGX,IAAIuT,EAAa9iB,AA9BJ,IAAI,CA8BO+iB,SAAS,CAAC3F,QAAQ,CAC1C,GAAI,CAAC5b,GACDshB,EAAathB,GACZkjB,GACG,CAAC1kB,AAlCI,IAAI,CAkCDkjB,SAAS,EACjB,CAACljB,AAnCI,IAAI,CAmCD+kB,kBAAkB,EAC1B,CAAC5kB,EAAQ4kB,kBAAkB,EAC3BjC,EAAa4B,EAKjB,OAJA1kB,AAtCS,IAAI,CAsCN+iB,SAAS,CAAC5F,QAAQ,CAACiB,UAAU,CAAC,CACjC1b,EAAG6K,EACH3K,EAAG2M,CACP,GACO,CAAA,EAeX,IAAK,IAjED7O,EACAY,EACA2R,EACAE,EACAC,EACAwR,EACAC,EACAC,EAoDApiB,EAGAE,EATAoiB,EAAgB,EAAE,CAClBC,EAAiB,EAAE,CACnBC,EAAiB,EAAE,CACnBC,EAAe,CAAE7F,CAAAA,GAAqBvQ,EAAU/N,GAAG,GAAKse,GAAqBvQ,EAAUlO,GAAG,CAAA,EAC1FukB,EAAe,CAAE9F,CAAAA,GAAqBlQ,EAAUpO,GAAG,GAAKse,GAAqBlQ,EAAUvO,GAAG,CAAA,EAC1FwkB,EAAU,CAAA,EAEVC,EAAW/X,CAAK,CAAC,EAAE,CACnBgY,EAAWhY,CAAK,CAAC,EAAE,CAEnBiY,EAAWjW,MAAAA,EAAqC,KAAK,EAAIA,CAAK,CAAC,EAAE,CACjEkW,EAAWlW,MAAAA,EAAqC,KAAK,EAAIA,CAAK,CAAC,EAAE,CAC5De,EAAI,EAAGoV,EAAOnY,EAAMjN,MAAM,CAAEgQ,EAAIoV,EAAM,EAAEpV,EAC7C5N,EAAI6K,CAAK,CAAC+C,EAAE,CACZ1N,EAAI2M,MAAAA,EAAqC,KAAK,EAAIA,CAAK,CAACe,EAAE,CACtD5N,GAAKuM,GAAQvM,GAAKyM,GAClBvM,GAAKyM,GAAQzM,GAAK0M,GAClB0V,EAAc9e,IAAI,CAAC,CAAExD,EAAGA,EAAGE,EAAGA,CAAE,GAChCqiB,EAAe/e,IAAI,CAACxD,GACpBwiB,EAAehf,IAAI,CAACtD,GAChBuiB,IACAG,EAAW1hB,KAAK5C,GAAG,CAACskB,EAAU5iB,GAC9B6iB,EAAW3hB,KAAK/C,GAAG,CAAC0kB,EAAU7iB,IAE9B0iB,IACAI,EAAW5hB,KAAK5C,GAAG,CAACwkB,EAAU5iB,GAC9B6iB,EAAW7hB,KAAK/C,GAAG,CAAC4kB,EAAU7iB,KAIlCyiB,EAAU,CAAA,EA2BlB,OAxBIF,IACAxkB,EAAMM,OAAO,CAAG2C,KAAK5C,GAAG,CAACskB,EAAU3kB,EAAMM,OAAO,EAAI,GACpDN,EAAMI,OAAO,CAAG6C,KAAK/C,GAAG,CAAC0kB,EAAU5kB,EAAMI,OAAO,EAAI,IAEpDqkB,IACA7hB,EAAMtC,OAAO,CAAG2C,KAAK5C,GAAG,CAACwkB,EAAUjiB,EAAMtC,OAAO,EAAI,GACpDsC,EAAMxC,OAAO,CAAG6C,KAAK/C,GAAG,CAAC4kB,EAAUliB,EAAMxC,OAAO,EAAI,IAGxDf,AAvFa,IAAI,CAuFVqlB,OAAO,CAAGA,EACjBrlB,AAxFa,IAAI,CAwFV2lB,SAAS,CAAG,EAEfN,GAAWrlB,AA1FF,IAAI,CA0FK+iB,SAAS,CAAC5F,QAAQ,GAAKnd,AA1FhC,IAAI,CA0FmC+iB,SAAS,EAGzD/iB,CAAAA,AA7FS,IAAI,CA6FN+iB,SAAS,CAAC5F,QAAQ,CAAG,IA5vBmBJ,CA4vBI,EAEvD/c,AA/Fa,IAAI,CA+FV+iB,SAAS,CAAC5F,QAAQ,CAACiB,UAAU,CAAC,CACjC1b,EAAGuiB,EACHriB,EAAGsiB,CACP,GACKjC,GAnGQ,IAAI,CAmGcgC,IAC3BjlB,CAAAA,AApGS,IAAI,CAoGNglB,aAAa,CAAGA,CAAY,EAEhC,CAAA,CACX,CAKA,SAASY,KACL,IAUIC,EAEAvX,EACAwX,EACAC,EACAC,EAfAphB,EAAQ,IAAI,CACZzE,EAAU,IAAI,CAACA,OAAO,EAAI,CAAC,EAAGL,EAAQ,IAAI,CAACA,KAAK,CAAEmmB,EAAanmB,EAAMG,KAAK,CAAEimB,EAAc,IAAI,CAACjmB,KAAK,CAAEU,EAAQ,IAAI,CAACA,KAAK,CAAE4C,EAAQ,IAAI,CAACA,KAAK,CAAEgK,EAAQpN,EAAQoN,KAAK,EAAI,IAAI,CAACpL,SAAS,CAAC,IAAK,CAAA,GAAOoN,EAAQpP,EAAQoP,KAAK,EAAI,IAAI,CAACpN,SAAS,CAAC,IAAK,CAAA,GAAOgkB,EAAU,IAAI,CAAChkB,SAAS,CAAC,MAAO,CAAA,GAAOikB,EAAW,IAAI,CAACjkB,SAAS,CAAC,OAAQ,CAAA,GAAO2M,EAAU,IAAI,CAACkW,aAAa,EAAI7kB,EAAQiC,IAAI,CAAE2M,EAAYpO,EAAMqO,WAAW,GAEjZC,EAAOF,EAAUlO,GAAG,CAAIF,CAAAA,EAAMuO,cAAc,EAAI,CAAA,EAAIC,EAAOJ,EAAU/N,GAAG,CAAIL,CAAAA,EAAMuO,cAAc,EAAI,CAAA,EAAIE,EAAY7L,EAAMyL,WAAW,GAAIK,EAAOD,EAAUvO,GAAG,CAAI0C,CAAAA,EAAM2L,cAAc,EAAI,CAAA,EAAII,EAAOF,EAAUpO,GAAG,CAAIuC,CAAAA,EAAM2L,cAAc,EAAI,CAAA,EAAImX,EAAa,CAAC,EAAGC,EAAW,CAAC,CAAC,IAAI,CAACA,QAAQ,CAAEC,EAAsBpmB,EAAQomB,mBAAmB,CAAEnmB,EAAYD,EAAQC,SAAS,CAAEuO,EAAU,IAAI,CAACC,aAAa,EACxY,AAAiC,aAAjC,IAAI,CAACA,aAAa,CAACvI,IAAI,CAAC,KAAqBiH,EAAY,CAAC,CAACnN,EAAQuN,QAAQ,CAAEiY,EAAY,IAAI,CAACA,SAAS,EAAI,EAAGa,EAAiB,IAAI,CAACA,cAAc,CAAE/W,EAAS,CAAClC,EAAOkZ,EAAWtmB,AAA+B,MAA/BA,EAAQumB,kBAAkB,CAAUC,EAAa,AAAC,CAAA,IAAI,CAACxkB,SAAS,CAAC,KAAK7B,MAAM,CAC5P,IAAI,CAAC6B,SAAS,CAAC,KACf,KAAK,CAAA,GACL,IAAI,CAAChC,OAAO,CAACoN,KAAK,EAClB,IAAI,CAACpL,SAAS,CAAC,IAAK,CAAA,GAAQqK,EAAY+S,GAAiBpf,EAAQqM,SAAS,CAAE,GAAIoa,EAAkBzmB,EAAQ0mB,eAAe,EAAIxX,EACjIoJ,EAAW,CAAA,EAEXjC,EAAUjT,EAAMkT,YAAY,CAACrW,GASjC,GAAIO,CAAAA,EAAMmmB,SAAS,GAAIvjB,EAAMujB,SAAS,CAMtC,CAAA,GAFArO,EAAWqH,GAAwBhgB,EAAO,IAAI,EAC9CA,EAAM2N,OAAO,CAAG,CAAA,EACX,IAAI,CAAChM,OAAO,EAIb,CAAA,IAAI,CAACY,MAAM,EAAI,IAAI,CAAC0kB,KAAK,AAAD,GACxB7E,GAAgB,IAAI,EAInBtC,AAxpGkB/f,EAwpGgBC,IAW/B,IAAI,CAACqX,WAAW,EAChB,IAAI,CAACA,WAAW,GAAM8O,CAAAA,MAAAA,EAA+C,KAAK,EAAIA,EAAW9O,WAAW,AAAD,GACnG,IAAI,CAACA,WAAW,CAACpQ,OAAO,GAG5B,IAAI,CAACoQ,WAAW,CAAG8O,MAAAA,EAA+C,KAAK,EAAIA,EAAW9O,WAAW,CAG7F+O,GAAeA,EAAY3jB,MAAM,EACjC,CAAA,IAAI,CAACie,YAAY,CACb0F,EAAY3jB,MAAM,CACd2jB,EAAY3jB,MAAM,CAACwE,OAAO,EAAC,IAnBnC,IAAI,CAACoQ,WAAW,GAAM8O,CAAAA,MAAAA,EAA+C,KAAK,EAAIA,EAAW9O,WAAW,AAAD,GACnG,CAAA,IAAI,CAACA,WAAW,CAAG,KAAK,CAAA,EAE5B,IAAI,CAACA,WAAW,CAAG,IAAI,CAACC,SAAS,CAAC,cAAe,UAAW,UAAW,EAAGtX,EAAMuX,WAAW,EAAEC,QAAQ,CAAC,uBAmB1G,IAAIjV,EAAS,IAAI,CAACA,MAAM,CAAG,EAAE,CACzB2kB,EAAa,SAAUC,CAAO,CAC9BpU,CAAK,CACLvC,CAAC,CACD6T,CAAU,EACN,IAAIzhB,EAAIikB,EAAAA,GAAYA,CAAS,CAAChB,EAAYrV,EAAE,CAChD4W,EAAY,SAAUhD,CAAK,EACfpkB,EAAMoD,QAAQ,GACdghB,EAAQvjB,EAAMkD,GAAG,CAAGqgB,EACxBrR,EAAQtP,EAAMM,GAAG,CAAGgP,GAExBxQ,EAAO6D,IAAI,CAAC,CACRa,QAASgY,EACTrc,EAAGA,EACHukB,QAAS/C,EACTA,MAAOA,EACPrR,MAAOA,EACPvC,EAAGqV,EAAYrV,EACf6T,WAAYA,CAChB,EACJ,EAIA8C,EAAUrjB,KAAKujB,IAAI,CAACF,GAEpBzpB,EAAQipB,EAAWQ,EAAUA,EAAU,IAAMpU,EAIzC0T,IACKF,CAAU,CAAC7oB,EAAM,CAIbkF,IAAMikB,CAAS,CAACA,EAAUrmB,MAAM,CAAG,EAAE,GAG1C+B,EAAO/B,MAAM,GACb4mB,EAAUD,KAPVZ,CAAU,CAAC7oB,EAAM,CAAG,CAAA,EACpB0pB,EAAUD,IAStB,CAEA,CAAA,IAAI,CAACG,WAAW,CAAGrI,EACnBM,GAAsB,IAAI,CAAE,gBACxB,IAAI,CAAC/b,EAAE,CAAC,SACRkJ,EAAY,GACX0Z,CAAAA,MAAAA,EAAiD,KAAK,EAAIA,EAAY3jB,MAAM,AAAD,GAC5E0jB,GACA,CAACA,EAAWoB,eAAe,GAC3BpB,EAAWoB,eAAe,CAAGvnB,EAAM2Y,QAAQ,CAACna,UAAU,CAAC,CACnDgpB,QAAS,SACTC,SAAU,CACN,CACID,QAAS,eACTE,WAAY,CACRC,SAAU,SACV9Q,OAAQ,IAAOnK,CACnB,CACJ,EACH,CACDgb,WAAY,CAAEvK,GAAI,WAAY,CAClC,GACAiJ,EAAY3jB,MAAM,CAACoe,IAAI,CAAC,CACpB0C,OAAQ,iBACZ,IAEA5K,IACAkH,GAA4BlH,EAAU,IAAI,EAC1CA,EAAS/D,UAAU,CAAC,IAAI,EAExB8O,GAA0B,IAAI,GA8FlC,IAAItjB,EAAeuY,EAASnM,QAAQ,AAgB/BxM,CAAAA,EAAM2Y,QAAQ,CAACC,SAAS,GACrBxY,EAAa2M,KAAK,CAACK,UAAU,EAC7BmH,QAAQO,IAAI,CAAC,oBAEjByN,GAAU/U,EACN,IAAI,CAAClL,IAAI,CAACwR,KAAK,CAAC+R,GACfpY,GAASuB,EA9GlB,SAAsB1Q,CAAC,CAAEkS,CAAC,EAGtB,IAFI5P,EACAY,EAEAoB,EACAE,EACAqkB,EACApU,EACAsR,EALArU,EAAiB,AAAuB,KAAA,IAAhBhQ,EAAMtC,KAAK,CAMnCiR,EAAM,CAAA,EACNoC,EAAY,CAAA,QAChB,CAAK6O,GAAQthB,KAGT,CAAC0R,IACGL,GACA/M,EAAItE,CAAC,CAAC,EAAE,CACRwE,EAAIxE,CAAC,CAAC,EAAE,GAGRsE,EAAItE,EACJwE,EAAI,AAA4E,OAA3EtB,CAAAA,EAAK,AAAoB,OAAnBZ,CAAAA,EAAK6O,CAAK,CAACe,EAAE,AAAD,GAAe5P,AAAO,KAAK,IAAZA,EAAgBA,EAAKkmB,CAAc,GAAetlB,AAAO,KAAK,IAAZA,EAAgBA,EAAK,MAG7GqN,GACIc,GACA7M,CAAAA,EAAIxE,EAAEwV,KAAK,CAAC,EAAG,EAAC,EAEpBnF,EAAM0X,CAAO,CAAC7V,EAAE,CAChB1N,EAAIwjB,CAAQ,CAAC9V,EAAE,EAEVhD,IACL5K,EAAItE,EAAEsE,CAAC,CAEP+L,EAAM7L,AADNA,CAAAA,EAAIxE,EAAEyV,MAAM,AAAD,EACDzV,EAAEwE,CAAC,CACbuhB,EAAa/lB,EAAE+lB,UAAU,EAGxBqC,GACD3V,CAAAA,EAAY,AAACjO,CAAAA,GAAK,CAAA,GAAMyM,GAAQzM,GAAK0M,CAAG,EAElC,OAAN1M,GAAcF,GAAKuM,GAAQvM,GAAKyM,GAAQ0B,IACxCoW,EAAUtmB,EAAMqT,QAAQ,CAACtR,EAAG,CAAA,GACxB4jB,GACI,CAAA,AAAgB,KAAA,IAATP,GACPkB,IAAYpB,CAAU,IACjBlX,GACDF,CAAAA,EAAM7L,CAAAA,EAEN,CAAA,AAAgB,KAAA,IAATojB,GACPpjB,EAAIkjB,CAAK,IACTA,EAASljB,EACTojB,EAAO1V,GAEP,CAAA,AAAgB,KAAA,IAATyV,GACPtX,EAAMH,CAAK,IACXA,EAASG,EACTsX,EAAOzV,IAIVmW,GAAYQ,IAAYpB,IAEL,KAAA,IAATE,IACPlT,EACItP,EAAMyQ,QAAQ,CAAC8R,EAAQ,CAAA,GAC3BtP,EACIjT,EAAMyQ,QAAQ,CAAC1F,EAAQ,CAAA,GAC3B0Y,EAAWC,EAASpU,EAAOmT,EAAM7B,GAC7B3N,IAAY3D,GACZmU,EAAWC,EAASzQ,EAASuP,EAAM5B,IAG3C4B,EAAOC,EAAO,KAAK,EACnBH,EAAcoB,IAKlBD,EAAWC,EADXpU,EAAQjP,KAAKujB,IAAI,CAAC5jB,EAAMyQ,QAAQ,CAACpR,EAAG,CAAA,IACT0N,EAAG6T,KAInC,CAACrU,EACZ,EAKqB,WACbuP,GAAsBza,EAAO,kBAEjC,OAAOA,EAAMwiB,WAAW,CAGpBxiB,EAAMzE,OAAO,EACbyE,EAAMwiB,WAAW,GAEjBlnB,EAAa2M,KAAK,CAACK,UAAU,EAC7BmH,QAAQY,OAAO,CAAC,mBAExB,IAnNA,CA8NJ,CAKA,SAASyS,GAAqBhF,CAAO,EACjC,IAAI3N,EAAU,CAAA,EAMd,GALI,IAAI,CAACjV,KAAK,CAACK,OAAO,EAAI,IAAI,CAACL,KAAK,CAACK,OAAO,CAACF,KAAK,EAC9C8U,CAAAA,EAAU,AAA4C,KAAA,IAArC,IAAI,CAACjV,KAAK,CAACK,OAAO,CAACF,KAAK,CAAC8U,OAAO,EAE7C,IAAI,CAACjV,KAAK,CAACK,OAAO,CAACF,KAAK,CAAC8U,OAAO,AAAD,EAEnC,CAACA,GAAW,CAAC,IAAI,CAACtH,OAAO,CACzB,OAAOiV,EAAQzjB,IAAI,CAAC,IAAI,CAE5B,CAAA,IAAI,CAACa,KAAK,CAAC2N,OAAO,CAAG,CAAA,EAErB,IAAIgL,EAAWqH,GAAwB,IAAI,CAAChgB,KAAK,CAC7C,IAAI,EACJ2Y,IACAkH,GAA4BlH,EAAU,IAAI,EAC1CA,EAAS/D,UAAU,CAAC,IAAI,GAE5B8O,GAA0B,IAAI,CAClC,CAmDA,SAASmE,GAAsBjF,CAAO,EAClC,GAAI,IAAI,CAACjV,OAAO,CAAE,CACd,GAAImV,GAAY,IAAI,EAChB,MAAO,CAAC,EAEZ,GAAI,IAAI,CAACjiB,KAAK,CAACmmB,SAAS,EAAI,IAAI,CAACvjB,KAAK,CAACujB,SAAS,CAI5C,OAAO,IAAI,AAEnB,CACA,OAAOpE,EAAQvG,KAAK,CAAC,IAAI,CAAE,EAAE,CAACvI,KAAK,CAAC3U,IAAI,CAAC6C,UAAW,GACxD,CAOA,SAAS8lB,GAAsBlF,CAAO,EAIlC,IAHIhiB,EACAY,EACA2R,EACA4U,EAAgB,IAAI,CAAC1nB,OAAO,CAACiC,IAAI,CACrC,GAAIyd,GAAa,IAAI,CAAC/f,KAAK,GAAK6B,AA3uHmBpC,CA2uHD,CAAC,IAAI,CAACmC,IAAI,CAAC,CAAE,CAC3D,IAGImiB,EAAY7jB,AAHH,IAAI,CAGMsD,EAAE,CAAC,YAClB,CAACtD,AAJI,IAAI,CAIDsD,EAAE,CAAC,WACX,CAACtD,AALI,IAAI,CAKDsD,EAAE,CAAC,YACX,CAACtD,AANI,IAAI,CAMDsD,EAAE,CAAC,WAInB,GAEA,CAAC2f,GAZY,IAAI,CAYU4E,IACvBhE,GACA7jB,AAdS,IAAI,CAcNsD,EAAE,CAAC,YAEVtD,AAhBS,IAAI,CAgBNG,OAAO,CAACuN,QAAQ,EACvB,CAACkV,GAjBQ,IAAI,CAiBQ,CAAA,GAAO,CAE5B,GAAI5iB,AAnBK,IAAI,CAmBFyN,OAAO,EAAK,CAAA,AAAC,CAAA,AAAwB,OAAvB/M,CAAAA,EAAKV,AAnBrB,IAAI,CAmBwBW,KAAK,AAAD,GAAeD,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGomB,SAAS,AAAD,GAAO,CAAA,AAAwB,OAAvBxlB,CAAAA,EAAKtB,AAnBhG,IAAI,CAmBmGuD,KAAK,AAAD,GAAejC,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGwlB,SAAS,AAAD,CAAC,EACpK,MAGAjD,CAAAA,GAAa7jB,AAAsB,aAAtBA,AAvBR,IAAI,CAuBWuD,KAAK,CAAC7B,IAAI,CAC9B4iB,GAAmBrlB,IAAI,CAxBlB,IAAI,CAwBuB6C,SAAS,CAAC,EAAE,EAG5C4gB,EAAQvG,KAAK,CA3BR,IAAI,CA2Ba,EAAE,CAACvI,KAAK,CAAC3U,IAAI,CAAC6C,UAAW,IAEnD+lB,EAAgB7nB,AA7BP,IAAI,CA6BUmC,SAAS,CAAC,IAAK,CAAA,EAC1C,CAKA,GAFAnC,AAjCa,IAAI,CAiCVyN,OAAO,CAAGwV,GAjCJ,IAAI,CAiC0B4E,GAEvC7nB,AAnCS,IAAI,CAmCNyN,OAAO,CAAE,CAEhB,IAAIqD,EAAa,KAAK,CACa,QAA9BmC,CAAAA,EAAKjT,AAtCD,IAAI,CAsCIG,OAAO,CAACiC,IAAI,AAAD,GAAe6Q,AAAO,KAAK,IAAZA,IAAyBA,EAAG3S,MAAM,EAErE,AAACgf,GADLxO,EAAa9Q,AAvCR,IAAI,CAuCW8nB,kBAAkB,CAAC9nB,AAvClC,IAAI,CAuCqCG,OAAO,CAACiC,IAAI,IAErDwZ,GAAQ9K,IACR9Q,AA1CA,IAAI,CA0CGsD,EAAE,CAAC,YACX6b,GAAkB,GAAI,CAAA,EAAOnf,AA3C5B,IAAI,CA2C+BF,KAAK,EAGjDioB,AAptBZ,SAAoB/nB,CAAM,EAEtBA,EAAOC,KAAK,CAAGD,EAAOC,KAAK,EAAI,CAE3BwjB,SAAW,SAAUuE,CAAE,EAAI,OAAOvE,GAASzjB,EAAQgoB,EAAK,CAC5D,EACA,IALItnB,EAKAunB,EAAiBjoB,EAAOC,KAAK,CAACioB,OAAO,CAAG,EAAE,CAoB9C,GAjBA,CAAC,UAAW,cAAe,iBAAiB,CAAC1oB,OAAO,CAAC,SAAUV,CAAI,EAC/DmpB,EAAe/hB,IAAI,CAAC,CAChBpH,KAAMA,EACNwK,IAAKtJ,CAAM,CAAClB,EAAK,CACjBqpB,IAAK1pB,OAAOO,cAAc,CAACC,IAAI,CAACe,EAAQlB,EAC5C,EACJ,GACAkB,EAAOooB,OAAO,CAAG,CAAA,EACjBpoB,EAAOqoB,WAAW,CAAG,CAAA,EACrBroB,EAAOsoB,cAAc,CAAG,CAAA,EAExBtoB,EAAOuoB,iBAAiB,CAAG,CAAA,EAEvBvoB,EAAOwoB,aAAa,EACpBxoB,CAAAA,EAAOwoB,aAAa,CAAGxoB,EAAOwoB,aAAa,CAACzhB,OAAO,EAAC,EAGpD/G,EAAOsD,EAAE,CAAC,YACV,CAACtD,EAAOsD,EAAE,CAAC,YACXtD,EAAOoC,IAAI,CAAC9B,MAAM,CAAE,CACpB,IAAK,IAAIG,EAAK,EAAGa,EAAKtB,EAAOoC,IAAI,CAAE3B,EAAKa,EAAGhB,MAAM,CAAEG,IAAM,CACrD,IAAIiS,EAAQpR,CAAE,CAACb,EAAG,AAClB,AAAuE,QAAtEC,CAAAA,EAAKgS,MAAAA,EAAqC,KAAK,EAAIA,EAAM3L,OAAO,AAAD,GAAerG,AAAO,KAAK,IAAZA,GAAyBA,EAAGzB,IAAI,CAACyT,EACpH,CACA1S,EAAOoC,IAAI,CAAC9B,MAAM,CAAG,EACrBN,EAAOqC,MAAM,CAAC/B,MAAM,CAAG,EACvB,OAAON,EAAOglB,aAAa,AAC/B,CACJ,EAioBqB,IAAI,CA+CjB,MAEIyD,AA5qBZ,SAAmBzoB,CAAM,EAErB,IADIU,EACAT,EAAQD,EAAOC,KAAK,CACpBH,EAAQE,EAAOF,KAAK,CACpBmmB,EAAanmB,EAAMG,KAAK,CAC5B,GAAIgmB,MAAAA,EAA+C,KAAK,EAAIA,EAAW9O,WAAW,CAAE,CAChF8O,EAAW9O,WAAW,CAACpQ,OAAO,GAC9Bkf,EAAW9O,WAAW,CAAG,KAAK,EAC9B,IAAK,IAAI1W,EAAK,EAAGa,EAAKxB,EAAME,MAAM,CAAES,EAAKa,EAAGhB,MAAM,CAAEG,IAAM,CACtD,IAAI+M,EAAIlM,CAAE,CAACb,EAAG,AACd+M,CAAAA,EAAE2J,WAAW,CAAG,KAAK,EACrB3J,EAAE2J,WAAW,CAAG3J,EAAE4J,SAAS,CAAC,cAAe,UAAW,UAAW,EAAGtX,EAAMuX,WAAW,EAAEC,QAAQ,CAAC,qBACpG,CACJ,CAGIrX,IACA,AAACA,CAAAA,EAAMioB,OAAO,EAAI,EAAE,AAAD,EAAG1oB,OAAO,CAAC,SAAUkpB,CAAO,EACvCA,EAAQP,GAAG,CACXnoB,CAAM,CAAC0oB,EAAQ5pB,IAAI,CAAC,CAAG4pB,EAAQpf,GAAG,CAIlC,OAAOtJ,CAAM,CAAC0oB,EAAQ5pB,IAAI,CAAC,AAEnC,GAEImB,EAAMgO,KAAK,EACXhO,EAAMgO,KAAK,IAInB,AAA+C,OAA9CvN,CAAAA,EAAMZ,EAAMuX,WAAW,EAAIrX,EAAOigB,KAAK,GAAevf,AAAO,KAAK,IAAZA,GAAyBA,EAAGshB,IAAI,EAC3F,EA0lBqB,IAAI,CAoDrB,MAEIU,EAAQvG,KAAK,CAAC,IAAI,CAAE,EAAE,CAACvI,KAAK,CAAC3U,IAAI,CAAC6C,UAAW,GAErD,CAKA,SAAS6mB,GAAsBjG,CAAO,EAClC,IAAIlG,EAASkG,EAAQvG,KAAK,CAAC,IAAI,CAC3B,EAAE,CAACvI,KAAK,CAAC3U,IAAI,CAAC6C,UAAW,WAC7B,AAAI,IAAI,CAAC7B,KAAK,EAAIuc,EACP,IAAI,CAACvc,KAAK,CAACwjB,QAAQ,CAACjH,GAExBA,CACX,CAY6B,IAuBzBoM,GAAoB,AAAC7kB,IAAuGuH,KAAK,CAEjIud,GAAkB,AAACxpB,IAA+EkM,GAAG,CAAEud,GAAmB,AAACzpB,IAA+E0f,IAAI,CAE9MgK,GAAuB,AAAC1pB,IAA+EM,QAAQ,CAAEqpB,GAAwB,AAAC3pB,IAA+Eud,SAAS,CAAEqM,GAAuB,AAAC5pB,IAA+EoM,QAAQ,CAAEyd,GAAoB,AAAC7pB,IAA+EsM,KAAK,CAAEwd,GAAmB,AAAC9pB,IAA+EO,IAAI,CAAEwpB,GAAmB,AAAC/pB,IAA+EogB,IAAI,EAOppB,AAAC,SAAU/hB,CAAW,EAOlB,IAQI2rB,EACAC,EATAC,EAAiB,qHAkBrB,SAASC,EAAiB9P,CAAG,CAAEuN,CAAO,CAAEpU,CAAK,CAAE2D,CAAO,CAAEiT,CAAS,EACzDA,GAAaxC,IAAYwC,EAAUxC,OAAO,GAC1CvN,EAAIwB,MAAM,CAACuO,EAAUxC,OAAO,CAAEwC,EAAUjT,OAAO,EAC/CkD,EAAIyB,MAAM,CAACsO,EAAUxC,OAAO,CAAEwC,EAAU5W,KAAK,EAC7C6G,EAAIyB,MAAM,CAAC8L,EAASpU,GACpB6G,EAAIyB,MAAM,CAAC8L,EAASzQ,GAE5B,CAIA,SAASkT,EAAsBhQ,CAAG,CAAEuN,CAAO,CAAEpU,CAAK,CAAE9Q,CAAC,CAAEuO,CAAC,EACpDoJ,EAAIwB,MAAM,CAAC+L,EAASpU,GACpB6G,EAAIqB,GAAG,CAACkM,EAASpU,EAAO,IAAI,CAAC8W,KAAK,EAAI,IAAI,CAACA,KAAK,CAACrZ,EAAE,CAAE,EAAG,EAAI1M,KAAKoX,EAAE,CAAE,CAAA,EACzE,CAIA,SAAS4O,EAAmBlQ,CAAG,CAAEuN,CAAO,CAAEpU,CAAK,CAAE2D,CAAO,EACpDkD,EAAImQ,IAAI,CAAC5C,EAAU,EAAGpU,EAAO,EAAG2D,EAAU3D,EAC9C,CAuDA,SAASiX,IACD,IAAI,CAAC7pB,KAAK,EAAI,IAAI,CAACA,KAAK,CAAC4gB,IAAI,EAC7B,IAAI,CAAC5gB,KAAK,CAAC4gB,IAAI,EAEvB,CAIA,SAASkJ,IACL,IAAI9pB,EAAQ,IAAI,CAACA,KAAK,EAAI,CAAC,CACvBA,CAAAA,EAAMsC,MAAM,EACZtC,EAAMsC,MAAM,CAACoe,IAAI,CAAC,CAAEC,KAAM2I,CAAc,GAExCtpB,EAAM+V,MAAM,EACZ/V,EAAM+V,MAAM,CAAC+C,UAAU,CAAC,MAAMiR,SAAS,CAAC,EAAG,EAAG/pB,EAAM+V,MAAM,CAAClT,KAAK,CAAE7C,EAAM+V,MAAM,CAAChT,MAAM,CAE7F,CAOA,SAASinB,IACAC,AAlsHcrqB,EAksHoB,IAAI,CAACC,KAAK,EAQxC,IAAI,CAACG,KAAK,EAAI,IAAI,CAACA,KAAK,CAACgO,KAAK,EACnC,IAAI,CAAChO,KAAK,CAACgO,KAAK,GARZ,IAAI,CAAChO,KAAK,EAAI,IAAI,CAACA,KAAK,CAAC4gB,IAAI,CAC7B,IAAI,CAAC5gB,KAAK,CAAC4gB,IAAI,GAEV,IAAI,CAAC/gB,KAAK,CAACG,KAAK,EAAI,IAAI,CAACH,KAAK,CAACG,KAAK,CAAC4gB,IAAI,EAC9C,IAAI,CAAC/gB,KAAK,CAACG,KAAK,CAAC4gB,IAAI,EAMjC,CAIA,SAASsJ,EAAgBzQ,CAAG,CAAEuN,CAAO,CAAEpU,CAAK,EACxC6G,EAAIyB,MAAM,CAAC8L,EAASpU,EACxB,CAQA,SAASuX,IACL,IAsBI1Q,EAtBA5Z,EAAQ,IAAI,CAACA,KAAK,CAClByC,EAAS2nB,AA7tHMrqB,EA6tH4BC,GAASA,EAAQ,IAAI,CAChEkgB,EAAezd,IAAWzC,EACtBA,EAAMuX,WAAW,CACjBvX,EAAMuX,WAAW,EAAI,IAAI,CAAC4I,KAAK,CACnCnd,EAAQhD,EAAMgW,UAAU,CACxB9S,EAASlD,EAAMiW,WAAW,CAC1BsU,EAAS,SAAU3H,CAAO,CAC1BhgB,CAAC,CACDE,CAAC,CACDvE,CAAC,CACD2M,CAAC,CACDsf,CAAC,CACDlsB,CAAC,EACGskB,EAAQzjB,IAAI,CAAC,IAAI,CACrB2D,EACAF,EACArE,EACA2M,EACAsf,EACAlsB,EACJ,EAEI6B,EAAQsC,EAAOtC,KAAK,CAChBsC,EAAOtC,KAAK,EACR,CAAC,EAmDb,OAlDAyZ,EAAMzZ,EAAMghB,SAAS,CAChBhhB,EAAM+V,MAAM,GACb/V,EAAM+V,MAAM,CAAG6S,GAAgBpP,aAAa,CAAC,UAC7CxZ,EAAMsC,MAAM,CAAGzC,EAAM2Y,QAAQ,CACxBgI,KAAK,CAAC,GAAI,EAAG,EAAG3d,EAAOE,GACvBsU,QAAQ,CAAC,2BACToJ,GAAG,CAACV,GACTtG,EAAMzZ,EAAMghB,SAAS,CACjBhhB,EAAM+V,MAAM,CAAC+C,UAAU,CAAC,MACxBjZ,EAAMoD,QAAQ,EACd,CAAC,SAAU,SAAU,OAAQ,MAAM,CAAC1D,OAAO,CAAC,SAAU+Z,CAAE,EACpD6P,GAAiB1P,EAAKH,EAAI8Q,EAC9B,GAEJpqB,EAAM4gB,IAAI,CAAG,WACT5gB,EAAMsC,MAAM,CAACoe,IAAI,CAAC,CACdC,KAAM3gB,EAAM+V,MAAM,CAAC+K,SAAS,CAAC,YACjC,EACJ,EACA9gB,EAAMgO,KAAK,CAAG,WACVyL,EAAIsQ,SAAS,CAAC,EAAG,EAAG/pB,EAAM+V,MAAM,CAAClT,KAAK,CAAE7C,EAAM+V,MAAM,CAAChT,MAAM,EACvDT,IAAWtC,EAAMsC,MAAM,EACvBtC,EAAMsC,MAAM,CAACoe,IAAI,CAAC,CACdC,KAAM2I,CACV,EAER,EACAtpB,EAAMyhB,QAAQ,CAAG5hB,EAAM2Y,QAAQ,CAACiJ,QAAQ,GACxCzhB,EAAMsC,MAAM,CAACyf,IAAI,CAAC/hB,EAAMyhB,QAAQ,GAKhCzhB,EAAM+V,MAAM,CAAClT,KAAK,GAAKA,GACvB7C,CAAAA,EAAM+V,MAAM,CAAClT,KAAK,CAAGA,CAAI,EAEzB7C,EAAM+V,MAAM,CAAChT,MAAM,GAAKA,GACxB/C,CAAAA,EAAM+V,MAAM,CAAChT,MAAM,CAAGA,CAAK,EAE/B/C,EAAMsC,MAAM,CAACoe,IAAI,CAAC,CACdje,EAAG,EACHE,EAAG,EACHE,MAAOA,EACPE,OAAQA,EACRunB,MAAO,uBACP3J,KAAM2I,CACV,GACItpB,EAAMyhB,QAAQ,EACdzhB,EAAMyhB,QAAQ,CAACf,IAAI,CAAC6J,EAA6B1qB,EAAOyC,IAErDmX,CACX,CAIA,SAASkM,IACL,IAAI5lB,EAAS,IAAI,CAAEG,EAAUH,EAAOG,OAAO,CAAEL,EAAQE,EAAOF,KAAK,CAAEa,EAAQX,EAAOW,KAAK,CAAE4C,EAAQvD,EAAOuD,KAAK,CAAEknB,EAAsB3qB,EAAMK,OAAO,CAACF,KAAK,EAAI,CAAC,EAAGyqB,EAAgB,CACxK5d,cAAe2d,EAAoB3d,aAAa,EAAI,CAAA,EACpDC,qBAAsB0d,EAAoB1d,oBAAoB,EAAI,CAAA,EAClEC,UAAWyd,EAAoBzd,SAAS,EAAI,CAAA,CAChD,EAAGO,EAAQvN,EAAOmC,SAAS,CAAC,IAAK,CAAA,GAAOoN,EAAQvP,EAAOmC,SAAS,CAAC,IAAK,CAAA,GAAO2M,EAAU3O,EAAQiC,IAAI,CAAE2M,EAAYpO,EAAMqO,WAAW,GAAIC,EAAOF,EAAUlO,GAAG,CAAEsO,EAAOJ,EAAU/N,GAAG,CAAEoO,EAAY7L,EAAMyL,WAAW,GAAIK,EAAOD,EAAUvO,GAAG,CAAEyO,EAAOF,EAAUpO,GAAG,CAAEqlB,EAAa,CAAC,EAAGC,EAAW,CAAC,CAACtmB,EAAOsmB,QAAQ,CAAEvkB,EAAI5B,EAAQ2U,MAAM,EAAI3U,EAAQ2U,MAAM,CAAC6B,MAAM,CAAEgU,EAAc3qB,EAAO4qB,cAAc,EAAI,IAAMrE,EAAsBpmB,EAAQomB,mBAAmB,CAAEnmB,EAAYD,EAAQC,SAAS,CAAEmW,EAAe0S,GAAqB7oB,GAAYyqB,EAAsBtnB,EAAMkT,YAAY,CAACrW,GAAY0qB,EAAS9qB,EAAO0R,IAAI,CAAE/C,EAAW3O,EAAO4O,aAAa,EAChnB5O,AAAmC,aAAnCA,EAAO4O,aAAa,CAACvI,IAAI,CAAC,KAAsBiH,EAAY,CAAC,CAACnN,EAAQuN,QAAQ,CAAEiY,EAAY3lB,EAAO2lB,SAAS,EAAI,EAAGoF,EAAiBjrB,EAAMK,OAAO,CAAC6qB,OAAO,CAAExE,EAAiBxmB,EAAOwmB,cAAc,CAAE9W,EAAevP,EAAQuP,YAAY,CAAED,EAAS,CAAClC,EAAOoC,EAASrC,EAClQtN,EAAOoC,IAAI,CACVmL,GAASuB,EAAWrC,EAAazM,EAAO0X,WAAW,CACpD3T,IAAsGuH,KAAK,CAACtL,EAAOuI,KAAK,EAAEoP,UAAU,CAACwR,GAAiBhpB,EAAQuX,WAAW,CAAE,MAAO9Y,GAAG,GACrLoB,EAAOuI,KAAK,CAAGke,EAAWtmB,AAA+B,MAA/BA,EAAQumB,kBAAkB,CAAUzmB,EAAQ,IAAI,CAACA,KAAK,EAAI,CAAC,EAAGgrB,EAAejrB,EAAOirB,YAAY,CAAEC,EAAY/qB,EAAQqM,SAAS,CAAGxM,EAAOkrB,SAAS,CAAG,KAAK,EAAGC,EAAappB,GAAKA,GAAK,EAC9M/B,EAAOorB,eAAe,CACtBprB,EAAOqrB,eAAe,AAC1BprB,CAAAA,EAAMsC,MAAM,EACZtC,EAAMsC,MAAM,CAACoe,IAAI,CAAC,CAAEC,KAAM2I,CAAc,GAGxCvpB,CAAAA,EAAOqC,MAAM,EAAIrC,EAAO+mB,KAAK,AAAD,GAC5BuE,AA/RSpJ,GA+RmBliB,GAGhCA,EAAOoX,SAAS,CAAC,QAAS,SAAUpX,EAAOyB,OAAO,CAAG,UAAY,SAAUtB,EAAQwhB,MAAM,CAAE7hB,EAAMuX,WAAW,EAC5GrX,EAAOmX,WAAW,CAAGnX,EAAOigB,KAAK,CACjC8I,GAAqB/oB,EAAQ,UAAW,WAEpCA,EAAOmX,WAAW,CAAG,IACzB,GACA,IAAI9U,EAAS,IAAI,CAACA,MAAM,CAAG,EAAE,CACzBqX,EAAM,IAAI,CAACX,UAAU,GAazB,GAZA/Y,EAAOonB,WAAW,CAAG0B,GACjB7oB,EAAMgO,KAAK,EACXhO,EAAMgO,KAAK,GAUVjO,EAAOyB,OAAO,EAIfqN,EAAQxO,MAAM,CAAG,QACjBR,EAAMK,OAAO,CAAC6qB,OAAO,CAAG9B,GAAkB6B,EAAgB,CACtDQ,WAAY,CACRC,gBAAiB5C,GAAkB,WAAyCjR,UAAU,CAAC,KAAM/Y,GAAG,GAChG6sB,QAAS,MACTC,aAAc,OAClB,EACAnB,MAAO,CACHiB,gBAAiB,OACjBhK,QAAS,CACb,CACJ,GACAniB,IAA8EssB,YAAY,CAACrC,GAC3FxpB,EAAM8rB,WAAW,CAAC,cAClB9rB,EAAMK,OAAO,CAAC6qB,OAAO,CAAGD,GAExBL,EAAc5d,aAAa,EAC3BuH,QAAQO,IAAI,CAAC,oBAGjB,IACIiR,EACA4D,EAEAoC,EACAvd,EACAwX,EACAC,EACAC,EACAxoB,EATA8sB,EAAI,EAGJ9T,GAAUqU,EAQVtX,GAAS,WACDuX,GACApR,EAAIM,SAAS,CAAGvN,EACpBiN,EAAIhI,IAAI,KAGRgI,EAAIK,WAAW,CAAG/Z,EAAOuI,KAAK,CAC9BmR,EAAIlN,SAAS,CAAGrM,EAAQqM,SAAS,CACjCkN,EAAInG,MAAM,GAElB,EAEAuY,GAAY,SAAU7E,CAAO,CAAEpU,CAAK,CAAE2D,CAAO,CAAElG,CAAC,EAClC,IAANga,IACA5Q,EAAIoB,SAAS,GACToQ,GACAxR,CAAAA,EAAIqS,QAAQ,CAAG,OAAM,GAGzBjsB,EAAMksB,QAAQ,EACdhsB,AACI,gCADJA,EAAOG,OAAO,CAAC8rB,SAAS,EAExBpZ,GAAS/S,EAAMksB,QAAQ,CAAC7oB,GAAG,CACvBqT,GACAA,CAAAA,GAAW1W,EAAMksB,QAAQ,CAAC7oB,GAAG,AAAD,GAIhC0P,GAAS/S,EAAM+C,OAAO,CAE1BokB,GAAWnnB,EAAM6C,QAAQ,CACrBkpB,EACAnS,EAAIwB,MAAM,CAAC+L,EAASpU,GAGhBoY,EACAA,EAAavR,EAAKuN,EAASpU,EAAO2D,EAASiT,GAEtCyB,EACLA,EAAUxR,EAAKuN,EAASpU,GAEnBsY,GACLA,EAAUlsB,IAAI,CAACe,EAAQ0Z,EAAKuN,EAASpU,EAAO9Q,EAAGuO,GAMvDga,CAAAA,GAAQ,CAAA,IACEK,IACNpX,KACA+W,EAAI,GAGRb,EAAY,CACRxC,QAASA,EACTpU,MAAOA,EACP2D,QAASA,CACb,CACJ,EAAGmQ,GAAa,AAAC,CAAA,IAAI,CAACxkB,SAAS,CAAC,KAAK7B,MAAM,CAAG,IAAI,CAAC6B,SAAS,CAAC,KAAO,KAAK,CAAA,GACrE,IAAI,CAAChC,OAAO,CAACoN,KAAK,EACjB,EAAA,IAAI,CAACpL,SAAS,CAAC,IAAK,CAAA,GAAM7B,MAAM,EAC7B,IAAI,CAAC6B,SAAS,CAAC,IAAK,CAAA,GAG5B6kB,GAAa,SAAUC,CAAO,CAAEpU,CAAK,CAAEvC,CAAC,EAEpC9S,EAAQipB,EAAWQ,EAAUA,EAAU,IAAMpU,EAIzC0T,GAAuB,CAACF,CAAU,CAAC7oB,EAAM,GACzC6oB,CAAU,CAAC7oB,EAAM,CAAG,CAAA,EAChBsC,EAAMoD,QAAQ,GACd+jB,EAAUtmB,EAAMkD,GAAG,CAAGojB,EACtBpU,EAAQtP,EAAMM,GAAG,CAAGgP,GAExBxQ,EAAO6D,IAAI,CAAC,CACRxD,EAAGikB,EAAAA,IACCA,EAAS,CAAChB,EAAYrV,EAAE,CAE5B2W,QAASA,EACT/C,MAAO+C,EACPpU,MAAOA,EACPvC,EAAGqV,EAAYrV,CACnB,GAER,EAEA4b,AAjbO7J,GAibqB1S,EAAO,SAAUvR,CAAC,CAAEkS,CAAC,EAC7C,IACI5N,EACAE,EACAqkB,EACApU,EACAsZ,EACA1d,EANAqB,EAAiB,AAAuB,KAAA,IAAhBhQ,EAAMtC,KAAK,CAOnC4uB,EAAe,CAAA,EACfC,EAAe,CAAA,EACf7b,EAAK8b,IACL/b,EAAK+b,IACLzb,EAAY,CAAA,EAsGhB,MArGI,CAACf,IACGL,GACA/M,EAAItE,CAAC,CAAC,EAAE,CACRwE,EAAIxE,CAAC,CAAC,EAAE,CACJuR,CAAK,CAACW,EAAI,EAAE,EACZE,CAAAA,EAAKb,CAAK,CAACW,EAAI,EAAE,CAAC,EAAE,AAAD,EAEnBX,CAAK,CAACW,EAAI,EAAE,EACZC,CAAAA,EAAKZ,CAAK,CAACW,EAAI,EAAE,CAAC,EAAE,AAAD,IAIvB5N,EAAItE,EACJwE,EAAI2M,CAAK,CAACe,EAAE,CACRX,CAAK,CAACW,EAAI,EAAE,EACZE,CAAAA,EAAKb,CAAK,CAACW,EAAI,EAAE,AAAD,EAEhBX,CAAK,CAACW,EAAI,EAAE,EACZC,CAAAA,EAAKZ,CAAK,CAACW,EAAI,EAAE,AAAD,GAGpBE,GAAMA,GAAMvB,GAAQuB,GAAMrB,GAC1Bid,CAAAA,EAAe,CAAA,CAAG,EAElB7b,GAAMA,GAAMtB,GAAQsB,GAAMpB,GAC1Bkd,CAAAA,EAAe,CAAA,CAAG,EAGlB1d,GACIc,GACA7M,CAAAA,EAAIxE,EAAEwV,KAAK,CAAC,EAAG,EAAC,EAEpBnF,EAAM7L,CAAC,CAAC,EAAE,CACVA,EAAIA,CAAC,CAAC,EAAE,EAEH0K,IACL5K,EAAItE,EAAEsE,CAAC,CAEP+L,EAAM7L,AADNA,CAAAA,EAAIxE,EAAEyV,MAAM,AAAD,EACDzV,EAAEwE,CAAC,EAEjBupB,EAASvpB,AAAM,OAANA,EAEJ4jB,GACD3V,CAAAA,EAAYjO,GAAKyM,GAAQzM,GAAK0M,CAAG,EAEjC,CAAC6c,GACA,CAAA,AAACzpB,GAAKuM,GAAQvM,GAAKyM,GAAQ0B,GACvBub,GAAgBC,CAAY,IACjCpF,EAAUrjB,KAAK2oB,KAAK,CAAC5rB,EAAMqT,QAAQ,CAACtR,EAAG,CAAA,IACnC4jB,GACI,CAAA,AAAgB,KAAA,IAATP,GACPkB,IAAYpB,CAAU,IACjBlX,GACDF,CAAAA,EAAM7L,CAAAA,EAEN,CAAA,AAAgB,KAAA,IAATojB,GAAwBpjB,EAAIkjB,CAAK,IACxCA,EAASljB,EACTojB,EAAO1V,GAEP,CAAA,AAAgB,KAAA,IAATyV,GACPtX,EAAMH,CAAK,IACXA,EAASG,EACTsX,EAAOzV,IAIX2W,IAAYpB,IAEQ,KAAA,IAATE,IACPlT,EAAQtP,EAAMyQ,QAAQ,CAAC8R,EAAQ,CAAA,GAC/BtP,GAAUjT,EAAMyQ,QAAQ,CAAC1F,EAAQ,CAAA,GACjCwd,GAAU7E,EAAS1Q,EACf3S,KAAK/C,GAAG,CAACgS,EAAOgY,GAAuBhY,EAAO0D,EAC9C3S,KAAK5C,GAAG,CAACwV,GAASqU,GAAuBrU,GAASlG,GACtD0W,GAAWC,EAASpU,EAAOmT,GACvBxP,KAAY3D,GACZmU,GAAWC,EAASzQ,GAASuP,IAGrCA,EAAOC,EAAO,KAAK,EACnBH,EAAcoB,KAKlB6E,GAAU7E,EADVpU,EAAQjP,KAAK2oB,KAAK,CAAChpB,EAAMyQ,QAAQ,CAACpR,EAAG,CAAA,IACX4T,GAASlG,GACnC0W,GAAWC,EAASpU,EAAOvC,KAGnCub,EAAUM,GAAU,CAACzc,EACjBY,EA1eC,KA0ekB,IACftQ,EAAOC,KAAK,EACZD,EAAOC,KAAK,CAAC4gB,IAAI,CACjB7gB,EAAOC,KAAK,CAAC4gB,IAAI,GAEZ7gB,EAAOF,KAAK,CAACG,KAAK,EACvBD,EAAOF,KAAK,CAACG,KAAK,CAAC4gB,IAAI,EACvB7gB,EAAOF,KAAK,CAACG,KAAK,CAAC4gB,IAAI,KAI5B,CAAC/Q,CACZ,EAAG,WACC,IAAI0c,EAAa1sB,EAAM0sB,UAAU,CAC7BC,EAAe3sB,EAAM2sB,YAAY,CACrClZ,KAIAvT,EAAO0sB,WAAW,GACdhC,EAAc5d,aAAa,EAC3BuH,QAAQY,OAAO,CAAC,oBAEpB+T,GAAsBhpB,EAAQ,kBAK1BysB,IACAD,EAAWjC,KAAK,CAACoC,UAAU,CAAG,gBAC9BH,EAAWhL,OAAO,CAAG,EACrB1hB,EAAM2sB,YAAY,CAAG,CAAA,EACrBnD,EAAoB3Q,WAAW,WACvB6T,EAAWI,UAAU,EACrBJ,EAAWI,UAAU,CAACC,WAAW,CAACL,GAEtC1sB,EAAM0sB,UAAU,CAAG1sB,EAAMgtB,WAAW,CAAG,IAC3C,EAAG,MAGP,OAAO9sB,EAAOonB,WAAW,CACzBpnB,EAAOonB,WAAW,EAGtB,EAAGtnB,EAAM2Y,QAAQ,CAACC,SAAS,CAAG1W,OAAOC,SAAS,CAAG,KAAK,GAC1D,CAIA,SAAS8qB,EAAuBrT,CAAG,CAAEuN,CAAO,CAAEpU,CAAK,CAAE9Q,CAAC,EAClD2X,EAAIwB,MAAM,CAAC+L,EAASpU,GACpB6G,EAAIqB,GAAG,CAACkM,EAASpU,EAAO9Q,EAAG,EAAG,EAAI6B,KAAKoX,EAAE,CAAE,CAAA,EAC/C,CAKA,SAASgS,EAAuBtT,CAAG,CAAEuN,CAAO,CAAEpU,CAAK,CAAE9Q,CAAC,EAClD2X,EAAImQ,IAAI,CAAC5C,EAAUllB,EAAG8Q,EAAQ9Q,EAAGA,AAAI,EAAJA,EAAOA,AAAI,EAAJA,EAC5C,CAIA,SAASkrB,IACL,IAAIntB,EAAQ,IAAI,CAACA,KAAK,CAClB4Z,EAAM,IAAI,CAACX,UAAU,GACrB7V,EAAW,IAAI,CAACpD,KAAK,CAACoD,QAAQ,CAC9BvC,EAAQ,IAAI,CAACA,KAAK,CAClB4C,EAAQ,IAAI,CAACA,KAAK,CAClBmW,GAEA,IAAI,CAACrX,MAAM,CAAC7C,OAAO,CAAC,SAAUkT,CAAK,EAC/B,IACIE,EADAC,EAAQH,EAAMG,KAAK,CAEvB,GAAI,AAAiB,KAAA,IAAVA,GACP,CAACC,MAAMD,IACPH,AAAY,OAAZA,EAAM9P,CAAC,EACP8W,EAAK,CACL,IAAIhZ,EAAKgS,EAAMK,SAAS,EAAI,CAAC,EACzBzR,EAAKZ,EAAGgC,CAAC,CACTA,EAAIpB,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EACxB2R,EAAKvS,EAAGkC,CAAC,CACTA,EAAIqQ,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EACxBE,EAAKzS,EAAGoC,KAAK,CACbA,EAAQqQ,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EAC5BC,EAAK1S,EAAGsC,MAAM,CACdA,EAASoQ,AAAO,KAAK,IAAZA,EAAgB,EAAIA,CAOjCsG,CAAAA,EAAIM,SAAS,CAAGpH,CANX9S,EAAMuT,UAAU,CAILX,EAAM1S,MAAM,CAACsT,YAAY,CAACZ,GAH1BA,EAAM1S,MAAM,CAACyR,YAAY,CAACiB,IAKhBhB,IAAI,CAC1BxO,EACAwW,EAAIuB,QAAQ,CAAC1X,EAAMM,GAAG,CAAGjB,EAAIjC,EAAM0C,IAAI,CAAE1C,EAAMkD,GAAG,CAAGnB,EAAIa,EAAMJ,GAAG,CAAE,CAACH,EAAQ,CAACF,GAG9E4W,EAAIuB,QAAQ,CAACvY,EAAI/B,EAAM0C,IAAI,CAAET,EAAIW,EAAMJ,GAAG,CAAEL,EAAOE,EAE3D,CACJ,GACA,IAAI,CAAC0pB,WAAW,IAGhB,IAAI,CAAC5sB,KAAK,CAAC8rB,WAAW,CAAC,6EAG/B,CA/fAluB,EAAYwvB,OAAO,CA/CnB,SAAiBnN,CAAU,CAAEoN,CAAW,CAAEC,CAAW,EACjD,IAAIC,EAAcF,EAAYpuB,SAAS,CACvC,GAAI,CAACsuB,EAAYC,YAAY,CAAE,CAC3B,IAAIC,EAAeH,EAAYI,IAAI,CAC/BC,EAAiBL,EAAYM,MAAM,CACnCC,EAAiBP,EAAY1R,MAAM,CACnCkS,EAAkBR,EAAYS,OAAO,CACrCC,EAAkBV,EAAYW,OAAO,CAUzC,GARAhO,EAAWhhB,SAAS,CAACivB,SAAS,CAAC9nB,IAAI,CAAC,SAAUpG,CAAK,EAC/CipB,GAAqBjpB,EAAO,UAAWiqB,GACvChB,GAAqBjpB,EAAO,SAAUgqB,EAC1C,GACAuD,EAAYX,WAAW,CAAGzC,EAC1BoD,EAAYnC,SAAS,CAAGf,EACxBkD,EAAYtU,UAAU,CAAGqR,EACzBiD,EAAYC,YAAY,CAAG1H,EACvB2H,EAAc,CACd,IAAIU,EAAYV,EAAaxuB,SAAS,AACtCkvB,CAAAA,EAAUhD,YAAY,CAAGzB,EACzByE,EAAUvc,IAAI,CAAG,CAAA,EACjBuc,EAAUvW,WAAW,CAAG,CAAA,EACxBuW,EAAU3H,QAAQ,CAAG,CAAA,CACzB,CACA,GAAImH,EAAgB,CAChB,IAAIS,EAAcT,EAAe1uB,SAAS,AAC1CmvB,CAAAA,EAAY7C,eAAe,CAAG3B,EAC9BwE,EAAYtD,cAAc,CAAG,CACjC,CACA,GAAI+C,EAAgB,CAChB,IAAIQ,EAAcR,EAAe5uB,SAAS,AAC1CovB,CAAAA,EAAYlD,YAAY,CAAGrB,EAC3BuE,EAAYzc,IAAI,CAAG,CAAA,EACnByc,EAAY7H,QAAQ,CAAG,CAAA,CAC3B,CAKA,GAJIsH,GAEAxE,GADmBwE,EAAgB7uB,SAAS,CACb,aAAckuB,GAE7Ca,EAAiB,CACjB,IAAIM,EAAeN,EAAgB/uB,SAAS,AAC5CqvB,CAAAA,EAAa/C,eAAe,CAAG0B,EAC/BqB,EAAahD,eAAe,CAAG4B,EAC/BoB,EAAa1c,IAAI,CAAG,CAAA,CACxB,CACJ,CACJ,CAigBJ,EAAGhU,GAAgBA,CAAAA,EAAc,CAAC,CAAA,GAML,IAAI2wB,GAA0B3wB,EAOvD4wB,GAAKjvB,GAMTivB,CAAAA,GAAEC,eAAe,CAAG,WAChBF,GAAuBnB,OAAO,CAACoB,GAAEE,KAAK,CAAEF,GAAEG,MAAM,CAAEH,GAAElB,WAAW,CACnE,EAC6B,IAAIjuB,GAAqBE,IAG5C,OADYH,EAAoB,OAAU,AAE3C,GAET"}