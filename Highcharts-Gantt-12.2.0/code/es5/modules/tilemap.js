!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("highcharts"),require("highcharts").SeriesRegistry,require("highcharts").Color):"function"==typeof define&&define.amd?define("highcharts/modules/tilemap",[["highcharts/highcharts"],["highcharts/highcharts","SeriesRegistry"],["highcharts/highcharts","Color"]],e):"object"==typeof exports?exports["highcharts/modules/tilemap"]=e(require("highcharts"),require("highcharts").SeriesRegistry,require("highcharts").Color):t.Highcharts=e(t.Highcharts,t.Highcharts.SeriesRegistry,t.Highcharts.Color)}(this,function(t,e,o){return function(){"use strict";var r,i,n,a={512:function(t){t.exports=e},620:function(t){t.exports=o},944:function(e){e.exports=t}},s={};function l(t){var e=s[t];if(void 0!==e)return e.exports;var o=s[t]={exports:{}};return a[t](o,o.exports,l),o.exports}l.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return l.d(e,{a:e}),e},l.d=function(t,e){for(var o in e)l.o(e,o)&&!l.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},l.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};var h={};l.d(h,{default:function(){return B}});var p=l(944),c=l.n(p),u=l(512),d=l.n(u),f=l(620),g=l.n(f)().parse,y=c().addEvent,x=c().extend,v=c().merge,b=c().pick,m=c().splat;!function(t){var e;function o(){var t=this,o=this.userOptions;this.colorAxis=[],o.colorAxis&&(o.colorAxis=m(o.colorAxis),o.colorAxis.map(function(o){return new e(t,o)}))}function r(t){var e,o,r=this,i=this.chart.colorAxis||[],n=function(e){var o=t.allItems.indexOf(e);-1!==o&&(r.destroyItem(t.allItems[o]),t.allItems.splice(o,1))},a=[];for(i.forEach(function(t){(null==(e=t.options)?void 0:e.showInLegend)&&(e.dataClasses&&e.visible?a=a.concat(t.getDataClassLegendSymbols()):e.visible&&a.push(t),t.series.forEach(function(t){(!t.options.showInLegend||e.dataClasses)&&("point"===t.options.legendType?t.points.forEach(function(t){n(t)}):n(t))}))}),o=a.length;o--;)t.allItems.unshift(a[o])}function i(t){t.visible&&t.item.legendColor&&t.item.legendItem.symbol.attr({fill:t.item.legendColor})}function n(t){var e;null===(e=this.chart.colorAxis)||void 0===e||e.forEach(function(e){e.update({},t.redraw)})}function a(){var t;((null===(t=this.chart.colorAxis)||void 0===t?void 0:t.length)||this.colorAttribs)&&this.translateColors()}function s(){var t=this.axisTypes;t?-1===t.indexOf("colorAxis")&&t.push("colorAxis"):this.axisTypes=["colorAxis"]}function l(t){var e=this,o=t?"show":"hide";e.visible=e.options.visible=!!t,["graphic","dataLabel"].forEach(function(t){e[t]&&e[t][o]()}),this.series.buildKDTree()}function h(){var t=this,e=this.getPointsCollection(),o=this.options.nullColor,r=this.colorAxis,i=this.colorKey;e.forEach(function(e){var n=e.getNestedProperty(i),a=e.options.color||(e.isNull||null===e.value?o:r&&void 0!==n?r.toColor(n,e):e.color||t.color);a&&e.color!==a&&(e.color=a,"point"===t.options.legendType&&e.legendItem&&e.legendItem.label&&t.chart.legend.colorizeItem(e,e.visible))})}function p(){this.elem.attr("fill",g(this.start).tweenTo(g(this.end),this.pos),void 0,!0)}function c(){this.elem.attr("stroke",g(this.start).tweenTo(g(this.end),this.pos),void 0,!0)}t.compose=function(t,u,d,f,g){var m,A,P=u.prototype,M=d.prototype,L=g.prototype;P.collectionsWithUpdate.includes("colorAxis")||(e=t,P.collectionsWithUpdate.push("colorAxis"),P.collectionsWithInit.colorAxis=[P.addColorAxis],y(u,"afterCreateAxes",o),A=(m=u).prototype.createAxis,m.prototype.createAxis=function(t,o){if("colorAxis"!==t)return A.apply(this,arguments);var r=new e(this,v(o.axis,{index:this[t].length,isX:!1}));return this.isDirtyLegend=!0,this.axes.forEach(function(t){t.series=[]}),this.series.forEach(function(t){t.bindAxes(),t.isDirtyData=!0}),b(o.redraw,!0)&&this.redraw(o.animation),r},M.fillSetter=p,M.strokeSetter=c,y(f,"afterGetAllItems",r),y(f,"afterColorizeItem",i),y(f,"afterUpdate",n),x(L,{optionalAxis:"colorAxis",translateColors:h}),x(L.pointClass.prototype,{setVisible:l}),y(g,"afterTranslate",a,{order:1}),y(g,"bindAxes",s))},t.pointSetVisible=l}(n||(n={}));var A=n,P=(r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function o(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),M=d().series.prototype.pointClass,L=d().seriesTypes.heatmap.prototype.pointClass,S=c().extend,C=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return P(e,t),e.prototype.haloPath=function(){return this.series.tileShape.haloPath.apply(this,arguments)},e}(L);S(C.prototype,{setState:M.prototype.setState,setVisible:A.pointSetVisible});var w={marker:null,states:{hover:{halo:{enabled:!0,size:2,opacity:.5,attributes:{zIndex:3}}}},pointPadding:2,tileShape:"hexagon"},T=c().noop,O=d().seriesTypes,_=O.heatmap,D=O.scatter,E=c().clamp,I=c().pick;function z(t,e,o){var r=t.options;return{xPad:-((r.colsize||1)/e),yPad:-((r.rowsize||1)/o)}}var j={hexagon:{alignDataLabel:D.prototype.alignDataLabel,getSeriesPadding:function(t){return z(t,3,2)},haloPath:function(t){if(!t)return[];var e=this.tileEdges;return[["M",e.x2-t,e.y1+t],["L",e.x3+t,e.y1+t],["L",e.x4+1.5*t,e.y2],["L",e.x3+t,e.y3-t],["L",e.x2-t,e.y3-t],["L",e.x1-1.5*t,e.y2],["Z"]]},translate:function(){var t,e,o=this.options,r=this.xAxis,i=this.yAxis,n=o.pointPadding||0,a=(o.colsize||1)/3,s=(o.rowsize||1)/2;this.generatePoints();for(var l=0,h=this.points;l<h.length;l++){var p=h[l],c=E(Math.floor(r.len-r.translate(p.x-2*a,0,1,0,1)),-r.len,2*r.len),u=E(Math.floor(r.len-r.translate(p.x-a,0,1,0,1)),-r.len,2*r.len),d=E(Math.floor(r.len-r.translate(p.x+a,0,1,0,1)),-r.len,2*r.len),f=E(Math.floor(r.len-r.translate(p.x+2*a,0,1,0,1)),-r.len,2*r.len),g=E(Math.floor(i.translate(p.y-s,0,1,0,1)),-i.len,2*i.len),y=E(Math.floor(i.translate(p.y,0,1,0,1)),-i.len,2*i.len),x=E(Math.floor(i.translate(p.y+s,0,1,0,1)),-i.len,2*i.len),v=null!==(t=p.pointPadding)&&void 0!==t?t:n,b=v*Math.abs(u-c)/Math.abs(x-y),m=r.reversed?-b:b,A=r.reversed?-v:v,P=i.reversed?-v:v;p.x%2&&(e=e||Math.round(Math.abs(x-g)/2)*(i.reversed?-1:1),g+=e,y+=e,x+=e),p.plotX=p.clientX=(u+d)/2,p.plotY=y,p.tileEdges={x1:c+=m+A,x2:u+=A,x3:d-=A,x4:f-=m+A,y1:g-=P,y2:y,y3:x+=P},p.shapeType="path",p.shapeArgs={d:[["M",u,g],["L",d,g],["L",f,y],["L",d,x],["L",u,x],["L",c,y],["Z"]]}}this.translateColors()}},diamond:{alignDataLabel:D.prototype.alignDataLabel,getSeriesPadding:function(t){return z(t,2,2)},haloPath:function(t){if(!t)return[];var e=this.tileEdges;return[["M",e.x2,e.y1+t],["L",e.x3+t,e.y2],["L",e.x2,e.y3-t],["L",e.x1-t,e.y2],["Z"]]},translate:function(){var t,e=this.options,o=this.xAxis,r=this.yAxis,i=e.pointPadding||0,n=e.colsize||1,a=(e.rowsize||1)/2;this.generatePoints();for(var s=0,l=this.points;s<l.length;s++){var h=l[s],p=E(Math.round(o.len-o.translate(h.x-n,0,1,0,0)),-o.len,2*o.len),c=E(Math.round(o.len-o.translate(h.x+n,0,1,0,0)),-o.len,2*o.len),u=E(Math.round(r.translate(h.y-a,0,1,0,0)),-r.len,2*r.len),d=E(Math.round(r.translate(h.y,0,1,0,0)),-r.len,2*r.len),f=E(Math.round(r.translate(h.y+a,0,1,0,0)),-r.len,2*r.len),g=E(Math.round(o.len-o.translate(h.x,0,1,0,0)),-o.len,2*o.len),y=I(h.pointPadding,i),x=y*Math.abs(g-p)/Math.abs(f-d),v=o.reversed?-x:x,b=r.reversed?-y:y;h.x%2&&(t=Math.abs(f-u)/2*(r.reversed?-1:1),u+=t,d+=t,f+=t),h.plotX=h.clientX=g,h.plotY=d,h.tileEdges={x1:p+=v,x2:g,x3:c-=v,y1:u-=b,y2:d,y3:f+=b},h.shapeType="path",h.shapeArgs={d:[["M",g,u],["L",c,d],["L",g,f],["L",p,d],["Z"]]}}this.translateColors()}},circle:{alignDataLabel:D.prototype.alignDataLabel,getSeriesPadding:function(t){return z(t,2,2)},haloPath:function(t){return D.prototype.pointClass.prototype.haloPath.call(this,t+(t&&this.radius))},translate:function(){var t,e,o,r,i=this.options,n=this.xAxis,a=this.yAxis,s=i.pointPadding||0,l=(i.rowsize||1)/2,h=i.colsize||1,p=!1;this.generatePoints();for(var c=0,u=this.points;c<u.length;c++){var d=u[c],f=E(Math.round(n.len-n.translate(d.x,0,1,0,0)),-n.len,2*n.len),g=s,y=!1,x=E(Math.round(a.translate(d.y,0,1,0,0)),-a.len,2*a.len);void 0!==d.pointPadding&&(g=d.pointPadding,y=!0,p=!0),(!r||p)&&(o=Math.floor(Math.sqrt((t=Math.abs(E(Math.floor(n.len-n.translate(d.x+h,0,1,0,0)),-n.len,2*n.len)-f))*t+(e=Math.abs(E(Math.floor(a.translate(d.y+l,0,1,0,0)),-a.len,2*a.len)-x))*e)/2),r=Math.min(t,o,e)-g,p&&!y&&(p=!1)),d.x%2&&(x+=e*(a.reversed?-1:1)),d.plotX=d.clientX=f,d.plotY=x,d.radius=r,d.shapeType="circle",d.shapeArgs={x:f,y:x,r:r}}this.translateColors()}},square:{alignDataLabel:_.prototype.alignDataLabel,translate:_.prototype.translate,getSeriesPadding:T,haloPath:_.prototype.pointClass.prototype.haloPath}},q=(i=function(t,e){return(i=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),X=c().composed,k=c().noop,F=d().seriesTypes,H=F.column,R=F.heatmap,U=F.scatter,V=c().addEvent,Z=c().extend,W=c().merge,Y=c().pushUnique;function K(){if(!this.recomputingForTilemap&&"colorAxis"!==this.coll){var t=this,e=t.series.map(function(e){return e.getSeriesPixelPadding&&e.getSeriesPixelPadding(t)}).reduce(function(t,e){return(t&&t.padding)>(e&&e.padding)?t:e},void 0)||{padding:0,axisLengthFactor:1},o=Math.round(e.padding*e.axisLengthFactor);e.padding&&(t.len-=o,t.recomputingForTilemap=!0,t.setAxisTranslation(),delete t.recomputingForTilemap,t.minPixelPadding+=e.padding,t.len+=o)}}var N=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return q(e,t),e.compose=function(t){Y(X,"TilemapSeries")&&V(t,"afterSetAxisTranslation",K)},e.prototype.alignDataLabel=function(){return this.tileShape.alignDataLabel.apply(this,arguments)},e.prototype.drawPoints=function(){H.prototype.drawPoints.call(this);for(var t=0,e=this.points;t<e.length;t++){var o=e[t];o.graphic&&o.graphic[this.chart.styledMode?"css":"animate"](this.colorAttribs(o))}},e.prototype.getSeriesPixelPadding=function(t){var e=t.isXAxis,o=this.tileShape.getSeriesPadding(this);if(!o)return{padding:0,axisLengthFactor:1};var r=Math.round(t.translate(e?2*o.xPad:o.yPad,0,1,0,1)),i=Math.round(t.translate(e?o.xPad:0,0,1,0,1));return{padding:(t.single?Math.abs(r-i)/2:Math.abs(r-i))||0,axisLengthFactor:e?2:1.1}},e.prototype.setOptions=function(){var e=t.prototype.setOptions.apply(this,arguments);return this.tileShape=j[e.tileShape],e},e.prototype.translate=function(){return this.tileShape.translate.apply(this,arguments)},e.defaultOptions=W(R.defaultOptions,w),e}(R);Z(N.prototype,{getSymbol:k,markerAttribs:U.prototype.markerAttribs,pointAttribs:H.prototype.pointAttribs,pointClass:C}),d().registerSeriesType("tilemap",N);var G=c();N.compose(G.Axis);var B=c();return h.default}()});