!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("highcharts"),require("highcharts").SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/modules/tiledwebmap",[["highcharts/highcharts"],["highcharts/highcharts","SeriesRegistry"]],t):"object"==typeof exports?exports["highcharts/modules/tiledwebmap"]=t(require("highcharts"),require("highcharts").SeriesRegistry):e.Highcharts=t(e.Highcharts,e.Highcharts.SeriesRegistry)}(this,function(e,t){return function(){"use strict";var o,r={512:function(e){e.exports=t},944:function(t){t.exports=e}},i={};function a(e){var t=i[e];if(void 0!==t)return t.exports;var o=i[e]={exports:{}};return r[e](o,o.exports,a),o.exports}a.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return a.d(t,{a:t}),t},a.d=function(e,t){for(var o in t)a.o(t,o)&&!a.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},a.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)};var s={};a.d(s,{default:function(){return w}});var n=a(944),p=a.n(n),m={Esri:function(){this.defaultCredits="Tiles &copy; Esri &mdash; Source: Esri, DeLorme, NAVTEQ, USGS,  Intermap, iPC, NRCAN, Esri Japan, METI, Esri China (Hong Kong), Esri (Thailand), TomTom, 2012",this.initialProjectionName="WebMercator",this.themes={WorldStreetMap:{url:"https://server.arcgisonline.com/ArcGIS/rest/services/World_Street_Map/MapServer/tile/{z}/{y}/{x}",minZoom:0,maxZoom:20},DeLorme:{url:"https://server.arcgisonline.com/ArcGIS/rest/services/Specialty/DeLorme_World_Base_Map/MapServer/tile/{z}/{y}/{x}",minZoom:1,maxZoom:11,credits:"Tiles &copy; Esri &mdash; Copyright: &copy;2012 DeLorme"},WorldTopoMap:{url:"https://server.arcgisonline.com/ArcGIS/rest/services/World_Topo_Map/MapServer/tile/{z}/{y}/{x}",minZoom:0,maxZoom:20,credits:"Tiles &copy; Esri &mdash; Esri, DeLorme, NAVTEQ, TomTom, Intermap, iPC, USGS, FAO, NPS, NRCAN, GeoBase, Kadaster NL, Ordnance Survey, Esri Japan, METI, Esri China (Hong Kong), and the GIS User Community"},WorldImagery:{url:"https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}",minZoom:0,maxZoom:20,credits:"Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community"},WorldTerrain:{url:"https://server.arcgisonline.com/ArcGIS/rest/services/World_Terrain_Base/MapServer/tile/{z}/{y}/{x}",minZoom:0,maxZoom:13,credits:"Tiles &copy; Esri &mdash; Source: USGS, Esri, TANA, DeLorme, and NPS"},WorldShadedRelief:{url:"https://server.arcgisonline.com/ArcGIS/rest/services/World_Shaded_Relief/MapServer/tile/{z}/{y}/{x}",minZoom:0,maxZoom:13,credits:"Tiles &copy; Esri &mdash; Source: Esri"},WorldPhysical:{url:"https://server.arcgisonline.com/ArcGIS/rest/services/World_Physical_Map/MapServer/tile/{z}/{y}/{x}",minZoom:0,maxZoom:8,credits:"Tiles &copy; Esri &mdash; Source: US National Park Service"},NatGeoWorldMap:{url:"https://server.arcgisonline.com/ArcGIS/rest/services/NatGeo_World_Map/MapServer/tile/{z}/{y}/{x}",minZoom:0,maxZoom:16,credits:"Tiles &copy; Esri &mdash; National Geographic, Esri, DeLorme, NAVTEQ, UNEP-WCMC, USGS, NASA, ESA, METI, NRCAN, GEBCO, NOAA, iPC"},WorldGrayCanvas:{url:"https://server.arcgisonline.com/ArcGIS/rest/services/Canvas/World_Light_Gray_Base/MapServer/tile/{z}/{y}/{x}",minZoom:0,maxZoom:16,credits:"Tiles &copy; Esri &mdash; Esri, DeLorme, NAVTEQ"}}},LimaLabs:function(){this.defaultCredits='Map data &copy;2023 <a href="https://maps.lima-labs.com/">LimaLabs</a>',this.initialProjectionName="WebMercator",this.requiresApiKey=!0,this.themes={Standard:{url:"https://cdn.lima-labs.com/{zoom}/{x}/{y}.png?api={apikey}",minZoom:0,maxZoom:20}}},OpenStreetMap:function(){this.defaultCredits='Map data &copy2023 <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a>',this.initialProjectionName="WebMercator",this.subdomains=["a","b","c"],this.themes={Standard:{url:"https://tile.openstreetmap.org/{zoom}/{x}/{y}.png",minZoom:0,maxZoom:19},Hot:{url:"https://{s}.tile.openstreetmap.fr/hot/{z}/{x}/{y}.png",minZoom:0,maxZoom:19},OpenTopoMap:{url:"https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png",minZoom:0,maxZoom:17,credits:'Map data: &copy; <a href="https://www.openstreetmap.org/copyright">\n                OpenStreetMap</a> contributors, <a href="https://viewfinderpanoramas.org">SRTM</a> \n                | Map style: &copy; <a href="https://opentopomap.org">OpenTopoMap</a> \n                (<a href="https://creativecommons.org/licenses/by-sa/3.0/">CC-BY-SA</a>)'}}},Stamen:function(){this.defaultCredits='&copy; Map tiles by <a href="https://stamen.com">Stamen Design</a>, under <a href="https://creativecommons.org/licenses/by/3.0">CC BY 3.0</a>. Data by <a href="https://openstreetmap.org">OpenStreetMap</a>, under <a href="https://www.openstreetmap.org/copyright">ODbL</a>',this.initialProjectionName="WebMercator",this.subdomains=["a","b","c","d"],this.themes={Toner:{url:"https://stamen-tiles-{s}.a.ssl.fastly.net/toner/{z}/{x}/{y}.png",minZoom:0,maxZoom:20},TonerBackground:{url:"https://stamen-tiles-{s}.a.ssl.fastly.net/toner-background/{z}/{x}/{y}.png",minZoom:0,maxZoom:20},TonerLite:{url:"https://stamen-tiles-{s}.a.ssl.fastly.net/toner-lite/{z}/{x}/{y}.png",minZoom:0,maxZoom:20},Terrain:{url:"https://stamen-tiles-{s}.a.ssl.fastly.net/terrain/{z}/{x}/{y}.png",minZoom:0,maxZoom:18},TerrainBackground:{url:"https://stamen-tiles-{s}.a.ssl.fastly.net/terrain-background/{z}/{x}/{y}.png",minZoom:0,maxZoom:18},Watercolor:{url:"https://stamen-tiles-{s}.a.ssl.fastly.net/watercolor/{z}/{x}/{y}.png",minZoom:1,maxZoom:16,credits:'&copy Map tiles by <a href="https://stamen.com">Stamen Design</a>, under <a href="https://creativecommons.org/licenses/by/3.0">CC BY 3.0</a>. Data by <a href="https://openstreetmap.org">OpenStreetMap</a>, under <a href="https://creativecommons.org/licenses/by-sa/3.0">CC BY SA</a>'}}},Thunderforest:function(){this.defaultCredits='Maps &copy <a href="https://www.thunderforest.com">Thunderforest</a>, Data &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap contributors</a>',this.initialProjectionName="WebMercator",this.requiresApiKey=!0,this.subdomains=["a","b","c"],this.themes={OpenCycleMap:{url:"https://{s}.tile.thunderforest.com/cycle/{z}/{x}/{y}.png?apikey={apikey}",minZoom:0,maxZoom:22},Transport:{url:"https://{s}.tile.thunderforest.com/transport/{z}/{x}/{y}.png?apikey={apikey}",minZoom:0,maxZoom:22},TransportDark:{url:"https://{s}.tile.thunderforest.com/transport-dark/{z}/{x}/{y}.png?apikey={apikey}",minZoom:0,maxZoom:22},SpinalMap:{url:"https://{s}.tile.thunderforest.com/spinal-map/{z}/{x}/{y}.png?apikey={apikey}",minZoom:0,maxZoom:22},Landscape:{url:"https://{s}.tile.thunderforest.com/landscape/{z}/{x}/{y}.png?apikey={apikey}",minZoom:0,maxZoom:22},Outdoors:{url:"https://{s}.tile.thunderforest.com/outdoors/{z}/{x}/{y}.png?apikey={apikey}",minZoom:0,maxZoom:22},Pioneer:{url:"https://{s}.tile.thunderforest.com/pioneer/{z}/{x}/{y}.png?apikey={apikey}",minZoom:0,maxZoom:22},MobileAtlas:{url:"https://{s}.tile.thunderforest.com/mobile-atlas/{z}/{x}/{y}.png?apikey={apikey}",minZoom:0,maxZoom:22},Neighbourhood:{url:"https://{s}.tile.thunderforest.com/neighbourhood/{z}/{x}/{y}.png?apikey={apikey}",minZoom:0,maxZoom:22}}},USGS:function(){this.defaultCredits='Tiles courtesy of the <a href="https://usgs.gov/">U.S. GeologicalSurvey</a>',this.initialProjectionName="WebMercator",this.themes={USTopo:{url:"https://basemap.nationalmap.gov/arcgis/rest/services/USGSTopo/MapServer/tile/{z}/{y}/{x}",minZoom:0,maxZoom:20},USImagery:{url:"https://basemap.nationalmap.gov/arcgis/rest/services/USGSImageryOnly/MapServer/tile/{z}/{y}/{x}",minZoom:0,maxZoom:20},USImageryTopo:{url:"https://basemap.nationalmap.gov/arcgis/rest/services/USGSImageryTopo/MapServer/tile/{z}/{y}/{x}",minZoom:0,maxZoom:20}}}},l=a(512),c=a.n(l),h={states:{inactive:{enabled:!1}}},d=(o=function(e,t){return(o=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var o in t)t.hasOwnProperty(o)&&(e[o]=t[o])})(e,t)},function(e,t){function r(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),u=p().composed,y=c().seriesTypes.map,f=p().addEvent,g=p().defined,v=p().error,x=p().merge,M=p().pick,S=p().pushUnique;function T(e){var t=e.geoBounds,o=(e.chart.options.series||[]).filter(function(e){return"tiledwebmap"===e.type})[0];if(o&&o.provider&&o.provider.type&&!o.provider.url){var r=m[o.provider.type];if(g(r)){var i=new r().initialProjectionName;if(t){var a=t.x1,s=t.y1,n=t.x2,p=t.y2;this.recommendedMapView={projection:{name:i,parallels:[s,p],rotation:[-(a+n)/2]}}}else this.recommendedMapView={projection:{name:i},minZoom:0};return!1}v("Highcharts warning: Tiles Provider not defined in the Provider Registry.",!1)}return!0}var b=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.redrawTiles=!1,t.isAnimating=!1,t}return d(t,e),t.compose=function(e){S(u,"TiledWebMapSeries")&&f(e,"onRecommendMapView",T)},t.prototype.lonLatToTile=function(e,t){var o=e.lon,r=e.lat;return{x:Math.floor((o+180)/360*Math.pow(2,t)),y:Math.floor((1-Math.log(Math.tan(r*Math.PI/180)+1/Math.cos(r*Math.PI/180))/Math.PI)/2*Math.pow(2,t))}},t.prototype.tileToLonLat=function(e,t,o){var r=e/Math.pow(2,o)*360-180,i=Math.PI-2*Math.PI*t/Math.pow(2,o);return{lon:r,lat:180/Math.PI*Math.atan(.5*(Math.exp(i)-Math.exp(-i)))}},t.prototype.drawPoints=function(){var e,t=this.chart,o=t.mapView;if(o){var r=this.tiles=this.tiles||{},i=this.transformGroups=this.transformGroups||[],a=this,s=this.options.provider,n=o.zoom,p=M(o.projection.options.rotation&&o.projection.options.rotation[0],0),l=200*!t.renderer.forExport,c=function(e){for(var t=function(t){parseFloat(t)===(o.zoom<0?0:Math.floor(o.zoom))||a.minZoom&&(o.zoom<0?0:Math.floor(o.zoom))<a.minZoom&&parseFloat(t)===a.minZoom||a.maxZoom&&(o.zoom<0?0:Math.floor(o.zoom))>a.maxZoom&&parseFloat(t)===a.maxZoom?Object.keys(r[t].tiles).forEach(function(o,i){r[t].tiles[o].animate({opacity:1},{duration:e},function(){i===Object.keys(r[t].tiles).length-1&&(r[t].isActive=!0)})}):Object.keys(r[t].tiles).forEach(function(o,i){r[t].tiles[o].animate({opacity:0},{duration:e,defer:e/2},function(){r[t].tiles[o].destroy(),delete r[t].tiles[o],i===Object.keys(r[t].tiles).length-1&&(r[t].isActive=!1,r[t].loaded=!1)})})},i=0,s=Object.keys(r);i<s.length;i++)t(s[i])},h=n<0?0:Math.floor(n),d=Math.pow(2,h),u=.638436911716859*Math.pow(2,n)/(.638436911716859*Math.pow(2,h)),y=256*u;if(s&&(s.type||s.url)){if(s.type&&!s.url){var f=m[s.type];if(!g(f)){v("Highcharts warning: Tiles Provider '"+s.type+"' not defined in the ProviderRegistry.",!1);return}var x=new f,S=x.initialProjectionName,T=void 0,b="";if(s.theme&&g(x.themes[s.theme]))T=x.themes[s.theme];else{var Z=Object.keys(x.themes)[0];T=x.themes[Z],v("Highcharts warning: The Tiles Provider's Theme '"+s.theme+"' is not defined in the Provider definition - falling back to '"+Z+"'.",!1)}s.subdomain&&x.subdomains&&-1!==x.subdomains.indexOf(s.subdomain)?b=s.subdomain:g(x.subdomains)&&-1!==T.url.indexOf("{s}")&&(b=M(x.subdomains&&x.subdomains[0],""),v("Highcharts warning: The Tiles Provider's Subdomain '"+s.subdomain+"' is not defined in the Provider definition - falling back to '"+b+"'.",!1)),x.requiresApiKey&&(s.apiKey?T.url=T.url.replace("{apikey}",s.apiKey):(v("Highcharts warning: The Tiles Provider requires API Key to use tiles, use provider.apiKey to provide a token.",!1),T.url=T.url.replace("?apikey={apikey}",""))),s.url=T.url.replace("{s}",b),this.minZoom=T.minZoom,this.maxZoom=T.maxZoom;var w=M(t.userOptions.credits&&t.userOptions.credits.text,"Highcharts.com "+M(T.credits,x.defaultCredits));t.credits?t.credits.update({text:w}):t.addCredits({text:w,style:M(null===(e=t.options.credits)||void 0===e?void 0:e.style,{})}),o.projection.options.name!==S&&v("Highcharts warning: The set projection is different than supported by Tiles Provider.",!1)}else o.projection.options.name||v("Highcharts warning: The set projection is different than supported by Tiles Provider.",!1);if(g(this.minZoom)&&h<this.minZoom?(d=Math.pow(2,h=this.minZoom),y=256*(u=.638436911716859*Math.pow(2,n)/(.638436911716859*Math.pow(2,h)))):g(this.maxZoom)&&h>this.maxZoom&&(d=Math.pow(2,h=this.maxZoom),y=256*(u=.638436911716859*Math.pow(2,n)/(.638436911716859*Math.pow(2,h)))),o.projection&&o.projection.def){o.projection.hasCoordinates=!0,i[h]||(i[h]=t.renderer.g().add(this.group));var j=function(e,n,p,m,h){var u=e%d,f=n%d,g=u<0?u+d:u,v=f<0?f+d:f;if(!r["".concat(p)].tiles[""+e+",".concat(n)]&&s.url){var x=s.url.replace("{x}",g.toString()).replace("{y}",v.toString()).replace("{zoom}",p.toString()).replace("{z}",p.toString());r[p].loaded=!1,r["".concat(p)].tiles[""+e+",".concat(n)]=t.renderer.image(x,e*y-m,n*y-h,y,y).attr({zIndex:2,opacity:0}).on("load",function(){s.onload&&s.onload.apply(this),(p===(o.zoom<0?0:Math.floor(o.zoom))||p===a.minZoom)&&(r["".concat(p)].actualTilesCount++,r["".concat(p)].howManyTiles===r["".concat(p)].actualTilesCount&&(r[p].loaded=!0,a.isAnimating?a.redrawTiles=!0:(a.redrawTiles=!1,c(l)),r["".concat(p)].actualTilesCount=0))}).add(i[p]),r["".concat(p)].tiles[""+e+",".concat(n)].posX=e,r["".concat(p)].tiles[""+e+",".concat(n)].posY=n,r["".concat(p)].tiles[""+e+",".concat(n)].originalURL=x}},P=o.pixelsToProjectedUnits({x:0,y:0}),k=o.projection.def.inverse([P.x,P.y]),A={lon:k[0]-p,lat:k[1]},z=o.pixelsToProjectedUnits({x:t.plotWidth,y:t.plotHeight}),O=o.projection.def.inverse([z.x,z.y]),E={lon:O[0]-p,lat:O[1]};(A.lat>o.projection.maxLatitude||E.lat<-1*o.projection.maxLatitude)&&(A.lat=o.projection.maxLatitude,E.lat=-1*o.projection.maxLatitude);var C=this.lonLatToTile(A,h),G=this.lonLatToTile(E,h),N=this.tileToLonLat(C.x,C.y,h),I=o.projection.def.forward([N.lon+p,N.lat]),L=o.projectedUnitsToPixels({x:I[0],y:I[1]}),_=C.x*y-L.x,W=C.y*y-L.y;r["".concat(h)]||(r["".concat(h)]={tiles:{},isActive:!1,howManyTiles:0,actualTilesCount:0,loaded:!1}),r["".concat(h)].howManyTiles=(G.x-C.x+1)*(G.y-C.y+1),r["".concat(h)].actualTilesCount=0;for(var U=C.x;U<=G.x;U++)for(var R=C.y;R<=G.y;R++)j(U,R,h,_,W)}for(var D=function(e){for(var i=function(i){if(o.projection&&o.projection.def){var s=256*(.638436911716859*Math.pow(2,n)/(.638436911716859*Math.pow(2,parseFloat(e)))),m=r[e].tiles[Object.keys(r[e].tiles)[0]],d=r[e].tiles[i],u=d.posX,y=d.posY;if(g(u)&&g(y)&&g(m.posX)&&g(m.posY)){var f=H.tileToLonLat(m.posX,m.posY,parseFloat(e)),v=o.projection.def.forward([f.lon+p,f.lat]),x=o.projectedUnitsToPixels({x:v[0],y:v[1]}),M=m.posX*s-x.x,S=m.posY*s-x.y;if(t.renderer.globalAnimation&&t.hasRendered){var T=Number(r[e].tiles[i].attr("x")),b=Number(r[e].tiles[i].attr("y")),Z=Number(r[e].tiles[i].attr("width")),w=Number(r[e].tiles[i].attr("height"));a.isAnimating=!0,r[e].tiles[i].attr({animator:0}).animate({animator:1},{step:function(t,o){r[e].tiles[i].attr({x:T+(u*s-M-T)*o.pos,y:b+(y*s-S-b)*o.pos,width:Z+(Math.ceil(s)+1-Z)*o.pos,height:w+(Math.ceil(s)+1-w)*o.pos})}},function(){a.isAnimating=!1,a.redrawTiles&&(a.redrawTiles=!1,c(l))})}else(a.redrawTiles||parseFloat(e)!==h||(r[e].isActive||parseFloat(e)===h)&&Object.keys(r[e].tiles).map(function(t){return r[e].tiles[t]}).some(function(e){return 0===e.opacity}))&&(a.redrawTiles=!1,c(l)),r[e].tiles[i].attr({x:u*s-M,y:y*s-S,width:Math.ceil(s)+1,height:Math.ceil(s)+1})}}},s=0,m=Object.keys(r[e].tiles);s<m.length;s++)i(m[s])},H=this,B=0,V=Object.keys(r);B<V.length;B++)D(V[B])}else v("Highcharts warning: Tiles Provider not defined in the Provider Registry.",!1)}},t.prototype.update=function(){var t,o=this.transformGroups,r=this.chart,i=r.mapView,a=arguments[0],s=a.provider;if(o&&(o.forEach(function(e){0!==Object.keys(e).length&&e.destroy()}),this.transformGroups=[]),i&&!g(null===(t=r.userOptions.mapView)||void 0===t?void 0:t.projection)&&s&&s.type){var n=m[s.type];if(n){var p=new n().initialProjectionName;i.update({projection:{name:p}})}}e.prototype.update.apply(this,arguments)},t.defaultOptions=x(y.defaultOptions,h),t}(y);c().registerSeriesType("tiledwebmap",b);var Z=p();Z.TilesProviderRegistry=Z.TilesProviderRegistry||m,b.compose(Z.MapView);var w=p();return s.default}()});