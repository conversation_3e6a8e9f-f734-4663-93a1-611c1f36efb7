{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/geoheatmap\n * @requires highcharts\n *\n * (c) 2009-2025\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/geoheatmap\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"SeriesRegistry\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/geoheatmap\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ geoheatmap_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es5/es-modules/Series/GeoHeatmap/GeoHeatmapPoint.js\n/* *\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\n\nvar MapPoint = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.map.prototype.pointClass;\nvar isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber;\n/* *\n *\n *  Class\n *\n * */\nvar GeoHeatmapPoint = /** @class */ (function (_super) {\n    __extends(GeoHeatmapPoint, _super);\n    function GeoHeatmapPoint() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * @private\n     */\n    GeoHeatmapPoint.prototype.applyOptions = function (options, x) {\n        var point = _super.prototype.applyOptions.call(this,\n            options,\n            x),\n            _a = point.options,\n            lat = _a.lat,\n            lon = _a.lon;\n        if (isNumber(lon) && isNumber(lat)) {\n            var _b = this.series.options, _c = _b.colsize, colsize = _c === void 0 ? 1 : _c, _d = _b.rowsize, rowsize = _d === void 0 ? 1 : _d, x1 = lon - colsize / 2, y1 = lat - rowsize / 2;\n            point.geometry = point.options.geometry = {\n                type: 'Polygon',\n                // A rectangle centered in lon/lat\n                coordinates: [\n                    [\n                        [x1, y1],\n                        [x1 + colsize, y1],\n                        [x1 + colsize, y1 + rowsize],\n                        [x1, y1 + rowsize],\n                        [x1, y1]\n                    ]\n                ]\n            };\n        }\n        return point;\n        /* eslint-enable valid-jsdoc */\n    };\n    return GeoHeatmapPoint;\n}(MapPoint));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var GeoHeatmap_GeoHeatmapPoint = (GeoHeatmapPoint);\n\n;// ./code/es5/es-modules/Series/InterpolationUtilities.js\n/* *\n *\n *  (c) 2010-2025 Hubert Kozik\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar doc = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).doc;\n\nvar defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Find color of point based on color axis.\n *\n * @function Highcharts.colorFromPoint\n *\n * @param {number | null} value\n *        Value to find corresponding color on the color axis.\n *\n * @param {Highcharts.Point} point\n *        Point to find it's color from color axis.\n *\n * @return {number[]}\n *        Color in RGBa array.\n */\nfunction colorFromPoint(value, point) {\n    var colorAxis = point.series.colorAxis;\n    if (colorAxis) {\n        var rgba = (colorAxis.toColor(value || 0, point)\n                .split(')')[0]\n                .split('(')[1]\n                .split(',')\n                .map(function (s) { return pick(parseFloat(s), parseInt(s, 10)); }));\n        rgba[3] = pick(rgba[3], 1.0) * 255;\n        if (!defined(value) || !point.visible) {\n            rgba[3] = 0;\n        }\n        return rgba;\n    }\n    return [0, 0, 0, 0];\n}\n/**\n * Method responsible for creating a canvas for interpolation image.\n * @private\n */\nfunction getContext(series) {\n    var canvas = series.canvas,\n        context = series.context;\n    if (canvas && context) {\n        context.clearRect(0, 0, canvas.width, canvas.height);\n    }\n    else {\n        series.canvas = doc.createElement('canvas');\n        series.context = series.canvas.getContext('2d', {\n            willReadFrequently: true\n        }) || void 0;\n        return series.context;\n    }\n    return context;\n}\nvar InterpolationUtilities = {\n    colorFromPoint: colorFromPoint,\n    getContext: getContext\n};\n/* harmony default export */ var Series_InterpolationUtilities = (InterpolationUtilities);\n\n;// ./code/es5/es-modules/Series/GeoHeatmap/GeoHeatmapSeries.js\n/* *\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar GeoHeatmapSeries_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar animObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).animObject, stop = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).stop;\n\n\nvar noop = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).noop;\n\nvar GeoHeatmapSeries_colorFromPoint = Series_InterpolationUtilities.colorFromPoint, GeoHeatmapSeries_getContext = Series_InterpolationUtilities.getContext;\n\nvar MapSeries = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.map;\n\nvar addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, error = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).error, extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, GeoHeatmapSeries_isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, isObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isObject, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, GeoHeatmapSeries_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/**\n * Normalize longitute value to -180:180 range.\n * @private\n */\nfunction normalizeLonValue(lon) {\n    return lon - Math.floor((lon + 180) / 360) * 360;\n}\n/**\n * Get proper point's position for PixelData array.\n * @private\n */\nfunction scaledPointPos(lon, lat, canvasWidth, canvasHeight, colsize, rowsize) {\n    return Math.ceil((canvasWidth * (canvasHeight - 1 - (lat + 90) / rowsize)) +\n        ((lon + 180) / colsize));\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Geo Heatmap series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.geoheatmap\n *\n * @augments Highcharts.Series\n */\nvar GeoHeatmapSeries = /** @class */ (function (_super) {\n    GeoHeatmapSeries_extends(GeoHeatmapSeries, _super);\n    function GeoHeatmapSeries() {\n        /* *\n         *\n         *  Static Properties\n         *\n         * */\n        var _this = _super !== null && _super.apply(this,\n            arguments) || this;\n        _this.isDirtyCanvas = true;\n        return _this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    /**\n     * For updated colsize and rowsize options\n     * @private\n     */\n    GeoHeatmapSeries.prototype.update = function () {\n        var series = this;\n        series.options = merge(series.options, arguments[0]);\n        if (series.getInterpolation().enabled) {\n            series.isDirtyCanvas = true;\n            series.points.forEach(function (point) {\n                if (point.graphic) {\n                    point.graphic.destroy();\n                    delete point.graphic;\n                }\n            });\n        }\n        _super.prototype.update.apply(series, arguments);\n    };\n    /**\n     * Override translate method to not fire if not needed.\n     * @private\n     */\n    GeoHeatmapSeries.prototype.translate = function () {\n        if (this.getInterpolation().enabled &&\n            this.image &&\n            !this.isDirty &&\n            !this.isDirtyData) {\n            return;\n        }\n        _super.prototype.translate.apply(this, arguments);\n    };\n    /**\n     * Create the extended object out of the boolean\n     * @private\n     */\n    GeoHeatmapSeries.prototype.getInterpolation = function () {\n        if (!isObject(this.options.interpolation)) {\n            return {\n                blur: 1,\n                enabled: this.options.interpolation\n            };\n        }\n        return this.options.interpolation;\n    };\n    /**\n     * Overriding drawPoints original method to apply new features.\n     * @private\n     */\n    GeoHeatmapSeries.prototype.drawPoints = function () {\n        var series = this,\n            chart = series.chart,\n            mapView = chart.mapView,\n            seriesOptions = series.options;\n        if (series.getInterpolation().enabled && mapView && series.bounds) {\n            var ctx = series.context || GeoHeatmapSeries_getContext(series),\n                canvas = series.canvas,\n                colorAxis = series.colorAxis,\n                image_1 = series.image,\n                chart_1 = series.chart,\n                points = series.points,\n                _a = [\n                    GeoHeatmapSeries_pick(seriesOptions.colsize, 1),\n                    GeoHeatmapSeries_pick(seriesOptions.rowsize, 1)\n                ],\n                colsize_1 = _a[0],\n                rowsize_1 = _a[1], \n                // Calculate dimensions based on series bounds\n                topLeft = mapView.projectedUnitsToPixels({\n                    x: series.bounds.x1,\n                    y: series.bounds.y2\n                }),\n                bottomRight = mapView.projectedUnitsToPixels({\n                    x: series.bounds.x2,\n                    y: series.bounds.y1\n                });\n            if (canvas && ctx && colorAxis && topLeft && bottomRight) {\n                var x_1 = topLeft.x,\n                    y_1 = topLeft.y,\n                    width_1 = bottomRight.x - x_1,\n                    height_1 = bottomRight.y - y_1,\n                    dimensions = {\n                        x: x_1,\n                        y: y_1,\n                        width: width_1,\n                        height: height_1\n                    };\n                if (\n                // Do not calculate new canvas if not necessary\n                series.isDirtyCanvas ||\n                    // Calculate new canvas if data is dirty\n                    series.isDirtyData ||\n                    // Always calculate new canvas for Orthographic projection\n                    mapView.projection.options.name === 'Orthographic') {\n                    var canvasWidth = canvas.width = ~~(360 / colsize_1) + 1, canvasHeight = canvas.height = ~~(180 / rowsize_1) + 1, canvasArea = canvasWidth * canvasHeight, pixelData = new Uint8ClampedArray(canvasArea * 4), \n                        // Guess if we have to round lon/lat with this data\n                        _b = points[0].options, _c = _b.lat, lat = _c === void 0 ? 0 : _c, _d = _b.lon, lon = _d === void 0 ? 0 : _d, unEvenLon = lon % rowsize_1 !== 0, unEvenLat = lat % colsize_1 !== 0, getAdjustedLon = (unEvenLon ?\n                            function (lon) { return (Math.round(lon / rowsize_1) * rowsize_1); } :\n                            function (lon) { return lon; }), getAdjustedLat = (unEvenLat ?\n                            function (lat) { return (Math.round(lat / colsize_1) * colsize_1); } :\n                            function (lat) { return lat; }), pointsLen = points.length;\n                    if (unEvenLon || unEvenLat) {\n                        error('Highcharts Warning: For best performance,' +\n                            ' lon/lat datapoints should spaced by a single ' +\n                            'colsize/rowsize', false, series.chart, {\n                            colsize: String(colsize_1),\n                            rowsize: String(rowsize_1)\n                        });\n                    }\n                    // Needed for tooltip\n                    series.directTouch = false;\n                    series.isDirtyCanvas = true;\n                    // First pixelData represents the geo coordinates\n                    for (var i = 0; i < pointsLen; i++) {\n                        var p = points[i],\n                            _e = p.options,\n                            lon_1 = _e.lon,\n                            lat_1 = _e.lat;\n                        if (GeoHeatmapSeries_isNumber(lon_1) && GeoHeatmapSeries_isNumber(lat_1)) {\n                            pixelData.set(GeoHeatmapSeries_colorFromPoint(p.value, p), scaledPointPos(getAdjustedLon(lon_1), getAdjustedLat(lat_1), canvasWidth, canvasHeight, colsize_1, rowsize_1) * 4);\n                        }\n                    }\n                    var blur_1 = series.getInterpolation().blur,\n                        blurFactor = blur_1 === 0 ? 1 : blur_1 * 11,\n                        upscaledWidth = ~~(canvasWidth * blurFactor),\n                        upscaledHeight = ~~(canvasHeight * blurFactor),\n                        projectedWidth = ~~width_1,\n                        projectedHeight = ~~height_1,\n                        img = new ImageData(pixelData,\n                        canvasWidth,\n                        canvasHeight);\n                    canvas.width = upscaledWidth;\n                    canvas.height = upscaledHeight;\n                    // Next step is to upscale pixelData to big image to get\n                    // the blur on the interpolation\n                    ctx.putImageData(img, 0, 0);\n                    // Now we have an unscaled version of our ImageData\n                    // let's make the compositing mode to 'copy' so that\n                    // our next drawing op erases whatever was there\n                    // previously just like putImageData would have done\n                    ctx.globalCompositeOperation = 'copy';\n                    // Now we can draw ourself over ourself\n                    ctx.drawImage(canvas, 0, 0, img.width, img.height, // Grab the ImageData\n                    0, 0, upscaledWidth, upscaledHeight // Scale it\n                    );\n                    // Add projection to upscaled ImageData\n                    var projectedPixelData = this.getProjectedImageData(mapView,\n                        projectedWidth,\n                        projectedHeight,\n                        ctx.getImageData(0, 0,\n                        upscaledWidth,\n                        upscaledHeight),\n                        canvas,\n                        x_1,\n                        y_1);\n                    canvas.width = projectedWidth;\n                    canvas.height = projectedHeight;\n                    ctx.putImageData(new ImageData(projectedPixelData, projectedWidth, projectedHeight), 0, 0);\n                }\n                if (image_1) {\n                    if (chart_1.renderer.globalAnimation && chart_1.hasRendered) {\n                        var startX_1 = Number(image_1.attr('x')), startY_1 = Number(image_1.attr('y')), startWidth_1 = Number(image_1.attr('width')), startHeight_1 = Number(image_1.attr('height'));\n                        var step_1 = function (now,\n                            fx) {\n                                var pos = fx.pos;\n                            image_1.attr({\n                                x: (startX_1 + (x_1 - startX_1) * pos),\n                                y: (startY_1 + (y_1 - startY_1) * pos),\n                                width: (startWidth_1 + (width_1 - startWidth_1) * pos),\n                                height: (startHeight_1 + (height_1 - startHeight_1) * pos)\n                            });\n                        };\n                        var animOptions = merge(animObject(chart_1.renderer.globalAnimation)),\n                            userStep_1 = animOptions.step;\n                        animOptions.step =\n                            function () {\n                                if (userStep_1) {\n                                    userStep_1.apply(this, arguments);\n                                }\n                                step_1.apply(this, arguments);\n                            };\n                        image_1\n                            .attr(merge({ animator: 0 }, series.isDirtyCanvas ? {\n                            href: canvas.toDataURL('image/png', 1)\n                        } : void 0))\n                            .animate({ animator: 1 }, animOptions);\n                        // When dragging or first rendering, animation is off\n                    }\n                    else {\n                        stop(image_1);\n                        image_1.attr(merge(dimensions, series.isDirtyCanvas ? {\n                            href: canvas.toDataURL('image/png', 1)\n                        } : void 0));\n                    }\n                }\n                else {\n                    series.image = chart_1.renderer.image(canvas.toDataURL('image/png', 1))\n                        .attr(dimensions)\n                        .add(series.group);\n                }\n                series.isDirtyCanvas = false;\n            }\n        }\n        else {\n            _super.prototype.drawPoints.apply(series, arguments);\n        }\n    };\n    /**\n     * Project ImageData to actual mapView projection used on a chart.\n     * @private\n     */\n    GeoHeatmapSeries.prototype.getProjectedImageData = function (mapView, projectedWidth, projectedHeight, cartesianImageData, canvas, horizontalShift, verticalShift) {\n        var _a;\n        var projectedPixelData = new Uint8ClampedArray(projectedWidth * projectedHeight * 4), lambda = GeoHeatmapSeries_pick((_a = mapView.projection.options.rotation) === null || _a === void 0 ? void 0 : _a[0], 0), widthFactor = canvas.width / 360, heightFactor = -1 * canvas.height / 180;\n        var y = -1;\n        // For each pixel on the map plane, find the map\n        // coordinate and get the color value\n        for (var i = 0; i < projectedPixelData.length; i += 4) {\n            var x = (i / 4) % projectedWidth;\n            if (x === 0) {\n                y++;\n            }\n            var projectedCoords = mapView.pixelsToLonLat({\n                    x: horizontalShift + x,\n                    y: verticalShift + y\n                });\n            if (projectedCoords) {\n                // Normalize lon values\n                if (projectedCoords.lon > -180 - lambda &&\n                    projectedCoords.lon < 180 - lambda) {\n                    projectedCoords.lon =\n                        normalizeLonValue(projectedCoords.lon);\n                }\n                var projected = [\n                        projectedCoords.lon,\n                        projectedCoords.lat\n                    ],\n                    cvs2PixelX = projected[0] * widthFactor + canvas.width / 2,\n                    cvs2PixelY = projected[1] * heightFactor +\n                        canvas.height / 2;\n                if (cvs2PixelX >= 0 &&\n                    cvs2PixelX <= canvas.width &&\n                    cvs2PixelY >= 0 &&\n                    cvs2PixelY <= canvas.height) {\n                    var redPos = (\n                        // Rows\n                        Math.floor(cvs2PixelY) *\n                            canvas.width * 4 +\n                            // Columns\n                            Math.round(cvs2PixelX) * 4);\n                    projectedPixelData[i] =\n                        cartesianImageData.data[redPos];\n                    projectedPixelData[i + 1] =\n                        cartesianImageData.data[redPos + 1];\n                    projectedPixelData[i + 2] =\n                        cartesianImageData.data[redPos + 2];\n                    projectedPixelData[i + 3] =\n                        cartesianImageData.data[redPos + 3];\n                }\n            }\n        }\n        return projectedPixelData;\n    };\n    GeoHeatmapSeries.prototype.searchPoint = function (e, compareX) {\n        var series = this,\n            chart = this.chart,\n            mapView = chart.mapView;\n        if (mapView &&\n            series.bounds &&\n            series.image &&\n            chart.tooltip &&\n            chart.tooltip.options.enabled) {\n            if (\n            // If user drags map do not build k-d-tree\n            !chart.pointer.hasDragged &&\n                // If user zooms in/out map do not build k-d-tree\n                (+series.image.attr('animator') <= 0.01 ||\n                    +series.image.attr('animator') >= 0.99)) {\n                var topLeft = mapView.projectedUnitsToPixels({\n                        x: series.bounds.x1,\n                        y: series.bounds.y2\n                    }),\n                    bottomRight = mapView.projectedUnitsToPixels({\n                        x: series.bounds.x2,\n                        y: series.bounds.y1\n                    });\n                chart.pointer.normalize(e);\n                if (e.lon && e.lat &&\n                    topLeft && bottomRight &&\n                    e.chartX - chart.plotLeft > topLeft.x &&\n                    e.chartX - chart.plotLeft < bottomRight.x &&\n                    e.chartY - chart.plotTop > topLeft.y &&\n                    e.chartY - chart.plotTop < bottomRight.y) {\n                    return this.searchKDTree({\n                        clientX: e.chartX,\n                        lon: normalizeLonValue(e.lon),\n                        lat: e.lat\n                    }, compareX, e);\n                }\n            }\n            else {\n                chart.tooltip.destroy();\n            }\n        }\n    };\n    /**\n     * A `geoheatmap` series is a variety of heatmap series, composed into\n     * the map projection, where the units are expressed in the latitude\n     * and longitude, and individual values contained in a matrix are\n     * represented as colors.\n     *\n     * @sample maps/demo/geoheatmap-europe/\n     *         GeoHeatmap Chart with interpolation on Europe map\n     * @sample maps/series-geoheatmap/geoheatmap-equalearth/\n     *         GeoHeatmap Chart on the Equal Earth Projection\n     *\n     * @extends      plotOptions.map\n     * @since        11.0.0\n     * @product      highmaps\n     * @excluding    allAreas, dragDrop, findNearestPointBy, geometry, joinBy,\n     * negativeColor, onPoint, stickyTracking\n     * @requires     modules/geoheatmap\n     * @optionparent plotOptions.geoheatmap\n     */\n    GeoHeatmapSeries.defaultOptions = merge(MapSeries.defaultOptions, {\n        nullColor: 'transparent',\n        tooltip: {\n            pointFormat: 'Lat: {point.lat}, Lon: {point.lon}, Value: {point.value}<br/>'\n        },\n        /**\n         * The border width of each geoheatmap tile.\n         *\n         * In styled mode, the border stroke width is given in the\n         * `.highcharts-point` class.\n         *\n         * @sample maps/demo/geoheatmap-orthographic/\n         *         borderWidth set to 1 to create a grid\n         *\n         * @type      {number|null}\n         * @default   0\n         * @product   highmaps\n         * @apioption plotOptions.geoheatmap.borderWidth\n         */\n        borderWidth: 0,\n        /**\n         * The column size - how many longitude units each column in the\n         * geoheatmap should span.\n         *\n         * @sample maps/demo/geoheatmap-europe/\n         *         1 by default, set to 5\n         *\n         * @product   highmaps\n         * @apioption plotOptions.geoheatmap.colsize\n         */\n        colsize: 1,\n        /**\n         * The main color of the series. In heat maps this color is rarely\n         * used, as we mostly use the color to denote the value of each\n         * point. Unless options are set in the [colorAxis](#colorAxis), the\n         * default value is pulled from the [options.colors](#colors) array.\n         *\n         * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n         * @product   highmaps\n         * @apioption plotOptions.geoheatmap.color\n         */\n        /**\n         * The rowsize size - how many latitude units each row in the\n         * geoheatmap should span.\n         *\n         * @sample maps/demo/geoheatmap-europe/\n         *         1 by default, set to 5\n         *\n         * @product   highmaps\n         * @apioption plotOptions.geoheatmap.rowsize\n         */\n        rowsize: 1,\n        stickyTracking: true,\n        /**\n         * Make the geoheatmap render its data points as an interpolated\n         * image. It can be used to show a Temperature Map-like charts.\n         *\n         * @sample maps/demo/geoheatmap-earth-statistics\n         *         Advanced demo of GeoHeatmap interpolation with multiple\n         *         datasets\n         *\n         * @type      {boolean|Highcharts.InterpolationOptionsObject}\n         * @since     11.2.0\n         * @product   highmaps\n         */\n        interpolation: {\n            /**\n             * Enable or disable the interpolation of the geoheatmap series.\n             *\n             * @since 11.2.0\n             */\n            enabled: false,\n            /**\n             * Represents how much blur should be added to the interpolated\n             * image. Works best in the range of 0-1, all higher values\n             * would need a lot more performance of the machine to calculate\n             * more detailed interpolation.\n             *\n             *  * **Note:** Useful, if the data is spread into wide range of\n             *  longitude and latitude values.\n             *\n             * @sample maps/series-geoheatmap/turkey-fire-areas\n             *         Simple demo of GeoHeatmap interpolation\n             *\n             * @since  11.2.0\n             */\n            blur: 1\n        }\n    });\n    return GeoHeatmapSeries;\n}(MapSeries));\naddEvent(GeoHeatmapSeries, 'afterDataClassLegendClick', function () {\n    this.isDirtyCanvas = true;\n    this.drawPoints();\n});\nextend(GeoHeatmapSeries.prototype, {\n    type: 'geoheatmap',\n    applyJitter: noop,\n    pointClass: GeoHeatmap_GeoHeatmapPoint,\n    pointArrayMap: ['lon', 'lat', 'value'],\n    kdAxisArray: ['lon', 'lat'] // Search k-d-tree by lon/lat values\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('geoheatmap', GeoHeatmapSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var GeoHeatmap_GeoHeatmapSeries = ((/* unused pure expression or super */ null && (GeoHeatmapSeries)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `geoheatmap` series. If the [type](#series.map.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.geoheatmap\n * @excluding allAreas, dataParser, dataURL, dragDrop, findNearestPointBy,\n *            joinBy, marker, mapData, negativeColor, onPoint, shadow,\n *            stickyTracking\n * @product   highmaps\n * @apioption series.geoheatmap\n */\n/**\n * An array of data points for the series. For the `geoheatmap` series\n * type, points can be given in the following ways:\n *\n * 1.  An array of arrays with 3 or 2 values. In this case, the values\n * correspond to `lon,lat,value`. The `value` refers to the color on the `colorAxis`.\n *\n *  ```js\n *     data: [\n *         [51.50, -0.12, 7],\n *         [54.59, -5.93, 4],\n *         [55.8, -4.25, 3]\n *     ]\n *  ```\n *\n * 2.  An array of objects with named values. The following snippet shows only a\n * few settings, see the complete options set below. If the total number of data\n * points exceeds the series' [turboThreshold](#series.heatmap.turboThreshold),\n * this option is not available.\n *\n *  ```js\n *     data: [{\n *         lat: 51.50,\n *         lon: -0.12,\n *         value: 7,\n *         name: \"London\"\n *     }, {\n *         lat: 54.59,\n *         lon: -5.93,\n *         value: 4,\n *         name: \"Belfast\"\n *     }]\n *  ```\n *\n * @sample maps/demo/geoheatmap-europe/\n *         GeoHeatmap Chart with interpolation on Europe map\n * @sample maps/series-geoheatmap/geoheatmap-equalearth/\n *         GeoHeatmap Chart on the Equal Earth Projection\n *\n * @type      {Array<Array<number>|*>}\n * @extends   series.map.data\n * @product   highmaps\n * @apioption series.geoheatmap.data\n */\n/**\n * Individual color for the point. By default the color is either used\n * to denote the value, or pulled from the global `colors` array.\n *\n * @type      {Highcharts.ColorString|Highcharts.GradientColorObject|Highcharts.PatternObject}\n * @product   highmaps\n * @apioption series.geoheatmap.data.color\n */\n/**\n * The value of the point, resulting in a color controlled by options\n * as set in the [colorAxis](#colorAxis) configuration.\n *\n * @type      {number|null}\n * @product   highmaps\n * @apioption series.geoheatmap.data.value\n */\n/**\n * Detailed options for interpolation object.\n *\n * @interface Highcharts.InterpolationOptionsObject\n */ /**\n*  Enable or disable the interpolation.\n*\n* @name Highcharts.InterpolationOptionsObject#enabled\n* @type {boolean}\n*/ /**\n* Represents how much blur should be added to the interpolated\n* image. Works best in the range of 0-1, all higher values\n* would need a lot more performance of the machine to calculate\n* more detailed interpolation.\n*\n* @name Highcharts.InterpolationOptionsObject#blur\n* @type {number}\n*/\n''; // Adds doclets above to the transpiled file\n\n;// ./code/es5/es-modules/masters/modules/geoheatmap.js\n\n\n\n\n/* harmony default export */ var geoheatmap_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "extendStatics", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "geoheatmap_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "__extends", "b", "setPrototypeOf", "__proto__", "Array", "p", "__", "constructor", "create", "MapPoint", "seriesTypes", "map", "pointClass", "isNumber", "GeoHeatmapPoint", "_super", "apply", "arguments", "applyOptions", "options", "x", "point", "_a", "lat", "lon", "_b", "series", "_c", "colsize", "_d", "rowsize", "x1", "y1", "geometry", "type", "coordinates", "doc", "defined", "pick", "canvas", "context", "clearRect", "width", "height", "createElement", "getContext", "willReadFrequently", "GeoHeatmapSeries_extends", "TypeError", "String", "animObject", "stop", "noop", "GeoHeatmapSeries_colorFromPoint", "value", "colorAxis", "rgba", "toColor", "split", "s", "parseFloat", "parseInt", "visible", "MapSeries", "addEvent", "error", "extend", "GeoHeatmapSeries_isNumber", "isObject", "merge", "GeoHeatmapSeries_pick", "normalizeLonValue", "Math", "floor", "GeoHeatmapSeries", "_this", "isDirtyCanvas", "update", "getInterpolation", "enabled", "points", "for<PERSON>ach", "graphic", "destroy", "translate", "image", "isDirty", "isDirtyData", "interpolation", "blur", "drawPoints", "mapView", "chart", "seriesOptions", "bounds", "ctx", "GeoHeatmapSeries_getContext", "image_1", "chart_1", "colsize_1", "rowsize_1", "topLeft", "projectedUnitsToPixels", "y", "y2", "bottomRight", "x2", "x_1", "y_1", "width_1", "height_1", "dimensions", "projection", "name", "canvasWidth", "canvasHeight", "pixelData", "Uint8ClampedArray", "canvasArea", "unEvenLon", "unEvenLat", "getAdjustedLon", "round", "getAdjustedLat", "pointsLen", "length", "directTouch", "i", "_e", "lon_1", "lat_1", "set", "scaledPointPos", "ceil", "blur_1", "blurFactor", "upscaledWidth", "upscaledHeight", "projectedWidth", "projectedHeight", "img", "ImageData", "putImageData", "globalCompositeOperation", "drawImage", "projectedPixelData", "getProjectedImageData", "getImageData", "renderer", "globalAnimation", "hasRendered", "startX_1", "Number", "attr", "startY_1", "startWidth_1", "startHeight_1", "step_1", "now", "fx", "pos", "animOptions", "userStep_1", "step", "animator", "href", "toDataURL", "animate", "add", "group", "cartesianImageData", "horizontalShift", "verticalShift", "lambda", "rotation", "widthFactor", "heightFactor", "projectedCoords", "pixelsToLonLat", "projected", "cvs2PixelX", "cvs2PixelY", "redPos", "data", "searchPoint", "e", "compareX", "tooltip", "pointer", "hasDragged", "normalize", "chartX", "plotLeft", "chartY", "plotTop", "searchKDTree", "clientX", "defaultOptions", "nullColor", "pointFormat", "borderWidth", "stickyTracking", "applyJitter", "pointArrayMap", "kdAxisArray", "registerSeriesType"], "mappings": "CASA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,EAChF,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,gCAAiC,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,iBAAiB,CAAC,CAAEJ,GAC1G,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,gCAAgC,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,EAEjHJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,EACpF,OAAgB,AAAC,WACP,aACA,IAoGFC,EAkKAA,EAtQMC,EAAuB,CAE/B,IACC,SAASP,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,SAASb,CAAM,EACtC,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,WAAa,OAAOf,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASjB,CAAO,CAAEmB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,CAAgB,CAC/D,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAc7KE,GACI9B,EAAgB,SAAUU,CAAC,CAC3BqB,CAAC,EAMD,MAAO/B,AALHA,CAAAA,EAAgBe,OAAOiB,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAUxB,CAAC,CAC1DqB,CAAC,EAAIrB,EAAEuB,SAAS,CAAGF,CAAG,GACd,SAAUrB,CAAC,CACnBqB,CAAC,EAAI,IAAK,IAAII,KAAKJ,EAAOA,EAAET,cAAc,CAACa,IAAIzB,CAAAA,CAAC,CAACyB,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,CAAG,CAAA,EACvCzB,EAAGqB,EAC5B,EACO,SAAUrB,CAAC,CAAEqB,CAAC,EAEjB,SAASK,IAAO,IAAI,CAACC,WAAW,CAAG3B,CAAG,CADtCV,EAAcU,EAAGqB,GAEjBrB,EAAEW,SAAS,CAAGU,AAAM,OAANA,EAAahB,OAAOuB,MAAM,CAACP,GAAMK,CAAAA,EAAGf,SAAS,CAAGU,EAAEV,SAAS,CAAE,IAAIe,CAAG,CACtF,GAIAG,EAAW,AAACV,IAA2IW,WAAW,CAACC,GAAG,CAACpB,SAAS,CAACqB,UAAU,CAC3LC,EAAW,AAAChB,IAA+EgB,QAAQ,CAMnGC,EAAiC,SAAUC,CAAM,EAEjD,SAASD,IACL,OAAOC,AAAW,OAAXA,GAAmBA,EAAOC,KAAK,CAAC,IAAI,CAAEC,YAAc,IAAI,AACnE,CAoCA,OAvCAjB,EAAUc,EAAiBC,GAa3BD,EAAgBvB,SAAS,CAAC2B,YAAY,CAAG,SAAUC,CAAO,CAAEC,CAAC,EACzD,IAAIC,EAAQN,EAAOxB,SAAS,CAAC2B,YAAY,CAACzB,IAAI,CAAC,IAAI,CAC/C0B,EACAC,GACAE,EAAKD,EAAMF,OAAO,CAClBI,EAAMD,EAAGC,GAAG,CACZC,EAAMF,EAAGE,GAAG,CAChB,GAAIX,EAASW,IAAQX,EAASU,GAAM,CAChC,IAAIE,EAAK,IAAI,CAACC,MAAM,CAACP,OAAO,CAAEQ,EAAKF,EAAGG,OAAO,CAAEA,EAAUD,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EAAIE,EAAKJ,EAAGK,OAAO,CAAEA,EAAUD,AAAO,KAAK,IAAZA,EAAgB,EAAIA,EAAIE,EAAKP,EAAMI,EAAU,EAAGI,EAAKT,EAAMO,EAAU,CACjLT,CAAAA,EAAMY,QAAQ,CAAGZ,EAAMF,OAAO,CAACc,QAAQ,CAAG,CACtCC,KAAM,UAENC,YAAa,CACT,CACI,CAACJ,EAAIC,EAAG,CACR,CAACD,EAAKH,EAASI,EAAG,CAClB,CAACD,EAAKH,EAASI,EAAKF,EAAQ,CAC5B,CAACC,EAAIC,EAAKF,EAAQ,CAClB,CAACC,EAAIC,EAAG,CACX,CACJ,AACL,CACJ,CACA,OAAOX,CAEX,EACOP,CACX,EAAEL,GAoBE2B,EAAM,AAACvC,IAA+EuC,GAAG,CAEzFC,EAAU,AAACxC,IAA+EwC,OAAO,CAAEC,EAAO,AAACzC,IAA+EyC,IAAI,GAwClM,SAAoBZ,CAAM,EACtB,IAAIa,EAASb,EAAOa,MAAM,CACtBC,EAAUd,EAAOc,OAAO,QAC5B,AAAID,GAAUC,GACVA,EAAQC,SAAS,CAAC,EAAG,EAAGF,EAAOG,KAAK,CAAEH,EAAOI,MAAM,EAShDH,IANHd,EAAOa,MAAM,CAAGH,EAAIQ,aAAa,CAAC,UAClClB,EAAOc,OAAO,CAAGd,EAAOa,MAAM,CAACM,UAAU,CAAC,KAAM,CAC5CC,mBAAoB,CAAA,CACxB,IAAM,KAAK,EACJpB,EAAOc,OAAO,CAG7B,EAoBIO,GACI7E,EAAgB,SAAUU,CAAC,CAC3BqB,CAAC,EAOD,MAAO/B,AANHA,CAAAA,EAAgBe,OAAOiB,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAUxB,CAAC,CAC1DqB,CAAC,EAAIrB,EAAEuB,SAAS,CAAGF,CAAG,GACd,SAAUrB,CAAC,CACnBqB,CAAC,EAAI,IAAK,IAAII,KAAKJ,EAAOhB,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACQ,EAC/DI,IAAIzB,CAAAA,CAAC,CAACyB,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,CAAG,CAAA,EACIzB,EAAGqB,EAC5B,EACO,SAAUrB,CAAC,CAAEqB,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAI+C,UAAU,uBAAyBC,OAAOhD,GAAK,iCAE7D,SAASK,IAAO,IAAI,CAACC,WAAW,CAAG3B,CAAG,CADtCV,EAAcU,EAAGqB,GAEjBrB,EAAEW,SAAS,CAAGU,AAAM,OAANA,EAAahB,OAAOuB,MAAM,CAACP,GAAMK,CAAAA,EAAGf,SAAS,CAAGU,EAAEV,SAAS,CAAE,IAAIe,CAAG,CACtF,GAGA4C,EAAa,AAACrD,IAA+EqD,UAAU,CAAEC,EAAO,AAACtD,IAA+EsD,IAAI,CAGpMC,EAAO,AAACvD,IAA+EuD,IAAI,CAE3FC,EA/EJ,SAAwBC,CAAK,CAAEjC,CAAK,EAChC,IAAIkC,EAAYlC,EAAMK,MAAM,CAAC6B,SAAS,CACtC,GAAIA,EAAW,CACX,IAAIC,EAAQD,EAAUE,OAAO,CAACH,GAAS,EAAGjC,GACjCqC,KAAK,CAAC,IAAI,CAAC,EAAE,CACbA,KAAK,CAAC,IAAI,CAAC,EAAE,CACbA,KAAK,CAAC,KACN/C,GAAG,CAAC,SAAUgD,CAAC,EAAI,OAAOrB,EAAKsB,WAAWD,GAAIE,SAASF,EAAG,IAAM,GAKzE,OAJAH,CAAI,CAAC,EAAE,CAAGlB,AAAqB,IAArBA,EAAKkB,CAAI,CAAC,EAAE,CAAE,GACnBnB,EAAQiB,IAAWjC,EAAMyC,OAAO,EACjCN,CAAAA,CAAI,CAAC,EAAE,CAAG,CAAA,EAEPA,CACX,CACA,MAAO,CAAC,EAAG,EAAG,EAAG,EAAE,AACvB,EAkEIO,EAAY,AAAChE,IAA2IW,WAAW,CAACC,GAAG,CAEvKqD,EAAW,AAACnE,IAA+EmE,QAAQ,CAAEC,EAAQ,AAACpE,IAA+EoE,KAAK,CAAEC,EAAS,AAACrE,IAA+EqE,MAAM,CAAEC,EAA4B,AAACtE,IAA+EgB,QAAQ,CAAEuD,EAAW,AAACvE,IAA+EuE,QAAQ,CAAEC,EAAQ,AAACxE,IAA+EwE,KAAK,CAAEC,EAAwB,AAACzE,IAA+EyC,IAAI,CAK/sB,SAASiC,EAAkB/C,CAAG,EAC1B,OAAOA,EAAMgD,AAAgC,IAAhCA,KAAKC,KAAK,CAAC,AAACjD,CAAAA,EAAM,GAAE,EAAK,IAC1C,CAuBA,IAAIkD,EAAkC,SAAU3D,CAAM,EAElD,SAAS2D,IAML,IAAIC,EAAQ5D,AAAW,OAAXA,GAAmBA,EAAOC,KAAK,CAAC,IAAI,CAC5CC,YAAc,IAAI,CAEtB,OADA0D,EAAMC,aAAa,CAAG,CAAA,EACfD,CACX,CAmaA,OA9aA5B,EAAyB2B,EAAkB3D,GAsB3C2D,EAAiBnF,SAAS,CAACsF,MAAM,CAAG,WAEhCnD,AADa,IAAI,CACVP,OAAO,CAAGkD,EAAM3C,AADV,IAAI,CACaP,OAAO,CAAEF,SAAS,CAAC,EAAE,EAC/CS,AAFS,IAAI,CAENoD,gBAAgB,GAAGC,OAAO,GACjCrD,AAHS,IAAI,CAGNkD,aAAa,CAAG,CAAA,EACvBlD,AAJS,IAAI,CAINsD,MAAM,CAACC,OAAO,CAAC,SAAU5D,CAAK,EAC7BA,EAAM6D,OAAO,GACb7D,EAAM6D,OAAO,CAACC,OAAO,GACrB,OAAO9D,EAAM6D,OAAO,CAE5B,IAEJnE,EAAOxB,SAAS,CAACsF,MAAM,CAAC7D,KAAK,CAXhB,IAAI,CAWqBC,UAC1C,EAKAyD,EAAiBnF,SAAS,CAAC6F,SAAS,CAAG,WAC/B,CAAA,CAAA,IAAI,CAACN,gBAAgB,GAAGC,OAAO,GAC/B,IAAI,CAACM,KAAK,EACT,IAAI,CAACC,OAAO,EACZ,IAAI,CAACC,WAAW,AAAD,GAGpBxE,EAAOxB,SAAS,CAAC6F,SAAS,CAACpE,KAAK,CAAC,IAAI,CAAEC,UAC3C,EAKAyD,EAAiBnF,SAAS,CAACuF,gBAAgB,CAAG,kBAC1C,AAAKV,EAAS,IAAI,CAACjD,OAAO,CAACqE,aAAa,EAMjC,IAAI,CAACrE,OAAO,CAACqE,aAAa,CALtB,CACHC,KAAM,EACNV,QAAS,IAAI,CAAC5D,OAAO,CAACqE,aAAa,AACvC,CAGR,EAKAd,EAAiBnF,SAAS,CAACmG,UAAU,CAAG,WACpC,IAEIC,EAAUC,AADFlE,AADC,IAAI,CACEkE,KAAK,CACJD,OAAO,CACvBE,EAAgBnE,AAHP,IAAI,CAGUP,OAAO,CAClC,GAAIO,AAJS,IAAI,CAINoD,gBAAgB,GAAGC,OAAO,EAAIY,GAAWjE,AAJvC,IAAI,CAI0CoE,MAAM,CAAE,CAC/D,IAAIC,EAAMrE,AALD,IAAI,CAKIc,OAAO,EAAIwD,EALnB,IAAI,EAMTzD,EAASb,AANJ,IAAI,CAMOa,MAAM,CACtBgB,EAAY7B,AAPP,IAAI,CAOU6B,SAAS,CAC5B0C,EAAUvE,AARL,IAAI,CAQQ2D,KAAK,CACtBa,EAAUxE,AATL,IAAI,CASQkE,KAAK,CACtBZ,EAAStD,AAVJ,IAAI,CAUOsD,MAAM,CACtB1D,EAAK,CACDgD,EAAsBuB,EAAcjE,OAAO,CAAE,GAC7C0C,EAAsBuB,EAAc/D,OAAO,CAAE,GAChD,CACDqE,EAAY7E,CAAE,CAAC,EAAE,CACjB8E,EAAY9E,CAAE,CAAC,EAAE,CAEjB+E,EAAUV,EAAQW,sBAAsB,CAAC,CACrClF,EAAGM,AAnBF,IAAI,CAmBKoE,MAAM,CAAC/D,EAAE,CACnBwE,EAAG7E,AApBF,IAAI,CAoBKoE,MAAM,CAACU,EAAE,AACvB,GACAC,EAAcd,EAAQW,sBAAsB,CAAC,CACzClF,EAAGM,AAvBF,IAAI,CAuBKoE,MAAM,CAACY,EAAE,CACnBH,EAAG7E,AAxBF,IAAI,CAwBKoE,MAAM,CAAC9D,EAAE,AACvB,GACJ,GAAIO,GAAUwD,GAAOxC,GAAa8C,GAAWI,EAAa,CACtD,IAAIE,EAAMN,EAAQjF,CAAC,CACfwF,EAAMP,EAAQE,CAAC,CACfM,EAAUJ,EAAYrF,CAAC,CAAGuF,EAC1BG,EAAWL,EAAYF,CAAC,CAAGK,EAC3BG,EAAa,CACT3F,EAAGuF,EACHJ,EAAGK,EACHlE,MAAOmE,EACPlE,OAAQmE,CACZ,EACJ,GAEApF,AAvCK,IAAI,CAuCFkD,aAAa,EAEhBlD,AAzCC,IAAI,CAyCE6D,WAAW,EAElBI,AAAoC,iBAApCA,EAAQqB,UAAU,CAAC7F,OAAO,CAAC8F,IAAI,CAAqB,CACpD,IAAIC,EAAc3E,EAAOG,KAAK,CAAG,CAAC,CAAE,CAAA,IAAMyD,CAAQ,EAAK,EAAGgB,EAAe5E,EAAOI,MAAM,CAAG,CAAC,CAAE,CAAA,IAAMyD,CAAQ,EAAK,EAA4CgB,EAAY,IAAIC,kBAAkBC,AAA9DJ,EAAcC,EAA6D,GAEtM1F,EAAKuD,CAAM,CAAC,EAAE,CAAC7D,OAAO,CAAEQ,EAAKF,EAAGF,GAAG,CAAgCM,EAAKJ,EAAGD,GAAG,CAAgC+F,EAAY/F,AAApCK,CAAAA,AAAO,KAAK,IAAZA,EAAgB,EAAIA,CAAC,EAAqBuE,GAAc,EAAGoB,EAAYjG,AAAlHI,CAAAA,AAAO,KAAK,IAAZA,EAAgB,EAAIA,CAAC,EAAmGwE,GAAc,EAAGsB,EAAkBF,EAClM,SAAU/F,CAAG,EAAI,OAAQgD,KAAKkD,KAAK,CAAClG,EAAM4E,GAAaA,CAAY,EACnE,SAAU5E,CAAG,EAAI,OAAOA,CAAK,EAAImG,EAAkBH,EACnD,SAAUjG,CAAG,EAAI,OAAQiD,KAAKkD,KAAK,CAACnG,EAAM4E,GAAaA,CAAY,EACnE,SAAU5E,CAAG,EAAI,OAAOA,CAAK,EAAIqG,EAAY5C,EAAO6C,MAAM,CAC9DN,CAAAA,GAAaC,CAAQ,GACrBvD,EAAM,yGAEiB,CAAA,EAAOvC,AAtDjC,IAAI,CAsDoCkE,KAAK,CAAE,CACxChE,QAASqB,OAAOkD,GAChBrE,QAASmB,OAAOmD,EACpB,GAGJ1E,AA5DC,IAAI,CA4DEoG,WAAW,CAAG,CAAA,EACrBpG,AA7DC,IAAI,CA6DEkD,aAAa,CAAG,CAAA,EAEvB,IAAK,IAAImD,EAAI,EAAGA,EAAIH,EAAWG,IAAK,CAChC,IAtJAvG,EAsJInB,EAAI2E,CAAM,CAAC+C,EAAE,CACbC,EAAK3H,EAAEc,OAAO,CACd8G,EAAQD,EAAGxG,GAAG,CACd0G,EAAQF,EAAGzG,GAAG,CACd4C,EAA0B8D,IAAU9D,EAA0B+D,IAC9Dd,EAAUe,GAAG,CAAC9E,EAAgChD,EAAEiD,KAAK,CAAEjD,GAAI+H,AAAgH,GA3J/K5G,EA2J8EiG,EAAeQ,GA1J1GzD,KAAK6D,IAAI,CAAC,AA0J+HnB,EA1J/GC,CAAAA,AA0J4HA,EA1J7G,EAAI,AAAC5F,CAAAA,AA0JoEoG,EAAeO,GA1J7E,EAAC,EA0J0H9B,CA1J/G,EAClE,AAAC5E,CAAAA,EAAM,GAAE,EAyJ6J2E,IAE3J,CACA,IAAImC,EAAS5G,AAxEZ,IAAI,CAwEeoD,gBAAgB,GAAGW,IAAI,CACvC8C,EAAaD,AAAW,IAAXA,EAAe,EAAIA,AAAS,GAATA,EAChCE,EAAgB,CAAC,CAAEtB,CAAAA,EAAcqB,CAAS,EAC1CE,EAAiB,CAAC,CAAEtB,CAAAA,EAAeoB,CAAS,EAC5CG,EAAiB,CAAC,CAAC7B,EACnB8B,EAAkB,CAAC,CAAC7B,EACpB8B,EAAM,IAAIC,UAAUzB,EACpBF,EACAC,EACJ5E,CAAAA,EAAOG,KAAK,CAAG8F,EACfjG,EAAOI,MAAM,CAAG8F,EAGhB1C,EAAI+C,YAAY,CAACF,EAAK,EAAG,GAKzB7C,EAAIgD,wBAAwB,CAAG,OAE/BhD,EAAIiD,SAAS,CAACzG,EAAQ,EAAG,EAAGqG,EAAIlG,KAAK,CAAEkG,EAAIjG,MAAM,CACjD,EAAG,EAAG6F,EAAeC,GAGrB,IAAIQ,EAAqB,IAAI,CAACC,qBAAqB,CAACvD,EAChD+C,EACAC,EACA5C,EAAIoD,YAAY,CAAC,EAAG,EACpBX,EACAC,GACAlG,EACAoE,EACAC,EACJrE,CAAAA,EAAOG,KAAK,CAAGgG,EACfnG,EAAOI,MAAM,CAAGgG,EAChB5C,EAAI+C,YAAY,CAAC,IAAID,UAAUI,EAAoBP,EAAgBC,GAAkB,EAAG,EAC5F,CACA,GAAI1C,GACA,GAAIC,EAAQkD,QAAQ,CAACC,eAAe,EAAInD,EAAQoD,WAAW,CAAE,CACzD,IAAIC,EAAWC,OAAOvD,EAAQwD,IAAI,CAAC,MAAOC,EAAWF,OAAOvD,EAAQwD,IAAI,CAAC,MAAOE,GAAeH,OAAOvD,EAAQwD,IAAI,CAAC,UAAWG,GAAgBJ,OAAOvD,EAAQwD,IAAI,CAAC,WAC9JI,GAAS,SAAUC,CAAG,CACtBC,CAAE,EACE,IAAIC,EAAMD,EAAGC,GAAG,CACpB/D,EAAQwD,IAAI,CAAC,CACTrI,EAAImI,EAAW,AAAC5C,CAAAA,EAAM4C,CAAO,EAAKS,EAClCzD,EAAImD,EAAW,AAAC9C,CAAAA,EAAM8C,CAAO,EAAKM,EAClCtH,MAAQiH,GAAe,AAAC9C,CAAAA,EAAU8C,EAAW,EAAKK,EAClDrH,OAASiH,GAAgB,AAAC9C,CAAAA,EAAW8C,EAAY,EAAKI,CAC1D,EACJ,EACIC,GAAc5F,EAAMnB,EAAWgD,EAAQkD,QAAQ,CAACC,eAAe,GAC/Da,GAAaD,GAAYE,IAAI,AACjCF,CAAAA,GAAYE,IAAI,CACZ,WACQD,IACAA,GAAWlJ,KAAK,CAAC,IAAI,CAAEC,WAE3B4I,GAAO7I,KAAK,CAAC,IAAI,CAAEC,UACvB,EACJgF,EACKwD,IAAI,CAACpF,EAAM,CAAE+F,SAAU,CAAE,EAAG1I,AApIpC,IAAI,CAoIuCkD,aAAa,CAAG,CACpDyF,KAAM9H,EAAO+H,SAAS,CAAC,YAAa,EACxC,EAAI,KAAK,IACJC,OAAO,CAAC,CAAEH,SAAU,CAAE,EAAGH,GAElC,MAEI9G,EAAK8C,GACLA,EAAQwD,IAAI,CAACpF,EAAM0C,EAAYrF,AA5IlC,IAAI,CA4IqCkD,aAAa,CAAG,CAClDyF,KAAM9H,EAAO+H,SAAS,CAAC,YAAa,EACxC,EAAI,KAAK,SAIb5I,AAlJC,IAAI,CAkJE2D,KAAK,CAAGa,EAAQkD,QAAQ,CAAC/D,KAAK,CAAC9C,EAAO+H,SAAS,CAAC,YAAa,IAC/Db,IAAI,CAAC1C,GACLyD,GAAG,CAAC9I,AApJR,IAAI,CAoJW+I,KAAK,CAEzB/I,CAtJK,IAAI,CAsJFkD,aAAa,CAAG,CAAA,CAC3B,CACJ,MAEI7D,EAAOxB,SAAS,CAACmG,UAAU,CAAC1E,KAAK,CA1JxB,IAAI,CA0J6BC,UAElD,EAKAyD,EAAiBnF,SAAS,CAAC2J,qBAAqB,CAAG,SAAUvD,CAAO,CAAE+C,CAAc,CAAEC,CAAe,CAAE+B,CAAkB,CAAEnI,CAAM,CAAEoI,CAAe,CAAEC,CAAa,EAM7J,IAAK,IALDtJ,EACA2H,EAAqB,IAAI5B,kBAAkBqB,EAAiBC,EAAkB,GAAIkC,EAASvG,EAAsB,AAA+C,OAA9ChD,CAAAA,EAAKqE,EAAQqB,UAAU,CAAC7F,OAAO,CAAC2J,QAAQ,AAAD,GAAexJ,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,CAAE,CAAC,EAAE,CAAE,GAAIyJ,EAAcxI,EAAOG,KAAK,CAAG,IAAKsI,EAAe,GAAKzI,EAAOI,MAAM,CAAG,IAClR4D,EAAI,GAGCwB,EAAI,EAAGA,EAAIkB,EAAmBpB,MAAM,CAAEE,GAAK,EAAG,CACnD,IAAI3G,EAAI,AAAC2G,EAAI,EAAKW,CACR,CAAA,IAANtH,GACAmF,IAEJ,IAAI0E,EAAkBtF,EAAQuF,cAAc,CAAC,CACrC9J,EAAGuJ,EAAkBvJ,EACrBmF,EAAGqE,EAAgBrE,CACvB,GACJ,GAAI0E,EAAiB,CAEbA,EAAgBzJ,GAAG,CAAG,KAAOqJ,GAC7BI,EAAgBzJ,GAAG,CAAG,IAAMqJ,GAC5BI,CAAAA,EAAgBzJ,GAAG,CACf+C,EAAkB0G,EAAgBzJ,GAAG,CAAA,EAE7C,IAAI2J,EAAY,CACRF,EAAgBzJ,GAAG,CACnByJ,EAAgB1J,GAAG,CACtB,CACD6J,EAAaD,CAAS,CAAC,EAAE,CAAGJ,EAAcxI,EAAOG,KAAK,CAAG,EACzD2I,EAAaF,CAAS,CAAC,EAAE,CAAGH,EACxBzI,EAAOI,MAAM,CAAG,EACxB,GAAIyI,GAAc,GACdA,GAAc7I,EAAOG,KAAK,EAC1B2I,GAAc,GACdA,GAAc9I,EAAOI,MAAM,CAAE,CAC7B,IAAI2I,EAEA9G,KAAKC,KAAK,CAAC4G,GACP9I,EAAOG,KAAK,CAAG,EAEf8B,AAAyB,EAAzBA,KAAKkD,KAAK,CAAC0D,EACnBnC,CAAAA,CAAkB,CAAClB,EAAE,CACjB2C,EAAmBa,IAAI,CAACD,EAAO,CACnCrC,CAAkB,CAAClB,EAAI,EAAE,CACrB2C,EAAmBa,IAAI,CAACD,EAAS,EAAE,CACvCrC,CAAkB,CAAClB,EAAI,EAAE,CACrB2C,EAAmBa,IAAI,CAACD,EAAS,EAAE,CACvCrC,CAAkB,CAAClB,EAAI,EAAE,CACrB2C,EAAmBa,IAAI,CAACD,EAAS,EAAE,AAC3C,CACJ,CACJ,CACA,OAAOrC,CACX,EACAvE,EAAiBnF,SAAS,CAACiM,WAAW,CAAG,SAAUC,CAAC,CAAEC,CAAQ,EAC1D,IACI9F,EAAQ,IAAI,CAACA,KAAK,CAClBD,EAAUC,EAAMD,OAAO,CAC3B,GAAIA,GACAjE,AAJS,IAAI,CAINoE,MAAM,EACbpE,AALS,IAAI,CAKN2D,KAAK,EACZO,EAAM+F,OAAO,EACb/F,EAAM+F,OAAO,CAACxK,OAAO,CAAC4D,OAAO,EAC7B,GAEA,CAACa,EAAMgG,OAAO,CAACC,UAAU,EAEpB,CAAA,AAAkC,KAAlC,CAACnK,AAZG,IAAI,CAYA2D,KAAK,CAACoE,IAAI,CAAC,aAChB,CAAC/H,AAbA,IAAI,CAaG2D,KAAK,CAACoE,IAAI,CAAC,aAAe,GAAG,EAAI,CAC7C,IAAIpD,EAAUV,EAAQW,sBAAsB,CAAC,CACrClF,EAAGM,AAfN,IAAI,CAeSoE,MAAM,CAAC/D,EAAE,CACnBwE,EAAG7E,AAhBN,IAAI,CAgBSoE,MAAM,CAACU,EAAE,AACvB,GACAC,EAAcd,EAAQW,sBAAsB,CAAC,CACzClF,EAAGM,AAnBN,IAAI,CAmBSoE,MAAM,CAACY,EAAE,CACnBH,EAAG7E,AApBN,IAAI,CAoBSoE,MAAM,CAAC9D,EAAE,AACvB,GAEJ,GADA4D,EAAMgG,OAAO,CAACE,SAAS,CAACL,GACpBA,EAAEjK,GAAG,EAAIiK,EAAElK,GAAG,EACd8E,GAAWI,GACXgF,EAAEM,MAAM,CAAGnG,EAAMoG,QAAQ,CAAG3F,EAAQjF,CAAC,EACrCqK,EAAEM,MAAM,CAAGnG,EAAMoG,QAAQ,CAAGvF,EAAYrF,CAAC,EACzCqK,EAAEQ,MAAM,CAAGrG,EAAMsG,OAAO,CAAG7F,EAAQE,CAAC,EACpCkF,EAAEQ,MAAM,CAAGrG,EAAMsG,OAAO,CAAGzF,EAAYF,CAAC,CACxC,OAAO,IAAI,CAAC4F,YAAY,CAAC,CACrBC,QAASX,EAAEM,MAAM,CACjBvK,IAAK+C,EAAkBkH,EAAEjK,GAAG,EAC5BD,IAAKkK,EAAElK,GAAG,AACd,EAAGmK,EAAUD,EAErB,MAEI7F,EAAM+F,OAAO,CAACxG,OAAO,GAGjC,EAoBAT,EAAiB2H,cAAc,CAAGhI,EAAMN,EAAUsI,cAAc,CAAE,CAC9DC,UAAW,cACXX,QAAS,CACLY,YAAa,+DACjB,EAeAC,YAAa,EAWb5K,QAAS,EAqBTE,QAAS,EACT2K,eAAgB,CAAA,EAahBjH,cAAe,CAMXT,QAAS,CAAA,EAeTU,KAAM,CACV,CACJ,GACOf,CACX,EAAEX,GACFC,EAASU,EAAkB,4BAA6B,WACpD,IAAI,CAACE,aAAa,CAAG,CAAA,EACrB,IAAI,CAACc,UAAU,EACnB,GACAxB,EAAOQ,EAAiBnF,SAAS,CAAE,CAC/B2C,KAAM,aACNwK,YAAatJ,EACbxC,WA7kB2DE,EA8kB3D6L,cAAe,CAAC,MAAO,MAAO,QAAQ,CACtCC,YAAa,CAAC,MAAO,MAAM,AAC/B,GACA7M,IAA0I8M,kBAAkB,CAAC,aAAcnI,GA4G9I,IAAI/E,EAAmBE,IAG1C,OADYH,EAAoB,OAAU,AAE3C,GAET"}