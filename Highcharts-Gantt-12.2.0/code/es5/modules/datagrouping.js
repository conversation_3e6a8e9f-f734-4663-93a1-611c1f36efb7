!function(t,i){"object"==typeof exports&&"object"==typeof module?module.exports=i(require("highcharts"),require("highcharts").SeriesRegistry,require("highcharts").Templating):"function"==typeof define&&define.amd?define("highcharts/modules/datagrouping",[["highcharts/highcharts"],["highcharts/highcharts","SeriesRegistry"],["highcharts/highcharts","Templating"]],i):"object"==typeof exports?exports["highcharts/modules/datagrouping"]=i(require("highcharts"),require("highcharts").SeriesRegistry,require("highcharts").Templating):t.Highcharts=i(t.Highcharts,t.Highcharts.SeriesRegistry,t.Highcharts.Templating)}(this,function(t,i,e){return function(){"use strict";var o,r,n,a,s={512:function(t){t.exports=i},944:function(i){i.exports=t},984:function(t){t.exports=e}},u={};function l(t){var i=u[t];if(void 0!==i)return i.exports;var e=u[t]={exports:{}};return s[t](e,e.exports,l),e.exports}l.n=function(t){var i=t&&t.__esModule?function(){return t.default}:function(){return t};return l.d(i,{a:i}),i},l.d=function(t,i){for(var e in i)l.o(i,e)&&!l.o(t,e)&&Object.defineProperty(t,e,{enumerable:!0,get:i[e]})},l.o=function(t,i){return Object.prototype.hasOwnProperty.call(t,i)};var h={};l.d(h,{default:function(){return tx}});var p=l(944),c=l.n(p),d={},g=c().arrayMax,f=c().arrayMin,m=c().correctFloat,v=c().extend,y=c().isNumber;function x(t){var i=t.length,e=G(t);return y(e)&&i&&(e=m(e/i)),e}function G(t){var i,e=t.length;if(!e&&t.hasNulls)i=null;else if(e)for(i=0;e--;)i+=t[e];return i}var M={average:x,averages:function(){var t=[];return[].forEach.call(arguments,function(i){t.push(x(i))}),void 0===t[0]?void 0:t},close:function(t){return t.length?t[t.length-1]:t.hasNulls?null:void 0},high:function(t){return t.length?g(t):t.hasNulls?null:void 0},hlc:function(t,i,e){if(t=d.high(t),i=d.low(i),e=d.close(e),y(t)||y(i)||y(e))return[t,i,e]},low:function(t){return t.length?f(t):t.hasNulls?null:void 0},ohlc:function(t,i,e,o){if(t=d.open(t),i=d.high(i),e=d.low(e),o=d.close(o),y(t)||y(i)||y(e)||y(o))return[t,i,e,o]},open:function(t){return t.length?t[0]:t.hasNulls?null:void 0},range:function(t,i){return(t=d.low(t),i=d.high(i),y(t)||y(i))?[t,i]:null===t&&null===i?null:void 0},sum:G};v(d,M);var b={common:{groupPixelWidth:2,dateTimeLabelFormats:{millisecond:["%[AebHMSL]","%[AebHMSL]","-%[HMSL]"],second:["%[AebHMS]","%[AebHMS]","-%[HMS]"],minute:["%[AebHM]","%[AebHM]","-%[HM]"],hour:["%[AebHM]","%[AebHM]","-%[HM]"],day:["%[AebY]","%[Aeb]","-%[AebY]"],week:["%v %[AebY]","%[Aeb]","-%[AebY]"],month:["%[BY]","%[B]","-%[BY]"],year:["%Y","%Y","-%Y"]}},seriesSpecific:{line:{},spline:{},area:{},areaspline:{},arearange:{},column:{groupPixelWidth:10},columnrange:{groupPixelWidth:10},candlestick:{groupPixelWidth:10},ohlc:{groupPixelWidth:5},hlc:{groupPixelWidth:5},heikinashi:{groupPixelWidth:10}},units:[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1]],["week",[1]],["month",[1,3,6]],["year",null]]},A=c().addEvent,T=c().extend,D=c().merge,C=c().pick;function P(t){var i=this,e=i.series;e.forEach(function(t){t.groupPixelWidth=void 0}),e.forEach(function(e){e.groupPixelWidth=i.getGroupPixelWidth&&i.getGroupPixelWidth(),e.groupPixelWidth&&(e.hasProcessed=!0),e.applyGrouping(!!t.hasExtremesChanged)})}function S(){for(var t,i,e=this.series,o=e.length,r=0,n=!1;o--;)(i=e[o].options.dataGrouping)&&(r=Math.max(r,C(i.groupPixelWidth,b.common.groupPixelWidth)),t=(e[o].dataTable.modified||e[o].dataTable).rowCount,(e[o].groupPixelWidth||t>this.chart.plotSizeX/r||t&&i.forced)&&(n=!0));return n?r:0}function w(){this.series.forEach(function(t){t.hasProcessed=!1})}function k(t,i){var e;if(i=C(i,!0),t||(t={forced:!1,units:null}),this instanceof r)for(e=this.series.length;e--;)this.series[e].update({dataGrouping:t},!1);else this.chart.options.series.forEach(function(i){i.dataGrouping="boolean"==typeof t?t:D(t,i.dataGrouping)});this.ordinal&&(this.ordinal.slope=void 0),i&&this.chart.redraw()}var R=function(t){r=t;var i=t.prototype;i.applyGrouping||(A(t,"afterSetScale",w),A(t,"postProcessData",P),T(i,{applyGrouping:P,getGroupPixelWidth:S,setDataGrouping:k}))},F=function(t,i,e){if(e||2==arguments.length)for(var o,r=0,n=i.length;r<n;r++)!o&&r in i||(o||(o=Array.prototype.slice.call(i,0,r)),o[r]=i[r]);return t.concat(o||Array.prototype.slice.call(i))};(o=n||(n={})).setLength=function(t,i,e){return Array.isArray(t)?(t.length=i,t):t[e?"subarray":"slice"](0,i)},o.splice=function(t,i,e,o,r){if(void 0===r&&(r=[]),Array.isArray(t))return Array.isArray(r)||(r=Array.from(r)),{removed:t.splice.apply(t,F([i,e],r,!1)),array:t};var n=Object.getPrototypeOf(t).constructor,a=t[o?"subarray":"slice"](i,i+e),s=new n(t.length-e+r.length);return s.set(t.subarray(0,i),0),s.set(r,i),s.set(t.subarray(i+e),i+r.length),{removed:a,array:s}};var O=n,W=O.setLength,E=O.splice,H=c().fireEvent,N=c().objectEach,I=c().uniqueKey,q=function(){function t(t){void 0===t&&(t={});var i=this;this.autoId=!t.id,this.columns={},this.id=t.id||I(),this.modified=this,this.rowCount=0,this.versionTag=I();var e=0;N(t.columns||{},function(t,o){i.columns[o]=t.slice(),e=Math.max(e,t.length)}),this.applyRowCount(e)}return t.prototype.applyRowCount=function(t){var i=this;this.rowCount=t,N(this.columns,function(e,o){e.length!==t&&(i.columns[o]=W(e,t))})},t.prototype.deleteRows=function(t,i){var e=this;if(void 0===i&&(i=1),i>0&&t<this.rowCount){var o=0;N(this.columns,function(r,n){e.columns[n]=E(r,t,i).array,o=r.length}),this.rowCount=o}H(this,"afterDeleteRows",{rowIndex:t,rowCount:i}),this.versionTag=I()},t.prototype.getColumn=function(t,i){return this.columns[t]},t.prototype.getColumns=function(t,i){var e=this;return(t||Object.keys(this.columns)).reduce(function(t,i){return t[i]=e.columns[i],t},{})},t.prototype.getRow=function(t,i){var e=this;return(i||Object.keys(this.columns)).map(function(i){var o;return null===(o=e.columns[i])||void 0===o?void 0:o[t]})},t.prototype.setColumn=function(t,i,e,o){var r;void 0===i&&(i=[]),void 0===e&&(e=0),this.setColumns(((r={})[t]=i,r),e,o)},t.prototype.setColumns=function(t,i,e){var o=this,r=this.rowCount;N(t,function(t,i){o.columns[i]=t.slice(),r=t.length}),this.applyRowCount(r),(null==e?void 0:e.silent)||(H(this,"afterSetColumns"),this.versionTag=I())},t.prototype.setRow=function(t,i,e,o){void 0===i&&(i=this.rowCount);var r=this.columns,n=e?this.rowCount+1:i+1;N(t,function(t,a){var s=r[a]||(null==o?void 0:o.addColumns)!==!1&&Array(n);s&&(e?s=E(s,i,0,!0,[t]).array:s[i]=t,r[a]=s)}),n>this.rowCount&&this.applyRowCount(n),(null==o?void 0:o.silent)||(H(this,"afterSetRows"),this.versionTag=I())},t}(),L=c().addEvent,j=c().getMagnitude,Y=c().normalizeTickInterval,z=c().timeUnits;!function(t){function i(){return this.chart.time.getTimeTicks.apply(this.chart.time,arguments)}function e(){if("datetime"!==this.type){this.dateTime=void 0;return}this.dateTime||(this.dateTime=new o(this))}t.compose=function(t){return t.keepProps.includes("dateTime")||(t.keepProps.push("dateTime"),t.prototype.getTimeTicks=i,L(t,"afterSetType",e)),t};var o=function(){function t(t){this.axis=t}return t.prototype.normalizeTimeTickInterval=function(t,i){var e,o=i||[["millisecond",[1,2,5,10,20,25,50,100,200,500]],["second",[1,2,5,10,15,30]],["minute",[1,2,5,10,15,30]],["hour",[1,2,3,4,6,8,12]],["day",[1,2]],["week",[1,2]],["month",[1,2,3,4,6]],["year",null]],r=o[o.length-1],n=z[r[0]],a=r[1];for(e=0;e<o.length&&(n=z[(r=o[e])[0]],a=r[1],!o[e+1]||!(t<=(n*a[a.length-1]+z[o[e+1][0]])/2));e++);n===z.year&&t<5*n&&(a=[1,2,5]);var s=Y(t/n,a,"year"===r[0]?Math.max(j(t/n),1):1);return{unitRange:n,count:s,unitName:r[0]}},t.prototype.getXDateFormat=function(t,i){var e=this.axis,o=e.chart.time;return e.closestPointRange?o.getDateFormat(e.closestPointRange,t,e.options.startOfWeek,i)||o.resolveDTLFormat(i.year).main:o.resolveDTLFormat(i.day).main},t}();t.Additions=o}(a||(a={}));var X=a,B=l(512),U=l.n(B)().series.prototype,_=c().addEvent,K=c().defined,J=c().error,Q=c().extend,V=c().isNumber,Z=c().merge,$=c().pick,tt=c().splat,ti=U.generatePoints;function te(t){var i,e,o,r,n=this.chart,a=this.options.dataGrouping,s=!1!==this.allowDG&&a&&$(a.enabled,n.options.isStock),u=this.reserveSpace(),l=this.currentDataGrouping,h=!1;s&&!this.requireSorting&&(this.requireSorting=h=!0);var p=!1==(x=this,G=t,!(x.isCartesian&&!x.isDirty&&!x.xAxis.isDirty&&!x.yAxis.isDirty&&!G))||!s;if(h&&(this.requireSorting=!1),!p){this.destroyGroupedData();var c=a.groupAll?this.dataTable:this.dataTable.modified||this.dataTable,d=this.getColumn("x",!a.groupAll),g=n.plotSizeX,f=this.xAxis,m=f.getExtremes(),v=f.options.ordinal,y=this.groupPixelWidth;if(y&&d&&c.rowCount&&g&&V(m.min)){e=!0,this.isDirty=!0,this.points=null;var x,G,M,A=m.min,T=m.max,D=v&&f.ordinal&&f.ordinal.getGroupIntervalFactor(A,T,this)||1,C=y*(T-A)/g*D,P=f.getTimeTicks(X.Additions.prototype.normalizeTimeTickInterval(C,a.units||b.units),Math.min(A,d[0]),Math.max(T,d[d.length-1]),f.options.startOfWeek,d,this.closestPointRange),S=U.groupData.apply(this,[c,P,a.approximation]),w=S.modified,k=w.getColumn("x",!0),R=0;for((null==a?void 0:a.smoothed)&&w.rowCount&&(a.firstAnchor="firstPoint",a.anchor="middle",a.lastAnchor="lastPoint",J(32,!1,n,{"dataGrouping.smoothed":"use dataGrouping.anchor"})),i=1;i<P.length;i++)P.info.segmentStarts&&-1!==P.info.segmentStarts.indexOf(i)||(R=Math.max(P[i]-P[i-1],R));(o=P.info).gapSize=R,this.closestPointRange=P.info.totalRange,this.groupMap=S.groupMap,this.currentDataGrouping=o,!function(t,i,e){var o=t.options.dataGrouping,r=t.currentDataGrouping&&t.currentDataGrouping.gapSize,n=t.getColumn("x");if(o&&n.length&&r&&t.groupMap){var a=i.length-1,s=o.anchor,u=o.firstAnchor,l=o.lastAnchor,h=i.length-1,p=0;if(u&&n[0]>=i[0]){p++;var c=t.groupMap[0].start,d=t.groupMap[0].length,g=void 0;V(c)&&V(d)&&(g=c+(d-1)),i[0]=({start:i[0],middle:i[0]+.5*r,end:i[0]+r,firstPoint:n[0],lastPoint:g&&n[g]})[u]}if(a>0&&l&&r&&i[a]>=e-r){h--;var f=t.groupMap[t.groupMap.length-1].start;i[a]=({start:i[a],middle:i[a]+.5*r,end:i[a]+r,firstPoint:f&&n[f],lastPoint:n[n.length-1]})[l]}if(s&&"start"!==s)for(var m=r*({middle:.5,end:1})[s];h>=p;)i[h]+=m,h--}}(this,k||[],T),u&&k&&(K((M=k)[0])&&V(f.min)&&V(f.dataMin)&&M[0]<f.min&&((!K(f.options.min)&&f.min<=f.dataMin||f.min===f.dataMin)&&(f.min=Math.min(M[0],f.min)),f.dataMin=Math.min(M[0],f.dataMin)),K(M[M.length-1])&&V(f.max)&&V(f.dataMax)&&M[M.length-1]>f.max&&((!K(f.options.max)&&V(f.dataMax)&&f.max>=f.dataMax||f.max===f.dataMax)&&(f.max=Math.max(M[M.length-1],f.max)),f.dataMax=Math.max(M[M.length-1],f.dataMax))),a.groupAll&&(this.allGroupedTable=w,k=(w=(r=this.cropData(w,f.min||0,f.max||0)).modified).getColumn("x"),this.cropStart=r.start),this.dataTable.modified=w}else this.groupMap=void 0,this.currentDataGrouping=void 0;this.hasGroupedData=e,this.preventGraphAnimation=(l&&l.totalRange)!==(o&&o.totalRange)}}function to(){this.groupedData&&(this.groupedData.forEach(function(t,i){t&&(this.groupedData[i]=t.destroy?t.destroy():null)},this),this.groupedData.length=0,delete this.allGroupedTable)}function tr(){ti.apply(this),this.destroyGroupedData(),this.groupedData=this.hasGroupedData?this.points:null}function tn(){return this.is("arearange")?"range":this.is("ohlc")?"ohlc":this.is("hlc")?"hlc":this.is("column")||this.options.cumulative?"sum":"average"}function ta(t,i,e){var o,r,n,a=t.getColumn("x",!0)||[],s=t.getColumn("y",!0),u=this,l=u.data,h=u.options&&u.options.data,p=[],c=new q,g=[],f=t.rowCount,m=!!s,v=[],y=u.pointArrayMap,x=y&&y.length,G=["x"].concat(y||["y"]),M=(y||["y"]).map(function(){return[]}),b=this.options.dataGrouping&&this.options.dataGrouping.groupAll,A=0,T=0,D="function"==typeof e?e:e&&d[e]?d[e]:d[u.getDGApproximation&&u.getDGApproximation()||"average"];if(x)for(var C=y.length;C--;)v.push([]);else v.push([]);for(var P=x||1,S=0;S<=f;S++)if(!(a[S]<i[0])){for(;void 0!==i[A+1]&&a[S]>=i[A+1]||S===f;){if(o=i[A],u.dataGroupInfo={start:b?T:u.cropStart+T,length:v[0].length,groupStart:o},n=D.apply(u,v),u.pointClass&&!K(u.dataGroupInfo.options)&&(u.dataGroupInfo.options=Z(u.pointClass.prototype.optionsToObject.call({series:u},u.options.data[u.cropStart+T])),G.forEach(function(t){delete u.dataGroupInfo.options[t]})),void 0!==n){p.push(o);for(var w=tt(n),k=0;k<w.length;k++)M[k].push(w[k]);g.push(u.dataGroupInfo)}T=S;for(var k=0;k<P;k++)v[k].length=0,v[k].hasNulls=!1;if(A+=1,S===f)break}if(S===f)break;if(y)for(var R=b?S:u.cropStart+S,F=l&&l[R]||u.pointClass.prototype.applyOptions.apply({series:u},[h[R]]),O=void 0,k=0;k<x;k++)V(O=F[y[k]])?v[k].push(O):null===O&&(v[k].hasNulls=!0);else V(r=m?s[S]:null)?v[0].push(r):null===r&&(v[0].hasNulls=!0)}var W={x:p};return(y||["y"]).forEach(function(t,i){W[t]=M[i]}),c.setColumns(W),{groupMap:g,modified:c}}function ts(t){var i=t.options,e=this.type,o=this.chart.options.plotOptions,r=this.useCommonDataGrouping&&b.common,n=b.seriesSpecific,a=c().defaultOptions.plotOptions[e].dataGrouping;if(o&&(n[e]||r)){var s=this.chart.rangeSelector;a||(a=Z(b.common,n[e])),i.dataGrouping=Z(r,a,o.series&&o.series.dataGrouping,o[e].dataGrouping,this.userOptions.dataGrouping,!i.isInternal&&s&&V(s.selected)&&s.buttonOptions[s.selected].dataGrouping)}}var tu=function(t){var i=t.prototype;i.applyGrouping||(_(t.prototype.pointClass,"update",function(){if(this.dataGroup)return J(24,!1,this.series.chart),!1}),_(t,"afterSetOptions",ts),_(t,"destroy",to),Q(i,{applyGrouping:te,destroyGroupedData:to,generatePoints:tr,getDGApproximation:tn,groupData:ta}))},tl=l(984),th=l.n(tl)().format,tp=c().composed,tc=c().addEvent,td=c().extend,tg=c().isNumber,tf=c().pick,tm=c().pushUnique;function tv(t){var i,e,o,r,n,a,s=this.chart,u=s.time,l=t.point,h=l.series,p=h.options,c=h.tooltipOptions,d=p.dataGrouping,g=h.xAxis,f=c.xDateFormat||"",m=c[t.isFooter?"footerFormat":"headerFormat"];if(g&&"datetime"===g.options.type&&d&&tg(l.key)){o=h.currentDataGrouping,r=d.dateTimeLabelFormats||b.common.dateTimeLabelFormats,o?(n=r[o.unitName],1===o.count?f=n[0]:(f=n[1],e=n[2])):!f&&r&&g.dateTime&&(f=g.dateTime.getXDateFormat(l.x,c.dateTimeLabelFormats));var v=tf(null===(i=h.groupMap)||void 0===i?void 0:i[l.index].groupStart,l.key),y=v+((null==o?void 0:o.totalRange)||0)-1;a=u.dateFormat(f,v),e&&(a+=u.dateFormat(e,y)),h.chart.styledMode&&(m=this.styledModeFormat(m)),t.text=th(m,{point:td(l,{key:a}),series:h},s),t.preventDefault()}}var ty=c();ty.dataGrouping=ty.dataGrouping||{},ty.dataGrouping.approximationDefaults=ty.dataGrouping.approximationDefaults||M,ty.dataGrouping.approximations=ty.dataGrouping.approximations||d,({compose:function(t,i,e){R(t),tu(i),e&&tm(tp,"DataGrouping")&&tc(e,"headerFormatter",tv)},groupData:ta}).compose(ty.Axis,ty.Series,ty.Tooltip);var tx=c();return h.default}()});