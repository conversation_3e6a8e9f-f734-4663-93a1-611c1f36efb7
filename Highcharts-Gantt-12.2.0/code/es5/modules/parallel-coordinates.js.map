{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/parallel-coordinates\n * @requires highcharts\n *\n * Support for parallel coordinates in Highcharts\n *\n * (c) 2010-2025 Pawel Fus\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"Templating\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/parallel-coordinates\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"Templating\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/parallel-coordinates\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"Templating\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Templating\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__984__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ }),\n\n/***/ 984:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__984__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ parallel_coordinates_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es5/es-modules/Extensions/ParallelCoordinates/ParallelCoordinatesDefaults.js\n/* *\n *\n *  Parallel coordinates module\n *\n *  (c) 2010-2025 Pawel Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * @optionparent chart\n */\nvar chartDefaults = {\n    /**\n     * Flag to render charts as a parallel coordinates plot. In a parallel\n     * coordinates plot (||-coords) by default all required yAxes are generated\n     * and the legend is disabled. This feature requires\n     * `modules/parallel-coordinates.js`.\n     *\n     * @sample {highcharts} /highcharts/demo/parallel-coordinates/\n     *         Parallel coordinates demo\n     * @sample {highcharts} highcharts/parallel-coordinates/polar/\n     *         Star plot, multivariate data in a polar chart\n     *\n     * @since    6.0.0\n     * @product  highcharts\n     * @requires modules/parallel-coordinates\n     */\n    parallelCoordinates: false,\n    /**\n     * Common options for all yAxes rendered in a parallel coordinates plot.\n     * This feature requires `modules/parallel-coordinates.js`.\n     *\n     * The default options are:\n     * ```js\n     * parallelAxes: {\n     *    lineWidth: 1,       // classic mode only\n     *    gridlinesWidth: 0,  // classic mode only\n     *    title: {\n     *        text: '',\n     *        reserveSpace: false\n     *    },\n     *    labels: {\n     *        x: 0,\n     *        y: 0,\n     *        align: 'center',\n     *        reserveSpace: false\n     *    },\n     *    offset: 0\n     * }\n     * ```\n     *\n     * @sample {highcharts} highcharts/parallel-coordinates/parallelaxes/\n     *         Set the same tickAmount for all yAxes\n     *\n     * @extends   yAxis\n     * @since     6.0.0\n     * @product   highcharts\n     * @excluding alternateGridColor, breaks, id, gridLineColor,\n     *            gridLineDashStyle, gridLineWidth, minorGridLineColor,\n     *            minorGridLineDashStyle, minorGridLineWidth, plotBands,\n     *            plotLines, angle, gridLineInterpolation, maxColor, maxZoom,\n     *            minColor, scrollbar, stackLabels, stops,\n     * @requires  modules/parallel-coordinates\n     */\n    parallelAxes: {\n        lineWidth: 1,\n        /**\n         * Titles for yAxes are taken from\n         * [xAxis.categories](#xAxis.categories). All options for `xAxis.labels`\n         * applies to parallel coordinates titles. For example, to style\n         * categories, use [xAxis.labels.style](#xAxis.labels.style).\n         *\n         * @excluding align, enabled, margin, offset, position3d, reserveSpace,\n         *            rotation, skew3d, style, text, useHTML, x, y\n         */\n        title: {\n            text: '',\n            reserveSpace: false\n        },\n        labels: {\n            x: 0,\n            y: 4,\n            align: 'center',\n            reserveSpace: false\n        },\n        offset: 0\n    }\n};\nvar xAxisDefaults = {\n    lineWidth: 0,\n    tickLength: 0,\n    opposite: true,\n    type: 'category'\n};\n/**\n * Parallel coordinates only. Format that will be used for point.y\n * and available in [tooltip.pointFormat](#tooltip.pointFormat) as\n * `{point.formattedValue}`. If not set, `{point.formattedValue}`\n * will use other options, in this order:\n *\n * 1. [yAxis.labels.format](#yAxis.labels.format) will be used if\n *    set\n *\n * 2. If yAxis is a category, then category name will be displayed\n *\n * 3. If yAxis is a datetime, then value will use the same format as\n *    yAxis labels\n *\n * 4. If yAxis is linear/logarithmic type, then simple value will be\n *    used\n *\n * @sample {highcharts}\n *         /highcharts/parallel-coordinates/tooltipvalueformat/\n *         Different tooltipValueFormats's\n *\n * @type      {string}\n * @default   undefined\n * @since     6.0.0\n * @product   highcharts\n * @requires  modules/parallel-coordinates\n * @apioption yAxis.tooltipValueFormat\n */\n''; // Keeps doclets above separate in JS file\n/* *\n *\n *  Default Options\n *\n * */\nvar ParallelCoordinatesDefaults = {\n    chart: chartDefaults,\n    xAxis: xAxisDefaults\n};\n/* harmony default export */ var ParallelCoordinates_ParallelCoordinatesDefaults = (ParallelCoordinatesDefaults);\n\n;// ./code/es5/es-modules/Extensions/ParallelCoordinates/ParallelAxis.js\n/* *\n *\n *  Parallel coordinates module\n *\n *  (c) 2010-2025 Pawel Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\n\n\nvar addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, arrayMax = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).arrayMax, arrayMin = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).arrayMin, isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Class\n *\n * */\n/**\n * Support for parallel axes.\n * @private\n * @class\n */\nvar ParallelAxisAdditions = /** @class */ (function () {\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    function ParallelAxisAdditions(axis) {\n        this.axis = axis;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Set predefined left+width and top+height (inverted) for yAxes.\n     * This method modifies options param.\n     *\n     * @private\n     *\n     * @param  {Array<string>} axisPosition\n     * ['left', 'width', 'height', 'top'] or ['top', 'height', 'width', 'left']\n     * for an inverted chart.\n     *\n     * @param  {Highcharts.AxisOptions} options\n     * Axis options.\n     */\n    ParallelAxisAdditions.prototype.setPosition = function (axisPosition, options) {\n        var parallel = this,\n            axis = parallel.axis,\n            chart = axis.chart,\n            fraction = ((parallel.position || 0) + 0.5) /\n                (chart.parallelInfo.counter + 1);\n        if (chart.polar) {\n            options.angle = 360 * fraction;\n        }\n        else {\n            options[axisPosition[0]] = 100 * fraction + '%';\n            axis[axisPosition[1]] = options[axisPosition[1]] = 0;\n            // In case of chart.update(inverted), remove old options:\n            axis[axisPosition[2]] = options[axisPosition[2]] = null;\n            axis[axisPosition[3]] = options[axisPosition[3]] = null;\n        }\n    };\n    return ParallelAxisAdditions;\n}());\n/* *\n *\n *  Composition\n *\n * */\nvar ParallelAxis;\n(function (ParallelAxis) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Adds support for parallel axes.\n     * @private\n     */\n    function compose(AxisClass) {\n        if (!AxisClass.keepProps.includes('parallel')) {\n            var axisCompo = AxisClass;\n            // On update, keep parallel additions.\n            AxisClass.keepProps.push('parallel');\n            addEvent(axisCompo, 'init', onInit);\n            addEvent(axisCompo, 'afterSetOptions', onAfterSetOptions);\n            addEvent(axisCompo, 'getSeriesExtremes', onGetSeriesExtremes);\n        }\n    }\n    ParallelAxis.compose = compose;\n    /**\n     * Update default options with predefined for a parallel coords.\n     * @private\n     */\n    function onAfterSetOptions(e) {\n        var axis = this,\n            chart = axis.chart,\n            parallelCoordinates = axis.parallelCoordinates;\n        var axisPosition = [\n                'left', 'width', 'height', 'top'\n            ];\n        if (chart.hasParallelCoordinates) {\n            if (chart.inverted) {\n                axisPosition = axisPosition.reverse();\n            }\n            if (axis.isXAxis) {\n                axis.options = merge(axis.options, ParallelCoordinates_ParallelCoordinatesDefaults.xAxis, e.userOptions);\n            }\n            else {\n                var axisIndex = chart.yAxis.indexOf(axis); // #13608\n                    axis.options = merge(axis.options,\n                    axis.chart.options.chart.parallelAxes,\n                    e.userOptions);\n                parallelCoordinates.position = pick(parallelCoordinates.position, axisIndex >= 0 ? axisIndex : chart.yAxis.length);\n                parallelCoordinates.setPosition(axisPosition, axis.options);\n            }\n        }\n    }\n    /**\n     * Each axis should gather extremes from points on a particular position in\n     * series.data. Not like the default one, which gathers extremes from all\n     * series bind to this axis. Consider using series.points instead of\n     * series.yData.\n     * @private\n     */\n    function onGetSeriesExtremes(e) {\n        var axis = this;\n        var chart = axis.chart;\n        var parallelCoordinates = axis.parallelCoordinates;\n        if (!parallelCoordinates) {\n            return;\n        }\n        if (chart && chart.hasParallelCoordinates && !axis.isXAxis) {\n            var index_1 = parallelCoordinates.position;\n            var currentPoints_1 = [];\n            axis.series.forEach(function (series) {\n                if (series.visible && isNumber(index_1)) {\n                    currentPoints_1 = (series.pointArrayMap || ['y'])\n                        .reduce(function (currentPoints, key) {\n                        var _a,\n                            _b;\n                        return __spreadArray(__spreadArray([], currentPoints, true), [\n                            (_b = (_a = series.getColumn(key)) === null || _a === void 0 ? void 0 : _a[index_1]) !== null && _b !== void 0 ? _b : null\n                        ], false);\n                    }, currentPoints_1);\n                }\n            });\n            currentPoints_1 = currentPoints_1.filter(isNumber);\n            axis.dataMin = arrayMin(currentPoints_1);\n            axis.dataMax = arrayMax(currentPoints_1);\n            e.preventDefault();\n        }\n    }\n    /**\n     * Add parallel addition\n     * @private\n     */\n    function onInit() {\n        var axis = this;\n        if (!axis.parallelCoordinates) {\n            axis.parallelCoordinates = new ParallelAxisAdditions(axis);\n        }\n    }\n})(ParallelAxis || (ParallelAxis = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var ParallelCoordinates_ParallelAxis = (ParallelAxis);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Templating\"],\"commonjs\":[\"highcharts\",\"Templating\"],\"commonjs2\":[\"highcharts\",\"Templating\"],\"root\":[\"Highcharts\",\"Templating\"]}\nvar highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_ = __webpack_require__(984);\nvar highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default = /*#__PURE__*/__webpack_require__.n(highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_);\n;// ./code/es5/es-modules/Extensions/ParallelCoordinates/ParallelSeries.js\n/* *\n *\n *  Parallel coordinates module\n *\n *  (c) 2010-2025 Pawel Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar composed = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).composed;\n\nvar format = (highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default()).format;\n\nvar ParallelSeries_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, erase = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).erase, extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, insertItem = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).insertItem, isArray = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isArray, ParallelSeries_isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, pushUnique = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pushUnique;\n/* *\n *\n *  Composition\n *\n * */\nvar ParallelSeries;\n(function (ParallelSeries) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /** @private */\n    function compose(SeriesClass) {\n        if (pushUnique(composed, 'ParallelSeries')) {\n            var CompoClass = SeriesClass;\n            ParallelSeries_addEvent(CompoClass, 'afterTranslate', onSeriesAfterTranslate, { order: 1 });\n            ParallelSeries_addEvent(CompoClass, 'bindAxes', onSeriesBindAxes);\n            ParallelSeries_addEvent(CompoClass, 'destroy', onSeriesDestroy);\n            ParallelSeries_addEvent(SeriesClass, 'afterGeneratePoints', onSeriesAfterGeneratePoints);\n        }\n    }\n    ParallelSeries.compose = compose;\n    /**\n     * Translate each point using corresponding yAxis.\n     * @private\n     */\n    function onSeriesAfterTranslate() {\n        var series = this,\n            chart = this.chart,\n            points = series.points,\n            dataLength = points && points.length;\n        var closestPointRangePx = Number.MAX_VALUE,\n            lastPlotX,\n            point;\n        if (this.chart.hasParallelCoordinates) {\n            for (var i = 0; i < dataLength; i++) {\n                point = points[i];\n                if (defined(point.y)) {\n                    if (chart.polar) {\n                        point.plotX = chart.yAxis[i].angleRad || 0;\n                    }\n                    else if (chart.inverted) {\n                        point.plotX = (chart.plotHeight -\n                            chart.yAxis[i].top +\n                            chart.plotTop);\n                    }\n                    else {\n                        point.plotX = chart.yAxis[i].left - chart.plotLeft;\n                    }\n                    point.clientX = point.plotX;\n                    point.plotY = chart.yAxis[i]\n                        .translate(point.y, false, true, void 0, true);\n                    // Range series (#15752)\n                    if (ParallelSeries_isNumber(point.high)) {\n                        point.plotHigh = chart.yAxis[i].translate(point.high, false, true, void 0, true);\n                    }\n                    if (typeof lastPlotX !== 'undefined') {\n                        closestPointRangePx = Math.min(closestPointRangePx, Math.abs(point.plotX - lastPlotX));\n                    }\n                    lastPlotX = point.plotX;\n                    point.isInside = chart.isInsidePlot(point.plotX, point.plotY, { inverted: chart.inverted });\n                }\n                else {\n                    point.isNull = true;\n                }\n            }\n            this.closestPointRangePx = closestPointRangePx;\n        }\n    }\n    /**\n     * Bind each series to each yAxis. yAxis needs a reference to all series to\n     * calculate extremes.\n     * @private\n     */\n    function onSeriesBindAxes(e) {\n        var series = this,\n            chart = series.chart;\n        if (chart.hasParallelCoordinates) {\n            var series_1 = this;\n            for (var _i = 0, _a = chart.axes; _i < _a.length; _i++) {\n                var axis = _a[_i];\n                insertItem(series_1, axis.series);\n                axis.isDirty = true;\n            }\n            series_1.xAxis = chart.xAxis[0];\n            series_1.yAxis = chart.yAxis[0];\n            e.preventDefault();\n        }\n    }\n    /**\n     * On destroy, we need to remove series from each `axis.series`.\n     * @private\n     */\n    function onSeriesDestroy() {\n        var series = this,\n            chart = series.chart;\n        if (chart.hasParallelCoordinates) {\n            for (var _i = 0, _a = (chart.axes || []); _i < _a.length; _i++) {\n                var axis = _a[_i];\n                if (axis && axis.series) {\n                    erase(axis.series, series);\n                    axis.isDirty = axis.forceRedraw = true;\n                }\n            }\n        }\n    }\n    /**\n     * @private\n     */\n    function onSeriesAfterGeneratePoints() {\n        var _a,\n            _b,\n            _c,\n            _d,\n            _e,\n            _f;\n        var chart = this.chart;\n        if (chart === null || chart === void 0 ? void 0 : chart.hasParallelCoordinates) {\n            for (var _i = 0, _g = this.points; _i < _g.length; _i++) {\n                var point = _g[_i];\n                var yAxis = chart.yAxis[point.x || 0],\n                    yAxisOptions = yAxis.options,\n                    labelFormat = (_a = yAxisOptions.tooltipValueFormat) !== null && _a !== void 0 ? _a : yAxisOptions.labels.format;\n                var formattedValue = void 0;\n                if (labelFormat) {\n                    formattedValue = format(labelFormat, extend(point, { value: point.y }), chart);\n                }\n                else if (yAxis.dateTime) {\n                    formattedValue = chart.time.dateFormat(chart.time.resolveDTLFormat(((_b = yAxisOptions.dateTimeLabelFormats) === null || _b === void 0 ? void 0 : _b[((_c = yAxis.tickPositions.info) === null || _c === void 0 ? void 0 : _c.unitName) || 'year']) || '').main, (_d = point.y) !== null && _d !== void 0 ? _d : void 0);\n                }\n                else if (isArray(yAxisOptions.categories)) {\n                    formattedValue = yAxisOptions.categories[(_e = point.y) !== null && _e !== void 0 ? _e : -1];\n                }\n                else {\n                    formattedValue = String((_f = point.y) !== null && _f !== void 0 ? _f : '');\n                }\n                point.formattedValue = formattedValue;\n            }\n        }\n    }\n})(ParallelSeries || (ParallelSeries = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var ParallelCoordinates_ParallelSeries = (ParallelSeries);\n\n;// ./code/es5/es-modules/Extensions/ParallelCoordinates/ParallelCoordinates.js\n/* *\n *\n *  Parallel coordinates module\n *\n *  (c) 2010-2025 Pawel Fus\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\nvar ParallelCoordinates_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, ParallelCoordinates_defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, ParallelCoordinates_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, splat = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).splat;\n/* *\n *\n *  Class\n *\n * */\nvar ChartAdditions = /** @class */ (function () {\n    /* *\n     *\n     *  Constructor\n     *\n     * */\n    function ChartAdditions(chart) {\n        this.chart = chart;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Define how many parellel axes we have according to the longest dataset.\n     * This is quite heavy - loop over all series and check series.data.length\n     * Consider:\n     *\n     * - make this an option, so user needs to set this to get better\n     *   performance\n     *\n     * - check only first series for number of points and assume the rest is the\n     *   same\n     *\n     * @private\n     * @function Highcharts.Chart#setParallelInfo\n     * @param {Highcharts.Options} options\n     * User options\n     * @requires modules/parallel-coordinates\n     */\n    ChartAdditions.prototype.setParallelInfo = function (options) {\n        var chart = (this.chart ||\n                this),\n            seriesOptions = options.series;\n        chart.parallelInfo = {\n            counter: 0\n        };\n        for (var _i = 0, seriesOptions_1 = seriesOptions; _i < seriesOptions_1.length; _i++) {\n            var series = seriesOptions_1[_i];\n            if (series.data) {\n                chart.parallelInfo.counter = Math.max(chart.parallelInfo.counter, series.data.length - 1);\n            }\n        }\n    };\n    return ChartAdditions;\n}());\n/* *\n *\n *  Composition\n *\n * */\nvar ParallelCoordinates;\n(function (ParallelCoordinates) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /** @private */\n    function compose(AxisClass, ChartClass, highchartsDefaultOptions, SeriesClass) {\n        ParallelCoordinates_ParallelAxis.compose(AxisClass);\n        ParallelCoordinates_ParallelSeries.compose(SeriesClass);\n        var ChartCompo = ChartClass,\n            addsProto = ChartAdditions.prototype,\n            chartProto = ChartCompo.prototype;\n        if (!chartProto.setParallelInfo) {\n            chartProto.setParallelInfo = addsProto.setParallelInfo;\n            ParallelCoordinates_addEvent(ChartCompo, 'init', onChartInit);\n            ParallelCoordinates_addEvent(ChartCompo, 'update', onChartUpdate);\n            ParallelCoordinates_merge(true, highchartsDefaultOptions.chart, ParallelCoordinates_ParallelCoordinatesDefaults.chart);\n        }\n    }\n    ParallelCoordinates.compose = compose;\n    /**\n     * Initialize parallelCoordinates\n     * @private\n     */\n    function onChartInit(e) {\n        var chart = this,\n            options = e.args[0],\n            defaultYAxis = splat(options.yAxis || {}),\n            newYAxes = [];\n        var yAxisLength = defaultYAxis.length;\n        /**\n         * Flag used in parallel coordinates plot to check if chart has\n         * ||-coords (parallel coords).\n         *\n         * @requires modules/parallel-coordinates\n         *\n         * @name Highcharts.Chart#hasParallelCoordinates\n         * @type {boolean}\n         */\n        chart.hasParallelCoordinates = options.chart &&\n            options.chart.parallelCoordinates;\n        if (chart.hasParallelCoordinates) {\n            chart.setParallelInfo(options);\n            // Push empty yAxes in case user did not define them:\n            for (; yAxisLength <= chart.parallelInfo.counter; yAxisLength++) {\n                newYAxes.push({});\n            }\n            if (!options.legend) {\n                options.legend = {};\n            }\n            if (options.legend &&\n                typeof options.legend.enabled === 'undefined') {\n                options.legend.enabled = false;\n            }\n            ParallelCoordinates_merge(true, options, \n            // Disable boost\n            {\n                boost: {\n                    seriesThreshold: Number.MAX_VALUE\n                },\n                plotOptions: {\n                    series: {\n                        boostThreshold: Number.MAX_VALUE\n                    }\n                }\n            });\n            options.yAxis = defaultYAxis.concat(newYAxes);\n            options.xAxis = ParallelCoordinates_merge(ParallelCoordinates_ParallelCoordinatesDefaults.xAxis, // Docs\n            splat(options.xAxis || {})[0]);\n        }\n    }\n    /**\n     * Initialize parallelCoordinates\n     * @private\n     */\n    function onChartUpdate(e) {\n        var chart = this,\n            options = e.options;\n        if (options.chart) {\n            if (ParallelCoordinates_defined(options.chart.parallelCoordinates)) {\n                chart.hasParallelCoordinates =\n                    options.chart.parallelCoordinates;\n            }\n            chart.options.chart.parallelAxes = ParallelCoordinates_merge(chart.options.chart.parallelAxes, options.chart.parallelAxes);\n        }\n        if (chart.hasParallelCoordinates) {\n            // (#10081)\n            if (options.series) {\n                chart.setParallelInfo(options);\n            }\n            for (var _i = 0, _a = chart.yAxis; _i < _a.length; _i++) {\n                var axis = _a[_i];\n                axis.update({}, false);\n            }\n        }\n    }\n})(ParallelCoordinates || (ParallelCoordinates = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var ParallelCoordinates_ParallelCoordinates = (ParallelCoordinates);\n\n;// ./code/es5/es-modules/masters/modules/parallel-coordinates.js\n\n\n\n\nvar G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nParallelCoordinates_ParallelCoordinates.compose(G.Axis, G.Chart, G.defaultOptions, G.Series);\n/* harmony default export */ var parallel_coordinates_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__984__", "ParallelAxis", "ParallelSeries", "ParallelCoordinates", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "parallel_coordinates_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "ParallelCoordinates_ParallelCoordinatesDefaults", "chart", "parallelCoordinates", "parallelAxes", "lineWidth", "title", "text", "reserveSpace", "labels", "x", "y", "align", "offset", "xAxis", "tick<PERSON><PERSON>th", "opposite", "type", "__spread<PERSON><PERSON>y", "to", "from", "pack", "arguments", "length", "ar", "i", "l", "Array", "slice", "concat", "addEvent", "arrayMax", "arrayMin", "isNumber", "merge", "pick", "ParallelAxisAdditions", "axis", "setPosition", "axisPosition", "options", "parallel", "fraction", "position", "parallelInfo", "counter", "polar", "angle", "onAfterSetOptions", "e", "hasParallelCoordinates", "inverted", "reverse", "isXAxis", "userOptions", "axisIndex", "yAxis", "indexOf", "onGetSeriesExtremes", "index_1", "currentPoints_1", "series", "for<PERSON>ach", "visible", "pointArrayMap", "reduce", "currentPoints", "_a", "_b", "getColumn", "filter", "dataMin", "dataMax", "preventDefault", "onInit", "compose", "AxisClass", "keepProps", "includes", "push", "ParallelCoordinates_ParallelAxis", "highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_", "highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default", "composed", "format", "ParallelSeries_addEvent", "defined", "erase", "extend", "insertItem", "isArray", "ParallelSeries_isNumber", "pushUnique", "onSeriesAfterTranslate", "lastPlotX", "point", "points", "dataLength", "closestPointRangePx", "Number", "MAX_VALUE", "plotX", "angleRad", "plotHeight", "top", "plotTop", "left", "plotLeft", "clientX", "plotY", "translate", "high", "plotHigh", "Math", "min", "abs", "isInside", "isInsidePlot", "isNull", "onSeriesBindAxes", "_i", "axes", "isDirty", "series_1", "onSeriesDestroy", "forceRedraw", "onSeriesAfterGeneratePoints", "_c", "_d", "_e", "_f", "_g", "yAxisOptions", "labelFormat", "tooltipValueFormat", "formattedValue", "value", "dateTime", "time", "dateFormat", "resolveDTLFormat", "dateTimeLabelFormats", "tickPositions", "info", "unitName", "main", "categories", "String", "SeriesClass", "order", "ParallelCoordinates_ParallelSeries", "ParallelCoordinates_addEvent", "ParallelCoordinates_defined", "ParallelCoordinates_merge", "splat", "ChartAdditions", "setParallelInfo", "seriesOptions", "seriesOptions_1", "data", "max", "onChartInit", "args", "defaultYAxis", "newYAxes", "yAxis<PERSON><PERSON>th", "legend", "enabled", "boost", "seriesThreshold", "plotOptions", "boostThreshold", "onChartUpdate", "update", "ChartClass", "highchartsDefaultOptions", "addsProto", "chartProto", "ChartCompo", "ParallelCoordinates_ParallelCoordinates", "G", "Axis", "Chart", "defaultOptions", "Series"], "mappings": "CAWA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,UAAa,EAC5E,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,0CAA2C,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,aAAa,CAAC,CAAEJ,GAChH,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,0CAA0C,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,UAAa,EAEvHJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,UAAa,CACnF,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,EACpF,OAAgB,AAAC,WACP,aACA,IAyTNC,EAuIAC,EAgOAC,EAhqBUC,EAAuB,CAE/B,IACC,SAAST,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,EAEA,IACC,SAASJ,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIK,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAad,OAAO,CAG5B,IAAIC,EAASU,CAAwB,CAACE,EAAS,CAAG,CAGjDb,QAAS,CAAC,CACX,EAMA,OAHAU,CAAmB,CAACG,EAAS,CAACZ,EAAQA,EAAOD,OAAO,CAAEY,GAG/CX,EAAOD,OAAO,AACtB,CAMCY,EAAoBI,CAAC,CAAG,SAASf,CAAM,EACtC,IAAIgB,EAAShB,GAAUA,EAAOiB,UAAU,CACvC,WAAa,OAAOjB,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAW,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASnB,CAAO,CAAEqB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACvB,EAASsB,IAC5EE,OAAOC,cAAc,CAACzB,EAASsB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,CAA0B,CACzE,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GA+IpFE,EAJC,CAC9BC,MAtHgB,CAgBhBC,oBAAqB,CAAA,EAqCrBC,aAAc,CACVC,UAAW,EAUXC,MAAO,CACHC,KAAM,GACNC,aAAc,CAAA,CAClB,EACAC,OAAQ,CACJC,EAAG,EACHC,EAAG,EACHC,MAAO,SACPJ,aAAc,CAAA,CAClB,EACAK,OAAQ,CACZ,CACJ,EA2CIC,MA1CgB,CAChBT,UAAW,EACXU,WAAY,EACZC,SAAU,CAAA,EACVC,KAAM,UACV,CAsCA,EAgBIC,EAA0D,SAAUC,CAAE,CAAEC,CAAI,CAAEC,CAAI,EAClF,GAAIA,GAAQC,AAAqB,GAArBA,UAAUC,MAAM,CAAQ,IAAK,IAA4BC,EAAxBC,EAAI,EAAGC,EAAIN,EAAKG,MAAM,CAAME,EAAIC,EAAGD,KACxED,GAAQC,KAAKL,IACRI,GAAIA,CAAAA,EAAKG,MAAMjC,SAAS,CAACkC,KAAK,CAAChC,IAAI,CAACwB,EAAM,EAAGK,EAAC,EACnDD,CAAE,CAACC,EAAE,CAAGL,CAAI,CAACK,EAAE,EAGvB,OAAON,EAAGU,MAAM,CAACL,GAAMG,MAAMjC,SAAS,CAACkC,KAAK,CAAChC,IAAI,CAACwB,GACtD,EAGIU,EAAW,AAAC9B,IAA+E8B,QAAQ,CAAEC,EAAW,AAAC/B,IAA+E+B,QAAQ,CAAEC,EAAW,AAAChC,IAA+EgC,QAAQ,CAAEC,EAAW,AAACjC,IAA+EiC,QAAQ,CAAEC,EAAQ,AAAClC,IAA+EkC,KAAK,CAAEC,EAAO,AAACnC,IAA+EmC,IAAI,CAW9kBC,EAAuC,WAMvC,SAASA,EAAsBC,CAAI,EAC/B,IAAI,CAACA,IAAI,CAAGA,CAChB,CAoCA,OAjBAD,EAAsB1C,SAAS,CAAC4C,WAAW,CAAG,SAAUC,CAAY,CAAEC,CAAO,EACzE,IACIH,EAAOI,AADI,IAAI,CACCJ,IAAI,CACpBnC,EAAQmC,EAAKnC,KAAK,CAClBwC,EAAW,AAAC,CAAA,AAACD,CAAAA,AAHF,IAAI,CAGOE,QAAQ,EAAI,CAAA,EAAK,EAAE,EACpCzC,CAAAA,EAAM0C,YAAY,CAACC,OAAO,CAAG,CAAA,CAClC3C,CAAAA,EAAM4C,KAAK,CACXN,EAAQO,KAAK,CAAG,IAAML,GAGtBF,CAAO,CAACD,CAAY,CAAC,EAAE,CAAC,CAAG,IAAMG,EAAW,IAC5CL,CAAI,CAACE,CAAY,CAAC,EAAE,CAAC,CAAGC,CAAO,CAACD,CAAY,CAAC,EAAE,CAAC,CAAG,EAEnDF,CAAI,CAACE,CAAY,CAAC,EAAE,CAAC,CAAGC,CAAO,CAACD,CAAY,CAAC,EAAE,CAAC,CAAG,KACnDF,CAAI,CAACE,CAAY,CAAC,EAAE,CAAC,CAAGC,CAAO,CAACD,CAAY,CAAC,EAAE,CAAC,CAAG,KAE3D,EACOH,CACX,KAOA,AAAC,SAAUjE,CAAY,EA8BnB,SAAS6E,EAAkBC,CAAC,EACxB,IACI/C,EAAQmC,AADD,IAAI,CACEnC,KAAK,CAClBC,EAAsBkC,AAFf,IAAI,CAEgBlC,mBAAmB,CAC9CoC,EAAe,CACX,OAAQ,QAAS,SAAU,MAC9B,CACL,GAAIrC,EAAMgD,sBAAsB,EAI5B,GAHIhD,EAAMiD,QAAQ,EACdZ,CAAAA,EAAeA,EAAaa,OAAO,EAAC,EAEpCf,AAVG,IAAI,CAUFgB,OAAO,CACZhB,AAXG,IAAI,CAWFG,OAAO,CAAGN,EAAMG,AAXlB,IAAI,CAWmBG,OAAO,CAAEvC,EAAgDa,KAAK,CAAEmC,EAAEK,WAAW,MAEtG,CACD,IAAIC,EAAYrD,EAAMsD,KAAK,CAACC,OAAO,CAdhC,IAAI,CAeHpB,CAfD,IAAI,CAeEG,OAAO,CAAGN,EAAMG,AAftB,IAAI,CAeuBG,OAAO,CACjCH,AAhBD,IAAI,CAgBEnC,KAAK,CAACsC,OAAO,CAACtC,KAAK,CAACE,YAAY,CACrC6C,EAAEK,WAAW,EACjBnD,EAAoBwC,QAAQ,CAAGR,EAAKhC,EAAoBwC,QAAQ,CAAEY,GAAa,EAAIA,EAAYrD,EAAMsD,KAAK,CAACjC,MAAM,EACjHpB,EAAoBmC,WAAW,CAACC,EAAcF,AAnB3C,IAAI,CAmB4CG,OAAO,CAC9D,EAER,CAQA,SAASkB,EAAoBT,CAAC,EAE1B,IAAI/C,EAAQmC,AADD,IAAI,CACEnC,KAAK,CAClBC,EAAsBkC,AAFf,IAAI,CAEgBlC,mBAAmB,CAClD,GAAKA,GAGDD,GAASA,EAAMgD,sBAAsB,EAAI,CAACb,AANnC,IAAI,CAMoCgB,OAAO,CAAE,CACxD,IAAIM,EAAUxD,EAAoBwC,QAAQ,CACtCiB,EAAkB,EAAE,CACxBvB,AATO,IAAI,CASNwB,MAAM,CAACC,OAAO,CAAC,SAAUD,CAAM,EAC5BA,EAAOE,OAAO,EAAI9B,EAAS0B,IAC3BC,CAAAA,EAAkB,AAACC,CAAAA,EAAOG,aAAa,EAAI,CAAC,IAAI,AAAD,EAC1CC,MAAM,CAAC,SAAUC,CAAa,CAAEhF,CAAG,EACpC,IAAIiF,EACAC,EACJ,OAAOlD,EAAcA,EAAc,EAAE,CAAEgD,EAAe,CAAA,GAAO,CACzD,AAAyF,OAAxFE,CAAAA,EAAK,AAAiC,OAAhCD,CAAAA,EAAKN,EAAOQ,SAAS,CAACnF,EAAG,GAAeiF,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,CAAE,CAACR,EAAQ,AAAD,GAAeS,AAAO,KAAK,IAAZA,EAAgBA,EAAK,KACzH,CAAE,CAAA,EACP,EAAGR,EAAe,CAE1B,GACAA,EAAkBA,EAAgBU,MAAM,CAACrC,GACzCI,AAtBO,IAAI,CAsBNkC,OAAO,CAAGvC,EAAS4B,GACxBvB,AAvBO,IAAI,CAuBNmC,OAAO,CAAGzC,EAAS6B,GACxBX,EAAEwB,cAAc,EACpB,CACJ,CAKA,SAASC,IAEArC,AADM,IAAI,CACLlC,mBAAmB,EACzBkC,CAAAA,AAFO,IAAI,CAENlC,mBAAmB,CAAG,IAAIiC,EAFxB,IAAI,CAE8C,CAEjE,CAzEAjE,EAAawG,OAAO,CAVpB,SAAiBC,CAAS,EACjBA,EAAUC,SAAS,CAACC,QAAQ,CAAC,cAG9BF,EAAUC,SAAS,CAACE,IAAI,CAAC,YACzBjD,EAHgB8C,EAGI,OAAQF,GAC5B5C,EAJgB8C,EAII,kBAAmB5B,GACvClB,EALgB8C,EAKI,oBAAqBlB,GAEjD,CA2EJ,EAAGvF,GAAiBA,CAAAA,EAAe,CAAC,CAAA,GAMP,IAAI6G,EAAoC7G,EAGjE8G,EAAmHzG,EAAoB,KACvI0G,EAAuI1G,EAAoBI,CAAC,CAACqG,GAe7JE,EAAW,AAACnF,IAA+EmF,QAAQ,CAEnGC,EAAS,AAACF,IAA2HE,MAAM,CAE3IC,EAA0B,AAACrF,IAA+E8B,QAAQ,CAAEwD,EAAU,AAACtF,IAA+EsF,OAAO,CAAEC,EAAQ,AAACvF,IAA+EuF,KAAK,CAAEC,EAAS,AAACxF,IAA+EwF,MAAM,CAAEC,EAAa,AAACzF,IAA+EyF,UAAU,CAAEC,EAAU,AAAC1F,IAA+E0F,OAAO,CAAEC,EAA0B,AAAC3F,IAA+EiC,QAAQ,CAAE2D,EAAa,AAAC5F,IAA+E4F,UAAU,EAOl0B,AAAC,SAAUxH,CAAc,EA0BrB,SAASyH,IACL,IAKIC,EACAC,EALA7F,EAAQ,IAAI,CAACA,KAAK,CAClB8F,EAASnC,AAFA,IAAI,CAEGmC,MAAM,CACtBC,EAAaD,GAAUA,EAAOzE,MAAM,CACpC2E,EAAsBC,OAAOC,SAAS,CAG1C,GAAI,IAAI,CAAClG,KAAK,CAACgD,sBAAsB,CAAE,CACnC,IAAK,IAAIzB,EAAI,EAAGA,EAAIwE,EAAYxE,IAExB6D,EAAQS,AADZA,CAAAA,EAAQC,CAAM,CAACvE,EAAE,AAAD,EACEd,CAAC,GACXT,EAAM4C,KAAK,CACXiD,EAAMM,KAAK,CAAGnG,EAAMsD,KAAK,CAAC/B,EAAE,CAAC6E,QAAQ,EAAI,EAEpCpG,EAAMiD,QAAQ,CACnB4C,EAAMM,KAAK,CAAInG,EAAMqG,UAAU,CAC3BrG,EAAMsD,KAAK,CAAC/B,EAAE,CAAC+E,GAAG,CAClBtG,EAAMuG,OAAO,CAGjBV,EAAMM,KAAK,CAAGnG,EAAMsD,KAAK,CAAC/B,EAAE,CAACiF,IAAI,CAAGxG,EAAMyG,QAAQ,CAEtDZ,EAAMa,OAAO,CAAGb,EAAMM,KAAK,CAC3BN,EAAMc,KAAK,CAAG3G,EAAMsD,KAAK,CAAC/B,EAAE,CACvBqF,SAAS,CAACf,EAAMpF,CAAC,CAAE,CAAA,EAAO,CAAA,EAAM,KAAK,EAAG,CAAA,GAEzCgF,EAAwBI,EAAMgB,IAAI,GAClChB,CAAAA,EAAMiB,QAAQ,CAAG9G,EAAMsD,KAAK,CAAC/B,EAAE,CAACqF,SAAS,CAACf,EAAMgB,IAAI,CAAE,CAAA,EAAO,CAAA,EAAM,KAAK,EAAG,CAAA,EAAI,EAE1D,KAAA,IAAdjB,GACPI,CAAAA,EAAsBe,KAAKC,GAAG,CAAChB,EAAqBe,KAAKE,GAAG,CAACpB,EAAMM,KAAK,CAAGP,GAAU,EAEzFA,EAAYC,EAAMM,KAAK,CACvBN,EAAMqB,QAAQ,CAAGlH,EAAMmH,YAAY,CAACtB,EAAMM,KAAK,CAAEN,EAAMc,KAAK,CAAE,CAAE1D,SAAUjD,EAAMiD,QAAQ,AAAC,IAGzF4C,EAAMuB,MAAM,CAAG,CAAA,CAGvB,CAAA,IAAI,CAACpB,mBAAmB,CAAGA,CAC/B,CACJ,CAMA,SAASqB,EAAiBtE,CAAC,EACvB,IACI/C,EAAQ2D,AADC,IAAI,CACE3D,KAAK,CACxB,GAAIA,EAAMgD,sBAAsB,CAAE,CAE9B,IAAK,IAAIsE,EAAK,EAAGrD,EAAKjE,EAAMuH,IAAI,CAAED,EAAKrD,EAAG5C,MAAM,CAAEiG,IAAM,CACpD,IAAInF,EAAO8B,CAAE,CAACqD,EAAG,CACjB/B,EAHW,IAAI,CAGMpD,EAAKwB,MAAM,EAChCxB,EAAKqF,OAAO,CAAG,CAAA,CACnB,CACAC,AANe,IAAI,CAMV7G,KAAK,CAAGZ,EAAMY,KAAK,CAAC,EAAE,CAC/B6G,AAPe,IAAI,CAOVnE,KAAK,CAAGtD,EAAMsD,KAAK,CAAC,EAAE,CAC/BP,EAAEwB,cAAc,EACpB,CACJ,CAKA,SAASmD,IACL,IACI1H,EAAQ2D,AADC,IAAI,CACE3D,KAAK,CACxB,GAAIA,EAAMgD,sBAAsB,CAC5B,IAAK,IAAIsE,EAAK,EAAGrD,EAAMjE,EAAMuH,IAAI,EAAI,EAAE,CAAGD,EAAKrD,EAAG5C,MAAM,CAAEiG,IAAM,CAC5D,IAAInF,EAAO8B,CAAE,CAACqD,EAAG,CACbnF,GAAQA,EAAKwB,MAAM,GACnB0B,EAAMlD,EAAKwB,MAAM,CANhB,IAAI,EAOLxB,EAAKqF,OAAO,CAAGrF,EAAKwF,WAAW,CAAG,CAAA,EAE1C,CAER,CAIA,SAASC,IAOL,IANI3D,EACAC,EACA2D,EACAC,EACAC,EACAC,EACAhI,EAAQ,IAAI,CAACA,KAAK,CACtB,GAAIA,MAAAA,EAAqC,KAAK,EAAIA,EAAMgD,sBAAsB,CAC1E,IAAK,IAAIsE,EAAK,EAAGW,EAAK,IAAI,CAACnC,MAAM,CAAEwB,EAAKW,EAAG5G,MAAM,CAAEiG,IAAM,CACrD,IAAIzB,EAAQoC,CAAE,CAACX,EAAG,CACdhE,EAAQtD,EAAMsD,KAAK,CAACuC,EAAMrF,CAAC,EAAI,EAAE,CACjC0H,EAAe5E,EAAMhB,OAAO,CAC5B6F,EAAc,AAA2C,OAA1ClE,CAAAA,EAAKiE,EAAaE,kBAAkB,AAAD,GAAenE,AAAO,KAAK,IAAZA,EAAgBA,EAAKiE,EAAa3H,MAAM,CAAC2E,MAAM,CAChHmD,EAAiB,KAAK,EAEtBA,EADAF,EACiBjD,EAAOiD,EAAa7C,EAAOO,EAAO,CAAEyC,MAAOzC,EAAMpF,CAAC,AAAC,GAAIT,GAEnEsD,EAAMiF,QAAQ,CACFvI,EAAMwI,IAAI,CAACC,UAAU,CAACzI,EAAMwI,IAAI,CAACE,gBAAgB,CAAC,AAAC,CAAA,AAA6C,OAA5CxE,CAAAA,EAAKgE,EAAaS,oBAAoB,AAAD,GAAezE,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,CAAE,CAAC,AAAC,CAAA,AAAoC,OAAnC2D,CAAAA,EAAKvE,EAAMsF,aAAa,CAACC,IAAI,AAAD,GAAehB,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGiB,QAAQ,AAAD,GAAM,OAAO,AAAD,GAAM,IAAIC,IAAI,CAAE,AAAmB,OAAlBjB,CAAAA,EAAKjC,EAAMpF,CAAC,AAADA,GAAeqH,AAAO,KAAK,IAAZA,EAAgBA,EAAK,KAAK,GAEjTtC,EAAQ0C,EAAac,UAAU,EACnBd,EAAac,UAAU,CAAC,AAAmB,OAAlBjB,CAAAA,EAAKlC,EAAMpF,CAAC,AAADA,GAAesH,AAAO,KAAK,IAAZA,EAAgBA,EAAK,GAAG,CAG3EkB,OAAO,AAAmB,OAAlBjB,CAAAA,EAAKnC,EAAMpF,CAAC,AAADA,GAAeuH,AAAO,KAAK,IAAZA,EAAgBA,EAAK,IAE5EnC,EAAMwC,cAAc,CAAGA,CAC3B,CAER,CAtHAnK,EAAeuG,OAAO,CATtB,SAAiByE,CAAW,EACpBxD,EAAWT,EAAU,oBAErBE,EADiB+D,EACmB,iBAAkBvD,EAAwB,CAAEwD,MAAO,CAAE,GACzFhE,EAFiB+D,EAEmB,WAAY7B,GAChDlC,EAHiB+D,EAGmB,UAAWxB,GAC/CvC,EAAwB+D,EAAa,sBAAuBtB,GAEpE,CAwHJ,EAAG1J,GAAmBA,CAAAA,EAAiB,CAAC,CAAA,GAMX,IAAIkL,EAAsClL,EAmBnEmL,EAA+B,AAACvJ,IAA+E8B,QAAQ,CAAE0H,EAA8B,AAACxJ,IAA+EsF,OAAO,CAAEmE,EAA4B,AAACzJ,IAA+EkC,KAAK,CAAEwH,EAAQ,AAAC1J,IAA+E0J,KAAK,CAMhcC,EAAgC,WAMhC,SAASA,EAAezJ,CAAK,EACzB,IAAI,CAACA,KAAK,CAAGA,CACjB,CAqCA,OAdAyJ,EAAejK,SAAS,CAACkK,eAAe,CAAG,SAAUpH,CAAO,EACxD,IAAItC,EAAS,IAAI,CAACA,KAAK,EACf,IAAI,CACR2J,EAAgBrH,EAAQqB,MAAM,AAClC3D,CAAAA,EAAM0C,YAAY,CAAG,CACjBC,QAAS,CACb,EACA,IAAK,IAAI2E,EAAK,EAAoCA,EAAKsC,AAApBD,EAAoCtI,MAAM,CAAEiG,IAAM,CACjF,IAAI3D,EAASiG,AADkBD,CACH,CAACrC,EAAG,AAC5B3D,CAAAA,EAAOkG,IAAI,EACX7J,CAAAA,EAAM0C,YAAY,CAACC,OAAO,CAAGoE,KAAK+C,GAAG,CAAC9J,EAAM0C,YAAY,CAACC,OAAO,CAAEgB,EAAOkG,IAAI,CAACxI,MAAM,CAAG,EAAC,CAEhG,CACJ,EACOoI,CACX,KAOA,AAAC,SAAUtL,CAAmB,EA8B1B,SAAS4L,EAAYhH,CAAC,EAClB,IACIT,EAAUS,EAAEiH,IAAI,CAAC,EAAE,CACnBC,EAAeT,EAAMlH,EAAQgB,KAAK,EAAI,CAAC,GACvC4G,EAAW,EAAE,CACbC,EAAcF,EAAa5I,MAAM,CAYrC,GAFArB,AAdY,IAAI,CAcVgD,sBAAsB,CAAGV,EAAQtC,KAAK,EACxCsC,EAAQtC,KAAK,CAACC,mBAAmB,CACjCD,AAhBQ,IAAI,CAgBNgD,sBAAsB,CAAE,CAG9B,IAFAhD,AAjBQ,IAAI,CAiBN0J,eAAe,CAACpH,GAEf6H,GAAenK,AAnBd,IAAI,CAmBgB0C,YAAY,CAACC,OAAO,CAAEwH,IAC9CD,EAASrF,IAAI,CAAC,CAAC,EAEdvC,CAAAA,EAAQ8H,MAAM,EACf9H,CAAAA,EAAQ8H,MAAM,CAAG,CAAC,CAAA,EAElB9H,EAAQ8H,MAAM,EACd,AAAkC,KAAA,IAA3B9H,EAAQ8H,MAAM,CAACC,OAAO,EAC7B/H,CAAAA,EAAQ8H,MAAM,CAACC,OAAO,CAAG,CAAA,CAAI,EAEjCd,EAA0B,CAAA,EAAMjH,EAEhC,CACIgI,MAAO,CACHC,gBAAiBtE,OAAOC,SAAS,AACrC,EACAsE,YAAa,CACT7G,OAAQ,CACJ8G,eAAgBxE,OAAOC,SAAS,AACpC,CACJ,CACJ,GACA5D,EAAQgB,KAAK,CAAG2G,EAAatI,MAAM,CAACuI,GACpC5H,EAAQ1B,KAAK,CAAG2I,EAA0BxJ,EAAgDa,KAAK,CAC/F4I,EAAMlH,EAAQ1B,KAAK,EAAI,CAAC,EAAE,CAAC,EAAE,CACjC,CACJ,CAKA,SAAS8J,EAAc3H,CAAC,EACpB,IACIT,EAAUS,EAAET,OAAO,CAQvB,GAPIA,EAAQtC,KAAK,GACTsJ,EAA4BhH,EAAQtC,KAAK,CAACC,mBAAmB,GAC7DD,CAAAA,AAJI,IAAI,CAIFgD,sBAAsB,CACxBV,EAAQtC,KAAK,CAACC,mBAAmB,AAAD,EAExCD,AAPQ,IAAI,CAONsC,OAAO,CAACtC,KAAK,CAACE,YAAY,CAAGqJ,EAA0BvJ,AAPrD,IAAI,CAOuDsC,OAAO,CAACtC,KAAK,CAACE,YAAY,CAAEoC,EAAQtC,KAAK,CAACE,YAAY,GAEzHF,AATQ,IAAI,CASNgD,sBAAsB,CAAE,CAE1BV,EAAQqB,MAAM,EACd3D,AAZI,IAAI,CAYF0J,eAAe,CAACpH,GAE1B,IAAK,IAAIgF,EAAK,EAAGrD,EAAKjE,AAdd,IAAI,CAcgBsD,KAAK,CAAEgE,EAAKrD,EAAG5C,MAAM,CAAEiG,IAE/CnF,AADW8B,CAAE,CAACqD,EAAG,CACZqD,MAAM,CAAC,CAAC,EAAG,CAAA,EAExB,CACJ,CA5EAxM,EAAoBsG,OAAO,CAb3B,SAAiBC,CAAS,CAAEkG,CAAU,CAAEC,CAAwB,CAAE3B,CAAW,EACzEpE,EAAiCL,OAAO,CAACC,GACzC0E,EAAmC3E,OAAO,CAACyE,GAC3C,IACI4B,EAAYrB,EAAejK,SAAS,CACpCuL,EAAaC,AAFAJ,EAEWpL,SAAS,AAChCuL,CAAAA,EAAWrB,eAAe,GAC3BqB,EAAWrB,eAAe,CAAGoB,EAAUpB,eAAe,CACtDL,EALauB,EAK4B,OAAQb,GACjDV,EANauB,EAM4B,SAAUF,GACnDnB,EAA0B,CAAA,EAAMsB,EAAyB7K,KAAK,CAAED,EAAgDC,KAAK,EAE7H,CA8EJ,EAAG7B,GAAwBA,CAAAA,EAAsB,CAAC,CAAA,GAMrB,IAAI8M,EAA2C9M,EAOxE+M,EAAKpL,IACTmL,EAAwCxG,OAAO,CAACyG,EAAEC,IAAI,CAAED,EAAEE,KAAK,CAAEF,EAAEG,cAAc,CAAEH,EAAEI,MAAM,EAC9D,IAAI1L,EAA6BE,IAGpD,OADYH,EAAoB,OAAU,AAE3C,GAET"}