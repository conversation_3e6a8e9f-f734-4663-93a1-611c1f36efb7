{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts Gantt JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/treegrid\n * @requires highcharts\n *\n * Tree Grid\n *\n * (c) 2016-2025 Jon <PERSON>ld Nygard\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"StackItem\"], require(\"highcharts\")[\"Axis\"], require(\"highcharts\")[\"Color\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/treegrid\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"StackItem\"], [\"highcharts/highcharts\",\"Axis\"], [\"highcharts/highcharts\",\"Color\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/treegrid\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"StackItem\"], require(\"highcharts\")[\"Axis\"], require(\"highcharts\")[\"Color\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"StackItem\"], root[\"Highcharts\"][\"Axis\"], root[\"Highcharts\"][\"Color\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__184__, __WEBPACK_EXTERNAL_MODULE__532__, __WEBPACK_EXTERNAL_MODULE__620__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 184:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__184__;\n\n/***/ }),\n\n/***/ 532:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__532__;\n\n/***/ }),\n\n/***/ 620:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__620__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ treegrid_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"StackItem\"],\"commonjs\":[\"highcharts\",\"StackItem\"],\"commonjs2\":[\"highcharts\",\"StackItem\"],\"root\":[\"Highcharts\",\"StackItem\"]}\nvar highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_ = __webpack_require__(184);\nvar highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_default = /*#__PURE__*/__webpack_require__.n(highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_);\n;// ./code/es5/es-modules/Core/Axis/BrokenAxis.js\n/* *\n *\n *  (c) 2009-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nvar addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, find = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).find, fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, isArray = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isArray, isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Composition\n *\n * */\n/**\n * Axis with support of broken data rows.\n * @private\n */\nvar BrokenAxis;\n(function (BrokenAxis) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Adds support for broken axes.\n     * @private\n     */\n    function compose(AxisClass, SeriesClass) {\n        if (!AxisClass.keepProps.includes('brokenAxis')) {\n            AxisClass.keepProps.push('brokenAxis');\n            addEvent(AxisClass, 'init', onAxisInit);\n            addEvent(AxisClass, 'afterInit', onAxisAfterInit);\n            addEvent(AxisClass, 'afterSetTickPositions', onAxisAfterSetTickPositions);\n            addEvent(AxisClass, 'afterSetOptions', onAxisAfterSetOptions);\n            var seriesProto = SeriesClass.prototype;\n            seriesProto.drawBreaks = seriesDrawBreaks;\n            seriesProto.gappedPath = seriesGappedPath;\n            addEvent(SeriesClass, 'afterGeneratePoints', onSeriesAfterGeneratePoints);\n            addEvent(SeriesClass, 'afterRender', onSeriesAfterRender);\n        }\n        return AxisClass;\n    }\n    BrokenAxis.compose = compose;\n    /**\n     * @private\n     */\n    function onAxisAfterInit() {\n        if (typeof this.brokenAxis !== 'undefined') {\n            this.brokenAxis.setBreaks(this.options.breaks, false);\n        }\n    }\n    /**\n     * Force Axis to be not-ordinal when breaks are defined.\n     * @private\n     */\n    function onAxisAfterSetOptions() {\n        var _a;\n        var axis = this;\n        if ((_a = axis.brokenAxis) === null || _a === void 0 ? void 0 : _a.hasBreaks) {\n            axis.options.ordinal = false;\n        }\n    }\n    /**\n     * @private\n     */\n    function onAxisAfterSetTickPositions() {\n        var axis = this,\n            brokenAxis = axis.brokenAxis;\n        if (brokenAxis === null || brokenAxis === void 0 ? void 0 : brokenAxis.hasBreaks) {\n            var tickPositions = axis.tickPositions,\n                info = axis.tickPositions.info,\n                newPositions = [];\n            for (var i = 0; i < tickPositions.length; i++) {\n                if (!brokenAxis.isInAnyBreak(tickPositions[i])) {\n                    newPositions.push(tickPositions[i]);\n                }\n            }\n            axis.tickPositions = newPositions;\n            axis.tickPositions.info = info;\n        }\n    }\n    /**\n     * @private\n     */\n    function onAxisInit() {\n        var axis = this;\n        if (!axis.brokenAxis) {\n            axis.brokenAxis = new Additions(axis);\n        }\n    }\n    /**\n     * @private\n     */\n    function onSeriesAfterGeneratePoints() {\n        var _a,\n            _b;\n        var _c = this,\n            isDirty = _c.isDirty,\n            connectNulls = _c.options.connectNulls,\n            points = _c.points,\n            xAxis = _c.xAxis,\n            yAxis = _c.yAxis;\n        // Set, or reset visibility of the points. Axis.setBreaks marks\n        // the series as isDirty\n        if (isDirty) {\n            var i = points.length;\n            while (i--) {\n                var point = points[i];\n                // Respect nulls inside the break (#4275)\n                var nullGap = point.y === null && connectNulls === false;\n                var isPointInBreak = (!nullGap && (((_a = xAxis === null || xAxis === void 0 ? void 0 : xAxis.brokenAxis) === null || _a === void 0 ? void 0 : _a.isInAnyBreak(point.x,\n                    true)) ||\n                        ((_b = yAxis === null || yAxis === void 0 ? void 0 : yAxis.brokenAxis) === null || _b === void 0 ? void 0 : _b.isInAnyBreak(point.y,\n                    true))));\n                // Set point.visible if in any break.\n                // If not in break, reset visible to original value.\n                point.visible = isPointInBreak ?\n                    false :\n                    point.options.visible !== false;\n            }\n        }\n    }\n    /**\n     * @private\n     */\n    function onSeriesAfterRender() {\n        this.drawBreaks(this.xAxis, ['x']);\n        this.drawBreaks(this.yAxis, pick(this.pointArrayMap, ['y']));\n    }\n    /**\n     * @private\n     */\n    function seriesDrawBreaks(axis, keys) {\n        var _a;\n        var series = this,\n            points = series.points;\n        var breaks,\n            threshold,\n            y;\n        if ((_a = axis === null || axis === void 0 ? void 0 : axis.brokenAxis) === null || _a === void 0 ? void 0 : _a.hasBreaks) {\n            var brokenAxis_1 = axis.brokenAxis;\n            keys.forEach(function (key) {\n                var _a,\n                    _b;\n                breaks = (brokenAxis_1 === null || brokenAxis_1 === void 0 ? void 0 : brokenAxis_1.breakArray) || [];\n                threshold = axis.isXAxis ?\n                    axis.min :\n                    pick(series.options.threshold, axis.min);\n                // Array of breaks that have been \"zoomed-out\" which means that\n                // they were shown previously, but now after zoom, they are not\n                // (#19885).\n                var breaksOutOfRange = (_b = (_a = axis === null || axis === void 0 ? void 0 : axis.options) === null || _a === void 0 ? void 0 : _a.breaks) === null || _b === void 0 ? void 0 : _b.filter(function (brk) {\n                        var isOut = true;\n                    // Iterate to see if \"brk\" is in axis range\n                    for (var i = 0; i < breaks.length; i++) {\n                        var otherBreak = breaks[i];\n                        if (otherBreak.from === brk.from &&\n                            otherBreak.to === brk.to) {\n                            isOut = false;\n                            break;\n                        }\n                    }\n                    return isOut;\n                });\n                points.forEach(function (point) {\n                    y = pick(point['stack' + key.toUpperCase()], point[key]);\n                    breaks.forEach(function (brk) {\n                        if (isNumber(threshold) && isNumber(y)) {\n                            var eventName = '';\n                            if ((threshold < brk.from && y > brk.to) ||\n                                (threshold > brk.from && y < brk.from)) {\n                                eventName = 'pointBreak';\n                            }\n                            else if ((threshold < brk.from &&\n                                y > brk.from &&\n                                y < brk.to) || (threshold > brk.from &&\n                                y > brk.to &&\n                                y < brk.from)) {\n                                eventName = 'pointInBreak';\n                            }\n                            if (eventName) {\n                                fireEvent(axis, eventName, { point: point, brk: brk });\n                            }\n                        }\n                    });\n                    breaksOutOfRange === null || breaksOutOfRange === void 0 ? void 0 : breaksOutOfRange.forEach(function (brk) {\n                        fireEvent(axis, 'pointOutsideOfBreak', { point: point, brk: brk });\n                    });\n                });\n            });\n        }\n    }\n    /**\n     * Extend getGraphPath by identifying gaps in the data so that we\n     * can draw a gap in the line or area. This was moved from ordinal\n     * axis module to broken axis module as of #5045.\n     *\n     * @private\n     * @function Highcharts.Series#gappedPath\n     *\n     * @return {Highcharts.SVGPathArray}\n     * Gapped path\n     */\n    function seriesGappedPath() {\n        var currentDataGrouping = this.currentDataGrouping,\n            groupingSize = currentDataGrouping === null || currentDataGrouping === void 0 ? void 0 : currentDataGrouping.gapSize,\n            points = this.points.slice(),\n            yAxis = this.yAxis;\n        var gapSize = this.options.gapSize,\n            i = points.length - 1,\n            stack;\n        /**\n         * Defines when to display a gap in the graph, together with the\n         * [gapUnit](plotOptions.series.gapUnit) option.\n         *\n         * In case when `dataGrouping` is enabled, points can be grouped\n         * into a larger time span. This can make the grouped points to\n         * have a greater distance than the absolute value of `gapSize`\n         * property, which will result in disappearing graph completely.\n         * To prevent this situation the mentioned distance between\n         * grouped points is used instead of previously defined\n         * `gapSize`.\n         *\n         * In practice, this option is most often used to visualize gaps\n         * in time series. In a stock chart, intraday data is available\n         * for daytime hours, while gaps will appear in nights and\n         * weekends.\n         *\n         * @see [gapUnit](plotOptions.series.gapUnit)\n         * @see [xAxis.breaks](#xAxis.breaks)\n         *\n         * @sample {highstock} stock/plotoptions/series-gapsize/\n         * Setting the gap size to 2 introduces gaps for weekends in\n         * daily datasets.\n         *\n         * @type      {number}\n         * @default   0\n         * @product   highstock\n         * @requires  modules/broken-axis\n         * @apioption plotOptions.series.gapSize\n         */\n        /**\n         * Together with [gapSize](plotOptions.series.gapSize), this\n         * option defines where to draw gaps in the graph.\n         *\n         * When the `gapUnit` is `\"relative\"` (default), a gap size of 5\n         * means that if the distance between two points is greater than\n         * 5 times that of the two closest points, the graph will be\n         * broken.\n         *\n         * When the `gapUnit` is `\"value\"`, the gap is based on absolute\n         * axis values, which on a datetime axis is milliseconds. This\n         * also applies to the navigator series that inherits gap\n         * options from the base series.\n         *\n         * @see [gapSize](plotOptions.series.gapSize)\n         *\n         * @type       {string}\n         * @default    relative\n         * @since      5.0.13\n         * @product    highstock\n         * @validvalue [\"relative\", \"value\"]\n         * @requires   modules/broken-axis\n         * @apioption  plotOptions.series.gapUnit\n         */\n        if (gapSize && i > 0) { // #5008\n            // Gap unit is relative\n            if (this.options.gapUnit !== 'value') {\n                gapSize *= this.basePointRange;\n            }\n            // Setting a new gapSize in case dataGrouping is enabled\n            // (#7686)\n            if (groupingSize &&\n                groupingSize > gapSize &&\n                // Except when DG is forced (e.g. from other series)\n                // and has lower granularity than actual points (#11351)\n                groupingSize >= this.basePointRange) {\n                gapSize = groupingSize;\n            }\n            // Extension for ordinal breaks\n            var current = void 0,\n                next = void 0;\n            while (i--) {\n                // Reassign next if it is not visible\n                if (!(next && next.visible !== false)) {\n                    next = points[i + 1];\n                }\n                current = points[i];\n                // Skip iteration if one of the points is not visible\n                if (next.visible === false || current.visible === false) {\n                    continue;\n                }\n                if (next.x - current.x > gapSize) {\n                    var xRange = (current.x + next.x) / 2;\n                    points.splice(// Insert after this one\n                    i + 1, 0, {\n                        isNull: true,\n                        x: xRange\n                    });\n                    // For stacked chart generate empty stack items, #6546\n                    if (yAxis.stacking && this.options.stacking) {\n                        stack = yAxis.stacking.stacks[this.stackKey][xRange] = new (highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_default())(yAxis, yAxis.options.stackLabels, false, xRange, this.stack);\n                        stack.total = 0;\n                    }\n                }\n                // Assign current to next for the upcoming iteration\n                next = current;\n            }\n        }\n        // Call base method\n        return this.getGraphPath(points);\n    }\n    /* *\n     *\n     *  Class\n     *\n     * */\n    /**\n     * Provides support for broken axes.\n     * @private\n     * @class\n     */\n    var Additions = /** @class */ (function () {\n            /* *\n             *\n             *  Constructors\n             *\n             * */\n            function Additions(axis) {\n                this.hasBreaks = false;\n            this.axis = axis;\n        }\n        /* *\n         *\n         *  Static Functions\n         *\n         * */\n        /**\n         * @private\n         */\n        Additions.isInBreak = function (brk, val) {\n            var repeat = brk.repeat || Infinity,\n                from = brk.from,\n                length = brk.to - brk.from,\n                test = (val >= from ?\n                    (val - from) % repeat :\n                    repeat - ((from - val) % repeat));\n            var ret;\n            if (!brk.inclusive) {\n                ret = test < length && test !== 0;\n            }\n            else {\n                ret = test <= length;\n            }\n            return ret;\n        };\n        /**\n         * @private\n         */\n        Additions.lin2Val = function (val) {\n            var axis = this;\n            var brokenAxis = axis.brokenAxis;\n            var breakArray = brokenAxis === null || brokenAxis === void 0 ? void 0 : brokenAxis.breakArray;\n            if (!breakArray || !isNumber(val)) {\n                return val;\n            }\n            var nval = val,\n                brk,\n                i;\n            for (i = 0; i < breakArray.length; i++) {\n                brk = breakArray[i];\n                if (brk.from >= nval) {\n                    break;\n                }\n                else if (brk.to < nval) {\n                    nval += brk.len;\n                }\n                else if (Additions.isInBreak(brk, nval)) {\n                    nval += brk.len;\n                }\n            }\n            return nval;\n        };\n        /**\n         * @private\n         */\n        Additions.val2Lin = function (val) {\n            var axis = this;\n            var brokenAxis = axis.brokenAxis;\n            var breakArray = brokenAxis === null || brokenAxis === void 0 ? void 0 : brokenAxis.breakArray;\n            if (!breakArray || !isNumber(val)) {\n                return val;\n            }\n            var nval = val,\n                brk,\n                i;\n            for (i = 0; i < breakArray.length; i++) {\n                brk = breakArray[i];\n                if (brk.to <= val) {\n                    nval -= brk.len;\n                }\n                else if (brk.from >= val) {\n                    break;\n                }\n                else if (Additions.isInBreak(brk, val)) {\n                    nval -= (val - brk.from);\n                    break;\n                }\n            }\n            return nval;\n        };\n        /* *\n         *\n         *  Functions\n         *\n         * */\n        /**\n         * Returns the first break found where the x is larger then break.from\n         * and smaller then break.to.\n         *\n         * @param {number} x\n         * The number which should be within a break.\n         *\n         * @param {Array<Highcharts.XAxisBreaksOptions>} breaks\n         * The array of breaks to search within.\n         *\n         * @return {Highcharts.XAxisBreaksOptions|undefined}\n         * Returns the first break found that matches, returns false if no break\n         * is found.\n         */\n        Additions.prototype.findBreakAt = function (x, breaks) {\n            return find(breaks, function (b) {\n                return b.from < x && x < b.to;\n            });\n        };\n        /**\n         * @private\n         */\n        Additions.prototype.isInAnyBreak = function (val, testKeep) {\n            var brokenAxis = this,\n                axis = brokenAxis.axis,\n                breaks = axis.options.breaks || [];\n            var i = breaks.length,\n                inbrk,\n                keep,\n                ret;\n            if (i && isNumber(val)) {\n                while (i--) {\n                    if (Additions.isInBreak(breaks[i], val)) {\n                        inbrk = true;\n                        if (!keep) {\n                            keep = pick(breaks[i].showPoints, !axis.isXAxis);\n                        }\n                    }\n                }\n                if (inbrk && testKeep) {\n                    ret = inbrk && !keep;\n                }\n                else {\n                    ret = inbrk;\n                }\n            }\n            return ret;\n        };\n        /**\n         * Dynamically set or unset breaks in an axis. This function in lighter\n         * than using Axis.update, and it also preserves animation.\n         *\n         * @private\n         * @function Highcharts.Axis#setBreaks\n         *\n         * @param {Array<Highcharts.XAxisBreaksOptions>} [breaks]\n         * The breaks to add. When `undefined` it removes existing breaks.\n         *\n         * @param {boolean} [redraw=true]\n         * Whether to redraw the chart immediately.\n         */\n        Additions.prototype.setBreaks = function (breaks, redraw) {\n            var brokenAxis = this,\n                axis = brokenAxis.axis,\n                time = axis.chart.time,\n                hasBreaks = isArray(breaks) &&\n                    !!breaks.length &&\n                    !!Object.keys(breaks[0]).length; // Check for [{}], #16368.\n                axis.isDirty = brokenAxis.hasBreaks !== hasBreaks;\n            brokenAxis.hasBreaks = hasBreaks;\n            // Compile string dates\n            breaks === null || breaks === void 0 ? void 0 : breaks.forEach(function (brk) {\n                brk.from = time.parse(brk.from) || 0;\n                brk.to = time.parse(brk.to) || 0;\n            });\n            if (breaks !== axis.options.breaks) {\n                axis.options.breaks = axis.userOptions.breaks = breaks;\n            }\n            axis.forceRedraw = true; // Force recalculation in setScale\n            // Recalculate series related to the axis.\n            axis.series.forEach(function (series) {\n                series.isDirty = true;\n            });\n            if (!hasBreaks && axis.val2lin === Additions.val2Lin) {\n                // Revert to prototype functions\n                delete axis.val2lin;\n                delete axis.lin2val;\n            }\n            if (hasBreaks) {\n                axis.userOptions.ordinal = false;\n                axis.lin2val = Additions.lin2Val;\n                axis.val2lin = Additions.val2Lin;\n                axis.setExtremes = function (newMin, newMax, redraw, animation, eventArguments) {\n                    // If trying to set extremes inside a break, extend min to\n                    // after, and max to before the break ( #3857 )\n                    if (brokenAxis.hasBreaks) {\n                        var breaks_1 = (this.options.breaks || []);\n                        var axisBreak = void 0;\n                        while ((axisBreak = brokenAxis.findBreakAt(newMin, breaks_1))) {\n                            newMin = axisBreak.to;\n                        }\n                        while ((axisBreak = brokenAxis.findBreakAt(newMax, breaks_1))) {\n                            newMax = axisBreak.from;\n                        }\n                        // If both min and max is within the same break.\n                        if (newMax < newMin) {\n                            newMax = newMin;\n                        }\n                    }\n                    axis.constructor.prototype.setExtremes.call(this, newMin, newMax, redraw, animation, eventArguments);\n                };\n                axis.setAxisTranslation = function () {\n                    axis.constructor.prototype.setAxisTranslation.call(this);\n                    brokenAxis.unitLength = void 0;\n                    if (brokenAxis.hasBreaks) {\n                        var breaks_2 = axis.options.breaks || [], \n                            // Temporary one:\n                            breakArrayT_1 = [],\n                            breakArray_1 = [],\n                            pointRangePadding = pick(axis.pointRangePadding, 0);\n                        var length_1 = 0,\n                            inBrk_1,\n                            repeat_1,\n                            min_1 = axis.userMin || axis.min,\n                            max_1 = axis.userMax || axis.max,\n                            start_1,\n                            i_1;\n                        // Min & max check (#4247)\n                        breaks_2.forEach(function (brk) {\n                            repeat_1 = brk.repeat || Infinity;\n                            if (isNumber(min_1) && isNumber(max_1)) {\n                                if (Additions.isInBreak(brk, min_1)) {\n                                    min_1 += ((brk.to % repeat_1) -\n                                        (min_1 % repeat_1));\n                                }\n                                if (Additions.isInBreak(brk, max_1)) {\n                                    max_1 -= ((max_1 % repeat_1) -\n                                        (brk.from % repeat_1));\n                                }\n                            }\n                        });\n                        // Construct an array holding all breaks in the axis\n                        breaks_2.forEach(function (brk) {\n                            start_1 = brk.from;\n                            repeat_1 = brk.repeat || Infinity;\n                            if (isNumber(min_1) && isNumber(max_1)) {\n                                while (start_1 - repeat_1 > min_1) {\n                                    start_1 -= repeat_1;\n                                }\n                                while (start_1 < min_1) {\n                                    start_1 += repeat_1;\n                                }\n                                for (i_1 = start_1; i_1 < max_1; i_1 += repeat_1) {\n                                    breakArrayT_1.push({\n                                        value: i_1,\n                                        move: 'in'\n                                    });\n                                    breakArrayT_1.push({\n                                        value: i_1 + brk.to - brk.from,\n                                        move: 'out',\n                                        size: brk.breakSize\n                                    });\n                                }\n                            }\n                        });\n                        breakArrayT_1.sort(function (a, b) {\n                            return ((a.value === b.value) ?\n                                ((a.move === 'in' ? 0 : 1) -\n                                    (b.move === 'in' ? 0 : 1)) :\n                                a.value - b.value);\n                        });\n                        // Simplify the breaks\n                        inBrk_1 = 0;\n                        start_1 = min_1;\n                        breakArrayT_1.forEach(function (brk) {\n                            inBrk_1 += (brk.move === 'in' ? 1 : -1);\n                            if (inBrk_1 === 1 && brk.move === 'in') {\n                                start_1 = brk.value;\n                            }\n                            if (inBrk_1 === 0 && isNumber(start_1)) {\n                                breakArray_1.push({\n                                    from: start_1,\n                                    to: brk.value,\n                                    len: brk.value - start_1 - (brk.size || 0)\n                                });\n                                length_1 += (brk.value -\n                                    start_1 -\n                                    (brk.size || 0));\n                            }\n                        });\n                        brokenAxis.breakArray = breakArray_1;\n                        // Used with staticScale, and below the actual axis\n                        // length, when breaks are subtracted.\n                        if (isNumber(min_1) &&\n                            isNumber(max_1) &&\n                            isNumber(axis.min)) {\n                            brokenAxis.unitLength = max_1 - min_1 - length_1 +\n                                pointRangePadding;\n                            fireEvent(axis, 'afterBreaks');\n                            if (axis.staticScale) {\n                                axis.transA = axis.staticScale;\n                            }\n                            else if (brokenAxis.unitLength) {\n                                axis.transA *=\n                                    (max_1 - axis.min + pointRangePadding) /\n                                        brokenAxis.unitLength;\n                            }\n                            if (pointRangePadding) {\n                                axis.minPixelPadding =\n                                    axis.transA * (axis.minPointOffset || 0);\n                            }\n                            axis.min = min_1;\n                            axis.max = max_1;\n                        }\n                    }\n                };\n            }\n            if (pick(redraw, true)) {\n                axis.chart.redraw();\n            }\n        };\n        return Additions;\n    }());\n    BrokenAxis.Additions = Additions;\n})(BrokenAxis || (BrokenAxis = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Axis_BrokenAxis = (BrokenAxis);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Axis\"],\"commonjs\":[\"highcharts\",\"Axis\"],\"commonjs2\":[\"highcharts\",\"Axis\"],\"root\":[\"Highcharts\",\"Axis\"]}\nvar highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_ = __webpack_require__(532);\nvar highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default = /*#__PURE__*/__webpack_require__.n(highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_);\n;// ./code/es5/es-modules/Core/Axis/GridAxis.js\n/* *\n *\n *  (c) 2016 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nvar dateFormats = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).dateFormats;\n\nvar GridAxis_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, erase = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).erase, GridAxis_find = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).find, GridAxis_isArray = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isArray, GridAxis_isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, GridAxis_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, timeUnits = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).timeUnits, wrap = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).wrap;\n/* *\n *\n *  Enums\n *\n * */\n/**\n * Enum for which side the axis is on. Maps to axis.side.\n * @private\n */\nvar GridAxisSide;\n(function (GridAxisSide) {\n    GridAxisSide[GridAxisSide[\"top\"] = 0] = \"top\";\n    GridAxisSide[GridAxisSide[\"right\"] = 1] = \"right\";\n    GridAxisSide[GridAxisSide[\"bottom\"] = 2] = \"bottom\";\n    GridAxisSide[GridAxisSide[\"left\"] = 3] = \"left\";\n})(GridAxisSide || (GridAxisSide = {}));\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction argsToArray(args) {\n    return Array.prototype.slice.call(args, 1);\n}\n/**\n * @private\n */\nfunction isObject(x) {\n    // Always use strict mode\n    return highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().isObject(x, true);\n}\n/**\n * @private\n */\nfunction applyGridOptions(axis) {\n    var options = axis.options;\n    // Center-align by default\n    /*\n    if (!options.labels) {\n        options.labels = {};\n    }\n    */\n    options.labels.align = GridAxis_pick(options.labels.align, 'center');\n    // @todo: Check against tickLabelPlacement between/on etc\n    /* Prevents adding the last tick label if the axis is not a category\n       axis.\n       Since numeric labels are normally placed at starts and ends of a\n       range of value, and this module makes the label point at the value,\n       an \"extra\" label would appear. */\n    if (!axis.categories) {\n        options.showLastLabel = false;\n    }\n    // Prevents rotation of labels when squished, as rotating them would not\n    // help.\n    axis.labelRotation = 0;\n    options.labels.rotation = 0;\n    // Allow putting ticks closer than their data points.\n    options.minTickInterval = 1;\n}\n/**\n * Extends axis class with grid support.\n * @private\n */\nfunction compose(AxisClass, ChartClass, TickClass) {\n    if (!AxisClass.keepProps.includes('grid')) {\n        AxisClass.keepProps.push('grid');\n        AxisClass.prototype.getMaxLabelDimensions = getMaxLabelDimensions;\n        wrap(AxisClass.prototype, 'unsquish', wrapUnsquish);\n        wrap(AxisClass.prototype, 'getOffset', wrapGetOffset);\n        // Add event handlers\n        GridAxis_addEvent(AxisClass, 'init', onInit);\n        GridAxis_addEvent(AxisClass, 'afterGetTitlePosition', onAfterGetTitlePosition);\n        GridAxis_addEvent(AxisClass, 'afterInit', onAfterInit);\n        GridAxis_addEvent(AxisClass, 'afterRender', onAfterRender);\n        GridAxis_addEvent(AxisClass, 'afterSetAxisTranslation', onAfterSetAxisTranslation);\n        GridAxis_addEvent(AxisClass, 'afterSetOptions', onAfterSetOptions);\n        GridAxis_addEvent(AxisClass, 'afterSetOptions', onAfterSetOptions2);\n        GridAxis_addEvent(AxisClass, 'afterSetScale', onAfterSetScale);\n        GridAxis_addEvent(AxisClass, 'afterTickSize', onAfterTickSize);\n        GridAxis_addEvent(AxisClass, 'trimTicks', onTrimTicks);\n        GridAxis_addEvent(AxisClass, 'destroy', onDestroy);\n        GridAxis_addEvent(ChartClass, 'afterSetChartSize', onChartAfterSetChartSize);\n        GridAxis_addEvent(TickClass, 'afterGetLabelPosition', onTickAfterGetLabelPosition);\n        GridAxis_addEvent(TickClass, 'labelFormat', onTickLabelFormat);\n    }\n    return AxisClass;\n}\n/**\n * Get the largest label width and height.\n *\n * @private\n * @function Highcharts.Axis#getMaxLabelDimensions\n *\n * @param {Highcharts.Dictionary<Highcharts.Tick>} ticks\n * All the ticks on one axis.\n *\n * @param {Array<number|string>} tickPositions\n * All the tick positions on one axis.\n *\n * @return {Highcharts.SizeObject}\n * Object containing the properties height and width.\n *\n * @todo Move this to the generic axis implementation, as it is used there.\n */\nfunction getMaxLabelDimensions(ticks, tickPositions) {\n    var dimensions = {\n            width: 0,\n            height: 0\n        };\n    tickPositions.forEach(function (pos) {\n        var tick = ticks[pos];\n        var labelHeight = 0,\n            labelWidth = 0,\n            label;\n        if (isObject(tick)) {\n            label = isObject(tick.label) ? tick.label : {};\n            // Find width and height of label\n            labelHeight = label.getBBox ? label.getBBox().height : 0;\n            if (label.textStr && !GridAxis_isNumber(label.textPxLength)) {\n                label.textPxLength = label.getBBox().width;\n            }\n            labelWidth = GridAxis_isNumber(label.textPxLength) ?\n                // Math.round ensures crisp lines\n                Math.round(label.textPxLength) :\n                0;\n            if (label.textStr) {\n                // Set the tickWidth same as the label width after ellipsis\n                // applied #10281\n                labelWidth = Math.round(label.getBBox().width);\n            }\n            // Update the result if width and/or height are larger\n            dimensions.height = Math.max(labelHeight, dimensions.height);\n            dimensions.width = Math.max(labelWidth, dimensions.width);\n        }\n    });\n    // For tree grid, add indentation\n    if (this.type === 'treegrid' &&\n        this.treeGrid &&\n        this.treeGrid.mapOfPosToGridNode) {\n        var treeDepth = this.treeGrid.mapOfPosToGridNode[-1].height || 0;\n        dimensions.width += (this.options.labels.indentation *\n            (treeDepth - 1));\n    }\n    return dimensions;\n}\n/**\n * Handle columns and getOffset.\n * @private\n */\nfunction wrapGetOffset(proceed) {\n    var grid = this.grid, \n        // On the left side we handle the columns first because the offset is\n        // calculated from the plot area and out\n        columnsFirst = this.side === 3;\n    if (!columnsFirst) {\n        proceed.apply(this);\n    }\n    if (!(grid === null || grid === void 0 ? void 0 : grid.isColumn)) {\n        var columns = (grid === null || grid === void 0 ? void 0 : grid.columns) || [];\n        if (columnsFirst) {\n            columns = columns.slice().reverse();\n        }\n        columns\n            .forEach(function (column) {\n            column.getOffset();\n        });\n    }\n    if (columnsFirst) {\n        proceed.apply(this);\n    }\n}\n/**\n * @private\n */\nfunction onAfterGetTitlePosition(e) {\n    var axis = this;\n    var options = axis.options;\n    var gridOptions = options.grid || {};\n    if (gridOptions.enabled === true) {\n        // Compute anchor points for each of the title align options\n        var axisTitle = axis.axisTitle,\n            axisHeight = axis.height,\n            horiz = axis.horiz,\n            axisLeft = axis.left,\n            offset = axis.offset,\n            opposite = axis.opposite,\n            options_1 = axis.options,\n            axisTop = axis.top,\n            axisWidth = axis.width;\n        var tickSize = axis.tickSize();\n        var titleWidth = axisTitle === null || axisTitle === void 0 ? void 0 : axisTitle.getBBox().width;\n        var xOption = options_1.title.x;\n        var yOption = options_1.title.y;\n        var titleMargin = GridAxis_pick(options_1.title.margin,\n            horiz ? 5 : 10);\n        var titleFontSize = axisTitle ? axis.chart.renderer.fontMetrics(axisTitle).f : 0;\n        var crispCorr = tickSize ? tickSize[0] / 2 : 0;\n        // TODO account for alignment\n        // the position in the perpendicular direction of the axis\n        var offAxis = ((horiz ? axisTop + axisHeight : axisLeft) +\n                (horiz ? 1 : -1) * // Horizontal axis reverses the margin\n                    (opposite ? -1 : 1) * // So does opposite axes\n                    crispCorr +\n                (axis.side === GridAxisSide.bottom ? titleFontSize : 0));\n        e.titlePosition.x = horiz ?\n            axisLeft - (titleWidth || 0) / 2 - titleMargin + xOption :\n            offAxis + (opposite ? axisWidth : 0) + offset + xOption;\n        e.titlePosition.y = horiz ?\n            (offAxis -\n                (opposite ? axisHeight : 0) +\n                (opposite ? titleFontSize : -titleFontSize) / 2 +\n                offset +\n                yOption) :\n            axisTop - titleMargin + yOption;\n    }\n}\n/**\n * @private\n */\nfunction onAfterInit() {\n    var axis = this;\n    var chart = axis.chart,\n        _a = axis.options.grid,\n        gridOptions = _a === void 0 ? {} : _a,\n        userOptions = axis.userOptions;\n    if (gridOptions.enabled) {\n        applyGridOptions(axis);\n    }\n    if (gridOptions.columns) {\n        var columns = axis.grid.columns = [];\n        var columnIndex = axis.grid.columnIndex = 0;\n        // Handle columns, each column is a grid axis\n        while (++columnIndex < gridOptions.columns.length) {\n            var columnOptions = merge(userOptions,\n                gridOptions.columns[columnIndex], {\n                    isInternal: true,\n                    linkedTo: 0,\n                    // Disable by default the scrollbar on the grid axis\n                    scrollbar: {\n                        enabled: false\n                    }\n                }, \n                // Avoid recursion\n                {\n                    grid: {\n                        columns: void 0\n                    }\n                });\n            var column = new (highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default())(axis.chart,\n                columnOptions, 'yAxis');\n            column.grid.isColumn = true;\n            column.grid.columnIndex = columnIndex;\n            // Remove column axis from chart axes array, and place it\n            // in the columns array.\n            erase(chart.axes, column);\n            erase(chart[axis.coll] || [], column);\n            columns.push(column);\n        }\n    }\n}\n/**\n * Draw an extra line on the far side of the outermost axis,\n * creating floor/roof/wall of a grid. And some padding.\n * ```\n * Make this:\n *             (axis.min) __________________________ (axis.max)\n *                           |    |    |    |    |\n * Into this:\n *             (axis.min) __________________________ (axis.max)\n *                        ___|____|____|____|____|__\n * ```\n * @private\n */\nfunction onAfterRender() {\n    var _a,\n        _b;\n    var axis = this,\n        axisTitle = axis.axisTitle,\n        grid = axis.grid,\n        options = axis.options,\n        gridOptions = options.grid || {};\n    if (gridOptions.enabled === true) {\n        var min = axis.min || 0,\n            max = axis.max || 0,\n            firstTick = axis.ticks[axis.tickPositions[0]];\n        // Adjust the title max width to the column width (#19657)\n        if (axisTitle &&\n            !axis.chart.styledMode &&\n            (firstTick === null || firstTick === void 0 ? void 0 : firstTick.slotWidth) &&\n            !axis.options.title.style.width) {\n            axisTitle.css({ width: \"\" + firstTick.slotWidth + \"px\" });\n        }\n        // @todo actual label padding (top, bottom, left, right)\n        axis.maxLabelDimensions = axis.getMaxLabelDimensions(axis.ticks, axis.tickPositions);\n        // Remove right wall before rendering if updating\n        if (axis.rightWall) {\n            axis.rightWall.destroy();\n        }\n        /*\n        Draw an extra axis line on outer axes\n                    >\n        Make this:    |______|______|______|___\n\n                    > _________________________\n        Into this:    |______|______|______|__|\n                                                */\n        if (((_a = axis.grid) === null || _a === void 0 ? void 0 : _a.isOuterAxis()) && axis.axisLine) {\n            var lineWidth = options.lineWidth;\n            if (lineWidth) {\n                var linePath = axis.getLinePath(lineWidth),\n                    startPoint = linePath[0],\n                    endPoint = linePath[1], \n                    // Negate distance if top or left axis\n                    // Subtract 1px to draw the line at the end of the tick\n                    tickLength = (axis.tickSize('tick') || [1])[0],\n                    distance = tickLength * ((axis.side === GridAxisSide.top ||\n                        axis.side === GridAxisSide.left) ? -1 : 1);\n                // If axis is horizontal, reposition line path vertically\n                if (startPoint[0] === 'M' && endPoint[0] === 'L') {\n                    if (axis.horiz) {\n                        startPoint[2] += distance;\n                        endPoint[2] += distance;\n                    }\n                    else {\n                        startPoint[1] += distance;\n                        endPoint[1] += distance;\n                    }\n                }\n                // If it doesn't exist, add an upper and lower border\n                // for the vertical grid axis.\n                if (!axis.horiz && axis.chart.marginRight) {\n                    var upperBorderStartPoint = startPoint,\n                        upperBorderEndPoint = [\n                            'L',\n                            axis.left,\n                            startPoint[2] || 0\n                        ],\n                        upperBorderPath = [\n                            upperBorderStartPoint,\n                            upperBorderEndPoint\n                        ],\n                        lowerBorderEndPoint = [\n                            'L',\n                            axis.chart.chartWidth - axis.chart.marginRight,\n                            axis.toPixels(max + axis.tickmarkOffset)\n                        ],\n                        lowerBorderStartPoint = [\n                            'M',\n                            endPoint[1] || 0,\n                            axis.toPixels(max + axis.tickmarkOffset)\n                        ],\n                        lowerBorderPath = [\n                            lowerBorderStartPoint,\n                            lowerBorderEndPoint\n                        ];\n                    if (!axis.grid.upperBorder && min % 1 !== 0) {\n                        axis.grid.upperBorder = axis.grid.renderBorder(upperBorderPath);\n                    }\n                    if (axis.grid.upperBorder) {\n                        axis.grid.upperBorder.attr({\n                            stroke: options.lineColor,\n                            'stroke-width': options.lineWidth\n                        });\n                        axis.grid.upperBorder.animate({\n                            d: upperBorderPath\n                        });\n                    }\n                    if (!axis.grid.lowerBorder && max % 1 !== 0) {\n                        axis.grid.lowerBorder = axis.grid.renderBorder(lowerBorderPath);\n                    }\n                    if (axis.grid.lowerBorder) {\n                        axis.grid.lowerBorder.attr({\n                            stroke: options.lineColor,\n                            'stroke-width': options.lineWidth\n                        });\n                        axis.grid.lowerBorder.animate({\n                            d: lowerBorderPath\n                        });\n                    }\n                }\n                // Render an extra line parallel to the existing axes, to\n                // close the grid.\n                if (!axis.grid.axisLineExtra) {\n                    axis.grid.axisLineExtra = axis.grid.renderBorder(linePath);\n                }\n                else {\n                    axis.grid.axisLineExtra.attr({\n                        stroke: options.lineColor,\n                        'stroke-width': options.lineWidth\n                    });\n                    axis.grid.axisLineExtra.animate({\n                        d: linePath\n                    });\n                }\n                // Show or hide the line depending on options.showEmpty\n                axis.axisLine[axis.showAxis ? 'show' : 'hide']();\n            }\n        }\n        ((grid === null || grid === void 0 ? void 0 : grid.columns) || []).forEach(function (column) { return column.render(); });\n        // Manipulate the tick mark visibility\n        // based on the axis.max- allows smooth scrolling.\n        if (!axis.horiz &&\n            axis.chart.hasRendered &&\n            (axis.scrollbar || ((_b = axis.linkedParent) === null || _b === void 0 ? void 0 : _b.scrollbar)) &&\n            axis.tickPositions.length) {\n            var tickmarkOffset = axis.tickmarkOffset,\n                lastTick = axis.tickPositions[axis.tickPositions.length - 1],\n                firstTick_1 = axis.tickPositions[0];\n            var label = void 0,\n                tickMark = void 0;\n            while ((label = axis.hiddenLabels.pop()) && label.element) {\n                label.show(); // #15453\n            }\n            while ((tickMark = axis.hiddenMarks.pop()) &&\n                tickMark.element) {\n                tickMark.show(); // #16439\n            }\n            // Hide/show first tick label.\n            label = axis.ticks[firstTick_1].label;\n            if (label) {\n                if (min - firstTick_1 > tickmarkOffset) {\n                    axis.hiddenLabels.push(label.hide());\n                }\n                else {\n                    label.show();\n                }\n            }\n            // Hide/show last tick mark/label.\n            label = axis.ticks[lastTick].label;\n            if (label) {\n                if (lastTick - max > tickmarkOffset) {\n                    axis.hiddenLabels.push(label.hide());\n                }\n                else {\n                    label.show();\n                }\n            }\n            var mark = axis.ticks[lastTick].mark;\n            if (mark &&\n                lastTick - max < tickmarkOffset &&\n                lastTick - max > 0 && axis.ticks[lastTick].isLast) {\n                axis.hiddenMarks.push(mark.hide());\n            }\n        }\n    }\n}\n/**\n * @private\n */\nfunction onAfterSetAxisTranslation() {\n    var _a;\n    var axis = this;\n    var tickInfo = (_a = axis.tickPositions) === null || _a === void 0 ? void 0 : _a.info;\n    var options = axis.options;\n    var gridOptions = options.grid || {};\n    var userLabels = axis.userOptions.labels || {};\n    // Fire this only for the Gantt type chart, #14868.\n    if (gridOptions.enabled) {\n        if (axis.horiz) {\n            axis.series.forEach(function (series) {\n                series.options.pointRange = 0;\n            });\n            // Lower level time ticks, like hours or minutes, represent\n            // points in time and not ranges. These should be aligned\n            // left in the grid cell by default. The same applies to\n            // years of higher order.\n            if (tickInfo &&\n                options.dateTimeLabelFormats &&\n                options.labels &&\n                !defined(userLabels.align) &&\n                (options.dateTimeLabelFormats[tickInfo.unitName]\n                    .range === false ||\n                    tickInfo.count > 1 // Years\n                )) {\n                options.labels.align = 'left';\n                if (!defined(userLabels.x)) {\n                    options.labels.x = 3;\n                }\n            }\n        }\n        else {\n            // Don't trim ticks which not in min/max range but\n            // they are still in the min/max plus tickInterval.\n            if (this.type !== 'treegrid' &&\n                axis.grid &&\n                axis.grid.columns) {\n                this.minPointOffset = this.tickInterval;\n            }\n        }\n    }\n}\n/**\n * Creates a left and right wall on horizontal axes:\n * - Places leftmost tick at the start of the axis, to create a left\n *   wall\n * - Ensures that the rightmost tick is at the end of the axis, to\n *   create a right wall.\n * @private\n */\nfunction onAfterSetOptions(e) {\n    var options = this.options,\n        userOptions = e.userOptions,\n        gridOptions = ((options && isObject(options.grid)) ? options.grid : {});\n    var gridAxisOptions;\n    if (gridOptions.enabled === true) {\n        // Merge the user options into default grid axis options so\n        // that when a user option is set, it takes precedence.\n        gridAxisOptions = merge(true, {\n            className: ('highcharts-grid-axis ' + (userOptions.className || '')),\n            dateTimeLabelFormats: {\n                hour: {\n                    list: ['%[HM]', '%[H]']\n                },\n                day: {\n                    list: ['%[AeB]', '%[aeb]', '%[E]']\n                },\n                week: {\n                    list: ['Week %W', 'W%W']\n                },\n                month: {\n                    list: ['%[B]', '%[b]', '%o']\n                }\n            },\n            grid: {\n                borderWidth: 1\n            },\n            labels: {\n                padding: 2,\n                style: {\n                    fontSize: '0.9em'\n                }\n            },\n            margin: 0,\n            title: {\n                text: null,\n                reserveSpace: false,\n                rotation: 0,\n                style: {\n                    textOverflow: 'ellipsis'\n                }\n            },\n            // In a grid axis, only allow one unit of certain types,\n            // for example we shouldn't have one grid cell spanning\n            // two days.\n            units: [[\n                    'millisecond', // Unit name\n                    [1, 10, 100]\n                ], [\n                    'second',\n                    [1, 10]\n                ], [\n                    'minute',\n                    [1, 5, 15]\n                ], [\n                    'hour',\n                    [1, 6]\n                ], [\n                    'day',\n                    [1]\n                ], [\n                    'week',\n                    [1]\n                ], [\n                    'month',\n                    [1]\n                ], [\n                    'year',\n                    null\n                ]]\n        }, userOptions);\n        // X-axis specific options\n        if (this.coll === 'xAxis') {\n            // For linked axes, tickPixelInterval is used only if\n            // the tickPositioner below doesn't run or returns\n            // undefined (like multiple years)\n            if (defined(userOptions.linkedTo) &&\n                !defined(userOptions.tickPixelInterval)) {\n                gridAxisOptions.tickPixelInterval = 350;\n            }\n            // For the secondary grid axis, use the primary axis'\n            // tick intervals and return ticks one level higher.\n            if (\n            // Check for tick pixel interval in options\n            !defined(userOptions.tickPixelInterval) &&\n                // Only for linked axes\n                defined(userOptions.linkedTo) &&\n                !defined(userOptions.tickPositioner) &&\n                !defined(userOptions.tickInterval) &&\n                !defined(userOptions.units)) {\n                gridAxisOptions.tickPositioner = function (min, max) {\n                    var _a,\n                        _b;\n                    var parentInfo = (_b = (_a = this.linkedParent) === null || _a === void 0 ? void 0 : _a.tickPositions) === null || _b === void 0 ? void 0 : _b.info;\n                    if (parentInfo) {\n                        var units = (gridAxisOptions.units || []);\n                        var unitIdx = void 0,\n                            count = 1,\n                            unitName = 'year';\n                        for (var i = 0; i < units.length; i++) {\n                            var unit_1 = units[i];\n                            if (unit_1 && unit_1[0] === parentInfo.unitName) {\n                                unitIdx = i;\n                                break;\n                            }\n                        }\n                        // Get the first allowed count on the next unit.\n                        var unit = (GridAxis_isNumber(unitIdx) && units[unitIdx + 1]);\n                        if (unit) {\n                            unitName = unit[0] || 'year';\n                            var counts = unit[1];\n                            count = (counts === null || counts === void 0 ? void 0 : counts[0]) || 1;\n                            // In case the base X axis shows years, make the\n                            // secondary axis show ten times the years (#11427)\n                        }\n                        else if (parentInfo.unitName === 'year') {\n                            // `unitName` is 'year'\n                            count = parentInfo.count * 10;\n                        }\n                        var unitRange = timeUnits[unitName];\n                        this.tickInterval = unitRange * count;\n                        return this.chart.time.getTimeTicks({ unitRange: unitRange, count: count, unitName: unitName }, min, max, this.options.startOfWeek);\n                    }\n                };\n            }\n        }\n        // Now merge the combined options into the axis options\n        merge(true, this.options, gridAxisOptions);\n        if (this.horiz) {\n            /*               _________________________\n            Make this:    ___|_____|_____|_____|__|\n                            ^                     ^\n                            _________________________\n            Into this:    |_____|_____|_____|_____|\n                                ^                 ^    */\n            options.minPadding = GridAxis_pick(userOptions.minPadding, 0);\n            options.maxPadding = GridAxis_pick(userOptions.maxPadding, 0);\n        }\n        // If borderWidth is set, then use its value for tick and\n        // line width.\n        if (GridAxis_isNumber(options.grid.borderWidth)) {\n            options.tickWidth = options.lineWidth =\n                gridOptions.borderWidth;\n        }\n    }\n}\n/**\n * @private\n */\nfunction onAfterSetOptions2(e) {\n    var axis = this;\n    var userOptions = e.userOptions;\n    var gridOptions = (userOptions === null || userOptions === void 0 ? void 0 : userOptions.grid) || {};\n    var columns = gridOptions.columns;\n    // Add column options to the parent axis. Children has their column options\n    // set on init in onGridAxisAfterInit.\n    if (gridOptions.enabled && columns) {\n        merge(true, axis.options, columns[0]);\n    }\n}\n/**\n * Handle columns and setScale.\n * @private\n */\nfunction onAfterSetScale() {\n    var axis = this;\n    (axis.grid.columns || []).forEach(function (column) { return column.setScale(); });\n}\n/**\n * Draw vertical axis ticks extra long to create cell floors and roofs.\n * Overrides the tickLength for vertical axes.\n * @private\n */\nfunction onAfterTickSize(e) {\n    var _a = this,\n        horiz = _a.horiz,\n        maxLabelDimensions = _a.maxLabelDimensions,\n        _b = _a.options.grid,\n        gridOptions = _b === void 0 ? {} : _b;\n    if (gridOptions.enabled && maxLabelDimensions) {\n        var labelPadding = this.options.labels.distance * 2;\n        var distance = horiz ?\n                (gridOptions.cellHeight ||\n                    labelPadding + maxLabelDimensions.height) :\n                labelPadding + maxLabelDimensions.width;\n        if (GridAxis_isArray(e.tickSize)) {\n            e.tickSize[0] = distance;\n        }\n        else {\n            e.tickSize = [distance, 0];\n        }\n    }\n}\n/**\n * @private\n */\nfunction onChartAfterSetChartSize() {\n    this.axes.forEach(function (axis) {\n        var _a;\n        (((_a = axis.grid) === null || _a === void 0 ? void 0 : _a.columns) || []).forEach(function (column) {\n            column.setAxisSize();\n            column.setAxisTranslation();\n        });\n    });\n}\n/**\n * @private\n */\nfunction onDestroy(e) {\n    var grid = this.grid;\n    (grid.columns || []).forEach(function (column) { return column.destroy(e.keepEvents); });\n    grid.columns = void 0;\n}\n/**\n * Wraps axis init to draw cell walls on vertical axes.\n * @private\n */\nfunction onInit(e) {\n    var axis = this;\n    var userOptions = e.userOptions || {};\n    var gridOptions = userOptions.grid || {};\n    if (gridOptions.enabled && defined(gridOptions.borderColor)) {\n        userOptions.tickColor = userOptions.lineColor = (gridOptions.borderColor);\n    }\n    if (!axis.grid) {\n        axis.grid = new GridAxisAdditions(axis);\n    }\n    axis.hiddenLabels = [];\n    axis.hiddenMarks = [];\n}\n/**\n * Center tick labels in cells.\n * @private\n */\nfunction onTickAfterGetLabelPosition(e) {\n    var tick = this,\n        label = tick.label,\n        axis = tick.axis,\n        reversed = axis.reversed,\n        chart = axis.chart,\n        options = axis.options,\n        gridOptions = options.grid || {},\n        labelOpts = axis.options.labels,\n        align = labelOpts.align, \n        // `verticalAlign` is currently not supported for axis.labels.\n        verticalAlign = 'middle', // LabelOpts.verticalAlign,\n        side = GridAxisSide[axis.side],\n        tickmarkOffset = e.tickmarkOffset,\n        tickPositions = axis.tickPositions,\n        tickPos = tick.pos - tickmarkOffset,\n        nextTickPos = (GridAxis_isNumber(tickPositions[e.index + 1]) ?\n            tickPositions[e.index + 1] - tickmarkOffset :\n            (axis.max || 0) + tickmarkOffset),\n        tickSize = axis.tickSize('tick'),\n        tickWidth = tickSize ? tickSize[0] : 0,\n        crispCorr = tickSize ? tickSize[1] / 2 : 0;\n    // Only center tick labels in grid axes\n    if (gridOptions.enabled === true) {\n        var bottom = void 0,\n            top_1,\n            left = void 0,\n            right = void 0;\n        // Calculate top and bottom positions of the cell.\n        if (side === 'top') {\n            bottom = axis.top + axis.offset;\n            top_1 = bottom - tickWidth;\n        }\n        else if (side === 'bottom') {\n            top_1 = chart.chartHeight - axis.bottom + axis.offset;\n            bottom = top_1 + tickWidth;\n        }\n        else {\n            bottom = axis.top + axis.len - (axis.translate(reversed ? nextTickPos : tickPos) || 0);\n            top_1 = axis.top + axis.len - (axis.translate(reversed ? tickPos : nextTickPos) || 0);\n        }\n        // Calculate left and right positions of the cell.\n        if (side === 'right') {\n            left = chart.chartWidth - axis.right + axis.offset;\n            right = left + tickWidth;\n        }\n        else if (side === 'left') {\n            right = axis.left + axis.offset;\n            left = right - tickWidth;\n        }\n        else {\n            left = Math.round(axis.left + (axis.translate(reversed ? nextTickPos : tickPos) || 0)) - crispCorr;\n            right = Math.min(// #15742\n            Math.round(axis.left + (axis.translate(reversed ? tickPos : nextTickPos) || 0)) - crispCorr, axis.left + axis.len);\n        }\n        tick.slotWidth = right - left;\n        // Calculate the positioning of the label based on\n        // alignment.\n        e.pos.x = (align === 'left' ?\n            left :\n            align === 'right' ?\n                right :\n                left + ((right - left) / 2) // Default to center\n        );\n        e.pos.y = (verticalAlign === 'top' ?\n            top_1 :\n            verticalAlign === 'bottom' ?\n                bottom :\n                top_1 + ((bottom - top_1) / 2) // Default to middle\n        );\n        if (label) {\n            var lblMetrics = chart.renderer.fontMetrics(label),\n                labelHeight = label.getBBox().height;\n            // Adjustment to y position to align the label correctly.\n            // Would be better to have a setter or similar for this.\n            if (!labelOpts.useHTML) {\n                var lines = Math.round(labelHeight / lblMetrics.h);\n                e.pos.y += (\n                // Center the label\n                // TODO: why does this actually center the label?\n                ((lblMetrics.b - (lblMetrics.h - lblMetrics.f)) / 2) +\n                    // Adjust for height of additional lines.\n                    -(((lines - 1) * lblMetrics.h) / 2));\n            }\n            else {\n                e.pos.y += (\n                // Readjust yCorr in htmlUpdateTransform\n                lblMetrics.b +\n                    // Adjust for height of html label\n                    -(labelHeight / 2));\n            }\n        }\n        e.pos.x += (axis.horiz && labelOpts.x) || 0;\n    }\n}\n/**\n * @private\n */\nfunction onTickLabelFormat(ctx) {\n    var _a;\n    var axis = ctx.axis,\n        value = ctx.value;\n    if ((_a = axis.options.grid) === null || _a === void 0 ? void 0 : _a.enabled) {\n        var tickPos = axis.tickPositions;\n        var series = (axis.linkedParent || axis).series[0];\n        var isFirst = value === tickPos[0];\n        var isLast = value === tickPos[tickPos.length - 1];\n        var point = series && GridAxis_find(series.options.data,\n            function (p) {\n                return p[axis.isXAxis ? 'x' : 'y'] === value;\n        });\n        var pointCopy = void 0;\n        if (point && series.is('gantt')) {\n            // For the Gantt set point aliases to the pointCopy\n            // to do not change the original point\n            pointCopy = merge(point);\n            highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default().seriesTypes.gantt.prototype.pointClass\n                .setGanttPointAliases(pointCopy, axis.chart);\n        }\n        // Make additional properties available for the\n        // formatter\n        ctx.isFirst = isFirst;\n        ctx.isLast = isLast;\n        ctx.point = pointCopy;\n    }\n}\n/**\n * Makes tick labels which are usually ignored in a linked axis\n * displayed if they are within range of linkedParent.min.\n * ```\n *                        _____________________________\n *                        |   |       |       |       |\n * Make this:             |   |   2   |   3   |   4   |\n *                        |___|_______|_______|_______|\n *                          ^\n *                        _____________________________\n *                        |   |       |       |       |\n * Into this:             | 1 |   2   |   3   |   4   |\n *                        |___|_______|_______|_______|\n *                          ^\n * ```\n * @private\n * @todo Does this function do what the drawing says? Seems to affect\n *       ticks and not the labels directly?\n */\nfunction onTrimTicks() {\n    var _a,\n        _b;\n    var axis = this,\n        options = axis.options,\n        gridOptions = options.grid || {},\n        categoryAxis = axis.categories,\n        tickPositions = axis.tickPositions,\n        firstPos = tickPositions[0],\n        secondPos = tickPositions[1],\n        lastPos = tickPositions[tickPositions.length - 1],\n        beforeLastPos = tickPositions[tickPositions.length - 2],\n        linkedMin = (_a = axis.linkedParent) === null || _a === void 0 ? void 0 : _a.min,\n        linkedMax = (_b = axis.linkedParent) === null || _b === void 0 ? void 0 : _b.max,\n        min = linkedMin || axis.min,\n        max = linkedMax || axis.max,\n        tickInterval = axis.tickInterval,\n        startLessThanMin = ( // #19845\n        GridAxis_isNumber(min) &&\n            min >= firstPos + tickInterval &&\n            min < secondPos),\n        endMoreThanMin = (GridAxis_isNumber(min) &&\n            firstPos < min &&\n            firstPos + tickInterval > min),\n        startLessThanMax = (GridAxis_isNumber(max) &&\n            lastPos > max &&\n            lastPos - tickInterval < max),\n        endMoreThanMax = (GridAxis_isNumber(max) &&\n            max <= lastPos - tickInterval &&\n            max > beforeLastPos);\n    if (gridOptions.enabled === true &&\n        !categoryAxis &&\n        (axis.isXAxis || axis.isLinked)) {\n        if ((endMoreThanMin || startLessThanMin) && !options.startOnTick) {\n            tickPositions[0] = min;\n        }\n        if ((startLessThanMax || endMoreThanMax) && !options.endOnTick) {\n            tickPositions[tickPositions.length - 1] = max;\n        }\n    }\n}\n/**\n * Avoid altering tickInterval when reserving space.\n * @private\n */\nfunction wrapUnsquish(proceed) {\n    var axis = this;\n    var _a = axis.options.grid,\n        gridOptions = _a === void 0 ? {} : _a;\n    if (gridOptions.enabled === true && axis.categories) {\n        return axis.tickInterval;\n    }\n    return proceed.apply(axis, argsToArray(arguments));\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * Additions for grid axes.\n * @private\n * @class\n */\nvar GridAxisAdditions = /** @class */ (function () {\n    /* *\n    *\n    *  Constructors\n    *\n    * */\n    function GridAxisAdditions(axis) {\n        this.axis = axis;\n    }\n    /* *\n    *\n    *  Functions\n    *\n    * */\n    /**\n     * Checks if an axis is the outer axis in its dimension. Since\n     * axes are placed outwards in order, the axis with the highest\n     * index is the outermost axis.\n     *\n     * Example: If there are multiple x-axes at the top of the chart,\n     * this function returns true if the axis supplied is the last\n     * of the x-axes.\n     *\n     * @private\n     *\n     * @return {boolean}\n     * True if the axis is the outermost axis in its dimension; false if\n     * not.\n     */\n    GridAxisAdditions.prototype.isOuterAxis = function () {\n        var _a;\n        var axis = this.axis;\n        var chart = axis.chart;\n        var columnIndex = axis.grid.columnIndex;\n        var columns = (((_a = axis.linkedParent) === null || _a === void 0 ? void 0 : _a.grid.columns) ||\n                axis.grid.columns ||\n                []);\n        var parentAxis = columnIndex ? axis.linkedParent : axis;\n        var thisIndex = -1,\n            lastIndex = 0;\n        // On the left side, when we have columns (not only multiple axes), the\n        // main axis is to the left\n        if (axis.side === 3 && !chart.inverted && columns.length) {\n            return !axis.linkedParent;\n        }\n        (chart[axis.coll] || []).forEach(function (otherAxis, index) {\n            if (otherAxis.side === axis.side &&\n                !otherAxis.options.isInternal) {\n                lastIndex = index;\n                if (otherAxis === parentAxis) {\n                    // Get the index of the axis in question\n                    thisIndex = index;\n                }\n            }\n        });\n        return (lastIndex === thisIndex &&\n            (GridAxis_isNumber(columnIndex) ?\n                columns.length === columnIndex :\n                true));\n    };\n    /**\n     * Add extra border based on the provided path.\n     * @private\n     * @param {SVGPath} path\n     * The path of the border.\n     * @return {Highcharts.SVGElement}\n     * Border\n     */\n    GridAxisAdditions.prototype.renderBorder = function (path) {\n        var axis = this.axis,\n            renderer = axis.chart.renderer,\n            options = axis.options,\n            extraBorderLine = renderer.path(path)\n                .addClass('highcharts-axis-line')\n                .add(axis.axisGroup);\n        if (!renderer.styledMode) {\n            extraBorderLine.attr({\n                stroke: options.lineColor,\n                'stroke-width': options.lineWidth,\n                zIndex: 7\n            });\n        }\n        return extraBorderLine;\n    };\n    return GridAxisAdditions;\n}());\n/* *\n *\n *  Registry\n *\n * */\n// First letter of the day of the week, e.g. 'M' for 'Monday'.\ndateFormats.E = function (timestamp) {\n    return this.dateFormat('%a', timestamp, true).charAt(0);\n};\n// Adds week date format\ndateFormats.W = function (timestamp) {\n    var d = this.toParts(timestamp),\n        firstDay = (d[7] + 6) % 7,\n        thursday = d.slice(0);\n    thursday[2] = d[2] - firstDay + 3;\n    var firstThursday = this.toParts(this.makeTime(thursday[0], 0, 1));\n    if (firstThursday[7] !== 4) {\n        d[1] = 0; // Set month to January\n        d[2] = 1 + (11 - firstThursday[7]) % 7;\n    }\n    var thursdayTS = this.makeTime(thursday[0],\n        thursday[1],\n        thursday[2]),\n        firstThursdayTS = this.makeTime(firstThursday[0],\n        firstThursday[1],\n        firstThursday[2]);\n    return (1 +\n        Math.floor((thursdayTS - firstThursdayTS) / 604800000)).toString();\n};\n/* *\n *\n *  Default Export\n *\n * */\nvar GridAxis = {\n    compose: compose\n};\n/* harmony default export */ var Axis_GridAxis = (GridAxis);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * @productdesc {gantt}\n * For grid axes (like in Gantt charts),\n * it is possible to declare as a list to provide different\n * formats depending on available space.\n *\n * Defaults to:\n * ```js\n * {\n *     hour: { list: ['%H:%M', '%H'] },\n *     day: { list: ['%A, %e. %B', '%a, %e. %b', '%E'] },\n *     week: { list: ['Week %W', 'W%W'] },\n *     month: { list: ['%B', '%b', '%o'] }\n * }\n * ```\n *\n * @sample {gantt} gantt/grid-axis/date-time-label-formats\n *         Gantt chart with custom axis date format.\n *\n * @apioption xAxis.dateTimeLabelFormats\n */\n/**\n * Set grid options for the axis labels. Requires Highcharts Gantt.\n *\n * @since     6.2.0\n * @product   gantt\n * @apioption xAxis.grid\n */\n/**\n * Enable grid on the axis labels. Defaults to true for Gantt charts.\n *\n * @type      {boolean}\n * @default   true\n * @since     6.2.0\n * @product   gantt\n * @apioption xAxis.grid.enabled\n */\n/**\n * Set specific options for each column (or row for horizontal axes) in the\n * grid. Each extra column/row is its own axis, and the axis options can be set\n * here.\n *\n * @sample gantt/demo/left-axis-table\n *         Left axis as a table\n * @sample gantt/demo/treegrid-columns\n *         Collapsible tree grid with columns\n *\n * @type      {Array<Highcharts.XAxisOptions>}\n * @apioption xAxis.grid.columns\n */\n/**\n * Set border color for the label grid lines.\n *\n * @type      {Highcharts.ColorString}\n * @default   #e6e6e6\n * @apioption xAxis.grid.borderColor\n */\n/**\n * Set border width of the label grid lines.\n *\n * @type      {number}\n * @default   1\n * @apioption xAxis.grid.borderWidth\n */\n/**\n * Set cell height for grid axis labels. By default this is calculated from font\n * size. This option only applies to horizontal axes. For vertical axes, check\n * the [#yAxis.staticScale](yAxis.staticScale) option.\n *\n * @sample gantt/grid-axis/cellheight\n *         Gant chart with custom cell height\n * @type      {number}\n * @apioption xAxis.grid.cellHeight\n */\n''; // Keeps doclets above in JS file\n\n;// ./code/es5/es-modules/Gantt/Tree.js\n/* *\n *\n *  (c) 2016-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar __rest = (undefined && undefined.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\n/* *\n *\n *  Imports\n *\n * */\n\nvar extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, Tree_isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, Tree_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Creates an object map from parent id to children's index.\n *\n * @private\n * @function Highcharts.Tree#getListOfParents\n *\n * @param {Array<*>} data\n *        List of points set in options. `Array.parent` is parent id of point.\n *\n * @return {Highcharts.Dictionary<Array<*>>}\n * Map from parent id to children index in data\n */\nfunction getListOfParents(data) {\n    var root = '',\n        ids = [],\n        listOfParents = data.reduce(function (prev,\n        curr) {\n            var _a = curr.parent,\n        parent = _a === void 0 ? '' : _a,\n        id = curr.id;\n        if (typeof prev[parent] === 'undefined') {\n            prev[parent] = [];\n        }\n        prev[parent].push(curr);\n        if (id) {\n            ids.push(id);\n        }\n        return prev;\n    }, {});\n    Object.keys(listOfParents).forEach(function (node) {\n        var _a;\n        if ((node !== root) && (ids.indexOf(node) === -1)) {\n            var adoptedByRoot = listOfParents[node].map(function (orphan) {\n                    var parentExcluded = __rest(orphan,\n                []); // #15196\n                    return parentExcluded;\n            });\n            (_a = listOfParents[root]).push.apply(_a, adoptedByRoot);\n            delete listOfParents[node];\n        }\n    });\n    return listOfParents;\n}\n/** @private */\nfunction getNode(id, parent, level, data, mapOfIdToChildren, options) {\n    var after = options && options.after,\n        before = options && options.before,\n        node = {\n            data: data,\n            depth: level - 1,\n            id: id,\n            level: level,\n            parent: (parent || '')\n        };\n    var descendants = 0,\n        height = 0,\n        start,\n        end;\n    // Allow custom logic before the children has been created.\n    if (typeof before === 'function') {\n        before(node, options);\n    }\n    // Call getNode recursively on the children. Calculate the height of the\n    // node, and the number of descendants.\n    var children = ((mapOfIdToChildren[id] || [])).map(function (child) {\n            var node = getNode(child.id,\n        id, (level + 1),\n        child,\n        mapOfIdToChildren,\n        options),\n        childStart = child.start || NaN,\n        childEnd = (child.milestone === true ?\n                childStart :\n                child.end ||\n                    NaN);\n        // Start should be the lowest child.start.\n        start = ((!Tree_isNumber(start) || childStart < start) ?\n            childStart :\n            start);\n        // End should be the largest child.end.\n        // If child is milestone, then use start as end.\n        end = ((!Tree_isNumber(end) || childEnd > end) ?\n            childEnd :\n            end);\n        descendants = descendants + 1 + node.descendants;\n        height = Math.max(node.height + 1, height);\n        return node;\n    });\n    // Calculate start and end for point if it is not already explicitly set.\n    if (data) {\n        data.start = Tree_pick(data.start, start);\n        data.end = Tree_pick(data.end, end);\n    }\n    extend(node, {\n        children: children,\n        descendants: descendants,\n        height: height\n    });\n    // Allow custom logic after the children has been created.\n    if (typeof after === 'function') {\n        after(node, options);\n    }\n    return node;\n}\n/** @private */\nfunction getTree(data, options) {\n    return getNode('', null, 1, null, getListOfParents(data), options);\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar Tree = {\n    getNode: getNode,\n    getTree: getTree\n};\n/* harmony default export */ var Gantt_Tree = (Tree);\n\n;// ./code/es5/es-modules/Core/Axis/TreeGrid/TreeGridTick.js\n/* *\n *\n *  (c) 2016 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar TreeGridTick_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, removeEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).removeEvent, TreeGridTick_isObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isObject, TreeGridTick_isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, TreeGridTick_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, TreeGridTick_wrap = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).wrap;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction onTickInit() {\n    var tick = this;\n    if (!tick.treeGrid) {\n        tick.treeGrid = new TreeGridTickAdditions(tick);\n    }\n}\n/**\n * @private\n */\nfunction onTickHover(label) {\n    label.addClass('highcharts-treegrid-node-active');\n    if (!label.renderer.styledMode) {\n        label.css({\n            textDecoration: 'underline'\n        });\n    }\n}\n/**\n * @private\n */\nfunction onTickHoverExit(label, options) {\n    var css = TreeGridTick_isObject(options.style) ? options.style : {};\n    label.removeClass('highcharts-treegrid-node-active');\n    if (!label.renderer.styledMode) {\n        label.css({ textDecoration: (css.textDecoration || 'none') });\n    }\n}\n/**\n * @private\n */\nfunction renderLabelIcon(tick, params) {\n    var _a;\n    var treeGrid = tick.treeGrid,\n        isNew = !treeGrid.labelIcon,\n        renderer = params.renderer,\n        labelBox = params.xy,\n        options = params.options,\n        width = options.width || 0,\n        height = options.height || 0,\n        padding = ((_a = options.padding) !== null && _a !== void 0 ? _a : tick.axis.linkedParent) ? 0 : 5,\n        iconCenter = {\n            x: labelBox.x - (width / 2) - padding,\n            y: labelBox.y - (height / 2)\n        },\n        rotation = params.collapsed ? 90 : 180,\n        shouldRender = params.show && TreeGridTick_isNumber(iconCenter.y);\n    var icon = treeGrid.labelIcon;\n    if (!icon) {\n        treeGrid.labelIcon = icon = renderer\n            .path(renderer.symbols[options.type](options.x || 0, options.y || 0, width, height))\n            .addClass('highcharts-label-icon')\n            .add(params.group);\n    }\n    // Set the new position, and show or hide\n    icon[shouldRender ? 'show' : 'hide'](); // #14904, #1338\n    // Presentational attributes\n    if (!renderer.styledMode) {\n        icon\n            .attr({\n            cursor: 'pointer',\n            'fill': TreeGridTick_pick(params.color, \"#666666\" /* Palette.neutralColor60 */),\n            'stroke-width': 1,\n            stroke: options.lineColor,\n            strokeWidth: options.lineWidth || 0\n        });\n    }\n    // Update the icon positions\n    icon[isNew ? 'attr' : 'animate']({\n        translateX: iconCenter.x,\n        translateY: iconCenter.y,\n        rotation: rotation\n    });\n}\n/**\n * @private\n */\nfunction wrapGetLabelPosition(proceed, x, y, label, horiz, labelOptions, tickmarkOffset, index, step) {\n    var _a;\n    var tick = this,\n        lbOptions = TreeGridTick_pick((_a = tick.options) === null || _a === void 0 ? void 0 : _a.labels,\n        labelOptions),\n        pos = tick.pos,\n        axis = tick.axis,\n        isTreeGrid = axis.type === 'treegrid',\n        result = proceed.apply(tick,\n        [x,\n        y,\n        label,\n        horiz,\n        lbOptions,\n        tickmarkOffset,\n        index,\n        step]);\n    var mapOfPosToGridNode,\n        node,\n        level;\n    if (isTreeGrid) {\n        var _b = (lbOptions && TreeGridTick_isObject(lbOptions.symbol,\n            true) ?\n                lbOptions.symbol :\n                {}),\n            _c = _b.width,\n            width = _c === void 0 ? 0 : _c,\n            _d = _b.padding,\n            padding = _d === void 0 ? axis.linkedParent ? 0 : 5 : _d,\n            indentation = (lbOptions && TreeGridTick_isNumber(lbOptions.indentation) ?\n                lbOptions.indentation :\n                0);\n        mapOfPosToGridNode = axis.treeGrid.mapOfPosToGridNode;\n        node = mapOfPosToGridNode === null || mapOfPosToGridNode === void 0 ? void 0 : mapOfPosToGridNode[pos];\n        level = (node === null || node === void 0 ? void 0 : node.depth) || 1;\n        result.x += (\n        // Add space for symbols\n        (width + (padding * 2)) +\n            // Apply indentation\n            ((level - 1) * indentation));\n    }\n    return result;\n}\n/**\n * @private\n */\nfunction wrapRenderLabel(proceed) {\n    var tick = this, pos = tick.pos, axis = tick.axis, label = tick.label, tickGrid = tick.treeGrid, tickOptions = tick.options, icon = tickGrid === null || tickGrid === void 0 ? void 0 : tickGrid.labelIcon, labelElement = label === null || label === void 0 ? void 0 : label.element, axisGrid = axis.treeGrid, axisOptions = axis.options, chart = axis.chart, tickPositions = axis.tickPositions, mapOfPosToGridNode = axisGrid.mapOfPosToGridNode, labelOptions = TreeGridTick_pick(tickOptions === null || tickOptions === void 0 ? void 0 : tickOptions.labels, axisOptions === null || axisOptions === void 0 ? void 0 : axisOptions.labels), symbolOptions = (labelOptions && TreeGridTick_isObject(labelOptions.symbol, true) ?\n            labelOptions.symbol :\n            {}), node = mapOfPosToGridNode === null || mapOfPosToGridNode === void 0 ? void 0 : mapOfPosToGridNode[pos], _a = node || {}, descendants = _a.descendants, depth = _a.depth, hasDescendants = node && descendants && descendants > 0, level = depth, isTreeGridElement = (axis.type === 'treegrid') && labelElement, shouldRender = tickPositions.indexOf(pos) > -1, prefixClassName = 'highcharts-treegrid-node-', prefixLevelClass = prefixClassName + 'level-', styledMode = chart.styledMode;\n    var collapsed,\n        addClassName,\n        removeClassName;\n    if (isTreeGridElement && node) {\n        // Add class name for hierarchical styling.\n        label\n            .removeClass(new RegExp(prefixLevelClass + '.*'))\n            .addClass(prefixLevelClass + level);\n    }\n    proceed.apply(tick, Array.prototype.slice.call(arguments, 1));\n    if (isTreeGridElement && hasDescendants) {\n        collapsed = axisGrid.isCollapsed(node);\n        renderLabelIcon(tick, {\n            color: (!styledMode &&\n                label.styles.color ||\n                ''),\n            collapsed: collapsed,\n            group: label.parentGroup,\n            options: symbolOptions,\n            renderer: label.renderer,\n            show: shouldRender,\n            xy: label.xy\n        });\n        // Add class name for the node.\n        addClassName = prefixClassName +\n            (collapsed ? 'collapsed' : 'expanded');\n        removeClassName = prefixClassName +\n            (collapsed ? 'expanded' : 'collapsed');\n        label\n            .addClass(addClassName)\n            .removeClass(removeClassName);\n        if (!styledMode) {\n            label.css({\n                cursor: 'pointer'\n            });\n        }\n        // Add events to both label text and icon\n        [label, icon].forEach(function (object) {\n            if (object && !object.attachedTreeGridEvents) {\n                // On hover\n                TreeGridTick_addEvent(object.element, 'mouseover', function () {\n                    onTickHover(label);\n                });\n                // On hover out\n                TreeGridTick_addEvent(object.element, 'mouseout', function () {\n                    onTickHoverExit(label, labelOptions);\n                });\n                TreeGridTick_addEvent(object.element, 'click', function () {\n                    tickGrid.toggleCollapse();\n                });\n                object.attachedTreeGridEvents = true;\n            }\n        });\n    }\n    else if (icon) {\n        removeEvent(labelElement);\n        label === null || label === void 0 ? void 0 : label.css({ cursor: 'default' });\n        icon.destroy();\n    }\n}\n/* *\n *\n *  Classes\n *\n * */\n/**\n * @private\n * @class\n */\nvar TreeGridTickAdditions = /** @class */ (function () {\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    /**\n     * @private\n     */\n    function TreeGridTickAdditions(tick) {\n        this.tick = tick;\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    TreeGridTickAdditions.compose = function (TickClass) {\n        var tickProto = TickClass.prototype;\n        if (!tickProto.toggleCollapse) {\n            TreeGridTick_addEvent(TickClass, 'init', onTickInit);\n            TreeGridTick_wrap(tickProto, 'getLabelPosition', wrapGetLabelPosition);\n            TreeGridTick_wrap(tickProto, 'renderLabel', wrapRenderLabel);\n            // Backwards compatibility\n            tickProto.collapse = function (redraw) {\n                this.treeGrid.collapse(redraw);\n            };\n            tickProto.expand = function (redraw) {\n                this.treeGrid.expand(redraw);\n            };\n            tickProto.toggleCollapse = function (redraw) {\n                this.treeGrid.toggleCollapse(redraw);\n            };\n        }\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Collapse the grid cell. Used when axis is of type treegrid.\n     *\n     * @see gantt/treegrid-axis/collapsed-dynamically/demo.js\n     *\n     * @private\n     * @function Highcharts.Tick#collapse\n     *\n     * @param {boolean} [redraw=true]\n     * Whether to redraw the chart or wait for an explicit call to\n     * {@link Highcharts.Chart#redraw}\n     */\n    TreeGridTickAdditions.prototype.collapse = function (redraw) {\n        var tick = this.tick,\n            axis = tick.axis,\n            brokenAxis = axis.brokenAxis;\n        if (brokenAxis &&\n            axis.treeGrid.mapOfPosToGridNode) {\n            var pos = tick.pos,\n                node = axis.treeGrid.mapOfPosToGridNode[pos],\n                breaks = axis.treeGrid.collapse(node);\n            brokenAxis.setBreaks(breaks, TreeGridTick_pick(redraw, true));\n        }\n    };\n    /**\n     * Destroy remaining labelIcon if exist.\n     *\n     * @private\n     * @function Highcharts.Tick#destroy\n     */\n    TreeGridTickAdditions.prototype.destroy = function () {\n        if (this.labelIcon) {\n            this.labelIcon.destroy();\n        }\n    };\n    /**\n     * Expand the grid cell. Used when axis is of type treegrid.\n     *\n     * @see gantt/treegrid-axis/collapsed-dynamically/demo.js\n     *\n     * @private\n     * @function Highcharts.Tick#expand\n     *\n     * @param {boolean} [redraw=true]\n     * Whether to redraw the chart or wait for an explicit call to\n     * {@link Highcharts.Chart#redraw}\n     */\n    TreeGridTickAdditions.prototype.expand = function (redraw) {\n        var _a = this.tick,\n            pos = _a.pos,\n            axis = _a.axis,\n            treeGrid = axis.treeGrid,\n            brokenAxis = axis.brokenAxis,\n            posMappedNodes = treeGrid.mapOfPosToGridNode;\n        if (brokenAxis && posMappedNodes) {\n            var node = posMappedNodes[pos],\n                breaks = treeGrid.expand(node);\n            brokenAxis.setBreaks(breaks, TreeGridTick_pick(redraw, true));\n        }\n    };\n    /**\n     * Toggle the collapse/expand state of the grid cell. Used when axis is\n     * of type treegrid.\n     *\n     * @see gantt/treegrid-axis/collapsed-dynamically/demo.js\n     *\n     * @private\n     * @function Highcharts.Tick#toggleCollapse\n     *\n     * @param {boolean} [redraw=true]\n     * Whether to redraw the chart or wait for an explicit call to\n     * {@link Highcharts.Chart#redraw}\n     */\n    TreeGridTickAdditions.prototype.toggleCollapse = function (redraw) {\n        var tick = this.tick,\n            axis = tick.axis,\n            brokenAxis = axis.brokenAxis;\n        if (brokenAxis &&\n            axis.treeGrid.mapOfPosToGridNode) {\n            var pos = tick.pos,\n                node = axis.treeGrid.mapOfPosToGridNode[pos],\n                breaks = axis.treeGrid.toggleCollapse(node);\n            brokenAxis.setBreaks(breaks, TreeGridTick_pick(redraw, true));\n        }\n    };\n    return TreeGridTickAdditions;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var TreeGridTick = (TreeGridTickAdditions);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Color\"],\"commonjs\":[\"highcharts\",\"Color\"],\"commonjs2\":[\"highcharts\",\"Color\"],\"root\":[\"Highcharts\",\"Color\"]}\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_ = __webpack_require__(620);\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default = /*#__PURE__*/__webpack_require__.n(highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_);\n;// ./code/es5/es-modules/Series/TreeUtilities.js\n/* *\n *\n *  (c) 2014-2025 Highsoft AS\n *\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nvar TreeUtilities_extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, TreeUtilities_isArray = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isArray, TreeUtilities_isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, TreeUtilities_isObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isObject, TreeUtilities_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, TreeUtilities_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, relativeLength = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).relativeLength;\n/* *\n *\n *  Functions\n *\n * */\n/* eslint-disable valid-jsdoc */\n/**\n * @private\n */\nfunction getColor(node, options) {\n    var index = options.index,\n        mapOptionsToLevel = options.mapOptionsToLevel,\n        parentColor = options.parentColor,\n        parentColorIndex = options.parentColorIndex,\n        series = options.series,\n        colors = options.colors,\n        siblings = options.siblings,\n        points = series.points,\n        chartOptionsChart = series.chart.options.chart;\n    var getColorByPoint,\n        point,\n        level,\n        colorByPoint,\n        colorIndexByPoint,\n        color,\n        colorIndex;\n    /**\n     * @private\n     */\n    var variateColor = function (color) {\n            var colorVariation = level && level.colorVariation;\n        if (colorVariation &&\n            colorVariation.key === 'brightness' &&\n            index &&\n            siblings) {\n            return highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default().parse(color).brighten(colorVariation.to * (index / siblings)).get();\n        }\n        return color;\n    };\n    if (node) {\n        point = points[node.i];\n        level = mapOptionsToLevel[node.level] || {};\n        getColorByPoint = point && level.colorByPoint;\n        if (getColorByPoint) {\n            colorIndexByPoint = point.index % (colors ?\n                colors.length :\n                chartOptionsChart.colorCount);\n            colorByPoint = colors && colors[colorIndexByPoint];\n        }\n        // Select either point color, level color or inherited color.\n        if (!series.chart.styledMode) {\n            color = TreeUtilities_pick(point && point.options.color, level && level.color, colorByPoint, parentColor && variateColor(parentColor), series.color);\n        }\n        colorIndex = TreeUtilities_pick(point && point.options.colorIndex, level && level.colorIndex, colorIndexByPoint, parentColorIndex, options.colorIndex);\n    }\n    return {\n        color: color,\n        colorIndex: colorIndex\n    };\n}\n/**\n * Creates a map from level number to its given options.\n *\n * @private\n *\n * @param {Object} params\n * Object containing parameters.\n * - `defaults` Object containing default options. The default options are\n *   merged with the userOptions to get the final options for a specific\n *   level.\n * - `from` The lowest level number.\n * - `levels` User options from series.levels.\n * - `to` The highest level number.\n *\n * @return {Highcharts.Dictionary<object>|null}\n * Returns a map from level number to its given options.\n */\nfunction getLevelOptions(params) {\n    var result = {};\n    var defaults,\n        converted,\n        i,\n        from,\n        to,\n        levels;\n    if (TreeUtilities_isObject(params)) {\n        from = TreeUtilities_isNumber(params.from) ? params.from : 1;\n        levels = params.levels;\n        converted = {};\n        defaults = TreeUtilities_isObject(params.defaults) ? params.defaults : {};\n        if (TreeUtilities_isArray(levels)) {\n            converted = levels.reduce(function (obj, item) {\n                var level,\n                    levelIsConstant,\n                    options;\n                if (TreeUtilities_isObject(item) && TreeUtilities_isNumber(item.level)) {\n                    options = TreeUtilities_merge({}, item);\n                    levelIsConstant = TreeUtilities_pick(options.levelIsConstant, defaults.levelIsConstant);\n                    // Delete redundant properties.\n                    delete options.levelIsConstant;\n                    delete options.level;\n                    // Calculate which level these options apply to.\n                    level = item.level + (levelIsConstant ? 0 : from - 1);\n                    if (TreeUtilities_isObject(obj[level])) {\n                        TreeUtilities_merge(true, obj[level], options); // #16329\n                    }\n                    else {\n                        obj[level] = options;\n                    }\n                }\n                return obj;\n            }, {});\n        }\n        to = TreeUtilities_isNumber(params.to) ? params.to : 1;\n        for (i = 0; i <= to; i++) {\n            result[i] = TreeUtilities_merge({}, defaults, TreeUtilities_isObject(converted[i]) ? converted[i] : {});\n        }\n    }\n    return result;\n}\n/**\n * @private\n * @todo Combine buildTree and buildNode with setTreeValues\n * @todo Remove logic from Treemap and make it utilize this mixin.\n */\nfunction setTreeValues(tree, options) {\n    var before = options.before,\n        idRoot = options.idRoot,\n        mapIdToNode = options.mapIdToNode,\n        nodeRoot = mapIdToNode[idRoot],\n        levelIsConstant = (options.levelIsConstant !== false),\n        points = options.points,\n        point = points[tree.i],\n        optionsPoint = point && point.options || {},\n        children = [];\n    var childrenTotal = 0;\n    tree.levelDynamic = tree.level - (levelIsConstant ? 0 : nodeRoot.level);\n    tree.name = TreeUtilities_pick(point && point.name, '');\n    tree.visible = (idRoot === tree.id ||\n        options.visible === true);\n    if (typeof before === 'function') {\n        tree = before(tree, options);\n    }\n    // First give the children some values\n    tree.children.forEach(function (child, i) {\n        var newOptions = TreeUtilities_extend({},\n            options);\n        TreeUtilities_extend(newOptions, {\n            index: i,\n            siblings: tree.children.length,\n            visible: tree.visible\n        });\n        child = setTreeValues(child, newOptions);\n        children.push(child);\n        if (child.visible) {\n            childrenTotal += child.val;\n        }\n    });\n    // Set the values\n    var value = TreeUtilities_pick(optionsPoint.value,\n        childrenTotal);\n    tree.visible = value >= 0 && (childrenTotal > 0 || tree.visible);\n    tree.children = children;\n    tree.childrenTotal = childrenTotal;\n    tree.isLeaf = tree.visible && !childrenTotal;\n    tree.val = value;\n    return tree;\n}\n/**\n * Update the rootId property on the series. Also makes sure that it is\n * accessible to exporting.\n *\n * @private\n *\n * @param {Object} series\n * The series to operate on.\n *\n * @return {string}\n * Returns the resulting rootId after update.\n */\nfunction updateRootId(series) {\n    var rootId,\n        options;\n    if (TreeUtilities_isObject(series)) {\n        // Get the series options.\n        options = TreeUtilities_isObject(series.options) ? series.options : {};\n        // Calculate the rootId.\n        rootId = TreeUtilities_pick(series.rootNode, options.rootId, '');\n        // Set rootId on series.userOptions to pick it up in exporting.\n        if (TreeUtilities_isObject(series.userOptions)) {\n            series.userOptions.rootId = rootId;\n        }\n        // Set rootId on series to pick it up on next update.\n        series.rootNode = rootId;\n    }\n    return rootId;\n}\n/**\n * Get the node width, which relies on the plot width and the nodeDistance\n * option.\n *\n * @private\n */\nfunction getNodeWidth(series, columnCount) {\n    var chart = series.chart,\n        options = series.options,\n        _a = options.nodeDistance,\n        nodeDistance = _a === void 0 ? 0 : _a,\n        _b = options.nodeWidth,\n        nodeWidth = _b === void 0 ? 0 : _b,\n        _c = chart.plotSizeX,\n        plotSizeX = _c === void 0 ? 1 : _c;\n    // Node width auto means they are evenly distributed along the width of\n    // the plot area\n    if (nodeWidth === 'auto') {\n        if (typeof nodeDistance === 'string' && /%$/.test(nodeDistance)) {\n            var fraction = parseFloat(nodeDistance) / 100,\n                total = columnCount + fraction * (columnCount - 1);\n            return plotSizeX / total;\n        }\n        var nDistance = Number(nodeDistance);\n        return ((plotSizeX + nDistance) /\n            (columnCount || 1)) - nDistance;\n    }\n    return relativeLength(nodeWidth, plotSizeX);\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar TreeUtilities = {\n    getColor: getColor,\n    getLevelOptions: getLevelOptions,\n    getNodeWidth: getNodeWidth,\n    setTreeValues: setTreeValues,\n    updateRootId: updateRootId\n};\n/* harmony default export */ var Series_TreeUtilities = (TreeUtilities);\n\n;// ./code/es5/es-modules/Core/Axis/TreeGrid/TreeGridAxis.js\n/* *\n *\n *  (c) 2016 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\n\n\nvar TreeGridAxis_getLevelOptions = Series_TreeUtilities.getLevelOptions;\n\nvar TreeGridAxis_addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, TreeGridAxis_isArray = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isArray, splat = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).splat, TreeGridAxis_find = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).find, TreeGridAxis_fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, TreeGridAxis_isObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isObject, isString = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isString, TreeGridAxis_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, TreeGridAxis_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick, TreeGridAxis_removeEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).removeEvent, TreeGridAxis_wrap = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).wrap;\n/* *\n *\n *  Variables\n *\n * */\nvar TickConstructor;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction getBreakFromNode(node, max) {\n    var to = node.collapseEnd || 0;\n    var from = node.collapseStart || 0;\n    // In broken-axis, the axis.max is minimized until it is not within a\n    // break. Therefore, if break.to is larger than axis.max, the axis.to\n    // should not add the 0.5 axis.tickMarkOffset, to avoid adding a break\n    // larger than axis.max.\n    // TODO consider simplifying broken-axis and this might solve itself\n    if (to >= max) {\n        from -= 0.5;\n    }\n    return {\n        from: from,\n        to: to,\n        showPoints: false\n    };\n}\n/**\n * Creates a tree structure of the data, and the treegrid. Calculates\n * categories, and y-values of points based on the tree.\n *\n * @private\n * @function getTreeGridFromData\n *\n * @param {Array<Highcharts.GanttPointOptions>} data\n * All the data points to display in the axis.\n *\n * @param {boolean} uniqueNames\n * Whether or not the data node with the same name should share grid cell. If\n * true they do share cell. False by default.\n *\n * @param {number} numberOfSeries\n *\n * @return {Object}\n * Returns an object containing categories, mapOfIdToNode,\n * mapOfPosToGridNode, and tree.\n *\n * @todo There should be only one point per line.\n * @todo It should be optional to have one category per point, or merge\n *       cells\n * @todo Add unit-tests.\n */\nfunction getTreeGridFromData(data, uniqueNames, numberOfSeries) {\n    var categories = [],\n        collapsedNodes = [],\n        mapOfIdToNode = {},\n        uniqueNamesEnabled = uniqueNames || false;\n    var mapOfPosToGridNode = {},\n        posIterator = -1;\n    // Build the tree from the series data.\n    var treeParams = {\n            // After the children has been created.\n            after: function (node) {\n                var gridNode = mapOfPosToGridNode[node.pos];\n            var height = 0,\n                descendants = 0;\n            gridNode.children.forEach(function (child) {\n                descendants += (child.descendants || 0) + 1;\n                height = Math.max((child.height || 0) + 1, height);\n            });\n            gridNode.descendants = descendants;\n            gridNode.height = height;\n            if (gridNode.collapsed) {\n                collapsedNodes.push(gridNode);\n            }\n        },\n        // Before the children has been created.\n        before: function (node) {\n            var data = TreeGridAxis_isObject(node.data,\n                true) ?\n                    node.data :\n                    {},\n                name = isString(data.name) ? data.name : '',\n                parentNode = mapOfIdToNode[node.parent],\n                parentGridNode = (TreeGridAxis_isObject(parentNode,\n                true) ?\n                    mapOfPosToGridNode[parentNode.pos] :\n                    null),\n                hasSameName = function (x) {\n                    return x.name === name;\n            };\n            var gridNode,\n                pos;\n            // If not unique names, look for sibling node with the same name\n            if (uniqueNamesEnabled &&\n                TreeGridAxis_isObject(parentGridNode, true) &&\n                !!(gridNode = TreeGridAxis_find(parentGridNode.children, hasSameName))) {\n                // If there is a gridNode with the same name, reuse position\n                pos = gridNode.pos;\n                // Add data node to list of nodes in the grid node.\n                gridNode.nodes.push(node);\n            }\n            else {\n                // If it is a new grid node, increment position.\n                pos = posIterator++;\n            }\n            // Add new grid node to map.\n            if (!mapOfPosToGridNode[pos]) {\n                mapOfPosToGridNode[pos] = gridNode = {\n                    depth: parentGridNode ? parentGridNode.depth + 1 : 0,\n                    name: name,\n                    id: data.id,\n                    nodes: [node],\n                    children: [],\n                    pos: pos\n                };\n                // If not root, then add name to categories.\n                if (pos !== -1) {\n                    categories.push(name);\n                }\n                // Add name to list of children.\n                if (TreeGridAxis_isObject(parentGridNode, true)) {\n                    parentGridNode.children.push(gridNode);\n                }\n            }\n            // Add data node to map\n            if (isString(node.id)) {\n                mapOfIdToNode[node.id] = node;\n            }\n            // If one of the points are collapsed, then start the grid node\n            // in collapsed state.\n            if (gridNode &&\n                data.collapsed === true) {\n                gridNode.collapsed = true;\n            }\n            // Assign pos to data node\n            node.pos = pos;\n        }\n    };\n    var updateYValuesAndTickPos = function (map,\n        numberOfSeries) {\n            var setValues = function (gridNode,\n        start,\n        result) {\n                var nodes = gridNode.nodes,\n        padding = 0.5;\n            var end = start + (start === -1 ? 0 : numberOfSeries - 1);\n            var diff = (end - start) / 2,\n                pos = start + diff;\n            nodes.forEach(function (node) {\n                var data = node.data;\n                if (TreeGridAxis_isObject(data, true)) {\n                    // Update point\n                    data.y = start + (data.seriesIndex || 0);\n                    // Remove the property once used\n                    delete data.seriesIndex;\n                }\n                node.pos = pos;\n            });\n            result[pos] = gridNode;\n            gridNode.pos = pos;\n            gridNode.tickmarkOffset = diff + padding;\n            gridNode.collapseStart = end + padding;\n            gridNode.children.forEach(function (child) {\n                setValues(child, end + 1, result);\n                end = (child.collapseEnd || 0) - padding;\n            });\n            // Set collapseEnd to the end of the last child node.\n            gridNode.collapseEnd = end + padding;\n            return result;\n        };\n        return setValues(map['-1'], -1, {});\n    };\n    // Create tree from data\n    var tree = Gantt_Tree.getTree(data,\n        treeParams);\n    // Update y values of data, and set calculate tick positions.\n    mapOfPosToGridNode = updateYValuesAndTickPos(mapOfPosToGridNode, numberOfSeries);\n    // Return the resulting data.\n    return {\n        categories: categories,\n        mapOfIdToNode: mapOfIdToNode,\n        mapOfPosToGridNode: mapOfPosToGridNode,\n        collapsedNodes: collapsedNodes,\n        tree: tree\n    };\n}\n/**\n * Builds the tree of categories and calculates its positions.\n * @private\n * @param {Object} e Event object\n * @param {Object} e.target The chart instance which the event was fired on.\n * @param {object[]} e.target.axes The axes of the chart.\n */\nfunction onBeforeRender(e) {\n    var chart = e.target,\n        axes = chart.axes;\n    axes.filter(function (axis) { return axis.type === 'treegrid'; }).forEach(function (axis) {\n        var _a;\n        var options = axis.options || {},\n            labelOptions = options.labels,\n            uniqueNames = axis.uniqueNames,\n            max = chart.time.parse(options.max), \n            // Check whether any of series is rendering for the first\n            // time, visibility has changed, or its data is dirty, and\n            // only then update. #10570, #10580. Also check if\n            // mapOfPosToGridNode exists. #10887\n            isDirty = (!axis.treeGrid.mapOfPosToGridNode ||\n                axis.series.some(function (series) {\n                    return !series.hasRendered ||\n                        series.isDirtyData ||\n                        series.isDirty;\n            }));\n        var numberOfSeries = 0,\n            data,\n            treeGrid;\n        if (isDirty) {\n            var seriesHasPrimitivePoints_1 = [];\n            // Concatenate data from all series assigned to this axis.\n            data = axis.series.reduce(function (arr, s) {\n                var seriesData = (s.options.data || []),\n                    firstPoint = seriesData[0], \n                    // Check if the first point is a simple array of values.\n                    // If so we assume that this is the case for all points.\n                    foundPrimitivePoint = (Array.isArray(firstPoint) &&\n                        !firstPoint.find(function (value) { return (typeof value === 'object'); }));\n                seriesHasPrimitivePoints_1.push(foundPrimitivePoint);\n                if (s.visible) {\n                    // Push all data to array\n                    seriesData.forEach(function (pointOptions) {\n                        var _a;\n                        // For using keys, or when using primitive points,\n                        // rebuild the data structure\n                        if (foundPrimitivePoint || ((_a = s.options.keys) === null || _a === void 0 ? void 0 : _a.length)) {\n                            pointOptions = s.pointClass.prototype\n                                .optionsToObject\n                                .call({ series: s }, pointOptions);\n                            s.pointClass.setGanttPointAliases(pointOptions, chart);\n                        }\n                        if (TreeGridAxis_isObject(pointOptions, true)) {\n                            // Set series index on data. Removed again\n                            // after use.\n                            pointOptions.seriesIndex = (numberOfSeries);\n                            arr.push(pointOptions);\n                        }\n                    });\n                    // Increment series index\n                    if (uniqueNames === true) {\n                        numberOfSeries++;\n                    }\n                }\n                return arr;\n            }, []);\n            // If max is higher than set data - add a\n            // dummy data to render categories #10779\n            if (max && data.length < max) {\n                for (var i = data.length; i <= max; i++) {\n                    data.push({\n                        // Use the zero-width character\n                        // to avoid conflict with uniqueNames\n                        name: i + '\\u200B'\n                    });\n                }\n            }\n            // `setScale` is fired after all the series is initialized,\n            // which is an ideal time to update the axis.categories.\n            treeGrid = getTreeGridFromData(data, uniqueNames || false, (uniqueNames === true) ? numberOfSeries : 1);\n            // Assign values to the axis.\n            axis.categories = treeGrid.categories;\n            axis.treeGrid.mapOfPosToGridNode = (treeGrid.mapOfPosToGridNode);\n            axis.hasNames = true;\n            axis.treeGrid.tree = treeGrid.tree;\n            // Update yData now that we have calculated the y values\n            axis.series.forEach(function (series, index) {\n                var axisData = (series.options.data || []).map(function (d) {\n                        if (seriesHasPrimitivePoints_1[index] ||\n                            (TreeGridAxis_isArray(d) &&\n                                series.options.keys &&\n                                series.options.keys.length)) {\n                            // Get the axisData from the data array used to\n                            // build the treeGrid where has been modified\n                            data.forEach(function (point) {\n                                var toArray = splat(d);\n                            if (toArray.indexOf(point.x || 0) >= 0 &&\n                                toArray.indexOf(point.x2 || 0) >= 0) {\n                                d = point;\n                            }\n                        });\n                    }\n                    return TreeGridAxis_isObject(d, true) ? TreeGridAxis_merge(d) : d;\n                });\n                // Avoid destroying points when series is not visible\n                if (series.visible) {\n                    series.setData(axisData, false);\n                }\n            });\n            // Calculate the label options for each level in the tree.\n            axis.treeGrid.mapOptionsToLevel =\n                TreeGridAxis_getLevelOptions({\n                    defaults: labelOptions,\n                    from: 1,\n                    levels: labelOptions === null || labelOptions === void 0 ? void 0 : labelOptions.levels,\n                    to: (_a = axis.treeGrid.tree) === null || _a === void 0 ? void 0 : _a.height\n                });\n            // Setting initial collapsed nodes\n            if (e.type === 'beforeRender') {\n                axis.treeGrid.collapsedNodes = treeGrid.collapsedNodes;\n            }\n        }\n    });\n}\n/**\n * Generates a tick for initial positioning.\n *\n * @private\n * @function Highcharts.GridAxis#generateTick\n *\n * @param {Function} proceed\n * The original generateTick function.\n *\n * @param {number} pos\n * The tick position in axis values.\n */\nfunction wrapGenerateTick(proceed, pos) {\n    var axis = this,\n        mapOptionsToLevel = axis.treeGrid.mapOptionsToLevel || {},\n        isTreeGrid = axis.type === 'treegrid',\n        ticks = axis.ticks;\n    var tick = ticks[pos],\n        levelOptions,\n        options,\n        gridNode;\n    if (isTreeGrid &&\n        axis.treeGrid.mapOfPosToGridNode) {\n        gridNode = axis.treeGrid.mapOfPosToGridNode[pos];\n        levelOptions = mapOptionsToLevel[gridNode.depth];\n        if (levelOptions) {\n            options = {\n                labels: levelOptions\n            };\n        }\n        if (!tick &&\n            TickConstructor) {\n            ticks[pos] = tick =\n                new TickConstructor(axis, pos, void 0, void 0, {\n                    category: gridNode.name,\n                    tickmarkOffset: gridNode.tickmarkOffset,\n                    options: options\n                });\n        }\n        else {\n            // Update labels depending on tick interval\n            tick.parameters.category = gridNode.name;\n            tick.options = options;\n            tick.addLabel();\n        }\n    }\n    else {\n        proceed.apply(axis, Array.prototype.slice.call(arguments, 1));\n    }\n}\n/**\n * @private\n */\nfunction wrapInit(proceed, chart, userOptions, coll) {\n    var axis = this,\n        isTreeGrid = userOptions.type === 'treegrid';\n    if (!axis.treeGrid) {\n        axis.treeGrid = new TreeGridAxisAdditions(axis);\n    }\n    // Set default and forced options for TreeGrid\n    if (isTreeGrid) {\n        // Add event for updating the categories of a treegrid.\n        // NOTE Preferably these events should be set on the axis.\n        TreeGridAxis_addEvent(chart, 'beforeRender', onBeforeRender);\n        TreeGridAxis_addEvent(chart, 'beforeRedraw', onBeforeRender);\n        // Add new collapsed nodes on addseries\n        TreeGridAxis_addEvent(chart, 'addSeries', function (e) {\n            if (e.options.data) {\n                var treeGrid = getTreeGridFromData(e.options.data,\n                    userOptions.uniqueNames || false, 1);\n                axis.treeGrid.collapsedNodes = (axis.treeGrid.collapsedNodes || []).concat(treeGrid.collapsedNodes);\n            }\n        });\n        // Collapse all nodes in axis.treegrid.collapsednodes\n        // where collapsed equals true.\n        TreeGridAxis_addEvent(axis, 'foundExtremes', function () {\n            if (axis.treeGrid.collapsedNodes) {\n                axis.treeGrid.collapsedNodes.forEach(function (node) {\n                    var breaks = axis.treeGrid.collapse(node);\n                    if (axis.brokenAxis) {\n                        axis.brokenAxis.setBreaks(breaks, false);\n                        // Remove the node from the axis collapsedNodes\n                        if (axis.treeGrid.collapsedNodes) {\n                            axis.treeGrid.collapsedNodes = axis.treeGrid\n                                .collapsedNodes\n                                .filter(function (n) { return ((node.collapseStart !==\n                                n.collapseStart) ||\n                                node.collapseEnd !== n.collapseEnd); });\n                        }\n                    }\n                });\n            }\n        });\n        // If staticScale is not defined on the yAxis\n        // and chart height is set, set axis.isDirty\n        // to ensure collapsing works (#12012)\n        TreeGridAxis_addEvent(axis, 'afterBreaks', function () {\n            if (axis.coll === 'yAxis' &&\n                !axis.staticScale &&\n                axis.chart.options.chart.height) {\n                axis.isDirty = true;\n            }\n        });\n        userOptions = TreeGridAxis_merge({\n            // Default options\n            grid: {\n                enabled: true\n            },\n            // TODO: add support for align in treegrid.\n            labels: {\n                align: 'left',\n                /**\n                * Set options on specific levels in a tree grid axis. Takes\n                * precedence over labels options.\n                *\n                * @sample {gantt} gantt/treegrid-axis/labels-levels\n                *         Levels on TreeGrid Labels\n                *\n                * @type      {Array<*>}\n                * @product   gantt\n                * @apioption yAxis.labels.levels\n                *\n                * @private\n                */\n                levels: [{\n                        /**\n                        * Specify the level which the options within this object\n                        * applies to.\n                        *\n                        * @type      {number}\n                        * @product   gantt\n                        * @apioption yAxis.labels.levels.level\n                        *\n                        * @private\n                        */\n                        level: void 0\n                    }, {\n                        level: 1,\n                        /**\n                         * @type      {Highcharts.CSSObject}\n                         * @product   gantt\n                         * @apioption yAxis.labels.levels.style\n                         *\n                         * @private\n                         */\n                        style: {\n                            /** @ignore-option */\n                            fontWeight: 'bold'\n                        }\n                    }],\n                /**\n                 * The symbol for the collapse and expand icon in a\n                 * treegrid.\n                 *\n                 * @product      gantt\n                 * @optionparent yAxis.labels.symbol\n                 *\n                 * @private\n                 */\n                symbol: {\n                    /**\n                     * The symbol type. Points to a definition function in\n                     * the `Highcharts.Renderer.symbols` collection.\n                     *\n                     * @type {Highcharts.SymbolKeyValue}\n                     *\n                     * @private\n                     */\n                    type: 'triangle',\n                    x: -5,\n                    y: -5,\n                    height: 10,\n                    width: 10\n                }\n            },\n            uniqueNames: false\n        }, userOptions, {\n            // Forced options\n            reversed: true\n        });\n    }\n    // Now apply the original function with the original arguments, which are\n    // sliced off this function's arguments\n    proceed.apply(axis, [chart, userOptions, coll]);\n    if (isTreeGrid) {\n        axis.hasNames = true;\n        axis.options.showLastLabel = true;\n    }\n}\n/**\n * Set the tick positions, tickInterval, axis min and max.\n *\n * @private\n * @function Highcharts.GridAxis#setTickInterval\n *\n * @param {Function} proceed\n * The original setTickInterval function.\n */\nfunction wrapSetTickInterval(proceed) {\n    var _a,\n        _b,\n        _c,\n        _d,\n        _e;\n    var axis = this,\n        options = axis.options,\n        time = axis.chart.time,\n        linkedParent = typeof options.linkedTo === 'number' ?\n            (_a = this.chart[axis.coll]) === null || _a === void 0 ? void 0 : _a[options.linkedTo] :\n            void 0,\n        isTreeGrid = axis.type === 'treegrid';\n    if (isTreeGrid) {\n        axis.min = (_c = (_b = axis.userMin) !== null && _b !== void 0 ? _b : time.parse(options.min)) !== null && _c !== void 0 ? _c : axis.dataMin;\n        axis.max = (_e = (_d = axis.userMax) !== null && _d !== void 0 ? _d : time.parse(options.max)) !== null && _e !== void 0 ? _e : axis.dataMax;\n        TreeGridAxis_fireEvent(axis, 'foundExtremes');\n        // `setAxisTranslation` modifies the min and max according to axis\n        // breaks.\n        axis.setAxisTranslation();\n        axis.tickInterval = 1;\n        axis.tickmarkOffset = 0.5;\n        axis.tickPositions = axis.treeGrid.mapOfPosToGridNode ?\n            axis.treeGrid.getTickPositions() :\n            [];\n        if (linkedParent) {\n            var linkedParentExtremes = linkedParent.getExtremes();\n            axis.min = TreeGridAxis_pick(linkedParentExtremes.min, linkedParentExtremes.dataMin);\n            axis.max = TreeGridAxis_pick(linkedParentExtremes.max, linkedParentExtremes.dataMax);\n            axis.tickPositions = linkedParent.tickPositions;\n        }\n        axis.linkedParent = linkedParent;\n    }\n    else {\n        proceed.apply(axis, Array.prototype.slice.call(arguments, 1));\n    }\n}\n/**\n * Wrap axis redraw to remove TreeGrid events from ticks\n *\n * @private\n * @function Highcharts.GridAxis#redraw\n *\n * @param {Function} proceed\n * The original setTickInterval function.\n */\nfunction wrapRedraw(proceed) {\n    var axis = this,\n        isTreeGrid = this.type === 'treegrid';\n    if (isTreeGrid && axis.visible) {\n        axis.tickPositions.forEach(function (pos) {\n            var _a;\n            var tick = axis.ticks[pos];\n            if ((_a = tick.label) === null || _a === void 0 ? void 0 : _a.attachedTreeGridEvents) {\n                TreeGridAxis_removeEvent(tick.label.element);\n                tick.label.attachedTreeGridEvents = false;\n            }\n        });\n    }\n    proceed.apply(axis, Array.prototype.slice.call(arguments, 1));\n}\n/* *\n *\n *  Classes\n *\n * */\n/**\n * @private\n * @class\n */\nvar TreeGridAxisAdditions = /** @class */ (function () {\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    /**\n     * @private\n     */\n    function TreeGridAxisAdditions(axis) {\n        this.axis = axis;\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * @private\n     */\n    TreeGridAxisAdditions.compose = function (AxisClass, ChartClass, SeriesClass, TickClass) {\n        if (!AxisClass.keepProps.includes('treeGrid')) {\n            var axisProps = AxisClass.prototype;\n            AxisClass.keepProps.push('treeGrid');\n            TreeGridAxis_wrap(axisProps, 'generateTick', wrapGenerateTick);\n            TreeGridAxis_wrap(axisProps, 'init', wrapInit);\n            TreeGridAxis_wrap(axisProps, 'setTickInterval', wrapSetTickInterval);\n            TreeGridAxis_wrap(axisProps, 'redraw', wrapRedraw);\n            // Make utility functions available for testing.\n            axisProps.utils = {\n                getNode: Gantt_Tree.getNode\n            };\n            if (!TickConstructor) {\n                TickConstructor = TickClass;\n            }\n        }\n        Axis_GridAxis.compose(AxisClass, ChartClass, TickClass);\n        Axis_BrokenAxis.compose(AxisClass, SeriesClass);\n        TreeGridTick.compose(TickClass);\n        return AxisClass;\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Set the collapse status.\n     *\n     * @private\n     *\n     * @param {Highcharts.Axis} axis\n     * The axis to check against.\n     *\n     * @param {Highcharts.GridNode} node\n     * The node to collapse.\n     */\n    TreeGridAxisAdditions.prototype.setCollapsedStatus = function (node) {\n        var axis = this.axis,\n            chart = axis.chart;\n        axis.series.forEach(function (series) {\n            var data = series.options.data;\n            if (node.id && data) {\n                var point = chart.get(node.id),\n                    dataPoint = data[series.data.indexOf(point)];\n                if (point && dataPoint) {\n                    point.collapsed = node.collapsed;\n                    dataPoint.collapsed = node.collapsed;\n                }\n            }\n        });\n    };\n    /**\n     * Calculates the new axis breaks to collapse a node.\n     *\n     * @private\n     *\n     * @param {Highcharts.Axis} axis\n     * The axis to check against.\n     *\n     * @param {Highcharts.GridNode} node\n     * The node to collapse.\n     *\n     * @param {number} pos\n     * The tick position to collapse.\n     *\n     * @return {Array<object>}\n     * Returns an array of the new breaks for the axis.\n     */\n    TreeGridAxisAdditions.prototype.collapse = function (node) {\n        var axis = this.axis,\n            breaks = (axis.options.breaks || []),\n            obj = getBreakFromNode(node,\n            axis.max);\n        breaks.push(obj);\n        // Change the collapsed flag #13838\n        node.collapsed = true;\n        axis.treeGrid.setCollapsedStatus(node);\n        return breaks;\n    };\n    /**\n     * Calculates the new axis breaks to expand a node.\n     *\n     * @private\n     *\n     * @param {Highcharts.Axis} axis\n     * The axis to check against.\n     *\n     * @param {Highcharts.GridNode} node\n     * The node to expand.\n     *\n     * @param {number} pos\n     * The tick position to expand.\n     *\n     * @return {Array<object>}\n     * Returns an array of the new breaks for the axis.\n     */\n    TreeGridAxisAdditions.prototype.expand = function (node) {\n        var axis = this.axis,\n            breaks = (axis.options.breaks || []),\n            obj = getBreakFromNode(node,\n            axis.max);\n        // Change the collapsed flag #13838\n        node.collapsed = false;\n        axis.treeGrid.setCollapsedStatus(node);\n        // Remove the break from the axis breaks array.\n        return breaks.reduce(function (arr, b) {\n            if (b.to !== obj.to || b.from !== obj.from) {\n                arr.push(b);\n            }\n            return arr;\n        }, []);\n    };\n    /**\n     * Creates a list of positions for the ticks on the axis. Filters out\n     * positions that are outside min and max, or is inside an axis break.\n     *\n     * @private\n     *\n     * @return {Array<number>}\n     * List of positions.\n     */\n    TreeGridAxisAdditions.prototype.getTickPositions = function () {\n        var axis = this.axis, roundedMin = Math.floor(axis.min / axis.tickInterval) * axis.tickInterval, roundedMax = Math.ceil(axis.max / axis.tickInterval) * axis.tickInterval;\n        return Object.keys(axis.treeGrid.mapOfPosToGridNode || {}).reduce(function (arr, key) {\n            var _a;\n            var pos = +key;\n            if (pos >= roundedMin &&\n                pos <= roundedMax &&\n                !((_a = axis.brokenAxis) === null || _a === void 0 ? void 0 : _a.isInAnyBreak(pos))) {\n                arr.push(pos);\n            }\n            return arr;\n        }, []);\n    };\n    /**\n     * Check if a node is collapsed.\n     *\n     * @private\n     *\n     * @param {Highcharts.Axis} axis\n     * The axis to check against.\n     *\n     * @param {Object} node\n     * The node to check if is collapsed.\n     *\n     * @param {number} pos\n     * The tick position to collapse.\n     *\n     * @return {boolean}\n     * Returns true if collapsed, false if expanded.\n     */\n    TreeGridAxisAdditions.prototype.isCollapsed = function (node) {\n        var axis = this.axis,\n            breaks = (axis.options.breaks || []),\n            obj = getBreakFromNode(node,\n            axis.max);\n        return breaks.some(function (b) {\n            return b.from === obj.from && b.to === obj.to;\n        });\n    };\n    /**\n     * Calculates the new axis breaks after toggling the collapse/expand\n     * state of a node. If it is collapsed it will be expanded, and if it is\n     * expanded it will be collapsed.\n     *\n     * @private\n     *\n     * @param {Highcharts.Axis} axis\n     * The axis to check against.\n     *\n     * @param {Highcharts.GridNode} node\n     * The node to toggle.\n     *\n     * @return {Array<object>}\n     * Returns an array of the new breaks for the axis.\n     */\n    TreeGridAxisAdditions.prototype.toggleCollapse = function (node) {\n        return (this.isCollapsed(node) ?\n            this.expand(node) :\n            this.collapse(node));\n    };\n    return TreeGridAxisAdditions;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var TreeGridAxis = (TreeGridAxisAdditions);\n\n;// ./code/es5/es-modules/masters/modules/treegrid.js\n\n\n\n\nvar G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nTreeGridAxis.compose(G.Axis, G.Chart, G.Series, G.Tick);\n/* harmony default export */ var treegrid_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__184__", "__WEBPACK_EXTERNAL_MODULE__532__", "__WEBPACK_EXTERNAL_MODULE__620__", "GridAxisSide", "BrokenAxis", "TickConstructor", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "treegrid_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_", "highcharts_StackItem_commonjs_highcharts_StackItem_commonjs2_highcharts_StackItem_root_Highcharts_StackItem_default", "addEvent", "find", "fireEvent", "isArray", "isNumber", "pick", "onAxisAfterInit", "broken<PERSON><PERSON>s", "setBreaks", "options", "breaks", "onAxisAfterSetOptions", "_a", "axis", "hasBreaks", "ordinal", "onAxisAfterSetTickPositions", "tickPositions", "info", "newPositions", "i", "length", "isInAnyBreak", "push", "onAxisInit", "Additions", "onSeriesAfterGeneratePoints", "_b", "isDirty", "_c", "connectNulls", "points", "xAxis", "yAxis", "point", "isPointInBreak", "y", "x", "visible", "onSeriesAfterRender", "drawBreaks", "pointArrayMap", "seriesDrawBreaks", "keys", "threshold", "series", "brokenAxis_1", "for<PERSON>ach", "breakArray", "isXAxis", "min", "breaksOutOfRange", "filter", "brk", "isOut", "otherBreak", "from", "to", "toUpperCase", "eventName", "seriesGappedPath", "currentDataGrouping", "groupingSize", "gapSize", "slice", "gapUnit", "basePointRange", "current", "next", "xRange", "splice", "isNull", "stacking", "stack", "stacks", "<PERSON><PERSON><PERSON>", "stackLabels", "total", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "compose", "AxisClass", "SeriesClass", "keepProps", "includes", "seriesProto", "gappedPath", "isInBreak", "val", "ret", "repeat", "Infinity", "test", "inclusive", "lin2Val", "nval", "len", "val2Lin", "findBreakAt", "b", "testKeep", "inbrk", "keep", "showPoints", "redraw", "time", "chart", "parse", "userOptions", "forceRedraw", "val2lin", "lin2val", "setExtremes", "newMin", "newMax", "animation", "eventArguments", "breaks_1", "axisBreak", "constructor", "setAxisTranslation", "unitLength", "inBrk_1", "repeat_1", "start_1", "i_1", "breaks_2", "breakArrayT_1", "breakArray_1", "pointRangePadding", "length_1", "min_1", "userMin", "max_1", "userMax", "max", "value", "move", "size", "breakSize", "sort", "staticScale", "transA", "minPixelPadding", "minPointOffset", "Axis_BrokenAxis", "highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_", "highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default", "dateFormats", "GridAxis_addEvent", "defined", "erase", "GridAxis_find", "GridAxis_isArray", "GridAxis_isNumber", "merge", "GridAxis_pick", "timeUnits", "wrap", "isObject", "getMaxLabelDimensions", "ticks", "dimensions", "width", "height", "pos", "label", "tick", "labelHeight", "labelWidth", "getBBox", "textStr", "textPxLength", "Math", "round", "type", "treeGrid", "mapOfPosToGridNode", "<PERSON><PERSON><PERSON><PERSON>", "labels", "indentation", "wrapGetOffset", "proceed", "grid", "columnsFirst", "side", "apply", "isColumn", "columns", "reverse", "column", "getOffset", "onAfterGetTitlePosition", "e", "gridOptions", "enabled", "axisTitle", "axisHeight", "horiz", "axisLeft", "left", "offset", "opposite", "options_1", "axisTop", "top", "axisWidth", "tickSize", "titleWidth", "xOption", "title", "yOption", "<PERSON><PERSON><PERSON><PERSON>", "margin", "titleFontSize", "renderer", "fontMetrics", "f", "offAxis", "bottom", "titlePosition", "onAfterInit", "align", "categories", "showLastLabel", "labelRotation", "rotation", "minTickInterval", "columnIndex", "columnOptions", "isInternal", "linkedTo", "scrollbar", "axes", "coll", "onAfterRender", "firstTick", "styledMode", "slotWidth", "style", "css", "maxLabelDimensions", "rightWall", "destroy", "isOuterAxis", "axisLine", "lineWidth", "linePath", "get<PERSON>inePath", "startPoint", "endPoint", "distance", "tick<PERSON><PERSON>th", "marginRight", "upperBorderEndPoint", "upperBorderPath", "lowerBorderEndPoint", "chartWidth", "toPixels", "tickmarkOffset", "lowerBorderPath", "upperBorder", "renderBorder", "attr", "stroke", "lineColor", "animate", "lowerBorder", "axisLineExtra", "showAxis", "render", "hasRendered", "linkedParent", "lastTick", "firstTick_1", "tickMark", "hiddenLabels", "pop", "element", "show", "hiddenMarks", "hide", "mark", "isLast", "onAfterSetAxisTranslation", "tickInfo", "userLabels", "pointRange", "dateTimeLabelFormats", "unitName", "range", "count", "tickInterval", "onAfterSetOptions", "gridAxisOptions", "className", "hour", "list", "day", "week", "month", "borderWidth", "padding", "fontSize", "text", "reserveSpace", "textOverflow", "units", "tickPixelInterval", "tickPositioner", "parentInfo", "unitIdx", "unit_1", "unit", "counts", "unitRange", "getTimeTicks", "startOfWeek", "minPadding", "maxPadding", "tickWidth", "onAfterSetOptions2", "onAfterSetScale", "setScale", "onAfterTickSize", "labelPadding", "cellHeight", "onChartAfterSetChartSize", "setAxisSize", "onDestroy", "keepEvents", "onInit", "borderColor", "tickColor", "GridAxisAdditions", "onTickAfterGetLabelPosition", "reversed", "labelOpts", "tickPos", "nextTickPos", "index", "crispCorr", "top_1", "right", "chartHeight", "translate", "lblMetrics", "useHTML", "lines", "h", "onTickLabelFormat", "ctx", "<PERSON><PERSON><PERSON><PERSON>", "data", "p", "pointCopy", "is", "seriesTypes", "gantt", "pointClass", "setGanttPointAliases", "onTrimTicks", "categoryAxis", "firstPos", "secondPos", "lastPos", "beforeLastPos", "linkedMin", "linkedMax", "startLessThanMin", "endMoreThanMin", "startLessThanMax", "endMoreThanMax", "isLinked", "startOnTick", "endOnTick", "wrapUnsquish", "args", "arguments", "Array", "parentAxis", "thisIndex", "lastIndex", "inverted", "otherAxis", "path", "extraBorderLine", "addClass", "add", "axisGroup", "zIndex", "E", "timestamp", "dateFormat", "char<PERSON>t", "W", "toParts", "firstDay", "thursday", "firstThursday", "makeTime", "floor", "thursdayTS", "toString", "__rest", "s", "t", "indexOf", "getOwnPropertySymbols", "propertyIsEnumerable", "extend", "Tree_isNumber", "Tree_pick", "getNode", "id", "parent", "level", "mapOfIdToChildren", "start", "end", "after", "before", "node", "depth", "descendants", "children", "map", "child", "childStart", "NaN", "childEnd", "milestone", "Gantt_Tree", "getTree", "ids", "listOfParents", "reduce", "prev", "curr", "adoptedByRoot", "orphan", "TreeGridTick_addEvent", "removeEvent", "TreeGridTick_isObject", "TreeGridTick_isNumber", "TreeGridTick_pick", "TreeGridTick_wrap", "onTickInit", "TreeGridTickAdditions", "wrapGetLabelPosition", "labelOptions", "step", "lbOptions", "isTreeGrid", "result", "symbol", "_d", "wrapRenderLabel", "params", "isNew", "labelBox", "iconCenter", "shouldRender", "icon", "collapsed", "tick<PERSON><PERSON>", "tickOptions", "labelIcon", "labelElement", "axisGrid", "axisOptions", "symbolOptions", "hasDescendants", "isTreeGridElement", "prefixClassName", "prefixLevelClass", "removeClass", "RegExp", "isCollapsed", "color", "styles", "group", "parentGroup", "xy", "symbols", "cursor", "strokeWidth", "translateX", "translateY", "object", "attachedTreeGridEvents", "textDecoration", "toggleCollapse", "TickClass", "tick<PERSON>roto", "collapse", "expand", "posMappedNodes", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_", "TreeUtilities_isArray", "TreeUtilities_isNumber", "TreeUtilities_isObject", "TreeUtilities_merge", "TreeUtilities_pick", "TreeGridAxis_getLevelOptions", "<PERSON><PERSON><PERSON><PERSON>", "defaults", "converted", "levels", "item", "levelIsConstant", "TreeGridAxis_addEvent", "TreeGridAxis_isArray", "splat", "TreeGridAxis_find", "TreeGridAxis_fireEvent", "TreeGridAxis_isObject", "isString", "TreeGridAxis_merge", "TreeGridAxis_pick", "TreeGridAxis_removeEvent", "TreeGridAxis_wrap", "getBreakFromNode", "collapseEnd", "collapseStart", "getTreeGridFromData", "uniqueNames", "numberOfSeries", "set<PERSON><PERSON><PERSON>", "collapsedNodes", "mapOfIdToNode", "uniqueNamesEnabled", "posIterator", "tree", "gridNode", "name", "parentNode", "parentGridNode", "nodes", "diff", "seriesIndex", "onBeforeRender", "target", "some", "isDirtyData", "seriesHasPrimitivePoints_1", "arr", "seriesData", "firstPoint", "foundPrimitivePoint", "pointOptions", "optionsToObject", "hasNames", "axisData", "toArray", "x2", "setData", "mapOptionsToLevel", "wrapGenerateTick", "levelOptions", "category", "parameters", "addLabel", "wrapInit", "TreeGridAxisAdditions", "concat", "fontWeight", "wrapSetTickInterval", "_e", "dataMin", "dataMax", "getTickPositions", "linkedParentExtremes", "getExtremes", "wrapRedraw", "ChartClass", "axisProps", "utils", "TreeGridTick", "setCollapsedStatus", "dataPoint", "roundedMin", "roundedMax", "ceil", "G", "TreeGridAxis", "Axis", "Chart", "Series", "Tick"], "mappings": "CAWA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,SAAY,CAAEA,QAAQ,cAAc,IAAO,CAAEA,QAAQ,cAAc,KAAQ,EAC1I,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,8BAA+B,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,YAAY,CAAE,CAAC,wBAAwB,OAAO,CAAE,CAAC,wBAAwB,QAAQ,CAAC,CAAEJ,GACxK,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,8BAA8B,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,SAAY,CAAEA,QAAQ,cAAc,IAAO,CAAEA,QAAQ,cAAc,KAAQ,EAEzKJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,SAAY,CAAEA,EAAK,UAAa,CAAC,IAAO,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAC3I,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,EACxJ,OAAgB,AAAC,WACP,aACA,IAsxBCC,EA3pBPC,EA0pBAD,EA84DAE,EAnqFUC,EAAuB,CAE/B,IACC,SAASX,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,SAASN,CAAM,EAEtBA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,SAASP,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGIQ,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAahB,OAAO,CAG5B,IAAIC,EAASY,CAAwB,CAACE,EAAS,CAAG,CAGjDf,QAAS,CAAC,CACX,EAMA,OAHAY,CAAmB,CAACG,EAAS,CAACd,EAAQA,EAAOD,OAAO,CAAEc,GAG/Cb,EAAOD,OAAO,AACtB,CAMCc,EAAoBI,CAAC,CAAG,SAASjB,CAAM,EACtC,IAAIkB,EAASlB,GAAUA,EAAOmB,UAAU,CACvC,WAAa,OAAOnB,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAa,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASrB,CAAO,CAAEuB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACzB,EAASwB,IAC5EE,OAAOC,cAAc,CAAC3B,EAASwB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,EAAc,CAC7D,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAA+GzB,EAAoB,KACnI0B,EAAmI1B,EAAoBI,CAAC,CAACqB,GAczJE,EAAW,AAACH,IAA+EG,QAAQ,CAAEC,EAAO,AAACJ,IAA+EI,IAAI,CAAEC,EAAY,AAACL,IAA+EK,SAAS,CAAEC,EAAU,AAACN,IAA+EM,OAAO,CAAEC,EAAW,AAACP,IAA+EO,QAAQ,CAAEC,EAAO,AAACR,IAA+EQ,IAAI,EAWhlB,AAAC,SAAUpC,CAAU,EAkCjB,SAASqC,IAC0B,KAAA,IAApB,IAAI,CAACC,UAAU,EACtB,IAAI,CAACA,UAAU,CAACC,SAAS,CAAC,IAAI,CAACC,OAAO,CAACC,MAAM,CAAE,CAAA,EAEvD,CAKA,SAASC,IAEL,IADIC,EAEA,CAAA,AAA2B,OAA1BA,CAAAA,EAAKC,AADC,IAAI,CACAN,UAAU,AAAD,GAAeK,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGE,SAAS,AAAD,GACvED,CAAAA,AAFO,IAAI,CAENJ,OAAO,CAACM,OAAO,CAAG,CAAA,CAAI,CAEnC,CAIA,SAASC,IACL,IACIT,EAAaM,AADN,IAAI,CACON,UAAU,CAChC,GAAIA,MAAAA,EAA+C,KAAK,EAAIA,EAAWO,SAAS,CAAE,CAI9E,IAAK,IAHDG,EAAgBJ,AAHb,IAAI,CAGcI,aAAa,CAClCC,EAAOL,AAJJ,IAAI,CAIKI,aAAa,CAACC,IAAI,CAC9BC,EAAe,EAAE,CACZC,EAAI,EAAGA,EAAIH,EAAcI,MAAM,CAAED,IACjCb,EAAWe,YAAY,CAACL,CAAa,CAACG,EAAE,GACzCD,EAAaI,IAAI,CAACN,CAAa,CAACG,EAAE,CAG1CP,CAXO,IAAI,CAWNI,aAAa,CAAGE,EACrBN,AAZO,IAAI,CAYNI,aAAa,CAACC,IAAI,CAAGA,CAC9B,CACJ,CAIA,SAASM,IAEAX,AADM,IAAI,CACLN,UAAU,EAChBM,CAAAA,AAFO,IAAI,CAENN,UAAU,CAAG,IAAIkB,EAFf,IAAI,CAEyB,CAE5C,CAIA,SAASC,IAGL,IAFId,EACAe,EAEAC,EAAUC,AADL,IAAI,CACID,OAAO,CACpBE,EAAeD,AAFV,IAAI,CAESpB,OAAO,CAACqB,YAAY,CACtCC,EAASF,AAHJ,IAAI,CAGGE,MAAM,CAClBC,EAAQH,AAJH,IAAI,CAIEG,KAAK,CAChBC,EAAQJ,AALH,IAAI,CAKEI,KAAK,CAGpB,GAAIL,EAEA,IADA,IAAIR,EAAIW,EAAOV,MAAM,CACdD,KAAK,CACR,IAAIc,EAAQH,CAAM,CAACX,EAAE,CAGjBe,EAAkB,AADRD,CAAAA,AAAY,OAAZA,EAAME,CAAC,EAAaN,AAAiB,CAAA,IAAjBA,CAAqB,GACpB,CAAA,AAAC,CAAA,AAA0E,OAAzElB,CAAAA,EAAKoB,MAAAA,EAAqC,KAAK,EAAIA,EAAMzB,UAAU,AAAD,GAAeK,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGU,YAAY,CAACY,EAAMG,CAAC,CAClK,CAAA,EAAI,GACC,CAAA,AAA0E,OAAzEV,CAAAA,EAAKM,MAAAA,EAAqC,KAAK,EAAIA,EAAM1B,UAAU,AAAD,GAAeoB,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGL,YAAY,CAACY,EAAME,CAAC,CACvI,CAAA,EAAI,CAAC,CAGTF,CAAAA,EAAMI,OAAO,CAAGH,CAAAA,GAEZD,AAA0B,CAAA,IAA1BA,EAAMzB,OAAO,CAAC6B,OAAO,AAC7B,CAER,CAIA,SAASC,IACL,IAAI,CAACC,UAAU,CAAC,IAAI,CAACR,KAAK,CAAE,CAAC,IAAI,EACjC,IAAI,CAACQ,UAAU,CAAC,IAAI,CAACP,KAAK,CAAE5B,EAAK,IAAI,CAACoC,aAAa,CAAE,CAAC,IAAI,EAC9D,CAIA,SAASC,EAAiB7B,CAAI,CAAE8B,CAAI,EAEhC,IADI/B,EAGAF,EACAkC,EACAR,EAJAS,EAAS,IAAI,CACbd,EAASc,EAAOd,MAAM,CAI1B,GAAI,AAAuE,OAAtEnB,CAAAA,EAAKC,MAAAA,EAAmC,KAAK,EAAIA,EAAKN,UAAU,AAAD,GAAeK,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGE,SAAS,CAAE,CACtH,IAAIgC,EAAejC,EAAKN,UAAU,CAClCoC,EAAKI,OAAO,CAAC,SAAUhE,CAAG,EAGtB2B,EAAS,AAACoC,CAAAA,MAAAA,EAAmD,KAAK,EAAIA,EAAaE,UAAU,AAAD,GAAM,EAAE,CACpGJ,EAAY/B,EAAKoC,OAAO,CACpBpC,EAAKqC,GAAG,CACR7C,EAAKwC,EAAOpC,OAAO,CAACmC,SAAS,CAAE/B,EAAKqC,GAAG,EAI3C,IATItC,EACAe,EAQAwB,EAAmB,AAA0H,OAAzHxB,CAAAA,EAAK,AAAoE,OAAnEf,CAAAA,EAAKC,MAAAA,EAAmC,KAAK,EAAIA,EAAKJ,OAAO,AAAD,GAAeG,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGF,MAAM,AAAD,GAAeiB,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGyB,MAAM,CAAC,SAAUC,CAAG,EAGrM,IAAK,IAFGC,EAAQ,CAAA,EAEPlC,EAAI,EAAGA,EAAIV,EAAOW,MAAM,CAAED,IAAK,CACpC,IAAImC,EAAa7C,CAAM,CAACU,EAAE,CAC1B,GAAImC,EAAWC,IAAI,GAAKH,EAAIG,IAAI,EAC5BD,EAAWE,EAAE,GAAKJ,EAAII,EAAE,CAAE,CAC1BH,EAAQ,CAAA,EACR,KACJ,CACJ,CACA,OAAOA,CACX,GACAvB,EAAOgB,OAAO,CAAC,SAAUb,CAAK,EAC1BE,EAAI/B,EAAK6B,CAAK,CAAC,QAAUnD,EAAI2E,WAAW,GAAG,CAAExB,CAAK,CAACnD,EAAI,EACvD2B,EAAOqC,OAAO,CAAC,SAAUM,CAAG,EACxB,GAAIjD,EAASwC,IAAcxC,EAASgC,GAAI,CACpC,IAAIuB,EAAY,EACZ,CAACf,EAAYS,EAAIG,IAAI,EAAIpB,EAAIiB,EAAII,EAAE,EAClCb,EAAYS,EAAIG,IAAI,EAAIpB,EAAIiB,EAAIG,IAAI,CACrCG,EAAY,aAEP,CAAA,AAACf,EAAYS,EAAIG,IAAI,EAC1BpB,EAAIiB,EAAIG,IAAI,EACZpB,EAAIiB,EAAII,EAAE,EAAMb,EAAYS,EAAIG,IAAI,EACpCpB,EAAIiB,EAAII,EAAE,EACVrB,EAAIiB,EAAIG,IAAI,GACZG,CAAAA,EAAY,cAAa,EAEzBA,GACAzD,EAAUW,EAAM8C,EAAW,CAAEzB,MAAOA,EAAOmB,IAAKA,CAAI,EAE5D,CACJ,GACAF,MAAAA,GAAoEA,EAAiBJ,OAAO,CAAC,SAAUM,CAAG,EACtGnD,EAAUW,EAAM,sBAAuB,CAAEqB,MAAOA,EAAOmB,IAAKA,CAAI,EACpE,EACJ,EACJ,EACJ,CACJ,CAYA,SAASO,IACL,IAAIC,EAAsB,IAAI,CAACA,mBAAmB,CAC9CC,EAAeD,MAAAA,EAAiE,KAAK,EAAIA,EAAoBE,OAAO,CACpHhC,EAAS,IAAI,CAACA,MAAM,CAACiC,KAAK,GAC1B/B,EAAQ,IAAI,CAACA,KAAK,CAClB8B,EAAU,IAAI,CAACtD,OAAO,CAACsD,OAAO,CAC9B3C,EAAIW,EAAOV,MAAM,CAAG,EAwDxB,GAAI0C,GAAW3C,EAAI,EAAG,CAEW,UAAzB,IAAI,CAACX,OAAO,CAACwD,OAAO,EACpBF,CAAAA,GAAW,IAAI,CAACG,cAAc,AAAD,EAI7BJ,GACAA,EAAeC,GAGfD,GAAgB,IAAI,CAACI,cAAc,EACnCH,CAAAA,EAAUD,CAAW,EAKzB,IAFA,IAAIK,EAAU,KAAK,EACfC,EAAO,KAAK,EACThD,KAOH,GALMgD,GAAQA,AAAiB,CAAA,IAAjBA,EAAK9B,OAAO,EACtB8B,CAAAA,EAAOrC,CAAM,CAACX,EAAI,EAAE,AAAD,EAEvB+C,EAAUpC,CAAM,CAACX,EAAE,CAEfgD,AAAiB,CAAA,IAAjBA,EAAK9B,OAAO,EAAc6B,AAAoB,CAAA,IAApBA,EAAQ7B,OAAO,EAG7C,GAAI8B,EAAK/B,CAAC,CAAG8B,EAAQ9B,CAAC,CAAG0B,EAAS,CAC9B,IAAIM,EAAS,AAACF,CAAAA,EAAQ9B,CAAC,CAAG+B,EAAK/B,CAAC,AAADA,EAAK,EACpCN,EAAOuC,MAAM,CACblD,EAAI,EAAG,EAAG,CACNmD,OAAQ,CAAA,EACRlC,EAAGgC,CACP,GAEIpC,EAAMuC,QAAQ,EAAI,IAAI,CAAC/D,OAAO,CAAC+D,QAAQ,EAEvCC,CAAAA,AADQxC,CAAAA,EAAMuC,QAAQ,CAACE,MAAM,CAAC,IAAI,CAACC,QAAQ,CAAC,CAACN,EAAO,CAAG,GAAKtE,CAAAA,GAAoH,EAAGkC,EAAOA,EAAMxB,OAAO,CAACmE,WAAW,CAAE,CAAA,EAAOP,EAAQ,IAAI,CAACI,KAAK,CAAA,EACxOI,KAAK,CAAG,CAAA,CAEtB,CAEAT,EAAOD,EAEf,CAEA,OAAO,IAAI,CAACW,YAAY,CAAC/C,EAC7B,CA7QA9D,EAAW8G,OAAO,CAflB,SAAiBC,CAAS,CAAEC,CAAW,EACnC,GAAI,CAACD,EAAUE,SAAS,CAACC,QAAQ,CAAC,cAAe,CAC7CH,EAAUE,SAAS,CAAC3D,IAAI,CAAC,cACzBvB,EAASgF,EAAW,OAAQxD,GAC5BxB,EAASgF,EAAW,YAAa1E,GACjCN,EAASgF,EAAW,wBAAyBhE,GAC7ChB,EAASgF,EAAW,kBAAmBrE,GACvC,IAAIyE,EAAcH,EAAY1F,SAAS,AACvC6F,CAAAA,EAAY5C,UAAU,CAAGE,EACzB0C,EAAYC,UAAU,CAAGzB,EACzB5D,EAASiF,EAAa,sBAAuBvD,GAC7C1B,EAASiF,EAAa,cAAe1C,EACzC,CACA,OAAOyC,CACX,EAyRA,IAAIvD,EAA2B,WAMvB,SAASA,EAAUZ,CAAI,EACnB,IAAI,CAACC,SAAS,CAAG,CAAA,EACrB,IAAI,CAACD,IAAI,CAAGA,CAChB,CAkTA,OAzSAY,EAAU6D,SAAS,CAAG,SAAUjC,CAAG,CAAEkC,CAAG,EACpC,IAMIC,EANAC,EAASpC,EAAIoC,MAAM,EAAIC,IACvBlC,EAAOH,EAAIG,IAAI,CACfnC,EAASgC,EAAII,EAAE,CAAGJ,EAAIG,IAAI,CAC1BmC,EAAQJ,GAAO/B,EACX,AAAC+B,CAAAA,EAAM/B,CAAG,EAAKiC,EACfA,EAAU,AAACjC,CAAAA,EAAO+B,CAAE,EAAKE,EAQjC,OANKpC,EAAIuC,SAAS,CAIRD,GAAQtE,EAHRsE,EAAOtE,GAAUsE,AAAS,IAATA,CAM/B,EAIAlE,EAAUoE,OAAO,CAAG,SAAUN,CAAG,EAE7B,IAAIhF,EAAaM,AADN,IAAI,CACON,UAAU,CAC5ByC,EAAazC,MAAAA,EAA+C,KAAK,EAAIA,EAAWyC,UAAU,CAC9F,GAAI,CAACA,GAAc,CAAC5C,EAASmF,GACzB,OAAOA,EAEX,IACIlC,EACAjC,EAFA0E,EAAOP,EAGX,IAAKnE,EAAI,EAEL,AAFQA,EAAI4B,EAAW3B,MAAM,GAEzBgC,CAAAA,AADJA,CAAAA,EAAML,CAAU,CAAC5B,EAAE,AAAD,EACVoC,IAAI,EAAIsC,CAAG,EAFY1E,IAKtBiC,EAAII,EAAE,CAAGqC,EACdA,GAAQzC,EAAI0C,GAAG,CAEVtE,EAAU6D,SAAS,CAACjC,EAAKyC,IAC9BA,CAAAA,GAAQzC,EAAI0C,GAAG,AAAD,EAGtB,OAAOD,CACX,EAIArE,EAAUuE,OAAO,CAAG,SAAUT,CAAG,EAE7B,IAAIhF,EAAaM,AADN,IAAI,CACON,UAAU,CAC5ByC,EAAazC,MAAAA,EAA+C,KAAK,EAAIA,EAAWyC,UAAU,CAC9F,GAAI,CAACA,GAAc,CAAC5C,EAASmF,GACzB,OAAOA,EAEX,IACIlC,EACAjC,EAFA0E,EAAOP,EAGX,IAAKnE,EAAI,EAAGA,EAAI4B,EAAW3B,MAAM,CAAED,IAE/B,GAAIiC,AADJA,CAAAA,EAAML,CAAU,CAAC5B,EAAE,AAAD,EACVqC,EAAE,EAAI8B,EACVO,GAAQzC,EAAI0C,GAAG,MAEd,GAAI1C,EAAIG,IAAI,EAAI+B,EACjB,WAEC,GAAI9D,EAAU6D,SAAS,CAACjC,EAAKkC,GAAM,CACpCO,GAASP,EAAMlC,EAAIG,IAAI,CACvB,KACJ,CAEJ,OAAOsC,CACX,EAoBArE,EAAUlC,SAAS,CAAC0G,WAAW,CAAG,SAAU5D,CAAC,CAAE3B,CAAM,EACjD,OAAOT,EAAKS,EAAQ,SAAUwF,CAAC,EAC3B,OAAOA,EAAE1C,IAAI,CAAGnB,GAAKA,EAAI6D,EAAEzC,EAAE,AACjC,EACJ,EAIAhC,EAAUlC,SAAS,CAAC+B,YAAY,CAAG,SAAUiE,CAAG,CAAEY,CAAQ,EACtD,IAIIC,EACAC,EACAb,EALA3E,EAAON,AADM,IAAI,CACCM,IAAI,CACtBH,EAASG,EAAKJ,OAAO,CAACC,MAAM,EAAI,EAAE,CAClCU,EAAIV,EAAOW,MAAM,CAIrB,GAAID,GAAKhB,EAASmF,GAAM,CACpB,KAAOnE,KACCK,EAAU6D,SAAS,CAAC5E,CAAM,CAACU,EAAE,CAAEmE,KAC/Ba,EAAQ,CAAA,EACHC,GACDA,CAAAA,EAAOhG,EAAKK,CAAM,CAACU,EAAE,CAACkF,UAAU,CAAE,CAACzF,EAAKoC,OAAO,CAAA,GAKvDuC,EADAY,GAASD,EACHC,GAAS,CAACC,EAGVD,CAEd,CACA,OAAOZ,CACX,EAcA/D,EAAUlC,SAAS,CAACiB,SAAS,CAAG,SAAUE,CAAM,CAAE6F,CAAM,EACpD,IAAIhG,EAAa,IAAI,CACjBM,EAAON,EAAWM,IAAI,CACtB2F,EAAO3F,EAAK4F,KAAK,CAACD,IAAI,CACtB1F,EAAYX,EAAQO,IAChB,CAAC,CAACA,EAAOW,MAAM,EACf,CAAC,CAACpC,OAAO0D,IAAI,CAACjC,CAAM,CAAC,EAAE,EAAEW,MAAM,AACnCR,CAAAA,EAAKe,OAAO,CAAGrB,EAAWO,SAAS,GAAKA,EAC5CP,EAAWO,SAAS,CAAGA,EAEvBJ,MAAAA,GAAgDA,EAAOqC,OAAO,CAAC,SAAUM,CAAG,EACxEA,EAAIG,IAAI,CAAGgD,EAAKE,KAAK,CAACrD,EAAIG,IAAI,GAAK,EACnCH,EAAII,EAAE,CAAG+C,EAAKE,KAAK,CAACrD,EAAII,EAAE,GAAK,CACnC,GACI/C,IAAWG,EAAKJ,OAAO,CAACC,MAAM,EAC9BG,CAAAA,EAAKJ,OAAO,CAACC,MAAM,CAAGG,EAAK8F,WAAW,CAACjG,MAAM,CAAGA,CAAK,EAEzDG,EAAK+F,WAAW,CAAG,CAAA,EAEnB/F,EAAKgC,MAAM,CAACE,OAAO,CAAC,SAAUF,CAAM,EAChCA,EAAOjB,OAAO,CAAG,CAAA,CACrB,GACKd,GAAaD,EAAKgG,OAAO,GAAKpF,EAAUuE,OAAO,GAEhD,OAAOnF,EAAKgG,OAAO,CACnB,OAAOhG,EAAKiG,OAAO,EAEnBhG,IACAD,EAAK8F,WAAW,CAAC5F,OAAO,CAAG,CAAA,EAC3BF,EAAKiG,OAAO,CAAGrF,EAAUoE,OAAO,CAChChF,EAAKgG,OAAO,CAAGpF,EAAUuE,OAAO,CAChCnF,EAAKkG,WAAW,CAAG,SAAUC,CAAM,CAAEC,CAAM,CAAEV,CAAM,CAAEW,CAAS,CAAEC,CAAc,EAG1E,GAAI5G,EAAWO,SAAS,CAAE,CAGtB,IAFA,IAAIsG,EAAY,IAAI,CAAC3G,OAAO,CAACC,MAAM,EAAI,EAAE,CACrC2G,EAAY,KAAK,EACbA,EAAY9G,EAAW0F,WAAW,CAACe,EAAQI,IAC/CJ,EAASK,EAAU5D,EAAE,CAEzB,KAAQ4D,EAAY9G,EAAW0F,WAAW,CAACgB,EAAQG,IAC/CH,EAASI,EAAU7D,IAAI,CAGvByD,EAASD,GACTC,CAAAA,EAASD,CAAK,CAEtB,CACAnG,EAAKyG,WAAW,CAAC/H,SAAS,CAACwH,WAAW,CAACtH,IAAI,CAAC,IAAI,CAAEuH,EAAQC,EAAQV,EAAQW,EAAWC,EACzF,EACAtG,EAAK0G,kBAAkB,CAAG,WAGtB,GAFA1G,EAAKyG,WAAW,CAAC/H,SAAS,CAACgI,kBAAkB,CAAC9H,IAAI,CAAC,IAAI,EACvDc,EAAWiH,UAAU,CAAG,KAAK,EACzBjH,EAAWO,SAAS,CAAE,CACtB,IAMI2G,EACAC,EAGAC,EACAC,EAXAC,EAAWhH,EAAKJ,OAAO,CAACC,MAAM,EAAI,EAAE,CAEpCoH,EAAgB,EAAE,CAClBC,EAAe,EAAE,CACjBC,EAAoB3H,EAAKQ,EAAKmH,iBAAiB,CAAE,GACjDC,EAAW,EAGXC,EAAQrH,EAAKsH,OAAO,EAAItH,EAAKqC,GAAG,CAChCkF,EAAQvH,EAAKwH,OAAO,EAAIxH,EAAKyH,GAAG,CAIpCT,EAAS9E,OAAO,CAAC,SAAUM,CAAG,EAC1BqE,EAAWrE,EAAIoC,MAAM,EAAIC,IACrBtF,EAAS8H,IAAU9H,EAASgI,KACxB3G,EAAU6D,SAAS,CAACjC,EAAK6E,IACzBA,CAAAA,GAAU,AAAC7E,EAAII,EAAE,CAAGiE,EACfQ,EAAQR,CAAS,EAEtBjG,EAAU6D,SAAS,CAACjC,EAAK+E,IACzBA,CAAAA,GAAU,AAACA,EAAQV,EACdrE,EAAIG,IAAI,CAAGkE,CAAS,EAGrC,GAEAG,EAAS9E,OAAO,CAAC,SAAUM,CAAG,EAG1B,GAFAsE,EAAUtE,EAAIG,IAAI,CAClBkE,EAAWrE,EAAIoC,MAAM,EAAIC,IACrBtF,EAAS8H,IAAU9H,EAASgI,GAAQ,CACpC,KAAOT,EAAUD,EAAWQ,GACxBP,GAAWD,EAEf,KAAOC,EAAUO,GACbP,GAAWD,EAEf,IAAKE,EAAMD,EAASC,EAAMQ,EAAOR,GAAOF,EACpCI,EAAcvG,IAAI,CAAC,CACfgH,MAAOX,EACPY,KAAM,IACV,GACAV,EAAcvG,IAAI,CAAC,CACfgH,MAAOX,EAAMvE,EAAII,EAAE,CAAGJ,EAAIG,IAAI,CAC9BgF,KAAM,MACNC,KAAMpF,EAAIqF,SAAS,AACvB,EAER,CACJ,GACAZ,EAAca,IAAI,CAAC,SAAU9J,CAAC,CAAEqH,CAAC,EAC7B,OAAQ,AAACrH,EAAE0J,KAAK,GAAKrC,EAAEqC,KAAK,CACvB,AAAC1J,CAAAA,CAAAA,AAAW,OAAXA,EAAE2J,IAAI,AAAQ,EACXtC,CAAAA,CAAAA,AAAW,OAAXA,EAAEsC,IAAI,AAAQ,EACnB3J,EAAE0J,KAAK,CAAGrC,EAAEqC,KAAK,AACzB,GAEAd,EAAU,EACVE,EAAUO,EACVJ,EAAc/E,OAAO,CAAC,SAAUM,CAAG,EAEf,IADhBoE,CAAAA,GAAYpE,AAAa,OAAbA,EAAImF,IAAI,CAAY,EAAI,EAAE,GACjBnF,AAAa,OAAbA,EAAImF,IAAI,EACzBb,CAAAA,EAAUtE,EAAIkF,KAAK,AAAD,EAEN,IAAZd,GAAiBrH,EAASuH,KAC1BI,EAAaxG,IAAI,CAAC,CACdiC,KAAMmE,EACNlE,GAAIJ,EAAIkF,KAAK,CACbxC,IAAK1C,EAAIkF,KAAK,CAAGZ,EAAWtE,CAAAA,EAAIoF,IAAI,EAAI,CAAA,CAC5C,GACAR,GAAa5E,EAAIkF,KAAK,CAClBZ,EACCtE,CAAAA,EAAIoF,IAAI,EAAI,CAAA,EAEzB,GACAlI,EAAWyC,UAAU,CAAG+E,EAGpB3H,EAAS8H,IACT9H,EAASgI,IACThI,EAASS,EAAKqC,GAAG,IACjB3C,EAAWiH,UAAU,CAAGY,EAAQF,EAAQD,EACpCD,EACJ9H,EAAUW,EAAM,eACZA,EAAK+H,WAAW,CAChB/H,EAAKgI,MAAM,CAAGhI,EAAK+H,WAAW,CAEzBrI,EAAWiH,UAAU,EAC1B3G,CAAAA,EAAKgI,MAAM,EACP,AAACT,CAAAA,EAAQvH,EAAKqC,GAAG,CAAG8E,CAAgB,EAChCzH,EAAWiH,UAAU,AAAD,EAE5BQ,GACAnH,CAAAA,EAAKiI,eAAe,CAChBjI,EAAKgI,MAAM,CAAIhI,CAAAA,EAAKkI,cAAc,EAAI,CAAA,CAAC,EAE/ClI,EAAKqC,GAAG,CAAGgF,EACXrH,EAAKyH,GAAG,CAAGF,EAEnB,CACJ,GAEA/H,EAAKkG,EAAQ,CAAA,IACb1F,EAAK4F,KAAK,CAACF,MAAM,EAEzB,EACO9E,CACX,GACAxD,CAAAA,EAAWwD,SAAS,CAAGA,CAC3B,EAAGxD,GAAeA,CAAAA,EAAa,CAAC,CAAA,GAMH,IAAI+K,EAAmB/K,EAGhDgL,EAA2F5K,EAAoB,KAC/G6K,EAA+G7K,EAAoBI,CAAC,CAACwK,GAerIE,EAAc,AAACtJ,IAA+EsJ,WAAW,CAEzGC,EAAoB,AAACvJ,IAA+EG,QAAQ,CAAEqJ,EAAU,AAACxJ,IAA+EwJ,OAAO,CAAEC,EAAQ,AAACzJ,IAA+EyJ,KAAK,CAAEC,EAAgB,AAAC1J,IAA+EI,IAAI,CAAEuJ,EAAmB,AAAC3J,IAA+EM,OAAO,CAAEsJ,EAAoB,AAAC5J,IAA+EO,QAAQ,CAAEsJ,EAAQ,AAAC7J,IAA+E6J,KAAK,CAAEC,EAAgB,AAAC9J,IAA+EQ,IAAI,CAAEuJ,EAAY,AAAC/J,IAA+E+J,SAAS,CAAEC,EAAO,AAAChK,IAA+EgK,IAAI,CA+B3/B,SAASC,EAASzH,CAAC,EAEf,OAAOxC,IAA8EiK,QAAQ,CAACzH,EAAG,CAAA,EACrG,CA0EA,SAAS0H,EAAsBC,CAAK,CAAE/I,CAAa,EAC/C,IAAIgJ,EAAa,CACTC,MAAO,EACPC,OAAQ,CACZ,EA4BJ,GA3BAlJ,EAAc8B,OAAO,CAAC,SAAUqH,CAAG,EAC/B,IAGIC,EAHAC,EAAON,CAAK,CAACI,EAAI,CACjBG,EAAc,EACdC,EAAa,EAEbV,EAASQ,KAGTC,EAAcF,AAFdA,CAAAA,EAAQP,EAASQ,EAAKD,KAAK,EAAIC,EAAKD,KAAK,CAAG,CAAC,CAAA,EAEzBI,OAAO,CAAGJ,EAAMI,OAAO,GAAGN,MAAM,CAAG,EACnDE,EAAMK,OAAO,EAAI,CAACjB,EAAkBY,EAAMM,YAAY,GACtDN,CAAAA,EAAMM,YAAY,CAAGN,EAAMI,OAAO,GAAGP,KAAK,AAAD,EAE7CM,EAAaf,EAAkBY,EAAMM,YAAY,EAE7CC,KAAKC,KAAK,CAACR,EAAMM,YAAY,EAC7B,EACAN,EAAMK,OAAO,EAGbF,CAAAA,EAAaI,KAAKC,KAAK,CAACR,EAAMI,OAAO,GAAGP,KAAK,CAAA,EAGjDD,EAAWE,MAAM,CAAGS,KAAKtC,GAAG,CAACiC,EAAaN,EAAWE,MAAM,EAC3DF,EAAWC,KAAK,CAAGU,KAAKtC,GAAG,CAACkC,EAAYP,EAAWC,KAAK,EAEhE,GAEI,AAAc,aAAd,IAAI,CAACY,IAAI,EACT,IAAI,CAACC,QAAQ,EACb,IAAI,CAACA,QAAQ,CAACC,kBAAkB,CAAE,CAClC,IAAIC,EAAY,IAAI,CAACF,QAAQ,CAACC,kBAAkB,CAAC,GAAG,CAACb,MAAM,EAAI,CAC/DF,CAAAA,EAAWC,KAAK,EAAK,IAAI,CAACzJ,OAAO,CAACyK,MAAM,CAACC,WAAW,CAC/CF,CAAAA,EAAY,CAAA,CACrB,CACA,OAAOhB,CACX,CAKA,SAASmB,EAAcC,CAAO,EAC1B,IAAIC,EAAO,IAAI,CAACA,IAAI,CAGhBC,EAAe,AAAc,IAAd,IAAI,CAACC,IAAI,CAI5B,GAHKD,GACDF,EAAQI,KAAK,CAAC,IAAI,EAElB,CAAEH,CAAAA,MAAAA,EAAmC,KAAK,EAAIA,EAAKI,QAAQ,AAAD,EAAI,CAC9D,IAAIC,EAAU,AAACL,CAAAA,MAAAA,EAAmC,KAAK,EAAIA,EAAKK,OAAO,AAAD,GAAM,EAAE,CAC1EJ,GACAI,CAAAA,EAAUA,EAAQ3H,KAAK,GAAG4H,OAAO,EAAC,EAEtCD,EACK5I,OAAO,CAAC,SAAU8I,CAAM,EACzBA,EAAOC,SAAS,EACpB,EACJ,CACIP,GACAF,EAAQI,KAAK,CAAC,IAAI,CAE1B,CAIA,SAASM,EAAwBC,CAAC,EAI9B,GAAIC,AAAwB,CAAA,IAAxBA,AADcxL,CAAAA,AADJI,AADH,IAAI,CACIJ,OAAO,CACA6K,IAAI,EAAI,CAAC,CAAA,EACnBY,OAAO,CAAW,CAE9B,IAAIC,EAAYtL,AALT,IAAI,CAKUsL,SAAS,CAC1BC,EAAavL,AANV,IAAI,CAMWsJ,MAAM,CACxBkC,EAAQxL,AAPL,IAAI,CAOMwL,KAAK,CAClBC,EAAWzL,AARR,IAAI,CAQS0L,IAAI,CACpBC,EAAS3L,AATN,IAAI,CASO2L,MAAM,CACpBC,EAAW5L,AAVR,IAAI,CAUS4L,QAAQ,CACxBC,EAAY7L,AAXT,IAAI,CAWUJ,OAAO,CACxBkM,EAAU9L,AAZP,IAAI,CAYQ+L,GAAG,CAClBC,EAAYhM,AAbT,IAAI,CAaUqJ,KAAK,CACtB4C,EAAWjM,AAdR,IAAI,CAcSiM,QAAQ,GACxBC,EAAaZ,MAAAA,EAA6C,KAAK,EAAIA,EAAU1B,OAAO,GAAGP,KAAK,CAC5F8C,EAAUN,EAAUO,KAAK,CAAC5K,CAAC,CAC3B6K,EAAUR,EAAUO,KAAK,CAAC7K,CAAC,CAC3B+K,EAAcxD,EAAc+C,EAAUO,KAAK,CAACG,MAAM,CAClDf,EAAQ,EAAI,IACZgB,EAAgBlB,EAAYtL,AApBzB,IAAI,CAoB0B4F,KAAK,CAAC6G,QAAQ,CAACC,WAAW,CAACpB,GAAWqB,CAAC,CAAG,EAI3EC,EAAW,AAACpB,CAAAA,EAAQM,EAAUP,EAAaE,CAAO,EAC9C,AAACD,CAAAA,EAAQ,EAAI,EAAC,EACTI,CAAAA,EAAW,GAAK,CAAA,EALbK,CAAAA,EAAWA,CAAQ,CAAC,EAAE,CAAG,EAAI,CAAA,EAOpCjM,CAAAA,AA5BF,IAAI,CA4BG2K,IAAI,GAAKxN,EAAa0P,MAAM,CAAGL,EAAgB,CAAA,CAC7DrB,CAAAA,EAAE2B,aAAa,CAACtL,CAAC,CAAGgK,EAChBC,EAAW,AAACS,CAAAA,GAAc,CAAA,EAAK,EAAII,EAAcH,EACjDS,EAAWhB,CAAAA,EAAWI,EAAY,CAAA,EAAKL,EAASQ,EACpDhB,EAAE2B,aAAa,CAACvL,CAAC,CAAGiK,EACfoB,EACIhB,CAAAA,EAAWL,EAAa,CAAA,EACzB,AAACK,CAAAA,EAAWY,EAAgB,CAACA,CAAY,EAAK,EAC9Cb,EACAU,EACJP,EAAUQ,EAAcD,CAChC,CACJ,CAIA,SAASU,IAEL,IA1LInN,EA0LAgG,EAAQ5F,AADD,IAAI,CACE4F,KAAK,CAClB7F,EAAKC,AAFE,IAAI,CAEDJ,OAAO,CAAC6K,IAAI,CACtBW,EAAcrL,AAAO,KAAK,IAAZA,EAAgB,CAAC,EAAIA,EACnC+F,EAAc9F,AAJP,IAAI,CAIQ8F,WAAW,CAIlC,GAHIsF,EAAYC,OAAO,GAvLvBzL,CAPIA,EAAUI,AAyLH,IAAI,CAzLIJ,OAAO,EAOlByK,MAAM,CAAC2C,KAAK,CAAGlE,EAAclJ,EAAQyK,MAAM,CAAC2C,KAAK,CAAE,UAOtDhN,AA2KM,IAAI,CA3KLiN,UAAU,EAChBrN,CAAAA,EAAQsN,aAAa,CAAG,CAAA,CAAI,EAIhClN,AAsKW,IAAI,CAtKVmN,aAAa,CAAG,EACrBvN,EAAQyK,MAAM,CAAC+C,QAAQ,CAAG,EAE1BxN,EAAQyN,eAAe,CAAG,GA2KtBjC,EAAYN,OAAO,CAInB,IAHA,IAAIA,EAAU9K,AATP,IAAI,CASQyK,IAAI,CAACK,OAAO,CAAG,EAAE,CAChCwC,EAActN,AAVX,IAAI,CAUYyK,IAAI,CAAC6C,WAAW,CAAG,EAEnC,EAAEA,EAAclC,EAAYN,OAAO,CAACtK,MAAM,EAAE,CAC/C,IAAI+M,EAAgB1E,EAAM/C,EACtBsF,EAAYN,OAAO,CAACwC,EAAY,CAAE,CAC9BE,WAAY,CAAA,EACZC,SAAU,EAEVC,UAAW,CACPrC,QAAS,CAAA,CACb,CACJ,EAEA,CACIZ,KAAM,CACFK,QAAS,KAAK,CAClB,CACJ,GACAE,EAAS,GAAK3C,CAAAA,GAAgG,EAAGrI,AA5BlH,IAAI,CA4BmH4F,KAAK,CAC3H2H,EAAe,QACnBvC,CAAAA,EAAOP,IAAI,CAACI,QAAQ,CAAG,CAAA,EACvBG,EAAOP,IAAI,CAAC6C,WAAW,CAAGA,EAG1B7E,EAAM7C,EAAM+H,IAAI,CAAE3C,GAClBvC,EAAM7C,CAAK,CAAC5F,AAnCT,IAAI,CAmCU4N,IAAI,CAAC,EAAI,EAAE,CAAE5C,GAC9BF,EAAQpK,IAAI,CAACsK,EACjB,CAER,CAcA,SAAS6C,IAGL,IAFI9N,EACAe,EAEAwK,EAAYtL,AADL,IAAI,CACMsL,SAAS,CAC1Bb,EAAOzK,AAFA,IAAI,CAECyK,IAAI,CAChB7K,EAAUI,AAHH,IAAI,CAGIJ,OAAO,CAE1B,GAAIwL,AAAwB,CAAA,IAAxBA,AADcxL,CAAAA,EAAQ6K,IAAI,EAAI,CAAC,CAAA,EACnBY,OAAO,CAAW,CAC9B,IAAIhJ,EAAMrC,AANH,IAAI,CAMIqC,GAAG,EAAI,EAClBoF,EAAMzH,AAPH,IAAI,CAOIyH,GAAG,EAAI,EAClBqG,EAAY9N,AART,IAAI,CAQUmJ,KAAK,CAACnJ,AARpB,IAAI,CAQqBI,aAAa,CAAC,EAAE,CAAC,CAsBjD,GApBIkL,GACA,CAACtL,AAXE,IAAI,CAWD4F,KAAK,CAACmI,UAAU,EACrBD,CAAAA,MAAAA,EAA6C,KAAK,EAAIA,EAAUE,SAAS,AAAD,GACzE,CAAChO,AAbE,IAAI,CAaDJ,OAAO,CAACwM,KAAK,CAAC6B,KAAK,CAAC5E,KAAK,EAC/BiC,EAAU4C,GAAG,CAAC,CAAE7E,MAAO,GAAKyE,EAAUE,SAAS,CAAG,IAAK,GAG3DhO,AAjBO,IAAI,CAiBNmO,kBAAkB,CAAGnO,AAjBnB,IAAI,CAiBoBkJ,qBAAqB,CAAClJ,AAjB9C,IAAI,CAiB+CmJ,KAAK,CAAEnJ,AAjB1D,IAAI,CAiB2DI,aAAa,EAE/EJ,AAnBG,IAAI,CAmBFoO,SAAS,EACdpO,AApBG,IAAI,CAoBFoO,SAAS,CAACC,OAAO,GAUtB,AAAC,CAAA,AAAqB,OAApBtO,CAAAA,EAAKC,AA9BJ,IAAI,CA8BKyK,IAAI,AAAD,GAAe1K,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGuO,WAAW,EAAC,GAAMtO,AA9BzE,IAAI,CA8B0EuO,QAAQ,CAAE,CAC3F,IAAIC,EAAY5O,EAAQ4O,SAAS,CACjC,GAAIA,EAAW,CACX,IAAIC,EAAWzO,AAjChB,IAAI,CAiCiB0O,WAAW,CAACF,GAC5BG,EAAaF,CAAQ,CAAC,EAAE,CACxBG,EAAWH,CAAQ,CAAC,EAAE,CAItBI,EAAWC,AADE,AAAC9O,CAAAA,AAtCnB,IAAI,CAsCoBiM,QAAQ,CAAC,SAAW,CAAC,EAAE,AAAD,CAAE,CAAC,EAAE,CACrB,CAAA,AAACjM,AAvC/B,IAAI,CAuCgC2K,IAAI,GAAKxN,EAAa4O,GAAG,EACpD/L,AAxCT,IAAI,CAwCU2K,IAAI,GAAKxN,EAAauO,IAAI,CAAI,GAAK,CAAA,EAchD,GAZsB,MAAlBiD,CAAU,CAAC,EAAE,EAAYC,AAAgB,MAAhBA,CAAQ,CAAC,EAAE,GAChC5O,AA3CT,IAAI,CA2CUwL,KAAK,EACVmD,CAAU,CAAC,EAAE,EAAIE,EACjBD,CAAQ,CAAC,EAAE,EAAIC,IAGfF,CAAU,CAAC,EAAE,EAAIE,EACjBD,CAAQ,CAAC,EAAE,EAAIC,IAKnB,CAAC7O,AAtDN,IAAI,CAsDOwL,KAAK,EAAIxL,AAtDpB,IAAI,CAsDqB4F,KAAK,CAACmJ,WAAW,CAAE,CACvC,IACIC,EAAsB,CAClB,IACAhP,AA1Db,IAAI,CA0Dc0L,IAAI,CACTiD,CAAU,CAAC,EAAE,EAAI,EACpB,CACDM,EAAkB,CANMN,EAQpBK,EACH,CACDE,EAAsB,CAClB,IACAlP,AAnEb,IAAI,CAmEc4F,KAAK,CAACuJ,UAAU,CAAGnP,AAnErC,IAAI,CAmEsC4F,KAAK,CAACmJ,WAAW,CAC9C/O,AApEb,IAAI,CAoEcoP,QAAQ,CAAC3H,EAAMzH,AApEjC,IAAI,CAoEkCqP,cAAc,EAC1C,CAMDC,EAAkB,CALM,CACpB,IACAV,CAAQ,CAAC,EAAE,EAAI,EACf5O,AAzEb,IAAI,CAyEcoP,QAAQ,CAAC3H,EAAMzH,AAzEjC,IAAI,CAyEkCqP,cAAc,EAC1C,CAGGH,EACH,AACAlP,CA/EV,IAAI,CA+EWyK,IAAI,CAAC8E,WAAW,EAAIlN,EAAM,GAAM,GACtCrC,CAAAA,AAhFT,IAAI,CAgFUyK,IAAI,CAAC8E,WAAW,CAAGvP,AAhFjC,IAAI,CAgFkCyK,IAAI,CAAC+E,YAAY,CAACP,EAAe,EAE9DjP,AAlFT,IAAI,CAkFUyK,IAAI,CAAC8E,WAAW,GACrBvP,AAnFT,IAAI,CAmFUyK,IAAI,CAAC8E,WAAW,CAACE,IAAI,CAAC,CACvBC,OAAQ9P,EAAQ+P,SAAS,CACzB,eAAgB/P,EAAQ4O,SAAS,AACrC,GACAxO,AAvFT,IAAI,CAuFUyK,IAAI,CAAC8E,WAAW,CAACK,OAAO,CAAC,CAC1B7R,EAAGkR,CACP,IAECjP,AA3FV,IAAI,CA2FWyK,IAAI,CAACoF,WAAW,EAAIpI,EAAM,GAAM,GACtCzH,CAAAA,AA5FT,IAAI,CA4FUyK,IAAI,CAACoF,WAAW,CAAG7P,AA5FjC,IAAI,CA4FkCyK,IAAI,CAAC+E,YAAY,CAACF,EAAe,EAE9DtP,AA9FT,IAAI,CA8FUyK,IAAI,CAACoF,WAAW,GACrB7P,AA/FT,IAAI,CA+FUyK,IAAI,CAACoF,WAAW,CAACJ,IAAI,CAAC,CACvBC,OAAQ9P,EAAQ+P,SAAS,CACzB,eAAgB/P,EAAQ4O,SAAS,AACrC,GACAxO,AAnGT,IAAI,CAmGUyK,IAAI,CAACoF,WAAW,CAACD,OAAO,CAAC,CAC1B7R,EAAGuR,CACP,GAER,CAGKtP,AA1GN,IAAI,CA0GOyK,IAAI,CAACqF,aAAa,EAIxB9P,AA9GL,IAAI,CA8GMyK,IAAI,CAACqF,aAAa,CAACL,IAAI,CAAC,CACzBC,OAAQ9P,EAAQ+P,SAAS,CACzB,eAAgB/P,EAAQ4O,SAAS,AACrC,GACAxO,AAlHL,IAAI,CAkHMyK,IAAI,CAACqF,aAAa,CAACF,OAAO,CAAC,CAC5B7R,EAAG0Q,CACP,IATAzO,AA3GL,IAAI,CA2GMyK,IAAI,CAACqF,aAAa,CAAG9P,AA3G/B,IAAI,CA2GgCyK,IAAI,CAAC+E,YAAY,CAACf,GAYrDzO,AAvHD,IAAI,CAuHEuO,QAAQ,CAACvO,AAvHf,IAAI,CAuHgB+P,QAAQ,CAAG,OAAS,OAAO,EAClD,CACJ,CAIA,GAHA,AAAC,CAAA,AAACtF,CAAAA,MAAAA,EAAmC,KAAK,EAAIA,EAAKK,OAAO,AAAD,GAAM,EAAE,AAAD,EAAG5I,OAAO,CAAC,SAAU8I,CAAM,EAAI,OAAOA,EAAOgF,MAAM,EAAI,GAGnH,CAAChQ,AA7HE,IAAI,CA6HDwL,KAAK,EACXxL,AA9HG,IAAI,CA8HF4F,KAAK,CAACqK,WAAW,EACrBjQ,CAAAA,AA/HE,IAAI,CA+HD0N,SAAS,EAAK,CAAA,AAA6B,OAA5B5M,CAAAA,EAAKd,AA/HvB,IAAI,CA+HwBkQ,YAAY,AAAD,GAAepP,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG4M,SAAS,AAAD,CAAC,GAC9F1N,AAhIG,IAAI,CAgIFI,aAAa,CAACI,MAAM,CAAE,CAM3B,IALA,IAAI6O,EAAiBrP,AAjIlB,IAAI,CAiImBqP,cAAc,CACpCc,EAAWnQ,AAlIZ,IAAI,CAkIaI,aAAa,CAACJ,AAlI/B,IAAI,CAkIgCI,aAAa,CAACI,MAAM,CAAG,EAAE,CAC5D4P,EAAcpQ,AAnIf,IAAI,CAmIgBI,aAAa,CAAC,EAAE,CACnCoJ,EAAQ,KAAK,EACb6G,EAAW,KAAK,EACb,AAAC7G,CAAAA,EAAQxJ,AAtIb,IAAI,CAsIcsQ,YAAY,CAACC,GAAG,EAAC,GAAM/G,EAAMgH,OAAO,EACrDhH,EAAMiH,IAAI,GAEd,KAAO,AAACJ,CAAAA,EAAWrQ,AAzIhB,IAAI,CAyIiB0Q,WAAW,CAACH,GAAG,EAAC,GACpCF,EAASG,OAAO,EAChBH,EAASI,IAAI,GAGjBjH,CAAAA,EAAQxJ,AA9IL,IAAI,CA8IMmJ,KAAK,CAACiH,EAAY,CAAC5G,KAAK,AAAD,IAE5BnH,EAAM+N,EAAcf,EACpBrP,AAjJL,IAAI,CAiJMsQ,YAAY,CAAC5P,IAAI,CAAC8I,EAAMmH,IAAI,IAGjCnH,EAAMiH,IAAI,IAIlBjH,CAAAA,EAAQxJ,AAxJL,IAAI,CAwJMmJ,KAAK,CAACgH,EAAS,CAAC3G,KAAK,AAAD,IAEzB2G,EAAW1I,EAAM4H,EACjBrP,AA3JL,IAAI,CA2JMsQ,YAAY,CAAC5P,IAAI,CAAC8I,EAAMmH,IAAI,IAGjCnH,EAAMiH,IAAI,IAGlB,IAAIG,EAAO5Q,AAjKR,IAAI,CAiKSmJ,KAAK,CAACgH,EAAS,CAACS,IAAI,CAChCA,GACAT,EAAW1I,EAAM4H,GACjBc,EAAW1I,EAAM,GAAKzH,AApKvB,IAAI,CAoKwBmJ,KAAK,CAACgH,EAAS,CAACU,MAAM,EACjD7Q,AArKD,IAAI,CAqKE0Q,WAAW,CAAChQ,IAAI,CAACkQ,EAAKD,IAAI,GAEvC,CACJ,CACJ,CAIA,SAASG,IAGL,IAFI/Q,EAEAgR,EAAW,AAA8B,OAA7BhR,CAAAA,EAAKC,AADV,IAAI,CACWI,aAAa,AAAD,GAAeL,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGM,IAAI,CACjFT,EAAUI,AAFH,IAAI,CAEIJ,OAAO,CACtBwL,EAAcxL,EAAQ6K,IAAI,EAAI,CAAC,EAC/BuG,EAAahR,AAJN,IAAI,CAIO8F,WAAW,CAACuE,MAAM,EAAI,CAAC,CAEzCe,CAAAA,EAAYC,OAAO,GACfrL,AAPG,IAAI,CAOFwL,KAAK,EACVxL,AARG,IAAI,CAQFgC,MAAM,CAACE,OAAO,CAAC,SAAUF,CAAM,EAChCA,EAAOpC,OAAO,CAACqR,UAAU,CAAG,CAChC,GAKIF,GACAnR,EAAQsR,oBAAoB,EAC5BtR,EAAQyK,MAAM,EACd,CAAC7B,EAAQwI,EAAWhE,KAAK,GACxBpN,CAAAA,AACc,CAAA,IADdA,EAAQsR,oBAAoB,CAACH,EAASI,QAAQ,CAAC,CAC3CC,KAAK,EACNL,EAASM,KAAK,CAAG,CAAA,IAErBzR,EAAQyK,MAAM,CAAC2C,KAAK,CAAG,OAClBxE,EAAQwI,EAAWxP,CAAC,GACrB5B,CAAAA,EAAQyK,MAAM,CAAC7I,CAAC,CAAG,CAAA,IAOT,aAAd,IAAI,CAACyI,IAAI,EACTjK,AAjCD,IAAI,CAiCEyK,IAAI,EACTzK,AAlCD,IAAI,CAkCEyK,IAAI,CAACK,OAAO,EACjB,CAAA,IAAI,CAAC5C,cAAc,CAAG,IAAI,CAACoJ,YAAY,AAAD,EAItD,CASA,SAASC,EAAkBpG,CAAC,EACxB,IAGIqG,EAHA5R,EAAU,IAAI,CAACA,OAAO,CACtBkG,EAAcqF,EAAErF,WAAW,CAC3BsF,EAAe,AAACxL,GAAWqJ,EAASrJ,EAAQ6K,IAAI,EAAK7K,EAAQ6K,IAAI,CAAG,CAAC,CAE7C,EAAA,IAAxBW,EAAYC,OAAO,GAGnBmG,EAAkB3I,EAAM,CAAA,EAAM,CAC1B4I,UAAY,wBAA2B3L,CAAAA,EAAY2L,SAAS,EAAI,EAAC,EACjEP,qBAAsB,CAClBQ,KAAM,CACFC,KAAM,CAAC,QAAS,OAAO,AAC3B,EACAC,IAAK,CACDD,KAAM,CAAC,SAAU,SAAU,OAAO,AACtC,EACAE,KAAM,CACFF,KAAM,CAAC,UAAW,MAAM,AAC5B,EACAG,MAAO,CACHH,KAAM,CAAC,OAAQ,OAAQ,KAAK,AAChC,CACJ,EACAlH,KAAM,CACFsH,YAAa,CACjB,EACA1H,OAAQ,CACJ2H,QAAS,EACT/D,MAAO,CACHgE,SAAU,OACd,CACJ,EACA1F,OAAQ,EACRH,MAAO,CACH8F,KAAM,KACNC,aAAc,CAAA,EACd/E,SAAU,EACVa,MAAO,CACHmE,aAAc,UAClB,CACJ,EAIAC,MAAO,CAAC,CACA,cACA,CAAC,EAAG,GAAI,IAAI,CACf,CAAE,CACC,SACA,CAAC,EAAG,GAAG,CACV,CAAE,CACC,SACA,CAAC,EAAG,EAAG,GAAG,CACb,CAAE,CACC,OACA,CAAC,EAAG,EAAE,CACT,CAAE,CACC,MACA,CAAC,EAAE,CACN,CAAE,CACC,OACA,CAAC,EAAE,CACN,CAAE,CACC,QACA,CAAC,EAAE,CACN,CAAE,CACC,OACA,KACH,CAAC,AACV,EAAGvM,GAEe,UAAd,IAAI,CAAC8H,IAAI,GAILpF,EAAQ1C,EAAY2H,QAAQ,GAC5B,CAACjF,EAAQ1C,EAAYwM,iBAAiB,GACtCd,CAAAA,EAAgBc,iBAAiB,CAAG,GAAE,GAM1C,CAAA,CAAC9J,EAAQ1C,EAAYwM,iBAAiB,GAElC9J,EAAQ1C,EAAY2H,QAAQ,CAAA,GAC3BjF,EAAQ1C,EAAYyM,cAAc,GAClC/J,EAAQ1C,EAAYwL,YAAY,GAChC9I,EAAQ1C,EAAYuM,KAAK,GAC1Bb,CAAAA,EAAgBe,cAAc,CAAG,SAAUlQ,CAAG,CAAEoF,CAAG,EAG/C,IAFI1H,EACAe,EACA0R,EAAa,AAA0F,OAAzF1R,CAAAA,EAAK,AAA6B,OAA5Bf,CAAAA,EAAK,IAAI,CAACmQ,YAAY,AAAD,GAAenQ,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGK,aAAa,AAAD,GAAeU,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGT,IAAI,CACnJ,GAAImS,EAAY,CAKZ,IAAK,IAJDH,EAASb,EAAgBa,KAAK,EAAI,EAAE,CACpCI,EAAU,KAAK,EACfpB,EAAQ,EACRF,EAAW,OACN5Q,EAAI,EAAGA,EAAI8R,EAAM7R,MAAM,CAAED,IAAK,CACnC,IAAImS,EAASL,CAAK,CAAC9R,EAAE,CACrB,GAAImS,GAAUA,CAAM,CAAC,EAAE,GAAKF,EAAWrB,QAAQ,CAAE,CAC7CsB,EAAUlS,EACV,KACJ,CACJ,CAVA,IAYIoS,EAAQ/J,EAAkB6J,IAAYJ,CAAK,CAACI,EAAU,EAAE,CAC5D,GAAIE,EAAM,CACNxB,EAAWwB,CAAI,CAAC,EAAE,EAAI,OACtB,IAAIC,EAASD,CAAI,CAAC,EAAE,CACpBtB,EAAQ,AAACuB,CAAAA,MAAAA,EAAuC,KAAK,EAAIA,CAAM,CAAC,EAAE,AAAD,GAAM,CAG3E,KACiC,SAAxBJ,EAAWrB,QAAQ,EAExBE,CAAAA,EAAQmB,AAAmB,GAAnBA,EAAWnB,KAAK,AAAI,EAEhC,IAAIwB,EAAY9J,CAAS,CAACoI,EAAS,CAEnC,OADA,IAAI,CAACG,YAAY,CAAGuB,EAAYxB,EACzB,IAAI,CAACzL,KAAK,CAACD,IAAI,CAACmN,YAAY,CAAC,CAAED,UAAWA,EAAWxB,MAAOA,EAAOF,SAAUA,CAAS,EAAG9O,EAAKoF,EAAK,IAAI,CAAC7H,OAAO,CAACmT,WAAW,CACtI,CACJ,CAAA,GAIRlK,EAAM,CAAA,EAAM,IAAI,CAACjJ,OAAO,CAAE4R,GACtB,IAAI,CAAChG,KAAK,GAOV5L,EAAQoT,UAAU,CAAGlK,EAAchD,EAAYkN,UAAU,CAAE,GAC3DpT,EAAQqT,UAAU,CAAGnK,EAAchD,EAAYmN,UAAU,CAAE,IAI3DrK,EAAkBhJ,EAAQ6K,IAAI,CAACsH,WAAW,GAC1CnS,CAAAA,EAAQsT,SAAS,CAAGtT,EAAQ4O,SAAS,CACjCpD,EAAY2G,WAAW,AAAD,EAGtC,CAIA,SAASoB,EAAmBhI,CAAC,EAEzB,IAAIrF,EAAcqF,EAAErF,WAAW,CAC3BsF,EAAc,AAACtF,CAAAA,MAAAA,EAAiD,KAAK,EAAIA,EAAY2E,IAAI,AAAD,GAAM,CAAC,EAC/FK,EAAUM,EAAYN,OAAO,AAG7BM,CAAAA,EAAYC,OAAO,EAAIP,GACvBjC,EAAM,CAAA,EAAM7I,AAPL,IAAI,CAOMJ,OAAO,CAAEkL,CAAO,CAAC,EAAE,CAE5C,CAKA,SAASsI,IAEL,AAACpT,CAAAA,AADU,IAAI,CACTyK,IAAI,CAACK,OAAO,EAAI,EAAE,AAAD,EAAG5I,OAAO,CAAC,SAAU8I,CAAM,EAAI,OAAOA,EAAOqI,QAAQ,EAAI,EACpF,CAMA,SAASC,EAAgBnI,CAAC,EACtB,IACIK,EAAQzL,AADH,IAAI,CACEyL,KAAK,CAChB2C,EAAqBpO,AAFhB,IAAI,CAEeoO,kBAAkB,CAC1CrN,EAAKf,AAHA,IAAI,CAGDH,OAAO,CAAC6K,IAAI,CACpBW,EAActK,AAAO,KAAK,IAAZA,EAAgB,CAAC,EAAIA,EACvC,GAAIsK,EAAYC,OAAO,EAAI8C,EAAoB,CAC3C,IAAIoF,EAAe,AAA+B,EAA/B,IAAI,CAAC3T,OAAO,CAACyK,MAAM,CAACwE,QAAQ,CAC3CA,EAAWrD,EACNJ,EAAYoI,UAAU,EACnBD,EAAepF,EAAmB7E,MAAM,CAC5CiK,EAAepF,EAAmB9E,KAAK,CAC3CV,EAAiBwC,EAAEc,QAAQ,EAC3Bd,EAAEc,QAAQ,CAAC,EAAE,CAAG4C,EAGhB1D,EAAEc,QAAQ,CAAG,CAAC4C,EAAU,EAAE,AAElC,CACJ,CAIA,SAAS4E,IACL,IAAI,CAAC9F,IAAI,CAACzL,OAAO,CAAC,SAAUlC,CAAI,EAC5B,IAAID,EACJ,AAAC,CAAA,AAAC,CAAA,AAAqB,OAApBA,CAAAA,EAAKC,EAAKyK,IAAI,AAAD,GAAe1K,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG+K,OAAO,AAAD,GAAM,EAAE,AAAD,EAAG5I,OAAO,CAAC,SAAU8I,CAAM,EAC/FA,EAAO0I,WAAW,GAClB1I,EAAOtE,kBAAkB,EAC7B,EACJ,EACJ,CAIA,SAASiN,EAAUxI,CAAC,EAChB,IAAIV,EAAO,IAAI,CAACA,IAAI,CACpB,AAACA,CAAAA,EAAKK,OAAO,EAAI,EAAE,AAAD,EAAG5I,OAAO,CAAC,SAAU8I,CAAM,EAAI,OAAOA,EAAOqD,OAAO,CAAClD,EAAEyI,UAAU,CAAG,GACtFnJ,EAAKK,OAAO,CAAG,KAAK,CACxB,CAKA,SAAS+I,EAAO1I,CAAC,EAEb,IAAIrF,EAAcqF,EAAErF,WAAW,EAAI,CAAC,EAChCsF,EAActF,EAAY2E,IAAI,EAAI,CAAC,CACnCW,CAAAA,EAAYC,OAAO,EAAI7C,EAAQ4C,EAAY0I,WAAW,GACtDhO,CAAAA,EAAYiO,SAAS,CAAGjO,EAAY6J,SAAS,CAAIvE,EAAY0I,WAAW,EAEvE9T,AANM,IAAI,CAMLyK,IAAI,EACVzK,CAAAA,AAPO,IAAI,CAONyK,IAAI,CAAG,IAAIuJ,GAPT,IAAI,CAO2B,EAE1ChU,AATW,IAAI,CASVsQ,YAAY,CAAG,EAAE,CACtBtQ,AAVW,IAAI,CAUV0Q,WAAW,CAAG,EAAE,AACzB,CAKA,SAASuD,EAA4B9I,CAAC,EAClC,IACI3B,EAAQC,AADD,IAAI,CACED,KAAK,CAClBxJ,EAAOyJ,AAFA,IAAI,CAECzJ,IAAI,CAChBkU,EAAWlU,EAAKkU,QAAQ,CACxBtO,EAAQ5F,EAAK4F,KAAK,CAElBwF,EAAcxL,AADJI,EAAKJ,OAAO,CACA6K,IAAI,EAAI,CAAC,EAC/B0J,EAAYnU,EAAKJ,OAAO,CAACyK,MAAM,CAC/B2C,EAAQmH,EAAUnH,KAAK,CAGvBrC,EAAOxN,CAAY,CAAC6C,EAAK2K,IAAI,CAAC,CAC9B0E,EAAiBlE,EAAEkE,cAAc,CACjCjP,EAAgBJ,EAAKI,aAAa,CAClCgU,EAAU3K,AAdH,IAAI,CAcIF,GAAG,CAAG8F,EACrBgF,EAAezL,EAAkBxI,CAAa,CAAC+K,EAAEmJ,KAAK,CAAG,EAAE,EACvDlU,CAAa,CAAC+K,EAAEmJ,KAAK,CAAG,EAAE,CAAGjF,EAC7B,AAACrP,CAAAA,EAAKyH,GAAG,EAAI,CAAA,EAAK4H,EACtBpD,EAAWjM,EAAKiM,QAAQ,CAAC,QACzBiH,EAAYjH,EAAWA,CAAQ,CAAC,EAAE,CAAG,EACrCsI,EAAYtI,EAAWA,CAAQ,CAAC,EAAE,CAAG,EAAI,EAE7C,GAAIb,AAAwB,CAAA,IAAxBA,EAAYC,OAAO,CAAW,CAC9B,IACImJ,EADA3H,EAAS,KAAK,EAEdnB,EAAO,KAAK,EACZ+I,EAAQ,KAAK,EA2CjB,GAzCI9J,AAAS,QAATA,EAEA6J,EAAQ3H,AADRA,CAAAA,EAAS7M,EAAK+L,GAAG,CAAG/L,EAAK2L,MAAM,AAAD,EACbuH,EAEZvI,AAAS,WAATA,EAELkC,EAAS2H,AADTA,CAAAA,EAAQ5O,EAAM8O,WAAW,CAAG1U,EAAK6M,MAAM,CAAG7M,EAAK2L,MAAM,AAAD,EACnCuH,GAGjBrG,EAAS7M,EAAK+L,GAAG,CAAG/L,EAAKkF,GAAG,CAAIlF,CAAAA,EAAK2U,SAAS,CAACT,EAAWG,EAAcD,IAAY,CAAA,EACpFI,EAAQxU,EAAK+L,GAAG,CAAG/L,EAAKkF,GAAG,CAAIlF,CAAAA,EAAK2U,SAAS,CAACT,EAAWE,EAAUC,IAAgB,CAAA,GAGnF1J,AAAS,UAATA,EAEA8J,EAAQ/I,AADRA,CAAAA,EAAO9F,EAAMuJ,UAAU,CAAGnP,EAAKyU,KAAK,CAAGzU,EAAK2L,MAAM,AAAD,EAClCuH,EAEVvI,AAAS,SAATA,EAELe,EAAO+I,AADPA,CAAAA,EAAQzU,EAAK0L,IAAI,CAAG1L,EAAK2L,MAAM,AAAD,EACfuH,GAGfxH,EAAO3B,KAAKC,KAAK,CAAChK,EAAK0L,IAAI,CAAI1L,CAAAA,EAAK2U,SAAS,CAACT,EAAWG,EAAcD,IAAY,CAAA,GAAMG,EACzFE,EAAQ1K,KAAK1H,GAAG,CAChB0H,KAAKC,KAAK,CAAChK,EAAK0L,IAAI,CAAI1L,CAAAA,EAAK2U,SAAS,CAACT,EAAWE,EAAUC,IAAgB,CAAA,GAAME,EAAWvU,EAAK0L,IAAI,CAAG1L,EAAKkF,GAAG,GAErHuE,AAtDO,IAAI,CAsDNuE,SAAS,CAAGyG,EAAQ/I,EAGzBP,EAAE5B,GAAG,CAAC/H,CAAC,CAAIwL,AAAU,SAAVA,EACPtB,EACAsB,AAAU,UAAVA,EACIyH,EACA/I,EAAQ,AAAC+I,CAAAA,EAAQ/I,CAAG,EAAK,EAEjCP,EAAE5B,GAAG,CAAChI,CAAC,CAICiT,EAAS,AAAC3H,CAAAA,EAAS2H,CAAI,EAAK,EAEhChL,EAAO,CACP,IAAIoL,EAAahP,EAAM6G,QAAQ,CAACC,WAAW,CAAClD,GACxCE,EAAcF,EAAMI,OAAO,GAAGN,MAAM,CAGxC,GAAK6K,EAAUU,OAAO,CAUlB1J,EAAE5B,GAAG,CAAChI,CAAC,EAEPqT,EAAWvP,CAAC,CAER,CAAEqE,CAAAA,EAAc,CAAA,MAdA,CACpB,IAAIoL,EAAQ/K,KAAKC,KAAK,CAACN,EAAckL,EAAWG,CAAC,CACjD5J,CAAAA,EAAE5B,GAAG,CAAChI,CAAC,EAGP,AAAEqT,CAAAA,EAAWvP,CAAC,CAAIuP,CAAAA,EAAWG,CAAC,CAAGH,EAAWjI,CAAC,AAADA,CAAC,EAAK,EAE9C,CAAE,CAAA,AAAEmI,CAAAA,EAAQ,CAAA,EAAKF,EAAWG,CAAC,CAAI,CAAA,CACzC,CAQJ,CACA5J,EAAE5B,GAAG,CAAC/H,CAAC,EAAI,AAACxB,EAAKwL,KAAK,EAAI2I,EAAU3S,CAAC,EAAK,CAC9C,CACJ,CAIA,SAASwT,EAAkBC,CAAG,EAE1B,IADIlV,EACAC,EAAOiV,EAAIjV,IAAI,CACf0H,EAAQuN,EAAIvN,KAAK,CACrB,GAAI,AAA6B,OAA5B3H,CAAAA,EAAKC,EAAKJ,OAAO,CAAC6K,IAAI,AAAD,GAAe1K,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGsL,OAAO,CAAE,CAC1E,IAAI+I,EAAUpU,EAAKI,aAAa,CAC5B4B,EAAS,AAAChC,CAAAA,EAAKkQ,YAAY,EAAIlQ,CAAG,EAAGgC,MAAM,CAAC,EAAE,CAC9CkT,EAAUxN,IAAU0M,CAAO,CAAC,EAAE,CAC9BvD,EAASnJ,IAAU0M,CAAO,CAACA,EAAQ5T,MAAM,CAAG,EAAE,CAC9Ca,EAAQW,GAAU0G,EAAc1G,EAAOpC,OAAO,CAACuV,IAAI,CACnD,SAAUC,CAAC,EACP,OAAOA,CAAC,CAACpV,EAAKoC,OAAO,CAAG,IAAM,IAAI,GAAKsF,CAC/C,GACI2N,EAAY,KAAK,EACjBhU,GAASW,EAAOsT,EAAE,CAAC,WAGnBD,EAAYxM,EAAMxH,GAClBrC,IAA8EuW,WAAW,CAACC,KAAK,CAAC9W,SAAS,CAAC+W,UAAU,CAC/GC,oBAAoB,CAACL,EAAWrV,EAAK4F,KAAK,GAInDqP,EAAIC,OAAO,CAAGA,EACdD,EAAIpE,MAAM,CAAGA,EACboE,EAAI5T,KAAK,CAAGgU,CAChB,CACJ,CAoBA,SAASM,IAGL,IAFI5V,EACAe,EAEAlB,EAAUI,AADH,IAAI,CACIJ,OAAO,CACtBwL,EAAcxL,EAAQ6K,IAAI,EAAI,CAAC,EAC/BmL,EAAe5V,AAHR,IAAI,CAGSiN,UAAU,CAC9B7M,EAAgBJ,AAJT,IAAI,CAIUI,aAAa,CAClCyV,EAAWzV,CAAa,CAAC,EAAE,CAC3B0V,EAAY1V,CAAa,CAAC,EAAE,CAC5B2V,EAAU3V,CAAa,CAACA,EAAcI,MAAM,CAAG,EAAE,CACjDwV,EAAgB5V,CAAa,CAACA,EAAcI,MAAM,CAAG,EAAE,CACvDyV,EAAY,AAA6B,OAA5BlW,CAAAA,EAAKC,AATX,IAAI,CASYkQ,YAAY,AAAD,GAAenQ,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGsC,GAAG,CAChF6T,EAAY,AAA6B,OAA5BpV,CAAAA,EAAKd,AAVX,IAAI,CAUYkQ,YAAY,AAAD,GAAepP,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG2G,GAAG,CAChFpF,EAAM4T,GAAajW,AAXZ,IAAI,CAWaqC,GAAG,CAC3BoF,EAAMyO,GAAalW,AAZZ,IAAI,CAYayH,GAAG,CAC3B6J,EAAetR,AAbR,IAAI,CAaSsR,YAAY,CAChC6E,EACAvN,EAAkBvG,IACdA,GAAOwT,EAAWvE,GAClBjP,EAAMyT,EACVM,EAAkBxN,EAAkBvG,IAChCwT,EAAWxT,GACXwT,EAAWvE,EAAejP,EAC9BgU,EAAoBzN,EAAkBnB,IAClCsO,EAAUtO,GACVsO,EAAUzE,EAAe7J,EAC7B6O,EAAkB1N,EAAkBnB,IAChCA,GAAOsO,EAAUzE,GACjB7J,EAAMuO,CACc,EAAA,IAAxB5K,EAAYC,OAAO,EACnB,CAACuK,GACA5V,CAAAA,AA7BM,IAAI,CA6BLoC,OAAO,EAAIpC,AA7BV,IAAI,CA6BWuW,QAAQ,AAAD,IACxBH,CAAAA,GAAkBD,CAAe,GAAM,CAACvW,EAAQ4W,WAAW,EAC5DpW,CAAAA,CAAa,CAAC,EAAE,CAAGiC,CAAE,EAEpBgU,CAAAA,GAAoBC,CAAa,GAAM,CAAC1W,EAAQ6W,SAAS,EAC1DrW,CAAAA,CAAa,CAACA,EAAcI,MAAM,CAAG,EAAE,CAAGiH,CAAE,EAGxD,CAKA,SAASiP,EAAalM,CAAO,EAEzB,IAx4BiBmM,EAw4Bb5W,EAAKC,AADE,IAAI,CACDJ,OAAO,CAAC6K,IAAI,OAE1B,AAAIW,AAAwB,CAAA,IAAxBA,AADcrL,CAAAA,AAAO,KAAK,IAAZA,EAAgB,CAAC,EAAIA,CAAC,EACxBsL,OAAO,EAAarL,AAHzB,IAAI,CAG0BiN,UAAU,CACxCjN,AAJA,IAAI,CAICsR,YAAY,CAErB9G,EAAQI,KAAK,CANT,IAAI,EAv4BE+L,EA64BsBC,UA54BhCC,MAAMnY,SAAS,CAACyE,KAAK,CAACvE,IAAI,CAAC+X,EAAM,IA64B5C,CA35BIxZ,CADOA,EAKRA,GAAiBA,CAAAA,EAAe,CAAC,CAAA,EAJpB,CAACA,EAAa,GAAM,CAAG,EAAE,CAAG,MACxCA,CAAY,CAACA,EAAa,KAAQ,CAAG,EAAE,CAAG,QAC1CA,CAAY,CAACA,EAAa,MAAS,CAAG,EAAE,CAAG,SAC3CA,CAAY,CAACA,EAAa,IAAO,CAAG,EAAE,CAAG,OAm6B7C,IAAI6W,GAAmC,WAMnC,SAASA,EAAkBhU,CAAI,EAC3B,IAAI,CAACA,IAAI,CAAGA,CAChB,CA4EA,OAvDAgU,EAAkBtV,SAAS,CAAC4P,WAAW,CAAG,WAEtC,IADIvO,EACAC,EAAO,IAAI,CAACA,IAAI,CAChB4F,EAAQ5F,EAAK4F,KAAK,CAClB0H,EAActN,EAAKyK,IAAI,CAAC6C,WAAW,CACnCxC,EAAW,AAAC,CAAA,AAA6B,OAA5B/K,CAAAA,EAAKC,EAAKkQ,YAAY,AAAD,GAAenQ,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG0K,IAAI,CAACK,OAAO,AAAD,GACpF9K,EAAKyK,IAAI,CAACK,OAAO,EACjB,EAAE,CACNgM,EAAaxJ,EAActN,EAAKkQ,YAAY,CAAGlQ,EAC/C+W,EAAY,GACZC,EAAY,SAGhB,AAAIhX,AAAc,IAAdA,EAAK2K,IAAI,EAAU,CAAC/E,EAAMqR,QAAQ,EAAInM,EAAQtK,MAAM,CAC7C,CAACR,EAAKkQ,YAAY,EAE7B,AAACtK,CAAAA,CAAK,CAAC5F,EAAK4N,IAAI,CAAC,EAAI,EAAE,AAAD,EAAG1L,OAAO,CAAC,SAAUgV,CAAS,CAAE5C,CAAK,EACnD4C,EAAUvM,IAAI,GAAK3K,EAAK2K,IAAI,EAC3BuM,EAAUtX,OAAO,CAAC4N,UAAU,GAC7BwJ,EAAY1C,EACR4C,IAAcJ,GAEdC,CAAAA,EAAYzC,CAAI,EAG5B,GACQ0C,IAAcD,GACjBnO,CAAAA,CAAAA,EAAkB0E,IACfxC,EAAQtK,MAAM,GAAK8M,CAChB,EACf,EASA0G,EAAkBtV,SAAS,CAAC8Q,YAAY,CAAG,SAAU2H,CAAI,EACrD,IAAInX,EAAO,IAAI,CAACA,IAAI,CAChByM,EAAWzM,EAAK4F,KAAK,CAAC6G,QAAQ,CAC9B7M,EAAUI,EAAKJ,OAAO,CACtBwX,EAAkB3K,EAAS0K,IAAI,CAACA,GAC3BE,QAAQ,CAAC,wBACTC,GAAG,CAACtX,EAAKuX,SAAS,EAQ3B,OAPK9K,EAASsB,UAAU,EACpBqJ,EAAgB3H,IAAI,CAAC,CACjBC,OAAQ9P,EAAQ+P,SAAS,CACzB,eAAgB/P,EAAQ4O,SAAS,CACjCgJ,OAAQ,CACZ,GAEGJ,CACX,EACOpD,CACX,GAOA1L,CAAAA,EAAYmP,CAAC,CAAG,SAAUC,CAAS,EAC/B,OAAO,IAAI,CAACC,UAAU,CAAC,KAAMD,EAAW,CAAA,GAAME,MAAM,CAAC,EACzD,EAEAtP,EAAYuP,CAAC,CAAG,SAAUH,CAAS,EAC/B,IAAI3Z,EAAI,IAAI,CAAC+Z,OAAO,CAACJ,GACjBK,EAAW,AAACha,CAAAA,CAAC,CAAC,EAAE,CAAG,CAAA,EAAK,EACxBia,EAAWja,EAAEoF,KAAK,CAAC,EACvB6U,CAAAA,CAAQ,CAAC,EAAE,CAAGja,CAAC,CAAC,EAAE,CAAGga,EAAW,EAChC,IAAIE,EAAgB,IAAI,CAACH,OAAO,CAAC,IAAI,CAACI,QAAQ,CAACF,CAAQ,CAAC,EAAE,CAAE,EAAG,IAW/D,OAVyB,IAArBC,CAAa,CAAC,EAAE,GAChBla,CAAC,CAAC,EAAE,CAAG,EACPA,CAAC,CAAC,EAAE,CAAG,EAAI,AAAC,CAAA,GAAKka,CAAa,CAAC,EAAE,AAAD,EAAK,GAQlC,AAAC,CAAA,EACJlO,KAAKoO,KAAK,CAAC,AAACC,CAAAA,AAPC,IAAI,CAACF,QAAQ,CAACF,CAAQ,CAAC,EAAE,CACtCA,CAAQ,CAAC,EAAE,CACXA,CAAQ,CAAC,EAAE,EACO,IAAI,CAACE,QAAQ,CAACD,CAAa,CAAC,EAAE,CAChDA,CAAa,CAAC,EAAE,CAChBA,CAAa,CAAC,EAAE,CAEuB,EAAK,OAAS,EAAGI,QAAQ,EACxE,EAS6B,IA+FzBC,GAA4C,SAAUC,CAAC,CAAEpN,CAAC,EAC1D,IAAIqN,EAAI,CAAC,EACT,IAAK,IAAIpD,KAAKmD,EAAOna,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC2Z,EAAGnD,IAAMjK,AAAe,EAAfA,EAAEsN,OAAO,CAACrD,IACzEoD,CAAAA,CAAC,CAACpD,EAAE,CAAGmD,CAAC,CAACnD,EAAE,AAAD,EACd,GAAImD,AAAK,MAALA,GAAa,AAAwC,YAAxC,OAAOna,OAAOsa,qBAAqB,CAChD,IAAK,IAAInY,EAAI,EAAG6U,EAAIhX,OAAOsa,qBAAqB,CAACH,GAAIhY,EAAI6U,EAAE5U,MAAM,CAAED,IACzC,EAAlB4K,EAAEsN,OAAO,CAACrD,CAAC,CAAC7U,EAAE,GAASnC,OAAOM,SAAS,CAACia,oBAAoB,CAAC/Z,IAAI,CAAC2Z,EAAGnD,CAAC,CAAC7U,EAAE,GACzEiY,CAAAA,CAAC,CAACpD,CAAC,CAAC7U,EAAE,CAAC,CAAGgY,CAAC,CAACnD,CAAC,CAAC7U,EAAE,CAAC,AAAD,EAE5B,OAAOiY,CACX,EAOII,GAAS,AAAC5Z,IAA+E4Z,MAAM,CAAEC,GAAgB,AAAC7Z,IAA+EO,QAAQ,CAAEuZ,GAAY,AAAC9Z,IAA+EQ,IAAI,CAkD/S,SAASuZ,GAAQC,CAAE,CAAEC,CAAM,CAAEC,CAAK,CAAE/D,CAAI,CAAEgE,CAAiB,CAAEvZ,CAAO,EAChE,IAWIwZ,EACAC,EAZAC,EAAQ1Z,GAAWA,EAAQ0Z,KAAK,CAChCC,EAAS3Z,GAAWA,EAAQ2Z,MAAM,CAClCC,EAAO,CACHrE,KAAMA,EACNsE,MAAOP,EAAQ,EACfF,GAAIA,EACJE,MAAOA,EACPD,OAASA,GAAU,EACvB,EACAS,EAAc,EACdpQ,EAAS,CAIS,CAAA,YAAlB,OAAOiQ,GACPA,EAAOC,EAAM5Z,GAIjB,IAAI+Z,EAAW,AAAER,CAAAA,CAAiB,CAACH,EAAG,EAAI,EAAE,AAAD,EAAIY,GAAG,CAAC,SAAUC,CAAK,EAC1D,IAAIL,EAAOT,GAAQc,EAAMb,EAAE,CAC/BA,EAAKE,EAAQ,EACbW,EACAV,EACAvZ,GACAka,EAAaD,EAAMT,KAAK,EAAIW,IAC5BC,EAAYH,AAAoB,CAAA,IAApBA,EAAMI,SAAS,CACnBH,EACAD,EAAMR,GAAG,EACLU,IAYZ,OAVAX,EAAS,AAAC,CAACP,GAAcO,IAAUU,EAAaV,EAC5CU,EACAV,EAGJC,EAAO,AAAC,CAACR,GAAcQ,IAAQW,EAAWX,EACtCW,EACAX,EACJK,EAAcA,EAAc,EAAIF,EAAKE,WAAW,CAChDpQ,EAASS,KAAKtC,GAAG,CAAC+R,EAAKlQ,MAAM,CAAG,EAAGA,GAC5BkQ,CACX,GAeA,OAbIrE,IACAA,EAAKiE,KAAK,CAAGN,GAAU3D,EAAKiE,KAAK,CAAEA,GACnCjE,EAAKkE,GAAG,CAAGP,GAAU3D,EAAKkE,GAAG,CAAEA,IAEnCT,GAAOY,EAAM,CACTG,SAAUA,EACVD,YAAaA,EACbpQ,OAAQA,CACZ,GAEqB,YAAjB,OAAOgQ,GACPA,EAAME,EAAM5Z,GAET4Z,CACX,CAc6B,IAAIU,GAJtB,CACPnB,QAASA,GACToB,QAVJ,SAAiBhF,CAAI,CAAEvV,CAAO,EA5F1B,IACIwa,EACAC,EA2FJ,OAAOtB,GAAQ,GAAI,KAAM,EAAG,MA5FxBqB,EAAM,EAAE,CAeZhc,OAAO0D,IAAI,CAdPuY,EAAgBlF,AA2F+BA,EA3F1BmF,MAAM,CAAC,SAAUC,CAAI,CAC1CC,CAAI,EACA,IAAIza,EAAKya,EAAKvB,MAAM,CACxBA,EAASlZ,AAAO,KAAK,IAAZA,EAAgB,GAAKA,EAC9BiZ,EAAKwB,EAAKxB,EAAE,CAQZ,OAP4B,KAAA,IAAjBuB,CAAI,CAACtB,EAAO,EACnBsB,CAAAA,CAAI,CAACtB,EAAO,CAAG,EAAE,AAAD,EAEpBsB,CAAI,CAACtB,EAAO,CAACvY,IAAI,CAAC8Z,GACdxB,GACAoB,EAAI1Z,IAAI,CAACsY,GAENuB,CACX,EAAG,CAAC,IACuBrY,OAAO,CAAC,SAAUsX,CAAI,EAC7C,IAAIzZ,EACJ,GAAI,AAlBG,KAkBFyZ,GAAmBY,AAAsB,KAAtBA,EAAI3B,OAAO,CAACe,GAAe,CAC/C,IAAIiB,EAAgBJ,CAAa,CAACb,EAAK,CAACI,GAAG,CAAC,SAAUc,CAAM,EAGpD,OAFqBpC,GAAOoC,EAChC,EAAE,CAEN,GACA,AAAC3a,CAAAA,EAAKsa,CAAa,CAxBhB,GAwBsB,AAAD,EAAG3Z,IAAI,CAACkK,KAAK,CAAC7K,EAAI0a,GAC1C,OAAOJ,CAAa,CAACb,EAAK,AAC9B,CACJ,GACOa,GAiEmDza,EAC9D,CASA,EAgBI+a,GAAwB,AAAC3b,IAA+EG,QAAQ,CAAEyb,GAAc,AAAC5b,IAA+E4b,WAAW,CAAEC,GAAwB,AAAC7b,IAA+EiK,QAAQ,CAAE6R,GAAwB,AAAC9b,IAA+EO,QAAQ,CAAEwb,GAAoB,AAAC/b,IAA+EQ,IAAI,CAAEwb,GAAoB,AAAChc,IAA+EgK,IAAI,CASvpB,SAASiS,KAEAxR,AADM,IAAI,CACLS,QAAQ,EACdT,CAAAA,AAFO,IAAI,CAENS,QAAQ,CAAG,IAAIgR,GAFb,IAAI,CAEmC,CAEtD,CAuEA,SAASC,GAAqB3Q,CAAO,CAAEhJ,CAAC,CAAED,CAAC,CAAEiI,CAAK,CAAEgC,CAAK,CAAE4P,CAAY,CAAE/L,CAAc,CAAEiF,CAAK,CAAE+G,CAAI,EAEhG,IADItb,EAgBAoK,EACAqP,EACAN,EAhBAoC,EAAYP,GAAkB,AAAwB,OAAvBhb,CAAAA,EAAK0J,AAD7B,IAAI,CAC8B7J,OAAO,AAAD,GAAeG,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGsK,MAAM,CAChG+Q,GACA7R,EAAME,AAHC,IAAI,CAGAF,GAAG,CACdvJ,EAAOyJ,AAJA,IAAI,CAICzJ,IAAI,CAChBub,EAAavb,AAAc,aAAdA,EAAKiK,IAAI,CACtBuR,EAAShR,EAAQI,KAAK,CANf,IAAI,CAOX,CAACpJ,EACDD,EACAiI,EACAgC,EACA8P,EACAjM,EACAiF,EACA+G,EAAK,EAIT,GAAIE,EAAY,CACZ,IAAIza,EAAMwa,GAAaT,GAAsBS,EAAUG,MAAM,CACzD,CAAA,GACIH,EAAUG,MAAM,CAChB,CAAC,EACLza,EAAKF,EAAGuI,KAAK,CAEbqS,EAAK5a,EAAGkR,OAAO,CACfA,EAAU0J,AAAO,KAAK,IAAZA,EAAgB1b,AAAwB,GAAxBA,EAAKkQ,YAAY,CAAWwL,EACtDpR,EAAegR,GAAaR,GAAsBQ,EAAUhR,WAAW,EACnEgR,EAAUhR,WAAW,CACrB,EAGR4O,EAAQ,AAACM,CAAAA,MADTA,CAAAA,EAAOrP,MADPA,CAAAA,EAAqBnK,EAAKkK,QAAQ,CAACC,kBAAkB,AAAD,EACkB,KAAK,EAAIA,CAAkB,CAACZ,EAAI,AAAD,EACzD,KAAK,EAAIiQ,EAAKC,KAAK,AAAD,GAAM,EACpE+B,EAAOha,CAAC,EAER,AAXYR,CAAAA,AAAO,KAAK,IAAZA,EAAgB,EAAIA,CAAC,EAWvBgR,AAAU,EAAVA,EAEL,AAACkH,CAAAA,EAAQ,CAAA,EAAK5O,CACvB,CACA,OAAOkR,CACX,CAIA,SAASG,GAAgBnR,CAAO,EAC5B,IA7F2BoR,EACvB7b,EACAmK,EACA2R,EACApP,EACAqP,EACAlc,EACAyJ,EACAC,EACA0I,EACA+J,EAIA3O,EACA4O,EACAC,EAgFAC,EAHa3S,EAAME,AAAZ,IAAI,CAAaF,GAAG,CAAEvJ,EAAOyJ,AAA7B,IAAI,CAA8BzJ,IAAI,CAAEwJ,EAAQC,AAAhD,IAAI,CAAiDD,KAAK,CAAE2S,EAAW1S,AAAvE,IAAI,CAAwES,QAAQ,CAAEkS,EAAc3S,AAApG,IAAI,CAAqG7J,OAAO,CAAEqc,EAAOE,MAAAA,EAA2C,KAAK,EAAIA,EAASE,SAAS,CAAEC,EAAe9S,MAAAA,EAAqC,KAAK,EAAIA,EAAMgH,OAAO,CAAE+L,EAAWvc,EAAKkK,QAAQ,CAAEsS,EAAcxc,EAAKJ,OAAO,CAAEgG,EAAQ5F,EAAK4F,KAAK,CAAExF,EAAgBJ,EAAKI,aAAa,CAAE+J,EAAqBoS,EAASpS,kBAAkB,CAAEiR,EAAeL,GAAkBqB,MAAAA,EAAiD,KAAK,EAAIA,EAAY/R,MAAM,CAAEmS,MAAAA,EAAiD,KAAK,EAAIA,EAAYnS,MAAM,EAAGoS,EAAiBrB,GAAgBP,GAAsBO,EAAaK,MAAM,CAAE,CAAA,GAC1rBL,EAAaK,MAAM,CACnB,CAAC,EAAIjC,EAAOrP,MAAAA,EAA+D,KAAK,EAAIA,CAAkB,CAACZ,EAAI,CAAExJ,EAAKyZ,GAAQ,CAAC,EAAGE,EAAc3Z,EAAG2Z,WAAW,CAAED,EAAQ1Z,EAAG0Z,KAAK,CAAEiD,EAAiBlD,GAAQE,GAAeA,EAAc,EAAkBiD,EAAoB,AAAe,aAAd3c,EAAKiK,IAAI,EAAoBqS,EAAcN,EAAe5b,EAAcqY,OAAO,CAAClP,GAAO,GAAIqT,EAAkB,4BAA6BC,EAAmBD,EAAkB,SAAU7O,EAAanI,EAAMmI,UAAU,EAIre4O,GAAqBnD,GAErBhQ,EACKsT,WAAW,CAAC,AAAIC,OAAOF,EAAmB,OAC1CxF,QAAQ,CAACwF,EARqOpD,GAUvPjP,EAAQI,KAAK,CAZF,IAAI,CAYKiM,MAAMnY,SAAS,CAACyE,KAAK,CAACvE,IAAI,CAACgY,UAAW,IACtD+F,GAAqBD,IACrBR,EAAYK,EAASS,WAAW,CAACxD,GA3GVoC,EA4GD,CAClBqB,MAAQ,CAAClP,GACLvE,EAAM0T,MAAM,CAACD,KAAK,EAClB,GACJf,UAAWA,EACXiB,MAAO3T,EAAM4T,WAAW,CACxBxd,QAAS6c,EACThQ,SAAUjD,EAAMiD,QAAQ,CACxBgE,KAAMuL,EACNqB,GAAI7T,EAAM6T,EAAE,AAChB,EAnHAxB,EAAQ,CAAC3R,CADTA,EAAWT,AA2FJ,IAAI,CA3FKS,QAAQ,EACNmS,SAAS,CAC3B5P,EAAWmP,EAAOnP,QAAQ,CAC1BqP,EAAWF,EAAOyB,EAAE,CAEpBhU,EAAQzJ,CADRA,EAAUgc,EAAOhc,OAAO,EACRyJ,KAAK,EAAI,EACzBC,EAAS1J,EAAQ0J,MAAM,EAAI,EAC3B0I,EAAU,AAAuF,EAAtF,CAAA,AAA2B,OAA1BjS,CAAAA,EAAKH,EAAQoS,OAAO,AAAD,GAAejS,AAAO,KAAK,IAAZA,GAAgBA,GAAK0J,AAoF5D,IAAI,CApF6DzJ,IAAI,CAACkQ,YAAY,AAAD,EACxF6L,EAAa,CACTva,EAAGsa,EAASta,CAAC,CAAI6H,EAAQ,EAAK2I,EAC9BzQ,EAAGua,EAASva,CAAC,CAAI+H,EAAS,CAC9B,EACA8D,EAAWwO,EAAOM,SAAS,CAAG,GAAK,IACnCF,EAAeJ,EAAOnL,IAAI,EAAIqK,GAAsBiB,EAAWxa,CAAC,GAChE0a,EAAO/R,EAASmS,SAAS,GAEzBnS,CAAAA,EAASmS,SAAS,CAAGJ,EAAOxP,EACvB0K,IAAI,CAAC1K,EAAS6Q,OAAO,CAAC1d,EAAQqK,IAAI,CAAC,CAACrK,EAAQ4B,CAAC,EAAI,EAAG5B,EAAQ2B,CAAC,EAAI,EAAG8H,EAAOC,IAC3E+N,QAAQ,CAAC,yBACTC,GAAG,CAACsE,EAAOuB,KAAK,CAAA,EAGzBlB,CAAI,CAACD,EAAe,OAAS,OAAO,GAE/BvP,EAASsB,UAAU,EACpBkO,EACKxM,IAAI,CAAC,CACN8N,OAAQ,UACR,KAAQxC,GAAkBa,EAAOqB,KAAK,CAAE,WACxC,eAAgB,EAChBvN,OAAQ9P,EAAQ+P,SAAS,CACzB6N,YAAa5d,EAAQ4O,SAAS,EAAI,CACtC,GAGJyN,CAAI,CAACJ,EAAQ,OAAS,UAAU,CAAC,CAC7B4B,WAAY1B,EAAWva,CAAC,CACxBkc,WAAY3B,EAAWxa,CAAC,CACxB6L,SAAUA,CACd,GAmFI5D,EACK6N,QAAQ,CALEuF,EACVV,CAAAA,EAAY,YAAc,UAAS,GAKnCY,WAAW,CAJEF,EACbV,CAAAA,EAAY,WAAa,WAAU,GAInCnO,GACDvE,EAAM0E,GAAG,CAAC,CACNqP,OAAQ,SACZ,GAGJ,CAAC/T,EAAOyS,EAAK,CAAC/Z,OAAO,CAAC,SAAUyb,CAAM,EAC9BA,GAAU,CAACA,EAAOC,sBAAsB,GAExCjD,GAAsBgD,EAAOnN,OAAO,CAAE,YAAa,WA5J/DhH,AA6J4BA,EA7JtB6N,QAAQ,CAAC,mCACV7N,AA4JuBA,EA5JjBiD,QAAQ,CAACsB,UAAU,EAC1BvE,AA2JwBA,EA3JlB0E,GAAG,CAAC,CACN2P,eAAgB,WACpB,EA0JQ,GAEAlD,GAAsBgD,EAAOnN,OAAO,CAAE,WAAY,eArJ1DtC,EAAAA,EAAM2M,GAAsBjb,AAsJOwb,EAtJCnN,KAAK,EAAIrO,AAsJVwb,EAtJkBnN,KAAK,CAAG,CAAC,EAClEzE,AAqJgCA,EArJ1BsT,WAAW,CAAC,mCACbtT,AAoJ2BA,EApJrBiD,QAAQ,CAACsB,UAAU,EAC1BvE,AAmJ4BA,EAnJtB0E,GAAG,CAAC,CAAE2P,eAAiB3P,EAAI2P,cAAc,EAAI,MAAQ,EAoJnD,GACAlD,GAAsBgD,EAAOnN,OAAO,CAAE,QAAS,WAC3C2L,EAAS2B,cAAc,EAC3B,GACAH,EAAOC,sBAAsB,CAAG,CAAA,EAExC,IAEK3B,IACLrB,GAAY0B,GACZ9S,MAAAA,GAA8CA,EAAM0E,GAAG,CAAC,CAAEqP,OAAQ,SAAU,GAC5EtB,EAAK5N,OAAO,GAEpB,CAUA,IAAI6M,GAAuC,WASvC,SAASA,EAAsBzR,CAAI,EAC/B,IAAI,CAACA,IAAI,CAAGA,CAChB,CAqHA,OA5GAyR,EAAsBhX,OAAO,CAAG,SAAU6Z,CAAS,EAC/C,IAAIC,EAAYD,EAAUrf,SAAS,AAC9Bsf,CAAAA,EAAUF,cAAc,GACzBnD,GAAsBoD,EAAW,OAAQ9C,IACzCD,GAAkBgD,EAAW,mBAAoB7C,IACjDH,GAAkBgD,EAAW,cAAerC,IAE5CqC,EAAUC,QAAQ,CAAG,SAAUvY,CAAM,EACjC,IAAI,CAACwE,QAAQ,CAAC+T,QAAQ,CAACvY,EAC3B,EACAsY,EAAUE,MAAM,CAAG,SAAUxY,CAAM,EAC/B,IAAI,CAACwE,QAAQ,CAACgU,MAAM,CAACxY,EACzB,EACAsY,EAAUF,cAAc,CAAG,SAAUpY,CAAM,EACvC,IAAI,CAACwE,QAAQ,CAAC4T,cAAc,CAACpY,EACjC,EAER,EAkBAwV,EAAsBxc,SAAS,CAACuf,QAAQ,CAAG,SAAUvY,CAAM,EACvD,IAAI+D,EAAO,IAAI,CAACA,IAAI,CAChBzJ,EAAOyJ,EAAKzJ,IAAI,CAChBN,EAAaM,EAAKN,UAAU,CAChC,GAAIA,GACAM,EAAKkK,QAAQ,CAACC,kBAAkB,CAAE,CAClC,IAAIZ,EAAME,EAAKF,GAAG,CACdiQ,EAAOxZ,EAAKkK,QAAQ,CAACC,kBAAkB,CAACZ,EAAI,CAC5C1J,EAASG,EAAKkK,QAAQ,CAAC+T,QAAQ,CAACzE,GACpC9Z,EAAWC,SAAS,CAACE,EAAQkb,GAAkBrV,EAAQ,CAAA,GAC3D,CACJ,EAOAwV,EAAsBxc,SAAS,CAAC2P,OAAO,CAAG,WAClC,IAAI,CAACgO,SAAS,EACd,IAAI,CAACA,SAAS,CAAChO,OAAO,EAE9B,EAaA6M,EAAsBxc,SAAS,CAACwf,MAAM,CAAG,SAAUxY,CAAM,EACrD,IAAI3F,EAAK,IAAI,CAAC0J,IAAI,CACdF,EAAMxJ,EAAGwJ,GAAG,CACZvJ,EAAOD,EAAGC,IAAI,CACdkK,EAAWlK,EAAKkK,QAAQ,CACxBxK,EAAaM,EAAKN,UAAU,CAC5Bye,EAAiBjU,EAASC,kBAAkB,CAChD,GAAIzK,GAAcye,EAAgB,CAC9B,IAAI3E,EAAO2E,CAAc,CAAC5U,EAAI,CAC1B1J,EAASqK,EAASgU,MAAM,CAAC1E,GAC7B9Z,EAAWC,SAAS,CAACE,EAAQkb,GAAkBrV,EAAQ,CAAA,GAC3D,CACJ,EAcAwV,EAAsBxc,SAAS,CAACof,cAAc,CAAG,SAAUpY,CAAM,EAC7D,IAAI+D,EAAO,IAAI,CAACA,IAAI,CAChBzJ,EAAOyJ,EAAKzJ,IAAI,CAChBN,EAAaM,EAAKN,UAAU,CAChC,GAAIA,GACAM,EAAKkK,QAAQ,CAACC,kBAAkB,CAAE,CAClC,IAAIZ,EAAME,EAAKF,GAAG,CACdiQ,EAAOxZ,EAAKkK,QAAQ,CAACC,kBAAkB,CAACZ,EAAI,CAC5C1J,EAASG,EAAKkK,QAAQ,CAAC4T,cAAc,CAACtE,GAC1C9Z,EAAWC,SAAS,CAACE,EAAQkb,GAAkBrV,EAAQ,CAAA,GAC3D,CACJ,EACOwV,CACX,IASIkD,GAA+F5gB,EAAoB,KAiBJ6gB,IAAxF,AAACrf,IAA+E4Z,MAAM,CAA0B,AAAC5Z,IAA+EM,OAAO,EAAEgf,GAAyB,AAACtf,IAA+EO,QAAQ,CAAEgf,GAAyB,AAACvf,IAA+EiK,QAAQ,CAAEuV,GAAsB,AAACxf,IAA+E6J,KAAK,CAAE4V,GAAqB,AAACzf,IAA+EQ,IAAI,CAkQ5pBkf,IAlQ+qB,AAAC1f,IAA+E2f,cAAc,CA8EjxB,SAAyB/C,CAAM,EAC3B,IACIgD,EACAC,EACAte,EACAoC,EACAC,EACAkc,EANAtD,EAAS,CAAC,EAOd,GAAI+C,GAAuB3C,GA6BvB,IA5BAjZ,EAAO2b,GAAuB1C,EAAOjZ,IAAI,EAAIiZ,EAAOjZ,IAAI,CAAG,EAC3Dmc,EAASlD,EAAOkD,MAAM,CACtBD,EAAY,CAAC,EACbD,EAAWL,GAAuB3C,EAAOgD,QAAQ,EAAIhD,EAAOgD,QAAQ,CAAG,CAAC,EACpEP,GAAsBS,IACtBD,CAAAA,EAAYC,EAAOxE,MAAM,CAAC,SAAU9b,CAAG,CAAEugB,CAAI,EACzC,IAAI7F,EACA8F,EACApf,EAgBJ,OAfI2e,GAAuBQ,IAAST,GAAuBS,EAAK7F,KAAK,IAEjE8F,EAAkBP,GAAmB7e,AADrCA,CAAAA,EAAU4e,GAAoB,CAAC,EAAGO,EAAI,EACOC,eAAe,CAAEJ,EAASI,eAAe,EAEtF,OAAOpf,EAAQof,eAAe,CAC9B,OAAOpf,EAAQsZ,KAAK,CAGhBqF,GAAuB/f,CAAG,CAD9B0a,EAAQ6F,EAAK7F,KAAK,CAAI8F,CAAAA,EAAkB,EAAIrc,EAAO,CAAA,EACd,EACjC6b,GAAoB,CAAA,EAAMhgB,CAAG,CAAC0a,EAAM,CAAEtZ,GAGtCpB,CAAG,CAAC0a,EAAM,CAAGtZ,GAGdpB,CACX,EAAG,CAAC,EAAC,EAEToE,EAAK0b,GAAuB1C,EAAOhZ,EAAE,EAAIgZ,EAAOhZ,EAAE,CAAG,EAChDrC,EAAI,EAAGA,GAAKqC,EAAIrC,IACjBib,CAAM,CAACjb,EAAE,CAAGie,GAAoB,CAAC,EAAGI,EAAUL,GAAuBM,CAAS,CAACte,EAAE,EAAIse,CAAS,CAACte,EAAE,CAAG,CAAC,GAG7G,OAAOib,CACX,GA4IIyD,GAAwB,AAACjgB,IAA+EG,QAAQ,CAAE+f,GAAuB,AAAClgB,IAA+EM,OAAO,CAAE6f,GAAQ,AAACngB,IAA+EmgB,KAAK,CAAEC,GAAoB,AAACpgB,IAA+EI,IAAI,CAAEigB,GAAyB,AAACrgB,IAA+EK,SAAS,CAAEigB,GAAwB,AAACtgB,IAA+EiK,QAAQ,CAAEsW,GAAW,AAACvgB,IAA+EugB,QAAQ,CAAEC,GAAqB,AAACxgB,IAA+E6J,KAAK,CAAE4W,GAAoB,AAACzgB,IAA+EQ,IAAI,CAAEkgB,GAA2B,AAAC1gB,IAA+E4b,WAAW,CAAE+E,GAAoB,AAAC3gB,IAA+EgK,IAAI,CAehrC,SAAS4W,GAAiBpG,CAAI,CAAE/R,CAAG,EAC/B,IAAI7E,EAAK4W,EAAKqG,WAAW,EAAI,EACzBld,EAAO6W,EAAKsG,aAAa,EAAI,EASjC,OAHIld,GAAM6E,GACN9E,CAAAA,GAAQ,EAAE,EAEP,CACHA,KAAMA,EACNC,GAAIA,EACJ6C,WAAY,CAAA,CAChB,CACJ,CA0BA,SAASsa,GAAoB5K,CAAI,CAAE6K,CAAW,CAAEC,CAAc,EAC1D,IAsFwCrG,EAE5BsG,EAxFRjT,EAAa,EAAE,CACfkT,EAAiB,EAAE,CACnBC,EAAgB,CAAC,EACjBC,EAAqBL,GAAe,CAAA,EACpC7V,EAAqB,CAAC,EACtBmW,EAAc,GAoHdC,EAAOrG,GAAWC,OAAO,CAAChF,EAlHb,CAETmE,MAAO,SAAUE,CAAI,EACjB,IAAIgH,EAAWrW,CAAkB,CAACqP,EAAKjQ,GAAG,CAAC,CAC3CD,EAAS,EACToQ,EAAc,EAClB8G,EAAS7G,QAAQ,CAACzX,OAAO,CAAC,SAAU2X,CAAK,EACrCH,GAAe,AAACG,CAAAA,EAAMH,WAAW,EAAI,CAAA,EAAK,EAC1CpQ,EAASS,KAAKtC,GAAG,CAAC,AAACoS,CAAAA,EAAMvQ,MAAM,EAAI,CAAA,EAAK,EAAGA,EAC/C,GACAkX,EAAS9G,WAAW,CAAGA,EACvB8G,EAASlX,MAAM,CAAGA,EACdkX,EAAStE,SAAS,EAClBiE,EAAezf,IAAI,CAAC8f,EAE5B,EAEAjH,OAAQ,SAAUC,CAAI,EAClB,IAaIgH,EACAjX,EAdA4L,EAAOmK,GAAsB9F,EAAKrE,IAAI,CACtC,CAAA,GACIqE,EAAKrE,IAAI,CACT,CAAC,EACLsL,EAAOlB,GAASpK,EAAKsL,IAAI,EAAItL,EAAKsL,IAAI,CAAG,GACzCC,EAAaN,CAAa,CAAC5G,EAAKP,MAAM,CAAC,CACvC0H,EAAkBrB,GAAsBoB,EACxC,CAAA,GACIvW,CAAkB,CAACuW,EAAWnX,GAAG,CAAC,CAClC,IAOJ8W,CAAAA,GACAf,GAAsBqB,EAAgB,CAAA,IACnCH,CAAAA,EAAWpB,GAAkBuB,EAAehH,QAAQ,CARzC,SAAUnY,CAAC,EACrB,OAAOA,EAAEif,IAAI,GAAKA,CAC1B,EAMwE,GAEpElX,EAAMiX,EAASjX,GAAG,CAElBiX,EAASI,KAAK,CAAClgB,IAAI,CAAC8Y,IAIpBjQ,EAAM+W,IAGN,CAACnW,CAAkB,CAACZ,EAAI,GACxBY,CAAkB,CAACZ,EAAI,CAAGiX,EAAW,CACjC/G,MAAOkH,EAAiBA,EAAelH,KAAK,CAAG,EAAI,EACnDgH,KAAMA,EACNzH,GAAI7D,EAAK6D,EAAE,CACX4H,MAAO,CAACpH,EAAK,CACbG,SAAU,EAAE,CACZpQ,IAAKA,CACT,EAEY,KAARA,GACA0D,EAAWvM,IAAI,CAAC+f,GAGhBnB,GAAsBqB,EAAgB,CAAA,IACtCA,EAAehH,QAAQ,CAACjZ,IAAI,CAAC8f,IAIjCjB,GAAS/F,EAAKR,EAAE,GAChBoH,CAAAA,CAAa,CAAC5G,EAAKR,EAAE,CAAC,CAAGQ,CAAG,EAI5BgH,GACArL,AAAmB,CAAA,IAAnBA,EAAK+G,SAAS,EACdsE,CAAAA,EAAStE,SAAS,CAAG,CAAA,CAAG,EAG5B1C,EAAKjQ,GAAG,CAAGA,CACf,CACJ,GAyCA,OAxCwCqQ,EAsCKzP,EAEtC,CACH8C,WAAYA,EACZmT,cAAeA,EACfjW,mBALJA,EANW+V,CA9BCA,EAAY,SAAUM,CAAQ,CACtCpH,CAAK,CACLoC,CAAM,EACE,IAAIoF,EAAQJ,EAASI,KAAK,CAE1BvH,EAAMD,EAASA,CAAAA,AAAU,KAAVA,EAAe,EAAI6G,AA+BmBA,EA/BF,CAAA,EACnDY,EAAO,AAACxH,CAAAA,EAAMD,CAAI,EAAK,EACvB7P,EAAM6P,EAAQyH,EAqBlB,OApBAD,EAAM1e,OAAO,CAAC,SAAUsX,CAAI,EACxB,IAAIrE,EAAOqE,EAAKrE,IAAI,CAChBmK,GAAsBnK,EAAM,CAAA,KAE5BA,EAAK5T,CAAC,CAAG6X,EAASjE,CAAAA,EAAK2L,WAAW,EAAI,CAAA,EAEtC,OAAO3L,EAAK2L,WAAW,EAE3BtH,EAAKjQ,GAAG,CAAGA,CACf,GACAiS,CAAM,CAACjS,EAAI,CAAGiX,EACdA,EAASjX,GAAG,CAAGA,EACfiX,EAASnR,cAAc,CAAGwR,EAhBpB,GAiBNL,EAASV,aAAa,CAAGzG,EAjBnB,GAkBNmH,EAAS7G,QAAQ,CAACzX,OAAO,CAAC,SAAU2X,CAAK,EACrCqG,EAAUrG,EAAOR,EAAM,EAAGmC,GAC1BnC,EAAM,AAACQ,CAAAA,EAAMgG,WAAW,EAAI,CAAA,EApB1B,EAqBN,GAEAW,EAASX,WAAW,CAAGxG,EAvBjB,GAwBCmC,CACX,GACiB5B,CAAG,CAAC,KAAK,CAAE,GAAI,CAAC,GAYjCuG,eAAgBA,EAChBI,KAAMA,CACV,CACJ,CAQA,SAASQ,GAAe5V,CAAC,EACrB,IAAIvF,EAAQuF,EAAE6V,MAAM,CAEpBrT,AADW/H,EAAM+H,IAAI,CAChBpL,MAAM,CAAC,SAAUvC,CAAI,EAAI,MAAOA,AAAc,aAAdA,EAAKiK,IAAI,AAAiB,GAAG/H,OAAO,CAAC,SAAUlC,CAAI,EAEpF,IADID,EAgBAoV,EACAjL,EAhBAtK,EAAUI,EAAKJ,OAAO,EAAI,CAAC,EAC3Bwb,EAAexb,EAAQyK,MAAM,CAC7B2V,EAAchgB,EAAKggB,WAAW,CAC9BvY,EAAM7B,EAAMD,IAAI,CAACE,KAAK,CAACjG,EAAQ6H,GAAG,EAKlC1G,EAAW,CAACf,EAAKkK,QAAQ,CAACC,kBAAkB,EACxCnK,EAAKgC,MAAM,CAACif,IAAI,CAAC,SAAUjf,CAAM,EAC7B,MAAO,CAACA,EAAOiO,WAAW,EACtBjO,EAAOkf,WAAW,EAClBlf,EAAOjB,OAAO,AAC1B,GACAkf,EAAiB,EAGrB,GAAIlf,EAAS,CACT,IAAIogB,EAA6B,EAAE,CAsCnC,GApCAhM,EAAOnV,EAAKgC,MAAM,CAACsY,MAAM,CAAC,SAAU8G,CAAG,CAAE7I,CAAC,EACtC,IAAI8I,EAAc9I,EAAE3Y,OAAO,CAACuV,IAAI,EAAI,EAAE,CAClCmM,EAAaD,CAAU,CAAC,EAAE,CAG1BE,EAAuB1K,MAAMvX,OAAO,CAACgiB,IACjC,CAACA,EAAWliB,IAAI,CAAC,SAAUsI,CAAK,EAAI,MAAQ,AAAiB,UAAjB,OAAOA,CAAqB,GA0BhF,OAzBAyZ,EAA2BzgB,IAAI,CAAC6gB,GAC5BhJ,EAAE9W,OAAO,GAET4f,EAAWnf,OAAO,CAAC,SAAUsf,CAAY,EACrC,IAAIzhB,EAGAwhB,CAAAA,GAAwB,CAAA,AAA0B,OAAzBxhB,CAAAA,EAAKwY,EAAE3Y,OAAO,CAACkC,IAAI,AAAD,GAAe/B,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGS,MAAM,AAAD,CAAC,IAC5FghB,EAAejJ,EAAE9C,UAAU,CAAC/W,SAAS,CAChC+iB,eAAe,CACf7iB,IAAI,CAAC,CAAEoD,OAAQuW,CAAE,EAAGiJ,GACzBjJ,EAAE9C,UAAU,CAACC,oBAAoB,CAAC8L,EAAc5b,IAEhD0Z,GAAsBkC,EAAc,CAAA,KAGpCA,EAAaV,WAAW,CAAIb,EAC5BmB,EAAI1gB,IAAI,CAAC8gB,GAEjB,GAEoB,CAAA,IAAhBxB,GACAC,KAGDmB,CACX,EAAG,EAAE,EAGD3Z,GAAO0N,EAAK3U,MAAM,CAAGiH,EACrB,IAAK,IAAIlH,EAAI4U,EAAK3U,MAAM,CAAED,GAAKkH,EAAKlH,IAChC4U,EAAKzU,IAAI,CAAC,CAGN+f,KAAMlgB,EAAI,GACd,EAORP,CAAAA,EAAKiN,UAAU,CAAG/C,AAFlBA,CAAAA,EAAW6V,GAAoB5K,EAAM6K,GAAe,CAAA,EAAO,AAACA,AAAgB,CAAA,IAAhBA,EAAwBC,EAAiB,EAAC,EAE3EhT,UAAU,CACrCjN,EAAKkK,QAAQ,CAACC,kBAAkB,CAAID,EAASC,kBAAkB,CAC/DnK,EAAK0hB,QAAQ,CAAG,CAAA,EAChB1hB,EAAKkK,QAAQ,CAACqW,IAAI,CAAGrW,EAASqW,IAAI,CAElCvgB,EAAKgC,MAAM,CAACE,OAAO,CAAC,SAAUF,CAAM,CAAEsS,CAAK,EACvC,IAAIqN,EAAW,AAAC3f,CAAAA,EAAOpC,OAAO,CAACuV,IAAI,EAAI,EAAE,AAAD,EAAGyE,GAAG,CAAC,SAAU7b,CAAC,EAetD,MAdQojB,CAAAA,CAA0B,CAAC7M,EAAM,EAChC4K,GAAqBnhB,IAClBiE,EAAOpC,OAAO,CAACkC,IAAI,EACnBE,EAAOpC,OAAO,CAACkC,IAAI,CAACtB,MAAM,GAG9B2U,EAAKjT,OAAO,CAAC,SAAUb,CAAK,EACxB,IAAIugB,EAAUzC,GAAMphB,GACpB6jB,EAAQnJ,OAAO,CAACpX,EAAMG,CAAC,EAAI,IAAM,GACjCogB,EAAQnJ,OAAO,CAACpX,EAAMwgB,EAAE,EAAI,IAAM,GAClC9jB,CAAAA,EAAIsD,CAAI,CAEhB,GAEGie,GAAsBvhB,EAAG,CAAA,GAAQyhB,GAAmBzhB,GAAKA,CACpE,EAEIiE,CAAAA,EAAOP,OAAO,EACdO,EAAO8f,OAAO,CAACH,EAAU,CAAA,EAEjC,GAEA3hB,EAAKkK,QAAQ,CAAC6X,iBAAiB,CAC3BrD,GAA6B,CACzBE,SAAUxD,EACVzY,KAAM,EACNmc,OAAQ1D,MAAAA,EAAmD,KAAK,EAAIA,EAAa0D,MAAM,CACvFlc,GAAI,AAA8B,OAA7B7C,CAAAA,EAAKC,EAAKkK,QAAQ,CAACqW,IAAI,AAAD,GAAexgB,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGuJ,MAAM,AAChF,GAEW,iBAAX6B,EAAElB,IAAI,EACNjK,CAAAA,EAAKkK,QAAQ,CAACiW,cAAc,CAAGjW,EAASiW,cAAc,AAAD,CAE7D,CACJ,EACJ,CAaA,SAAS6B,GAAiBxX,CAAO,CAAEjB,CAAG,EAClC,IAKI0Y,EACAriB,EACA4gB,EANAuB,EAAoB/hB,AADb,IAAI,CACckK,QAAQ,CAAC6X,iBAAiB,EAAI,CAAC,EACxDxG,EAAavb,AAAc,aAAdA,AAFN,IAAI,CAEOiK,IAAI,CACtBd,EAAQnJ,AAHD,IAAI,CAGEmJ,KAAK,CAClBM,EAAON,CAAK,CAACI,EAAI,AAIjBgS,CAAAA,GACAvb,AATO,IAAI,CASNkK,QAAQ,CAACC,kBAAkB,EAEhC8X,CAAAA,EAAeF,CAAiB,CAACvB,AADjCA,CAAAA,EAAWxgB,AAVJ,IAAI,CAUKkK,QAAQ,CAACC,kBAAkB,CAACZ,EAAI,AAAD,EACLkQ,KAAK,CAAC,AAAD,GAE3C7Z,CAAAA,EAAU,CACNyK,OAAQ4X,CACZ,CAAA,EAEA,CAACxY,GACDpM,EACA8L,CAAK,CAACI,EAAI,CAAGE,EACT,IAAIpM,EApBL,IAAI,CAoBuBkM,EAAK,KAAK,EAAG,KAAK,EAAG,CAC3C2Y,SAAU1B,EAASC,IAAI,CACvBpR,eAAgBmR,EAASnR,cAAc,CACvCzP,QAASA,CACb,IAIJ6J,EAAK0Y,UAAU,CAACD,QAAQ,CAAG1B,EAASC,IAAI,CACxChX,EAAK7J,OAAO,CAAGA,EACf6J,EAAK2Y,QAAQ,KAIjB5X,EAAQI,KAAK,CAlCN,IAAI,CAkCSiM,MAAMnY,SAAS,CAACyE,KAAK,CAACvE,IAAI,CAACgY,UAAW,GAElE,CAIA,SAASyL,GAAS7X,CAAO,CAAE5E,CAAK,CAAEE,CAAW,CAAE8H,CAAI,EAC/C,IAAI5N,EAAO,IAAI,CACXub,EAAazV,AAAqB,aAArBA,EAAYmE,IAAI,AAC5BjK,CAAAA,EAAKkK,QAAQ,EACdlK,CAAAA,EAAKkK,QAAQ,CAAG,IAAIoY,GAAsBtiB,EAAI,EAG9Cub,IAGA0D,GAAsBrZ,EAAO,eAAgBmb,IAC7C9B,GAAsBrZ,EAAO,eAAgBmb,IAE7C9B,GAAsBrZ,EAAO,YAAa,SAAUuF,CAAC,EACjD,GAAIA,EAAEvL,OAAO,CAACuV,IAAI,CAAE,CAChB,IAAIjL,EAAW6V,GAAoB5U,EAAEvL,OAAO,CAACuV,IAAI,CAC7CrP,EAAYka,WAAW,EAAI,CAAA,EAAO,EACtChgB,CAAAA,EAAKkK,QAAQ,CAACiW,cAAc,CAAG,AAACngB,CAAAA,EAAKkK,QAAQ,CAACiW,cAAc,EAAI,EAAE,AAAD,EAAGoC,MAAM,CAACrY,EAASiW,cAAc,CACtG,CACJ,GAGAlB,GAAsBjf,EAAM,gBAAiB,WACrCA,EAAKkK,QAAQ,CAACiW,cAAc,EAC5BngB,EAAKkK,QAAQ,CAACiW,cAAc,CAACje,OAAO,CAAC,SAAUsX,CAAI,EAC/C,IAAI3Z,EAASG,EAAKkK,QAAQ,CAAC+T,QAAQ,CAACzE,EAChCxZ,CAAAA,EAAKN,UAAU,GACfM,EAAKN,UAAU,CAACC,SAAS,CAACE,EAAQ,CAAA,GAE9BG,EAAKkK,QAAQ,CAACiW,cAAc,EAC5BngB,CAAAA,EAAKkK,QAAQ,CAACiW,cAAc,CAAGngB,EAAKkK,QAAQ,CACvCiW,cAAc,CACd5d,MAAM,CAAC,SAAU3E,CAAC,EAAI,OAAQ,AAAC4b,EAAKsG,aAAa,GAClDliB,EAAEkiB,aAAa,EACftG,EAAKqG,WAAW,GAAKjiB,EAAEiiB,WAAW,AAAG,EAAC,EAGtD,EAER,GAIAZ,GAAsBjf,EAAM,cAAe,WACrB,UAAdA,EAAK4N,IAAI,EACT,CAAC5N,EAAK+H,WAAW,EACjB/H,EAAK4F,KAAK,CAAChG,OAAO,CAACgG,KAAK,CAAC0D,MAAM,EAC/BtJ,CAAAA,EAAKe,OAAO,CAAG,CAAA,CAAG,CAE1B,GACA+E,EAAc0Z,GAAmB,CAE7B/U,KAAM,CACFY,QAAS,CAAA,CACb,EAEAhB,OAAQ,CACJ2C,MAAO,OAcP8R,OAAQ,CAAC,CAWD5F,MAAO,KAAK,CAChB,EAAG,CACCA,MAAO,EAQPjL,MAAO,CAEHuU,WAAY,MAChB,CACJ,EAAE,CAUN/G,OAAQ,CASJxR,KAAM,WACNzI,EAAG,GACHD,EAAG,GACH+H,OAAQ,GACRD,MAAO,EACX,CACJ,EACA2W,YAAa,CAAA,CACjB,EAAGla,EAAa,CAEZoO,SAAU,CAAA,CACd,IAIJ1J,EAAQI,KAAK,CAAC5K,EAAM,CAAC4F,EAAOE,EAAa8H,EAAK,EAC1C2N,IACAvb,EAAK0hB,QAAQ,CAAG,CAAA,EAChB1hB,EAAKJ,OAAO,CAACsN,aAAa,CAAG,CAAA,EAErC,CAUA,SAASuV,GAAoBjY,CAAO,EAMhC,IALIzK,EACAe,EACAE,EACA0a,EACAgH,EAEA9iB,EAAUI,AADH,IAAI,CACIJ,OAAO,CACtB+F,EAAO3F,AAFA,IAAI,CAEC4F,KAAK,CAACD,IAAI,CACtBuK,EAAe,AAA4B,UAA5B,OAAOtQ,EAAQ6N,QAAQ,CAClC,AAAiC,OAAhC1N,CAAAA,EAAK,IAAI,CAAC6F,KAAK,CAAC5F,AAJd,IAAI,CAIe4N,IAAI,CAAC,AAAD,GAAe7N,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,CAAE,CAACH,EAAQ6N,QAAQ,CAAC,CACtF,KAAK,EAEb,GADiBzN,AAAc,aAAdA,AANN,IAAI,CAMOiK,IAAI,CACV,CAYZ,GAXAjK,AARO,IAAI,CAQNqC,GAAG,CAAG,AAAwF,OAAvFrB,CAAAA,EAAK,AAAwB,OAAvBF,CAAAA,EAAKd,AARhB,IAAI,CAQiBsH,OAAO,AAAD,GAAexG,AAAO,KAAK,IAAZA,EAAgBA,EAAK6E,EAAKE,KAAK,CAACjG,EAAQyC,GAAG,CAAA,GAAerB,AAAO,KAAK,IAAZA,EAAgBA,EAAKhB,AARzH,IAAI,CAQ0H2iB,OAAO,CAC5I3iB,AATO,IAAI,CASNyH,GAAG,CAAG,AAAwF,OAAvFib,CAAAA,EAAK,AAAwB,OAAvBhH,CAAAA,EAAK1b,AAThB,IAAI,CASiBwH,OAAO,AAAD,GAAekU,AAAO,KAAK,IAAZA,EAAgBA,EAAK/V,EAAKE,KAAK,CAACjG,EAAQ6H,GAAG,CAAA,GAAeib,AAAO,KAAK,IAAZA,EAAgBA,EAAK1iB,AATzH,IAAI,CAS0H4iB,OAAO,CAC5IvD,GAVO,IAAI,CAUkB,iBAG7Brf,AAbO,IAAI,CAaN0G,kBAAkB,GACvB1G,AAdO,IAAI,CAcNsR,YAAY,CAAG,EACpBtR,AAfO,IAAI,CAeNqP,cAAc,CAAG,GACtBrP,AAhBO,IAAI,CAgBNI,aAAa,CAAGJ,AAhBd,IAAI,CAgBekK,QAAQ,CAACC,kBAAkB,CACjDnK,AAjBG,IAAI,CAiBFkK,QAAQ,CAAC2Y,gBAAgB,GAC9B,EAAE,CACF3S,EAAc,CACd,IAAI4S,EAAuB5S,EAAa6S,WAAW,EACnD/iB,CArBG,IAAI,CAqBFqC,GAAG,CAAGod,GAAkBqD,EAAqBzgB,GAAG,CAAEygB,EAAqBH,OAAO,EACnF3iB,AAtBG,IAAI,CAsBFyH,GAAG,CAAGgY,GAAkBqD,EAAqBrb,GAAG,CAAEqb,EAAqBF,OAAO,EACnF5iB,AAvBG,IAAI,CAuBFI,aAAa,CAAG8P,EAAa9P,aAAa,AACnD,CACAJ,AAzBO,IAAI,CAyBNkQ,YAAY,CAAGA,CACxB,MAEI1F,EAAQI,KAAK,CA5BN,IAAI,CA4BSiM,MAAMnY,SAAS,CAACyE,KAAK,CAACvE,IAAI,CAACgY,UAAW,GAElE,CAUA,SAASoM,GAAWxY,CAAO,EACvB,IAAIxK,EAAO,IAAI,AACgB,CAAA,aAAd,IAAI,CAACiK,IAAI,EACRjK,EAAKyB,OAAO,EAC1BzB,EAAKI,aAAa,CAAC8B,OAAO,CAAC,SAAUqH,CAAG,EAEpC,IADIxJ,EACA0J,EAAOzJ,EAAKmJ,KAAK,CAACI,EAAI,CACtB,CAAA,AAAsB,OAArBxJ,CAAAA,EAAK0J,EAAKD,KAAK,AAAD,GAAezJ,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG6d,sBAAsB,AAAD,IAC/E8B,GAAyBjW,EAAKD,KAAK,CAACgH,OAAO,EAC3C/G,EAAKD,KAAK,CAACoU,sBAAsB,CAAG,CAAA,EAE5C,GAEJpT,EAAQI,KAAK,CAAC5K,EAAM6W,MAAMnY,SAAS,CAACyE,KAAK,CAACvE,IAAI,CAACgY,UAAW,GAC9D,CAUA,IAAI0L,GAAuC,WASvC,SAASA,EAAsBtiB,CAAI,EAC/B,IAAI,CAACA,IAAI,CAAGA,CAChB,CA+LA,OAtLAsiB,EAAsBpe,OAAO,CAAG,SAAUC,CAAS,CAAE8e,CAAU,CAAE7e,CAAW,CAAE2Z,CAAS,EACnF,GAAI,CAAC5Z,EAAUE,SAAS,CAACC,QAAQ,CAAC,YAAa,CAC3C,IA76EKH,EAAW8e,EAAYlF,EA66ExBmF,EAAY/e,EAAUzF,SAAS,CACnCyF,EAAUE,SAAS,CAAC3D,IAAI,CAAC,YACzBif,GAAkBuD,EAAW,eAAgBlB,IAC7CrC,GAAkBuD,EAAW,OAAQb,IACrC1C,GAAkBuD,EAAW,kBAAmBT,IAChD9C,GAAkBuD,EAAW,SAAUF,IAEvCE,EAAUC,KAAK,CAAG,CACdpK,QAASmB,GAAWnB,OAAO,AAC/B,EACK1b,GACDA,CAAAA,EAAkB0gB,CAAQ,CAElC,CAIA,OA97ES5Z,EA27EaA,EA37EF8e,EA27EaA,EA37EDlF,EA27EaA,EA17E5C5Z,EAAUE,SAAS,CAACC,QAAQ,CAAC,UAC9BH,EAAUE,SAAS,CAAC3D,IAAI,CAAC,QACzByD,EAAUzF,SAAS,CAACwK,qBAAqB,CAAGA,EAC5CF,EAAK7E,EAAUzF,SAAS,CAAE,WAAYgY,GACtC1N,EAAK7E,EAAUzF,SAAS,CAAE,YAAa6L,GAEvChC,EAAkBpE,EAAW,OAAQ0P,GACrCtL,EAAkBpE,EAAW,wBAAyB+G,GACtD3C,EAAkBpE,EAAW,YAAa4I,GAC1CxE,EAAkBpE,EAAW,cAAe0J,GAC5CtF,EAAkBpE,EAAW,0BAA2B2M,GACxDvI,EAAkBpE,EAAW,kBAAmBoN,GAChDhJ,EAAkBpE,EAAW,kBAAmBgP,GAChD5K,EAAkBpE,EAAW,gBAAiBiP,GAC9C7K,EAAkBpE,EAAW,gBAAiBmP,GAC9C/K,EAAkBpE,EAAW,YAAawR,GAC1CpN,EAAkBpE,EAAW,UAAWwP,GACxCpL,EAAkB0a,EAAY,oBAAqBxP,GACnDlL,EAAkBwV,EAAW,wBAAyB9J,GACtD1L,EAAkBwV,EAAW,cAAe/I,IAw6E5C7M,EAAgBjE,OAAO,CAACC,EAAWC,GACnCgf,AAt4ByClI,GAs4B5BhX,OAAO,CAAC6Z,GACd5Z,CACX,EAiBAme,EAAsB5jB,SAAS,CAAC2kB,kBAAkB,CAAG,SAAU7J,CAAI,EAC/D,IAAIxZ,EAAO,IAAI,CAACA,IAAI,CAChB4F,EAAQ5F,EAAK4F,KAAK,CACtB5F,EAAKgC,MAAM,CAACE,OAAO,CAAC,SAAUF,CAAM,EAChC,IAAImT,EAAOnT,EAAOpC,OAAO,CAACuV,IAAI,CAC9B,GAAIqE,EAAKR,EAAE,EAAI7D,EAAM,CACjB,IAAI9T,EAAQuE,EAAMrH,GAAG,CAACib,EAAKR,EAAE,EACzBsK,EAAYnO,CAAI,CAACnT,EAAOmT,IAAI,CAACsD,OAAO,CAACpX,GAAO,CAC5CA,GAASiiB,IACTjiB,EAAM6a,SAAS,CAAG1C,EAAK0C,SAAS,CAChCoH,EAAUpH,SAAS,CAAG1C,EAAK0C,SAAS,CAE5C,CACJ,EACJ,EAkBAoG,EAAsB5jB,SAAS,CAACuf,QAAQ,CAAG,SAAUzE,CAAI,EACrD,IAAIxZ,EAAO,IAAI,CAACA,IAAI,CAChBH,EAAUG,EAAKJ,OAAO,CAACC,MAAM,EAAI,EAAE,CACnCrB,EAAMohB,GAAiBpG,EACvBxZ,EAAKyH,GAAG,EAKZ,OAJA5H,EAAOa,IAAI,CAAClC,GAEZgb,EAAK0C,SAAS,CAAG,CAAA,EACjBlc,EAAKkK,QAAQ,CAACmZ,kBAAkB,CAAC7J,GAC1B3Z,CACX,EAkBAyiB,EAAsB5jB,SAAS,CAACwf,MAAM,CAAG,SAAU1E,CAAI,EACnD,IAAIxZ,EAAO,IAAI,CAACA,IAAI,CAChBH,EAAUG,EAAKJ,OAAO,CAACC,MAAM,EAAI,EAAE,CACnCrB,EAAMohB,GAAiBpG,EACvBxZ,EAAKyH,GAAG,EAKZ,OAHA+R,EAAK0C,SAAS,CAAG,CAAA,EACjBlc,EAAKkK,QAAQ,CAACmZ,kBAAkB,CAAC7J,GAE1B3Z,EAAOya,MAAM,CAAC,SAAU8G,CAAG,CAAE/b,CAAC,EAIjC,MAHIA,CAAAA,EAAEzC,EAAE,GAAKpE,EAAIoE,EAAE,EAAIyC,EAAE1C,IAAI,GAAKnE,EAAImE,IAAI,AAAD,GACrCye,EAAI1gB,IAAI,CAAC2E,GAEN+b,CACX,EAAG,EAAE,CACT,EAUAkB,EAAsB5jB,SAAS,CAACmkB,gBAAgB,CAAG,WAC/C,IAAI7iB,EAAO,IAAI,CAACA,IAAI,CAAEujB,EAAaxZ,KAAKoO,KAAK,CAACnY,EAAKqC,GAAG,CAAGrC,EAAKsR,YAAY,EAAItR,EAAKsR,YAAY,CAAEkS,EAAazZ,KAAK0Z,IAAI,CAACzjB,EAAKyH,GAAG,CAAGzH,EAAKsR,YAAY,EAAItR,EAAKsR,YAAY,CACzK,OAAOlT,OAAO0D,IAAI,CAAC9B,EAAKkK,QAAQ,CAACC,kBAAkB,EAAI,CAAC,GAAGmQ,MAAM,CAAC,SAAU8G,CAAG,CAAEljB,CAAG,EAEhF,IADI6B,EACAwJ,EAAM,CAACrL,EAMX,OALIqL,GAAOga,GACPha,GAAOia,GACP,CAAE,CAAA,AAA2B,OAA1BzjB,CAAAA,EAAKC,EAAKN,UAAU,AAAD,GAAeK,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAGU,YAAY,CAAC8I,EAAG,GACjF6X,EAAI1gB,IAAI,CAAC6I,GAEN6X,CACX,EAAG,EAAE,CACT,EAkBAkB,EAAsB5jB,SAAS,CAACse,WAAW,CAAG,SAAUxD,CAAI,EACxD,IAAIxZ,EAAO,IAAI,CAACA,IAAI,CAChBH,EAAUG,EAAKJ,OAAO,CAACC,MAAM,EAAI,EAAE,CACnCrB,EAAMohB,GAAiBpG,EACvBxZ,EAAKyH,GAAG,EACZ,OAAO5H,EAAOohB,IAAI,CAAC,SAAU5b,CAAC,EAC1B,OAAOA,EAAE1C,IAAI,GAAKnE,EAAImE,IAAI,EAAI0C,EAAEzC,EAAE,GAAKpE,EAAIoE,EAAE,AACjD,EACJ,EAiBA0f,EAAsB5jB,SAAS,CAACof,cAAc,CAAG,SAAUtE,CAAI,EAC3D,OAAQ,IAAI,CAACwD,WAAW,CAACxD,GACrB,IAAI,CAAC0E,MAAM,CAAC1E,GACZ,IAAI,CAACyE,QAAQ,CAACzE,EACtB,EACO8I,CACX,IAaIoB,GAAK1kB,IACT2kB,AARiDrB,GAQpCpe,OAAO,CAACwf,GAAEE,IAAI,CAAEF,GAAEG,KAAK,CAAEH,GAAEI,MAAM,CAAEJ,GAAEK,IAAI,EACzB,IAAIjlB,GAAiBE,IAGxC,OADYH,EAAoB,OAAU,AAE3C,GAET"}