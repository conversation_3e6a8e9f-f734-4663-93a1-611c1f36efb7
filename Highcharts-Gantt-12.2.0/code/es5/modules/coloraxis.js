!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("highcharts"),require("highcharts").Axis,require("highcharts").Color,require("highcharts").LegendSymbol,require("highcharts").SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/modules/coloraxis",[["highcharts/highcharts"],["highcharts/highcharts","Axis"],["highcharts/highcharts","Color"],["highcharts/highcharts","LegendSymbol"],["highcharts/highcharts","SeriesRegistry"]],e):"object"==typeof exports?exports["highcharts/modules/coloraxis"]=e(require("highcharts"),require("highcharts").Axis,require("highcharts").Color,require("highcharts").LegendSymbol,require("highcharts").SeriesRegistry):t.Highcharts=e(t.Highcharts,t.Highcharts.Axis,t.Highcharts.Color,t.Highcharts.LegendSymbol,t.Highcharts.SeriesRegistry)}(this,function(t,e,i,o,s){return function(){"use strict";var r,n,a,l,h={500:function(t){t.exports=o},512:function(t){t.exports=s},532:function(t){t.exports=e},620:function(t){t.exports=i},944:function(e){e.exports=t}},c={};function d(t){var e=c[t];if(void 0!==e)return e.exports;var i=c[t]={exports:{}};return h[t](i,i.exports,d),i.exports}d.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return d.d(e,{a:e}),e},d.d=function(t,e){for(var i in e)d.o(e,i)&&!d.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},d.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};var p={};d.d(p,{default:function(){return Y}});var u=d(944),f=d.n(u),g=d(532),y=d.n(g),m=d(620),v=d.n(m),x=v().parse,b=f().addEvent,C=f().extend,A=f().merge,I=f().pick,L=f().splat;!function(t){var e;function i(){var t=this,i=this.userOptions;this.colorAxis=[],i.colorAxis&&(i.colorAxis=L(i.colorAxis),i.colorAxis.map(function(i){return new e(t,i)}))}function o(t){var e,i,o=this,s=this.chart.colorAxis||[],r=function(e){var i=t.allItems.indexOf(e);-1!==i&&(o.destroyItem(t.allItems[i]),t.allItems.splice(i,1))},n=[];for(s.forEach(function(t){(null==(e=t.options)?void 0:e.showInLegend)&&(e.dataClasses&&e.visible?n=n.concat(t.getDataClassLegendSymbols()):e.visible&&n.push(t),t.series.forEach(function(t){(!t.options.showInLegend||e.dataClasses)&&("point"===t.options.legendType?t.points.forEach(function(t){r(t)}):r(t))}))}),i=n.length;i--;)t.allItems.unshift(n[i])}function s(t){t.visible&&t.item.legendColor&&t.item.legendItem.symbol.attr({fill:t.item.legendColor})}function r(t){var e;null===(e=this.chart.colorAxis)||void 0===e||e.forEach(function(e){e.update({},t.redraw)})}function n(){var t;((null===(t=this.chart.colorAxis)||void 0===t?void 0:t.length)||this.colorAttribs)&&this.translateColors()}function a(){var t=this.axisTypes;t?-1===t.indexOf("colorAxis")&&t.push("colorAxis"):this.axisTypes=["colorAxis"]}function l(t){var e=this,i=t?"show":"hide";e.visible=e.options.visible=!!t,["graphic","dataLabel"].forEach(function(t){e[t]&&e[t][i]()}),this.series.buildKDTree()}function h(){var t=this,e=this.getPointsCollection(),i=this.options.nullColor,o=this.colorAxis,s=this.colorKey;e.forEach(function(e){var r=e.getNestedProperty(s),n=e.options.color||(e.isNull||null===e.value?i:o&&void 0!==r?o.toColor(r,e):e.color||t.color);n&&e.color!==n&&(e.color=n,"point"===t.options.legendType&&e.legendItem&&e.legendItem.label&&t.chart.legend.colorizeItem(e,e.visible))})}function c(){this.elem.attr("fill",x(this.start).tweenTo(x(this.end),this.pos),void 0,!0)}function d(){this.elem.attr("stroke",x(this.start).tweenTo(x(this.end),this.pos),void 0,!0)}t.compose=function(t,p,u,f,g){var y,m,v=p.prototype,x=u.prototype,L=g.prototype;v.collectionsWithUpdate.includes("colorAxis")||(e=t,v.collectionsWithUpdate.push("colorAxis"),v.collectionsWithInit.colorAxis=[v.addColorAxis],b(p,"afterCreateAxes",i),m=(y=p).prototype.createAxis,y.prototype.createAxis=function(t,i){if("colorAxis"!==t)return m.apply(this,arguments);var o=new e(this,A(i.axis,{index:this[t].length,isX:!1}));return this.isDirtyLegend=!0,this.axes.forEach(function(t){t.series=[]}),this.series.forEach(function(t){t.bindAxes(),t.isDirtyData=!0}),I(i.redraw,!0)&&this.redraw(i.animation),o},x.fillSetter=c,x.strokeSetter=d,b(f,"afterGetAllItems",o),b(f,"afterColorizeItem",s),b(f,"afterUpdate",r),C(L,{optionalAxis:"colorAxis",translateColors:h}),C(L.pointClass.prototype,{setVisible:l}),b(g,"afterTranslate",n,{order:1}),b(g,"bindAxes",a))},t.pointSetVisible=l}(a||(a={}));var w=a,S=v().parse,O=f().merge;(r=l||(l={})).initDataClasses=function(t){var e,i,o,s=this.chart,r=this.legendItem=this.legendItem||{},n=this.options,a=t.dataClasses||[],l=s.options.chart.colorCount,h=0;this.dataClasses=i=[],r.labels=[];for(var c=0,d=a.length;c<d;++c)e=O(e=a[c]),i.push(e),(s.styledMode||!e.color)&&("category"===n.dataClassColor?(s.styledMode||(l=(o=s.options.colors||[]).length,e.color=o[h]),e.colorIndex=h,++h===l&&(h=0)):e.color=S(n.minColor).tweenTo(S(n.maxColor),d<2?.5:c/(d-1)))},r.initStops=function(){for(var t=this.options,e=this.stops=t.stops||[[0,t.minColor||""],[1,t.maxColor||""]],i=0,o=e.length;i<o;++i)e[i].color=S(e[i][1])},r.normalizedValue=function(t){var e=this.max||0,i=this.min||0;return this.logarithmic&&(t=this.logarithmic.log2lin(t)),1-(e-t)/(e-i||1)},r.toColor=function(t,e){var i,o,s,r,n,a,l=this.dataClasses,h=this.stops;if(l){for(a=l.length;a--;)if(o=(n=l[a]).from,s=n.to,(void 0===o||t>=o)&&(void 0===s||t<=s)){r=n.color,e&&(e.dataClass=a,e.colorIndex=n.colorIndex);break}}else{for(i=this.normalizedValue(t),a=h.length;a--&&!(i>h[a][0]););o=h[a]||h[a+1],i=1-((s=h[a+1]||o)[0]-i)/(s[0]-o[0]||1),r=o.color.tweenTo(s.color,i)}return r};var P=l,M=d(500),k=d.n(M),z=d(512),D=d.n(z),E=(n=function(t,e){return(n=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)e.hasOwnProperty(i)&&(t[i]=e[i])})(t,e)},function(t,e){function i(){this.constructor=t}n(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),V=f().defaultOptions,T=D().series,q=f().defined,H=f().extend,_=f().fireEvent,j=f().isArray,W=f().isNumber,K=f().merge,R=f().pick,N=f().relativeLength;V.colorAxis=K(V.xAxis,{lineWidth:0,minPadding:0,maxPadding:0,gridLineColor:"#ffffff",gridLineWidth:1,tickPixelInterval:72,startOnTick:!0,endOnTick:!0,offset:0,marker:{animation:{duration:50},width:.01,color:"#999999"},labels:{distance:8,overflow:"justify",rotation:0},minColor:"#e6e9ff",maxColor:"#0022ff",tickLength:5,showInLegend:!0});var X=function(t){function e(e,i){var o=t.call(this,e,i)||this;return o.coll="colorAxis",o.visible=!0,o.init(e,i),o}return E(e,t),e.compose=function(t,i,o,s){w.compose(e,t,i,o,s)},e.prototype.init=function(e,i){var o=e.options.legend||{},s=i.layout?"vertical"!==i.layout:"vertical"!==o.layout;this.side=i.side||s?2:1,this.reversed=i.reversed||!s,this.opposite=!s,t.prototype.init.call(this,e,i,"colorAxis"),this.userOptions=i,j(e.userOptions.colorAxis)&&(e.userOptions.colorAxis[this.index]=i),i.dataClasses&&this.initDataClasses(i),this.initStops(),this.horiz=s,this.zoomEnabled=!1},e.prototype.hasData=function(){return!!(this.tickPositions||[]).length},e.prototype.setTickPositions=function(){if(!this.dataClasses)return t.prototype.setTickPositions.call(this)},e.prototype.setOptions=function(e){var i=K(V.colorAxis,e,{showEmpty:!1,title:null,visible:this.chart.options.legend.enabled&&!1!==e.visible});t.prototype.setOptions.call(this,i),this.options.crosshair=this.options.marker},e.prototype.setAxisSize=function(){var t,i=this.chart,o=null===(t=this.legendItem)||void 0===t?void 0:t.symbol,s=this.getSize(),r=s.width,n=s.height;o&&(this.left=+o.attr("x"),this.top=+o.attr("y"),this.width=r=+o.attr("width"),this.height=n=+o.attr("height"),this.right=i.chartWidth-this.left-r,this.bottom=i.chartHeight-this.top-n,this.pos=this.horiz?this.left:this.top),this.len=(this.horiz?r:n)||e.defaultLegendLength},e.prototype.getOffset=function(){var i,o=null===(i=this.legendItem)||void 0===i?void 0:i.group,s=this.chart.axisOffset[this.side];if(o){this.axisParent=o,t.prototype.getOffset.call(this);var r=this.chart.legend;r.allItems.forEach(function(t){t instanceof e&&t.drawLegendSymbol(r,t)}),r.render(),this.chart.getMargins(!0),this.chart.series.some(function(t){return t.isDrilling})||(this.isDirty=!0),this.added||(this.added=!0,this.labelLeft=0,this.labelRight=this.width),this.chart.axisOffset[this.side]=s}},e.prototype.setLegendColor=function(){var t=this.horiz,e=this.reversed,i=+!!e,o=+!e,s=t?[i,0,o,0]:[0,o,0,i];this.legendColor={linearGradient:{x1:s[0],y1:s[1],x2:s[2],y2:s[3]},stops:this.stops}},e.prototype.drawLegendSymbol=function(t,e){var i,o=e.legendItem||{},s=t.padding,r=t.options,n=this.options.labels,a=R(r.itemDistance,10),l=this.horiz,h=this.getSize(),c=h.width,d=h.height,p=R(r.labelPadding,l?16:30);this.setLegendColor(),o.symbol||(o.symbol=this.chart.renderer.symbol("roundedRect").attr({r:null!==(i=r.symbolRadius)&&void 0!==i?i:3,zIndex:1}).add(o.group)),o.symbol.attr({x:0,y:(t.baseline||0)-11,width:c,height:d}),o.labelWidth=c+s+(l?a:R(n.x,n.distance)+(this.maxLabelLength||0)),o.labelHeight=d+s+(l?p:0)},e.prototype.setState=function(t){this.series.forEach(function(e){e.setState(t)})},e.prototype.setVisible=function(){},e.prototype.getSeriesExtremes=function(){var t,e,i,o,s=this.series,r=s.length;for(this.dataMin=1/0,this.dataMax=-1/0;r--;){e=(o=s[r]).colorKey=R(o.options.colorKey,o.colorKey,o.pointValKey,o.zoneAxis,"y"),i=o[e+"Min"]&&o[e+"Max"];for(var n=0,a=[e,"value","y"];n<a.length;n++){var l=a[n];if((t=o.getColumn(l)).length)break}if(i)o.minColorValue=o[e+"Min"],o.maxColorValue=o[e+"Max"];else{var h=T.prototype.getExtremes.call(o,t);o.minColorValue=h.dataMin,o.maxColorValue=h.dataMax}q(o.minColorValue)&&q(o.maxColorValue)&&(this.dataMin=Math.min(this.dataMin,o.minColorValue),this.dataMax=Math.max(this.dataMax,o.maxColorValue)),i||T.prototype.applyExtremes.call(o)}},e.prototype.drawCrosshair=function(e,i){var o,s=this.legendItem||{},r=null==i?void 0:i.plotX,n=null==i?void 0:i.plotY,a=this.pos,l=this.len;i&&((o=this.toPixels(i.getNestedProperty(i.series.colorKey)))<a?o=a-2:o>a+l&&(o=a+l+2),i.plotX=o,i.plotY=this.len-o,t.prototype.drawCrosshair.call(this,e,i),i.plotX=r,i.plotY=n,this.cross&&!this.cross.addedToColorAxis&&s.group&&(this.cross.addClass("highcharts-coloraxis-marker").add(s.group),this.cross.addedToColorAxis=!0,this.chart.styledMode||"object"!=typeof this.crosshair||this.cross.attr({fill:this.crosshair.color})))},e.prototype.getPlotLinePath=function(e){var i=this.left,o=e.translatedValue,s=this.top;return W(o)?this.horiz?[["M",o-4,s-6],["L",o+4,s-6],["L",o,s],["Z"]]:[["M",i,o],["L",i-6,o+6],["L",i-6,o-6],["Z"]]:t.prototype.getPlotLinePath.call(this,e)},e.prototype.update=function(e,i){var o,s=this.chart.legend;this.series.forEach(function(t){t.isDirtyData=!0}),(e.dataClasses&&s.allItems||this.dataClasses)&&this.destroyItems(),t.prototype.update.call(this,e,i),(null===(o=this.legendItem)||void 0===o?void 0:o.label)&&(this.setLegendColor(),s.colorizeItem(this,!0))},e.prototype.destroyItems=function(){var t=this.chart,e=this.legendItem||{};if(e.label)t.legend.destroyItem(this);else if(e.labels)for(var i=0,o=e.labels;i<o.length;i++){var s=o[i];t.legend.destroyItem(s)}t.isDirtyLegend=!0},e.prototype.destroy=function(){this.chart.isDirtyLegend=!0,this.destroyItems(),t.prototype.destroy.apply(this,[].slice.call(arguments))},e.prototype.remove=function(e){this.destroyItems(),t.prototype.remove.call(this,e)},e.prototype.getDataClassLegendSymbols=function(){var t,e=this,i=e.chart,o=e.legendItem&&e.legendItem.labels||[],s=i.options.legend,r=R(s.valueDecimals,-1),n=R(s.valueSuffix,""),a=function(t){return e.series.reduce(function(e,i){return e.push.apply(e,i.points.filter(function(e){return e.dataClass===t})),e},[])};return o.length||e.dataClasses.forEach(function(s,l){var h=s.from,c=s.to,d=i.numberFormatter,p=!0;t="",void 0===h?t="< ":void 0===c&&(t="> "),void 0!==h&&(t+=d(h,r)+n),void 0!==h&&void 0!==c&&(t+=" - "),void 0!==c&&(t+=d(c,r)+n),o.push(H({chart:i,name:t,options:{},drawLegendSymbol:k().rectangle,visible:!0,isDataClass:!0,setState:function(t){for(var e=0,i=a(l);e<i.length;e++)i[e].setState(t)},setVisible:function(){this.visible=p=e.visible=!p;for(var t=[],o=0,s=a(l);o<s.length;o++){var r=s[o];r.setVisible(p),r.hiddenInDataClass=!p,-1===t.indexOf(r.series)&&t.push(r.series)}i.legend.colorizeItem(this,p),t.forEach(function(t){_(t,"afterDataClassLegendClick")})}},s))}),o},e.prototype.getSize=function(){var t=this.chart,i=this.horiz,o=this.options,s=o.height,r=o.width,n=t.options.legend;return{width:R(q(r)?N(r,t.chartWidth):void 0,null==n?void 0:n.symbolWidth,i?e.defaultLegendLength:12),height:R(q(s)?N(s,t.chartHeight):void 0,null==n?void 0:n.symbolHeight,i?12:e.defaultLegendLength)}},e.defaultLegendLength=200,e.keepProps=["legendItem"],e}(y());H(X.prototype,P),Array.prototype.push.apply(y().keepProps,X.keepProps);var U=f();U.ColorAxis=U.ColorAxis||X,U.ColorAxis.compose(U.Chart,U.Fx,U.Legend,U.Series);var Y=f();return p.default}()});