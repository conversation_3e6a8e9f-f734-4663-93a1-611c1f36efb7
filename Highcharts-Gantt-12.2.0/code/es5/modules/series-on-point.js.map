{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/series-on-point\n * @requires highcharts\n *\n * Series on point module\n *\n * (c) 2010-2025 Highsoft AS\n * Author: <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON>j\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"Point\"], require(\"highcharts\")[\"Series\"], require(\"highcharts\")[\"SeriesRegistry\"], require(\"highcharts\")[\"SVGRenderer\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/series-on-point\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"Point\"], [\"highcharts/highcharts\",\"Series\"], [\"highcharts/highcharts\",\"SeriesRegistry\"], [\"highcharts/highcharts\",\"SVGRenderer\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/series-on-point\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"Point\"], require(\"highcharts\")[\"Series\"], require(\"highcharts\")[\"SeriesRegistry\"], require(\"highcharts\")[\"SVGRenderer\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Point\"], root[\"Highcharts\"][\"Series\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"SVGRenderer\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__260__, __WEBPACK_EXTERNAL_MODULE__820__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__540__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 260:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__260__;\n\n/***/ }),\n\n/***/ 512:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 540:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__540__;\n\n/***/ }),\n\n/***/ 820:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__820__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ series_on_point_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es5/es-modules/Data/ColumnUtils.js\n/* *\n *\n *  (c) 2020-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *\n * */\nvar __spreadArray = (undefined && undefined.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\n/**\n * Utility functions for columns that can be either arrays or typed arrays.\n * @private\n */\nvar ColumnUtils;\n(function (ColumnUtils) {\n    /* *\n    *\n    *  Declarations\n    *\n    * */\n    /* *\n    *\n    * Functions\n    *\n    * */\n    /**\n     * Sets the length of the column array.\n     *\n     * @param {DataTable.Column} column\n     * Column to be modified.\n     *\n     * @param {number} length\n     * New length of the column.\n     *\n     * @param {boolean} asSubarray\n     * If column is a typed array, return a subarray instead of a new array. It\n     * is faster `O(1)`, but the entire buffer will be kept in memory until all\n     * views to it are destroyed. Default is `false`.\n     *\n     * @return {DataTable.Column}\n     * Modified column.\n     *\n     * @private\n     */\n    function setLength(column, length, asSubarray) {\n        if (Array.isArray(column)) {\n            column.length = length;\n            return column;\n        }\n        return column[asSubarray ? 'subarray' : 'slice'](0, length);\n    }\n    ColumnUtils.setLength = setLength;\n    /**\n     * Splices a column array.\n     *\n     * @param {DataTable.Column} column\n     * Column to be modified.\n     *\n     * @param {number} start\n     * Index at which to start changing the array.\n     *\n     * @param {number} deleteCount\n     * An integer indicating the number of old array elements to remove.\n     *\n     * @param {boolean} removedAsSubarray\n     * If column is a typed array, return a subarray instead of a new array. It\n     * is faster `O(1)`, but the entire buffer will be kept in memory until all\n     * views to it are destroyed. Default is `true`.\n     *\n     * @param {Array<number>|TypedArray} items\n     * The elements to add to the array, beginning at the start index. If you\n     * don't specify any elements, `splice()` will only remove elements from the\n     * array.\n     *\n     * @return {SpliceResult}\n     * Object containing removed elements and the modified column.\n     *\n     * @private\n     */\n    function splice(column, start, deleteCount, removedAsSubarray, items) {\n        if (items === void 0) { items = []; }\n        if (Array.isArray(column)) {\n            if (!Array.isArray(items)) {\n                items = Array.from(items);\n            }\n            return {\n                removed: column.splice.apply(column, __spreadArray([start, deleteCount], items, false)),\n                array: column\n            };\n        }\n        var Constructor = Object.getPrototypeOf(column)\n                .constructor;\n        var removed = column[removedAsSubarray ? 'subarray' : 'slice'](start,\n            start + deleteCount);\n        var newLength = column.length - deleteCount + items.length;\n        var result = new Constructor(newLength);\n        result.set(column.subarray(0, start), 0);\n        result.set(items, start);\n        result.set(column.subarray(start + deleteCount), start + items.length);\n        return {\n            removed: removed,\n            array: result\n        };\n    }\n    ColumnUtils.splice = splice;\n})(ColumnUtils || (ColumnUtils = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Data_ColumnUtils = (ColumnUtils);\n\n;// ./code/es5/es-modules/Data/DataTableCore.js\n/* *\n *\n *  (c) 2009-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Authors: <AUTHORS>\n *  - Gøran Slettemark\n *  - Torstein Hønsi\n *\n * */\n\n\nvar setLength = Data_ColumnUtils.setLength, splice = Data_ColumnUtils.splice;\n\nvar fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, objectEach = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).objectEach, uniqueKey = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).uniqueKey;\n/* *\n *\n *  Class\n *\n * */\n/**\n * Class to manage columns and rows in a table structure. It provides methods\n * to add, remove, and manipulate columns and rows, as well as to retrieve data\n * from specific cells.\n *\n * @class\n * @name Highcharts.DataTable\n *\n * @param {Highcharts.DataTableOptions} [options]\n * Options to initialize the new DataTable instance.\n */\nvar DataTableCore = /** @class */ (function () {\n    /**\n     * Constructs an instance of the DataTable class.\n     *\n     * @example\n     * const dataTable = new Highcharts.DataTableCore({\n     *   columns: {\n     *     year: [2020, 2021, 2022, 2023],\n     *     cost: [11, 13, 12, 14],\n     *     revenue: [12, 15, 14, 18]\n     *   }\n     * });\n\n     *\n     * @param {Highcharts.DataTableOptions} [options]\n     * Options to initialize the new DataTable instance.\n     */\n    function DataTableCore(options) {\n        if (options === void 0) { options = {}; }\n        var _this = this;\n        /**\n         * Whether the ID was automatic generated or given in the constructor.\n         *\n         * @name Highcharts.DataTable#autoId\n         * @type {boolean}\n         */\n        this.autoId = !options.id;\n        this.columns = {};\n        /**\n         * ID of the table for identification purposes.\n         *\n         * @name Highcharts.DataTable#id\n         * @type {string}\n         */\n        this.id = (options.id || uniqueKey());\n        this.modified = this;\n        this.rowCount = 0;\n        this.versionTag = uniqueKey();\n        var rowCount = 0;\n        objectEach(options.columns || {}, function (column, columnName) {\n            _this.columns[columnName] = column.slice();\n            rowCount = Math.max(rowCount, column.length);\n        });\n        this.applyRowCount(rowCount);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Applies a row count to the table by setting the `rowCount` property and\n     * adjusting the length of all columns.\n     *\n     * @private\n     * @param {number} rowCount The new row count.\n     */\n    DataTableCore.prototype.applyRowCount = function (rowCount) {\n        var _this = this;\n        this.rowCount = rowCount;\n        objectEach(this.columns, function (column, columnName) {\n            if (column.length !== rowCount) {\n                _this.columns[columnName] = setLength(column, rowCount);\n            }\n        });\n    };\n    /**\n     * Delete rows. Simplified version of the full\n     * `DataTable.deleteRows` method.\n     *\n     * @param {number} rowIndex\n     * The start row index\n     *\n     * @param {number} [rowCount=1]\n     * The number of rows to delete\n     *\n     * @return {void}\n     *\n     * @emits #afterDeleteRows\n     */\n    DataTableCore.prototype.deleteRows = function (rowIndex, rowCount) {\n        var _this = this;\n        if (rowCount === void 0) { rowCount = 1; }\n        if (rowCount > 0 && rowIndex < this.rowCount) {\n            var length_1 = 0;\n            objectEach(this.columns, function (column, columnName) {\n                _this.columns[columnName] =\n                    splice(column, rowIndex, rowCount).array;\n                length_1 = column.length;\n            });\n            this.rowCount = length_1;\n        }\n        fireEvent(this, 'afterDeleteRows', { rowIndex: rowIndex, rowCount: rowCount });\n        this.versionTag = uniqueKey();\n    };\n    /**\n     * Fetches the given column by the canonical column name. Simplified version\n     * of the full `DataTable.getRow` method, always returning by reference.\n     *\n     * @param {string} columnName\n     * Name of the column to get.\n     *\n     * @return {Highcharts.DataTableColumn|undefined}\n     * A copy of the column, or `undefined` if not found.\n     */\n    DataTableCore.prototype.getColumn = function (columnName, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    asReference) {\n        return this.columns[columnName];\n    };\n    /**\n     * Retrieves all or the given columns. Simplified version of the full\n     * `DataTable.getColumns` method, always returning by reference.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names to retrieve.\n     *\n     * @return {Highcharts.DataTableColumnCollection}\n     * Collection of columns. If a requested column was not found, it is\n     * `undefined`.\n     */\n    DataTableCore.prototype.getColumns = function (columnNames, \n    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n    asReference) {\n        var _this = this;\n        return (columnNames || Object.keys(this.columns)).reduce(function (columns, columnName) {\n            columns[columnName] = _this.columns[columnName];\n            return columns;\n        }, {});\n    };\n    /**\n     * Retrieves the row at a given index.\n     *\n     * @param {number} rowIndex\n     * Row index to retrieve. First row has index 0.\n     *\n     * @param {Array<string>} [columnNames]\n     * Column names to retrieve.\n     *\n     * @return {Record<string, number|string|undefined>|undefined}\n     * Returns the row values, or `undefined` if not found.\n     */\n    DataTableCore.prototype.getRow = function (rowIndex, columnNames) {\n        var _this = this;\n        return (columnNames || Object.keys(this.columns)).map(function (key) { var _a; return (_a = _this.columns[key]) === null || _a === void 0 ? void 0 : _a[rowIndex]; });\n    };\n    /**\n     * Sets cell values for a column. Will insert a new column, if not found.\n     *\n     * @param {string} columnName\n     * Column name to set.\n     *\n     * @param {Highcharts.DataTableColumn} [column]\n     * Values to set in the column.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to change. (Default: 0)\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #setColumns\n     * @emits #afterSetColumns\n     */\n    DataTableCore.prototype.setColumn = function (columnName, column, rowIndex, eventDetail) {\n        var _a;\n        if (column === void 0) { column = []; }\n        if (rowIndex === void 0) { rowIndex = 0; }\n        this.setColumns((_a = {}, _a[columnName] = column, _a), rowIndex, eventDetail);\n    };\n    /**\n     * Sets cell values for multiple columns. Will insert new columns, if not\n     * found. Simplified version of the full `DataTableCore.setColumns`, limited\n     * to full replacement of the columns (undefined `rowIndex`).\n     *\n     * @param {Highcharts.DataTableColumnCollection} columns\n     * Columns as a collection, where the keys are the column names.\n     *\n     * @param {number} [rowIndex]\n     * Index of the first row to change. Ignored in the `DataTableCore`, as it\n     * always replaces the full column.\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #setColumns\n     * @emits #afterSetColumns\n     */\n    DataTableCore.prototype.setColumns = function (columns, rowIndex, eventDetail) {\n        var _this = this;\n        var rowCount = this.rowCount;\n        objectEach(columns, function (column, columnName) {\n            _this.columns[columnName] = column.slice();\n            rowCount = column.length;\n        });\n        this.applyRowCount(rowCount);\n        if (!(eventDetail === null || eventDetail === void 0 ? void 0 : eventDetail.silent)) {\n            fireEvent(this, 'afterSetColumns');\n            this.versionTag = uniqueKey();\n        }\n    };\n    /**\n     * Sets cell values of a row. Will insert a new row if no index was\n     * provided, or if the index is higher than the total number of table rows.\n     * A simplified version of the full `DateTable.setRow`, limited to objects.\n     *\n     * @param {Record<string, number|string|undefined>} row\n     * Cell values to set.\n     *\n     * @param {number} [rowIndex]\n     * Index of the row to set. Leave `undefined` to add as a new row.\n     *\n     * @param {boolean} [insert]\n     * Whether to insert the row at the given index, or to overwrite the row.\n     *\n     * @param {Record<string, (boolean|number|string|null|undefined)>} [eventDetail]\n     * Custom information for pending events.\n     *\n     * @emits #afterSetRows\n     */\n    DataTableCore.prototype.setRow = function (row, rowIndex, insert, eventDetail) {\n        if (rowIndex === void 0) { rowIndex = this.rowCount; }\n        var columns = this.columns,\n            indexRowCount = insert ? this.rowCount + 1 : rowIndex + 1;\n        objectEach(row, function (cellValue, columnName) {\n            var column = columns[columnName] ||\n                    (eventDetail === null || eventDetail === void 0 ? void 0 : eventDetail.addColumns) !== false && new Array(indexRowCount);\n            if (column) {\n                if (insert) {\n                    column = splice(column, rowIndex, 0, true, [cellValue]).array;\n                }\n                else {\n                    column[rowIndex] = cellValue;\n                }\n                columns[columnName] = column;\n            }\n        });\n        if (indexRowCount > this.rowCount) {\n            this.applyRowCount(indexRowCount);\n        }\n        if (!(eventDetail === null || eventDetail === void 0 ? void 0 : eventDetail.silent)) {\n            fireEvent(this, 'afterSetRows');\n            this.versionTag = uniqueKey();\n        }\n    };\n    return DataTableCore;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Data_DataTableCore = (DataTableCore);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * A typed array.\n * @typedef {Int8Array|Uint8Array|Uint8ClampedArray|Int16Array|Uint16Array|Int32Array|Uint32Array|Float32Array|Float64Array} Highcharts.TypedArray\n * //**\n * A column of values in a data table.\n * @typedef {Array<boolean|null|number|string|undefined>|Highcharts.TypedArray} Highcharts.DataTableColumn\n */ /**\n* A collection of data table columns defined by a object where the key is the\n* column name and the value is an array of the column values.\n* @typedef {Record<string, Highcharts.DataTableColumn>} Highcharts.DataTableColumnCollection\n*/\n/**\n * Options for the `DataTable` or `DataTableCore` classes.\n * @interface Highcharts.DataTableOptions\n */ /**\n* The column options for the data table. The columns are defined by an object\n* where the key is the column ID and the value is an array of the column\n* values.\n*\n* @name Highcharts.DataTableOptions.columns\n* @type {Highcharts.DataTableColumnCollection|undefined}\n*/ /**\n* Custom ID to identify the new DataTable instance.\n*\n* @name Highcharts.DataTableOptions.id\n* @type {string|undefined}\n*/\n(''); // Keeps doclets above in JS file\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Point\"],\"commonjs\":[\"highcharts\",\"Point\"],\"commonjs2\":[\"highcharts\",\"Point\"],\"root\":[\"Highcharts\",\"Point\"]}\nvar highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_ = __webpack_require__(260);\nvar highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default = /*#__PURE__*/__webpack_require__.n(highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Series\"],\"commonjs\":[\"highcharts\",\"Series\"],\"commonjs2\":[\"highcharts\",\"Series\"],\"root\":[\"Highcharts\",\"Series\"]}\nvar highcharts_Series_commonjs_highcharts_Series_commonjs2_highcharts_Series_root_Highcharts_Series_ = __webpack_require__(820);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SVGRenderer\"],\"commonjs\":[\"highcharts\",\"SVGRenderer\"],\"commonjs2\":[\"highcharts\",\"SVGRenderer\"],\"root\":[\"Highcharts\",\"SVGRenderer\"]}\nvar highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_ = __webpack_require__(540);\nvar highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default = /*#__PURE__*/__webpack_require__.n(highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_);\n;// ./code/es5/es-modules/Series/SeriesOnPointComposition.js\n/* *\n *\n *  (c) 2010-2025 Rafal Sebestjanski, Piotr Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nvar composed = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).composed;\n\n\n\nvar bubble = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.bubble;\n\n\nvar addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, find = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).find, isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, pushUnique = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pushUnique;\n/* *\n *\n *  Composition\n *\n * */\nvar SeriesOnPointComposition;\n(function (SeriesOnPointComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Extends the series with a small addition.\n     *\n     * @private\n     *\n     * @param SeriesClass\n     * Series class to use.\n     *\n     * @param ChartClass\n     * Chart class to use.\n     */\n    function compose(SeriesClass, ChartClass) {\n        if (pushUnique(composed, 'SeriesOnPoint')) {\n            var _a = Additions.prototype,\n                chartGetZData = _a.chartGetZData,\n                seriesAfterInit = _a.seriesAfterInit,\n                seriesAfterRender = _a.seriesAfterRender,\n                seriesGetCenter = _a.seriesGetCenter,\n                seriesShowOrHide = _a.seriesShowOrHide,\n                seriesTranslate = _a.seriesTranslate;\n            // We can mark support for pie series here because it's in the core.\n            // But all other series outside the core should be marked in its\n            // module. This is crucial when loading series-on-point before\n            // loading a module, e.g. sunburst.\n            // Supported series types:\n            // - pie\n            // - sunburst\n            SeriesClass.types.pie.prototype.onPointSupported = true;\n            addEvent(SeriesClass, 'afterInit', seriesAfterInit);\n            addEvent(SeriesClass, 'afterRender', seriesAfterRender);\n            addEvent(SeriesClass, 'afterGetCenter', seriesGetCenter);\n            addEvent(SeriesClass, 'hide', seriesShowOrHide);\n            addEvent(SeriesClass, 'show', seriesShowOrHide);\n            addEvent(SeriesClass, 'translate', seriesTranslate);\n            addEvent(ChartClass, 'beforeRender', chartGetZData);\n            addEvent(ChartClass, 'beforeRedraw', chartGetZData);\n        }\n        return SeriesClass;\n    }\n    SeriesOnPointComposition.compose = compose;\n    /* *\n     *\n     *  Classes\n     *\n     * */\n    /**\n     * @private\n     */\n    var Additions = /** @class */ (function () {\n            /* *\n             *\n             *  Constructors\n             *\n             * */\n            /**\n             * @private\n             */\n            function Additions(series) {\n                /**\n                 * @ignore\n                 */\n                this.getColumn = bubble.prototype.getColumn;\n            /**\n             * @ignore\n             */\n            this.getRadii = bubble.prototype.getRadii;\n            /**\n             * @ignore\n             */\n            this.getRadius = bubble.prototype.getRadius;\n            /**\n             * @ignore\n             */\n            this.getPxExtremes = bubble.prototype.getPxExtremes;\n            /**\n             * @ignore\n             */\n            this.getZExtremes = bubble.prototype.getZExtremes;\n            this.chart = series.chart;\n            this.series = series;\n            this.options = series.options.onPoint;\n        }\n        /**\n         * Draw connector line that starts from the initial point's position\n         * and ends in the center of the series.\n         * @private\n         */\n        Additions.prototype.drawConnector = function () {\n            if (!this.connector) {\n                this.connector = this.series.chart.renderer.path()\n                    .addClass('highcharts-connector-seriesonpoint')\n                    .attr({\n                    zIndex: -1\n                })\n                    .add(this.series.markerGroup);\n            }\n            var attribs = this.getConnectorAttributes();\n            attribs && this.connector.animate(attribs);\n        };\n        /**\n         * Get connector line path and styles that connects series and point.\n         *\n         * @private\n         *\n         * @return {Highcharts.SVGAttributes} attribs - the path and styles.\n         */\n        Additions.prototype.getConnectorAttributes = function () {\n            var chart = this.series.chart,\n                onPointOptions = this.options;\n            if (!onPointOptions) {\n                return;\n            }\n            var connectorOpts = onPointOptions.connectorOptions || {},\n                position = onPointOptions.position,\n                connectedPoint = chart.get(onPointOptions.id);\n            if (!(connectedPoint instanceof (highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default())) ||\n                !position ||\n                !defined(connectedPoint.plotX) ||\n                !defined(connectedPoint.plotY)) {\n                return;\n            }\n            var xFrom = defined(position.x) ?\n                    position.x :\n                    connectedPoint.plotX,\n                yFrom = defined(position.y) ?\n                    position.y :\n                    connectedPoint.plotY,\n                xTo = xFrom + (position.offsetX || 0),\n                yTo = yFrom + (position.offsetY || 0),\n                width = connectorOpts.width || 1,\n                color = connectorOpts.stroke || this.series.color,\n                dashStyle = connectorOpts.dashstyle,\n                attribs = {\n                    d: highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default().prototype.crispLine([\n                        ['M',\n                xFrom,\n                yFrom],\n                        ['L',\n                xTo,\n                yTo]\n                    ],\n                width),\n                    'stroke-width': width\n                };\n            if (!chart.styledMode) {\n                attribs.stroke = color;\n                attribs.dashstyle = dashStyle;\n            }\n            return attribs;\n        };\n        /**\n         * Initialize Series on point on series init.\n         *\n         * @ignore\n         */\n        Additions.prototype.seriesAfterInit = function () {\n            if (this.onPointSupported && this.options.onPoint) {\n                this.bubblePadding = true;\n                this.useMapGeometry = true;\n                this.onPoint = new Additions(this);\n            }\n        };\n        /**\n         * @ignore\n         */\n        Additions.prototype.seriesAfterRender = function () {\n            // Clear bubbleZExtremes to reset z calculations on update.\n            delete this.chart.bubbleZExtremes;\n            this.onPoint && this.onPoint.drawConnector();\n        };\n        /**\n         * Recalculate series.center (x, y and size).\n         *\n         * @ignore\n         */\n        Additions.prototype.seriesGetCenter = function (e) {\n            var onPointOptions = this.options.onPoint,\n                center = e.positions;\n            if (onPointOptions) {\n                var connectedPoint = this.chart.get(onPointOptions.id);\n                if (connectedPoint instanceof (highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default()) &&\n                    defined(connectedPoint.plotX) &&\n                    defined(connectedPoint.plotY)) {\n                    center[0] = connectedPoint.plotX;\n                    center[1] = connectedPoint.plotY;\n                }\n                var position = onPointOptions.position;\n                if (position) {\n                    if (defined(position.x)) {\n                        center[0] = position.x;\n                    }\n                    if (defined(position.y)) {\n                        center[1] = position.y;\n                    }\n                    if (position.offsetX) {\n                        center[0] += position.offsetX;\n                    }\n                    if (position.offsetY) {\n                        center[1] += position.offsetY;\n                    }\n                }\n            }\n            // Get and set the size\n            var radius = this.radii && this.radii[this.index];\n            if (isNumber(radius)) {\n                center[2] = radius * 2;\n            }\n            e.positions = center;\n        };\n        /**\n         * @ignore\n         */\n        Additions.prototype.seriesShowOrHide = function () {\n            var _a;\n            var allSeries = this.chart.series;\n            // When toggling a series visibility, loop through all points\n            (_a = this.points) === null || _a === void 0 ? void 0 : _a.forEach(function (point) {\n                // Find all series that are on toggled points\n                var series = find(allSeries,\n                    function (series) {\n                        var id = ((series.onPoint || {}).options || {}).id;\n                    if (!id) {\n                        return false;\n                    }\n                    return id === point.id;\n                });\n                // And also toggle series that are on toggled points. Redraw is\n                // not needed because it's fired later after showOrHide event\n                series && series.setVisible(!series.visible, false);\n            });\n        };\n        /**\n         * Calculate required radius (z data) before original translate.\n         *\n         * @ignore\n         * @function Highcharts.Series#translate\n         */\n        Additions.prototype.seriesTranslate = function () {\n            if (this.onPoint) {\n                this.onPoint.getRadii();\n                this.radii = this.onPoint.radii;\n            }\n        };\n        /**\n         * @ignore\n         */\n        Additions.prototype.chartGetZData = function () {\n            var zData = [];\n            this.series.forEach(function (series) {\n                var _a;\n                var onPointOpts = series.options.onPoint;\n                zData.push((_a = onPointOpts === null || onPointOpts === void 0 ? void 0 : onPointOpts.z) !== null && _a !== void 0 ? _a : null);\n            });\n            var dataTable = new Data_DataTableCore({\n                    columns: {\n                        z: zData\n                    }\n                });\n            this.series.forEach(function (series) {\n                // Save z values of all the series\n                if (series.onPoint) {\n                    series.onPoint.dataTable = series.dataTable = dataTable;\n                }\n            });\n        };\n        return Additions;\n    }());\n    SeriesOnPointComposition.Additions = Additions;\n})(SeriesOnPointComposition || (SeriesOnPointComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Series_SeriesOnPointComposition = (SeriesOnPointComposition);\n/* *\n *\n *  API Options\n *\n * */\n/**\n * Options for the _Series on point_ feature. Only `pie` and `sunburst` series\n * are supported at this moment.\n *\n * @sample      {highcharts} highcharts/series-on-point/series-on-point\n *              Series on point\n * @sample      {highmaps} maps/demo/map-pies\n *              Pies on a map\n * @requires    modules/series-on-point\n * @since 10.2.0\n * @type        {object}\n * @apioption   plotOptions.series.onPoint\n */\n/**\n * Options for the connector in the _Series on point_ feature.\n *\n * In styled mode, the connector can be styled with the\n * `.highcharts-connector-seriesonpoint` class name.\n *\n * @requires    modules/series-on-point\n * @since 10.2.0\n * @type        {Highcharts.SVGAttributes}\n * @apioption   plotOptions.series.onPoint.connectorOptions\n */\n/**\n * Color of the connector line. By default it's the series' color.\n *\n * @requires    modules/series-on-point\n * @since 10.2.0\n * @type        {string}\n * @apioption   plotOptions.series.onPoint.connectorOptions.stroke\n */\n/**\n * A name for the dash style to use for the connector.\n *\n * @requires    modules/series-on-point\n * @since 10.2.0\n * @type        {string}\n * @apioption   plotOptions.series.onPoint.connectorOptions.dashstyle\n */\n/**\n * Pixel width of the connector line.\n *\n * @default     1\n * @requires    modules/series-on-point\n * @type        {number}\n * @since 10.2.0\n * @apioption   plotOptions.series.onPoint.connectorOptions.width\n */\n/**\n * The `id` of the point that we connect the series to. Only points with a given\n * `plotX` and `plotY` values and map points are valid.\n *\n * @requires   modules/series-on-point\n * @since 10.2.0\n * @type       {string}\n * @apioption  plotOptions.series.onPoint.id\n */\n/**\n * Options allowing to set a position and an offset of the series in the\n * _Series on point_ feature.\n *\n * @requires    modules/series-on-point\n * @since 10.2.0\n * @type        {object}\n * @apioption   plotOptions.series.onPoint.position\n */\n/**\n * Series center offset from the original x position. If defined, the connector\n * line is drawn connecting original position with new position.\n *\n * @requires   modules/series-on-point\n * @since 10.2.0\n * @type       {number}\n * @apioption  plotOptions.series.onPoint.position.offsetX\n */\n/**\n * Series center offset from the original y position. If defined, the connector\n * line is drawn from original position to a new position.\n *\n * @requires   modules/series-on-point\n * @since 10.2.0\n * @type       {number}\n * @apioption  plotOptions.series.onPoint.position.offsetY\n */\n/**\n * X position of the series center. By default, the series is displayed on the\n * point that it is connected to.\n *\n * @requires   modules/series-on-point\n * @since 10.2.0\n * @type       {number}\n * @apioption  plotOptions.series.onPoint.position.x\n */\n/**\n * Y position of the series center. By default, the series is displayed on the\n * point that it is connected to.\n *\n * @requires   modules/series-on-point\n * @since 10.2.0\n * @type       {number}\n * @apioption  plotOptions.series.onPoint.position.y\n */\n''; // Keeps doclets above in transpiled file\n\n;// ./code/es5/es-modules/masters/modules/series-on-point.js\n\n\n\n\nvar G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nSeries_SeriesOnPointComposition.compose(G.Series, G.Chart);\n/* harmony default export */ var series_on_point_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__260__", "__WEBPACK_EXTERNAL_MODULE__820__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__540__", "Column<PERSON><PERSON><PERSON>", "SeriesOnPointComposition", "Additions", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "series_on_point_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "__spread<PERSON><PERSON>y", "to", "from", "pack", "arguments", "length", "ar", "i", "l", "Array", "slice", "concat", "<PERSON><PERSON><PERSON><PERSON>", "column", "as<PERSON><PERSON><PERSON><PERSON>", "isArray", "splice", "start", "deleteCount", "removedAsSubarray", "items", "removed", "apply", "array", "<PERSON><PERSON><PERSON><PERSON>", "getPrototypeOf", "constructor", "result", "set", "subarray", "Data_ColumnUtils", "fireEvent", "objectEach", "<PERSON><PERSON><PERSON>", "DataTableCore", "options", "_this", "autoId", "id", "columns", "modified", "rowCount", "versionTag", "columnName", "Math", "max", "applyRowCount", "deleteRows", "rowIndex", "length_1", "getColumn", "asReference", "getColumns", "columnNames", "keys", "reduce", "getRow", "map", "_a", "setColumn", "eventDetail", "setColumns", "silent", "setRow", "row", "insert", "indexRowCount", "cellValue", "addColumns", "highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_", "highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_", "highcharts_SVGRenderer_commonjs_highcharts_SVGRenderer_commonjs2_highcharts_SVGRenderer_root_Highcharts_SVGRenderer_default", "composed", "bubble", "seriesTypes", "addEvent", "defined", "find", "isNumber", "pushUnique", "compose", "SeriesClass", "ChartClass", "chartGetZData", "seriesAfterInit", "seriesAfterRender", "seriesGetCenter", "seriesShowOrHide", "seriesTranslate", "types", "pie", "onPointSupported", "series", "getRadii", "getRadius", "getPxExtremes", "getZExtremes", "chart", "onPoint", "drawConnector", "connector", "renderer", "path", "addClass", "attr", "zIndex", "add", "markerGroup", "attribs", "getConnectorAttributes", "animate", "onPointOptions", "connectorOpts", "connectorOptions", "position", "connectedPoint", "plotX", "plotY", "xFrom", "x", "yFrom", "y", "xTo", "offsetX", "yTo", "offsetY", "width", "color", "stroke", "dashStyle", "dashstyle", "crispLine", "styledMode", "bubblePadding", "useMapGeometry", "bubbleZExtremes", "e", "center", "positions", "radius", "radii", "index", "allSeries", "points", "for<PERSON>ach", "point", "setVisible", "visible", "zData", "onPointOpts", "push", "z", "dataTable", "Series_SeriesOnPointComposition", "G", "Series", "Chart"], "mappings": "CAYA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,KAAQ,CAAEA,QAAQ,cAAc,MAAS,CAAEA,QAAQ,cAAc,cAAiB,CAAEA,QAAQ,cAAc,WAAc,EACvL,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,qCAAsC,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,QAAQ,CAAE,CAAC,wBAAwB,SAAS,CAAE,CAAC,wBAAwB,iBAAiB,CAAE,CAAC,wBAAwB,cAAc,CAAC,CAAEJ,GAC/N,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,qCAAqC,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,KAAQ,CAAEA,QAAQ,cAAc,MAAS,CAAEA,QAAQ,cAAc,cAAiB,CAAEA,QAAQ,cAAc,WAAc,EAE7NJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,MAAS,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,WAAc,CACrL,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,EAC1L,OAAgB,AAAC,WACP,aACA,IAmICC,EA4cAC,EA2DHC,EAxgBJF,EA4cAC,EA9kBUE,EAAuB,CAE/B,IACC,SAASZ,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,SAASP,CAAM,EAEtBA,EAAOD,OAAO,CAAGS,CAEX,EAEA,IACC,SAASR,CAAM,EAEtBA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,SAASN,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGIS,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAajB,OAAO,CAG5B,IAAIC,EAASa,CAAwB,CAACE,EAAS,CAAG,CAGjDhB,QAAS,CAAC,CACX,EAMA,OAHAa,CAAmB,CAACG,EAAS,CAACf,EAAQA,EAAOD,OAAO,CAAEe,GAG/Cd,EAAOD,OAAO,AACtB,CAMCe,EAAoBI,CAAC,CAAG,SAASlB,CAAM,EACtC,IAAImB,EAASnB,GAAUA,EAAOoB,UAAU,CACvC,WAAa,OAAOpB,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAc,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAAStB,CAAO,CAAEwB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAAC1B,EAASyB,IAC5EE,OAAOC,cAAc,CAAC5B,EAASyB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,CAAqB,CACpE,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAcjHE,EAA0D,SAAUC,CAAE,CAAEC,CAAI,CAAEC,CAAI,EAClF,GAAIA,GAAQC,AAAqB,GAArBA,UAAUC,MAAM,CAAQ,IAAK,IAA4BC,EAAxBC,EAAI,EAAGC,EAAIN,EAAKG,MAAM,CAAME,EAAIC,EAAGD,KACxED,GAAQC,KAAKL,IACRI,GAAIA,CAAAA,EAAKG,MAAMhB,SAAS,CAACiB,KAAK,CAACf,IAAI,CAACO,EAAM,EAAGK,EAAC,EACnDD,CAAE,CAACC,EAAE,CAAGL,CAAI,CAACK,EAAE,EAGvB,OAAON,EAAGU,MAAM,CAACL,GAAMG,MAAMhB,SAAS,CAACiB,KAAK,CAACf,IAAI,CAACO,GACtD,CA2CIhC,EArCOA,EA2FRA,GAAgBA,CAAAA,EAAc,CAAC,CAAA,GAtDlB0C,SAAS,CAPrB,SAAmBC,CAAM,CAAER,CAAM,CAAES,CAAU,SACzC,AAAIL,MAAMM,OAAO,CAACF,IACdA,EAAOR,MAAM,CAAGA,EACTQ,GAEJA,CAAM,CAACC,EAAa,WAAa,QAAQ,CAAC,EAAGT,EACxD,EAsDAnC,EAAY8C,MAAM,CAzBlB,SAAgBH,CAAM,CAAEI,CAAK,CAAEC,CAAW,CAAEC,CAAiB,CAAEC,CAAK,EAEhE,GADc,KAAK,IAAfA,GAAoBA,CAAAA,EAAQ,EAAE,AAAD,EAC7BX,MAAMM,OAAO,CAACF,GAId,OAHKJ,MAAMM,OAAO,CAACK,IACfA,CAAAA,EAAQX,MAAMP,IAAI,CAACkB,EAAK,EAErB,CACHC,QAASR,EAAOG,MAAM,CAACM,KAAK,CAACT,EAAQb,EAAc,CAACiB,EAAOC,EAAY,CAAEE,EAAO,CAAA,IAChFG,MAAOV,CACX,EAEJ,IAAIW,EAAcrC,OAAOsC,cAAc,CAACZ,GAC/Ba,WAAW,CAChBL,EAAUR,CAAM,CAACM,EAAoB,WAAa,QAAQ,CAACF,EAC3DA,EAAQC,GAERS,EAAS,IAAIH,EADDX,EAAOR,MAAM,CAAGa,EAAcE,EAAMf,MAAM,EAK1D,OAHAsB,EAAOC,GAAG,CAACf,EAAOgB,QAAQ,CAAC,EAAGZ,GAAQ,GACtCU,EAAOC,GAAG,CAACR,EAAOH,GAClBU,EAAOC,GAAG,CAACf,EAAOgB,QAAQ,CAACZ,EAAQC,GAAcD,EAAQG,EAAMf,MAAM,EAC9D,CACHgB,QAASA,EACTE,MAAOI,CACX,CACJ,EAQyB,IAAIG,EAAoB5D,EAmBjD0C,EAAYkB,EAAiBlB,SAAS,CAAEI,EAASc,EAAiBd,MAAM,CAExEe,EAAY,AAAChC,IAA+EgC,SAAS,CAAEC,EAAa,AAACjC,IAA+EiC,UAAU,CAAEC,EAAY,AAAClC,IAA+EkC,SAAS,CAiBrTC,EAA+B,WAiB/B,SAASA,EAAcC,CAAO,EACV,KAAK,IAAjBA,GAAsBA,CAAAA,EAAU,CAAC,CAAA,EACrC,IAAIC,EAAQ,IAAI,AAOhB,CAAA,IAAI,CAACC,MAAM,CAAG,CAACF,EAAQG,EAAE,CACzB,IAAI,CAACC,OAAO,CAAG,CAAC,EAOhB,IAAI,CAACD,EAAE,CAAIH,EAAQG,EAAE,EAAIL,IACzB,IAAI,CAACO,QAAQ,CAAG,IAAI,CACpB,IAAI,CAACC,QAAQ,CAAG,EAChB,IAAI,CAACC,UAAU,CAAGT,IAClB,IAAIQ,EAAW,EACfT,EAAWG,EAAQI,OAAO,EAAI,CAAC,EAAG,SAAU1B,CAAM,CAAE8B,CAAU,EAC1DP,EAAMG,OAAO,CAACI,EAAW,CAAG9B,EAAOH,KAAK,GACxC+B,EAAWG,KAAKC,GAAG,CAACJ,EAAU5B,EAAOR,MAAM,CAC/C,GACA,IAAI,CAACyC,aAAa,CAACL,EACvB,CAyMA,OA5LAP,EAAczC,SAAS,CAACqD,aAAa,CAAG,SAAUL,CAAQ,EACtD,IAAIL,EAAQ,IAAI,AAChB,CAAA,IAAI,CAACK,QAAQ,CAAGA,EAChBT,EAAW,IAAI,CAACO,OAAO,CAAE,SAAU1B,CAAM,CAAE8B,CAAU,EAC7C9B,EAAOR,MAAM,GAAKoC,GAClBL,CAAAA,EAAMG,OAAO,CAACI,EAAW,CAAG/B,EAAUC,EAAQ4B,EAAQ,CAE9D,EACJ,EAeAP,EAAczC,SAAS,CAACsD,UAAU,CAAG,SAAUC,CAAQ,CAAEP,CAAQ,EAC7D,IAAIL,EAAQ,IAAI,CAEhB,GADiB,KAAK,IAAlBK,GAAuBA,CAAAA,EAAW,CAAA,EAClCA,EAAW,GAAKO,EAAW,IAAI,CAACP,QAAQ,CAAE,CAC1C,IAAIQ,EAAW,EACfjB,EAAW,IAAI,CAACO,OAAO,CAAE,SAAU1B,CAAM,CAAE8B,CAAU,EACjDP,EAAMG,OAAO,CAACI,EAAW,CACrB3B,EAAOH,EAAQmC,EAAUP,GAAUlB,KAAK,CAC5C0B,EAAWpC,EAAOR,MAAM,AAC5B,GACA,IAAI,CAACoC,QAAQ,CAAGQ,CACpB,CACAlB,EAAU,IAAI,CAAE,kBAAmB,CAAEiB,SAAUA,EAAUP,SAAUA,CAAS,GAC5E,IAAI,CAACC,UAAU,CAAGT,GACtB,EAWAC,EAAczC,SAAS,CAACyD,SAAS,CAAG,SAAUP,CAAU,CAExDQ,CAAW,EACP,OAAO,IAAI,CAACZ,OAAO,CAACI,EAAW,AACnC,EAYAT,EAAczC,SAAS,CAAC2D,UAAU,CAAG,SAAUC,CAAW,CAE1DF,CAAW,EACP,IAAIf,EAAQ,IAAI,CAChB,MAAO,AAACiB,CAAAA,GAAelE,OAAOmE,IAAI,CAAC,IAAI,CAACf,OAAO,CAAA,EAAGgB,MAAM,CAAC,SAAUhB,CAAO,CAAEI,CAAU,EAElF,OADAJ,CAAO,CAACI,EAAW,CAAGP,EAAMG,OAAO,CAACI,EAAW,CACxCJ,CACX,EAAG,CAAC,EACR,EAaAL,EAAczC,SAAS,CAAC+D,MAAM,CAAG,SAAUR,CAAQ,CAAEK,CAAW,EAC5D,IAAIjB,EAAQ,IAAI,CAChB,MAAO,AAACiB,CAAAA,GAAelE,OAAOmE,IAAI,CAAC,IAAI,CAACf,OAAO,CAAA,EAAGkB,GAAG,CAAC,SAAUxE,CAAG,EAAI,IAAIyE,EAAI,OAAO,AAA8B,OAA7BA,CAAAA,EAAKtB,EAAMG,OAAO,CAACtD,EAAI,AAAD,GAAeyE,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,CAAE,CAACV,EAAS,AAAE,EACvK,EAmBAd,EAAczC,SAAS,CAACkE,SAAS,CAAG,SAAUhB,CAAU,CAAE9B,CAAM,CAAEmC,CAAQ,CAAEY,CAAW,EACnF,IAAIF,CACW,MAAK,IAAhB7C,GAAqBA,CAAAA,EAAS,EAAE,AAAD,EAClB,KAAK,IAAlBmC,GAAuBA,CAAAA,EAAW,CAAA,EACtC,IAAI,CAACa,UAAU,CAAEH,CAAAA,AAASA,CAATA,EAAK,CAAC,CAAA,CAAK,CAACf,EAAW,CAAG9B,EAAQ6C,CAAC,EAAIV,EAAUY,EACtE,EAmBA1B,EAAczC,SAAS,CAACoE,UAAU,CAAG,SAAUtB,CAAO,CAAES,CAAQ,CAAEY,CAAW,EACzE,IAAIxB,EAAQ,IAAI,CACZK,EAAW,IAAI,CAACA,QAAQ,CAC5BT,EAAWO,EAAS,SAAU1B,CAAM,CAAE8B,CAAU,EAC5CP,EAAMG,OAAO,CAACI,EAAW,CAAG9B,EAAOH,KAAK,GACxC+B,EAAW5B,EAAOR,MAAM,AAC5B,GACA,IAAI,CAACyC,aAAa,CAACL,GACbmB,CAAAA,MAAAA,EAAiD,KAAK,EAAIA,EAAYE,MAAM,AAAD,IAC7E/B,EAAU,IAAI,CAAE,mBAChB,IAAI,CAACW,UAAU,CAAGT,IAE1B,EAoBAC,EAAczC,SAAS,CAACsE,MAAM,CAAG,SAAUC,CAAG,CAAEhB,CAAQ,CAAEiB,CAAM,CAAEL,CAAW,EACxD,KAAK,IAAlBZ,GAAuBA,CAAAA,EAAW,IAAI,CAACP,QAAQ,AAAD,EAClD,IAAIF,EAAU,IAAI,CAACA,OAAO,CACtB2B,EAAgBD,EAAS,IAAI,CAACxB,QAAQ,CAAG,EAAIO,EAAW,EAC5DhB,EAAWgC,EAAK,SAAUG,CAAS,CAAExB,CAAU,EAC3C,IAAI9B,EAAS0B,CAAO,CAACI,EAAW,EACxB,AAACiB,CAAAA,MAAAA,EAAiD,KAAK,EAAIA,EAAYQ,UAAU,AAAD,IAAO,CAAA,GAAS,AAAI3D,MAAMyD,GAC9GrD,IACIoD,EACApD,EAASG,EAAOH,EAAQmC,EAAU,EAAG,CAAA,EAAM,CAACmB,EAAU,EAAE5C,KAAK,CAG7DV,CAAM,CAACmC,EAAS,CAAGmB,EAEvB5B,CAAO,CAACI,EAAW,CAAG9B,EAE9B,GACIqD,EAAgB,IAAI,CAACzB,QAAQ,EAC7B,IAAI,CAACK,aAAa,CAACoB,GAEjBN,CAAAA,MAAAA,EAAiD,KAAK,EAAIA,EAAYE,MAAM,AAAD,IAC7E/B,EAAU,IAAI,CAAE,gBAChB,IAAI,CAACW,UAAU,CAAGT,IAE1B,EACOC,CACX,IA0CImC,EAA+F9F,EAAoB,KACnH+F,EAAmH/F,EAAoBI,CAAC,CAAC0F,GAEtC9F,EAAoB,KAE3H,IAAIgG,EAAmIhG,EAAoB,KACvJiG,EAAuJjG,EAAoBI,CAAC,CAAC4F,GAE7KE,EAAuHlG,EAAoB,KAC3ImG,EAA2InG,EAAoBI,CAAC,CAAC8F,GAcjKE,EAAW,AAAC5E,IAA+E4E,QAAQ,CAInGC,EAAS,AAACJ,IAA2IK,WAAW,CAACD,MAAM,CAGvKE,EAAW,AAAC/E,IAA+E+E,QAAQ,CAAEC,EAAU,AAAChF,IAA+EgF,OAAO,CAAEC,EAAO,AAACjF,IAA+EiF,IAAI,CAAEC,EAAW,AAAClF,IAA+EkF,QAAQ,CAAEC,EAAa,AAACnF,IAA+EmF,UAAU,AAyDjf/G,EAlDOA,EAuRRA,GAA6BA,CAAAA,EAA2B,CAAC,CAAA,GArO/BgH,OAAO,CA5BhC,SAAiBC,CAAW,CAAEC,CAAU,EACpC,GAAIH,EAAWP,EAAU,iBAAkB,CACvC,IAAIjB,EAAKtF,EAAUqB,SAAS,CACxB6F,EAAgB5B,EAAG4B,aAAa,CAChCC,EAAkB7B,EAAG6B,eAAe,CACpCC,EAAoB9B,EAAG8B,iBAAiB,CACxCC,EAAkB/B,EAAG+B,eAAe,CACpCC,EAAmBhC,EAAGgC,gBAAgB,CACtCC,EAAkBjC,EAAGiC,eAAe,AAQxCP,CAAAA,EAAYQ,KAAK,CAACC,GAAG,CAACpG,SAAS,CAACqG,gBAAgB,CAAG,CAAA,EACnDhB,EAASM,EAAa,YAAaG,GACnCT,EAASM,EAAa,cAAeI,GACrCV,EAASM,EAAa,iBAAkBK,GACxCX,EAASM,EAAa,OAAQM,GAC9BZ,EAASM,EAAa,OAAQM,GAC9BZ,EAASM,EAAa,YAAaO,GACnCb,EAASO,EAAY,eAAgBC,GACrCR,EAASO,EAAY,eAAgBC,EACzC,CACA,OAAOF,CACX,EAqOAjH,EAAyBC,SAAS,CA3N9BA,EAA2B,WASvB,SAASA,EAAU2H,CAAM,EAIrB,IAAI,CAAC7C,SAAS,CAAG0B,EAAOnF,SAAS,CAACyD,SAAS,CAI/C,IAAI,CAAC8C,QAAQ,CAAGpB,EAAOnF,SAAS,CAACuG,QAAQ,CAIzC,IAAI,CAACC,SAAS,CAAGrB,EAAOnF,SAAS,CAACwG,SAAS,CAI3C,IAAI,CAACC,aAAa,CAAGtB,EAAOnF,SAAS,CAACyG,aAAa,CAInD,IAAI,CAACC,YAAY,CAAGvB,EAAOnF,SAAS,CAAC0G,YAAY,CACjD,IAAI,CAACC,KAAK,CAAGL,EAAOK,KAAK,CACzB,IAAI,CAACL,MAAM,CAAGA,EACd,IAAI,CAAC5D,OAAO,CAAG4D,EAAO5D,OAAO,CAACkE,OAAO,AACzC,CAwLA,OAlLAjI,EAAUqB,SAAS,CAAC6G,aAAa,CAAG,WAC3B,IAAI,CAACC,SAAS,EACf,CAAA,IAAI,CAACA,SAAS,CAAG,IAAI,CAACR,MAAM,CAACK,KAAK,CAACI,QAAQ,CAACC,IAAI,GAC3CC,QAAQ,CAAC,sCACTC,IAAI,CAAC,CACNC,OAAQ,EACZ,GACKC,GAAG,CAAC,IAAI,CAACd,MAAM,CAACe,WAAW,CAAA,EAEpC,IAAIC,EAAU,IAAI,CAACC,sBAAsB,EACzCD,CAAAA,GAAW,IAAI,CAACR,SAAS,CAACU,OAAO,CAACF,EACtC,EAQA3I,EAAUqB,SAAS,CAACuH,sBAAsB,CAAG,WACzC,IAAIZ,EAAQ,IAAI,CAACL,MAAM,CAACK,KAAK,CACzBc,EAAiB,IAAI,CAAC/E,OAAO,CACjC,GAAK+E,GAGL,IAAIC,EAAgBD,EAAeE,gBAAgB,EAAI,CAAC,EACpDC,EAAWH,EAAeG,QAAQ,CAClCC,EAAiBlB,EAAM9G,GAAG,CAAC4H,EAAe5E,EAAE,EAChD,GAAI,AAAEgF,aAA2BhD,KAC5B+C,GACAtC,EAAQuC,EAAeC,KAAK,GAC5BxC,EAAQuC,EAAeE,KAAK,GAGjC,IAAIC,EAAQ1C,EAAQsC,EAASK,CAAC,EACtBL,EAASK,CAAC,CACVJ,EAAeC,KAAK,CACxBI,EAAQ5C,EAAQsC,EAASO,CAAC,EACtBP,EAASO,CAAC,CACVN,EAAeE,KAAK,CACxBK,EAAMJ,EAASJ,CAAAA,EAASS,OAAO,EAAI,CAAA,EACnCC,EAAMJ,EAASN,CAAAA,EAASW,OAAO,EAAI,CAAA,EACnCC,EAAQd,EAAcc,KAAK,EAAI,EAC/BC,EAAQf,EAAcgB,MAAM,EAAI,IAAI,CAACpC,MAAM,CAACmC,KAAK,CACjDE,EAAYjB,EAAckB,SAAS,CACnCtB,EAAU,CACNjI,EAAG4F,IAA8HjF,SAAS,CAAC6I,SAAS,CAAC,CACjJ,CAAC,IACTb,EACAE,EAAM,CACE,CAAC,IACTE,EACAE,EAAI,CACC,CACLE,GACI,eAAgBA,CACpB,EAKJ,OAJK7B,EAAMmC,UAAU,GACjBxB,EAAQoB,MAAM,CAAGD,EACjBnB,EAAQsB,SAAS,CAAGD,GAEjBrB,GACX,EAMA3I,EAAUqB,SAAS,CAAC8F,eAAe,CAAG,WAC9B,IAAI,CAACO,gBAAgB,EAAI,IAAI,CAAC3D,OAAO,CAACkE,OAAO,GAC7C,IAAI,CAACmC,aAAa,CAAG,CAAA,EACrB,IAAI,CAACC,cAAc,CAAG,CAAA,EACtB,IAAI,CAACpC,OAAO,CAAG,IAAIjI,EAAU,IAAI,EAEzC,EAIAA,EAAUqB,SAAS,CAAC+F,iBAAiB,CAAG,WAEpC,OAAO,IAAI,CAACY,KAAK,CAACsC,eAAe,CACjC,IAAI,CAACrC,OAAO,EAAI,IAAI,CAACA,OAAO,CAACC,aAAa,EAC9C,EAMAlI,EAAUqB,SAAS,CAACgG,eAAe,CAAG,SAAUkD,CAAC,EAC7C,IAAIzB,EAAiB,IAAI,CAAC/E,OAAO,CAACkE,OAAO,CACrCuC,EAASD,EAAEE,SAAS,CACxB,GAAI3B,EAAgB,CAChB,IAAII,EAAiB,IAAI,CAAClB,KAAK,CAAC9G,GAAG,CAAC4H,EAAe5E,EAAE,EACjDgF,aAA2BhD,KAC3BS,EAAQuC,EAAeC,KAAK,GAC5BxC,EAAQuC,EAAeE,KAAK,IAC5BoB,CAAM,CAAC,EAAE,CAAGtB,EAAeC,KAAK,CAChCqB,CAAM,CAAC,EAAE,CAAGtB,EAAeE,KAAK,EAEpC,IAAIH,EAAWH,EAAeG,QAAQ,CAClCA,IACItC,EAAQsC,EAASK,CAAC,GAClBkB,CAAAA,CAAM,CAAC,EAAE,CAAGvB,EAASK,CAAC,AAADA,EAErB3C,EAAQsC,EAASO,CAAC,GAClBgB,CAAAA,CAAM,CAAC,EAAE,CAAGvB,EAASO,CAAC,AAADA,EAErBP,EAASS,OAAO,EAChBc,CAAAA,CAAM,CAAC,EAAE,EAAIvB,EAASS,OAAO,AAAD,EAE5BT,EAASW,OAAO,EAChBY,CAAAA,CAAM,CAAC,EAAE,EAAIvB,EAASW,OAAO,AAAD,EAGxC,CAEA,IAAIc,EAAS,IAAI,CAACC,KAAK,EAAI,IAAI,CAACA,KAAK,CAAC,IAAI,CAACC,KAAK,CAAC,CAC7C/D,EAAS6D,IACTF,CAAAA,CAAM,CAAC,EAAE,CAAGE,AAAS,EAATA,CAAS,EAEzBH,EAAEE,SAAS,CAAGD,CAClB,EAIAxK,EAAUqB,SAAS,CAACiG,gBAAgB,CAAG,WAEnC,IADIhC,EACAuF,EAAY,IAAI,CAAC7C,KAAK,CAACL,MAAM,AAEjC,AAAuB,QAAtBrC,CAAAA,EAAK,IAAI,CAACwF,MAAM,AAAD,GAAexF,AAAO,KAAK,IAAZA,GAAyBA,EAAGyF,OAAO,CAAC,SAAUC,CAAK,EAE9E,IAAIrD,EAASf,EAAKiE,EACd,SAAUlD,CAAM,EACZ,IAAIzD,EAAK,AAAC,CAAA,AAACyD,CAAAA,EAAOM,OAAO,EAAI,CAAC,CAAA,EAAGlE,OAAO,EAAI,CAAC,CAAA,EAAGG,EAAE,OACtD,EAAKA,GAGEA,IAAO8G,EAAM9G,EAAE,AAC1B,EAGAyD,CAAAA,GAAUA,EAAOsD,UAAU,CAAC,CAACtD,EAAOuD,OAAO,CAAE,CAAA,EACjD,EACJ,EAOAlL,EAAUqB,SAAS,CAACkG,eAAe,CAAG,WAC9B,IAAI,CAACU,OAAO,GACZ,IAAI,CAACA,OAAO,CAACL,QAAQ,GACrB,IAAI,CAAC+C,KAAK,CAAG,IAAI,CAAC1C,OAAO,CAAC0C,KAAK,CAEvC,EAIA3K,EAAUqB,SAAS,CAAC6F,aAAa,CAAG,WAChC,IAAIiE,EAAQ,EAAE,CACd,IAAI,CAACxD,MAAM,CAACoD,OAAO,CAAC,SAAUpD,CAAM,EAEhC,IADIrC,EACA8F,EAAczD,EAAO5D,OAAO,CAACkE,OAAO,CACxCkD,EAAME,IAAI,CAAC,AAAmF,OAAlF/F,CAAAA,EAAK8F,MAAAA,EAAiD,KAAK,EAAIA,EAAYE,CAAC,AAADA,GAAehG,AAAO,KAAK,IAAZA,EAAgBA,EAAK,KAC/H,GACA,IAAIiG,EAAY,IAjV2BzH,EAiVJ,CAC/BK,QAAS,CACLmH,EAAGH,CACP,CACJ,GACJ,IAAI,CAACxD,MAAM,CAACoD,OAAO,CAAC,SAAUpD,CAAM,EAE5BA,EAAOM,OAAO,EACdN,CAAAA,EAAOM,OAAO,CAACsD,SAAS,CAAG5D,EAAO4D,SAAS,CAAGA,CAAQ,CAE9D,EACJ,EACOvL,CACX,IAQyB,IAAIwL,EAAmCzL,EAoHhE0L,EAAK9J,IACT6J,EAAgCzE,OAAO,CAAC0E,EAAEC,MAAM,CAAED,EAAEE,KAAK,EAC5B,IAAIlK,EAAwBE,IAG/C,OADYH,EAAoB,OAAU,AAE3C,GAET"}