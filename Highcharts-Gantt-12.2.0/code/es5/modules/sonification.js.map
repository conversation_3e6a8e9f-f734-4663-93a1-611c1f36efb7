{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/sonification\n * @requires highcharts\n *\n * Sonification module\n *\n * (c) 2010-2025 Highsoft AS\n * Author: <PERSON>ystein Mo<PERSON>g\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"Templating\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/sonification\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"Templating\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/sonification\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"Templating\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Templating\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__984__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ }),\n\n/***/ 984:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__984__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ sonification_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es5/es-modules/Extensions/Sonification/Options.js\n/* *\n *\n *  (c) 2009-2025 Øystein Moseng\n *\n *  Default options for sonification.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar Options = {\n    /**\n     * Options for configuring sonification and audio charts. Requires the\n     * [sonification module](https://code.highcharts.com/modules/sonification.js)\n     * to be loaded.\n     *\n     * @sample  highcharts/demo/all-instruments\n     *          All predefined instruments\n     * @sample  highcharts/demo/audio-boxplot\n     *          Audio boxplots\n     * @sample  highcharts/demo/plotline-context\n     *          Context tracks\n     * @sample  highcharts/demo/sonification-music\n     *          Musical chart\n     *\n     * @since        11.0.0\n     * @requires     modules/sonification\n     * @optionparent sonification\n     */\n    sonification: {\n        /**\n         * Global tracks to add to every series.\n         *\n         * Defined as an array of either instrument or speech tracks,\n         * or a combination.\n         *\n         * @declare   Highcharts.SonificationTracksOptions\n         * @extends   sonification.defaultSpeechOptions\n         * @extends   sonification.defaultInstrumentOptions\n         * @type      {Array<*>}\n         * @apioption sonification.globalTracks\n         */\n        /**\n         * Rate mapping for speech tracks.\n         *\n         * @declare   Highcharts.SonificationTracksRateOptions\n         * @extends   sonification.defaultSpeechOptions.mapping.rate\n         * @apioption sonification.globalTracks.mapping.rate\n         */\n        /**\n         * Text mapping for speech tracks.\n         *\n         * @declare   Highcharts.SonificationTracksTextOptions\n         * @extends   sonification.defaultSpeechOptions.mapping.text\n         * @apioption sonification.globalTracks.mapping.text\n         */\n        /**\n         * Context tracks to add globally, an array of either instrument\n         * tracks, speech tracks, or a mix.\n         *\n         * Context tracks are not tied to data points, but play at a set\n         * interval - either based on time or on prop values.\n         *\n         * @sample  highcharts/demo/plotline-context\n         *          Using contexts\n         *\n         * @declare   Highcharts.SonificationContextTracksOptions\n         * @extends   sonification.globalTracks\n         * @type      {Array<*>}\n         * @apioption sonification.globalContextTracks\n         */\n        /**\n         * Set a context track to play periodically every `timeInterval`\n         * milliseconds while the sonification is playing.\n         *\n         * @sample  highcharts/demo/plotline-context\n         *          Using contexts\n         *\n         * @type      {number}\n         * @apioption sonification.globalContextTracks.timeInterval\n         */\n        /**\n         * Set a context track to play periodically every `valueInterval`\n         * units of a data property `valueProp` while the sonification is\n         * playing.\n         *\n         * For example, setting `valueProp` to `x` and `valueInterval` to 5\n         * will play the context track for every 5th X value.\n         *\n         * The context audio events will be mapped to time according to the\n         * prop value relative to the min/max values for that prop.\n         *\n         * @sample  highcharts/demo/plotline-context\n         *          Using contexts\n         *\n         * @type      {number}\n         * @apioption sonification.globalContextTracks.valueInterval\n         */\n        /**\n         * The point property to play context for when using `valueInterval`.\n         *\n         * @type      {string}\n         * @default   \"x\"\n         * @apioption sonification.globalContextTracks.valueProp\n         */\n        /**\n         * How to map context events to time when using the `valueInterval`\n         * option.\n         *\n         * @type      {\"linear\"|\"logarithmic\"}\n         * @default   \"linear\"\n         * @apioption sonification.globalContextTracks.valueMapFunction\n         */\n        /**\n         * Set up event handlers for the sonification\n         *\n         * @apioption sonification.events\n         */\n        /**\n         * Called on play.\n         *\n         * A context object is passed to the function, with properties `chart`\n         * and `timeline`.\n         *\n         * @type      {Function}\n         * @apioption sonification.events.onPlay\n         */\n        /**\n         * Called on pause, cancel, or if play is completed.\n         *\n         * A context object is passed to the function, with properties `chart`,\n         * `timeline` and `pointsPlayed`. `pointsPlayed` is an array of `Point`\n         * objects, referencing data points that were related to the audio\n         * events played.\n         *\n         * @type      {Function}\n         * @apioption sonification.events.onStop\n         */\n        /**\n         * Called when play is completed.\n         *\n         * A context object is passed to the function, with properties `chart`,\n         * `timeline` and `pointsPlayed`. `pointsPlayed` is an array of `Point`\n         * objects, referencing data points that were related to the audio\n         * events played.\n         *\n         * @type      {Function}\n         * @apioption sonification.events.onEnd\n         */\n        /**\n         * Called immediately when a play is requested.\n         *\n         * A context object is passed to the function, with properties `chart`\n         * and `timeline`.\n         *\n         * @type      {Function}\n         * @apioption sonification.events.beforePlay\n         */\n        /**\n         * Called before updating the sonification.\n         *\n         * A context object is passed to the function, with properties `chart`\n         * and `timeline`.\n         *\n         * @type      {Function}\n         * @apioption sonification.events.beforeUpdate\n         */\n        /**\n         * Called after updating the sonification.\n         *\n         * A context object is passed to the function, with properties `chart`\n         * and `timeline`.\n         *\n         * @type      {Function}\n         * @apioption sonification.events.afterUpdate\n         */\n        /**\n         * Called on the beginning of playing a series.\n         *\n         * A context object is passed to the function, with properties `series`\n         * and `timeline`.\n         *\n         * @type      {Function}\n         * @apioption sonification.events.onSeriesStart\n         */\n        /**\n         * Called when finished playing a series.\n         *\n         * A context object is passed to the function, with properties `series`\n         * and `timeline`.\n         *\n         * @type      {Function}\n         * @apioption sonification.events.onSeriesEnd\n         */\n        /**\n         * Called when attempting to play an adjacent point or series, and\n         * there is none.\n         *\n         * By default a percussive sound is played.\n         *\n         * A context object is passed to the function, with properties `chart`,\n         * `timeline`, and `attemptedNext`. `attemptedNext` is a boolean\n         * property that is `true` if the boundary hit was from trying to play\n         * the next series/point, and `false` if it was from trying to play the\n         * previous.\n         *\n         * @type      {Function}\n         * @apioption sonification.events.onBoundaryHit\n         */\n        /**\n         * Enable sonification functionality for the chart.\n         */\n        enabled: true,\n        /**\n         * The total duration of the sonification, in milliseconds.\n         */\n        duration: 6000,\n        /**\n         * The time to wait in milliseconds after each data series when playing\n         * the series one after the other.\n         *\n         * @sample highcharts/sonification/chart-earcon\n         *         Notification after series\n         *\n         * @see [order](#sonification.order)\n         */\n        afterSeriesWait: 700,\n        /**\n         * How long to wait between each recomputation of the sonification, if\n         * the chart updates rapidly. This avoids slowing down processes like\n         * panning. Given in milliseconds.\n         */\n        updateInterval: 200,\n        /**\n         * Overall/master volume for the sonification, from 0 to 1.\n         */\n        masterVolume: 0.7,\n        /**\n         * What order to play the data series in, either `sequential` where\n         * the series play individually one after the other, or `simultaneous`\n         * where the series play all at once.\n         *\n         * @sample highcharts/sonification/chart-simultaneous\n         *         Simultaneous sonification\n         *\n         * @type  {\"sequential\"|\"simultaneous\"}\n         */\n        order: 'sequential',\n        /**\n         * Show tooltip as the chart plays.\n         *\n         * Note that if multiple tracks that play at different times try to\n         * show the tooltip, it can be glitchy, so it is recommended in\n         * those cases to turn this on/off for individual tracks using the\n         * [showPlayMarker](#plotOptions.series.sonification.tracks.showPlayMarker)\n         * option.\n         *\n         * @see [showCrosshair](#sonification.showCrosshair)\n         */\n        showTooltip: true,\n        /**\n         * Show X and Y axis crosshairs (if they exist) as the chart plays.\n         *\n         * Note that if multiple tracks that play at different times try to\n         * show the crosshairs, it can be glitchy, so it is recommended in\n         * those cases to turn this on/off for individual tracks using the\n         * [showPlayMarker](#plotOptions.series.sonification.tracks.showPlayMarker)\n         * option.\n         *\n         * @see [showTooltip](#sonification.showTooltip)\n         * @see [crosshair](#xAxis.crosshair)\n         */\n        showCrosshair: true,\n        /**\n         * Options for grouping data points together when sonifying. This\n         * allows for the visual presentation to contain more points than what\n         * is being played. If not enabled, all visible / uncropped points are\n         * played.\n         *\n         * @see [series.cropThreshold](#plotOptions.series.cropThreshold)\n         */\n        pointGrouping: {\n            /**\n             * Whether or not to group points\n             */\n            enabled: true,\n            /**\n             * The size of each group in milliseconds. Audio events closer than\n             * this are grouped together.\n             */\n            groupTimespan: 15,\n            /**\n             * The grouping algorithm, deciding which points to keep when\n             * grouping a set of points together. By default `\"minmax\"` is\n             * used, which keeps both the minimum and maximum points.\n             *\n             * The other algorithms will either keep the first point in the\n             * group (time wise), last point, middle point, or both the first\n             * and the last point.\n             *\n             * The timing of the resulting point(s) is then adjusted to play\n             * evenly, regardless of its original position within the group.\n             *\n             * @type {\"minmax\"|\"first\"|\"last\"|\"middle\"|\"firstlast\"}\n             */\n            algorithm: 'minmax',\n            /**\n             * The data property for each point to compare when deciding which\n             * points to keep in the group.\n             *\n             * By default it is \"y\", which means that if the `\"minmax\"`\n             * algorithm is used, the two points the group with the lowest and\n             * highest `y` value will be kept, and the others not played.\n             */\n            prop: 'y'\n        },\n        /**\n         * Default sonification options for all instrument tracks.\n         *\n         * If specific options are also set on individual tracks or per\n         * series, those will override these options.\n         *\n         * @sample  highcharts/sonification/point-sonify\n         *          Sonify points on click\n         *\n         * @declare Highcharts.SonificationInstrumentOptions\n         */\n        defaultInstrumentOptions: {\n            /**\n             * Round pitch mapping to musical notes.\n             *\n             * If `false`, will play the exact mapped note, even if it is out\n             * of tune compared to the musical notes as defined by 440Hz\n             * standard tuning.\n             */\n            roundToMusicalNotes: true,\n            /**\n             * Type of track. Always `\"instrument\"` for instrument tracks, and\n             * `\"speech\"` for speech tracks.\n             *\n             * @declare    Highcharts.SonifcationTypeValue\n             * @type       {string}\n             * @default    instrument\n             * @validvalue [\"instrument\",\"speech\"]\n             * @apioption  sonification.defaultInstrumentOptions.type\n             */\n            /**\n             * Show play marker (tooltip and/or crosshair) for a track.\n             *\n             * @type      {boolean}\n             * @default   true\n             * @apioption sonification.defaultInstrumentOptions.showPlayMarker\n             */\n            /**\n             * Name to use for a track when exporting to MIDI.\n             * By default it uses the series name if the track is related to\n             * a series.\n             *\n             * @type      {string}\n             * @apioption sonification.defaultInstrumentOptions.midiName\n             */\n            /**\n             * Options for point grouping, specifically for instrument tracks.\n             *\n             * @declare   Highcharts.SonificationInstrumentPointGroupingOptions\n             * @extends   sonification.pointGrouping\n             * @apioption sonification.defaultInstrumentOptions.pointGrouping\n             */\n            /**\n             * Define a condition for when a track should be active and not.\n             *\n             * Can either be a function callback or a configuration object.\n             *\n             * If a function is used, it should return a `boolean` for whether\n             * or not the track should be active. The function is called for\n             * each audio event, and receives a parameter object with `time`,\n             * and potentially `point` and `value` properties depending on the\n             * track. `point` is available if the audio event is related to a\n             * data point. `value` is available if the track is used as a\n             * context track, and `valueInterval` is used.\n             *\n             * @sample highcharts/sonification/mapping-zones\n             *         Mapping zones\n             *\n             * @declare   Highcharts.SonificationInstrumentActiveWhenOptions\n             * @type      {Function|*}\n             * @apioption sonification.defaultInstrumentOptions.activeWhen\n             */\n            /**\n             * Track is only active when `prop` is above or at this value.\n             *\n             * @type      {number}\n             * @apioption sonification.defaultInstrumentOptions.activeWhen.min\n             */\n            /**\n             * Track is only active when `prop` is below or at this value.\n             *\n             * @type      {number}\n             * @apioption sonification.defaultInstrumentOptions.activeWhen.max\n             */\n            /**\n             * Track is only active when `prop` was below, and is now at or\n             * above this value.\n             *\n             * If both `crossingUp` and `crossingDown` are defined, the track\n             * is active if either condition is met.\n             *\n             * @type      {number}\n             * @apioption sonification.defaultInstrumentOptions.activeWhen.crossingUp\n             */\n            /**\n             * Track is only active when `prop` was above, and is now at or\n             * below this value.\n             *\n             * If both `crossingUp` and `crossingDown` are defined, the track\n             * is active if either condition is met.\n             *\n             * @type      {number}\n             * @apioption sonification.defaultInstrumentOptions.activeWhen.crossingDown\n             */\n            /**\n             * The point property to compare, for example `y` or `x`.\n             *\n             * @type      {string}\n             * @apioption sonification.defaultInstrumentOptions.activeWhen.prop\n             */\n            /**\n             * Instrument to use for playing.\n             *\n             * Can either be a string referencing a synth preset, or it can be\n             * a synth configuration object.\n             *\n             * @sample  highcharts/demo/all-instruments\n             *          Overview of available presets\n             * @sample  highcharts/sonification/custom-instrument\n             *          Custom instrument\n             *\n             * @type {string|Highcharts.SynthPatchOptionsObject}\n             */\n            instrument: 'piano',\n            /**\n             * Mapping options for the audio parameters.\n             *\n             * All parameters can be either:\n             *  - A string, referencing a point property to map to.\n             *  - A number, setting the value of the audio parameter directly.\n             *  - A callback function, returning the value programmatically.\n             *  - An object defining detailed configuration of the mapping.\n             *\n             * If a function is used, it should return the desired value for\n             * the audio parameter. The function is called for each audio event\n             * to be played, and receives a context object parameter with\n             * `time`, and potentially `point` and `value` depending on the\n             * track. `point` is available if the audio event is related to a\n             * data point, and `value` is available if the track is used for a\n             * context track using `valueInterval`.\n             *\n             * @sample  highcharts/sonification/mapping-overview\n             *          Overview of common mapping parameters\n             * @sample  highcharts/sonification/pitch-mapping\n             *          Various types of mapping used\n             * @sample  highcharts/sonification/polarity-invert\n             *          Inverted mapping to property\n             * @sample  highcharts/sonification/log-mapping\n             *          Logarithmic mapping to property\n             *\n             * @declare Highcharts.SonificationInstrumentMappingOptions\n             */\n            mapping: {\n                /**\n                 * The volume of notes, from 0 to 1.\n                 *\n                 * @declare   Highcharts.SonificationInstrumentVolumeOptions\n                 * @extends   sonification.defaultInstrumentOptions.mapping.time\n                 * @default   1\n                 * @apioption sonification.defaultInstrumentOptions.mapping.volume\n                 */\n                /**\n                 * Frequency in Hertz of notes. Overrides pitch mapping if set.\n                 *\n                 * @declare   Highcharts.SonificationInstrumentFrequencyOptions\n                 * @extends   sonification.defaultInstrumentOptions.mapping.time\n                 * @apioption sonification.defaultInstrumentOptions.mapping.frequency\n                 */\n                /**\n                 * Milliseconds to wait before playing, comes in addition to\n                 * the time determined by the `time` mapping.\n                 *\n                 * Can also be negative to play before the mapped time.\n                 *\n                 * @declare   Highcharts.SonificationInstrumentPlayDelayOptions\n                 * @extends   sonification.defaultInstrumentOptions.mapping.time\n                 * @apioption sonification.defaultInstrumentOptions.mapping.playDelay\n                 */\n                /**\n                 * Mapping options for tremolo effects.\n                 *\n                 * Tremolo is repeated changes of volume over time.\n                 *\n                 * @declare   Highcharts.SonificationInstrumentTremoloOptions\n                 * @apioption sonification.defaultInstrumentOptions.mapping.tremolo\n                 */\n                /**\n                 * Map to tremolo depth, from 0 to 1.\n                 *\n                 * This determines the intensity of the tremolo effect, how\n                 * much the volume changes.\n                 *\n                 * @declare   Highcharts.SonificationInstrumentTremoloDepthOptions\n                 * @extends   sonification.defaultInstrumentOptions.mapping.time\n                 * @apioption sonification.defaultInstrumentOptions.mapping.tremolo.depth\n                 */\n                /**\n                 * Map to tremolo speed, from 0 to 1.\n                 *\n                 * This determines the speed of the tremolo effect, how fast\n                 * the volume changes.\n                 *\n                 * @declare   Highcharts.SonificationInstrumentTremoloSpeedOptions\n                 * @extends   sonification.defaultInstrumentOptions.mapping.time\n                 * @apioption sonification.defaultInstrumentOptions.mapping.tremolo.speed\n                 */\n                /**\n                 * Mapping options for the lowpass filter.\n                 *\n                 * A lowpass filter lets low frequencies through, but stops high\n                 * frequencies, making the sound more dull.\n                 *\n                 * @declare   Highcharts.SonificationInstrumentLowpassOptions\n                 * @apioption sonification.defaultInstrumentOptions.mapping.lowpass\n                 */\n                /**\n                 * Map to filter frequency in Hertz from 1 to 20,000Hz.\n                 *\n                 * @declare   Highcharts.SonificationInstrumentLowpassFrequencyOptions\n                 * @extends   sonification.defaultInstrumentOptions.mapping.time\n                 * @apioption sonification.defaultInstrumentOptions.mapping.lowpass.frequency\n                 */\n                /**\n                 * Map to filter resonance in dB. Can be negative to cause a\n                 * dip, or positive to cause a bump.\n                 *\n                 * @declare   Highcharts.SonificationInstrumentLowpassResonanceOptions\n                 * @extends   sonification.defaultInstrumentOptions.mapping.time\n                 * @apioption sonification.defaultInstrumentOptions.mapping.lowpass.resonance\n                 */\n                /**\n                 * Mapping options for the highpass filter.\n                 *\n                 * A highpass filter lets high frequencies through, but stops\n                 * low frequencies, making the sound thinner.\n                 *\n                 * @declare   Highcharts.SonificationInstrumentHighpassOptions\n                 * @extends   sonification.defaultInstrumentOptions.mapping.lowpass\n                 * @apioption sonification.defaultInstrumentOptions.mapping.highpass\n                 */\n                /**\n                 * Time mapping determines what time each point plays. It is\n                 * defined as an offset in milliseconds, where 0 means it\n                 * plays immediately when the chart is sonified.\n                 *\n                 * By default time is mapped to `x`, meaning points with the\n                 * lowest `x` value plays first, and points with the highest\n                 * `x` value plays last.\n                 *\n                 * Can be set to a fixed value, a prop to map to, a function,\n                 * or a mapping object.\n                 *\n                 * @sample  highcharts/sonification/point-play-time\n                 *          Play points in order of Y value\n                 *\n                 * @declare Highcharts.SonificationInstrumentTimeOptions\n                 * @type    {string|number|Function|*}\n                 * @default \"x\"\n                 */\n                time: 'x',\n                /**\n                 * A point property to map the mapping parameter to.\n                 *\n                 * A negative sign `-` can be placed before the property name\n                 * to make mapping inverted.\n                 *\n                 * @sample  highcharts/sonification/polarity-invert\n                 *          Inverted mapping to property\n                 *\n                 * @type      {string}\n                 * @apioption sonification.defaultInstrumentOptions.mapping.time.mapTo\n                 */\n                /**\n                 * The minimum value for the audio parameter. This is the\n                 * lowest value the audio parameter will be mapped to.\n                 *\n                 * @type      {number}\n                 * @apioption sonification.defaultInstrumentOptions.mapping.time.min\n                 */\n                /**\n                 * The maximum value for the audio parameter. This is the\n                 * highest value the audio parameter will be mapped to.\n                 *\n                 * @type      {number}\n                 * @apioption sonification.defaultInstrumentOptions.mapping.time.max\n                 */\n                /**\n                 * What data values to map the parameter within.\n                 *\n                 * Mapping within `\"series\"` will make the lowest value point\n                 * in the series map to the min audio parameter value, and the\n                 * highest value will map to the max audio parameter.\n                 *\n                 * Mapping within `\"chart\"` will make the lowest value point in\n                 * the whole chart map to the min audio parameter value, and\n                 * the highest value in the whole chart will map to the max\n                 * audio parameter.\n                 *\n                 * You can also map within the X or Y axis of each series.\n                 *\n                 * @sample highcharts/sonification/mapping-within\n                 *         Mapping within demonstrated\n                 *\n                 * @type      {\"chart\"|\"series\"|\"xAxis\"|\"yAxis\"}\n                 * @apioption sonification.defaultInstrumentOptions.mapping.time.within\n                 */\n                /**\n                 * How to perform the mapping.\n                 * @sample highcharts/sonification/log-mapping\n                 *         Logarithmic mapping to property\n                 *\n                 * @type      {\"linear\"|\"logarithmic\"}\n                 * @apioption sonification.defaultInstrumentOptions.mapping.time.mapFunction\n                 */\n                /**\n                 * A fixed value to use for the prop when mapping.\n                 *\n                 * For example, if mapping to `y`, setting value to `4` will\n                 * map as if all points had a y value of 4.\n                 *\n                 * @sample highcharts/demo/plotline-context\n                 *         Map to fixed y value\n                 *\n                 * @type      {number}\n                 * @apioption sonification.defaultInstrumentOptions.mapping.time.value\n                 */\n                /**\n                 * Pan refers to the stereo panning position of the sound.\n                 * It is defined from -1 (left) to 1 (right).\n                 *\n                 * By default it is mapped to `x`, making the sound move from\n                 * left to right as the chart plays.\n                 *\n                 * Can be set to a fixed value, a prop to map to, a function,\n                 * or a mapping object.\n                 *\n                 * @extends sonification.defaultInstrumentOptions.mapping.time\n                 * @default \"x\"\n                 */\n                pan: 'x',\n                /**\n                 * Note duration determines for how long a note plays, in\n                 * milliseconds.\n                 *\n                 * It only affects instruments that are able to play\n                 * continuous sustained notes. Examples of these instruments\n                 * from the presets include `flute`, `saxophone`, `trumpet`,\n                 * `sawsynth`, `wobble`, `basic1`, `basic2`, `sine`,\n                 * `sineGlide`, `triangle`, `square`, `sawtooth`, `noise`,\n                 * `filteredNoise`, and `wind`.\n                 *\n                 * Can be set to a fixed value, a prop to map to, a function,\n                 * or a mapping object.\n                 *\n                 * @extends sonification.defaultInstrumentOptions.mapping.time\n                 * @default 200\n                 */\n                noteDuration: 200,\n                /**\n                 * Musical pitch refers to how high or low notes are played.\n                 *\n                 * By default it is mapped to `y` so low `y` values are played\n                 * with a lower pitch, and high values are played with a higher\n                 * pitch.\n                 *\n                 * Pitch mapping has a few extra features compared to other\n                 * audio parameters.\n                 *\n                 * Firstly, it accepts not only number values, but also string\n                 * values denoting note names. These are given in the form\n                 * `<note><octave>`, for example `\"c6\"` or `\"F#2\"`.\n                 *\n                 * Secondly, it is possible to map pitch to an array of notes.\n                 * In this case, the `[gapBetweenNotes](#sonification.defaultInstrumentOptions.mapping.gapBetweenNotes)`\n                 * mapping determines the delay between these notes.\n                 *\n                 * Thirdly, it is possible to define a musical scale to follow\n                 * when mapping.\n                 *\n                 * Can be set to a fixed value, an array, a prop to map to, a\n                 * function, or a mapping object.\n                 *\n                 * @sample  highcharts/sonification/pitch-mapping\n                 *          Various types of mapping used\n                 * @sample  highcharts/sonification/polarity-invert\n                 *          Inverted mapping to property\n                 * @sample  highcharts/sonification/log-mapping\n                 *          Logarithmic mapping to property\n                 *\n                 * @declare Highcharts.SonificationInstrumentPitchOptions\n                 * @extends sonification.defaultInstrumentOptions.mapping.time\n                 * @type    {string|number|Function|Array<string|number>|*}\n                 */\n                pitch: {\n                    mapTo: 'y',\n                    min: 'c2',\n                    max: 'c6',\n                    within: 'yAxis'\n                },\n                /**\n                 * Map pitches to a musical scale. The scale is defined as an\n                 * array of semitone offsets from the root note.\n                 *\n                 * @sample  highcharts/sonification/all-scales\n                 *          Predefined scale presets\n                 *\n                 * @type      {Array<number>}\n                 * @apioption sonification.defaultInstrumentOptions.mapping.pitch.scale\n                 */\n                /**\n                 * Gap in milliseconds between notes if pitch is mapped to an\n                 * array of notes.\n                 *\n                 * Can be set to a fixed value, a prop to map to, a function,\n                 * or a mapping object.\n                 *\n                 * @sample  maps/demo/audio-map\n                 *          Mapping to gap between notes\n                 *\n                 * @extends sonification.defaultInstrumentOptions.mapping.time\n                 * @default 100\n                 */\n                gapBetweenNotes: 100\n            }\n        },\n        /**\n         * Default sonification options for all speech tracks.\n         *\n         * If specific options are also set on individual tracks or per\n         * series, those will override these options.\n         *\n         * @sample  highcharts/sonification/speak-values\n         *          Speak values\n         *\n         * @declare   Highcharts.SonificationSpeechOptions\n         * @extends   sonification.defaultInstrumentOptions\n         * @excluding roundToMusicalNotes, midiName, instrument\n         */\n        defaultSpeechOptions: {\n            /**\n             * Type of track. Always `\"instrument\"` for instrument tracks, and\n             * `\"speech\"` for speech tracks.\n             *\n             * @declare    Highcharts.SonifcationTypeValue\n             * @type       {string}\n             * @default    speech\n             * @validvalue [\"instrument\",\"speech\"]\n             * @apioption  sonification.defaultSpeechOptions.type\n             */\n            /**\n             * Name of the voice synthesis to prefer for speech tracks.\n             *\n             * If not available, falls back to the default voice for the\n             * selected language.\n             *\n             * Different platforms provide different voices for web speech\n             * synthesis.\n             *\n             * @type      {string}\n             * @apioption sonification.defaultSpeechOptions.preferredVoice\n             */\n            /**\n             * The language to speak in for speech tracks, as an IETF BCP 47\n             * language tag.\n             *\n             * @sample  maps/demo/audio-map\n             *          French language speech\n             */\n            language: 'en-US',\n            /**\n             * Mapping configuration for the speech/audio parameters.\n             *\n             * All parameters except `text` can be either:\n             *  - A string, referencing a point property to map to.\n             *  - A number, setting the value of the speech parameter directly.\n             *  - A callback function, returning the value programmatically.\n             *  - An object defining detailed configuration of the mapping.\n             *\n             * If a function is used, it should return the desired value for\n             * the speech parameter. The function is called for each speech\n             * event to be played, and receives a context object parameter with\n             * `time`, and potentially `point` and `value` depending on the\n             * track. `point` is available if the audio event is related to a\n             * data point, and `value` is available if the track is used for a\n             * context track using `valueInterval`.\n             *\n             * @declare   Highcharts.SonificationSpeechMappingOptions\n             * @extends   sonification.defaultInstrumentOptions.mapping\n             * @excluding frequency, gapBetweenNotes, highpass, lowpass,\n             *            tremolo, noteDuration, pan\n             * @apioption sonification.defaultSpeechOptions.mapping\n             */\n            mapping: {\n                /**\n                 * Milliseconds to wait before playing, comes in addition to\n                 * the time determined by the `time` mapping.\n                 *\n                 * Can also be negative to play before the mapped time.\n                 *\n                 * @declare   Highcharts.SonificationSpeechPlayDelayOptions\n                 * @extends   sonification.defaultInstrumentOptions.mapping.time\n                 * @apioption sonification.defaultSpeechOptions.mapping.playDelay\n                 */\n                /**\n                 * Speech pitch (how high/low the voice is) multiplier.\n                 * @sample  highcharts/sonification/speak-values\n                 *          Speak values\n                 *\n                 * @declare   Highcharts.SonificationSpeechPitchOptions\n                 * @extends   sonification.defaultInstrumentOptions.mapping.time\n                 * @excluding scale\n                 * @type      {string|number|Function|*}\n                 * @default   1\n                 * @apioption sonification.defaultSpeechOptions.mapping.pitch\n                 */\n                /**\n                 * @default   undefined\n                 * @apioption sonification.defaultSpeechOptions.mapping.pitch.mapTo\n                 */\n                /**\n                 * @default   undefined\n                 * @apioption sonification.defaultSpeechOptions.mapping.pitch.min\n                 */\n                /**\n                 * @default   undefined\n                 * @apioption sonification.defaultSpeechOptions.mapping.pitch.max\n                 */\n                /**\n                 * @default   undefined\n                 * @apioption sonification.defaultSpeechOptions.mapping.pitch.within\n                 */\n                /**\n                 * The text to announce for speech tracks. Can either be a\n                 * format string or a function.\n                 *\n                 * If it is a function, it should return the format string to\n                 * announce. The function is called for each audio event, and\n                 * receives a parameter object with `time`, and potentially\n                 * `point` and `value` properties depending on the track.\n                 * `point` is available if the audio event is related to a data\n                 * point. `value` is available if the track is used as a\n                 * context track, and `valueInterval` is used.\n                 *\n                 * If it is a format string, in addition to normal string\n                 * content, format values can be accessed using bracket\n                 * notation. For example `\"Value is {point.y}%\"`.\n                 *\n                 * `time`, `point` and `value` are available to the format\n                 * strings similarly to with functions. Nested properties can\n                 * be accessed with dot notation, for example\n                 * `\"Density: {point.options.custom.density}\"`\n                 *\n                 * @sample highcharts/sonification/speak-values\n                 *         Speak values\n                 *\n                 * @type      {string|Function}\n                 * @apioption sonification.defaultSpeechOptions.mapping.text\n                 */\n                /**\n                 * @extends sonification.defaultInstrumentOptions.mapping.time\n                 * @default \"x\"\n                 */\n                time: 'x',\n                /**\n                 * Speech rate (speed) multiplier.\n                 * @extends sonification.defaultInstrumentOptions.mapping.time\n                 * @default 1.3\n                 */\n                rate: 1.3,\n                /**\n                 * Volume of the speech announcement.\n                 * @extends sonification.defaultInstrumentOptions.mapping.volume\n                 * @default 0.4\n                 */\n                volume: 0.4\n            },\n            pointGrouping: {\n                algorithm: 'last'\n            }\n        }\n    },\n    exporting: {\n        menuItemDefinitions: {\n            downloadMIDI: {\n                textKey: 'downloadMIDI',\n                onclick: function () {\n                    if (this.sonification) {\n                        this.sonification.downloadMIDI();\n                    }\n                }\n            },\n            playAsSound: {\n                textKey: 'playAsSound',\n                onclick: function () {\n                    var s = this.sonification;\n                    if (s && s.isPlaying()) {\n                        s.cancel();\n                    }\n                    else {\n                        this.sonify();\n                    }\n                }\n            }\n        }\n    },\n    /**\n     * @optionparent lang\n     * @private\n     */\n    lang: {\n        /**\n         * The text for the MIDI download menu item in the export menu.\n         * @requires modules/sonification\n         * @since 11.0.0\n         */\n        downloadMIDI: 'Download MIDI',\n        /**\n         * The text for the Play as sound menu item in the export menu.\n         * @requires modules/sonification\n         * @since 11.0.0\n         */\n        playAsSound: 'Play as sound'\n    }\n};\n/* harmony default export */ var Sonification_Options = (Options);\n/* *\n *\n *  API declarations\n *\n * */\n/**\n * Sonification/audio chart options for a series.\n *\n * @declare    Highcharts.SeriesSonificationOptions\n * @since      11.0.0\n * @requires   modules/sonification\n * @apioption  plotOptions.series.sonification\n */\n/**\n * Whether or not sonification is enabled for this series.\n *\n * @type       {boolean}\n * @default    true\n * @apioption  plotOptions.series.sonification.enabled\n */\n/**\n * Context tracks for this series. Context tracks are tracks that are not\n * tied to data points.\n *\n * Given as an array of instrument tracks, speech tracks, or a mix of both.\n *\n * @declare    Highcharts.SeriesSonificationContextTracksOptions\n * @type       {Array<*>}\n * @extends    sonification.globalContextTracks\n * @apioption  plotOptions.series.sonification.contextTracks\n */\n/**\n * Tracks for this series.\n *\n * Given as an array of instrument tracks, speech tracks, or a mix of both.\n *\n * @declare    Highcharts.SeriesSonificationTracksOptions\n * @type       {Array<*>}\n * @extends    sonification.globalTracks\n * @apioption  plotOptions.series.sonification.tracks\n */\n/**\n * Default options for all this series' instrument tracks.\n *\n * @declare    Highcharts.SeriesSonificationInstrumentOptions\n * @extends    sonification.defaultInstrumentOptions\n * @apioption  plotOptions.series.sonification.defaultInstrumentOptions\n */\n/**\n * Default options for all this series' speech tracks.\n *\n * @declare    Highcharts.SeriesSonificationSpeechOptions\n * @extends    sonification.defaultSpeechOptions\n * @apioption  plotOptions.series.sonification.defaultSpeechOptions\n */\n/**\n * Sonification point grouping options for this series.\n *\n * @declare    Highcharts.SeriesSonificationPointGroupingOptions\n * @extends    sonification.pointGrouping\n * @apioption  plotOptions.series.sonification.pointGrouping\n */\n/**\n * Event context object sent to sonification chart events.\n * @requires  modules/sonification\n * @interface Highcharts.SonificationChartEventCallbackContext\n */ /**\n* The relevant chart\n* @name Highcharts.SonificationChartEventCallbackContext#chart\n* @type {Highcharts.Chart|undefined}\n*/ /**\n* The points that were played, if any\n* @name Highcharts.SonificationChartEventCallbackContext#pointsPlayed\n* @type {Array<Highcharts.Point>|undefined}\n*/ /**\n* The playing timeline object with advanced and internal content\n* @name Highcharts.SonificationChartEventCallbackContext#timeline\n* @type {object|undefined}\n*/\n/**\n * Event context object sent to sonification series events.\n * @requires  modules/sonification\n * @interface Highcharts.SonificationSeriesEventCallbackContext\n */ /**\n* The relevant series\n* @name Highcharts.SonificationSeriesEventCallbackContext#series\n* @type {Highcharts.Series|undefined}\n*/ /**\n* The playing timeline object with advanced and internal content\n* @name Highcharts.SonificationSeriesEventCallbackContext#timeline\n* @type {object|undefined}\n*/\n/**\n * Callback function for sonification events on chart.\n * @callback Highcharts.SonificationChartEventCallback\n * @param {Highcharts.SonificationChartEventCallbackContext} e Sonification chart event context\n */\n/**\n * Callback function for sonification events on series.\n * @callback Highcharts.SonificationSeriesEventCallback\n * @param {Highcharts.SonificationSeriesEventCallbackContext} e Sonification series event context\n */\n(''); // Keep above doclets in JS file\n\n;// ./code/es5/es-modules/Extensions/Sonification/SynthPatch.js\n/* *\n *\n *  (c) 2009-2025 Øystein Moseng\n *\n *  Class representing a Synth Patch, used by Instruments in the\n *  sonification.js module.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar __assign = (undefined && undefined.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\n\nvar clamp = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).clamp, defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/**\n * Get the multiplier value from a pitch tracked multiplier. The parameter\n * specifies the multiplier at ca 3200Hz. It is 1 at ca 50Hz. In between\n * it is mapped logarithmically.\n * @private\n * @param {number} multiplier The multiplier to track.\n * @param {number} freq The current frequency.\n */\nfunction getPitchTrackedMultiplierVal(multiplier, freq) {\n    var a = 0.2414 * multiplier - 0.2414,\n        b = (3.5 - 1.7 * multiplier) / 1.8;\n    return a * Math.log(freq) + b;\n}\n/**\n * Schedule a mini ramp to volume at time - avoid clicks/pops.\n * @private\n * @param {Object} gainNode The gain node to schedule for.\n * @param {number} time The time in seconds to start ramp.\n * @param {number} vol The volume to ramp to.\n */\nfunction miniRampToVolAtTime(gainNode, time, vol) {\n    gainNode.gain.cancelScheduledValues(time);\n    gainNode.gain.setTargetAtTime(vol, time, SynthPatch.stopRampTime / 4);\n    gainNode.gain.setValueAtTime(vol, time + SynthPatch.stopRampTime);\n}\n/**\n * Schedule a gain envelope for a gain node.\n * @private\n * @param {Array<Object>} envelope The envelope to schedule.\n * @param {string} type Type of envelope, attack or release.\n * @param {number} time At what time (in seconds) to start envelope.\n * @param {Object} gainNode The gain node to schedule on.\n * @param {number} [volumeMultiplier] Volume multiplier for the envelope.\n */\nfunction scheduleGainEnvelope(envelope, type, time, gainNode, volumeMultiplier) {\n    if (volumeMultiplier === void 0) { volumeMultiplier = 1; }\n    var isAtk = type === 'attack',\n        gain = gainNode.gain;\n    gain.cancelScheduledValues(time);\n    if (!envelope.length) {\n        miniRampToVolAtTime(gainNode, time, isAtk ? volumeMultiplier : 0);\n        return;\n    }\n    if (envelope[0].t > 1) {\n        envelope.unshift({ t: 0, vol: isAtk ? 0 : 1 });\n    }\n    envelope.forEach(function (ep, ix) {\n        var prev = envelope[ix - 1], delta = prev ? (ep.t - prev.t) / 1000 : 0, startTime = time + (prev ? prev.t / 1000 + SynthPatch.stopRampTime : 0);\n        gain.setTargetAtTime(ep.vol * volumeMultiplier, startTime, Math.max(delta, SynthPatch.stopRampTime) / 2);\n    });\n}\n/**\n * Internal class used by Oscillator, representing a Pulse Oscillator node.\n * Combines two sawtooth oscillators to create a pulse by phase inverting and\n * delaying one of them.\n * @class\n * @private\n */\nvar PulseOscNode = /** @class */ (function () {\n    function PulseOscNode(context, options) {\n        this.pulseWidth = Math.min(Math.max(0, options.pulseWidth || 0.5));\n        var makeOsc = function () { return new OscillatorNode(context, {\n                type: 'sawtooth',\n                detune: options.detune,\n                frequency: Math.max(1,\n            options.frequency || 350)\n            }); };\n        this.sawOscA = makeOsc();\n        this.sawOscB = makeOsc();\n        this.phaseInverter = new GainNode(context, { gain: -1 });\n        this.masterGain = new GainNode(context);\n        this.delayNode = new DelayNode(context, {\n            delayTime: this.pulseWidth / this.sawOscA.frequency.value\n        });\n        this.sawOscA.connect(this.masterGain);\n        this.sawOscB.connect(this.phaseInverter);\n        this.phaseInverter.connect(this.delayNode);\n        this.delayNode.connect(this.masterGain);\n    }\n    PulseOscNode.prototype.connect = function (destination) {\n        this.masterGain.connect(destination);\n    };\n    // Polymorph with normal osc.frequency API\n    PulseOscNode.prototype.getFrequencyFacade = function () {\n        var pulse = this;\n        return {\n            cancelScheduledValues: function (fromTime) {\n                pulse.sawOscA.frequency.cancelScheduledValues(fromTime);\n                pulse.sawOscB.frequency.cancelScheduledValues(fromTime);\n                pulse.delayNode.delayTime.cancelScheduledValues(fromTime);\n                return pulse.sawOscA.frequency;\n            },\n            setValueAtTime: function (frequency, time) {\n                this.cancelScheduledValues(time);\n                pulse.sawOscA.frequency.setValueAtTime(frequency, time);\n                pulse.sawOscB.frequency.setValueAtTime(frequency, time);\n                pulse.delayNode.delayTime.setValueAtTime(Math.round(10000 * pulse.pulseWidth / frequency) / 10000, time);\n                return pulse.sawOscA.frequency;\n            },\n            setTargetAtTime: function (frequency, time, timeConstant) {\n                this.cancelScheduledValues(time);\n                pulse.sawOscA.frequency\n                    .setTargetAtTime(frequency, time, timeConstant);\n                pulse.sawOscB.frequency\n                    .setTargetAtTime(frequency, time, timeConstant);\n                pulse.delayNode.delayTime.setTargetAtTime(Math.round(10000 * pulse.pulseWidth / frequency) / 10000, time, timeConstant);\n                return pulse.sawOscA.frequency;\n            }\n        };\n    };\n    PulseOscNode.prototype.getPWMTarget = function () {\n        return this.delayNode.delayTime;\n    };\n    PulseOscNode.prototype.start = function () {\n        this.sawOscA.start();\n        this.sawOscB.start();\n    };\n    PulseOscNode.prototype.stop = function (time) {\n        this.sawOscA.stop(time);\n        this.sawOscB.stop(time);\n    };\n    return PulseOscNode;\n}());\n/**\n * Internal class used by SynthPatch\n * @class\n * @private\n */\nvar Oscillator = /** @class */ (function () {\n    function Oscillator(audioContext, options, destination) {\n        this.audioContext = audioContext;\n        this.options = options;\n        this.fmOscillatorIx = options.fmOscillator;\n        this.vmOscillatorIx = options.vmOscillator;\n        this.createSoundSource();\n        this.createGain();\n        this.createFilters();\n        this.createVolTracking();\n        if (destination) {\n            this.connect(destination);\n        }\n    }\n    // Connect the node tree from destination down to oscillator,\n    // depending on which nodes exist. Done automatically unless\n    // no destination was passed to constructor.\n    Oscillator.prototype.connect = function (destination) {\n        [\n            this.lowpassNode,\n            this.highpassNode,\n            this.volTrackingNode,\n            this.vmNode,\n            this.gainNode,\n            this.whiteNoise,\n            this.pulseNode,\n            this.oscNode\n        ].reduce(function (prev, cur) {\n            return (cur ?\n                (cur.connect(prev), cur) :\n                prev);\n        }, destination);\n    };\n    Oscillator.prototype.start = function () {\n        if (this.oscNode) {\n            this.oscNode.start();\n        }\n        if (this.whiteNoise) {\n            this.whiteNoise.start();\n        }\n        if (this.pulseNode) {\n            this.pulseNode.start();\n        }\n    };\n    Oscillator.prototype.stopAtTime = function (time) {\n        if (this.oscNode) {\n            this.oscNode.stop(time);\n        }\n        if (this.whiteNoise) {\n            this.whiteNoise.stop(time);\n        }\n        if (this.pulseNode) {\n            this.pulseNode.stop(time);\n        }\n    };\n    Oscillator.prototype.setFreqAtTime = function (time, frequency, glideDuration) {\n        if (glideDuration === void 0) { glideDuration = 0; }\n        var opts = this.options,\n            f = clamp(pick(opts.fixedFrequency,\n            frequency) *\n                (opts.freqMultiplier || 1), 0, 21000),\n            oscTarget = this.getOscTarget(),\n            timeConstant = glideDuration / 5000;\n        if (oscTarget) {\n            oscTarget.cancelScheduledValues(time);\n            if (glideDuration && time - (this.lastUpdateTime || -1) > 0.01) {\n                oscTarget.setTargetAtTime(f, time, timeConstant);\n                oscTarget.setValueAtTime(f, time + timeConstant);\n            }\n            else {\n                oscTarget.setValueAtTime(f, time);\n            }\n        }\n        this.scheduleVolTrackingChange(f, time, glideDuration);\n        this.scheduleFilterTrackingChange(f, time, glideDuration);\n        this.lastUpdateTime = time;\n    };\n    // Get target for FM synthesis if another oscillator wants to modulate.\n    // Pulse nodes don't do FM, but do PWM instead.\n    Oscillator.prototype.getFMTarget = function () {\n        return this.oscNode && this.oscNode.detune ||\n            this.whiteNoise && this.whiteNoise.detune ||\n            this.pulseNode && this.pulseNode.getPWMTarget();\n    };\n    // Get target for volume modulation if another oscillator wants to modulate.\n    Oscillator.prototype.getVMTarget = function () {\n        return this.vmNode && this.vmNode.gain;\n    };\n    // Schedule one of the oscillator envelopes at a specified time in\n    // seconds (in AudioContext timespace).\n    Oscillator.prototype.runEnvelopeAtTime = function (type, time) {\n        if (!this.gainNode) {\n            return;\n        }\n        var env = (type === 'attack' ? this.options.attackEnvelope :\n                this.options.releaseEnvelope) || [];\n        scheduleGainEnvelope(env, type, time, this.gainNode, this.options.volume);\n    };\n    // Cancel any envelopes or frequency changes currently scheduled\n    Oscillator.prototype.cancelScheduled = function () {\n        if (this.gainNode) {\n            this.gainNode.gain\n                .cancelScheduledValues(this.audioContext.currentTime);\n        }\n        var oscTarget = this.getOscTarget();\n        if (oscTarget) {\n            oscTarget.cancelScheduledValues(0);\n        }\n        if (this.lowpassNode) {\n            this.lowpassNode.frequency.cancelScheduledValues(0);\n        }\n        if (this.highpassNode) {\n            this.highpassNode.frequency.cancelScheduledValues(0);\n        }\n        if (this.volTrackingNode) {\n            this.volTrackingNode.gain.cancelScheduledValues(0);\n        }\n    };\n    // Set the pitch dependent volume to fit some frequency at some time\n    Oscillator.prototype.scheduleVolTrackingChange = function (frequency, time, glideDuration) {\n        if (this.volTrackingNode) {\n            var v = getPitchTrackedMultiplierVal(this.options.volumePitchTrackingMultiplier || 1,\n                frequency),\n                rampTime = glideDuration ? glideDuration / 1000 :\n                    SynthPatch.stopRampTime;\n            this.volTrackingNode.gain.cancelScheduledValues(time);\n            this.volTrackingNode.gain.setTargetAtTime(v, time, rampTime / 5);\n            this.volTrackingNode.gain.setValueAtTime(v, time + rampTime);\n        }\n    };\n    // Set the pitch dependent filter frequency to fit frequency at some time\n    Oscillator.prototype.scheduleFilterTrackingChange = function (frequency, time, glideDuration) {\n        var opts = this.options,\n            rampTime = glideDuration ? glideDuration / 1000 :\n                SynthPatch.stopRampTime,\n            scheduleFilterTarget = function (filterNode,\n            filterOptions) {\n                var multiplier = getPitchTrackedMultiplierVal(filterOptions.frequencyPitchTrackingMultiplier || 1,\n            frequency),\n            f = clamp((filterOptions.frequency || 1000) * multiplier, 0, 21000);\n            filterNode.frequency.cancelScheduledValues(time);\n            filterNode.frequency.setTargetAtTime(f, time, rampTime / 5);\n            filterNode.frequency.setValueAtTime(f, time + rampTime);\n        };\n        if (this.lowpassNode && opts.lowpass) {\n            scheduleFilterTarget(this.lowpassNode, opts.lowpass);\n        }\n        if (this.highpassNode && opts.highpass) {\n            scheduleFilterTarget(this.highpassNode, opts.highpass);\n        }\n    };\n    Oscillator.prototype.createGain = function () {\n        var opts = this.options,\n            needsGainNode = defined(opts.volume) ||\n                opts.attackEnvelope && opts.attackEnvelope.length ||\n                opts.releaseEnvelope && opts.releaseEnvelope.length;\n        if (needsGainNode) {\n            this.gainNode = new GainNode(this.audioContext, {\n                gain: pick(opts.volume, 1)\n            });\n        }\n        // We always need VM gain, so make that\n        this.vmNode = new GainNode(this.audioContext);\n    };\n    // Create the oscillator or audio buffer acting as the sound source\n    Oscillator.prototype.createSoundSource = function () {\n        var opts = this.options,\n            ctx = this.audioContext,\n            frequency = (opts.fixedFrequency || 0) *\n                (opts.freqMultiplier || 1);\n        if (opts.type === 'whitenoise') {\n            var bSize = ctx.sampleRate * 2,\n                buffer = ctx.createBuffer(1,\n                bSize,\n                ctx.sampleRate),\n                data = buffer.getChannelData(0);\n            for (var i = 0; i < bSize; ++i) {\n                // More pleasant \"white\" noise with less variance than -1 to +1\n                data[i] = Math.random() * 1.2 - 0.6;\n            }\n            var wn = this.whiteNoise = ctx.createBufferSource();\n            wn.buffer = buffer;\n            wn.loop = true;\n        }\n        else if (opts.type === 'pulse') {\n            this.pulseNode = new PulseOscNode(ctx, {\n                detune: opts.detune,\n                pulseWidth: opts.pulseWidth,\n                frequency: frequency\n            });\n        }\n        else {\n            this.oscNode = new OscillatorNode(ctx, {\n                type: opts.type || 'sine',\n                detune: opts.detune,\n                frequency: frequency\n            });\n        }\n    };\n    // Lowpass/Highpass filters\n    Oscillator.prototype.createFilters = function () {\n        var opts = this.options;\n        if (opts.lowpass && opts.lowpass.frequency) {\n            this.lowpassNode = new BiquadFilterNode(this.audioContext, {\n                type: 'lowpass',\n                Q: opts.lowpass.Q || 1,\n                frequency: opts.lowpass.frequency\n            });\n        }\n        if (opts.highpass && opts.highpass.frequency) {\n            this.highpassNode = new BiquadFilterNode(this.audioContext, {\n                type: 'highpass',\n                Q: opts.highpass.Q || 1,\n                frequency: opts.highpass.frequency\n            });\n        }\n    };\n    // Gain node used for frequency dependent volume tracking\n    Oscillator.prototype.createVolTracking = function () {\n        var opts = this.options;\n        if (opts.volumePitchTrackingMultiplier &&\n            opts.volumePitchTrackingMultiplier !== 1) {\n            this.volTrackingNode = new GainNode(this.audioContext, {\n                gain: 1\n            });\n        }\n    };\n    // Get the oscillator frequency target\n    Oscillator.prototype.getOscTarget = function () {\n        return this.oscNode ? this.oscNode.frequency :\n            this.pulseNode && this.pulseNode.getFrequencyFacade();\n    };\n    return Oscillator;\n}());\n/**\n * The SynthPatch class. This class represents an instance and configuration\n * of the built-in Highcharts synthesizer. It can be used to play various\n * generated sounds.\n *\n * @sample highcharts/sonification/manual-using-synth\n *         Using Synth directly to sonify manually\n * @sample highcharts/sonification/custom-instrument\n *         Using custom Synth options with chart\n *\n * @requires modules/sonification\n *\n * @class\n * @name Highcharts.SynthPatch\n *\n * @param {AudioContext} audioContext\n *        The AudioContext to use.\n * @param {Highcharts.SynthPatchOptionsObject} options\n *        Configuration for the synth.\n */\nvar SynthPatch = /** @class */ (function () {\n    function SynthPatch(audioContext, options) {\n        var _this = this;\n        this.audioContext = audioContext;\n        this.options = options;\n        this.eqNodes = [];\n        this.midiInstrument = options.midiInstrument || 1;\n        this.outputNode = new GainNode(audioContext, { gain: 0 });\n        this.createEqChain(this.outputNode);\n        var inputNode = this.eqNodes.length ?\n                this.eqNodes[0] : this.outputNode;\n        this.oscillators = (this.options.oscillators || []).map(function (oscOpts) { return new Oscillator(audioContext, oscOpts, defined(oscOpts.fmOscillator) || defined(oscOpts.vmOscillator) ?\n            void 0 : inputNode); });\n        // Now that we have all oscillators, connect the ones\n        // that are used for modulation.\n        this.oscillators.forEach(function (osc) {\n            var connectTarget = function (targetFunc,\n                targetOsc) {\n                    if (targetOsc) {\n                        var target = targetOsc[targetFunc]();\n                    if (target) {\n                        osc.connect(target);\n                    }\n                }\n            };\n            if (defined(osc.fmOscillatorIx)) {\n                connectTarget('getFMTarget', _this.oscillators[osc.fmOscillatorIx]);\n            }\n            if (defined(osc.vmOscillatorIx)) {\n                connectTarget('getVMTarget', _this.oscillators[osc.vmOscillatorIx]);\n            }\n        });\n    }\n    /**\n     * Start the oscillators, but don't output sound.\n     * @function Highcharts.SynthPatch#startSilently\n     */\n    SynthPatch.prototype.startSilently = function () {\n        this.outputNode.gain.value = 0;\n        this.oscillators.forEach(function (o) { return o.start(); });\n    };\n    /**\n     * Stop the synth. It can't be started again.\n     * @function Highcharts.SynthPatch#stop\n     */\n    SynthPatch.prototype.stop = function () {\n        var curTime = this.audioContext.currentTime,\n            endTime = curTime + SynthPatch.stopRampTime;\n        miniRampToVolAtTime(this.outputNode, curTime, 0);\n        this.oscillators.forEach(function (o) { return o.stopAtTime(endTime); });\n        this.outputNode.disconnect();\n    };\n    /**\n     * Mute sound at time (in seconds).\n     * Will still run release envelope. Note: If scheduled multiple times in\n     * succession, the release envelope will run, and that could make sound.\n     * @function Highcharts.SynthPatch#silenceAtTime\n     * @param {number} time Time offset from now, in seconds\n     */\n    SynthPatch.prototype.silenceAtTime = function (time) {\n        if (!time && this.outputNode.gain.value < 0.01) {\n            this.outputNode.gain.value = 0;\n            return; // Skip if not needed\n        }\n        this.releaseAtTime((time || 0) + this.audioContext.currentTime);\n    };\n    /**\n     * Mute sound immediately.\n     * @function Highcharts.SynthPatch#mute\n     */\n    SynthPatch.prototype.mute = function () {\n        this.cancelScheduled();\n        miniRampToVolAtTime(this.outputNode, this.audioContext.currentTime, 0);\n    };\n    /**\n     * Play a frequency at time (in seconds).\n     * Time denotes when the attack ramp starts. Note duration is given in\n     * milliseconds. If note duration is not given, the note plays indefinitely.\n     * @function Highcharts.SynthPatch#silenceAtTime\n     * @param {number} time Time offset from now, in seconds\n     * @param {number} frequency The frequency to play at\n     * @param {number|undefined} noteDuration Duration to play, in milliseconds\n     */\n    SynthPatch.prototype.playFreqAtTime = function (time, frequency, noteDuration) {\n        var t = (time || 0) + this.audioContext.currentTime,\n            opts = this.options;\n        this.oscillators.forEach(function (o) {\n            o.setFreqAtTime(t, frequency, opts.noteGlideDuration);\n            o.runEnvelopeAtTime('attack', t);\n        });\n        scheduleGainEnvelope(opts.masterAttackEnvelope || [], 'attack', t, this.outputNode, opts.masterVolume);\n        if (noteDuration) {\n            this.releaseAtTime(t + noteDuration / 1000);\n        }\n    };\n    /**\n     * Cancel any scheduled actions\n     * @function Highcharts.SynthPatch#cancelScheduled\n     */\n    SynthPatch.prototype.cancelScheduled = function () {\n        this.outputNode.gain.cancelScheduledValues(this.audioContext.currentTime);\n        this.oscillators.forEach(function (o) { return o.cancelScheduled(); });\n    };\n    /**\n     * Connect the SynthPatch output to an audio node / destination.\n     * @function Highcharts.SynthPatch#connect\n     * @param {AudioNode} destinationNode The node to connect to.\n     * @return {AudioNode} The destination node, to allow chaining.\n     */\n    SynthPatch.prototype.connect = function (destinationNode) {\n        return this.outputNode.connect(destinationNode);\n    };\n    /**\n     * Create nodes for master EQ\n     * @private\n     */\n    SynthPatch.prototype.createEqChain = function (outputNode) {\n        var _this = this;\n        this.eqNodes = (this.options.eq || []).map(function (eqDef) {\n            return new BiquadFilterNode(_this.audioContext, __assign({ type: 'peaking' }, eqDef));\n        });\n        // Connect nodes\n        this.eqNodes.reduceRight(function (chain, node) {\n            node.connect(chain);\n            return node;\n        }, outputNode);\n    };\n    /**\n     * Fade by release envelopes at time\n     * @private\n     */\n    SynthPatch.prototype.releaseAtTime = function (time) {\n        var maxReleaseDuration = 0;\n        this.oscillators.forEach(function (o) {\n            var env = o.options.releaseEnvelope;\n            if (env && env.length) {\n                maxReleaseDuration = Math.max(maxReleaseDuration, env[env.length - 1].t);\n                o.runEnvelopeAtTime('release', time);\n            }\n        });\n        var masterEnv = this.options.masterReleaseEnvelope || [];\n        if (masterEnv.length) {\n            scheduleGainEnvelope(masterEnv, 'release', time, this.outputNode, this.options.masterVolume);\n            maxReleaseDuration = Math.max(maxReleaseDuration, masterEnv[masterEnv.length - 1].t);\n        }\n        miniRampToVolAtTime(this.outputNode, time + maxReleaseDuration / 1000, 0);\n    };\n    SynthPatch.stopRampTime = 0.012; // Ramp time to 0 when stopping sound\n    return SynthPatch;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Sonification_SynthPatch = (SynthPatch);\n/* *\n *\n *  API declarations\n *\n * */\n/**\n * An EQ filter definition for a low/highpass filter.\n * @requires modules/sonification\n * @interface Highcharts.SynthPatchPassFilter\n */ /**\n* Filter frequency.\n* @name Highcharts.SynthPatchPassFilter#frequency\n* @type {number|undefined}\n*/ /**\n* A pitch tracking multiplier similarly to the one for oscillator volume.\n* Affects the filter frequency.\n* @name Highcharts.SynthPatchPassFilter#frequencyPitchTrackingMultiplier\n* @type {number|undefined}\n*/ /**\n* Filter resonance bump/dip in dB. Defaults to 0.\n* @name Highcharts.SynthPatchPassFilter#Q\n* @type {number|undefined}\n*/\n/**\n * @typedef {Highcharts.Record<\"t\"|\"vol\",number>} Highcharts.SynthEnvelopePoint\n * @requires modules/sonification\n */\n/**\n * @typedef {Array<Highcharts.SynthEnvelopePoint>} Highcharts.SynthEnvelope\n * @requires modules/sonification\n */\n/**\n * @typedef {\"sine\"|\"square\"|\"sawtooth\"|\"triangle\"|\"whitenoise\"|\"pulse\"} Highcharts.SynthPatchOscillatorType\n * @requires modules/sonification\n */\n/**\n * Configuration for an oscillator for the synth.\n * @requires modules/sonification\n * @interface Highcharts.SynthPatchOscillatorOptionsObject\n */ /**\n* The type of oscillator. This describes the waveform of the oscillator.\n* @name Highcharts.SynthPatchOscillatorOptionsObject#type\n* @type {Highcharts.SynthPatchOscillatorType|undefined}\n*/ /**\n* A volume modifier for the oscillator. Defaults to 1.\n* @name Highcharts.SynthPatchOscillatorOptionsObject#volume\n* @type {number|undefined}\n*/ /**\n* A multiplier for the input frequency of the oscillator. Defaults to 1. If\n* this is for example set to 4, an input frequency of 220Hz will cause the\n* oscillator to play at 880Hz.\n* @name Highcharts.SynthPatchOscillatorOptionsObject#freqMultiplier\n* @type {number|undefined}\n*/ /**\n* Play a fixed frequency for the oscillator - ignoring input frequency. The\n* frequency multiplier is still applied.\n* @name Highcharts.SynthPatchOscillatorOptionsObject#fixedFrequency\n* @type {number|undefined}\n*/ /**\n* Applies a detuning of all frequencies. Set in cents. Defaults to 0.\n* @name Highcharts.SynthPatchOscillatorOptionsObject#detune\n* @type {number|undefined}\n*/ /**\n* Width of the pulse waveform. Only applies to \"pulse\" type oscillators. A\n* width of 0.5 is roughly equal to a square wave. This is the default.\n* @name Highcharts.SynthPatchOscillatorOptionsObject#pulseWidth\n* @type {number|undefined}\n*/ /**\n* Index of another oscillator to use as carrier, with this oscillator being\n* used as a volume modulator. The first oscillator in the array has index 0,\n* and so on. This option can be used to produce tremolo effects.\n* @name Highcharts.SynthPatchOscillatorOptionsObject#vmOscillator\n* @type {number|undefined}\n*/ /**\n* Index of another oscillator to use as carrier, with this oscillator being\n* used as a frequency modulator. Note: If the carrier is a pulse oscillator,\n* the modulation will be on pulse width instead of frequency, allowing for\n* PWM effects.\n* @name Highcharts.SynthPatchOscillatorOptionsObject#fmOscillator\n* @type {number|undefined}\n*/ /**\n* A tracking multiplier used for frequency dependent behavior. For example, by\n* setting the volume tracking multiplier to 0.01, the volume will be lower at\n* higher notes. The multiplier is a logarithmic function, where 1 is at ca\n* 50Hz, and you define the output multiplier for an input frequency around\n* 3.2kHz.\n* @name Highcharts.SynthPatchOscillatorOptionsObject#volumePitchTrackingMultiplier\n* @type {number|undefined}\n*/ /**\n* Volume envelope for note attack, specific to this oscillator.\n* @name Highcharts.SynthPatchOscillatorOptionsObject#attackEnvelope\n* @type {Highcharts.SynthEnvelope|undefined}\n*/ /**\n* Volume envelope for note release, specific to this oscillator.\n* @name Highcharts.SynthPatchOscillatorOptionsObject#releaseEnvelope\n* @type {Highcharts.SynthEnvelope|undefined}\n*/ /**\n* Highpass filter options for the oscillator.\n* @name Highcharts.SynthPatchOscillatorOptionsObject#highpass\n* @type {Highcharts.SynthPatchPassFilter|undefined}\n*/ /**\n* Lowpass filter options for the oscillator.\n* @name Highcharts.SynthPatchOscillatorOptionsObject#lowpass\n* @type {Highcharts.SynthPatchPassFilter|undefined}\n*/\n/**\n * An EQ filter definition for a bell filter.\n * @requires modules/sonification\n * @interface Highcharts.SynthPatchEQFilter\n */ /**\n* Filter frequency.\n* @name Highcharts.SynthPatchEQFilter#frequency\n* @type {number|undefined}\n*/ /**\n* Filter gain. Defaults to 0.\n* @name Highcharts.SynthPatchEQFilter#gain\n* @type {number|undefined}\n*/ /**\n* Filter Q. Defaults to 1. Lower numbers mean a wider bell.\n* @name Highcharts.SynthPatchEQFilter#Q\n* @type {number|undefined}\n*/\n/**\n * A set of options for the SynthPatch class.\n *\n * @requires modules/sonification\n *\n * @interface Highcharts.SynthPatchOptionsObject\n */ /**\n* Global volume modifier for the synth. Defaults to 1. Note that if the total\n* volume of all oscillators is too high, the browser's audio engine can\n* distort.\n* @name Highcharts.SynthPatchOptionsObject#masterVolume\n* @type {number|undefined}\n*/ /**\n* Time in milliseconds to glide between notes. Causes a glissando effect.\n* @name Highcharts.SynthPatchOptionsObject#noteGlideDuration\n* @type {number|undefined}\n*/ /**\n* MIDI instrument ID for the synth. Used with MIDI export of Timelines to have\n* tracks open with a similar instrument loaded when imported into other\n* applications. Defaults to 1, \"Acoustic Grand Piano\".\n* @name Highcharts.SynthPatchOptionsObject#midiInstrument\n* @type {number|undefined}\n*/ /**\n* Volume envelope for the overall attack of a note - what happens to the\n* volume when a note first plays. If the volume goes to 0 in the attack\n* envelope, the synth will not be able to play the note continuously/\n* sustained, and the notes will be staccato.\n* @name Highcharts.SynthPatchOptionsObject#masterAttackEnvelope\n* @type {Highcharts.SynthEnvelope|undefined}\n*/ /**\n* Volume envelope for the overall release of a note - what happens to the\n* volume when a note stops playing. If the release envelope starts at a higher\n* volume than the attack envelope ends, the volume will first rise to that\n* volume before falling when releasing a note. If the note is released while\n* the attack envelope is still in effect, the attack envelope is interrupted,\n* and the release envelope plays instead.\n* @name Highcharts.SynthPatchOptionsObject#masterReleaseEnvelope\n* @type {Highcharts.SynthEnvelope|undefined}\n*/ /**\n* Master EQ filters for the synth, affecting the overall sound.\n* @name Highcharts.SynthPatchOptionsObject#eq\n* @type {Array<Highcharts.SynthPatchEQFilter>|undefined}\n*/ /**\n* Array of oscillators to add to the synth.\n* @name Highcharts.SynthPatchOptionsObject#oscillators\n* @type {Array<Highcharts.SynthPatchOscillatorOptionsObject>|undefined}\n*/\n(''); // Keep above doclets in JS file\n\n;// ./code/es5/es-modules/Extensions/Sonification/InstrumentPresets.js\n/* *\n *\n *  (c) 2009-2025 Øystein Moseng\n *\n *  Presets for SynthPatch.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar InstrumentPresets = {\n    // PIANO ----------------------------\n    piano: {\n        masterVolume: 0.45,\n        masterAttackEnvelope: [\n            { t: 1, vol: 0.71 },\n            { t: 40, vol: 0.79 },\n            { t: 82, vol: 0.64 },\n            { t: 147, vol: 0.29 },\n            { t: 260, vol: 0.15 },\n            { t: 417, vol: 0.05 },\n            { t: 589, vol: 0 }\n        ],\n        eq: [\n            { frequency: 200, Q: 0.7, gain: 6 },\n            { frequency: 450, gain: 6 },\n            { frequency: 1300, gain: 2 },\n            { frequency: 2600, Q: 0.8, gain: 8 },\n            { frequency: 3500, Q: 0.8, gain: 6 },\n            { frequency: 6200, Q: 0.8, gain: 10 },\n            { frequency: 8000, gain: -23 },\n            { frequency: 10000, Q: 0.4, gain: -12 }\n        ],\n        oscillators: [{\n                type: 'pulse',\n                volume: 0.5,\n                pulseWidth: 0.55,\n                volumePitchTrackingMultiplier: 0.1,\n                lowpass: {\n                    frequency: 4.5,\n                    frequencyPitchTrackingMultiplier: 900,\n                    Q: -2\n                },\n                highpass: { frequency: 270 },\n                attackEnvelope: [{ t: 1, vol: 1 }],\n                releaseEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 282, vol: 0.64 },\n                    { t: 597, vol: 0 }\n                ]\n            }, {\n                type: 'whitenoise',\n                volume: 0.8,\n                lowpass: { frequency: 400 },\n                highpass: { frequency: 300 },\n                attackEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 19, vol: 0 }\n                ]\n            }]\n    },\n    // PLUCKED --------------------------\n    plucked: {\n        masterVolume: 0.5,\n        midiInstrument: 25,\n        masterAttackEnvelope: [\n            { t: 1, vol: 0.71 },\n            { t: 4, vol: 0.71 },\n            { t: 31, vol: 0.4 },\n            { t: 109, vol: 0.12 },\n            { t: 234, vol: 0.04 },\n            { t: 442, vol: 0 }\n        ],\n        eq: [\n            { frequency: 800, gain: -8 },\n            { frequency: 1400, Q: 4, gain: 4 },\n            { frequency: 1600, gain: -14 },\n            { frequency: 2200, gain: -8 },\n            { frequency: 3600, gain: -2 },\n            { frequency: 6400, Q: 2, gain: -6 }\n        ],\n        oscillators: [{\n                type: 'sawtooth',\n                volume: 0.9,\n                volumePitchTrackingMultiplier: 0.6,\n                highpass: { frequency: 100 },\n                lowpass: { frequency: 8000 },\n                releaseEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 315, vol: 0.56 },\n                    { t: 550, vol: 0 }\n                ]\n            }]\n    },\n    // FLUTE ----------------------------\n    flute: {\n        masterVolume: 1.1,\n        midiInstrument: 74,\n        noteGlideDuration: 30,\n        masterAttackEnvelope: [\n            { t: 0, vol: 0 },\n            { t: 29, vol: 1 },\n            { t: 76, vol: 0.48 },\n            { t: 600, vol: 0.36 }\n        ],\n        masterReleaseEnvelope: [\n            { t: 1, vol: 0.36 },\n            { t: 24, vol: 0.15 },\n            { t: 119, vol: 0 }\n        ],\n        eq: [\n            { frequency: 150, Q: 0.6, gain: -10 },\n            { frequency: 500, gain: 4 },\n            { frequency: 1100, gain: -4 },\n            { frequency: 2200, gain: -14 },\n            { frequency: 5000, gain: 8 },\n            { frequency: 6400, gain: 10 },\n            { frequency: 8000, gain: 12 },\n            { frequency: 10800, gain: 8 }\n        ],\n        oscillators: [{\n                type: 'triangle',\n                volume: 1,\n                volumePitchTrackingMultiplier: 0.4,\n                lowpass: {\n                    frequency: 12,\n                    frequencyPitchTrackingMultiplier: 100\n                },\n                highpass: {\n                    frequency: 200\n                }\n            }, {\n                type: 'sine',\n                fixedFrequency: 5,\n                volume: 0.2,\n                vmOscillator: 0,\n                attackEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 48, vol: 0 },\n                    { t: 225, vol: 0.05 },\n                    { t: 600, vol: 0.77 }\n                ]\n            }, {\n                type: 'whitenoise',\n                volume: 0.13,\n                lowpass: {\n                    frequency: 9000,\n                    Q: 3\n                },\n                highpass: {\n                    frequency: 6000,\n                    Q: 3\n                },\n                vmOscillator: 0,\n                attackEnvelope: [\n                    { t: 0, vol: 0 },\n                    { t: 26, vol: 1 },\n                    { t: 93, vol: 0.8 }\n                ]\n            }]\n    },\n    // LEAD -----------------------------\n    lead: {\n        masterVolume: 1,\n        midiInstrument: 20,\n        masterAttackEnvelope: [\n            { t: 1, vol: 0.81 },\n            { t: 98, vol: 0.5 },\n            { t: 201, vol: 0.18 },\n            { t: 377, vol: 0.04 },\n            { t: 586, vol: 0 },\n            { t: 586, vol: 0 }\n        ],\n        eq: [\n            { frequency: 200, gain: -6 },\n            { frequency: 400, gain: -8 },\n            { frequency: 800, Q: 0.5, gain: -10 },\n            { frequency: 1200, gain: 4 },\n            { frequency: 3600, gain: -4 },\n            { frequency: 4200, gain: -12 },\n            { frequency: 7400, gain: -14 },\n            { frequency: 10000, gain: 2 }\n        ],\n        oscillators: [{\n                type: 'triangle',\n                volume: 1.1,\n                volumePitchTrackingMultiplier: 0.6,\n                lowpass: { frequency: 5000 },\n                highpass: { frequency: 100 }\n            }, {\n                type: 'sawtooth',\n                volume: 0.4,\n                lowpass: { frequency: 7000 },\n                highpass: { frequency: 800, Q: 6 },\n                releaseEnvelope: [\n                    { t: 0, vol: 0.99 },\n                    { t: 200, vol: 0.83 },\n                    { t: 495, vol: 0 }\n                ]\n            }]\n    },\n    // VIBRAPHONE -----------------------\n    vibraphone: {\n        masterVolume: 1,\n        midiInstrument: 12,\n        masterAttackEnvelope: [\n            { t: 1, vol: 0 },\n            { t: 10, vol: 0.63 },\n            { t: 82, vol: 0.64 },\n            { t: 149, vol: 0.26 },\n            { t: 600, vol: 0 }\n        ],\n        eq: [\n            { frequency: 200, Q: 0.8, gain: -12 },\n            { frequency: 400, gain: -4 },\n            { frequency: 1600, Q: 0.5, gain: 6 },\n            { frequency: 2200, Q: 0.5, gain: 6 },\n            { frequency: 6400, gain: 4 },\n            { frequency: 12800, gain: 4 }\n        ],\n        oscillators: [{\n                type: 'sine',\n                volume: 1.5,\n                volumePitchTrackingMultiplier: 0.0000001,\n                attackEnvelope: [{ t: 1, vol: 1 }],\n                releaseEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 146, vol: 0.39 },\n                    { t: 597, vol: 0 }\n                ]\n            }, {\n                type: 'whitenoise',\n                volume: 0.03,\n                volumePitchTrackingMultiplier: 0.0001,\n                lowpass: {\n                    frequency: 900\n                },\n                highpass: {\n                    frequency: 800\n                },\n                attackEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 9, vol: 0 }\n                ]\n            }, {\n                type: 'sine',\n                freqMultiplier: 4,\n                volume: 0.15,\n                volumePitchTrackingMultiplier: 0.0001\n            }, {\n                type: 'sine',\n                fixedFrequency: 3,\n                volume: 6,\n                fmOscillator: 0,\n                releaseEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 190, vol: 0.41 },\n                    { t: 600, vol: 0 }\n                ]\n            }, {\n                type: 'sine',\n                fixedFrequency: 6,\n                volume: 3,\n                fmOscillator: 2\n            }, {\n                type: 'sine',\n                freqMultiplier: 9,\n                volume: 0.0005,\n                volumePitchTrackingMultiplier: 0.0001,\n                releaseEnvelope: [\n                    { t: 1, vol: 0.97 },\n                    { t: 530, vol: 0 }\n                ]\n            }]\n    },\n    // SAXOPHONE ------------------------\n    saxophone: {\n        masterVolume: 1,\n        midiInstrument: 67,\n        noteGlideDuration: 10,\n        masterAttackEnvelope: [\n            { t: 1, vol: 0.57 },\n            { t: 35, vol: 1 },\n            { t: 87, vol: 0.84 },\n            { t: 111, vol: 0.6 },\n            { t: 296, vol: 0.49 },\n            { t: 600, vol: 0.58 }\n        ],\n        masterReleaseEnvelope: [\n            { t: 1, vol: 0.58 },\n            { t: 47, vol: 0.16 },\n            { t: 119, vol: 0 }\n        ],\n        eq: [\n            { frequency: 200, gain: -2 },\n            { frequency: 600, gain: 2 },\n            { frequency: 800, gain: -10 },\n            { frequency: 1100, gain: -2 },\n            { frequency: 2200, gain: -2 },\n            { frequency: 3500, gain: 10 },\n            { frequency: 12800, gain: 4 }\n        ],\n        oscillators: [{\n                type: 'sawtooth',\n                volume: 0.45,\n                volumePitchTrackingMultiplier: 0.06,\n                lowpass: {\n                    frequency: 18,\n                    frequencyPitchTrackingMultiplier: 200\n                },\n                highpass: {\n                    frequency: 300\n                }\n            }, {\n                type: 'whitenoise',\n                fixedFrequency: 1,\n                volume: 0.4,\n                highpass: {\n                    frequency: 7000\n                },\n                vmOscillator: 0,\n                attackEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 51, vol: 1 },\n                    { t: 86, vol: 0.84 },\n                    { t: 500, vol: 0.78 }\n                ]\n            }, {\n                type: 'sine',\n                fixedFrequency: 4,\n                volume: 2,\n                fmOscillator: 0,\n                attackEnvelope: [\n                    { t: 0, vol: 0 },\n                    { t: 15, vol: 0.94 },\n                    { t: 79, vol: 1 },\n                    { t: 172, vol: 0.47 },\n                    { t: 500, vol: 0.26 }\n                ]\n            }, {\n                type: 'sine',\n                fixedFrequency: 7,\n                volume: 6,\n                fmOscillator: 0,\n                attackEnvelope: [\n                    { t: 0, vol: 0 },\n                    { t: 25, vol: 0.99 },\n                    { t: 85, vol: 0 },\n                    { t: 85, vol: 0 },\n                    { t: 387, vol: 0.02 },\n                    { t: 511, vol: 0.43 },\n                    { t: 600, vol: 0 }\n                ]\n            }]\n    },\n    // TRUMPET ------------------------\n    trumpet: {\n        masterVolume: 0.3,\n        midiInstrument: 57,\n        noteGlideDuration: 40,\n        masterAttackEnvelope: [\n            { t: 1, vol: 0 },\n            { t: 17, vol: 1 },\n            { t: 42, vol: 0.85 },\n            { t: 76, vol: 1 },\n            { t: 202, vol: 0.65 },\n            { t: 226, vol: 0.86 },\n            { t: 282, vol: 0.63 }\n        ],\n        masterReleaseEnvelope: [\n            { t: 1, vol: 0.62 },\n            { t: 34, vol: 0.14 },\n            { t: 63, vol: 0.21 },\n            { t: 96, vol: 0 }\n        ],\n        eq: [\n            { frequency: 200, Q: 0.6, gain: 10 },\n            { frequency: 600, Q: 0.5, gain: 6 },\n            { frequency: 1500, Q: 0.7, gain: 14 },\n            { frequency: 3200, Q: 2, gain: 8 },\n            { frequency: 3800, Q: 0.8, gain: 10 },\n            { frequency: 6200, gain: 12 },\n            { frequency: 8400, gain: -20 },\n            { frequency: 12800, Q: 0.5, gain: -18 }\n        ],\n        oscillators: [{\n                type: 'sawtooth',\n                volume: 0.15,\n                pulseWidth: 0.5,\n                volumePitchTrackingMultiplier: 0.5,\n                lowpass: { frequency: 1900, Q: 3 }\n            }, {\n                type: 'sine',\n                fixedFrequency: 6,\n                volume: 0.2,\n                vmOscillator: 0,\n                attackEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 102, vol: 0.13 },\n                    { t: 556, vol: 0.24 }\n                ]\n            }, {\n                type: 'whitenoise',\n                volume: 0.45,\n                highpass: { frequency: 7000, Q: 9 },\n                vmOscillator: 0,\n                attackEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 89, vol: 0.51 },\n                    { t: 577, vol: 0.29 }\n                ]\n            }, {\n                type: 'sine',\n                fixedFrequency: 5.7,\n                volume: 20,\n                fmOscillator: 0,\n                attackEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 89, vol: 1 },\n                    { t: 137, vol: 0.46 },\n                    { t: 283, vol: 0.15 },\n                    { t: 600, vol: 0.28 }\n                ]\n            }]\n    },\n    // SAWSYNTH --------------------------\n    sawsynth: {\n        masterVolume: 0.3,\n        midiInstrument: 51,\n        noteGlideDuration: 40,\n        masterAttackEnvelope: [\n            { t: 0, vol: 0.6 },\n            { t: 9, vol: 1 },\n            { t: 102, vol: 0.48 }\n        ],\n        eq: [{ frequency: 200, gain: -6 }],\n        oscillators: [{\n                type: 'sawtooth',\n                volume: 0.4,\n                volumePitchTrackingMultiplier: 0.3\n            }, {\n                type: 'sawtooth',\n                volume: 0.4,\n                detune: 11,\n                volumePitchTrackingMultiplier: 0.3\n            }, {\n                type: 'sawtooth',\n                volume: 0.4,\n                detune: -11,\n                volumePitchTrackingMultiplier: 0.3\n            }]\n    },\n    // BASIC1 ---------------------------\n    basic1: {\n        masterVolume: 1,\n        noteGlideDuration: 0,\n        masterReleaseEnvelope: [\n            { t: 1, vol: 0.36 },\n            { t: 24, vol: 0.15 },\n            { t: 119, vol: 0 }\n        ],\n        eq: [\n            { frequency: 150, Q: 0.6, gain: -12 },\n            { frequency: 1100, gain: -2 },\n            { frequency: 2200, gain: -16 },\n            { frequency: 5000, gain: 8 },\n            { frequency: 6400, gain: 10 },\n            { frequency: 8000, gain: 12 },\n            { frequency: 10800, gain: 8 }\n        ],\n        oscillators: [{\n                type: 'triangle',\n                volume: 1,\n                volumePitchTrackingMultiplier: 0.05,\n                lowpass: { frequency: 17, frequencyPitchTrackingMultiplier: 100 },\n                highpass: { frequency: 200 }\n            }, {\n                type: 'whitenoise',\n                volume: 0.04,\n                lowpass: { frequency: 9000, Q: 3 },\n                highpass: { frequency: 6000, Q: 3 },\n                vmOscillator: 0,\n                attackEnvelope: [\n                    { t: 0, vol: 0 },\n                    { t: 26, vol: 1 },\n                    { t: 71, vol: 0.73 }\n                ]\n            }]\n    },\n    // BASIC2 ---------------------------\n    basic2: {\n        masterVolume: 0.3,\n        eq: [\n            { frequency: 200, Q: 0.7, gain: 6 },\n            { frequency: 450, gain: 2 },\n            { frequency: 1300, gain: -2 },\n            { frequency: 2600, Q: 0.8, gain: 6 },\n            { frequency: 3500, Q: 0.8, gain: 6 },\n            { frequency: 6200, Q: 0.8, gain: 10 },\n            { frequency: 8000, gain: -18 },\n            { frequency: 10000, Q: 0.4, gain: -12 }\n        ],\n        oscillators: [{\n                type: 'pulse',\n                volume: 0.4,\n                pulseWidth: 0.55,\n                volumePitchTrackingMultiplier: 0.1,\n                lowpass: {\n                    frequency: 4.5,\n                    frequencyPitchTrackingMultiplier: 900,\n                    Q: -2\n                },\n                highpass: { frequency: 270 }\n            }]\n    },\n    // CHORD -------------------------------\n    chord: {\n        masterVolume: 1,\n        masterAttackEnvelope: [\n            { t: 1, vol: 0.79 },\n            { t: 27, vol: 0.86 },\n            { t: 62, vol: 0.81 },\n            { t: 150, vol: 0.35 },\n            { t: 408, vol: 0.04 },\n            { t: 600, vol: 0 }\n        ],\n        eq: [\n            { frequency: 200, gain: -8 },\n            { frequency: 600, Q: 2, gain: 4 },\n            { frequency: 800, gain: -10 },\n            { frequency: 1600, gain: -2 },\n            { frequency: 2200, gain: -6 },\n            { frequency: 3600, Q: 0.7, gain: -2 },\n            { frequency: 6400, gain: 6 },\n            { frequency: 12800, gain: 6 }\n        ],\n        oscillators: [{\n                type: 'triangle',\n                volume: 1.1,\n                volumePitchTrackingMultiplier: 0.05,\n                lowpass: { frequency: 8000 },\n                highpass: { frequency: 100 },\n                releaseEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 315, vol: 0.56 },\n                    { t: 540, vol: 0 }\n                ]\n            }, {\n                type: 'triangle',\n                freqMultiplier: 1.17,\n                volume: 0.4,\n                volumePitchTrackingMultiplier: 0.07,\n                lowpass: { frequency: 5000 },\n                highpass: { frequency: 100 },\n                releaseEnvelope: [\n                    { t: 0, vol: 1 },\n                    { t: 476, vol: 0 }\n                ]\n            }, {\n                type: 'triangle',\n                freqMultiplier: 1.58333,\n                volume: 0.7,\n                volumePitchTrackingMultiplier: 0.02,\n                highpass: { frequency: 200 },\n                releaseEnvelope: [\n                    { t: 0, vol: 1 },\n                    { t: 422, vol: 0.56 },\n                    { t: 577, vol: 0 }\n                ]\n            }, {\n                type: 'sine',\n                fixedFrequency: 10,\n                volume: 4,\n                fmOscillator: 0,\n                attackEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 157, vol: 0.65 }\n                ]\n            }, {\n                type: 'sine',\n                fixedFrequency: 5,\n                volume: 0.3,\n                vmOscillator: 2,\n                attackEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 155, vol: 0.91 },\n                    { t: 289, vol: 0.78 }\n                ]\n            }]\n    },\n    // WOBBLE ---------------------------\n    wobble: {\n        masterVolume: 0.9,\n        masterReleaseEnvelope: [\n            { t: 1, vol: 0.36 },\n            { t: 24, vol: 0.15 },\n            { t: 119, vol: 0 }\n        ],\n        eq: [\n            { frequency: 150, Q: 0.6, gain: -12 },\n            { frequency: 1100, gain: -2 },\n            { frequency: 2200, gain: -16 },\n            { frequency: 5000, gain: 8 },\n            { frequency: 6400, gain: 10 },\n            { frequency: 8000, gain: 12 },\n            { frequency: 10800, gain: 8 }\n        ],\n        oscillators: [{\n                type: 'triangle',\n                volume: 0.9,\n                volumePitchTrackingMultiplier: 0.1,\n                lowpass: { frequency: 17, frequencyPitchTrackingMultiplier: 100 },\n                highpass: { frequency: 200 }\n            }, {\n                type: 'whitenoise',\n                volume: 0.04,\n                lowpass: { frequency: 9000, Q: 3 },\n                highpass: { frequency: 6000, Q: 3 },\n                vmOscillator: 0,\n                attackEnvelope: [\n                    { t: 0, vol: 0 },\n                    { t: 26, vol: 1 },\n                    { t: 71, vol: 0.73 }\n                ]\n            }, {\n                type: 'sine',\n                freqMultiplier: 0.011,\n                volume: 30,\n                fmOscillator: 0\n            }]\n    },\n    // SINE -----------------------------\n    sine: {\n        masterVolume: 1,\n        oscillators: [{\n                type: 'sine',\n                volumePitchTrackingMultiplier: 0.07\n            }]\n    },\n    // SINE GLIDE -----------------------\n    sineGlide: {\n        masterVolume: 1,\n        noteGlideDuration: 100,\n        oscillators: [{\n                type: 'sine',\n                volumePitchTrackingMultiplier: 0.07\n            }]\n    },\n    // TRIANGLE -------------------------\n    triangle: {\n        masterVolume: 0.5,\n        oscillators: [{\n                type: 'triangle',\n                volume: 1,\n                volumePitchTrackingMultiplier: 0.07\n            }]\n    },\n    // SAWTOOTH -------------------------\n    sawtooth: {\n        masterVolume: 0.25,\n        midiInstrument: 82,\n        oscillators: [{\n                type: 'sawtooth',\n                volume: 0.3,\n                volumePitchTrackingMultiplier: 0.07\n            }]\n    },\n    // SQUARE ---------------------------\n    square: {\n        masterVolume: 0.3,\n        midiInstrument: 81,\n        oscillators: [{\n                type: 'square',\n                volume: 0.2,\n                volumePitchTrackingMultiplier: 0.07\n            }]\n    },\n    // PERCUSSION INSTRUMENTS ----------\n    chop: {\n        masterVolume: 1,\n        midiInstrument: 116,\n        masterAttackEnvelope: [{ t: 1, vol: 1 }, { t: 44, vol: 0 }],\n        oscillators: [{\n                type: 'whitenoise',\n                volume: 1,\n                lowpass: { frequency: 600 },\n                highpass: { frequency: 200 }\n            }]\n    },\n    shaker: {\n        masterVolume: 0.4,\n        midiInstrument: 116,\n        masterAttackEnvelope: [{ t: 1, vol: 1 }, { t: 44, vol: 0 }],\n        oscillators: [{\n                type: 'whitenoise',\n                volume: 1,\n                lowpass: { frequency: 6500 },\n                highpass: { frequency: 5000 }\n            }]\n    },\n    step: {\n        masterVolume: 1,\n        midiInstrument: 116,\n        masterAttackEnvelope: [{ t: 1, vol: 1 }, { t: 44, vol: 0 }],\n        eq: [\n            { frequency: 200, gain: -1 },\n            { frequency: 400, gain: -14 },\n            { frequency: 800, gain: 8 },\n            { frequency: 1000, Q: 5, gain: -24 },\n            { frequency: 1600, gain: 8 },\n            { frequency: 2200, gain: -10 },\n            { frequency: 5400, gain: 4 },\n            { frequency: 12800, gain: -36 }\n        ],\n        oscillators: [{\n                type: 'whitenoise',\n                volume: 1.5,\n                lowpass: { frequency: 300 },\n                highpass: { frequency: 100, Q: 6 }\n            }]\n    },\n    kick: {\n        masterVolume: 0.55,\n        masterAttackEnvelope: [\n            { t: 1, vol: 0.8 },\n            { t: 15, vol: 1 },\n            { t: 45, vol: 0.35 },\n            { t: 121, vol: 0.11 },\n            { t: 242, vol: 0 }\n        ],\n        eq: [\n            { frequency: 50, gain: 6 },\n            { frequency: 400, gain: -18 },\n            { frequency: 1600, gain: 18 }\n        ],\n        oscillators: [{\n                type: 'triangle',\n                fixedFrequency: 90,\n                volume: 1,\n                lowpass: { frequency: 300 },\n                attackEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 6, vol: 1 },\n                    { t: 45, vol: 0.01 }\n                ]\n            }, {\n                type: 'whitenoise',\n                volume: 0.4,\n                lowpass: { frequency: 200 },\n                attackEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 30, vol: 0 }\n                ]\n            }, {\n                type: 'triangle',\n                freqMultiplier: 0.1,\n                volume: 1,\n                lowpass: { frequency: 200 }\n            }]\n    },\n    shortnote: {\n        masterVolume: 0.8,\n        midiInstrument: 116,\n        masterAttackEnvelope: [\n            { t: 1, vol: 1 },\n            { t: 15, vol: 0 }\n        ],\n        eq: [\n            { frequency: 400, gain: -4 },\n            { frequency: 800, gain: -12 },\n            { frequency: 2400, gain: 4 },\n            { frequency: 7200, gain: -20 },\n            { frequency: 1000, Q: 5, gain: -12 },\n            { frequency: 5400, gain: -32 },\n            { frequency: 12800, gain: -14 }\n        ],\n        oscillators: [{\n                type: 'sawtooth',\n                volume: 0.6,\n                lowpass: { frequency: 1000 }\n            }, {\n                type: 'whitenoise',\n                volume: 0.2,\n                lowpass: { frequency: 10000 },\n                highpass: { frequency: 7000 },\n                attackEnvelope: [\n                    { t: 1, vol: 1 },\n                    { t: 10, vol: 0 }\n                ]\n            }, {\n                type: 'whitenoise',\n                volume: 1.3,\n                lowpass: { frequency: 700, Q: 4 },\n                highpass: { frequency: 250 }\n            }]\n    },\n    // NOISE ----------------------------\n    noise: {\n        masterVolume: 0.3,\n        midiInstrument: 122,\n        oscillators: [{\n                type: 'whitenoise'\n            }]\n    },\n    // FILTERED NOISE -------------------\n    filteredNoise: {\n        masterVolume: 0.3,\n        midiInstrument: 122,\n        eq: [\n            { frequency: 1600, gain: -8 },\n            { frequency: 2200, gain: -4 }\n        ],\n        oscillators: [{\n                type: 'whitenoise',\n                lowpass: {\n                    frequency: 5,\n                    frequencyPitchTrackingMultiplier: 1300,\n                    Q: 6\n                },\n                highpass: {\n                    frequency: 5,\n                    frequencyPitchTrackingMultiplier: 300,\n                    Q: 6\n                }\n            }]\n    },\n    // WIND -------------------------------\n    wind: {\n        masterVolume: 0.75,\n        midiInstrument: 122,\n        noteGlideDuration: 150,\n        masterReleaseEnvelope: [\n            { t: 0, vol: 1 },\n            { t: 124, vol: 0.24 },\n            { t: 281, vol: 0 }\n        ],\n        oscillators: [{\n                type: 'whitenoise',\n                volume: 1,\n                lowpass: {\n                    frequency: 100,\n                    frequencyPitchTrackingMultiplier: 6,\n                    Q: 23\n                },\n                highpass: {\n                    frequency: 170,\n                    frequencyPitchTrackingMultiplier: 6\n                }\n            }, {\n                type: 'sine',\n                freqMultiplier: 0.016,\n                volume: 1000,\n                fmOscillator: 0\n            }]\n    }\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Sonification_InstrumentPresets = (InstrumentPresets);\n/* *\n *\n *  API declarations\n *\n * */\n/**\n * @typedef {\"piano\"|\"plucked\"|\"flute\"|\"lead\"|\"vibraphone\"|\"saxophone\"|\"trumpet\"|\"sawsynth\"|\"basic1\"|\"basic2\"|\"chord\"|\"wobble\"|\"sine\"|\"sineGlide\"|\"triangle\"|\"sawtooth\"|\"square\"|\"chop\"|\"shaker\"|\"step\"|\"kick\"|\"shortnote\"|\"noise\"|\"filteredNoise\"|\"wind\"} Highcharts.SonificationSynthPreset\n * @requires modules/sonification\n */\n(''); // Keep above doclets in JS file\n\n;// ./code/es5/es-modules/Extensions/Sonification/SonificationInstrument.js\n/* *\n *\n *  (c) 2009-2025 Øystein Moseng\n *\n *  Class representing an Instrument with mappable parameters for sonification.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nvar SonificationInstrument_defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend;\n/**\n * The SonificationInstrument class. This class represents an instrument with\n * mapping capabilities. The instrument wraps a SynthPatch object, and extends\n * it with functionality such as panning, tremolo, and global low/highpass\n * filters.\n *\n * @sample highcharts/sonification/instrument-raw\n *         Using SonificationInstrument directly, with no chart.\n *\n * @requires modules/sonification\n *\n * @class\n * @name Highcharts.SonificationInstrument\n *\n * @param {AudioContext} audioContext\n *        The AudioContext to use.\n * @param {AudioNode} outputNode\n *        The destination node to connect to.\n * @param {Highcharts.SonificationInstrumentOptionsObject} options\n *        Configuration for the instrument.\n */\nvar SonificationInstrument = /** @class */ (function () {\n    function SonificationInstrument(audioContext, outputNode, options) {\n        this.audioContext = audioContext;\n        this.curParams = {};\n        this.midiTrackName = options.midiTrackName;\n        this.masterVolNode = new GainNode(audioContext);\n        this.masterVolNode.connect(outputNode);\n        this.volumeNode = new GainNode(audioContext);\n        this.createNodesFromCapabilities(extend({\n            pan: true\n        }, options.capabilities || {}));\n        this.connectCapabilityNodes(this.volumeNode, this.masterVolNode);\n        this.synthPatch = new Sonification_SynthPatch(audioContext, typeof options.synthPatch === 'string' ?\n            Sonification_InstrumentPresets[options.synthPatch] : options.synthPatch);\n        this.midiInstrument = this.synthPatch.midiInstrument || 1;\n        this.synthPatch.startSilently();\n        this.synthPatch.connect(this.volumeNode);\n    }\n    /**\n     * Set the overall volume.\n     * @function Highcharts.SonificationInstrument#setMasterVolume\n     * @param {number} volume The volume to set, from 0 to 1.\n     */\n    SonificationInstrument.prototype.setMasterVolume = function (volume) {\n        this.masterVolNode.gain.setTargetAtTime(volume, 0, SonificationInstrument.rampTime);\n    };\n    /**\n     * Schedule an instrument event at a given time offset, whether it is\n     * playing a note or changing the parameters of the instrument.\n     * @function Highcharts.SonificationInstrument#scheduleEventAtTime\n     * @param {number} time Time is given in seconds, where 0 is now.\n     * @param {Highcharts.SonificationInstrumentScheduledEventOptionsObject} params\n     * Parameters for the instrument event.\n     */\n    SonificationInstrument.prototype.scheduleEventAtTime = function (time, params) {\n        var mergedParams = extend(this.curParams,\n            params),\n            freq = SonificationInstrument_defined(params.frequency) ?\n                params.frequency : SonificationInstrument_defined(params.note) ?\n                SonificationInstrument.musicalNoteToFrequency(params.note) :\n                220;\n        if (SonificationInstrument_defined(freq)) {\n            this.synthPatch.playFreqAtTime(time, freq, mergedParams.noteDuration);\n        }\n        if (SonificationInstrument_defined(mergedParams.tremoloDepth) ||\n            SonificationInstrument_defined(mergedParams.tremoloSpeed)) {\n            this.setTremoloAtTime(time, mergedParams.tremoloDepth, mergedParams.tremoloSpeed);\n        }\n        if (SonificationInstrument_defined(mergedParams.pan)) {\n            this.setPanAtTime(time, mergedParams.pan);\n        }\n        if (SonificationInstrument_defined(mergedParams.volume)) {\n            this.setVolumeAtTime(time, mergedParams.volume);\n        }\n        if (SonificationInstrument_defined(mergedParams.lowpassFreq) ||\n            SonificationInstrument_defined(mergedParams.lowpassResonance)) {\n            this.setFilterAtTime('lowpass', time, mergedParams.lowpassFreq, mergedParams.lowpassResonance);\n        }\n        if (SonificationInstrument_defined(mergedParams.highpassFreq) ||\n            SonificationInstrument_defined(mergedParams.highpassResonance)) {\n            this.setFilterAtTime('highpass', time, mergedParams.highpassFreq, mergedParams.highpassResonance);\n        }\n    };\n    /**\n     * Schedule silencing the instrument at a given time offset.\n     * @function Highcharts.SonificationInstrument#silenceAtTime\n     * @param {number} time Time is given in seconds, where 0 is now.\n     */\n    SonificationInstrument.prototype.silenceAtTime = function (time) {\n        this.synthPatch.silenceAtTime(time);\n    };\n    /**\n     * Cancel currently playing sounds and any scheduled actions.\n     * @function Highcharts.SonificationInstrument#cancel\n     */\n    SonificationInstrument.prototype.cancel = function () {\n        this.synthPatch.mute();\n        [\n            this.tremoloDepth && this.tremoloDepth.gain,\n            this.tremoloOsc && this.tremoloOsc.frequency,\n            this.lowpassNode && this.lowpassNode.frequency,\n            this.lowpassNode && this.lowpassNode.Q,\n            this.highpassNode && this.highpassNode.frequency,\n            this.highpassNode && this.highpassNode.Q,\n            this.panNode && this.panNode.pan,\n            this.volumeNode.gain\n        ].forEach(function (p) { return (p && p.cancelScheduledValues(0)); });\n    };\n    /**\n     * Stop instrument and destroy it, cleaning up used resources.\n     * @function Highcharts.SonificationInstrument#destroy\n     */\n    SonificationInstrument.prototype.destroy = function () {\n        this.cancel();\n        this.synthPatch.stop();\n        if (this.tremoloOsc) {\n            this.tremoloOsc.stop();\n        }\n        [\n            this.tremoloDepth, this.tremoloOsc, this.lowpassNode,\n            this.highpassNode, this.panNode, this.volumeNode,\n            this.masterVolNode\n        ].forEach((function (n) { return n && n.disconnect(); }));\n    };\n    /**\n     * Schedule a pan value at a given time offset.\n     * @private\n     */\n    SonificationInstrument.prototype.setPanAtTime = function (time, pan) {\n        if (this.panNode) {\n            this.panNode.pan.setTargetAtTime(pan, time + this.audioContext.currentTime, SonificationInstrument.rampTime);\n        }\n    };\n    /**\n     * Schedule a filter configuration at a given time offset.\n     * @private\n     */\n    SonificationInstrument.prototype.setFilterAtTime = function (filter, time, frequency, resonance) {\n        var node = this[filter + 'Node'],\n            audioTime = this.audioContext.currentTime + time;\n        if (node) {\n            if (SonificationInstrument_defined(resonance)) {\n                node.Q.setTargetAtTime(resonance, audioTime, SonificationInstrument.rampTime);\n            }\n            if (SonificationInstrument_defined(frequency)) {\n                node.frequency.setTargetAtTime(frequency, audioTime, SonificationInstrument.rampTime);\n            }\n        }\n    };\n    /**\n     * Schedule a volume value at a given time offset.\n     * @private\n     */\n    SonificationInstrument.prototype.setVolumeAtTime = function (time, volume) {\n        if (this.volumeNode) {\n            this.volumeNode.gain.setTargetAtTime(volume, time + this.audioContext.currentTime, SonificationInstrument.rampTime);\n        }\n    };\n    /**\n     * Schedule a tremolo configuration at a given time offset.\n     * @private\n     */\n    SonificationInstrument.prototype.setTremoloAtTime = function (time, depth, speed) {\n        var audioTime = this.audioContext.currentTime + time;\n        if (this.tremoloDepth && SonificationInstrument_defined(depth)) {\n            this.tremoloDepth.gain.setTargetAtTime(depth, audioTime, SonificationInstrument.rampTime);\n        }\n        if (this.tremoloOsc && SonificationInstrument_defined(speed)) {\n            this.tremoloOsc.frequency.setTargetAtTime(15 * speed, audioTime, SonificationInstrument.rampTime);\n        }\n    };\n    /**\n     * Create audio nodes according to instrument capabilities\n     * @private\n     */\n    SonificationInstrument.prototype.createNodesFromCapabilities = function (capabilities) {\n        var ctx = this.audioContext;\n        if (capabilities.pan) {\n            this.panNode = new StereoPannerNode(ctx);\n        }\n        if (capabilities.tremolo) {\n            this.tremoloOsc = new OscillatorNode(ctx, {\n                type: 'sine',\n                frequency: 3\n            });\n            this.tremoloDepth = new GainNode(ctx);\n            this.tremoloOsc.connect(this.tremoloDepth);\n            this.tremoloDepth.connect(this.masterVolNode.gain);\n            this.tremoloOsc.start();\n        }\n        if (capabilities.filters) {\n            this.lowpassNode = new BiquadFilterNode(ctx, {\n                type: 'lowpass',\n                frequency: 20000\n            });\n            this.highpassNode = new BiquadFilterNode(ctx, {\n                type: 'highpass',\n                frequency: 0\n            });\n        }\n    };\n    /**\n     * Connect audio node chain from output down to input, depending on which\n     * nodes exist.\n     * @private\n     */\n    SonificationInstrument.prototype.connectCapabilityNodes = function (input, output) {\n        [\n            this.panNode,\n            this.lowpassNode,\n            this.highpassNode,\n            input\n        ].reduce(function (prev, cur) {\n            return (cur ?\n                (cur.connect(prev), cur) :\n                prev);\n        }, output);\n    };\n    /**\n     * Get number of notes from C0 from a string like \"F#4\"\n     * @static\n     * @private\n     */\n    SonificationInstrument.noteStringToC0Distance = function (note) {\n        var match = note.match(/^([a-g][#b]?)([0-8])$/i), semitone = match ? match[1] : 'a', wholetone = semitone[0].toLowerCase(), accidental = semitone[1], octave = match ? parseInt(match[2], 10) : 4, accidentalOffset = accidental === '#' ?\n                1 : accidental === 'b' ? -1 : 0;\n        return ({\n            c: 0, d: 2, e: 4, f: 5, g: 7, a: 9, b: 11\n        }[wholetone] || 0) + accidentalOffset + octave * 12;\n    };\n    /**\n     * Convert a note value to a frequency.\n     * @static\n     * @function Highcharts.SonificationInstrument#musicalNoteToFrequency\n     * @param {string|number} note\n     * Note to convert. Can be a string 'c0' to 'b8' or a number of semitones\n     * from c0.\n     * @return {number} The converted frequency\n     */\n    SonificationInstrument.musicalNoteToFrequency = function (note) {\n        var notesFromC0 = typeof note === 'string' ?\n                this.noteStringToC0Distance(note) : note;\n        return 16.3516 * Math.pow(2, Math.min(notesFromC0, 107) / 12);\n    };\n    SonificationInstrument.rampTime = Sonification_SynthPatch.stopRampTime / 4;\n    return SonificationInstrument;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Sonification_SonificationInstrument = (SonificationInstrument);\n/* *\n *\n *  API definitions\n *\n * */\n/**\n * Capabilities configuration for a SonificationInstrument.\n * @requires modules/sonification\n * @interface Highcharts.SonificationInstrumentCapabilitiesOptionsObject\n */ /**\n* Whether or not instrument should be able to pan. Defaults to `true`.\n* @name Highcharts.SonificationInstrumentCapabilitiesOptionsObject#pan\n* @type {boolean|undefined}\n*/ /**\n* Whether or not instrument should be able to use tremolo effects. Defaults\n* to `false`.\n* @name Highcharts.SonificationInstrumentCapabilitiesOptionsObject#tremolo\n* @type {boolean|undefined}\n*/ /**\n* Whether or not instrument should be able to use filter effects. Defaults\n* to `false`.\n* @name Highcharts.SonificationInstrumentCapabilitiesOptionsObject#filters\n* @type {boolean|undefined}\n*/\n/**\n * Configuration for a SonificationInstrument.\n * @requires modules/sonification\n * @interface Highcharts.SonificationInstrumentOptionsObject\n */ /**\n* The synth configuration for the instrument. Can be either a string,\n* referencing the instrument presets, or an actual SynthPatch configuration.\n* @name Highcharts.SonificationInstrumentOptionsObject#synthPatch\n* @type {Highcharts.SonificationSynthPreset|Highcharts.SynthPatchOptionsObject}\n* @sample highcharts/demo/all-instruments\n*      All instrument presets\n* @sample highcharts/sonification/custom-instrument\n*      Custom instrument preset\n*/ /**\n* Define additional capabilities for the instrument, such as panning, filters,\n* and tremolo effects.\n* @name Highcharts.SonificationInstrumentOptionsObject#capabilities\n* @type {Highcharts.SonificationInstrumentCapabilitiesOptionsObject|undefined}\n*/ /**\n* A track name to use for this instrument in MIDI export.\n* @name Highcharts.SonificationInstrumentOptionsObject#midiTrackName\n* @type {string|undefined}\n*/\n/**\n * Options for a scheduled event for a SonificationInstrument\n * @requires modules/sonification\n * @interface Highcharts.SonificationInstrumentScheduledEventOptionsObject\n */ /**\n* Number of semitones from c0, or a note string - such as \"c4\" or \"F#6\".\n* @name Highcharts.SonificationInstrumentScheduledEventOptionsObject#note\n* @type {number|string|undefined}\n*/ /**\n* Note frequency in Hertz. Overrides note, if both are given.\n* @name Highcharts.SonificationInstrumentScheduledEventOptionsObject#frequency\n* @type {number|undefined}\n*/ /**\n* Duration to play the note in milliseconds. If not given, the note keeps\n* playing indefinitely\n* @name Highcharts.SonificationInstrumentScheduledEventOptionsObject#noteDuration\n* @type {number|undefined}\n*/ /**\n* Depth/intensity of the tremolo effect - which is a periodic change in\n* volume. From 0 to 1.\n* @name Highcharts.SonificationInstrumentScheduledEventOptionsObject#tremoloDepth\n* @type {number|undefined}\n*/ /**\n* Speed of the tremolo effect, from 0 to 1.\n* @name Highcharts.SonificationInstrumentScheduledEventOptionsObject#tremoloSpeed\n* @type {number|undefined}\n*/ /**\n* Stereo panning value, from -1 (left) to 1 (right).\n* @name Highcharts.SonificationInstrumentScheduledEventOptionsObject#pan\n* @type {number|undefined}\n*/ /**\n* Volume of the instrument, from 0 to 1. Can be set independent of the\n* master/overall volume.\n* @name Highcharts.SonificationInstrumentScheduledEventOptionsObject#volume\n* @type {number|undefined}\n*/ /**\n* Frequency of the lowpass filter, in Hertz.\n* @name Highcharts.SonificationInstrumentScheduledEventOptionsObject#lowpassFreq\n* @type {number|undefined}\n*/ /**\n* Resonance of the lowpass filter, in dB. Can be negative for a dip, or\n* positive for a bump.\n* @name Highcharts.SonificationInstrumentScheduledEventOptionsObject#lowpassResonance\n* @type {number|undefined}\n*/ /**\n* Frequency of the highpass filter, in Hertz.\n* @name Highcharts.SonificationInstrumentScheduledEventOptionsObject#highpassFreq\n* @type {number|undefined}\n*/ /**\n* Resonance of the highpass filter, in dB. Can be negative for a dip, or\n* positive for a bump.\n* @name Highcharts.SonificationInstrumentScheduledEventOptionsObject#highpassResonance\n* @type {number|undefined}\n*/\n(''); // Keep above doclets in JS file\n\n;// ./code/es5/es-modules/Extensions/Sonification/SonificationSpeaker.js\n/* *\n *\n *  (c) 2009-2025 Øystein Moseng\n *\n *  Class representing a speech synthesis voice.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar SonificationSpeaker_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n/**\n * The SonificationSpeaker class. This class represents an announcer using\n * speech synthesis. It allows for scheduling speech announcements, as well\n * as speech parameter changes - including rate, volume and pitch.\n *\n * @sample highcharts/demo/sonification-navigation\n *         Demo using SonificationSpeaker directly for some announcements\n *\n * @requires modules/sonification\n *\n * @class\n * @name Highcharts.SonificationSpeaker\n *\n * @param {Highcharts.SonificationSpeakerOptionsObject} options\n *        Configuration for the speaker\n */\nvar SonificationSpeaker = /** @class */ (function () {\n    function SonificationSpeaker(options) {\n        this.options = options;\n        this.masterVolume = 1;\n        this.synthesis = window.speechSynthesis;\n        if (typeof speechSynthesis.onvoiceschanged !== 'undefined') {\n            speechSynthesis.onvoiceschanged = this.setVoice.bind(this);\n        }\n        this.setVoice();\n        this.scheduled = [];\n    }\n    /**\n     * Say a message using the speaker voice. Interrupts other currently\n     * speaking announcements from this speaker.\n     * @function Highcharts.SonificationSpeaker#say\n     * @param {string} message The message to speak.\n     * @param {SonificationSpeakerOptionsObject} [options]\n     * Optionally override speaker configuration.\n     */\n    SonificationSpeaker.prototype.say = function (message, options) {\n        if (this.synthesis) {\n            this.synthesis.cancel();\n            var utterance = new SpeechSynthesisUtterance(message);\n            if (this.voice) {\n                utterance.voice = this.voice;\n            }\n            utterance.rate = options && options.rate || this.options.rate || 1;\n            utterance.pitch = options && options.pitch ||\n                this.options.pitch || 1;\n            utterance.volume = SonificationSpeaker_pick(options && options.volume, this.options.volume, 1) * this.masterVolume;\n            this.synthesis.speak(utterance);\n        }\n    };\n    /**\n     * Schedule a message using the speaker voice.\n     * @function Highcharts.SonificationSpeaker#sayAtTime\n     * @param {number} time\n     * The time offset to speak at, in milliseconds from now.\n     * @param {string} message\n     * The message to speak.\n     * @param {SonificationSpeakerOptionsObject} [options]\n     * Optionally override speaker configuration.\n     */\n    SonificationSpeaker.prototype.sayAtTime = function (time, message, options) {\n        this.scheduled.push(setTimeout(this.say.bind(this, message, options), time));\n    };\n    /**\n     * Clear scheduled announcements, and stop current speech.\n     * @function Highcharts.SonificationSpeaker#cancel\n     */\n    SonificationSpeaker.prototype.cancel = function () {\n        this.scheduled.forEach(clearTimeout);\n        this.scheduled = [];\n        this.synthesis.cancel();\n    };\n    /**\n     * Stop speech and release any used resources\n     * @private\n     */\n    SonificationSpeaker.prototype.destroy = function () {\n        // Ran on TimelineChannel.destroy\n        // (polymorphism with SonificationInstrument).\n        // Currently all we need to do is cancel.\n        this.cancel();\n    };\n    /**\n     * Set speaker overall/master volume modifier. This affects all\n     * announcements, and applies in addition to the individual announcement\n     * volume.\n     * @function Highcharts.SonificationSpeaker#setMasterVolume\n     * @param {number} vol Volume from 0 to 1.\n     */\n    SonificationSpeaker.prototype.setMasterVolume = function (vol) {\n        this.masterVolume = vol;\n    };\n    /**\n     * Set the active synthesis voice for the speaker.\n     * @private\n     */\n    SonificationSpeaker.prototype.setVoice = function () {\n        if (this.synthesis) {\n            var name_1 = this.options.name,\n                lang = this.options.language || 'en-US',\n                voices = this.synthesis.getVoices(),\n                len = voices.length;\n            var langFallback = void 0;\n            for (var i = 0; i < len; ++i) {\n                if (name_1 && voices[i].name === name_1) {\n                    this.voice = voices[i];\n                    return;\n                }\n                if (!langFallback && voices[i].lang === lang) {\n                    langFallback = voices[i];\n                    if (!name_1) {\n                        break;\n                    }\n                }\n            }\n            this.voice = langFallback;\n        }\n    };\n    return SonificationSpeaker;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Sonification_SonificationSpeaker = (SonificationSpeaker);\n/* *\n *\n *  API declarations\n *\n * */\n/**\n * Configuration for a SonificationSpeaker.\n * @requires modules/sonification\n * @interface Highcharts.SonificationSpeakerOptionsObject\n */ /**\n* Name of the voice synthesis to use. If not found, reverts to the default\n* voice for the language chosen.\n* @name Highcharts.SonificationSpeakerOptionsObject#name\n* @type {string|undefined}\n*/ /**\n* The language of the voice synthesis. Defaults to `\"en-US\"`.\n* @name Highcharts.SonificationSpeakerOptionsObject#language\n* @type {string|undefined}\n*/ /**\n* The pitch modifier of the voice. Defaults to `1`. Set higher for a higher\n* voice pitch.\n* @name Highcharts.SonificationSpeakerOptionsObject#pitch\n* @type {number|undefined}\n*/ /**\n* The speech rate modifier. Defaults to `1`.\n* @name Highcharts.SonificationSpeakerOptionsObject#rate\n* @type {number|undefined}\n*/ /**\n* The speech volume, from 0 to 1. Defaults to `1`.\n* @name Highcharts.SonificationSpeakerOptionsObject#volume\n* @type {number|undefined}\n*/\n(''); // Keep above doclets in JS file\n\n;// ./code/es5/es-modules/Extensions/Sonification/TimelineChannel.js\n/* *\n *\n *  (c) 2009-2025 Øystein Moseng\n *\n *  Class representing a TimelineChannel with sonification events to play.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/**\n * Represents a channel of TimelineEvents for an engine (either an instrument\n * or a speaker).\n * @private\n */\nvar TimelineChannel = /** @class */ (function () {\n    function TimelineChannel(type, engine, showPlayMarker, events, muted) {\n        if (showPlayMarker === void 0) { showPlayMarker = false; }\n        this.type = type;\n        this.engine = engine;\n        this.showPlayMarker = showPlayMarker;\n        this.muted = muted;\n        this.events = events || [];\n    }\n    TimelineChannel.prototype.addEvent = function (event) {\n        var lastEvent = this.events[this.events.length - 1];\n        if (lastEvent && event.time < lastEvent.time) {\n            // Ensure we are sorted by time, so insert at the right place\n            var i = this.events.length;\n            while (i-- && this.events[i].time > event.time) { /* */ }\n            this.events.splice(i + 1, 0, event);\n        }\n        else {\n            this.events.push(event);\n        }\n        return event;\n    };\n    TimelineChannel.prototype.mute = function () {\n        this.muted = true;\n    };\n    TimelineChannel.prototype.unmute = function () {\n        this.muted = false;\n    };\n    TimelineChannel.prototype.cancel = function () {\n        this.engine.cancel();\n    };\n    TimelineChannel.prototype.destroy = function () {\n        this.engine.destroy();\n    };\n    return TimelineChannel;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Sonification_TimelineChannel = (TimelineChannel);\n/* *\n *\n *  API declarations\n *\n * */\n/**\n * A TimelineEvent object represents a scheduled audio event to play for a\n * SonificationTimeline.\n * @requires modules/sonification\n * @interface Highcharts.SonificationTimelineEvent\n */ /**\n* Time is given in milliseconds, where 0 is now.\n* @name Highcharts.SonificationTimelineEvent#time\n* @type {number}\n*/ /**\n* A reference to a data point related to the TimelineEvent. Populated when\n* sonifying points.\n* @name Highcharts.SonificationTimelineEvent#relatedPoint\n* @type {Highcharts.Point|undefined}\n*/ /**\n* Options for an instrument event to be played.\n* @name Highcharts.SonificationTimelineEvent#instrumentEventOptions\n* @type {Highcharts.SonificationInstrumentScheduledEventOptionsObject|undefined}\n*/ /**\n* Options for a speech event to be played.\n* @name Highcharts.SonificationTimelineEvent#speechOptions\n* @type {Highcharts.SonificationSpeakerOptionsObject|undefined}\n*/ /**\n* The message to speak for speech events.\n* @name Highcharts.SonificationTimelineEvent#message\n* @type {string|undefined}\n*/ /**\n* Callback to call when playing the event.\n* @name Highcharts.SonificationTimelineEvent#callback\n* @type {Function|undefined}\n*/\n(''); // Keep above doclets in JS file\n\n;// ./code/es5/es-modules/Extensions/Sonification/MIDI.js\n/* *\n *\n *  (c) 2009-2025 Øystein Moseng\n *\n *  Small MIDI file writer for sonification export.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n/* eslint-disable no-multi-spaces */\n\n\n\nvar MIDI_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\nvar freqToNote = function (f) { return Math.round(12 * Math.log(f) / Math.LN2 - 48.37632); }, b = function (byte, n) { return n >>> 8 * byte & 0xFF; }, getHeader = function (nTracks) { return [\n    0x4D, 0x54, 0x68, 0x64, // HD_TYPE\n    0, 0, 0, 6, // HD_SIZE\n    0, nTracks > 1 ? 1 : 0, // HD_FORMAT\n    b(1, nTracks), b(0, nTracks), // HD_NTRACKS\n    // SMTPE: 0xE7 0x28\n    // -25/40 time div gives us millisecond SMTPE, but not widely supported.\n    1, 0xF4 // HD_TIMEDIV, 500 ticks per beat = millisecond at 120bpm\n]; }, timeInfo = [0, 0xFF, 0x51, 0x03, 0x07, 0xA1, 0x20], // META_TEMPO\nvarLenEnc = function (n) {\n    var buf = n & 0x7F;\n    var res = [];\n    while (n >>= 7) { // eslint-disable-line no-cond-assign\n        buf <<= 8;\n        buf |= (n & 0x7F) | 0x80;\n    }\n    while (true) { // eslint-disable-line no-constant-condition\n        res.push(buf & 0xFF);\n        if (buf & 0x80) {\n            buf >>= 8;\n        }\n        else {\n            break;\n        }\n    }\n    return res;\n}, toMIDIEvents = function (events) {\n    var cachedVel,\n        cachedDur;\n    var res = [],\n        add = function (el) {\n            var ix = res.length;\n        while (ix-- && res[ix].timeMS > el.timeMS) { /* */ }\n        res.splice(ix + 1, 0, el);\n    };\n    events.forEach(function (e) {\n        var o = e.instrumentEventOptions || {},\n            t = e.time,\n            dur = cachedDur = MIDI_pick(o.noteDuration,\n            cachedDur),\n            tNOF = dur && e.time + dur,\n            ctrl = [{\n                    valMap: function (n) { return 64 + 63 * n & 0x7F; },\n                    data: {\n                        0x0A: o.pan, // Use MSB only, no need for fine adjust\n                        0x5C: o.tremoloDepth,\n                        0x5D: o.tremoloSpeed\n                    }\n                }, {\n                    valMap: function (n) { return 127 * n / 20000 & 0x7F; },\n                    data: {\n                        0x4A: o.lowpassFreq,\n                        0x4B: o.highpassFreq\n                    }\n                }, {\n                    valMap: function (n) {\n                        return 63 * Math.min(18,\n            Math.max(-18,\n            n)) / 18 + 63 & 0x7F;\n                },\n                data: {\n                    0x47: o.lowpassResonance,\n                    0x4C: o.highpassResonance\n                }\n            }], v = cachedVel = o.volume === void 0 ?\n            MIDI_pick(cachedVel, 127) : 127 * o.volume & 0x7F, freq = o.frequency, note = o.note || 0, noteVal = 12 + (freq ? freqToNote(freq) : // MIDI note #0 is C-1\n            typeof note === 'string' ? Sonification_SonificationInstrument\n                .noteStringToC0Distance(note) : note) & 0x7F;\n        // CTRL_CHANGE events\n        ctrl.forEach(function (ctrlDef) { return Object.keys(ctrlDef.data)\n            .forEach(function (ctrlSignal) {\n            var val = ctrlDef.data[ctrlSignal];\n            if (val !== void 0) {\n                add({\n                    timeMS: t,\n                    type: 'CTRL_CHG',\n                    data: [\n                        0xB0, parseInt(ctrlSignal, 10),\n                        ctrlDef.valMap(val)\n                    ]\n                });\n            }\n        }); });\n        // NON/NOF\n        if (tNOF) {\n            add({ timeMS: t, type: 'NON', data: [0x90, noteVal, v] });\n            add({ timeMS: tNOF, type: 'NOF', data: [0x80, noteVal, v] });\n        }\n    });\n    return res;\n}, getMetaEvents = function (midiTrackName, midiInstrument) {\n    var events = [];\n    if (midiInstrument) {\n        // Program Change MIDI event\n        events.push(0, 0xC0, midiInstrument & 0x7F);\n    }\n    if (midiTrackName) {\n        // Track name meta event\n        var textArr = [];\n        for (var i = 0; i < midiTrackName.length; ++i) {\n            var code = midiTrackName.charCodeAt(i);\n            if (code < 128) { // Keep ASCII only\n                textArr.push(code);\n            }\n        }\n        return events.concat([0, 0xFF, 0x03], varLenEnc(textArr.length), textArr);\n    }\n    return events;\n}, getTrackChunk = function (events, addTimeInfo, midiTrackName, midiInstrument) {\n    var prevTime = 0;\n    var metaEvents = getMetaEvents(midiTrackName,\n        midiInstrument),\n        trackEvents = toMIDIEvents(events).reduce(function (data,\n        e) {\n            var t = varLenEnc(e.timeMS - prevTime);\n        prevTime = e.timeMS;\n        return data.concat(t, e.data);\n    }, []);\n    var trackEnd = [0, 0xFF, 0x2F, 0],\n        size = (addTimeInfo ? timeInfo.length : 0) +\n            metaEvents.length +\n            trackEvents.length + trackEnd.length;\n    return [\n        0x4D, 0x54, 0x72, 0x6B, // TRK_TYPE\n        b(3, size), b(2, size), // TRK_SIZE\n        b(1, size), b(0, size)\n    ].concat(addTimeInfo ? timeInfo : [], metaEvents, trackEvents, trackEnd // SYSEX_TRACK_END\n    );\n};\n/**\n * Get MIDI data from a set of Timeline instrument channels.\n *\n * Outputs multi-track MIDI for Timelines with multiple channels.\n *\n * @private\n */\nfunction toMIDI(channels) {\n    var channelsToAdd = channels.filter(function (c) { return !!c.events.length; }),\n        numCh = channelsToAdd.length,\n        multiCh = numCh > 1;\n    return new Uint8Array(getHeader(multiCh ? numCh + 1 : numCh).concat(multiCh ? getTrackChunk([], true) : [], // Time info only\n    channelsToAdd.reduce(function (chunks, channel) {\n        var engine = channel.engine;\n        return chunks.concat(getTrackChunk(channel.events, !multiCh, engine.midiTrackName, engine.midiInstrument));\n    }, [])));\n}\n/* harmony default export */ var MIDI = (toMIDI);\n\n;// ./code/es5/es-modules/Extensions/DownloadURL.js\n/* *\n *\n *  (c) 2015-2025 Oystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n *  Mixin for downloading content in the browser\n *\n * */\n\n/* *\n *\n *  Imports\n *\n * */\n\nvar isSafari = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isSafari, win = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).win, doc = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).win.document;\n/* *\n *\n *  Constants\n *\n * */\nvar domurl = win.URL || win.webkitURL || win;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Convert base64 dataURL to Blob if supported, otherwise returns undefined.\n * @private\n * @function Highcharts.dataURLtoBlob\n * @param {string} dataURL\n *        URL to convert\n * @return {string|undefined}\n *         Blob\n */\nfunction dataURLtoBlob(dataURL) {\n    var parts = dataURL\n            .replace(/filename=.*;/, '')\n            .match(/data:([^;]*)(;base64)?,([A-Z+\\d\\/]+)/i);\n    if (parts &&\n        parts.length > 3 &&\n        (win.atob) &&\n        win.ArrayBuffer &&\n        win.Uint8Array &&\n        win.Blob &&\n        (domurl.createObjectURL)) {\n        // Try to convert data URL to Blob\n        var binStr = win.atob(parts[3]),\n            buf = new win.ArrayBuffer(binStr.length),\n            binary = new win.Uint8Array(buf);\n        for (var i = 0; i < binary.length; ++i) {\n            binary[i] = binStr.charCodeAt(i);\n        }\n        return domurl\n            .createObjectURL(new win.Blob([binary], { 'type': parts[1] }));\n    }\n}\n/**\n * Download a data URL in the browser. Can also take a blob as first param.\n *\n * @private\n * @function Highcharts.downloadURL\n * @param {string|global.URL} dataURL\n *        The dataURL/Blob to download\n * @param {string} filename\n *        The name of the resulting file (w/extension)\n * @return {void}\n */\nfunction downloadURL(dataURL, filename) {\n    var nav = win.navigator,\n        a = doc.createElement('a');\n    // IE specific blob implementation\n    // Don't use for normal dataURLs\n    if (typeof dataURL !== 'string' &&\n        !(dataURL instanceof String) &&\n        nav.msSaveOrOpenBlob) {\n        nav.msSaveOrOpenBlob(dataURL, filename);\n        return;\n    }\n    dataURL = '' + dataURL;\n    if (nav.userAgent.length > 1000 /* RegexLimits.shortLimit */) {\n        throw new Error('Input too long');\n    }\n    var // Some browsers have limitations for data URL lengths. Try to convert\n        // to Blob or fall back. Edge always needs that blob.\n        isOldEdgeBrowser = /Edge\\/\\d+/.test(nav.userAgent), \n        // Safari on iOS needs Blob in order to download PDF\n        safariBlob = (isSafari &&\n            typeof dataURL === 'string' &&\n            dataURL.indexOf('data:application/pdf') === 0);\n    if (safariBlob || isOldEdgeBrowser || dataURL.length > 2000000) {\n        dataURL = dataURLtoBlob(dataURL) || '';\n        if (!dataURL) {\n            throw new Error('Failed to convert to blob');\n        }\n    }\n    // Try HTML5 download attr if supported\n    if (typeof a.download !== 'undefined') {\n        a.href = dataURL;\n        a.download = filename; // HTML5 download attribute\n        doc.body.appendChild(a);\n        a.click();\n        doc.body.removeChild(a);\n    }\n    else {\n        // No download attr, just opening data URI\n        try {\n            if (!win.open(dataURL, 'chart')) {\n                throw new Error('Failed to open window');\n            }\n        }\n        catch (_a) {\n            // If window.open failed, try location.href\n            win.location.href = dataURL;\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar DownloadURL = {\n    dataURLtoBlob: dataURLtoBlob,\n    downloadURL: downloadURL\n};\n/* harmony default export */ var Extensions_DownloadURL = (DownloadURL);\n\n;// ./code/es5/es-modules/Extensions/Sonification/SonificationTimeline.js\n/* *\n *\n *  (c) 2009-2025 Øystein Moseng\n *\n *  Class representing a Timeline with sonification events to play.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nvar SonificationTimeline_downloadURL = Extensions_DownloadURL.downloadURL;\n\nvar SonificationTimeline_defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, find = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).find, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/**\n * Get filtered channels. Timestamps are compensated, so that the first\n * event starts immediately.\n * @private\n */\nfunction filterChannels(filter, channels) {\n    var filtered = channels.map(function (channel) {\n            channel.cancel();\n        return {\n            channel: channel,\n            filteredEvents: channel.muted ?\n                [] : channel.events.filter(filter)\n        };\n    }), minTime = filtered.reduce(function (acc, cur) {\n        return Math.min(acc, cur.filteredEvents.length ?\n            cur.filteredEvents[0].time : Infinity);\n    }, Infinity);\n    return filtered.map(function (c) { return (new Sonification_TimelineChannel(c.channel.type, c.channel.engine, c.channel.showPlayMarker, c.filteredEvents.map(function (e) {\n        return merge(e, { time: e.time - minTime });\n    }), c.channel.muted)); });\n}\n/**\n * The SonificationTimeline class. This class represents a timeline of\n * audio events scheduled to play. It provides functionality for manipulating\n * and navigating the timeline.\n * @private\n */\nvar SonificationTimeline = /** @class */ (function () {\n    function SonificationTimeline(options, chart) {\n        this.chart = chart;\n        this.isPaused = false;\n        this.isPlaying = false;\n        this.channels = [];\n        this.scheduledCallbacks = [];\n        this.playTimestamp = 0;\n        this.resumeFromTime = 0;\n        this.options = options || {};\n    }\n    // Add a channel, optionally with events, to be played.\n    // Note: Only one speech channel is supported at a time.\n    SonificationTimeline.prototype.addChannel = function (type, engine, showPlayMarker, events) {\n        if (showPlayMarker === void 0) { showPlayMarker = false; }\n        if (type === 'instrument' &&\n            !engine.scheduleEventAtTime ||\n            type === 'speech' &&\n                !engine.sayAtTime) {\n            throw new Error('Highcharts Sonification: Invalid channel engine.');\n        }\n        var channel = new Sonification_TimelineChannel(type,\n            engine,\n            showPlayMarker,\n            events);\n        this.channels.push(channel);\n        return channel;\n    };\n    // Play timeline, optionally filtering out only some of the events to play.\n    // Note that if not all instrument parameters are updated on each event,\n    // parameters may update differently depending on the events filtered out,\n    // since some of the events that update parameters can be filtered out too.\n    // The filterPersists argument determines whether or not the filter persists\n    // after e.g. pausing and resuming. Usually this should be true.\n    SonificationTimeline.prototype.play = function (filter, filterPersists, resetAfter, onEnd) {\n        var _this = this;\n        if (filterPersists === void 0) { filterPersists = true; }\n        if (resetAfter === void 0) { resetAfter = true; }\n        if (this.isPlaying) {\n            this.cancel();\n        }\n        else {\n            this.clearScheduledCallbacks();\n        }\n        this.onEndArgument = onEnd;\n        this.playTimestamp = Date.now();\n        this.resumeFromTime = 0;\n        this.isPaused = false;\n        this.isPlaying = true;\n        var skipThreshold = this.options.skipThreshold || 2,\n            onPlay = this.options.onPlay,\n            showTooltip = this.options.showTooltip,\n            showCrosshair = this.options.showCrosshair,\n            channels = filter ?\n                filterChannels(filter,\n            this.playingChannels || this.channels) :\n                this.channels,\n            getEventKeysSignature = function (e) {\n                return Object.keys(e.speechOptions || {})\n                    .concat(Object.keys(e.instrumentEventOptions || {}))\n                    .join();\n        }, pointsPlayed = [];\n        if (filterPersists) {\n            this.playingChannels = channels;\n        }\n        if (onPlay) {\n            onPlay({ chart: this.chart, timeline: this });\n        }\n        var maxTime = 0;\n        channels.forEach(function (channel) {\n            if (channel.muted) {\n                return;\n            }\n            var numEvents = channel.events.length;\n            var lastCallbackTime = -Infinity,\n                lastEventTime = -Infinity,\n                lastEventKeys = '';\n            maxTime = Math.max(channel.events[numEvents - 1] &&\n                channel.events[numEvents - 1].time || 0, maxTime);\n            var _loop_1 = function (i) {\n                    var e = channel.events[i],\n                keysSig = getEventKeysSignature(e);\n                // Optimize by skipping extremely close events (<2ms apart by\n                // default), as long as they don't introduce new event options\n                if (keysSig === lastEventKeys &&\n                    e.time - lastEventTime < skipThreshold) {\n                    return \"continue\";\n                }\n                lastEventKeys = keysSig;\n                lastEventTime = e.time;\n                if (channel.type === 'instrument') {\n                    channel.engine\n                        .scheduleEventAtTime(e.time / 1000, e.instrumentEventOptions || {});\n                }\n                else {\n                    channel.engine.sayAtTime(e.time, e.message || '', e.speechOptions || {});\n                }\n                var point = e.relatedPoint,\n                    chart = point && point.series && point.series.chart,\n                    needsCallback = e.callback ||\n                        point && (showTooltip || showCrosshair) &&\n                            channel.showPlayMarker !== false &&\n                            (e.time - lastCallbackTime > 50 || i === numEvents - 1);\n                if (point) {\n                    pointsPlayed.push(point);\n                }\n                if (needsCallback) {\n                    _this.scheduledCallbacks.push(setTimeout(function () {\n                        if (e.callback) {\n                            e.callback();\n                        }\n                        if (point) {\n                            if (showCrosshair) {\n                                var s = point.series;\n                                if (s && s.xAxis && s.xAxis.crosshair) {\n                                    s.xAxis.drawCrosshair(void 0, point);\n                                }\n                                if (s && s.yAxis && s.yAxis.crosshair) {\n                                    s.yAxis.drawCrosshair(void 0, point);\n                                }\n                            }\n                            if (showTooltip && !(\n                            // Don't re-hover if shared tooltip\n                            chart && chart.hoverPoints &&\n                                chart.hoverPoints.length > 1 &&\n                                find(chart.hoverPoints, function (p) { return p === point; }) &&\n                                // Stock issue w/Navigator\n                                point.onMouseOver)) {\n                                point.onMouseOver();\n                            }\n                        }\n                    }, e.time));\n                    lastCallbackTime = e.time;\n                }\n            };\n            for (var i = 0; i < numEvents; ++i) {\n                _loop_1(i);\n            }\n        });\n        var onEndOpt = this.options.onEnd,\n            onStop = this.options.onStop;\n        this.scheduledCallbacks.push(setTimeout(function () {\n            var chart = _this.chart,\n                context = { chart: chart,\n                timeline: _this,\n                pointsPlayed: pointsPlayed };\n            _this.isPlaying = false;\n            if (resetAfter) {\n                _this.resetPlayState();\n            }\n            if (onStop) {\n                onStop(context);\n            }\n            if (onEndOpt) {\n                onEndOpt(context);\n            }\n            if (onEnd) {\n                onEnd(context);\n            }\n            if (chart) {\n                if (chart.tooltip) {\n                    chart.tooltip.hide(0);\n                }\n                if (chart.hoverSeries) {\n                    chart.hoverSeries.onMouseOut();\n                }\n                chart.axes.forEach(function (a) { return a.hideCrosshair(); });\n            }\n        }, maxTime + 250));\n        this.resumeFromTime = filterPersists ? maxTime : this.getLength();\n    };\n    // Pause for later resuming. Returns current timestamp to resume from.\n    SonificationTimeline.prototype.pause = function () {\n        this.isPaused = true;\n        this.cancel();\n        this.resumeFromTime = Date.now() - this.playTimestamp - 10;\n        return this.resumeFromTime;\n    };\n    // Get current time\n    SonificationTimeline.prototype.getCurrentTime = function () {\n        return this.isPlaying ?\n            Date.now() - this.playTimestamp :\n            this.resumeFromTime;\n    };\n    // Get length of timeline in milliseconds\n    SonificationTimeline.prototype.getLength = function () {\n        return this.channels.reduce(function (maxTime, channel) {\n            var lastEvent = channel.events[channel.events.length - 1];\n            return lastEvent ? Math.max(lastEvent.time, maxTime) : maxTime;\n        }, 0);\n    };\n    // Resume from paused\n    SonificationTimeline.prototype.resume = function () {\n        if (this.playingChannels) {\n            var resumeFrom_1 = this.resumeFromTime - 50;\n            this.play(function (e) { return e.time > resumeFrom_1; }, false, false, this.onEndArgument);\n            this.playTimestamp -= resumeFrom_1;\n        }\n        else {\n            this.play(void 0, false, false, this.onEndArgument);\n        }\n    };\n    // Play a short moment, then pause, setting the cursor to the final\n    // event's time.\n    SonificationTimeline.prototype.anchorPlayMoment = function (eventFilter, onEnd) {\n        if (this.isPlaying) {\n            this.pause();\n        }\n        var finalEventTime = 0;\n        this.play(function (e, ix, arr) {\n            // We have to keep track of final event time ourselves, since\n            // play() messes with the time internally upon filtering.\n            var res = eventFilter(e,\n                ix,\n                arr);\n            if (res && e.time > finalEventTime) {\n                finalEventTime = e.time;\n            }\n            return res;\n        }, false, false, onEnd);\n        this.playingChannels = this.playingChannels || this.channels;\n        this.isPaused = true;\n        this.isPlaying = false;\n        this.resumeFromTime = finalEventTime;\n    };\n    // Play event(s) occurring next/prev from paused state.\n    SonificationTimeline.prototype.playAdjacent = function (next, onEnd, onBoundaryHit, eventFilter) {\n        if (this.isPlaying) {\n            this.pause();\n        }\n        var fromTime = this.resumeFromTime,\n            closestTime = this.channels.reduce(function (time,\n            channel) {\n                // Adapted binary search since events are sorted by time\n                var events = eventFilter ?\n                    channel.events.filter(eventFilter) : channel.events;\n            var s = 0,\n                e = events.length,\n                lastValidTime = time;\n            while (s < e) {\n                var mid = (s + e) >> 1,\n                    t = events[mid].time,\n                    cmp = t - fromTime;\n                if (cmp > 0) { // Ahead\n                    if (next && t < lastValidTime) {\n                        lastValidTime = t;\n                    }\n                    e = mid;\n                }\n                else if (cmp < 0) { // Behind\n                    if (!next && t > lastValidTime) {\n                        lastValidTime = t;\n                    }\n                    s = mid + 1;\n                }\n                else { // Same as from time\n                    if (next) {\n                        s = mid + 1;\n                    }\n                    else {\n                        e = mid;\n                    }\n                }\n            }\n            return lastValidTime;\n        }, next ? Infinity : -Infinity), margin = 0.02;\n        if (closestTime === Infinity || closestTime === -Infinity) {\n            if (onBoundaryHit) {\n                onBoundaryHit({\n                    chart: this.chart, timeline: this, attemptedNext: next\n                });\n            }\n            return;\n        }\n        this.anchorPlayMoment(function (e, ix, arr) {\n            var withinTime = next ?\n                    e.time > fromTime && e.time <= closestTime + margin :\n                    e.time < fromTime && e.time >= closestTime - margin;\n            return eventFilter ? withinTime && eventFilter(e, ix, arr) :\n                withinTime;\n        }, onEnd);\n    };\n    // Play event with related point, where the value of a prop on the\n    // related point is closest to a target value.\n    // Note: not very efficient.\n    SonificationTimeline.prototype.playClosestToPropValue = function (prop, targetVal, onEnd, onBoundaryHit, eventFilter) {\n        var filter = function (e,\n            ix,\n            arr) { return !!(eventFilter ?\n                eventFilter(e,\n            ix,\n            arr) && e.relatedPoint :\n                e.relatedPoint); };\n        var closestValDiff = Infinity,\n            closestEvent = null;\n        (this.playingChannels || this.channels).forEach(function (channel) {\n            var events = channel.events;\n            var i = events.length;\n            while (i--) {\n                if (!filter(events[i], i, events)) {\n                    continue;\n                }\n                var val = events[i].relatedPoint[prop],\n                    diff = SonificationTimeline_defined(val) && Math.abs(targetVal - val);\n                if (diff !== false && diff < closestValDiff) {\n                    closestValDiff = diff;\n                    closestEvent = events[i];\n                }\n            }\n        });\n        if (closestEvent) {\n            this.play(function (e) { return !!(closestEvent &&\n                e.time < closestEvent.time + 1 &&\n                e.time > closestEvent.time - 1 &&\n                e.relatedPoint === closestEvent.relatedPoint); }, false, false, onEnd);\n            this.playingChannels = this.playingChannels || this.channels;\n            this.isPaused = true;\n            this.isPlaying = false;\n            this.resumeFromTime = closestEvent.time;\n        }\n        else if (onBoundaryHit) {\n            onBoundaryHit({ chart: this.chart, timeline: this });\n        }\n    };\n    // Get timeline events that are related to a certain point.\n    // Note: Point grouping may cause some points not to have a\n    //  related point in the timeline.\n    SonificationTimeline.prototype.getEventsForPoint = function (point) {\n        return this.channels.reduce(function (events, channel) {\n            var pointEvents = channel.events\n                    .filter(function (e) { return e.relatedPoint === point; });\n            return events.concat(pointEvents);\n        }, []);\n    };\n    // Divide timeline into 100 parts of equal time, and play one of them.\n    // Used for scrubbing.\n    // Note: Should be optimized?\n    SonificationTimeline.prototype.playSegment = function (segment, onEnd) {\n        var numSegments = 100;\n        var eventTimes = {\n                first: Infinity,\n                last: -Infinity\n            };\n        this.channels.forEach(function (c) {\n            if (c.events.length) {\n                eventTimes.first = Math.min(c.events[0].time, eventTimes.first);\n                eventTimes.last = Math.max(c.events[c.events.length - 1].time, eventTimes.last);\n            }\n        });\n        if (eventTimes.first < Infinity) {\n            var segmentSize = (eventTimes.last - eventTimes.first) / numSegments,\n                fromTime_1 = eventTimes.first + segment * segmentSize,\n                toTime_1 = fromTime_1 + segmentSize;\n            // Binary search, do we have any events within time range?\n            if (!this.channels.some(function (c) {\n                var events = c.events;\n                var s = 0,\n                    e = events.length;\n                while (s < e) {\n                    var mid = (s + e) >> 1,\n                        t = events[mid].time;\n                    if (t < fromTime_1) { // Behind\n                        s = mid + 1;\n                    }\n                    else if (t > toTime_1) { // Ahead\n                        e = mid;\n                    }\n                    else {\n                        return true;\n                    }\n                }\n                return false;\n            })) {\n                return; // If not, don't play - avoid cancelling current play\n            }\n            this.play(function (e) { return e.time >= fromTime_1 && e.time <= toTime_1; }, false, false, onEnd);\n            this.playingChannels = this.playingChannels || this.channels;\n            this.isPaused = true;\n            this.isPlaying = false;\n            this.resumeFromTime = toTime_1;\n        }\n    };\n    // Get last played / current point\n    // Since events are scheduled we can't just store points as we play them\n    SonificationTimeline.prototype.getLastPlayedPoint = function (filter) {\n        var curTime = this.getCurrentTime(),\n            channels = this.playingChannels || this.channels;\n        var closestDiff = Infinity,\n            closestPoint = null;\n        channels.forEach(function (c) {\n            var events = c.events.filter(function (e,\n                ix,\n                arr) { return !!(e.relatedPoint && e.time <= curTime &&\n                    (!filter || filter(e,\n                ix,\n                arr))); }),\n                closestEvent = events[events.length - 1];\n            if (closestEvent) {\n                var closestTime = closestEvent.time,\n                    diff = Math.abs(closestTime - curTime);\n                if (diff < closestDiff) {\n                    closestDiff = diff;\n                    closestPoint = closestEvent.relatedPoint;\n                }\n            }\n        });\n        return closestPoint;\n    };\n    // Reset play/pause state so that a later call to resume() will start over\n    SonificationTimeline.prototype.reset = function () {\n        if (this.isPlaying) {\n            this.cancel();\n        }\n        this.resetPlayState();\n    };\n    SonificationTimeline.prototype.cancel = function () {\n        var onStop = this.options.onStop;\n        if (onStop) {\n            onStop({ chart: this.chart, timeline: this });\n        }\n        this.isPlaying = false;\n        this.channels.forEach(function (c) { return c.cancel(); });\n        if (this.playingChannels && this.playingChannels !== this.channels) {\n            this.playingChannels.forEach(function (c) { return c.cancel(); });\n        }\n        this.clearScheduledCallbacks();\n        this.resumeFromTime = 0;\n    };\n    SonificationTimeline.prototype.destroy = function () {\n        this.cancel();\n        if (this.playingChannels && this.playingChannels !== this.channels) {\n            this.playingChannels.forEach(function (c) { return c.destroy(); });\n        }\n        this.channels.forEach(function (c) { return c.destroy(); });\n    };\n    SonificationTimeline.prototype.setMasterVolume = function (vol) {\n        this.channels.forEach(function (c) { return c.engine.setMasterVolume(vol); });\n    };\n    SonificationTimeline.prototype.getMIDIData = function () {\n        return MIDI(this.channels.filter(function (c) { return c.type === 'instrument'; }));\n    };\n    SonificationTimeline.prototype.downloadMIDI = function (filename) {\n        var data = this.getMIDIData(), name = (filename ||\n                this.chart &&\n                    this.chart.options.title &&\n                    this.chart.options.title.text ||\n                'chart') + '.mid', blob = new Blob([data], { type: 'application/octet-stream' }), url = window.URL.createObjectURL(blob);\n        SonificationTimeline_downloadURL(url, name);\n        window.URL.revokeObjectURL(url);\n    };\n    SonificationTimeline.prototype.resetPlayState = function () {\n        delete this.playingChannels;\n        delete this.onEndArgument;\n        this.playTimestamp = this.resumeFromTime = 0;\n        this.isPaused = false;\n    };\n    SonificationTimeline.prototype.clearScheduledCallbacks = function () {\n        this.scheduledCallbacks.forEach(clearTimeout);\n        this.scheduledCallbacks = [];\n    };\n    return SonificationTimeline;\n}());\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Sonification_SonificationTimeline = (SonificationTimeline);\n/* *\n *\n *  API declarations\n *\n * */\n/**\n * Filter callback for filtering timeline events on a SonificationTimeline.\n *\n * @callback Highcharts.SonificationTimelineFilterCallback\n *\n * @param {Highcharts.SonificationTimelineEvent} e TimelineEvent being filtered\n *\n * @param {number} ix Index of TimelineEvent in current event array\n *\n * @param {Array<Highcharts.SonificationTimelineEvent>} arr The current event array\n *\n * @return {boolean}\n * The function should return true if the TimelineEvent should be included,\n * false otherwise.\n */\n(''); // Keep above doclets in JS file\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Templating\"],\"commonjs\":[\"highcharts\",\"Templating\"],\"commonjs2\":[\"highcharts\",\"Templating\"],\"root\":[\"Highcharts\",\"Templating\"]}\nvar highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_ = __webpack_require__(984);\nvar highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default = /*#__PURE__*/__webpack_require__.n(highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_);\n;// ./code/es5/es-modules/Extensions/Sonification/TimelineFromChart.js\n/* *\n *\n *  (c) 2009-2025 Øystein Moseng\n *\n *  Build a timeline from a chart.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar TimelineFromChart_assign = (undefined && undefined.__assign) || function () {\n    TimelineFromChart_assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return TimelineFromChart_assign.apply(this, arguments);\n};\n\n\n\n\nvar TimelineFromChart_clamp = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).clamp, TimelineFromChart_defined = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defined, TimelineFromChart_extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, getNestedProperty = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).getNestedProperty, TimelineFromChart_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, TimelineFromChart_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n\nvar format = (highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default()).format;\nvar isNoteDefinition = function (str) {\n    return (/^([a-g][#b]?)[0-8]$/i).test(str);\n};\n/**\n * Get the value of a point property from string.\n * @private\n */\nfunction getPointPropValue(point, prop) {\n    var ret;\n    if (prop) {\n        ret = point[prop];\n        if (typeof ret === 'number') {\n            return ret;\n        }\n        ret = getNestedProperty(prop, point);\n    }\n    return typeof ret === 'number' ? ret : void 0;\n}\n/**\n * Get chart wide min/max for a set of props, as well as per\n * series min/max for selected props.\n * @private\n */\nfunction getChartExtremesForProps(chart, props, perSeriesProps) {\n    var series = chart.series,\n        numProps = props.length,\n        numSeriesProps = perSeriesProps.length,\n        initCache = function (propList) {\n            return propList.reduce(function (cache,\n        prop) {\n                ((cache[prop] = { min: Infinity,\n        max: -Infinity }),\n        cache);\n            return cache;\n        }, {});\n    }, updateCache = function (cache, point, prop) {\n        var val = point[prop];\n        if (val === void 0) {\n            val = getNestedProperty(prop, point);\n        }\n        if (typeof val === 'number') {\n            cache[prop].min = Math.min(cache[prop].min, val);\n            cache[prop].max = Math.max(cache[prop].max, val);\n        }\n    }, globalExtremes = initCache(props);\n    var i = series.length;\n    var allSeriesExtremes = new Array(i);\n    while (i--) {\n        var seriesExtremes = initCache(perSeriesProps);\n        var opts = series[i].options;\n        if (!series[i].visible ||\n            opts && opts.sonification && opts.sonification.enabled === false) {\n            continue;\n        }\n        var points = series[i].points || [];\n        var j = points.length;\n        while (j--) {\n            var k = numProps;\n            while (k--) {\n                updateCache(globalExtremes, points[j], props[k]);\n            }\n            k = numSeriesProps;\n            while (k--) {\n                updateCache(seriesExtremes, points[j], perSeriesProps[k]);\n            }\n        }\n        allSeriesExtremes[i] = seriesExtremes;\n    }\n    return {\n        globalExtremes: globalExtremes,\n        seriesExtremes: allSeriesExtremes\n    };\n}\n/**\n * Build a cache of prop extremes for the chart. Goes through\n * options to find out which props are needed.\n * @private\n */\nfunction getPropMetrics(chart) {\n    var globalOpts = chart.options.sonification ||\n            {}, defaultInstrMapping = (globalOpts.defaultInstrumentOptions || {})\n            .mapping || { time: 'x', pitch: 'y' }, defaultSpeechMapping = globalOpts.defaultSpeechOptions &&\n            globalOpts.defaultSpeechOptions.mapping || {}, seriesTimeProps = [], commonTimeProps = {}, addTimeProp = function (prop, seriesIx) {\n            if (seriesIx !== null) {\n                seriesTimeProps[seriesIx] =\n                    seriesTimeProps[seriesIx] || {};\n            seriesTimeProps[seriesIx][prop] = true;\n        }\n        else {\n            commonTimeProps[prop] = true;\n        }\n    }, props = {}, perSeriesProps = {}, addPropFromMappingParam = function (param, val, seriesIx) {\n        var removeInvertedFlag = function (s) { return (s.charAt(0) === '-' ? s.slice(1) : s); };\n        if (typeof val === 'string' && param !== 'text') {\n            if (param === 'pitch' && isNoteDefinition(val)) {\n                return;\n            }\n            if (param === 'time') {\n                perSeriesProps[val] = true;\n                addTimeProp(val, seriesIx);\n            }\n            props[removeInvertedFlag(val)] = true;\n            return;\n        }\n        var paramOpts = val;\n        if (paramOpts && paramOpts.mapTo &&\n            typeof paramOpts.mapTo === 'string') {\n            var mapTo = removeInvertedFlag(paramOpts.mapTo);\n            if (param === 'time') {\n                addTimeProp(mapTo, seriesIx);\n            }\n            if (param === 'time' || paramOpts.within === 'series') {\n                perSeriesProps[mapTo] = true;\n            }\n            props[mapTo] = true;\n            return;\n        }\n        if (['tremolo', 'lowpass', 'highpass'].indexOf(param) > -1 &&\n            typeof val === 'object') {\n            Object.keys(val).forEach(function (subParam) {\n                return addPropFromMappingParam(subParam, val[subParam], seriesIx);\n            });\n        }\n    }, addPropsFromMappingOptions = function (mapping, seriesIx) {\n        (Object.keys(mapping)).forEach(function (param) {\n            return addPropFromMappingParam(param, mapping[param], seriesIx);\n        });\n    }, addPropsFromContextTracks = function (tracks) { return tracks.forEach(function (track) {\n        props[track.valueProp || 'x'] =\n            perSeriesProps[track.valueProp || 'x'] = true;\n    }); };\n    addPropsFromMappingOptions(defaultInstrMapping, null);\n    addPropsFromMappingOptions(defaultSpeechMapping, null);\n    addPropsFromContextTracks(globalOpts.globalContextTracks || []);\n    var hasCommonTimeProps = Object.keys(commonTimeProps).length;\n    chart.series.forEach(function (series) {\n        var sOpts = series.options.sonification;\n        if (series.visible && !(sOpts && sOpts.enabled === false)) {\n            if (hasCommonTimeProps) {\n                seriesTimeProps[series.index] = TimelineFromChart_merge(commonTimeProps);\n            }\n            if (sOpts) {\n                var defaultInstrMapping_1 = (sOpts.defaultInstrumentOptions || {}).mapping,\n                    defaultSpeechMapping_1 = (sOpts.defaultSpeechOptions || {}).mapping;\n                if (defaultInstrMapping_1) {\n                    addPropsFromMappingOptions(defaultInstrMapping_1, series.index);\n                }\n                if (defaultSpeechMapping_1) {\n                    addPropsFromMappingOptions(defaultSpeechMapping_1, series.index);\n                }\n                addPropsFromContextTracks(sOpts.contextTracks || []);\n                (sOpts.tracks || [])\n                    .concat(sOpts.contextTracks || [])\n                    .forEach(function (trackOpts) {\n                    if (trackOpts.mapping) {\n                        addPropsFromMappingOptions(trackOpts.mapping, series.index);\n                    }\n                });\n            }\n        }\n    });\n    return TimelineFromChart_assign({ seriesTimeProps: seriesTimeProps }, getChartExtremesForProps(chart, Object.keys(props), Object.keys(perSeriesProps)));\n}\n/**\n * Map a relative value onto a virtual axis.\n * @private\n */\nfunction mapToVirtualAxis(value, valueExtremes, virtualAxisExtremes, invert, logarithmic // Virtual axis is logarithmic\n) {\n    var lenValueAxis = valueExtremes.max - valueExtremes.min;\n    if (lenValueAxis <= 0) {\n        return virtualAxisExtremes.min;\n    }\n    var lenVirtualAxis = virtualAxisExtremes.max - virtualAxisExtremes.min,\n        valueDelta = value - valueExtremes.min;\n    var virtualValueDelta = lenVirtualAxis * valueDelta / lenValueAxis;\n    if (logarithmic) {\n        var log = valueExtremes.min > 0 ?\n                // Normal log formula\n                function (x) { return Math.log(x) / Math.LOG10E; } :\n                // Negative logarithmic support needed\n                function (x) {\n                    var adjustedNum = Math.abs(x);\n                if (adjustedNum < 10) {\n                    adjustedNum += (10 - adjustedNum) / 10;\n                }\n                var res = Math.log(adjustedNum) / Math.LN10;\n                return x < 0 ? -res : res;\n            };\n        var logValMin = log(valueExtremes.min);\n        virtualValueDelta = lenVirtualAxis *\n            (log(value) - logValMin) /\n            (log(valueExtremes.max) - logValMin);\n    }\n    var val = invert ?\n            virtualAxisExtremes.max - virtualValueDelta :\n            virtualAxisExtremes.min + virtualValueDelta;\n    return TimelineFromChart_clamp(val, virtualAxisExtremes.min, virtualAxisExtremes.max);\n}\n/**\n * Get the value of a mapped parameter for a point.\n * @private\n */\nfunction getMappingParameterValue(context, propMetrics, useSeriesExtremes, defaultMapping, mappingOptions, contextValueProp) {\n    if (typeof mappingOptions === 'number') {\n        return mappingOptions;\n    }\n    if (typeof mappingOptions === 'function') {\n        return mappingOptions(TimelineFromChart_extend({ time: 0 }, context));\n    }\n    var mapTo = mappingOptions,\n        mapFunc = defaultMapping.mapFunction,\n        min = defaultMapping.min,\n        max = defaultMapping.max,\n        within = defaultMapping.within,\n        scale;\n    if (typeof mappingOptions === 'object') {\n        mapTo = mappingOptions.mapTo;\n        mapFunc = mappingOptions.mapFunction || mapFunc;\n        min = TimelineFromChart_pick(mappingOptions.min, min);\n        max = TimelineFromChart_pick(mappingOptions.max, max);\n        within = mappingOptions.within || defaultMapping.within;\n        scale = mappingOptions.scale;\n    }\n    if (!mapTo) {\n        return null;\n    }\n    var isInverted = mapTo.charAt(0) === '-';\n    if (isInverted) {\n        mapTo = mapTo.slice(1);\n    }\n    var value = context.value;\n    var useContextValue = mapTo === 'value' && value !== void 0 &&\n            contextValueProp;\n    if (!useContextValue) {\n        var fixedValue = mappingOptions.value;\n        if (fixedValue !== void 0) {\n            value = fixedValue;\n        }\n        else {\n            if (!context.point) {\n                return null;\n            }\n            value = context.point[mapTo];\n        }\n        if (value === void 0) {\n            value = getNestedProperty(mapTo, context.point);\n        }\n    }\n    if (typeof value !== 'number' || value === null) {\n        return null;\n    }\n    // Figure out extremes for this mapping\n    var extremes = null;\n    if (context.point) {\n        if (within === 'xAxis' || within === 'yAxis') {\n            var axis = context.point.series[within];\n            if (axis && TimelineFromChart_defined(axis.dataMin) && TimelineFromChart_defined(axis.dataMax)) {\n                extremes = {\n                    min: axis.dataMin,\n                    max: axis.dataMax\n                };\n            }\n        }\n        else if ((within === 'series' || useSeriesExtremes) &&\n            context.point.series) {\n            extremes = propMetrics.seriesExtremes[context.point.series.index][useContextValue ? contextValueProp : mapTo];\n        }\n    }\n    if (!extremes) { // Chart extremes\n        extremes = propMetrics.globalExtremes[useContextValue ? contextValueProp : mapTo];\n    }\n    if (scale) {\n        // Build a musical scale from array\n        var scaleAxis = [], minOctave = Math.floor(min / 12), maxOctave = Math.ceil(max / 12) + 1, lenScale = scale.length;\n        for (var octave = minOctave; octave < maxOctave; ++octave) {\n            for (var scaleIx = 0; scaleIx < lenScale; ++scaleIx) {\n                var note = 12 * octave + scale[scaleIx];\n                if (note >= min && note <= max) {\n                    scaleAxis.push(note);\n                }\n            }\n        }\n        // Map to the scale\n        var noteNum = mapToVirtualAxis(value,\n            extremes, { min: 0,\n            max: scaleAxis.length - 1 },\n            isInverted,\n            mapFunc === 'logarithmic');\n        return scaleAxis[Math.round(noteNum)];\n    }\n    return mapToVirtualAxis(value, extremes, { min: min, max: max }, isInverted, mapFunc === 'logarithmic');\n}\n/**\n * Get mapping parameter value with defined fallback and defaults.\n * @private\n */\nfunction getParamValWithDefault(context, propMetrics, useSeriesExtremes, mappingParamOptions, fallback, defaults, contextValueProp) {\n    return TimelineFromChart_pick(getMappingParameterValue(context, propMetrics, useSeriesExtremes, TimelineFromChart_extend({\n        min: 0, max: 1, mapTo: 'y', mapFunction: 'linear', within: 'chart'\n    }, (defaults || {})), mappingParamOptions, contextValueProp), fallback);\n}\n/**\n * Get time value for a point event.\n * @private\n */\nfunction getPointTime(point, startTime, duration, timeMappingOptions, propMetrics, useSeriesExtremes) {\n    var time = getParamValWithDefault({ point: point,\n        time: 0 },\n        propMetrics,\n        useSeriesExtremes,\n        timeMappingOptions, 0, { min: 0,\n        max: duration,\n        mapTo: 'x' });\n    return time + startTime;\n}\n/**\n * Get duration for a series\n * @private\n */\nfunction getAvailableDurationForSeries(series, totalDuration, propMetrics, afterSeriesWait) {\n    var timeProp,\n        seriesDuration;\n    var availableDuration = totalDuration -\n            (series.chart.series.length - 1) * afterSeriesWait,\n        hasGlobalTimeProp = propMetrics.seriesTimeProps.every(function (timeProps) {\n            var props = Object.keys(timeProps);\n        if (props.length > 1) {\n            return false;\n        }\n        if (!timeProp) {\n            timeProp = props[0];\n        }\n        return timeProp === props[0];\n    });\n    if (hasGlobalTimeProp) {\n        // Chart-wide single time prop, use time prop extremes\n        var seriesExtremes = propMetrics\n                .seriesExtremes[series.index][timeProp],\n            seriesTimeLen = seriesExtremes.max - seriesExtremes.min,\n            totalTimeLen = propMetrics.seriesExtremes.reduce(function (sum,\n            s) { return (s[timeProp] ?\n                sum + s[timeProp].max - s[timeProp].min :\n                sum); }, 0);\n        seriesDuration = Math.round(seriesTimeLen / totalTimeLen * availableDuration);\n    }\n    else {\n        // No common time prop, so use percent of total points\n        var totalPoints = series.chart.series.reduce(function (sum,\n            s) { return sum + s.points.length; }, 0);\n        seriesDuration = Math.round((series.points || []).length / totalPoints * availableDuration);\n    }\n    return Math.max(50, seriesDuration);\n}\n/**\n * Build and add a track to the timeline.\n * @private\n */\nfunction addTimelineChannelFromTrack(timeline, audioContext, destinationNode, options) {\n    var speechOpts = options,\n        instrMappingOpts = (options.mapping || {}),\n        engine = options.type === 'speech' ?\n            new Sonification_SonificationSpeaker({\n                language: speechOpts.language,\n                name: speechOpts.preferredVoice\n            }) :\n            new Sonification_SonificationInstrument(audioContext,\n        destinationNode, {\n                capabilities: {\n                    pan: !!instrMappingOpts.pan,\n                    tremolo: !!instrMappingOpts.tremolo,\n                    filters: !!(instrMappingOpts.highpass ||\n                        instrMappingOpts.lowpass)\n                },\n                synthPatch: options.instrument,\n                midiTrackName: options.midiName\n            });\n    return timeline.addChannel(options.type || 'instrument', engine, TimelineFromChart_pick(options.showPlayMarker, true));\n}\n/**\n * Add event from a point to a mapped instrument track.\n * @private\n */\nfunction addMappedInstrumentEvent(context, channel, mappingOptions, propMetrics, roundToMusicalNotes, contextValueProp) {\n    var getParam = function (param,\n        fallback,\n        defaults,\n        parent) { return getParamValWithDefault(context,\n        propMetrics,\n        false, (parent || mappingOptions)[param],\n        fallback,\n        defaults,\n        contextValueProp); };\n    var eventsAdded = [],\n        eventOpts = {\n            noteDuration: getParam('noteDuration', 200, { min: 40,\n        max: 1000 }),\n            pan: getParam('pan', 0, { min: -1,\n        max: 1 }),\n            volume: getParam('volume', 1, { min: 0.1,\n        max: 1 })\n        };\n    if (mappingOptions.frequency) {\n        eventOpts.frequency = getParam('frequency', 440, { min: 50, max: 6000 });\n    }\n    if (mappingOptions.lowpass) {\n        eventOpts.lowpassFreq = getParam('frequency', 20000, { min: 0, max: 20000 }, mappingOptions.lowpass);\n        eventOpts.lowpassResonance = getParam('resonance', 0, { min: -6, max: 12 }, mappingOptions.lowpass);\n    }\n    if (mappingOptions.highpass) {\n        eventOpts.highpassFreq = getParam('frequency', 20000, { min: 0, max: 20000 }, mappingOptions.highpass);\n        eventOpts.highpassResonance = getParam('resonance', 0, { min: -6, max: 12 }, mappingOptions.highpass);\n    }\n    if (mappingOptions.tremolo) {\n        eventOpts.tremoloDepth = getParam('depth', 0, { min: 0, max: 0.8 }, mappingOptions.tremolo);\n        eventOpts.tremoloSpeed = getParam('speed', 0, { min: 0, max: 0.8 }, mappingOptions.tremolo);\n    }\n    var gapBetweenNotes = getParam('gapBetweenNotes', 150, { min: 50, max: 1000 }), playDelay = getParam('playDelay', 0, { max: 200 });\n    var addNoteEvent = function (noteDef,\n        ix) {\n            if (ix === void 0) { ix = 0; }\n            var opts = noteDef;\n        if (noteDef.mapTo) {\n            // Transform the pitch mapping options to normal mapping options\n            if (typeof noteDef.min === 'string') {\n                opts.min = Sonification_SonificationInstrument\n                    .noteStringToC0Distance(noteDef.min);\n            }\n            if (typeof noteDef.max === 'string') {\n                opts.max = Sonification_SonificationInstrument\n                    .noteStringToC0Distance(noteDef.max);\n            }\n        }\n        else if (typeof noteDef === 'string' && isNoteDefinition(noteDef)) {\n            opts = Sonification_SonificationInstrument.noteStringToC0Distance(noteDef);\n        }\n        eventOpts.note = getParamValWithDefault(context, propMetrics, false, opts, -1, { min: 0, max: 107 }, contextValueProp);\n        if (eventOpts.note > -1) {\n            if (roundToMusicalNotes) {\n                eventOpts.note = Math.round(eventOpts.note);\n            }\n            eventsAdded.push(channel.addEvent({\n                time: context.time + playDelay + gapBetweenNotes * ix,\n                relatedPoint: context.point,\n                instrumentEventOptions: ix !== void 0 ?\n                    TimelineFromChart_extend({}, eventOpts) : eventOpts\n            }));\n        }\n    };\n    if (mappingOptions.pitch &&\n        mappingOptions.pitch.constructor === Array) {\n        mappingOptions.pitch.forEach(addNoteEvent);\n    }\n    else if (mappingOptions.pitch) {\n        addNoteEvent(mappingOptions.pitch);\n    }\n    else if (mappingOptions.frequency) {\n        eventsAdded.push(channel.addEvent({\n            time: context.time + playDelay,\n            relatedPoint: context.point,\n            instrumentEventOptions: eventOpts\n        }));\n    }\n    return eventsAdded;\n}\n/**\n * Get the message value to speak for a point.\n * @private\n */\nfunction getSpeechMessageValue(context, messageParam) {\n    return format(typeof messageParam === 'function' ?\n        messageParam(context) :\n        messageParam, context, context.point && context.point.series.chart);\n}\n/**\n * Add an event from a point to a mapped speech track.\n * @private\n */\nfunction addMappedSpeechEvent(context, channel, mappingOptions, propMetrics, contextValueProp) {\n    var getParam = function (param,\n        fallback,\n        defaults) { return getParamValWithDefault(context,\n        propMetrics,\n        false,\n        mappingOptions[param],\n        fallback,\n        defaults,\n        contextValueProp); };\n    var playDelay = getParam('playDelay', 0, { max: 200 }), pitch = getParam('pitch', 1, { min: 0.3, max: 2 }), rate = getParam('rate', 1, { min: 0.4, max: 4 }), volume = getParam('volume', 1, { min: 0.1 }), message = getSpeechMessageValue(context, mappingOptions.text);\n    if (message) {\n        return channel.addEvent({\n            time: context.time + playDelay,\n            relatedPoint: context.point,\n            speechOptions: {\n                pitch: pitch,\n                rate: rate,\n                volume: volume\n            },\n            message: message\n        });\n    }\n}\n/**\n * Add events to a channel for a point&track combo.\n * @private\n */\nfunction addMappedEventForPoint(context, channel, trackOptions, propMetrics) {\n    var eventsAdded = [];\n    if (trackOptions.type === 'speech' && trackOptions.mapping) {\n        var eventAdded = addMappedSpeechEvent(context,\n            channel,\n            trackOptions.mapping,\n            propMetrics);\n        if (eventAdded) {\n            eventsAdded = [eventAdded];\n        }\n    }\n    else if (trackOptions.mapping) {\n        eventsAdded = addMappedInstrumentEvent(context, channel, trackOptions.mapping, propMetrics, TimelineFromChart_pick(trackOptions\n            .roundToMusicalNotes, true));\n    }\n    return eventsAdded;\n}\n/**\n * Get a reduced set of points from a list, depending on grouping opts.\n * @private\n */\nfunction getGroupedPoints(pointGroupOpts, points) {\n    var alg = pointGroupOpts.algorithm || 'minmax',\n        r = function (ix) { return (points[ix] ? [points[ix].point] : []); };\n    if (alg === 'first') {\n        return r(0);\n    }\n    if (alg === 'last') {\n        return r(points.length - 1);\n    }\n    if (alg === 'middle') {\n        return r(points.length >> 1);\n    }\n    if (alg === 'firstlast') {\n        return r(0).concat(r(points.length - 1));\n    }\n    if (alg === 'minmax') {\n        var prop_1 = pointGroupOpts.prop || 'y';\n        var min_1,\n            max_1,\n            minVal_1,\n            maxVal_1;\n        points.forEach(function (p) {\n            var val = getPointPropValue(p.point,\n                prop_1);\n            if (val === void 0) {\n                return;\n            }\n            if (!min_1 || val < minVal_1) {\n                min_1 = p;\n                minVal_1 = val;\n            }\n            if (!max_1 || val > maxVal_1) {\n                max_1 = p;\n                maxVal_1 = val;\n            }\n        });\n        if (min_1 && max_1) {\n            if (min_1.point === max_1.point) {\n                return [min_1.point];\n            }\n            return min_1.time > max_1.time ?\n                [max_1.point, min_1.point] :\n                [min_1.point, max_1.point];\n        }\n    }\n    return [];\n}\n/**\n * Should a track be active for this event?\n * @private\n */\nfunction isActive(context, activeWhen, lastPropValue) {\n    if (typeof activeWhen === 'function') {\n        return activeWhen(context);\n    }\n    if (typeof activeWhen === 'object') {\n        var prop = activeWhen.prop,\n            val = TimelineFromChart_pick(context.value,\n            context.point && getPointPropValue(context.point,\n            prop));\n        if (typeof val !== 'number') {\n            return false;\n        }\n        var crossingOk = true;\n        var crossingUp = activeWhen.crossingUp,\n            crossingDown = activeWhen.crossingDown,\n            hasLastValue = typeof lastPropValue === 'number';\n        if (crossingUp && crossingDown) {\n            crossingOk = hasLastValue && (lastPropValue < crossingUp && val >= crossingUp ||\n                lastPropValue > crossingDown && val <= crossingDown);\n        }\n        else {\n            crossingOk = (crossingUp === void 0 ||\n                hasLastValue && lastPropValue < crossingUp &&\n                    val >= crossingUp) && (crossingDown === void 0 ||\n                hasLastValue && lastPropValue > crossingDown &&\n                    val <= crossingDown);\n        }\n        var max = TimelineFromChart_pick(activeWhen.max,\n            Infinity),\n            min = TimelineFromChart_pick(activeWhen.min, -Infinity);\n        return val <= max && val >= min && crossingOk;\n    }\n    return true;\n}\n/**\n * Build a new timeline object from a chart.\n * @private\n */\nfunction timelineFromChart(audioContext, destinationNode, chart) {\n    var options = chart.options.sonification ||\n            {},\n        defaultInstrOpts = options.defaultInstrumentOptions,\n        defaultSpeechOpts = options.defaultSpeechOptions,\n        defaultPointGroupOpts = TimelineFromChart_merge({\n            enabled: true,\n            groupTimespan: 15,\n            algorithm: 'minmax',\n            prop: 'y'\n        },\n        options.pointGrouping),\n        globalTracks = options.globalTracks || [],\n        globalContextTracks = options.globalContextTracks || [],\n        isSequential = options.order === 'sequential', \n        // Slight margin for note end\n        totalDuration = Math.max(50,\n        options.duration - 300),\n        afterSeriesWait = options.afterSeriesWait,\n        eventOptions = options.events || {},\n        propMetrics = getPropMetrics(chart),\n        timeline = new Sonification_SonificationTimeline({\n            onPlay: eventOptions.onPlay,\n            onEnd: eventOptions.onEnd,\n            onStop: eventOptions.onStop,\n            showCrosshair: options.showCrosshair,\n            showTooltip: options.showTooltip\n        },\n        chart);\n    // Expose PropMetrics for tests\n    if (chart.sonification) {\n        chart.sonification.propMetrics = propMetrics;\n    }\n    var startTime = 0;\n    chart.series.forEach(function (series, seriesIx) {\n        var sOptions = series.options.sonification ||\n                {};\n        if (series.visible && sOptions.enabled !== false) {\n            var seriesDuration_1 = isSequential ? getAvailableDurationForSeries(series,\n                totalDuration,\n                propMetrics,\n                afterSeriesWait) : totalDuration,\n                seriesDefaultInstrOpts_1 = TimelineFromChart_merge(defaultInstrOpts,\n                sOptions.defaultInstrumentOptions),\n                seriesDefaultSpeechOpts_1 = TimelineFromChart_merge(defaultSpeechOpts,\n                sOptions.defaultSpeechOptions),\n                seriesPointGroupOpts_1 = TimelineFromChart_merge(defaultPointGroupOpts,\n                sOptions.pointGrouping),\n                mainTracks = (sOptions.tracks || [seriesDefaultInstrOpts_1])\n                    .concat(globalTracks),\n                hasAddedSeries = !!timeline.channels.length,\n                contextTracks = hasAddedSeries && !isSequential ?\n                    sOptions.contextTracks || [] :\n                    (sOptions.contextTracks || []).concat(globalContextTracks),\n                eventsAdded_1 = [];\n            // For crossing threshold notifications\n            var lastPropValue_1;\n            // Add events for the mapped tracks\n            mainTracks.forEach(function (trackOpts) {\n                var mergedOpts = TimelineFromChart_merge({\n                        pointGrouping: seriesPointGroupOpts_1,\n                        midiName: trackOpts.midiName || series.name\n                    },\n                    trackOpts.type === 'speech' ?\n                        seriesDefaultSpeechOpts_1 : seriesDefaultInstrOpts_1,\n                    trackOpts),\n                    pointGroupOpts = mergedOpts.pointGrouping,\n                    activeWhen = mergedOpts.activeWhen,\n                    updateLastPropValue = function (point) {\n                        if (typeof activeWhen === 'object' &&\n                            activeWhen.prop) {\n                            lastPropValue_1 = getPointPropValue(point,\n                    activeWhen.prop);\n                    }\n                };\n                var channel = addTimelineChannelFromTrack(timeline,\n                    audioContext,\n                    destinationNode,\n                    mergedOpts),\n                    add = function (c) { return eventsAdded_1.push.apply(eventsAdded_1,\n                    addMappedEventForPoint(c,\n                    channel,\n                    mergedOpts,\n                    propMetrics)); };\n                // Go through the points and add events to channel\n                var pointGroup = [],\n                    pointGroupTime = 0;\n                var addCurrentPointGroup = function (groupSpanTime) {\n                        if (pointGroup.length === 1) {\n                            add({\n                                point: pointGroup[0].point,\n                                time: pointGroupTime + groupSpanTime / 2\n                            });\n                    }\n                    else {\n                        var points = getGroupedPoints(pointGroupOpts,\n                            pointGroup),\n                            t_1 = groupSpanTime / points.length;\n                        points.forEach(function (p, ix) { return add({\n                            point: p,\n                            time: pointGroupTime + t_1 / 2 + t_1 * ix\n                        }); });\n                    }\n                    pointGroup = [];\n                };\n                (series.points || []).forEach(function (point, pointIx) {\n                    var isLastPoint = pointIx === series.points.length - 1;\n                    var time = getPointTime(point,\n                        startTime,\n                        seriesDuration_1,\n                        mergedOpts.mapping && mergedOpts.mapping.time || 0,\n                        propMetrics,\n                        isSequential);\n                    var context = { point: point,\n                        time: time };\n                    // Is this point active?\n                    if (!mergedOpts.mapping ||\n                        !isActive(context, activeWhen, lastPropValue_1)) {\n                        updateLastPropValue(point);\n                        // Remaining points in group\n                        if (isLastPoint && pointGroup.length) {\n                            addCurrentPointGroup(pointGroup[pointGroup.length - 1].time -\n                                pointGroup[0].time);\n                        }\n                        return;\n                    }\n                    updateLastPropValue(point);\n                    // Add the events\n                    if (!pointGroupOpts.enabled) {\n                        add(context);\n                    }\n                    else {\n                        var dT = time - pointGroupTime,\n                            groupSpan = pointGroupOpts.groupTimespan,\n                            spanTime = isLastPoint &&\n                                dT <= groupSpan ? dT : groupSpan;\n                        if (isLastPoint || dT > groupSpan) {\n                            if (dT <= groupSpan) {\n                                // Only happens if last point is within group\n                                pointGroup.push(context);\n                            }\n                            addCurrentPointGroup(spanTime);\n                            pointGroupTime = Math.floor(time / groupSpan) *\n                                groupSpan;\n                            if (isLastPoint && dT > groupSpan) {\n                                add({\n                                    point: context.point,\n                                    time: pointGroupTime + spanTime / 2\n                                });\n                            }\n                            else {\n                                pointGroup = [context];\n                            }\n                        }\n                        else {\n                            pointGroup.push(context);\n                        }\n                    }\n                });\n            });\n            // Add callbacks to first/last events\n            var firstEvent = eventsAdded_1.reduce(function (first,\n                e) { return (e.time < first.time ? e : first); }, { time: Infinity });\n            var lastEvent = eventsAdded_1.reduce(function (last,\n                e) { return (e.time > last.time ? e : last); }, { time: -Infinity });\n            firstEvent.callback = eventOptions.onSeriesStart ?\n                eventOptions.onSeriesStart.bind(null, { series: series, timeline: timeline }) :\n                void 0;\n            lastEvent.callback = eventOptions.onSeriesEnd ?\n                eventOptions.onSeriesEnd.bind(null, { series: series, timeline: timeline }) :\n                void 0;\n            // Add the context tracks that are not related to points\n            contextTracks.forEach(function (trackOpts) {\n                var mergedOpts = trackOpts.type === 'speech' ?\n                        TimelineFromChart_merge(defaultSpeechOpts,\n                    trackOpts) :\n                        TimelineFromChart_merge(defaultInstrOpts, {\n                            mapping: { pitch: { mapTo: 'value' } }\n                        },\n                    trackOpts);\n                var contextChannel = addTimelineChannelFromTrack(timeline,\n                    audioContext,\n                    destinationNode,\n                    mergedOpts);\n                lastPropValue_1 = void 0;\n                var timeInterval = mergedOpts.timeInterval,\n                    valueInterval = mergedOpts.valueInterval,\n                    valueProp = mergedOpts.valueProp || 'x',\n                    activeWhen = mergedOpts.activeWhen,\n                    contextExtremes = propMetrics\n                        .seriesExtremes[seriesIx][valueProp],\n                    addContextEvent = function (time,\n                    value) {\n                        if (!mergedOpts.mapping ||\n                            !isActive({ time: time,\n                    value: value },\n                    typeof activeWhen === 'object' ?\n                                TimelineFromChart_extend({ prop: valueProp },\n                    activeWhen) :\n                                activeWhen,\n                    lastPropValue_1)) {\n                            lastPropValue_1 = value;\n                        return;\n                    }\n                    lastPropValue_1 = value;\n                    if (mergedOpts.type === 'speech') {\n                        addMappedSpeechEvent({ time: time, value: value }, contextChannel, mergedOpts.mapping, propMetrics, valueProp);\n                    }\n                    else {\n                        addMappedInstrumentEvent({ time: time, value: value }, contextChannel, mergedOpts.mapping, propMetrics, TimelineFromChart_pick(mergedOpts.roundToMusicalNotes, true), valueProp);\n                    }\n                };\n                if (timeInterval) {\n                    var time = 0;\n                    while (time <= seriesDuration_1) {\n                        var val = mapToVirtualAxis(time, { min: 0,\n                            max: seriesDuration_1 },\n                            contextExtremes);\n                        addContextEvent(time + startTime, val);\n                        time += timeInterval;\n                    }\n                }\n                if (valueInterval) {\n                    var val = contextExtremes.min;\n                    while (val <= contextExtremes.max) {\n                        var time = mapToVirtualAxis(val,\n                            contextExtremes, { min: 0,\n                            max: seriesDuration_1 },\n                            false,\n                            mergedOpts.valueMapFunction === 'logarithmic');\n                        addContextEvent(time + startTime, val);\n                        val += valueInterval;\n                    }\n                }\n            });\n            if (isSequential) {\n                startTime += seriesDuration_1 + afterSeriesWait;\n            }\n        }\n    });\n    return timeline;\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var TimelineFromChart = (timelineFromChart);\n\n;// ./code/es5/es-modules/Extensions/Sonification/Sonification.js\n/* *\n *\n *  (c) 2009-2025 Øystein Moseng\n *\n *  Sonification module.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Imports\n *\n * */\n\nvar defaultOptions = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).defaultOptions, getOptions = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).getOptions;\n\nvar addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, Sonification_extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, fireEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).fireEvent, Sonification_merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, Sonification_pick = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).pick;\n\nvar Sonification_doc = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).doc, Sonification_win = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).win;\n\n\n\n\n\n\n/**\n * The Sonification class. This class represents a chart's sonification\n * capabilities. A chart automatically gets an instance of this class when\n * applicable.\n *\n * @sample highcharts/sonification/chart-events\n *         Basic demo accessing some of the chart.sonification methods.\n * @sample highcharts/demo/sonification-navigation\n *         More advanced demo using more functionality.\n *\n * @requires modules/sonification\n *\n * @class\n * @name Highcharts.Sonification\n *\n * @param {Highcharts.Chart} chart The chart to tie the sonification to\n */\nvar Sonification = /** @class */ (function () {\n    function Sonification(chart) {\n        this.chart = chart;\n        this.retryContextCounter = 0;\n        this.lastUpdate = 0;\n        this.unbindKeydown = addEvent(Sonification_doc, 'keydown', function (e) {\n            if (chart && chart.sonification &&\n                (e.key === 'Esc' || e.key === 'Escape')) {\n                chart.sonification.cancel();\n            }\n        });\n        try {\n            this.audioContext = new Sonification_win.AudioContext();\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            this.audioContext.suspend();\n            this.audioDestination = this.audioContext.destination;\n        }\n        catch (e) { /* Ignore */ }\n    }\n    /**\n     * Set the audio destination node to something other than the default\n     * output. This allows for inserting custom WebAudio chains after the\n     * sonification.\n     * @function Highcharts.Sonification#setAudioDestination\n     * @param {AudioDestinationNode} audioDestination The destination node\n     */\n    Sonification.prototype.setAudioDestination = function (audioDestination) {\n        this.audioDestination = audioDestination;\n        this.update();\n    };\n    /**\n     * Check if sonification is playing currently\n     * @function Highcharts.Sonification#isPlaying\n     * @return {boolean} `true` if currently playing, `false` if not\n     */\n    Sonification.prototype.isPlaying = function () {\n        return !!this.timeline && this.timeline.isPlaying;\n    };\n    /**\n     * Divide timeline into 100 parts of equal time, and play one of them.\n     * Can be used for scrubbing navigation.\n     * @function Highcharts.Sonification#playSegment\n     *\n     * @sample highcharts/sonification/scrubbing\n     *         Scrubbing with slider\n     *\n     * @param {number} segment The segment to play, from 0 to 100\n     * @param {Highcharts.SonificationChartEventCallback} [onEnd] Callback to call after play completed\n     */\n    Sonification.prototype.playSegment = function (segment, onEnd) {\n        if (!this.ready(this.playSegment.bind(this, segment, onEnd))) {\n            return;\n        }\n        if (this.timeline) {\n            this.timeline.playSegment(segment, onEnd);\n        }\n    };\n    /**\n     * Play point(s)/event(s) adjacent to current timeline cursor location.\n     * @function Highcharts.Sonification#playAdjacent\n     *\n     * @sample highcharts/demo/sonification-navigation\n     *         Sonification keyboard navigation\n     *\n     * @param {number} next Pass `true` to play next point, `false` for previous\n     * @param {Highcharts.SonificationChartEventCallback} [onEnd]\n     * Callback to call after play completed\n     * @param {Highcharts.SonificationTimelineFilterCallback} [eventFilter]\n     * Filter to apply to the events before finding adjacent to play\n     */\n    Sonification.prototype.playAdjacent = function (next, onEnd, eventFilter) {\n        var _this = this;\n        if (!this.ready(this.playAdjacent.bind(this, next, onEnd, eventFilter))) {\n            return;\n        }\n        if (this.timeline) {\n            var opts = this.chart.options.sonification,\n                onHit = opts && opts.events && opts.events.onBoundaryHit;\n            if (!onHit) {\n                this.initBoundaryInstrument();\n            }\n            this.timeline.playAdjacent(next, onEnd, onHit || (function () {\n                _this.defaultBoundaryHit();\n            }), eventFilter);\n        }\n    };\n    /**\n     * Play next/previous series, picking the point closest to a prop value\n     * from last played point. By default picks the point in the adjacent\n     * series with the closest x value as the last played point.\n     * @function Highcharts.Sonification#playAdjacentSeries\n     *\n     * @sample highcharts/demo/sonification-navigation\n     *         Sonification keyboard navigation\n     *\n     * @param {number} next Pass `true` to play next series, `false` for previous\n     * @param {string} [prop] Prop to find closest value of, defaults to `x`.\n     * @param {Highcharts.SonificationChartEventCallback} [onEnd]\n     * Callback to call after play completed\n     *\n     * @return {Highcharts.Series|null} The played series, or `null` if none found\n     */\n    Sonification.prototype.playAdjacentSeries = function (next, prop, onEnd) {\n        if (prop === void 0) { prop = 'x'; }\n        var lastPlayed = this.getLastPlayedPoint();\n        if (lastPlayed) {\n            var targetSeriesIx_1 = lastPlayed.series.index + (next ? 1 : -1);\n            this.playClosestToProp(prop, lastPlayed[prop], function (e) { return !!e.relatedPoint &&\n                e.relatedPoint.series.index === targetSeriesIx_1; }, onEnd);\n            return this.chart.series[targetSeriesIx_1] || null;\n        }\n        return null;\n    };\n    /**\n     * Play point(s)/event(s) closest to a prop relative to a reference value.\n     * @function Highcharts.Sonification#playClosestToProp\n     *\n     * @param {string} prop Prop to compare.\n     * @param {number} targetValue Target value to find closest value of.\n     * @param {Highcharts.SonificationTimelineFilterCallback} [targetFilter]\n     * Filter to apply to the events before finding closest point(s)\n     * @param {Highcharts.SonificationChartEventCallback} [onEnd]\n     * Callback to call after play completed\n     */\n    Sonification.prototype.playClosestToProp = function (prop, targetValue, targetFilter, onEnd) {\n        var _this = this;\n        if (!this.ready(this.playClosestToProp.bind(this, prop, targetValue, targetFilter, onEnd))) {\n            return;\n        }\n        if (this.timeline) {\n            var opts = this.chart.options.sonification,\n                onHit = opts && opts.events && opts.events.onBoundaryHit;\n            if (!onHit) {\n                this.initBoundaryInstrument();\n            }\n            this.timeline.playClosestToPropValue(prop, targetValue, onEnd, onHit || (function () {\n                return _this.defaultBoundaryHit();\n            }), targetFilter);\n        }\n    };\n    /**\n     * Get last played point\n     * @function Highcharts.Sonification#getLastPlayedPoint\n     *\n     * @sample highcharts/demo/sonification-navigation\n     *         Sonification keyboard navigation\n     *\n     * @return {Highcharts.Point|null} The point, or null if none\n     */\n    Sonification.prototype.getLastPlayedPoint = function () {\n        if (this.timeline) {\n            return this.timeline.getLastPlayedPoint();\n        }\n        return null;\n    };\n    /**\n     * Play a note with a specific instrument, and optionally a time offset.\n     * @function Highcharts.Sonification#playNote\n     *\n     * @sample highcharts/sonification/chart-events\n     *         Custom notifications\n     *\n     * @param {Highcharts.SonificationSynthPreset|Highcharts.SynthPatchOptionsObject} instrument\n     * The instrument to play. Can be either a string referencing the\n     * instrument presets, or an actual SynthPatch configuration.\n     * @param {Highcharts.SonificationInstrumentScheduledEventOptionsObject} options\n     * Configuration for the instrument event to play.\n     * @param {number} [delayMs]\n     * Time offset from now, in milliseconds. Defaults to 0.\n     */\n    Sonification.prototype.playNote = function (instrument, options, delayMs) {\n        if (delayMs === void 0) { delayMs = 0; }\n        if (!this.ready(this.playNote.bind(this, instrument, options))) {\n            return;\n        }\n        var duration = options.noteDuration = options.noteDuration || 500;\n        var instr = new Sonification_SonificationInstrument(this.audioContext,\n            this.audioDestination, {\n                synthPatch: instrument,\n                capabilities: {\n                    filters: true,\n                    tremolo: true,\n                    pan: true\n                }\n            });\n        instr.scheduleEventAtTime(delayMs / 1000, options);\n        setTimeout(function () { return instr && instr.destroy(); }, delayMs + duration + 500);\n    };\n    /**\n     * Speak a text string, optionally with a custom speaker configuration\n     * @function Highcharts.Sonification#speak\n     *\n     * @sample highcharts/sonification/chart-events\n     *         Custom notifications\n     *\n     * @param {string} text Text to announce\n     * @param {Highcharts.SonificationSpeakerOptionsObject} [speakerOptions]\n     * Options for the announcement\n     * @param {number} [delayMs]\n     * Time offset from now, in milliseconds. Defaults to 0.\n     */\n    Sonification.prototype.speak = function (text, speakerOptions, delayMs) {\n        if (delayMs === void 0) { delayMs = 0; }\n        var speaker = new Sonification_SonificationSpeaker(Sonification_merge({\n                language: 'en-US',\n                rate: 1.5,\n                volume: 0.4\n            },\n            speakerOptions || {}));\n        speaker.sayAtTime(delayMs, text);\n    };\n    /**\n     * Cancel current playing audio and reset the timeline.\n     * @function Highcharts.Sonification#cancel\n     */\n    Sonification.prototype.cancel = function () {\n        if (this.timeline) {\n            this.timeline.cancel();\n        }\n        fireEvent(this, 'cancel');\n    };\n    /**\n     * Start download of a MIDI file export of the timeline.\n     * @function Highcharts.Sonification#downloadMIDI\n     */\n    Sonification.prototype.downloadMIDI = function () {\n        if (!this.ready(this.downloadMIDI.bind(this))) {\n            return;\n        }\n        if (this.timeline) {\n            this.timeline.reset();\n            this.timeline.downloadMIDI();\n        }\n    };\n    /**\n     * Implementation of chart.sonify\n     * @private\n     */\n    Sonification.prototype.sonifyChart = function (resetAfter, onEnd) {\n        if (!this.ready(this.sonifyChart.bind(this, resetAfter, onEnd))) {\n            return;\n        }\n        if (this.timeline) {\n            this.timeline.reset();\n            this.beforePlay();\n            this.timeline.play(void 0, void 0, resetAfter, onEnd);\n        }\n    };\n    /**\n     * Implementation of series.sonify\n     * @private\n     */\n    Sonification.prototype.sonifySeries = function (series, resetAfter, onEnd) {\n        if (!this.ready(this.sonifySeries.bind(this, series, resetAfter, onEnd))) {\n            return;\n        }\n        if (this.timeline) {\n            this.timeline.reset();\n            this.beforePlay();\n            this.timeline.play(function (e) {\n                return !!e.relatedPoint && e.relatedPoint.series === series;\n            }, void 0, resetAfter, onEnd);\n        }\n    };\n    /**\n     * Implementation of point.sonify\n     * @private\n     */\n    Sonification.prototype.sonifyPoint = function (point, onEnd) {\n        if (!this.ready(this.sonifyPoint.bind(this, point, onEnd))) {\n            return;\n        }\n        if (this.timeline) {\n            this.timeline.reset();\n            this.beforePlay();\n            this.timeline.anchorPlayMoment(function (e) { return e.relatedPoint === point; }, onEnd);\n        }\n    };\n    /**\n     * Set the overall/master volume for the sonification.\n     * Usually handled through chart update.\n     * @private\n     */\n    Sonification.prototype.setMasterVolume = function (vol) {\n        if (this.timeline) {\n            this.timeline.setMasterVolume(vol);\n        }\n    };\n    /**\n     * Destroy the sonification capabilities\n     * @private\n     */\n    Sonification.prototype.destroy = function () {\n        this.unbindKeydown();\n        if (this.timeline) {\n            this.timeline.destroy();\n            delete this.timeline;\n        }\n        if (this.boundaryInstrument) {\n            this.boundaryInstrument.stop();\n        }\n        if (this.audioContext) {\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\n            this.audioContext.close();\n            delete this.audioContext;\n        }\n    };\n    /**\n     * Update the timeline with latest chart changes. Usually handled\n     * automatically. Note that the [sonification.updateInterval](https://api.highcharts.com/highcharts/sonification.updateInterval)\n     * option can stop updates from happening in rapid succession, including\n     * manual calls to this function.\n     * @private\n     */\n    Sonification.prototype.update = function () {\n        var sOpts = this.chart.options && this.chart.options.sonification;\n        if (!this.ready(this.update.bind(this)) || !sOpts) {\n            return;\n        }\n        // Don't update too often, it gets performance intensive\n        var now = Date.now(),\n            updateInterval = sOpts.updateInterval;\n        if (now - this.lastUpdate < updateInterval && !this.forceReady) {\n            clearTimeout(this.scheduledUpdate);\n            this.scheduledUpdate = setTimeout(this.update.bind(this), updateInterval / 2);\n            return;\n        }\n        var events = sOpts.events || {};\n        if (events.beforeUpdate) {\n            events.beforeUpdate({ chart: this.chart, timeline: this.timeline });\n        }\n        this.lastUpdate = now;\n        if (this.timeline) {\n            this.timeline.destroy();\n        }\n        if (this.audioContext && this.audioDestination) {\n            this.timeline = TimelineFromChart(this.audioContext, this.audioDestination, this.chart);\n            var sOpts_1 = this.chart.options.sonification;\n            this.timeline.setMasterVolume(Sonification_pick(sOpts_1 && sOpts_1.masterVolume, 1));\n        }\n        if (events.afterUpdate) {\n            events.afterUpdate({ chart: this.chart, timeline: this.timeline });\n        }\n    };\n    /**\n     * Only continue if sonification enabled. If audioContext is\n     * suspended, retry up to 20 times with a small delay.\n     * @private\n     */\n    Sonification.prototype.ready = function (whenReady) {\n        var _this = this;\n        if (!this.audioContext ||\n            !this.audioDestination ||\n            !this.chart.options ||\n            this.chart.options.sonification &&\n                this.chart.options.sonification.enabled === false) {\n            return false;\n        }\n        if (this.audioContext.state === 'suspended' && !this.forceReady) {\n            if (this.retryContextCounter++ < 20) {\n                setTimeout(function () {\n                    if (_this.audioContext &&\n                        _this.audioContext.state === 'suspended') {\n                        // eslint-disable-next-line @typescript-eslint/no-floating-promises\n                        _this.audioContext.resume().then(whenReady);\n                    }\n                    else {\n                        whenReady();\n                    }\n                }, 5);\n            }\n            return false;\n        }\n        this.retryContextCounter = 0;\n        return true;\n    };\n    /**\n     * Call beforePlay event handler if exists\n     * @private\n     */\n    Sonification.prototype.beforePlay = function () {\n        var opts = this.chart.options.sonification,\n            beforePlay = opts && opts.events && opts.events.beforePlay;\n        if (beforePlay) {\n            beforePlay({ chart: this.chart, timeline: this.timeline });\n        }\n    };\n    /**\n     * Initialize the builtin boundary hit instrument\n     * @private\n     */\n    Sonification.prototype.initBoundaryInstrument = function () {\n        if (!this.boundaryInstrument) {\n            this.boundaryInstrument = new Sonification_SynthPatch(this.audioContext, Sonification_merge(Sonification_InstrumentPresets.chop, { masterVolume: 0.3 }));\n            this.boundaryInstrument.startSilently();\n            this.boundaryInstrument.connect(this.audioDestination);\n        }\n    };\n    /**\n     * The default boundary hit sound\n     * @private\n     */\n    Sonification.prototype.defaultBoundaryHit = function () {\n        if (this.boundaryInstrument) {\n            this.boundaryInstrument.playFreqAtTime(0.1, 1, 200);\n            this.boundaryInstrument.playFreqAtTime(0.2, 1, 200);\n        }\n    };\n    return Sonification;\n}());\n(function (Sonification) {\n    var composedClasses = [];\n    /**\n     * Update sonification object on chart.\n     * @private\n     */\n    function updateSonificationEnabled() {\n        var sonification = this.sonification,\n            sOptions = this.options && this.options.sonification;\n        if (sOptions && sOptions.enabled) {\n            if (sonification) {\n                sonification.update();\n            }\n            else {\n                this.sonification = new Sonification(this);\n                this.sonification.update();\n            }\n        }\n        else if (sonification) {\n            sonification.destroy();\n            delete this.sonification;\n        }\n    }\n    /**\n     * Destroy with chart.\n     * @private\n     */\n    function chartOnDestroy() {\n        if (this && this.sonification) {\n            this.sonification.destroy();\n        }\n    }\n    /**\n     * Update on render\n     * @private\n     */\n    function chartOnRender() {\n        if (this.updateSonificationEnabled) {\n            this.updateSonificationEnabled();\n        }\n    }\n    /**\n     * Update\n     * @private\n     */\n    function chartOnUpdate(e) {\n        var newOptions = e.options.sonification;\n        if (newOptions) {\n            Sonification_merge(true, this.options.sonification, newOptions);\n            chartOnRender.call(this);\n        }\n    }\n    /**\n     * Compose\n     * @private\n     */\n    function compose(ChartClass, SeriesClass, PointClass) {\n        // Extend chart\n        if (composedClasses.indexOf(ChartClass) === -1) {\n            composedClasses.push(ChartClass);\n            Sonification_extend(ChartClass.prototype, {\n                updateSonificationEnabled: updateSonificationEnabled,\n                sonify: function (onEnd) {\n                    if (this.sonification) {\n                        this.sonification.sonifyChart(false, onEnd);\n                    }\n                },\n                toggleSonify: function (reset, onEnd) {\n                    if (reset === void 0) { reset = true; }\n                    if (!this.sonification) {\n                        return;\n                    }\n                    var timeline = this.sonification.timeline;\n                    if (Sonification_win.speechSynthesis) {\n                        Sonification_win.speechSynthesis.cancel();\n                    }\n                    if (timeline && this.sonification.isPlaying()) {\n                        if (reset) {\n                            this.sonification.cancel();\n                        }\n                        else {\n                            timeline.pause();\n                        }\n                    }\n                    else if (timeline && timeline.isPaused) {\n                        timeline.resume();\n                    }\n                    else {\n                        this.sonification.sonifyChart(reset, onEnd);\n                    }\n                }\n            });\n            addEvent(ChartClass, 'destroy', chartOnDestroy);\n            addEvent(ChartClass, 'render', chartOnRender);\n            addEvent(ChartClass, 'update', chartOnUpdate);\n        }\n        // Extend series\n        if (composedClasses.indexOf(SeriesClass) === -1) {\n            composedClasses.push(SeriesClass);\n            SeriesClass.prototype.sonify = function (onEnd) {\n                if (this.chart.sonification) {\n                    this.chart.sonification.sonifySeries(this, false, onEnd);\n                }\n            };\n        }\n        // Extend points\n        if (composedClasses.indexOf(PointClass) === -1) {\n            composedClasses.push(PointClass);\n            PointClass.prototype.sonify = function (onEnd) {\n                if (this.series.chart.sonification) {\n                    this.series.chart.sonification.sonifyPoint(this, onEnd);\n                }\n            };\n        }\n        // Add items to the exporting menu\n        var exportingOptions = getOptions().exporting;\n        if (exportingOptions &&\n            exportingOptions.buttons &&\n            exportingOptions.buttons.contextButton.menuItems) {\n            exportingOptions.buttons.contextButton.menuItems.push('separator', 'downloadMIDI', 'playAsSound');\n        }\n    }\n    Sonification.compose = compose;\n})(Sonification || (Sonification = {}));\n// Add default options\nSonification_merge(true, defaultOptions, Sonification_Options);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Sonification_Sonification = (Sonification);\n/* *\n *\n *  API declarations\n *\n * */\n/**\n * Play a sonification of a chart.\n *\n * @function Highcharts.Chart#sonify\n * @param {Highcharts.SonificationChartEventCallback} [onEnd]\n * Callback to call after play completed\n *\n * @requires modules/sonification\n */\n/**\n * Play/pause sonification of a chart.\n *\n * @function Highcharts.Chart#toggleSonify\n *\n * @param {boolean} [reset]\n * Reset the playing cursor after play completed. Defaults to `true`.\n * @param {Highcharts.SonificationChartEventCallback} [onEnd]\n * Callback to call after play completed\n *\n * @requires modules/sonification\n */\n/**\n * Play a sonification of a series.\n *\n * @function Highcharts.Series#sonify\n * @param {Highcharts.SonificationChartEventCallback} [onEnd]\n * Callback to call after play completed\n *\n * @requires modules/sonification\n */\n/**\n * Play a sonification of a point.\n *\n * @function Highcharts.Point#sonify\n * @param {Highcharts.SonificationChartEventCallback} [onEnd]\n * Callback to call after play completed\n *\n * @requires modules/sonification\n */\n/**\n * Sonification capabilities for the chart.\n *\n * @name Highcharts.Chart#sonification\n * @type {Highcharts.Sonification|undefined}\n *\n * @requires modules/sonification\n */\n/**\n * Collection of Sonification classes and objects.\n * @requires modules/sonification\n * @interface Highcharts.SonificationGlobalObject\n */ /**\n* SynthPatch presets\n* @name Highcharts.SonificationGlobalObject#InstrumentPresets\n* @type {Record<Highcharts.SonificationSynthPreset,Highcharts.SynthPatchOptionsObject>|undefined}\n*/ /**\n* Musical scale presets\n* @name Highcharts.SonificationGlobalObject#Scales\n* @type {Highcharts.SonificationScalePresetsObject|undefined}\n*/ /**\n* SynthPatch class\n* @name Highcharts.SonificationGlobalObject#SynthPatch\n* @type {Highcharts.SynthPatch|undefined}\n*/ /**\n* SonificationInstrument class\n* @name Highcharts.SonificationGlobalObject#SonificationInstrument\n* @type {Highcharts.SonificationInstrument|undefined}\n*/ /**\n* SonificationSpeaker class\n* @name Highcharts.SonificationGlobalObject#SonificationSpeaker\n* @type {Highcharts.SonificationSpeaker|undefined}\n*/\n/**\n * Global Sonification classes and objects.\n *\n * @name Highcharts.sonification\n * @type {Highcharts.SonificationGlobalObject}\n *\n * @requires modules/sonification\n */\n(''); // Keep above doclets in JS file\n\n;// ./code/es5/es-modules/Extensions/Sonification/Scales.js\n/* *\n *\n *  (c) 2009-2025 Øystein Moseng\n *\n *  Musical scales for sonification.\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar Scales = {\n    minor: [0, 2, 3, 5, 7, 8, 10],\n    dorian: [0, 2, 3, 5, 7, 9, 10],\n    harmonicMinor: [0, 2, 3, 5, 7, 8, 11],\n    phrygian: [0, 1, 3, 5, 7, 8, 11],\n    major: [0, 2, 4, 5, 7, 9, 11],\n    lydian: [0, 2, 4, 6, 7, 9, 11],\n    mixolydian: [0, 2, 4, 5, 7, 9, 10],\n    majorPentatonic: [0, 2, 4, 7, 9],\n    minorPentatonic: [0, 3, 5, 7, 10]\n};\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Sonification_Scales = (Scales);\n/* *\n *\n *  API declarations\n *\n * */\n/**\n * Preset scales for pitch mapping.\n * @requires modules/sonification\n * @interface Highcharts.SonificationScalePresetsObject\n */ /**\n* Minor scale (aeolian)\n* @name Highcharts.SonificationScalePresetsObject#minor\n* @type {Array<number>}\n*/ /**\n* Dorian scale\n* @name Highcharts.SonificationScalePresetsObject#dorian\n* @type {Array<number>}\n*/ /**\n* Harmonic minor scale\n* @name Highcharts.SonificationScalePresetsObject#harmonicMinor\n* @type {Array<number>}\n*/ /**\n* Phrygian scale\n* @name Highcharts.SonificationScalePresetsObject#phrygian\n* @type {Array<number>}\n*/ /**\n* Major (ionian) scale\n* @name Highcharts.SonificationScalePresetsObject#major\n* @type {Array<number>}\n*/ /**\n* Lydian scale\n* @name Highcharts.SonificationScalePresetsObject#lydian\n* @type {Array<number>}\n*/ /**\n* Mixolydian scale\n* @name Highcharts.SonificationScalePresetsObject#mixolydian\n* @type {Array<number>}\n*/ /**\n* Major pentatonic scale\n* @name Highcharts.SonificationScalePresetsObject#majorPentatonic\n* @type {Array<number>}\n*/ /**\n* Minor pentatonic scale\n* @name Highcharts.SonificationScalePresetsObject#minorPentatonic\n* @type {Array<number>}\n*/\n(''); // Keep above doclets in JS file\n\n;// ./code/es5/es-modules/masters/modules/sonification.js\n\n\n\n\n\n\n\n\n\n\nvar G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n// Global objects\nG.sonification = {\n    InstrumentPresets: Sonification_InstrumentPresets,\n    Scales: Sonification_Scales,\n    SynthPatch: Sonification_SynthPatch,\n    SonificationInstrument: Sonification_SonificationInstrument,\n    SonificationSpeaker: Sonification_SonificationSpeaker,\n    SonificationTimeline: Sonification_SonificationTimeline,\n    Sonification: Sonification_Sonification\n};\nSonification_Sonification.compose(G.Chart, G.Series, G.Point);\n/* harmony default export */ var sonification_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__984__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "sonification_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "__assign", "assign", "t", "s", "i", "arguments", "length", "p", "apply", "clamp", "defined", "pick", "getPitchTrackedMultiplierVal", "multiplier", "freq", "Math", "log", "miniRampToVolAtTime", "gainNode", "time", "vol", "gain", "cancelScheduledValues", "setTargetAtTime", "SynthPatch", "stopRampTime", "setValueAtTime", "scheduleGainEnvelope", "envelope", "type", "volumeMultiplier", "isAtk", "unshift", "for<PERSON>ach", "ep", "ix", "prev", "delta", "startTime", "max", "PulseOscNode", "context", "options", "pulseWidth", "min", "makeOsc", "OscillatorNode", "detune", "frequency", "sawOscA", "sawOscB", "phaseInverter", "GainNode", "masterGain", "delayNode", "DelayNode", "delayTime", "value", "connect", "destination", "getFrequencyFacade", "pulse", "fromTime", "round", "timeConstant", "getPWMTarget", "start", "stop", "Oscillator", "audioContext", "fmOscillatorIx", "fmOscillator", "vmOscillatorIx", "vmOscillator", "createSoundSource", "createGain", "createFilters", "createVolTracking", "lowpassNode", "highpassNode", "volTrackingNode", "vmNode", "white<PERSON><PERSON>", "pulseNode", "oscNode", "reduce", "cur", "stopAtTime", "setFreqAtTime", "glideDuration", "opts", "f", "fixedFrequency", "freqMultiplier", "oscTarget", "get<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastUpdateTime", "scheduleVolTrackingChange", "scheduleFilterTrackingChange", "getFMTarget", "getVMTarget", "runEnvelopeAtTime", "attackEnvelope", "releaseEnvelope", "volume", "cancelScheduled", "currentTime", "v", "volumePitchTrackingMultiplier", "rampTime", "scheduleFilter<PERSON>arget", "filterNode", "filterOptions", "frequencyPitchTrackingMultiplier", "lowpass", "highpass", "ctx", "bSize", "sampleRate", "buffer", "createBuffer", "data", "getChannelData", "random", "wn", "createBufferSource", "loop", "BiquadFilterNode", "Q", "_this", "eqNodes", "midiInstrument", "outputNode", "createEqChain", "inputNode", "oscillators", "map", "oscOpts", "osc", "connectTarget", "targetFunc", "targetOsc", "target", "startSilently", "curTime", "endTime", "disconnect", "silenceAtTime", "releaseAtTime", "mute", "playFreqAtTime", "noteDuration", "noteGlideDuration", "masterAttackEnvelope", "masterVolume", "destinationNode", "eq", "eqDef", "reduceRight", "chain", "node", "maxReleaseDuration", "env", "masterEnv", "masterReleaseEnvelope", "InstrumentPresets", "piano", "plucked", "flute", "lead", "vibraphone", "saxophone", "trumpet", "sawsynth", "basic1", "basic2", "chord", "wobble", "sine", "sineGlide", "triangle", "sawtooth", "square", "chop", "shaker", "step", "kick", "shortnote", "noise", "filteredN<PERSON>", "wind", "SonificationInstrument_defined", "extend", "SonificationInstrument", "curPara<PERSON>", "midiTrackName", "masterVolNode", "volumeNode", "createNodesFromCapabilities", "pan", "capabilities", "connectCapabilityNodes", "synthPatch", "Sonification_InstrumentPresets", "setMasterVolume", "scheduleEventAtTime", "params", "mergedParams", "note", "musicalNoteToFrequency", "tremolo<PERSON><PERSON>h", "tremoloSpeed", "setTremoloAtTime", "setPanAtTime", "setVolumeAtTime", "lowpassFreq", "lowpassResonance", "setFilterAtTime", "highpassFreq", "highpassResonance", "cancel", "tremoloOsc", "panNode", "destroy", "filter", "resonance", "audioTime", "depth", "speed", "StereoPannerNode", "tremolo", "filters", "input", "output", "noteStringToC0Distance", "match", "semitone", "wholetone", "toLowerCase", "accidental", "c", "e", "g", "b", "octave", "parseInt", "pow", "Sonification_SynthPatch", "SonificationSpeaker_pick", "SonificationSpeaker", "synthesis", "window", "speechSynthesis", "onvoiceschanged", "setVoice", "bind", "scheduled", "say", "message", "utterance", "SpeechSynthesisUtterance", "voice", "rate", "pitch", "speak", "sayAtTime", "push", "setTimeout", "clearTimeout", "name_1", "name", "lang", "language", "voices", "getVoices", "len", "lang<PERSON><PERSON><PERSON>", "TimelineChannel", "engine", "showPlayMarker", "events", "muted", "addEvent", "event", "lastEvent", "splice", "unmute", "MIDI_pick", "byte", "timeInfo", "varLenEnc", "buf", "res", "toMIDIEvents", "cachedVel", "cachedDur", "add", "el", "timeMS", "instrumentEventOptions", "dur", "tNOF", "ctrl", "valMap", "noteVal", "LN2", "Sonification_SonificationInstrument", "ctrlDef", "keys", "ctrlSignal", "val", "getMetaEvents", "textArr", "code", "charCodeAt", "concat", "getTrackChunk", "addTimeInfo", "prevTime", "metaEvents", "trackEvents", "trackEnd", "size", "MIDI", "channels", "nTracks", "channelsToAdd", "numCh", "multiCh", "Uint8Array", "<PERSON><PERSON><PERSON><PERSON>", "chunks", "channel", "<PERSON><PERSON><PERSON><PERSON>", "win", "doc", "document", "domurl", "URL", "webkitURL", "SonificationTimeline_downloadURL", "dataURL", "filename", "nav", "navigator", "createElement", "String", "msSaveOrOpenBlob", "userAgent", "Error", "isOldEdgeBrowser", "test", "safariBlob", "indexOf", "dataURLtoBlob", "parts", "replace", "atob", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Blob", "createObjectURL", "binStr", "binary", "download", "href", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "open", "_a", "location", "SonificationTimeline_defined", "find", "merge", "SonificationTimeline", "chart", "isPaused", "isPlaying", "scheduledCallbacks", "playTimestamp", "resumeFromTime", "addChannel", "play", "filterPersists", "resetAfter", "onEnd", "filtered", "minTime", "clearScheduledCallbacks", "onEndArgument", "Date", "now", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onPlay", "showTooltip", "showCrosshair", "playingChannels", "filteredEvents", "acc", "Infinity", "pointsPlayed", "timeline", "maxTime", "numEvents", "lastCallbackTime", "lastEventTime", "lastEvent<PERSON>eys", "_loop_1", "keysSig", "speechOptions", "join", "point", "relatedPoint", "series", "<PERSON><PERSON><PERSON><PERSON>", "callback", "xAxis", "crosshair", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "yAxis", "hoverPoints", "onMouseOver", "onEndOpt", "onStop", "resetPlayState", "tooltip", "hide", "hoverSeries", "onMouseOut", "axes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "pause", "getCurrentTime", "resume", "resumeFrom_1", "anchorPlayMoment", "eventFilter", "finalEventTime", "arr", "play<PERSON>d<PERSON><PERSON>", "next", "onBoundaryHit", "closestTime", "lastValidTime", "mid", "cmp", "attemptedNext", "withinTime", "playClosestToPropValue", "targetVal", "closestValDiff", "closestEvent", "diff", "abs", "getEventsForPoint", "pointEvents", "playSegment", "segment", "eventTimes", "first", "last", "segmentSize", "fromTime_1", "toTime_1", "some", "getLastPlayedPoint", "closestDiff", "closestPoint", "reset", "getMIDIData", "downloadMIDI", "title", "text", "blob", "url", "revokeObjectURL", "highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_", "highcharts_Templating_commonjs_highcharts_Templating_commonjs2_highcharts_Templating_root_Highcharts_Templating_default", "TimelineFromChart_assign", "TimelineFromChart_clamp", "TimelineFromChart_defined", "TimelineFromChart_extend", "getNestedProperty", "TimelineFromChart_merge", "TimelineFrom<PERSON><PERSON>_pick", "format", "isNoteDefinition", "str", "getPointPropValue", "ret", "mapToVirtualAxis", "valueExtremes", "virtualAxisExtremes", "invert", "logarithmic", "lenValueAxis", "lenVirtualAxis", "virtualValueDelta", "x", "LOG10E", "adjustedNum", "LN10", "logValMin", "getParamValWithDefault", "propMetrics", "useSeriesExtremes", "mappingParamOptions", "fallback", "defaults", "contextValueProp", "getMappingParameterValue", "defaultMapping", "mappingOptions", "scale", "mapTo", "mapFunc", "mapFunction", "within", "isInverted", "char<PERSON>t", "slice", "useContextValue", "fixedValue", "extremes", "axis", "dataMin", "dataMax", "seriesExtremes", "index", "globalExtremes", "scaleAxis", "minOctave", "floor", "maxOctave", "ceil", "lenScale", "scaleIx", "noteNum", "addTimelineChannelFromTrack", "instrMappingOpts", "mapping", "speechOpts", "preferredVoice", "instrument", "midiName", "addMappedInstrumentEvent", "roundToMusicalNotes", "getPara<PERSON>", "param", "parent", "eventsAdded", "eventOpts", "gapBetweenNotes", "play<PERSON>elay", "addNoteEvent", "noteDef", "constructor", "Array", "addMappedSpeechEvent", "messageParam", "isActive", "activeWhen", "lastPropValue", "crossingOk", "crossingUp", "crossingDown", "hasLastValue", "TimelineFromChart", "globalOpts", "defaultInstrMapping", "defaultSpeechMapping", "seriesTimeProps", "commonTimeProps", "addTimeProp", "props", "perSeriesProps", "addPropFromMappingParam", "addPropsFromMappingOptions", "addPropsFromContextTracks", "hasCommonTimeProps", "sonification", "defaultInstrOpts", "defaultInstrumentOptions", "defaultSpeechOpts", "defaultSpeechOptions", "defaultPointGroupOpts", "enabled", "groupTimespan", "algorithm", "pointGrouping", "globalTracks", "globalContextTracks", "isSequential", "order", "totalDuration", "duration", "afterSeriesWait", "eventOptions", "seriesIx", "removeInvertedFlag", "paramOpts", "subParam", "tracks", "track", "valueProp", "sOpts", "visible", "defaultInstrMapping_1", "defaultSpeechMapping_1", "contextTracks", "trackOpts", "getChartExtremesForProps", "numProps", "numSeriesProps", "initCache", "propList", "cache", "updateCache", "allSeriesExtremes", "points", "j", "k", "sOptions", "lastPropValue_1", "seriesDuration_1", "getAvailableDurationForSeries", "timeProp", "seriesDuration", "availableDuration", "every", "timeProps", "seriesTimeLen", "sum", "totalPoints", "seriesDefaultInstrOpts_1", "seriesDefaultSpeechOpts_1", "seriesPointGroupOpts_1", "mainTracks", "hasAddedSeries", "eventsAdded_1", "mergedOpts", "pointGroupOpts", "updateLastPropValue", "addMappedEventForPoint", "trackOptions", "eventAdded", "pointGroup", "pointGroupTime", "addCurrentPointGroup", "groupSpanTime", "getGroupedPoints", "alg", "r", "min_1", "max_1", "minVal_1", "maxVal_1", "prop_1", "t_1", "pointIx", "isLastPoint", "dT", "groupSpan", "spanTime", "firstEvent", "onSeriesStart", "onSeriesEnd", "contextChannel", "timeInterval", "valueInterval", "contextExtremes", "addContextEvent", "valueMapFunction", "defaultOptions", "getOptions", "Sonification_extend", "fireEvent", "Sonification_merge", "Sonification_pick", "Sonification_doc", "Sonification_win", "Sonification", "retryContextCounter", "lastUpdate", "unbindKeydown", "AudioContext", "suspend", "audioDestination", "setAudioDestination", "update", "ready", "onHit", "initBoundaryInstrument", "defaultBoundaryHit", "playAdjacentSeries", "lastPlayed", "targetSeriesIx_1", "playClosestToProp", "targetValue", "targetFilter", "playNote", "delayMs", "instr", "speakerOptions", "speaker", "son<PERSON><PERSON><PERSON>", "beforePlay", "sonifySeries", "sonifyPoint", "boundaryInstrument", "close", "updateInterval", "forceReady", "scheduledUpdate", "beforeUpdate", "sOpts_1", "afterUpdate", "when<PERSON><PERSON><PERSON>", "state", "then", "composedClasses", "updateSonificationEnabled", "chartOnDestroy", "chartOnRender", "chartOnUpdate", "newOptions", "compose", "ChartClass", "SeriesClass", "PointClass", "sonify", "toggleSonify", "exportingOptions", "exporting", "buttons", "contextButton", "menuItems", "menuItemDefinitions", "<PERSON><PERSON><PERSON>", "onclick", "playAsSound", "Sonification_Sonification", "G", "Scales", "minor", "dorian", "harmonicMinor", "phrygian", "major", "lydian", "mixolydian", "majorPentatonic", "minorPentatonic", "Chart", "Series", "Point"], "mappings": "CAYA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,UAAa,EAC5E,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,kCAAmC,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,aAAa,CAAC,CAAEJ,GACxG,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,kCAAkC,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,UAAa,EAE/GJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,UAAa,CACnF,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,EACpF,OAAgB,AAAC,WACP,aACA,IAAIC,EAAuB,CAE/B,IACC,SAASN,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,EAEA,IACC,SAASJ,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIE,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaX,OAAO,CAG5B,IAAIC,EAASO,CAAwB,CAACE,EAAS,CAAG,CAGjDV,QAAS,CAAC,CACX,EAMA,OAHAO,CAAmB,CAACG,EAAS,CAACT,EAAQA,EAAOD,OAAO,CAAES,GAG/CR,EAAOD,OAAO,AACtB,CAMCS,EAAoBI,CAAC,CAAG,SAASZ,CAAM,EACtC,IAAIa,EAASb,GAAUA,EAAOc,UAAU,CACvC,WAAa,OAAOd,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAQ,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAAShB,CAAO,CAAEkB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACpB,EAASmB,IAC5EE,OAAOC,cAAc,CAACtB,EAASmB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,EAAkB,CACjE,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAuiCjHE,EAAgD,WAShD,MAAOA,AARPA,CAAAA,EAAWb,OAAOc,MAAM,EAAI,SAASC,CAAC,EAClC,IAAK,IAAIC,EAAGC,EAAI,EAAGzB,EAAI0B,UAAUC,MAAM,CAAEF,EAAIzB,EAAGyB,IAE5C,IAAK,IAAIG,KADTJ,EAAIE,SAAS,CAACD,EAAE,CACKjB,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACQ,EAAGI,IACzDL,CAAAA,CAAC,CAACK,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,EAElB,OAAOL,CACX,CAAA,EACgBM,KAAK,CAAC,IAAI,CAAEH,UAChC,EAEII,EAAQ,AAACV,IAA+EU,KAAK,CAAEC,EAAU,AAACX,IAA+EW,OAAO,CAAEC,EAAO,AAACZ,IAA+EY,IAAI,CASjS,SAASC,EAA6BC,CAAU,CAAEC,CAAI,EAGlD,MAAO/B,AAFC,CAAA,MAAS8B,EAAa,KAAK,EAExBE,KAAKC,GAAG,CAACF,GADZ,AAAC,CAAA,IAAM,IAAMD,CAAS,EAAK,GAEvC,CAQA,SAASI,EAAoBC,CAAQ,CAAEC,CAAI,CAAEC,CAAG,EAC5CF,EAASG,IAAI,CAACC,qBAAqB,CAACH,GACpCD,EAASG,IAAI,CAACE,eAAe,CAACH,EAAKD,EAAMK,EAAWC,YAAY,CAAG,GACnEP,EAASG,IAAI,CAACK,cAAc,CAACN,EAAKD,EAAOK,EAAWC,YAAY,CACpE,CAUA,SAASE,EAAqBC,CAAQ,CAAEC,CAAI,CAAEV,CAAI,CAAED,CAAQ,CAAEY,CAAgB,EACjD,KAAK,IAA1BA,GAA+BA,CAAAA,EAAmB,CAAA,EACtD,IAAIC,EAAQF,AAAS,WAATA,EACRR,EAAOH,EAASG,IAAI,CAExB,GADAA,EAAKC,qBAAqB,CAACH,GACvB,CAACS,EAAStB,MAAM,CAAE,CAClBW,EAAoBC,EAAUC,EAAMY,EAAQD,EAAmB,GAC/D,MACJ,CACIF,CAAQ,CAAC,EAAE,CAAC1B,CAAC,CAAG,GAChB0B,EAASI,OAAO,CAAC,CAAE9B,EAAG,EAAGkB,IAAKW,EAAAA,CAAc,GAEhDH,EAASK,OAAO,CAAC,SAAUC,CAAE,CAAEC,CAAE,EAC7B,IAAIC,EAAOR,CAAQ,CAACO,EAAK,EAAE,CAAEE,EAAQD,EAAO,AAACF,CAAAA,EAAGhC,CAAC,CAAGkC,EAAKlC,CAAC,AAADA,EAAK,IAAO,EAAGoC,EAAYnB,EAAQiB,CAAAA,EAAOA,EAAKlC,CAAC,CAAG,IAAOsB,EAAWC,YAAY,CAAG,CAAA,EAC7IJ,EAAKE,eAAe,CAACW,EAAGd,GAAG,CAAGU,EAAkBQ,EAAWvB,KAAKwB,GAAG,CAACF,EAAOb,EAAWC,YAAY,EAAI,EAC1G,EACJ,CAQA,IAAIe,EAA8B,WAC9B,SAASA,EAAaC,CAAO,CAAEC,CAAO,EAClC,IAAI,CAACC,UAAU,CAAG5B,KAAK6B,GAAG,CAAC7B,KAAKwB,GAAG,CAAC,EAAGG,EAAQC,UAAU,EAAI,KAC7D,IAAIE,EAAU,WAAc,OAAO,IAAIC,eAAeL,EAAS,CACvDZ,KAAM,WACNkB,OAAQL,EAAQK,MAAM,CACtBC,UAAWjC,KAAKwB,GAAG,CAAC,EACxBG,EAAQM,SAAS,EAAI,IACrB,EAAI,CACR,CAAA,IAAI,CAACC,OAAO,CAAGJ,IACf,IAAI,CAACK,OAAO,CAAGL,IACf,IAAI,CAACM,aAAa,CAAG,IAAIC,SAASX,EAAS,CAAEpB,KAAM,EAAG,GACtD,IAAI,CAACgC,UAAU,CAAG,IAAID,SAASX,GAC/B,IAAI,CAACa,SAAS,CAAG,IAAIC,UAAUd,EAAS,CACpCe,UAAW,IAAI,CAACb,UAAU,CAAG,IAAI,CAACM,OAAO,CAACD,SAAS,CAACS,KAAK,AAC7D,GACA,IAAI,CAACR,OAAO,CAACS,OAAO,CAAC,IAAI,CAACL,UAAU,EACpC,IAAI,CAACH,OAAO,CAACQ,OAAO,CAAC,IAAI,CAACP,aAAa,EACvC,IAAI,CAACA,aAAa,CAACO,OAAO,CAAC,IAAI,CAACJ,SAAS,EACzC,IAAI,CAACA,SAAS,CAACI,OAAO,CAAC,IAAI,CAACL,UAAU,CAC1C,CA2CA,OA1CAb,EAAa/C,SAAS,CAACiE,OAAO,CAAG,SAAUC,CAAW,EAClD,IAAI,CAACN,UAAU,CAACK,OAAO,CAACC,EAC5B,EAEAnB,EAAa/C,SAAS,CAACmE,kBAAkB,CAAG,WACxC,IAAIC,EAAQ,IAAI,CAChB,MAAO,CACHvC,sBAAuB,SAAUwC,CAAQ,EAIrC,OAHAD,EAAMZ,OAAO,CAACD,SAAS,CAAC1B,qBAAqB,CAACwC,GAC9CD,EAAMX,OAAO,CAACF,SAAS,CAAC1B,qBAAqB,CAACwC,GAC9CD,EAAMP,SAAS,CAACE,SAAS,CAAClC,qBAAqB,CAACwC,GACzCD,EAAMZ,OAAO,CAACD,SAAS,AAClC,EACAtB,eAAgB,SAAUsB,CAAS,CAAE7B,CAAI,EAKrC,OAJA,IAAI,CAACG,qBAAqB,CAACH,GAC3B0C,EAAMZ,OAAO,CAACD,SAAS,CAACtB,cAAc,CAACsB,EAAW7B,GAClD0C,EAAMX,OAAO,CAACF,SAAS,CAACtB,cAAc,CAACsB,EAAW7B,GAClD0C,EAAMP,SAAS,CAACE,SAAS,CAAC9B,cAAc,CAACX,KAAKgD,KAAK,CAAC,IAAQF,EAAMlB,UAAU,CAAGK,GAAa,IAAO7B,GAC5F0C,EAAMZ,OAAO,CAACD,SAAS,AAClC,EACAzB,gBAAiB,SAAUyB,CAAS,CAAE7B,CAAI,CAAE6C,CAAY,EAOpD,OANA,IAAI,CAAC1C,qBAAqB,CAACH,GAC3B0C,EAAMZ,OAAO,CAACD,SAAS,CAClBzB,eAAe,CAACyB,EAAW7B,EAAM6C,GACtCH,EAAMX,OAAO,CAACF,SAAS,CAClBzB,eAAe,CAACyB,EAAW7B,EAAM6C,GACtCH,EAAMP,SAAS,CAACE,SAAS,CAACjC,eAAe,CAACR,KAAKgD,KAAK,CAAC,IAAQF,EAAMlB,UAAU,CAAGK,GAAa,IAAO7B,EAAM6C,GACnGH,EAAMZ,OAAO,CAACD,SAAS,AAClC,CACJ,CACJ,EACAR,EAAa/C,SAAS,CAACwE,YAAY,CAAG,WAClC,OAAO,IAAI,CAACX,SAAS,CAACE,SAAS,AACnC,EACAhB,EAAa/C,SAAS,CAACyE,KAAK,CAAG,WAC3B,IAAI,CAACjB,OAAO,CAACiB,KAAK,GAClB,IAAI,CAAChB,OAAO,CAACgB,KAAK,EACtB,EACA1B,EAAa/C,SAAS,CAAC0E,IAAI,CAAG,SAAUhD,CAAI,EACxC,IAAI,CAAC8B,OAAO,CAACkB,IAAI,CAAChD,GAClB,IAAI,CAAC+B,OAAO,CAACiB,IAAI,CAAChD,EACtB,EACOqB,CACX,IAMI4B,EAA4B,WAC5B,SAASA,EAAWC,CAAY,CAAE3B,CAAO,CAAEiB,CAAW,EAClD,IAAI,CAACU,YAAY,CAAGA,EACpB,IAAI,CAAC3B,OAAO,CAAGA,EACf,IAAI,CAAC4B,cAAc,CAAG5B,EAAQ6B,YAAY,CAC1C,IAAI,CAACC,cAAc,CAAG9B,EAAQ+B,YAAY,CAC1C,IAAI,CAACC,iBAAiB,GACtB,IAAI,CAACC,UAAU,GACf,IAAI,CAACC,aAAa,GAClB,IAAI,CAACC,iBAAiB,GAClBlB,GACA,IAAI,CAACD,OAAO,CAACC,EAErB,CA2NA,OAvNAS,EAAW3E,SAAS,CAACiE,OAAO,CAAG,SAAUC,CAAW,EAChD,CACI,IAAI,CAACmB,WAAW,CAChB,IAAI,CAACC,YAAY,CACjB,IAAI,CAACC,eAAe,CACpB,IAAI,CAACC,MAAM,CACX,IAAI,CAAC/D,QAAQ,CACb,IAAI,CAACgE,UAAU,CACf,IAAI,CAACC,SAAS,CACd,IAAI,CAACC,OAAO,CACf,CAACC,MAAM,CAAC,SAAUjD,CAAI,CAAEkD,CAAG,EACxB,OAAQA,EACHA,CAAAA,EAAI5B,OAAO,CAACtB,GAAOkD,CAAE,EACtBlD,CACR,EAAGuB,EACP,EACAS,EAAW3E,SAAS,CAACyE,KAAK,CAAG,WACrB,IAAI,CAACkB,OAAO,EACZ,IAAI,CAACA,OAAO,CAAClB,KAAK,GAElB,IAAI,CAACgB,UAAU,EACf,IAAI,CAACA,UAAU,CAAChB,KAAK,GAErB,IAAI,CAACiB,SAAS,EACd,IAAI,CAACA,SAAS,CAACjB,KAAK,EAE5B,EACAE,EAAW3E,SAAS,CAAC8F,UAAU,CAAG,SAAUpE,CAAI,EACxC,IAAI,CAACiE,OAAO,EACZ,IAAI,CAACA,OAAO,CAACjB,IAAI,CAAChD,GAElB,IAAI,CAAC+D,UAAU,EACf,IAAI,CAACA,UAAU,CAACf,IAAI,CAAChD,GAErB,IAAI,CAACgE,SAAS,EACd,IAAI,CAACA,SAAS,CAAChB,IAAI,CAAChD,EAE5B,EACAiD,EAAW3E,SAAS,CAAC+F,aAAa,CAAG,SAAUrE,CAAI,CAAE6B,CAAS,CAAEyC,CAAa,EACnD,KAAK,IAAvBA,GAA4BA,CAAAA,EAAgB,CAAA,EAChD,IAAIC,EAAO,IAAI,CAAChD,OAAO,CACnBiD,EAAIlF,EAAME,EAAK+E,EAAKE,cAAc,CAClC5C,GACK0C,CAAAA,EAAKG,cAAc,EAAI,CAAA,EAAI,EAAG,MACnCC,EAAY,IAAI,CAACC,YAAY,GAC7B/B,EAAeyB,EAAgB,IAC/BK,IACAA,EAAUxE,qBAAqB,CAACH,GAC5BsE,GAAiBtE,EAAQ,CAAA,IAAI,CAAC6E,cAAc,EAAI,EAAC,EAAK,KACtDF,EAAUvE,eAAe,CAACoE,EAAGxE,EAAM6C,GACnC8B,EAAUpE,cAAc,CAACiE,EAAGxE,EAAO6C,IAGnC8B,EAAUpE,cAAc,CAACiE,EAAGxE,IAGpC,IAAI,CAAC8E,yBAAyB,CAACN,EAAGxE,EAAMsE,GACxC,IAAI,CAACS,4BAA4B,CAACP,EAAGxE,EAAMsE,GAC3C,IAAI,CAACO,cAAc,CAAG7E,CAC1B,EAGAiD,EAAW3E,SAAS,CAAC0G,WAAW,CAAG,WAC/B,OAAO,IAAI,CAACf,OAAO,EAAI,IAAI,CAACA,OAAO,CAACrC,MAAM,EACtC,IAAI,CAACmC,UAAU,EAAI,IAAI,CAACA,UAAU,CAACnC,MAAM,EACzC,IAAI,CAACoC,SAAS,EAAI,IAAI,CAACA,SAAS,CAAClB,YAAY,EACrD,EAEAG,EAAW3E,SAAS,CAAC2G,WAAW,CAAG,WAC/B,OAAO,IAAI,CAACnB,MAAM,EAAI,IAAI,CAACA,MAAM,CAAC5D,IAAI,AAC1C,EAGA+C,EAAW3E,SAAS,CAAC4G,iBAAiB,CAAG,SAAUxE,CAAI,CAAEV,CAAI,EACpD,IAAI,CAACD,QAAQ,EAKlBS,EAFU,AAACE,CAAAA,AAAS,WAATA,EAAoB,IAAI,CAACa,OAAO,CAAC4D,cAAc,CAClD,IAAI,CAAC5D,OAAO,CAAC6D,eAAe,AAAD,GAAM,EAAE,CACjB1E,EAAMV,EAAM,IAAI,CAACD,QAAQ,CAAE,IAAI,CAACwB,OAAO,CAAC8D,MAAM,CAC5E,EAEApC,EAAW3E,SAAS,CAACgH,eAAe,CAAG,WAC/B,IAAI,CAACvF,QAAQ,EACb,IAAI,CAACA,QAAQ,CAACG,IAAI,CACbC,qBAAqB,CAAC,IAAI,CAAC+C,YAAY,CAACqC,WAAW,EAE5D,IAAIZ,EAAY,IAAI,CAACC,YAAY,GAC7BD,GACAA,EAAUxE,qBAAqB,CAAC,GAEhC,IAAI,CAACwD,WAAW,EAChB,IAAI,CAACA,WAAW,CAAC9B,SAAS,CAAC1B,qBAAqB,CAAC,GAEjD,IAAI,CAACyD,YAAY,EACjB,IAAI,CAACA,YAAY,CAAC/B,SAAS,CAAC1B,qBAAqB,CAAC,GAElD,IAAI,CAAC0D,eAAe,EACpB,IAAI,CAACA,eAAe,CAAC3D,IAAI,CAACC,qBAAqB,CAAC,EAExD,EAEA8C,EAAW3E,SAAS,CAACwG,yBAAyB,CAAG,SAAUjD,CAAS,CAAE7B,CAAI,CAAEsE,CAAa,EACrF,GAAI,IAAI,CAACT,eAAe,CAAE,CACtB,IAAI2B,EAAI/F,EAA6B,IAAI,CAAC8B,OAAO,CAACkE,6BAA6B,EAAI,EAC/E5D,GACA6D,EAAWpB,EAAgBA,EAAgB,IACvCjE,EAAWC,YAAY,CAC/B,IAAI,CAACuD,eAAe,CAAC3D,IAAI,CAACC,qBAAqB,CAACH,GAChD,IAAI,CAAC6D,eAAe,CAAC3D,IAAI,CAACE,eAAe,CAACoF,EAAGxF,EAAM0F,EAAW,GAC9D,IAAI,CAAC7B,eAAe,CAAC3D,IAAI,CAACK,cAAc,CAACiF,EAAGxF,EAAO0F,EACvD,CACJ,EAEAzC,EAAW3E,SAAS,CAACyG,4BAA4B,CAAG,SAAUlD,CAAS,CAAE7B,CAAI,CAAEsE,CAAa,EACxF,IAAIC,EAAO,IAAI,CAAChD,OAAO,CACnBmE,EAAWpB,EAAgBA,EAAgB,IACvCjE,EAAWC,YAAY,CAC3BqF,EAAuB,SAAUC,CAAU,CAC3CC,CAAa,EACT,IAAInG,EAAaD,EAA6BoG,EAAcC,gCAAgC,EAAI,EACpGjE,GACA2C,EAAIlF,EAAM,AAACuG,CAAAA,EAAchE,SAAS,EAAI,GAAG,EAAKnC,EAAY,EAAG,MAC7DkG,EAAW/D,SAAS,CAAC1B,qBAAqB,CAACH,GAC3C4F,EAAW/D,SAAS,CAACzB,eAAe,CAACoE,EAAGxE,EAAM0F,EAAW,GACzDE,EAAW/D,SAAS,CAACtB,cAAc,CAACiE,EAAGxE,EAAO0F,EAClD,CACI,CAAA,IAAI,CAAC/B,WAAW,EAAIY,EAAKwB,OAAO,EAChCJ,EAAqB,IAAI,CAAChC,WAAW,CAAEY,EAAKwB,OAAO,EAEnD,IAAI,CAACnC,YAAY,EAAIW,EAAKyB,QAAQ,EAClCL,EAAqB,IAAI,CAAC/B,YAAY,CAAEW,EAAKyB,QAAQ,CAE7D,EACA/C,EAAW3E,SAAS,CAACkF,UAAU,CAAG,WAC9B,IAAIe,EAAO,IAAI,CAAChD,OAAO,CACHhC,CAAAA,EAAQgF,EAAKc,MAAM,GAC/Bd,EAAKY,cAAc,EAAIZ,EAAKY,cAAc,CAAChG,MAAM,EACjDoF,EAAKa,eAAe,EAAIb,EAAKa,eAAe,CAACjG,MAAM,AAAD,GAEtD,CAAA,IAAI,CAACY,QAAQ,CAAG,IAAIkC,SAAS,IAAI,CAACiB,YAAY,CAAE,CAC5ChD,KAAMV,EAAK+E,EAAKc,MAAM,CAAE,EAC5B,EAAC,EAGL,IAAI,CAACvB,MAAM,CAAG,IAAI7B,SAAS,IAAI,CAACiB,YAAY,CAChD,EAEAD,EAAW3E,SAAS,CAACiF,iBAAiB,CAAG,WACrC,IAAIgB,EAAO,IAAI,CAAChD,OAAO,CACnB0E,EAAM,IAAI,CAAC/C,YAAY,CACvBrB,EAAY,AAAC0C,CAAAA,EAAKE,cAAc,EAAI,CAAA,EAC/BF,CAAAA,EAAKG,cAAc,EAAI,CAAA,EAChC,GAAIH,AAAc,eAAdA,EAAK7D,IAAI,CAAmB,CAM5B,IAAK,IALDwF,EAAQD,AAAiB,EAAjBA,EAAIE,UAAU,CACtBC,EAASH,EAAII,YAAY,CAAC,EAC1BH,EACAD,EAAIE,UAAU,EACdG,EAAOF,EAAOG,cAAc,CAAC,GACxBtH,EAAI,EAAGA,EAAIiH,EAAO,EAAEjH,EAEzBqH,CAAI,CAACrH,EAAE,CAAGW,AAAgB,IAAhBA,KAAK4G,MAAM,GAAW,GAPpC,IASIC,EAAK,IAAI,CAAC1C,UAAU,CAAGkC,EAAIS,kBAAkB,EACjDD,CAAAA,EAAGL,MAAM,CAAGA,EACZK,EAAGE,IAAI,CAAG,CAAA,CACd,KACSpC,AAAc,UAAdA,EAAK7D,IAAI,CACd,IAAI,CAACsD,SAAS,CAAG,IAAI3C,EAAa4E,EAAK,CACnCrE,OAAQ2C,EAAK3C,MAAM,CACnBJ,WAAY+C,EAAK/C,UAAU,CAC3BK,UAAWA,CACf,GAGA,IAAI,CAACoC,OAAO,CAAG,IAAItC,eAAesE,EAAK,CACnCvF,KAAM6D,EAAK7D,IAAI,EAAI,OACnBkB,OAAQ2C,EAAK3C,MAAM,CACnBC,UAAWA,CACf,EAER,EAEAoB,EAAW3E,SAAS,CAACmF,aAAa,CAAG,WACjC,IAAIc,EAAO,IAAI,CAAChD,OAAO,AACnBgD,CAAAA,EAAKwB,OAAO,EAAIxB,EAAKwB,OAAO,CAAClE,SAAS,EACtC,CAAA,IAAI,CAAC8B,WAAW,CAAG,IAAIiD,iBAAiB,IAAI,CAAC1D,YAAY,CAAE,CACvDxC,KAAM,UACNmG,EAAGtC,EAAKwB,OAAO,CAACc,CAAC,EAAI,EACrBhF,UAAW0C,EAAKwB,OAAO,CAAClE,SAAS,AACrC,EAAC,EAED0C,EAAKyB,QAAQ,EAAIzB,EAAKyB,QAAQ,CAACnE,SAAS,EACxC,CAAA,IAAI,CAAC+B,YAAY,CAAG,IAAIgD,iBAAiB,IAAI,CAAC1D,YAAY,CAAE,CACxDxC,KAAM,WACNmG,EAAGtC,EAAKyB,QAAQ,CAACa,CAAC,EAAI,EACtBhF,UAAW0C,EAAKyB,QAAQ,CAACnE,SAAS,AACtC,EAAC,CAET,EAEAoB,EAAW3E,SAAS,CAACoF,iBAAiB,CAAG,WACrC,IAAIa,EAAO,IAAI,CAAChD,OAAO,AACnBgD,CAAAA,EAAKkB,6BAA6B,EAClClB,AAAuC,IAAvCA,EAAKkB,6BAA6B,EAClC,CAAA,IAAI,CAAC5B,eAAe,CAAG,IAAI5B,SAAS,IAAI,CAACiB,YAAY,CAAE,CACnDhD,KAAM,CACV,EAAC,CAET,EAEA+C,EAAW3E,SAAS,CAACsG,YAAY,CAAG,WAChC,OAAO,IAAI,CAACX,OAAO,CAAG,IAAI,CAACA,OAAO,CAACpC,SAAS,CACxC,IAAI,CAACmC,SAAS,EAAI,IAAI,CAACA,SAAS,CAACvB,kBAAkB,EAC3D,EACOQ,CACX,IAqBI5C,EAA4B,WAC5B,SAASA,EAAW6C,CAAY,CAAE3B,CAAO,EACrC,IAAIuF,EAAQ,IAAI,AAChB,CAAA,IAAI,CAAC5D,YAAY,CAAGA,EACpB,IAAI,CAAC3B,OAAO,CAAGA,EACf,IAAI,CAACwF,OAAO,CAAG,EAAE,CACjB,IAAI,CAACC,cAAc,CAAGzF,EAAQyF,cAAc,EAAI,EAChD,IAAI,CAACC,UAAU,CAAG,IAAIhF,SAASiB,EAAc,CAAEhD,KAAM,CAAE,GACvD,IAAI,CAACgH,aAAa,CAAC,IAAI,CAACD,UAAU,EAClC,IAAIE,EAAY,IAAI,CAACJ,OAAO,CAAC5H,MAAM,CAC3B,IAAI,CAAC4H,OAAO,CAAC,EAAE,CAAG,IAAI,CAACE,UAAU,AACzC,CAAA,IAAI,CAACG,WAAW,CAAG,AAAC,CAAA,IAAI,CAAC7F,OAAO,CAAC6F,WAAW,EAAI,EAAE,AAAD,EAAGC,GAAG,CAAC,SAAUC,CAAO,EAAI,OAAO,IAAIrE,EAAWC,EAAcoE,EAAS/H,EAAQ+H,EAAQlE,YAAY,GAAK7D,EAAQ+H,EAAQhE,YAAY,EACnL,KAAK,EAAI6D,EAAY,GAGzB,IAAI,CAACC,WAAW,CAACtG,OAAO,CAAC,SAAUyG,CAAG,EAClC,IAAIC,EAAgB,SAAUC,CAAU,CACpCC,CAAS,EACL,GAAIA,EAAW,CACX,IAAIC,EAASD,CAAS,CAACD,EAAW,GAClCE,GACAJ,EAAIhF,OAAO,CAACoF,EAEpB,CACJ,EACIpI,EAAQgI,EAAIpE,cAAc,GAC1BqE,EAAc,cAAeV,EAAMM,WAAW,CAACG,EAAIpE,cAAc,CAAC,EAElE5D,EAAQgI,EAAIlE,cAAc,GAC1BmE,EAAc,cAAeV,EAAMM,WAAW,CAACG,EAAIlE,cAAc,CAAC,CAE1E,EACJ,CAoHA,OA/GAhD,EAAW/B,SAAS,CAACsJ,aAAa,CAAG,WACjC,IAAI,CAACX,UAAU,CAAC/G,IAAI,CAACoC,KAAK,CAAG,EAC7B,IAAI,CAAC8E,WAAW,CAACtG,OAAO,CAAC,SAAU/C,CAAC,EAAI,OAAOA,EAAEgF,KAAK,EAAI,EAC9D,EAKA1C,EAAW/B,SAAS,CAAC0E,IAAI,CAAG,WACxB,IAAI6E,EAAU,IAAI,CAAC3E,YAAY,CAACqC,WAAW,CACvCuC,EAAUD,EAAUxH,EAAWC,YAAY,CAC/CR,EAAoB,IAAI,CAACmH,UAAU,CAAEY,EAAS,GAC9C,IAAI,CAACT,WAAW,CAACtG,OAAO,CAAC,SAAU/C,CAAC,EAAI,OAAOA,EAAEqG,UAAU,CAAC0D,EAAU,GACtE,IAAI,CAACb,UAAU,CAACc,UAAU,EAC9B,EAQA1H,EAAW/B,SAAS,CAAC0J,aAAa,CAAG,SAAUhI,CAAI,EAC/C,GAAI,CAACA,GAAQ,IAAI,CAACiH,UAAU,CAAC/G,IAAI,CAACoC,KAAK,CAAG,IAAM,CAC5C,IAAI,CAAC2E,UAAU,CAAC/G,IAAI,CAACoC,KAAK,CAAG,EAC7B,MACJ,CACA,IAAI,CAAC2F,aAAa,CAAC,AAACjI,CAAAA,GAAQ,CAAA,EAAK,IAAI,CAACkD,YAAY,CAACqC,WAAW,CAClE,EAKAlF,EAAW/B,SAAS,CAAC4J,IAAI,CAAG,WACxB,IAAI,CAAC5C,eAAe,GACpBxF,EAAoB,IAAI,CAACmH,UAAU,CAAE,IAAI,CAAC/D,YAAY,CAACqC,WAAW,CAAE,EACxE,EAUAlF,EAAW/B,SAAS,CAAC6J,cAAc,CAAG,SAAUnI,CAAI,CAAE6B,CAAS,CAAEuG,CAAY,EACzE,IAAIrJ,EAAI,AAACiB,CAAAA,GAAQ,CAAA,EAAK,IAAI,CAACkD,YAAY,CAACqC,WAAW,CAC/ChB,EAAO,IAAI,CAAChD,OAAO,CACvB,IAAI,CAAC6F,WAAW,CAACtG,OAAO,CAAC,SAAU/C,CAAC,EAChCA,EAAEsG,aAAa,CAACtF,EAAG8C,EAAW0C,EAAK8D,iBAAiB,EACpDtK,EAAEmH,iBAAiB,CAAC,SAAUnG,EAClC,GACAyB,EAAqB+D,EAAK+D,oBAAoB,EAAI,EAAE,CAAE,SAAUvJ,EAAG,IAAI,CAACkI,UAAU,CAAE1C,EAAKgE,YAAY,EACjGH,GACA,IAAI,CAACH,aAAa,CAAClJ,EAAIqJ,EAAe,IAE9C,EAKA/H,EAAW/B,SAAS,CAACgH,eAAe,CAAG,WACnC,IAAI,CAAC2B,UAAU,CAAC/G,IAAI,CAACC,qBAAqB,CAAC,IAAI,CAAC+C,YAAY,CAACqC,WAAW,EACxE,IAAI,CAAC6B,WAAW,CAACtG,OAAO,CAAC,SAAU/C,CAAC,EAAI,OAAOA,EAAEuH,eAAe,EAAI,EACxE,EAOAjF,EAAW/B,SAAS,CAACiE,OAAO,CAAG,SAAUiG,CAAe,EACpD,OAAO,IAAI,CAACvB,UAAU,CAAC1E,OAAO,CAACiG,EACnC,EAKAnI,EAAW/B,SAAS,CAAC4I,aAAa,CAAG,SAAUD,CAAU,EACrD,IAAIH,EAAQ,IAAI,AAChB,CAAA,IAAI,CAACC,OAAO,CAAG,AAAC,CAAA,IAAI,CAACxF,OAAO,CAACkH,EAAE,EAAI,EAAE,AAAD,EAAGpB,GAAG,CAAC,SAAUqB,CAAK,EACtD,OAAO,IAAI9B,iBAAiBE,EAAM5D,YAAY,CAAErE,EAAS,CAAE6B,KAAM,SAAU,EAAGgI,GAClF,GAEA,IAAI,CAAC3B,OAAO,CAAC4B,WAAW,CAAC,SAAUC,CAAK,CAAEC,CAAI,EAE1C,OADAA,EAAKtG,OAAO,CAACqG,GACNC,CACX,EAAG5B,EACP,EAKA5G,EAAW/B,SAAS,CAAC2J,aAAa,CAAG,SAAUjI,CAAI,EAC/C,IAAI8I,EAAqB,EACzB,IAAI,CAAC1B,WAAW,CAACtG,OAAO,CAAC,SAAU/C,CAAC,EAChC,IAAIgL,EAAMhL,EAAEwD,OAAO,CAAC6D,eAAe,CAC/B2D,GAAOA,EAAI5J,MAAM,GACjB2J,EAAqBlJ,KAAKwB,GAAG,CAAC0H,EAAoBC,CAAG,CAACA,EAAI5J,MAAM,CAAG,EAAE,CAACJ,CAAC,EACvEhB,EAAEmH,iBAAiB,CAAC,UAAWlF,GAEvC,GACA,IAAIgJ,EAAY,IAAI,CAACzH,OAAO,CAAC0H,qBAAqB,EAAI,EAAE,AACpDD,CAAAA,EAAU7J,MAAM,GAChBqB,EAAqBwI,EAAW,UAAWhJ,EAAM,IAAI,CAACiH,UAAU,CAAE,IAAI,CAAC1F,OAAO,CAACgH,YAAY,EAC3FO,EAAqBlJ,KAAKwB,GAAG,CAAC0H,EAAoBE,CAAS,CAACA,EAAU7J,MAAM,CAAG,EAAE,CAACJ,CAAC,GAEvFe,EAAoB,IAAI,CAACmH,UAAU,CAAEjH,EAAO8I,EAAqB,IAAM,EAC3E,EACAzI,EAAWC,YAAY,CAAG,KACnBD,CACX,IA+LI6I,EAAoB,CAEpBC,MAAO,CACHZ,aAAc,IACdD,qBAAsB,CAClB,CAAEvJ,EAAG,EAAGkB,IAAK,GAAK,EAClB,CAAElB,EAAG,GAAIkB,IAAK,GAAK,EACnB,CAAElB,EAAG,GAAIkB,IAAK,GAAK,EACnB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,CAAE,EACpB,CACDwI,GAAI,CACA,CAAE5G,UAAW,IAAKgF,EAAG,GAAK3G,KAAM,CAAE,EAClC,CAAE2B,UAAW,IAAK3B,KAAM,CAAE,EAC1B,CAAE2B,UAAW,KAAM3B,KAAM,CAAE,EAC3B,CAAE2B,UAAW,KAAMgF,EAAG,GAAK3G,KAAM,CAAE,EACnC,CAAE2B,UAAW,KAAMgF,EAAG,GAAK3G,KAAM,CAAE,EACnC,CAAE2B,UAAW,KAAMgF,EAAG,GAAK3G,KAAM,EAAG,EACpC,CAAE2B,UAAW,IAAM3B,KAAM,GAAI,EAC7B,CAAE2B,UAAW,IAAOgF,EAAG,GAAK3G,KAAM,GAAI,EACzC,CACDkH,YAAa,CAAC,CACN1G,KAAM,QACN2E,OAAQ,GACR7D,WAAY,IACZiE,8BAA+B,GAC/BM,QAAS,CACLlE,UAAW,IACXiE,iCAAkC,IAClCe,EAAG,EACP,EACAb,SAAU,CAAEnE,UAAW,GAAI,EAC3BsD,eAAgB,CAAC,CAAEpG,EAAG,EAAGkB,IAAK,CAAE,EAAE,CAClCmF,gBAAiB,CACb,CAAErG,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,CAAE,EACpB,AACL,EAAG,CACCS,KAAM,aACN2E,OAAQ,GACRU,QAAS,CAAElE,UAAW,GAAI,EAC1BmE,SAAU,CAAEnE,UAAW,GAAI,EAC3BsD,eAAgB,CACZ,CAAEpG,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,GAAIkB,IAAK,CAAE,EACnB,AACL,EAAE,AACV,EAEAmJ,QAAS,CACLb,aAAc,GACdvB,eAAgB,GAChBsB,qBAAsB,CAClB,CAAEvJ,EAAG,EAAGkB,IAAK,GAAK,EAClB,CAAElB,EAAG,EAAGkB,IAAK,GAAK,EAClB,CAAElB,EAAG,GAAIkB,IAAK,EAAI,EAClB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,CAAE,EACpB,CACDwI,GAAI,CACA,CAAE5G,UAAW,IAAK3B,KAAM,EAAG,EAC3B,CAAE2B,UAAW,KAAMgF,EAAG,EAAG3G,KAAM,CAAE,EACjC,CAAE2B,UAAW,KAAM3B,KAAM,GAAI,EAC7B,CAAE2B,UAAW,KAAM3B,KAAM,EAAG,EAC5B,CAAE2B,UAAW,KAAM3B,KAAM,EAAG,EAC5B,CAAE2B,UAAW,KAAMgF,EAAG,EAAG3G,KAAM,EAAG,EACrC,CACDkH,YAAa,CAAC,CACN1G,KAAM,WACN2E,OAAQ,GACRI,8BAA+B,GAC/BO,SAAU,CAAEnE,UAAW,GAAI,EAC3BkE,QAAS,CAAElE,UAAW,GAAK,EAC3BuD,gBAAiB,CACb,CAAErG,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,CAAE,EACpB,AACL,EAAE,AACV,EAEAoJ,MAAO,CACHd,aAAc,IACdvB,eAAgB,GAChBqB,kBAAmB,GACnBC,qBAAsB,CAClB,CAAEvJ,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,GAAIkB,IAAK,CAAE,EAChB,CAAElB,EAAG,GAAIkB,IAAK,GAAK,EACnB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACvB,CACDgJ,sBAAuB,CACnB,CAAElK,EAAG,EAAGkB,IAAK,GAAK,EAClB,CAAElB,EAAG,GAAIkB,IAAK,GAAK,EACnB,CAAElB,EAAG,IAAKkB,IAAK,CAAE,EACpB,CACDwI,GAAI,CACA,CAAE5G,UAAW,IAAKgF,EAAG,GAAK3G,KAAM,GAAI,EACpC,CAAE2B,UAAW,IAAK3B,KAAM,CAAE,EAC1B,CAAE2B,UAAW,KAAM3B,KAAM,EAAG,EAC5B,CAAE2B,UAAW,KAAM3B,KAAM,GAAI,EAC7B,CAAE2B,UAAW,IAAM3B,KAAM,CAAE,EAC3B,CAAE2B,UAAW,KAAM3B,KAAM,EAAG,EAC5B,CAAE2B,UAAW,IAAM3B,KAAM,EAAG,EAC5B,CAAE2B,UAAW,MAAO3B,KAAM,CAAE,EAC/B,CACDkH,YAAa,CAAC,CACN1G,KAAM,WACN2E,OAAQ,EACRI,8BAA+B,GAC/BM,QAAS,CACLlE,UAAW,GACXiE,iCAAkC,GACtC,EACAE,SAAU,CACNnE,UAAW,GACf,CACJ,EAAG,CACCnB,KAAM,OACN+D,eAAgB,EAChBY,OAAQ,GACR/B,aAAc,EACd6B,eAAgB,CACZ,CAAEpG,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,GAAIkB,IAAK,CAAE,EAChB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACvB,AACL,EAAG,CACCS,KAAM,aACN2E,OAAQ,IACRU,QAAS,CACLlE,UAAW,IACXgF,EAAG,CACP,EACAb,SAAU,CACNnE,UAAW,IACXgF,EAAG,CACP,EACAvD,aAAc,EACd6B,eAAgB,CACZ,CAAEpG,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,GAAIkB,IAAK,CAAE,EAChB,CAAElB,EAAG,GAAIkB,IAAK,EAAI,EACrB,AACL,EAAE,AACV,EAEAqJ,KAAM,CACFf,aAAc,EACdvB,eAAgB,GAChBsB,qBAAsB,CAClB,CAAEvJ,EAAG,EAAGkB,IAAK,GAAK,EAClB,CAAElB,EAAG,GAAIkB,IAAK,EAAI,EAClB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,CAAE,EACjB,CAAElB,EAAG,IAAKkB,IAAK,CAAE,EACpB,CACDwI,GAAI,CACA,CAAE5G,UAAW,IAAK3B,KAAM,EAAG,EAC3B,CAAE2B,UAAW,IAAK3B,KAAM,EAAG,EAC3B,CAAE2B,UAAW,IAAKgF,EAAG,GAAK3G,KAAM,GAAI,EACpC,CAAE2B,UAAW,KAAM3B,KAAM,CAAE,EAC3B,CAAE2B,UAAW,KAAM3B,KAAM,EAAG,EAC5B,CAAE2B,UAAW,KAAM3B,KAAM,GAAI,EAC7B,CAAE2B,UAAW,KAAM3B,KAAM,GAAI,EAC7B,CAAE2B,UAAW,IAAO3B,KAAM,CAAE,EAC/B,CACDkH,YAAa,CAAC,CACN1G,KAAM,WACN2E,OAAQ,IACRI,8BAA+B,GAC/BM,QAAS,CAAElE,UAAW,GAAK,EAC3BmE,SAAU,CAAEnE,UAAW,GAAI,CAC/B,EAAG,CACCnB,KAAM,WACN2E,OAAQ,GACRU,QAAS,CAAElE,UAAW,GAAK,EAC3BmE,SAAU,CAAEnE,UAAW,IAAKgF,EAAG,CAAE,EACjCzB,gBAAiB,CACb,CAAErG,EAAG,EAAGkB,IAAK,GAAK,EAClB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,CAAE,EACpB,AACL,EAAE,AACV,EAEAsJ,WAAY,CACRhB,aAAc,EACdvB,eAAgB,GAChBsB,qBAAsB,CAClB,CAAEvJ,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,GAAIkB,IAAK,GAAK,EACnB,CAAElB,EAAG,GAAIkB,IAAK,GAAK,EACnB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,CAAE,EACpB,CACDwI,GAAI,CACA,CAAE5G,UAAW,IAAKgF,EAAG,GAAK3G,KAAM,GAAI,EACpC,CAAE2B,UAAW,IAAK3B,KAAM,EAAG,EAC3B,CAAE2B,UAAW,KAAMgF,EAAG,GAAK3G,KAAM,CAAE,EACnC,CAAE2B,UAAW,KAAMgF,EAAG,GAAK3G,KAAM,CAAE,EACnC,CAAE2B,UAAW,KAAM3B,KAAM,CAAE,EAC3B,CAAE2B,UAAW,MAAO3B,KAAM,CAAE,EAC/B,CACDkH,YAAa,CAAC,CACN1G,KAAM,OACN2E,OAAQ,IACRI,8BAA+B,KAC/BN,eAAgB,CAAC,CAAEpG,EAAG,EAAGkB,IAAK,CAAE,EAAE,CAClCmF,gBAAiB,CACb,CAAErG,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,CAAE,EACpB,AACL,EAAG,CACCS,KAAM,aACN2E,OAAQ,IACRI,8BAA+B,KAC/BM,QAAS,CACLlE,UAAW,GACf,EACAmE,SAAU,CACNnE,UAAW,GACf,EACAsD,eAAgB,CACZ,CAAEpG,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,EAAGkB,IAAK,CAAE,EAClB,AACL,EAAG,CACCS,KAAM,OACNgE,eAAgB,EAChBW,OAAQ,IACRI,8BAA+B,IACnC,EAAG,CACC/E,KAAM,OACN+D,eAAgB,EAChBY,OAAQ,EACRjC,aAAc,EACdgC,gBAAiB,CACb,CAAErG,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,CAAE,EACpB,AACL,EAAG,CACCS,KAAM,OACN+D,eAAgB,EAChBY,OAAQ,EACRjC,aAAc,CAClB,EAAG,CACC1C,KAAM,OACNgE,eAAgB,EAChBW,OAAQ,KACRI,8BAA+B,KAC/BL,gBAAiB,CACb,CAAErG,EAAG,EAAGkB,IAAK,GAAK,EAClB,CAAElB,EAAG,IAAKkB,IAAK,CAAE,EACpB,AACL,EAAE,AACV,EAEAuJ,UAAW,CACPjB,aAAc,EACdvB,eAAgB,GAChBqB,kBAAmB,GACnBC,qBAAsB,CAClB,CAAEvJ,EAAG,EAAGkB,IAAK,GAAK,EAClB,CAAElB,EAAG,GAAIkB,IAAK,CAAE,EAChB,CAAElB,EAAG,GAAIkB,IAAK,GAAK,EACnB,CAAElB,EAAG,IAAKkB,IAAK,EAAI,EACnB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACvB,CACDgJ,sBAAuB,CACnB,CAAElK,EAAG,EAAGkB,IAAK,GAAK,EAClB,CAAElB,EAAG,GAAIkB,IAAK,GAAK,EACnB,CAAElB,EAAG,IAAKkB,IAAK,CAAE,EACpB,CACDwI,GAAI,CACA,CAAE5G,UAAW,IAAK3B,KAAM,EAAG,EAC3B,CAAE2B,UAAW,IAAK3B,KAAM,CAAE,EAC1B,CAAE2B,UAAW,IAAK3B,KAAM,GAAI,EAC5B,CAAE2B,UAAW,KAAM3B,KAAM,EAAG,EAC5B,CAAE2B,UAAW,KAAM3B,KAAM,EAAG,EAC5B,CAAE2B,UAAW,KAAM3B,KAAM,EAAG,EAC5B,CAAE2B,UAAW,MAAO3B,KAAM,CAAE,EAC/B,CACDkH,YAAa,CAAC,CACN1G,KAAM,WACN2E,OAAQ,IACRI,8BAA+B,IAC/BM,QAAS,CACLlE,UAAW,GACXiE,iCAAkC,GACtC,EACAE,SAAU,CACNnE,UAAW,GACf,CACJ,EAAG,CACCnB,KAAM,aACN+D,eAAgB,EAChBY,OAAQ,GACRW,SAAU,CACNnE,UAAW,GACf,EACAyB,aAAc,EACd6B,eAAgB,CACZ,CAAEpG,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,GAAIkB,IAAK,CAAE,EAChB,CAAElB,EAAG,GAAIkB,IAAK,GAAK,EACnB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACvB,AACL,EAAG,CACCS,KAAM,OACN+D,eAAgB,EAChBY,OAAQ,EACRjC,aAAc,EACd+B,eAAgB,CACZ,CAAEpG,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,GAAIkB,IAAK,GAAK,EACnB,CAAElB,EAAG,GAAIkB,IAAK,CAAE,EAChB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACvB,AACL,EAAG,CACCS,KAAM,OACN+D,eAAgB,EAChBY,OAAQ,EACRjC,aAAc,EACd+B,eAAgB,CACZ,CAAEpG,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,GAAIkB,IAAK,GAAK,EACnB,CAAElB,EAAG,GAAIkB,IAAK,CAAE,EAChB,CAAElB,EAAG,GAAIkB,IAAK,CAAE,EAChB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,CAAE,EACpB,AACL,EAAE,AACV,EAEAwJ,QAAS,CACLlB,aAAc,GACdvB,eAAgB,GAChBqB,kBAAmB,GACnBC,qBAAsB,CAClB,CAAEvJ,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,GAAIkB,IAAK,CAAE,EAChB,CAAElB,EAAG,GAAIkB,IAAK,GAAK,EACnB,CAAElB,EAAG,GAAIkB,IAAK,CAAE,EAChB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACvB,CACDgJ,sBAAuB,CACnB,CAAElK,EAAG,EAAGkB,IAAK,GAAK,EAClB,CAAElB,EAAG,GAAIkB,IAAK,GAAK,EACnB,CAAElB,EAAG,GAAIkB,IAAK,GAAK,EACnB,CAAElB,EAAG,GAAIkB,IAAK,CAAE,EACnB,CACDwI,GAAI,CACA,CAAE5G,UAAW,IAAKgF,EAAG,GAAK3G,KAAM,EAAG,EACnC,CAAE2B,UAAW,IAAKgF,EAAG,GAAK3G,KAAM,CAAE,EAClC,CAAE2B,UAAW,KAAMgF,EAAG,GAAK3G,KAAM,EAAG,EACpC,CAAE2B,UAAW,KAAMgF,EAAG,EAAG3G,KAAM,CAAE,EACjC,CAAE2B,UAAW,KAAMgF,EAAG,GAAK3G,KAAM,EAAG,EACpC,CAAE2B,UAAW,KAAM3B,KAAM,EAAG,EAC5B,CAAE2B,UAAW,KAAM3B,KAAM,GAAI,EAC7B,CAAE2B,UAAW,MAAOgF,EAAG,GAAK3G,KAAM,GAAI,EACzC,CACDkH,YAAa,CAAC,CACN1G,KAAM,WACN2E,OAAQ,IACR7D,WAAY,GACZiE,8BAA+B,GAC/BM,QAAS,CAAElE,UAAW,KAAMgF,EAAG,CAAE,CACrC,EAAG,CACCnG,KAAM,OACN+D,eAAgB,EAChBY,OAAQ,GACR/B,aAAc,EACd6B,eAAgB,CACZ,CAAEpG,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACvB,AACL,EAAG,CACCS,KAAM,aACN2E,OAAQ,IACRW,SAAU,CAAEnE,UAAW,IAAMgF,EAAG,CAAE,EAClCvD,aAAc,EACd6B,eAAgB,CACZ,CAAEpG,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,GAAIkB,IAAK,GAAK,EACnB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACvB,AACL,EAAG,CACCS,KAAM,OACN+D,eAAgB,IAChBY,OAAQ,GACRjC,aAAc,EACd+B,eAAgB,CACZ,CAAEpG,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,GAAIkB,IAAK,CAAE,EAChB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACvB,AACL,EAAE,AACV,EAEAyJ,SAAU,CACNnB,aAAc,GACdvB,eAAgB,GAChBqB,kBAAmB,GACnBC,qBAAsB,CAClB,CAAEvJ,EAAG,EAAGkB,IAAK,EAAI,EACjB,CAAElB,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACvB,CACDwI,GAAI,CAAC,CAAE5G,UAAW,IAAK3B,KAAM,EAAG,EAAE,CAClCkH,YAAa,CAAC,CACN1G,KAAM,WACN2E,OAAQ,GACRI,8BAA+B,EACnC,EAAG,CACC/E,KAAM,WACN2E,OAAQ,GACRzD,OAAQ,GACR6D,8BAA+B,EACnC,EAAG,CACC/E,KAAM,WACN2E,OAAQ,GACRzD,OAAQ,IACR6D,8BAA+B,EACnC,EAAE,AACV,EAEAkE,OAAQ,CACJpB,aAAc,EACdF,kBAAmB,EACnBY,sBAAuB,CACnB,CAAElK,EAAG,EAAGkB,IAAK,GAAK,EAClB,CAAElB,EAAG,GAAIkB,IAAK,GAAK,EACnB,CAAElB,EAAG,IAAKkB,IAAK,CAAE,EACpB,CACDwI,GAAI,CACA,CAAE5G,UAAW,IAAKgF,EAAG,GAAK3G,KAAM,GAAI,EACpC,CAAE2B,UAAW,KAAM3B,KAAM,EAAG,EAC5B,CAAE2B,UAAW,KAAM3B,KAAM,GAAI,EAC7B,CAAE2B,UAAW,IAAM3B,KAAM,CAAE,EAC3B,CAAE2B,UAAW,KAAM3B,KAAM,EAAG,EAC5B,CAAE2B,UAAW,IAAM3B,KAAM,EAAG,EAC5B,CAAE2B,UAAW,MAAO3B,KAAM,CAAE,EAC/B,CACDkH,YAAa,CAAC,CACN1G,KAAM,WACN2E,OAAQ,EACRI,8BAA+B,IAC/BM,QAAS,CAAElE,UAAW,GAAIiE,iCAAkC,GAAI,EAChEE,SAAU,CAAEnE,UAAW,GAAI,CAC/B,EAAG,CACCnB,KAAM,aACN2E,OAAQ,IACRU,QAAS,CAAElE,UAAW,IAAMgF,EAAG,CAAE,EACjCb,SAAU,CAAEnE,UAAW,IAAMgF,EAAG,CAAE,EAClCvD,aAAc,EACd6B,eAAgB,CACZ,CAAEpG,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,GAAIkB,IAAK,CAAE,EAChB,CAAElB,EAAG,GAAIkB,IAAK,GAAK,EACtB,AACL,EAAE,AACV,EAEA2J,OAAQ,CACJrB,aAAc,GACdE,GAAI,CACA,CAAE5G,UAAW,IAAKgF,EAAG,GAAK3G,KAAM,CAAE,EAClC,CAAE2B,UAAW,IAAK3B,KAAM,CAAE,EAC1B,CAAE2B,UAAW,KAAM3B,KAAM,EAAG,EAC5B,CAAE2B,UAAW,KAAMgF,EAAG,GAAK3G,KAAM,CAAE,EACnC,CAAE2B,UAAW,KAAMgF,EAAG,GAAK3G,KAAM,CAAE,EACnC,CAAE2B,UAAW,KAAMgF,EAAG,GAAK3G,KAAM,EAAG,EACpC,CAAE2B,UAAW,IAAM3B,KAAM,GAAI,EAC7B,CAAE2B,UAAW,IAAOgF,EAAG,GAAK3G,KAAM,GAAI,EACzC,CACDkH,YAAa,CAAC,CACN1G,KAAM,QACN2E,OAAQ,GACR7D,WAAY,IACZiE,8BAA+B,GAC/BM,QAAS,CACLlE,UAAW,IACXiE,iCAAkC,IAClCe,EAAG,EACP,EACAb,SAAU,CAAEnE,UAAW,GAAI,CAC/B,EAAE,AACV,EAEAgI,MAAO,CACHtB,aAAc,EACdD,qBAAsB,CAClB,CAAEvJ,EAAG,EAAGkB,IAAK,GAAK,EAClB,CAAElB,EAAG,GAAIkB,IAAK,GAAK,EACnB,CAAElB,EAAG,GAAIkB,IAAK,GAAK,EACnB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,CAAE,EACpB,CACDwI,GAAI,CACA,CAAE5G,UAAW,IAAK3B,KAAM,EAAG,EAC3B,CAAE2B,UAAW,IAAKgF,EAAG,EAAG3G,KAAM,CAAE,EAChC,CAAE2B,UAAW,IAAK3B,KAAM,GAAI,EAC5B,CAAE2B,UAAW,KAAM3B,KAAM,EAAG,EAC5B,CAAE2B,UAAW,KAAM3B,KAAM,EAAG,EAC5B,CAAE2B,UAAW,KAAMgF,EAAG,GAAK3G,KAAM,EAAG,EACpC,CAAE2B,UAAW,KAAM3B,KAAM,CAAE,EAC3B,CAAE2B,UAAW,MAAO3B,KAAM,CAAE,EAC/B,CACDkH,YAAa,CAAC,CACN1G,KAAM,WACN2E,OAAQ,IACRI,8BAA+B,IAC/BM,QAAS,CAAElE,UAAW,GAAK,EAC3BmE,SAAU,CAAEnE,UAAW,GAAI,EAC3BuD,gBAAiB,CACb,CAAErG,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,CAAE,EACpB,AACL,EAAG,CACCS,KAAM,WACNgE,eAAgB,KAChBW,OAAQ,GACRI,8BAA+B,IAC/BM,QAAS,CAAElE,UAAW,GAAK,EAC3BmE,SAAU,CAAEnE,UAAW,GAAI,EAC3BuD,gBAAiB,CACb,CAAErG,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,IAAKkB,IAAK,CAAE,EACpB,AACL,EAAG,CACCS,KAAM,WACNgE,eAAgB,QAChBW,OAAQ,GACRI,8BAA+B,IAC/BO,SAAU,CAAEnE,UAAW,GAAI,EAC3BuD,gBAAiB,CACb,CAAErG,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,CAAE,EACpB,AACL,EAAG,CACCS,KAAM,OACN+D,eAAgB,GAChBY,OAAQ,EACRjC,aAAc,EACd+B,eAAgB,CACZ,CAAEpG,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACvB,AACL,EAAG,CACCS,KAAM,OACN+D,eAAgB,EAChBY,OAAQ,GACR/B,aAAc,EACd6B,eAAgB,CACZ,CAAEpG,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACvB,AACL,EAAE,AACV,EAEA6J,OAAQ,CACJvB,aAAc,GACdU,sBAAuB,CACnB,CAAElK,EAAG,EAAGkB,IAAK,GAAK,EAClB,CAAElB,EAAG,GAAIkB,IAAK,GAAK,EACnB,CAAElB,EAAG,IAAKkB,IAAK,CAAE,EACpB,CACDwI,GAAI,CACA,CAAE5G,UAAW,IAAKgF,EAAG,GAAK3G,KAAM,GAAI,EACpC,CAAE2B,UAAW,KAAM3B,KAAM,EAAG,EAC5B,CAAE2B,UAAW,KAAM3B,KAAM,GAAI,EAC7B,CAAE2B,UAAW,IAAM3B,KAAM,CAAE,EAC3B,CAAE2B,UAAW,KAAM3B,KAAM,EAAG,EAC5B,CAAE2B,UAAW,IAAM3B,KAAM,EAAG,EAC5B,CAAE2B,UAAW,MAAO3B,KAAM,CAAE,EAC/B,CACDkH,YAAa,CAAC,CACN1G,KAAM,WACN2E,OAAQ,GACRI,8BAA+B,GAC/BM,QAAS,CAAElE,UAAW,GAAIiE,iCAAkC,GAAI,EAChEE,SAAU,CAAEnE,UAAW,GAAI,CAC/B,EAAG,CACCnB,KAAM,aACN2E,OAAQ,IACRU,QAAS,CAAElE,UAAW,IAAMgF,EAAG,CAAE,EACjCb,SAAU,CAAEnE,UAAW,IAAMgF,EAAG,CAAE,EAClCvD,aAAc,EACd6B,eAAgB,CACZ,CAAEpG,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,GAAIkB,IAAK,CAAE,EAChB,CAAElB,EAAG,GAAIkB,IAAK,GAAK,EACtB,AACL,EAAG,CACCS,KAAM,OACNgE,eAAgB,KAChBW,OAAQ,GACRjC,aAAc,CAClB,EAAE,AACV,EAEA2G,KAAM,CACFxB,aAAc,EACdnB,YAAa,CAAC,CACN1G,KAAM,OACN+E,8BAA+B,GACnC,EAAE,AACV,EAEAuE,UAAW,CACPzB,aAAc,EACdF,kBAAmB,IACnBjB,YAAa,CAAC,CACN1G,KAAM,OACN+E,8BAA+B,GACnC,EAAE,AACV,EAEAwE,SAAU,CACN1B,aAAc,GACdnB,YAAa,CAAC,CACN1G,KAAM,WACN2E,OAAQ,EACRI,8BAA+B,GACnC,EAAE,AACV,EAEAyE,SAAU,CACN3B,aAAc,IACdvB,eAAgB,GAChBI,YAAa,CAAC,CACN1G,KAAM,WACN2E,OAAQ,GACRI,8BAA+B,GACnC,EAAE,AACV,EAEA0E,OAAQ,CACJ5B,aAAc,GACdvB,eAAgB,GAChBI,YAAa,CAAC,CACN1G,KAAM,SACN2E,OAAQ,GACRI,8BAA+B,GACnC,EAAE,AACV,EAEA2E,KAAM,CACF7B,aAAc,EACdvB,eAAgB,IAChBsB,qBAAsB,CAAC,CAAEvJ,EAAG,EAAGkB,IAAK,CAAE,EAAG,CAAElB,EAAG,GAAIkB,IAAK,CAAE,EAAE,CAC3DmH,YAAa,CAAC,CACN1G,KAAM,aACN2E,OAAQ,EACRU,QAAS,CAAElE,UAAW,GAAI,EAC1BmE,SAAU,CAAEnE,UAAW,GAAI,CAC/B,EAAE,AACV,EACAwI,OAAQ,CACJ9B,aAAc,GACdvB,eAAgB,IAChBsB,qBAAsB,CAAC,CAAEvJ,EAAG,EAAGkB,IAAK,CAAE,EAAG,CAAElB,EAAG,GAAIkB,IAAK,CAAE,EAAE,CAC3DmH,YAAa,CAAC,CACN1G,KAAM,aACN2E,OAAQ,EACRU,QAAS,CAAElE,UAAW,IAAK,EAC3BmE,SAAU,CAAEnE,UAAW,GAAK,CAChC,EAAE,AACV,EACAyI,KAAM,CACF/B,aAAc,EACdvB,eAAgB,IAChBsB,qBAAsB,CAAC,CAAEvJ,EAAG,EAAGkB,IAAK,CAAE,EAAG,CAAElB,EAAG,GAAIkB,IAAK,CAAE,EAAE,CAC3DwI,GAAI,CACA,CAAE5G,UAAW,IAAK3B,KAAM,EAAG,EAC3B,CAAE2B,UAAW,IAAK3B,KAAM,GAAI,EAC5B,CAAE2B,UAAW,IAAK3B,KAAM,CAAE,EAC1B,CAAE2B,UAAW,IAAMgF,EAAG,EAAG3G,KAAM,GAAI,EACnC,CAAE2B,UAAW,KAAM3B,KAAM,CAAE,EAC3B,CAAE2B,UAAW,KAAM3B,KAAM,GAAI,EAC7B,CAAE2B,UAAW,KAAM3B,KAAM,CAAE,EAC3B,CAAE2B,UAAW,MAAO3B,KAAM,GAAI,EACjC,CACDkH,YAAa,CAAC,CACN1G,KAAM,aACN2E,OAAQ,IACRU,QAAS,CAAElE,UAAW,GAAI,EAC1BmE,SAAU,CAAEnE,UAAW,IAAKgF,EAAG,CAAE,CACrC,EAAE,AACV,EACA0D,KAAM,CACFhC,aAAc,IACdD,qBAAsB,CAClB,CAAEvJ,EAAG,EAAGkB,IAAK,EAAI,EACjB,CAAElB,EAAG,GAAIkB,IAAK,CAAE,EAChB,CAAElB,EAAG,GAAIkB,IAAK,GAAK,EACnB,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,CAAE,EACpB,CACDwI,GAAI,CACA,CAAE5G,UAAW,GAAI3B,KAAM,CAAE,EACzB,CAAE2B,UAAW,IAAK3B,KAAM,GAAI,EAC5B,CAAE2B,UAAW,KAAM3B,KAAM,EAAG,EAC/B,CACDkH,YAAa,CAAC,CACN1G,KAAM,WACN+D,eAAgB,GAChBY,OAAQ,EACRU,QAAS,CAAElE,UAAW,GAAI,EAC1BsD,eAAgB,CACZ,CAAEpG,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,GAAIkB,IAAK,GAAK,EACtB,AACL,EAAG,CACCS,KAAM,aACN2E,OAAQ,GACRU,QAAS,CAAElE,UAAW,GAAI,EAC1BsD,eAAgB,CACZ,CAAEpG,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,GAAIkB,IAAK,CAAE,EACnB,AACL,EAAG,CACCS,KAAM,WACNgE,eAAgB,GAChBW,OAAQ,EACRU,QAAS,CAAElE,UAAW,GAAI,CAC9B,EAAE,AACV,EACA2I,UAAW,CACPjC,aAAc,GACdvB,eAAgB,IAChBsB,qBAAsB,CAClB,CAAEvJ,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,GAAIkB,IAAK,CAAE,EACnB,CACDwI,GAAI,CACA,CAAE5G,UAAW,IAAK3B,KAAM,EAAG,EAC3B,CAAE2B,UAAW,IAAK3B,KAAM,GAAI,EAC5B,CAAE2B,UAAW,KAAM3B,KAAM,CAAE,EAC3B,CAAE2B,UAAW,KAAM3B,KAAM,GAAI,EAC7B,CAAE2B,UAAW,IAAMgF,EAAG,EAAG3G,KAAM,GAAI,EACnC,CAAE2B,UAAW,KAAM3B,KAAM,GAAI,EAC7B,CAAE2B,UAAW,MAAO3B,KAAM,GAAI,EACjC,CACDkH,YAAa,CAAC,CACN1G,KAAM,WACN2E,OAAQ,GACRU,QAAS,CAAElE,UAAW,GAAK,CAC/B,EAAG,CACCnB,KAAM,aACN2E,OAAQ,GACRU,QAAS,CAAElE,UAAW,GAAM,EAC5BmE,SAAU,CAAEnE,UAAW,GAAK,EAC5BsD,eAAgB,CACZ,CAAEpG,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,GAAIkB,IAAK,CAAE,EACnB,AACL,EAAG,CACCS,KAAM,aACN2E,OAAQ,IACRU,QAAS,CAAElE,UAAW,IAAKgF,EAAG,CAAE,EAChCb,SAAU,CAAEnE,UAAW,GAAI,CAC/B,EAAE,AACV,EAEA4I,MAAO,CACHlC,aAAc,GACdvB,eAAgB,IAChBI,YAAa,CAAC,CACN1G,KAAM,YACV,EAAE,AACV,EAEAgK,cAAe,CACXnC,aAAc,GACdvB,eAAgB,IAChByB,GAAI,CACA,CAAE5G,UAAW,KAAM3B,KAAM,EAAG,EAC5B,CAAE2B,UAAW,KAAM3B,KAAM,EAAG,EAC/B,CACDkH,YAAa,CAAC,CACN1G,KAAM,aACNqF,QAAS,CACLlE,UAAW,EACXiE,iCAAkC,KAClCe,EAAG,CACP,EACAb,SAAU,CACNnE,UAAW,EACXiE,iCAAkC,IAClCe,EAAG,CACP,CACJ,EAAE,AACV,EAEA8D,KAAM,CACFpC,aAAc,IACdvB,eAAgB,IAChBqB,kBAAmB,IACnBY,sBAAuB,CACnB,CAAElK,EAAG,EAAGkB,IAAK,CAAE,EACf,CAAElB,EAAG,IAAKkB,IAAK,GAAK,EACpB,CAAElB,EAAG,IAAKkB,IAAK,CAAE,EACpB,CACDmH,YAAa,CAAC,CACN1G,KAAM,aACN2E,OAAQ,EACRU,QAAS,CACLlE,UAAW,IACXiE,iCAAkC,EAClCe,EAAG,EACP,EACAb,SAAU,CACNnE,UAAW,IACXiE,iCAAkC,CACtC,CACJ,EAAG,CACCpF,KAAM,OACNgE,eAAgB,KAChBW,OAAQ,IACRjC,aAAc,CAClB,EAAE,AACV,CACJ,EAkCIwH,EAAiC,AAAChM,IAA+EW,OAAO,CAAEsL,EAAS,AAACjM,IAA+EiM,MAAM,CAsBzNC,EAAwC,WACxC,SAASA,EAAuB5H,CAAY,CAAE+D,CAAU,CAAE1F,CAAO,EAC7D,IAAI,CAAC2B,YAAY,CAAGA,EACpB,IAAI,CAAC6H,SAAS,CAAG,CAAC,EAClB,IAAI,CAACC,aAAa,CAAGzJ,EAAQyJ,aAAa,CAC1C,IAAI,CAACC,aAAa,CAAG,IAAIhJ,SAASiB,GAClC,IAAI,CAAC+H,aAAa,CAAC1I,OAAO,CAAC0E,GAC3B,IAAI,CAACiE,UAAU,CAAG,IAAIjJ,SAASiB,GAC/B,IAAI,CAACiI,2BAA2B,CAACN,EAAO,CACpCO,IAAK,CAAA,CACT,EAAG7J,EAAQ8J,YAAY,EAAI,CAAC,IAC5B,IAAI,CAACC,sBAAsB,CAAC,IAAI,CAACJ,UAAU,CAAE,IAAI,CAACD,aAAa,EAC/D,IAAI,CAACM,UAAU,CAAG,IA1kCkClL,EA0kCN6C,EAAc,AAA8B,UAA9B,OAAO3B,EAAQgK,UAAU,CACjFC,AA/DuDtC,CA+DzB,CAAC3H,EAAQgK,UAAU,CAAC,CAAGhK,EAAQgK,UAAU,EAC3E,IAAI,CAACvE,cAAc,CAAG,IAAI,CAACuE,UAAU,CAACvE,cAAc,EAAI,EACxD,IAAI,CAACuE,UAAU,CAAC3D,aAAa,GAC7B,IAAI,CAAC2D,UAAU,CAAChJ,OAAO,CAAC,IAAI,CAAC2I,UAAU,CAC3C,CAgNA,OA1MAJ,EAAuBxM,SAAS,CAACmN,eAAe,CAAG,SAAUpG,CAAM,EAC/D,IAAI,CAAC4F,aAAa,CAAC/K,IAAI,CAACE,eAAe,CAACiF,EAAQ,EAAGyF,EAAuBpF,QAAQ,CACtF,EASAoF,EAAuBxM,SAAS,CAACoN,mBAAmB,CAAG,SAAU1L,CAAI,CAAE2L,CAAM,EACzE,IAAIC,EAAef,EAAO,IAAI,CAACE,SAAS,CACpCY,GACAhM,EAAOiL,EAA+Be,EAAO9J,SAAS,EAClD8J,EAAO9J,SAAS,CAAG+I,EAA+Be,EAAOE,IAAI,EAC7Df,EAAuBgB,sBAAsB,CAACH,EAAOE,IAAI,EACzD,IACJjB,EAA+BjL,IAC/B,IAAI,CAAC4L,UAAU,CAACpD,cAAc,CAACnI,EAAML,EAAMiM,EAAaxD,YAAY,EAEpEwC,CAAAA,EAA+BgB,EAAaG,YAAY,GACxDnB,EAA+BgB,EAAaI,YAAY,CAAA,GACxD,IAAI,CAACC,gBAAgB,CAACjM,EAAM4L,EAAaG,YAAY,CAAEH,EAAaI,YAAY,EAEhFpB,EAA+BgB,EAAaR,GAAG,GAC/C,IAAI,CAACc,YAAY,CAAClM,EAAM4L,EAAaR,GAAG,EAExCR,EAA+BgB,EAAavG,MAAM,GAClD,IAAI,CAAC8G,eAAe,CAACnM,EAAM4L,EAAavG,MAAM,EAE9CuF,CAAAA,EAA+BgB,EAAaQ,WAAW,GACvDxB,EAA+BgB,EAAaS,gBAAgB,CAAA,GAC5D,IAAI,CAACC,eAAe,CAAC,UAAWtM,EAAM4L,EAAaQ,WAAW,CAAER,EAAaS,gBAAgB,EAE7FzB,CAAAA,EAA+BgB,EAAaW,YAAY,GACxD3B,EAA+BgB,EAAaY,iBAAiB,CAAA,GAC7D,IAAI,CAACF,eAAe,CAAC,WAAYtM,EAAM4L,EAAaW,YAAY,CAAEX,EAAaY,iBAAiB,CAExG,EAMA1B,EAAuBxM,SAAS,CAAC0J,aAAa,CAAG,SAAUhI,CAAI,EAC3D,IAAI,CAACuL,UAAU,CAACvD,aAAa,CAAChI,EAClC,EAKA8K,EAAuBxM,SAAS,CAACmO,MAAM,CAAG,WACtC,IAAI,CAAClB,UAAU,CAACrD,IAAI,GACpB,CACI,IAAI,CAAC6D,YAAY,EAAI,IAAI,CAACA,YAAY,CAAC7L,IAAI,CAC3C,IAAI,CAACwM,UAAU,EAAI,IAAI,CAACA,UAAU,CAAC7K,SAAS,CAC5C,IAAI,CAAC8B,WAAW,EAAI,IAAI,CAACA,WAAW,CAAC9B,SAAS,CAC9C,IAAI,CAAC8B,WAAW,EAAI,IAAI,CAACA,WAAW,CAACkD,CAAC,CACtC,IAAI,CAACjD,YAAY,EAAI,IAAI,CAACA,YAAY,CAAC/B,SAAS,CAChD,IAAI,CAAC+B,YAAY,EAAI,IAAI,CAACA,YAAY,CAACiD,CAAC,CACxC,IAAI,CAAC8F,OAAO,EAAI,IAAI,CAACA,OAAO,CAACvB,GAAG,CAChC,IAAI,CAACF,UAAU,CAAChL,IAAI,CACvB,CAACY,OAAO,CAAC,SAAU1B,CAAC,EAAI,OAAQA,GAAKA,EAAEe,qBAAqB,CAAC,EAAK,EACvE,EAKA2K,EAAuBxM,SAAS,CAACsO,OAAO,CAAG,WACvC,IAAI,CAACH,MAAM,GACX,IAAI,CAAClB,UAAU,CAACvI,IAAI,GAChB,IAAI,CAAC0J,UAAU,EACf,IAAI,CAACA,UAAU,CAAC1J,IAAI,GAExB,CACI,IAAI,CAAC+I,YAAY,CAAE,IAAI,CAACW,UAAU,CAAE,IAAI,CAAC/I,WAAW,CACpD,IAAI,CAACC,YAAY,CAAE,IAAI,CAAC+I,OAAO,CAAE,IAAI,CAACzB,UAAU,CAChD,IAAI,CAACD,aAAa,CACrB,CAACnK,OAAO,CAAE,SAAUtD,CAAC,EAAI,OAAOA,GAAKA,EAAEuK,UAAU,EAAI,EAC1D,EAKA+C,EAAuBxM,SAAS,CAAC4N,YAAY,CAAG,SAAUlM,CAAI,CAAEoL,CAAG,EAC3D,IAAI,CAACuB,OAAO,EACZ,IAAI,CAACA,OAAO,CAACvB,GAAG,CAAChL,eAAe,CAACgL,EAAKpL,EAAO,IAAI,CAACkD,YAAY,CAACqC,WAAW,CAAEuF,EAAuBpF,QAAQ,CAEnH,EAKAoF,EAAuBxM,SAAS,CAACgO,eAAe,CAAG,SAAUO,CAAM,CAAE7M,CAAI,CAAE6B,CAAS,CAAEiL,CAAS,EAC3F,IAAIjE,EAAO,IAAI,CAACgE,EAAS,OAAO,CAC5BE,EAAY,IAAI,CAAC7J,YAAY,CAACqC,WAAW,CAAGvF,EAC5C6I,IACI+B,EAA+BkC,IAC/BjE,EAAKhC,CAAC,CAACzG,eAAe,CAAC0M,EAAWC,EAAWjC,EAAuBpF,QAAQ,EAE5EkF,EAA+B/I,IAC/BgH,EAAKhH,SAAS,CAACzB,eAAe,CAACyB,EAAWkL,EAAWjC,EAAuBpF,QAAQ,EAGhG,EAKAoF,EAAuBxM,SAAS,CAAC6N,eAAe,CAAG,SAAUnM,CAAI,CAAEqF,CAAM,EACjE,IAAI,CAAC6F,UAAU,EACf,IAAI,CAACA,UAAU,CAAChL,IAAI,CAACE,eAAe,CAACiF,EAAQrF,EAAO,IAAI,CAACkD,YAAY,CAACqC,WAAW,CAAEuF,EAAuBpF,QAAQ,CAE1H,EAKAoF,EAAuBxM,SAAS,CAAC2N,gBAAgB,CAAG,SAAUjM,CAAI,CAAEgN,CAAK,CAAEC,CAAK,EAC5E,IAAIF,EAAY,IAAI,CAAC7J,YAAY,CAACqC,WAAW,CAAGvF,CAC5C,CAAA,IAAI,CAAC+L,YAAY,EAAInB,EAA+BoC,IACpD,IAAI,CAACjB,YAAY,CAAC7L,IAAI,CAACE,eAAe,CAAC4M,EAAOD,EAAWjC,EAAuBpF,QAAQ,EAExF,IAAI,CAACgH,UAAU,EAAI9B,EAA+BqC,IAClD,IAAI,CAACP,UAAU,CAAC7K,SAAS,CAACzB,eAAe,CAAC,GAAK6M,EAAOF,EAAWjC,EAAuBpF,QAAQ,CAExG,EAKAoF,EAAuBxM,SAAS,CAAC6M,2BAA2B,CAAG,SAAUE,CAAY,EACjF,IAAIpF,EAAM,IAAI,CAAC/C,YAAY,AACvBmI,CAAAA,EAAaD,GAAG,EAChB,CAAA,IAAI,CAACuB,OAAO,CAAG,IAAIO,iBAAiBjH,EAAG,EAEvCoF,EAAa8B,OAAO,GACpB,IAAI,CAACT,UAAU,CAAG,IAAI/K,eAAesE,EAAK,CACtCvF,KAAM,OACNmB,UAAW,CACf,GACA,IAAI,CAACkK,YAAY,CAAG,IAAI9J,SAASgE,GACjC,IAAI,CAACyG,UAAU,CAACnK,OAAO,CAAC,IAAI,CAACwJ,YAAY,EACzC,IAAI,CAACA,YAAY,CAACxJ,OAAO,CAAC,IAAI,CAAC0I,aAAa,CAAC/K,IAAI,EACjD,IAAI,CAACwM,UAAU,CAAC3J,KAAK,IAErBsI,EAAa+B,OAAO,GACpB,IAAI,CAACzJ,WAAW,CAAG,IAAIiD,iBAAiBX,EAAK,CACzCvF,KAAM,UACNmB,UAAW,GACf,GACA,IAAI,CAAC+B,YAAY,CAAG,IAAIgD,iBAAiBX,EAAK,CAC1CvF,KAAM,WACNmB,UAAW,CACf,GAER,EAMAiJ,EAAuBxM,SAAS,CAACgN,sBAAsB,CAAG,SAAU+B,CAAK,CAAEC,CAAM,EAC7E,CACI,IAAI,CAACX,OAAO,CACZ,IAAI,CAAChJ,WAAW,CAChB,IAAI,CAACC,YAAY,CACjByJ,EACH,CAACnJ,MAAM,CAAC,SAAUjD,CAAI,CAAEkD,CAAG,EACxB,OAAQA,EACHA,CAAAA,EAAI5B,OAAO,CAACtB,GAAOkD,CAAE,EACtBlD,CACR,EAAGqM,EACP,EAMAxC,EAAuByC,sBAAsB,CAAG,SAAU1B,CAAI,EAC1D,IAAI2B,EAAQ3B,EAAK2B,KAAK,CAAC,0BAA2BC,EAAWD,EAAQA,CAAK,CAAC,EAAE,CAAG,IAAKE,EAAYD,CAAQ,CAAC,EAAE,CAACE,WAAW,GAAIC,EAAaH,CAAQ,CAAC,EAAE,CAEpJ,MAAO,AAAC,CAAA,CAAA,CACJI,EAAG,EAAGlQ,EAAG,EAAGmQ,EAAG,EAAGtJ,EAAG,EAAGuJ,EAAG,EAAGnQ,EAAG,EAAGoQ,EAAG,EAC3C,CAAA,CAAC,CAACN,EAAU,EAAI,CAAA,EAJsME,CAAAA,AAAe,MAAfA,EAC9M,EAAIA,AAAe,MAAfA,EAAqB,GAAK,CAAA,EAGEK,AAAS,GAJ8GT,CAAAA,EAAQU,SAASV,CAAK,CAAC,EAAE,CAAE,IAAM,CAAA,CAKpM,EAUA1C,EAAuBgB,sBAAsB,CAAG,SAAUD,CAAI,EAG1D,OAAO,QAAUjM,KAAKuO,GAAG,CAAC,EAAGvO,KAAK6B,GAAG,CAFnB,AAAgB,UAAhB,OAAOoK,EACjB,IAAI,CAAC0B,sBAAsB,CAAC1B,GAAQA,EACO,KAAO,GAC9D,EACAf,EAAuBpF,QAAQ,CAAG0I,AA9xCsB/N,EA8xCEC,YAAY,CAAG,EAClEwK,CACX,IA4HIuD,EAA2B,AAACzP,IAA+EY,IAAI,CAiB/G8O,EAAqC,WACrC,SAASA,EAAoB/M,CAAO,EAChC,IAAI,CAACA,OAAO,CAAGA,EACf,IAAI,CAACgH,YAAY,CAAG,EACpB,IAAI,CAACgG,SAAS,CAAGC,OAAOC,eAAe,CACQ,KAAA,IAApCA,gBAAgBC,eAAe,EACtCD,CAAAA,gBAAgBC,eAAe,CAAG,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC,IAAI,CAAA,EAE7D,IAAI,CAACD,QAAQ,GACb,IAAI,CAACE,SAAS,CAAG,EAAE,AACvB,CA2FA,OAlFAP,EAAoBhQ,SAAS,CAACwQ,GAAG,CAAG,SAAUC,CAAO,CAAExN,CAAO,EAC1D,GAAI,IAAI,CAACgN,SAAS,CAAE,CAChB,IAAI,CAACA,SAAS,CAAC9B,MAAM,GACrB,IAAIuC,EAAY,IAAIC,yBAAyBF,EACzC,CAAA,IAAI,CAACG,KAAK,EACVF,CAAAA,EAAUE,KAAK,CAAG,IAAI,CAACA,KAAK,AAAD,EAE/BF,EAAUG,IAAI,CAAG5N,GAAWA,EAAQ4N,IAAI,EAAI,IAAI,CAAC5N,OAAO,CAAC4N,IAAI,EAAI,EACjEH,EAAUI,KAAK,CAAG7N,GAAWA,EAAQ6N,KAAK,EACtC,IAAI,CAAC7N,OAAO,CAAC6N,KAAK,EAAI,EAC1BJ,EAAU3J,MAAM,CAAGgJ,EAAyB9M,GAAWA,EAAQ8D,MAAM,CAAE,IAAI,CAAC9D,OAAO,CAAC8D,MAAM,CAAE,GAAK,IAAI,CAACkD,YAAY,CAClH,IAAI,CAACgG,SAAS,CAACc,KAAK,CAACL,EACzB,CACJ,EAWAV,EAAoBhQ,SAAS,CAACgR,SAAS,CAAG,SAAUtP,CAAI,CAAE+O,CAAO,CAAExN,CAAO,EACtE,IAAI,CAACsN,SAAS,CAACU,IAAI,CAACC,WAAW,IAAI,CAACV,GAAG,CAACF,IAAI,CAAC,IAAI,CAAEG,EAASxN,GAAUvB,GAC1E,EAKAsO,EAAoBhQ,SAAS,CAACmO,MAAM,CAAG,WACnC,IAAI,CAACoC,SAAS,CAAC/N,OAAO,CAAC2O,cACvB,IAAI,CAACZ,SAAS,CAAG,EAAE,CACnB,IAAI,CAACN,SAAS,CAAC9B,MAAM,EACzB,EAKA6B,EAAoBhQ,SAAS,CAACsO,OAAO,CAAG,WAIpC,IAAI,CAACH,MAAM,EACf,EAQA6B,EAAoBhQ,SAAS,CAACmN,eAAe,CAAG,SAAUxL,CAAG,EACzD,IAAI,CAACsI,YAAY,CAAGtI,CACxB,EAKAqO,EAAoBhQ,SAAS,CAACqQ,QAAQ,CAAG,WACrC,GAAI,IAAI,CAACJ,SAAS,CAAE,CAMhB,IAAK,IALDmB,EAAS,IAAI,CAACnO,OAAO,CAACoO,IAAI,CAC1BC,EAAO,IAAI,CAACrO,OAAO,CAACsO,QAAQ,EAAI,QAChCC,EAAS,IAAI,CAACvB,SAAS,CAACwB,SAAS,GACjCC,EAAMF,EAAO3Q,MAAM,CACnB8Q,EAAe,KAAK,EACfhR,EAAI,EAAGA,EAAI+Q,EAAK,EAAE/Q,EAAG,CAC1B,GAAIyQ,GAAUI,CAAM,CAAC7Q,EAAE,CAAC0Q,IAAI,GAAKD,EAAQ,CACrC,IAAI,CAACR,KAAK,CAAGY,CAAM,CAAC7Q,EAAE,CACtB,MACJ,CACA,GAAI,CAACgR,GAAgBH,CAAM,CAAC7Q,EAAE,CAAC2Q,IAAI,GAAKA,IACpCK,EAAeH,CAAM,CAAC7Q,EAAE,CACpB,CAACyQ,GACD,KAGZ,CACA,IAAI,CAACR,KAAK,CAAGe,CACjB,CACJ,EACO3B,CACX,IA2DI4B,EAAiC,WACjC,SAASA,EAAgBxP,CAAI,CAAEyP,CAAM,CAAEC,CAAc,CAAEC,CAAM,CAAEC,CAAK,EACzC,KAAK,IAAxBF,GAA6BA,CAAAA,EAAiB,CAAA,CAAI,EACtD,IAAI,CAAC1P,IAAI,CAAGA,EACZ,IAAI,CAACyP,MAAM,CAAGA,EACd,IAAI,CAACC,cAAc,CAAGA,EACtB,IAAI,CAACE,KAAK,CAAGA,EACb,IAAI,CAACD,MAAM,CAAGA,GAAU,EAAE,AAC9B,CA0BA,OAzBAH,EAAgB5R,SAAS,CAACiS,QAAQ,CAAG,SAAUC,CAAK,EAChD,IAAIC,EAAY,IAAI,CAACJ,MAAM,CAAC,IAAI,CAACA,MAAM,CAAClR,MAAM,CAAG,EAAE,CACnD,GAAIsR,GAAaD,EAAMxQ,IAAI,CAAGyQ,EAAUzQ,IAAI,CAAE,CAG1C,IADA,IAAIf,EAAI,IAAI,CAACoR,MAAM,CAAClR,MAAM,CACnBF,KAAO,IAAI,CAACoR,MAAM,CAACpR,EAAE,CAACe,IAAI,CAAGwQ,EAAMxQ,IAAI,GAC9C,IAAI,CAACqQ,MAAM,CAACK,MAAM,CAACzR,EAAI,EAAG,EAAGuR,EACjC,MAEI,IAAI,CAACH,MAAM,CAACd,IAAI,CAACiB,GAErB,OAAOA,CACX,EACAN,EAAgB5R,SAAS,CAAC4J,IAAI,CAAG,WAC7B,IAAI,CAACoI,KAAK,CAAG,CAAA,CACjB,EACAJ,EAAgB5R,SAAS,CAACqS,MAAM,CAAG,WAC/B,IAAI,CAACL,KAAK,CAAG,CAAA,CACjB,EACAJ,EAAgB5R,SAAS,CAACmO,MAAM,CAAG,WAC/B,IAAI,CAAC0D,MAAM,CAAC1D,MAAM,EACtB,EACAyD,EAAgB5R,SAAS,CAACsO,OAAO,CAAG,WAChC,IAAI,CAACuD,MAAM,CAACvD,OAAO,EACvB,EACOsD,CACX,IA6DIU,EAAY,AAAChS,IAA+EY,IAAI,CACNwO,EAAI,SAAU6C,CAAI,CAAErT,CAAC,EAAI,OAAOA,IAAM,EAAIqT,EAAO,GAAM,EAQ/IC,EAAW,CAAC,EAAG,IAAM,GAAM,EAAM,EAAM,IAAM,GAAK,CACxDC,EAAY,SAAUvT,CAAC,EAGnB,IAFA,IAAIwT,EAAMxT,AAAI,IAAJA,EACNyT,EAAM,EAAE,CACLzT,IAAM,GACTwT,IAAQ,EACRA,GAAO,AAAK,IAAJxT,EAAY,IAExB,OAEI,GADAyT,EAAI1B,IAAI,CAACyB,AAAM,IAANA,GACLA,AAAM,IAANA,EACAA,IAAQ,OAGR,MAGR,OAAOC,CACX,EAAGC,EAAe,SAAUb,CAAM,EAG9B,IAFIc,EACAC,EACAH,EAAM,EAAE,CACRI,EAAM,SAAUC,CAAE,EAElB,IADI,IAAItQ,EAAKiQ,EAAI9R,MAAM,CAChB6B,KAAQiQ,CAAG,CAACjQ,EAAG,CAACuQ,MAAM,CAAGD,EAAGC,MAAM,GACzCN,EAAIP,MAAM,CAAC1P,EAAK,EAAG,EAAGsQ,EAC1B,EAuDA,OAtDAjB,EAAOvP,OAAO,CAAC,SAAUgN,CAAC,EACtB,IAAI/P,EAAI+P,EAAE0D,sBAAsB,EAAI,CAAC,EACjCzS,EAAI+O,EAAE9N,IAAI,CACVyR,EAAML,EAAYR,EAAU7S,EAAEqK,YAAY,CAC1CgJ,GACAM,EAAOD,GAAO3D,EAAE9N,IAAI,CAAGyR,EACvBE,EAAO,CAAC,CACAC,OAAQ,SAAUpU,CAAC,EAAI,OAAO,GAAK,GAAKA,EAAI,GAAM,EAClD8I,KAAM,CACF,GAAMvI,EAAEqN,GAAG,CACX,GAAMrN,EAAEgO,YAAY,CACpB,GAAMhO,EAAEiO,YAAY,AACxB,CACJ,EAAG,CACC4F,OAAQ,SAAUpU,CAAC,EAAI,OAAO,IAAMA,EAAI,IAAQ,GAAM,EACtD8I,KAAM,CACF,GAAMvI,EAAEqO,WAAW,CACnB,GAAMrO,EAAEwO,YAAY,AACxB,CACJ,EAAG,CACCqF,OAAQ,SAAUpU,CAAC,EACf,OAAO,GAAKoC,KAAK6B,GAAG,CAAC,GACjC7B,KAAKwB,GAAG,CAAC,IACT5D,IAAM,GAAK,GAAK,GACZ,EACA8I,KAAM,CACF,GAAMvI,EAAEsO,gBAAgB,CACxB,GAAMtO,EAAEyO,iBAAiB,AAC7B,CACJ,EAAE,CAAEhH,EAAI2L,EAAYpT,AAAa,KAAK,IAAlBA,EAAEsH,MAAM,CAC5BuL,EAAUO,EAAW,KAAO,IAAMpT,EAAEsH,MAAM,CAAG,IAAM1F,EAAO5B,EAAE8D,SAAS,CAAEgK,EAAO9N,EAAE8N,IAAI,EAAI,EAAGgG,EAAU,GAAMlS,CAAAA,EAjEhFC,KAAKgD,KAAK,CAAC,GAAKhD,KAAKC,GAAG,CAiE0EF,GAjEpEC,KAAKkS,GAAG,CAAG,UAkEpE,AAAgB,UAAhB,OAAOjG,EAAoBkG,AA3ciCjH,EA4cvDyC,sBAAsB,CAAC1B,GAAQA,CAAG,EAAK,IAEhD8F,EAAK7Q,OAAO,CAAC,SAAUkR,CAAO,EAAI,OAAOhU,OAAOiU,IAAI,CAACD,EAAQ1L,IAAI,EAC5DxF,OAAO,CAAC,SAAUoR,CAAU,EAC7B,IAAIC,EAAMH,EAAQ1L,IAAI,CAAC4L,EAAW,AACtB,MAAK,IAAbC,GACAd,EAAI,CACAE,OAAQxS,EACR2B,KAAM,WACN4F,KAAM,CACF,IAAM4H,SAASgE,EAAY,IAC3BF,EAAQJ,MAAM,CAACO,GAClB,AACL,EAER,EAAI,GAEAT,IACAL,EAAI,CAAEE,OAAQxS,EAAG2B,KAAM,MAAO4F,KAAM,CAAC,IAAMuL,EAASrM,EAAE,AAAC,GACvD6L,EAAI,CAAEE,OAAQG,EAAMhR,KAAM,MAAO4F,KAAM,CAAC,IAAMuL,EAASrM,EAAE,AAAC,GAElE,GACOyL,CACX,EAAGmB,EAAgB,SAAUpH,CAAa,CAAEhE,CAAc,EACtD,IAAIqJ,EAAS,EAAE,CAKf,GAJIrJ,GAEAqJ,EAAOd,IAAI,CAAC,EAAG,IAAMvI,AAAiB,IAAjBA,GAErBgE,EAAe,CAGf,IAAK,IADDqH,EAAU,EAAE,CACPpT,EAAI,EAAGA,EAAI+L,EAAc7L,MAAM,CAAE,EAAEF,EAAG,CAC3C,IAAIqT,EAAOtH,EAAcuH,UAAU,CAACtT,GAChCqT,EAAO,KACPD,EAAQ9C,IAAI,CAAC+C,EAErB,CACA,OAAOjC,EAAOmC,MAAM,CAAC,CAAC,EAAG,IAAM,EAAK,CAAEzB,EAAUsB,EAAQlT,MAAM,EAAGkT,EACrE,CACA,OAAOhC,CACX,EAAGoC,EAAgB,SAAUpC,CAAM,CAAEqC,CAAW,CAAE1H,CAAa,CAAEhE,CAAc,EAC3E,IAAI2L,EAAW,EACXC,EAAaR,EAAcpH,EAC3BhE,GACA6L,EAAc3B,EAAab,GAAQnM,MAAM,CAAC,SAAUoC,CAAI,CACxDwH,CAAC,EACG,IAAI/O,EAAIgS,EAAUjD,EAAEyD,MAAM,CAAGoB,GAEjC,OADAA,EAAW7E,EAAEyD,MAAM,CACZjL,EAAKkM,MAAM,CAACzT,EAAG+O,EAAExH,IAAI,CAChC,EAAG,EAAE,EACDwM,EAAW,CAAC,EAAG,IAAM,GAAM,EAAE,CAC7BC,EAAO,AAACL,CAAAA,EAAc5B,EAAS3R,MAAM,CAAG,CAAA,EACpCyT,EAAWzT,MAAM,CACjB0T,EAAY1T,MAAM,CAAG2T,EAAS3T,MAAM,CAC5C,MAAO,CACH,GAAM,GAAM,IAAM,IAClB6O,EAAE,EAAG+E,GAAO/E,EAAE,EAAG+E,GACjB/E,EAAE,EAAG+E,GAAO/E,EAAE,EAAG+E,GACpB,CAACP,MAAM,CAACE,EAAc5B,EAAW,EAAE,CAAE8B,EAAYC,EAAaC,EAEnE,EAkBiCE,EAVjC,SAAgBC,CAAQ,EACpB,IAzI0KC,EAyItKC,EAAgBF,EAASpG,MAAM,CAAC,SAAUgB,CAAC,EAAI,MAAO,CAAC,CAACA,EAAEwC,MAAM,CAAClR,MAAM,AAAE,GACzEiU,EAAQD,EAAchU,MAAM,CAC5BkU,EAAUD,EAAQ,EACtB,OAAO,IAAIE,WAAWC,AA5IsK,CAC5L,GAAM,GAAM,IAAM,IAClB,EAAG,EAAG,EAAG,EACT,EAAGL,CAAAA,CAAAA,CAHuKA,EA4I1IG,EAAUD,EAAQ,EAAIA,GAzIzC,CAAA,EACbpF,EAAE,EAAGkF,GAAUlF,EAAE,EAAGkF,GAGpB,EAAG,IACN,CAoIgEV,MAAM,CAACa,EAAUZ,EAAc,EAAE,CAAE,CAAA,GAAQ,EAAE,CAC1GU,EAAcjP,MAAM,CAAC,SAAUsP,CAAM,CAAEC,CAAO,EAC1C,IAAItD,EAASsD,EAAQtD,MAAM,CAC3B,OAAOqD,EAAOhB,MAAM,CAACC,EAAcgB,EAAQpD,MAAM,CAAE,CAACgD,EAASlD,EAAOnF,aAAa,CAAEmF,EAAOnJ,cAAc,EAC5G,EAAG,EAAE,GACT,EAsBI0M,EAAW,AAAC9U,IAA+E8U,QAAQ,CAAEC,EAAM,AAAC/U,IAA+E+U,GAAG,CAAEC,EAAM,AAAChV,IAA+E+U,GAAG,CAACE,QAAQ,CAMlSC,EAASH,EAAII,GAAG,EAAIJ,EAAIK,SAAS,EAAIL,EA4HrCM,EA5EJ,SAAqBC,CAAO,CAAEC,CAAQ,EAClC,IAAIC,EAAMT,EAAIU,SAAS,CACnBzW,EAAIgW,EAAIU,aAAa,CAAC,KAG1B,GAAI,AAAmB,UAAnB,OAAOJ,GACP,CAAEA,CAAAA,aAAmBK,MAAK,GAC1BH,EAAII,gBAAgB,CAAE,CACtBJ,EAAII,gBAAgB,CAACN,EAASC,GAC9B,MACJ,CAEA,GADAD,EAAU,GAAKA,EACXE,EAAIK,SAAS,CAACtV,MAAM,CAAG,IACvB,MAAM,AAAIuV,MAAM,kBAEpB,IAEIC,EAAmB,YAAYC,IAAI,CAACR,EAAIK,SAAS,EAKrD,GAAII,CAAAA,AAHcnB,GACV,AAAmB,UAAnB,OAAOQ,GACPA,AAA4C,IAA5CA,EAAQY,OAAO,CAAC,yBACNH,GAAoBT,EAAQ/U,MAAM,CAAG,GAAM,GAErD,CADJ+U,CAAAA,EAAUa,AAxDlB,SAAuBb,CAAO,EAC1B,IAAIc,EAAQd,EACHe,OAAO,CAAC,eAAgB,IACxBzH,KAAK,CAAC,yCACf,GAAIwH,GACAA,EAAM7V,MAAM,CAAG,GACdwU,EAAIuB,IAAI,EACTvB,EAAIwB,WAAW,EACfxB,EAAIL,UAAU,EACdK,EAAIyB,IAAI,EACPtB,EAAOuB,eAAe,CAAG,CAK1B,IAAK,IAHDC,EAAS3B,EAAIuB,IAAI,CAACF,CAAK,CAAC,EAAE,EAC1BhE,EAAM,IAAI2C,EAAIwB,WAAW,CAACG,EAAOnW,MAAM,EACvCoW,EAAS,IAAI5B,EAAIL,UAAU,CAACtC,GACvB/R,EAAI,EAAGA,EAAIsW,EAAOpW,MAAM,CAAE,EAAEF,EACjCsW,CAAM,CAACtW,EAAE,CAAGqW,EAAO/C,UAAU,CAACtT,GAElC,OAAO6U,EACFuB,eAAe,CAAC,IAAI1B,EAAIyB,IAAI,CAAC,CAACG,EAAO,CAAE,CAAE,KAAQP,CAAK,CAAC,EAAE,AAAC,GACnE,CACJ,EAmCgCd,IAAY,EAAC,EAEjC,MAAM,AAAIQ,MAAM,6BAIxB,GAAI,AAAsB,KAAA,IAAf9W,EAAE4X,QAAQ,CACjB5X,EAAE6X,IAAI,CAAGvB,EACTtW,EAAE4X,QAAQ,CAAGrB,EACbP,EAAI8B,IAAI,CAACC,WAAW,CAAC/X,GACrBA,EAAEgY,KAAK,GACPhC,EAAI8B,IAAI,CAACG,WAAW,CAACjY,QAIrB,GAAI,CACA,GAAI,CAAC+V,EAAImC,IAAI,CAAC5B,EAAS,SACnB,MAAM,AAAIQ,MAAM,wBAExB,CACA,MAAOqB,EAAI,CAEPpC,EAAIqC,QAAQ,CAACP,IAAI,CAAGvB,CACxB,CAER,EA8BI+B,EAA+B,AAACrX,IAA+EW,OAAO,CAAE2W,EAAO,AAACtX,IAA+EsX,IAAI,CAAEC,EAAQ,AAACvX,IAA+EuX,KAAK,CA4BlTC,EAAsC,WACtC,SAASA,EAAqB7U,CAAO,CAAE8U,CAAK,EACxC,IAAI,CAACA,KAAK,CAAGA,EACb,IAAI,CAACC,QAAQ,CAAG,CAAA,EAChB,IAAI,CAACC,SAAS,CAAG,CAAA,EACjB,IAAI,CAACtD,QAAQ,CAAG,EAAE,CAClB,IAAI,CAACuD,kBAAkB,CAAG,EAAE,CAC5B,IAAI,CAACC,aAAa,CAAG,EACrB,IAAI,CAACC,cAAc,CAAG,EACtB,IAAI,CAACnV,OAAO,CAAGA,GAAW,CAAC,CAC/B,CAkcA,OA/bA6U,EAAqB9X,SAAS,CAACqY,UAAU,CAAG,SAAUjW,CAAI,CAAEyP,CAAM,CAAEC,CAAc,CAAEC,CAAM,EAEtF,GADuB,KAAK,IAAxBD,GAA6BA,CAAAA,EAAiB,CAAA,CAAI,EAClD1P,AAAS,eAATA,GACA,CAACyP,EAAOzE,mBAAmB,EAC3BhL,AAAS,WAATA,GACI,CAACyP,EAAOb,SAAS,CACrB,MAAM,AAAIoF,MAAM,oDAEpB,IAAIjB,EAAU,IApZ2CvD,EAoZVxP,EAC3CyP,EACAC,EACAC,GAEJ,OADA,IAAI,CAAC4C,QAAQ,CAAC1D,IAAI,CAACkE,GACZA,CACX,EAOA2C,EAAqB9X,SAAS,CAACsY,IAAI,CAAG,SAAU/J,CAAM,CAAEgK,CAAc,CAAEC,CAAU,CAAEC,CAAK,EACrF,IAxDAC,EAOAC,EAiDInQ,EAAQ,IAAI,AACO,MAAK,IAAxB+P,GAA6BA,CAAAA,EAAiB,CAAA,CAAG,EAClC,KAAK,IAApBC,GAAyBA,CAAAA,EAAa,CAAA,CAAG,EACzC,IAAI,CAACP,SAAS,CACd,IAAI,CAAC9J,MAAM,GAGX,IAAI,CAACyK,uBAAuB,GAEhC,IAAI,CAACC,aAAa,CAAGJ,EACrB,IAAI,CAACN,aAAa,CAAGW,KAAKC,GAAG,GAC7B,IAAI,CAACX,cAAc,CAAG,EACtB,IAAI,CAACJ,QAAQ,CAAG,CAAA,EAChB,IAAI,CAACC,SAAS,CAAG,CAAA,EACjB,IAAIe,EAAgB,IAAI,CAAC/V,OAAO,CAAC+V,aAAa,EAAI,EAC9CC,EAAS,IAAI,CAAChW,OAAO,CAACgW,MAAM,CAC5BC,EAAc,IAAI,CAACjW,OAAO,CAACiW,WAAW,CACtCC,EAAgB,IAAI,CAAClW,OAAO,CAACkW,aAAa,CAC1CxE,EAAWpG,GAnEfoK,EAAUD,CAPVA,EAAW/D,AA4EP,CAAA,IAAI,CAACyE,eAAe,EAAI,IAAI,CAACzE,QAAQ,AAAD,EA5EpB5L,GAAG,CAAC,SAAUoM,CAAO,EAEzC,OADIA,EAAQhH,MAAM,GACX,CACHgH,QAASA,EACTkE,eAAgBlE,EAAQnD,KAAK,CACzB,EAAE,CAAGmD,EAAQpD,MAAM,CAACxD,MAAM,CAsEXA,EArEvB,CACJ,IAAuB3I,MAAM,CAAC,SAAU0T,CAAG,CAAEzT,CAAG,EAC5C,OAAOvE,KAAK6B,GAAG,CAACmW,EAAKzT,EAAIwT,cAAc,CAACxY,MAAM,CAC1CgF,EAAIwT,cAAc,CAAC,EAAE,CAAC3X,IAAI,CAAG6X,IACrC,EAAGA,KACIb,EAAS3P,GAAG,CAAC,SAAUwG,CAAC,EAAI,OAAQ,IArXkBqC,EAqXerC,EAAE4F,OAAO,CAAC/S,IAAI,CAAEmN,EAAE4F,OAAO,CAACtD,MAAM,CAAEtC,EAAE4F,OAAO,CAACrD,cAAc,CAAEvC,EAAE8J,cAAc,CAACtQ,GAAG,CAAC,SAAUyG,CAAC,EACpK,OAAOqI,EAAMrI,EAAG,CAAE9N,KAAM8N,EAAE9N,IAAI,CAAGiX,CAAQ,EAC7C,GAAIpJ,EAAE4F,OAAO,CAACnD,KAAK,CAAI,IAgEX,IAAI,CAAC2C,QAAQ,CAKlB6E,EAAe,EAAE,CAChBjB,GACA,CAAA,IAAI,CAACa,eAAe,CAAGzE,CAAO,EAE9BsE,GACAA,EAAO,CAAElB,MAAO,IAAI,CAACA,KAAK,CAAE0B,SAAU,IAAI,AAAC,GAE/C,IAAIC,EAAU,EACd/E,EAASnS,OAAO,CAAC,SAAU2S,CAAO,EAC9B,IAAIA,EAAQnD,KAAK,EAGjB,IAAI2H,EAAYxE,EAAQpD,MAAM,CAAClR,MAAM,CACjC+Y,EAAmB,CAACL,IACpBM,EAAgB,CAACN,IACjBO,EAAgB,GACpBJ,EAAUpY,KAAKwB,GAAG,CAACqS,EAAQpD,MAAM,CAAC4H,EAAY,EAAE,EAC5CxE,EAAQpD,MAAM,CAAC4H,EAAY,EAAE,CAACjY,IAAI,EAAI,EAAGgY,GAyD7C,IAAK,IAAI/Y,EAAI,EAAGA,EAAIgZ,EAAW,EAAEhZ,GAC7BoZ,AAzDU,SAAUpZ,CAAC,EACjB,IAAI6O,EAAI2F,EAAQpD,MAAM,CAACpR,EAAE,CAC7BqZ,EAvBOta,OAAOiU,IAAI,CAACnE,AAuBaA,EAvBXyK,aAAa,EAAI,CAAC,GAClC/F,MAAM,CAACxU,OAAOiU,IAAI,CAACnE,AAsBQA,EAtBN0D,sBAAsB,EAAI,CAAC,IAChDgH,IAAI,GAwBT,GAAIF,IAAYF,IACZtK,CAAAA,EAAE9N,IAAI,CAAGmY,EAAgBb,CAAY,GAGzCc,EAAgBE,EAChBH,EAAgBrK,EAAE9N,IAAI,CAClByT,AAAiB,eAAjBA,EAAQ/S,IAAI,CACZ+S,EAAQtD,MAAM,CACTzE,mBAAmB,CAACoC,EAAE9N,IAAI,CAAG,IAAM8N,EAAE0D,sBAAsB,EAAI,CAAC,GAGrEiC,EAAQtD,MAAM,CAACb,SAAS,CAACxB,EAAE9N,IAAI,CAAE8N,EAAEiB,OAAO,EAAI,GAAIjB,EAAEyK,aAAa,EAAI,CAAC,GAE1E,IAAIE,EAAQ3K,EAAE4K,YAAY,CACtBrC,EAAQoC,GAASA,EAAME,MAAM,EAAIF,EAAME,MAAM,CAACtC,KAAK,CACnDuC,EAAgB9K,EAAE+K,QAAQ,EACtBJ,GAAUjB,CAAAA,GAAeC,CAAY,GACjChE,AAA2B,CAAA,IAA3BA,EAAQrD,cAAc,EACrBtC,CAAAA,EAAE9N,IAAI,CAAGkY,EAAmB,IAAMjZ,IAAMgZ,EAAY,CAAA,EAC7DQ,GACAX,EAAavI,IAAI,CAACkJ,GAElBG,IACA9R,EAAM0P,kBAAkB,CAACjH,IAAI,CAACC,WAAW,WAIrC,GAHI1B,EAAE+K,QAAQ,EACV/K,EAAE+K,QAAQ,GAEVJ,EAAO,CACP,GAAIhB,EAAe,CACf,IAAIzY,EAAIyZ,EAAME,MAAM,CAChB3Z,GAAKA,EAAE8Z,KAAK,EAAI9Z,EAAE8Z,KAAK,CAACC,SAAS,EACjC/Z,EAAE8Z,KAAK,CAACE,aAAa,CAAC,KAAK,EAAGP,GAE9BzZ,GAAKA,EAAEia,KAAK,EAAIja,EAAEia,KAAK,CAACF,SAAS,EACjC/Z,EAAEia,KAAK,CAACD,aAAa,CAAC,KAAK,EAAGP,EAEtC,CACIjB,GAAe,CAEnBnB,CAAAA,GAASA,EAAM6C,WAAW,EACtB7C,EAAM6C,WAAW,CAAC/Z,MAAM,CAAG,GAC3B+W,EAAKG,EAAM6C,WAAW,CAAE,SAAU9Z,CAAC,EAAI,OAAOA,IAAMqZ,CAAO,IAE3DA,EAAMU,WAAW,AAAD,GAChBV,EAAMU,WAAW,EAEzB,CACJ,EAAGrL,EAAE9N,IAAI,GACTkY,EAAmBpK,EAAE9N,IAAI,EAEjC,EAEYf,GAEhB,GACA,IAAIma,EAAW,IAAI,CAAC7X,OAAO,CAACwV,KAAK,CAC7BsC,EAAS,IAAI,CAAC9X,OAAO,CAAC8X,MAAM,CAChC,IAAI,CAAC7C,kBAAkB,CAACjH,IAAI,CAACC,WAAW,WACpC,IAAI6G,EAAQvP,EAAMuP,KAAK,CACnB/U,EAAU,CAAE+U,MAAOA,EACnB0B,SAAUjR,EACVgR,aAAcA,CAAa,CAC/BhR,CAAAA,EAAMyP,SAAS,CAAG,CAAA,EACdO,GACAhQ,EAAMwS,cAAc,GAEpBD,GACAA,EAAO/X,GAEP8X,GACAA,EAAS9X,GAETyV,GACAA,EAAMzV,GAEN+U,IACIA,EAAMkD,OAAO,EACblD,EAAMkD,OAAO,CAACC,IAAI,CAAC,GAEnBnD,EAAMoD,WAAW,EACjBpD,EAAMoD,WAAW,CAACC,UAAU,GAEhCrD,EAAMsD,IAAI,CAAC7Y,OAAO,CAAC,SAAUlD,CAAC,EAAI,OAAOA,EAAEgc,aAAa,EAAI,GAEpE,EAAG5B,EAAU,MACb,IAAI,CAACtB,cAAc,CAAGG,EAAiBmB,EAAU,IAAI,CAAC6B,SAAS,EACnE,EAEAzD,EAAqB9X,SAAS,CAACwb,KAAK,CAAG,WAInC,OAHA,IAAI,CAACxD,QAAQ,CAAG,CAAA,EAChB,IAAI,CAAC7J,MAAM,GACX,IAAI,CAACiK,cAAc,CAAGU,KAAKC,GAAG,GAAK,IAAI,CAACZ,aAAa,CAAG,GACjD,IAAI,CAACC,cAAc,AAC9B,EAEAN,EAAqB9X,SAAS,CAACyb,cAAc,CAAG,WAC5C,OAAO,IAAI,CAACxD,SAAS,CACjBa,KAAKC,GAAG,GAAK,IAAI,CAACZ,aAAa,CAC/B,IAAI,CAACC,cAAc,AAC3B,EAEAN,EAAqB9X,SAAS,CAACub,SAAS,CAAG,WACvC,OAAO,IAAI,CAAC5G,QAAQ,CAAC/O,MAAM,CAAC,SAAU8T,CAAO,CAAEvE,CAAO,EAClD,IAAIhD,EAAYgD,EAAQpD,MAAM,CAACoD,EAAQpD,MAAM,CAAClR,MAAM,CAAG,EAAE,CACzD,OAAOsR,EAAY7Q,KAAKwB,GAAG,CAACqP,EAAUzQ,IAAI,CAAEgY,GAAWA,CAC3D,EAAG,EACP,EAEA5B,EAAqB9X,SAAS,CAAC0b,MAAM,CAAG,WACpC,GAAI,IAAI,CAACtC,eAAe,CAAE,CACtB,IAAIuC,EAAe,IAAI,CAACvD,cAAc,CAAG,GACzC,IAAI,CAACE,IAAI,CAAC,SAAU9I,CAAC,EAAI,OAAOA,EAAE9N,IAAI,CAAGia,CAAc,EAAG,CAAA,EAAO,CAAA,EAAO,IAAI,CAAC9C,aAAa,EAC1F,IAAI,CAACV,aAAa,EAAIwD,CAC1B,MAEI,IAAI,CAACrD,IAAI,CAAC,KAAK,EAAG,CAAA,EAAO,CAAA,EAAO,IAAI,CAACO,aAAa,CAE1D,EAGAf,EAAqB9X,SAAS,CAAC4b,gBAAgB,CAAG,SAAUC,CAAW,CAAEpD,CAAK,EACtE,IAAI,CAACR,SAAS,EACd,IAAI,CAACuD,KAAK,GAEd,IAAIM,EAAiB,EACrB,IAAI,CAACxD,IAAI,CAAC,SAAU9I,CAAC,CAAE9M,CAAE,CAAEqZ,CAAG,EAG1B,IAAIpJ,EAAMkJ,EAAYrM,EAClB9M,EACAqZ,GAIJ,OAHIpJ,GAAOnD,EAAE9N,IAAI,CAAGoa,GAChBA,CAAAA,EAAiBtM,EAAE9N,IAAI,AAAD,EAEnBiR,CACX,EAAG,CAAA,EAAO,CAAA,EAAO8F,GACjB,IAAI,CAACW,eAAe,CAAG,IAAI,CAACA,eAAe,EAAI,IAAI,CAACzE,QAAQ,CAC5D,IAAI,CAACqD,QAAQ,CAAG,CAAA,EAChB,IAAI,CAACC,SAAS,CAAG,CAAA,EACjB,IAAI,CAACG,cAAc,CAAG0D,CAC1B,EAEAhE,EAAqB9X,SAAS,CAACgc,YAAY,CAAG,SAAUC,CAAI,CAAExD,CAAK,CAAEyD,CAAa,CAAEL,CAAW,EACvF,IAAI,CAAC5D,SAAS,EACd,IAAI,CAACuD,KAAK,GAEd,IAAInX,EAAW,IAAI,CAAC+T,cAAc,CAC9B+D,EAAc,IAAI,CAACxH,QAAQ,CAAC/O,MAAM,CAAC,SAAUlE,CAAI,CACjDyT,CAAO,EAOP,IALI,IAAIpD,EAAS8J,EACT1G,EAAQpD,MAAM,CAACxD,MAAM,CAACsN,GAAe1G,EAAQpD,MAAM,CACvDrR,EAAI,EACJ8O,EAAIuC,EAAOlR,MAAM,CACjBub,EAAgB1a,EACbhB,EAAI8O,GAAG,CACV,IAAI6M,EAAM,AAAC3b,EAAI8O,GAAM,EACjB/O,EAAIsR,CAAM,CAACsK,EAAI,CAAC3a,IAAI,CACpB4a,EAAM7b,EAAI4D,CACViY,CAAAA,EAAM,GACFL,GAAQxb,EAAI2b,GACZA,CAAAA,EAAgB3b,CAAAA,EAEpB+O,EAAI6M,GAECC,EAAM,GACP,CAACL,GAAQxb,EAAI2b,GACbA,CAAAA,EAAgB3b,CAAAA,EAEpBC,EAAI2b,EAAM,GAGNJ,EACAvb,EAAI2b,EAAM,EAGV7M,EAAI6M,CAGhB,CACA,OAAOD,CACX,EAAGH,EAAO1C,IAAW,CAACA,KACtB,GAAI4C,IAAgB5C,KAAY4C,IAAgB,CAAC5C,IAAU,CACnD2C,GACAA,EAAc,CACVnE,MAAO,IAAI,CAACA,KAAK,CAAE0B,SAAU,IAAI,CAAE8C,cAAeN,CACtD,GAEJ,MACJ,CACA,IAAI,CAACL,gBAAgB,CAAC,SAAUpM,CAAC,CAAE9M,CAAE,CAAEqZ,CAAG,EACtC,IAAIS,EAAaP,EACTzM,EAAE9N,IAAI,CAAG2C,GAAYmL,EAAE9N,IAAI,EAAIya,EAXD,IAY9B3M,EAAE9N,IAAI,CAAG2C,GAAYmL,EAAE9N,IAAI,EAAIya,EAZD,IAatC,OAAON,EAAcW,GAAcX,EAAYrM,EAAG9M,EAAIqZ,GAClDS,CACR,EAAG/D,EACP,EAIAX,EAAqB9X,SAAS,CAACyc,sBAAsB,CAAG,SAAU1c,CAAI,CAAE2c,CAAS,CAAEjE,CAAK,CAAEyD,CAAa,CAAEL,CAAW,EAQhH,IAAIc,EAAiBpD,IACjBqD,EAAe,KACnB,AAAC,CAAA,IAAI,CAACxD,eAAe,EAAI,IAAI,CAACzE,QAAQ,AAAD,EAAGnS,OAAO,CAAC,SAAU2S,CAAO,EAG7D,IAFA,IAAIpD,EAASoD,EAAQpD,MAAM,CACvBpR,EAAIoR,EAAOlR,MAAM,CACdF,KACH,GAbe6O,EAaHuC,CAAM,CAACpR,EAAE,CAZzB+B,EAY2B/B,EAXVkb,EACbA,EAAYrM,EAChB9M,EAS8BqP,IARtBvC,EAAE4K,YAAY,CAClB5K,EAAE4K,YAAY,EAUd,IAhBe5K,EACnB9M,EAeQmR,EAAM9B,CAAM,CAACpR,EAAE,CAACyZ,YAAY,CAACra,EAAK,CAClC8c,EAAOlF,EAA6B9D,IAAQvS,KAAKwb,GAAG,CAACJ,EAAY7I,EACxD,EAAA,IAATgJ,GAAkBA,EAAOF,IACzBA,EAAiBE,EACjBD,EAAe7K,CAAM,CAACpR,EAAE,EAGpC,GACIic,GACA,IAAI,CAACtE,IAAI,CAAC,SAAU9I,CAAC,EAAI,MAAO,CAAC,CAAEoN,CAAAA,GAC/BpN,EAAE9N,IAAI,CAAGkb,EAAalb,IAAI,CAAG,GAC7B8N,EAAE9N,IAAI,CAAGkb,EAAalb,IAAI,CAAG,GAC7B8N,EAAE4K,YAAY,GAAKwC,EAAaxC,YAAY,AAAD,CAAI,EAAG,CAAA,EAAO,CAAA,EAAO3B,GACpE,IAAI,CAACW,eAAe,CAAG,IAAI,CAACA,eAAe,EAAI,IAAI,CAACzE,QAAQ,CAC5D,IAAI,CAACqD,QAAQ,CAAG,CAAA,EAChB,IAAI,CAACC,SAAS,CAAG,CAAA,EACjB,IAAI,CAACG,cAAc,CAAGwE,EAAalb,IAAI,EAElCwa,GACLA,EAAc,CAAEnE,MAAO,IAAI,CAACA,KAAK,CAAE0B,SAAU,IAAI,AAAC,EAE1D,EAIA3B,EAAqB9X,SAAS,CAAC+c,iBAAiB,CAAG,SAAU5C,CAAK,EAC9D,OAAO,IAAI,CAACxF,QAAQ,CAAC/O,MAAM,CAAC,SAAUmM,CAAM,CAAEoD,CAAO,EACjD,IAAI6H,EAAc7H,EAAQpD,MAAM,CACvBxD,MAAM,CAAC,SAAUiB,CAAC,EAAI,OAAOA,EAAE4K,YAAY,GAAKD,CAAO,GAChE,OAAOpI,EAAOmC,MAAM,CAAC8I,EACzB,EAAG,EAAE,CACT,EAIAlF,EAAqB9X,SAAS,CAACid,WAAW,CAAG,SAAUC,CAAO,CAAEzE,CAAK,EAEjE,IAAI0E,EAAa,CACTC,MAAO7D,IACP8D,KAAM,CAAC9D,GACX,EAOJ,GANA,IAAI,CAAC5E,QAAQ,CAACnS,OAAO,CAAC,SAAU+M,CAAC,EACzBA,EAAEwC,MAAM,CAAClR,MAAM,GACfsc,EAAWC,KAAK,CAAG9b,KAAK6B,GAAG,CAACoM,EAAEwC,MAAM,CAAC,EAAE,CAACrQ,IAAI,CAAEyb,EAAWC,KAAK,EAC9DD,EAAWE,IAAI,CAAG/b,KAAKwB,GAAG,CAACyM,EAAEwC,MAAM,CAACxC,EAAEwC,MAAM,CAAClR,MAAM,CAAG,EAAE,CAACa,IAAI,CAAEyb,EAAWE,IAAI,EAEtF,GACIF,EAAWC,KAAK,CAAG7D,IAAU,CAC7B,IAAI+D,EAAc,AAACH,CAAAA,EAAWE,IAAI,CAAGF,EAAWC,KAAK,AAAD,EAZtC,IAaVG,EAAaJ,EAAWC,KAAK,CAAGF,EAAUI,EAC1CE,EAAWD,EAAaD,EAE5B,GAAI,CAAC,IAAI,CAAC3I,QAAQ,CAAC8I,IAAI,CAAC,SAAUlO,CAAC,EAI/B,IAHA,IAAIwC,EAASxC,EAAEwC,MAAM,CACjBrR,EAAI,EACJ8O,EAAIuC,EAAOlR,MAAM,CACdH,EAAI8O,GAAG,CACV,IAAI6M,EAAM,AAAC3b,EAAI8O,GAAM,EACjB/O,EAAIsR,CAAM,CAACsK,EAAI,CAAC3a,IAAI,CACxB,GAAIjB,EAAI8c,EACJ7c,EAAI2b,EAAM,OAET,IAAI5b,CAAAA,EAAI+c,CAAO,EAIhB,MAAO,CAAA,EAHPhO,EAAI6M,EAKZ,CACA,MAAO,CAAA,CACX,GACI,OAEJ,IAAI,CAAC/D,IAAI,CAAC,SAAU9I,CAAC,EAAI,OAAOA,EAAE9N,IAAI,EAAI6b,GAAc/N,EAAE9N,IAAI,EAAI8b,CAAU,EAAG,CAAA,EAAO,CAAA,EAAO/E,GAC7F,IAAI,CAACW,eAAe,CAAG,IAAI,CAACA,eAAe,EAAI,IAAI,CAACzE,QAAQ,CAC5D,IAAI,CAACqD,QAAQ,CAAG,CAAA,EAChB,IAAI,CAACC,SAAS,CAAG,CAAA,EACjB,IAAI,CAACG,cAAc,CAAGoF,CAC1B,CACJ,EAGA1F,EAAqB9X,SAAS,CAAC0d,kBAAkB,CAAG,SAAUnP,CAAM,EAChE,IAAIhF,EAAU,IAAI,CAACkS,cAAc,GAC7B9G,EAAW,IAAI,CAACyE,eAAe,EAAI,IAAI,CAACzE,QAAQ,CAChDgJ,EAAcpE,IACdqE,EAAe,KAkBnB,OAjBAjJ,EAASnS,OAAO,CAAC,SAAU+M,CAAC,EACxB,IAAIwC,EAASxC,EAAEwC,MAAM,CAACxD,MAAM,CAAC,SAAUiB,CAAC,CACpC9M,CAAE,CACFqZ,CAAG,EAAI,MAAO,CAAC,CAAEvM,CAAAA,EAAE4K,YAAY,EAAI5K,EAAE9N,IAAI,EAAI6H,GACxC,CAAA,CAACgF,GAAUA,EAAOiB,EACvB9M,EACAqZ,EAAG,CAAC,CAAI,GACRa,EAAe7K,CAAM,CAACA,EAAOlR,MAAM,CAAG,EAAE,CAC5C,GAAI+b,EAAc,CACd,IACIC,EAAOvb,KAAKwb,GAAG,CAACX,AADFS,EAAalb,IAAI,CACD6H,GAC9BsT,EAAOc,IACPA,EAAcd,EACde,EAAehB,EAAaxC,YAAY,CAEhD,CACJ,GACOwD,CACX,EAEA9F,EAAqB9X,SAAS,CAAC6d,KAAK,CAAG,WAC/B,IAAI,CAAC5F,SAAS,EACd,IAAI,CAAC9J,MAAM,GAEf,IAAI,CAAC6M,cAAc,EACvB,EACAlD,EAAqB9X,SAAS,CAACmO,MAAM,CAAG,WACpC,IAAI4M,EAAS,IAAI,CAAC9X,OAAO,CAAC8X,MAAM,CAC5BA,GACAA,EAAO,CAAEhD,MAAO,IAAI,CAACA,KAAK,CAAE0B,SAAU,IAAI,AAAC,GAE/C,IAAI,CAACxB,SAAS,CAAG,CAAA,EACjB,IAAI,CAACtD,QAAQ,CAACnS,OAAO,CAAC,SAAU+M,CAAC,EAAI,OAAOA,EAAEpB,MAAM,EAAI,GACpD,IAAI,CAACiL,eAAe,EAAI,IAAI,CAACA,eAAe,GAAK,IAAI,CAACzE,QAAQ,EAC9D,IAAI,CAACyE,eAAe,CAAC5W,OAAO,CAAC,SAAU+M,CAAC,EAAI,OAAOA,EAAEpB,MAAM,EAAI,GAEnE,IAAI,CAACyK,uBAAuB,GAC5B,IAAI,CAACR,cAAc,CAAG,CAC1B,EACAN,EAAqB9X,SAAS,CAACsO,OAAO,CAAG,WACrC,IAAI,CAACH,MAAM,GACP,IAAI,CAACiL,eAAe,EAAI,IAAI,CAACA,eAAe,GAAK,IAAI,CAACzE,QAAQ,EAC9D,IAAI,CAACyE,eAAe,CAAC5W,OAAO,CAAC,SAAU+M,CAAC,EAAI,OAAOA,EAAEjB,OAAO,EAAI,GAEpE,IAAI,CAACqG,QAAQ,CAACnS,OAAO,CAAC,SAAU+M,CAAC,EAAI,OAAOA,EAAEjB,OAAO,EAAI,EAC7D,EACAwJ,EAAqB9X,SAAS,CAACmN,eAAe,CAAG,SAAUxL,CAAG,EAC1D,IAAI,CAACgT,QAAQ,CAACnS,OAAO,CAAC,SAAU+M,CAAC,EAAI,OAAOA,EAAEsC,MAAM,CAAC1E,eAAe,CAACxL,EAAM,EAC/E,EACAmW,EAAqB9X,SAAS,CAAC8d,WAAW,CAAG,WACzC,OAAOpJ,EAAK,IAAI,CAACC,QAAQ,CAACpG,MAAM,CAAC,SAAUgB,CAAC,EAAI,MAAOA,AAAW,eAAXA,EAAEnN,IAAI,AAAmB,GACpF,EACA0V,EAAqB9X,SAAS,CAAC+d,YAAY,CAAG,SAAUlI,CAAQ,EAC5D,IAAI7N,EAAO,IAAI,CAAC8V,WAAW,GAAIzM,EAAO,AAACwE,CAAAA,GAC/B,IAAI,CAACkC,KAAK,EACN,IAAI,CAACA,KAAK,CAAC9U,OAAO,CAAC+a,KAAK,EACxB,IAAI,CAACjG,KAAK,CAAC9U,OAAO,CAAC+a,KAAK,CAACC,IAAI,EACjC,OAAM,EAAK,OAAQC,EAAO,IAAIpH,KAAK,CAAC9O,EAAK,CAAE,CAAE5F,KAAM,0BAA2B,GAAI+b,EAAMjO,OAAOuF,GAAG,CAACsB,eAAe,CAACmH,GAC3HvI,EAAiCwI,EAAK9M,GACtCnB,OAAOuF,GAAG,CAAC2I,eAAe,CAACD,EAC/B,EACArG,EAAqB9X,SAAS,CAACgb,cAAc,CAAG,WAC5C,OAAO,IAAI,CAAC5B,eAAe,CAC3B,OAAO,IAAI,CAACP,aAAa,CACzB,IAAI,CAACV,aAAa,CAAG,IAAI,CAACC,cAAc,CAAG,EAC3C,IAAI,CAACJ,QAAQ,CAAG,CAAA,CACpB,EACAF,EAAqB9X,SAAS,CAAC4Y,uBAAuB,CAAG,WACrD,IAAI,CAACV,kBAAkB,CAAC1V,OAAO,CAAC2O,cAChC,IAAI,CAAC+G,kBAAkB,CAAG,EAAE,AAChC,EACOJ,CACX,IA8BIuG,EAAmHvf,EAAoB,KACvIwf,EAAuIxf,EAAoBI,CAAC,CAACmf,GAc7JE,EAAgE,WAShE,MAAOA,AARPA,CAAAA,EAA2B7e,OAAOc,MAAM,EAAI,SAASC,CAAC,EAClD,IAAK,IAAIC,EAAGC,EAAI,EAAGzB,EAAI0B,UAAUC,MAAM,CAAEF,EAAIzB,EAAGyB,IAE5C,IAAK,IAAIG,KADTJ,EAAIE,SAAS,CAACD,EAAE,CACKjB,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACQ,EAAGI,IACzDL,CAAAA,CAAC,CAACK,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,EAElB,OAAOL,CACX,CAAA,EACgCM,KAAK,CAAC,IAAI,CAAEH,UAChD,EAKI4d,EAA0B,AAACle,IAA+EU,KAAK,CAAEyd,EAA4B,AAACne,IAA+EW,OAAO,CAAEyd,EAA2B,AAACpe,IAA+EiM,MAAM,CAAEoS,EAAoB,AAACre,IAA+Eqe,iBAAiB,CAAEC,EAA0B,AAACte,IAA+EuX,KAAK,CAAEgH,EAAyB,AAACve,IAA+EY,IAAI,CAE9qB4d,EAAS,AAACR,IAA2HQ,MAAM,CAC3IC,EAAmB,SAAUC,CAAG,EAChC,MAAO,AAAC,uBAAwB1I,IAAI,CAAC0I,EACzC,EAKA,SAASC,EAAkB9E,CAAK,CAAEpa,CAAI,EAClC,IAAImf,EACJ,GAAInf,EAAM,CAEN,GAAI,AAAe,UAAf,MADJmf,CAAAA,EAAM/E,CAAK,CAACpa,EAAK,AAAD,EAEZ,OAAOmf,EAEXA,EAAMP,EAAkB5e,EAAMoa,EAClC,CACA,MAAO,AAAe,UAAf,OAAO+E,EAAmBA,EAAM,KAAK,CAChD,CAsJA,SAASC,GAAiBnb,CAAK,CAAEob,CAAa,CAAEC,CAAmB,CAAEC,CAAM,CAAEC,CAAW,EAEpF,IAAIC,EAAeJ,EAActc,GAAG,CAAGsc,EAAcjc,GAAG,CACxD,GAAIqc,GAAgB,EAChB,OAAOH,EAAoBlc,GAAG,CAElC,IAAIsc,EAAiBJ,EAAoBvc,GAAG,CAAGuc,EAAoBlc,GAAG,CAElEuc,EAAoBD,EADPzb,CAAAA,EAAQob,EAAcjc,GAAG,AAAD,EACaqc,EACtD,GAAID,EAAa,CACb,IAAIhe,EAAM6d,EAAcjc,GAAG,CAAG,EAEtB,SAAUwc,CAAC,EAAI,OAAOre,KAAKC,GAAG,CAACoe,GAAKre,KAAKse,MAAM,AAAE,EAEjD,SAAUD,CAAC,EACP,IAAIE,EAAcve,KAAKwb,GAAG,CAAC6C,GAC3BE,EAAc,IACdA,CAAAA,GAAe,AAAC,CAAA,GAAKA,CAAU,EAAK,EAAC,EAEzC,IAAIlN,EAAMrR,KAAKC,GAAG,CAACse,GAAeve,KAAKwe,IAAI,CAC3C,OAAOH,EAAI,EAAI,CAAChN,EAAMA,CAC1B,EACAoN,EAAYxe,EAAI6d,EAAcjc,GAAG,EACrCuc,EAAoBD,EACfle,CAAAA,EAAIyC,GAAS+b,CAAQ,EACrBxe,CAAAA,EAAI6d,EAActc,GAAG,EAAIid,CAAQ,CAC1C,CAIA,OAAOvB,EAHGc,EACFD,EAAoBvc,GAAG,CAAG4c,EAC1BL,EAAoBlc,GAAG,CAAGuc,EACEL,EAAoBlc,GAAG,CAAEkc,EAAoBvc,GAAG,CACxF,CAmGA,SAASkd,GAAuBhd,CAAO,CAAEid,CAAW,CAAEC,CAAiB,CAAEC,CAAmB,CAAEC,CAAQ,CAAEC,CAAQ,CAAEC,CAAgB,EAC9H,OAAOzB,EAAuB0B,AA/FlC,SAAkCvd,CAAO,CAAEid,CAAW,CAAEC,CAAiB,CAAEM,CAAc,CAAEC,CAAc,CAAEH,CAAgB,EACvH,GAAI,AAA0B,UAA1B,OAAOG,EACP,OAAOA,EAEX,GAAI,AAA0B,YAA1B,OAAOA,EACP,OAAOA,EAAe/B,EAAyB,CAAEhd,KAAM,CAAE,EAAGsB,IAEhE,IAKI0d,EALAC,EAAQF,EACRG,EAAUJ,EAAeK,WAAW,CACpC1d,EAAMqd,EAAerd,GAAG,CACxBL,EAAM0d,EAAe1d,GAAG,CACxBge,EAASN,EAAeM,MAAM,CAUlC,GAR8B,UAA1B,OAAOL,IACPE,EAAQF,EAAeE,KAAK,CAC5BC,EAAUH,EAAeI,WAAW,EAAID,EACxCzd,EAAM0b,EAAuB4B,EAAetd,GAAG,CAAEA,GACjDL,EAAM+b,EAAuB4B,EAAe3d,GAAG,CAAEA,GACjDge,EAASL,EAAeK,MAAM,EAAIN,EAAeM,MAAM,CACvDJ,EAAQD,EAAeC,KAAK,EAE5B,CAACC,EACD,OAAO,KAEX,IAAII,EAAaJ,AAAoB,MAApBA,EAAMK,MAAM,CAAC,GAC1BD,GACAJ,CAAAA,EAAQA,EAAMM,KAAK,CAAC,EAAC,EAEzB,IAAIjd,EAAQhB,EAAQgB,KAAK,CACrBkd,EAAkBP,AAAU,UAAVA,GAAqB3c,AAAU,KAAK,IAAfA,GACnCsc,EACR,GAAI,CAACY,EAAiB,CAClB,IAAIC,EAAaV,EAAezc,KAAK,CACrC,GAAImd,AAAe,KAAK,IAApBA,EACAnd,EAAQmd,MAEP,CACD,GAAI,CAACne,EAAQmX,KAAK,CACd,OAAO,KAEXnW,EAAQhB,EAAQmX,KAAK,CAACwG,EAAM,AAChC,CACc,KAAK,IAAf3c,GACAA,CAAAA,EAAQ2a,EAAkBgC,EAAO3d,EAAQmX,KAAK,CAAA,CAEtD,CACA,GAAI,AAAiB,UAAjB,OAAOnW,GAAsBA,AAAU,OAAVA,EAC7B,OAAO,KAGX,IAAIod,EAAW,KACf,GAAIpe,EAAQmX,KAAK,EACb,GAAI2G,AAAW,UAAXA,GAAsBA,AAAW,UAAXA,EAAoB,CAC1C,IAAIO,EAAOre,EAAQmX,KAAK,CAACE,MAAM,CAACyG,EAAO,CACnCO,GAAQ5C,EAA0B4C,EAAKC,OAAO,GAAK7C,EAA0B4C,EAAKE,OAAO,GACzFH,CAAAA,EAAW,CACPje,IAAKke,EAAKC,OAAO,CACjBxe,IAAKue,EAAKE,OAAO,AACrB,CAAA,CAER,KACUT,CAAAA,AAAW,WAAXA,GAAuBZ,CAAgB,GAC7Cld,EAAQmX,KAAK,CAACE,MAAM,EACpB+G,CAAAA,EAAWnB,EAAYuB,cAAc,CAACxe,EAAQmX,KAAK,CAACE,MAAM,CAACoH,KAAK,CAAC,CAACP,EAAkBZ,EAAmBK,EAAM,AAAD,EAMpH,GAHKS,GACDA,CAAAA,EAAWnB,EAAYyB,cAAc,CAACR,EAAkBZ,EAAmBK,EAAM,AAAD,EAEhFD,EAAO,CAGP,IAAK,IADDiB,EAAY,EAAE,CAAEC,EAAYtgB,KAAKugB,KAAK,CAAC1e,EAAM,IAAK2e,EAAYxgB,KAAKygB,IAAI,CAACjf,EAAM,IAAM,EAAGkf,EAAWtB,EAAM7f,MAAM,CACzG8O,EAASiS,EAAWjS,EAASmS,EAAW,EAAEnS,EAC/C,IAAK,IAAIsS,EAAU,EAAGA,EAAUD,EAAU,EAAEC,EAAS,CACjD,IAAI1U,EAAO,GAAKoC,EAAS+Q,CAAK,CAACuB,EAAQ,CACnC1U,GAAQpK,GAAOoK,GAAQzK,GACvB6e,EAAU1Q,IAAI,CAAC1D,EAEvB,CAPJ,IAUI2U,EAAU/C,GAAiBnb,EAC3Bod,EAAU,CAAEje,IAAK,EACjBL,IAAK6e,EAAU9gB,MAAM,CAAG,CAAE,EAC1BkgB,EACAH,AAAY,gBAAZA,GACJ,OAAOe,CAAS,CAACrgB,KAAKgD,KAAK,CAAC4d,GAAS,AACzC,CACA,OAAO/C,GAAiBnb,EAAOod,EAAU,CAAEje,IAAKA,EAAKL,IAAKA,CAAI,EAAGie,EAAYH,AAAY,gBAAZA,EACjF,EAM2D5d,EAASid,EAAaC,EAAmBxB,EAAyB,CACrHvb,IAAK,EAAGL,IAAK,EAAG6d,MAAO,IAAKE,YAAa,SAAUC,OAAQ,OAC/D,EAAIT,GAAY,CAAC,GAAKF,EAAqBG,GAAmBF,EAClE,CAyDA,SAAS+B,GAA4B1I,CAAQ,CAAE7U,CAAY,CAAEsF,CAAe,CAAEjH,CAAO,EACjF,IACImf,EAAoBnf,EAAQof,OAAO,EAAI,CAAC,EACxCxQ,EAAS5O,AAAiB,WAAjBA,EAAQb,IAAI,CACjB,IAn1CyD4N,EAm1CpB,CACjCuB,SAAU+Q,AAJLrf,EAIgBsO,QAAQ,CAC7BF,KAAMiR,AALDrf,EAKYsf,cAAc,AACnC,GACA,IA1kD4D/V,EA0kDpB5H,EAC5CsF,EAAiB,CACT6C,aAAc,CACVD,IAAK,CAAC,CAACsV,EAAiBtV,GAAG,CAC3B+B,QAAS,CAAC,CAACuT,EAAiBvT,OAAO,CACnCC,QAAS,CAAC,CAAEsT,CAAAA,EAAiB1a,QAAQ,EACjC0a,EAAiB3a,OAAO,AAAD,CAC/B,EACAwF,WAAYhK,EAAQuf,UAAU,CAC9B9V,cAAezJ,EAAQwf,QAAQ,AACnC,GACR,OAAOhJ,EAASpB,UAAU,CAACpV,EAAQb,IAAI,EAAI,aAAcyP,EAAQgN,EAAuB5b,EAAQ6O,cAAc,CAAE,CAAA,GACpH,CAKA,SAAS4Q,GAAyB1f,CAAO,CAAEmS,CAAO,CAAEsL,CAAc,CAAER,CAAW,CAAE0C,CAAmB,CAAErC,CAAgB,EAClH,IAAIsC,EAAW,SAAUC,CAAK,CAC1BzC,CAAQ,CACRC,CAAQ,CACRyC,CAAM,EAAI,OAAO9C,GAAuBhd,EACxCid,EACA,CAAA,EAAO,AAAC6C,CAAAA,GAAUrC,CAAa,CAAE,CAACoC,EAAM,CACxCzC,EACAC,EACAC,EAAmB,EACnByC,EAAc,EAAE,CAChBC,EAAY,CACRlZ,aAAc8Y,EAAS,eAAgB,IAAK,CAAEzf,IAAK,GACvDL,IAAK,GAAK,GACNgK,IAAK8V,EAAS,MAAO,EAAG,CAAEzf,IAAK,GACnCL,IAAK,CAAE,GACHiE,OAAQ6b,EAAS,SAAU,EAAG,CAAEzf,IAAK,GACzCL,IAAK,CAAE,EACP,CACA2d,CAAAA,EAAeld,SAAS,EACxByf,CAAAA,EAAUzf,SAAS,CAAGqf,EAAS,YAAa,IAAK,CAAEzf,IAAK,GAAIL,IAAK,GAAK,EAAC,EAEvE2d,EAAehZ,OAAO,GACtBub,EAAUlV,WAAW,CAAG8U,EAAS,YAAa,IAAO,CAAEzf,IAAK,EAAGL,IAAK,GAAM,EAAG2d,EAAehZ,OAAO,EACnGub,EAAUjV,gBAAgB,CAAG6U,EAAS,YAAa,EAAG,CAAEzf,IAAK,GAAIL,IAAK,EAAG,EAAG2d,EAAehZ,OAAO,GAElGgZ,EAAe/Y,QAAQ,GACvBsb,EAAU/U,YAAY,CAAG2U,EAAS,YAAa,IAAO,CAAEzf,IAAK,EAAGL,IAAK,GAAM,EAAG2d,EAAe/Y,QAAQ,EACrGsb,EAAU9U,iBAAiB,CAAG0U,EAAS,YAAa,EAAG,CAAEzf,IAAK,GAAIL,IAAK,EAAG,EAAG2d,EAAe/Y,QAAQ,GAEpG+Y,EAAe5R,OAAO,GACtBmU,EAAUvV,YAAY,CAAGmV,EAAS,QAAS,EAAG,CAAEzf,IAAK,EAAGL,IAAK,EAAI,EAAG2d,EAAe5R,OAAO,EAC1FmU,EAAUtV,YAAY,CAAGkV,EAAS,QAAS,EAAG,CAAEzf,IAAK,EAAGL,IAAK,EAAI,EAAG2d,EAAe5R,OAAO,GAE9F,IAAIoU,EAAkBL,EAAS,kBAAmB,IAAK,CAAEzf,IAAK,GAAIL,IAAK,GAAK,GAAIogB,EAAYN,EAAS,YAAa,EAAG,CAAE9f,IAAK,GAAI,GAC5HqgB,EAAe,SAAUC,CAAO,CAChC1gB,CAAE,EACa,KAAK,IAAZA,GAAiBA,CAAAA,EAAK,CAAA,EAC1B,IAAIuD,EAAOmd,CACXA,CAAAA,EAAQzC,KAAK,EAEc,UAAvB,OAAOyC,EAAQjgB,GAAG,EAClB8C,CAAAA,EAAK9C,GAAG,CAAGsQ,AAroD6CjH,EAsoDnDyC,sBAAsB,CAACmU,EAAQjgB,GAAG,CAAA,EAEhB,UAAvB,OAAOigB,EAAQtgB,GAAG,EAClBmD,CAAAA,EAAKnD,GAAG,CAAG2Q,AAzoD6CjH,EA0oDnDyC,sBAAsB,CAACmU,EAAQtgB,GAAG,CAAA,GAGnB,UAAnB,OAAOsgB,GAAwBrE,EAAiBqE,IACrDnd,CAAAA,EAAOwN,AA9oDqDjH,EA8oDjByC,sBAAsB,CAACmU,EAAO,EAE7EJ,EAAUzV,IAAI,CAAGyS,GAAuBhd,EAASid,EAAa,CAAA,EAAOha,EAAM,GAAI,CAAE9C,IAAK,EAAGL,IAAK,GAAI,EAAGwd,GACjG0C,EAAUzV,IAAI,CAAG,KACboV,GACAK,CAAAA,EAAUzV,IAAI,CAAGjM,KAAKgD,KAAK,CAAC0e,EAAUzV,IAAI,CAAA,EAE9CwV,EAAY9R,IAAI,CAACkE,EAAQlD,QAAQ,CAAC,CAC9BvQ,KAAMsB,EAAQtB,IAAI,CAAGwhB,EAAYD,EAAkBvgB,EACnD0X,aAAcpX,EAAQmX,KAAK,CAC3BjH,uBAAwBxQ,AAAO,KAAK,IAAZA,EACpBgc,EAAyB,CAAC,EAAGsE,GAAaA,CAClD,IAER,EAeA,OAdIvC,EAAe3P,KAAK,EACpB2P,EAAe3P,KAAK,CAACuS,WAAW,GAAKC,MACrC7C,EAAe3P,KAAK,CAACtO,OAAO,CAAC2gB,GAExB1C,EAAe3P,KAAK,CACzBqS,EAAa1C,EAAe3P,KAAK,EAE5B2P,EAAeld,SAAS,EAC7Bwf,EAAY9R,IAAI,CAACkE,EAAQlD,QAAQ,CAAC,CAC9BvQ,KAAMsB,EAAQtB,IAAI,CAAGwhB,EACrB9I,aAAcpX,EAAQmX,KAAK,CAC3BjH,uBAAwB8P,CAC5B,IAEGD,CACX,CAcA,SAASQ,GAAqBvgB,CAAO,CAAEmS,CAAO,CAAEsL,CAAc,CAAER,CAAW,CAAEK,CAAgB,EACzF,IAVoCkD,EAUhCZ,EAAW,SAAUC,CAAK,CAC1BzC,CAAQ,CACRC,CAAQ,EAAI,OAAOL,GAAuBhd,EAC1Cid,EACA,CAAA,EACAQ,CAAc,CAACoC,EAAM,CACrBzC,EACAC,EACAC,EAAmB,EACnB4C,EAAYN,EAAS,YAAa,EAAG,CAAE9f,IAAK,GAAI,GAAIgO,EAAQ8R,EAAS,QAAS,EAAG,CAAEzf,IAAK,GAAKL,IAAK,CAAE,GAAI+N,EAAO+R,EAAS,OAAQ,EAAG,CAAEzf,IAAK,GAAKL,IAAK,CAAE,GAAIiE,EAAS6b,EAAS,SAAU,EAAG,CAAEzf,IAAK,EAAI,GAAIsN,EAlBrMqO,EAAO,AAAwB,YAAxB,OADsB0E,EAmBiN/C,EAAexC,IAAI,EAjBpQuF,EAiBwOxgB,GAhBxOwgB,EAgBwOxgB,EAhBjNA,AAgBiNA,EAhBzMmX,KAAK,EAAInX,AAgBgMA,EAhBxLmX,KAAK,CAACE,MAAM,CAACtC,KAAK,EAiBtE,GAAItH,EACA,OAAO0E,EAAQlD,QAAQ,CAAC,CACpBvQ,KAAMsB,EAAQtB,IAAI,CAAGwhB,EACrB9I,aAAcpX,EAAQmX,KAAK,CAC3BF,cAAe,CACXnJ,MAAOA,EACPD,KAAMA,EACN9J,OAAQA,CACZ,EACA0J,QAASA,CACb,EAER,CA6EA,SAASgT,GAASzgB,CAAO,CAAE0gB,CAAU,CAAEC,CAAa,EAChD,GAAI,AAAsB,YAAtB,OAAOD,EACP,OAAOA,EAAW1gB,GAEtB,GAAI,AAAsB,UAAtB,OAAO0gB,EAAyB,CAChC,IAAI3jB,EAAO2jB,EAAW3jB,IAAI,CACtB8T,EAAMgL,EAAuB7b,EAAQgB,KAAK,CAC1ChB,EAAQmX,KAAK,EAAI8E,EAAkBjc,EAAQmX,KAAK,CAChDpa,IACJ,GAAI,AAAe,UAAf,OAAO8T,EACP,MAAO,CAAA,EAEX,IAAI+P,EAAa,CAAA,EACbC,EAAaH,EAAWG,UAAU,CAClCC,EAAeJ,EAAWI,YAAY,CACtCC,EAAe,AAAyB,UAAzB,OAAOJ,EAEtBC,EADAC,GAAcC,EACDC,GAAiBJ,CAAAA,EAAgBE,GAAchQ,GAAOgQ,GAC/DF,EAAgBG,GAAgBjQ,GAAOiQ,CAAW,EAGzC,AAACD,CAAAA,AAAe,KAAK,IAApBA,GACVE,GAAgBJ,EAAgBE,GAC5BhQ,GAAOgQ,CAAS,GAAOC,CAAAA,AAAiB,KAAK,IAAtBA,GAC3BC,GAAgBJ,EAAgBG,GAC5BjQ,GAAOiQ,CAAW,EAE9B,IAAIhhB,EAAM+b,EAAuB6E,EAAW5gB,GAAG,CAC3CyW,KACApW,EAAM0b,EAAuB6E,EAAWvgB,GAAG,CAAE,CAACoW,KAClD,OAAO1F,GAAO/Q,GAAO+Q,GAAO1Q,GAAOygB,CACvC,CACA,MAAO,CAAA,CACX,CA4P6B,IAAII,GAvPjC,SAA2Bpf,CAAY,CAAEsF,CAAe,CAAE6N,CAAK,EAC3D,IA1hBIkM,EACQC,EACmCC,EACQC,EAAsBC,EAAsBC,EAShGC,EAAYC,EAAqBC,EAgCjCC,EAIAC,EAOCC,EAmeA3hB,EAAU8U,EAAM9U,OAAO,CAAC4hB,YAAY,EAChC,CAAC,EACLC,EAAmB7hB,EAAQ8hB,wBAAwB,CACnDC,EAAoB/hB,EAAQgiB,oBAAoB,CAChDC,EAAwBtG,EAAwB,CAC5CuG,QAAS,CAAA,EACTC,cAAe,GACfC,UAAW,SACXtlB,KAAM,GACV,EACAkD,EAAQqiB,aAAa,EACrBC,EAAetiB,EAAQsiB,YAAY,EAAI,EAAE,CACzCC,EAAsBviB,EAAQuiB,mBAAmB,EAAI,EAAE,CACvDC,EAAexiB,AAAkB,eAAlBA,EAAQyiB,KAAK,CAE5BC,EAAgBrkB,KAAKwB,GAAG,CAAC,GACzBG,EAAQ2iB,QAAQ,CAAG,KACnBC,EAAkB5iB,EAAQ4iB,eAAe,CACzCC,EAAe7iB,EAAQ8O,MAAM,EAAI,CAAC,EAClCkO,GA5iBQiE,EAAsB,AAACD,CAAAA,CAD/BA,EAAalM,AA6iBgBA,EA7iBV9U,OAAO,CAAC4hB,YAAY,EACnC,CAAC,GAAqCE,wBAAwB,EAAI,CAAC,CAAA,EAClE1C,OAAO,EAAI,CAAE3gB,KAAM,IAAKoP,MAAO,GAAI,EAAGqT,EAAuBF,EAAWgB,oBAAoB,EAC7FhB,EAAWgB,oBAAoB,CAAC5C,OAAO,EAAI,CAAC,EAAG+B,EAAkB,EAAE,CAAEC,EAAkB,CAAC,EAAGC,EAAc,SAAUvkB,CAAI,CAAEgmB,CAAQ,EAC7HA,AAAa,OAAbA,GACA3B,CAAe,CAAC2B,EAAS,CACrB3B,CAAe,CAAC2B,EAAS,EAAI,CAAC,EACtC3B,CAAe,CAAC2B,EAAS,CAAChmB,EAAK,CAAG,CAAA,GAGlCskB,CAAe,CAACtkB,EAAK,CAAG,CAAA,CAEhC,EAAGwkB,EAAQ,CAAC,EAAGC,EAAiB,CAAC,EAAGC,EAA0B,SAAU5B,CAAK,CAAEhP,CAAG,CAAEkS,CAAQ,EACxF,IAAIC,EAAqB,SAAUtlB,CAAC,EAAI,MAAQA,AAAgB,MAAhBA,EAAEsgB,MAAM,CAAC,GAAatgB,EAAEugB,KAAK,CAAC,GAAKvgB,CAAI,EACvF,GAAI,AAAe,UAAf,OAAOmT,GAAoBgP,AAAU,SAAVA,EAAkB,CAC7C,GAAIA,AAAU,UAAVA,GAAqB9D,EAAiBlL,GACtC,MAEU,CAAA,SAAVgP,IACA2B,CAAc,CAAC3Q,EAAI,CAAG,CAAA,EACtByQ,EAAYzQ,EAAKkS,IAErBxB,CAAK,CAACyB,EAAmBnS,GAAK,CAAG,CAAA,EACjC,MACJ,CAEA,GAAIoS,AADYpS,GACCoS,AADDpS,EACW8M,KAAK,EAC5B,AAA2B,UAA3B,OAAOsF,AAFKpS,EAEK8M,KAAK,CAAe,CACrC,IAAIA,EAAQqF,EAAmBC,AAHnBpS,EAG6B8M,KAAK,CAChC,CAAA,SAAVkC,GACAyB,EAAY3D,EAAOoF,GAEnBlD,CAAAA,AAAU,SAAVA,GAAoBoD,AAAqB,WAArBA,AAPZpS,EAOsBiN,MAAM,AAAY,GAChD0D,CAAAA,CAAc,CAAC7D,EAAM,CAAG,CAAA,CAAG,EAE/B4D,CAAK,CAAC5D,EAAM,CAAG,CAAA,EACf,MACJ,CACI,CAAC,UAAW,UAAW,WAAW,CAACnK,OAAO,CAACqM,GAAS,IACpD,AAAe,UAAf,OAAOhP,GACPnU,OAAOiU,IAAI,CAACE,GAAKrR,OAAO,CAAC,SAAU0jB,CAAQ,EACvC,OAAOzB,EAAwByB,EAAUrS,CAAG,CAACqS,EAAS,CAAEH,EAC5D,EAER,EAAGrB,EAA6B,SAAUrC,CAAO,CAAE0D,CAAQ,EACvD,AAACrmB,OAAOiU,IAAI,CAAC0O,GAAU7f,OAAO,CAAC,SAAUqgB,CAAK,EAC1C,OAAO4B,EAAwB5B,EAAOR,CAAO,CAACQ,EAAM,CAAEkD,EAC1D,EACJ,EAAGpB,EAA4B,SAAUwB,CAAM,EAAI,OAAOA,EAAO3jB,OAAO,CAAC,SAAU4jB,CAAK,EACpF7B,CAAK,CAAC6B,EAAMC,SAAS,EAAI,IAAI,CACzB7B,CAAc,CAAC4B,EAAMC,SAAS,EAAI,IAAI,CAAG,CAAA,CACjD,EAAI,EACJ3B,EAA2BR,EAAqB,MAChDQ,EAA2BP,EAAsB,MACjDQ,EAA0BV,EAAWuB,mBAAmB,EAAI,EAAE,EAC1DZ,EAAqBllB,OAAOiU,IAAI,CAAC0Q,GAAiBxjB,MAAM,CAC5DkX,AAqfiCA,EArf3BsC,MAAM,CAAC7X,OAAO,CAAC,SAAU6X,CAAM,EACjC,IAAIiM,EAAQjM,EAAOpX,OAAO,CAAC4hB,YAAY,CACvC,GAAIxK,EAAOkM,OAAO,EAAI,CAAED,CAAAA,GAASA,AAAkB,CAAA,IAAlBA,EAAMnB,OAAO,AAAS,IAC/CP,GACAR,CAAAA,CAAe,CAAC/J,EAAOoH,KAAK,CAAC,CAAG7C,EAAwByF,EAAe,EAEvEiC,GAAO,CACP,IAAIE,EAAwB,AAACF,CAAAA,EAAMvB,wBAAwB,EAAI,CAAC,CAAA,EAAG1C,OAAO,CACtEoE,EAAyB,AAACH,CAAAA,EAAMrB,oBAAoB,EAAI,CAAC,CAAA,EAAG5C,OAAO,CACnEmE,GACA9B,EAA2B8B,EAAuBnM,EAAOoH,KAAK,EAE9DgF,GACA/B,EAA2B+B,EAAwBpM,EAAOoH,KAAK,EAEnEkD,EAA0B2B,EAAMI,aAAa,EAAI,EAAE,EACnD,AAACJ,CAAAA,EAAMH,MAAM,EAAI,EAAE,AAAD,EACbjS,MAAM,CAACoS,EAAMI,aAAa,EAAI,EAAE,EAChClkB,OAAO,CAAC,SAAUmkB,CAAS,EACxBA,EAAUtE,OAAO,EACjBqC,EAA2BiC,EAAUtE,OAAO,CAAEhI,EAAOoH,KAAK,CAElE,EACJ,CAER,GACOlD,EAAyB,CAAE6F,gBAAiBA,CAAgB,EAAGwC,AA1I1E,SAAkC7O,CAAK,CAAEwM,CAAK,CAAEC,CAAc,EAwB1D,IAvBA,IAAInK,EAAStC,EAAMsC,MAAM,CACrBwM,EAAWtC,EAAM1jB,MAAM,CACvBimB,EAAiBtC,EAAe3jB,MAAM,CACtCkmB,EAAY,SAAUC,CAAQ,EAC1B,OAAOA,EAASphB,MAAM,CAAC,SAAUqhB,CAAK,CAC1ClnB,CAAI,EAIA,OAHMknB,CAAK,CAAClnB,EAAK,CAAG,CAAEoD,IAAKoW,IAC/BzW,IAAK,CAACyW,GAAS,EAEJ0N,CACX,EAAG,CAAC,EACR,EAAGC,EAAc,SAAUD,CAAK,CAAE9M,CAAK,CAAEpa,CAAI,EACzC,IAAI8T,EAAMsG,CAAK,CAACpa,EAAK,AACT,MAAK,IAAb8T,GACAA,CAAAA,EAAM8K,EAAkB5e,EAAMoa,EAAK,EAEpB,UAAf,OAAOtG,IACPoT,CAAK,CAAClnB,EAAK,CAACoD,GAAG,CAAG7B,KAAK6B,GAAG,CAAC8jB,CAAK,CAAClnB,EAAK,CAACoD,GAAG,CAAE0Q,GAC5CoT,CAAK,CAAClnB,EAAK,CAAC+C,GAAG,CAAGxB,KAAKwB,GAAG,CAACmkB,CAAK,CAAClnB,EAAK,CAAC+C,GAAG,CAAE+Q,GAEpD,EAAG6N,EAAiBqF,EAAUxC,GAC1B5jB,EAAI0Z,EAAOxZ,MAAM,CACjBsmB,EAAoB,AAAI7D,MAAM3iB,GAC3BA,KAAK,CACR,IAAI6gB,EAAiBuF,EAAUvC,GAC3Bve,EAAOoU,CAAM,CAAC1Z,EAAE,CAACsC,OAAO,CAC5B,GAAI,AAACoX,CAAM,CAAC1Z,EAAE,CAAC4lB,OAAO,EAClBtgB,CAAAA,CAAAA,IAAQA,EAAK4e,YAAY,EAAI5e,AAA8B,CAAA,IAA9BA,EAAK4e,YAAY,CAACM,OAAO,AAAS,GAKnE,IAFA,IAAIiC,EAAS/M,CAAM,CAAC1Z,EAAE,CAACymB,MAAM,EAAI,EAAE,CAC/BC,EAAID,EAAOvmB,MAAM,CACdwmB,KAAK,CAER,IADA,IAAIC,EAAIT,EACDS,KACHJ,EAAYxF,EAAgB0F,CAAM,CAACC,EAAE,CAAE9C,CAAK,CAAC+C,EAAE,EAGnD,IADAA,EAAIR,EACGQ,KACHJ,EAAY1F,EAAgB4F,CAAM,CAACC,EAAE,CAAE7C,CAAc,CAAC8C,EAAE,CAEhE,CACAH,CAAiB,CAACxmB,EAAE,CAAG6gB,EAC3B,CACA,MAAO,CACHE,eAAgBA,EAChBF,eAAgB2F,CACpB,CACJ,EAojBqCpP,EA3dqErY,OAAOiU,IAAI,CAAC4Q,GAAQ7kB,OAAOiU,IAAI,CAAC6Q,MA4dlI/K,EAAW,IAtrBmD3B,EAsrBb,CAC7CmB,OAAQ6M,EAAa7M,MAAM,CAC3BR,MAAOqN,EAAarN,KAAK,CACzBsC,OAAQ+K,EAAa/K,MAAM,CAC3B5B,cAAelW,EAAQkW,aAAa,CACpCD,YAAajW,EAAQiW,WAAW,AACpC,EACAnB,EAEAA,CAAAA,EAAM8M,YAAY,EAClB9M,CAAAA,EAAM8M,YAAY,CAAC5E,WAAW,CAAGA,CAAU,EAE/C,IAAIpd,EAAY,EA+MhB,OA9MAkV,EAAMsC,MAAM,CAAC7X,OAAO,CAAC,SAAU6X,CAAM,CAAE0L,CAAQ,EAC3C,IAAIwB,EAAWlN,EAAOpX,OAAO,CAAC4hB,YAAY,EAClC,CAAC,EACT,GAAIxK,EAAOkM,OAAO,EAAIgB,AAAqB,CAAA,IAArBA,EAASpC,OAAO,CAAY,CAC9C,IAkBIqC,EAlBAC,EAAmBhC,EAAeiC,AA9UlD,SAAuCrN,CAAM,CAAEsL,CAAa,CAAE1F,CAAW,CAAE4F,CAAe,EAGtF,IAFI8B,EACAC,EACAC,EAAoBlC,EAChB,AAACtL,CAAAA,EAAOtC,KAAK,CAACsC,MAAM,CAACxZ,MAAM,CAAG,CAAA,EAAKglB,EAW3C,GAVwB5F,EAAYmE,eAAe,CAAC0D,KAAK,CAAC,SAAUC,CAAS,EACrE,IAAIxD,EAAQ7kB,OAAOiU,IAAI,CAACoU,SAC5B,CAAIxD,CAAAA,EAAM1jB,MAAM,CAAG,CAAA,IAGd8mB,GACDA,CAAAA,EAAWpD,CAAK,CAAC,EAAE,AAAD,EAEfoD,IAAapD,CAAK,CAAC,EAAE,CAChC,GACuB,CAEnB,IAAI/C,EAAiBvB,EACZuB,cAAc,CAACnH,EAAOoH,KAAK,CAAC,CAACkG,EAAS,CAM/CC,EAAiBtmB,KAAKgD,KAAK,CAAC0jB,AALRxG,CAAAA,EAAe1e,GAAG,CAAG0e,EAAere,GAAG,AAAD,EACvC8c,EAAYuB,cAAc,CAAC5b,MAAM,CAAC,SAAUqiB,CAAG,CAC9DvnB,CAAC,EAAI,OAAQA,CAAC,CAACinB,EAAS,CACpBM,EAAMvnB,CAAC,CAACinB,EAAS,CAAC7kB,GAAG,CAAGpC,CAAC,CAACinB,EAAS,CAACxkB,GAAG,CACvC8kB,CAAM,EAAG,GAC0CJ,EAC/D,KACK,CAED,IAAIK,EAAc7N,EAAOtC,KAAK,CAACsC,MAAM,CAACzU,MAAM,CAAC,SAAUqiB,CAAG,CACtDvnB,CAAC,EAAI,OAAOunB,EAAMvnB,EAAE0mB,MAAM,CAACvmB,MAAM,AAAE,EAAG,GAC1C+mB,EAAiBtmB,KAAKgD,KAAK,CAAC,AAAC+V,CAAAA,EAAO+M,MAAM,EAAI,EAAE,AAAD,EAAGvmB,MAAM,CAAGqnB,EAAcL,EAC7E,CACA,OAAOvmB,KAAKwB,GAAG,CAAC,GAAI8kB,EACxB,EA6SgFvN,EAChEsL,EACA1F,EACA4F,GAAmBF,EACnBwC,EAA2BvJ,EAAwBkG,EACnDyC,EAASxC,wBAAwB,EACjCqD,EAA4BxJ,EAAwBoG,EACpDuC,EAAStC,oBAAoB,EAC7BoD,EAAyBzJ,EAAwBsG,EACjDqC,EAASjC,aAAa,EACtBgD,EAAa,AAACf,CAAAA,EAASpB,MAAM,EAAI,CAACgC,EAAyB,AAAD,EACrDjU,MAAM,CAACqR,GAEZmB,EAAgB6B,AADG9O,EAAS9E,QAAQ,CAAC9T,MAAM,EACT,CAAC4kB,EAC/B8B,EAASb,aAAa,EAAI,EAAE,CAC5B,AAACa,CAAAA,EAASb,aAAa,EAAI,EAAE,AAAD,EAAGxS,MAAM,CAACsR,GAC1CgD,EAAgB,EAAE,CAItBF,EAAW9lB,OAAO,CAAC,SAAUmkB,CAAS,EAClC,IAAI8B,EAAa7J,EAAwB,CACjC0G,cAAe+C,EACf5F,SAAUkE,EAAUlE,QAAQ,EAAIpI,EAAOhJ,IAAI,AAC/C,EACAsV,AAAmB,WAAnBA,EAAUvkB,IAAI,CACVgmB,EAA4BD,EAChCxB,GACA+B,EAAiBD,EAAWnD,aAAa,CACzC5B,EAAa+E,EAAW/E,UAAU,CAClCiF,EAAsB,SAAUxO,CAAK,EACP,UAAtB,OAAOuJ,GACPA,EAAW3jB,IAAI,EACfynB,CAAAA,EAAkBvI,EAAkB9E,EAC5CuJ,EAAW3jB,IAAI,CAAA,CAEnB,EACIoV,EAAUgN,GAA4B1I,EACtC7U,EACAsF,EACAue,GACA1V,EAAM,SAAUxD,CAAC,EAAI,OAAOiZ,EAAcvX,IAAI,CAAClQ,KAAK,CAACynB,EACrDI,AA9LpB,SAAgC5lB,CAAO,CAAEmS,CAAO,CAAE0T,CAAY,CAAE5I,CAAW,EACvE,IAAI8C,EAAc,EAAE,CACpB,GAAI8F,AAAsB,WAAtBA,EAAazmB,IAAI,EAAiBymB,EAAaxG,OAAO,CAAE,CACxD,IAAIyG,EAAavF,GAAqBvgB,EAClCmS,EACA0T,EAAaxG,OAAO,CACpBpC,GACA6I,GACA/F,CAAAA,EAAc,CAAC+F,EAAW,AAAD,CAEjC,MACSD,EAAaxG,OAAO,EACzBU,CAAAA,EAAcL,GAAyB1f,EAASmS,EAAS0T,EAAaxG,OAAO,CAAEpC,EAAapB,EAAuBgK,EAC9GlG,mBAAmB,CAAE,CAAA,GAAK,EAEnC,OAAOI,CACX,EA8K2CxT,EACvB4F,EACAsT,EACAxI,GAAe,EAEf8I,EAAa,EAAE,CACfC,EAAiB,EACjBC,EAAuB,SAAUC,CAAa,EAC1C,GAAIH,AAAsB,IAAtBA,EAAWloB,MAAM,CACjBkS,EAAI,CACAoH,MAAO4O,CAAU,CAAC,EAAE,CAAC5O,KAAK,CAC1BzY,KAAMsnB,EAAiBE,EAAgB,CAC3C,OAEH,CACD,IAAI9B,EAAS+B,AAxLrC,SAA0BT,CAAc,CAAEtB,CAAM,EAC5C,IAAIgC,EAAMV,EAAerD,SAAS,EAAI,SAClCgE,EAAI,SAAU3mB,CAAE,EAAI,OAAQ0kB,CAAM,CAAC1kB,EAAG,CAAG,CAAC0kB,CAAM,CAAC1kB,EAAG,CAACyX,KAAK,CAAC,CAAG,EAAE,AAAG,EACvE,GAAIiP,AAAQ,UAARA,EACA,OAAOC,EAAE,GAEb,GAAID,AAAQ,SAARA,EACA,OAAOC,EAAEjC,EAAOvmB,MAAM,CAAG,GAE7B,GAAIuoB,AAAQ,WAARA,EACA,OAAOC,EAAEjC,EAAOvmB,MAAM,EAAI,GAE9B,GAAIuoB,AAAQ,cAARA,EACA,OAAOC,EAAE,GAAGnV,MAAM,CAACmV,EAAEjC,EAAOvmB,MAAM,CAAG,IAEzC,GAAIuoB,AAAQ,WAARA,EAAkB,CAClB,IACIE,EACAC,EACAC,EACAC,EAJAC,EAAShB,EAAe3oB,IAAI,EAAI,IAoBpC,GAfAqnB,EAAO5kB,OAAO,CAAC,SAAU1B,CAAC,EACtB,IAAI+S,EAAMoL,EAAkBne,EAAEqZ,KAAK,CAC/BuP,EACQ,MAAK,IAAb7V,IAGA,CAAA,CAACyV,GAASzV,EAAM2V,CAAO,IACvBF,EAAQxoB,EACR0oB,EAAW3V,GAEX,CAAA,CAAC0V,GAAS1V,EAAM4V,CAAO,IACvBF,EAAQzoB,EACR2oB,EAAW5V,GAEnB,GACIyV,GAASC,SACT,AAAID,EAAMnP,KAAK,GAAKoP,EAAMpP,KAAK,CACpB,CAACmP,EAAMnP,KAAK,CAAC,CAEjBmP,EAAM5nB,IAAI,CAAG6nB,EAAM7nB,IAAI,CAC1B,CAAC6nB,EAAMpP,KAAK,CAAEmP,EAAMnP,KAAK,CAAC,CAC1B,CAACmP,EAAMnP,KAAK,CAAEoP,EAAMpP,KAAK,CAAC,AAEtC,CACA,MAAO,EAAE,AACb,EA0IsDuO,EAC1BK,GACAY,EAAMT,EAAgB9B,EAAOvmB,MAAM,CACvCumB,EAAO5kB,OAAO,CAAC,SAAU1B,CAAC,CAAE4B,CAAE,EAAI,OAAOqQ,EAAI,CACzCoH,MAAOrZ,EACPY,KAAMsnB,EAAiBW,EAAM,EAAIA,EAAMjnB,CAC3C,EAAI,EACR,CACAqmB,EAAa,EAAE,AACnB,EACA,AAAC1O,CAAAA,EAAO+M,MAAM,EAAI,EAAE,AAAD,EAAG5kB,OAAO,CAAC,SAAU2X,CAAK,CAAEyP,CAAO,EAClD,IAhaS/mB,EAgaLgnB,EAAcD,IAAYvP,EAAO+M,MAAM,CAACvmB,MAAM,CAAG,EACjDa,GAjaKmB,EAkaLA,EA1ZbnB,AAPIse,GAAuB,CAAE7F,MAgaIA,EA/ZpCzY,KAAM,CAAE,EAmaQue,EACAwF,EAFAgD,EAAWpG,OAAO,EAAIoG,EAAWpG,OAAO,CAAC3gB,IAAI,EAAI,EA/Z7C,EAAG,CAAEyB,IAAK,EAC9BL,IA6ZgB2kB,EA5ZhB9G,MAAO,GAAI,GACD9d,GA+ZMG,EAAU,CAAEmX,MAAOA,EACnBzY,KAAMA,CAAK,EAEf,GAAI,CAAC+mB,EAAWpG,OAAO,EACnB,CAACoB,GAASzgB,EAAS0gB,EAAY8D,GAAkB,CACjDmB,EAAoBxO,GAEhB0P,GAAed,EAAWloB,MAAM,EAChCooB,EAAqBF,CAAU,CAACA,EAAWloB,MAAM,CAAG,EAAE,CAACa,IAAI,CACvDqnB,CAAU,CAAC,EAAE,CAACrnB,IAAI,EAE1B,MACJ,CAGA,GAFAinB,EAAoBxO,GAEfuO,EAAevD,OAAO,CAGtB,CACD,IAAI2E,EAAKpoB,EAAOsnB,EACZe,EAAYrB,EAAetD,aAAa,CACxC4E,EAAWH,GACPC,GAAMC,EAAYD,EAAKC,CAC3BF,CAAAA,GAAeC,EAAKC,GAChBD,GAAMC,GAENhB,EAAW9X,IAAI,CAACjO,GAEpBimB,EAAqBe,GACrBhB,EAAiB1nB,KAAKugB,KAAK,CAACngB,EAAOqoB,GAC/BA,EACAF,GAAeC,EAAKC,EACpBhX,EAAI,CACAoH,MAAOnX,EAAQmX,KAAK,CACpBzY,KAAMsnB,EAAiBgB,EAAW,CACtC,GAGAjB,EAAa,CAAC/lB,EAAQ,EAI1B+lB,EAAW9X,IAAI,CAACjO,EAExB,MA5BI+P,EAAI/P,EA6BZ,EACJ,GAEA,IAAIinB,EAAazB,EAAc5iB,MAAM,CAAC,SAAUwX,CAAK,CACjD5N,CAAC,EAAI,OAAQA,EAAE9N,IAAI,CAAG0b,EAAM1b,IAAI,CAAG8N,EAAI4N,CAAQ,EAAG,CAAE1b,KAAM6X,GAAS,GACnEpH,EAAYqW,EAAc5iB,MAAM,CAAC,SAAUyX,CAAI,CAC/C7N,CAAC,EAAI,OAAQA,EAAE9N,IAAI,CAAG2b,EAAK3b,IAAI,CAAG8N,EAAI6N,CAAO,EAAG,CAAE3b,KAAM,CAAC6X,GAAS,EACtE0Q,CAAAA,EAAW1P,QAAQ,CAAGuL,EAAaoE,aAAa,CAC5CpE,EAAaoE,aAAa,CAAC5Z,IAAI,CAAC,KAAM,CAAE+J,OAAQA,EAAQZ,SAAUA,CAAS,GAC3E,KAAK,EACTtH,EAAUoI,QAAQ,CAAGuL,EAAaqE,WAAW,CACzCrE,EAAaqE,WAAW,CAAC7Z,IAAI,CAAC,KAAM,CAAE+J,OAAQA,EAAQZ,SAAUA,CAAS,GACzE,KAAK,EAETiN,EAAclkB,OAAO,CAAC,SAAUmkB,CAAS,EACrC,IAAI8B,EAAa9B,AAAmB,WAAnBA,EAAUvkB,IAAI,CACvBwc,EAAwBoG,EAC5B2B,GACI/H,EAAwBkG,EAAkB,CACtCzC,QAAS,CAAEvR,MAAO,CAAE6P,MAAO,OAAQ,CAAE,CACzC,EACJgG,GACAyD,EAAiBjI,GAA4B1I,EAC7C7U,EACAsF,EACAue,GACJjB,EAAkB,KAAK,EACvB,IAAI6C,EAAe5B,EAAW4B,YAAY,CACtCC,EAAgB7B,EAAW6B,aAAa,CACxCjE,EAAYoC,EAAWpC,SAAS,EAAI,IACpC3C,EAAa+E,EAAW/E,UAAU,CAClC6G,EAAkBtK,EACbuB,cAAc,CAACuE,EAAS,CAACM,EAAU,CACxCmE,EAAkB,SAAU9oB,CAAI,CAChCsC,CAAK,EACD,GAAI,CAACykB,EAAWpG,OAAO,EACnB,CAACoB,GAAS,CAAE/hB,KAAMA,EAC1BsC,MAAOA,CAAM,EACb,AAAsB,UAAtB,OAAO0f,EACKhF,EAAyB,CAAE3e,KAAMsmB,CAAU,EACvD3C,GACYA,EACZ8D,GAAkB,CACVA,EAAkBxjB,EACtB,MACJ,CACAwjB,EAAkBxjB,EACdykB,AAAoB,WAApBA,EAAWrmB,IAAI,CACfmhB,GAAqB,CAAE7hB,KAAMA,EAAMsC,MAAOA,CAAM,EAAGomB,EAAgB3B,EAAWpG,OAAO,CAAEpC,EAAaoG,GAGpG3D,GAAyB,CAAEhhB,KAAMA,EAAMsC,MAAOA,CAAM,EAAGomB,EAAgB3B,EAAWpG,OAAO,CAAEpC,EAAapB,EAAuB4J,EAAW9F,mBAAmB,CAAE,CAAA,GAAO0D,EAE9K,EACA,GAAIgE,EAEA,IADA,IAAI3oB,EAAO,EACJA,GAAQ+lB,GAAkB,CAC7B,IAAI5T,EAAMsL,GAAiBzd,EAAM,CAAEyB,IAAK,EACpCL,IAAK2kB,CAAiB,EACtB8C,GACJC,EAAgB9oB,EAAOmB,EAAWgR,GAClCnS,GAAQ2oB,CACZ,CAEJ,GAAIC,EAEA,IADA,IAAIzW,EAAM0W,EAAgBpnB,GAAG,CACtB0Q,GAAO0W,EAAgBznB,GAAG,EAAE,CAC/B,IAAIpB,EAAOyd,GAAiBtL,EACxB0W,EAAiB,CAAEpnB,IAAK,EACxBL,IAAK2kB,CAAiB,EACtB,CAAA,EACAgB,AAAgC,gBAAhCA,EAAWgC,gBAAgB,EAC/BD,EAAgB9oB,EAAOmB,EAAWgR,GAClCA,GAAOyW,CACX,CAER,GACI7E,GACA5iB,CAAAA,GAAa4kB,EAAmB5B,CAAc,CAEtD,CACJ,GACOpM,CACX,EA2BIiR,GAAiB,AAACpqB,IAA+EoqB,cAAc,CAAEC,GAAa,AAACrqB,IAA+EqqB,UAAU,CAExN1Y,GAAW,AAAC3R,IAA+E2R,QAAQ,CAAE2Y,GAAsB,AAACtqB,IAA+EiM,MAAM,CAAEse,GAAY,AAACvqB,IAA+EuqB,SAAS,CAAEC,GAAqB,AAACxqB,IAA+EuX,KAAK,CAAEkT,GAAoB,AAACzqB,IAA+EY,IAAI,CAE9gB8pB,GAAmB,AAAC1qB,IAA+EgV,GAAG,CAAE2V,GAAmB,AAAC3qB,IAA+E+U,GAAG,CAwB9M6V,GAA8B,WAC9B,SAASA,EAAanT,CAAK,EACvB,IAAI,CAACA,KAAK,CAAGA,EACb,IAAI,CAACoT,mBAAmB,CAAG,EAC3B,IAAI,CAACC,UAAU,CAAG,EAClB,IAAI,CAACC,aAAa,CAAGpZ,GAAS+Y,GAAkB,UAAW,SAAUxb,CAAC,EAC9DuI,GAASA,EAAM8M,YAAY,EAC1BrV,CAAAA,AAAU,QAAVA,EAAEhQ,GAAG,EAAcgQ,AAAU,WAAVA,EAAEhQ,GAAG,AAAY,GACrCuY,EAAM8M,YAAY,CAAC1W,MAAM,EAEjC,GACA,GAAI,CACA,IAAI,CAACvJ,YAAY,CAAG,IAAIqmB,GAAiBK,YAAY,CAErD,IAAI,CAAC1mB,YAAY,CAAC2mB,OAAO,GACzB,IAAI,CAACC,gBAAgB,CAAG,IAAI,CAAC5mB,YAAY,CAACV,WAAW,AACzD,CACA,MAAOsL,EAAG,CAAe,CAC7B,CAsYA,OA9XA0b,EAAalrB,SAAS,CAACyrB,mBAAmB,CAAG,SAAUD,CAAgB,EACnE,IAAI,CAACA,gBAAgB,CAAGA,EACxB,IAAI,CAACE,MAAM,EACf,EAMAR,EAAalrB,SAAS,CAACiY,SAAS,CAAG,WAC/B,MAAO,CAAC,CAAC,IAAI,CAACwB,QAAQ,EAAI,IAAI,CAACA,QAAQ,CAACxB,SAAS,AACrD,EAYAiT,EAAalrB,SAAS,CAACid,WAAW,CAAG,SAAUC,CAAO,CAAEzE,CAAK,EACpD,IAAI,CAACkT,KAAK,CAAC,IAAI,CAAC1O,WAAW,CAAC3M,IAAI,CAAC,IAAI,CAAE4M,EAASzE,KAGjD,IAAI,CAACgB,QAAQ,EACb,IAAI,CAACA,QAAQ,CAACwD,WAAW,CAACC,EAASzE,EAE3C,EAcAyS,EAAalrB,SAAS,CAACgc,YAAY,CAAG,SAAUC,CAAI,CAAExD,CAAK,CAAEoD,CAAW,EACpE,IAAIrT,EAAQ,IAAI,CAChB,GAAK,IAAI,CAACmjB,KAAK,CAAC,IAAI,CAAC3P,YAAY,CAAC1L,IAAI,CAAC,IAAI,CAAE2L,EAAMxD,EAAOoD,KAGtD,IAAI,CAACpC,QAAQ,CAAE,CACf,IAAIxT,EAAO,IAAI,CAAC8R,KAAK,CAAC9U,OAAO,CAAC4hB,YAAY,CACtC+G,EAAQ3lB,GAAQA,EAAK8L,MAAM,EAAI9L,EAAK8L,MAAM,CAACmK,aAAa,CACvD0P,GACD,IAAI,CAACC,sBAAsB,GAE/B,IAAI,CAACpS,QAAQ,CAACuC,YAAY,CAACC,EAAMxD,EAAOmT,GAAU,WAC9CpjB,EAAMsjB,kBAAkB,EAC5B,EAAIjQ,EACR,CACJ,EAiBAqP,EAAalrB,SAAS,CAAC+rB,kBAAkB,CAAG,SAAU9P,CAAI,CAAElc,CAAI,CAAE0Y,CAAK,EACtD,KAAK,IAAd1Y,GAAmBA,CAAAA,EAAO,GAAE,EAChC,IAAIisB,EAAa,IAAI,CAACtO,kBAAkB,GACxC,GAAIsO,EAAY,CACZ,IAAIC,EAAmBD,EAAW3R,MAAM,CAACoH,KAAK,CAAIxF,CAAAA,EAAO,EAAI,EAAC,EAG9D,OAFA,IAAI,CAACiQ,iBAAiB,CAACnsB,EAAMisB,CAAU,CAACjsB,EAAK,CAAE,SAAUyP,CAAC,EAAI,MAAO,CAAC,CAACA,EAAE4K,YAAY,EACjF5K,EAAE4K,YAAY,CAACC,MAAM,CAACoH,KAAK,GAAKwK,CAAkB,EAAGxT,GAClD,IAAI,CAACV,KAAK,CAACsC,MAAM,CAAC4R,EAAiB,EAAI,IAClD,CACA,OAAO,IACX,EAYAf,EAAalrB,SAAS,CAACksB,iBAAiB,CAAG,SAAUnsB,CAAI,CAAEosB,CAAW,CAAEC,CAAY,CAAE3T,CAAK,EACvF,IAAIjQ,EAAQ,IAAI,CAChB,GAAK,IAAI,CAACmjB,KAAK,CAAC,IAAI,CAACO,iBAAiB,CAAC5b,IAAI,CAAC,IAAI,CAAEvQ,EAAMosB,EAAaC,EAAc3T,KAG/E,IAAI,CAACgB,QAAQ,CAAE,CACf,IAAIxT,EAAO,IAAI,CAAC8R,KAAK,CAAC9U,OAAO,CAAC4hB,YAAY,CACtC+G,EAAQ3lB,GAAQA,EAAK8L,MAAM,EAAI9L,EAAK8L,MAAM,CAACmK,aAAa,CACvD0P,GACD,IAAI,CAACC,sBAAsB,GAE/B,IAAI,CAACpS,QAAQ,CAACgD,sBAAsB,CAAC1c,EAAMosB,EAAa1T,EAAOmT,GAAU,WACrE,OAAOpjB,EAAMsjB,kBAAkB,EACnC,EAAIM,EACR,CACJ,EAUAlB,EAAalrB,SAAS,CAAC0d,kBAAkB,CAAG,kBACxC,AAAI,IAAI,CAACjE,QAAQ,CACN,IAAI,CAACA,QAAQ,CAACiE,kBAAkB,GAEpC,IACX,EAgBAwN,EAAalrB,SAAS,CAACqsB,QAAQ,CAAG,SAAU7J,CAAU,CAAEvf,CAAO,CAAEqpB,CAAO,EAEpE,GADgB,KAAK,IAAjBA,GAAsBA,CAAAA,EAAU,CAAA,EAC/B,IAAI,CAACX,KAAK,CAAC,IAAI,CAACU,QAAQ,CAAC/b,IAAI,CAAC,IAAI,CAAEkS,EAAYvf,KAGrD,IAAI2iB,EAAW3iB,EAAQ6G,YAAY,CAAG7G,EAAQ6G,YAAY,EAAI,IAC1DyiB,EAAQ,IA5xEoD/f,EA4xEZ,IAAI,CAAC5H,YAAY,CACjE,IAAI,CAAC4mB,gBAAgB,CAAE,CACnBve,WAAYuV,EACZzV,aAAc,CACV+B,QAAS,CAAA,EACTD,QAAS,CAAA,EACT/B,IAAK,CAAA,CACT,CACJ,GACJyf,EAAMnf,mBAAmB,CAACkf,EAAU,IAAMrpB,GAC1CiO,WAAW,WAAc,OAAOqb,GAASA,EAAMje,OAAO,EAAI,EAAGge,EAAU1G,EAAW,KACtF,EAcAsF,EAAalrB,SAAS,CAAC+Q,KAAK,CAAG,SAAUkN,CAAI,CAAEuO,CAAc,CAAEF,CAAO,EAClD,KAAK,IAAjBA,GAAsBA,CAAAA,EAAU,CAAA,EAOpCG,AANc,IApkE+Czc,EAokEV8a,GAAmB,CAC9DvZ,SAAU,QACVV,KAAM,IACN9J,OAAQ,EACZ,EACAylB,GAAkB,CAAC,IACfxb,SAAS,CAACsb,EAASrO,EAC/B,EAKAiN,EAAalrB,SAAS,CAACmO,MAAM,CAAG,WACxB,IAAI,CAACsL,QAAQ,EACb,IAAI,CAACA,QAAQ,CAACtL,MAAM,GAExB0c,GAAU,IAAI,CAAE,SACpB,EAKAK,EAAalrB,SAAS,CAAC+d,YAAY,CAAG,WAC7B,IAAI,CAAC4N,KAAK,CAAC,IAAI,CAAC5N,YAAY,CAACzN,IAAI,CAAC,IAAI,IAGvC,IAAI,CAACmJ,QAAQ,GACb,IAAI,CAACA,QAAQ,CAACoE,KAAK,GACnB,IAAI,CAACpE,QAAQ,CAACsE,YAAY,GAElC,EAKAmN,EAAalrB,SAAS,CAAC0sB,WAAW,CAAG,SAAUlU,CAAU,CAAEC,CAAK,EACvD,IAAI,CAACkT,KAAK,CAAC,IAAI,CAACe,WAAW,CAACpc,IAAI,CAAC,IAAI,CAAEkI,EAAYC,KAGpD,IAAI,CAACgB,QAAQ,GACb,IAAI,CAACA,QAAQ,CAACoE,KAAK,GACnB,IAAI,CAAC8O,UAAU,GACf,IAAI,CAAClT,QAAQ,CAACnB,IAAI,CAAC,KAAK,EAAG,KAAK,EAAGE,EAAYC,GAEvD,EAKAyS,EAAalrB,SAAS,CAAC4sB,YAAY,CAAG,SAAUvS,CAAM,CAAE7B,CAAU,CAAEC,CAAK,EAChE,IAAI,CAACkT,KAAK,CAAC,IAAI,CAACiB,YAAY,CAACtc,IAAI,CAAC,IAAI,CAAE+J,EAAQ7B,EAAYC,KAG7D,IAAI,CAACgB,QAAQ,GACb,IAAI,CAACA,QAAQ,CAACoE,KAAK,GACnB,IAAI,CAAC8O,UAAU,GACf,IAAI,CAAClT,QAAQ,CAACnB,IAAI,CAAC,SAAU9I,CAAC,EAC1B,MAAO,CAAC,CAACA,EAAE4K,YAAY,EAAI5K,EAAE4K,YAAY,CAACC,MAAM,GAAKA,CACzD,EAAG,KAAK,EAAG7B,EAAYC,GAE/B,EAKAyS,EAAalrB,SAAS,CAAC6sB,WAAW,CAAG,SAAU1S,CAAK,CAAE1B,CAAK,EAClD,IAAI,CAACkT,KAAK,CAAC,IAAI,CAACkB,WAAW,CAACvc,IAAI,CAAC,IAAI,CAAE6J,EAAO1B,KAG/C,IAAI,CAACgB,QAAQ,GACb,IAAI,CAACA,QAAQ,CAACoE,KAAK,GACnB,IAAI,CAAC8O,UAAU,GACf,IAAI,CAAClT,QAAQ,CAACmC,gBAAgB,CAAC,SAAUpM,CAAC,EAAI,OAAOA,EAAE4K,YAAY,GAAKD,CAAO,EAAG1B,GAE1F,EAMAyS,EAAalrB,SAAS,CAACmN,eAAe,CAAG,SAAUxL,CAAG,EAC9C,IAAI,CAAC8X,QAAQ,EACb,IAAI,CAACA,QAAQ,CAACtM,eAAe,CAACxL,EAEtC,EAKAupB,EAAalrB,SAAS,CAACsO,OAAO,CAAG,WAC7B,IAAI,CAAC+c,aAAa,GACd,IAAI,CAAC5R,QAAQ,GACb,IAAI,CAACA,QAAQ,CAACnL,OAAO,GACrB,OAAO,IAAI,CAACmL,QAAQ,EAEpB,IAAI,CAACqT,kBAAkB,EACvB,IAAI,CAACA,kBAAkB,CAACpoB,IAAI,GAE5B,IAAI,CAACE,YAAY,GAEjB,IAAI,CAACA,YAAY,CAACmoB,KAAK,GACvB,OAAO,IAAI,CAACnoB,YAAY,CAEhC,EAQAsmB,EAAalrB,SAAS,CAAC0rB,MAAM,CAAG,WAC5B,IAAIpF,EAAQ,IAAI,CAACvO,KAAK,CAAC9U,OAAO,EAAI,IAAI,CAAC8U,KAAK,CAAC9U,OAAO,CAAC4hB,YAAY,CACjE,GAAI,AAAC,IAAI,CAAC8G,KAAK,CAAC,IAAI,CAACD,MAAM,CAACpb,IAAI,CAAC,IAAI,IAAOgW,GAI5C,IAAIvN,EAAMD,KAAKC,GAAG,GACdiU,EAAiB1G,EAAM0G,cAAc,CACzC,GAAIjU,EAAM,IAAI,CAACqS,UAAU,CAAG4B,GAAkB,CAAC,IAAI,CAACC,UAAU,CAAE,CAC5D9b,aAAa,IAAI,CAAC+b,eAAe,EACjC,IAAI,CAACA,eAAe,CAAGhc,WAAW,IAAI,CAACwa,MAAM,CAACpb,IAAI,CAAC,IAAI,EAAG0c,EAAiB,GAC3E,MACJ,CACA,IAAIjb,EAASuU,EAAMvU,MAAM,EAAI,CAAC,EAQ9B,GAPIA,EAAOob,YAAY,EACnBpb,EAAOob,YAAY,CAAC,CAAEpV,MAAO,IAAI,CAACA,KAAK,CAAE0B,SAAU,IAAI,CAACA,QAAQ,AAAC,GAErE,IAAI,CAAC2R,UAAU,CAAGrS,EACd,IAAI,CAACU,QAAQ,EACb,IAAI,CAACA,QAAQ,CAACnL,OAAO,GAErB,IAAI,CAAC1J,YAAY,EAAI,IAAI,CAAC4mB,gBAAgB,CAAE,CAC5C,IAAI,CAAC/R,QAAQ,CAAGuK,GAAkB,IAAI,CAACpf,YAAY,CAAE,IAAI,CAAC4mB,gBAAgB,CAAE,IAAI,CAACzT,KAAK,EACtF,IAAIqV,EAAU,IAAI,CAACrV,KAAK,CAAC9U,OAAO,CAAC4hB,YAAY,CAC7C,IAAI,CAACpL,QAAQ,CAACtM,eAAe,CAAC4d,GAAkBqC,GAAWA,EAAQnjB,YAAY,CAAE,GACrF,CACI8H,EAAOsb,WAAW,EAClBtb,EAAOsb,WAAW,CAAC,CAAEtV,MAAO,IAAI,CAACA,KAAK,CAAE0B,SAAU,IAAI,CAACA,QAAQ,AAAC,GAExE,EAMAyR,EAAalrB,SAAS,CAAC2rB,KAAK,CAAG,SAAU2B,CAAS,EAC9C,IAAI9kB,EAAQ,IAAI,OAChB,EAAK,IAAI,CAAC5D,YAAY,IACjB,IAAI,CAAC4mB,gBAAgB,IACrB,IAAI,CAACzT,KAAK,CAAC9U,OAAO,EACnB,CAAA,CAAA,IAAI,CAAC8U,KAAK,CAAC9U,OAAO,CAAC4hB,YAAY,EAC3B,AAA4C,CAAA,IAA5C,IAAI,CAAC9M,KAAK,CAAC9U,OAAO,CAAC4hB,YAAY,CAACM,OAAO,AAAS,IAGpD,AAA4B,cAA5B,IAAI,CAACvgB,YAAY,CAAC2oB,KAAK,EAAqB,IAAI,CAACN,UAAU,EAe/D,IAAI,CAAC9B,mBAAmB,CAAG,EACpB,CAAA,IAfC,IAAI,CAACA,mBAAmB,GAAK,IAC7Bja,WAAW,WACH1I,EAAM5D,YAAY,EAClB4D,AAA6B,cAA7BA,EAAM5D,YAAY,CAAC2oB,KAAK,CAExB/kB,EAAM5D,YAAY,CAAC8W,MAAM,GAAG8R,IAAI,CAACF,GAGjCA,GAER,EAAG,GAEA,CAAA,GAIf,EAKApC,EAAalrB,SAAS,CAAC2sB,UAAU,CAAG,WAChC,IAAI1mB,EAAO,IAAI,CAAC8R,KAAK,CAAC9U,OAAO,CAAC4hB,YAAY,CACtC8H,EAAa1mB,GAAQA,EAAK8L,MAAM,EAAI9L,EAAK8L,MAAM,CAAC4a,UAAU,CAC1DA,GACAA,EAAW,CAAE5U,MAAO,IAAI,CAACA,KAAK,CAAE0B,SAAU,IAAI,CAACA,QAAQ,AAAC,EAEhE,EAKAyR,EAAalrB,SAAS,CAAC6rB,sBAAsB,CAAG,WACvC,IAAI,CAACiB,kBAAkB,GACxB,IAAI,CAACA,kBAAkB,CAAG,IA3xHsB/qB,EA2xHM,IAAI,CAAC6C,YAAY,CAAEkmB,GAAmB5d,AA/wFrCtC,EA+wFoEkB,IAAI,CAAE,CAAE7B,aAAc,EAAI,IACrJ,IAAI,CAAC6iB,kBAAkB,CAACxjB,aAAa,GACrC,IAAI,CAACwjB,kBAAkB,CAAC7oB,OAAO,CAAC,IAAI,CAACunB,gBAAgB,EAE7D,EAKAN,EAAalrB,SAAS,CAAC8rB,kBAAkB,CAAG,WACpC,IAAI,CAACgB,kBAAkB,GACvB,IAAI,CAACA,kBAAkB,CAACjjB,cAAc,CAAC,GAAK,EAAG,KAC/C,IAAI,CAACijB,kBAAkB,CAACjjB,cAAc,CAAC,GAAK,EAAG,KAEvD,EACOqhB,CACX,KACA,AAAC,SAAUA,CAAY,EACnB,IAAIuC,EAAkB,EAAE,CAKxB,SAASC,IACL,IAAI7I,EAAe,IAAI,CAACA,YAAY,CAChC0C,EAAW,IAAI,CAACtkB,OAAO,EAAI,IAAI,CAACA,OAAO,CAAC4hB,YAAY,AACpD0C,CAAAA,GAAYA,EAASpC,OAAO,CACxBN,EACAA,EAAa6G,MAAM,IAGnB,IAAI,CAAC7G,YAAY,CAAG,IAAIqG,EAAa,IAAI,EACzC,IAAI,CAACrG,YAAY,CAAC6G,MAAM,IAGvB7G,IACLA,EAAavW,OAAO,GACpB,OAAO,IAAI,CAACuW,YAAY,CAEhC,CAKA,SAAS8I,IACD,IAAI,EAAI,IAAI,CAAC9I,YAAY,EACzB,IAAI,CAACA,YAAY,CAACvW,OAAO,EAEjC,CAKA,SAASsf,IACD,IAAI,CAACF,yBAAyB,EAC9B,IAAI,CAACA,yBAAyB,EAEtC,CAKA,SAASG,EAAcre,CAAC,EACpB,IAAIse,EAAate,EAAEvM,OAAO,CAAC4hB,YAAY,CACnCiJ,IACAhD,GAAmB,CAAA,EAAM,IAAI,CAAC7nB,OAAO,CAAC4hB,YAAY,CAAEiJ,GACpDF,EAAc1tB,IAAI,CAAC,IAAI,EAE/B,CAuEAgrB,EAAa6C,OAAO,CAlEpB,SAAiBC,CAAU,CAAEC,CAAW,CAAEC,CAAU,EAEJ,KAAxCT,EAAgBjX,OAAO,CAACwX,KACxBP,EAAgBxc,IAAI,CAAC+c,GACrBpD,GAAoBoD,EAAWhuB,SAAS,CAAE,CACtC0tB,0BAA2BA,EAC3BS,OAAQ,SAAU1V,CAAK,EACf,IAAI,CAACoM,YAAY,EACjB,IAAI,CAACA,YAAY,CAAC6H,WAAW,CAAC,CAAA,EAAOjU,EAE7C,EACA2V,aAAc,SAAUvQ,CAAK,CAAEpF,CAAK,EAEhC,GADc,KAAK,IAAfoF,GAAoBA,CAAAA,EAAQ,CAAA,CAAG,EAC9B,IAAI,CAACgH,YAAY,EAGtB,IAAIpL,EAAW,IAAI,CAACoL,YAAY,CAACpL,QAAQ,AACrCwR,CAAAA,GAAiB9a,eAAe,EAChC8a,GAAiB9a,eAAe,CAAChC,MAAM,GAEvCsL,GAAY,IAAI,CAACoL,YAAY,CAAC5M,SAAS,GACnC4F,EACA,IAAI,CAACgH,YAAY,CAAC1W,MAAM,GAGxBsL,EAAS+B,KAAK,GAGb/B,GAAYA,EAASzB,QAAQ,CAClCyB,EAASiC,MAAM,GAGf,IAAI,CAACmJ,YAAY,CAAC6H,WAAW,CAAC7O,EAAOpF,GAE7C,CACJ,GACAxG,GAAS+b,EAAY,UAAWL,GAChC1b,GAAS+b,EAAY,SAAUJ,GAC/B3b,GAAS+b,EAAY,SAAUH,IAGU,KAAzCJ,EAAgBjX,OAAO,CAACyX,KACxBR,EAAgBxc,IAAI,CAACgd,GACrBA,EAAYjuB,SAAS,CAACmuB,MAAM,CAAG,SAAU1V,CAAK,EACtC,IAAI,CAACV,KAAK,CAAC8M,YAAY,EACvB,IAAI,CAAC9M,KAAK,CAAC8M,YAAY,CAAC+H,YAAY,CAAC,IAAI,CAAE,CAAA,EAAOnU,EAE1D,GAGwC,KAAxCgV,EAAgBjX,OAAO,CAAC0X,KACxBT,EAAgBxc,IAAI,CAACid,GACrBA,EAAWluB,SAAS,CAACmuB,MAAM,CAAG,SAAU1V,CAAK,EACrC,IAAI,CAAC4B,MAAM,CAACtC,KAAK,CAAC8M,YAAY,EAC9B,IAAI,CAACxK,MAAM,CAACtC,KAAK,CAAC8M,YAAY,CAACgI,WAAW,CAAC,IAAI,CAAEpU,EAEzD,GAGJ,IAAI4V,EAAmB1D,KAAa2D,SAAS,CACzCD,GACAA,EAAiBE,OAAO,EACxBF,EAAiBE,OAAO,CAACC,aAAa,CAACC,SAAS,EAChDJ,EAAiBE,OAAO,CAACC,aAAa,CAACC,SAAS,CAACxd,IAAI,CAAC,YAAa,eAAgB,cAE3F,CAEJ,EAAGia,IAAiBA,CAAAA,GAAe,CAAC,CAAA,GAEpCJ,GAAmB,CAAA,EAAMJ,GAx+KX,CAmBV7F,aAAc,CAuLVM,QAAS,CAAA,EAITS,SAAU,IAUVC,gBAAiB,IAMjBmH,eAAgB,IAIhB/iB,aAAc,GAWdyb,MAAO,aAYPxM,YAAa,CAAA,EAabC,cAAe,CAAA,EASfmM,cAAe,CAIXH,QAAS,CAAA,EAKTC,cAAe,GAefC,UAAW,SASXtlB,KAAM,GACV,EAYAglB,yBAA0B,CAQtBpC,oBAAqB,CAAA,EAwGrBH,WAAY,QA6BZH,QAAS,CA2GL3gB,KAAM,IAgFNoL,IAAK,IAkBLhD,aAAc,IAoCdgH,MAAO,CACH6P,MAAO,IACPxd,IAAK,KACLL,IAAK,KACLge,OAAQ,OACZ,EAwBAmC,gBAAiB,GACrB,CACJ,EAcAgC,qBAAsB,CA8BlB1T,SAAU,QAwBV8Q,QAAS,CAsEL3gB,KAAM,IAMNmP,KAAM,IAMN9J,OAAQ,EACZ,EACAue,cAAe,CACXD,UAAW,MACf,CACJ,CACJ,EACAiJ,UAAW,CACPI,oBAAqB,CACjB3Q,aAAc,CACV4Q,QAAS,eACTC,QAAS,WACD,IAAI,CAAC/J,YAAY,EACjB,IAAI,CAACA,YAAY,CAAC9G,YAAY,EAEtC,CACJ,EACA8Q,YAAa,CACTF,QAAS,cACTC,QAAS,WACL,IAAIluB,EAAI,IAAI,CAACmkB,YAAY,AACrBnkB,CAAAA,GAAKA,EAAEuX,SAAS,GAChBvX,EAAEyN,MAAM,GAGR,IAAI,CAACggB,MAAM,EAEnB,CACJ,CACJ,CACJ,EAKA7c,KAAM,CAMFyM,aAAc,gBAMd8Q,YAAa,eACjB,CACJ,GA6kJ6B,IAAIC,GAA6B5D,GAiL1D6D,GAAKzuB,GAETyuB,CAAAA,GAAElK,YAAY,CAAG,CACbja,kBAvlG+DA,EAwlG/DokB,OAhFS,CACTC,MAAO,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAG,CAC7BC,OAAQ,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAG,CAC9BC,cAAe,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAG,CACrCC,SAAU,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAG,CAChCC,MAAO,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAG,CAC7BC,OAAQ,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAG,CAC9BC,WAAY,CAAC,EAAG,EAAG,EAAG,EAAG,EAAG,EAAG,GAAG,CAClCC,gBAAiB,CAAC,EAAG,EAAG,EAAG,EAAG,EAAE,CAChCC,gBAAiB,CAAC,EAAG,EAAG,EAAG,EAAG,GAAG,AACrC,EAuEI1tB,WArmIwDA,EAsmIxDyK,uBAh0FoEA,EAi0FpEwD,oBA9kFiEA,EA+kFjE8H,qBA/pDkEA,EAgqDlEoT,aAAc4D,EAClB,EACAA,GAA0Bf,OAAO,CAACgB,GAAEW,KAAK,CAAEX,GAAEY,MAAM,CAAEZ,GAAEa,KAAK,EAC/B,IAAIxvB,GAAqBE,IAG5C,OADYH,EAAoB,OAAU,AAE3C,GAET"}