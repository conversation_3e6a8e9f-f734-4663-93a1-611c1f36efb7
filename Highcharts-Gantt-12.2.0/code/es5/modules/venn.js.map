{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/venn\n * @requires highcharts\n *\n * (c) 2017-2025 Highsoft AS\n * Authors: <AUTHORS>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"Color\"], require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/venn\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"Color\"], [\"highcharts/highcharts\",\"SeriesRegistry\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/venn\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"Color\"], require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Color\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__620__, __WEBPACK_EXTERNAL_MODULE__512__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 620:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__620__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ venn_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Color\"],\"commonjs\":[\"highcharts\",\"Color\"],\"commonjs2\":[\"highcharts\",\"Color\"],\"root\":[\"Highcharts\",\"Color\"]}\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_ = __webpack_require__(620);\nvar highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default = /*#__PURE__*/__webpack_require__.n(highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_);\n;// ./code/es5/es-modules/Core/Geometry/GeometryUtilities.js\n/* *\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  Namespace\n *\n * */\nvar GeometryUtilities;\n(function (GeometryUtilities) {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Calculates the center between a list of points.\n     *\n     * @private\n     *\n     * @param {Array<Highcharts.PositionObject>} points\n     * A list of points to calculate the center of.\n     *\n     * @return {Highcharts.PositionObject}\n     * Calculated center\n     */\n    function getCenterOfPoints(points) {\n        var sum = points.reduce(function (sum,\n            point) {\n                sum.x += point.x;\n            sum.y += point.y;\n            return sum;\n        }, { x: 0, y: 0 });\n        return {\n            x: sum.x / points.length,\n            y: sum.y / points.length\n        };\n    }\n    GeometryUtilities.getCenterOfPoints = getCenterOfPoints;\n    /**\n     * Calculates the distance between two points based on their x and y\n     * coordinates.\n     *\n     * @private\n     *\n     * @param {Highcharts.PositionObject} p1\n     * The x and y coordinates of the first point.\n     *\n     * @param {Highcharts.PositionObject} p2\n     * The x and y coordinates of the second point.\n     *\n     * @return {number}\n     * Returns the distance between the points.\n     */\n    function getDistanceBetweenPoints(p1, p2) {\n        return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));\n    }\n    GeometryUtilities.getDistanceBetweenPoints = getDistanceBetweenPoints;\n    /**\n     * Calculates the angle between two points.\n     * @todo add unit tests.\n     * @private\n     * @param {Highcharts.PositionObject} p1 The first point.\n     * @param {Highcharts.PositionObject} p2 The second point.\n     * @return {number} Returns the angle in radians.\n     */\n    function getAngleBetweenPoints(p1, p2) {\n        return Math.atan2(p2.x - p1.x, p2.y - p1.y);\n    }\n    GeometryUtilities.getAngleBetweenPoints = getAngleBetweenPoints;\n    /**\n     * Test for point in polygon. Polygon defined as array of [x,y] points.\n     * @private\n     * @param {PositionObject} point The point potentially within a polygon.\n     * @param {Array<Array<number>>} polygon The polygon potentially containing the point.\n     */\n    function pointInPolygon(_a, polygon) {\n        var x = _a.x,\n            y = _a.y;\n        var len = polygon.length;\n        var i,\n            j,\n            inside = false;\n        for (i = 0, j = len - 1; i < len; j = i++) {\n            var _b = polygon[i],\n                x1 = _b[0],\n                y1 = _b[1],\n                _c = polygon[j],\n                x2 = _c[0],\n                y2 = _c[1];\n            if (y1 > y !== y2 > y &&\n                (x < (x2 - x1) *\n                    (y - y1) /\n                    (y2 - y1) +\n                    x1)) {\n                inside = !inside;\n            }\n        }\n        return inside;\n    }\n    GeometryUtilities.pointInPolygon = pointInPolygon;\n})(GeometryUtilities || (GeometryUtilities = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Geometry_GeometryUtilities = (GeometryUtilities);\n\n;// ./code/es5/es-modules/Core/Geometry/CircleUtilities.js\n/* *\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar getAngleBetweenPoints = Geometry_GeometryUtilities.getAngleBetweenPoints, getCenterOfPoints = Geometry_GeometryUtilities.getCenterOfPoints, getDistanceBetweenPoints = Geometry_GeometryUtilities.getDistanceBetweenPoints;\n/* *\n *\n *  Namespace\n *\n * */\nvar CircleUtilities;\n(function (CircleUtilities) {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * @private\n     *\n     * @param {number} x\n     * Number to round\n     *\n     * @param {number} decimals\n     * Number of decimals to round to\n     *\n     * @return {number}\n     * Rounded number\n     */\n    function round(x, decimals) {\n        var a = Math.pow(10,\n            decimals);\n        return Math.round(x * a) / a;\n    }\n    CircleUtilities.round = round;\n    /**\n     * Calculates the area of a circle based on its radius.\n     *\n     * @private\n     *\n     * @param {number} r\n     * The radius of the circle.\n     *\n     * @return {number}\n     * Returns the area of the circle.\n     */\n    function getAreaOfCircle(r) {\n        if (r <= 0) {\n            throw new Error('radius of circle must be a positive number.');\n        }\n        return Math.PI * r * r;\n    }\n    CircleUtilities.getAreaOfCircle = getAreaOfCircle;\n    /**\n     * Calculates the area of a circular segment based on the radius of the\n     * circle and the height of the segment.\n     *\n     * @see http://mathworld.wolfram.com/CircularSegment.html\n     *\n     * @private\n     *\n     * @param {number} r\n     * The radius of the circle.\n     *\n     * @param {number} h\n     * The height of the circular segment.\n     *\n     * @return {number}\n     * Returns the area of the circular segment.\n     */\n    function getCircularSegmentArea(r, h) {\n        return (r * r * Math.acos(1 - h / r) -\n            (r - h) * Math.sqrt(h * (2 * r - h)));\n    }\n    CircleUtilities.getCircularSegmentArea = getCircularSegmentArea;\n    /**\n     * Calculates the area of overlap between two circles based on their\n     * radiuses and the distance between them.\n     *\n     * @see http://mathworld.wolfram.com/Circle-CircleIntersection.html\n     *\n     * @private\n     *\n     * @param {number} r1\n     * Radius of the first circle.\n     *\n     * @param {number} r2\n     * Radius of the second circle.\n     *\n     * @param {number} d\n     * The distance between the two circles.\n     *\n     * @return {number}\n     * Returns the area of overlap between the two circles.\n     */\n    function getOverlapBetweenCircles(r1, r2, d) {\n        var overlap = 0;\n        // If the distance is larger than the sum of the radiuses then the\n        // circles does not overlap.\n        if (d < r1 + r2) {\n            if (d <= Math.abs(r2 - r1)) {\n                // If the circles are completely overlapping, then the overlap\n                // equals the area of the smallest circle.\n                overlap = getAreaOfCircle(r1 < r2 ? r1 : r2);\n            }\n            else {\n                // Height of first triangle segment.\n                var d1 = (r1 * r1 - r2 * r2 + d * d) / (2 * d), \n                    // Height of second triangle segment.\n                    d2 = d - d1;\n                overlap = (getCircularSegmentArea(r1, r1 - d1) +\n                    getCircularSegmentArea(r2, r2 - d2));\n            }\n            // Round the result to two decimals.\n            overlap = round(overlap, 14);\n        }\n        return overlap;\n    }\n    CircleUtilities.getOverlapBetweenCircles = getOverlapBetweenCircles;\n    /**\n     * Calculates the intersection points of two circles.\n     *\n     * NOTE: does not handle floating errors well.\n     *\n     * @private\n     *\n     * @param {Highcharts.CircleObject} c1\n     * The first circle.\n     *\n     * @param {Highcharts.CircleObject} c2\n     * The second circle.\n     *\n     * @return {Array<Highcharts.PositionObject>}\n     * Returns the resulting intersection points.\n     */\n    function getCircleCircleIntersection(c1, c2) {\n        var d = getDistanceBetweenPoints(c1,\n            c2),\n            r1 = c1.r,\n            r2 = c2.r;\n        var points = [];\n        if (d < r1 + r2 && d > Math.abs(r1 - r2)) {\n            // If the circles are overlapping, but not completely overlapping,\n            // then it exists intersecting points.\n            var r1Square = r1 * r1, r2Square = r2 * r2, \n                // `d^2 - r^2 + R^2 / 2d`\n                x = (r1Square - r2Square + d * d) / (2 * d), \n                // `y^2 = R^2 - x^2`\n                y = Math.sqrt(r1Square - x * x), x1 = c1.x, x2 = c2.x, y1 = c1.y, y2 = c2.y, x0 = x1 + x * (x2 - x1) / d, y0 = y1 + x * (y2 - y1) / d, rx = -(y2 - y1) * (y / d), ry = -(x2 - x1) * (y / d);\n            points = [\n                { x: round(x0 + rx, 14), y: round(y0 - ry, 14) },\n                { x: round(x0 - rx, 14), y: round(y0 + ry, 14) }\n            ];\n        }\n        return points;\n    }\n    CircleUtilities.getCircleCircleIntersection = getCircleCircleIntersection;\n    /**\n     * Calculates all the intersection points for between a list of circles.\n     *\n     * @private\n     *\n     * @param {Array<Highcharts.CircleObject>} circles\n     * The circles to calculate the points from.\n     *\n     * @return {Array<Highcharts.GeometryObject>}\n     * Returns a list of intersection points.\n     */\n    function getCirclesIntersectionPoints(circles) {\n        return circles.reduce(function (points, c1, i, arr) {\n            var additional = arr\n                    .slice(i + 1)\n                    .reduce(function (points,\n                c2,\n                j) {\n                    var indexes = [i,\n                j + i + 1];\n                return points.concat(getCircleCircleIntersection(c1, c2).map(function (p) {\n                    p.indexes = indexes;\n                    return p;\n                }));\n            }, []);\n            return points.concat(additional);\n        }, []);\n    }\n    CircleUtilities.getCirclesIntersectionPoints = getCirclesIntersectionPoints;\n    /**\n     * Tests whether the first circle is completely overlapping the second\n     * circle.\n     *\n     * @private\n     *\n     * @param {Highcharts.CircleObject} circle1\n     * The first circle.\n     *\n     * @param {Highcharts.CircleObject} circle2\n     * The second circle.\n     *\n     * @return {boolean}\n     * Returns true if circle1 is completely overlapping circle2, false if not.\n     */\n    function isCircle1CompletelyOverlappingCircle2(circle1, circle2) {\n        return getDistanceBetweenPoints(circle1, circle2) + circle2.r < circle1.r + 1e-10;\n    }\n    CircleUtilities.isCircle1CompletelyOverlappingCircle2 = isCircle1CompletelyOverlappingCircle2;\n    /**\n     * Tests whether a point lies within a given circle.\n     * @private\n     * @param {Highcharts.PositionObject} point\n     * The point to test for.\n     *\n     * @param {Highcharts.CircleObject} circle\n     * The circle to test if the point is within.\n     *\n     * @return {boolean}\n     * Returns true if the point is inside, false if outside.\n     */\n    function isPointInsideCircle(point, circle) {\n        return getDistanceBetweenPoints(point, circle) <= circle.r + 1e-10;\n    }\n    CircleUtilities.isPointInsideCircle = isPointInsideCircle;\n    /**\n     * Tests whether a point lies within a set of circles.\n     *\n     * @private\n     *\n     * @param {Highcharts.PositionObject} point\n     * The point to test.\n     *\n     * @param {Array<Highcharts.CircleObject>} circles\n     * The list of circles to test against.\n     *\n     * @return {boolean}\n     * Returns true if the point is inside all the circles, false if not.\n     */\n    function isPointInsideAllCircles(point, circles) {\n        return !circles.some(function (circle) {\n            return !isPointInsideCircle(point, circle);\n        });\n    }\n    CircleUtilities.isPointInsideAllCircles = isPointInsideAllCircles;\n    /**\n     * Tests whether a point lies outside a set of circles.\n     *\n     * TODO: add unit tests.\n     *\n     * @private\n     *\n     * @param {Highcharts.PositionObject} point\n     * The point to test.\n     *\n     * @param {Array<Highcharts.CircleObject>} circles\n     * The list of circles to test against.\n     *\n     * @return {boolean}\n     * Returns true if the point is outside all the circles, false if not.\n     */\n    function isPointOutsideAllCircles(point, circles) {\n        return !circles.some(function (circle) {\n            return isPointInsideCircle(point, circle);\n        });\n    }\n    CircleUtilities.isPointOutsideAllCircles = isPointOutsideAllCircles;\n    /**\n     * Calculates the points for the polygon of the intersection area between\n     * a set of circles.\n     *\n     * @private\n     *\n     * @param {Array<Highcharts.CircleObject>} circles\n     * List of circles to calculate polygon of.\n     *\n     * @return {Array<Highcharts.GeometryObject>}\n     * Return list of points in the intersection polygon.\n     */\n    function getCirclesIntersectionPolygon(circles) {\n        return getCirclesIntersectionPoints(circles)\n            .filter(function (p) {\n            return isPointInsideAllCircles(p, circles);\n        });\n    }\n    CircleUtilities.getCirclesIntersectionPolygon = getCirclesIntersectionPolygon;\n    /**\n     * Calculate the path for the area of overlap between a set of circles.\n     *\n     * @todo handle cases with only 1 or 0 arcs.\n     *\n     * @private\n     *\n     * @param {Array<Highcharts.CircleObject>} circles\n     * List of circles to calculate area of.\n     *\n     * @return {Highcharts.GeometryIntersectionObject|undefined}\n     * Returns the path for the area of overlap. Returns an empty string if\n     * there are no intersection between all the circles.\n     */\n    function getAreaOfIntersectionBetweenCircles(circles) {\n        var intersectionPoints = getCirclesIntersectionPolygon(circles),\n            result;\n        if (intersectionPoints.length > 1) {\n            // Calculate the center of the intersection points.\n            var center_1 = getCenterOfPoints(intersectionPoints);\n            intersectionPoints = intersectionPoints\n                // Calculate the angle between the center and the points.\n                .map(function (p) {\n                p.angle = getAngleBetweenPoints(center_1, p);\n                return p;\n            })\n                // Sort the points by the angle to the center.\n                .sort(function (a, b) {\n                return b.angle - a.angle;\n            });\n            var startPoint = intersectionPoints[intersectionPoints.length - 1];\n            var arcs = intersectionPoints\n                    .reduce(function (data,\n                p1) {\n                    var startPoint = data.startPoint,\n                midPoint = getCenterOfPoints([startPoint,\n                p1]);\n                // Calculate the arc from the intersection points and their\n                // circles.\n                var arc = p1.indexes\n                        // Filter out circles that are not included in both\n                        // intersection points.\n                        .filter(function (index) {\n                        return startPoint.indexes.indexOf(index) > -1;\n                })\n                    // Iterate the circles of the intersection points and\n                    // calculate arcs.\n                    .reduce(function (arc, index) {\n                    var circle = circles[index],\n                        angle1 = getAngleBetweenPoints(circle,\n                        p1),\n                        angle2 = getAngleBetweenPoints(circle,\n                        startPoint),\n                        angleDiff = angle2 - angle1 +\n                            (angle2 < angle1 ? 2 * Math.PI : 0),\n                        angle = angle2 - angleDiff / 2;\n                    var width = getDistanceBetweenPoints(midPoint, {\n                            x: circle.x + circle.r * Math.sin(angle),\n                            y: circle.y + circle.r * Math.cos(angle)\n                        });\n                    var r = circle.r;\n                    // Width can sometimes become to large due to\n                    // floating point errors\n                    if (width > r * 2) {\n                        width = r * 2;\n                    }\n                    // Get the arc with the smallest width.\n                    if (!arc || arc.width > width) {\n                        arc = {\n                            r: r,\n                            largeArc: width > r ? 1 : 0,\n                            width: width,\n                            x: p1.x,\n                            y: p1.y\n                        };\n                    }\n                    // Return the chosen arc.\n                    return arc;\n                }, null);\n                // If we find an arc then add it to the list and update p2.\n                if (arc) {\n                    var r = arc.r;\n                    data.arcs.push(['A', r, r, 0, arc.largeArc, 1, arc.x, arc.y]);\n                    data.startPoint = p1;\n                }\n                return data;\n            }, {\n                startPoint: startPoint,\n                arcs: []\n            }).arcs;\n            if (arcs.length === 0) {\n                // Empty\n            }\n            else if (arcs.length === 1) {\n                // Empty\n            }\n            else {\n                arcs.unshift(['M', startPoint.x, startPoint.y]);\n                result = {\n                    center: center_1,\n                    d: arcs\n                };\n            }\n        }\n        return result;\n    }\n    CircleUtilities.getAreaOfIntersectionBetweenCircles = getAreaOfIntersectionBetweenCircles;\n})(CircleUtilities || (CircleUtilities = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Geometry_CircleUtilities = (CircleUtilities);\n\n;// ./code/es5/es-modules/Series/DrawPointUtilities.js\n/* *\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar __assign = (undefined && undefined.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Handles the drawing of a component.\n * Can be used for any type of component that reserves the graphic property,\n * and provides a shouldDraw on its context.\n *\n * @private\n *\n * @todo add type checking.\n * @todo export this function to enable usage\n */\nfunction draw(point, params) {\n    var animatableAttribs = params.animatableAttribs,\n        onComplete = params.onComplete,\n        css = params.css,\n        renderer = params.renderer;\n    var animation = (point.series && point.series.chart.hasRendered) ?\n            // Chart-level animation on updates\n            void 0 :\n            // Series-level animation on new points\n            (point.series &&\n                point.series.options.animation);\n    var graphic = point.graphic;\n    params.attribs = __assign(__assign({}, params.attribs), { 'class': point.getClassName() }) || {};\n    if ((point.shouldDraw())) {\n        if (!graphic) {\n            if (params.shapeType === 'text') {\n                graphic = renderer.text();\n            }\n            else if (params.shapeType === 'image') {\n                graphic = renderer.image(params.imageUrl || '')\n                    .attr(params.shapeArgs || {});\n            }\n            else {\n                graphic = renderer[params.shapeType](params.shapeArgs || {});\n            }\n            point.graphic = graphic;\n            graphic.add(params.group);\n        }\n        if (css) {\n            graphic.css(css);\n        }\n        graphic\n            .attr(params.attribs)\n            .animate(animatableAttribs, params.isNew ? false : animation, onComplete);\n    }\n    else if (graphic) {\n        var destroy_1 = function () {\n                point.graphic = graphic = (graphic && graphic.destroy());\n            if (typeof onComplete === 'function') {\n                onComplete();\n            }\n        };\n        // Animate only runs complete callback if something was animated.\n        if (Object.keys(animatableAttribs).length) {\n            graphic.animate(animatableAttribs, void 0, function () { return destroy_1(); });\n        }\n        else {\n            destroy_1();\n        }\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar DrawPointUtilities = {\n    draw: draw\n};\n/* harmony default export */ var Series_DrawPointUtilities = (DrawPointUtilities);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es5/es-modules/Series/Venn/VennPoint.js\n/* *\n *\n *  Experimental Highcharts module which enables visualization of a Venn\n *  diagram.\n *\n *  (c) 2016-2025 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  Layout algorithm by Ben Frederickson:\n *  https://www.benfrederickson.com/better-venn-diagrams/\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar ScatterPoint = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.scatter.prototype.pointClass;\n\nvar isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber;\n/* *\n *\n *  Class\n *\n * */\nvar VennPoint = /** @class */ (function (_super) {\n    __extends(VennPoint, _super);\n    function VennPoint() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    VennPoint.prototype.isValid = function () {\n        return isNumber(this.value);\n    };\n    VennPoint.prototype.shouldDraw = function () {\n        // Only draw points with single sets.\n        return !!this.shapeArgs;\n    };\n    return VennPoint;\n}(ScatterPoint));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Venn_VennPoint = (VennPoint);\n\n;// ./code/es5/es-modules/Series/Venn/VennSeriesDefaults.js\n/* *\n *\n *  Experimental Highcharts module which enables visualization of a Venn\n *  diagram.\n *\n *  (c) 2016-2025 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  Layout algorithm by Ben Frederickson:\n *  https://www.benfrederickson.com/better-venn-diagrams/\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A Venn diagram displays all possible logical relations between a\n * collection of different sets. The sets are represented by circles, and\n * the relation between the sets are displayed by the overlap or lack of\n * overlap between them. The venn diagram is a special case of Euler\n * diagrams, which can also be displayed by this series type.\n *\n * @sample {highcharts} highcharts/demo/venn-diagram/\n *         Venn diagram\n * @sample {highcharts} highcharts/demo/euler-diagram/\n *         Euler diagram\n * @sample {highcharts} highcharts/series-venn/point-legend/\n *         Venn diagram with a legend\n *\n * @extends      plotOptions.scatter\n * @excluding    connectEnds, connectNulls, cropThreshold, dragDrop,\n *               findNearestPointBy, getExtremesFromAll, jitter, label,\n *               linecap, lineWidth, linkedTo, marker, negativeColor,\n *               pointInterval, pointIntervalUnit, pointPlacement,\n *               pointStart, softThreshold, stacking, steps, threshold,\n *               xAxis, yAxis, zoneAxis, zones, dataSorting, boostThreshold,\n *               boostBlending\n * @product      highcharts\n * @requires     modules/venn\n * @optionparent plotOptions.venn\n */\nvar VennSeriesDefaults = {\n    borderColor: \"#cccccc\" /* Palette.neutralColor20 */,\n    borderDashStyle: 'solid',\n    borderWidth: 1,\n    brighten: 0,\n    clip: false,\n    colorByPoint: true,\n    dataLabels: {\n        enabled: true,\n        verticalAlign: 'middle',\n        formatter: function () {\n            return this.point.name;\n        }\n    },\n    /**\n     * @default   true\n     * @extends   plotOptions.series.inactiveOtherPoints\n     * @private\n     */\n    inactiveOtherPoints: true,\n    /**\n     * @ignore-option\n     * @private\n     */\n    marker: false,\n    opacity: 0.75,\n    showInLegend: false,\n    /**\n     * @ignore-option\n     *\n     * @private\n     */\n    legendType: 'point',\n    states: {\n        /**\n         * @excluding halo\n         */\n        hover: {\n            opacity: 1,\n            borderColor: \"#333333\" /* Palette.neutralColor80 */\n        },\n        /**\n         * @excluding halo\n         */\n        select: {\n            color: \"#cccccc\" /* Palette.neutralColor20 */,\n            borderColor: \"#000000\" /* Palette.neutralColor100 */,\n            animation: false\n        },\n        inactive: {\n            opacity: 0.075\n        }\n    },\n    tooltip: {\n        pointFormat: '{point.name}: {point.value}'\n    },\n    legendSymbol: 'rectangle'\n};\n/**\n * A `venn` series. If the [type](#series.venn.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.venn\n * @excluding connectEnds, connectNulls, cropThreshold, dataParser, dataURL,\n *            findNearestPointBy, getExtremesFromAll, label, linecap, lineWidth,\n *            linkedTo, marker, negativeColor, pointInterval, pointIntervalUnit,\n *            pointPlacement, pointStart, softThreshold, stack, stacking, steps,\n *            threshold, xAxis, yAxis, zoneAxis, zones, dataSorting,\n *            boostThreshold, boostBlending\n * @product   highcharts\n * @requires  modules/venn\n * @apioption series.venn\n */\n/**\n * @type      {Array<*>}\n * @extends   series.scatter.data\n * @excluding marker, x, y\n * @product   highcharts\n * @apioption series.venn.data\n */\n/**\n * The name of the point. Used in data labels and tooltip. If name is not\n * defined then it will default to the joined values in\n * [sets](#series.venn.sets).\n *\n * @sample {highcharts} highcharts/demo/venn-diagram/\n *         Venn diagram\n * @sample {highcharts} highcharts/demo/euler-diagram/\n *         Euler diagram\n *\n * @type      {string}\n * @since     7.0.0\n * @product   highcharts\n * @apioption series.venn.data.name\n */\n/**\n * The value of the point, resulting in a relative area of the circle, or area\n * of overlap between two sets in the venn or euler diagram.\n *\n * @sample {highcharts} highcharts/demo/venn-diagram/\n *         Venn diagram\n * @sample {highcharts} highcharts/demo/euler-diagram/\n *         Euler diagram\n *\n * @type      {number}\n * @since     7.0.0\n * @product   highcharts\n * @apioption series.venn.data.value\n */\n/**\n * The set or sets the options will be applied to. If a single entry is defined,\n * then it will create a new set. If more than one entry is defined, then it\n * will define the overlap between the sets in the array.\n *\n * @sample {highcharts} highcharts/demo/venn-diagram/\n *         Venn diagram\n * @sample {highcharts} highcharts/demo/euler-diagram/\n *         Euler diagram\n *\n * @type      {Array<string>}\n * @since     7.0.0\n * @product   highcharts\n * @apioption series.venn.data.sets\n */\n/**\n * @excluding halo\n * @apioption series.venn.states.hover\n */\n/**\n * @excluding halo\n * @apioption series.venn.states.select\n */\n''; // Detachs doclets above\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Venn_VennSeriesDefaults = (VennSeriesDefaults);\n\n;// ./code/es5/es-modules/Series/Venn/VennUtils.js\n/* *\n *\n *  Experimental Highcharts module which enables visualization of a Venn\n *  diagram.\n *\n *  (c) 2016-2025 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  Layout algorithm by Ben Frederickson:\n *  https://www.benfrederickson.com/better-venn-diagrams/\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar VennUtils_assign = (undefined && undefined.__assign) || function () {\n    VennUtils_assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return VennUtils_assign.apply(this, arguments);\n};\n\nvar getAreaOfCircle = Geometry_CircleUtilities.getAreaOfCircle, getCircleCircleIntersection = Geometry_CircleUtilities.getCircleCircleIntersection, getOverlapBetweenCirclesByDistance = Geometry_CircleUtilities.getOverlapBetweenCircles, isPointInsideAllCircles = Geometry_CircleUtilities.isPointInsideAllCircles, isPointInsideCircle = Geometry_CircleUtilities.isPointInsideCircle, isPointOutsideAllCircles = Geometry_CircleUtilities.isPointOutsideAllCircles;\n\nvar VennUtils_getDistanceBetweenPoints = Geometry_GeometryUtilities.getDistanceBetweenPoints;\n\nvar extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, isArray = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isArray, VennUtils_isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, isObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isObject, isString = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isString;\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Takes an array of relations and adds the properties `totalOverlap` and\n * `overlapping` to each set. The property `totalOverlap` is the sum of\n * value for each relation where this set is included. The property\n * `overlapping` is a map of how much this set is overlapping another set.\n * NOTE: This algorithm ignores relations consisting of more than 2 sets.\n * @private\n * @param {Array<Highcharts.VennRelationObject>} relations\n * The list of relations that should be sorted.\n * @return {Array<Highcharts.VennRelationObject>}\n * Returns the modified input relations with added properties `totalOverlap`\n * and `overlapping`.\n */\nfunction addOverlapToSets(relations) {\n    // Calculate the amount of overlap per set.\n    var mapOfIdToProps = {};\n    relations\n        // Filter out relations consisting of 2 sets.\n        .filter(function (relation) { return (relation.sets.length === 2); })\n        // Sum up the amount of overlap for each set.\n        .forEach(function (relation) {\n        relation.sets.forEach(function (set, i, arr) {\n            var _a;\n            if (!isObject(mapOfIdToProps[set])) {\n                mapOfIdToProps[set] = {\n                    totalOverlap: 0,\n                    overlapping: {}\n                };\n            }\n            mapOfIdToProps[set] = {\n                totalOverlap: (mapOfIdToProps[set].totalOverlap || 0) +\n                    relation.value,\n                overlapping: VennUtils_assign(VennUtils_assign({}, (mapOfIdToProps[set].overlapping || {})), (_a = {}, _a[arr[1 - i]] = relation.value, _a))\n            };\n        });\n    });\n    relations\n        // Filter out single sets\n        .filter(isSet)\n        // Extend the set with the calculated properties.\n        .forEach(function (set) {\n        var properties = mapOfIdToProps[set.sets[0]];\n        extend(set, properties);\n    });\n    // Returns the modified relations.\n    return relations;\n}\n/**\n * Finds the root of a given function. The root is the input value needed\n * for a function to return 0.\n *\n * See https://en.wikipedia.org/wiki/Bisection_method#Algorithm\n *\n * TODO: Add unit tests.\n *\n * @param {Function} f\n * The function to find the root of.\n * @param {number} a\n * The lowest number in the search range.\n * @param {number} b\n * The highest number in the search range.\n * @param {number} [tolerance=1e-10]\n * The allowed difference between the returned value and root.\n * @param {number} [maxIterations=100]\n * The maximum iterations allowed.\n * @return {number}\n * Root number.\n */\nfunction bisect(f, a, b, tolerance, maxIterations) {\n    var fA = f(a),\n        fB = f(b),\n        nMax = maxIterations || 100,\n        tol = tolerance || 1e-10;\n    var delta = b - a,\n        x,\n        fX,\n        n = 1;\n    if (a >= b) {\n        throw new Error('a must be smaller than b.');\n    }\n    else if (fA * fB > 0) {\n        throw new Error('f(a) and f(b) must have opposite signs.');\n    }\n    if (fA === 0) {\n        x = a;\n    }\n    else if (fB === 0) {\n        x = b;\n    }\n    else {\n        while (n++ <= nMax && fX !== 0 && delta > tol) {\n            delta = (b - a) / 2;\n            x = a + delta;\n            fX = f(x);\n            // Update low and high for next search interval.\n            if (fA * fX > 0) {\n                a = x;\n            }\n            else {\n                b = x;\n            }\n        }\n    }\n    return x;\n}\n/**\n * @private\n */\nfunction getCentroid(simplex) {\n    var arr = simplex.slice(0, -1),\n        length = arr.length,\n        result = [],\n        sum = function (data,\n        point) {\n            data.sum += point[data.i];\n        return data;\n    };\n    for (var i = 0; i < length; i++) {\n        result[i] = arr.reduce(sum, { sum: 0, i: i }).sum / length;\n    }\n    return result;\n}\n/**\n * Uses the bisection method to make a best guess of the ideal distance\n * between two circles too get the desired overlap.\n * Currently there is no known formula to calculate the distance from the\n * area of overlap, which makes the bisection method preferred.\n * @private\n * @param {number} r1\n * Radius of the first circle.\n * @param {number} r2\n * Radius of the second circle.\n * @param {number} overlap\n * The wanted overlap between the two circles.\n * @return {number}\n * Returns the distance needed to get the wanted overlap between the two\n * circles.\n */\nfunction getDistanceBetweenCirclesByOverlap(r1, r2, overlap) {\n    var maxDistance = r1 + r2;\n    var distance;\n    if (overlap <= 0) {\n        // If overlap is below or equal to zero, then there is no overlap.\n        distance = maxDistance;\n    }\n    else if (getAreaOfCircle(r1 < r2 ? r1 : r2) <= overlap) {\n        // When area of overlap is larger than the area of the smallest\n        // circle, then it is completely overlapping.\n        distance = 0;\n    }\n    else {\n        distance = bisect(function (x) {\n            var actualOverlap = getOverlapBetweenCirclesByDistance(r1,\n                r2,\n                x);\n            // Return the difference between wanted and actual overlap.\n            return overlap - actualOverlap;\n        }, 0, maxDistance);\n    }\n    return distance;\n}\n/**\n * Finds the available width for a label, by taking the label position and\n * finding the largest distance, which is inside all internal circles, and\n * outside all external circles.\n *\n * @private\n * @param {Highcharts.PositionObject} pos\n * The x and y coordinate of the label.\n * @param {Array<Highcharts.CircleObject>} internal\n * Internal circles.\n * @param {Array<Highcharts.CircleObject>} external\n * External circles.\n * @return {number}\n * Returns available width for the label.\n */\nfunction getLabelWidth(pos, internal, external) {\n    var radius = internal.reduce(function (min,\n        circle) { return Math.min(circle.r,\n        min); },\n        Infinity), \n        // Filter out external circles that are completely overlapping.\n        filteredExternals = external.filter(function (circle) { return !isPointInsideCircle(pos,\n        circle); });\n    var findDistance = function (maxDistance,\n        direction) {\n            return bisect(function (x) {\n                var testPos = {\n                    x: pos.x + (direction * x),\n                    y: pos.y\n                },\n        isValid = (isPointInsideAllCircles(testPos,\n        internal) &&\n                    isPointOutsideAllCircles(testPos,\n        filteredExternals));\n            // If the position is valid, then we want to move towards the\n            // max distance. If not, then we want to away from the max distance.\n            return -(maxDistance - x) + (isValid ? 0 : Number.MAX_VALUE);\n        }, 0, maxDistance);\n    };\n    // Find the smallest distance of left and right.\n    return Math.min(findDistance(radius, -1), findDistance(radius, 1)) * 2;\n}\n/**\n * Calculates a margin for a point based on the internal and external\n * circles. The margin describes if the point is well placed within the\n * internal circles, and away from the external.\n * @private\n * @todo add unit tests.\n * @param {Highcharts.PositionObject} point\n * The point to evaluate.\n * @param {Array<Highcharts.CircleObject>} internal\n * The internal circles.\n * @param {Array<Highcharts.CircleObject>} external\n * The external circles.\n * @return {number}\n * Returns the margin.\n */\nfunction getMarginFromCircles(point, internal, external) {\n    var margin = internal.reduce(function (margin,\n        circle) {\n            var m = circle.r - VennUtils_getDistanceBetweenPoints(point,\n        circle);\n        return (m <= margin) ? m : margin;\n    }, Number.MAX_VALUE);\n    margin = external.reduce(function (margin, circle) {\n        var m = VennUtils_getDistanceBetweenPoints(point,\n            circle) - circle.r;\n        return (m <= margin) ? m : margin;\n    }, margin);\n    return margin;\n}\n/**\n * Calculates the area of overlap between a list of circles.\n * @private\n * @todo add support for calculating overlap between more than 2 circles.\n * @param {Array<Highcharts.CircleObject>} circles\n * List of circles with their given positions.\n * @return {number}\n * Returns the area of overlap between all the circles.\n */\nfunction getOverlapBetweenCircles(circles) {\n    var overlap = 0;\n    // When there is only two circles we can find the overlap by using their\n    // radiuses and the distance between them.\n    if (circles.length === 2) {\n        var circle1 = circles[0];\n        var circle2 = circles[1];\n        overlap = getOverlapBetweenCirclesByDistance(circle1.r, circle2.r, VennUtils_getDistanceBetweenPoints(circle1, circle2));\n    }\n    return overlap;\n}\n// eslint-disable-next-line require-jsdoc\n/**\n *\n */\nfunction isSet(x) {\n    return isArray(x.sets) && x.sets.length === 1;\n}\n// eslint-disable-next-line require-jsdoc\n/**\n *\n */\nfunction isValidRelation(x) {\n    var map = {};\n    return (isObject(x) &&\n        (VennUtils_isNumber(x.value) && x.value > -1) &&\n        (isArray(x.sets) && x.sets.length > 0) &&\n        !x.sets.some(function (set) {\n            var invalid = false;\n            if (!map[set] && isString(set)) {\n                map[set] = true;\n            }\n            else {\n                invalid = true;\n            }\n            return invalid;\n        }));\n}\n// eslint-disable-next-line require-jsdoc\n/**\n *\n */\nfunction isValidSet(x) {\n    return (isValidRelation(x) && isSet(x) && x.value > 0);\n}\n/**\n * Uses a greedy approach to position all the sets. Works well with a small\n * number of sets, and are in these cases a good choice aesthetically.\n * @private\n * @param {Array<object>} relations List of the overlap between two or more\n * sets, or the size of a single set.\n * @return {Array<object>} List of circles and their calculated positions.\n */\nfunction layoutGreedyVenn(relations) {\n    var positionedSets = [],\n        mapOfIdToCircles = {};\n    // Define a circle for each set.\n    relations\n        .filter(function (relation) { return (relation.sets.length === 1); })\n        .forEach(function (relation) {\n        mapOfIdToCircles[relation.sets[0]] = relation.circle = {\n            x: Number.MAX_VALUE,\n            y: Number.MAX_VALUE,\n            r: Math.sqrt(relation.value / Math.PI)\n        };\n    });\n    /**\n     * Takes a set and updates the position, and add the set to the list of\n     * positioned sets.\n     * @private\n     * @param {Object} set\n     * The set to add to its final position.\n     * @param {Object} coordinates\n     * The coordinates to position the set at.\n     */\n    var positionSet = function (set,\n        coordinates) {\n            var circle = set.circle;\n        if (circle) {\n            circle.x = coordinates.x;\n            circle.y = coordinates.y;\n        }\n        positionedSets.push(set);\n    };\n    // Find overlap between sets. Ignore relations with more then 2 sets.\n    addOverlapToSets(relations);\n    // Sort sets by the sum of their size from large to small.\n    var sortedByOverlap = relations\n            .filter(isSet)\n            .sort(sortByTotalOverlap);\n    // Position the most overlapped set at 0,0.\n    positionSet(sortedByOverlap.shift(), { x: 0, y: 0 });\n    var relationsWithTwoSets = relations.filter(function (x) { return (x.sets.length === 2); });\n    var _loop_1 = function (set) {\n            var circle = set.circle;\n        if (!circle) {\n            return \"continue\";\n        }\n        var radius = circle.r,\n            overlapping = set.overlapping;\n        var bestPosition = positionedSets.reduce(function (best,\n            positionedSet,\n            i) {\n                var positionedCircle = positionedSet.circle;\n            if (!positionedCircle || !overlapping) {\n                return best;\n            }\n            var overlap = overlapping[positionedSet.sets[0]];\n            // Calculate the distance between the sets to get the\n            // correct overlap\n            var distance = getDistanceBetweenCirclesByOverlap(radius,\n                positionedCircle.r,\n                overlap);\n            // Create a list of possible coordinates calculated from\n            // distance.\n            var possibleCoordinates = [\n                    { x: positionedCircle.x + distance,\n                y: positionedCircle.y },\n                    { x: positionedCircle.x - distance,\n                y: positionedCircle.y },\n                    { x: positionedCircle.x,\n                y: positionedCircle.y + distance },\n                    { x: positionedCircle.x,\n                y: positionedCircle.y - distance }\n                ];\n            // If there are more circles overlapping, then add the\n            // intersection points as possible positions.\n            for (var _i = 0, _a = positionedSets.slice(i + 1); _i < _a.length; _i++) {\n                var positionedSet2 = _a[_i];\n                var positionedCircle2 = positionedSet2.circle,\n                    overlap2 = overlapping[positionedSet2.sets[0]];\n                if (!positionedCircle2) {\n                    continue;\n                }\n                var distance2 = getDistanceBetweenCirclesByOverlap(radius,\n                    positionedCircle2.r,\n                    overlap2);\n                // Add intersections to list of coordinates.\n                possibleCoordinates = possibleCoordinates.concat(getCircleCircleIntersection({\n                    x: positionedCircle.x,\n                    y: positionedCircle.y,\n                    r: distance\n                }, {\n                    x: positionedCircle2.x,\n                    y: positionedCircle2.y,\n                    r: distance2\n                }));\n            }\n            // Iterate all suggested coordinates and find the best one.\n            for (var _b = 0, possibleCoordinates_1 = possibleCoordinates; _b < possibleCoordinates_1.length; _b++) {\n                var coordinates = possibleCoordinates_1[_b];\n                circle.x = coordinates.x;\n                circle.y = coordinates.y;\n                // Calculate loss for the suggested coordinates.\n                var currentLoss = loss(mapOfIdToCircles,\n                    relationsWithTwoSets);\n                // If the loss is better, then use these new coordinates\n                if (currentLoss < best.loss) {\n                    best.loss = currentLoss;\n                    best.coordinates = coordinates;\n                }\n            }\n            // Return resulting coordinates.\n            return best;\n        }, {\n            loss: Number.MAX_VALUE,\n            coordinates: void 0\n        });\n        // Add the set to its final position.\n        positionSet(set, bestPosition.coordinates);\n    };\n    // Iterate and position the remaining sets.\n    for (var _i = 0, sortedByOverlap_1 = sortedByOverlap; _i < sortedByOverlap_1.length; _i++) {\n        var set = sortedByOverlap_1[_i];\n        _loop_1(set);\n    }\n    // Return the positions of each set.\n    return mapOfIdToCircles;\n}\n/**\n * Calculates the difference between the desired overlap and the actual\n * overlap between two circles.\n * @private\n * @param {Dictionary<Highcharts.CircleObject>} mapOfIdToCircle\n * Map from id to circle.\n * @param {Array<Highcharts.VennRelationObject>} relations\n * List of relations to calculate the loss of.\n * @return {number}\n * Returns the loss between positions of the circles for the given\n * relations.\n */\nfunction loss(mapOfIdToCircle, relations) {\n    var precision = 10e10;\n    // Iterate all the relations and calculate their individual loss.\n    return relations.reduce(function (totalLoss, relation) {\n        var loss = 0;\n        if (relation.sets.length > 1) {\n            var wantedOverlap = relation.value;\n            // Calculate the actual overlap between the sets.\n            var actualOverlap = getOverlapBetweenCircles(\n                // Get the circles for the given sets.\n                relation.sets.map(function (set) {\n                    return mapOfIdToCircle[set];\n            }));\n            var diff = wantedOverlap - actualOverlap;\n            loss = Math.round((diff * diff) * precision) / precision;\n        }\n        // Add calculated loss to the sum.\n        return totalLoss + loss;\n    }, 0);\n}\n/**\n * Finds an optimal position for a given point.\n * @todo add unit tests.\n * @todo add constraints to optimize the algorithm.\n * @private\n * @param {Highcharts.NelderMeadTestFunction} fn\n *        The function to test a point.\n * @param {Highcharts.NelderMeadPointArray} initial\n *        The initial point to optimize.\n * @return {Highcharts.NelderMeadPointArray}\n *         Returns the optimized position of a point.\n */\nfunction nelderMead(fn, initial) {\n    var maxIterations = 100,\n        sortByFx = function (a,\n        b) {\n            return a.fx - b.fx;\n    }, pRef = 1, // Reflection parameter\n    pExp = 2, // Expansion parameter\n    pCon = -0.5, // Contraction parameter\n    pOCon = pCon * pRef, // Outwards contraction parameter\n    pShrink = 0.5; // Shrink parameter\n    /**\n     * @private\n     */\n    var weightedSum = function (weight1,\n        v1,\n        weight2,\n        v2) { return v1.map(function (x,\n        i) { return weight1 * x + weight2 * v2[i]; }); };\n    /**\n     * @private\n     */\n    var getSimplex = function (initial) {\n            var n = initial.length,\n        simplex = new Array(n + 1);\n        // Initial point to the simplex.\n        simplex[0] = initial;\n        simplex[0].fx = fn(initial);\n        // Create a set of extra points based on the initial.\n        for (var i = 0; i < n; ++i) {\n            var point = initial.slice();\n            point[i] = point[i] ? point[i] * 1.05 : 0.001;\n            point.fx = fn(point);\n            simplex[i + 1] = point;\n        }\n        return simplex;\n    };\n    var updateSimplex = function (simplex,\n        point) {\n            point.fx = fn(point);\n        simplex[simplex.length - 1] = point;\n        return simplex;\n    };\n    var shrinkSimplex = function (simplex) {\n            var best = simplex[0];\n        return simplex.map(function (point) {\n            var p = weightedSum(1 - pShrink,\n                best,\n                pShrink,\n                point);\n            p.fx = fn(p);\n            return p;\n        });\n    };\n    var getPoint = function (centroid,\n        worst,\n        a,\n        b) {\n            var point = weightedSum(a,\n        centroid,\n        b,\n        worst);\n        point.fx = fn(point);\n        return point;\n    };\n    // Create a simplex\n    var simplex = getSimplex(initial);\n    // Iterate from 0 to max iterations\n    for (var i = 0; i < maxIterations; i++) {\n        // Sort the simplex\n        simplex.sort(sortByFx);\n        // Create a centroid from the simplex\n        var worst = simplex[simplex.length - 1];\n        var centroid = getCentroid(simplex);\n        // Calculate the reflected point.\n        var reflected = getPoint(centroid,\n            worst, 1 + pRef, -pRef);\n        if (reflected.fx < simplex[0].fx) {\n            // If reflected point is the best, then possibly expand.\n            var expanded = getPoint(centroid,\n                worst, 1 + pExp, -pExp);\n            simplex = updateSimplex(simplex, (expanded.fx < reflected.fx) ? expanded : reflected);\n        }\n        else if (reflected.fx >= simplex[simplex.length - 2].fx) {\n            // If the reflected point is worse than the second worse, then\n            // contract.\n            var contracted = void 0;\n            if (reflected.fx > worst.fx) {\n                // If the reflected is worse than the worst point, do a\n                // contraction\n                contracted = getPoint(centroid, worst, 1 + pCon, -pCon);\n                if (contracted.fx < worst.fx) {\n                    simplex = updateSimplex(simplex, contracted);\n                }\n                else {\n                    simplex = shrinkSimplex(simplex);\n                }\n            }\n            else {\n                // Otherwise do an outwards contraction\n                contracted = getPoint(centroid, worst, 1 - pOCon, pOCon);\n                if (contracted.fx < reflected.fx) {\n                    simplex = updateSimplex(simplex, contracted);\n                }\n                else {\n                    simplex = shrinkSimplex(simplex);\n                }\n            }\n        }\n        else {\n            simplex = updateSimplex(simplex, reflected);\n        }\n    }\n    return simplex[0];\n}\n/**\n * Prepares the venn data so that it is usable for the layout function.\n * Filter out sets, or intersections that includes sets, that are missing in\n * the data or has (value < 1). Adds missing relations between sets in the\n * data as value = 0.\n * @private\n * @param {Array<object>} data The raw input data.\n * @return {Array<object>} Returns an array of valid venn data.\n */\nfunction processVennData(data, splitter) {\n    var d = isArray(data) ? data : [];\n    var validSets = d\n            .reduce(function (arr,\n        x) {\n            // Check if x is a valid set, and that it is not an duplicate.\n            if (x.sets && isValidSet(x) && arr.indexOf(x.sets[0]) === -1) {\n                arr.push(x.sets[0]);\n        }\n        return arr;\n    }, [])\n        .sort();\n    var mapOfIdToRelation = d.reduce(function (mapOfIdToRelation,\n        relation) {\n            if (relation.sets &&\n                isValidRelation(relation) &&\n                !relation.sets.some(function (set) {\n                    return validSets.indexOf(set) === -1;\n            })) {\n            mapOfIdToRelation[relation.sets.sort().join(splitter)] = {\n                sets: relation.sets,\n                value: relation.value || 0\n            };\n        }\n        return mapOfIdToRelation;\n    }, {});\n    validSets.reduce(function (combinations, set, i, arr) {\n        var remaining = arr.slice(i + 1);\n        remaining.forEach(function (set2) {\n            combinations.push(set + splitter + set2);\n        });\n        return combinations;\n    }, []).forEach(function (combination) {\n        if (!mapOfIdToRelation[combination]) {\n            var obj = {\n                    sets: combination.split(splitter),\n                    value: 0\n                };\n            mapOfIdToRelation[combination] = obj;\n        }\n    });\n    // Transform map into array.\n    return Object\n        .keys(mapOfIdToRelation)\n        .map(function (id) {\n        return mapOfIdToRelation[id];\n    });\n}\n/**\n * Takes two sets and finds the one with the largest total overlap.\n * @private\n * @param {Object} a\n * The first set to compare.\n * @param {Object} b\n * The second set to compare.\n * @return {number}\n * Returns 0 if a and b are equal, <0 if a is greater, >0 if b is greater.\n */\nfunction sortByTotalOverlap(a, b) {\n    if (typeof b.totalOverlap !== 'undefined' &&\n        typeof a.totalOverlap !== 'undefined') {\n        return b.totalOverlap - a.totalOverlap;\n    }\n    return NaN;\n}\n/* *\n *\n *  Default Export\n *\n * */\nvar VennUtils = {\n    geometry: Geometry_GeometryUtilities,\n    geometryCircles: Geometry_CircleUtilities,\n    addOverlapToSets: addOverlapToSets,\n    getCentroid: getCentroid,\n    getDistanceBetweenCirclesByOverlap: getDistanceBetweenCirclesByOverlap,\n    getLabelWidth: getLabelWidth,\n    getMarginFromCircles: getMarginFromCircles,\n    isSet: isSet,\n    layoutGreedyVenn: layoutGreedyVenn,\n    loss: loss,\n    nelderMead: nelderMead,\n    processVennData: processVennData,\n    sortByTotalOverlap: sortByTotalOverlap\n};\n/* harmony default export */ var Venn_VennUtils = (VennUtils);\n\n;// ./code/es5/es-modules/Series/Venn/VennSeries.js\n/* *\n *\n *  Experimental Highcharts module which enables visualization of a Venn\n *  diagram.\n *\n *  (c) 2016-2025 Highsoft AS\n *  Authors: <AUTHORS>\n *\n *  Layout algorithm by Ben Frederickson:\n *  https://www.benfrederickson.com/better-venn-diagrams/\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar VennSeries_extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b,\n        p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar animObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).animObject;\n\nvar color = (highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default()).parse;\n\nvar getAreaOfIntersectionBetweenCircles = Geometry_CircleUtilities.getAreaOfIntersectionBetweenCircles, getCirclesIntersectionPolygon = Geometry_CircleUtilities.getCirclesIntersectionPolygon, isCircle1CompletelyOverlappingCircle2 = Geometry_CircleUtilities.isCircle1CompletelyOverlappingCircle2, VennSeries_isPointInsideAllCircles = Geometry_CircleUtilities.isPointInsideAllCircles, VennSeries_isPointOutsideAllCircles = Geometry_CircleUtilities.isPointOutsideAllCircles;\n\n\nvar VennSeries_getCenterOfPoints = Geometry_GeometryUtilities.getCenterOfPoints;\n\nvar ScatterSeries = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.scatter;\n\n\n\n\nvar addEvent = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).addEvent, VennSeries_extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, VennSeries_isArray = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isArray, VennSeries_isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, VennSeries_isObject = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isObject, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.venn\n *\n * @augments Highcharts.Series\n */\nvar VennSeries = /** @class */ (function (_super) {\n    VennSeries_extends(VennSeries, _super);\n    function VennSeries() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     *  Static Functions\n     *\n     * */\n    /**\n     * Finds the optimal label position by looking for a position that has a low\n     * distance from the internal circles, and as large possible distance to the\n     * external circles.\n     * @private\n     * @todo Optimize the intial position.\n     * @todo Add unit tests.\n     * @param {Array<Highcharts.CircleObject>} internal\n     * Internal circles.\n     * @param {Array<Highcharts.CircleObject>} external\n     * External circles.\n     * @return {Highcharts.PositionObject}\n     * Returns the found position.\n     */\n    VennSeries.getLabelPosition = function (internal, external) {\n        // Get the best label position within the internal circles.\n        var best = internal.reduce(function (best,\n            circle) {\n                var d = circle.r / 2;\n            // Give a set of points with the circle to evaluate as the best\n            // label position.\n            return [\n                { x: circle.x, y: circle.y },\n                { x: circle.x + d, y: circle.y },\n                { x: circle.x - d, y: circle.y },\n                { x: circle.x, y: circle.y + d },\n                { x: circle.x, y: circle.y - d }\n            ]\n                // Iterate the given points and return the one with the\n                // largest margin.\n                .reduce(function (best, point) {\n                var margin = Venn_VennUtils.getMarginFromCircles(point,\n                    internal,\n                    external);\n                // If the margin better than the current best, then\n                // update sbest.\n                if (best.margin < margin) {\n                    best.point = point;\n                    best.margin = margin;\n                }\n                return best;\n            }, best);\n        }, {\n            point: void 0,\n            margin: -Number.MAX_VALUE\n        }).point;\n        // Use nelder mead to optimize the initial label position.\n        var optimal = Venn_VennUtils.nelderMead(function (p) { return -(Venn_VennUtils.getMarginFromCircles({ x: p[0],\n            y: p[1] },\n            internal,\n            external)); },\n            [\n                best.x,\n                best.y\n            ]);\n        // Update best to be the point which was found to have the best margin.\n        best = {\n            x: optimal[0],\n            y: optimal[1]\n        };\n        if (!(VennSeries_isPointInsideAllCircles(best, internal) &&\n            VennSeries_isPointOutsideAllCircles(best, external))) {\n            // If point was either outside one of the internal, or inside one of\n            // the external, then it was invalid and should use a fallback.\n            if (internal.length > 1) {\n                best = VennSeries_getCenterOfPoints(getCirclesIntersectionPolygon(internal));\n            }\n            else {\n                best = {\n                    x: internal[0].x,\n                    y: internal[0].y\n                };\n            }\n        }\n        // Return the best point.\n        return best;\n    };\n    /**\n     * Calculates data label values for a given relations object.\n     *\n     * @private\n     * @todo add unit tests\n     * @param {Highcharts.VennRelationObject} relation A relations object.\n     * @param {Array<Highcharts.VennRelationObject>} setRelations The list of\n     * relations that is a set.\n     * @return {Highcharts.VennLabelValuesObject}\n     * Returns an object containing position and width of the label.\n     */\n    VennSeries.getLabelValues = function (relation, setRelations) {\n        var sets = relation.sets;\n        // Create a list of internal and external circles.\n        var data = setRelations.reduce(function (data,\n            set) {\n                // If the set exists in this relation, then it is internal,\n                // otherwise it will be external.\n                var isInternal = sets.indexOf(set.sets[0]) > -1;\n            var property = isInternal ? 'internal' : 'external';\n            // Add the circle to the list.\n            if (set.circle) {\n                data[property].push(set.circle);\n            }\n            return data;\n        }, {\n            internal: [],\n            external: []\n        });\n        // Filter out external circles that are completely overlapping all\n        // internal\n        data.external = data.external.filter(function (externalCircle) {\n            return data.internal.some(function (internalCircle) {\n                return !isCircle1CompletelyOverlappingCircle2(externalCircle, internalCircle);\n            });\n        });\n        // Calculate the label position.\n        var position = VennSeries.getLabelPosition(data.internal,\n            data.external);\n        // Calculate the label width\n        var width = Venn_VennUtils.getLabelWidth(position,\n            data.internal,\n            data.external);\n        return {\n            position: position,\n            width: width\n        };\n    };\n    /**\n     * Calculates the positions, and the label values of all the sets in the\n     * venn diagram.\n     *\n     * @private\n     * @todo Add support for constrained MDS.\n     * @param {Array<Highchats.VennRelationObject>} relations\n     * List of the overlap between two or more sets, or the size of a single\n     * set.\n     * @return {Highcharts.Dictionary<*>}\n     * List of circles and their calculated positions.\n     */\n    VennSeries.layout = function (relations) {\n        var mapOfIdToShape = {};\n        var mapOfIdToLabelValues = {};\n        // Calculate best initial positions by using greedy layout.\n        if (relations.length > 0) {\n            var mapOfIdToCircles_1 = Venn_VennUtils.layoutGreedyVenn(relations);\n            var setRelations = relations.filter(Venn_VennUtils.isSet);\n            for (var _i = 0, relations_1 = relations; _i < relations_1.length; _i++) {\n                var relation = relations_1[_i];\n                var sets = relation.sets;\n                var id = sets.join();\n                // Get shape from map of circles, or calculate intersection.\n                var shape = Venn_VennUtils.isSet(relation) ?\n                        mapOfIdToCircles_1[id] :\n                        getAreaOfIntersectionBetweenCircles(sets.map(function (set) { return mapOfIdToCircles_1[set]; }));\n                // Calculate label values if the set has a shape\n                if (shape) {\n                    mapOfIdToShape[id] = shape;\n                    mapOfIdToLabelValues[id] = VennSeries.getLabelValues(relation, setRelations);\n                }\n            }\n        }\n        return { mapOfIdToShape: mapOfIdToShape, mapOfIdToLabelValues: mapOfIdToLabelValues };\n    };\n    /**\n     * Calculates the proper scale to fit the cloud inside the plotting area.\n     * @private\n     * @todo add unit test\n     * @param {number} targetWidth\n     * Width of target area.\n     * @param {number} targetHeight\n     * Height of target area.\n     * @param {Highcharts.PolygonBoxObject} field\n     * The playing field.\n     * @return {Highcharts.Dictionary<number>}\n     * Returns the value to scale the playing field up to the size of the target\n     * area, and center of x and y.\n     */\n    VennSeries.getScale = function (targetWidth, targetHeight, field) {\n        var height = field.bottom - field.top, // Top is smaller than bottom\n            width = field.right - field.left, scaleX = width > 0 ? 1 / width * targetWidth : 1, scaleY = height > 0 ? 1 / height * targetHeight : 1, adjustX = (field.right + field.left) / 2, adjustY = (field.top + field.bottom) / 2, scale = Math.min(scaleX, scaleY);\n        return {\n            scale: scale,\n            centerX: targetWidth / 2 - adjustX * scale,\n            centerY: targetHeight / 2 - adjustY * scale\n        };\n    };\n    /**\n     * If a circle is outside a give field, then the boundaries of the field is\n     * adjusted accordingly. Modifies the field object which is passed as the\n     * first parameter.\n     * @private\n     * @todo NOTE: Copied from wordcloud, can probably be unified.\n     * @param {Highcharts.PolygonBoxObject} field\n     * The bounding box of a playing field.\n     * @param {Highcharts.CircleObject} circle\n     * The bounding box for a placed point.\n     * @return {Highcharts.PolygonBoxObject}\n     * Returns a modified field object.\n     */\n    VennSeries.updateFieldBoundaries = function (field, circle) {\n        var left = circle.x - circle.r,\n            right = circle.x + circle.r,\n            bottom = circle.y + circle.r,\n            top = circle.y - circle.r;\n        // TODO improve type checking.\n        if (!VennSeries_isNumber(field.left) || field.left > left) {\n            field.left = left;\n        }\n        if (!VennSeries_isNumber(field.right) || field.right < right) {\n            field.right = right;\n        }\n        if (!VennSeries_isNumber(field.top) || field.top > top) {\n            field.top = top;\n        }\n        if (!VennSeries_isNumber(field.bottom) || field.bottom < bottom) {\n            field.bottom = bottom;\n        }\n        return field;\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /* eslint-disable valid-jsdoc */\n    VennSeries.prototype.animate = function (init) {\n        if (!init) {\n            var series = this,\n                animOptions = animObject(series.options.animation);\n            var _loop_1 = function (point) {\n                    var args = point.shapeArgs;\n                if (point.graphic && args) {\n                    var attr = {},\n                        animate = {};\n                    if (args.d) {\n                        // If shape is a path, then animate opacity.\n                        attr.opacity = 0.001;\n                    }\n                    else {\n                        // If shape is a circle, then animate radius.\n                        attr.r = 0;\n                        animate.r = args.r;\n                    }\n                    point.graphic\n                        .attr(attr)\n                        .animate(animate, animOptions);\n                    // If shape is path, then fade it in after the circles\n                    // animation\n                    if (args.d) {\n                        setTimeout(function () {\n                            var _a;\n                            (_a = point === null || point === void 0 ? void 0 : point.graphic) === null || _a === void 0 ? void 0 : _a.animate({\n                                opacity: 1\n                            });\n                        }, animOptions.duration);\n                    }\n                }\n            };\n            for (var _i = 0, _a = series.points; _i < _a.length; _i++) {\n                var point = _a[_i];\n                _loop_1(point);\n            }\n        }\n    };\n    /**\n     * Draw the graphics for each point.\n     * @private\n     */\n    VennSeries.prototype.drawPoints = function () {\n        var series = this, \n            // Series properties\n            chart = series.chart,\n            group = series.group,\n            points = series.points || [], \n            // Chart properties\n            renderer = chart.renderer;\n        // Iterate all points and calculate and draw their graphics.\n        for (var _i = 0, points_1 = points; _i < points_1.length; _i++) {\n            var point = points_1[_i];\n            var attribs = {\n                    zIndex: VennSeries_isArray(point.sets) ? point.sets.length : 0\n                },\n                shapeArgs = point.shapeArgs;\n            // Add point attribs\n            if (!chart.styledMode) {\n                VennSeries_extend(attribs, series.pointAttribs(point, point.state));\n            }\n            // Draw the point graphic.\n            Series_DrawPointUtilities.draw(point, {\n                isNew: !point.graphic,\n                animatableAttribs: shapeArgs,\n                attribs: attribs,\n                group: group,\n                renderer: renderer,\n                shapeType: (shapeArgs === null || shapeArgs === void 0 ? void 0 : shapeArgs.d) ? 'path' : 'circle'\n            });\n        }\n    };\n    VennSeries.prototype.init = function () {\n        ScatterSeries.prototype.init.apply(this, arguments);\n        // Venn's opacity is a different option from other series\n        delete this.opacity;\n    };\n    /**\n     * Calculates the style attributes for a point. The attributes can vary\n     * depending on the state of the point.\n     * @private\n     * @param {Highcharts.Point} point\n     * The point which will get the resulting attributes.\n     * @param {string} [state]\n     * The state of the point.\n     * @return {Highcharts.SVGAttributes}\n     * Returns the calculated attributes.\n     */\n    VennSeries.prototype.pointAttribs = function (point, state) {\n        var series = this,\n            seriesOptions = series.options || {},\n            pointOptions = (point === null || point === void 0 ? void 0 : point.options) || {},\n            stateOptions = (state && seriesOptions.states[state]) || {},\n            options = merge(seriesOptions, { color: point === null || point === void 0 ? void 0 : point.color },\n            pointOptions,\n            stateOptions);\n        // Return resulting values for the attributes.\n        return {\n            'fill': color(options.color)\n                .brighten(options.brightness)\n                .get(),\n            // Set opacity directly to the SVG element, not to pattern #14372.\n            opacity: options.opacity,\n            'stroke': options.borderColor,\n            'stroke-width': options.borderWidth,\n            'dashstyle': options.borderDashStyle\n        };\n    };\n    VennSeries.prototype.translate = function () {\n        var _a;\n        var chart = this.chart;\n        this.dataTable.modified = this.dataTable;\n        this.generatePoints();\n        // Process the data before passing it into the layout function.\n        var relations = Venn_VennUtils.processVennData(this.options.data,\n            VennSeries.splitter);\n        // Calculate the positions of each circle.\n        var _b = VennSeries.layout(relations),\n            mapOfIdToShape = _b.mapOfIdToShape,\n            mapOfIdToLabelValues = _b.mapOfIdToLabelValues;\n        // Calculate the scale, and center of the plot area.\n        var field = Object.keys(mapOfIdToShape)\n                .filter(function (key) {\n                var shape = mapOfIdToShape[key];\n            return shape && VennSeries_isNumber(shape.r);\n        })\n            .reduce(function (field, key) { return VennSeries.updateFieldBoundaries(field, mapOfIdToShape[key]); }, {\n            top: 0,\n            bottom: 0,\n            left: 0,\n            right: 0\n        }), scaling = VennSeries.getScale(chart.plotWidth, chart.plotHeight, field), scale = scaling.scale, centerX = scaling.centerX, centerY = scaling.centerY;\n        // Iterate all points and calculate and draw their graphics.\n        for (var _i = 0, _c = this.points; _i < _c.length; _i++) {\n            var point = _c[_i];\n            var sets = VennSeries_isArray(point.sets) ? point.sets : [],\n                id = sets.join(),\n                shape = mapOfIdToShape[id],\n                dataLabelValues = mapOfIdToLabelValues[id] || {},\n                dlOptions = (_a = point.options) === null || _a === void 0 ? void 0 : _a.dataLabels;\n            var shapeArgs = void 0,\n                dataLabelWidth = dataLabelValues.width,\n                dataLabelPosition = dataLabelValues.position;\n            if (shape) {\n                if (shape.r) {\n                    shapeArgs = {\n                        x: centerX + shape.x * scale,\n                        y: centerY + shape.y * scale,\n                        r: shape.r * scale\n                    };\n                }\n                else if (shape.d) {\n                    var d = shape.d;\n                    d.forEach(function (seg) {\n                        if (seg[0] === 'M') {\n                            seg[1] = centerX + seg[1] * scale;\n                            seg[2] = centerY + seg[2] * scale;\n                        }\n                        else if (seg[0] === 'A') {\n                            seg[1] = seg[1] * scale;\n                            seg[2] = seg[2] * scale;\n                            seg[6] = centerX + seg[6] * scale;\n                            seg[7] = centerY + seg[7] * scale;\n                        }\n                    });\n                    shapeArgs = { d: d };\n                }\n                // Scale the position for the data label.\n                if (dataLabelPosition) {\n                    dataLabelPosition.x = centerX + dataLabelPosition.x * scale;\n                    dataLabelPosition.y = centerY + dataLabelPosition.y * scale;\n                }\n                else {\n                    dataLabelPosition = {};\n                }\n                if (VennSeries_isNumber(dataLabelWidth)) {\n                    dataLabelWidth = Math.round(dataLabelWidth * scale);\n                }\n            }\n            point.shapeArgs = shapeArgs;\n            // Placement for the data labels\n            if (dataLabelPosition && shapeArgs) {\n                point.plotX = dataLabelPosition.x;\n                point.plotY = dataLabelPosition.y;\n            }\n            // Add width for the data label\n            if (dataLabelWidth && shapeArgs) {\n                point.dlOptions = merge(true, {\n                    style: {\n                        width: dataLabelWidth\n                    }\n                }, VennSeries_isObject(dlOptions, true) ? dlOptions : void 0);\n            }\n            // Set name for usage in tooltip and in data label.\n            point.name = point.options.name || sets.join('∩');\n        }\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    VennSeries.splitter = 'highcharts-split';\n    VennSeries.defaultOptions = merge(ScatterSeries.defaultOptions, Venn_VennSeriesDefaults);\n    return VennSeries;\n}(ScatterSeries));\nVennSeries_extend(VennSeries.prototype, {\n    axisTypes: [],\n    directTouch: true,\n    isCartesian: false,\n    pointArrayMap: ['value'],\n    pointClass: Venn_VennPoint,\n    utils: Venn_VennUtils\n});\n// Modify final series options.\naddEvent(VennSeries, 'afterSetOptions', function (e) {\n    var options = e.options,\n        states = options.states || {};\n    if (this.is('venn')) {\n        // Explicitly disable all halo options.\n        for (var _i = 0, _a = Object.keys(states); _i < _a.length; _i++) {\n            var state = _a[_i];\n            states[state].halo = false;\n        }\n    }\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('venn', VennSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Venn_VennSeries = ((/* unused pure expression or super */ null && (VennSeries)));\n\n;// ./code/es5/es-modules/masters/modules/venn.js\n\n\n\n\n/* harmony default export */ var venn_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__620__", "__WEBPACK_EXTERNAL_MODULE__512__", "GeometryUtilities", "extendStatics", "CircleUtilities", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "venn_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_", "highcharts_Color_commonjs_highcharts_Color_commonjs2_highcharts_Color_root_Highcharts_Color_default", "getCenterOfPoints", "points", "sum", "reduce", "point", "x", "y", "length", "getDistanceBetweenPoints", "p1", "p2", "Math", "sqrt", "pow", "getAngleBetweenPoints", "atan2", "pointInPolygon", "_a", "polygon", "i", "j", "len", "inside", "_b", "x1", "y1", "_c", "x2", "y2", "Geometry_GeometryUtilities", "round", "decimals", "getAreaOfCircle", "r", "Error", "PI", "getCircularSegmentArea", "h", "acos", "getCircleCircleIntersection", "c1", "c2", "r1", "r2", "abs", "r1Square", "x0", "y0", "rx", "ry", "getCirclesIntersectionPoints", "circles", "arr", "additional", "slice", "indexes", "concat", "map", "p", "isPointInsideCircle", "circle", "isPointInsideAllCircles", "some", "getCirclesIntersectionPolygon", "filter", "getOverlapBetweenCircles", "overlap", "d1", "isCircle1CompletelyOverlappingCircle2", "circle1", "circle2", "isPointOutsideAllCircles", "getAreaOfIntersectionBetweenCircles", "result", "intersectionPoints", "center_1", "startPoint", "angle", "sort", "b", "arcs", "data", "midPoint", "arc", "index", "indexOf", "angle1", "angle2", "angleDiff", "width", "sin", "cos", "largeArc", "push", "unshift", "center", "Geometry_CircleUtilities", "__assign", "assign", "t", "s", "arguments", "apply", "Series_DrawPointUtilities", "draw", "params", "animatableAttribs", "onComplete", "css", "renderer", "animation", "series", "chart", "hasRendered", "options", "graphic", "attribs", "getClassName", "shouldDraw", "shapeType", "text", "image", "imageUrl", "attr", "shapeArgs", "add", "group", "animate", "isNew", "destroy_1", "destroy", "keys", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "__extends", "setPrototypeOf", "__proto__", "Array", "__", "constructor", "create", "ScatterPoint", "seriesTypes", "scatter", "pointClass", "isNumber", "VennPoint", "_super", "<PERSON><PERSON><PERSON><PERSON>", "value", "Venn_VennSeriesDefaults", "borderColor", "borderDashStyle", "borderWidth", "brighten", "clip", "colorByPoint", "dataLabels", "enabled", "verticalAlign", "formatter", "name", "inactiveOtherPoints", "marker", "opacity", "showInLegend", "legendType", "states", "hover", "select", "color", "inactive", "tooltip", "pointFormat", "legendSymbol", "VennUtils_assign", "getOverlapBetweenCirclesByDistance", "VennUtils_getDistanceBetweenPoints", "extend", "isArray", "VennUtils_isNumber", "isObject", "isString", "addOverlapToSets", "relations", "mapOfIdToProps", "relation", "sets", "for<PERSON>ach", "set", "totalOverlap", "overlapping", "isSet", "properties", "bisect", "f", "tolerance", "maxIterations", "fX", "fA", "fB", "nMax", "tol", "delta", "getCentroid", "simplex", "getDistanceBetweenCirclesByOverlap", "distance", "maxDistance", "isValidRelation", "invalid", "loss", "mapOfIdToCircle", "totalLoss", "diff", "wantedOverlap", "sortByTotalOverlap", "NaN", "Venn_VennUtils", "geometry", "geometryCircles", "<PERSON><PERSON><PERSON><PERSON>", "pos", "internal", "external", "radius", "min", "Infinity", "filteredExternals", "findDistance", "direction", "testPos", "Number", "MAX_VALUE", "getMarginFromCircles", "margin", "m", "layoutGreedyVenn", "positionedSets", "mapOfIdToCircles", "positionSet", "coordinates", "sortedByOverlap", "shift", "relationsWithTwoSets", "_i", "sortedByOverlap_1", "_loop_1", "bestPosition", "best", "positionedSet", "positionedCircle", "possibleCoordinates", "positionedSet2", "positionedCircle2", "overlap2", "distance2", "possibleCoordinates_1", "currentLoss", "nelderMead", "fn", "initial", "sortByFx", "fx", "weightedSum", "weight1", "v1", "weight2", "v2", "updateSimplex", "shrinkSimplex", "getPoint", "centroid", "worst", "getSimplex", "reflected", "expanded", "contracted", "pCon", "processVennData", "splitter", "validSets", "mapOfIdToRelation", "join", "combinations", "remaining", "set2", "combination", "split", "id", "VennSeries_extends", "TypeError", "String", "animObject", "parse", "VennSeries_isPointInsideAllCircles", "VennSeries_isPointOutsideAllCircles", "VennSeries_getCenterOfPoints", "ScatterSeries", "addEvent", "VennSeries_extend", "VennSeries_isArray", "VennSeries_isNumber", "VennSeries_isObject", "merge", "VennSeries", "getLabelPosition", "optimal", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setRelations", "isInternal", "externalCircle", "internalCircle", "position", "layout", "mapOfIdToShape", "mapOfIdToLabelValues", "mapOfIdToCircles_1", "relations_1", "shape", "getScale", "targetWidth", "targetHeight", "field", "height", "bottom", "top", "right", "left", "adjustX", "adjustY", "scale", "centerX", "centerY", "updateFieldBoundaries", "init", "animOptions", "args", "setTimeout", "duration", "drawPoints", "points_1", "zIndex", "styledMode", "pointAttribs", "state", "seriesOptions", "pointOptions", "stateOptions", "brightness", "translate", "dataTable", "modified", "generatePoints", "scaling", "plot<PERSON>id<PERSON>", "plotHeight", "dataLabelValues", "dlOptions", "dataLabelWidth", "dataLabelPosition", "seg", "plotX", "plotY", "style", "defaultOptions", "axisTypes", "directTouch", "isCartesian", "pointArrayMap", "utils", "e", "is", "halo", "registerSeriesType"], "mappings": "CAUA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,KAAQ,CAAEA,QAAQ,cAAc,cAAiB,EAChH,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,0BAA2B,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,QAAQ,CAAE,CAAC,wBAAwB,iBAAiB,CAAC,CAAEJ,GACvI,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,0BAA0B,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,KAAQ,CAAEA,QAAQ,cAAc,cAAiB,EAE3IJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACpH,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,CAAEC,CAAgC,EACtH,OAAgB,AAAC,WACP,aACA,IA8GCC,EA6mBHC,EA48BAA,EA1jDJD,EAuHAE,EApOUC,EAAuB,CAE/B,IACC,SAASV,CAAM,EAEtBA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,SAASN,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGIO,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaf,OAAO,CAG5B,IAAIC,EAASW,CAAwB,CAACE,EAAS,CAAG,CAGjDd,QAAS,CAAC,CACX,EAMA,OAHAW,CAAmB,CAACG,EAAS,CAACb,EAAQA,EAAOD,OAAO,CAAEa,GAG/CZ,EAAOD,OAAO,AACtB,CAMCa,EAAoBI,CAAC,CAAG,SAAShB,CAAM,EACtC,IAAIiB,EAASjB,GAAUA,EAAOkB,UAAU,CACvC,WAAa,OAAOlB,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAY,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASpB,CAAO,CAAEsB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACxB,EAASuB,IAC5EE,OAAOC,cAAc,CAAC1B,EAASuB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,EAAU,CACzD,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAA+FzB,EAAoB,KACnH0B,EAAmH1B,EAAoBI,CAAC,CAACqB,EA+CzI9B,EA7BOA,EA4FRA,GAAsBA,CAAAA,EAAoB,CAAC,CAAA,GA/DxBgC,iBAAiB,CAZnC,SAA2BC,CAAM,EAC7B,IAAIC,EAAMD,EAAOE,MAAM,CAAC,SAAUD,CAAG,CACjCE,CAAK,EAGL,OAFIF,EAAIG,CAAC,EAAID,EAAMC,CAAC,CACpBH,EAAII,CAAC,EAAIF,EAAME,CAAC,CACTJ,CACX,EAAG,CAAEG,EAAG,EAAGC,EAAG,CAAE,GAChB,MAAO,CACHD,EAAGH,EAAIG,CAAC,CAAGJ,EAAOM,MAAM,CACxBD,EAAGJ,EAAII,CAAC,CAAGL,EAAOM,MAAM,AAC5B,CACJ,EAoBAvC,EAAkBwC,wBAAwB,CAH1C,SAAkCC,CAAE,CAAEC,CAAE,EACpC,OAAOC,KAAKC,IAAI,CAACD,KAAKE,GAAG,CAACH,EAAGL,CAAC,CAAGI,EAAGJ,CAAC,CAAE,GAAKM,KAAKE,GAAG,CAACH,EAAGJ,CAAC,CAAGG,EAAGH,CAAC,CAAE,GACtE,EAaAtC,EAAkB8C,qBAAqB,CAHvC,SAA+BL,CAAE,CAAEC,CAAE,EACjC,OAAOC,KAAKI,KAAK,CAACL,EAAGL,CAAC,CAAGI,EAAGJ,CAAC,CAAEK,EAAGJ,CAAC,CAAGG,EAAGH,CAAC,CAC9C,EAgCAtC,EAAkBgD,cAAc,CAxBhC,SAAwBC,CAAE,CAAEC,CAAO,EAC/B,IAGIC,EACAC,EAJAf,EAAIY,EAAGZ,CAAC,CACRC,EAAIW,EAAGX,CAAC,CACRe,EAAMH,EAAQX,MAAM,CAGpBe,EAAS,CAAA,EACb,IAAKH,EAAI,EAAGC,EAAIC,EAAM,EAAGF,EAAIE,EAAKD,EAAID,IAAK,CACvC,IAAII,EAAKL,CAAO,CAACC,EAAE,CACfK,EAAKD,CAAE,CAAC,EAAE,CACVE,EAAKF,CAAE,CAAC,EAAE,CACVG,EAAKR,CAAO,CAACE,EAAE,CACfO,EAAKD,CAAE,CAAC,EAAE,CACVE,EAAKF,CAAE,CAAC,EAAE,CACVD,EAAKnB,GAAMsB,EAAKtB,GACfD,EAAI,AAACsB,CAAAA,EAAKH,CAAC,EACPlB,CAAAA,EAAImB,CAAC,EACLG,CAAAA,EAAKH,CAAC,EACPD,GACJF,CAAAA,EAAS,CAACA,CAAK,CAEvB,CACA,OAAOA,CACX,EAQyB,IAAIO,EAA8B7D,EAc3D8C,EAAwBe,EAA2Bf,qBAAqB,CAAEd,EAAoB6B,EAA2B7B,iBAAiB,CAAEQ,EAA2BqB,EAA2BrB,wBAAwB,EAO9N,AAAC,SAAUtC,CAAe,EAkBtB,SAAS4D,EAAMzB,CAAC,CAAE0B,CAAQ,EACtB,IAAIlD,EAAI8B,KAAKE,GAAG,CAAC,GACbkB,GACJ,OAAOpB,KAAKmB,KAAK,CAACzB,EAAIxB,GAAKA,CAC/B,CAaA,SAASmD,EAAgBC,CAAC,EACtB,GAAIA,GAAK,EACL,MAAM,AAAIC,MAAM,+CAEpB,OAAOvB,KAAKwB,EAAE,CAAGF,EAAIA,CACzB,CAmBA,SAASG,EAAuBH,CAAC,CAAEI,CAAC,EAChC,OAAQJ,EAAIA,EAAItB,KAAK2B,IAAI,CAAC,EAAID,EAAIJ,GAC9B,AAACA,CAAAA,EAAII,CAAAA,EAAK1B,KAAKC,IAAI,CAACyB,EAAK,CAAA,EAAIJ,EAAII,CAAAA,EACzC,CA8DA,SAASE,EAA4BC,CAAE,CAAEC,CAAE,EACvC,IAAI7D,EAAI4B,EAAyBgC,EAC7BC,GACAC,EAAKF,EAAGP,CAAC,CACTU,EAAKF,EAAGR,CAAC,CACThC,EAAS,EAAE,CACf,GAAIrB,EAAI8D,EAAKC,GAAM/D,EAAI+B,KAAKiC,GAAG,CAACF,EAAKC,GAAK,CAGtC,IAAIE,EAAWH,EAAKA,EAEhBrC,EAAI,AAACwC,CAAAA,EAF0BF,EAAKA,EAET/D,EAAIA,CAAAA,EAAM,CAAA,EAAIA,CAAAA,EAEzC0B,EAAIK,KAAKC,IAAI,CAACiC,EAAWxC,EAAIA,GAAImB,EAAKgB,EAAGnC,CAAC,CAAEsB,EAAKc,EAAGpC,CAAC,CAAEoB,EAAKe,EAAGlC,CAAC,CAAEsB,EAAKa,EAAGnC,CAAC,CAAEwC,EAAKtB,EAAKnB,EAAKsB,CAAAA,EAAKH,CAAC,EAAK5C,EAAGmE,EAAKtB,EAAKpB,EAAKuB,CAAAA,EAAKH,CAAC,EAAK7C,EAAGoE,EAAK,CAAA,CAAA,AAAc1C,EAAI1B,EAAhBgD,CAAAA,EAAKH,CAAC,CAAW,EAAGwB,EAAK,CAAA,CAAA,AAAc3C,EAAI1B,EAAhB+C,CAAAA,EAAKH,CAAC,CAAW,EAC9LvB,EAAS,CACL,CAAEI,EAAGyB,EAAMgB,EAAKE,EAAI,IAAK1C,EAAGwB,EAAMiB,EAAKE,EAAI,GAAI,EAC/C,CAAE5C,EAAGyB,EAAMgB,EAAKE,EAAI,IAAK1C,EAAGwB,EAAMiB,EAAKE,EAAI,GAAI,EAClD,AACL,CACA,OAAOhD,CACX,CAaA,SAASiD,EAA6BC,CAAO,EACzC,OAAOA,EAAQhD,MAAM,CAAC,SAAUF,CAAM,CAAEuC,CAAE,CAAErB,CAAC,CAAEiC,CAAG,EAC9C,IAAIC,EAAaD,EACRE,KAAK,CAACnC,EAAI,GACVhB,MAAM,CAAC,SAAUF,CAAM,CAC5BwC,CAAE,CACFrB,CAAC,EACG,IAAImC,EAAU,CAACpC,EACnBC,EAAID,EAAI,EAAE,CACV,OAAOlB,EAAOuD,MAAM,CAACjB,EAA4BC,EAAIC,GAAIgB,GAAG,CAAC,SAAUC,CAAC,EAEpE,OADAA,EAAEH,OAAO,CAAGA,EACLG,CACX,GACJ,EAAG,EAAE,EACL,OAAOzD,EAAOuD,MAAM,CAACH,EACzB,EAAG,EAAE,CACT,CAiCA,SAASM,EAAoBvD,CAAK,CAAEwD,CAAM,EACtC,OAAOpD,EAAyBJ,EAAOwD,IAAWA,EAAO3B,CAAC,CAAG,KACjE,CAgBA,SAAS4B,EAAwBzD,CAAK,CAAE+C,CAAO,EAC3C,MAAO,CAACA,EAAQW,IAAI,CAAC,SAAUF,CAAM,EACjC,MAAO,CAACD,EAAoBvD,EAAOwD,EACvC,EACJ,CAoCA,SAASG,EAA8BZ,CAAO,EAC1C,OAAOD,EAA6BC,GAC/Ba,MAAM,CAAC,SAAUN,CAAC,EACnB,OAAOG,EAAwBH,EAAGP,EACtC,EACJ,CAtPAjF,EAAgB4D,KAAK,CAAGA,EAkBxB5D,EAAgB8D,eAAe,CAAGA,EAsBlC9D,EAAgBkE,sBAAsB,CAAGA,EA4CzClE,EAAgB+F,wBAAwB,CAvBxC,SAAkCvB,CAAE,CAAEC,CAAE,CAAE/D,CAAC,EACvC,IAAIsF,EAAU,EAGd,GAAItF,EAAI8D,EAAKC,EAAI,CACb,GAAI/D,GAAK+B,KAAKiC,GAAG,CAACD,EAAKD,GAGnBwB,EAAUlC,EAAgBU,EAAKC,EAAKD,EAAKC,OAExC,CAED,IAAIwB,EAAK,AAACzB,CAAAA,EAAKA,EAAKC,EAAKA,EAAK/D,EAAIA,CAAAA,EAAM,CAAA,EAAIA,CAAAA,EAG5CsF,EAAW9B,EAAuBM,EAAIA,EAAKyB,GACvC/B,EAAuBO,EAAIA,EAFtB/D,CAAAA,EAAIuF,CAAC,EAGlB,CAEAD,EAAUpC,EAAMoC,EAAS,GAC7B,CACA,OAAOA,CACX,EAuCAhG,EAAgBqE,2BAA2B,CAAGA,EA6B9CrE,EAAgBgF,4BAA4B,CAAGA,EAmB/ChF,EAAgBkG,qCAAqC,CAHrD,SAA+CC,CAAO,CAAEC,CAAO,EAC3D,OAAO9D,EAAyB6D,EAASC,GAAWA,EAAQrC,CAAC,CAAGoC,EAAQpC,CAAC,CAAG,KAChF,EAiBA/D,EAAgByF,mBAAmB,CAAGA,EAoBtCzF,EAAgB2F,uBAAuB,CAAGA,EAsB1C3F,EAAgBqG,wBAAwB,CALxC,SAAkCnE,CAAK,CAAE+C,CAAO,EAC5C,MAAO,CAACA,EAAQW,IAAI,CAAC,SAAUF,CAAM,EACjC,OAAOD,EAAoBvD,EAAOwD,EACtC,EACJ,EAoBA1F,EAAgB6F,6BAA6B,CAAGA,EA2GhD7F,EAAgBsG,mCAAmC,CA5FnD,SAA6CrB,CAAO,EAChD,IACIsB,EADAC,EAAqBX,EAA8BZ,GAEvD,GAAIuB,EAAmBnE,MAAM,CAAG,EAAG,CAE/B,IAAIoE,EAAW3E,EAAkB0E,GAW7BE,EAAaF,AAVjBA,CAAAA,EAAqBA,EAEhBjB,GAAG,CAAC,SAAUC,CAAC,EAEhB,OADAA,EAAEmB,KAAK,CAAG/D,EAAsB6D,EAAUjB,GACnCA,CACX,GAEKoB,IAAI,CAAC,SAAUjG,CAAC,CAAEkG,CAAC,EACpB,OAAOA,EAAEF,KAAK,CAAGhG,EAAEgG,KAAK,AAC5B,EAAC,CACkC,CAACH,EAAmBnE,MAAM,CAAG,EAAE,CAC9DyE,EAAON,EACFvE,MAAM,CAAC,SAAU8E,CAAI,CAC1BxE,CAAE,EACE,IAAImE,EAAaK,EAAKL,UAAU,CACpCM,EAAWlF,EAAkB,CAAC4E,EAC9BnE,EAAG,EAGC0E,EAAM1E,EAAG8C,OAAO,CAGXS,MAAM,CAAC,SAAUoB,CAAK,EACvB,OAAOR,EAAWrB,OAAO,CAAC8B,OAAO,CAACD,GAAS,EACnD,GAGKjF,MAAM,CAAC,SAAUgF,CAAG,CAAEC,CAAK,EAC5B,IAAIxB,EAAST,CAAO,CAACiC,EAAM,CACvBE,EAASxE,EAAsB8C,EAC/BnD,GACA8E,EAASzE,EAAsB8C,EAC/BgB,GACAY,EAAYD,EAASD,EAChBC,CAAAA,EAASD,EAAS,EAAI3E,KAAKwB,EAAE,CAAG,CAAA,EACrC0C,EAAQU,EAASC,EAAY,EAC7BC,EAAQjF,EAAyB0E,EAAU,CACvC7E,EAAGuD,EAAOvD,CAAC,CAAGuD,EAAO3B,CAAC,CAAGtB,KAAK+E,GAAG,CAACb,GAClCvE,EAAGsD,EAAOtD,CAAC,CAAGsD,EAAO3B,CAAC,CAAGtB,KAAKgF,GAAG,CAACd,EACtC,GACA5C,EAAI2B,EAAO3B,CAAC,CAiBhB,OAdIwD,EAAQxD,AAAI,EAAJA,GACRwD,CAAAA,EAAQxD,AAAI,EAAJA,CAAI,EAGZ,CAAA,CAACkD,GAAOA,EAAIM,KAAK,CAAGA,CAAI,GACxBN,CAAAA,EAAM,CACFlD,EAAGA,EACH2D,SAAUH,CAAAA,CAAAA,EAAQxD,CAAAA,EAClBwD,MAAOA,EACPpF,EAAGI,EAAGJ,CAAC,CACPC,EAAGG,EAAGH,CAAC,AACX,CAAA,EAGG6E,CACX,EAAG,MAEH,GAAIA,EAAK,CACL,IAAIlD,EAAIkD,EAAIlD,CAAC,CACbgD,EAAKD,IAAI,CAACa,IAAI,CAAC,CAAC,IAAK5D,EAAGA,EAAG,EAAGkD,EAAIS,QAAQ,CAAE,EAAGT,EAAI9E,CAAC,CAAE8E,EAAI7E,CAAC,CAAC,EAC5D2E,EAAKL,UAAU,CAAGnE,CACtB,CACA,OAAOwE,CACX,EAAG,CACCL,WAAYA,EACZI,KAAM,EAAE,AACZ,GAAGA,IAAI,AACa,CAAA,IAAhBA,EAAKzE,MAAM,EAGNyE,AAAgB,IAAhBA,EAAKzE,MAAM,GAIhByE,EAAKc,OAAO,CAAC,CAAC,IAAKlB,EAAWvE,CAAC,CAAEuE,EAAWtE,CAAC,CAAC,EAC9CmE,EAAS,CACLsB,OAAQpB,EACR/F,EAAGoG,CACP,EAER,CACA,OAAOP,CACX,CAEJ,EAAGvG,GAAoBA,CAAAA,EAAkB,CAAC,CAAA,GAMb,IAAI8H,EAA4B9H,EASzD+H,EAAgD,WAShD,MAAOA,AARPA,CAAAA,EAAWhH,OAAOiH,MAAM,EAAI,SAASC,CAAC,EAClC,IAAK,IAAIC,EAAGjF,EAAI,EAAG1C,EAAI4H,UAAU9F,MAAM,CAAEY,EAAI1C,EAAG0C,IAE5C,IAAK,IAAIuC,KADT0C,EAAIC,SAAS,CAAClF,EAAE,CACKlC,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC2G,EAAG1C,IACzDyC,CAAAA,CAAC,CAACzC,EAAE,CAAG0C,CAAC,CAAC1C,EAAE,AAAD,EAElB,OAAOyC,CACX,CAAA,EACgBG,KAAK,CAAC,IAAI,CAAED,UAChC,EA2EiCE,EAHR,CACrBC,KAzDJ,SAAcpG,CAAK,CAAEqG,CAAM,EACvB,IAAIC,EAAoBD,EAAOC,iBAAiB,CAC5CC,EAAaF,EAAOE,UAAU,CAC9BC,EAAMH,EAAOG,GAAG,CAChBC,EAAWJ,EAAOI,QAAQ,CAC1BC,EAAY,AAAC1G,EAAM2G,MAAM,EAAI3G,EAAM2G,MAAM,CAACC,KAAK,CAACC,WAAW,CAEvD,KAAK,EAEJ7G,EAAM2G,MAAM,EACT3G,EAAM2G,MAAM,CAACG,OAAO,CAACJ,SAAS,CACtCK,EAAU/G,EAAM+G,OAAO,CAE3B,GADAV,EAAOW,OAAO,CAAGnB,EAASA,EAAS,CAAC,EAAGQ,EAAOW,OAAO,EAAG,CAAE,MAAShH,EAAMiH,YAAY,EAAG,IAAM,CAAC,EAC1FjH,EAAMkH,UAAU,GACZH,IAWD/G,EAAM+G,OAAO,CATTA,EADAV,AAAqB,SAArBA,EAAOc,SAAS,CACNV,EAASW,IAAI,GAElBf,AAAqB,UAArBA,EAAOc,SAAS,CACXV,EAASY,KAAK,CAAChB,EAAOiB,QAAQ,EAAI,IACvCC,IAAI,CAAClB,EAAOmB,SAAS,EAAI,CAAC,GAGrBf,CAAQ,CAACJ,EAAOc,SAAS,CAAC,CAACd,EAAOmB,SAAS,EAAI,CAAC,GAG9DT,EAAQU,GAAG,CAACpB,EAAOqB,KAAK,GAExBlB,GACAO,EAAQP,GAAG,CAACA,GAEhBO,EACKQ,IAAI,CAAClB,EAAOW,OAAO,EACnBW,OAAO,CAACrB,EAAmBD,CAAAA,EAAOuB,KAAK,EAAWlB,EAAWH,QAEjE,GAAIQ,EAAS,CACd,IAAIc,EAAY,WACR7H,EAAM+G,OAAO,CAAGA,EAAWA,GAAWA,EAAQe,OAAO,GAC/B,YAAtB,OAAOvB,GACPA,GAER,CAEI1H,CAAAA,OAAOkJ,IAAI,CAACzB,GAAmBnG,MAAM,CACrC4G,EAAQY,OAAO,CAACrB,EAAmB,KAAK,EAAG,WAAc,OAAOuB,GAAa,GAG7EA,GAER,CACJ,CAQA,EAIIG,EAAmI/J,EAAoB,KACvJgK,EAAuJhK,EAAoBI,CAAC,CAAC2J,GAmB7KE,GACIrK,EAAgB,SAAUW,CAAC,CAC3BmG,CAAC,EAMD,MAAO9G,AALHA,CAAAA,EAAgBgB,OAAOsJ,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAU7J,CAAC,CAC1DmG,CAAC,EAAInG,EAAE4J,SAAS,CAAGzD,CAAG,GACd,SAAUnG,CAAC,CACnBmG,CAAC,EAAI,IAAK,IAAIrB,KAAKqB,EAAOA,EAAEvF,cAAc,CAACkE,IAAI9E,CAAAA,CAAC,CAAC8E,EAAE,CAAGqB,CAAC,CAACrB,EAAE,AAAD,CAAG,CAAA,EACvC9E,EAAGmG,EAC5B,EACO,SAAUnG,CAAC,CAAEmG,CAAC,EAEjB,SAAS2D,IAAO,IAAI,CAACC,WAAW,CAAG/J,CAAG,CADtCX,EAAcW,EAAGmG,GAEjBnG,EAAEW,SAAS,CAAGwF,AAAM,OAANA,EAAa9F,OAAO2J,MAAM,CAAC7D,GAAM2D,CAAAA,EAAGnJ,SAAS,CAAGwF,EAAExF,SAAS,CAAE,IAAImJ,CAAG,CACtF,GAGAG,EAAe,AAACR,IAA2IS,WAAW,CAACC,OAAO,CAACxJ,SAAS,CAACyJ,UAAU,CAEnMC,EAAW,AAACpJ,IAA+EoJ,QAAQ,CAMnGC,EAA2B,SAAUC,CAAM,EAE3C,SAASD,IACL,OAAOC,AAAW,OAAXA,GAAmBA,EAAO7C,KAAK,CAAC,IAAI,CAAED,YAAc,IAAI,AACnE,CAaA,OAhBAiC,EAAUY,EAAWC,GASrBD,EAAU3J,SAAS,CAAC6J,OAAO,CAAG,WAC1B,OAAOH,EAAS,IAAI,CAACI,KAAK,CAC9B,EACAH,EAAU3J,SAAS,CAAC+H,UAAU,CAAG,WAE7B,MAAO,CAAC,CAAC,IAAI,CAACM,SAAS,AAC3B,EACOsB,CACX,EAAEL,GAmM+BS,EA1IR,CACrBC,YAAa,UACbC,gBAAiB,QACjBC,YAAa,EACbC,SAAU,EACVC,KAAM,CAAA,EACNC,aAAc,CAAA,EACdC,WAAY,CACRC,QAAS,CAAA,EACTC,cAAe,SACfC,UAAW,WACP,OAAO,IAAI,CAAC5J,KAAK,CAAC6J,IAAI,AAC1B,CACJ,EAMAC,oBAAqB,CAAA,EAKrBC,OAAQ,CAAA,EACRC,QAAS,IACTC,aAAc,CAAA,EAMdC,WAAY,QACZC,OAAQ,CAIJC,MAAO,CACHJ,QAAS,EACTb,YAAa,SACjB,EAIAkB,OAAQ,CACJC,MAAO,UACPnB,YAAa,UACbzC,UAAW,CAAA,CACf,EACA6D,SAAU,CACNP,QAAS,IACb,CACJ,EACAQ,QAAS,CACLC,YAAa,6BACjB,EACAC,aAAc,WAClB,EAqGIC,EAAwD,WASxD,MAAOA,AARPA,CAAAA,EAAmB9L,OAAOiH,MAAM,EAAI,SAASC,CAAC,EAC1C,IAAK,IAAIC,EAAGjF,EAAI,EAAG1C,EAAI4H,UAAU9F,MAAM,CAAEY,EAAI1C,EAAG0C,IAE5C,IAAK,IAAIuC,KADT0C,EAAIC,SAAS,CAAClF,EAAE,CACKlC,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAAC2G,EAAG1C,IACzDyC,CAAAA,CAAC,CAACzC,EAAE,CAAG0C,CAAC,CAAC1C,EAAE,AAAD,EAElB,OAAOyC,CACX,CAAA,EACwBG,KAAK,CAAC,IAAI,CAAED,UACxC,EAEIrE,EAAkBgE,EAAyBhE,eAAe,CAAEO,EAA8ByD,EAAyBzD,2BAA2B,CAAEyI,EAAqChF,EAAyB/B,wBAAwB,CAAEJ,EAA0BmC,EAAyBnC,uBAAuB,CAAEF,EAAsBqC,EAAyBrC,mBAAmB,CAAEY,EAA2ByB,EAAyBzB,wBAAwB,CAEpc0G,EAAqCpJ,EAA2BrB,wBAAwB,CAExF0K,EAAS,AAACrL,IAA+EqL,MAAM,CAAEC,EAAU,AAACtL,IAA+EsL,OAAO,CAAEC,EAAqB,AAACvL,IAA+EoJ,QAAQ,CAAEoC,EAAW,AAACxL,IAA+EwL,QAAQ,CAAEC,EAAW,AAACzL,IAA+EyL,QAAQ,CAmB/f,SAASC,EAAiBC,CAAS,EAE/B,IAAIC,EAAiB,CAAC,EA8BtB,OA7BAD,EAEKxH,MAAM,CAAC,SAAU0H,CAAQ,EAAI,OAAQA,AAAyB,IAAzBA,EAASC,IAAI,CAACpL,MAAM,AAAS,GAElEqL,OAAO,CAAC,SAAUF,CAAQ,EAC3BA,EAASC,IAAI,CAACC,OAAO,CAAC,SAAUC,CAAG,CAAE1K,CAAC,CAAEiC,CAAG,EACvC,IAAInC,EACCoK,EAASI,CAAc,CAACI,EAAI,GAC7BJ,CAAAA,CAAc,CAACI,EAAI,CAAG,CAClBC,aAAc,EACdC,YAAa,CAAC,CAClB,CAAA,EAEJN,CAAc,CAACI,EAAI,CAAG,CAClBC,aAAc,AAACL,CAAAA,CAAc,CAACI,EAAI,CAACC,YAAY,EAAI,CAAA,EAC/CJ,EAASrC,KAAK,CAClB0C,YAAahB,EAAiBA,EAAiB,CAAC,EAAIU,CAAc,CAACI,EAAI,CAACE,WAAW,EAAI,CAAC,GAAM9K,CAAAA,AAASA,CAATA,EAAK,CAAC,CAAA,CAAK,CAACmC,CAAG,CAAC,EAAIjC,EAAE,CAAC,CAAGuK,EAASrC,KAAK,CAAEpI,CAAC,EAC7I,CACJ,EACJ,GACAuK,EAEKxH,MAAM,CAACgI,GAEPJ,OAAO,CAAC,SAAUC,CAAG,EACtB,IAAII,EAAaR,CAAc,CAACI,EAAIF,IAAI,CAAC,EAAE,CAAC,CAC5CT,EAAOW,EAAKI,EAChB,GAEOT,CACX,CAsBA,SAASU,EAAOC,CAAC,CAAEtN,CAAC,CAAEkG,CAAC,CAAEqH,CAAS,CAAEC,CAAa,EAC7C,IAKIhM,EACAiM,EANAC,EAAKJ,EAAEtN,GACP2N,EAAKL,EAAEpH,GACP0H,EAAOJ,GAAiB,IACxBK,EAAMN,GAAa,MACnBO,EAAQ5H,EAAIlG,EAGZJ,EAAI,EACR,GAAII,GAAKkG,EACL,MAAM,AAAI7C,MAAM,6BAEf,GAAIqK,EAAKC,EAAK,EACf,MAAM,AAAItK,MAAM,2CAEpB,GAAIqK,AAAO,IAAPA,EACAlM,EAAIxB,OAEH,GAAI2N,AAAO,IAAPA,EACLnM,EAAI0E,OAGJ,KAAOtG,KAAOgO,GAAQH,AAAO,IAAPA,GAAYK,EAAQD,GACtCC,EAAQ,AAAC5H,CAAAA,EAAIlG,CAAAA,EAAK,EAId0N,EAFJD,CAAAA,EAAKH,EADL9L,EAAIxB,EAAI8N,EACA,EAEM,EACV9N,EAAIwB,EAGJ0E,EAAI1E,EAIhB,OAAOA,CACX,CAIA,SAASuM,EAAYC,CAAO,EASxB,IAAK,IARDzJ,EAAMyJ,EAAQvJ,KAAK,CAAC,EAAG,IACvB/C,EAAS6C,EAAI7C,MAAM,CACnBkE,EAAS,EAAE,CACXvE,EAAM,SAAU+E,CAAI,CACpB7E,CAAK,EAEL,OADI6E,EAAK/E,GAAG,EAAIE,CAAK,CAAC6E,EAAK9D,CAAC,CAAC,CACtB8D,CACX,EACS9D,EAAI,EAAGA,EAAIZ,EAAQY,IACxBsD,CAAM,CAACtD,EAAE,CAAGiC,EAAIjD,MAAM,CAACD,EAAK,CAAEA,IAAK,EAAGiB,EAAGA,CAAE,GAAGjB,GAAG,CAAGK,EAExD,OAAOkE,CACX,CAiBA,SAASqI,EAAmCpK,CAAE,CAAEC,CAAE,CAAEuB,CAAO,EACvD,IACI6I,EADAC,EAActK,EAAKC,EAoBvB,OAlBIuB,GAAW,EAEA8I,EAENhL,EAAgBU,EAAKC,EAAKD,EAAKC,IAAOuB,EAGhC,EAGAgI,EAAO,SAAU7L,CAAC,EAKzB,OAAO6D,EAJa8G,EAAmCtI,EACnDC,EACAtC,EAGR,EAAG,EAAG2M,EAGd,CAgGA,SAAShB,EAAM3L,CAAC,EACZ,OAAO8K,EAAQ9K,EAAEsL,IAAI,GAAKtL,AAAkB,IAAlBA,EAAEsL,IAAI,CAACpL,MAAM,AAC3C,CAKA,SAAS0M,EAAgB5M,CAAC,EACtB,IAAIoD,EAAM,CAAC,EACX,OAAQ4H,EAAShL,IACZ+K,EAAmB/K,EAAEgJ,KAAK,GAAKhJ,EAAEgJ,KAAK,CAAG,IACzC8B,EAAQ9K,EAAEsL,IAAI,GAAKtL,EAAEsL,IAAI,CAACpL,MAAM,CAAG,GACpC,CAACF,EAAEsL,IAAI,CAAC7H,IAAI,CAAC,SAAU+H,CAAG,EACtB,IAAIqB,EAAU,CAAA,EAOd,MANI,CAACzJ,CAAG,CAACoI,EAAI,EAAIP,EAASO,GACtBpI,CAAG,CAACoI,EAAI,CAAG,CAAA,EAGXqB,EAAU,CAAA,EAEPA,CACX,EACR,CA0JA,SAASC,EAAKC,CAAe,CAAE5B,CAAS,EAGpC,OAAOA,EAAUrL,MAAM,CAAC,SAAUkN,CAAS,CAAE3B,CAAQ,EACjD,IAAIyB,EAAO,EACX,GAAIzB,EAASC,IAAI,CAACpL,MAAM,CAAG,EAAG,CAQ1B,IAAI+M,EAAOC,AAPS7B,EAASrC,KAAK,CAEdpF,AAvMhC,SAAkCd,CAAO,EACrC,IAAIe,EAAU,EAGd,GAAIf,AAAmB,IAAnBA,EAAQ5C,MAAM,CAAQ,CACtB,IAAI8D,EAAUlB,CAAO,CAAC,EAAE,CACpBmB,EAAUnB,CAAO,CAAC,EAAE,CACxBe,EAAU8G,EAAmC3G,EAAQpC,CAAC,CAAEqC,EAAQrC,CAAC,CAAEgJ,EAAmC5G,EAASC,GACnH,CACA,OAAOJ,CACX,EA+LgBwH,EAASC,IAAI,CAAClI,GAAG,CAAC,SAAUoI,CAAG,EAC3B,OAAOuB,CAAe,CAACvB,EAAI,AACnC,IAEAsB,EAAOxM,KAAKmB,KAAK,CAAC,AAACwL,EAAOA,EAblB,MAAA,IAcZ,CAEA,OAAOD,EAAYF,CACvB,EAAG,EACP,CAkMA,SAASK,EAAmB3O,CAAC,CAAEkG,CAAC,SAC5B,AAAI,AAA0B,KAAA,IAAnBA,EAAE+G,YAAY,EACrB,AAA0B,KAAA,IAAnBjN,EAAEiN,YAAY,CACd/G,EAAE+G,YAAY,CAAGjN,EAAEiN,YAAY,CAEnC2B,GACX,CAqB6B,IAAIC,EAfjB,CACZC,SAAU9L,EACV+L,gBAAiB5H,EACjBuF,iBAAkBA,EAClBqB,YAAaA,EACbE,mCAAoCA,EACpCe,cAveJ,SAAuBC,CAAG,CAAEC,CAAQ,CAAEC,CAAQ,EAC1C,IAAIC,EAASF,EAAS5N,MAAM,CAAC,SAAU+N,CAAG,CACtCtK,CAAM,EAAI,OAAOjD,KAAKuN,GAAG,CAACtK,EAAO3B,CAAC,CAClCiM,EAAM,EACNC,KAEAC,EAAoBJ,EAAShK,MAAM,CAAC,SAAUJ,CAAM,EAAI,MAAO,CAACD,EAAoBmK,EACpFlK,EAAS,GACTyK,EAAe,SAAUrB,CAAW,CACpCsB,CAAS,EACL,OAAOpC,EAAO,SAAU7L,CAAC,EACrB,IAAIkO,EAAU,CACVlO,EAAGyN,EAAIzN,CAAC,CAAIiO,EAAYjO,EACxBC,EAAGwN,EAAIxN,CAAC,AACZ,EAOJ,MAAO,CAAE0M,CAAAA,EAAc3M,CAAAA,EAAM+I,CAAAA,AANtBvF,EAAwB0K,EACnCR,IACYxJ,EAAyBgK,EACrCH,GAG2C,EAAII,OAAOC,SAAS,AAAD,CAC9D,EAAG,EAAGzB,EACV,EAEA,OAAOrM,AAA8D,EAA9DA,KAAKuN,GAAG,CAACG,EAAaJ,EAAQ,IAAKI,EAAaJ,EAAQ,GACnE,EA8cIS,qBA9bJ,SAA8BtO,CAAK,CAAE2N,CAAQ,CAAEC,CAAQ,EACnD,IAAIW,EAASZ,EAAS5N,MAAM,CAAC,SAAUwO,CAAM,CACzC/K,CAAM,EACF,IAAIgL,EAAIhL,EAAO3B,CAAC,CAAGgJ,EAAmC7K,EAC1DwD,GACA,OAAO,AAACgL,GAAKD,EAAUC,EAAID,CAC/B,EAAGH,OAAOC,SAAS,EAMnB,OALST,EAAS7N,MAAM,CAAC,SAAUwO,CAAM,CAAE/K,CAAM,EAC7C,IAAIgL,EAAI3D,EAAmC7K,EACvCwD,GAAUA,EAAO3B,CAAC,CACtB,OAAO,AAAC2M,GAAKD,EAAUC,EAAID,CAC/B,EAAGA,EAEP,EAkbI3C,MAAOA,EACP6C,iBApXJ,SAA0BrD,CAAS,EAC/B,IAAIsD,EAAiB,EAAE,CACnBC,EAAmB,CAAC,EAExBvD,EACKxH,MAAM,CAAC,SAAU0H,CAAQ,EAAI,OAAQA,AAAyB,IAAzBA,EAASC,IAAI,CAACpL,MAAM,AAAS,GAClEqL,OAAO,CAAC,SAAUF,CAAQ,EAC3BqD,CAAgB,CAACrD,EAASC,IAAI,CAAC,EAAE,CAAC,CAAGD,EAAS9H,MAAM,CAAG,CACnDvD,EAAGmO,OAAOC,SAAS,CACnBnO,EAAGkO,OAAOC,SAAS,CACnBxM,EAAGtB,KAAKC,IAAI,CAAC8K,EAASrC,KAAK,CAAG1I,KAAKwB,EAAE,CACzC,CACJ,GAUA,IAAI6M,EAAc,SAAUnD,CAAG,CAC3BoD,CAAW,EACP,IAAIrL,EAASiI,EAAIjI,MAAM,CACvBA,IACAA,EAAOvD,CAAC,CAAG4O,EAAY5O,CAAC,CACxBuD,EAAOtD,CAAC,CAAG2O,EAAY3O,CAAC,EAE5BwO,EAAejJ,IAAI,CAACgG,EACxB,EAEAN,EAAiBC,GAEjB,IAAI0D,EAAkB1D,EACbxH,MAAM,CAACgI,GACPlH,IAAI,CAAC0I,GAEdwB,EAAYE,EAAgBC,KAAK,GAAI,CAAE9O,EAAG,EAAGC,EAAG,CAAE,GAiFlD,IAAK,IAhFD8O,EAAuB5D,EAAUxH,MAAM,CAAC,SAAU3D,CAAC,EAAI,OAAQA,AAAkB,IAAlBA,EAAEsL,IAAI,CAACpL,MAAM,AAAS,GAgFhF8O,EAAK,EAAwCA,EAAKC,AAAtBJ,EAAwC3O,MAAM,CAAE8O,KAEjFE,AAjFU,SAAU1D,CAAG,EACnB,IAAIjI,EAASiI,EAAIjI,MAAM,CAC3B,GAAKA,GAGL,IAAIqK,EAASrK,EAAO3B,CAAC,CACjB8J,EAAcF,EAAIE,WAAW,CAsEjCiD,EAAYnD,EAAK2D,AArEEV,EAAe3O,MAAM,CAAC,SAAUsP,CAAI,CACnDC,CAAa,CACbvO,CAAC,EACG,IAAIwO,EAAmBD,EAAc9L,MAAM,CAC/C,GAAI,CAAC+L,GAAoB,CAAC5D,EACtB,OAAO0D,EAsBX,IAAK,IApBDvL,EAAU6H,CAAW,CAAC2D,EAAc/D,IAAI,CAAC,EAAE,CAAC,CAG5CoB,EAAWD,EAAmCmB,EAC9C0B,EAAiB1N,CAAC,CAClBiC,GAGA0L,EAAsB,CAClB,CAAEvP,EAAGsP,EAAiBtP,CAAC,CAAG0M,EAC9BzM,EAAGqP,EAAiBrP,CAAC,AAAC,EAClB,CAAED,EAAGsP,EAAiBtP,CAAC,CAAG0M,EAC9BzM,EAAGqP,EAAiBrP,CAAC,AAAC,EAClB,CAAED,EAAGsP,EAAiBtP,CAAC,CAC3BC,EAAGqP,EAAiBrP,CAAC,CAAGyM,CAAS,EAC7B,CAAE1M,EAAGsP,EAAiBtP,CAAC,CAC3BC,EAAGqP,EAAiBrP,CAAC,CAAGyM,CAAS,EAChC,CAGIsC,EAAK,EAAGpO,EAAK6N,EAAexL,KAAK,CAACnC,EAAI,GAAIkO,EAAKpO,EAAGV,MAAM,CAAE8O,IAAM,CACrE,IAAIQ,EAAiB5O,CAAE,CAACoO,EAAG,CACvBS,EAAoBD,EAAejM,MAAM,CACzCmM,EAAWhE,CAAW,CAAC8D,EAAelE,IAAI,CAAC,EAAE,CAAC,CAClD,GAAKmE,GAGL,IAAIE,EAAYlD,EAAmCmB,EAC/C6B,EAAkB7N,CAAC,CACnB8N,GAEJH,EAAsBA,EAAoBpM,MAAM,CAACjB,EAA4B,CACzElC,EAAGsP,EAAiBtP,CAAC,CACrBC,EAAGqP,EAAiBrP,CAAC,CACrB2B,EAAG8K,CACP,EAAG,CACC1M,EAAGyP,EAAkBzP,CAAC,CACtBC,EAAGwP,EAAkBxP,CAAC,CACtB2B,EAAG+N,CACP,IACJ,CAEA,IAAK,IAAIzO,EAAK,EAAG0O,EAAwBL,EAAqBrO,EAAK0O,EAAsB1P,MAAM,CAAEgB,IAAM,CACnG,IAAI0N,EAAcgB,CAAqB,CAAC1O,EAAG,AAC3CqC,CAAAA,EAAOvD,CAAC,CAAG4O,EAAY5O,CAAC,CACxBuD,EAAOtD,CAAC,CAAG2O,EAAY3O,CAAC,CAExB,IAAI4P,EAAc/C,EAAK4B,EACnBK,GAEAc,EAAcT,EAAKtC,IAAI,GACvBsC,EAAKtC,IAAI,CAAG+C,EACZT,EAAKR,WAAW,CAAGA,EAE3B,CAEA,OAAOQ,CACX,EAAG,CACCtC,KAAMqB,OAAOC,SAAS,CACtBQ,YAAa,KAAK,CACtB,GAE8BA,WAAW,EAC7C,EAGcK,AADuBJ,CACN,CAACG,EAAG,EAInC,OAAON,CACX,EAwPI5B,KAAMA,EACNgD,WA5MJ,SAAoBC,CAAE,CAAEC,CAAO,EAmE3B,IAAK,IAjEDC,EAAW,SAAUzR,CAAC,CACtBkG,CAAC,EACG,OAAOlG,EAAE0R,EAAE,CAAGxL,EAAEwL,EAAE,AAC1B,EAQIC,EAAc,SAAUC,CAAO,CAC/BC,CAAE,CACFC,CAAO,CACPC,CAAE,EAAI,OAAOF,EAAGjN,GAAG,CAAC,SAAUpD,CAAC,CAC/Bc,CAAC,EAAI,OAAOsP,EAAUpQ,EAAIsQ,EAAUC,CAAE,CAACzP,EAAE,AAAE,EAAI,EAmB/C0P,EAAgB,SAAUhE,CAAO,CACjCzM,CAAK,EAGL,OAFIA,EAAMmQ,EAAE,CAAGH,EAAGhQ,GAClByM,CAAO,CAACA,EAAQtM,MAAM,CAAG,EAAE,CAAGH,EACvByM,CACX,EACIiE,EAAgB,SAAUjE,CAAO,EAC7B,IAAI4C,EAAO5C,CAAO,CAAC,EAAE,CACzB,OAAOA,EAAQpJ,GAAG,CAAC,SAAUrD,CAAK,EAC9B,IAAIsD,EAAI8M,EAAY,GAChBf,EArCF,GAuCErP,GAEJ,OADAsD,EAAE6M,EAAE,CAAGH,EAAG1M,GACHA,CACX,EACJ,EACIqN,EAAW,SAAUC,CAAQ,CAC7BC,CAAK,CACLpS,CAAC,CACDkG,CAAC,EACG,IAAI3E,EAAQoQ,EAAY3R,EAC5BmS,EACAjM,EACAkM,GAEA,OADA7Q,EAAMmQ,EAAE,CAAGH,EAAGhQ,GACPA,CACX,EAEIyM,EAAUqE,AA5CG,SAAUb,CAAO,EAC1B,IAAI5R,EAAI4R,EAAQ9P,MAAM,CAC1BsM,EAAU,AAAIpE,MAAMhK,EAAI,EAExBoO,CAAAA,CAAO,CAAC,EAAE,CAAGwD,EACbxD,CAAO,CAAC,EAAE,CAAC0D,EAAE,CAAGH,EAAGC,GAEnB,IAAK,IAAIlP,EAAI,EAAGA,EAAI1C,EAAG,EAAE0C,EAAG,CACxB,IAAIf,EAAQiQ,EAAQ/M,KAAK,EACzBlD,CAAAA,CAAK,CAACe,EAAE,CAAGf,CAAK,CAACe,EAAE,CAAGf,AAAW,KAAXA,CAAK,CAACe,EAAE,CAAU,KACxCf,EAAMmQ,EAAE,CAAGH,EAAGhQ,GACdyM,CAAO,CAAC1L,EAAI,EAAE,CAAGf,CACrB,CACA,OAAOyM,CACX,EA8ByBwD,GAEhBlP,EAAI,EAAGA,EAlEI,IAkEeA,IAAK,CAEpC0L,EAAQ/H,IAAI,CAACwL,GAEb,IAAIW,EAAQpE,CAAO,CAACA,EAAQtM,MAAM,CAAG,EAAE,CACnCyQ,EAAWpE,EAAYC,GAEvBsE,EAAYJ,EAASC,EACrBC,EAAO,EAAU,IACrB,GAAIE,EAAUZ,EAAE,CAAG1D,CAAO,CAAC,EAAE,CAAC0D,EAAE,CAAE,CAE9B,IAAIa,EAAWL,EAASC,EACpBC,EAAO,EAAU,IACrBpE,EAAUgE,EAAchE,EAAS,AAACuE,EAASb,EAAE,CAAGY,EAAUZ,EAAE,CAAIa,EAAWD,EAC/E,MACK,GAAIA,EAAUZ,EAAE,EAAI1D,CAAO,CAACA,EAAQtM,MAAM,CAAG,EAAE,CAACgQ,EAAE,CAAE,CAGrD,IAAIc,EAAa,KAAK,EAMdxE,EALJsE,EAAUZ,EAAE,CAAGU,EAAMV,EAAE,CAInBc,AADJA,CAAAA,EAAaN,EAASC,EAAUC,EAAO,GAAU,GAAK,EACvCV,EAAE,CAAGU,EAAMV,EAAE,CACdM,EAAchE,EAASwE,GAGvBP,EAAcjE,GAMxBwE,AADJA,CAAAA,EAAaN,EAASC,EAAUC,EAAO,IA3F3CK,IA2F2D,EACxCf,EAAE,CAAGY,EAAUZ,EAAE,CAClBM,EAAchE,EAASwE,GAGvBP,EAAcjE,EAGpC,MAEIA,EAAUgE,EAAchE,EAASsE,EAEzC,CACA,OAAOtE,CAAO,CAAC,EAAE,AACrB,EA4FI0E,gBAlFJ,SAAyBtM,CAAI,CAAEuM,CAAQ,EACnC,IAAI5S,EAAIuM,EAAQlG,GAAQA,EAAO,EAAE,CAC7BwM,EAAY7S,EACPuB,MAAM,CAAC,SAAUiD,CAAG,CACzB/C,CAAC,MApTWA,EAyTZ,OAHQA,EAAEsL,IAAI,EArTVsB,EADQ5M,EAsTiBA,IArTH2L,EAAM3L,IAAMA,EAAEgJ,KAAK,CAAG,GAqTbjG,AAA2B,KAA3BA,EAAIiC,OAAO,CAAChF,EAAEsL,IAAI,CAAC,EAAE,GAChDvI,EAAIyC,IAAI,CAACxF,EAAEsL,IAAI,CAAC,EAAE,EAEnBvI,CACX,EAAG,EAAE,EACA0B,IAAI,GACL4M,EAAoB9S,EAAEuB,MAAM,CAAC,SAAUuR,CAAiB,CACxDhG,CAAQ,EAWR,OAVQA,EAASC,IAAI,EACbsB,EAAgBvB,IAChB,CAACA,EAASC,IAAI,CAAC7H,IAAI,CAAC,SAAU+H,CAAG,EAC7B,OAAO4F,AAA2B,KAA3BA,EAAUpM,OAAO,CAACwG,EACjC,IACA6F,CAAAA,CAAiB,CAAChG,EAASC,IAAI,CAAC7G,IAAI,GAAG6M,IAAI,CAACH,GAAU,CAAG,CACrD7F,KAAMD,EAASC,IAAI,CACnBtC,MAAOqC,EAASrC,KAAK,EAAI,CAC7B,CAAA,EAEGqI,CACX,EAAG,CAAC,GAiBJ,OAhBAD,EAAUtR,MAAM,CAAC,SAAUyR,CAAY,CAAE/F,CAAG,CAAE1K,CAAC,CAAEiC,CAAG,EAKhD,OAHAyO,AADgBzO,EAAIE,KAAK,CAACnC,EAAI,GACpByK,OAAO,CAAC,SAAUkG,CAAI,EAC5BF,EAAa/L,IAAI,CAACgG,EAAM2F,EAAWM,EACvC,GACOF,CACX,EAAG,EAAE,EAAEhG,OAAO,CAAC,SAAUmG,CAAW,EAChC,GAAI,CAACL,CAAiB,CAACK,EAAY,CAAE,CACjC,IAAI1S,EAAM,CACFsM,KAAMoG,EAAYC,KAAK,CAACR,GACxBnI,MAAO,CACX,CACJqI,CAAAA,CAAiB,CAACK,EAAY,CAAG1S,CACrC,CACJ,GAEOJ,OACFkJ,IAAI,CAACuJ,GACLjO,GAAG,CAAC,SAAUwO,CAAE,EACjB,OAAOP,CAAiB,CAACO,EAAG,AAChC,EACJ,EAoCIzE,mBAAoBA,CACxB,EAqBI0E,GACIjU,EAAgB,SAAUW,CAAC,CAC3BmG,CAAC,EAOD,MAAO9G,AANHA,CAAAA,EAAgBgB,OAAOsJ,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAU7J,CAAC,CAC1DmG,CAAC,EAAInG,EAAE4J,SAAS,CAAGzD,CAAG,GACd,SAAUnG,CAAC,CACnBmG,CAAC,EAAI,IAAK,IAAIrB,KAAKqB,EAAO9F,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACsF,EAC/DrB,IAAI9E,CAAAA,CAAC,CAAC8E,EAAE,CAAGqB,CAAC,CAACrB,EAAE,AAAD,CAAG,CAAA,EACI9E,EAAGmG,EAC5B,EACO,SAAUnG,CAAC,CAAEmG,CAAC,EACjB,GAAI,AAAa,YAAb,OAAOA,GAAoBA,AAAM,OAANA,EAC3B,MAAM,AAAIoN,UAAU,uBAAyBC,OAAOrN,GAAK,iCAE7D,SAAS2D,IAAO,IAAI,CAACC,WAAW,CAAG/J,CAAG,CADtCX,EAAcW,EAAGmG,GAEjBnG,EAAEW,SAAS,CAAGwF,AAAM,OAANA,EAAa9F,OAAO2J,MAAM,CAAC7D,GAAM2D,CAAAA,EAAGnJ,SAAS,CAAGwF,EAAExF,SAAS,CAAE,IAAImJ,CAAG,CACtF,GAGA2J,EAAa,AAACxS,IAA+EwS,UAAU,CAEvG3H,GAAQ,AAAC3K,IAAuGuS,KAAK,CAErH9N,GAAsCwB,EAAyBxB,mCAAmC,CAAET,GAAgCiC,EAAyBjC,6BAA6B,CAAEK,GAAwC4B,EAAyB5B,qCAAqC,CAAEmO,GAAqCvM,EAAyBnC,uBAAuB,CAAE2O,GAAsCxM,EAAyBzB,wBAAwB,CAGldkO,GAA+B5Q,EAA2B7B,iBAAiB,CAE3E0S,GAAgB,AAACrK,IAA2IS,WAAW,CAACC,OAAO,CAK/K4J,GAAW,AAAC9S,IAA+E8S,QAAQ,CAAEC,GAAoB,AAAC/S,IAA+EqL,MAAM,CAAE2H,GAAqB,AAAChT,IAA+EsL,OAAO,CAAE2H,GAAsB,AAACjT,IAA+EoJ,QAAQ,CAAE8J,GAAsB,AAAClT,IAA+EwL,QAAQ,CAAE2H,GAAQ,AAACnT,IAA+EmT,KAAK,CAa5nBC,GAA4B,SAAU9J,CAAM,EAE5C,SAAS8J,IACL,OAAO9J,AAAW,OAAXA,GAAmBA,EAAO7C,KAAK,CAAC,IAAI,CAAED,YAAc,IAAI,AACnE,CAkbA,OArbA6L,EAAmBe,EAAY9J,GAuB/B8J,EAAWC,gBAAgB,CAAG,SAAUnF,CAAQ,CAAEC,CAAQ,EAEtD,IAAIyB,EAAO1B,EAAS5N,MAAM,CAAC,SAAUsP,CAAI,CACrC7L,CAAM,EACF,IAAIhF,EAAIgF,EAAO3B,CAAC,CAAG,EAGvB,MAAO,CACH,CAAE5B,EAAGuD,EAAOvD,CAAC,CAAEC,EAAGsD,EAAOtD,CAAC,AAAC,EAC3B,CAAED,EAAGuD,EAAOvD,CAAC,CAAGzB,EAAG0B,EAAGsD,EAAOtD,CAAC,AAAC,EAC/B,CAAED,EAAGuD,EAAOvD,CAAC,CAAGzB,EAAG0B,EAAGsD,EAAOtD,CAAC,AAAC,EAC/B,CAAED,EAAGuD,EAAOvD,CAAC,CAAEC,EAAGsD,EAAOtD,CAAC,CAAG1B,CAAE,EAC/B,CAAEyB,EAAGuD,EAAOvD,CAAC,CAAEC,EAAGsD,EAAOtD,CAAC,CAAG1B,CAAE,EAClC,CAGIuB,MAAM,CAAC,SAAUsP,CAAI,CAAErP,CAAK,EAC7B,IAAIuO,EAASjB,EAAegB,oBAAoB,CAACtO,EAC7C2N,EACAC,GAOJ,OAJIyB,EAAKd,MAAM,CAAGA,IACdc,EAAKrP,KAAK,CAAGA,EACbqP,EAAKd,MAAM,CAAGA,GAEXc,CACX,EAAGA,EACP,EAAG,CACCrP,MAAO,KAAK,EACZuO,OAAQ,CAACH,OAAOC,SAAS,AAC7B,GAAGrO,KAAK,CAEJ+S,EAAUzF,EAAeyC,UAAU,CAAC,SAAUzM,CAAC,EAAI,MAAO,CAAEgK,EAAegB,oBAAoB,CAAC,CAAErO,EAAGqD,CAAC,CAAC,EAAE,CACzGpD,EAAGoD,CAAC,CAAC,EAAE,AAAC,EACRqK,EACAC,EAAY,EACZ,CACIyB,EAAKpP,CAAC,CACNoP,EAAKnP,CAAC,CACT,EAqBL,OAfMiS,GAJN9C,EAAO,CACHpP,EAAG8S,CAAO,CAAC,EAAE,CACb7S,EAAG6S,CAAO,CAAC,EAAE,AACjB,EAC+CpF,IAC3CyE,GAAoC/C,EAAMzB,KAItCyB,EADA1B,EAASxN,MAAM,CAAG,EACXkS,GAA6B1O,GAA8BgK,IAG3D,CACH1N,EAAG0N,CAAQ,CAAC,EAAE,CAAC1N,CAAC,CAChBC,EAAGyN,CAAQ,CAAC,EAAE,CAACzN,CAAC,AACpB,GAIDmP,CACX,EAYAwD,EAAWG,cAAc,CAAG,SAAU1H,CAAQ,CAAE2H,CAAY,EACxD,IAAI1H,EAAOD,EAASC,IAAI,CAEpB1G,EAAOoO,EAAalT,MAAM,CAAC,SAAU8E,CAAI,CACzC4G,CAAG,EAGC,IAAIyH,EAAa3H,EAAKtG,OAAO,CAACwG,EAAIF,IAAI,CAAC,EAAE,EAAI,GAMjD,OAHIE,EAAIjI,MAAM,EACVqB,CAAI,CAHOqO,EAAa,WAAa,WAGvB,CAACzN,IAAI,CAACgG,EAAIjI,MAAM,EAE3BqB,CACX,EAAG,CACC8I,SAAU,EAAE,CACZC,SAAU,EAAE,AAChB,EAGA/I,CAAAA,EAAK+I,QAAQ,CAAG/I,EAAK+I,QAAQ,CAAChK,MAAM,CAAC,SAAUuP,CAAc,EACzD,OAAOtO,EAAK8I,QAAQ,CAACjK,IAAI,CAAC,SAAU0P,CAAc,EAC9C,MAAO,CAACpP,GAAsCmP,EAAgBC,EAClE,EACJ,GAEA,IAAIC,EAAWR,EAAWC,gBAAgB,CAACjO,EAAK8I,QAAQ,CACpD9I,EAAK+I,QAAQ,EAEbvI,EAAQiI,EAAeG,aAAa,CAAC4F,EACrCxO,EAAK8I,QAAQ,CACb9I,EAAK+I,QAAQ,EACjB,MAAO,CACHyF,SAAUA,EACVhO,MAAOA,CACX,CACJ,EAaAwN,EAAWS,MAAM,CAAG,SAAUlI,CAAS,EACnC,IAAImI,EAAiB,CAAC,EAClBC,EAAuB,CAAC,EAE5B,GAAIpI,EAAUjL,MAAM,CAAG,EAGnB,IAAK,IAFDsT,EAAqBnG,EAAemB,gBAAgB,CAACrD,GACrD6H,EAAe7H,EAAUxH,MAAM,CAAC0J,EAAe1B,KAAK,EAC/CqD,EAAK,EAA4BA,EAAKyE,AAAhBtI,EAA4BjL,MAAM,CAAE8O,IAAM,CACrE,IAAI3D,EAAWoI,AADYtI,CACD,CAAC6D,EAAG,CAC1B1D,EAAOD,EAASC,IAAI,CACpBsG,EAAKtG,EAAKgG,IAAI,GAEdoC,EAAQrG,EAAe1B,KAAK,CAACN,GACzBmI,CAAkB,CAAC5B,EAAG,CACtBzN,GAAoCmH,EAAKlI,GAAG,CAAC,SAAUoI,CAAG,EAAI,OAAOgI,CAAkB,CAAChI,EAAI,AAAE,IAElGkI,IACAJ,CAAc,CAAC1B,EAAG,CAAG8B,EACrBH,CAAoB,CAAC3B,EAAG,CAAGgB,EAAWG,cAAc,CAAC1H,EAAU2H,GAEvE,CAEJ,MAAO,CAAEM,eAAgBA,EAAgBC,qBAAsBA,CAAqB,CACxF,EAeAX,EAAWe,QAAQ,CAAG,SAAUC,CAAW,CAAEC,CAAY,CAAEC,CAAK,EAC5D,IAAIC,EAASD,EAAME,MAAM,CAAGF,EAAMG,GAAG,CACjC7O,EAAQ0O,EAAMI,KAAK,CAAGJ,EAAMK,IAAI,CAAyGC,EAAU,AAACN,CAAAA,EAAMI,KAAK,CAAGJ,EAAMK,IAAI,AAAD,EAAK,EAAGE,EAAU,AAACP,CAAAA,EAAMG,GAAG,CAAGH,EAAME,MAAM,AAAD,EAAK,EAAGM,EAAQhU,KAAKuN,GAAG,CAAlMzI,EAAQ,EAAI,EAAIA,EAAQwO,EAAc,EAAYG,EAAS,EAAI,EAAIA,EAASF,EAAe,GAC1I,MAAO,CACHS,MAAOA,EACPC,QAASX,EAAc,EAAIQ,EAAUE,EACrCE,QAASX,EAAe,EAAIQ,EAAUC,CAC1C,CACJ,EAcA1B,EAAW6B,qBAAqB,CAAG,SAAUX,CAAK,CAAEvQ,CAAM,EACtD,IAAI4Q,EAAO5Q,EAAOvD,CAAC,CAAGuD,EAAO3B,CAAC,CAC1BsS,EAAQ3Q,EAAOvD,CAAC,CAAGuD,EAAO3B,CAAC,CAC3BoS,EAASzQ,EAAOtD,CAAC,CAAGsD,EAAO3B,CAAC,CAC5BqS,EAAM1Q,EAAOtD,CAAC,CAAGsD,EAAO3B,CAAC,CAc7B,MAZI,CAAA,CAAC6Q,GAAoBqB,EAAMK,IAAI,GAAKL,EAAMK,IAAI,CAAGA,CAAG,GACpDL,CAAAA,EAAMK,IAAI,CAAGA,CAAG,EAEhB,CAAA,CAAC1B,GAAoBqB,EAAMI,KAAK,GAAKJ,EAAMI,KAAK,CAAGA,CAAI,GACvDJ,CAAAA,EAAMI,KAAK,CAAGA,CAAI,EAElB,CAAA,CAACzB,GAAoBqB,EAAMG,GAAG,GAAKH,EAAMG,GAAG,CAAGA,CAAE,GACjDH,CAAAA,EAAMG,GAAG,CAAGA,CAAE,EAEd,CAAA,CAACxB,GAAoBqB,EAAME,MAAM,GAAKF,EAAME,MAAM,CAAGA,CAAK,GAC1DF,CAAAA,EAAME,MAAM,CAAGA,CAAK,EAEjBF,CACX,EAOAlB,EAAW1T,SAAS,CAACwI,OAAO,CAAG,SAAUgN,CAAI,EACzC,GAAI,CAACA,EAgCD,IAAK,IA9BDC,EAAc3C,EAAWtL,AADhB,IAAI,CACmBG,OAAO,CAACJ,SAAS,EACjDyI,EAAU,SAAUnP,CAAK,EACrB,IAAI6U,EAAO7U,EAAMwH,SAAS,CAC9B,GAAIxH,EAAM+G,OAAO,EAAI8N,EAAM,CACvB,IAAItN,EAAO,CAAC,EACRI,EAAU,CAAC,CACXkN,CAAAA,EAAKrW,CAAC,CAEN+I,EAAKyC,OAAO,CAAG,MAIfzC,EAAK1F,CAAC,CAAG,EACT8F,EAAQ9F,CAAC,CAAGgT,EAAKhT,CAAC,EAEtB7B,EAAM+G,OAAO,CACRQ,IAAI,CAACA,GACLI,OAAO,CAACA,EAASiN,GAGlBC,EAAKrW,CAAC,EACNsW,WAAW,WACP,IAAIjU,CACJ,AAAuE,QAAtEA,CAAAA,EAAKb,MAAAA,EAAqC,KAAK,EAAIA,EAAM+G,OAAO,AAAD,GAAelG,AAAO,KAAK,IAAZA,GAAyBA,EAAG8G,OAAO,CAAC,CAC/GqC,QAAS,CACb,EACJ,EAAG4K,EAAYG,QAAQ,CAE/B,CACJ,EACS9F,EAAK,EAAGpO,EAAK8F,AA/BT,IAAI,CA+BY9G,MAAM,CAAEoP,EAAKpO,EAAGV,MAAM,CAAE8O,IAEjDE,EADYtO,CAAE,CAACoO,EAAG,CAI9B,EAKA4D,EAAW1T,SAAS,CAAC6V,UAAU,CAAG,WAS9B,IAAK,IANDpO,EAAQD,AAFC,IAAI,CAEEC,KAAK,CACpBc,EAAQf,AAHC,IAAI,CAGEe,KAAK,CACpB7H,EAAS8G,AAJA,IAAI,CAIG9G,MAAM,EAAI,EAAE,CAE5B4G,EAAWG,EAAMH,QAAQ,CAEpBwI,EAAK,EAAsBA,EAAKgG,AAAbpV,EAAsBM,MAAM,CAAE8O,IAAM,CAC5D,IAAIjP,EAAQiV,AADYpV,CACJ,CAACoP,EAAG,CACpBjI,EAAU,CACNkO,OAAQzC,GAAmBzS,EAAMuL,IAAI,EAAIvL,EAAMuL,IAAI,CAACpL,MAAM,CAAG,CACjE,EACAqH,EAAYxH,EAAMwH,SAAS,AAE1BZ,CAAAA,EAAMuO,UAAU,EACjB3C,GAAkBxL,EAASL,AAhBtB,IAAI,CAgByByO,YAAY,CAACpV,EAAOA,EAAMqV,KAAK,GAGrElP,EAA0BC,IAAI,CAACpG,EAAO,CAClC4H,MAAO,CAAC5H,EAAM+G,OAAO,CACrBT,kBAAmBkB,EACnBR,QAASA,EACTU,MAAOA,EACPjB,SAAUA,EACVU,UAAW,AAACK,CAAAA,MAAAA,EAA6C,KAAK,EAAIA,EAAUhJ,CAAC,AAADA,EAAK,OAAS,QAC9F,EACJ,CACJ,EACAqU,EAAW1T,SAAS,CAACwV,IAAI,CAAG,WACxBrC,GAAcnT,SAAS,CAACwV,IAAI,CAACzO,KAAK,CAAC,IAAI,CAAED,WAEzC,OAAO,IAAI,CAAC+D,OAAO,AACvB,EAYA6I,EAAW1T,SAAS,CAACiW,YAAY,CAAG,SAAUpV,CAAK,CAAEqV,CAAK,EACtD,IACIC,EAAgB3O,AADP,IAAI,CACUG,OAAO,EAAI,CAAC,EACnCyO,EAAe,AAACvV,CAAAA,MAAAA,EAAqC,KAAK,EAAIA,EAAM8G,OAAO,AAAD,GAAM,CAAC,EACjF0O,EAAe,AAACH,GAASC,EAAcnL,MAAM,CAACkL,EAAM,EAAK,CAAC,EAC1DvO,EAAU8L,GAAM0C,EAAe,CAAEhL,MAAOtK,MAAAA,EAAqC,KAAK,EAAIA,EAAMsK,KAAK,AAAC,EAClGiL,EACAC,GAEJ,MAAO,CACH,KAAQlL,GAAMxD,EAAQwD,KAAK,EACtBhB,QAAQ,CAACxC,EAAQ2O,UAAU,EAC3BzW,GAAG,GAERgL,QAASlD,EAAQkD,OAAO,CACxB,OAAUlD,EAAQqC,WAAW,CAC7B,eAAgBrC,EAAQuC,WAAW,CACnC,UAAavC,EAAQsC,eAAe,AACxC,CACJ,EACAyJ,EAAW1T,SAAS,CAACuW,SAAS,CAAG,WAE7B,IADI7U,EACA+F,EAAQ,IAAI,CAACA,KAAK,AACtB,CAAA,IAAI,CAAC+O,SAAS,CAACC,QAAQ,CAAG,IAAI,CAACD,SAAS,CACxC,IAAI,CAACE,cAAc,GAqBnB,IAAK,IAnBDzK,EAAYkC,EAAe6D,eAAe,CAAC,IAAI,CAACrK,OAAO,CAACjC,IAAI,CAC5DgO,EAAWzB,QAAQ,EAEnBjQ,EAAK0R,EAAWS,MAAM,CAAClI,GACvBmI,EAAiBpS,EAAGoS,cAAc,CAClCC,EAAuBrS,EAAGqS,oBAAoB,CAE9CO,EAAQlV,OAAOkJ,IAAI,CAACwL,GACf3P,MAAM,CAAC,SAAUjF,CAAG,EACrB,IAAIgV,EAAQJ,CAAc,CAAC5U,EAAI,CACnC,OAAOgV,GAASjB,GAAoBiB,EAAM9R,CAAC,CAC/C,GACK9B,MAAM,CAAC,SAAUgU,CAAK,CAAEpV,CAAG,EAAI,OAAOkU,EAAW6B,qBAAqB,CAACX,EAAOR,CAAc,CAAC5U,EAAI,CAAG,EAAG,CACxGuV,IAAK,EACLD,OAAQ,EACRG,KAAM,EACND,MAAO,CACX,GAAI2B,EAAUjD,EAAWe,QAAQ,CAAChN,EAAMmP,SAAS,CAAEnP,EAAMoP,UAAU,CAAEjC,GAAQQ,EAAQuB,EAAQvB,KAAK,CAAEC,EAAUsB,EAAQtB,OAAO,CAAEC,EAAUqB,EAAQrB,OAAO,CAE/IxF,EAAK,EAAG3N,EAAK,IAAI,CAACzB,MAAM,CAAEoP,EAAK3N,EAAGnB,MAAM,CAAE8O,IAAM,CACrD,IAAIjP,EAAQsB,CAAE,CAAC2N,EAAG,CACd1D,EAAOkH,GAAmBzS,EAAMuL,IAAI,EAAIvL,EAAMuL,IAAI,CAAG,EAAE,CACvDsG,EAAKtG,EAAKgG,IAAI,GACdoC,EAAQJ,CAAc,CAAC1B,EAAG,CAC1BoE,EAAkBzC,CAAoB,CAAC3B,EAAG,EAAI,CAAC,EAC/CqE,EAAY,AAAyB,OAAxBrV,CAAAA,EAAKb,EAAM8G,OAAO,AAAD,GAAejG,AAAO,KAAK,IAAZA,EAAgB,KAAK,EAAIA,EAAG4I,UAAU,CACnFjC,EAAY,KAAK,EACjB2O,EAAiBF,EAAgB5Q,KAAK,CACtC+Q,EAAoBH,EAAgB5C,QAAQ,CAChD,GAAIM,EAAO,CACP,GAAIA,EAAM9R,CAAC,CACP2F,EAAY,CACRvH,EAAGuU,EAAUb,EAAM1T,CAAC,CAAGsU,EACvBrU,EAAGuU,EAAUd,EAAMzT,CAAC,CAAGqU,EACvB1S,EAAG8R,EAAM9R,CAAC,CAAG0S,CACjB,OAEC,GAAIZ,EAAMnV,CAAC,CAAE,CACd,IAAIA,EAAImV,EAAMnV,CAAC,CACfA,EAAEgN,OAAO,CAAC,SAAU6K,CAAG,EACfA,AAAW,MAAXA,CAAG,CAAC,EAAE,EACNA,CAAG,CAAC,EAAE,CAAG7B,EAAU6B,CAAG,CAAC,EAAE,CAAG9B,EAC5B8B,CAAG,CAAC,EAAE,CAAG5B,EAAU4B,CAAG,CAAC,EAAE,CAAG9B,GAEZ,MAAX8B,CAAG,CAAC,EAAE,GACXA,CAAG,CAAC,EAAE,CAAGA,CAAG,CAAC,EAAE,CAAG9B,EAClB8B,CAAG,CAAC,EAAE,CAAGA,CAAG,CAAC,EAAE,CAAG9B,EAClB8B,CAAG,CAAC,EAAE,CAAG7B,EAAU6B,CAAG,CAAC,EAAE,CAAG9B,EAC5B8B,CAAG,CAAC,EAAE,CAAG5B,EAAU4B,CAAG,CAAC,EAAE,CAAG9B,EAEpC,GACA/M,EAAY,CAAEhJ,EAAGA,CAAE,CACvB,CAEI4X,GACAA,EAAkBnW,CAAC,CAAGuU,EAAU4B,EAAkBnW,CAAC,CAAGsU,EACtD6B,EAAkBlW,CAAC,CAAGuU,EAAU2B,EAAkBlW,CAAC,CAAGqU,GAGtD6B,EAAoB,CAAC,EAErB1D,GAAoByD,IACpBA,CAAAA,EAAiB5V,KAAKmB,KAAK,CAACyU,EAAiB5B,EAAK,CAE1D,CACAvU,EAAMwH,SAAS,CAAGA,EAEd4O,GAAqB5O,IACrBxH,EAAMsW,KAAK,CAAGF,EAAkBnW,CAAC,CACjCD,EAAMuW,KAAK,CAAGH,EAAkBlW,CAAC,EAGjCiW,GAAkB3O,GAClBxH,CAAAA,EAAMkW,SAAS,CAAGtD,GAAM,CAAA,EAAM,CAC1B4D,MAAO,CACHnR,MAAO8Q,CACX,CACJ,EAAGxD,GAAoBuD,EAAW,CAAA,GAAQA,EAAY,KAAK,EAAC,EAGhElW,EAAM6J,IAAI,CAAG7J,EAAM8G,OAAO,CAAC+C,IAAI,EAAI0B,EAAKgG,IAAI,CAAC,IACjD,CACJ,EAMAsB,EAAWzB,QAAQ,CAAG,mBACtByB,EAAW4D,cAAc,CAAG7D,GAAMN,GAAcmE,cAAc,CAAEvN,GACzD2J,CACX,EAAEP,IACFE,GAAkBK,GAAW1T,SAAS,CAAE,CACpCuX,UAAW,EAAE,CACbC,YAAa,CAAA,EACbC,YAAa,CAAA,EACbC,cAAe,CAAC,QAAQ,CACxBjO,WAv4C+CE,EAw4C/CgO,MAAOxJ,CACX,GAEAiF,GAASM,GAAY,kBAAmB,SAAUkE,CAAC,EAC/C,IACI5M,EAASrD,AADCiQ,EAAEjQ,OAAO,CACFqD,MAAM,EAAI,CAAC,EAChC,GAAI,IAAI,CAAC6M,EAAE,CAAC,QAER,IAAK,IAAI/H,EAAK,EAAGpO,EAAKhC,OAAOkJ,IAAI,CAACoC,GAAS8E,EAAKpO,EAAGV,MAAM,CAAE8O,IAEvD9E,CAAM,CADMtJ,CAAE,CAACoO,EAAG,CACL,CAACgI,IAAI,CAAG,CAAA,CAGjC,GACAhP,IAA0IiP,kBAAkB,CAAC,OAAQrE,IAaxI,IAAItT,GAAaE,IAGpC,OADYH,EAAoB,OAAU,AAE3C,GAET"}