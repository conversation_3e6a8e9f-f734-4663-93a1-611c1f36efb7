{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.2.0 (2025-04-07)\n * @module highcharts/indicators/cmo\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Pawel Lysy\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/cmo\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"SeriesRegistry\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/cmo\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ cmo_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es5/es-modules/Stock/Indicators/CMO/CMOIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar SMAIndicator = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.sma;\n\nvar isNumber = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).isNumber, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n *  Class\n *\n * */\n/**\n * The CMO series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.cmo\n *\n * @augments Highcharts.Series\n */\nvar CMOIndicator = /** @class */ (function (_super) {\n    __extends(CMOIndicator, _super);\n    function CMOIndicator() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    CMOIndicator.prototype.getValues = function (series, params) {\n        var period = params.period,\n            xVal = series.xData,\n            yVal = series.yData,\n            yValLen = yVal ? yVal.length : 0,\n            CMO = [],\n            xData = [],\n            yData = [];\n        var i,\n            index = params.index,\n            values;\n        if (xVal.length < period) {\n            return;\n        }\n        if (isNumber(yVal[0])) {\n            values = yVal;\n        }\n        else {\n            // In case of the situation, where the series type has data length\n            // shorter then 4 (HLC, range), this ensures that we are not trying\n            // to reach the index out of bounds\n            index = Math.min(index, yVal[0].length - 1);\n            values = yVal.map(function (value) { return value[index]; });\n        }\n        var firstAddedSum = 0,\n            sumOfHigherValues = 0,\n            sumOfLowerValues = 0,\n            y;\n        // Calculate first point, check if the first value\n        // was added to sum of higher/lower values, and what was the value.\n        for (var j = period; j > 0; j--) {\n            if (values[j] > values[j - 1]) {\n                sumOfHigherValues += values[j] - values[j - 1];\n            }\n            else if (values[j] < values[j - 1]) {\n                sumOfLowerValues += values[j - 1] - values[j];\n            }\n        }\n        // You might divide by 0 if all values are equal,\n        // so return 0 in this case.\n        y =\n            sumOfHigherValues + sumOfLowerValues > 0 ?\n                (100 * (sumOfHigherValues - sumOfLowerValues)) /\n                    (sumOfHigherValues + sumOfLowerValues) :\n                0;\n        xData.push(xVal[period]);\n        yData.push(y);\n        CMO.push([xVal[period], y]);\n        for (i = period + 1; i < yValLen; i++) {\n            firstAddedSum = Math.abs(values[i - period - 1] - values[i - period]);\n            if (values[i] > values[i - 1]) {\n                sumOfHigherValues += values[i] - values[i - 1];\n            }\n            else if (values[i] < values[i - 1]) {\n                sumOfLowerValues += values[i - 1] - values[i];\n            }\n            // Check, to which sum was the first value added to,\n            // and subtract this value from given sum.\n            if (values[i - period] > values[i - period - 1]) {\n                sumOfHigherValues -= firstAddedSum;\n            }\n            else {\n                sumOfLowerValues -= firstAddedSum;\n            }\n            // Same as above.\n            y =\n                sumOfHigherValues + sumOfLowerValues > 0 ?\n                    (100 * (sumOfHigherValues - sumOfLowerValues)) /\n                        (sumOfHigherValues + sumOfLowerValues) :\n                    0;\n            xData.push(xVal[i]);\n            yData.push(y);\n            CMO.push([xVal[i], y]);\n        }\n        return {\n            values: CMO,\n            xData: xData,\n            yData: yData\n        };\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    /**\n     * Chande Momentum Oscilator (CMO) technical indicator. This series\n     * requires the `linkedTo` option to be set and should be loaded after\n     * the `stock/indicators/indicators.js` file.\n     *\n     * @sample stock/indicators/cmo\n     *         CMO indicator\n     *\n     * @extends      plotOptions.sma\n     * @since 9.1.0\n     * @product      highstock\n     * @requires     stock/indicators/indicators\n     * @requires     stock/indicators/cmo\n     * @optionparent plotOptions.cmo\n     */\n    CMOIndicator.defaultOptions = merge(SMAIndicator.defaultOptions, {\n        params: {\n            period: 20,\n            index: 3\n        }\n    });\n    return CMOIndicator;\n}(SMAIndicator));\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('cmo', CMOIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var CMO_CMOIndicator = ((/* unused pure expression or super */ null && (CMOIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `CMO` series. If the [type](#series.cmo.type) option is not\n * specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.cmo\n * @since 9.1.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/cmo\n * @apioption series.cmo\n */\n(''); // To include the above in the js output\n\n;// ./code/es5/es-modules/masters/indicators/cmo.js\n\n\n\n\n/* harmony default export */ var cmo_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "extendStatics", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "cmo_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "__extends", "b", "setPrototypeOf", "__proto__", "Array", "p", "__", "constructor", "create", "SMAIndicator", "seriesTypes", "sma", "isNumber", "merge", "CMOIndicator", "_super", "apply", "arguments", "getV<PERSON>ues", "series", "params", "i", "values", "period", "xVal", "xData", "yVal", "yData", "yValLen", "length", "CMO", "index", "Math", "min", "map", "value", "y", "firstAddedSum", "sumOfHigher<PERSON><PERSON><PERSON>", "sumOfLowerValues", "j", "push", "abs", "defaultOptions", "registerSeriesType"], "mappings": "CAYA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,EAChF,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,4BAA6B,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,iBAAiB,CAAC,CAAEJ,GACtG,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,4BAA4B,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,EAE7GJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,EACpF,OAAgB,AAAC,WACP,aACA,IAgGFC,EAhGMC,EAAuB,CAE/B,IACC,SAASP,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,SAASb,CAAM,EACtC,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,WAAa,OAAOf,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASjB,CAAO,CAAEmB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,CAAS,CACxD,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAU7KE,GACI9B,EAAgB,SAAUU,CAAC,CAC3BqB,CAAC,EAMD,MAAO/B,AALHA,CAAAA,EAAgBe,OAAOiB,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAUxB,CAAC,CAC1DqB,CAAC,EAAIrB,EAAEuB,SAAS,CAAGF,CAAG,GACd,SAAUrB,CAAC,CACnBqB,CAAC,EAAI,IAAK,IAAII,KAAKJ,EAAOA,EAAET,cAAc,CAACa,IAAIzB,CAAAA,CAAC,CAACyB,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,CAAG,CAAA,EACvCzB,EAAGqB,EAC5B,EACO,SAAUrB,CAAC,CAAEqB,CAAC,EAEjB,SAASK,IAAO,IAAI,CAACC,WAAW,CAAG3B,CAAG,CADtCV,EAAcU,EAAGqB,GAEjBrB,EAAEW,SAAS,CAAGU,AAAM,OAANA,EAAahB,OAAOuB,MAAM,CAACP,GAAMK,CAAAA,EAAGf,SAAS,CAAGU,EAAEV,SAAS,CAAE,IAAIe,CAAG,CACtF,GAGAG,EAAe,AAACV,IAA2IW,WAAW,CAACC,GAAG,CAE1KC,EAAW,AAACf,IAA+Ee,QAAQ,CAAEC,EAAQ,AAAChB,IAA+EgB,KAAK,CAelMC,EAA8B,SAAUC,CAAM,EAE9C,SAASD,IACL,OAAOC,AAAW,OAAXA,GAAmBA,EAAOC,KAAK,CAAC,IAAI,CAAEC,YAAc,IAAI,AACnE,CAgHA,OAnHAjB,EAAUc,EAAcC,GASxBD,EAAavB,SAAS,CAAC2B,SAAS,CAAG,SAAUC,CAAM,CAAEC,CAAM,EACvD,IAOIC,EAEAC,EATAC,EAASH,EAAOG,MAAM,CACtBC,EAAOL,EAAOM,KAAK,CACnBC,EAAOP,EAAOQ,KAAK,CACnBC,EAAUF,EAAOA,EAAKG,MAAM,CAAG,EAC/BC,EAAM,EAAE,CACRL,EAAQ,EAAE,CACVE,EAAQ,EAAE,CAEVI,EAAQX,EAAOW,KAAK,CAExB,IAAIP,CAAAA,EAAKK,MAAM,CAAGN,CAAK,GAGnBX,EAASc,CAAI,CAAC,EAAE,EAChBJ,EAASI,GAMTK,EAAQC,KAAKC,GAAG,CAACF,EAAOL,CAAI,CAAC,EAAE,CAACG,MAAM,CAAG,GACzCP,EAASI,EAAKQ,GAAG,CAAC,SAAUC,CAAK,EAAI,OAAOA,CAAK,CAACJ,EAAM,AAAE,IAQ9D,IAAK,IAHDK,EAHAC,EAAgB,EAChBC,EAAoB,EACpBC,EAAmB,EAIdC,EAAIjB,EAAQiB,EAAI,EAAGA,IACpBlB,CAAM,CAACkB,EAAE,CAAGlB,CAAM,CAACkB,EAAI,EAAE,CACzBF,GAAqBhB,CAAM,CAACkB,EAAE,CAAGlB,CAAM,CAACkB,EAAI,EAAE,CAEzClB,CAAM,CAACkB,EAAE,CAAGlB,CAAM,CAACkB,EAAI,EAAE,EAC9BD,CAAAA,GAAoBjB,CAAM,CAACkB,EAAI,EAAE,CAAGlB,CAAM,CAACkB,EAAE,AAAD,EAapD,IARAJ,EACIE,EAAoBC,EAAmB,EACnC,AAAC,IAAOD,CAAAA,EAAoBC,CAAe,EACtCD,CAAAA,EAAoBC,CAAe,EACxC,EACRd,EAAMgB,IAAI,CAACjB,CAAI,CAACD,EAAO,EACvBI,EAAMc,IAAI,CAACL,GACXN,EAAIW,IAAI,CAAC,CAACjB,CAAI,CAACD,EAAO,CAAEa,EAAE,EACrBf,EAAIE,EAAS,EAAGF,EAAIO,EAASP,IAC9BgB,EAAgBL,KAAKU,GAAG,CAACpB,CAAM,CAACD,EAAIE,EAAS,EAAE,CAAGD,CAAM,CAACD,EAAIE,EAAO,EAChED,CAAM,CAACD,EAAE,CAAGC,CAAM,CAACD,EAAI,EAAE,CACzBiB,GAAqBhB,CAAM,CAACD,EAAE,CAAGC,CAAM,CAACD,EAAI,EAAE,CAEzCC,CAAM,CAACD,EAAE,CAAGC,CAAM,CAACD,EAAI,EAAE,EAC9BkB,CAAAA,GAAoBjB,CAAM,CAACD,EAAI,EAAE,CAAGC,CAAM,CAACD,EAAE,AAAD,EAI5CC,CAAM,CAACD,EAAIE,EAAO,CAAGD,CAAM,CAACD,EAAIE,EAAS,EAAE,CAC3Ce,GAAqBD,EAGrBE,GAAoBF,EAGxBD,EACIE,EAAoBC,EAAmB,EACnC,AAAC,IAAOD,CAAAA,EAAoBC,CAAe,EACtCD,CAAAA,EAAoBC,CAAe,EACxC,EACRd,EAAMgB,IAAI,CAACjB,CAAI,CAACH,EAAE,EAClBM,EAAMc,IAAI,CAACL,GACXN,EAAIW,IAAI,CAAC,CAACjB,CAAI,CAACH,EAAE,CAAEe,EAAE,EAEzB,MAAO,CACHd,OAAQQ,EACRL,MAAOA,EACPE,MAAOA,CACX,EACJ,EAqBAb,EAAa6B,cAAc,CAAG9B,EAAMJ,EAAakC,cAAc,CAAE,CAC7DvB,OAAQ,CACJG,OAAQ,GACRQ,MAAO,CACX,CACJ,GACOjB,CACX,EAAEL,GACFV,IAA0I6C,kBAAkB,CAAC,MAAO9B,GA+BvI,IAAInB,EAAYE,IAGnC,OADYH,EAAoB,OAAU,AAE3C,GAET"}