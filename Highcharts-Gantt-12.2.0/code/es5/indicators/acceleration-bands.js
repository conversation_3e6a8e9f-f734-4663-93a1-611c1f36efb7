!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("highcharts"),require("highcharts").SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/indicators/acceleration-bands",[["highcharts/highcharts"],["highcharts/highcharts","SeriesRegistry"]],e):"object"==typeof exports?exports["highcharts/indicators/acceleration-bands"]=e(require("highcharts"),require("highcharts").SeriesRegistry):t.Highcharts=e(t.Highcharts,t.Highcharts.SeriesRegistry)}(this,function(t,e){return function(){"use strict";var r,a,o={512:function(t){t.exports=e},944:function(e){e.exports=t}},i={};function n(t){var e=i[t];if(void 0!==e)return e.exports;var r=i[t]={exports:{}};return o[t](r,r.exports,n),r.exports}n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,{a:e}),e},n.d=function(t,e){for(var r in e)n.o(e,r)&&!n.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};var s={};n.d(s,{default:function(){return O}});var p=n(944),h=n.n(p),l=n(512),c=n.n(l),u=c().seriesTypes.sma.prototype,f=h().defined,d=h().error,y=h().merge;!function(t){var e=["bottomLine"],r=["top","bottom"],a=["top"];function o(t){return"plot"+t.charAt(0).toUpperCase()+t.slice(1)}function i(t,e){var r=[];return(t.pointArrayMap||[]).forEach(function(t){t!==e&&r.push(o(t))}),r}function n(){var t,e=this,r=e.pointValKey,a=e.linesApiNames,n=e.areaLinesNames,s=e.points,p=e.options,h=e.graph,l={options:{gapSize:p.gapSize}},c=[],g=i(e,r),m=s.length;if(g.forEach(function(e,r){for(c[r]=[];m--;)t=s[m],c[r].push({x:t.x,plotX:t.plotX,plotY:t[e],isNull:!f(t[e])});m=s.length}),e.userOptions.fillColor&&n.length){var x=c[g.indexOf(o(n[0]))],v=1===n.length?s:c[g.indexOf(o(n[1]))],A=e.color;e.points=v,e.nextPoints=x,e.color=e.userOptions.fillColor,e.options=y(s,l),e.graph=e.area,e.fillGraph=!0,u.drawGraph.call(e),e.area=e.graph,delete e.nextPoints,delete e.fillGraph,e.color=A}a.forEach(function(t,r){c[r]?(e.points=c[r],p[t]?e.options=y(p[t].styles,l):d('Error: "There is no '+t+' in DOCS options declared. Check if linesApiNames are consistent with your DOCS line names."'),e.graph=e["graph"+t],u.drawGraph.call(e),e["graph"+t]=e.graph):d('Error: "'+t+" doesn't have equivalent in pointArrayMap. To many elements in linesApiNames relative to pointArrayMap.\"")}),e.points=s,e.options=p,e.graph=h,u.drawGraph.call(e)}function s(t){var e,r=[],a=[];if(t=t||this.points,this.fillGraph&&this.nextPoints){if((e=u.getGraphPath.call(this,this.nextPoints))&&e.length){e[0][0]="L",r=u.getGraphPath.call(this,t),a=e.slice(0,r.length);for(var o=a.length-1;o>=0;o--)r.push(a[o])}}else r=u.getGraphPath.apply(this,arguments);return r}function p(t){var e=[];return(this.pointArrayMap||[]).forEach(function(r){e.push(t[r])}),e}function h(){var t,e=this,r=this.pointArrayMap,a=[];a=i(this),u.translate.apply(this,arguments),this.points.forEach(function(o){r.forEach(function(r,i){t=o[r],e.dataModify&&(t=e.dataModify.modifyValue(t)),null!==t&&(o[a[i]]=e.yAxis.toPixels(t,!0))})})}t.compose=function(t){var o=t.prototype;return o.linesApiNames=o.linesApiNames||e.slice(),o.pointArrayMap=o.pointArrayMap||r.slice(),o.pointValKey=o.pointValKey||"top",o.areaLinesNames=o.areaLinesNames||a.slice(),o.drawGraph=n,o.getGraphPath=s,o.toYData=p,o.translate=h,t}}(a||(a={}));var g=a,m=(r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function a(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(a.prototype=e.prototype,new a)}),x=c().seriesTypes.sma,v=h().correctFloat,A=h().extend,b=h().merge,D=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return m(e,t),e.prototype.getValues=function(e,r){var a,o,i,n,s,p,h,l,c,u,f,d,y,g=r.period,m=r.factor,x=r.index,A=e.xData,b=e.yData,D=b?b.length:0,O=[],P=[],G=[],M=[],N=[];if(!(D<g)){for(y=0;y<=D;y++)y<D&&(a=b[y][2],h=v((o=b[y][1])-a)/(v(o+a)/2)*1e3*m,O.push(b[y][1]*v(1+2*h)),P.push(b[y][2]*v(1-2*h))),y>=g&&(f=A.slice(y-g,y),d=b.slice(y-g,y),c=t.prototype.getValues.call(this,{xData:f,yData:O.slice(y-g,y)},{period:g}),u=t.prototype.getValues.call(this,{xData:f,yData:P.slice(y-g,y)},{period:g}),p=(l=t.prototype.getValues.call(this,{xData:f,yData:d},{period:g,index:x})).xData[0],n=c.yData[0],s=u.yData[0],i=l.yData[0],G.push([p,n,i,s]),M.push(p),N.push([n,i,s]));return{values:G,xData:M,yData:N}}},e.defaultOptions=b(x.defaultOptions,{params:{period:20,factor:.001,index:3},lineWidth:1,topLine:{styles:{lineWidth:1}},bottomLine:{styles:{lineWidth:1}},dataGrouping:{approximation:"averages"}}),e}(x);A(D.prototype,{areaLinesNames:["top","bottom"],linesApiNames:["topLine","bottomLine"],nameBase:"Acceleration Bands",nameComponents:["period","factor"],pointArrayMap:["top","middle","bottom"],pointValKey:"middle"}),g.compose(D),c().registerSeriesType("abands",D);var O=h();return s.default}()});