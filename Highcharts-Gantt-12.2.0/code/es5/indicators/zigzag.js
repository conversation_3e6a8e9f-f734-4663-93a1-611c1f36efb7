!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("highcharts"),require("highcharts").SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/indicators/zigzag",[["highcharts/highcharts"],["highcharts/highcharts","SeriesRegistry"]],e):"object"==typeof exports?exports["highcharts/indicators/zigzag"]=e(require("highcharts"),require("highcharts").SeriesRegistry):t.Highcharts=e(t.Highcharts,t.Highcharts.SeriesRegistry)}(this,function(t,e){return function(){"use strict";var r,n={512:function(t){t.exports=e},944:function(e){e.exports=t}},i={};function o(t){var e=i[t];if(void 0!==e)return e.exports;var r=i[t]={exports:{}};return n[t](r,r.exports,o),r.exports}o.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return o.d(e,{a:e}),e},o.d=function(t,e){for(var r in e)o.o(e,r)&&!o.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},o.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};var s={};o.d(s,{default:function(){return y}});var a=o(944),h=o.n(a),u=o(512),p=o.n(u),c=(r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),f=p().seriesTypes.sma,g=h().merge,d=h().extend,l=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return c(e,t),e.prototype.getValues=function(t,e){var r,n,i,o,s=e.lowIndex,a=e.highIndex,h=e.deviation/100,u={low:1+h,high:1-h},p=t.xData,c=t.yData,f=c?c.length:0,g=[],d=[],l=[],y=!1,v=!1;if(p&&!(p.length<=1)&&(!f||void 0!==c[0][s]&&void 0!==c[0][a])){var x=c[0][s],m=c[0][a];for(r=1;r<f;r++)c[r][s]<=m*u.high?(g.push([p[0],m]),i=[p[r],c[r][s]],o=!0,y=!0):c[r][a]>=x*u.low&&(g.push([p[0],x]),i=[p[r],c[r][a]],o=!1,y=!0),y&&(d.push(g[0][0]),l.push(g[0][1]),n=r++,r=f);for(r=n;r<f;r++)o?(c[r][s]<=i[1]&&(i=[p[r],c[r][s]]),c[r][a]>=i[1]*u.low&&(v=a)):(c[r][a]>=i[1]&&(i=[p[r],c[r][a]]),c[r][s]<=i[1]*u.high&&(v=s)),!1!==v&&(g.push(i),d.push(i[0]),l.push(i[1]),i=[p[r],c[r][v]],o=!o,v=!1);var _=g.length;return 0!==_&&g[_-1][0]<p[f-1]&&(g.push(i),d.push(i[0]),l.push(i[1])),{values:g,xData:d,yData:l}}},e.defaultOptions=g(f.defaultOptions,{params:{index:void 0,period:void 0,lowIndex:2,highIndex:1,deviation:1}}),e}(f);d(l.prototype,{nameComponents:["deviation"],nameSuffixes:["%"],nameBase:"Zig Zag"}),p().registerSeriesType("zigzag",l);var y=h();return s.default}()});