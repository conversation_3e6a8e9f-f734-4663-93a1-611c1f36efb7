!function(r,t){"object"==typeof exports&&"object"==typeof module?module.exports=t(require("highcharts"),require("highcharts").SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/indicators/ao",[["highcharts/highcharts"],["highcharts/highcharts","SeriesRegistry"]],t):"object"==typeof exports?exports["highcharts/indicators/ao"]=t(require("highcharts"),require("highcharts").SeriesRegistry):r.Highcharts=t(r.Highcharts,r.Highcharts.SeriesRegistry)}(this,function(r,t){return function(){"use strict";var e,o={512:function(r){r.exports=t},944:function(t){t.exports=r}},n={};function i(r){var t=n[r];if(void 0!==t)return t.exports;var e=n[r]={exports:{}};return o[r](e,e.exports,i),e.exports}i.n=function(r){var t=r&&r.__esModule?function(){return r.default}:function(){return r};return i.d(t,{a:t}),t},i.d=function(r,t){for(var e in t)i.o(t,e)&&!i.o(r,e)&&Object.defineProperty(r,e,{enumerable:!0,get:t[e]})},i.o=function(r,t){return Object.prototype.hasOwnProperty.call(r,t)};var s={};i.d(s,{default:function(){return b}});var a=i(944),c=i.n(a),u=i(512),p=i.n(u),h=(e=function(r,t){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(r,t){r.__proto__=t}||function(r,t){for(var e in t)t.hasOwnProperty(e)&&(r[e]=t[e])})(r,t)},function(r,t){function o(){this.constructor=r}e(r,t),r.prototype=null===t?Object.create(t):(o.prototype=t.prototype,new o)}),f=c().noop,l=p().seriesTypes,d=l.column.prototype,g=l.sma,y=c().extend,v=c().merge,m=c().correctFloat,x=c().isArray,O=function(r){function t(){return null!==r&&r.apply(this,arguments)||this}return h(t,r),t.prototype.drawGraph=function(){var r,t=this.options,e=this.points,o=this.userOptions.color,n=t.greaterBarColor,i=t.lowerBarColor,s=e[0];if(!o&&s)for(r=1,s.color=n;r<e.length;r++)e[r].y>e[r-1].y?e[r].color=n:e[r].y<e[r-1].y?e[r].color=i:e[r].color=e[r-1].color},t.prototype.getValues=function(r){var t,e,o,n,i,s,a=r.xData||[],c=r.yData||[],u=c.length,p=[],h=[],f=[],l=0,d=0;if(!(a.length<=34)&&x(c[0])&&4===c[0].length){for(i=0;i<33;i++)n=(c[i][1]+c[i][2])/2,i>=29&&(d=m(d+n)),l=m(l+n);for(s=33;s<u;s++)d=m(d+(n=(c[s][1]+c[s][2])/2)),l=m(l+n),t=m(d/5-l/34),p.push([a[s],t]),h.push(a[s]),f.push(t),e=s+1-5,o=s+1-34,d=m(d-(c[e][1]+c[e][2])/2),l=m(l-(c[o][1]+c[o][2])/2);return{values:p,xData:h,yData:f}}},t.defaultOptions=v(g.defaultOptions,{params:{index:void 0,period:void 0},greaterBarColor:"#06b535",lowerBarColor:"#f21313",threshold:0,groupPadding:.2,pointPadding:.2,crisp:!1,states:{hover:{halo:{size:0}}}}),t}(g);y(O.prototype,{nameBase:"AO",nameComponents:void 0,markerAttribs:f,getColumnMetrics:d.getColumnMetrics,crispCol:d.crispCol,translate:d.translate,drawPoints:d.drawPoints}),p().registerSeriesType("ao",O);var b=c();return s.default}()});