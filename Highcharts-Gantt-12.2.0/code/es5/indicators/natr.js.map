{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.2.0 (2025-04-07)\n * @module highcharts/indicators/natr\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Pawe<PERSON>\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/natr\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"SeriesRegistry\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/natr\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ natr_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es5/es-modules/Stock/Indicators/NATR/NATRIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar ATRIndicator = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.atr;\n\nvar merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge;\n/* *\n *\n *  Class\n *\n * */\n/**\n * The NATR series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.natr\n *\n * @augments Highcharts.Series\n */\nvar NATRIndicator = /** @class */ (function (_super) {\n    __extends(NATRIndicator, _super);\n    function NATRIndicator() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    NATRIndicator.prototype.getValues = function (series, params) {\n        var atrData = (_super.prototype.getValues.apply(this,\n            arguments)),\n            atrLength = atrData.values.length,\n            yVal = series.yData;\n        var i = 0,\n            period = params.period - 1;\n        if (!atrData) {\n            return;\n        }\n        for (; i < atrLength; i++) {\n            atrData.yData[i] = (atrData.values[i][1] / yVal[period][3] * 100);\n            atrData.values[i][1] = atrData.yData[i];\n            period++;\n        }\n        return atrData;\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    /**\n     * Normalized average true range indicator (NATR). This series requires\n     * `linkedTo` option to be set and should be loaded after the\n     * `stock/indicators/indicators.js` and `stock/indicators/atr.js`.\n     *\n     * @sample {highstock} stock/indicators/natr\n     *         NATR indicator\n     *\n     * @extends      plotOptions.atr\n     * @since        7.0.0\n     * @product      highstock\n     * @requires     stock/indicators/indicators\n     * @requires     stock/indicators/natr\n     * @optionparent plotOptions.natr\n     */\n    NATRIndicator.defaultOptions = merge(ATRIndicator.defaultOptions, {\n        tooltip: {\n            valueSuffix: '%'\n        }\n    });\n    return NATRIndicator;\n}(ATRIndicator));\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('natr', NATRIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var NATR_NATRIndicator = ((/* unused pure expression or super */ null && (NATRIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A `NATR` series. If the [type](#series.natr.type) option is not specified, it\n * is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.natr\n * @since     7.0.0\n * @product   highstock\n * @excluding dataParser, dataURL\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/atr\n * @requires  stock/indicators/natr\n * @apioption series.natr\n */\n''; // To include the above in the js output'\n\n;// ./code/es5/es-modules/masters/indicators/natr.js\n\n\n\n\n/* harmony default export */ var natr_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "extendStatics", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "natr_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "__extends", "b", "setPrototypeOf", "__proto__", "Array", "p", "__", "constructor", "create", "ATRIndicator", "seriesTypes", "atr", "merge", "NATRIndicator", "_super", "apply", "arguments", "getV<PERSON>ues", "series", "params", "atrData", "at<PERSON><PERSON><PERSON><PERSON>", "values", "length", "yVal", "yData", "i", "period", "defaultOptions", "tooltip", "valueSuffix", "registerSeriesType"], "mappings": "CAYA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,EAChF,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,6BAA8B,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,iBAAiB,CAAC,CAAEJ,GACvG,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,6BAA6B,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,EAE9GJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,EACpF,OAAgB,AAAC,WACP,aACA,IAgGFC,EAhGMC,EAAuB,CAE/B,IACC,SAASP,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,SAASb,CAAM,EACtC,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,WAAa,OAAOf,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASjB,CAAO,CAAEmB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,CAAU,CACzD,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAU7KE,GACI9B,EAAgB,SAAUU,CAAC,CAC3BqB,CAAC,EAMD,MAAO/B,AALHA,CAAAA,EAAgBe,OAAOiB,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAUxB,CAAC,CAC1DqB,CAAC,EAAIrB,EAAEuB,SAAS,CAAGF,CAAG,GACd,SAAUrB,CAAC,CACnBqB,CAAC,EAAI,IAAK,IAAII,KAAKJ,EAAOA,EAAET,cAAc,CAACa,IAAIzB,CAAAA,CAAC,CAACyB,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,CAAG,CAAA,EACvCzB,EAAGqB,EAC5B,EACO,SAAUrB,CAAC,CAAEqB,CAAC,EAEjB,SAASK,IAAO,IAAI,CAACC,WAAW,CAAG3B,CAAG,CADtCV,EAAcU,EAAGqB,GAEjBrB,EAAEW,SAAS,CAAGU,AAAM,OAANA,EAAahB,OAAOuB,MAAM,CAACP,GAAMK,CAAAA,EAAGf,SAAS,CAAGU,EAAEV,SAAS,CAAE,IAAIe,CAAG,CACtF,GAGAG,EAAe,AAACV,IAA2IW,WAAW,CAACC,GAAG,CAE1KC,EAAQ,AAACf,IAA+Ee,KAAK,CAe7FC,EAA+B,SAAUC,CAAM,EAE/C,SAASD,IACL,OAAOC,AAAW,OAAXA,GAAmBA,EAAOC,KAAK,CAAC,IAAI,CAAEC,YAAc,IAAI,AACnE,CAgDA,OAnDAhB,EAAUa,EAAeC,GASzBD,EAActB,SAAS,CAAC0B,SAAS,CAAG,SAAUC,CAAM,CAAEC,CAAM,EACxD,IAAIC,EAAWN,EAAOvB,SAAS,CAAC0B,SAAS,CAACF,KAAK,CAAC,IAAI,CAChDC,WACAK,EAAYD,EAAQE,MAAM,CAACC,MAAM,CACjCC,EAAON,EAAOO,KAAK,CACnBC,EAAI,EACJC,EAASR,EAAOQ,MAAM,CAAG,EAC7B,GAAKP,GAGL,KAAOM,EAAIL,EAAWK,IAClBN,EAAQK,KAAK,CAACC,EAAE,CAAIN,EAAQE,MAAM,CAACI,EAAE,CAAC,EAAE,CAAGF,CAAI,CAACG,EAAO,CAAC,EAAE,CAAG,IAC7DP,EAAQE,MAAM,CAACI,EAAE,CAAC,EAAE,CAAGN,EAAQK,KAAK,CAACC,EAAE,CACvCC,IAEJ,OAAOP,EACX,EAqBAP,EAAce,cAAc,CAAGhB,EAAMH,EAAamB,cAAc,CAAE,CAC9DC,QAAS,CACLC,YAAa,GACjB,CACJ,GACOjB,CACX,EAAEJ,GACFV,IAA0IgC,kBAAkB,CAAC,OAAQlB,GAgCxI,IAAIlB,EAAaE,IAGpC,OADYH,EAAoB,OAAU,AAE3C,GAET"}