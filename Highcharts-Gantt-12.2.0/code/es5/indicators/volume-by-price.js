!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("highcharts"),require("highcharts").SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/indicators/volume-by-price",[["highcharts/highcharts"],["highcharts/highcharts","SeriesRegistry"]],e):"object"==typeof exports?exports["highcharts/indicators/volume-by-price"]=e(require("highcharts"),require("highcharts").SeriesRegistry):t.Highcharts=e(t.Highcharts,t.Highcharts.SeriesRegistry)}(this,function(t,e){return function(){"use strict";var o,i,n={512:function(t){t.exports=e},944:function(e){e.exports=t}},r={};function s(t){var e=r[t];if(void 0!==e)return e.exports;var o=r[t]={exports:{}};return n[t](o,o.exports,s),o.exports}s.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return s.d(e,{a:e}),e},s.d=function(t,e){for(var o in e)s.o(e,o)&&!s.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},s.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};var a={};s.d(a,{default:function(){return L}});var p=s(944),u=s.n(p),h=s(512),l=s.n(h),c=(o=function(t,e){return(o=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)e.hasOwnProperty(o)&&(t[o]=e[o])})(t,e)},function(t,e){function i(){this.constructor=t}o(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),d=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return c(e,t),e.prototype.destroy=function(){this.negativeGraphic&&(this.negativeGraphic=this.negativeGraphic.destroy()),t.prototype.destroy.apply(this,arguments)},e}(l().seriesTypes.sma.prototype.pointClass),f=(i=function(t,e){return(i=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o])})(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function o(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),m=u().animObject,y=u().noop,v=l().seriesTypes,g=v.column.prototype,x=v.sma,b=u().addEvent,S=u().arrayMax,D=u().arrayMin,V=u().correctFloat,P=u().defined,w=u().error,C=u().extend,A=u().isArray,z=u().merge,O=Math.abs,_=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return f(e,t),e.prototype.init=function(e,o){var i=this;delete o.data,t.prototype.init.apply(i,arguments);var n=b(this.chart.constructor,"afterLinkSeries",function(){if(i.options){var t=i.options.params,o=i.linkedParent,r=e.get(t.volumeSeriesID);i.addCustomEvents(o,r)}n()},{order:1});return i},e.prototype.addCustomEvents=function(t,e){var o=this,i=function(){o.chart.redraw(),o.setData([]),o.zoneStarts=[],o.zoneLinesSVG&&(o.zoneLinesSVG=o.zoneLinesSVG.destroy())};return o.dataEventsToUnbind.push(b(t,"remove",function(){i()})),e&&o.dataEventsToUnbind.push(b(e,"remove",function(){i()})),o},e.prototype.animate=function(t){var e=this,o=e.chart.inverted,i=e.group,n={};if(!t&&i){var r=o?e.yAxis.top:e.xAxis.left;o?(i["forceAnimate:translateY"]=!0,n.translateY=r):(i["forceAnimate:translateX"]=!0,n.translateX=r),i.animate(n,C(m(e.options.animation),{step:function(t,o){e.group.attr({scaleX:Math.max(.001,o.pos)})}}))}},e.prototype.drawPoints=function(){this.options.volumeDivision.enabled&&(this.posNegVolume(!0,!0),g.drawPoints.apply(this,arguments),this.posNegVolume(!1,!1)),g.drawPoints.apply(this,arguments)},e.prototype.posNegVolume=function(t,e){var o,i,n,r,s=e?["positive","negative"]:["negative","positive"],a=this.options.volumeDivision,p=this.points.length,u=[],h=[],l=0;for(t?(this.posWidths=u,this.negWidths=h):(u=this.posWidths,h=this.negWidths);l<p;l++)(r=this.points[l])[s[0]+"Graphic"]=r.graphic,r.graphic=r[s[1]+"Graphic"],t&&(o=r.shapeArgs.width,(n=(i=this.priceZones[l]).wholeVolumeData)?(u.push(o/n*i.positiveVolumeData),h.push(o/n*i.negativeVolumeData)):(u.push(0),h.push(0))),r.color=e?a.styles.positiveColor:a.styles.negativeColor,r.shapeArgs.width=e?this.posWidths[l]:this.negWidths[l],r.shapeArgs.x=e?r.shapeArgs.x:this.posWidths[l]},e.prototype.translate=function(){var t,e,o,i,n,r,s,a,p,u,h=this,l=h.options,c=h.chart,d=h.yAxis,f=d.min,m=h.options.zoneLines,y=h.priceZones,v=0;g.translate.apply(h);var x=h.points;x.length&&(s=l.pointPadding<.5?l.pointPadding:.1,t=S(h.volumeDataArray),e=c.plotWidth/2,a=c.plotTop,o=O(d.toPixels(f)-d.toPixels(f+h.rangeStep)),n=O(d.toPixels(f)-d.toPixels(f+h.rangeStep)),s&&(i=O(o*(1-2*s)),v=O((o-i)/2),o=O(i)),x.forEach(function(i,s){p=i.barX=i.plotX=0,u=i.plotY=d.toPixels(y[s].start)-a-(d.reversed?o-n:o)-v,i.pointWidth=r=V(e*y[s].wholeVolumeData/t),i.shapeArgs=h.crispCol.apply(h,[p,u,r,o]),i.volumeNeg=y[s].negativeVolumeData,i.volumePos=y[s].positiveVolumeData,i.volumeAll=y[s].wholeVolumeData}),m.enabled&&h.drawZones(c,d,h.zoneStarts,m.styles))},e.prototype.getExtremes=function(){var e,o=this.options.compare,i=this.options.cumulative;return this.options.compare?(this.options.compare=void 0,e=t.prototype.getExtremes.call(this),this.options.compare=o):this.options.cumulative?(this.options.cumulative=!1,e=t.prototype.getExtremes.call(this),this.options.cumulative=i):e=t.prototype.getExtremes.call(this),e},e.prototype.getValues=function(t,e){var o=t.getColumn("x",!0),i=t.processedYData,n=this.chart,r=e.ranges,s=[],a=[],p=[],u=n.get(e.volumeSeriesID);if(!t.chart){w("Base series not found! In case it has been removed, add a new one.",!0,n);return}if(!u||!u.getColumn("x",!0).length){var h=u&&!u.getColumn("x",!0).length?" does not contain any data.":" not found! Check `volumeSeriesID`.";w("Series "+e.volumeSeriesID+h,!0,n);return}var l=A(i[0]);if(l&&4!==i[0].length){w("Type of "+t.name+" series is different than line, OHLC or candlestick.",!0,n);return}return(this.priceZones=this.specifyZones(l,o,i,r,u)).forEach(function(t,e){s.push([t.x,t.end]),a.push(s[e][0]),p.push(s[e][1])}),{values:s,xData:a,yData:p}},e.prototype.specifyZones=function(t,e,o,i,n){var r=!!t&&function(t){for(var e,o=t.length,i=t[0][3],n=i,r=1;r<o;r++)(e=t[r][3])<i&&(i=e),e>n&&(n=e);return{min:i,max:n}}(o),s=this.zoneStarts=[],a=[],p=r?r.min:D(o),u=r?r.max:S(o),h=0,l=1,c=this.linkedParent;if(!this.options.compareToMain&&c.dataModify&&(p=c.dataModify.modifyValue(p),u=c.dataModify.modifyValue(u)),!P(p)||!P(u))return this.points.length&&(this.setData([]),this.zoneStarts=[],this.zoneLinesSVG&&(this.zoneLinesSVG=this.zoneLinesSVG.destroy())),[];var d=this.rangeStep=V(u-p)/i;for(s.push(p);h<i-1;h++)s.push(V(s[h]+d));s.push(u);for(var f=s.length;l<f;l++)a.push({index:l-1,x:e[0],start:s[l-1],end:s[l]});return this.volumePerZone(t,a,n,e,o)},e.prototype.volumePerZone=function(t,e,o,i,n){var r,s,a,p,u,h=this,l=o.getColumn("x",!0),c=o.getColumn("y",!0),d=e.length-1,f=n.length,m=c.length;return O(f-m)&&(i[0]!==l[0]&&c.unshift(0),i[f-1]!==l[m-1]&&c.push(0)),h.volumeDataArray=[],e.forEach(function(e){for(u=0,e.wholeVolumeData=0,e.positiveVolumeData=0,e.negativeVolumeData=0;u<f;u++){s=!1,a=!1,p=t?n[u][3]:n[u],r=u?t?n[u-1][3]:n[u-1]:p;var o=h.linkedParent;!h.options.compareToMain&&o.dataModify&&(p=o.dataModify.modifyValue(p),r=o.dataModify.modifyValue(r)),p<=e.start&&0===e.index&&(s=!0),p>=e.end&&e.index===d&&(a=!0),(p>e.start||s)&&(p<e.end||a)&&(e.wholeVolumeData+=c[u],r>p?e.negativeVolumeData+=c[u]:e.positiveVolumeData+=c[u])}h.volumeDataArray.push(e.wholeVolumeData)}),e},e.prototype.drawZones=function(t,e,o,i){var n,r=t.renderer,s=t.plotWidth,a=t.plotTop,p=this.zoneLinesSVG,u=[];o.forEach(function(o){n=e.toPixels(o)-a,u=u.concat(t.renderer.crispLine([["M",0,n],["L",s,n]],i.lineWidth))}),p?p.animate({d:u}):p=this.zoneLinesSVG=r.path(u).attr({"stroke-width":i.lineWidth,stroke:i.color,dashstyle:i.dashStyle,zIndex:this.group.zIndex+.1}).add(this.group)},e.defaultOptions=z(x.defaultOptions,{params:{index:void 0,period:void 0,ranges:12,volumeSeriesID:"volume"},zoneLines:{enabled:!0,styles:{color:"#0A9AC9",dashStyle:"LongDash",lineWidth:1}},volumeDivision:{enabled:!0,styles:{positiveColor:"rgba(144, 237, 125, 0.8)",negativeColor:"rgba(244, 91, 91, 0.8)"}},animationLimit:1e3,enableMouseTracking:!1,pointPadding:0,zIndex:-1,crisp:!0,dataGrouping:{enabled:!1},dataLabels:{align:"left",allowOverlap:!0,enabled:!0,format:"P: {point.volumePos:.2f} | N: {point.volumeNeg:.2f}",padding:0,style:{fontSize:"0.5em"},verticalAlign:"top"}}),e}(x);C(_.prototype,{nameBase:"Volume by Price",nameComponents:["ranges"],calculateOn:{chart:"render",xAxis:"afterSetExtremes"},pointClass:d,markerAttribs:y,drawGraph:y,getColumnMetrics:g.getColumnMetrics,crispCol:g.crispCol}),l().registerSeriesType("vbp",_);var L=u();return a.default}()});