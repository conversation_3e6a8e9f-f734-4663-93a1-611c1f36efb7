!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("highcharts"),require("highcharts").SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/indicators/cmf",[["highcharts/highcharts"],["highcharts/highcharts","SeriesRegistry"]],e):"object"==typeof exports?exports["highcharts/indicators/cmf"]=e(require("highcharts"),require("highcharts").SeriesRegistry):t.Highcharts=e(t.Highcharts,t.Highcharts.SeriesRegistry)}(this,function(t,e){return function(){"use strict";var r,n={512:function(t){t.exports=e},944:function(e){e.exports=t}},o={};function i(t){var e=o[t];if(void 0!==e)return e.exports;var r=o[t]={exports:{}};return n[t](r,r.exports,i),r.exports}i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,{a:e}),e},i.d=function(t,e){for(var r in e)i.o(e,r)&&!i.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};var u={};i.d(u,{default:function(){return y}});var s=i(944),a=i.n(s),h=i(512),c=i.n(h),p=(r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}),l=c().seriesTypes.sma,f=a().merge,d=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.nameBase="Chaikin Money Flow",e}return p(e,t),e.prototype.isValid=function(){var t,e=this.chart,r=this.options,n=this.linkedParent,o=this.volumeSeries||(this.volumeSeries=e.get(r.params.volumeSeriesID)),i=(null===(t=null==n?void 0:n.pointArrayMap)||void 0===t?void 0:t.length)===4;function u(t){return t.dataTable.rowCount>=r.params.period}return!!(n&&o&&u(n)&&u(o)&&i)},e.prototype.getValues=function(t,e){if(this.isValid())return this.getMoneyFlow(t.xData,t.yData,this.volumeSeries.getColumn("y"),e.period)},e.prototype.getMoneyFlow=function(t,e,r,n){var o,i,u=e.length,s=[],a=[],h=[],c=[],p=-1,l=0,f=0;function d(t,e){var r=t[1],n=t[2],i=t[3];return null!==e&&null!==r&&null!==n&&null!==i&&r!==n?(i-n-(r-i))/(r-n)*e:(p=o,null)}if(n>0&&n<=u){for(o=0;o<n;o++)s[o]=d(e[o],r[o]),l+=r[o],f+=s[o];for(a.push(t[o-1]),h.push(o-p>=n&&0!==l?f/l:null),c.push([a[0],h[0]]);o<u;o++)s[o]=d(e[o],r[o]),l-=r[o-n],l+=r[o],f-=s[o-n],f+=s[o],i=[t[o],o-p>=n?f/l:null],a.push(i[0]),h.push(i[1]),c.push([i[0],i[1]])}return{values:c,xData:a,yData:h}},e.defaultOptions=f(l.defaultOptions,{params:{index:void 0,volumeSeriesID:"volume"}}),e}(l);c().registerSeriesType("cmf",d);var y=a();return u.default}()});