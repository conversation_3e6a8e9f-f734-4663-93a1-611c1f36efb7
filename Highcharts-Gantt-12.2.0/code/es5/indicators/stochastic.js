!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("highcharts"),require("highcharts").SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/indicators/stochastic",[["highcharts/highcharts"],["highcharts/highcharts","SeriesRegistry"]],e):"object"==typeof exports?exports["highcharts/indicators/stochastic"]=e(require("highcharts"),require("highcharts").SeriesRegistry):t.Highcharts=e(t.Highcharts,t.Highcharts.SeriesRegistry)}(this,function(t,e){return function(){"use strict";var r,o,i={512:function(t){t.exports=e},944:function(e){e.exports=t}},n={};function a(t){var e=n[t];if(void 0!==e)return e.exports;var r=n[t]={exports:{}};return i[t](r,r.exports,a),r.exports}a.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return a.d(e,{a:e}),e},a.d=function(t,e){for(var r in e)a.o(e,r)&&!a.o(t,r)&&Object.defineProperty(t,r,{enumerable:!0,get:e[r]})},a.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};var s={};a.d(s,{default:function(){return O}});var p=a(944),h=a.n(p),l={getArrayExtremes:function(t,e,r){return t.reduce(function(t,o){return[Math.min(t[0],o[e]),Math.max(t[1],o[r])]},[Number.MAX_VALUE,-Number.MAX_VALUE])}},c=a(512),u=a.n(c),f=u().seriesTypes.sma.prototype,y=h().defined,d=h().error,g=h().merge;!function(t){var e=["bottomLine"],r=["top","bottom"],o=["top"];function i(t){return"plot"+t.charAt(0).toUpperCase()+t.slice(1)}function n(t,e){var r=[];return(t.pointArrayMap||[]).forEach(function(t){t!==e&&r.push(i(t))}),r}function a(){var t,e=this,r=e.pointValKey,o=e.linesApiNames,a=e.areaLinesNames,s=e.points,p=e.options,h=e.graph,l={options:{gapSize:p.gapSize}},c=[],u=n(e,r),m=s.length;if(u.forEach(function(e,r){for(c[r]=[];m--;)t=s[m],c[r].push({x:t.x,plotX:t.plotX,plotY:t[e],isNull:!y(t[e])});m=s.length}),e.userOptions.fillColor&&a.length){var v=c[u.indexOf(i(a[0]))],x=1===a.length?s:c[u.indexOf(i(a[1]))],A=e.color;e.points=x,e.nextPoints=v,e.color=e.userOptions.fillColor,e.options=g(s,l),e.graph=e.area,e.fillGraph=!0,f.drawGraph.call(e),e.area=e.graph,delete e.nextPoints,delete e.fillGraph,e.color=A}o.forEach(function(t,r){c[r]?(e.points=c[r],p[t]?e.options=g(p[t].styles,l):d('Error: "There is no '+t+' in DOCS options declared. Check if linesApiNames are consistent with your DOCS line names."'),e.graph=e["graph"+t],f.drawGraph.call(e),e["graph"+t]=e.graph):d('Error: "'+t+" doesn't have equivalent in pointArrayMap. To many elements in linesApiNames relative to pointArrayMap.\"")}),e.points=s,e.options=p,e.graph=h,f.drawGraph.call(e)}function s(t){var e,r=[],o=[];if(t=t||this.points,this.fillGraph&&this.nextPoints){if((e=f.getGraphPath.call(this,this.nextPoints))&&e.length){e[0][0]="L",r=f.getGraphPath.call(this,t),o=e.slice(0,r.length);for(var i=o.length-1;i>=0;i--)r.push(o[i])}}else r=f.getGraphPath.apply(this,arguments);return r}function p(t){var e=[];return(this.pointArrayMap||[]).forEach(function(r){e.push(t[r])}),e}function h(){var t,e=this,r=this.pointArrayMap,o=[];o=n(this),f.translate.apply(this,arguments),this.points.forEach(function(i){r.forEach(function(r,n){t=i[r],e.dataModify&&(t=e.dataModify.modifyValue(t)),null!==t&&(i[o[n]]=e.yAxis.toPixels(t,!0))})})}t.compose=function(t){var i=t.prototype;return i.linesApiNames=i.linesApiNames||e.slice(),i.pointArrayMap=i.pointArrayMap||r.slice(),i.pointValKey=i.pointValKey||"top",i.areaLinesNames=i.areaLinesNames||o.slice(),i.drawGraph=a,i.getGraphPath=s,i.toYData=p,i.translate=h,t}}(o||(o={}));var m=o,v=(r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])})(t,e)},function(t,e){function o(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(o.prototype=e.prototype,new o)}),x=u().seriesTypes.sma,A=h().extend,b=h().isArray,N=h().merge,M=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return v(e,t),e.prototype.init=function(){t.prototype.init.apply(this,arguments),this.options=N({smoothedLine:{styles:{lineColor:this.color}}},this.options)},e.prototype.getValues=function(e,r){var o,i,n,a,s,p=r.periods[0],h=r.periods[1],c=e.xData,u=e.yData,f=u?u.length:0,y=[],d=[],g=[],m=null;if(!(f<p)&&b(u[0])&&4===u[0].length){var v=!0,x=0;for(s=p-1;s<f;s++){if(o=u.slice(s-p+1,s+1),i=(a=l.getArrayExtremes(o,2,1))[0],isNaN(n=(u[s][3]-i)/(a[1]-i)*100)&&v){x++;continue}v&&!isNaN(n)&&(v=!1);var A=d.push(c[s]);isNaN(n)?g.push([g[A-2]&&"number"==typeof g[A-2][0]?g[A-2][0]:null,null]):g.push([n,null]),s>=x+(p-1)+(h-1)&&(m=t.prototype.getValues.call(this,{xData:d.slice(-h),yData:g.slice(-h)},{period:h}).yData[0]),y.push([c[s],n,m]),g[A-1][1]=m}return{values:y,xData:d,yData:g}}},e.defaultOptions=N(x.defaultOptions,{params:{index:void 0,period:void 0,periods:[14,3]},marker:{enabled:!1},tooltip:{pointFormat:'<span style="color:{point.color}">●</span><b> {series.name}</b><br/>%K: {point.y}<br/>%D: {point.smoothed}<br/>'},smoothedLine:{styles:{lineWidth:1,lineColor:void 0}},dataGrouping:{approximation:"averages"}}),e}(x);A(M.prototype,{areaLinesNames:[],nameComponents:["periods"],nameBase:"Stochastic",pointArrayMap:["y","smoothed"],parallelArrays:["x","y","smoothed"],pointValKey:"y",linesApiNames:["smoothedLine"]}),m.compose(M),u().registerSeriesType("stochastic",M);var O=h();return s.default}()});