!function(t,r){"object"==typeof exports&&"object"==typeof module?module.exports=r(require("highcharts"),require("highcharts").SeriesRegistry):"function"==typeof define&&define.amd?define("highcharts/indicators/aroon-oscillator",[["highcharts/highcharts"],["highcharts/highcharts","SeriesRegistry"]],r):"object"==typeof exports?exports["highcharts/indicators/aroon-oscillator"]=r(require("highcharts"),require("highcharts").SeriesRegistry):t.Highcharts=r(t.Highcharts,t.Highcharts.SeriesRegistry)}(this,function(t,r){return function(){"use strict";var e,o,n={512:function(t){t.exports=r},944:function(r){r.exports=t}},i={};function a(t){var r=i[t];if(void 0!==r)return r.exports;var e=i[t]={exports:{}};return n[t](e,e.exports,a),e.exports}a.n=function(t){var r=t&&t.__esModule?function(){return t.default}:function(){return t};return a.d(r,{a:r}),r},a.d=function(t,r){for(var e in r)a.o(r,e)&&!a.o(t,e)&&Object.defineProperty(t,e,{enumerable:!0,get:r[e]})},a.o=function(t,r){return Object.prototype.hasOwnProperty.call(t,r)};var s={};a.d(s,{default:function(){return P}});var p=a(944),h=a.n(p),l=a(512),c=a.n(l),u=c().seriesTypes.sma.prototype,f=h().defined,y=h().error,g=h().merge;!function(t){var r=["bottomLine"],e=["top","bottom"],o=["top"];function n(t){return"plot"+t.charAt(0).toUpperCase()+t.slice(1)}function i(t,r){var e=[];return(t.pointArrayMap||[]).forEach(function(t){t!==r&&e.push(n(t))}),e}function a(){var t,r=this,e=r.pointValKey,o=r.linesApiNames,a=r.areaLinesNames,s=r.points,p=r.options,h=r.graph,l={options:{gapSize:p.gapSize}},c=[],d=i(r,e),m=s.length;if(d.forEach(function(r,e){for(c[e]=[];m--;)t=s[m],c[e].push({x:t.x,plotX:t.plotX,plotY:t[r],isNull:!f(t[r])});m=s.length}),r.userOptions.fillColor&&a.length){var v=c[d.indexOf(n(a[0]))],x=1===a.length?s:c[d.indexOf(n(a[1]))],A=r.color;r.points=x,r.nextPoints=v,r.color=r.userOptions.fillColor,r.options=g(s,l),r.graph=r.area,r.fillGraph=!0,u.drawGraph.call(r),r.area=r.graph,delete r.nextPoints,delete r.fillGraph,r.color=A}o.forEach(function(t,e){c[e]?(r.points=c[e],p[t]?r.options=g(p[t].styles,l):y('Error: "There is no '+t+' in DOCS options declared. Check if linesApiNames are consistent with your DOCS line names."'),r.graph=r["graph"+t],u.drawGraph.call(r),r["graph"+t]=r.graph):y('Error: "'+t+" doesn't have equivalent in pointArrayMap. To many elements in linesApiNames relative to pointArrayMap.\"")}),r.points=s,r.options=p,r.graph=h,u.drawGraph.call(r)}function s(t){var r,e=[],o=[];if(t=t||this.points,this.fillGraph&&this.nextPoints){if((r=u.getGraphPath.call(this,this.nextPoints))&&r.length){r[0][0]="L",e=u.getGraphPath.call(this,t),o=r.slice(0,e.length);for(var n=o.length-1;n>=0;n--)e.push(o[n])}}else e=u.getGraphPath.apply(this,arguments);return e}function p(t){var r=[];return(this.pointArrayMap||[]).forEach(function(e){r.push(t[e])}),r}function h(){var t,r=this,e=this.pointArrayMap,o=[];o=i(this),u.translate.apply(this,arguments),this.points.forEach(function(n){e.forEach(function(e,i){t=n[e],r.dataModify&&(t=r.dataModify.modifyValue(t)),null!==t&&(n[o[i]]=r.yAxis.toPixels(t,!0))})})}t.compose=function(t){var n=t.prototype;return n.linesApiNames=n.linesApiNames||r.slice(),n.pointArrayMap=n.pointArrayMap||e.slice(),n.pointValKey=n.pointValKey||"top",n.areaLinesNames=n.areaLinesNames||o.slice(),n.drawGraph=a,n.getGraphPath=s,n.toYData=p,n.translate=h,t}}(o||(o={}));var d=o,m=(e=function(t,r){return(e=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)r.hasOwnProperty(e)&&(t[e]=r[e])})(t,r)},function(t,r){function o(){this.constructor=t}e(t,r),t.prototype=null===r?Object.create(r):(o.prototype=r.prototype,new o)}),v=c().seriesTypes.aroon,x=h().extend,A=h().merge,O=function(t){function r(){return null!==t&&t.apply(this,arguments)||this}return m(r,t),r.prototype.getValues=function(r,e){var o,n,i=[],a=[],s=[],p=t.prototype.getValues.call(this,r,e);for(n=0;n<p.yData.length;n++)o=p.yData[n][0]-p.yData[n][1],i.push([p.xData[n],o]),a.push(p.xData[n]),s.push(o);return{values:i,xData:a,yData:s}},r.defaultOptions=A(v.defaultOptions,{tooltip:{pointFormat:'<span style="color:{point.color}">●</span><b> {series.name}</b>: {point.y}'}}),r}(v);x(O.prototype,{nameBase:"Aroon Oscillator",linesApiNames:[],pointArrayMap:["y"],pointValKey:"y"}),d.compose(v),c().registerSeriesType("aroonoscillator",O);var P=h();return s.default}()});