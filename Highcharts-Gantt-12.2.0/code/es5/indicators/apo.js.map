{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highstock JS v12.2.0 (2025-04-07)\n * @module highcharts/indicators/apo\n * @requires highcharts\n * @requires highcharts/modules/stock\n *\n * Indicator series type for Highcharts Stock\n *\n * (c) 2010-2025 Wojciech Chmiel\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/indicators/apo\", [[\"highcharts/highcharts\"], [\"highcharts/highcharts\",\"SeriesRegistry\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/indicators/apo\"] = factory(require(\"highcharts\"), require(\"highcharts\")[\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ apo_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es5/es-modules/Stock/Indicators/APO/APOIndicator.js\n/* *\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\nvar __extends = (undefined && undefined.__extends) || (function () {\n    var extendStatics = function (d,\n        b) {\n            extendStatics = Object.setPrototypeOf ||\n                ({ __proto__: [] } instanceof Array && function (d,\n        b) { d.__proto__ = b; }) ||\n                function (d,\n        b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\n\nvar EMAIndicator = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes.ema;\n\nvar extend = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).extend, merge = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).merge, error = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).error;\n/* *\n *\n *  Class\n *\n * */\n/**\n * The APO series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.apo\n *\n * @augments Highcharts.Series\n */\nvar APOIndicator = /** @class */ (function (_super) {\n    __extends(APOIndicator, _super);\n    function APOIndicator() {\n        return _super !== null && _super.apply(this, arguments) || this;\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    APOIndicator.prototype.getValues = function (series, params) {\n        var periods = params.periods,\n            index = params.index, \n            // 0- date, 1- Absolute price oscillator\n            APO = [],\n            xData = [],\n            yData = [];\n        var oscillator,\n            i;\n        // Check if periods are correct\n        if (periods.length !== 2 || periods[1] <= periods[0]) {\n            error('Error: \"APO requires two periods. Notice, first period ' +\n                'should be lower than the second one.\"');\n            return;\n        }\n        // Shorter Period EMA\n        var SPE = _super.prototype.getValues.call(this,\n            series, {\n                index: index,\n                period: periods[0]\n            });\n        // Longer Period EMA\n        var LPE = _super.prototype.getValues.call(this,\n            series, {\n                index: index,\n                period: periods[1]\n            });\n        // Check if ema is calculated properly, if not skip\n        if (!SPE || !LPE) {\n            return;\n        }\n        var periodsOffset = periods[1] - periods[0];\n        for (i = 0; i < LPE.yData.length; i++) {\n            oscillator = (SPE.yData[i + periodsOffset] -\n                LPE.yData[i]);\n            APO.push([LPE.xData[i], oscillator]);\n            xData.push(LPE.xData[i]);\n            yData.push(oscillator);\n        }\n        return {\n            values: APO,\n            xData: xData,\n            yData: yData\n        };\n    };\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    /**\n     * Absolute Price Oscillator. This series requires the `linkedTo` option to\n     * be set and should be loaded after the `stock/indicators/indicators.js`.\n     *\n     * @sample {highstock} stock/indicators/apo\n     *         Absolute Price Oscillator\n     *\n     * @extends      plotOptions.ema\n     * @since        7.0.0\n     * @product      highstock\n     * @excluding    allAreas, colorAxis, joinBy, keys, navigatorOptions,\n     *               pointInterval, pointIntervalUnit, pointPlacement,\n     *               pointRange, pointStart, showInNavigator, stacking\n     * @requires     stock/indicators/indicators\n     * @requires     stock/indicators/apo\n     * @optionparent plotOptions.apo\n     */\n    APOIndicator.defaultOptions = merge(EMAIndicator.defaultOptions, {\n        /**\n         * Parameters used in calculation of Absolute Price Oscillator\n         * series points.\n         *\n         * @excluding period\n         */\n        params: {\n            period: void 0, // Unchangeable period, do not inherit (#15362)\n            /**\n             * Periods for Absolute Price Oscillator calculations.\n             *\n             * @type    {Array<number>}\n             * @default [10, 20]\n             * @since   7.0.0\n             */\n            periods: [10, 20]\n        }\n    });\n    return APOIndicator;\n}(EMAIndicator));\nextend(APOIndicator.prototype, {\n    nameBase: 'APO',\n    nameComponents: ['periods']\n});\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('apo', APOIndicator);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var APO_APOIndicator = ((/* unused pure expression or super */ null && (APOIndicator)));\n/* *\n *\n *  API Options\n *\n * */\n/**\n * An `Absolute Price Oscillator` series. If the [type](#series.apo.type) option\n * is not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.apo\n * @since     7.0.0\n * @product   highstock\n * @excluding allAreas, colorAxis, dataParser, dataURL, joinBy, keys,\n *            navigatorOptions, pointInterval, pointIntervalUnit,\n *            pointPlacement, pointRange, pointStart, showInNavigator, stacking\n * @requires  stock/indicators/indicators\n * @requires  stock/indicators/apo\n * @apioption series.apo\n */\n''; // To include the above in the js output\n\n;// ./code/es5/es-modules/masters/indicators/apo.js\n\n\n\n\n/* harmony default export */ var apo_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "extendStatics", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "apo_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "__extends", "b", "setPrototypeOf", "__proto__", "Array", "p", "__", "constructor", "create", "EMAIndicator", "seriesTypes", "ema", "extend", "merge", "error", "APOIndicator", "_super", "apply", "arguments", "getV<PERSON>ues", "series", "params", "oscillator", "i", "periods", "index", "APO", "xData", "yData", "length", "SPE", "period", "LPE", "periodsOffset", "push", "values", "defaultOptions", "nameBase", "nameComponents", "registerSeriesType"], "mappings": "CAYA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,EAChF,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,4BAA6B,CAAC,CAAC,wBAAwB,CAAE,CAAC,wBAAwB,iBAAiB,CAAC,CAAEJ,GACtG,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,4BAA4B,CAAGD,EAAQG,QAAQ,cAAeA,QAAQ,cAAc,cAAiB,EAE7GJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,IAAI,CAAE,SAASO,CAAgC,CAAEC,CAAgC,EACpF,OAAgB,AAAC,WACP,aACA,IAgGFC,EAhGMC,EAAuB,CAE/B,IACC,SAASP,CAAM,EAEtBA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,SAASL,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,SAASb,CAAM,EACtC,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,WAAa,OAAOf,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAASjB,CAAO,CAAEmB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,CAAS,CACxD,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAU7KE,GACI9B,EAAgB,SAAUU,CAAC,CAC3BqB,CAAC,EAMD,MAAO/B,AALHA,CAAAA,EAAgBe,OAAOiB,cAAc,EAChC,CAAA,CAAEC,UAAW,EAAE,AAAC,CAAA,YAAaC,OAAS,SAAUxB,CAAC,CAC1DqB,CAAC,EAAIrB,EAAEuB,SAAS,CAAGF,CAAG,GACd,SAAUrB,CAAC,CACnBqB,CAAC,EAAI,IAAK,IAAII,KAAKJ,EAAOA,EAAET,cAAc,CAACa,IAAIzB,CAAAA,CAAC,CAACyB,EAAE,CAAGJ,CAAC,CAACI,EAAE,AAAD,CAAG,CAAA,EACvCzB,EAAGqB,EAC5B,EACO,SAAUrB,CAAC,CAAEqB,CAAC,EAEjB,SAASK,IAAO,IAAI,CAACC,WAAW,CAAG3B,CAAG,CADtCV,EAAcU,EAAGqB,GAEjBrB,EAAEW,SAAS,CAAGU,AAAM,OAANA,EAAahB,OAAOuB,MAAM,CAACP,GAAMK,CAAAA,EAAGf,SAAS,CAAGU,EAAEV,SAAS,CAAE,IAAIe,CAAG,CACtF,GAGAG,EAAe,AAACV,IAA2IW,WAAW,CAACC,GAAG,CAE1KC,EAAS,AAACf,IAA+Ee,MAAM,CAAEC,EAAQ,AAAChB,IAA+EgB,KAAK,CAAEC,EAAQ,AAACjB,IAA+EiB,KAAK,CAe7RC,EAA8B,SAAUC,CAAM,EAE9C,SAASD,IACL,OAAOC,AAAW,OAAXA,GAAmBA,EAAOC,KAAK,CAAC,IAAI,CAAEC,YAAc,IAAI,AACnE,CA4FA,OA/FAlB,EAAUe,EAAcC,GASxBD,EAAaxB,SAAS,CAAC4B,SAAS,CAAG,SAAUC,CAAM,CAAEC,CAAM,EACvD,IAMIC,EACAC,EAPAC,EAAUH,EAAOG,OAAO,CACxBC,EAAQJ,EAAOI,KAAK,CAEpBC,EAAM,EAAE,CACRC,EAAQ,EAAE,CACVC,EAAQ,EAAE,CAId,GAAIJ,AAAmB,IAAnBA,EAAQK,MAAM,EAAUL,CAAO,CAAC,EAAE,EAAIA,CAAO,CAAC,EAAE,CAAE,CAClDV,EAAM,gGAEN,MACJ,CAEA,IAAIgB,EAAMd,EAAOzB,SAAS,CAAC4B,SAAS,CAAC1B,IAAI,CAAC,IAAI,CAC1C2B,EAAQ,CACJK,MAAOA,EACPM,OAAQP,CAAO,CAAC,EAAE,AACtB,GAEAQ,EAAMhB,EAAOzB,SAAS,CAAC4B,SAAS,CAAC1B,IAAI,CAAC,IAAI,CAC1C2B,EAAQ,CACJK,MAAOA,EACPM,OAAQP,CAAO,CAAC,EAAE,AACtB,GAEJ,GAAI,AAACM,GAAQE,GAGb,IAAIC,EAAgBT,CAAO,CAAC,EAAE,CAAGA,CAAO,CAAC,EAAE,CAC3C,IAAKD,EAAI,EAAGA,EAAIS,EAAIJ,KAAK,CAACC,MAAM,CAAEN,IAC9BD,EAAcQ,EAAIF,KAAK,CAACL,EAAIU,EAAc,CACtCD,EAAIJ,KAAK,CAACL,EAAE,CAChBG,EAAIQ,IAAI,CAAC,CAACF,EAAIL,KAAK,CAACJ,EAAE,CAAED,EAAW,EACnCK,EAAMO,IAAI,CAACF,EAAIL,KAAK,CAACJ,EAAE,EACvBK,EAAMM,IAAI,CAACZ,GAEf,MAAO,CACHa,OAAQT,EACRC,MAAOA,EACPC,MAAOA,CACX,EACJ,EAuBAb,EAAaqB,cAAc,CAAGvB,EAAMJ,EAAa2B,cAAc,CAAE,CAO7Df,OAAQ,CACJU,OAAQ,KAAK,EAQbP,QAAS,CAAC,GAAI,GAAG,AACrB,CACJ,GACOT,CACX,EAAEN,GACFG,EAAOG,EAAaxB,SAAS,CAAE,CAC3B8C,SAAU,MACVC,eAAgB,CAAC,UAAU,AAC/B,GACAvC,IAA0IwC,kBAAkB,CAAC,MAAOxB,GAiCvI,IAAIpB,EAAYE,IAGnC,OADYH,EAAoB,OAAU,AAE3C,GAET"}