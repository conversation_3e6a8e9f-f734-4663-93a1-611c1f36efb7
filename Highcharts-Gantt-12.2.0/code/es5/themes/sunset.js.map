{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/themes/sunset\n * @requires highcharts\n *\n * (c) 2009-2025 Highsoft AS\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(require(\"highcharts\"));\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/themes/sunset\", [[\"highcharts/highcharts\"]], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/themes/sunset\"] = factory(require(\"highcharts\"));\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"]);\n})(this, function(__WEBPACK_EXTERNAL_MODULE__944__) {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 944:\n/***/ (function(module) {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ sunset_src; }\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es5/es-modules/Extensions/Themes/Sunset.js\n/* *\n *\n *  (c) 2010-2025 Highsoft AS\n *\n *  Author: Øystein Moseng\n *\n *  License: www.highcharts.com/license\n *\n *  Accessible high-contrast theme for Highcharts. Considers colorblindness and\n *  monochrome rendering.\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nvar setOptions = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).setOptions;\n/* *\n *\n *  Theme\n *\n * */\nvar SunsetTheme;\n(function (SunsetTheme) {\n    /* *\n     *\n     *  Constants\n     *\n     * */\n    SunsetTheme.options = {\n        colors: ['#FDD089', '#FF7F79', '#A0446E', '#251535'],\n        colorAxis: {\n            maxColor: '#60042E',\n            minColor: '#FDD089'\n        },\n        plotOptions: {\n            map: {\n                nullColor: '#fefefc'\n            }\n        },\n        navigator: {\n            series: {\n                color: '#FF7F79',\n                lineColor: '#A0446E'\n            }\n        }\n    };\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Apply the theme.\n     */\n    function apply() {\n        setOptions(SunsetTheme.options);\n    }\n    SunsetTheme.apply = apply;\n})(SunsetTheme || (SunsetTheme = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ var Sunset = (SunsetTheme);\n\n;// ./code/es5/es-modules/masters/themes/sunset.js\n\n\n\n\n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()).theme = Sunset.options;\nSunset.apply();\n/* harmony default export */ var sunset_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "require", "define", "amd", "__WEBPACK_EXTERNAL_MODULE__944__", "SunsetTheme", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "sunset_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "setOptions", "options", "colors", "colorAxis", "maxColor", "minColor", "plotOptions", "map", "nullColor", "navigator", "series", "color", "lineColor", "apply", "Sunset", "theme"], "mappings": "CASA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQG,QAAQ,eAC1B,AAAkB,YAAlB,OAAOC,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,2BAA4B,CAAC,CAAC,wBAAwB,CAAC,CAAEJ,GACzD,AAAmB,UAAnB,OAAOC,QACdA,OAAO,CAAC,2BAA2B,CAAGD,EAAQG,QAAQ,eAEtDJ,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CACjD,EAAG,IAAI,CAAE,SAASO,CAAgC,EAClD,OAAgB,AAAC,WACP,aACA,IAoGCC,EADPA,EAnGUC,EAAuB,CAE/B,IACC,SAASN,CAAM,EAEtBA,EAAOD,OAAO,CAAGK,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaX,OAAO,CAG5B,IAAIC,EAASO,CAAwB,CAACE,EAAS,CAAG,CAGjDV,QAAS,CAAC,CACX,EAMA,OAHAO,CAAmB,CAACG,EAAS,CAACT,EAAQA,EAAOD,OAAO,CAAES,GAG/CR,EAAOD,OAAO,AACtB,CAMCS,EAAoBI,CAAC,CAAG,SAASZ,CAAM,EACtC,IAAIa,EAASb,GAAUA,EAAOc,UAAU,CACvC,WAAa,OAAOd,EAAO,OAAU,AAAE,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAQ,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,SAAShB,CAAO,CAAEkB,CAAU,EACnD,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACpB,EAASmB,IAC5EE,OAAOC,cAAc,CAACtB,EAASmB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,SAASK,CAAG,CAAEC,CAAI,EAAI,OAAOL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,EAAO,EAIjH,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,WAAa,OAAqBC,CAAY,CAC3D,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAkBjHE,EAAa,AAACD,IAA+EC,UAAU,AAavG5B,EANOA,EAoCRA,GAAgBA,CAAAA,EAAc,CAAC,CAAA,GA9BlB6B,OAAO,CAAG,CAClBC,OAAQ,CAAC,UAAW,UAAW,UAAW,UAAU,CACpDC,UAAW,CACPC,SAAU,UACVC,SAAU,SACd,EACAC,YAAa,CACTC,IAAK,CACDC,UAAW,SACf,CACJ,EACAC,UAAW,CACPC,OAAQ,CACJC,MAAO,UACPC,UAAW,SACf,CACJ,CACJ,EAYAxC,EAAYyC,KAAK,CAHjB,WACIb,EAAW5B,EAAY6B,OAAO,CAClC,EAQyB,IAAIa,EAAU1C,CAO3C,CAAC2B,IAA+EgB,KAAK,CAAGD,EAAOb,OAAO,CACtGa,EAAOD,KAAK,GACiB,IAAIhB,EAAeE,IAGtC,OADYH,EAAoB,OAAU,AAE3C,GAET"}