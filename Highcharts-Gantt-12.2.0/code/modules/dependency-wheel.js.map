{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/dependency-wheel\n * @requires highcharts\n * @requires highcharts/modules/sankey\n *\n * Dependency wheel module\n *\n * (c) 2010-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"SVGElement\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/dependency-wheel\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"],amd1[\"SVGElement\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/dependency-wheel\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"], root[\"_Highcharts\"][\"SVGElement\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"], root[\"Highcharts\"][\"SVGElement\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__, __WEBPACK_EXTERNAL_MODULE__28__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 28:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__28__;\n\n/***/ }),\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ dependency_wheel_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/DependencyWheel/DependencyWheelPoint.js\n/* *\n *\n *  Dependency wheel module\n *\n *  (c) 2018-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { sankey: { prototype: { pointClass: SankeyPoint } } } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { pInt, wrap } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\nclass DependencyWheelPoint extends SankeyPoint {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Return a text path that the data label uses.\n     * @private\n     */\n    getDataLabelPath(label) {\n        const point = this, renderer = point.series.chart.renderer, shapeArgs = point.shapeArgs, upperHalf = point.angle < 0 || point.angle > Math.PI, start = shapeArgs.start || 0, end = shapeArgs.end || 0;\n        // First time\n        if (!point.dataLabelPath) {\n            // Destroy the path with the label\n            wrap(label, 'destroy', function (proceed) {\n                if (point.dataLabelPath) {\n                    point.dataLabelPath = point.dataLabelPath.destroy();\n                }\n                return proceed.call(this);\n            });\n            // Subsequent times\n        }\n        else {\n            point.dataLabelPath = point.dataLabelPath.destroy();\n            delete point.dataLabelPath;\n        }\n        // All times\n        point.dataLabelPath = renderer\n            .arc({\n            open: true,\n            longArc: Math.abs(Math.abs(start) - Math.abs(end)) < Math.PI ? 0 : 1\n        })\n            .attr({\n            x: shapeArgs.x,\n            y: shapeArgs.y,\n            r: ((shapeArgs.r || 0) + pInt(label.options?.distance || 0)),\n            start: (upperHalf ? start : end),\n            end: (upperHalf ? end : start),\n            clockwise: +upperHalf\n        })\n            .add(renderer.defs);\n        return point.dataLabelPath;\n    }\n    isValid() {\n        // No null points here\n        return true;\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const DependencyWheel_DependencyWheelPoint = (DependencyWheelPoint);\n\n;// ./code/es-modules/Series/DependencyWheel/DependencyWheelSeriesDefaults.js\n/* *\n *\n *  Dependency wheel module\n *\n *  (c) 2018-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A dependency wheel chart is a type of flow diagram, where all nodes are laid\n * out in a circle, and the flow between the are drawn as link bands.\n *\n * @sample highcharts/demo/dependency-wheel/\n *         Dependency wheel\n *\n * @extends      plotOptions.sankey\n * @exclude      dataSorting, nodeAlignment, nodeDistance\n * @since        7.1.0\n * @product      highcharts\n * @requires     modules/dependency-wheel\n * @optionparent plotOptions.dependencywheel\n */\nconst DependencyWheelSeriesDefaults = {\n    /**\n     * The corner radius of the border surrounding each node. A number\n     * signifies pixels. A percentage string, like for example `50%`, signifies\n     * a relative size. For nodes this is relative to the node width.\n     *\n     * @type    {number|string|Highcharts.BorderRadiusOptionsObject}\n     * @default 3\n     * @product highcharts\n     * @since   11.0.0\n     * @apioption plotOptions.dependencywheel.borderRadius\n    */\n    /**\n     * Distance between the data label and the center of the node.\n     *\n     * @type      {number}\n     * @default   0\n     * @apioption plotOptions.dependencywheel.dataLabels.distance\n     */\n    /**\n     * A format string for data labels of the links between nodes. Available\n     * variables are the same as for `formatter`.\n     *\n     * @see [nodeFormat](#nodeFormat) for formatting node labels\n     *\n     * @apioption plotOptions.dependencywheel.dataLabels.format\n     */\n    /**\n     * Callback to format data labels of the links between nodes. The `format`\n     * option takes precedence over the `formatter` option.\n     *\n     * @see [nodeFormatter](#nodeFormatter) for formatting node labels\n     *\n     * @apioption plotOptions.dependencywheel.dataLabels.formatter\n     */\n    /**\n     * The format string specifying what to show for nodes in the sankey\n     * diagram. By default the nodeFormatter returns `{point.name}`. Available\n     * variables are the same as for `nodeFormatter`.\n     *\n     * @apioption plotOptions.dependencywheel.dataLabels.nodeFormat\n     */\n    /**\n     * Callback to format data labels of nodes in the dependency wheel. The\n     * `nodeFormat` option takes precedence over the `nodeFormatter` option.\n     *\n     * @apioption plotOptions.dependencywheel.dataLabels.nodeFormatter\n     */\n    /**\n     * Size of the wheel in pixel or percent relative to the canvas space.\n     *\n     * @type      {number|string}\n     * @default   100%\n     * @apioption plotOptions.dependencywheel.size\n     */\n    /**\n     * The center of the wheel relative to the plot area. Can be\n     * percentages or pixel values. The default behaviour is to\n     * center the wheel inside the plot area.\n     *\n     * @type    {Array<number|string|null>}\n     * @default [null, null]\n     * @product highcharts\n     */\n    center: [null, null],\n    curveFactor: 0.6,\n    /**\n     * The start angle of the dependency wheel, in degrees where 0 is up.\n     */\n    startAngle: 0,\n    dataLabels: {\n        textPath: {\n            /**\n             * Enable or disable `textPath` option for link's or marker's data\n             * labels.\n             *\n             * @type      {boolean}\n             * @default   false\n             * @since     7.1.0\n             * @apioption plotOptions.series.dataLabels.textPath.enabled\n             */\n            enabled: false,\n            attributes: {\n                /**\n                * Text path shift along its y-axis.\n                *\n                * @type      {Highcharts.SVGAttributes}\n                * @default   5\n                * @since     7.1.0\n                * @apioption plotOptions.dependencywheel.dataLabels.textPath.attributes.dy\n                */\n                dy: 5\n            }\n        }\n    }\n};\n/**\n * A `dependencywheel` series. If the [type](#series.dependencywheel.type)\n * option is not specified, it is inherited from [chart.type](#chart.type).\n *\n * @extends   series,plotOptions.dependencywheel\n * @exclude   dataSorting\n * @product   highcharts\n * @requires  modules/sankey\n * @requires  modules/dependency-wheel\n * @apioption series.dependencywheel\n */\n/**\n * A collection of options for the individual nodes. The nodes in a dependency\n * diagram are auto-generated instances of `Highcharts.Point`, but options can\n * be applied here and linked by the `id`.\n *\n * @extends   series.sankey.nodes\n * @type      {Array<*>}\n * @product   highcharts\n * @excluding offset\n * @apioption series.dependencywheel.nodes\n */\n/**\n * An array of data points for the series. For the `dependencywheel` series\n * type, points can be given in the following way:\n *\n * An array of objects with named values. The following snippet shows only a\n * few settings, see the complete options set below. If the total number of data\n * points exceeds the series' [turboThreshold](#series.area.turboThreshold),\n * this option is not available.\n *\n *  ```js\n *     data: [{\n *         from: 'Category1',\n *         to: 'Category2',\n *         weight: 2\n *     }, {\n *         from: 'Category1',\n *         to: 'Category3',\n *         weight: 5\n *     }]\n *  ```\n *\n * @type      {Array<Array<string,string,number>|*>}\n * @extends   series.sankey.data\n * @product   highcharts\n * @excluding outgoing, dataLabels\n * @apioption series.dependencywheel.data\n */\n/**\n * Individual data label for each node. The options are the same as\n * the ones for [series.dependencywheel.dataLabels](#series.dependencywheel.dataLabels).\n *\n * @apioption series.dependencywheel.nodes.dataLabels\n */\n''; // Keeps doclets above separate\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const DependencyWheel_DependencyWheelSeriesDefaults = (DependencyWheelSeriesDefaults);\n\n;// ./code/es-modules/Series/Sankey/SankeyColumnComposition.js\n/* *\n *\n *  Sankey diagram module\n *\n *  (c) 2010-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { defined, getAlignFactor, relativeLength } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Composition\n *\n * */\nvar SankeyColumnComposition;\n(function (SankeyColumnComposition) {\n    /* *\n     *\n     *  Declarations\n     *\n     * */\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * SankeyColumn Composition\n     * @private\n     * @function Highcharts.SankeyColumn#compose\n     *\n     * @param {Array<SankeyPoint>} points\n     * The array of nodes\n     * @param {SankeySeries} series\n     * Series connected to column\n     * @return {ArrayComposition} SankeyColumnArray\n     */\n    function compose(points, series) {\n        const sankeyColumnArray = points;\n        sankeyColumnArray.sankeyColumn =\n            new SankeyColumnAdditions(sankeyColumnArray, series);\n        return sankeyColumnArray;\n    }\n    SankeyColumnComposition.compose = compose;\n    /* *\n     *\n     *  Classes\n     *\n     * */\n    class SankeyColumnAdditions {\n        /* *\n         *\n         *  Constructor\n         *\n         * */\n        constructor(points, series) {\n            this.points = points;\n            this.series = series;\n        }\n        /* *\n         *\n         *  Functions\n         *\n         * */\n        /**\n         * Calculate translation factor used in column and nodes distribution\n         * @private\n         * @function Highcharts.SankeyColumn#getTranslationFactor\n         *\n         * @param {SankeySeries} series\n         * The Series\n         * @return {number} TranslationFactor\n         * Translation Factor\n         */\n        getTranslationFactor(series) {\n            const column = this.points, nodes = column.slice(), chart = series.chart, minLinkWidth = series.options.minLinkWidth || 0;\n            let skipPoint, factor = 0, i, remainingHeight = ((chart.plotSizeY || 0) -\n                (series.options.borderWidth || 0) -\n                (column.length - 1) * series.nodePadding);\n            // Because the minLinkWidth option doesn't obey the direct\n            // translation, we need to run translation iteratively, check\n            // node heights, remove those nodes affected by minLinkWidth,\n            // check again, etc.\n            while (column.length) {\n                factor = remainingHeight / column.sankeyColumn.sum();\n                skipPoint = false;\n                i = column.length;\n                while (i--) {\n                    if (column[i].getSum() * factor < minLinkWidth) {\n                        column.splice(i, 1);\n                        remainingHeight =\n                            Math.max(0, remainingHeight - minLinkWidth);\n                        skipPoint = true;\n                    }\n                }\n                if (!skipPoint) {\n                    break;\n                }\n            }\n            // Re-insert original nodes\n            column.length = 0;\n            for (const node of nodes) {\n                column.push(node);\n            }\n            return factor;\n        }\n        /**\n         * Get the top position of the column in pixels\n         * @private\n         * @function Highcharts.SankeyColumn#top\n         *\n         * @param {number} factor\n         * The Translation Factor\n         * @return {number} top\n         * The top position of the column\n         */\n        top(factor) {\n            const series = this.series, nodePadding = series.nodePadding, height = this.points.reduce((height, node) => {\n                if (height > 0) {\n                    height += nodePadding;\n                }\n                const nodeHeight = Math.max(node.getSum() * factor, series.options.minLinkWidth || 0);\n                height += nodeHeight;\n                return height;\n            }, 0);\n            // Node alignment option handling #19096\n            return getAlignFactor(series.options.nodeAlignment || 'center') * ((series.chart.plotSizeY || 0) - height);\n        }\n        /**\n         * Get the left position of the column in pixels\n         * @private\n         * @function Highcharts.SankeyColumn#top\n         *\n         * @param {number} factor\n         * The Translation Factor\n         * @return {number} left\n         * The left position of the column\n         */\n        left(factor) {\n            const series = this.series, chart = series.chart, equalNodes = series.options.equalNodes, maxNodesLength = (chart.inverted ? chart.plotHeight : chart.plotWidth), nodePadding = series.nodePadding, width = this.points.reduce((width, node) => {\n                if (width > 0) {\n                    width += nodePadding;\n                }\n                const nodeWidth = equalNodes ?\n                    maxNodesLength / node.series.nodes.length -\n                        nodePadding :\n                    Math.max(node.getSum() * factor, series.options.minLinkWidth || 0);\n                width += nodeWidth;\n                return width;\n            }, 0);\n            return ((chart.plotSizeX || 0) - Math.round(width)) / 2;\n        }\n        /**\n         * Calculate sum of all nodes inside specific column\n         * @private\n         * @function Highcharts.SankeyColumn#sum\n         *\n         * @param {ArrayComposition} this\n         * Sankey Column Array\n         *\n         * @return {number} sum\n         * Sum of all nodes inside column\n         */\n        sum() {\n            return this.points.reduce((sum, node) => (sum + node.getSum()), 0);\n        }\n        /**\n         * Get the offset in pixels of a node inside the column\n         * @private\n         * @function Highcharts.SankeyColumn#offset\n         *\n         * @param {SankeyPoint} node\n         * Sankey node\n         * @param {number} factor\n         * Translation Factor\n         * @return {number} offset\n         * Offset of a node inside column\n         */\n        offset(node, factor) {\n            const column = this.points, series = this.series, nodePadding = series.nodePadding;\n            let offset = 0, totalNodeOffset;\n            if (series.is('organization') && node.hangsFrom) {\n                return {\n                    absoluteTop: node.hangsFrom.nodeY\n                };\n            }\n            for (let i = 0; i < column.length; i++) {\n                const sum = column[i].getSum();\n                const height = Math.max(sum * factor, series.options.minLinkWidth || 0);\n                const directionOffset = node.options[series.chart.inverted ?\n                    'offsetHorizontal' :\n                    'offsetVertical'], optionOffset = node.options.offset || 0;\n                if (sum) {\n                    totalNodeOffset = height + nodePadding;\n                }\n                else {\n                    // If node sum equals 0 nodePadding is missed #12453\n                    totalNodeOffset = 0;\n                }\n                if (column[i] === node) {\n                    return {\n                        relativeTop: offset + (defined(directionOffset) ?\n                            // `directionOffset` is a percent of the node\n                            // height\n                            relativeLength(directionOffset, height) :\n                            relativeLength(optionOffset, totalNodeOffset))\n                    };\n                }\n                offset += totalNodeOffset;\n            }\n        }\n    }\n    SankeyColumnComposition.SankeyColumnAdditions = SankeyColumnAdditions;\n})(SankeyColumnComposition || (SankeyColumnComposition = {}));\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Sankey_SankeyColumnComposition = (SankeyColumnComposition);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SVGElement\"],\"commonjs\":[\"highcharts\",\"SVGElement\"],\"commonjs2\":[\"highcharts\",\"SVGElement\"],\"root\":[\"Highcharts\",\"SVGElement\"]}\nvar highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_ = __webpack_require__(28);\nvar highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default = /*#__PURE__*/__webpack_require__.n(highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_);\n;// ./code/es-modules/Extensions/TextPath.js\n/* *\n *\n *  Highcharts module with textPath functionality.\n *\n *  (c) 2009-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { deg2rad } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\nconst { addEvent, merge, uniqueKey, defined: TextPath_defined, extend } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/**\n * Set a text path for a `text` or `label` element, allowing the text to\n * flow along a path.\n *\n * In order to unset the path for an existing element, call `setTextPath`\n * with `{ enabled: false }` as the second argument.\n *\n * Text path support is not bundled into `highcharts.js`, and requires the\n * `modules/textpath.js` file. However, it is included in the script files of\n * those series types that use it by default\n *\n * @sample highcharts/members/renderer-textpath/ Text path demonstrated\n *\n * @function Highcharts.SVGElement#setTextPath\n *\n * @param {Highcharts.SVGElement|undefined} path\n *        Path to follow. If undefined, it allows changing options for the\n *        existing path.\n *\n * @param {Highcharts.DataLabelsTextPathOptionsObject} textPathOptions\n *        Options.\n *\n * @return {Highcharts.SVGElement} Returns the SVGElement for chaining.\n */\nfunction setTextPath(path, textPathOptions) {\n    // Defaults\n    textPathOptions = merge(true, {\n        enabled: true,\n        attributes: {\n            dy: -5,\n            startOffset: '50%',\n            textAnchor: 'middle'\n        }\n    }, textPathOptions);\n    const url = this.renderer.url, textWrapper = this.text || this, textPath = textWrapper.textPath, { attributes, enabled } = textPathOptions;\n    path = path || (textPath && textPath.path);\n    // Remove previously added event\n    if (textPath) {\n        textPath.undo();\n    }\n    if (path && enabled) {\n        const undo = addEvent(textWrapper, 'afterModifyTree', (e) => {\n            if (path && enabled) {\n                // Set ID for the path\n                let textPathId = path.attr('id');\n                if (!textPathId) {\n                    path.attr('id', textPathId = uniqueKey());\n                }\n                // Set attributes for the <text>\n                const textAttribs = {\n                    // `dx`/`dy` options must by set on <text> (parent), the\n                    // rest should be set on <textPath>\n                    x: 0,\n                    y: 0\n                };\n                if (TextPath_defined(attributes.dx)) {\n                    textAttribs.dx = attributes.dx;\n                    delete attributes.dx;\n                }\n                if (TextPath_defined(attributes.dy)) {\n                    textAttribs.dy = attributes.dy;\n                    delete attributes.dy;\n                }\n                textWrapper.attr(textAttribs);\n                // Handle label properties\n                this.attr({ transform: '' });\n                if (this.box) {\n                    this.box = this.box.destroy();\n                }\n                // Wrap the nodes in a textPath\n                const children = e.nodes.slice(0);\n                e.nodes.length = 0;\n                e.nodes[0] = {\n                    tagName: 'textPath',\n                    attributes: extend(attributes, {\n                        'text-anchor': attributes.textAnchor,\n                        href: `${url}#${textPathId}`\n                    }),\n                    children\n                };\n            }\n        });\n        // Set the reference\n        textWrapper.textPath = { path, undo };\n    }\n    else {\n        textWrapper.attr({ dx: 0, dy: 0 });\n        delete textWrapper.textPath;\n    }\n    if (this.added) {\n        // Rebuild text after added\n        textWrapper.textCache = '';\n        this.renderer.buildText(textWrapper);\n    }\n    return this;\n}\n/**\n * Attach a polygon to a bounding box if the element contains a textPath.\n *\n * @function Highcharts.SVGElement#setPolygon\n *\n * @param {any} event\n *        An event containing a bounding box object\n *\n * @return {Highcharts.BBoxObject} Returns the bounding box object.\n */\nfunction setPolygon(event) {\n    const bBox = event.bBox, tp = this.element?.querySelector('textPath');\n    if (tp) {\n        const polygon = [], { b, h } = this.renderer.fontMetrics(this.element), descender = h - b, lineCleanerRegex = new RegExp('(<tspan>|' +\n            '<tspan(?!\\\\sclass=\"highcharts-br\")[^>]*>|' +\n            '<\\\\/tspan>)', 'g'), lines = tp\n            .innerHTML\n            .replace(lineCleanerRegex, '')\n            .split(/<tspan class=\"highcharts-br\"[^>]*>/), numOfLines = lines.length;\n        // Calculate top and bottom coordinates for\n        // either the start or the end of a single\n        // character, and append it to the polygon.\n        const appendTopAndBottom = (charIndex, positionOfChar) => {\n            const { x, y } = positionOfChar, rotation = (tp.getRotationOfChar(charIndex) - 90) * deg2rad, cosRot = Math.cos(rotation), sinRot = Math.sin(rotation);\n            return [\n                [\n                    x - descender * cosRot,\n                    y - descender * sinRot\n                ],\n                [\n                    x + b * cosRot,\n                    y + b * sinRot\n                ]\n            ];\n        };\n        for (let i = 0, lineIndex = 0; lineIndex < numOfLines; lineIndex++) {\n            const line = lines[lineIndex], lineLen = line.length;\n            for (let lineCharIndex = 0; lineCharIndex < lineLen; lineCharIndex += 5) {\n                try {\n                    const srcCharIndex = (i +\n                        lineCharIndex +\n                        lineIndex), [lower, upper] = appendTopAndBottom(srcCharIndex, tp.getStartPositionOfChar(srcCharIndex));\n                    if (lineCharIndex === 0) {\n                        polygon.push(upper);\n                        polygon.push(lower);\n                    }\n                    else {\n                        if (lineIndex === 0) {\n                            polygon.unshift(upper);\n                        }\n                        if (lineIndex === numOfLines - 1) {\n                            polygon.push(lower);\n                        }\n                    }\n                }\n                catch (e) {\n                    // Safari fails on getStartPositionOfChar even if the\n                    // character is within the `textContent.length`\n                    break;\n                }\n            }\n            i += lineLen - 1;\n            try {\n                const srcCharIndex = i + lineIndex, charPos = tp.getEndPositionOfChar(srcCharIndex), [lower, upper] = appendTopAndBottom(srcCharIndex, charPos);\n                polygon.unshift(upper);\n                polygon.unshift(lower);\n            }\n            catch (e) {\n                // Safari fails on getStartPositionOfChar even if the character\n                // is within the `textContent.length`\n                break;\n            }\n        }\n        // Close it\n        if (polygon.length) {\n            polygon.push(polygon[0].slice());\n        }\n        bBox.polygon = polygon;\n    }\n    return bBox;\n}\n/**\n * Draw text along a textPath for a dataLabel.\n *\n * @function Highcharts.SVGElement#setTextPath\n *\n * @param {any} event\n *        An event containing label options\n *\n * @return {void}\n */\nfunction drawTextPath(event) {\n    const labelOptions = event.labelOptions, point = event.point, textPathOptions = (labelOptions[point.formatPrefix + 'TextPath'] ||\n        labelOptions.textPath);\n    if (textPathOptions && !labelOptions.useHTML) {\n        this.setTextPath(point.getDataLabelPath?.(this) || point.graphic, textPathOptions);\n        if (point.dataLabelPath &&\n            !textPathOptions.enabled) {\n            // Clean the DOM\n            point.dataLabelPath = (point.dataLabelPath.destroy());\n        }\n    }\n}\nfunction compose(SVGElementClass) {\n    addEvent(SVGElementClass, 'afterGetBBox', setPolygon);\n    addEvent(SVGElementClass, 'beforeAddingDataLabel', drawTextPath);\n    const svgElementProto = SVGElementClass.prototype;\n    if (!svgElementProto.setTextPath) {\n        svgElementProto.setTextPath = setTextPath;\n    }\n}\nconst TextPath = {\n    compose\n};\n/* harmony default export */ const Extensions_TextPath = (TextPath);\n\n;// ./code/es-modules/Series/DependencyWheel/DependencyWheelSeries.js\n/* *\n *\n *  Dependency wheel module\n *\n *  (c) 2018-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { animObject } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\n\nconst { deg2rad: DependencyWheelSeries_deg2rad } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nconst { pie: PieSeries, sankey: SankeySeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { extend: DependencyWheelSeries_extend, merge: DependencyWheelSeries_merge, relativeLength: DependencyWheelSeries_relativeLength } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\n\nExtensions_TextPath.compose((highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default()));\n/* *\n *\n *  Class\n *\n * */\n/**\n * @private\n * @class\n * @name Highcharts.seriesTypes.dependencywheel\n *\n * @augments Highcharts.seriesTypes.sankey\n */\nclass DependencyWheelSeries extends SankeySeries {\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    animate(init) {\n        const series = this;\n        if (!init) {\n            const duration = animObject(series.options.animation).duration, step = (duration / 2) / series.nodes.length;\n            let i = 0;\n            for (const point of series.nodes) {\n                const graphic = point.graphic;\n                if (graphic) {\n                    graphic.attr({ opacity: 0 });\n                    setTimeout(() => {\n                        if (point.graphic) {\n                            point.graphic.animate({ opacity: 1 }, { duration: step });\n                        }\n                    }, step * i++);\n                }\n            }\n            for (const point of series.points) {\n                const graphic = point.graphic;\n                if (!point.isNode && graphic) {\n                    graphic.attr({ opacity: 0 })\n                        .animate({\n                        opacity: 1\n                    }, series.options.animation);\n                }\n            }\n        }\n    }\n    createNode(id) {\n        const node = super.createNode(id);\n        /**\n         * Return the sum of incoming and outgoing links.\n         * @private\n         */\n        node.getSum = () => (node.linksFrom\n            .concat(node.linksTo)\n            .reduce((acc, link) => (acc + link.weight), 0));\n        /**\n         * Get the offset in weight values of a point/link.\n         * @private\n         */\n        node.offset = (point) => {\n            const otherNode = (link) => (link.fromNode === node ?\n                link.toNode :\n                link.fromNode);\n            let offset = 0, links = node.linksFrom.concat(node.linksTo), sliced;\n            // Sort and slice the links to avoid links going out of each\n            // node crossing each other.\n            links.sort((a, b) => (otherNode(a).index - otherNode(b).index));\n            for (let i = 0; i < links.length; i++) {\n                if (otherNode(links[i]).index > node.index) {\n                    links = links.slice(0, i).reverse().concat(links.slice(i).reverse());\n                    sliced = true;\n                    break;\n                }\n            }\n            if (!sliced) {\n                links.reverse();\n            }\n            for (let i = 0; i < links.length; i++) {\n                if (links[i] === point) {\n                    return offset;\n                }\n                offset += links[i].weight;\n            }\n        };\n        return node;\n    }\n    /**\n     * Dependency wheel has only one column, it runs along the perimeter.\n     * @private\n     */\n    createNodeColumns() {\n        const series = this, columns = [Sankey_SankeyColumnComposition.compose([], series)];\n        for (const node of series.nodes) {\n            node.column = 0;\n            columns[0].push(node);\n        }\n        return columns;\n    }\n    /**\n     * Translate from vertical pixels to perimeter.\n     * @private\n     */\n    getNodePadding() {\n        return this.options.nodePadding / Math.PI;\n    }\n    /**\n     * @ignore\n     * @todo Override the refactored sankey translateLink and translateNode\n     * functions instead of the whole translate function.\n     */\n    translate() {\n        const series = this, options = series.options, factor = 2 * Math.PI /\n            (series.chart.plotHeight + series.getNodePadding()), center = series.getCenter(), startAngle = (options.startAngle - 90) * DependencyWheelSeries_deg2rad, brOption = options.borderRadius, borderRadius = typeof brOption === 'object' ?\n            brOption.radius : brOption;\n        super.translate();\n        for (const node of this.nodeColumns[0]) {\n            // Don't render the nodes if sum is 0 #12453\n            if (node.sum) {\n                const shapeArgs = node.shapeArgs, centerX = center[0], centerY = center[1], r = center[2] / 2, nodeWidth = options.nodeWidth === 'auto' ?\n                    20 : options.nodeWidth, innerR = r - DependencyWheelSeries_relativeLength(nodeWidth || 0, r), start = startAngle + factor * (shapeArgs.y || 0), end = startAngle +\n                    factor * ((shapeArgs.y || 0) + (shapeArgs.height || 0));\n                // Middle angle\n                node.angle = start + (end - start) / 2;\n                node.shapeType = 'arc';\n                node.shapeArgs = {\n                    x: centerX,\n                    y: centerY,\n                    r: r,\n                    innerR: innerR,\n                    start: start,\n                    end: end,\n                    borderRadius\n                };\n                node.dlBox = {\n                    x: centerX + Math.cos((start + end) / 2) * (r + innerR) / 2,\n                    y: centerY + Math.sin((start + end) / 2) * (r + innerR) / 2,\n                    width: 1,\n                    height: 1\n                };\n                // Draw the links from this node\n                for (const point of node.linksFrom) {\n                    if (point.linkBase) {\n                        let curveFactor, distance;\n                        const corners = point.linkBase.map((top, i) => {\n                            const angle = factor * top, x = Math.cos(startAngle + angle) * (innerR + 1), y = Math.sin(startAngle + angle) * (innerR + 1);\n                            curveFactor = options.curveFactor || 0;\n                            // The distance between the from and to node\n                            // along the perimeter. This affect how curved\n                            // the link is, so that links between neighbours\n                            // don't extend too far towards the center.\n                            distance = Math.abs(point.linkBase[3 - i] * factor - angle);\n                            if (distance > Math.PI) {\n                                distance = 2 * Math.PI - distance;\n                            }\n                            distance = distance * innerR;\n                            if (distance < innerR) {\n                                curveFactor *= (distance / innerR);\n                            }\n                            return {\n                                x: centerX + x,\n                                y: centerY + y,\n                                cpX: centerX + (1 - curveFactor) * x,\n                                cpY: centerY + (1 - curveFactor) * y\n                            };\n                        });\n                        point.shapeArgs = {\n                            d: [[\n                                    'M',\n                                    corners[0].x, corners[0].y\n                                ], [\n                                    'A',\n                                    innerR, innerR,\n                                    0,\n                                    0, // Long arc\n                                    1, // Clockwise\n                                    corners[1].x, corners[1].y\n                                ], [\n                                    'C',\n                                    corners[1].cpX, corners[1].cpY,\n                                    corners[2].cpX, corners[2].cpY,\n                                    corners[2].x, corners[2].y\n                                ], [\n                                    'A',\n                                    innerR, innerR,\n                                    0,\n                                    0,\n                                    1,\n                                    corners[3].x, corners[3].y\n                                ], [\n                                    'C',\n                                    corners[3].cpX, corners[3].cpY,\n                                    corners[0].cpX, corners[0].cpY,\n                                    corners[0].x, corners[0].y\n                                ]]\n                        };\n                    }\n                }\n            }\n        }\n    }\n}\n/* *\n *\n *  Static Properties\n *\n * */\nDependencyWheelSeries.defaultOptions = DependencyWheelSeries_merge(SankeySeries.defaultOptions, DependencyWheel_DependencyWheelSeriesDefaults);\nDependencyWheelSeries_extend(DependencyWheelSeries.prototype, {\n    orderNodes: false,\n    getCenter: PieSeries.prototype.getCenter\n});\nDependencyWheelSeries.prototype.pointClass = DependencyWheel_DependencyWheelPoint;\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('dependencywheel', DependencyWheelSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const DependencyWheel_DependencyWheelSeries = ((/* unused pure expression or super */ null && (DependencyWheelSeries)));\n\n;// ./code/es-modules/masters/modules/dependency-wheel.js\n\n\n\n\n/* harmony default export */ const dependency_wheel_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__WEBPACK_EXTERNAL_MODULE__28__", "SankeyColumnComposition", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "dependency_wheel_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "sankey", "pointClass", "SankeyPoint", "seriesTypes", "pInt", "wrap", "DependencyWheel_DependencyWheelPoint", "getDataLabelPath", "label", "point", "renderer", "series", "chart", "shapeArgs", "upperHalf", "angle", "Math", "PI", "start", "end", "dataLabelPath", "destroy", "proceed", "arc", "open", "longArc", "abs", "attr", "x", "y", "r", "options", "distance", "clockwise", "add", "defs", "<PERSON><PERSON><PERSON><PERSON>", "defined", "getAlignFactor", "<PERSON><PERSON><PERSON><PERSON>", "compose", "points", "sankeyColumnArray", "sankeyColumn", "SankeyColumnAdditions", "constructor", "getTranslationFactor", "column", "nodes", "slice", "minLinkWidth", "skipPoint", "factor", "i", "remainingHeight", "plotSizeY", "borderWidth", "length", "nodePadding", "sum", "getSum", "splice", "max", "node", "push", "top", "height", "reduce", "nodeAlignment", "left", "equalNodes", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "inverted", "plotHeight", "plot<PERSON>id<PERSON>", "width", "plotSizeX", "round", "offset", "totalNodeOffset", "is", "hangsFrom", "absoluteTop", "nodeY", "directionOffset", "optionOffset", "relativeTop", "Sankey_SankeyColumnComposition", "highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_", "highcharts_SVGElement_commonjs_highcharts_SVGElement_commonjs2_highcharts_SVGElement_root_Highcharts_SVGElement_default", "deg2rad", "addEvent", "merge", "<PERSON><PERSON><PERSON>", "TextPath_defined", "extend", "setTextPath", "path", "textPathOptions", "enabled", "attributes", "dy", "startOffset", "textAnchor", "url", "textWrapper", "text", "textPath", "undo", "e", "textPathId", "textAttribs", "dx", "transform", "box", "children", "tagName", "href", "added", "textCache", "buildText", "setPolygon", "event", "bBox", "tp", "element", "querySelector", "polygon", "b", "h", "fontMetrics", "descender", "lineCleanerRegex", "RegExp", "lines", "innerHTML", "replace", "split", "numOfLines", "appendTopAndBottom", "charIndex", "positionOfChar", "rotation", "getRotationOfChar", "cosRot", "cos", "sinRot", "sin", "lineIndex", "lineLen", "line", "lineCharIndex", "srcCharIndex", "lower", "upper", "getStartPositionOfChar", "unshift", "char<PERSON><PERSON>", "getEndPositionOfChar", "drawTextPath", "labelOptions", "formatPrefix", "useHTML", "graphic", "animObject", "DependencyWheelSeries_deg2rad", "pie", "PieSeries", "SankeySeries", "DependencyWheelSeries_extend", "DependencyWheelSeries_merge", "DependencyWheelSeries_relativeLength", "Extensions_TextPath", "SVGElementClass", "svgElementProto", "DependencyWheelSeries", "animate", "init", "step", "animation", "duration", "opacity", "setTimeout", "isNode", "createNode", "id", "linksFrom", "concat", "linksTo", "acc", "link", "weight", "otherNode", "fromNode", "toNode", "links", "sliced", "sort", "index", "reverse", "createNodeColumns", "columns", "getNodePadding", "translate", "center", "getCenter", "startAngle", "brOption", "borderRadius", "radius", "nodeColumns", "centerX", "centerY", "innerR", "nodeWidth", "shapeType", "dlBox", "linkBase", "curveFactor", "corners", "map", "cpX", "cpY", "defaultOptions", "dataLabels", "orderNodes", "registerSeriesType"], "mappings": "CAYA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,UAAa,EAC/G,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,sCAAuC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAACA,EAAK,UAAa,CAAE,GAClJ,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,sCAAsC,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,CAAEA,EAAK,WAAc,CAAC,UAAa,EAEtJA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAAEA,EAAK,UAAa,CAAC,UAAa,CACzH,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,IACvG,AAAC,CAAA,KACP,aACA,IA8XNC,EA9XUC,EAAuB,CAE/B,GACC,AAACT,IAERA,EAAOD,OAAO,CAAGQ,CAEX,EAEA,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIK,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAad,OAAO,CAG5B,IAAIC,EAASU,CAAwB,CAACE,EAAS,CAAG,CAGjDb,QAAS,CAAC,CACX,EAMA,OAHAU,CAAmB,CAACG,EAAS,CAACZ,EAAQA,EAAOD,OAAO,CAAEY,GAG/CX,EAAOD,OAAO,AACtB,CAMCY,EAAoBI,CAAC,CAAG,AAACf,IACxB,IAAIgB,EAAShB,GAAUA,EAAOiB,UAAU,CACvC,IAAOjB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAW,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACnB,EAASqB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACvB,EAASsB,IAC5EE,OAAOC,cAAc,CAACzB,EAASsB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAEjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAejL,GAAM,CAAEE,OAAQ,CAAET,UAAW,CAAEU,WAAYC,CAAW,CAAE,CAAE,CAAE,CAAG,AAACH,IAA2II,WAAW,CAEhN,CAAEC,KAAAA,CAAI,CAAEC,KAAAA,CAAI,CAAE,CAAIR,IA4DWS,EAtDnC,cAAmCJ,EAU/BK,iBAAiBC,CAAK,CAAE,CACpB,IAAMC,EAAQ,IAAI,CAAEC,EAAWD,EAAME,MAAM,CAACC,KAAK,CAACF,QAAQ,CAAEG,EAAYJ,EAAMI,SAAS,CAAEC,EAAYL,EAAMM,KAAK,CAAG,GAAKN,EAAMM,KAAK,CAAGC,KAAKC,EAAE,CAAEC,EAAQL,EAAUK,KAAK,EAAI,EAAGC,EAAMN,EAAUM,GAAG,EAAI,EA+BpM,OA7BKV,EAAMW,aAAa,EAWpBX,EAAMW,aAAa,CAAGX,EAAMW,aAAa,CAACC,OAAO,GACjD,OAAOZ,EAAMW,aAAa,EAV1Bf,EAAKG,EAAO,UAAW,SAAUc,CAAO,EAIpC,OAHIb,EAAMW,aAAa,EACnBX,CAAAA,EAAMW,aAAa,CAAGX,EAAMW,aAAa,CAACC,OAAO,EAAC,EAE/CC,EAAQ7B,IAAI,CAAC,IAAI,CAC5B,GAQJgB,EAAMW,aAAa,CAAGV,EACjBa,GAAG,CAAC,CACLC,KAAM,CAAA,EACNC,QAAST,KAAKU,GAAG,CAACV,KAAKU,GAAG,CAACR,GAASF,KAAKU,GAAG,CAACP,IAAQH,KAAKC,EAAE,CAAG,EAAI,CACvE,GACKU,IAAI,CAAC,CACNC,EAAGf,EAAUe,CAAC,CACdC,EAAGhB,EAAUgB,CAAC,CACdC,EAAI,AAACjB,CAAAA,EAAUiB,CAAC,EAAI,CAAA,EAAK1B,EAAKI,EAAMuB,OAAO,EAAEC,UAAY,GACzDd,MAAQJ,EAAYI,EAAQC,EAC5BA,IAAML,EAAYK,EAAMD,EACxBe,UAAW,CAACnB,CAChB,GACKoB,GAAG,CAACxB,EAASyB,IAAI,EACf1B,EAAMW,aAAa,AAC9B,CACAgB,SAAU,CAEN,MAAO,CAAA,CACX,CACJ,EAqNM,CAAEC,QAAAA,CAAO,CAAEC,eAAAA,CAAc,CAAEC,eAAAA,CAAc,CAAE,CAAI1C,KAOrD,AAAC,SAAU3B,CAAuB,EA4B9BA,EAAwBsE,OAAO,CAN/B,SAAiBC,CAAM,CAAE9B,CAAM,EAI3B,OAFA+B,AAD0BD,EACRE,YAAY,CAC1B,IAAIC,EAFkBH,EAEuB9B,GAFvB8B,CAI9B,CAOA,OAAMG,EAMFC,YAAYJ,CAAM,CAAE9B,CAAM,CAAE,CACxB,IAAI,CAAC8B,MAAM,CAAGA,EACd,IAAI,CAAC9B,MAAM,CAAGA,CAClB,CAgBAmC,qBAAqBnC,CAAM,CAAE,CACzB,IAAMoC,EAAS,IAAI,CAACN,MAAM,CAAEO,EAAQD,EAAOE,KAAK,GAAIrC,EAAQD,EAAOC,KAAK,CAAEsC,EAAevC,EAAOoB,OAAO,CAACmB,YAAY,EAAI,EACpHC,EAAWC,EAAS,EAAGC,EAAGC,EAAmB,AAAC1C,CAAAA,EAAM2C,SAAS,EAAI,CAAA,EAChE5C,CAAAA,EAAOoB,OAAO,CAACyB,WAAW,EAAI,CAAA,EAC/B,AAACT,CAAAA,EAAOU,MAAM,CAAG,CAAA,EAAK9C,EAAO+C,WAAW,CAK5C,KAAOX,EAAOU,MAAM,EAAE,CAIlB,IAHAL,EAASE,EAAkBP,EAAOJ,YAAY,CAACgB,GAAG,GAClDR,EAAY,CAAA,EACZE,EAAIN,EAAOU,MAAM,CACVJ,KACCN,CAAM,CAACM,EAAE,CAACO,MAAM,GAAKR,EAASF,IAC9BH,EAAOc,MAAM,CAACR,EAAG,GACjBC,EACItC,KAAK8C,GAAG,CAAC,EAAGR,EAAkBJ,GAClCC,EAAY,CAAA,GAGpB,GAAI,CAACA,EACD,KAER,CAGA,IAAK,IAAMY,KADXhB,EAAOU,MAAM,CAAG,EACGT,GACfD,EAAOiB,IAAI,CAACD,GAEhB,OAAOX,CACX,CAWAa,IAAIb,CAAM,CAAE,CACR,IAAMzC,EAAS,IAAI,CAACA,MAAM,CAAE+C,EAAc/C,EAAO+C,WAAW,CAAEQ,EAAS,IAAI,CAACzB,MAAM,CAAC0B,MAAM,CAAC,CAACD,EAAQH,KAC3FG,EAAS,GACTA,CAAAA,GAAUR,CAAU,EAGxBQ,GADmBlD,KAAK8C,GAAG,CAACC,EAAKH,MAAM,GAAKR,EAAQzC,EAAOoB,OAAO,CAACmB,YAAY,EAAI,IAGpF,GAEH,OAAOZ,EAAe3B,EAAOoB,OAAO,CAACqC,aAAa,EAAI,UAAa,CAAA,AAACzD,CAAAA,EAAOC,KAAK,CAAC2C,SAAS,EAAI,CAAA,EAAKW,CAAK,CAC5G,CAWAG,KAAKjB,CAAM,CAAE,CACT,IAAMzC,EAAS,IAAI,CAACA,MAAM,CAAEC,EAAQD,EAAOC,KAAK,CAAE0D,EAAa3D,EAAOoB,OAAO,CAACuC,UAAU,CAAEC,EAAkB3D,EAAM4D,QAAQ,CAAG5D,EAAM6D,UAAU,CAAG7D,EAAM8D,SAAS,CAAGhB,EAAc/C,EAAO+C,WAAW,CAAEiB,EAAQ,IAAI,CAAClC,MAAM,CAAC0B,MAAM,CAAC,CAACQ,EAAOZ,KAC/NY,EAAQ,GACRA,CAAAA,GAASjB,CAAU,EAMvBiB,GAJkBL,EACdC,EAAiBR,EAAKpD,MAAM,CAACqC,KAAK,CAACS,MAAM,CACrCC,EACJ1C,KAAK8C,GAAG,CAACC,EAAKH,MAAM,GAAKR,EAAQzC,EAAOoB,OAAO,CAACmB,YAAY,EAAI,IAGrE,GACH,MAAO,AAAC,CAAA,AAACtC,CAAAA,EAAMgE,SAAS,EAAI,CAAA,EAAK5D,KAAK6D,KAAK,CAACF,EAAK,EAAK,CAC1D,CAYAhB,KAAM,CACF,OAAO,IAAI,CAAClB,MAAM,CAAC0B,MAAM,CAAC,CAACR,EAAKI,IAAUJ,EAAMI,EAAKH,MAAM,GAAK,EACpE,CAaAkB,OAAOf,CAAI,CAAEX,CAAM,CAAE,CACjB,IAAML,EAAS,IAAI,CAACN,MAAM,CAAE9B,EAAS,IAAI,CAACA,MAAM,CAAE+C,EAAc/C,EAAO+C,WAAW,CAC9EoB,EAAS,EAAGC,EAChB,GAAIpE,EAAOqE,EAAE,CAAC,iBAAmBjB,EAAKkB,SAAS,CAC3C,MAAO,CACHC,YAAanB,EAAKkB,SAAS,CAACE,KAAK,AACrC,EAEJ,IAAK,IAAI9B,EAAI,EAAGA,EAAIN,EAAOU,MAAM,CAAEJ,IAAK,CACpC,IAAMM,EAAMZ,CAAM,CAACM,EAAE,CAACO,MAAM,GACtBM,EAASlD,KAAK8C,GAAG,CAACH,EAAMP,EAAQzC,EAAOoB,OAAO,CAACmB,YAAY,EAAI,GAC/DkC,EAAkBrB,EAAKhC,OAAO,CAACpB,EAAOC,KAAK,CAAC4D,QAAQ,CACtD,mBACA,iBAAiB,CAAEa,EAAetB,EAAKhC,OAAO,CAAC+C,MAAM,EAAI,EAQ7D,GANIC,EADApB,EACkBO,EAASR,EAIT,EAElBX,CAAM,CAACM,EAAE,GAAKU,EACd,MAAO,CACHuB,YAAaR,EAAUzC,CAAAA,EAAQ+C,GAG3B7C,EAAe6C,EAAiBlB,GAChC3B,EAAe8C,EAAcN,EAAe,CACpD,EAEJD,GAAUC,CACd,CACJ,CACJ,CACA7G,EAAwB0E,qBAAqB,CAAGA,CACpD,EAAG1E,GAA4BA,CAAAA,EAA0B,CAAC,CAAA,GAM7B,IAAMqH,EAAkCrH,EAGrE,IAAIsH,EAAmHnH,EAAoB,IACvIoH,EAAuIpH,EAAoBI,CAAC,CAAC+G,GAgBjK,GAAM,CAAEE,QAAAA,CAAO,CAAE,CAAI7F,IACf,CAAE8F,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAEC,UAAAA,CAAS,CAAExD,QAASyD,CAAgB,CAAEC,OAAAA,CAAM,CAAE,CAAIlG,IAyB3E,SAASmG,EAAYC,CAAI,CAAEC,CAAe,EAEtCA,EAAkBN,EAAM,CAAA,EAAM,CAC1BO,QAAS,CAAA,EACTC,WAAY,CACRC,GAAI,GACJC,YAAa,MACbC,WAAY,QAChB,CACJ,EAAGL,GACH,IAAMM,EAAM,IAAI,CAAC9F,QAAQ,CAAC8F,GAAG,CAAEC,EAAc,IAAI,CAACC,IAAI,EAAI,IAAI,CAAEC,EAAWF,EAAYE,QAAQ,CAAE,CAAEP,WAAAA,CAAU,CAAED,QAAAA,CAAO,CAAE,CAAGD,EAM3H,GALAD,EAAOA,GAASU,GAAYA,EAASV,IAAI,CAErCU,GACAA,EAASC,IAAI,GAEbX,GAAQE,EAAS,CACjB,IAAMS,EAAOjB,EAASc,EAAa,kBAAmB,AAACI,IACnD,GAAIZ,GAAQE,EAAS,CAEjB,IAAIW,EAAab,EAAKtE,IAAI,CAAC,MACtBmF,GACDb,EAAKtE,IAAI,CAAC,KAAMmF,EAAajB,KAGjC,IAAMkB,EAAc,CAGhBnF,EAAG,EACHC,EAAG,CACP,EACIiE,EAAiBM,EAAWY,EAAE,IAC9BD,EAAYC,EAAE,CAAGZ,EAAWY,EAAE,CAC9B,OAAOZ,EAAWY,EAAE,EAEpBlB,EAAiBM,EAAWC,EAAE,IAC9BU,EAAYV,EAAE,CAAGD,EAAWC,EAAE,CAC9B,OAAOD,EAAWC,EAAE,EAExBI,EAAY9E,IAAI,CAACoF,GAEjB,IAAI,CAACpF,IAAI,CAAC,CAAEsF,UAAW,EAAG,GACtB,IAAI,CAACC,GAAG,EACR,CAAA,IAAI,CAACA,GAAG,CAAG,IAAI,CAACA,GAAG,CAAC7F,OAAO,EAAC,EAGhC,IAAM8F,EAAWN,EAAE7D,KAAK,CAACC,KAAK,CAAC,EAC/B4D,CAAAA,EAAE7D,KAAK,CAACS,MAAM,CAAG,EACjBoD,EAAE7D,KAAK,CAAC,EAAE,CAAG,CACToE,QAAS,WACThB,WAAYL,EAAOK,EAAY,CAC3B,cAAeA,EAAWG,UAAU,CACpCc,KAAM,CAAC,EAAEb,EAAI,CAAC,EAAEM,EAAW,CAAC,AAChC,GACAK,SAAAA,CACJ,CACJ,CACJ,EAEAV,CAAAA,EAAYE,QAAQ,CAAG,CAAEV,KAAAA,EAAMW,KAAAA,CAAK,CACxC,MAEIH,EAAY9E,IAAI,CAAC,CAAEqF,GAAI,EAAGX,GAAI,CAAE,GAChC,OAAOI,EAAYE,QAAQ,CAO/B,OALI,IAAI,CAACW,KAAK,GAEVb,EAAYc,SAAS,CAAG,GACxB,IAAI,CAAC7G,QAAQ,CAAC8G,SAAS,CAACf,IAErB,IAAI,AACf,CAWA,SAASgB,EAAWC,CAAK,EACrB,IAAMC,EAAOD,EAAMC,IAAI,CAAEC,EAAK,IAAI,CAACC,OAAO,EAAEC,cAAc,YAC1D,GAAIF,EAAI,CACJ,IAAMG,EAAU,EAAE,CAAE,CAAEC,EAAAA,CAAC,CAAEC,EAAAA,CAAC,CAAE,CAAG,IAAI,CAACvH,QAAQ,CAACwH,WAAW,CAAC,IAAI,CAACL,OAAO,EAAGM,EAAYF,EAAID,EAAGI,EAAmB,AAAIC,OAAO,gEAEtG,KAAMC,EAAQV,EAC5BW,SAAS,CACTC,OAAO,CAACJ,EAAkB,IAC1BK,KAAK,CAAC,sCAAuCC,EAAaJ,EAAM7E,MAAM,CAIrEkF,EAAqB,CAACC,EAAWC,KACnC,GAAM,CAAEjH,EAAAA,CAAC,CAAEC,EAAAA,CAAC,CAAE,CAAGgH,EAAgBC,EAAW,AAAClB,CAAAA,EAAGmB,iBAAiB,CAACH,GAAa,EAAC,EAAKlD,EAASsD,EAAShI,KAAKiI,GAAG,CAACH,GAAWI,EAASlI,KAAKmI,GAAG,CAACL,GAC7I,MAAO,CACH,CACIlH,EAAIuG,EAAYa,EAChBnH,EAAIsG,EAAYe,EACnB,CACD,CACItH,EAAIoG,EAAIgB,EACRnH,EAAImG,EAAIkB,EACX,CACJ,AACL,EACA,IAAK,IAAI7F,EAAI,EAAG+F,EAAY,EAAGA,EAAYV,EAAYU,IAAa,CAChE,IAA+BC,EAAUC,AAA5BhB,CAAK,CAACc,EAAU,CAAiB3F,MAAM,CACpD,IAAK,IAAI8F,EAAgB,EAAGA,EAAgBF,EAASE,GAAiB,EAClE,GAAI,CACA,IAAMC,EAAgBnG,EAClBkG,EACAH,EAAY,CAACK,EAAOC,EAAM,CAAGf,EAAmBa,EAAc5B,EAAG+B,sBAAsB,CAACH,GACxFD,AAAkB,CAAA,IAAlBA,GACAxB,EAAQ/D,IAAI,CAAC0F,GACb3B,EAAQ/D,IAAI,CAACyF,KAGK,IAAdL,GACArB,EAAQ6B,OAAO,CAACF,GAEhBN,IAAcV,EAAa,GAC3BX,EAAQ/D,IAAI,CAACyF,GAGzB,CACA,MAAO5C,EAAG,CAGN,KACJ,CAEJxD,GAAKgG,EAAU,EACf,GAAI,CACA,IAAMG,EAAenG,EAAI+F,EAAWS,EAAUjC,EAAGkC,oBAAoB,CAACN,GAAe,CAACC,EAAOC,EAAM,CAAGf,EAAmBa,EAAcK,GACvI9B,EAAQ6B,OAAO,CAACF,GAChB3B,EAAQ6B,OAAO,CAACH,EACpB,CACA,MAAO5C,EAAG,CAGN,KACJ,CACJ,CAEIkB,EAAQtE,MAAM,EACdsE,EAAQ/D,IAAI,CAAC+D,CAAO,CAAC,EAAE,CAAC9E,KAAK,IAEjC0E,EAAKI,OAAO,CAAGA,CACnB,CACA,OAAOJ,CACX,CAWA,SAASoC,EAAarC,CAAK,EACvB,IAAMsC,EAAetC,EAAMsC,YAAY,CAAEvJ,EAAQiH,EAAMjH,KAAK,CAAEyF,EAAmB8D,CAAY,CAACvJ,EAAMwJ,YAAY,CAAG,WAAW,EAC1HD,EAAarD,QAAQ,CACrBT,GAAmB,CAAC8D,EAAaE,OAAO,GACxC,IAAI,CAAClE,WAAW,CAACvF,EAAMF,gBAAgB,GAAG,IAAI,GAAKE,EAAM0J,OAAO,CAAEjE,GAC9DzF,EAAMW,aAAa,EACnB,CAAC8E,EAAgBC,OAAO,EAExB1F,CAAAA,EAAMW,aAAa,CAAIX,EAAMW,aAAa,CAACC,OAAO,EAAE,EAGhE,CA4BA,GAAM,CAAE+I,WAAAA,CAAU,CAAE,CAAIvK,IAIlB,CAAE6F,QAAS2E,CAA6B,CAAE,CAAIxK,IAG9C,CAAEyK,IAAKC,CAAS,CAAEvK,OAAQwK,CAAY,CAAE,CAAG,AAACzK,IAA2II,WAAW,CAElM,CAAE4F,OAAQ0E,CAA4B,CAAE7E,MAAO8E,CAA2B,CAAEnI,eAAgBoI,CAAoC,CAAE,CAAI9K,IAG5I+K,AA/BiB,CAAA,CACbpI,QATJ,SAAiBqI,CAAe,EAC5BlF,EAASkF,EAAiB,eAAgBpD,GAC1C9B,EAASkF,EAAiB,wBAAyBd,GACnD,IAAMe,EAAkBD,EAAgBtL,SAAS,AAC5CuL,CAAAA,EAAgB9E,WAAW,EAC5B8E,CAAAA,EAAgB9E,WAAW,CAAGA,CAAU,CAEhD,CAGA,CAAA,EA6BoBxD,OAAO,CAAEiD,IAa7B,OAAMsF,UAA8BP,EAMhCQ,QAAQC,CAAI,CAAE,CAEV,GAAI,CAACA,EAAM,CACP,IAAgEC,EAAO,AAAtDd,EAAWzJ,AAFjB,IAAI,CAEoBoB,OAAO,CAACoJ,SAAS,EAAEC,QAAQ,CAAqB,EAAKzK,AAF7E,IAAI,CAEgFqC,KAAK,CAACS,MAAM,CACvGJ,EAAI,EACR,IAAK,IAAM5C,KAASE,AAJT,IAAI,CAIYqC,KAAK,CAAE,CAC9B,IAAMmH,EAAU1J,EAAM0J,OAAO,CACzBA,IACAA,EAAQxI,IAAI,CAAC,CAAE0J,QAAS,CAAE,GAC1BC,WAAW,KACH7K,EAAM0J,OAAO,EACb1J,EAAM0J,OAAO,CAACa,OAAO,CAAC,CAAEK,QAAS,CAAE,EAAG,CAAED,SAAUF,CAAK,EAE/D,EAAGA,EAAO7H,KAElB,CACA,IAAK,IAAM5C,KAASE,AAfT,IAAI,CAeY8B,MAAM,CAAE,CAC/B,IAAM0H,EAAU1J,EAAM0J,OAAO,AACzB,EAAC1J,EAAM8K,MAAM,EAAIpB,GACjBA,EAAQxI,IAAI,CAAC,CAAE0J,QAAS,CAAE,GACrBL,OAAO,CAAC,CACTK,QAAS,CACb,EAAG1K,AArBA,IAAI,CAqBGoB,OAAO,CAACoJ,SAAS,CAEnC,CACJ,CACJ,CACAK,WAAWC,CAAE,CAAE,CACX,IAAM1H,EAAO,KAAK,CAACyH,WAAWC,GAqC9B,OAhCA1H,EAAKH,MAAM,CAAG,IAAOG,EAAK2H,SAAS,CAC9BC,MAAM,CAAC5H,EAAK6H,OAAO,EACnBzH,MAAM,CAAC,CAAC0H,EAAKC,IAAUD,EAAMC,EAAKC,MAAM,CAAG,GAKhDhI,EAAKe,MAAM,CAAG,AAACrE,IACX,IAAMuL,EAAY,AAACF,GAAUA,EAAKG,QAAQ,GAAKlI,EAC3C+H,EAAKI,MAAM,CACXJ,EAAKG,QAAQ,CACbnH,EAAS,EAAGqH,EAAQpI,EAAK2H,SAAS,CAACC,MAAM,CAAC5H,EAAK6H,OAAO,EAAGQ,EAG7DD,EAAME,IAAI,CAAC,CAACxN,EAAGmJ,IAAOgE,EAAUnN,GAAGyN,KAAK,CAAGN,EAAUhE,GAAGsE,KAAK,EAC7D,IAAK,IAAIjJ,EAAI,EAAGA,EAAI8I,EAAM1I,MAAM,CAAEJ,IAC9B,GAAI2I,EAAUG,CAAK,CAAC9I,EAAE,EAAEiJ,KAAK,CAAGvI,EAAKuI,KAAK,CAAE,CACxCH,EAAQA,EAAMlJ,KAAK,CAAC,EAAGI,GAAGkJ,OAAO,GAAGZ,MAAM,CAACQ,EAAMlJ,KAAK,CAACI,GAAGkJ,OAAO,IACjEH,EAAS,CAAA,EACT,KACJ,CAECA,GACDD,EAAMI,OAAO,GAEjB,IAAK,IAAIlJ,EAAI,EAAGA,EAAI8I,EAAM1I,MAAM,CAAEJ,IAAK,CACnC,GAAI8I,CAAK,CAAC9I,EAAE,GAAK5C,EACb,OAAOqE,EAEXA,GAAUqH,CAAK,CAAC9I,EAAE,CAAC0I,MAAM,AAC7B,CACJ,EACOhI,CACX,CAKAyI,mBAAoB,CAChB,IAAqBC,EAAU,CAAClH,EAA+B/C,OAAO,CAAC,EAAE,CAA1D,IAAI,EAAgE,CACnF,IAAK,IAAMuB,KAAQpD,AADJ,IAAI,CACOqC,KAAK,CAC3Be,EAAKhB,MAAM,CAAG,EACd0J,CAAO,CAAC,EAAE,CAACzI,IAAI,CAACD,GAEpB,OAAO0I,CACX,CAKAC,gBAAiB,CACb,OAAO,IAAI,CAAC3K,OAAO,CAAC2B,WAAW,CAAG1C,KAAKC,EAAE,AAC7C,CAMA0L,WAAY,CACR,IAAqB5K,EAAUpB,AAAhB,IAAI,CAAmBoB,OAAO,CAAEqB,EAAS,EAAIpC,KAAKC,EAAE,CAC9DN,CAAAA,AADU,IAAI,CACPC,KAAK,CAAC6D,UAAU,CAAG9D,AADhB,IAAI,CACmB+L,cAAc,EAAC,EAAIE,EAASjM,AADnD,IAAI,CACsDkM,SAAS,GAAIC,EAAa,AAAC/K,CAAAA,EAAQ+K,UAAU,CAAG,EAAC,EAAKzC,EAA+B0C,EAAWhL,EAAQiL,YAAY,CAAEA,EAAe,AAAoB,UAApB,OAAOD,EACjNA,EAASE,MAAM,CAAGF,EAEtB,IAAK,IAAMhJ,KADX,KAAK,CAAC4I,YACa,IAAI,CAACO,WAAW,CAAC,EAAE,EAElC,GAAInJ,EAAKJ,GAAG,CAAE,CACV,IAAM9C,EAAYkD,EAAKlD,SAAS,CAAEsM,EAAUP,CAAM,CAAC,EAAE,CAAEQ,EAAUR,CAAM,CAAC,EAAE,CAAE9K,EAAI8K,CAAM,CAAC,EAAE,CAAG,EAChES,EAASvL,EAAI6I,EAAqC2C,AAD6BvL,CAAAA,AAAsB,SAAtBA,EAAQuL,SAAS,CACxH,GAAKvL,EAAQuL,SAAS,AAAD,GAAkE,EAAGxL,GAAIZ,EAAQ4L,EAAa1J,EAAUvC,CAAAA,EAAUgB,CAAC,EAAI,CAAA,EAAIV,EAAM2L,EACtJ1J,EAAU,CAAA,AAACvC,CAAAA,EAAUgB,CAAC,EAAI,CAAA,EAAMhB,CAAAA,EAAUqD,MAAM,EAAI,CAAA,CAAC,EAoBzD,IAAK,IAAMzD,KAlBXsD,EAAKhD,KAAK,CAAGG,EAAQ,AAACC,CAAAA,EAAMD,CAAI,EAAK,EACrC6C,EAAKwJ,SAAS,CAAG,MACjBxJ,EAAKlD,SAAS,CAAG,CACbe,EAAGuL,EACHtL,EAAGuL,EACHtL,EAAGA,EACHuL,OAAQA,EACRnM,MAAOA,EACPC,IAAKA,EACL6L,aAAAA,CACJ,EACAjJ,EAAKyJ,KAAK,CAAG,CACT5L,EAAGuL,EAAUnM,KAAKiI,GAAG,CAAC,AAAC/H,CAAAA,EAAQC,CAAE,EAAK,GAAMW,CAAAA,EAAIuL,CAAK,EAAK,EAC1DxL,EAAGuL,EAAUpM,KAAKmI,GAAG,CAAC,AAACjI,CAAAA,EAAQC,CAAE,EAAK,GAAMW,CAAAA,EAAIuL,CAAK,EAAK,EAC1D1I,MAAO,EACPT,OAAQ,CACZ,EAEoBH,EAAK2H,SAAS,EAC9B,GAAIjL,EAAMgN,QAAQ,CAAE,CAEhB,IADIC,EAAa1L,EACX2L,EAAUlN,EAAMgN,QAAQ,CAACG,GAAG,CAAC,CAAC3J,EAAKZ,KACrC,IAAMtC,EAAQqC,EAASa,EAAKrC,EAAIZ,KAAKiI,GAAG,CAAC6D,EAAa/L,GAAUsM,CAAAA,EAAS,CAAA,EAAIxL,EAAIb,KAAKmI,GAAG,CAAC2D,EAAa/L,GAAUsM,CAAAA,EAAS,CAAA,EAc1H,OAbAK,EAAc3L,EAAQ2L,WAAW,EAAI,EAKrC1L,CAAAA,EAAWhB,KAAKU,GAAG,CAACjB,EAAMgN,QAAQ,CAAC,EAAIpK,EAAE,CAAGD,EAASrC,EAAK,EAC3CC,KAAKC,EAAE,EAClBe,CAAAA,EAAW,EAAIhB,KAAKC,EAAE,CAAGe,CAAO,EAEpCA,CAAAA,GAAsBqL,CAAK,EACZA,GACXK,CAAAA,GAAgB1L,EAAWqL,CAAM,EAE9B,CACHzL,EAAGuL,EAAUvL,EACbC,EAAGuL,EAAUvL,EACbgM,IAAKV,EAAU,AAAC,CAAA,EAAIO,CAAU,EAAK9L,EACnCkM,IAAKV,EAAU,AAAC,CAAA,EAAIM,CAAU,EAAK7L,CACvC,CACJ,EACApB,CAAAA,EAAMI,SAAS,CAAG,CACdjC,EAAG,CAAC,CACI,IACA+O,CAAO,CAAC,EAAE,CAAC/L,CAAC,CAAE+L,CAAO,CAAC,EAAE,CAAC9L,CAAC,CAC7B,CAAE,CACC,IACAwL,EAAQA,EACR,EACA,EACA,EACAM,CAAO,CAAC,EAAE,CAAC/L,CAAC,CAAE+L,CAAO,CAAC,EAAE,CAAC9L,CAAC,CAC7B,CAAE,CACC,IACA8L,CAAO,CAAC,EAAE,CAACE,GAAG,CAAEF,CAAO,CAAC,EAAE,CAACG,GAAG,CAC9BH,CAAO,CAAC,EAAE,CAACE,GAAG,CAAEF,CAAO,CAAC,EAAE,CAACG,GAAG,CAC9BH,CAAO,CAAC,EAAE,CAAC/L,CAAC,CAAE+L,CAAO,CAAC,EAAE,CAAC9L,CAAC,CAC7B,CAAE,CACC,IACAwL,EAAQA,EACR,EACA,EACA,EACAM,CAAO,CAAC,EAAE,CAAC/L,CAAC,CAAE+L,CAAO,CAAC,EAAE,CAAC9L,CAAC,CAC7B,CAAE,CACC,IACA8L,CAAO,CAAC,EAAE,CAACE,GAAG,CAAEF,CAAO,CAAC,EAAE,CAACG,GAAG,CAC9BH,CAAO,CAAC,EAAE,CAACE,GAAG,CAAEF,CAAO,CAAC,EAAE,CAACG,GAAG,CAC9BH,CAAO,CAAC,EAAE,CAAC/L,CAAC,CAAE+L,CAAO,CAAC,EAAE,CAAC9L,CAAC,CAC7B,CAAC,AACV,CACJ,CAER,CAER,CACJ,CAMAkJ,EAAsBgD,cAAc,CAAGrD,EAA4BF,EAAauD,cAAc,CAl1BxD,CAgElCnB,OAAQ,CAAC,KAAM,KAAK,CACpBc,YAAa,GAIbZ,WAAY,EACZkB,WAAY,CACRrH,SAAU,CAUNR,QAAS,CAAA,EACTC,WAAY,CASRC,GAAI,CACR,CACJ,CACJ,CACJ,GAovBAoE,EAA6BM,EAAsBxL,SAAS,CAAE,CAC1D0O,WAAY,CAAA,EACZpB,UAAWtC,EAAUhL,SAAS,CAACsN,SAAS,AAC5C,GACA9B,EAAsBxL,SAAS,CAACU,UAAU,CAAGK,EAC7CP,IAA0ImO,kBAAkB,CAAC,kBAAmBnD,GAanJ,IAAMpL,EAAyBE,IAGlD,OADYH,EAAoB,OAAU,AAE3C,CAAA"}