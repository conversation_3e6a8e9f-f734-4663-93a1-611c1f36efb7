{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/data\n * @requires highcharts\n *\n * Data module\n *\n * (c) 2012-2025 Torstein Honsi\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Axis\"], root[\"_Highcharts\"][\"Chart\"], root[\"_Highcharts\"][\"Point\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/data\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"Axis\"],amd1[\"Chart\"],amd1[\"Point\"],amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/data\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"Axis\"], root[\"_Highcharts\"][\"Chart\"], root[\"_Highcharts\"][\"Point\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"Axis\"], root[\"Highcharts\"][\"Chart\"], root[\"Highcharts\"][\"Point\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__532__, __WEBPACK_EXTERNAL_MODULE__960__, __WEBPACK_EXTERNAL_MODULE__260__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 260:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__260__;\n\n/***/ }),\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 532:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__532__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ }),\n\n/***/ 960:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__960__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ data_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Core/HttpUtilities.js\n/* *\n *\n *  (c) 2010-2025 Christer Vasseng, Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\nconst { win } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { discardElement, objectEach } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * Perform an Ajax call.\n *\n * @function Highcharts.ajax\n *\n * @param {Highcharts.AjaxSettingsObject} settings\n *        The Ajax settings to use.\n *\n * @return {false|undefined}\n *         Returns false, if error occurred.\n */\nfunction ajax(settings) {\n    const headers = {\n        json: 'application/json',\n        xml: 'application/xml',\n        text: 'text/plain',\n        octet: 'application/octet-stream'\n    }, r = new XMLHttpRequest();\n    /**\n     * Private error handler.\n     * @private\n     * @param {XMLHttpRequest} xhr\n     * Internal request object.\n     * @param {string|Error} err\n     * Occurred error.\n     */\n    function handleError(xhr, err) {\n        if (settings.error) {\n            settings.error(xhr, err);\n        }\n        else {\n            // @todo Maybe emit a highcharts error event here\n        }\n    }\n    if (!settings.url) {\n        return false;\n    }\n    r.open((settings.type || 'get').toUpperCase(), settings.url, true);\n    if (!settings.headers?.['Content-Type']) {\n        r.setRequestHeader('Content-Type', headers[settings.dataType || 'json'] || headers.text);\n    }\n    objectEach(settings.headers, function (val, key) {\n        r.setRequestHeader(key, val);\n    });\n    if (settings.responseType) {\n        r.responseType = settings.responseType;\n    }\n    // @todo lacking timeout handling\n    r.onreadystatechange = function () {\n        let res;\n        if (r.readyState === 4) {\n            if (r.status === 200) {\n                if (settings.responseType !== 'blob') {\n                    res = r.responseText;\n                    if (settings.dataType === 'json') {\n                        try {\n                            res = JSON.parse(res);\n                        }\n                        catch (e) {\n                            if (e instanceof Error) {\n                                return handleError(r, e);\n                            }\n                        }\n                    }\n                }\n                return settings.success?.(res, r);\n            }\n            handleError(r, r.responseText);\n        }\n    };\n    if (settings.data && typeof settings.data !== 'string') {\n        settings.data = JSON.stringify(settings.data);\n    }\n    r.send(settings.data);\n}\n/**\n * Get a JSON resource over XHR, also supporting CORS without preflight.\n *\n * @function Highcharts.getJSON\n * @param {string} url\n *        The URL to load.\n * @param {Function} success\n *        The success callback. For error handling, use the `Highcharts.ajax`\n *        function instead.\n */\nfunction getJSON(url, success) {\n    HttpUtilities.ajax({\n        url: url,\n        success: success,\n        dataType: 'json',\n        headers: {\n            // Override the Content-Type to avoid preflight problems with CORS\n            // in the Highcharts demos\n            'Content-Type': 'text/plain'\n        }\n    });\n}\n/**\n * The post utility\n *\n * @private\n * @function Highcharts.post\n *\n * @param {string} url\n * Post URL\n *\n * @param {Object} data\n * Post data\n *\n * @param {RequestInit} [fetchOptions]\n * Additional attributes for the post request\n */\n/**\n *\n */\nfunction post(url, data, fetchOptions) {\n    const formData = new win.FormData();\n    // Add the data\n    objectEach(data, function (val, name) {\n        formData.append(name, val);\n    });\n    formData.append('b64', 'true');\n    const { filename, type } = data;\n    return win.fetch(url, {\n        method: 'POST',\n        body: formData,\n        ...fetchOptions\n    }).then((res) => {\n        if (res.ok) {\n            res.text().then((text) => {\n                const link = document.createElement('a');\n                link.href = `data:${type};base64,${text}`;\n                link.download = filename;\n                link.click();\n                discardElement(link);\n            });\n        }\n    });\n}\n/* *\n *\n *  Default Export\n *\n * */\nconst HttpUtilities = {\n    ajax,\n    getJSON,\n    post\n};\n/* harmony default export */ const Core_HttpUtilities = (HttpUtilities);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * @interface Highcharts.AjaxSettingsObject\n */ /**\n* The payload to send.\n*\n* @name Highcharts.AjaxSettingsObject#data\n* @type {string|Highcharts.Dictionary<any>|undefined}\n*/ /**\n* The data type expected.\n* @name Highcharts.AjaxSettingsObject#dataType\n* @type {\"json\"|\"xml\"|\"text\"|\"octet\"|undefined}\n*/ /**\n* Function to call on error.\n* @name Highcharts.AjaxSettingsObject#error\n* @type {Function|undefined}\n*/ /**\n* The headers; keyed on header name.\n* @name Highcharts.AjaxSettingsObject#headers\n* @type {Highcharts.Dictionary<string>|undefined}\n*/ /**\n* Function to call on success.\n* @name Highcharts.AjaxSettingsObject#success\n* @type {Function|undefined}\n*/ /**\n* The HTTP method to use. For example GET or POST.\n* @name Highcharts.AjaxSettingsObject#type\n* @type {string|undefined}\n*/ /**\n* The URL to call.\n* @name Highcharts.AjaxSettingsObject#url\n* @type {string}\n*/\n(''); // Keeps doclets above in JS file\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Axis\"],\"commonjs\":[\"highcharts\",\"Axis\"],\"commonjs2\":[\"highcharts\",\"Axis\"],\"root\":[\"Highcharts\",\"Axis\"]}\nvar highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_ = __webpack_require__(532);\nvar highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default = /*#__PURE__*/__webpack_require__.n(highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Chart\"],\"commonjs\":[\"highcharts\",\"Chart\"],\"commonjs2\":[\"highcharts\",\"Chart\"],\"root\":[\"Highcharts\",\"Chart\"]}\nvar highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_ = __webpack_require__(960);\nvar highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default = /*#__PURE__*/__webpack_require__.n(highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"Point\"],\"commonjs\":[\"highcharts\",\"Point\"],\"commonjs2\":[\"highcharts\",\"Point\"],\"root\":[\"Highcharts\",\"Point\"]}\nvar highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_ = __webpack_require__(260);\nvar highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default = /*#__PURE__*/__webpack_require__.n(highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_);\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Extensions/Data.js\n/* *\n *\n *  Data module\n *\n *  (c) 2012-2025 Torstein Honsi\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\n\nconst { getOptions } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { doc } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n\nconst { ajax: Data_ajax } = Core_HttpUtilities;\n\n\nconst { seriesTypes } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default());\n\nconst { addEvent, defined, extend, fireEvent, isNumber, merge, objectEach: Data_objectEach, pick, splat } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Functions\n *\n * */\n/**\n * @private\n */\nfunction getFreeIndexes(numberOfColumns, seriesBuilders) {\n    const freeIndexes = [], freeIndexValues = [];\n    let s, i, referencedIndexes;\n    // Add all columns as free\n    for (i = 0; i < numberOfColumns; i = i + 1) {\n        freeIndexes.push(true);\n    }\n    // Loop all defined builders and remove their referenced columns\n    for (s = 0; s < seriesBuilders.length; s = s + 1) {\n        referencedIndexes = seriesBuilders[s].getReferencedColumnIndexes();\n        for (i = 0; i < referencedIndexes.length; i = i + 1) {\n            freeIndexes[referencedIndexes[i]] = false;\n        }\n    }\n    // Collect the values for the free indexes\n    for (i = 0; i < freeIndexes.length; i = i + 1) {\n        if (freeIndexes[i]) {\n            freeIndexValues.push(i);\n        }\n    }\n    return freeIndexValues;\n}\n/**\n *\n */\nfunction hasURLOption(options) {\n    return Boolean(options &&\n        (options.rowsURL || options.csvURL || options.columnsURL));\n}\n/* *\n *\n *  Class\n *\n * */\n/**\n * The Data class\n *\n * @requires modules/data\n *\n * @class\n * @name Highcharts.Data\n *\n * @param {Highcharts.DataOptions} dataOptions\n *\n * @param {Highcharts.Options} [chartOptions]\n *\n * @param {Highcharts.Chart} [chart]\n */\nclass Data {\n    /* *\n     *\n     *  Static Properties\n     *\n     * */\n    /**\n     * Creates a data object to parse data for a chart.\n     *\n     * @function Highcharts.data\n     */\n    static data(dataOptions, chartOptions = {}, chart) {\n        return new Data(dataOptions, chartOptions, chart);\n    }\n    /**\n     * Reorganize rows into columns.\n     *\n     * @function Highcharts.Data.rowsToColumns\n     */\n    static rowsToColumns(rows) {\n        let row, rowsLength, col, colsLength, columns;\n        if (rows) {\n            columns = [];\n            rowsLength = rows.length;\n            for (row = 0; row < rowsLength; row++) {\n                colsLength = rows[row].length;\n                for (col = 0; col < colsLength; col++) {\n                    if (!columns[col]) {\n                        columns[col] = [];\n                    }\n                    columns[col][row] = rows[row][col];\n                }\n            }\n        }\n        return columns;\n    }\n    /* *\n     *\n     *  Constructors\n     *\n     * */\n    constructor(dataOptions, chartOptions = {}, chart) {\n        this.rowsToColumns = Data.rowsToColumns; // Backwards compatibility\n        /**\n         * A collection of available date formats, extendable from the outside to\n         * support custom date formats.\n         *\n         * @name Highcharts.Data#dateFormats\n         * @type {Highcharts.Dictionary<Highcharts.DataDateFormatObject>}\n         */\n        this.dateFormats = {\n            'YYYY/mm/dd': {\n                regex: /^(\\d{4})[\\-\\/\\.](\\d{1,2})[\\-\\/\\.](\\d{1,2})$/,\n                parser: function (match) {\n                    return (match ?\n                        Date.UTC(+match[1], +match[2] - 1, +match[3]) :\n                        NaN);\n                }\n            },\n            'dd/mm/YYYY': {\n                regex: /^(\\d{1,2})[\\-\\/\\.](\\d{1,2})[\\-\\/\\.](\\d{4})$/,\n                parser: function (match) {\n                    return (match ?\n                        Date.UTC(+match[3], +match[2] - 1, +match[1]) :\n                        NaN);\n                },\n                alternative: 'mm/dd/YYYY' // Different format with the same regex\n            },\n            'mm/dd/YYYY': {\n                regex: /^(\\d{1,2})[\\-\\/\\.](\\d{1,2})[\\-\\/\\.](\\d{4})$/,\n                parser: function (match) {\n                    return (match ?\n                        Date.UTC(+match[3], +match[1] - 1, +match[2]) :\n                        NaN);\n                }\n            },\n            'dd/mm/YY': {\n                regex: /^(\\d{1,2})[\\-\\/\\.](\\d{1,2})[\\-\\/\\.](\\d{2})$/,\n                parser: function (match) {\n                    if (!match) {\n                        return NaN;\n                    }\n                    const d = new Date();\n                    let year = +match[3];\n                    if (year > (d.getFullYear() - 2000)) {\n                        year += 1900;\n                    }\n                    else {\n                        year += 2000;\n                    }\n                    return Date.UTC(year, +match[2] - 1, +match[1]);\n                },\n                alternative: 'mm/dd/YY' // Different format with the same regex\n            },\n            'mm/dd/YY': {\n                regex: /^(\\d{1,2})[\\-\\/\\.](\\d{1,2})[\\-\\/\\.](\\d{2})$/,\n                parser: function (match) {\n                    return (match ?\n                        Date.UTC(+match[3] + 2000, +match[1] - 1, +match[2]) :\n                        NaN);\n                }\n            }\n        };\n        this.chart = chart;\n        this.chartOptions = chartOptions;\n        this.options = dataOptions;\n        this.rawColumns = [];\n        this.init(dataOptions, chartOptions, chart);\n    }\n    /* *\n     *\n     *  Functions\n     *\n     * */\n    /**\n     * Initialize the Data object with the given options\n     *\n     * @private\n     * @function Highcharts.Data#init\n     */\n    init(dataOptions, chartOptions, chart) {\n        let decimalPoint = dataOptions.decimalPoint, hasData;\n        if (chartOptions) {\n            this.chartOptions = chartOptions;\n        }\n        if (chart) {\n            this.chart = chart;\n        }\n        if (decimalPoint !== '.' && decimalPoint !== ',') {\n            decimalPoint = void 0;\n        }\n        this.options = dataOptions;\n        this.columns = (dataOptions.columns ||\n            this.rowsToColumns(dataOptions.rows) ||\n            []);\n        this.firstRowAsNames = pick(dataOptions.firstRowAsNames, this.firstRowAsNames, true);\n        this.decimalRegex = (decimalPoint &&\n            new RegExp('^(-?[0-9]+)' + decimalPoint + '([0-9]+)$'));\n        // Always stop old polling when we have new options\n        if (this.liveDataTimeout !== void 0) {\n            clearTimeout(this.liveDataTimeout);\n        }\n        // This is a two-dimensional array holding the raw, trimmed string\n        // values with the same organisation as the columns array. It makes it\n        // possible for example to revert from interpreted timestamps to\n        // string-based categories.\n        this.rawColumns = [];\n        // No need to parse or interpret anything\n        if (this.columns.length) {\n            this.dataFound();\n            hasData = !hasURLOption(dataOptions);\n        }\n        if (!hasData) {\n            // Fetch live data\n            hasData = this.fetchLiveData();\n        }\n        if (!hasData) {\n            // Parse a CSV string if options.csv is given. The parseCSV function\n            // returns a columns array, if it has no length, we have no data\n            hasData = Boolean(this.parseCSV().length);\n        }\n        if (!hasData) {\n            // Parse a HTML table if options.table is given\n            hasData = Boolean(this.parseTable().length);\n        }\n        if (!hasData) {\n            // Parse a Google Spreadsheet\n            hasData = this.parseGoogleSpreadsheet();\n        }\n        if (!hasData && dataOptions.afterComplete) {\n            dataOptions.afterComplete(this);\n        }\n    }\n    /**\n     * Get the column distribution. For example, a line series takes a single\n     * column for Y values. A range series takes two columns for low and high\n     * values respectively, and an OHLC series takes four columns.\n     *\n     * @function Highcharts.Data#getColumnDistribution\n     */\n    getColumnDistribution() {\n        const chartOptions = this.chartOptions, options = this.options, xColumns = [], getValueCount = function (type = 'line') {\n            return (seriesTypes[type].prototype.pointArrayMap || [0]).length;\n        }, getPointArrayMap = function (type = 'line') {\n            return seriesTypes[type].prototype.pointArrayMap;\n        }, globalType = chartOptions?.chart?.type, individualCounts = [], seriesBuilders = [], \n        // If no series mapping is defined, check if the series array is\n        // defined with types.\n        seriesMapping = (options?.seriesMapping ||\n            chartOptions?.series?.map(function () {\n                return { x: 0 };\n            }) ||\n            []);\n        let seriesIndex = 0;\n        (chartOptions?.series || []).forEach((series) => {\n            individualCounts.push(getValueCount(series.type || globalType));\n        });\n        // Collect the x-column indexes from seriesMapping\n        seriesMapping.forEach((mapping) => {\n            xColumns.push(mapping.x || 0);\n        });\n        // If there are no defined series with x-columns, use the first column\n        // as x column\n        if (xColumns.length === 0) {\n            xColumns.push(0);\n        }\n        // Loop all seriesMappings and constructs SeriesBuilders from\n        // the mapping options.\n        seriesMapping.forEach((mapping) => {\n            const builder = new SeriesBuilder(), numberOfValueColumnsNeeded = individualCounts[seriesIndex] ||\n                getValueCount(globalType), seriesArr = chartOptions?.series ?? [], series = seriesArr[seriesIndex] ?? {}, defaultPointArrayMap = getPointArrayMap(series.type || globalType), pointArrayMap = defaultPointArrayMap ?? ['y'];\n            if (\n            // User-defined x.mapping\n            defined(mapping.x) ||\n                // All non cartesian don't need 'x'\n                series.isCartesian ||\n                // Except pie series:\n                !defaultPointArrayMap) {\n                // Add an x reader from the x property or from an undefined\n                // column if the property is not set. It will then be auto\n                // populated later.\n                builder.addColumnReader(mapping.x, 'x');\n            }\n            // Add all column mappings\n            Data_objectEach(mapping, function (val, name) {\n                if (name !== 'x') {\n                    builder.addColumnReader(val, name);\n                }\n            });\n            // Add missing columns\n            for (let i = 0; i < numberOfValueColumnsNeeded; i++) {\n                if (!builder.hasReader(pointArrayMap[i])) {\n                    // Create and add a column reader for the next free column\n                    // index\n                    builder.addColumnReader(void 0, pointArrayMap[i]);\n                }\n            }\n            seriesBuilders.push(builder);\n            seriesIndex++;\n        });\n        let globalPointArrayMap = getPointArrayMap(globalType);\n        if (typeof globalPointArrayMap === 'undefined') {\n            globalPointArrayMap = ['y'];\n        }\n        this.valueCount = {\n            global: getValueCount(globalType),\n            xColumns: xColumns,\n            individual: individualCounts,\n            seriesBuilders: seriesBuilders,\n            globalPointArrayMap: globalPointArrayMap\n        };\n    }\n    /**\n     * When the data is parsed into columns, either by CSV, table, GS or direct\n     * input, continue with other operations.\n     *\n     * @private\n     * @function Highcharts.Data#dataFound\n     */\n    dataFound() {\n        if (this.options.switchRowsAndColumns) {\n            this.columns = this.rowsToColumns(this.columns);\n        }\n        // Interpret the info about series and columns\n        this.getColumnDistribution();\n        // Interpret the values into right types\n        this.parseTypes();\n        // Handle columns if a handleColumns callback is given\n        if (this.parsed() !== false) {\n            // Complete if a complete callback is given\n            this.complete();\n        }\n    }\n    /**\n     * Parse a CSV input string\n     *\n     * @function Highcharts.Data#parseCSV\n     */\n    parseCSV(inOptions) {\n        const self = this, columns = this.columns = [], options = inOptions || this.options, startColumn = options.startColumn || 0, endColumn = options.endColumn || Number.MAX_VALUE, dataTypes = [], \n        // We count potential delimiters in the prepass, and use the\n        // result as the basis of half-intelligent guesses.\n        potDelimiters = {\n            ',': 0,\n            ';': 0,\n            '\\t': 0\n        };\n        let csv = options.csv, startRow = options.startRow || 0, endRow = options.endRow || Number.MAX_VALUE, itemDelimiter, lines, rowIt = 0;\n        /*\n            This implementation is quite verbose. It will be shortened once\n            it's stable and passes all the test.\n\n            It's also not written with speed in mind, instead everything is\n            very segregated, and there a several redundant loops.\n            This is to make it easier to stabilize the code initially.\n\n            We do a pre-pass on the first 4 rows to make some intelligent\n            guesses on the set. Guessed delimiters are in this pass counted.\n\n            Auto detecting delimiters\n                - If we meet a quoted string, the next symbol afterwards\n                  (that's not \\s, \\t) is the delimiter\n                - If we meet a date, the next symbol afterwards is the delimiter\n\n            Date formats\n                - If we meet a column with date formats, check all of them to\n                  see if one of the potential months crossing 12. If it does,\n                  we now know the format\n\n            It would make things easier to guess the delimiter before\n            doing the actual parsing.\n\n            General rules:\n                - Quoting is allowed, e.g: \"Col 1\",123,321\n                - Quoting is optional, e.g.: Col1,123,321\n                - Double quoting is escaping, e.g. \"Col \"\"Hello world\"\"\",123\n                - Spaces are considered part of the data: Col1 ,123\n                - New line is always the row delimiter\n                - Potential column delimiters are , ; \\t\n                - First row may optionally contain headers\n                - The last row may or may not have a row delimiter\n                - Comments are optionally supported, in which case the comment\n                  must start at the first column, and the rest of the line will\n                  be ignored\n        */\n        /**\n         * Parse a single row.\n         * @private\n         */\n        function parseRow(columnStr, rowNumber, noAdd, callbacks) {\n            let i = 0, c = '', cl = '', cn = '', token = '', actualColumn = 0, column = 0;\n            /**\n             * @private\n             */\n            function read(j) {\n                c = columnStr[j];\n                cl = columnStr[j - 1];\n                cn = columnStr[j + 1];\n            }\n            /**\n             * @private\n             */\n            function pushType(type) {\n                if (dataTypes.length < column + 1) {\n                    dataTypes.push([type]);\n                }\n                if (dataTypes[column][dataTypes[column].length - 1] !== type) {\n                    dataTypes[column].push(type);\n                }\n            }\n            /**\n             * @private\n             */\n            function push() {\n                if (startColumn > actualColumn || actualColumn > endColumn) {\n                    // Skip this column, but increment the column count (#7272)\n                    ++actualColumn;\n                    token = '';\n                    return;\n                }\n                if (!options.columnTypes) {\n                    if (!isNaN(parseFloat(token)) && isFinite(token)) {\n                        token = parseFloat(token);\n                        pushType('number');\n                    }\n                    else if (!isNaN(Date.parse(token))) {\n                        token = token.replace(/\\//g, '-');\n                        pushType('date');\n                    }\n                    else {\n                        pushType('string');\n                    }\n                }\n                if (columns.length < column + 1) {\n                    columns.push([]);\n                }\n                if (!noAdd) {\n                    // Don't push - if there's a varying amount of columns\n                    // for each row, pushing will skew everything down n slots\n                    columns[column][rowNumber] = token;\n                }\n                token = '';\n                ++column;\n                ++actualColumn;\n            }\n            if (!columnStr.trim().length) {\n                return;\n            }\n            if (columnStr.trim()[0] === '#') {\n                return;\n            }\n            for (; i < columnStr.length; i++) {\n                read(i);\n                if (c === '\"') {\n                    read(++i);\n                    while (i < columnStr.length) {\n                        if (c === '\"' && cl !== '\"' && cn !== '\"') {\n                            break;\n                        }\n                        if (c !== '\"' || (c === '\"' && cl !== '\"')) {\n                            token += c;\n                        }\n                        read(++i);\n                    }\n                    // Perform \"plugin\" handling\n                }\n                else if (callbacks?.[c]) {\n                    if (callbacks[c](c, token)) {\n                        push();\n                    }\n                    // Delimiter - push current token\n                }\n                else if (c === itemDelimiter) {\n                    push();\n                    // Actual column data\n                }\n                else {\n                    token += c;\n                }\n            }\n            push();\n        }\n        /**\n         * Attempt to guess the delimiter. We do a separate parse pass here\n         * because we need to count potential delimiters softly without making\n         * any assumptions.\n         * @private\n         */\n        function guessDelimiter(lines) {\n            let points = 0, commas = 0, guessed = false;\n            lines.some(function (columnStr, i) {\n                let inStr = false, c, cn, cl, token = '';\n                // We should be able to detect dateformats within 13 rows\n                if (i > 13) {\n                    return true;\n                }\n                for (let j = 0; j < columnStr.length; j++) {\n                    c = columnStr[j];\n                    cn = columnStr[j + 1];\n                    cl = columnStr[j - 1];\n                    if (c === '#') {\n                        // Skip the rest of the line - it's a comment\n                        return;\n                    }\n                    if (c === '\"') {\n                        if (inStr) {\n                            if (cl !== '\"' && cn !== '\"') {\n                                while (cn === ' ' && j < columnStr.length) {\n                                    cn = columnStr[++j];\n                                }\n                                // After parsing a string, the next non-blank\n                                // should be a delimiter if the CSV is properly\n                                // formed.\n                                if (typeof potDelimiters[cn] !== 'undefined') {\n                                    potDelimiters[cn]++;\n                                }\n                                inStr = false;\n                            }\n                        }\n                        else {\n                            inStr = true;\n                        }\n                    }\n                    else if (typeof potDelimiters[c] !== 'undefined') {\n                        token = token.trim();\n                        if (!isNaN(Date.parse(token))) {\n                            potDelimiters[c]++;\n                        }\n                        else if (isNaN(token) ||\n                            !isFinite(token)) {\n                            potDelimiters[c]++;\n                        }\n                        token = '';\n                    }\n                    else {\n                        token += c;\n                    }\n                    if (c === ',') {\n                        commas++;\n                    }\n                    if (c === '.') {\n                        points++;\n                    }\n                }\n            });\n            // Count the potential delimiters.\n            // This could be improved by checking if the number of delimiters\n            // equals the number of columns - 1\n            if (potDelimiters[';'] > potDelimiters[',']) {\n                guessed = ';';\n            }\n            else if (potDelimiters[','] > potDelimiters[';']) {\n                guessed = ',';\n            }\n            else {\n                // No good guess could be made..\n                guessed = ',';\n            }\n            // Try to deduce the decimal point if it's not explicitly set.\n            // If both commas or points is > 0 there is likely an issue\n            if (!options.decimalPoint) {\n                if (points > commas) {\n                    options.decimalPoint = '.';\n                }\n                else {\n                    options.decimalPoint = ',';\n                }\n                // Apply a new decimal regex based on the presumed decimal sep.\n                self.decimalRegex = new RegExp('^(-?[0-9]+)' +\n                    options.decimalPoint +\n                    '([0-9]+)$');\n            }\n            return guessed;\n        }\n        /**\n         * Tries to guess the date format\n         *  - Check if either month candidate exceeds 12\n         *  - Check if year is missing (use current year)\n         *  - Check if a shortened year format is used (e.g. 1/1/99)\n         *  - If no guess can be made, the user must be prompted\n         * data is the data to deduce a format based on\n         * @private\n         */\n        function deduceDateFormat(data, limit) {\n            const format = 'YYYY/mm/dd', stable = [], max = [];\n            let thing, guessedFormat = [], calculatedFormat, i = 0, madeDeduction = false, j;\n            if (!limit || limit > data.length) {\n                limit = data.length;\n            }\n            for (; i < limit; i++) {\n                if (typeof data[i] !== 'undefined' &&\n                    data[i]?.length) {\n                    thing = data[i]\n                        .trim()\n                        .replace(/\\//g, ' ')\n                        .replace(/\\-/g, ' ')\n                        .replace(/\\./g, ' ')\n                        .split(' ');\n                    guessedFormat = [\n                        '',\n                        '',\n                        ''\n                    ];\n                    for (j = 0; j < thing.length; j++) {\n                        if (j < guessedFormat.length) {\n                            thing[j] = parseInt(thing[j], 10);\n                            if (thing[j]) {\n                                max[j] = (!max[j] || max[j] < thing[j]) ?\n                                    thing[j] :\n                                    max[j];\n                                if (typeof stable[j] !== 'undefined') {\n                                    if (stable[j] !== thing[j]) {\n                                        stable[j] = false;\n                                    }\n                                }\n                                else {\n                                    stable[j] = thing[j];\n                                }\n                                if (thing[j] > 31) {\n                                    if (thing[j] < 100) {\n                                        guessedFormat[j] = 'YY';\n                                    }\n                                    else {\n                                        guessedFormat[j] = 'YYYY';\n                                    }\n                                }\n                                else if (thing[j] > 12 &&\n                                    thing[j] <= 31) {\n                                    guessedFormat[j] = 'dd';\n                                    madeDeduction = true;\n                                }\n                                else if (!guessedFormat[j].length) {\n                                    guessedFormat[j] = 'mm';\n                                }\n                            }\n                        }\n                    }\n                }\n            }\n            if (madeDeduction) {\n                // This handles a few edge cases with hard to guess dates\n                for (j = 0; j < stable.length; j++) {\n                    if (stable[j] !== false) {\n                        if (max[j] > 12 &&\n                            guessedFormat[j] !== 'YY' &&\n                            guessedFormat[j] !== 'YYYY') {\n                            guessedFormat[j] = 'YY';\n                        }\n                    }\n                    else if (max[j] > 12 && guessedFormat[j] === 'mm') {\n                        guessedFormat[j] = 'dd';\n                    }\n                }\n                // If the middle one is dd, and the last one is dd,\n                // the last should likely be year.\n                if (guessedFormat.length === 3 &&\n                    guessedFormat[1] === 'dd' &&\n                    guessedFormat[2] === 'dd') {\n                    guessedFormat[2] = 'YY';\n                }\n                calculatedFormat = guessedFormat.join('/');\n                // If the calculated format is not valid, we need to present an\n                // error.\n                if (!(options.dateFormats || self.dateFormats)[calculatedFormat]) {\n                    // This should emit an event instead\n                    fireEvent(self, 'deduceDateFailed');\n                    return format;\n                }\n                return calculatedFormat;\n            }\n            return format;\n        }\n        if (csv && options.beforeParse) {\n            csv = options.beforeParse.call(this, csv);\n        }\n        if (csv) {\n            lines = csv\n                .replace(/\\r\\n/g, '\\n') // Unix\n                .replace(/\\r/g, '\\n') // Mac\n                .split(options.lineDelimiter || '\\n');\n            if (!startRow || startRow < 0) {\n                startRow = 0;\n            }\n            if (!endRow || endRow >= lines.length) {\n                endRow = lines.length - 1;\n            }\n            if (options.itemDelimiter) {\n                itemDelimiter = options.itemDelimiter;\n            }\n            else {\n                itemDelimiter = guessDelimiter(lines);\n            }\n            let offset = 0;\n            for (rowIt = startRow; rowIt <= endRow; rowIt++) {\n                if (lines[rowIt][0] === '#') {\n                    offset++;\n                }\n                else {\n                    parseRow(lines[rowIt], rowIt - startRow - offset);\n                }\n            }\n            if ((!options.columnTypes || options.columnTypes.length === 0) &&\n                dataTypes.length &&\n                dataTypes[0].length &&\n                dataTypes[0][1] === 'date' &&\n                !options.dateFormat) {\n                options.dateFormat = deduceDateFormat(columns[0]);\n            }\n            /// lines.forEach(function (line, rowNo) {\n            //    let trimmed = self.trim(line),\n            //        isComment = trimmed.indexOf('#') === 0,\n            //        isBlank = trimmed === '',\n            //        items;\n            //    if (\n            //        rowNo >= startRow &&\n            //        rowNo <= endRow &&\n            //        !isComment && !isBlank\n            //    ) {\n            //        items = line.split(itemDelimiter);\n            //        items.forEach(function (item, colNo) {\n            //            if (colNo >= startColumn && colNo <= endColumn) {\n            //                if (!columns[colNo - startColumn]) {\n            //                    columns[colNo - startColumn] = [];\n            //                }\n            //                columns[colNo - startColumn][activeRowNo] = item;\n            //            }\n            //        });\n            //        activeRowNo += 1;\n            //    }\n            // });\n            //\n            this.dataFound();\n        }\n        return columns;\n    }\n    /**\n     * Parse a HTML table\n     *\n     * @function Highcharts.Data#parseTable\n     */\n    parseTable() {\n        const options = this.options, columns = this.columns || [], startRow = options.startRow || 0, endRow = options.endRow || Number.MAX_VALUE, startColumn = options.startColumn || 0, endColumn = options.endColumn || Number.MAX_VALUE;\n        if (options.table) {\n            let table = options.table;\n            if (typeof table === 'string') {\n                table = doc.getElementById(table);\n            }\n            [].forEach.call(table.getElementsByTagName('tr'), (tr, rowNo) => {\n                if (rowNo >= startRow && rowNo <= endRow) {\n                    [].forEach.call(tr.children, (item, colNo) => {\n                        const row = columns[colNo - startColumn];\n                        let i = 1;\n                        if ((item.tagName === 'TD' ||\n                            item.tagName === 'TH') &&\n                            colNo >= startColumn &&\n                            colNo <= endColumn) {\n                            if (!columns[colNo - startColumn]) {\n                                columns[colNo - startColumn] = [];\n                            }\n                            columns[colNo - startColumn][rowNo - startRow] = item.innerHTML;\n                            // Loop over all previous indices and make sure\n                            // they are nulls, not undefined.\n                            while (rowNo - startRow >= i &&\n                                row[rowNo - startRow - i] === void 0) {\n                                row[rowNo - startRow - i] = null;\n                                i++;\n                            }\n                        }\n                    });\n                }\n            });\n            this.dataFound(); // Continue\n        }\n        return columns;\n    }\n    /**\n     * Fetch or refetch live data\n     *\n     * @function Highcharts.Data#fetchLiveData\n     *\n     * @return {boolean}\n     *         The URLs that were tried can be found in the options\n     */\n    fetchLiveData() {\n        const data = this, chart = this.chart, options = this.options, maxRetries = 3, pollingEnabled = options.enablePolling, originalOptions = merge(options);\n        let currentRetries = 0, updateIntervalMs = (options.dataRefreshRate || 2) * 1000;\n        if (!hasURLOption(options)) {\n            return false;\n        }\n        // Do not allow polling more than once a second\n        if (updateIntervalMs < 1000) {\n            updateIntervalMs = 1000;\n        }\n        delete options.csvURL;\n        delete options.rowsURL;\n        delete options.columnsURL;\n        /**\n         * @private\n         */\n        function performFetch(initialFetch) {\n            /**\n             * Helper function for doing the data fetch + polling.\n             * @private\n             */\n            function request(url, done, tp) {\n                if (!url ||\n                    !/^(http|\\/|\\.\\/|\\.\\.\\/)/.test(url)) {\n                    if (url && options.error) {\n                        options.error('Invalid URL');\n                    }\n                    return false;\n                }\n                if (initialFetch) {\n                    clearTimeout(data.liveDataTimeout);\n                    chart.liveDataURL = url;\n                }\n                /**\n                 * @private\n                 */\n                function poll() {\n                    // Poll\n                    if (pollingEnabled && chart.liveDataURL === url) {\n                        // We need to stop doing this if the URL has changed\n                        data.liveDataTimeout =\n                            setTimeout(performFetch, updateIntervalMs);\n                    }\n                }\n                Data_ajax({\n                    url: url,\n                    dataType: tp || 'json',\n                    success: function (res) {\n                        if (chart?.series) {\n                            done(res);\n                        }\n                        poll();\n                    },\n                    error: function (xhr, text) {\n                        if (++currentRetries < maxRetries) {\n                            poll();\n                        }\n                        return options.error?.(text, xhr);\n                    }\n                });\n                return true;\n            }\n            if (!request(originalOptions.csvURL, function (res) {\n                chart.update({\n                    data: {\n                        csv: res\n                    }\n                });\n            }, 'text')) {\n                if (!request(originalOptions.rowsURL, function (res) {\n                    chart.update({\n                        data: {\n                            rows: res\n                        }\n                    });\n                })) {\n                    request(originalOptions.columnsURL, function (res) {\n                        chart.update({\n                            data: {\n                                columns: res\n                            }\n                        });\n                    });\n                }\n            }\n        }\n        performFetch(true);\n        return hasURLOption(options);\n    }\n    /**\n     * Parse a Google spreadsheet.\n     *\n     * @function Highcharts.Data#parseGoogleSpreadsheet\n     *\n     * @return {boolean}\n     *         Always returns false, because it is an intermediate fetch.\n     */\n    parseGoogleSpreadsheet() {\n        const data = this, options = this.options, googleSpreadsheetKey = options.googleSpreadsheetKey, chart = this.chart, refreshRate = Math.max((options.dataRefreshRate || 2) * 1000, 4000);\n        /**\n         * Form the `values` field after range settings, unless the\n         * googleSpreadsheetRange option is set.\n         */\n        const getRange = () => {\n            if (options.googleSpreadsheetRange) {\n                return options.googleSpreadsheetRange;\n            }\n            const alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';\n            const start = (alphabet.charAt(options.startColumn || 0) || 'A') +\n                ((options.startRow || 0) + 1);\n            let end = alphabet.charAt(pick(options.endColumn, -1)) || 'ZZ';\n            if (defined(options.endRow)) {\n                end += options.endRow + 1;\n            }\n            return `${start}:${end}`;\n        };\n        /**\n         * Fetch the actual spreadsheet using XMLHttpRequest.\n         * @private\n         */\n        function fetchSheet(fn) {\n            const url = [\n                'https://sheets.googleapis.com/v4/spreadsheets',\n                googleSpreadsheetKey,\n                'values',\n                getRange(),\n                '?alt=json&' +\n                    'majorDimension=COLUMNS&' +\n                    'valueRenderOption=UNFORMATTED_VALUE&' +\n                    'dateTimeRenderOption=FORMATTED_STRING&' +\n                    'key=' + options.googleAPIKey\n            ].join('/');\n            Data_ajax({\n                url,\n                dataType: 'json',\n                success: function (json) {\n                    fn(json);\n                    if (options.enablePolling) {\n                        data.liveDataTimeout = setTimeout(function () {\n                            fetchSheet(fn);\n                        }, refreshRate);\n                    }\n                },\n                error: function (xhr, text) {\n                    return options.error?.(text, xhr);\n                }\n            });\n        }\n        if (googleSpreadsheetKey) {\n            delete options.googleSpreadsheetKey;\n            fetchSheet(function (json) {\n                // Prepare the data from the spreadsheet\n                const columns = json.values;\n                if (!columns || columns.length === 0) {\n                    return false;\n                }\n                // Find the maximum row count in order to extend shorter columns\n                const rowCount = columns.reduce((rowCount, column) => Math.max(rowCount, column.length), 0);\n                // Insert null for empty spreadsheet cells (#5298)\n                columns.forEach((column) => {\n                    for (let i = 0; i < rowCount; i++) {\n                        if (typeof column[i] === 'undefined') {\n                            column[i] = null;\n                        }\n                    }\n                });\n                if (chart?.series) {\n                    chart.update({\n                        data: {\n                            columns: columns\n                        }\n                    });\n                }\n                else { // #8245\n                    data.columns = columns;\n                    data.dataFound();\n                }\n            });\n        }\n        // This is an intermediate fetch, so always return false.\n        return false;\n    }\n    /**\n     * Trim a string from whitespaces.\n     *\n     * @function Highcharts.Data#trim\n     *\n     * @param {string} str\n     *        String to trim\n     *\n     * @param {boolean} [inside=false]\n     *        Remove all spaces between numbers.\n     *\n     * @return {string}\n     *         Trimed string\n     */\n    trim(str, inside) {\n        if (typeof str === 'string') {\n            str = str.replace(/^\\s+|\\s+$/g, '');\n            // Clear white space inside the string, like thousands separators\n            if (inside && /[\\d\\s]+/.test(str)) {\n                str = str.replace(/\\s/g, '');\n            }\n            if (this.decimalRegex) {\n                str = str.replace(this.decimalRegex, '$1.$2');\n            }\n        }\n        return str;\n    }\n    /**\n     * Parse numeric cells in to number types and date types in to true dates.\n     *\n     * @function Highcharts.Data#parseTypes\n     */\n    parseTypes() {\n        const columns = this.columns || [];\n        let col = columns.length;\n        while (col--) {\n            this.parseColumn(columns[col], col);\n        }\n    }\n    /**\n     * Parse a single column. Set properties like .isDatetime and .isNumeric.\n     *\n     * @function Highcharts.Data#parseColumn\n     *\n     * @param {Array<Highcharts.DataValueType>} column\n     *        Column to parse\n     *\n     * @param {number} col\n     *        Column index\n     */\n    parseColumn(column, col) {\n        const rawColumns = this.rawColumns, columns = this.columns = this.columns || [], firstRowAsNames = this.firstRowAsNames, isXColumn = this.valueCount?.xColumns.indexOf(col) !== -1, backup = [], chartOptions = this.chartOptions, columnTypes = this.options.columnTypes || [], columnType = columnTypes[col], forceCategory = (isXColumn &&\n            (chartOptions?.xAxis &&\n                splat(chartOptions.xAxis)[0].type === 'category')) || columnType === 'string', columnHasName = defined(column.name);\n        let row = column.length, val, floatVal, trimVal, trimInsideVal, dateVal, diff, descending;\n        if (!rawColumns[col]) {\n            rawColumns[col] = [];\n        }\n        while (row--) {\n            val = backup[row] || column[row];\n            trimVal = this.trim(val);\n            trimInsideVal = this.trim(val, true);\n            floatVal = parseFloat(trimInsideVal);\n            // Set it the first time\n            if (typeof rawColumns[col][row] === 'undefined') {\n                rawColumns[col][row] = trimVal;\n            }\n            // Disable number or date parsing by setting the X axis type to\n            // category\n            if (forceCategory ||\n                (row === 0 && firstRowAsNames && !columnHasName)) {\n                column[row] = '' + trimVal;\n            }\n            else if (+trimInsideVal === floatVal) { // Is numeric\n                column[row] = floatVal;\n                // If the number is greater than milliseconds in a year, assume\n                // datetime\n                if (floatVal > 365 * 24 * 3600 * 1000 &&\n                    columnType !== 'float') {\n                    column.isDatetime = true;\n                }\n                else {\n                    column.isNumeric = true;\n                }\n                if (typeof column[row + 1] !== 'undefined') {\n                    descending = floatVal > column[row + 1];\n                }\n                // String, continue to determine if it is a date string or really a\n                // string\n            }\n            else {\n                if (trimVal?.length) {\n                    dateVal = this.parseDate(val);\n                }\n                // Only allow parsing of dates if this column is an x-column\n                if (isXColumn && isNumber(dateVal) && columnType !== 'float') {\n                    backup[row] = val;\n                    column[row] = dateVal;\n                    column.isDatetime = true;\n                    // Check if the dates are uniformly descending or ascending.\n                    // If they are not, chances are that they are a different\n                    // time format, so check for alternative.\n                    if (typeof column[row + 1] !== 'undefined') {\n                        diff = dateVal > column[row + 1];\n                        if (diff !== descending &&\n                            typeof descending !== 'undefined') {\n                            if (this.alternativeFormat) {\n                                this.dateFormat = this.alternativeFormat;\n                                row = column.length;\n                                this.alternativeFormat =\n                                    this.dateFormats[this.dateFormat]\n                                        .alternative;\n                            }\n                            else {\n                                column.unsorted = true;\n                            }\n                        }\n                        descending = diff;\n                    }\n                }\n                else { // String\n                    column[row] = trimVal === '' ? null : trimVal;\n                    if (row !== 0 &&\n                        (column.isDatetime ||\n                            column.isNumeric)) {\n                        column.mixed = true;\n                    }\n                }\n            }\n        }\n        // If strings are intermixed with numbers or dates in a parsed column,\n        // it is an indication that parsing went wrong or the data was not\n        // intended to display as numbers or dates and parsing is too\n        // aggressive. Fall back to categories. Demonstrated in the\n        // highcharts/demo/column-drilldown sample.\n        if (isXColumn && column.mixed) {\n            columns[col] = rawColumns[col];\n        }\n        // If the 0 column is date or number and descending, reverse all\n        // columns.\n        if (isXColumn && descending && this.options.sort) {\n            for (col = 0; col < columns.length; col++) {\n                columns[col].reverse();\n                if (firstRowAsNames) {\n                    const poppedColumn = columns[col].pop();\n                    if (poppedColumn) {\n                        columns[col].unshift(poppedColumn);\n                    }\n                }\n            }\n        }\n    }\n    /**\n     * Parse a date and return it as a number. Overridable through\n     * `options.parseDate`.\n     *\n     * @function Highcharts.Data#parseDate\n     */\n    parseDate(val) {\n        const parseDate = this.options.parseDate;\n        let ret, key, format, dateFormat = this.options.dateFormat || this.dateFormat, match;\n        if (parseDate) {\n            ret = parseDate(val);\n        }\n        else if (typeof val === 'string') {\n            // Auto-detect the date format the first time\n            if (!dateFormat) {\n                for (key in this.dateFormats) { // eslint-disable-line guard-for-in\n                    format = this.dateFormats[key];\n                    match = val.match(format.regex);\n                    if (match) {\n                        this.dateFormat = dateFormat = key;\n                        this.alternativeFormat = format.alternative;\n                        ret = format.parser(match);\n                        break;\n                    }\n                }\n                // Next time, use the one previously found\n            }\n            else {\n                format = this.dateFormats[dateFormat];\n                if (!format) {\n                    // The selected format is invalid\n                    format = this.dateFormats['YYYY/mm/dd'];\n                }\n                match = val.match(format.regex);\n                if (match) {\n                    ret = format.parser(match);\n                }\n            }\n            // Fall back to Date.parse\n            if (!match) {\n                if (val.match(/:.+(GMT|UTC|[Z+\\-])/)) {\n                    val = val\n                        .replace(/\\s*(?:GMT|UTC)?([+\\-])(\\d\\d)(\\d\\d)$/, '$1$2:$3')\n                        .replace(/(?:\\s+|GMT|UTC)([+\\-])/, '$1')\n                        .replace(/(\\d)\\s*(?:GMT|UTC|Z)$/, '$1+00:00');\n                }\n                match = Date.parse(val);\n                // External tools like Date.js and MooTools extend Date object\n                // and return a date.\n                if (typeof match === 'object' &&\n                    match !== null &&\n                    match.getTime) {\n                    ret = (match.getTime() -\n                        match.getTimezoneOffset() *\n                            60000);\n                    // Timestamp\n                }\n                else if (isNumber(match)) {\n                    ret = match - (new Date(match)).getTimezoneOffset() * 60000;\n                }\n            }\n        }\n        return ret;\n    }\n    /**\n     * Get the parsed data in a form that we can apply directly to the\n     * `series.data` config. Array positions can be mapped using the\n     * `series.keys` option.\n     *\n     * @example\n     * const data = Highcharts.data({\n     *   csv: document.getElementById('data').innerHTML\n     * }).getData();\n     *\n     * @function Highcharts.Data#getData\n     *\n     * @return {Array<Array<DataValueType>>|undefined} Data rows\n     */\n    getData() {\n        if (this.columns) {\n            return this.rowsToColumns(this.columns)?.slice(1);\n        }\n    }\n    /**\n     * A hook for working directly on the parsed columns\n     *\n     * @function Highcharts.Data#parsed\n     */\n    parsed() {\n        if (this.options.parsed) {\n            return this.options.parsed.call(this, this.columns);\n        }\n    }\n    /**\n     * If a complete callback function is provided in the options, interpret the\n     * columns into a Highcharts options object.\n     *\n     * The function requires that the context has the `valueCount` property set.\n     *\n     * @function Highcharts.Data#complete\n     */\n    complete() {\n        const columns = this.columns = this.columns || [], xColumns = [], options = this.options, allSeriesBuilders = [];\n        let type = 'linear', series, data, i, j, r, seriesIndex, chartOptions, builder, freeIndexes, typeCol, index;\n        xColumns.length = columns.length;\n        if (options.complete || options.afterComplete) {\n            // Get the names and shift the top row\n            if (this.firstRowAsNames) {\n                for (i = 0; i < columns.length; i++) {\n                    const curCol = columns[i];\n                    if (!defined(curCol.name)) {\n                        curCol.name = pick(curCol.shift(), '').toString();\n                    }\n                }\n            }\n            // Use the next columns for series\n            series = [];\n            freeIndexes = getFreeIndexes(columns?.length || 0, this.valueCount.seriesBuilders);\n            // Populate defined series\n            for (seriesIndex = 0; seriesIndex < this.valueCount.seriesBuilders.length; seriesIndex++) {\n                builder = this.valueCount.seriesBuilders[seriesIndex];\n                // If the builder can be populated with remaining columns, then\n                // add it to allBuilders\n                if (builder.populateColumns(freeIndexes)) {\n                    allSeriesBuilders.push(builder);\n                }\n            }\n            // Populate dynamic series\n            while (freeIndexes.length > 0) {\n                builder = new SeriesBuilder();\n                builder.addColumnReader(0, 'x');\n                // Mark index as used (not free)\n                index = freeIndexes.indexOf(0);\n                if (index !== -1) {\n                    freeIndexes.splice(index, 1);\n                }\n                for (i = 0; i < this.valueCount.global; i++) {\n                    // Create and add a column reader for the next free column\n                    // index\n                    builder.addColumnReader(void 0, this.valueCount.globalPointArrayMap[i]);\n                }\n                // If the builder can be populated with remaining columns, then\n                // add it to allBuilders\n                if (builder.populateColumns(freeIndexes)) {\n                    allSeriesBuilders.push(builder);\n                }\n            }\n            // Get the data-type from the first series x column\n            if (allSeriesBuilders.length > 0 &&\n                allSeriesBuilders[0].readers.length > 0) {\n                typeCol = columns?.[allSeriesBuilders[0].readers[0].columnIndex ?? -1];\n                if (typeof typeCol !== 'undefined') {\n                    if (typeCol.isDatetime) {\n                        type = 'datetime';\n                    }\n                    else if (!typeCol.isNumeric) {\n                        type = 'category';\n                    }\n                }\n            }\n            // Axis type is category, then the \"x\" column should be called\n            // \"name\"\n            if (type === 'category') {\n                for (seriesIndex = 0; seriesIndex < allSeriesBuilders.length; seriesIndex++) {\n                    builder = allSeriesBuilders[seriesIndex];\n                    for (r = 0; r < builder.readers.length; r++) {\n                        if (builder.readers[r].configName === 'x') {\n                            builder.readers[r].configName = 'name';\n                        }\n                    }\n                }\n            }\n            // Read data for all builders\n            for (seriesIndex = 0; seriesIndex < allSeriesBuilders.length; seriesIndex++) {\n                builder = allSeriesBuilders[seriesIndex];\n                // Iterate down the cells of each column and add data to the\n                // series\n                data = [];\n                for (j = 0; j < columns[0].length; j++) {\n                    data[j] = builder.read(columns, j);\n                }\n                // Add the series\n                series[seriesIndex] = {\n                    data,\n                    pointStart: data[0] && (builder.pointIsArray ?\n                        data[0]?.[0] :\n                        data[0]?.x) || void 0\n                };\n                if (builder.name) {\n                    series[seriesIndex].name = builder.name;\n                }\n                if (type === 'category') {\n                    series[seriesIndex].turboThreshold = 0;\n                    series[seriesIndex].pointStart = 0;\n                }\n            }\n            // Do the callback\n            chartOptions = { series };\n            // Prepare the axis options\n            if (type === 'linear' && (!this.xAxisOptions ||\n                this.xAxisOptions.type === type)) {\n                // Clear default value ('linear') if it is not changing the\n                // axis type to avoid loosing animation\n                type = this.xAxisOptions = void 0;\n            }\n            else {\n                this.xAxisOptions = { type };\n                if (type === 'category') {\n                    this.xAxisOptions.uniqueNames = false;\n                }\n            }\n            // Merge the xAxisOptions for the standalone Data module\n            if (!this.chart) {\n                merge(true, chartOptions, { xAxis: this.xAxisOptions || {} });\n            }\n            options.complete?.(chartOptions);\n            // The afterComplete hook is used internally to avoid conflict with\n            // the externally available complete option.\n            options.afterComplete?.(this, chartOptions);\n        }\n    }\n    /**\n     * Sets properties directly on the xAxis object.\n     *\n     * @private\n     */\n    xAxisUpdateHandler(axis) {\n        const options = this.xAxisOptions;\n        if (!options) {\n            return;\n        }\n        // Set the axis properties if not blocked by the axis options that could\n        // have changed in the update event.\n        if (!axis.options.type && options.type) {\n            axis.type = options.type;\n        }\n        if (!axis.options.uniqueNames &&\n            options.uniqueNames === false) {\n            axis.uniqueNames = options.uniqueNames;\n        }\n    }\n    /**\n     * Updates the chart with new data options.\n     *\n     * @function Highcharts.Data#update\n     *\n     * @param {Highcharts.DataOptions} options\n     *        The new data options.\n     *\n     * @param {boolean} [redraw=true]\n     *        Whether to redraw the chart after the new options are set.\n     */\n    update(options, redraw) {\n        const chart = this.chart, chartOptions = chart.options;\n        if (options) {\n            // Set the complete handler\n            options.afterComplete = function (dataInstance, dataOptions) {\n                if (!dataOptions) {\n                    return;\n                }\n                // Avoid setting axis options unless they change. Running\n                // Axis.update will cause the whole structure to be\n                // destroyed and rebuilt, and animation is lost.\n                const xAxis = chart.xAxis[0], xAxisOptions = dataInstance.xAxisOptions;\n                // Update axis if xAxisOptions are different from the current\n                // and not blocked by the axis options.\n                if (xAxisOptions && xAxis && ((xAxis.type !== xAxisOptions.type && !xAxis.options.type) ||\n                    (xAxis.uniqueNames &&\n                        xAxisOptions.uniqueNames === false &&\n                        xAxis.options.uniqueNames === void 0))) {\n                    xAxis.update({}, false);\n                }\n                else {\n                    // Prefer smooth points update when no axis update\n                    (dataOptions?.series || []).forEach(function (seriesOptions) {\n                        delete seriesOptions.pointStart;\n                    });\n                }\n                chart.update(dataOptions, redraw, true);\n            };\n            // Apply it\n            merge(true, chartOptions.data, options);\n            // Reset columns if fetching spreadsheet, to force a re-fetch\n            if (chartOptions.data?.googleSpreadsheetKey && !options.columns) {\n                delete chartOptions.data.columns;\n            }\n            this.init(chartOptions.data || {}, chartOptions);\n        }\n    }\n}\n// Fire 1st xAxis properties modifier after the options are set.\naddEvent((highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default()), 'afterSetOptions', function () {\n    // Target first xAxis only\n    if (this.isXAxis &&\n        // Init or update\n        (!this.chart.xAxis.length || this.chart.xAxis[0] === this)) {\n        this.chart.data?.xAxisUpdateHandler(this);\n    }\n});\n// Extend Chart.init so that the Chart constructor accepts a new configuration\n// option group, data.\naddEvent((highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default()), 'init', function (e) {\n    const chart = this, callback = e.args[1], defaultDataOptions = getOptions().data;\n    let userOptions = (e.args[0] || {});\n    if ((defaultDataOptions || userOptions && userOptions.data) &&\n        !chart.hasDataDef) {\n        chart.hasDataDef = true;\n        /**\n         * The data parser for this chart.\n         *\n         * @name Highcharts.Chart#data\n         * @type {Highcharts.Data|undefined}\n         */\n        const dataOptions = merge(defaultDataOptions, userOptions.data);\n        chart.data = new Data(extend(dataOptions, {\n            afterComplete: function (dataInstance, dataOptions) {\n                let i, series;\n                // Merge series configs\n                if (Object.hasOwnProperty.call(userOptions, 'series')) {\n                    if (typeof userOptions.series === 'object') {\n                        i = Math.max(userOptions.series.length, dataOptions?.series?.length ?? 0);\n                        while (i--) {\n                            series = userOptions.series[i] || {};\n                            userOptions.series[i] = merge(series, dataOptions?.series?.[i] ?? {});\n                        }\n                    }\n                    else { // Allow merging in dataOptions.series (#2856)\n                        delete userOptions.series;\n                    }\n                }\n                // Do the merge\n                userOptions = merge(dataOptions, userOptions);\n                // Register for access in events (Axis' afterSetOptions)\n                chart.data = dataInstance;\n                // Run chart.init again\n                chart.init(userOptions, callback);\n            }\n        }), userOptions, chart);\n        e.preventDefault();\n    }\n});\n/**\n * Creates a new SeriesBuilder. A SeriesBuilder consists of a number\n * of ColumnReaders that reads columns and give them a name.\n * Ex: A series builder can be constructed to read column 3 as 'x' and\n * column 7 and 8 as 'y1' and 'y2'.\n * The output would then be points/rows of the form {x: 11, y1: 22, y2: 33}\n *\n * The name of the builder is taken from the second column. In the above\n * example it would be the column with index 7.\n *\n * @private\n * @class\n * @name SeriesBuilder\n */\nclass SeriesBuilder {\n    constructor() {\n        /* eslint-disable no-invalid-this */\n        this.readers = [];\n        this.pointIsArray = true;\n    }\n    /**\n     * Populates readers with column indexes. A reader can be added without\n     * a specific index and for those readers the index is taken sequentially\n     * from the free columns (this is handled by the ColumnCursor instance).\n     *\n     * @function SeriesBuilder#populateColumns\n     */\n    populateColumns(freeIndexes) {\n        const builder = this;\n        let enoughColumns = true;\n        // Loop each reader and give it an index if its missing.\n        // The freeIndexes.shift() will return undefined if there\n        // are no more columns.\n        builder.readers.forEach((reader) => {\n            if (typeof reader.columnIndex === 'undefined') {\n                reader.columnIndex = freeIndexes.shift();\n            }\n        });\n        // Now, all readers should have columns mapped. If not\n        // then return false to signal that this series should\n        // not be added.\n        builder.readers.forEach((reader) => {\n            if (typeof reader.columnIndex === 'undefined') {\n                enoughColumns = false;\n            }\n        });\n        return enoughColumns;\n    }\n    /**\n     * Reads a row from the dataset and returns a point or array depending\n     * on the names of the readers.\n     *\n     * @function SeriesBuilder#read<T>\n     */\n    read(columns, rowIndex) {\n        const builder = this, pointIsArray = builder.pointIsArray, point = pointIsArray ? [] : {};\n        // Loop each reader and ask it to read its value.\n        // Then, build an array or point based on the readers names.\n        builder.readers.forEach((reader) => {\n            const value = columns[reader.columnIndex][rowIndex];\n            if (pointIsArray) {\n                point.push(value);\n            }\n            else {\n                if (reader.configName.indexOf('.') > 0) {\n                    // Handle nested property names\n                    highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default().prototype.setNestedProperty(point, value, reader.configName);\n                }\n                else {\n                    point[reader.configName] = value;\n                }\n            }\n        });\n        // The name comes from the first column (excluding the x column)\n        if (typeof this.name === 'undefined' && builder.readers.length >= 2) {\n            const columnIndexes = [];\n            builder.readers.forEach(function (reader) {\n                if (reader.configName === 'x' ||\n                    reader.configName === 'name' ||\n                    reader.configName === 'y') {\n                    if (typeof reader.columnIndex !== 'undefined') {\n                        columnIndexes.push(reader.columnIndex);\n                    }\n                }\n            });\n            if (columnIndexes.length >= 2) {\n                // Remove the first one (x col)\n                columnIndexes.shift();\n                // Sort the remaining\n                columnIndexes.sort(function (a, b) {\n                    return a - b;\n                });\n            }\n            // Now use the lowest index as name column\n            this.name = columns[pick(columnIndexes.shift(), 0)].name;\n        }\n        return point;\n    }\n    /**\n     * Creates and adds ColumnReader from the given columnIndex and configName.\n     * ColumnIndex can be undefined and in that case the reader will be given\n     * an index when columns are populated.\n     *\n     * @function SeriesBuilder#addColumnReader\n     */\n    addColumnReader(columnIndex, configName) {\n        this.readers.push({\n            columnIndex: columnIndex,\n            configName: configName\n        });\n        if (!(configName === 'x' ||\n            configName === 'y' ||\n            typeof configName === 'undefined')) {\n            this.pointIsArray = false;\n        }\n    }\n    /**\n     * Returns an array of column indexes that the builder will use when\n     * reading data.\n     *\n     * @function SeriesBuilder#getReferencedColumnIndexes\n     */\n    getReferencedColumnIndexes() {\n        const referencedColumnIndexes = [];\n        let i, columnReader;\n        for (i = 0; i < this.readers.length; i = i + 1) {\n            columnReader = this.readers[i];\n            if (typeof columnReader.columnIndex !== 'undefined') {\n                referencedColumnIndexes.push(columnReader.columnIndex);\n            }\n        }\n        return referencedColumnIndexes;\n    }\n    /**\n     * Returns true if the builder has a reader for the given configName.\n     *\n     * @function SeriesBuilder#hasReader\n     */\n    hasReader(configName) {\n        let i, columnReader;\n        for (i = 0; i < this.readers.length; i = i + 1) {\n            columnReader = this.readers[i];\n            if (columnReader.configName === configName) {\n                return true;\n            }\n        }\n        // Else return undefined\n    }\n}\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Extensions_Data = (Data);\n/* *\n *\n *  API Declarations\n *\n * */\n/**\n * Callback function to modify the CSV before parsing it by the data module.\n *\n * @callback Highcharts.DataBeforeParseCallbackFunction\n *\n * @param {string} csv\n *        The CSV to modify.\n *\n * @return {string}\n *         The CSV to parse.\n */\n/**\n * Callback function that gets called after parsing data.\n *\n * @callback Highcharts.DataCompleteCallbackFunction\n *\n * @param {Highcharts.Options} chartOptions\n *        The chart options that were used.\n */\n/**\n * Callback function that returns the corresponding Date object to a match.\n *\n * @callback Highcharts.DataDateFormatCallbackFunction\n *\n * @param {Array<number>} match\n *\n * @return {number}\n */\n/**\n * Structure for alternative date formats to parse.\n *\n * @interface Highcharts.DataDateFormatObject\n */ /**\n* @name Highcharts.DataDateFormatObject#alternative\n* @type {string|undefined}\n*/ /**\n* @name Highcharts.DataDateFormatObject#parser\n* @type {Highcharts.DataDateFormatCallbackFunction}\n*/ /**\n* @name Highcharts.DataDateFormatObject#regex\n* @type {global.RegExp}\n*/\n/**\n * Possible types for a data item in a column or row.\n *\n * @typedef {number|string|null} Highcharts.DataValueType\n */\n/**\n * Callback function to parse string representations of dates into\n * JavaScript timestamps (milliseconds since 1.1.1970).\n *\n * @callback Highcharts.DataParseDateCallbackFunction\n *\n * @param {string} dateValue\n *\n * @return {number}\n *         Timestamp (milliseconds since 1.1.1970) as integer for Date class.\n */\n/**\n * Callback function to access the parsed columns, the two-dimensional\n * input data array directly, before they are interpreted into series\n * data and categories.\n *\n * @callback Highcharts.DataParsedCallbackFunction\n *\n * @param {Array<Array<*>>} columns\n *        The parsed columns by the data module.\n *\n * @return {boolean|undefined}\n *         Return `false` to stop completion, or call `this.complete()` to\n *         continue async.\n */\n/* *\n *\n *  API Options\n *\n * */\n/**\n * The Data module provides a simplified interface for adding data to\n * a chart from sources like CVS, HTML tables or grid views. See also\n * the [tutorial article on the Data module](\n * https://www.highcharts.com/docs/working-with-data/data-module).\n *\n * It requires the `modules/data.js` file to be loaded.\n *\n * Please note that the default way of adding data in Highcharts, without\n * the need of a module, is through the [series._type_.data](#series.line.data)\n * option.\n *\n * @sample {highcharts} highcharts/demo/column-parsed/\n *         HTML table\n * @sample {highcharts} highcharts/data/csv/\n *         CSV\n *\n * @since     4.0\n * @requires  modules/data\n * @apioption data\n */\n/**\n * A callback function to modify the CSV before parsing it. Return the modified\n * string.\n *\n * @sample {highcharts} highcharts/demo/line-csv/\n *         Modify CSV before parse\n *\n * @type      {Highcharts.DataBeforeParseCallbackFunction}\n * @since     6.1\n * @apioption data.beforeParse\n */\n/**\n * A two-dimensional array representing the input data on tabular form.\n * This input can be used when the data is already parsed, for example\n * from a grid view component. Each cell can be a string or number.\n * If not switchRowsAndColumns is set, the columns are interpreted as\n * series.\n *\n * @see [data.rows](#data.rows)\n *\n * @sample {highcharts} highcharts/data/columns/\n *         Columns\n *\n * @type      {Array<Array<Highcharts.DataValueType>>}\n * @since     4.0\n * @apioption data.columns\n */\n/**\n * An array option that specifies the data type for each column in the series\n * loaded within the data module.\n *\n * Possible values: `\"string\"`, `\"number\"`, `\"float\"`, `\"date\"`.\n *\n * @sample {highcharts|highstock} highcharts/data/column-types/\n *         X-axis categories based on CSV data\n * @sample {highmaps} highcharts/data/column-types-map/\n *         Map chart created with fips from CSV\n *\n * @type       {Array<'string'|'number'|'float'|'date'>}\n * @since      11.3.0\n * @validvalue [\"string\", \"number\", \"float\", \"date\"]\n * @apioption  data.columnTypes\n */\n/**\n * The callback that is evaluated when the data is finished loading,\n * optionally from an external source, and parsed. The first argument\n * passed is a finished chart options object, containing the series.\n * These options can be extended with additional options and passed\n * directly to the chart constructor.\n *\n * @see [data.parsed](#data.parsed)\n *\n * @sample {highcharts} highcharts/data/complete/\n *         Modify data on complete\n *\n * @type      {Highcharts.DataCompleteCallbackFunction}\n * @since     4.0\n * @apioption data.complete\n */\n/**\n * A comma delimited string to be parsed. Related options are [startRow](\n * #data.startRow), [endRow](#data.endRow), [startColumn](#data.startColumn)\n * and [endColumn](#data.endColumn) to delimit what part of the table\n * is used. The [lineDelimiter](#data.lineDelimiter) and [itemDelimiter](\n * #data.itemDelimiter) options define the CSV delimiter formats.\n *\n * The built-in CSV parser doesn't support all flavours of CSV, so in\n * some cases it may be necessary to use an external CSV parser. See\n * [this example](https://jsfiddle.net/highcharts/u59176h4/) of parsing\n * CSV through the MIT licensed [Papa Parse](http://papaparse.com/)\n * library.\n *\n * @sample {highcharts} highcharts/data/csv/\n *         Data from CSV\n *\n * @type      {string}\n * @since     4.0\n * @apioption data.csv\n */\n/**\n * Which of the predefined date formats in Date.prototype.dateFormats\n * to use to parse date values. Defaults to a best guess based on what\n * format gives valid and ordered dates. Valid options include: `YYYY/mm/dd`,\n * `dd/mm/YYYY`, `mm/dd/YYYY`, `dd/mm/YY`, `mm/dd/YY`.\n *\n * @see [data.parseDate](#data.parseDate)\n *\n * @sample {highcharts} highcharts/data/dateformat-auto/\n *         Best guess date format\n *\n * @type       {string}\n * @since      4.0\n * @validvalue [\"YYYY/mm/dd\", \"dd/mm/YYYY\", \"mm/dd/YYYY\", \"dd/mm/YYYY\",\n *             \"dd/mm/YY\", \"mm/dd/YY\"]\n * @apioption  data.dateFormat\n */\n/**\n * The decimal point used for parsing numbers in the CSV.\n *\n * If both this and data.delimiter is set to `undefined`, the parser will\n * attempt to deduce the decimal point automatically.\n *\n * @sample {highcharts} highcharts/data/delimiters/\n *         Comma as decimal point\n *\n * @type      {string}\n * @default   .\n * @since     4.1.0\n * @apioption data.decimalPoint\n */\n/**\n * In tabular input data, the last column (indexed by 0) to use. Defaults\n * to the last column containing data.\n *\n * @sample {highcharts} highcharts/data/start-end/\n *         Limited data\n *\n * @type      {number}\n * @since     4.0\n * @apioption data.endColumn\n */\n/**\n * In tabular input data, the last row (indexed by 0) to use. Defaults\n * to the last row containing data.\n *\n * @sample {highcharts} highcharts/data/start-end/\n *         Limited data\n *\n * @type      {number}\n * @since     4.0.4\n * @apioption data.endRow\n */\n/**\n * Whether to use the first row in the data set as series names.\n *\n * @sample {highcharts} highcharts/data/start-end/\n *         Don't get series names from the CSV\n * @sample {highstock} highcharts/data/start-end/\n *         Don't get series names from the CSV\n *\n * @type      {boolean}\n * @default   true\n * @since     4.1.0\n * @product   highcharts highstock gantt\n * @apioption data.firstRowAsNames\n */\n/**\n * The Google Spreadsheet API key required for access generated at [API Services\n * / Credentials](https://console.cloud.google.com/apis/credentials). See a\n * comprehensive tutorial for setting up the key at the\n * [Hands-On Data Visualization](https://handsondataviz.org/google-sheets-api-key.html)\n * book website.\n *\n * @sample {highcharts} highcharts/data/google-spreadsheet/\n *         Load a Google Spreadsheet\n *\n * @type      {string}\n * @since     9.2.2\n * @apioption data.googleAPIKey\n */\n/**\n * The key or `spreadsheetId` value for a Google Spreadsheet to load. See\n * [developers.google.com](https://developers.google.com/sheets/api/guides/concepts)\n * for how to find the `spreadsheetId`.\n *\n * In order for Google Sheets to load, a valid [googleAPIKey](#data.googleAPIKey)\n * must also be given.\n *\n * @sample {highcharts} highcharts/data/google-spreadsheet/\n *         Load a Google Spreadsheet\n *\n * @type      {string}\n * @since     4.0\n * @apioption data.googleSpreadsheetKey\n */\n/**\n * The Google Spreadsheet `range` to use in combination with\n * [googleSpreadsheetKey](#data.googleSpreadsheetKey). See\n * [developers.google.com](https://developers.google.com/sheets/api/reference/rest/v4/spreadsheets.values/get)\n * for details.\n *\n * If given, it takes precedence over `startColumn`, `endColumn`, `startRow` and\n * `endRow`.\n *\n * @example\n * googleSpreadsheetRange: 'Fruit Consumption' // Load a named worksheet\n * googleSpreadsheetRange: 'A:Z' // Load columns A to Z\n *\n * @sample {highcharts} highcharts/data/google-spreadsheet/\n *         Load a Google Spreadsheet\n *\n * @type      {string|undefined}\n * @since     9.2.2\n * @apioption data.googleSpreadsheetRange\n */\n/**\n * No longer works since v9.2.2, that uses Google Sheets API v4. Instead, use\n * the [googleSpreadsheetRange](#data.googleSpreadsheetRange) option to load a\n * specific sheet.\n *\n * @deprecated\n * @type      {string}\n * @since     4.0\n * @apioption data.googleSpreadsheetWorksheet\n */\n/**\n * Item or cell delimiter for parsing CSV. Defaults to the tab character\n * `\\t` if a tab character is found in the CSV string, if not it defaults\n * to `,`.\n *\n * If this is set to false or undefined, the parser will attempt to deduce\n * the delimiter automatically.\n *\n * @sample {highcharts} highcharts/data/delimiters/\n *         Delimiters\n *\n * @type      {string}\n * @since     4.0\n * @apioption data.itemDelimiter\n */\n/**\n * Line delimiter for parsing CSV.\n *\n * @sample {highcharts} highcharts/data/delimiters/\n *         Delimiters\n *\n * @type      {string}\n * @default   \\n\n * @since     4.0\n * @apioption data.lineDelimiter\n */\n/**\n * A callback function to access the parsed columns, the two-dimensional\n * input data array directly, before they are interpreted into series\n * data and categories. Return `false` to stop completion, or call\n * `this.complete()` to continue async.\n *\n * @see [data.complete](#data.complete)\n *\n * @sample {highcharts} highcharts/data/parsed/\n *         Modify data after parse\n *\n * @type      {Highcharts.DataParsedCallbackFunction}\n * @since     4.0\n * @apioption data.parsed\n */\n/**\n * A callback function to parse string representations of dates into\n * JavaScript timestamps. Should return an integer timestamp on success.\n *\n * @see [dateFormat](#data.dateFormat)\n *\n * @type      {Highcharts.DataParseDateCallbackFunction}\n * @since     4.0\n * @apioption data.parseDate\n */\n/**\n * The same as the columns input option, but defining rows instead of\n * columns.\n *\n * @see [data.columns](#data.columns)\n *\n * @sample {highcharts} highcharts/data/rows/\n *         Data in rows\n *\n * @type      {Array<Array<Highcharts.DataValueType>>}\n * @since     4.0\n * @apioption data.rows\n */\n/**\n * An array containing dictionaries for each series. A dictionary exists of\n * Point property names as the key and the CSV column index as the value.\n *\n * @sample {highcharts} highcharts/data/seriesmapping-label/\n *         Label from data set\n *\n * @type      {Array<Highcharts.Dictionary<number>>}\n * @since     4.0.4\n * @apioption data.seriesMapping\n */\n/**\n * In tabular input data, the first column (indexed by 0) to use.\n *\n * @sample {highcharts} highcharts/data/start-end/\n *         Limited data\n *\n * @type      {number}\n * @default   0\n * @since     4.0\n * @apioption data.startColumn\n */\n/**\n * In tabular input data, the first row (indexed by 0) to use.\n *\n * @sample {highcharts} highcharts/data/start-end/\n *         Limited data\n *\n * @type      {number}\n * @default   0\n * @since     4.0\n * @apioption data.startRow\n */\n/**\n * Switch rows and columns of the input data, so that `this.columns`\n * effectively becomes the rows of the data set, and the rows are interpreted\n * as series.\n *\n * @sample {highcharts} highcharts/data/switchrowsandcolumns/\n *         Switch rows and columns\n *\n * @type      {boolean}\n * @default   false\n * @since     4.0\n * @apioption data.switchRowsAndColumns\n */\n/**\n * An HTML table or the id of such to be parsed as input data. Related\n * options are `startRow`, `endRow`, `startColumn` and `endColumn` to\n * delimit what part of the table is used.\n *\n * @sample {highcharts} highcharts/demo/column-parsed/\n *         Parsed table\n *\n * @type      {string|global.HTMLElement}\n * @since     4.0\n * @apioption data.table\n */\n/**\n * An URL to a remote CSV dataset. Will be fetched when the chart is created\n * using Ajax.\n *\n * @sample highcharts/data/livedata-columns\n *         Categorized bar chart with CSV and live polling\n * @sample highcharts/data/livedata-csv\n *         Time based line chart with CSV and live polling\n *\n * @type      {string}\n * @apioption data.csvURL\n */\n/**\n * A URL to a remote JSON dataset, structured as a row array.\n * Will be fetched when the chart is created using Ajax.\n *\n * @sample highcharts/data/livedata-rows\n *         Rows with live polling\n *\n * @type      {string}\n * @apioption data.rowsURL\n */\n/**\n * A URL to a remote JSON dataset, structured as a column array.\n * Will be fetched when the chart is created using Ajax.\n *\n * @sample highcharts/data/livedata-columns\n *         Columns with live polling\n *\n * @type      {string}\n * @apioption data.columnsURL\n */\n/**\n * Sets the refresh rate for data polling when importing remote dataset by\n * setting [data.csvURL](data.csvURL), [data.rowsURL](data.rowsURL),\n * [data.columnsURL](data.columnsURL), or\n * [data.googleSpreadsheetKey](data.googleSpreadsheetKey).\n *\n * Note that polling must be enabled by setting\n * [data.enablePolling](data.enablePolling) to true.\n *\n * The value is the number of seconds between pollings.\n * It cannot be set to less than 1 second.\n *\n * @sample highcharts/demo/live-data\n *         Live data with user set refresh rate\n *\n * @default   1\n * @type      {number}\n * @apioption data.dataRefreshRate\n */\n/**\n * Enables automatic refetching of remote datasets every _n_ seconds (defined by\n * setting [data.dataRefreshRate](data.dataRefreshRate)).\n *\n * Only works when either [data.csvURL](data.csvURL),\n * [data.rowsURL](data.rowsURL), [data.columnsURL](data.columnsURL), or\n * [data.googleSpreadsheetKey](data.googleSpreadsheetKey).\n *\n * @sample highcharts/demo/live-data\n *         Live data\n * @sample highcharts/data/livedata-columns\n *         Categorized bar chart with CSV and live polling\n *\n * @type      {boolean}\n * @default   false\n * @apioption data.enablePolling\n */\n(''); // Keeps doclets above in JS file\n\n;// ./code/es-modules/masters/modules/data.js\n\n\n\n\n\nconst G = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n// Classes\nG.Data = G.Data || Extensions_Data;\nG.HttpUtilities = G.HttpUtilities || Core_HttpUtilities;\n// Functions\nG.ajax = G.HttpUtilities.ajax;\nG.data = G.Data.data;\nG.getJSON = G.HttpUtilities.getJSON;\nG.post = G.HttpUtilities.post;\n/* harmony default export */ const data_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__532__", "__WEBPACK_EXTERNAL_MODULE__960__", "__WEBPACK_EXTERNAL_MODULE__260__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "data_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "win", "discardElement", "objectEach", "HttpUtilities", "ajax", "settings", "headers", "json", "xml", "text", "octet", "r", "XMLHttpRequest", "handleError", "xhr", "err", "error", "url", "open", "type", "toUpperCase", "setRequestHeader", "dataType", "val", "responseType", "onreadystatechange", "res", "readyState", "status", "responseText", "JSON", "parse", "e", "Error", "success", "data", "stringify", "send", "getJSON", "post", "fetchOptions", "formData", "FormData", "name", "append", "filename", "fetch", "method", "body", "then", "ok", "link", "document", "createElement", "href", "download", "click", "highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_", "highcharts_Axis_commonjs_highcharts_Axis_commonjs2_highcharts_Axis_root_Highcharts_Axis_default", "highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_", "highcharts_Chart_commonjs_highcharts_Chart_commonjs2_highcharts_Chart_root_Highcharts_Chart_default", "highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_", "highcharts_Point_commonjs_highcharts_Point_commonjs2_highcharts_Point_root_Highcharts_Point_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "getOptions", "doc", "Data_ajax", "seriesTypes", "addEvent", "defined", "extend", "fireEvent", "isNumber", "merge", "Data_objectEach", "pick", "splat", "hasURLOption", "options", "Boolean", "rowsURL", "csvURL", "columnsURL", "Data", "dataOptions", "chartOptions", "chart", "rowsToColumns", "rows", "row", "rows<PERSON><PERSON><PERSON>", "col", "co<PERSON><PERSON><PERSON><PERSON>", "columns", "length", "constructor", "dateFormats", "regex", "parser", "match", "Date", "UTC", "NaN", "alternative", "year", "getFullYear", "rawColumns", "init", "decimalPoint", "hasData", "firstRowAsNames", "decimalRegex", "RegExp", "liveDataTimeout", "clearTimeout", "dataFound", "fetchLiveData", "parseCSV", "parseTable", "parseGoogleSpreadsheet", "afterComplete", "getColumnDistribution", "xColumns", "getValueCount", "pointArrayMap", "getPointArrayMap", "globalType", "individualCounts", "seriesBuilders", "seriesMapping", "series", "map", "x", "seriesIndex", "for<PERSON>ach", "push", "mapping", "builder", "SeriesBuilder", "numberOfValueColumnsNeeded", "seriesArr", "defaultPointArrayMap", "isCartesian", "addColumnReader", "i", "<PERSON><PERSON><PERSON><PERSON>", "globalPointArrayMap", "valueCount", "global", "individual", "switchRowsAndColumns", "parseTypes", "parsed", "complete", "inOptions", "self", "startColumn", "endColumn", "Number", "MAX_VALUE", "dataTypes", "potDelimiters", "csv", "startRow", "endRow", "itemDelimiter", "lines", "rowIt", "beforeParse", "replace", "split", "lineDelimiter", "points", "commas", "guessed", "some", "columnStr", "inStr", "c", "cn", "cl", "token", "j", "isNaN", "trim", "isFinite", "offset", "parseRow", "rowNumber", "noAdd", "callbacks", "actualColumn", "column", "read", "pushType", "columnTypes", "parseFloat", "dateFormat", "deduceDateFormat", "limit", "format", "stable", "max", "thing", "guessedFormat", "calculatedFormat", "madeDeduction", "parseInt", "join", "table", "getElementById", "getElementsByTagName", "tr", "rowNo", "children", "item", "colNo", "tagName", "innerHTML", "pollingEnabled", "enablePolling", "originalOptions", "currentRetries", "updateIntervalMs", "dataRefreshRate", "performFetch", "initialFetch", "request", "done", "tp", "test", "poll", "liveDataURL", "setTimeout", "update", "googleSpreadsheetKey", "refreshRate", "Math", "getRange", "googleSpreadsheetRange", "alphabet", "start", "char<PERSON>t", "end", "fetchSheet", "fn", "googleAPIKey", "values", "rowCount", "reduce", "str", "inside", "parseColumn", "isXColumn", "indexOf", "backup", "columnType", "forceCategory", "xAxis", "columnHasName", "floatVal", "trimVal", "trimInsideVal", "dateVal", "diff", "descending", "isDatetime", "isNumeric", "parseDate", "alternativeFormat", "unsorted", "mixed", "sort", "reverse", "poppedColumn", "pop", "unshift", "ret", "getTime", "getTimezoneOffset", "getData", "slice", "allSeriesBuilders", "freeIndexes", "typeCol", "index", "curCol", "shift", "toString", "getFreeIndexes", "numberOfColumns", "s", "referencedIndexes", "freeIndexValues", "getReferencedColumnIndexes", "populateColumns", "splice", "readers", "columnIndex", "config<PERSON><PERSON>", "pointStart", "pointIsArray", "turboThreshold", "xAxisOptions", "uniqueNames", "xAxisUpdateHandler", "axis", "redraw", "dataInstance", "seriesOptions", "isXAxis", "callback", "args", "defaultDataOptions", "userOptions", "hasDataDef", "preventDefault", "enoughColumns", "reader", "rowIndex", "point", "value", "setNestedProperty", "columnIndexes", "b", "columnReader", "referencedColumnIndexes", "G"], "mappings": "CAWA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,IAAO,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,cAAiB,EACrK,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,0BAA2B,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,IAAO,CAACA,EAAK,KAAQ,CAACA,EAAK,KAAQ,CAACA,EAAK,cAAiB,CAAE,GAC5J,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,0BAA0B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,IAAO,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,KAAQ,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAEhMA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,IAAO,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,KAAQ,CAAEA,EAAK,UAAa,CAAC,cAAiB,CAC7K,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,EAAkCC,EAAkCC,EAAkCC,IAC3K,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACV,IAERA,EAAOD,OAAO,CAAGS,CAEX,EAEA,IACC,AAACR,IAERA,EAAOD,OAAO,CAAGU,CAEX,EAEA,IACC,AAACT,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,EAEA,IACC,AAACL,IAERA,EAAOD,OAAO,CAAGQ,CAEX,CAEI,EAGII,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaf,OAAO,CAG5B,IAAIC,EAASW,CAAwB,CAACE,EAAS,CAAG,CAGjDd,QAAS,CAAC,CACX,EAMA,OAHAW,CAAmB,CAACG,EAAS,CAACb,EAAQA,EAAOD,OAAO,CAAEa,GAG/CZ,EAAOD,OAAO,AACtB,CAMCa,EAAoBI,CAAC,CAAG,AAAChB,IACxB,IAAIiB,EAASjB,GAAUA,EAAOkB,UAAU,CACvC,IAAOlB,EAAO,OAAU,CACxB,IAAOA,EAER,OADAY,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACpB,EAASsB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACxB,EAASuB,IAC5EE,OAAOC,cAAc,CAAC1B,EAASuB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GAarH,GAAM,CAAEE,IAAAA,CAAG,CAAE,CAAID,IAEX,CAAEE,eAAAA,CAAc,CAAEC,WAAAA,CAAU,CAAE,CAAIH,IAsJlCI,EAAgB,CAClBC,KAtIJ,SAAcC,CAAQ,EAClB,IAAMC,EAAU,CACZC,KAAM,mBACNC,IAAK,kBACLC,KAAM,aACNC,MAAO,0BACX,EAAGC,EAAI,IAAIC,eASX,SAASC,EAAYC,CAAG,CAAEC,CAAG,EACrBV,EAASW,KAAK,EACdX,EAASW,KAAK,CAACF,EAAKC,EAK5B,CACA,GAAI,CAACV,EAASY,GAAG,CACb,MAAO,CAAA,EAEXN,EAAEO,IAAI,CAAC,AAACb,CAAAA,EAASc,IAAI,EAAI,KAAI,EAAGC,WAAW,GAAIf,EAASY,GAAG,CAAE,CAAA,GACxDZ,EAASC,OAAO,EAAE,CAAC,eAAe,EACnCK,EAAEU,gBAAgB,CAAC,eAAgBf,CAAO,CAACD,EAASiB,QAAQ,EAAI,OAAO,EAAIhB,EAAQG,IAAI,EAE3FP,EAAWG,EAASC,OAAO,CAAE,SAAUiB,CAAG,CAAEtC,CAAG,EAC3C0B,EAAEU,gBAAgB,CAACpC,EAAKsC,EAC5B,GACIlB,EAASmB,YAAY,EACrBb,CAAAA,EAAEa,YAAY,CAAGnB,EAASmB,YAAY,AAAD,EAGzCb,EAAEc,kBAAkB,CAAG,WACnB,IAAIC,EACJ,GAAIf,AAAiB,IAAjBA,EAAEgB,UAAU,CAAQ,CACpB,GAAIhB,AAAa,MAAbA,EAAEiB,MAAM,CAAU,CAClB,GAAIvB,AAA0B,SAA1BA,EAASmB,YAAY,GACrBE,EAAMf,EAAEkB,YAAY,CAChBxB,AAAsB,SAAtBA,EAASiB,QAAQ,EACjB,GAAI,CACAI,EAAMI,KAAKC,KAAK,CAACL,EACrB,CACA,MAAOM,EAAG,CACN,GAAIA,aAAaC,MACb,OAAOpB,EAAYF,EAAGqB,EAE9B,CAGR,OAAO3B,EAAS6B,OAAO,GAAGR,EAAKf,EACnC,CACAE,EAAYF,EAAGA,EAAEkB,YAAY,CACjC,CACJ,EACIxB,EAAS8B,IAAI,EAAI,AAAyB,UAAzB,OAAO9B,EAAS8B,IAAI,EACrC9B,CAAAA,EAAS8B,IAAI,CAAGL,KAAKM,SAAS,CAAC/B,EAAS8B,IAAI,CAAA,EAEhDxB,EAAE0B,IAAI,CAAChC,EAAS8B,IAAI,CACxB,EAwEIG,QA7DJ,SAAiBrB,CAAG,CAAEiB,CAAO,EACzB/B,EAAcC,IAAI,CAAC,CACfa,IAAKA,EACLiB,QAASA,EACTZ,SAAU,OACVhB,QAAS,CAGL,eAAgB,YACpB,CACJ,EACJ,EAmDIiC,KAhCJ,SAActB,CAAG,CAAEkB,CAAI,CAAEK,CAAY,EACjC,IAAMC,EAAW,IAAIzC,EAAI0C,QAAQ,CAEjCxC,EAAWiC,EAAM,SAAUZ,CAAG,CAAEoB,CAAI,EAChCF,EAASG,MAAM,CAACD,EAAMpB,EAC1B,GACAkB,EAASG,MAAM,CAAC,MAAO,QACvB,GAAM,CAAEC,SAAAA,CAAQ,CAAE1B,KAAAA,CAAI,CAAE,CAAGgB,EAC3B,OAAOnC,EAAI8C,KAAK,CAAC7B,EAAK,CAClB8B,OAAQ,OACRC,KAAMP,EACN,GAAGD,CAAY,AACnB,GAAGS,IAAI,CAAC,AAACvB,IACDA,EAAIwB,EAAE,EACNxB,EAAIjB,IAAI,GAAGwC,IAAI,CAAC,AAACxC,IACb,IAAM0C,EAAOC,SAASC,aAAa,CAAC,IACpCF,CAAAA,EAAKG,IAAI,CAAG,CAAC,KAAK,EAAEnC,EAAK,QAAQ,EAAEV,EAAK,CAAC,CACzC0C,EAAKI,QAAQ,CAAGV,EAChBM,EAAKK,KAAK,GACVvD,EAAekD,EACnB,EAER,EACJ,CAUA,EA0CA,IAAIM,EAA2FlF,EAAoB,KAC/GmF,EAA+GnF,EAAoBI,CAAC,CAAC8E,GAErIE,EAA+FpF,EAAoB,KACnHqF,EAAmHrF,EAAoBI,CAAC,CAACgF,GAEzIE,EAA+FtF,EAAoB,KACnHuF,EAAmHvF,EAAoBI,CAAC,CAACkF,GAEzIE,EAAmIxF,EAAoB,KACvJyF,EAAuJzF,EAAoBI,CAAC,CAACoF,GAiBjL,GAAM,CAAEE,WAAAA,CAAU,CAAE,CAAIlE,IAElB,CAAEmE,IAAAA,CAAG,CAAE,CAAInE,IAEX,CAAEK,KAAM+D,CAAS,CAAE,CAxEgChE,EA2EnD,CAAEiE,YAAAA,CAAW,CAAE,CAAIJ,IAEnB,CAAEK,SAAAA,CAAQ,CAAEC,QAAAA,CAAO,CAAEC,OAAAA,CAAM,CAAEC,UAAAA,CAAS,CAAEC,SAAAA,CAAQ,CAAEC,MAAAA,CAAK,CAAExE,WAAYyE,CAAe,CAAEC,KAAAA,CAAI,CAAEC,MAAAA,CAAK,CAAE,CAAI9E,IAkC7G,SAAS+E,EAAaC,CAAO,EACzB,MAAOC,CAAAA,CAAQD,CAAAA,GACVA,CAAAA,EAAQE,OAAO,EAAIF,EAAQG,MAAM,EAAIH,EAAQI,UAAU,AAAD,CAAC,CAChE,CAoBA,MAAMC,EAWF,OAAOjD,KAAKkD,CAAW,CAAEC,EAAe,CAAC,CAAC,CAAEC,CAAK,CAAE,CAC/C,OAAO,IAAIH,EAAKC,EAAaC,EAAcC,EAC/C,CAMA,OAAOC,cAAcC,CAAI,CAAE,CACvB,IAAIC,EAAKC,EAAYC,EAAKC,EAAYC,EACtC,GAAIL,EAGA,IAAKC,EAAM,EAFXI,EAAU,EAAE,CACZH,EAAaF,EAAKM,MAAM,CACVL,EAAMC,EAAYD,IAE5B,IAAKE,EAAM,EADXC,EAAaJ,CAAI,CAACC,EAAI,CAACK,MAAM,CACfH,EAAMC,EAAYD,IACvBE,CAAO,CAACF,EAAI,EACbE,CAAAA,CAAO,CAACF,EAAI,CAAG,EAAE,AAAD,EAEpBE,CAAO,CAACF,EAAI,CAACF,EAAI,CAAGD,CAAI,CAACC,EAAI,CAACE,EAAI,CAI9C,OAAOE,CACX,CAMAE,YAAYX,CAAW,CAAEC,EAAe,CAAC,CAAC,CAAEC,CAAK,CAAE,CAC/C,IAAI,CAACC,aAAa,CAAGJ,EAAKI,aAAa,CAQvC,IAAI,CAACS,WAAW,CAAG,CACf,aAAc,CACVC,MAAO,8CACPC,OAAQ,SAAUC,CAAK,EACnB,OAAQA,EACJC,KAAKC,GAAG,CAAC,CAACF,CAAK,CAAC,EAAE,CAAE,CAACA,CAAK,CAAC,EAAE,CAAG,EAAG,CAACA,CAAK,CAAC,EAAE,EAC5CG,GACR,CACJ,EACA,aAAc,CACVL,MAAO,8CACPC,OAAQ,SAAUC,CAAK,EACnB,OAAQA,EACJC,KAAKC,GAAG,CAAC,CAACF,CAAK,CAAC,EAAE,CAAE,CAACA,CAAK,CAAC,EAAE,CAAG,EAAG,CAACA,CAAK,CAAC,EAAE,EAC5CG,GACR,EACAC,YAAa,YACjB,EACA,aAAc,CACVN,MAAO,8CACPC,OAAQ,SAAUC,CAAK,EACnB,OAAQA,EACJC,KAAKC,GAAG,CAAC,CAACF,CAAK,CAAC,EAAE,CAAE,CAACA,CAAK,CAAC,EAAE,CAAG,EAAG,CAACA,CAAK,CAAC,EAAE,EAC5CG,GACR,CACJ,EACA,WAAY,CACRL,MAAO,8CACPC,OAAQ,SAAUC,CAAK,EACnB,GAAI,CAACA,EACD,OAAOG,IAEX,IAAMzH,EAAI,IAAIuH,KACVI,EAAO,CAACL,CAAK,CAAC,EAAE,CAOpB,OANIK,EAAQ3H,EAAE4H,WAAW,GAAK,IAC1BD,GAAQ,KAGRA,GAAQ,IAELJ,KAAKC,GAAG,CAACG,EAAM,CAACL,CAAK,CAAC,EAAE,CAAG,EAAG,CAACA,CAAK,CAAC,EAAE,CAClD,EACAI,YAAa,UACjB,EACA,WAAY,CACRN,MAAO,8CACPC,OAAQ,SAAUC,CAAK,EACnB,OAAQA,EACJC,KAAKC,GAAG,CAAC,CAACF,CAAK,CAAC,EAAE,CAAG,IAAM,CAACA,CAAK,CAAC,EAAE,CAAG,EAAG,CAACA,CAAK,CAAC,EAAE,EACnDG,GACR,CACJ,CACJ,EACA,IAAI,CAAChB,KAAK,CAAGA,EACb,IAAI,CAACD,YAAY,CAAGA,EACpB,IAAI,CAACP,OAAO,CAAGM,EACf,IAAI,CAACsB,UAAU,CAAG,EAAE,CACpB,IAAI,CAACC,IAAI,CAACvB,EAAaC,EAAcC,EACzC,CAYAqB,KAAKvB,CAAW,CAAEC,CAAY,CAAEC,CAAK,CAAE,CACnC,IAAIsB,EAAexB,EAAYwB,YAAY,CAAEC,EACzCxB,GACA,CAAA,IAAI,CAACA,YAAY,CAAGA,CAAW,EAE/BC,GACA,CAAA,IAAI,CAACA,KAAK,CAAGA,CAAI,EAEA,MAAjBsB,GAAwBA,AAAiB,MAAjBA,GACxBA,CAAAA,EAAe,KAAK,CAAA,EAExB,IAAI,CAAC9B,OAAO,CAAGM,EACf,IAAI,CAACS,OAAO,CAAIT,EAAYS,OAAO,EAC/B,IAAI,CAACN,aAAa,CAACH,EAAYI,IAAI,GACnC,EAAE,CACN,IAAI,CAACsB,eAAe,CAAGnC,EAAKS,EAAY0B,eAAe,CAAE,IAAI,CAACA,eAAe,CAAE,CAAA,GAC/E,IAAI,CAACC,YAAY,CAAIH,GACjB,AAAII,OAAO,cAAgBJ,EAAe,aAEjB,KAAK,IAA9B,IAAI,CAACK,eAAe,EACpBC,aAAa,IAAI,CAACD,eAAe,EAMrC,IAAI,CAACP,UAAU,CAAG,EAAE,CAEhB,IAAI,CAACb,OAAO,CAACC,MAAM,GACnB,IAAI,CAACqB,SAAS,GACdN,EAAU,CAAChC,EAAaO,IAEvByB,GAEDA,CAAAA,EAAU,IAAI,CAACO,aAAa,EAAC,EAE5BP,GAGDA,CAAAA,EAAU9B,CAAAA,CAAQ,IAAI,CAACsC,QAAQ,GAAGvB,MAAM,EAEvCe,GAEDA,CAAAA,EAAU9B,CAAAA,CAAQ,IAAI,CAACuC,UAAU,GAAGxB,MAAM,EAEzCe,GAEDA,CAAAA,EAAU,IAAI,CAACU,sBAAsB,EAAC,EAEtC,CAACV,GAAWzB,EAAYoC,aAAa,EACrCpC,EAAYoC,aAAa,CAAC,IAAI,CAEtC,CAQAC,uBAAwB,CACpB,IAAMpC,EAAe,IAAI,CAACA,YAAY,CAAEP,EAAU,IAAI,CAACA,OAAO,CAAE4C,EAAW,EAAE,CAAEC,EAAgB,SAAUzG,EAAO,MAAM,EAClH,MAAO,AAACiD,CAAAA,CAAW,CAACjD,EAAK,CAAC1B,SAAS,CAACoI,aAAa,EAAI,CAAC,EAAE,AAAD,EAAG9B,MAAM,AACpE,EAAG+B,EAAmB,SAAU3G,EAAO,MAAM,EACzC,OAAOiD,CAAW,CAACjD,EAAK,CAAC1B,SAAS,CAACoI,aAAa,AACpD,EAAGE,EAAazC,GAAcC,OAAOpE,KAAM6G,EAAmB,EAAE,CAAEC,EAAiB,EAAE,CAGrFC,EAAiBnD,GAASmD,eACtB5C,GAAc6C,QAAQC,IAAI,WACtB,MAAO,CAAEC,EAAG,CAAE,CAClB,IACA,EAAE,CACFC,EAAc,EAClB,AAAChD,CAAAA,GAAc6C,QAAU,EAAE,AAAD,EAAGI,OAAO,CAAC,AAACJ,IAClCH,EAAiBQ,IAAI,CAACZ,EAAcO,EAAOhH,IAAI,EAAI4G,GACvD,GAEAG,EAAcK,OAAO,CAAC,AAACE,IACnBd,EAASa,IAAI,CAACC,EAAQJ,CAAC,EAAI,EAC/B,GAGwB,IAApBV,EAAS5B,MAAM,EACf4B,EAASa,IAAI,CAAC,GAIlBN,EAAcK,OAAO,CAAC,AAACE,IACnB,IAAMC,EAAU,IAAIC,EAAiBC,EAA6BZ,CAAgB,CAACM,EAAY,EAC3FV,EAAcG,GAAqDI,EAASU,AAArCvD,CAAAA,GAAc6C,QAAU,EAAE,AAAD,CAAqB,CAACG,EAAY,EAAI,CAAC,EAAGQ,EAAuBhB,EAAiBK,EAAOhH,IAAI,EAAI4G,GAAaF,EAAgBiB,GAAwB,CAAC,IAAI,CAG/NxE,CAAAA,EAAQmE,EAAQJ,CAAC,GAEbF,EAAOY,WAAW,EAElB,CAACD,CAAmB,GAIpBJ,EAAQM,eAAe,CAACP,EAAQJ,CAAC,CAAE,KAGvC1D,EAAgB8D,EAAS,SAAUlH,CAAG,CAAEoB,CAAI,EAC3B,MAATA,GACA+F,EAAQM,eAAe,CAACzH,EAAKoB,EAErC,GAEA,IAAK,IAAIsG,EAAI,EAAGA,EAAIL,EAA4BK,IACvCP,EAAQQ,SAAS,CAACrB,CAAa,CAACoB,EAAE,GAGnCP,EAAQM,eAAe,CAAC,KAAK,EAAGnB,CAAa,CAACoB,EAAE,EAGxDhB,EAAeO,IAAI,CAACE,GACpBJ,GACJ,GACA,IAAIa,EAAsBrB,EAAiBC,EACR,MAAA,IAAxBoB,GACPA,CAAAA,EAAsB,CAAC,IAAI,AAAD,EAE9B,IAAI,CAACC,UAAU,CAAG,CACdC,OAAQzB,EAAcG,GACtBJ,SAAUA,EACV2B,WAAYtB,EACZC,eAAgBA,EAChBkB,oBAAqBA,CACzB,CACJ,CAQA/B,WAAY,CACJ,IAAI,CAACrC,OAAO,CAACwE,oBAAoB,EACjC,CAAA,IAAI,CAACzD,OAAO,CAAG,IAAI,CAACN,aAAa,CAAC,IAAI,CAACM,OAAO,CAAA,EAGlD,IAAI,CAAC4B,qBAAqB,GAE1B,IAAI,CAAC8B,UAAU,GAEO,CAAA,IAAlB,IAAI,CAACC,MAAM,IAEX,IAAI,CAACC,QAAQ,EAErB,CAMApC,SAASqC,CAAS,CAAE,CAChB,IAAMC,EAAO,IAAI,CAAE9D,EAAU,IAAI,CAACA,OAAO,CAAG,EAAE,CAAEf,EAAU4E,GAAa,IAAI,CAAC5E,OAAO,CAAE8E,EAAc9E,EAAQ8E,WAAW,EAAI,EAAGC,EAAY/E,EAAQ+E,SAAS,EAAIC,OAAOC,SAAS,CAAEC,EAAY,EAAE,CAG9LC,EAAgB,CACZ,IAAK,EACL,IAAK,EACL,IAAM,CACV,EACIC,EAAMpF,EAAQoF,GAAG,CAAEC,EAAWrF,EAAQqF,QAAQ,EAAI,EAAGC,EAAStF,EAAQsF,MAAM,EAAIN,OAAOC,SAAS,CAAEM,EAAeC,EAAOC,EAAQ,EAwUpI,GAHIL,GAAOpF,EAAQ0F,WAAW,EAC1BN,CAAAA,EAAMpF,EAAQ0F,WAAW,CAAC9K,IAAI,CAAC,IAAI,CAAEwK,EAAG,EAExCA,EAAK,CAWL,GAVAI,EAAQJ,EACHO,OAAO,CAAC,QAAS,MACjBA,OAAO,CAAC,MAAO,MACfC,KAAK,CAAC5F,EAAQ6F,aAAa,EAAI,MAChC,CAAA,CAACR,GAAYA,EAAW,CAAA,GACxBA,CAAAA,EAAW,CAAA,EAEX,CAAA,CAACC,GAAUA,GAAUE,EAAMxE,MAAM,AAAD,GAChCsE,CAAAA,EAASE,EAAMxE,MAAM,CAAG,CAAA,EAExBhB,EAAQuF,aAAa,CACrBA,EAAgBvF,EAAQuF,aAAa,KAEpC,CAxML,IAAIO,EAAYC,EAAYC,EAAxBF,EAAS,EAAGC,EAAS,EAAGC,EAAU,CAAA,EACtCR,AAwMmCA,EAxM7BS,IAAI,CAAC,SAAUC,CAAS,CAAEhC,CAAC,EAC7B,IAAIiC,EAAQ,CAAA,EAAOC,EAAGC,EAAIC,EAAIC,EAAQ,GAEtC,GAAIrC,EAAI,GACJ,MAAO,CAAA,EAEX,IAAK,IAAIsC,EAAI,EAAGA,EAAIN,EAAUlF,MAAM,CAAEwF,IAAK,CAIvC,GAHAJ,EAAIF,CAAS,CAACM,EAAE,CAChBH,EAAKH,CAAS,CAACM,EAAI,EAAE,CACrBF,EAAKJ,CAAS,CAACM,EAAI,EAAE,CACjBJ,AAAM,MAANA,EAEA,OAEJ,GAAIA,AAAM,MAANA,GACA,GAAID,EACA,CAAA,GAAIG,AAAO,MAAPA,GAAcD,AAAO,MAAPA,EAAY,CAC1B,KAAOA,AAAO,MAAPA,GAAcG,EAAIN,EAAUlF,MAAM,EACrCqF,EAAKH,CAAS,CAAC,EAAEM,EAAE,AAKU,MAAA,IAAtBrB,CAAa,CAACkB,EAAG,EACxBlB,CAAa,CAACkB,EAAG,GAErBF,EAAQ,CAAA,CACZ,CAAA,MAGAA,EAAQ,CAAA,OAGP,AAA4B,KAAA,IAArBhB,CAAa,CAACiB,EAAE,EAEvBK,MAAMnF,KAAKtE,KAAK,CADrBuJ,EAAQA,EAAMG,IAAI,KAITD,CAAAA,MAAMF,IACX,CAACI,SAASJ,EAAK,GACfpB,CAAa,CAACiB,EAAE,GAJhBjB,CAAa,CAACiB,EAAE,GAMpBG,EAAQ,IAGRA,GAASH,CAEH,CAAA,MAANA,GACAL,IAEM,MAANK,GACAN,GAER,CACJ,GAIIX,CAAa,CAAC,IAAI,CAAGA,CAAa,CAAC,IAAI,CACvCa,EAAU,KAELb,CAAa,CAAC,IAAI,CAAGA,CAAa,CAAC,IAAI,CAC5Ca,EAAU,KAQThG,EAAQ8B,YAAY,GACjBgE,EAASC,EACT/F,EAAQ8B,YAAY,CAAG,IAGvB9B,EAAQ8B,YAAY,CAAG,IAG3B+C,EAAK5C,YAAY,CAAG,AAAIC,OAAO,cAC3BlC,EAAQ8B,YAAY,CACpB,cAwHJyD,EAtHGS,CAuHP,CACA,IAAIY,EAAS,EACb,IAAKnB,EAAQJ,EAAUI,GAASH,EAAQG,IAChCD,AAAoB,MAApBA,CAAK,CAACC,EAAM,CAAC,EAAE,CACfmB,IAGAC,AArTZ,SAAkBX,CAAS,CAAEY,CAAS,CAAEC,CAAK,CAAEC,CAAS,EACpD,IAAI9C,EAAI,EAAGkC,EAAI,GAAIE,EAAK,GAAID,EAAK,GAAIE,EAAQ,GAAIU,EAAe,EAAGC,EAAS,EAI5E,SAASC,EAAKX,CAAC,EACXJ,EAAIF,CAAS,CAACM,EAAE,CAChBF,EAAKJ,CAAS,CAACM,EAAI,EAAE,CACrBH,EAAKH,CAAS,CAACM,EAAI,EAAE,AACzB,CAIA,SAASY,EAAShL,CAAI,EACd8I,EAAUlE,MAAM,CAAGkG,EAAS,GAC5BhC,EAAUzB,IAAI,CAAC,CAACrH,EAAK,EAErB8I,CAAS,CAACgC,EAAO,CAAChC,CAAS,CAACgC,EAAO,CAAClG,MAAM,CAAG,EAAE,GAAK5E,GACpD8I,CAAS,CAACgC,EAAO,CAACzD,IAAI,CAACrH,EAE/B,CAIA,SAASqH,IACL,GAAIqB,EAAcmC,GAAgBA,EAAelC,EAAW,CAExD,EAAEkC,EACFV,EAAQ,GACR,MACJ,CACKvG,EAAQqH,WAAW,GAChB,CAACZ,MAAMa,WAAWf,KAAWI,SAASJ,IACtCA,EAAQe,WAAWf,GACnBa,EAAS,WAEHX,MAAMnF,KAAKtE,KAAK,CAACuJ,IAKvBa,EAAS,WAJTb,EAAQA,EAAMZ,OAAO,CAAC,MAAO,KAC7ByB,EAAS,UAMbrG,EAAQC,MAAM,CAAGkG,EAAS,GAC1BnG,EAAQ0C,IAAI,CAAC,EAAE,EAKf1C,CAAO,CAACmG,EAAO,CAACJ,EAAU,CAAGP,EAEjCA,EAAQ,GACR,EAAEW,EACF,EAAED,CACN,CACA,GAAKf,EAAUQ,IAAI,GAAG1F,MAAM,EAGxBkF,AAAwB,MAAxBA,EAAUQ,IAAI,EAAE,CAAC,EAAE,EAGvB,KAAOxC,EAAIgC,EAAUlF,MAAM,CAAEkD,IAEzB,GADAiD,EAAKjD,GACDkC,AAAM,MAANA,EAEA,IADAe,EAAK,EAAEjD,GAEH,AADGA,EAAIgC,EAAUlF,MAAM,EACnBoF,CAAAA,AAAM,MAANA,GAAaE,AAAO,MAAPA,GAAcD,AAAO,MAAPA,CAAS,GAGpCD,CAAAA,AAAM,MAANA,GAAcA,AAAM,MAANA,GAAaE,AAAO,MAAPA,CAAU,GACrCC,CAAAA,GAASH,CAAAA,EAEbe,EAAK,EAAEjD,QAUNkC,IAAMb,EACX9B,IAIA8C,GAASH,EAGjB3C,IACJ,EAyNqB+B,CAAK,CAACC,EAAM,CAAEA,EAAQJ,EAAWuB,GAG7C,CAAA,CAAC5G,EAAQqH,WAAW,EAAIrH,AAA+B,IAA/BA,EAAQqH,WAAW,CAACrG,MAAM,AAAK,GACxDkE,EAAUlE,MAAM,EAChBkE,CAAS,CAAC,EAAE,CAAClE,MAAM,EACnBkE,AAAoB,SAApBA,CAAS,CAAC,EAAE,CAAC,EAAE,EACf,CAAClF,EAAQuH,UAAU,EACnBvH,CAAAA,EAAQuH,UAAU,CAAGC,AA3H7B,SAA0BpK,CAAI,CAAEqK,CAAK,EACjC,IAAMC,EAAS,aAAcC,EAAS,EAAE,CAAEC,EAAM,EAAE,CAC9CC,EAAOC,EAAgB,EAAE,CAAEC,EAAkB7D,EAAI,EAAG8D,EAAgB,CAAA,EAAOxB,EAI/E,IAHI,CAAA,CAACiB,GAASA,EAAQrK,EAAK4D,MAAM,AAAD,GAC5ByG,CAAAA,EAAQrK,EAAK4D,MAAM,AAAD,EAEfkD,EAAIuD,EAAOvD,IACd,GAAI,AAAmB,KAAA,IAAZ9G,CAAI,CAAC8G,EAAE,EACd9G,CAAI,CAAC8G,EAAE,EAAElD,OAYT,IAAKwF,EAAI,EAXTqB,EAAQzK,CAAI,CAAC8G,EAAE,CACVwC,IAAI,GACJf,OAAO,CAAC,MAAO,KACfA,OAAO,CAAC,MAAO,KACfA,OAAO,CAAC,MAAO,KACfC,KAAK,CAAC,KACXkC,EAAgB,CACZ,GACA,GACA,GACH,CACWtB,EAAIqB,EAAM7G,MAAM,CAAEwF,IACtBA,EAAIsB,EAAc9G,MAAM,GACxB6G,CAAK,CAACrB,EAAE,CAAGyB,SAASJ,CAAK,CAACrB,EAAE,CAAE,IAC1BqB,CAAK,CAACrB,EAAE,GACRoB,CAAG,CAACpB,EAAE,CAAG,AAAC,CAACoB,CAAG,CAACpB,EAAE,EAAIoB,CAAG,CAACpB,EAAE,CAAGqB,CAAK,CAACrB,EAAE,CAClCqB,CAAK,CAACrB,EAAE,CACRoB,CAAG,CAACpB,EAAE,CACN,AAAqB,KAAA,IAAdmB,CAAM,CAACnB,EAAE,CACZmB,CAAM,CAACnB,EAAE,GAAKqB,CAAK,CAACrB,EAAE,EACtBmB,CAAAA,CAAM,CAACnB,EAAE,CAAG,CAAA,CAAI,EAIpBmB,CAAM,CAACnB,EAAE,CAAGqB,CAAK,CAACrB,EAAE,CAEpBqB,CAAK,CAACrB,EAAE,CAAG,GACPqB,CAAK,CAACrB,EAAE,CAAG,IACXsB,CAAa,CAACtB,EAAE,CAAG,KAGnBsB,CAAa,CAACtB,EAAE,CAAG,OAGlBqB,CAAK,CAACrB,EAAE,CAAG,IAChBqB,CAAK,CAACrB,EAAE,EAAI,IACZsB,CAAa,CAACtB,EAAE,CAAG,KACnBwB,EAAgB,CAAA,GAEVF,CAAa,CAACtB,EAAE,CAACxF,MAAM,EAC7B8G,CAAAA,CAAa,CAACtB,EAAE,CAAG,IAAG,IAO9C,GAAIwB,EAAe,CAEf,IAAKxB,EAAI,EAAGA,EAAImB,EAAO3G,MAAM,CAAEwF,IACvBmB,AAAc,CAAA,IAAdA,CAAM,CAACnB,EAAE,CACLoB,CAAG,CAACpB,EAAE,CAAG,IACTsB,AAAqB,OAArBA,CAAa,CAACtB,EAAE,EAChBsB,AAAqB,SAArBA,CAAa,CAACtB,EAAE,EAChBsB,CAAAA,CAAa,CAACtB,EAAE,CAAG,IAAG,EAGrBoB,CAAG,CAACpB,EAAE,CAAG,IAAMsB,AAAqB,OAArBA,CAAa,CAACtB,EAAE,EACpCsB,CAAAA,CAAa,CAACtB,EAAE,CAAG,IAAG,QAa9B,CAR6B,IAAzBsB,EAAc9G,MAAM,EACpB8G,AAAqB,OAArBA,CAAa,CAAC,EAAE,EAChBA,AAAqB,OAArBA,CAAa,CAAC,EAAE,EAChBA,CAAAA,CAAa,CAAC,EAAE,CAAG,IAAG,EAE1BC,EAAmBD,EAAcI,IAAI,CAAC,KAGjC,AAAClI,CAAAA,EAAQkB,WAAW,EAAI2D,EAAK3D,WAAW,AAAD,CAAE,CAAC6G,EAAiB,EAKzDA,GAHHtI,EAAUoF,EAAM,oBACT6C,EAGf,CACA,OAAOA,CACX,EAmC8C3G,CAAO,CAAC,EAAE,CAAA,EAyBpD,IAAI,CAACsB,SAAS,EAClB,CACA,OAAOtB,CACX,CAMAyB,YAAa,CACT,IAAMxC,EAAU,IAAI,CAACA,OAAO,CAAEe,EAAU,IAAI,CAACA,OAAO,EAAI,EAAE,CAAEsE,EAAWrF,EAAQqF,QAAQ,EAAI,EAAGC,EAAStF,EAAQsF,MAAM,EAAIN,OAAOC,SAAS,CAAEH,EAAc9E,EAAQ8E,WAAW,EAAI,EAAGC,EAAY/E,EAAQ+E,SAAS,EAAIC,OAAOC,SAAS,CACpO,GAAIjF,EAAQmI,KAAK,CAAE,CACf,IAAIA,EAAQnI,EAAQmI,KAAK,AACJ,CAAA,UAAjB,OAAOA,GACPA,CAAAA,EAAQhJ,EAAIiJ,cAAc,CAACD,EAAK,EAEpC,EAAE,CAAC3E,OAAO,CAAC5I,IAAI,CAACuN,EAAME,oBAAoB,CAAC,MAAO,CAACC,EAAIC,KAC/CA,GAASlD,GAAYkD,GAASjD,GAC9B,EAAE,CAAC9B,OAAO,CAAC5I,IAAI,CAAC0N,EAAGE,QAAQ,CAAE,CAACC,EAAMC,KAChC,IAAM/H,EAAMI,CAAO,CAAC2H,EAAQ5D,EAAY,CACpCZ,EAAI,EACR,GAAI,AAACuE,CAAAA,AAAiB,OAAjBA,EAAKE,OAAO,EACbF,AAAiB,OAAjBA,EAAKE,OAAO,AAAQ,GACpBD,GAAS5D,GACT4D,GAAS3D,EAOT,IANKhE,CAAO,CAAC2H,EAAQ5D,EAAY,EAC7B/D,CAAAA,CAAO,CAAC2H,EAAQ5D,EAAY,CAAG,EAAE,AAAD,EAEpC/D,CAAO,CAAC2H,EAAQ5D,EAAY,CAACyD,EAAQlD,EAAS,CAAGoD,EAAKG,SAAS,CAGxDL,EAAQlD,GAAYnB,GACvBvD,AAA8B,KAAK,IAAnCA,CAAG,CAAC4H,EAAQlD,EAAWnB,EAAE,EACzBvD,CAAG,CAAC4H,EAAQlD,EAAWnB,EAAE,CAAG,KAC5BA,GAGZ,EAER,GACA,IAAI,CAAC7B,SAAS,EAClB,CACA,OAAOtB,CACX,CASAuB,eAAgB,CACZ,IAAMlF,EAAO,IAAI,CAAEoD,EAAQ,IAAI,CAACA,KAAK,CAAER,EAAU,IAAI,CAACA,OAAO,CAAkB6I,EAAiB7I,EAAQ8I,aAAa,CAAEC,EAAkBpJ,EAAMK,GAC3IgJ,EAAiB,EAAGC,EAAmB,AAAiC,IAAhCjJ,CAAAA,EAAQkJ,eAAe,EAAI,CAAA,QACvE,EAAKnJ,EAAaC,KAIdiJ,EAAmB,KACnBA,CAAAA,EAAmB,GAAG,EAE1B,OAAOjJ,EAAQG,MAAM,CACrB,OAAOH,EAAQE,OAAO,CACtB,OAAOF,EAAQI,UAAU,EA0EzB+I,AAtEA,SAASA,EAAaC,CAAY,EAK9B,SAASC,EAAQnN,CAAG,CAAEoN,CAAI,CAAEC,CAAE,EAC1B,GAAI,CAACrN,GACD,CAAC,yBAAyBsN,IAAI,CAACtN,GAI/B,OAHIA,GAAO8D,EAAQ/D,KAAK,EACpB+D,EAAQ/D,KAAK,CAAC,eAEX,CAAA,EASX,SAASwN,IAEDZ,GAAkBrI,EAAMkJ,WAAW,GAAKxN,GAExCkB,CAAAA,EAAK+E,eAAe,CAChBwH,WAAWR,EAAcF,EAAgB,CAErD,CAiBA,OA/BIG,IACAhH,aAAahF,EAAK+E,eAAe,EACjC3B,EAAMkJ,WAAW,CAAGxN,GAaxBkD,EAAU,CACNlD,IAAKA,EACLK,SAAUgN,GAAM,OAChBpM,QAAS,SAAUR,CAAG,EACd6D,GAAO4C,QACPkG,EAAK3M,GAET8M,GACJ,EACAxN,MAAO,SAAUF,CAAG,CAAEL,CAAI,EAItB,MAHI,EAAEsN,EArDsD,GAsDxDS,IAEGzJ,EAAQ/D,KAAK,GAAGP,EAAMK,EACjC,CACJ,GACO,CAAA,CACX,CACKsN,EAAQN,EAAgB5I,MAAM,CAAE,SAAUxD,CAAG,EAC9C6D,EAAMoJ,MAAM,CAAC,CACTxM,KAAM,CACFgI,IAAKzI,CACT,CACJ,EACJ,EAAG,SACM0M,EAAQN,EAAgB7I,OAAO,CAAE,SAAUvD,CAAG,EAC/C6D,EAAMoJ,MAAM,CAAC,CACTxM,KAAM,CACFsD,KAAM/D,CACV,CACJ,EACJ,IACI0M,EAAQN,EAAgB3I,UAAU,CAAE,SAAUzD,CAAG,EAC7C6D,EAAMoJ,MAAM,CAAC,CACTxM,KAAM,CACF2D,QAASpE,CACb,CACJ,EACJ,EAGZ,EACa,CAAA,GACNoD,EAAaC,GACxB,CASAyC,wBAAyB,CACrB,IAAMrF,EAAO,IAAI,CAAE4C,EAAU,IAAI,CAACA,OAAO,CAAE6J,EAAuB7J,EAAQ6J,oBAAoB,CAAErJ,EAAQ,IAAI,CAACA,KAAK,CAAEsJ,EAAcC,KAAKnC,GAAG,CAAC,AAAiC,IAAhC5H,CAAAA,EAAQkJ,eAAe,EAAI,CAAA,EAAW,KAK5Kc,EAAW,KACb,GAAIhK,EAAQiK,sBAAsB,CAC9B,OAAOjK,EAAQiK,sBAAsB,CAEzC,IAAMC,EAAW,6BACXC,EAAQ,AAACD,CAAAA,EAASE,MAAM,CAACpK,EAAQ8E,WAAW,EAAI,IAAM,GAAE,EACzD,CAAA,AAAC9E,CAAAA,EAAQqF,QAAQ,EAAI,CAAA,EAAK,CAAA,EAC3BgF,EAAMH,EAASE,MAAM,CAACvK,EAAKG,EAAQ+E,SAAS,CAAE,MAAQ,KAI1D,OAHIxF,EAAQS,EAAQsF,MAAM,GACtB+E,CAAAA,GAAOrK,EAAQsF,MAAM,CAAG,CAAA,EAErB,CAAC,EAAE6E,EAAM,CAAC,EAAEE,EAAI,CAAC,AAC5B,EAiEA,OAhCIR,IACA,OAAO7J,EAAQ6J,oBAAoB,CACnCS,AA9BJ,SAASA,EAAWC,CAAE,EAYlBnL,EAAU,CACNlD,IAZQ,CACR,gDACA2N,EACA,SACAG,IACA,kHAIahK,EAAQwK,YAAY,CACpC,CAACtC,IAAI,CAAC,KAGH3L,SAAU,OACVY,QAAS,SAAU3B,CAAI,EACnB+O,EAAG/O,GACCwE,EAAQ8I,aAAa,EACrB1L,CAAAA,EAAK+E,eAAe,CAAGwH,WAAW,WAC9BW,EAAWC,EACf,EAAGT,EAAW,CAEtB,EACA7N,MAAO,SAAUF,CAAG,CAAEL,CAAI,EACtB,OAAOsE,EAAQ/D,KAAK,GAAGP,EAAMK,EACjC,CACJ,EACJ,EAGe,SAAUP,CAAI,EAErB,IAAMuF,EAAUvF,EAAKiP,MAAM,CAC3B,GAAI,CAAC1J,GAAWA,AAAmB,IAAnBA,EAAQC,MAAM,CAC1B,MAAO,CAAA,EAGX,IAAM0J,EAAW3J,EAAQ4J,MAAM,CAAC,CAACD,EAAUxD,IAAW6C,KAAKnC,GAAG,CAAC8C,EAAUxD,EAAOlG,MAAM,EAAG,GAEzFD,EAAQyC,OAAO,CAAC,AAAC0D,IACb,IAAK,IAAIhD,EAAI,EAAGA,EAAIwG,EAAUxG,IACD,KAAA,IAAdgD,CAAM,CAAChD,EAAE,EAChBgD,CAAAA,CAAM,CAAChD,EAAE,CAAG,IAAG,CAG3B,GACI1D,GAAO4C,OACP5C,EAAMoJ,MAAM,CAAC,CACTxM,KAAM,CACF2D,QAASA,CACb,CACJ,IAGA3D,EAAK2D,OAAO,CAAGA,EACf3D,EAAKiF,SAAS,GAEtB,IAGG,CAAA,CACX,CAeAqE,KAAKkE,CAAG,CAAEC,CAAM,CAAE,CAWd,MAVmB,UAAf,OAAOD,IACPA,EAAMA,EAAIjF,OAAO,CAAC,aAAc,IAE5BkF,GAAU,UAAUrB,IAAI,CAACoB,IACzBA,CAAAA,EAAMA,EAAIjF,OAAO,CAAC,MAAO,GAAE,EAE3B,IAAI,CAAC1D,YAAY,EACjB2I,CAAAA,EAAMA,EAAIjF,OAAO,CAAC,IAAI,CAAC1D,YAAY,CAAE,QAAO,GAG7C2I,CACX,CAMAnG,YAAa,CACT,IAAM1D,EAAU,IAAI,CAACA,OAAO,EAAI,EAAE,CAC9BF,EAAME,EAAQC,MAAM,CACxB,KAAOH,KACH,IAAI,CAACiK,WAAW,CAAC/J,CAAO,CAACF,EAAI,CAAEA,EAEvC,CAYAiK,YAAY5D,CAAM,CAAErG,CAAG,CAAE,CACrB,IAAMe,EAAa,IAAI,CAACA,UAAU,CAAEb,EAAU,IAAI,CAACA,OAAO,CAAG,IAAI,CAACA,OAAO,EAAI,EAAE,CAAEiB,EAAkB,IAAI,CAACA,eAAe,CAAE+I,EAAY,IAAI,CAAC1G,UAAU,EAAEzB,SAASoI,QAAQnK,KAAS,GAAIoK,EAAS,EAAE,CAAE1K,EAAe,IAAI,CAACA,YAAY,CAAgD2K,EAAa7D,AAA7C,CAAA,IAAI,CAACrH,OAAO,CAACqH,WAAW,EAAI,EAAE,AAAD,CAA2B,CAACxG,EAAI,CAAEsK,EAAgB,AAACJ,GAC5TxK,GAAc6K,OACXtL,AAAsC,aAAtCA,EAAMS,EAAa6K,KAAK,CAAC,CAAC,EAAE,CAAChP,IAAI,EAAqB8O,AAAe,WAAfA,EAAyBG,EAAgB9L,EAAQ2H,EAAOtJ,IAAI,EACtH+C,EAAMuG,EAAOlG,MAAM,CAAExE,EAAK8O,EAAUC,EAASC,EAAeC,EAASC,EAAMC,EAI/E,IAHK/J,CAAU,CAACf,EAAI,EAChBe,CAAAA,CAAU,CAACf,EAAI,CAAG,EAAE,AAAD,EAEhBF,KACHnE,EAAMyO,CAAM,CAACtK,EAAI,EAAIuG,CAAM,CAACvG,EAAI,CAChC4K,EAAU,IAAI,CAAC7E,IAAI,CAAClK,GAEpB8O,EAAWhE,WADXkE,EAAgB,IAAI,CAAC9E,IAAI,CAAClK,EAAK,CAAA,IAGK,KAAA,IAAzBoF,CAAU,CAACf,EAAI,CAACF,EAAI,EAC3BiB,CAAAA,CAAU,CAACf,EAAI,CAACF,EAAI,CAAG4K,CAAM,EAI7BJ,GACCxK,AAAQ,IAARA,GAAaqB,GAAmB,CAACqJ,EAClCnE,CAAM,CAACvG,EAAI,CAAG,GAAK4K,EAEd,CAACC,IAAkBF,GACxBpE,CAAM,CAACvG,EAAI,CAAG2K,EAGVA,EAAW,SACXJ,AAAe,UAAfA,EACAhE,EAAO0E,UAAU,CAAG,CAAA,EAGpB1E,EAAO2E,SAAS,CAAG,CAAA,EAEQ,KAAA,IAApB3E,CAAM,CAACvG,EAAM,EAAE,EACtBgL,CAAAA,EAAaL,EAAWpE,CAAM,CAACvG,EAAM,EAAE,AAAD,IAMtC4K,GAASvK,QACTyK,CAAAA,EAAU,IAAI,CAACK,SAAS,CAACtP,EAAG,EAG5BuO,GAAarL,EAAS+L,IAAYP,AAAe,UAAfA,GAClCD,CAAM,CAACtK,EAAI,CAAGnE,EACd0K,CAAM,CAACvG,EAAI,CAAG8K,EACdvE,EAAO0E,UAAU,CAAG,CAAA,EAIW,KAAA,IAApB1E,CAAM,CAACvG,EAAM,EAAE,GACtB+K,CAAAA,EAAOD,EAAUvE,CAAM,CAACvG,EAAM,EAAE,AAAD,IAClBgL,GACT,AAAsB,KAAA,IAAfA,IACH,IAAI,CAACI,iBAAiB,EACtB,IAAI,CAACxE,UAAU,CAAG,IAAI,CAACwE,iBAAiB,CACxCpL,EAAMuG,EAAOlG,MAAM,CACnB,IAAI,CAAC+K,iBAAiB,CAClB,IAAI,CAAC7K,WAAW,CAAC,IAAI,CAACqG,UAAU,CAAC,CAC5B9F,WAAW,EAGpByF,EAAO8E,QAAQ,CAAG,CAAA,GAG1BL,EAAaD,KAIjBxE,CAAM,CAACvG,EAAI,CAAG4K,AAAY,KAAZA,EAAiB,KAAOA,EAC1B,IAAR5K,GACCuG,CAAAA,EAAO0E,UAAU,EACd1E,EAAO2E,SAAS,AAAD,GACnB3E,CAAAA,EAAO+E,KAAK,CAAG,CAAA,CAAG,IAelC,GALIlB,GAAa7D,EAAO+E,KAAK,EACzBlL,CAAAA,CAAO,CAACF,EAAI,CAAGe,CAAU,CAACf,EAAI,AAAD,EAI7BkK,GAAaY,GAAc,IAAI,CAAC3L,OAAO,CAACkM,IAAI,CAC5C,CAAA,IAAKrL,EAAM,EAAGA,EAAME,EAAQC,MAAM,CAAEH,IAEhC,GADAE,CAAO,CAACF,EAAI,CAACsL,OAAO,GAChBnK,EAAiB,CACjB,IAAMoK,EAAerL,CAAO,CAACF,EAAI,CAACwL,GAAG,GACjCD,GACArL,CAAO,CAACF,EAAI,CAACyL,OAAO,CAACF,EAE7B,CACJ,CAER,CAOAN,UAAUtP,CAAG,CAAE,CACX,IAAMsP,EAAY,IAAI,CAAC9L,OAAO,CAAC8L,SAAS,CACpCS,EAAKrS,EAAKwN,EAAQH,EAAa,IAAI,CAACvH,OAAO,CAACuH,UAAU,EAAI,IAAI,CAACA,UAAU,CAAElG,EAC/E,GAAIyK,EACAS,EAAMT,EAAUtP,QAEf,GAAI,AAAe,UAAf,OAAOA,EAAkB,CAE9B,GAAK+K,EAcDG,CAAAA,EAAS,IAAI,CAACxG,WAAW,CAACqG,EAAW,AAAD,GAGhCG,CAAAA,EAAS,IAAI,CAACxG,WAAW,CAAC,aAAa,AAAD,EAE1CG,CAAAA,EAAQ7E,EAAI6E,KAAK,CAACqG,EAAOvG,KAAK,CAAA,GAE1BoL,CAAAA,EAAM7E,EAAOtG,MAAM,CAACC,EAAK,OApB7B,IAAKnH,KAAO,IAAI,CAACgH,WAAW,CAGxB,GAFAwG,EAAS,IAAI,CAACxG,WAAW,CAAChH,EAAI,CAC9BmH,EAAQ7E,EAAI6E,KAAK,CAACqG,EAAOvG,KAAK,EACnB,CACP,IAAI,CAACoG,UAAU,CAAGA,EAAarN,EAC/B,IAAI,CAAC6R,iBAAiB,CAAGrE,EAAOjG,WAAW,CAC3C8K,EAAM7E,EAAOtG,MAAM,CAACC,GACpB,KACJ,CAgBJ,CAACA,IACG7E,EAAI6E,KAAK,CAAC,wBACV7E,CAAAA,EAAMA,EACDmJ,OAAO,CAAC,sCAAuC,WAC/CA,OAAO,CAAC,yBAA0B,MAClCA,OAAO,CAAC,wBAAyB,WAAU,EAKhD,AAAiB,UAAjB,MAHJtE,CAAAA,EAAQC,KAAKtE,KAAK,CAACR,EAAG,GAIlB6E,AAAU,OAAVA,GACAA,EAAMmL,OAAO,CACbD,EAAOlL,EAAMmL,OAAO,GAChBnL,AACI,IADJA,EAAMoL,iBAAiB,GAItB/M,EAAS2B,IACdkL,CAAAA,EAAMlL,EAAQ,AAAwC,IAAxC,AAAC,IAAIC,KAAKD,GAAQoL,iBAAiB,EAAS,EAGtE,CACA,OAAOF,CACX,CAeAG,SAAU,CACN,GAAI,IAAI,CAAC3L,OAAO,CACZ,OAAO,IAAI,CAACN,aAAa,CAAC,IAAI,CAACM,OAAO,GAAG4L,MAAM,EAEvD,CAMAjI,QAAS,CACL,GAAI,IAAI,CAAC1E,OAAO,CAAC0E,MAAM,CACnB,OAAO,IAAI,CAAC1E,OAAO,CAAC0E,MAAM,CAAC9J,IAAI,CAAC,IAAI,CAAE,IAAI,CAACmG,OAAO,CAE1D,CASA4D,UAAW,CACP,IAAM5D,EAAU,IAAI,CAACA,OAAO,CAAG,IAAI,CAACA,OAAO,EAAI,EAAE,CAAiBf,EAAU,IAAI,CAACA,OAAO,CAAE4M,EAAoB,EAAE,CAC5GxQ,EAAO,SAAUgH,EAAQhG,EAAM8G,EAAGsC,EAAG5K,EAAG2H,EAAahD,EAAcoD,EAASkJ,EAAaC,EAASC,EAEtG,GADAnK,AAF8D,EAAE,CAEvD5B,MAAM,CAAGD,EAAQC,MAAM,CAC5BhB,EAAQ2E,QAAQ,EAAI3E,EAAQ0C,aAAa,CAAE,CAE3C,GAAI,IAAI,CAACV,eAAe,CACpB,IAAKkC,EAAI,EAAGA,EAAInD,EAAQC,MAAM,CAAEkD,IAAK,CACjC,IAAM8I,EAASjM,CAAO,CAACmD,EAAE,CACpB3E,EAAQyN,EAAOpP,IAAI,GACpBoP,CAAAA,EAAOpP,IAAI,CAAGiC,EAAKmN,EAAOC,KAAK,GAAI,IAAIC,QAAQ,EAAC,CAExD,CAMJ,IAAK3J,EAAc,EAHnBH,EAAS,EAAE,CACXyJ,EAAcM,AAtsC1B,SAAwBC,CAAe,CAAElK,CAAc,EACnD,IACImK,EAAGnJ,EAAGoJ,EADJT,EAAc,EAAE,CAAEU,EAAkB,EAAE,CAG5C,IAAKrJ,EAAI,EAAGA,EAAIkJ,EAAiBlJ,GAAQ,EACrC2I,EAAYpJ,IAAI,CAAC,CAAA,GAGrB,IAAK4J,EAAI,EAAGA,EAAInK,EAAelC,MAAM,CAAEqM,GAAQ,EAE3C,IAAKnJ,EAAI,EADToJ,EAAoBpK,CAAc,CAACmK,EAAE,CAACG,0BAA0B,GACpDtJ,EAAIoJ,EAAkBtM,MAAM,CAAEkD,GAAQ,EAC9C2I,CAAW,CAACS,CAAiB,CAACpJ,EAAE,CAAC,CAAG,CAAA,EAI5C,IAAKA,EAAI,EAAGA,EAAI2I,EAAY7L,MAAM,CAAEkD,GAAQ,EACpC2I,CAAW,CAAC3I,EAAE,EACdqJ,EAAgB9J,IAAI,CAACS,GAG7B,OAAOqJ,CACX,EAirCyCxM,GAASC,QAAU,EAAG,IAAI,CAACqD,UAAU,CAACnB,cAAc,EAE3DK,EAAc,IAAI,CAACc,UAAU,CAACnB,cAAc,CAAClC,MAAM,CAAEuC,IAInEI,AAHJA,CAAAA,EAAU,IAAI,CAACU,UAAU,CAACnB,cAAc,CAACK,EAAY,AAAD,EAGxCkK,eAAe,CAACZ,IACxBD,EAAkBnJ,IAAI,CAACE,GAI/B,KAAOkJ,EAAY7L,MAAM,CAAG,GAAG,CAQ3B,IANA2C,AADAA,CAAAA,EAAU,IAAIC,CAAc,EACpBK,eAAe,CAAC,EAAG,KAGb,KADd8I,CAAAA,EAAQF,EAAY7B,OAAO,CAAC,EAAC,GAEzB6B,EAAYa,MAAM,CAACX,EAAO,GAEzB7I,EAAI,EAAGA,EAAI,IAAI,CAACG,UAAU,CAACC,MAAM,CAAEJ,IAGpCP,EAAQM,eAAe,CAAC,KAAK,EAAG,IAAI,CAACI,UAAU,CAACD,mBAAmB,CAACF,EAAE,EAItEP,EAAQ8J,eAAe,CAACZ,IACxBD,EAAkBnJ,IAAI,CAACE,EAE/B,CAgBA,GAdIiJ,EAAkB5L,MAAM,CAAG,GAC3B4L,CAAiB,CAAC,EAAE,CAACe,OAAO,CAAC3M,MAAM,CAAG,GAElC,AAAmB,KAAA,IADvB8L,CAAAA,EAAU/L,GAAS,CAAC6L,CAAiB,CAAC,EAAE,CAACe,OAAO,CAAC,EAAE,CAACC,WAAW,EAAI,CAAC,EAAE,AAAD,IAE7Dd,EAAQlB,UAAU,CAClBxP,EAAO,WAED0Q,EAAQjB,SAAS,EACvBzP,CAAAA,EAAO,UAAS,GAMxBA,AAAS,aAATA,EACA,IAAKmH,EAAc,EAAGA,EAAcqJ,EAAkB5L,MAAM,CAAEuC,IAE1D,IAAK3H,EAAI,EADT+H,EAAUiJ,CAAiB,CAACrJ,EAAY,CAC5B3H,EAAI+H,EAAQgK,OAAO,CAAC3M,MAAM,CAAEpF,IACE,MAAlC+H,EAAQgK,OAAO,CAAC/R,EAAE,CAACiS,UAAU,EAC7BlK,CAAAA,EAAQgK,OAAO,CAAC/R,EAAE,CAACiS,UAAU,CAAG,MAAK,EAMrD,IAAKtK,EAAc,EAAGA,EAAcqJ,EAAkB5L,MAAM,CAAEuC,IAAe,CAKzE,IAAKiD,EAAI,EAJT7C,EAAUiJ,CAAiB,CAACrJ,EAAY,CAGxCnG,EAAO,EAAE,CACGoJ,EAAIzF,CAAO,CAAC,EAAE,CAACC,MAAM,CAAEwF,IAC/BpJ,CAAI,CAACoJ,EAAE,CAAG7C,EAAQwD,IAAI,CAACpG,EAASyF,EAGpCpD,CAAAA,CAAM,CAACG,EAAY,CAAG,CAClBnG,KAAAA,EACA0Q,WAAY1Q,CAAI,CAAC,EAAE,EAAKuG,CAAAA,EAAQoK,YAAY,CACxC3Q,CAAI,CAAC,EAAE,EAAE,CAAC,EAAE,CACZA,CAAI,CAAC,EAAE,EAAEkG,CAAAA,GAAM,KAAK,CAC5B,EACIK,EAAQ/F,IAAI,EACZwF,CAAAA,CAAM,CAACG,EAAY,CAAC3F,IAAI,CAAG+F,EAAQ/F,IAAI,AAAD,EAE7B,aAATxB,IACAgH,CAAM,CAACG,EAAY,CAACyK,cAAc,CAAG,EACrC5K,CAAM,CAACG,EAAY,CAACuK,UAAU,CAAG,EAEzC,CAEAvN,EAAe,CAAE6C,OAAAA,CAAO,EAEpBhH,AAAS,WAATA,GAAsB,AAAC,IAAI,CAAC6R,YAAY,EACxC,IAAI,CAACA,YAAY,CAAC7R,IAAI,GAAKA,GAM3B,IAAI,CAAC6R,YAAY,CAAG,CAAE7R,KAAAA,CAAK,EACd,aAATA,GACA,CAAA,IAAI,CAAC6R,YAAY,CAACC,WAAW,CAAG,CAAA,CAAI,GALxC9R,EAAO,IAAI,CAAC6R,YAAY,CAAG,KAAK,EAS/B,IAAI,CAACzN,KAAK,EACXb,EAAM,CAAA,EAAMY,EAAc,CAAE6K,MAAO,IAAI,CAAC6C,YAAY,EAAI,CAAC,CAAE,GAE/DjO,EAAQ2E,QAAQ,GAAGpE,GAGnBP,EAAQ0C,aAAa,GAAG,IAAI,CAAEnC,EAClC,CACJ,CAMA4N,mBAAmBC,CAAI,CAAE,CACrB,IAAMpO,EAAU,IAAI,CAACiO,YAAY,CAC5BjO,IAKD,CAACoO,EAAKpO,OAAO,CAAC5D,IAAI,EAAI4D,EAAQ5D,IAAI,EAClCgS,CAAAA,EAAKhS,IAAI,CAAG4D,EAAQ5D,IAAI,AAAD,EAEtBgS,EAAKpO,OAAO,CAACkO,WAAW,EACzBlO,AAAwB,CAAA,IAAxBA,EAAQkO,WAAW,EACnBE,CAAAA,EAAKF,WAAW,CAAGlO,EAAQkO,WAAW,AAAD,EAE7C,CAYAtE,OAAO5J,CAAO,CAAEqO,CAAM,CAAE,CACpB,IAAM7N,EAAQ,IAAI,CAACA,KAAK,CAAED,EAAeC,EAAMR,OAAO,CAClDA,IAEAA,EAAQ0C,aAAa,CAAG,SAAU4L,CAAY,CAAEhO,CAAW,EACvD,GAAI,CAACA,EACD,OAKJ,IAAM8K,EAAQ5K,EAAM4K,KAAK,CAAC,EAAE,CAAE6C,EAAeK,EAAaL,YAAY,AAGlEA,CAAAA,GAAgB7C,GAAU,CAAA,AAACA,EAAMhP,IAAI,GAAK6R,EAAa7R,IAAI,EAAI,CAACgP,EAAMpL,OAAO,CAAC5D,IAAI,EACjFgP,EAAM8C,WAAW,EACdD,AAA6B,CAAA,IAA7BA,EAAaC,WAAW,EACxB9C,AAA8B,KAAK,IAAnCA,EAAMpL,OAAO,CAACkO,WAAW,AAAW,EACxC9C,EAAMxB,MAAM,CAAC,CAAC,EAAG,CAAA,GAIjB,AAACtJ,CAAAA,GAAa8C,QAAU,EAAE,AAAD,EAAGI,OAAO,CAAC,SAAU+K,CAAa,EACvD,OAAOA,EAAcT,UAAU,AACnC,GAEJtN,EAAMoJ,MAAM,CAACtJ,EAAa+N,EAAQ,CAAA,EACtC,EAEA1O,EAAM,CAAA,EAAMY,EAAanD,IAAI,CAAE4C,GAE3BO,EAAanD,IAAI,EAAEyM,sBAAwB,CAAC7J,EAAQe,OAAO,EAC3D,OAAOR,EAAanD,IAAI,CAAC2D,OAAO,CAEpC,IAAI,CAACc,IAAI,CAACtB,EAAanD,IAAI,EAAI,CAAC,EAAGmD,GAE3C,CACJ,CAEAjB,EAAUX,IAAoG,kBAAmB,WAEzH,IAAI,CAAC6P,OAAO,EAEX,CAAA,CAAC,IAAI,CAAChO,KAAK,CAAC4K,KAAK,CAACpK,MAAM,EAAI,IAAI,CAACR,KAAK,CAAC4K,KAAK,CAAC,EAAE,GAAK,IAAI,AAAD,GACxD,IAAI,CAAC5K,KAAK,CAACpD,IAAI,EAAE+Q,mBAAmB,IAAI,CAEhD,GAGA7O,EAAUT,IAAwG,OAAQ,SAAU5B,CAAC,EACjI,IAAMuD,EAAQ,IAAI,CAAEiO,EAAWxR,EAAEyR,IAAI,CAAC,EAAE,CAAEC,EAAqBzP,IAAa9B,IAAI,CAC5EwR,EAAe3R,EAAEyR,IAAI,CAAC,EAAE,EAAI,CAAC,EAC5BC,CAAAA,GAAsBC,GAAeA,EAAYxR,IAAI,AAAD,GACrD,CAACoD,EAAMqO,UAAU,GACjBrO,EAAMqO,UAAU,CAAG,CAAA,EAQnBrO,EAAMpD,IAAI,CAAG,IAAIiD,EAAKb,EADFG,EAAMgP,EAAoBC,EAAYxR,IAAI,EACpB,CACtCsF,cAAe,SAAU4L,CAAY,CAAEhO,CAAW,EAC9C,IAAI4D,EAAGd,EAEP,GAAIhJ,OAAOO,cAAc,CAACC,IAAI,CAACgU,EAAa,WACxC,GAAI,AAA8B,UAA9B,OAAOA,EAAYxL,MAAM,CAEzB,IADAc,EAAI6F,KAAKnC,GAAG,CAACgH,EAAYxL,MAAM,CAACpC,MAAM,CAAEV,GAAa8C,QAAQpC,QAAU,GAChEkD,KACHd,EAASwL,EAAYxL,MAAM,CAACc,EAAE,EAAI,CAAC,EACnC0K,EAAYxL,MAAM,CAACc,EAAE,CAAGvE,EAAMyD,EAAQ9C,GAAa8C,QAAQ,CAACc,EAAE,EAAI,CAAC,QAIvE,OAAO0K,EAAYxL,MAAM,CAIjCwL,EAAcjP,EAAMW,EAAasO,GAEjCpO,EAAMpD,IAAI,CAAGkR,EAEb9N,EAAMqB,IAAI,CAAC+M,EAAaH,EAC5B,CACJ,GAAIG,EAAapO,GACjBvD,EAAE6R,cAAc,GAExB,EAeA,OAAMlL,EACF3C,aAAc,CAEV,IAAI,CAAC0M,OAAO,CAAG,EAAE,CACjB,IAAI,CAACI,YAAY,CAAG,CAAA,CACxB,CAQAN,gBAAgBZ,CAAW,CAAE,CAEzB,IAAIkC,EAAgB,CAAA,EAiBpB,OAbApL,AALgB,IAAI,CAKZgK,OAAO,CAACnK,OAAO,CAAC,AAACwL,IACa,KAAA,IAAvBA,EAAOpB,WAAW,EACzBoB,CAAAA,EAAOpB,WAAW,CAAGf,EAAYI,KAAK,EAAC,CAE/C,GAIAtJ,AAbgB,IAAI,CAaZgK,OAAO,CAACnK,OAAO,CAAC,AAACwL,IACa,KAAA,IAAvBA,EAAOpB,WAAW,EACzBmB,CAAAA,EAAgB,CAAA,CAAI,CAE5B,GACOA,CACX,CAOA5H,KAAKpG,CAAO,CAAEkO,CAAQ,CAAE,CACpB,IAAsBlB,EAAepK,AAArB,IAAI,CAAyBoK,YAAY,CAAEmB,EAAQnB,EAAe,EAAE,CAAG,CAAC,EAmBxF,GAhBApK,AAHgB,IAAI,CAGZgK,OAAO,CAACnK,OAAO,CAAC,AAACwL,IACrB,IAAMG,EAAQpO,CAAO,CAACiO,EAAOpB,WAAW,CAAC,CAACqB,EAAS,CAC/ClB,EACAmB,EAAMzL,IAAI,CAAC0L,GAGPH,EAAOnB,UAAU,CAAC7C,OAAO,CAAC,KAAO,EAEjCjM,IAAsGrE,SAAS,CAAC0U,iBAAiB,CAACF,EAAOC,EAAOH,EAAOnB,UAAU,EAGjKqB,CAAK,CAACF,EAAOnB,UAAU,CAAC,CAAGsB,CAGvC,GAEI,AAAqB,KAAA,IAAd,IAAI,CAACvR,IAAI,EAAoB+F,AAnBxB,IAAI,CAmB4BgK,OAAO,CAAC3M,MAAM,EAAI,EAAG,CACjE,IAAMqO,EAAgB,EAAE,CACxB1L,AArBY,IAAI,CAqBRgK,OAAO,CAACnK,OAAO,CAAC,SAAUwL,CAAM,EAChCA,CAAAA,AAAsB,MAAtBA,EAAOnB,UAAU,EACjBmB,AAAsB,SAAtBA,EAAOnB,UAAU,EACjBmB,AAAsB,MAAtBA,EAAOnB,UAAU,AAAO,GACpB,AAA8B,KAAA,IAAvBmB,EAAOpB,WAAW,EACzByB,EAAc5L,IAAI,CAACuL,EAAOpB,WAAW,CAGjD,GACIyB,EAAcrO,MAAM,EAAI,IAExBqO,EAAcpC,KAAK,GAEnBoC,EAAcnD,IAAI,CAAC,SAAUlS,CAAC,CAAEsV,CAAC,EAC7B,OAAOtV,EAAIsV,CACf,IAGJ,IAAI,CAAC1R,IAAI,CAAGmD,CAAO,CAAClB,EAAKwP,EAAcpC,KAAK,GAAI,GAAG,CAACrP,IAAI,AAC5D,CACA,OAAOsR,CACX,CAQAjL,gBAAgB2J,CAAW,CAAEC,CAAU,CAAE,CACrC,IAAI,CAACF,OAAO,CAAClK,IAAI,CAAC,CACdmK,YAAaA,EACbC,WAAYA,CAChB,GACqB,MAAfA,GACFA,AAAe,MAAfA,GACA,AAAsB,KAAA,IAAfA,GACP,CAAA,IAAI,CAACE,YAAY,CAAG,CAAA,CAAI,CAEhC,CAOAP,4BAA6B,CACzB,IACItJ,EAAGqL,EADDC,EAA0B,EAAE,CAElC,IAAKtL,EAAI,EAAGA,EAAI,IAAI,CAACyJ,OAAO,CAAC3M,MAAM,CAAEkD,GAAQ,EAED,KAAA,IAA7BqL,AADXA,CAAAA,EAAe,IAAI,CAAC5B,OAAO,CAACzJ,EAAE,AAAD,EACL0J,WAAW,EAC/B4B,EAAwB/L,IAAI,CAAC8L,EAAa3B,WAAW,EAG7D,OAAO4B,CACX,CAMArL,UAAU0J,CAAU,CAAE,CAClB,IAAI3J,EACJ,IAAKA,EAAI,EAAGA,EAAI,IAAI,CAACyJ,OAAO,CAAC3M,MAAM,CAAEkD,GAAQ,EAEzC,GAAIqL,AADW,IAAI,CAAC5B,OAAO,CAACzJ,EAAE,CACb2J,UAAU,GAAKA,EAC5B,MAAO,CAAA,CAInB,CACJ,CAigBA,IAAM4B,EAAKzU,GAEXyU,CAAAA,EAAEpP,IAAI,CAAGoP,EAAEpP,IAAI,EA7fuCA,EA8ftDoP,EAAErU,aAAa,CAAGqU,EAAErU,aAAa,EAppEwBA,EAspEzDqU,EAAEpU,IAAI,CAAGoU,EAAErU,aAAa,CAACC,IAAI,CAC7BoU,EAAErS,IAAI,CAAGqS,EAAEpP,IAAI,CAACjD,IAAI,CACpBqS,EAAElS,OAAO,CAAGkS,EAAErU,aAAa,CAACmC,OAAO,CACnCkS,EAAEjS,IAAI,CAAGiS,EAAErU,aAAa,CAACoC,IAAI,CACA,IAAM1C,EAAaE,IAGtC,OADYH,EAAoB,OAAU,AAE3C,CAAA"}