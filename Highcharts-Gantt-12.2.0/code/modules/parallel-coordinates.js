!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(t._Highcharts,t._Highcharts.Templating):"function"==typeof define&&define.amd?define("highcharts/modules/parallel-coordinates",["highcharts/highcharts"],function(t){return e(t,t.Templating)}):"object"==typeof exports?exports["highcharts/modules/parallel-coordinates"]=e(t._Highcharts,t._Highcharts.Templating):t.Highcharts=e(t.Highcharts,t.Highcharts.Templating)}("undefined"==typeof window?this:window,(t,e)=>(()=>{"use strict";var i,s,o,a={944:e=>{e.exports=t},984:t=>{t.exports=e}},l={};function r(t){var e=l[t];if(void 0!==e)return e.exports;var i=l[t]={exports:{}};return a[t](i,i.exports,r),i.exports}r.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return r.d(e,{a:e}),e},r.d=(t,e)=>{for(var i in e)r.o(e,i)&&!r.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);var n={};r.d(n,{default:()=>F});var h=r(944),p=r.n(h);let c={chart:{parallelCoordinates:!1,parallelAxes:{lineWidth:1,title:{text:"",reserveSpace:!1},labels:{x:0,y:4,align:"center",reserveSpace:!1},offset:0}},xAxis:{lineWidth:0,tickLength:0,opposite:!0,type:"category"}},{addEvent:d,arrayMax:f,arrayMin:u,isNumber:x,merge:g,pick:y}=p();class m{constructor(t){this.axis=t}setPosition(t,e){let i=this.axis,s=i.chart,o=((this.position||0)+.5)/(s.parallelInfo.counter+1);s.polar?e.angle=360*o:(e[t[0]]=100*o+"%",i[t[1]]=e[t[1]]=0,i[t[2]]=e[t[2]]=null,i[t[3]]=e[t[3]]=null)}}!function(t){function e(t){let e=this.chart,i=this.parallelCoordinates,s=["left","width","height","top"];if(e.hasParallelCoordinates){if(e.inverted&&(s=s.reverse()),this.isXAxis)this.options=g(this.options,c.xAxis,t.userOptions);else{let o=e.yAxis.indexOf(this);this.options=g(this.options,this.chart.options.chart.parallelAxes,t.userOptions),i.position=y(i.position,o>=0?o:e.yAxis.length),i.setPosition(s,this.options)}}}function i(t){let e=this.chart,i=this.parallelCoordinates;if(i&&e&&e.hasParallelCoordinates&&!this.isXAxis){let e=i.position,s=[];this.series.forEach(function(t){t.visible&&x(e)&&(s=(t.pointArrayMap||["y"]).reduce((i,s)=>[...i,t.getColumn(s)?.[e]??null],s))}),s=s.filter(x),this.dataMin=u(s),this.dataMax=f(s),t.preventDefault()}}function s(){this.parallelCoordinates||(this.parallelCoordinates=new m(this))}t.compose=function(t){t.keepProps.includes("parallel")||(t.keepProps.push("parallel"),d(t,"init",s),d(t,"afterSetOptions",e),d(t,"getSeriesExtremes",i))}}(i||(i={}));let A=i;var v=r(984),P=r.n(v);let{composed:b}=p(),{format:C}=P(),{addEvent:I,defined:X,erase:T,extend:M,insertItem:H,isArray:O,isNumber:_,pushUnique:w}=p();!function(t){function e(){let t=this.chart,e=this.points,i=e&&e.length,s=Number.MAX_VALUE,o,a;if(this.chart.hasParallelCoordinates){for(let l=0;l<i;l++)X((a=e[l]).y)?(t.polar?a.plotX=t.yAxis[l].angleRad||0:t.inverted?a.plotX=t.plotHeight-t.yAxis[l].top+t.plotTop:a.plotX=t.yAxis[l].left-t.plotLeft,a.clientX=a.plotX,a.plotY=t.yAxis[l].translate(a.y,!1,!0,void 0,!0),_(a.high)&&(a.plotHigh=t.yAxis[l].translate(a.high,!1,!0,void 0,!0)),void 0!==o&&(s=Math.min(s,Math.abs(a.plotX-o))),o=a.plotX,a.isInside=t.isInsidePlot(a.plotX,a.plotY,{inverted:t.inverted})):a.isNull=!0;this.closestPointRangePx=s}}function i(t){let e=this.chart;if(e.hasParallelCoordinates){for(let t of e.axes)H(this,t.series),t.isDirty=!0;this.xAxis=e.xAxis[0],this.yAxis=e.yAxis[0],t.preventDefault()}}function s(){let t=this.chart;if(t.hasParallelCoordinates)for(let e of t.axes||[])e&&e.series&&(T(e.series,this),e.isDirty=e.forceRedraw=!0)}function o(){let t=this.chart;if(t?.hasParallelCoordinates)for(let e of this.points){let i,s=t.yAxis[e.x||0],o=s.options,a=o.tooltipValueFormat??o.labels.format;i=a?C(a,M(e,{value:e.y}),t):s.dateTime?t.time.dateFormat(t.time.resolveDTLFormat(o.dateTimeLabelFormats?.[s.tickPositions.info?.unitName||"year"]||"").main,e.y??void 0):O(o.categories)?o.categories[e.y??-1]:String(e.y??""),e.formattedValue=i}}t.compose=function(t){w(b,"ParallelSeries")&&(I(t,"afterTranslate",e,{order:1}),I(t,"bindAxes",i),I(t,"destroy",s),I(t,"afterGeneratePoints",o))}}(s||(s={}));let E=s,{addEvent:L,defined:S,merge:N,splat:j}=p();class D{constructor(t){this.chart=t}setParallelInfo(t){let e=this.chart||this,i=t.series;for(let t of(e.parallelInfo={counter:0},i))t.data&&(e.parallelInfo.counter=Math.max(e.parallelInfo.counter,t.data.length-1))}}!function(t){function e(t){let e=t.args[0],i=j(e.yAxis||{}),s=[],o=i.length;if(this.hasParallelCoordinates=e.chart&&e.chart.parallelCoordinates,this.hasParallelCoordinates){for(this.setParallelInfo(e);o<=this.parallelInfo.counter;o++)s.push({});e.legend||(e.legend={}),e.legend&&void 0===e.legend.enabled&&(e.legend.enabled=!1),N(!0,e,{boost:{seriesThreshold:Number.MAX_VALUE},plotOptions:{series:{boostThreshold:Number.MAX_VALUE}}}),e.yAxis=i.concat(s),e.xAxis=N(c.xAxis,j(e.xAxis||{})[0])}}function i(t){let e=t.options;if(e.chart&&(S(e.chart.parallelCoordinates)&&(this.hasParallelCoordinates=e.chart.parallelCoordinates),this.options.chart.parallelAxes=N(this.options.chart.parallelAxes,e.chart.parallelAxes)),this.hasParallelCoordinates)for(let t of(e.series&&this.setParallelInfo(e),this.yAxis))t.update({},!1)}t.compose=function(t,s,o,a){A.compose(t),E.compose(a);let l=D.prototype,r=s.prototype;r.setParallelInfo||(r.setParallelInfo=l.setParallelInfo,L(s,"init",e),L(s,"update",i),N(!0,o.chart,c.chart))}}(o||(o={}));let V=o,k=p();V.compose(k.Axis,k.Chart,k.defaultOptions,k.Series);let F=p();return n.default})());