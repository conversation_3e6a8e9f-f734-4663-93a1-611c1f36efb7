{"version": 3, "sources": ["<anon>"], "sourcesContent": ["/**\n * @license Highcharts JS v12.2.0 (2025-04-07)\n * @module highcharts/modules/pyramid3d\n * @requires highcharts\n * @requires highcharts/highcharts-3d\n * @requires highcharts/modules/cylinder\n * @requires highcharts/modules/funnel3d\n *\n * Highcharts 3D funnel module\n *\n * (c) 2010-2025 Kacper Madej\n *\n * License: www.highcharts.com/license\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine(\"highcharts/modules/pyramid3d\", [\"highcharts/highcharts\"], function (amd1) {return factory(amd1,amd1[\"SeriesRegistry\"]);});\n\telse if(typeof exports === 'object')\n\t\texports[\"highcharts/modules/pyramid3d\"] = factory(root[\"_Highcharts\"], root[\"_Highcharts\"][\"SeriesRegistry\"]);\n\telse\n\t\troot[\"Highcharts\"] = factory(root[\"Highcharts\"], root[\"Highcharts\"][\"SeriesRegistry\"]);\n})(typeof window === 'undefined' ? this : window, (__WEBPACK_EXTERNAL_MODULE__944__, __WEBPACK_EXTERNAL_MODULE__512__) => {\nreturn /******/ (() => { // webpackBootstrap\n/******/ \t\"use strict\";\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 512:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__512__;\n\n/***/ }),\n\n/***/ 944:\n/***/ ((module) => {\n\nmodule.exports = __WEBPACK_EXTERNAL_MODULE__944__;\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tvar cachedModule = __webpack_module_cache__[moduleId];\n/******/ \t\tif (cachedModule !== undefined) {\n/******/ \t\t\treturn cachedModule.exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t(() => {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = (module) => {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\t() => (module['default']) :\n/******/ \t\t\t\t() => (module);\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t(() => {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = (exports, definition) => {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t})();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t(() => {\n/******/ \t\t__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))\n/******/ \t})();\n/******/ \t\n/************************************************************************/\nvar __webpack_exports__ = {};\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": () => (/* binding */ pyramid3d_src)\n});\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\"],\"commonjs\":[\"highcharts\"],\"commonjs2\":[\"highcharts\"],\"root\":[\"Highcharts\"]}\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_ = __webpack_require__(944);\nvar highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default = /*#__PURE__*/__webpack_require__.n(highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_);\n;// ./code/es-modules/Series/Pyramid3D/Pyramid3DSeriesDefaults.js\n/* *\n *\n *  Highcharts pyramid3d series module\n *\n *  (c) 2010-2025 Highsoft AS\n *  Author: Kacper Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n/* *\n *\n *  API Options\n *\n * */\n/**\n * A pyramid3d is a 3d version of pyramid series type. Pyramid charts are\n * a type of chart often used to visualize stages in a sales project,\n * where the top are the initial stages with the most clients.\n *\n * @sample highcharts/demo/pyramid3d/\n *         Pyramid3d\n *\n * @extends      plotOptions.funnel3d\n * @excluding    dataSorting, legendSymbolColor, neckHeight, neckWidth,\n * @product      highcharts\n * @since        7.1.0\n * @requires     highcharts-3d\n * @requires     modules/cylinder\n * @requires     modules/funnel3d\n * @requires     modules/pyramid3d\n * @optionparent plotOptions.pyramid3d\n */\nconst Pyramid3DSeriesDefaults = {\n    /**\n     * A reversed pyramid3d is funnel3d, but the latter supports neck\n     * related options: neckHeight and neckWidth\n     *\n     * @product highcharts\n     */\n    reversed: true,\n    neckHeight: 0,\n    neckWidth: 0,\n    dataLabels: {\n        /**\n         * @default top\n         */\n        verticalAlign: 'top'\n    }\n};\n/**\n * A `pyramid3d` series. If the [type](#series.pyramid3d.type) option is\n * not specified, it is inherited from [chart.type](#chart.type).\n *\n * @since     7.1.0\n * @extends   series,plotOptions.pyramid3d\n * @excluding allAreas,boostThreshold,colorAxis,compare,compareBase,dataSorting\n * @product   highcharts\n * @sample    {highcharts} highcharts/demo/pyramid3d/ Pyramid3d\n * @requires  modules/pyramid3d\n * @apioption series.pyramid3d\n */\n/**\n * An array of data points for the series. For the `pyramid3d` series\n * type, points can be given in the following ways:\n *\n * 1.  An array of numerical values. In this case, the numerical values\n * will be interpreted as `y` options. The `x` values will be automatically\n * calculated, either starting at 0 and incremented by 1, or from `pointStart`\n * and `pointInterval` given in the series options. If the axis has\n * categories, these will be used. Example:\n *\n *  ```js\n *  data: [0, 5, 3, 5]\n *  ```\n *\n * 2.  An array of objects with named values. The following snippet shows only a\n * few settings, see the complete options set below. If the total number of data\n * points exceeds the series'\n * [turboThreshold](#series.pyramid3d.turboThreshold),\n * this option is not available.\n *\n *  ```js\n *     data: [{\n *         y: 2,\n *         name: \"Point2\",\n *         color: \"#00FF00\"\n *     }, {\n *         y: 4,\n *         name: \"Point1\",\n *         color: \"#FF00FF\"\n *     }]\n *  ```\n *\n * @sample {highcharts} highcharts/chart/reflow-true/\n *         Numerical values\n * @sample {highcharts} highcharts/series/data-array-of-arrays/\n *         Arrays of numeric x and y\n * @sample {highcharts} highcharts/series/data-array-of-arrays-datetime/\n *         Arrays of datetime x and y\n * @sample {highcharts} highcharts/series/data-array-of-name-value/\n *         Arrays of point.name and y\n * @sample {highcharts} highcharts/series/data-array-of-objects/\n *         Config objects\n *\n * @type      {Array<number|Array<number>|*>}\n * @extends   series.funnel3d.data\n * @product   highcharts\n * @apioption series.pyramid3d.data\n */\n''; // Detachs doclets above\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Pyramid3D_Pyramid3DSeriesDefaults = (Pyramid3DSeriesDefaults);\n\n// EXTERNAL MODULE: external {\"amd\":[\"highcharts/highcharts\",\"SeriesRegistry\"],\"commonjs\":[\"highcharts\",\"SeriesRegistry\"],\"commonjs2\":[\"highcharts\",\"SeriesRegistry\"],\"root\":[\"Highcharts\",\"SeriesRegistry\"]}\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_ = __webpack_require__(512);\nvar highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default = /*#__PURE__*/__webpack_require__.n(highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_);\n;// ./code/es-modules/Series/Pyramid3D/Pyramid3DSeries.js\n/* *\n *\n *  Highcharts pyramid3d series module\n *\n *  (c) 2010-2025 Highsoft AS\n *  Author: Kacper Madej\n *\n *  License: www.highcharts.com/license\n *\n *  !!!!!!! SOURCE GETS TRANSPILED BY TYPESCRIPT. EDIT TS FILE ONLY. !!!!!!!\n *\n * */\n\n\n\nconst { funnel3d: Funnel3DSeries } = (highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default()).seriesTypes;\n\nconst { merge } = (highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default());\n/* *\n *\n *  Class\n *\n * */\n/**\n * The pyramid3d series type.\n *\n * @private\n * @class\n * @name Highcharts.seriesTypes.pyramid3d\n * @augments seriesTypes.funnel3d\n * @requires highcharts-3d\n * @requires modules/cylinder\n * @requires modules/funnel3d\n * @requires modules/pyramid3d\n */\nclass Pyramid3DSeries extends Funnel3DSeries {\n}\n/* *\n *\n *  Static Properties\n *\n * */\nPyramid3DSeries.defaultOptions = merge(Funnel3DSeries.defaultOptions, Pyramid3D_Pyramid3DSeriesDefaults);\nhighcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default().registerSeriesType('pyramid3d', Pyramid3DSeries);\n/* *\n *\n *  Default Export\n *\n * */\n/* harmony default export */ const Pyramid3D_Pyramid3DSeries = ((/* unused pure expression or super */ null && (Pyramid3DSeries)));\n\n;// ./code/es-modules/masters/modules/pyramid3d.js\n\n\n\n\n/* harmony default export */ const pyramid3d_src = ((highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default()));\n\n__webpack_exports__ = __webpack_exports__[\"default\"];\n/******/ \treturn __webpack_exports__;\n/******/ })()\n;\n});"], "names": ["root", "factory", "exports", "module", "define", "amd", "amd1", "window", "__WEBPACK_EXTERNAL_MODULE__944__", "__WEBPACK_EXTERNAL_MODULE__512__", "__webpack_modules__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "__webpack_exports__", "pyramid3d_src", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_", "highcharts_commonjs_highcharts_commonjs2_highcharts_root_Highcharts_default", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_", "highcharts_SeriesRegistry_commonjs_highcharts_SeriesRegistry_commonjs2_highcharts_SeriesRegistry_root_Highcharts_SeriesRegistry_default", "funnel3d", "Funnel3DSeries", "seriesTypes", "merge", "Pyramid3DSeries", "defaultOptions", "reversed", "neckHeight", "neckWidth", "dataLabels", "verticalAlign", "registerSeriesType"], "mappings": "CAcA,AAAC,SAA0CA,CAAI,CAAEC,CAAO,EACpD,AAAmB,UAAnB,OAAOC,SAAwB,AAAkB,UAAlB,OAAOC,OACxCA,OAAOD,OAAO,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAC5E,AAAkB,YAAlB,OAAOI,QAAyBA,OAAOC,GAAG,CACjDD,OAAO,+BAAgC,CAAC,wBAAwB,CAAE,SAAUE,CAAI,EAAG,OAAOL,EAAQK,EAAKA,EAAK,cAAiB,CAAE,GACxH,AAAmB,UAAnB,OAAOJ,QACdA,OAAO,CAAC,+BAA+B,CAAGD,EAAQD,EAAK,WAAc,CAAEA,EAAK,WAAc,CAAC,cAAiB,EAE5GA,EAAK,UAAa,CAAGC,EAAQD,EAAK,UAAa,CAAEA,EAAK,UAAa,CAAC,cAAiB,CACvF,EAAG,AAAkB,aAAlB,OAAOO,OAAyB,IAAI,CAAGA,OAAQ,CAACC,EAAkCC,IACrE,AAAC,CAAA,KACP,aACA,IAAIC,EAAuB,CAE/B,IACC,AAACP,IAERA,EAAOD,OAAO,CAAGO,CAEX,EAEA,IACC,AAACN,IAERA,EAAOD,OAAO,CAAGM,CAEX,CAEI,EAGIG,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,CAAQ,EAEpC,IAAIC,EAAeH,CAAwB,CAACE,EAAS,CACrD,GAAIC,AAAiBC,KAAAA,IAAjBD,EACH,OAAOA,EAAaZ,OAAO,CAG5B,IAAIC,EAASQ,CAAwB,CAACE,EAAS,CAAG,CAGjDX,QAAS,CAAC,CACX,EAMA,OAHAQ,CAAmB,CAACG,EAAS,CAACV,EAAQA,EAAOD,OAAO,CAAEU,GAG/CT,EAAOD,OAAO,AACtB,CAMCU,EAAoBI,CAAC,CAAG,AAACb,IACxB,IAAIc,EAASd,GAAUA,EAAOe,UAAU,CACvC,IAAOf,EAAO,OAAU,CACxB,IAAOA,EAER,OADAS,EAAoBO,CAAC,CAACF,EAAQ,CAAEG,EAAGH,CAAO,GACnCA,CACR,EAMAL,EAAoBO,CAAC,CAAG,CAACjB,EAASmB,KACjC,IAAI,IAAIC,KAAOD,EACXT,EAAoBW,CAAC,CAACF,EAAYC,IAAQ,CAACV,EAAoBW,CAAC,CAACrB,EAASoB,IAC5EE,OAAOC,cAAc,CAACvB,EAASoB,EAAK,CAAEI,WAAY,CAAA,EAAMC,IAAKN,CAAU,CAACC,EAAI,AAAC,EAGhF,EAKAV,EAAoBW,CAAC,CAAG,CAACK,EAAKC,IAAUL,OAAOM,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,EAAKC,GAI7F,IAAII,EAAsB,CAAC,EAG3BrB,EAAoBO,CAAC,CAACc,EAAqB,CACzC,QAAW,IAAqBC,CAClC,GAGA,IAAIC,EAAuEvB,EAAoB,KAC3FwB,EAA2FxB,EAAoBI,CAAC,CAACmB,GA4HjHE,EAAmIzB,EAAoB,KACvJ0B,EAAuJ1B,EAAoBI,CAAC,CAACqB,GAiBjL,GAAM,CAAEE,SAAUC,CAAc,CAAE,CAAG,AAACF,IAA2IG,WAAW,CAEtL,CAAEC,MAAAA,CAAK,CAAE,CAAIN,GAkBnB,OAAMO,UAAwBH,EAC9B,CAMAG,EAAgBC,cAAc,CAAGF,EAAMF,EAAeI,cAAc,CAnIpC,CAO5BC,SAAU,CAAA,EACVC,WAAY,EACZC,UAAW,EACXC,WAAY,CAIRC,cAAe,KACnB,CACJ,GAoHAX,IAA0IY,kBAAkB,CAAC,YAAaP,GAa7I,IAAMT,EAAkBE,IAG3C,OADYH,EAAoB,OAAU,AAE3C,CAAA"}