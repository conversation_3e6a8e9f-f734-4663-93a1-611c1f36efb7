Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 3994
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 3498
"GET /accounts/signup/ HTTP/1.1" 200 2883
"GET /accounts/login/ HTTP/1.1" 200 2676
"GET /accounts/google/login/?process=login HTTP/1.1" 200 1404
"POST /accounts/google/login/?process=login HTTP/1.1" 302 0
"GET /accounts/google/login/?process=login HTTP/1.1" 200 1404
"GET /accounts/login/ HTTP/1.1" 200 2676
"GET /accounts/signup/ HTTP/1.1" 200 2883
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
Watching for file changes with StatReloader
"GET /gantt/ HTTP/1.1" 200 39302
"GET /gantt/ HTTP/1.1" 200 39302
"GET /gantt/ HTTP/1.1" 200 39302
"GET /gantt/ HTTP/1.1" 200 39302
"GET /gantt/ HTTP/1.1" 200 39302
"GET /gantt/ HTTP/1.1" 200 39302
"GET /gantt/ HTTP/1.1" 200 39302
Forbidden: /api/projects/
"GET /api/projects/ HTTP/1.1" 403 43
"GET / HTTP/1.1" 200 23427
Internal Server Error: /accounts/signup/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'google_oauth2_login' not found. 'google_oauth2_login' is not a valid view function or pattern name.
"GET /accounts/signup/ HTTP/1.1" 500 167004
"GET /accounts/login/ HTTP/1.1" 200 18386
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 23427
"GET /accounts/login/ HTTP/1.1" 200 18375
"POST /accounts/login/ HTTP/1.1" 302 0
"GET /accounts/confirm-email/ HTTP/1.1" 200 1534
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\settings.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\settings.py changed, reloading.
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 23427
"GET /accounts/signup/ HTTP/1.1" 200 19412
"GET /accounts/login/ HTTP/1.1" 200 18375
"POST /accounts/login/ HTTP/1.1" 200 18602
"GET /accounts/login/ HTTP/1.1" 200 18375
"POST /accounts/login/ HTTP/1.1" 302 0
"GET / HTTP/1.1" 200 38449
"GET /api/projects/ HTTP/1.1" 200 2015
"GET /api/projects/ HTTP/1.1" 200 2015
"GET /api/tasks/ HTTP/1.1" 200 6453
"GET /project/?id=1 HTTP/1.1" 200 34890
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/list-items/?project_id=1 HTTP/1.1" 200 1511
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 6453
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 6453
"GET / HTTP/1.1" 200 38056
"GET /api/projects/ HTTP/1.1" 200 2015
"GET /gantt/ HTTP/1.1" 200 40908
"GET /api/projects/ HTTP/1.1" 200 2015
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 1824
"GET / HTTP/1.1" 200 38056
"GET /api/projects/ HTTP/1.1" 200 2015
"GET /api/projects/ HTTP/1.1" 200 2015
"GET /api/tasks/ HTTP/1.1" 200 6453
"GET /api/projects/ HTTP/1.1" 200 2015
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 33714
"GET /api/projects/ HTTP/1.1" 200 2015
"GET /api/projects/ HTTP/1.1" 200 2015
"GET /api/tasks/ HTTP/1.1" 200 6453
"GET /create-project/ HTTP/1.1" 200 51565
"POST /api/projects/ HTTP/1.1" 201 239
"GET /api/projects/ HTTP/1.1" 200 2442
"GET /api/tasks/ HTTP/1.1" 200 6453
"GET /project/?id=2 HTTP/1.1" 200 34890
"GET /api/tasks/?project_id=2 HTTP/1.1" 200 52
"GET /api/list-items/?project_id=2 HTTP/1.1" 200 52
"GET /api/projects/2/ HTTP/1.1" 200 426
"GET /api/tasks/?project_id=2 HTTP/1.1" 200 52
"GET /project/?id=1 HTTP/1.1" 200 34890
"GET /api/list-items/?project_id=1 HTTP/1.1" 200 1511
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 6453
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 6453
"GET /create-project/ HTTP/1.1" 200 51565
Internal Server Error: /api/projects/import_project/
"POST /api/projects/import_project/ HTTP/1.1" 500 61
"GET /api/projects/ HTTP/1.1" 200 2442
"GET /api/projects/ HTTP/1.1" 200 2442
"GET /api/tasks/ HTTP/1.1" 200 6453
"GET /project/?id=1 HTTP/1.1" 200 34890
"GET /api/list-items/?project_id=1 HTTP/1.1" 200 1511
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 6453
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 6453
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
Watching for file changes with StatReloader
"GET /project/?id=1 HTTP/1.1" 200 34890
"GET /api/list-items/?project_id=1 HTTP/1.1" 200 1511
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 6453
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 6453
"GET /project-tasks/ HTTP/1.1" 200 33719
"GET / HTTP/1.1" 200 23427
"GET /accounts/signup/ HTTP/1.1" 200 19412
"GET /accounts/login/ HTTP/1.1" 200 18375
"GET /api/projects/ HTTP/1.1" 200 2442
"GET /create-project/ HTTP/1.1" 200 51565
"POST /api/projects/ HTTP/1.1" 201 208
"GET /create-project/ HTTP/1.1" 200 51565
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /gantt/ HTTP/1.1" 200 74294
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/2/ HTTP/1.1" 200 426
Internal Server Error: /api/projects/2/gantt_data/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py", line 66, in gantt_data
    min_date = project.start_date.timestamp() * 1000
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'datetime.date' object has no attribute 'timestamp'. Did you mean: 'fromtimestamp'?
"GET /api/projects/2/gantt_data/ HTTP/1.1" 500 107285
Internal Server Error: /api/projects/2/gantt_data/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py", line 66, in gantt_data
    min_date = project.start_date.timestamp() * 1000
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'datetime.date' object has no attribute 'timestamp'. Did you mean: 'fromtimestamp'?
"GET /api/projects/2/gantt_data/ HTTP/1.1" 500 107285
"GET /project/?id=2 HTTP/1.1" 200 34890
"GET /api/list-items/?project_id=2 HTTP/1.1" 200 52
"GET /api/tasks/?project_id=2 HTTP/1.1" 200 52
"GET /api/projects/2/ HTTP/1.1" 200 426
"GET /api/tasks/?project_id=2 HTTP/1.1" 200 52
Not Found: /manifest.json
"GET /manifest.json HTTP/1.1" 404 4211
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 4205
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4187
"GET /admin/login/ HTTP/1.1" 200 4174
Forbidden: /api/projects/
"GET /api/projects/ HTTP/1.1" 403 43
Not Found: /manifest.json
"GET /manifest.json HTTP/1.1" 404 4211
Not Found: /manifest.json
"GET /manifest.json HTTP/1.1" 404 4211
Not Found: /manifest.json
"GET /manifest.json HTTP/1.1" 404 4211
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4187
Forbidden: /api/
"GET /api/ HTTP/1.1" 403 43
Not Found: /manifest.json
"GET /manifest.json HTTP/1.1" 404 4211
Not Found: /manifest.json
"GET /manifest.json HTTP/1.1" 404 4211
Not Found: /manifest.json
"GET /manifest.json HTTP/1.1" 404 4211
Not Found: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 404 20364
Not Found: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 404 20364
Not Found: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 404 20364
Not Found: /api/auth/login/
"POST /api/auth/login/ HTTP/1.1" 404 20364
Watching for file changes with StatReloader
"GET /gantt/ HTTP/1.1" 200 74294
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 1824
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 1824
"GET /gantt/ HTTP/1.1" 200 78125
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 1824
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 6453
"GET /api/tasks/1/ HTTP/1.1" 200 811
"GET / HTTP/1.1" 200 34817
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/tasks/ HTTP/1.1" 200 6453
"GET /project-tasks/?id=3 HTTP/1.1" 200 36035
"GET /api/tasks/?project_id=3 HTTP/1.1" 200 52
"GET /api/projects/3/ HTTP/1.1" 200 395
"GET /api/tasks/?project_id=3 HTTP/1.1" 200 52
"GET /gantt/?project_id=3 HTTP/1.1" 200 78125
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 1824
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 6453
"GET /api/tasks/1/ HTTP/1.1" 200 811
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 1824
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 6453
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
Watching for file changes with StatReloader
"GET /gantt HTTP/1.1" 301 0
Internal Server Error: /gantt/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 510, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'static'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 134, in render
    compiled_parent = self.get_parent(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 131, in get_parent
    return self.find_template(parent, context)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 109, in find_template
    template, origin = context.template.engine.find_template(
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        template_name,
        ^^^^^^^^^^^^^^
        skip=history,
        ^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 512, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 577, in invalid_block_tag
    raise self.error(
    ...<3 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 27: 'static'. Did you forget to register or load this tag?
"GET /gantt/ HTTP/1.1" **********
Internal Server Error: /
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 510, in parse
    compile_func = self.tags[command]
                   ~~~~~~~~~^^^^^^^^^
KeyError: 'static'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 163, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 134, in render
    compiled_parent = self.get_parent(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 131, in get_parent
    return self.find_template(parent, context)
           ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loader_tags.py", line 109, in find_template
    template, origin = context.template.engine.find_template(
                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        template_name,
        ^^^^^^^^^^^^^^
        skip=history,
        ^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\engine.py", line 159, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 196, in compile_nodelist
    nodelist = parser.parse()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 512, in parse
    self.invalid_block_tag(token, command, parse_until)
    ~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\template\base.py", line 577, in invalid_block_tag
    raise self.error(
    ...<3 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Invalid block tag on line 27: 'static'. Did you forget to register or load this tag?
"GET / HTTP/1.1" **********
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 24198
"GET /gantt/ HTTP/1.1" 200 81645
"GET /interactive-gantt/ HTTP/1.1" 200 31172
"GET /test-gantt/ HTTP/1.1" 200 9556
"GET /project-tasks/ HTTP/1.1" 200 34490
"GET /static/js/highcharts/highcharts.js HTTP/1.1" **********
"GET /static/js/highcharts/highcharts-gantt.js HTTP/1.1" **********
"GET /static/js/advanced-gantt.js HTTP/1.1" 200 13438
"GET /static/js/simple-gantt.js HTTP/1.1" 200 7793
"GET / HTTP/1.1" 200 34878
"GET /static/js/highcharts/exporting.js HTTP/1.1" 200 19683
"GET /static/js/highcharts/draggable-points.js HTTP/1.1" 200 17875
"GET /static/js/highcharts/accessibility.js HTTP/1.1" **********
"GET /static/js/highcharts/highcharts.js HTTP/1.1" **********
"GET /static/js/highcharts/highcharts-gantt.js HTTP/1.1" **********
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/tasks/ HTTP/1.1" 200 6453
"GET /gantt/ HTTP/1.1" 200 83251
"GET /static/js/advanced-gantt.js HTTP/1.1" 200 13438
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /static/js/simple-gantt.js HTTP/1.1" 200 7793
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 1824
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 6453
Watching for file changes with StatReloader
"GET /gantt/ HTTP/1.1" 200 83583
"GET /static/js/highcharts/gantt.js HTTP/1.1" 200 121221
"GET /static/js/advanced-gantt.js HTTP/1.1" 304 0
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 1824
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 6453
"GET /interactive-gantt/ HTTP/1.1" 200 33110
"GET /gantt/ HTTP/1.1" 200 83583
"GET /static/js/advanced-gantt.js HTTP/1.1" 304 0
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 1824
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 6453
"GET /interactive-gantt/ HTTP/1.1" 200 33110
"GET /gantt/ HTTP/1.1" 200 83583
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 1824
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 6453
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
Watching for file changes with StatReloader
"GET /gantt/ HTTP/1.1" 200 86971
"GET /static/js/advanced-gantt.js HTTP/1.1" 304 0
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 1824
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 6453
"POST /api/tasks/ HTTP/1.1" 201 302
Bad Request: /api/tasks/
"POST /api/tasks/ HTTP/1.1" 400 80
Watching for file changes with StatReloader
"GET /gantt/ HTTP/1.1" 200 87079
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 2001
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 2001
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
"GET /gantt/ HTTP/1.1" 200 104704
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 2001
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
"GET /api/tasks/1/ HTTP/1.1" 200 811
"GET /api/list-items/?project_id=1 HTTP/1.1" 200 1511
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /gantt/ HTTP/1.1" 200 120393
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 4398
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
"GET /api/tasks/1/ HTTP/1.1" 200 811
"GET /api/tasks/1/ HTTP/1.1" 200 811
"GET /gantt/ HTTP/1.1" 200 123037
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 4398
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 4398
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
"GET /gantt/ HTTP/1.1" 200 121488
"GET /gantt/ HTTP/1.1" 200 123094
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 4398
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
"GET /api/tasks/5/ HTTP/1.1" 200 631
"GET /api/tasks/5/ HTTP/1.1" 200 631
"GET /api/tasks/4/ HTTP/1.1" 200 991
"GET /api/list-items/?project_id=1 HTTP/1.1" 200 1511
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
"GET /api/tasks/2/ HTTP/1.1" 200 631
"GET /api/tasks/3/ HTTP/1.1" 200 631
"GET /api/tasks/3/ HTTP/1.1" 200 631
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /gantt/ HTTP/1.1" 200 124167
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 4586
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 4586
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 4586
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
"GET /gantt/ HTTP/1.1" 200 121716
"GET /gantt/ HTTP/1.1" 200 123322
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 4586
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
"GET /api/tasks/8/ HTTP/1.1" 200 628
"GET / HTTP/1.1" 200 35210
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/tasks/ HTTP/1.1" 200 7105
"GET /api/projects/ HTTP/1.1" 200 24156
"GET /static/rest_framework/css/bootstrap-tweaks.css HTTP/1.1" 200 3426
"GET /static/rest_framework/js/csrf.js HTTP/1.1" 200 1793
"GET /static/rest_framework/js/bootstrap.min.js HTTP/1.1" 200 39680
"GET /static/rest_framework/js/prettify-min.js HTTP/1.1" 200 13632
"GET /static/rest_framework/js/default.js HTTP/1.1" 200 1268
"GET /static/rest_framework/js/load-ajax-form.js HTTP/1.1" 200 59
"GET /static/rest_framework/js/jquery-3.7.1.min.js HTTP/1.1" 200 87533
"GET /static/rest_framework/css/prettify.css HTTP/1.1" 200 817
"GET /static/rest_framework/js/ajax-form.js HTTP/1.1" 200 3796
"GET /static/rest_framework/css/default.css HTTP/1.1" 200 1152
"GET /static/rest_framework/css/bootstrap.min.css HTTP/1.1" 200 121457
"GET /static/rest_framework/img/grid.png HTTP/1.1" 200 1458
"GET /static/rest_framework/css/bootstrap.min.css.map HTTP/1.1" 200 540434
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/tasks/ HTTP/1.1" 200 35772
"GET /api/tasks/ HTTP/1.1" 200 7105
"GET /gantt/ HTTP/1.1" 200 123322
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 4586
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
"GET /api/tasks/1/ HTTP/1.1" 200 811
"GET /gantt/ HTTP/1.1" 200 122924
"GET /gantt/ HTTP/1.1" 200 124530
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 4586
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
"GET /api/projects/2/ HTTP/1.1" 200 426
Internal Server Error: /api/projects/2/gantt_data/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py", line 119, in gantt_data
    min_date = project.start_date.timestamp() * 1000
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'datetime.date' object has no attribute 'timestamp'. Did you mean: 'fromtimestamp'?
"GET /api/projects/2/gantt_data/ HTTP/1.1" 500 107558
"GET /api/projects/3/ HTTP/1.1" 200 395
Internal Server Error: /api/projects/3/gantt_data/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py", line 119, in gantt_data
    min_date = project.start_date.timestamp() * 1000
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'datetime.date' object has no attribute 'timestamp'. Did you mean: 'fromtimestamp'?
"GET /api/projects/3/gantt_data/ HTTP/1.1" 500 107543
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 4586
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
"GET /api/tasks/1/ HTTP/1.1" 200 811
"GET /api/tasks/1/ HTTP/1.1" 200 811
"GET /api/projects/2/ HTTP/1.1" 200 426
Internal Server Error: /api/projects/2/gantt_data/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py", line 119, in gantt_data
    min_date = project.start_date.timestamp() * 1000
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'datetime.date' object has no attribute 'timestamp'. Did you mean: 'fromtimestamp'?
"GET /api/projects/2/gantt_data/ HTTP/1.1" 500 107558
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 4586
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
"GET /gantt/ HTTP/1.1" 200 123200
Forbidden: /api/projects/
"GET /api/projects/ HTTP/1.1" 403 43
"GET /gantt/ HTTP/1.1" 200 123134
"GET /gantt/ HTTP/1.1" 200 124740
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 4586
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
Watching for file changes with StatReloader
"GET /gantt/ HTTP/1.1" 200 123140
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /test-advanced-gantt/ HTTP/1.1" 200 23665
"GET /gantt/ HTTP/1.1" 200 123140
"GET /gantt/ HTTP/1.1" 200 126132
"GET /test-advanced-gantt/ HTTP/1.1" 200 23665
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /debug-gantt/ HTTP/1.1" 200 11105
"GET /gantt/ HTTP/1.1" 200 127738
"GET /static/js/advanced-gantt.js HTTP/1.1" 200 16185
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 4586
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 4586
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 4586
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
Forbidden: /api/projects/
"GET /api/projects/ HTTP/1.1" 403 43
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /test-gantt-data/ HTTP/1.1" 200 25097
"GET /gantt/ HTTP/1.1" 200 127232
"GET /gantt/ HTTP/1.1" 200 127466
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /simple-gantt-test/ HTTP/1.1" 200 7037
"GET /gantt/ HTTP/1.1" 200 129072
"GET /static/js/advanced-gantt.js HTTP/1.1" 304 0
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 4586
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
Watching for file changes with StatReloader
"GET /gantt/ HTTP/1.1" 200 127466
"GET /simple-gantt-test/ HTTP/1.1" 200 7037
"GET /gantt/ HTTP/1.1" 200 127466
"GET /gantt/ HTTP/1.1" 200 129072
"GET /static/js/advanced-gantt.js HTTP/1.1" 200 20655
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 4586
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 4586
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
"GET /api/tasks/2/ HTTP/1.1" 200 631
"GET /api/tasks/3/ HTTP/1.1" 200 631
Watching for file changes with StatReloader
"GET /gantt/ HTTP/1.1" 200 129461
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /test-date-calculation/ HTTP/1.1" 200 9922
"GET /gantt/ HTTP/1.1" 200 131067
"GET /static/js/advanced-gantt.js HTTP/1.1" 304 0
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 4586
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
"GET /api/tasks/1/ HTTP/1.1" 200 811
Watching for file changes with StatReloader
"GET /gantt/ HTTP/1.1" 200 129463
"GET /gantt/ HTTP/1.1" 200 131069
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 4586
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
"GET /api/tasks/2/ HTTP/1.1" 200 631
"GET /api/tasks/2/ HTTP/1.1" 200 631
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\ai_deep_cpms\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /test-end-date-calculation/ HTTP/1.1" 200 10373
"GET /gantt/ HTTP/1.1" 200 132230
"GET /static/js/advanced-gantt.js HTTP/1.1" 304 0
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 4586
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
"GET /api/tasks/1/ HTTP/1.1" 200 811
"GET /api/tasks/1/ HTTP/1.1" 200 811
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\tasks\models.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\tasks\models.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\tasks\models.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\tasks\models.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\tasks\models.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\tasks\models.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /gantt/ HTTP/1.1" 200 130624
"GET /gantt/ HTTP/1.1" 200 132230
"GET /static/js/advanced-gantt.js HTTP/1.1" 304 0
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 4586
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7105
"GET /api/tasks/10/ HTTP/1.1" 200 650
"GET /api/tasks/2/ HTTP/1.1" 200 631
"GET /api/tasks/2/ HTTP/1.1" 200 631
"PATCH /api/tasks/2/ HTTP/1.1" 200 323
Watching for file changes with StatReloader
"GET /gantt/ HTTP/1.1" 200 154438
"GET /gantt/ HTTP/1.1" 200 156044
"GET /static/js/advanced-gantt.js HTTP/1.1" 304 0
"GET /api/projects/ HTTP/1.1" 200 2838
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 4558
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7078
"GET /api/tasks/2/ HTTP/1.1" 200 618
"GET /api/tasks/2/ HTTP/1.1" 200 618
Not Found: /api/projects/1/list-items/
"GET /api/projects/1/list-items/ HTTP/1.1" 404 22073
"PATCH /api/tasks/2/ HTTP/1.1" 200 323
Watching for file changes with StatReloader
Forbidden: /api/list-items/
"GET /api/list-items/?project_id=1 HTTP/1.1" 403 43
"GET /gantt/ HTTP/1.1" 200 154566
"GET /gantt/ HTTP/1.1" 200 154566
"GET /gantt/ HTTP/1.1" 200 156172
"GET /api/projects/ HTTP/1.1" 200 4212
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 4558
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7078
"GET /api/tasks/2/ HTTP/1.1" 200 618
"GET /api/tasks/2/ HTTP/1.1" 200 618
"GET /api/list-items/?project_id=1 HTTP/1.1" 200 1511
"GET /api/tasks/1/ HTTP/1.1" 200 797
Watching for file changes with StatReloader
"GET /gantt/ HTTP/1.1" 200 161351
"GET /gantt/ HTTP/1.1" 200 162957
"GET /static/js/advanced-gantt.js HTTP/1.1" 304 0
"GET /api/projects/ HTTP/1.1" 200 4212
"GET /api/projects/1/ HTTP/1.1" 200 1963
"GET /api/projects/1/gantt_data/ HTTP/1.1" 200 4558
"GET /api/tasks/?project_id=1 HTTP/1.1" 200 7078
"GET /api/tasks/2/ HTTP/1.1" 200 618
"GET /api/tasks/2/ HTTP/1.1" 200 618
"GET /api/list-items/?project_id=1 HTTP/1.1" 200 1511
"PATCH /api/tasks/2/ HTTP/1.1" 200 324
Internal Server Error: /api/projects/1/gantt_data/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\csrf.py", line 65, in _view_wrapper
    return view_func(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\viewsets.py", line 125, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 515, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 475, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 486, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 512, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py", line 71, in gantt_data
    total_cost = sum([item.total_cost for item in task.list_items.all()])
                      ^^^^^^^^^^^^^^^
AttributeError: 'ProjectListItem' object has no attribute 'total_cost'
"GET /api/projects/1/gantt_data/ HTTP/1.1" 500 110129
"GET /api/tasks/2/ HTTP/1.1" 200 625
"GET /api/list-items/?project_id=1 HTTP/1.1" 200 1511
"GET /api/tasks/2/ HTTP/1.1" 200 625
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py changed, reloading.
C:\Users\<USER>\Desktop\trace\cost-Project-2025-06-16\projects\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /gantt/ HTTP/1.1" 200 161351
