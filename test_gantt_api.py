#!/usr/bin/env python
"""
测试甘特图API修复
"""
import os
import sys
import django
import requests
from datetime import datetime

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_deep_cpms.settings')
django.setup()

from django.contrib.auth.models import User
from projects.models import Project
from tasks.models import Task

def test_gantt_api():
    """测试甘特图API"""
    print("🧪 测试甘特图API修复...")
    
    try:
        # 获取测试项目
        project = Project.objects.filter(name='API测试项目').first()
        if not project:
            print("❌ 未找到测试项目")
            return
        
        print(f"📁 测试项目: {project.name} (ID: {project.id})")
        print(f"  项目开始日期: {project.start_date}")
        print(f"  项目开始日期类型: {type(project.start_date)}")
        
        # 获取项目任务
        tasks = project.tasks.all()
        print(f"  任务数量: {tasks.count()}")
        
        for task in tasks:
            print(f"  任务: {task.name}")
            print(f"    开始日期: {task.start_date} (类型: {type(task.start_date)})")
            print(f"    结束日期: {task.end_date} (类型: {type(task.end_date)})")
        
        # 测试甘特图API
        print(f"\n🌐 测试甘特图API: /api/projects/{project.id}/gantt_data/")
        
        # 直接调用视图函数
        from projects.views import ProjectViewSet
        from rest_framework.test import APIRequestFactory
        from django.contrib.auth.models import AnonymousUser
        
        factory = APIRequestFactory()
        request = factory.get(f'/api/projects/{project.id}/gantt_data/')
        request.user = project.owner  # 设置用户
        
        view = ProjectViewSet()
        view.request = request
        view.format_kwarg = None
        
        try:
            response = view.gantt_data(request, pk=project.id)
            print("✅ 甘特图API调用成功")
            
            data = response.data
            print(f"  返回数据结构:")
            print(f"    任务数量: {len(data.get('tasks', []))}")
            print(f"    依赖关系数量: {len(data.get('dependencies', []))}")
            print(f"    最小日期: {data.get('min_date')}")
            print(f"    最大日期: {data.get('max_date')}")
            print(f"    项目信息: {data.get('project', {}).get('name')}")
            
            # 检查任务数据
            tasks_data = data.get('tasks', [])
            if tasks_data:
                print(f"\n  任务详情:")
                for task_data in tasks_data:
                    print(f"    {task_data.get('wbs_code')}: {task_data.get('name')}")
                    print(f"      开始: {task_data.get('start')}")
                    print(f"      结束: {task_data.get('end')}")
                    print(f"      工期: {task_data.get('planned_duration')}天")
                    print(f"      成本: ¥{task_data.get('total_cost', 0)}")
            
            print("\n✅ 甘特图API数据格式正确")
            
        except Exception as api_error:
            print(f"❌ 甘特图API调用失败: {api_error}")
            import traceback
            traceback.print_exc()
            return False
        
        # 测试日期处理函数
        print(f"\n🔧 测试日期处理函数...")
        view_instance = ProjectViewSet()
        
        # 测试date对象
        from datetime import date
        test_date = date(2025, 6, 21)
        result = view_instance.safe_isoformat(test_date)
        print(f"  date对象处理: {test_date} -> {result}")
        
        # 测试datetime对象
        test_datetime = datetime(2025, 6, 21, 8, 0, 0)
        result = view_instance.safe_isoformat(test_datetime)
        print(f"  datetime对象处理: {test_datetime} -> {result}")
        
        # 测试None值
        result = view_instance.safe_isoformat(None)
        print(f"  None值处理: None -> {result}")
        
        print("\n✅ 日期处理函数测试通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_timestamp_conversion():
    """测试时间戳转换"""
    print("\n🕐 测试时间戳转换...")
    
    try:
        from datetime import date, datetime, time
        
        # 测试date对象转时间戳
        test_date = date(2025, 6, 21)
        print(f"  原始date对象: {test_date}")
        
        # 转换为datetime
        dt = datetime.combine(test_date, time.min)
        timestamp = dt.timestamp() * 1000
        print(f"  转换为datetime: {dt}")
        print(f"  时间戳: {timestamp}")
        
        # 测试datetime对象转时间戳
        test_datetime = datetime(2025, 6, 21, 8, 0, 0)
        timestamp2 = test_datetime.timestamp() * 1000
        print(f"  datetime时间戳: {timestamp2}")
        
        print("✅ 时间戳转换测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 时间戳转换测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始甘特图API修复验证测试...\n")
    
    # 测试时间戳转换
    timestamp_ok = test_timestamp_conversion()
    
    # 测试甘特图API
    api_ok = test_gantt_api()
    
    print(f"\n📝 测试总结:")
    print(f"  1. {'✅' if timestamp_ok else '❌'} 时间戳转换功能")
    print(f"  2. {'✅' if api_ok else '❌'} 甘特图API功能")
    
    if timestamp_ok and api_ok:
        print(f"\n🎉 所有测试通过！甘特图API修复成功！")
        print(f"\n🌐 现在可以在甘特图页面正常加载数据了：")
        print(f"  http://127.0.0.1:8000/gantt/")
    else:
        print(f"\n❌ 部分测试失败，请检查错误信息")
