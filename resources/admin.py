from django.contrib import admin
from .models import ProjectResource, ResourceCostSplit


class ResourceCostSplitInline(admin.TabularInline):
    model = ResourceCostSplit
    extra = 1
    readonly_fields = ['cost']


@admin.register(ProjectResource)
class ProjectResourceAdmin(admin.ModelAdmin):
    list_display = ['resource_number', 'name', 'project', 'category', 'market_price', 'budget_price', 'supplier', 'is_active']
    list_filter = ['project', 'category', 'is_active']
    search_fields = ['resource_number', 'name', 'supplier']
    inlines = [ResourceCostSplitInline]
    fieldsets = (
        ('基本信息', {
            'fields': ('project', 'quota', 'resource_number', 'name', 'category', 'unit')
        }),
        ('价格信息', {
            'fields': ('market_price', 'budget_price')
        }),
        ('供应商信息', {
            'fields': ('supplier', 'supplier_contact')
        }),
        ('其他信息', {
            'fields': ('specification', 'remarks', 'is_active')
        }),
    )


@admin.register(ResourceCostSplit)
class ResourceCostSplitAdmin(admin.ModelAdmin):
    list_display = ['resource', 'description', 'cost_type', 'percentage', 'cost']
    list_filter = ['cost_type', 'resource__project']
    search_fields = ['resource__name', 'description']
    readonly_fields = ['cost']
