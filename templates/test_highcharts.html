{% extends 'base.html' %}
{% load static %}

{% block title %}Highcharts 测试页面{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-chart-gantt me-2"></i>Highcharts 甘特图测试</h3>
                </div>
                <div class="card-body">
                    <div id="status-info" class="alert alert-info">
                        <h5>检测状态:</h5>
                        <ul id="status-list">
                            <li>正在检测 Highcharts...</li>
                        </ul>
                    </div>
                    
                    <div id="gantt-test-container" style="height: 400px; border: 1px solid #ddd; margin: 20px 0;">
                        <div class="text-center p-5">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-3">正在初始化甘特图...</p>
                        </div>
                    </div>
                    
                    <button id="test-btn" class="btn btn-primary" onclick="testGanttChart()">
                        <i class="fas fa-play me-2"></i>测试甘特图
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let statusList = [];

function addStatus(message, type = 'info') {
    statusList.push(`[${new Date().toLocaleTimeString()}] ${message}`);
    updateStatusDisplay();
    console.log(message);
}

function updateStatusDisplay() {
    const list = document.getElementById('status-list');
    list.innerHTML = statusList.map(status => `<li>${status}</li>`).join('');
}

function testGanttChart() {
    addStatus('开始测试甘特图功能...');
    
    if (typeof Highcharts === 'undefined') {
        addStatus('❌ Highcharts 未定义', 'error');
        return;
    }
    
    addStatus('✅ Highcharts 已加载');
    addStatus(`📊 Highcharts 版本: ${Highcharts.version}`);
    
    if (typeof Highcharts.ganttChart !== 'function') {
        addStatus('❌ Highcharts.ganttChart 函数不可用', 'error');
        return;
    }
    
    addStatus('✅ Highcharts.ganttChart 函数可用');
    
    // 检查甘特图系列类型
    if (Highcharts.seriesTypes && Highcharts.seriesTypes.gantt) {
        addStatus('✅ Gantt 系列类型可用');
    } else {
        addStatus('⚠️ Gantt 系列类型不可用', 'warning');
    }
    
    try {
        // 创建测试甘特图
        const today = new Date();
        const day = 24 * 60 * 60 * 1000;
        
        const testData = [{
            name: '任务 1',
            id: 'task1',
            start: today.getTime(),
            end: today.getTime() + 3 * day,
            completed: {
                amount: 0.5
            },
            y: 0
        }, {
            name: '任务 2',
            id: 'task2',
            start: today.getTime() + 2 * day,
            end: today.getTime() + 5 * day,
            completed: {
                amount: 0.3
            },
            y: 1
        }];
        
        addStatus('🎯 正在创建测试甘特图...');
        
        const chart = Highcharts.ganttChart('gantt-test-container', {
            title: {
                text: '测试甘特图'
            },
            subtitle: {
                text: '这是一个简单的测试甘特图'
            },
            xAxis: {
                type: 'datetime'
            },
            yAxis: {
                type: 'category',
                categories: ['任务 1', '任务 2']
            },
            series: [{
                name: '测试项目',
                data: testData
            }],
            credits: {
                enabled: false
            }
        });
        
        if (chart) {
            addStatus('🎉 甘特图创建成功！');
            document.getElementById('test-btn').innerHTML = '<i class="fas fa-check me-2"></i>测试完成';
            document.getElementById('test-btn').className = 'btn btn-success';
        }
        
    } catch (error) {
        addStatus(`❌ 甘特图创建失败: ${error.message}`, 'error');
        console.error('Gantt chart error:', error);
    }
}

// 页面加载时自动检测
document.addEventListener('DOMContentLoaded', function() {
    addStatus('页面加载完成，开始检测...');
    
    // 等待一下让所有脚本加载完成
    setTimeout(() => {
        if (typeof Highcharts === 'undefined') {
            addStatus('❌ Highcharts 未加载');
            document.getElementById('status-info').className = 'alert alert-danger';
        } else {
            addStatus('✅ Highcharts 已加载');
            addStatus(`📊 版本: ${Highcharts.version}`);
            
            if (typeof Highcharts.ganttChart === 'function') {
                addStatus('✅ ganttChart 函数可用');
                document.getElementById('status-info').className = 'alert alert-success';
                
                // 自动运行测试
                setTimeout(testGanttChart, 1000);
            } else {
                addStatus('❌ ganttChart 函数不可用');
                document.getElementById('status-info').className = 'alert alert-warning';
            }
        }
    }, 500);
});
</script>
{% endblock %}
