<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单甘特图测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .gantt-container {
            height: 500px;
            border: 2px solid #ddd;
            border-radius: 6px;
            margin: 20px 0;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
        .log {
            background: #1a1a1a;
            color: #00ff00;
            padding: 15px;
            border-radius: 6px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>简单甘特图测试</h1>
        
        <div>
            <button class="btn" onclick="testAdvancedGantt()">测试 AdvancedGantt</button>
            <button class="btn" onclick="testBasicGantt()">测试基础甘特图</button>
            <button class="btn" onclick="clearContainer()">清空</button>
        </div>
        
        <div id="gantt-container" class="gantt-container"></div>
        
        <div id="log" class="log"></div>
    </div>

    <!-- 加载脚本 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="/static/js/highcharts/highcharts.js"></script>
    <script src="/static/js/highcharts/gantt.js"></script>
    <script src="/static/js/highcharts/exporting.js"></script>
    <script src="/static/js/highcharts/draggable-points.js"></script>
    <script src="/static/js/highcharts/accessibility.js"></script>
    <script src="/static/js/advanced-gantt.js"></script>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function testAdvancedGantt() {
            log('开始测试 AdvancedGantt...');
            
            if (typeof AdvancedGantt === 'undefined') {
                log('❌ AdvancedGantt 不可用');
                return;
            }

            try {
                clearContainer();
                
                const today = Date.now();
                const day = 24 * 60 * 60 * 1000;
                
                const testData = [{
                    name: '测试项目',
                    data: [{
                        name: '项目启动',
                        id: 'start',
                        start: today,
                        end: today + 2 * day,
                        completed: { amount: 1 },
                        owner: '项目经理'
                    }, {
                        name: '需求分析',
                        id: 'analysis',
                        dependency: 'start',
                        start: today + 2 * day,
                        end: today + 5 * day,
                        completed: { amount: 0.8 },
                        owner: '业务分析师'
                    }]
                }];
                
                const gantt = new AdvancedGantt('gantt-container', {
                    title: 'AdvancedGantt 测试',
                    subtitle: '这是一个测试甘特图',
                    data: testData,
                    height: 500,
                    onChartReady: function(chart) {
                        log('✅ AdvancedGantt 创建成功');
                    },
                    onError: function(error) {
                        log(`❌ AdvancedGantt 创建失败: ${error.message}`);
                    }
                });
                
            } catch (error) {
                log(`❌ 测试 AdvancedGantt 时发生错误: ${error.message}`);
            }
        }

        function testBasicGantt() {
            log('开始测试基础甘特图...');
            
            if (typeof Highcharts === 'undefined' || typeof Highcharts.ganttChart !== 'function') {
                log('❌ Highcharts Gantt 不可用');
                return;
            }

            try {
                clearContainer();
                
                const today = Date.now();
                const day = 24 * 60 * 60 * 1000;
                
                const chart = Highcharts.ganttChart('gantt-container', {
                    title: {
                        text: '基础甘特图测试'
                    },
                    series: [{
                        name: '测试项目',
                        data: [{
                            name: '任务1',
                            start: today,
                            end: today + 3 * day,
                            y: 0
                        }, {
                            name: '任务2',
                            start: today + 1 * day,
                            end: today + 4 * day,
                            y: 1
                        }]
                    }]
                });
                
                log('✅ 基础甘特图创建成功');
                
            } catch (error) {
                log(`❌ 测试基础甘特图时发生错误: ${error.message}`);
            }
        }

        function clearContainer() {
            document.getElementById('gantt-container').innerHTML = '';
            log('容器已清空');
        }

        // 页面加载完成后执行
        window.addEventListener('load', function() {
            log('页面加载完成');
            log(`jQuery: ${typeof $ !== 'undefined' ? '✅' : '❌'}`);
            log(`Highcharts: ${typeof Highcharts !== 'undefined' ? '✅' : '❌'}`);
            log(`Highcharts.ganttChart: ${typeof Highcharts !== 'undefined' && typeof Highcharts.ganttChart === 'function' ? '✅' : '❌'}`);
            log(`AdvancedGantt: ${typeof AdvancedGantt !== 'undefined' ? '✅' : '❌'}`);
            
            // 自动测试 AdvancedGantt
            setTimeout(() => {
                log('自动测试 AdvancedGantt...');
                testAdvancedGantt();
            }, 1000);
        });
    </script>
</body>
</html>
