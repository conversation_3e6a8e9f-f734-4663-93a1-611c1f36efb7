<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>结束日期计算测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-form {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
            background: #f9f9f9;
        }
        .result {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 4px;
            margin-top: 15px;
            font-family: monospace;
        }
        .error {
            background: #ffe8e8;
            color: #d00;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
        .row {
            display: flex;
            gap: 20px;
        }
        .col {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>结束日期计算测试</h1>
        <p>测试修复后的工期和结束日期计算逻辑</p>
        
        <div class="test-form">
            <h3>手动测试</h3>
            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label for="start-date">开始日期:</label>
                        <input type="date" id="start-date" value="2025-06-21">
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label for="quantity">工程数量:</label>
                        <input type="number" id="quantity" value="1" step="0.01">
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col">
                    <div class="form-group">
                        <label for="efficiency">每日工效:</label>
                        <input type="number" id="efficiency" value="1" step="0.01">
                    </div>
                </div>
                <div class="col">
                    <div class="form-group">
                        <label for="duration">计算工期:</label>
                        <input type="number" id="duration" readonly>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="end-date">计算结束日期:</label>
                <input type="date" id="end-date" readonly>
            </div>
            
            <button class="btn" onclick="calculateEndDate()">计算结束日期</button>
            <button class="btn" onclick="runPredefinedTests()">运行预定义测试</button>
            
            <div id="manual-result" class="result" style="display: none;"></div>
        </div>
        
        <div id="test-results"></div>
    </div>

    <script>
        // 复制修复后的计算函数
        function calculateTaskEndDate(startDate, duration) {
            let endDate = new Date(startDate);
            let workDaysAdded = 0;
            
            // 注意：工期包含开始日期，所以如果工期是1天，结束日期就是开始日期
            if (duration === 1) {
                // 1天工期，结束日期就是开始日期
                endDate = new Date(startDate);
            } else {
                // 多天工期，需要添加工作日
                while (workDaysAdded < duration - 1) { // -1 因为开始日期已经算作第一天
                    endDate.setDate(endDate.getDate() + 1);
                    const dayOfWeek = endDate.getDay();
                    
                    // 0 = 周日, 6 = 周六，跳过周末
                    if (dayOfWeek !== 0 && dayOfWeek !== 6) {
                        workDaysAdded++;
                    }
                }
            }
            
            return endDate;
        }

        function calculateEndDate() {
            const startDate = new Date(document.getElementById('start-date').value);
            const quantity = parseFloat(document.getElementById('quantity').value) || 0;
            const efficiency = parseFloat(document.getElementById('efficiency').value) || 1;
            
            if (quantity <= 0 || efficiency <= 0) {
                document.getElementById('manual-result').innerHTML = '<span class="error">请输入有效的工程数量和每日工效</span>';
                document.getElementById('manual-result').style.display = 'block';
                return;
            }
            
            const duration = Math.ceil(quantity / efficiency);
            document.getElementById('duration').value = duration;
            
            const endDate = calculateTaskEndDate(startDate, duration);
            document.getElementById('end-date').value = endDate.toISOString().slice(0, 10);
            
            const result = `
                <strong>计算结果:</strong><br>
                开始日期: ${startDate.toLocaleDateString('zh-CN')}<br>
                工程数量: ${quantity}<br>
                每日工效: ${efficiency}<br>
                计算工期: ${duration} 天<br>
                结束日期: ${endDate.toLocaleDateString('zh-CN')}<br>
                实际跨度: ${Math.ceil((endDate - startDate) / (1000*60*60*24))} 天
            `;
            
            document.getElementById('manual-result').innerHTML = result;
            document.getElementById('manual-result').style.display = 'block';
        }

        function runPredefinedTests() {
            const tests = [
                {
                    name: "1天工期测试",
                    startDate: "2025-06-21",
                    quantity: 1,
                    efficiency: 1,
                    expectedDuration: 1,
                    expectedEndDate: "2025-06-21",
                    description: "1天工期，开始和结束应该是同一天"
                },
                {
                    name: "2天工期测试",
                    startDate: "2025-06-21", // 周六
                    quantity: 2,
                    efficiency: 1,
                    expectedDuration: 2,
                    expectedEndDate: "2025-06-23", // 周一（跳过周日）
                    description: "2天工期，从周六开始，应该跳过周日"
                },
                {
                    name: "5天工期测试",
                    startDate: "2025-06-23", // 周一
                    quantity: 5,
                    efficiency: 1,
                    expectedDuration: 5,
                    expectedEndDate: "2025-06-27", // 周五
                    description: "5天工期，从周一到周五"
                },
                {
                    name: "小数工程量测试",
                    startDate: "2025-06-21",
                    quantity: 2.5,
                    efficiency: 1,
                    expectedDuration: 3,
                    expectedEndDate: "2025-06-24", // 跳过周日
                    description: "2.5天工程量，向上取整为3天"
                }
            ];

            let resultsHtml = '<h3>预定义测试结果</h3>';
            
            tests.forEach((test, index) => {
                const startDate = new Date(test.startDate);
                const duration = Math.ceil(test.quantity / test.efficiency);
                const endDate = calculateTaskEndDate(startDate, duration);
                const actualEndDate = endDate.toISOString().slice(0, 10);
                
                const success = duration === test.expectedDuration && actualEndDate === test.expectedEndDate;
                
                resultsHtml += `
                    <div class="test-form">
                        <h4>测试 ${index + 1}: ${test.name}</h4>
                        <p><strong>描述:</strong> ${test.description}</p>
                        <p><strong>输入:</strong> 开始=${test.startDate}, 数量=${test.quantity}, 工效=${test.efficiency}</p>
                        <div class="result ${success ? '' : 'error'}">
                            <strong>结果:</strong> ${success ? '✅ 通过' : '❌ 失败'}<br>
                            计算工期: ${duration} 天 (期望: ${test.expectedDuration})<br>
                            计算结束日期: ${actualEndDate} (期望: ${test.expectedEndDate})<br>
                            开始日期: ${startDate.toLocaleDateString('zh-CN', {weekday: 'long'})}<br>
                            结束日期: ${endDate.toLocaleDateString('zh-CN', {weekday: 'long'})}
                        </div>
                    </div>
                `;
            });
            
            document.getElementById('test-results').innerHTML = resultsHtml;
        }

        // 页面加载时自动运行测试
        window.addEventListener('load', function() {
            // 设置默认值并计算
            calculateEndDate();
            
            // 运行预定义测试
            setTimeout(runPredefinedTests, 500);
        });

        // 监听输入变化
        document.getElementById('quantity').addEventListener('input', calculateEndDate);
        document.getElementById('efficiency').addEventListener('input', calculateEndDate);
        document.getElementById('start-date').addEventListener('change', calculateEndDate);
    </script>
</body>
</html>
