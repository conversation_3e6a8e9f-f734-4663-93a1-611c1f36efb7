{% extends 'base.html' %}

{% block title %}项目任务管理 - AI-DEEP-CPMS{% endblock %}

{% block extra_css %}
<style>
/* 主要布局样式 */
.main-container {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    padding: 2rem 0;
}

.page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    color: white;
    padding: 2rem;
    border-radius: 1rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-lg);
}

.breadcrumb-nav {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.breadcrumb-nav a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb-nav a:hover {
    color: white;
}

/* 模板选择区域 */
.template-section {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
}

.template-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.template-card {
    background: white;
    border: 2px solid var(--border-color);
    border-radius: 1rem;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.template-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #2563eb, #10b981, #f59e0b, #ef4444);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.template-card:hover::before {
    transform: scaleX(1);
}

.template-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
    transform: translateY(-5px);
}

.template-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    border-radius: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
}

.template-card.active {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, #f0f7ff 0%, #e0f2fe 100%);
}

/* 工具栏样式 */
.gantt-toolbar {
    background: white;
    padding: 2rem;
    border-radius: 1rem;
    margin-bottom: 2rem;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.toolbar-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 1.5rem;
    border-radius: 0.75rem;
    border: 1px solid var(--border-color);
    height: 100%;
}

.project-selector {
    max-width: 100%;
}

/* 甘特图容器 */
#gantt-container {
    min-height: 600px;
    border: 1px solid var(--border-color);
    border-radius: 1rem;
    background: white;
    overflow: hidden;
    position: relative;
}

.gantt-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 1rem 1rem 0 0;
    margin: -1px -1px 0 -1px;
}

.stats-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 2rem;
    font-weight: 500;
    font-size: 0.875rem;
    backdrop-filter: blur(10px);
}

/* 任务控制面板 */
.task-controls {
    margin-top: 2rem;
}

.task-panel {
    background: white;
    border-radius: 1rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.task-panel-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.task-list-container {
    max-height: 500px;
    overflow-y: auto;
}

.task-item {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.task-item::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--primary-color);
    transform: scaleY(0);
    transition: transform 0.3s ease;
}

.task-item:hover::before {
    transform: scaleY(1);
}

.task-item:hover {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    transform: translateX(8px);
}

.task-item:last-child {
    border-bottom: none;
}

.task-meta {
    display: flex;
    gap: 1rem;
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: var(--secondary-color);
}

.task-progress {
    width: 100%;
    height: 6px;
    background: var(--border-color);
    border-radius: 3px;
    overflow: hidden;
    margin-top: 0.5rem;
}

.task-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), #10b981);
    border-radius: 3px;
    transition: width 0.3s ease;
}

/* 任务详情面板 */
.task-details-panel {
    background: white;
    border-radius: 1rem;
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.task-details-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    color: white;
    padding: 1.5rem;
}

.task-details-content {
    padding: 2rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 600;
    color: var(--secondary-color);
}

.detail-value {
    color: var(--dark-color);
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    color: var(--secondary-color);
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
    background: linear-gradient(135deg, var(--primary-color), #1d4ed8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.empty-features {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
}

.empty-feature {
    text-align: center;
}

.empty-feature-icon {
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    margin: 0 auto 0.5rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .template-grid {
        grid-template-columns: 1fr;
    }

    .empty-features {
        flex-direction: column;
        gap: 1rem;
    }

    .task-meta {
        flex-direction: column;
        gap: 0.5rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="main-container">
    <div class="container">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="breadcrumb-nav">
                <a href="/" class="me-2">
                    <i class="fas fa-home me-1"></i>首页
                </a>
                <span class="me-2">/</span>
                <span>项目任务管理</span>
            </div>
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="fw-bold mb-2">
                        <i class="fas fa-tasks me-3"></i>
                        项目任务管理
                    </h1>
                    <p class="mb-0 opacity-75">智能化项目管理，可视化任务进度，优化资源配置</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-light" onclick="showTemplateSelector()">
                        <i class="fas fa-layer-group me-1"></i>选择模板
                    </button>
                    <a href="/interactive-gantt/" class="btn btn-outline-light">
                        <i class="fas fa-mouse-pointer me-1"></i>交互式甘特图
                    </a>
                    <button class="btn btn-light" onclick="importProject()">
                        <i class="fas fa-file-import me-1"></i>导入项目
                    </button>
                </div>
            </div>
        </div>

        <!-- 项目模板选择区域 -->
        <div class="template-section" id="template-section" style="display: none;">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h4 class="fw-bold mb-0">
                    <i class="fas fa-layer-group me-2 text-primary"></i>
                    选择项目模板
                </h4>
                <button class="btn btn-outline-secondary" onclick="hideTemplateSelector()">
                    <i class="fas fa-times me-1"></i>关闭
                </button>
            </div>
            <p class="text-muted mb-4">选择适合您项目类型的模板，快速开始项目管理</p>

            <div class="template-grid">
                <div class="template-card" data-template="construction" onclick="selectTemplate('construction')">
                    <div class="template-icon">
                        <i class="fas fa-hard-hat"></i>
                    </div>
                    <h5 class="fw-bold mb-2">建筑工程</h5>
                    <p class="text-muted small mb-3">适用于建筑施工、装修改造等工程项目</p>
                    <div class="d-flex justify-content-center gap-2">
                        <span class="badge bg-primary">WBS结构</span>
                        <span class="badge bg-success">进度管控</span>
                    </div>
                </div>

                <div class="template-card" data-template="software" onclick="selectTemplate('software')">
                    <div class="template-icon">
                        <i class="fas fa-code"></i>
                    </div>
                    <h5 class="fw-bold mb-2">软件开发</h5>
                    <p class="text-muted small mb-3">适用于软件开发、系统集成等IT项目</p>
                    <div class="d-flex justify-content-center gap-2">
                        <span class="badge bg-info">敏捷开发</span>
                        <span class="badge bg-warning">迭代管理</span>
                    </div>
                </div>

                <div class="template-card" data-template="marketing" onclick="selectTemplate('marketing')">
                    <div class="template-icon">
                        <i class="fas fa-bullhorn"></i>
                    </div>
                    <h5 class="fw-bold mb-2">市场营销</h5>
                    <p class="text-muted small mb-3">适用于市场推广、品牌建设等营销项目</p>
                    <div class="d-flex justify-content-center gap-2">
                        <span class="badge bg-danger">活动策划</span>
                        <span class="badge bg-secondary">效果跟踪</span>
                    </div>
                </div>

                <div class="template-card" data-template="research" onclick="selectTemplate('research')">
                    <div class="template-icon">
                        <i class="fas fa-flask"></i>
                    </div>
                    <h5 class="fw-bold mb-2">研发项目</h5>
                    <p class="text-muted small mb-3">适用于产品研发、技术创新等研发项目</p>
                    <div class="d-flex justify-content-center gap-2">
                        <span class="badge bg-success">里程碑</span>
                        <span class="badge bg-info">风险管控</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 工具栏 -->
        <div class="gantt-toolbar">
            <div class="row g-4">
                <div class="col-lg-3 col-md-6">
                    <div class="toolbar-section">
                        <label for="project-select" class="form-label fw-semibold mb-3">
                            <i class="fas fa-folder-open me-2 text-primary"></i>
                            选择项目
                        </label>
                        <select id="project-select" class="form-select project-selector">
                            <option value="">请选择项目...</option>
                        </select>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="toolbar-section">
                        <label class="form-label fw-semibold mb-3">
                            <i class="fas fa-cogs me-2 text-primary"></i>
                            项目操作
                        </label>
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="loadGanttChart()">
                                <i class="fas fa-chart-gantt me-2"></i>加载甘特图
                            </button>
                            <button class="btn btn-success" onclick="showAddTaskForm()">
                                <i class="fas fa-plus me-2"></i>添加任务
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="toolbar-section">
                        <label class="form-label fw-semibold mb-3">
                            <i class="fas fa-eye me-2 text-primary"></i>
                            视图控制
                        </label>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-secondary" onclick="zoomToFit()">
                                <i class="fas fa-expand-arrows-alt me-2"></i>适应窗口
                            </button>
                            <button class="btn btn-outline-info" onclick="toggleTimeScale()">
                                <i class="fas fa-calendar-alt me-2"></i>时间刻度
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="toolbar-section">
                        <label class="form-label fw-semibold mb-3">
                            <i class="fas fa-download me-2 text-primary"></i>
                            导入导出
                        </label>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" onclick="exportGantt()">
                                <i class="fas fa-file-export me-2"></i>导出项目
                            </button>
                            <button class="btn btn-outline-warning" onclick="showImportDialog()">
                                <i class="fas fa-file-import me-2"></i>导入文件
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 甘特图容器 -->
        <div class="card border-0 shadow-sm">
            <div class="gantt-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1 fw-bold">
                            <i class="fas fa-chart-gantt me-2"></i>
                            <span id="gantt-title" onclick="editGanttTitle()" style="cursor: pointer;" title="点击编辑标题">项目进度甘特图</span>
                            <i class="fas fa-edit ms-2 text-white-50" style="font-size: 0.8em;"></i>
                        </h4>
                        <small class="opacity-75">实时可视化项目任务和进度，支持拖拽调整和依赖关系管理</small>
                    </div>
                    <div class="d-flex gap-2 flex-wrap">
                        <span class="stats-badge" id="task-count">
                            <i class="fas fa-tasks me-1"></i>任务数: 0
                        </span>
                        <span class="stats-badge" id="critical-path">
                            <i class="fas fa-route me-1"></i>关键路径: 0
                        </span>
                        <span class="stats-badge" id="progress-stats">
                            <i class="fas fa-percentage me-1"></i>完成度: 0%
                        </span>
                        <span class="stats-badge" id="duration-stats">
                            <i class="fas fa-clock me-1"></i>总工期: 0天
                        </span>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div id="gantt-container">
                    <div class="empty-state">
                        <i class="fas fa-chart-gantt"></i>
                        <h4 class="mt-4 mb-3">智能甘特图视图</h4>
                        <p class="mb-4 text-muted">选择项目并点击"加载甘特图"开始可视化您的项目进度</p>
                        <div class="empty-features">
                            <div class="empty-feature">
                                <div class="empty-feature-icon">
                                    <i class="fas fa-tasks"></i>
                                </div>
                                <h6 class="fw-bold">任务管理</h6>
                                <small class="text-muted">创建、编辑、删除任务</small>
                            </div>
                            <div class="empty-feature">
                                <div class="empty-feature-icon">
                                    <i class="fas fa-link"></i>
                                </div>
                                <h6 class="fw-bold">依赖关系</h6>
                                <small class="text-muted">设置任务间的依赖</small>
                            </div>
                            <div class="empty-feature">
                                <div class="empty-feature-icon">
                                    <i class="fas fa-route"></i>
                                </div>
                                <h6 class="fw-bold">关键路径</h6>
                                <small class="text-muted">自动计算关键路径</small>
                            </div>
                            <div class="empty-feature">
                                <div class="empty-feature-icon">
                                    <i class="fas fa-chart-line"></i>
                                </div>
                                <h6 class="fw-bold">进度跟踪</h6>
                                <small class="text-muted">实时监控项目进度</small>
                            </div>
                        </div>
                        <div class="mt-4">
                            <button class="btn btn-primary btn-lg" onclick="showQuickStart()">
                                <i class="fas fa-rocket me-2"></i>快速开始
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 任务控制面板 -->
        <div class="task-controls" id="task-controls" style="display: none;">
            <div class="row g-4">
                <div class="col-lg-8">
                    <div class="task-panel">
                        <div class="task-panel-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h5 class="mb-0 fw-bold">
                                    <i class="fas fa-list me-2 text-primary"></i>
                                    任务列表
                                </h5>
                                <div class="d-flex gap-2">
                                    <div class="input-group" style="width: 250px;">
                                        <input type="text" class="form-control" placeholder="搜索任务..." id="task-search">
                                        <button class="btn btn-outline-secondary" type="button">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                    <button class="btn btn-outline-primary" onclick="loadTaskList(currentProjectId)">
                                        <i class="fas fa-sync-alt me-1"></i>刷新
                                    </button>
                                    <div class="dropdown">
                                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                            <i class="fas fa-filter me-1"></i>筛选
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" onclick="filterTasks('all')">全部任务</a></li>
                                            <li><a class="dropdown-item" href="#" onclick="filterTasks('not_started')">未开始</a></li>
                                            <li><a class="dropdown-item" href="#" onclick="filterTasks('in_progress')">进行中</a></li>
                                            <li><a class="dropdown-item" href="#" onclick="filterTasks('completed')">已完成</a></li>
                                            <li><a class="dropdown-item" href="#" onclick="filterTasks('critical')">关键路径</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="task-list-container">
                            <div id="task-list">
                                <!-- 任务列表将在这里显示 -->
                                <div class="text-center py-5">
                                    <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">暂无任务数据</h5>
                                    <p class="text-muted">请先选择项目并加载甘特图</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4">
                    <div class="task-details-panel">
                        <div class="task-details-header">
                            <h5 class="mb-0 fw-bold">
                                <i class="fas fa-info-circle me-2"></i>
                                任务详情
                            </h5>
                        </div>
                        <div class="task-details-content">
                            <div id="task-details">
                                <div class="text-center py-5">
                                    <i class="fas fa-mouse-pointer fa-3x text-muted mb-3"></i>
                                    <h6 class="text-muted">选择任务查看详情</h6>
                                    <p class="text-muted small">点击左侧任务列表中的任务项目</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 添加任务表单 -->
        <div class="task-form" id="add-task-form" style="display: none;">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-gradient-primary text-white border-bottom-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0 fw-bold">
                            <i class="fas fa-plus me-2"></i>
                            创建新任务
                        </h4>
                        <button type="button" class="btn-close btn-close-white" onclick="hideAddTaskForm()"></button>
                    </div>
                    <p class="mb-0 mt-2 opacity-75">填写任务详细信息，系统将自动计算工期和依赖关系</p>
                </div>
                <div class="card-body p-4">
                    <form id="task-form">
                        <div class="row g-4">
                            <div class="col-lg-6">
                                <div class="mb-3">
                                    <label for="task-wbs" class="form-label fw-semibold">
                                        <i class="fas fa-code me-2 text-primary"></i>
                                        WBS编码 *
                                    </label>
                                    <input type="text" class="form-control form-control-lg" id="task-wbs" required placeholder="例如: 1.1.1">
                                    <div class="form-text">工作分解结构编码，用于任务层级管理</div>
                                </div>
                                <div class="mb-3">
                                    <label for="task-name" class="form-label fw-semibold">
                                        <i class="fas fa-tag me-2 text-primary"></i>
                                        任务名称 *
                                    </label>
                                    <input type="text" class="form-control form-control-lg" id="task-name" required placeholder="输入任务名称">
                                </div>
                                <div class="mb-3">
                                    <label for="task-priority" class="form-label fw-semibold">
                                        <i class="fas fa-flag me-2 text-primary"></i>
                                        优先级
                                    </label>
                                    <select class="form-select form-select-lg" id="task-priority">
                                        <option value="low">低优先级</option>
                                        <option value="normal" selected>普通优先级</option>
                                        <option value="high">高优先级</option>
                                        <option value="urgent">紧急</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="task-unit" class="form-label fw-semibold">
                                        <i class="fas fa-ruler me-2 text-primary"></i>
                                        计量单位
                                    </label>
                                    <input type="text" class="form-control" id="task-unit" placeholder="例如: 立方米、平方米、个">
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="mb-3">
                                    <label for="task-quantity" class="form-label fw-semibold">
                                        <i class="fas fa-calculator me-2 text-primary"></i>
                                        工程数量
                                    </label>
                                    <input type="number" class="form-control" id="task-quantity" step="0.01" placeholder="0.00" onchange="calculateDuration()">
                                </div>
                                <div class="mb-3">
                                    <label for="task-efficiency" class="form-label fw-semibold">
                                        <i class="fas fa-tachometer-alt me-2 text-primary"></i>
                                        每日工效
                                    </label>
                                    <input type="number" class="form-control" id="task-efficiency" step="0.01" placeholder="0.00" onchange="calculateDuration()">
                                    <div class="form-text">每日计划完成的工程量</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-semibold">
                                        <i class="fas fa-clock me-2 text-primary"></i>
                                        计划工期
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="task-duration" readonly>
                                        <span class="input-group-text">天</span>
                                    </div>
                                    <div class="form-text">自动计算：工程数量 ÷ 每日工效</div>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <label for="task-start" class="form-label fw-semibold">
                                            <i class="fas fa-calendar-alt me-2 text-primary"></i>
                                            开始时间 *
                                        </label>
                                        <input type="datetime-local" class="form-control" id="task-start" required onchange="calculateEndDate()">
                                    </div>
                                    <div class="col-6">
                                        <label for="task-end" class="form-label fw-semibold">
                                            <i class="fas fa-calendar-check me-2 text-primary"></i>
                                            计划完成时间
                                        </label>
                                        <input type="datetime-local" class="form-control" id="task-end" readonly>
                                        <div class="form-text">自动计算（排除非工作日）</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="mb-4">
                                    <label for="task-description" class="form-label fw-semibold">
                                        <i class="fas fa-align-left me-2 text-primary"></i>
                                        任务描述
                                    </label>
                                    <textarea class="form-control" id="task-description" rows="4" placeholder="详细描述任务内容、要求、注意事项等..."></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- 前置任务和依赖关系 -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="task-predecessors" class="form-label fw-semibold">
                                        <i class="fas fa-link me-2 text-primary"></i>
                                        前置任务
                                    </label>
                                    <select class="form-select" id="task-predecessors" multiple onchange="toggleDependencySettings()">
                                        <option value="">无前置任务</option>
                                        <!-- 动态加载项目中的其他任务 -->
                                    </select>
                                    <div class="form-text">选择当前任务依赖的前置任务</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="dependency-type" class="form-label fw-semibold">
                                        <i class="fas fa-project-diagram me-2 text-primary"></i>
                                        依赖类型
                                    </label>
                                    <select class="form-select" id="dependency-type">
                                        <option value="FS">FS: 完成-开始</option>
                                        <option value="SS">SS: 开始-开始</option>
                                        <option value="SF">SF: 开始-完成</option>
                                        <option value="FF">FF: 完成-完成</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="lag-time" class="form-label fw-semibold">
                                        <i class="fas fa-stopwatch me-2 text-primary"></i>
                                        滞后时间
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="lag-time" value="0">
                                        <span class="input-group-text">分钟</span>
                                    </div>
                                    <div class="form-text">正数表示延迟，负数表示提前</div>
                                </div>
                            </div>
                        </div>

                        <!-- 关联清单项 -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="mb-3">
                                    <label for="task-list-items" class="form-label fw-semibold">
                                        <i class="fas fa-list-ul me-2 text-primary"></i>
                                        关联清单项
                                    </label>
                                    <select class="form-select" id="task-list-items" multiple>
                                        <!-- 动态加载项目清单项 -->
                                    </select>
                                    <div class="form-text">选择与此任务相关的项目清单项，用于成本管理</div>
                                </div>
                                <div id="selected-list-items" class="mt-2">
                                    <!-- 显示已选择的清单项 -->
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex gap-3 justify-content-end">
                                    <button type="button" class="btn btn-outline-secondary btn-lg" onclick="hideAddTaskForm()">
                                        <i class="fas fa-times me-2"></i>取消
                                    </button>
                                    <button type="button" class="btn btn-outline-primary btn-lg" onclick="saveAsDraft()">
                                        <i class="fas fa-save me-2"></i>保存草稿
                                    </button>
                                    <button type="submit" class="btn btn-success btn-lg">
                                        <i class="fas fa-check me-2"></i>创建任务
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentProjectId = null;
let ganttChart = null;
let currentTasks = [];
let selectedTemplate = null;
let taskFilter = 'all';

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    loadProjects();
    setupEventListeners();

    // 等待Highcharts加载完成，然后显示默认甘特图
    waitForHighcharts();
});



// 等待Highcharts加载
function waitForHighcharts() {
    let attempts = 0;
    const maxAttempts = 50; // 最多等待5秒

    const checkHighcharts = () => {
        attempts++;

        console.log(`检测 Highcharts 第 ${attempts} 次...`);

        // 详细检测
        if (typeof Highcharts !== 'undefined') {
            console.log('✅ Highcharts 已定义, 版本:', Highcharts.version);

            if (typeof Highcharts.ganttChart === 'function') {
                console.log('✅ Highcharts.ganttChart 函数可用');

                // 检查甘特图系列类型
                if (Highcharts.seriesTypes && Highcharts.seriesTypes.gantt) {
                    console.log('✅ Gantt 系列类型可用');
                } else {
                    console.log('⚠️ Gantt 系列类型不可用');
                }

                console.log('🎉 Highcharts Gantt ready');

                // 检查 AdvancedGantt 是否已加载
                if (typeof AdvancedGantt !== 'undefined') {
                    console.log('✅ AdvancedGantt 组件已可用');
                    showNotification('高级甘特图组件已就绪', 'success');
                } else {
                    console.log('⚠️ AdvancedGantt 组件未加载，将使用基础功能');
                    showNotification('甘特图组件已就绪（基础模式）', 'info');
                }

                // 立即显示一个示例甘特图来验证功能
                renderDefaultGanttChart();
                return;
            } else {
                console.log('❌ Highcharts.ganttChart 函数不可用');
            }
        } else {
            console.log('❌ Highcharts 未定义');
        }

        if (attempts < maxAttempts) {
            setTimeout(checkHighcharts, 100);
        } else {
            console.error('Highcharts failed to load after 5 seconds, using fallback');
            showNotification('Highcharts 加载超时，使用备用方案', 'warning');
            loadSimpleGanttFallback();
        }
    };

    checkHighcharts();
}

// 加载备用甘特图组件
function loadSimpleGanttFallback() {
    // 动态加载简单甘特图脚本
    const script = document.createElement('script');
    script.src = '/static/js/simple-gantt.js';
    script.onload = function() {
        console.log('Simple Gantt fallback loaded');
        showNotification('备用甘特图组件已加载', 'info');
        window.useSimpleGantt = true;
    };
    script.onerror = function() {
        console.error('Failed to load simple gantt fallback');
        showNotification('甘特图组件加载失败', 'error');
    };
    document.head.appendChild(script);
}

// 初始化页面
function initializePage() {
    // 添加加载动画
    showLoadingState();

    // 设置默认日期
    const now = new Date();
    const startInput = document.getElementById('task-start');
    if (startInput) {
        startInput.value = now.toISOString().slice(0, 16);
    }

    // 隐藏加载状态
    setTimeout(hideLoadingState, 1000);
}

// 设置事件监听器
function setupEventListeners() {
    // 表单提交事件
    const taskForm = document.getElementById('task-form');
    if (taskForm) {
        taskForm.addEventListener('submit', function(e) {
            e.preventDefault();
            createTask();
        });
    }

    // 搜索功能
    const searchInput = document.getElementById('task-search');
    if (searchInput) {
        searchInput.addEventListener('input', debounce(searchTasks, 300));
    }

    // 项目选择变化
    const projectSelect = document.getElementById('project-select');
    if (projectSelect) {
        projectSelect.addEventListener('change', function() {
            if (this.value) {
                currentProjectId = this.value;
                loadProjectInfo(this.value);
                loadSavedGanttTitle();
                // 自动加载甘特图
                setTimeout(() => {
                    loadGanttChart();
                }, 500);
            }
        });
    }

    // 清单项选择变化
    const listItemSelect = document.getElementById('task-list-items');
    if (listItemSelect) {
        listItemSelect.addEventListener('change', updateSelectedListItems);
    }
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 显示加载状态
function showLoadingState() {
    const elements = document.querySelectorAll('.loading-target');
    elements.forEach(el => el.classList.add('loading-pulse'));
}

// 隐藏加载状态
function hideLoadingState() {
    const elements = document.querySelectorAll('.loading-target');
    elements.forEach(el => el.classList.remove('loading-pulse'));
}

// 加载项目列表
function loadProjects() {
    fetch('/api/projects/')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('project-select');
            select.innerHTML = '<option value="">请选择项目...</option>';

            if (data.results) {
                data.results.forEach(project => {
                    const option = document.createElement('option');
                    option.value = project.id;
                    option.textContent = `${project.name} (${project.status})`;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading projects:', error);
            showNotification('加载项目列表失败', 'error');
        });
}

// 加载项目信息
function loadProjectInfo(projectId) {
    fetch(`/api/projects/${projectId}/`)
        .then(response => response.json())
        .then(project => {
            updateProjectStats(project);
        })
        .catch(error => {
            console.error('Error loading project info:', error);
        });
}

// 更新项目统计信息
function updateProjectStats(project) {
    // 这里可以添加项目统计信息的更新逻辑
    console.log('Project loaded:', project.name);
}

// 模板选择功能
function showTemplateSelector() {
    const templateSection = document.getElementById('template-section');
    templateSection.style.display = 'block';
    templateSection.scrollIntoView({ behavior: 'smooth' });
}

function hideTemplateSelector() {
    document.getElementById('template-section').style.display = 'none';
}

function selectTemplate(templateType) {
    // 移除之前的选中状态
    document.querySelectorAll('.template-card').forEach(card => {
        card.classList.remove('active');
    });

    // 添加选中状态
    const selectedCard = document.querySelector(`[data-template="${templateType}"]`);
    selectedCard.classList.add('active');

    selectedTemplate = templateType;

    // 显示确认按钮或直接应用模板
    setTimeout(() => {
        if (confirm(`确定要应用"${getTemplateName(templateType)}"模板吗？`)) {
            applyTemplate(templateType);
        }
    }, 500);
}

function getTemplateName(templateType) {
    const names = {
        'construction': '建筑工程',
        'software': '软件开发',
        'marketing': '市场营销',
        'research': '研发项目'
    };
    return names[templateType] || templateType;
}

function applyTemplate(templateType) {
    // 这里可以根据模板类型创建预设的任务结构
    showNotification(`已应用${getTemplateName(templateType)}模板`, 'success');
    hideTemplateSelector();

    // 如果有选中的项目，可以自动创建模板任务
    if (currentProjectId) {
        createTemplateTask(templateType);
    }
}

// 加载甘特图
function loadGanttChart() {
    const projectId = document.getElementById('project-select').value;
    if (!projectId) {
        showNotification('请先选择一个项目', 'warning');
        return;
    }

    currentProjectId = projectId;
    showLoadingState();

    fetch(`/api/projects/${projectId}/gantt_data/`)
        .then(response => response.json())
        .then(data => {
            currentTasks = data.tasks || [];
            renderGanttChart(data);
            loadTaskList(projectId);
            document.getElementById('task-controls').style.display = 'block';

            // 更新统计信息
            updateGanttStats(data);
            showNotification('甘特图加载成功', 'success');
        })
        .catch(error => {
            console.error('Error loading gantt data:', error);
            showNotification('加载甘特图数据失败', 'error');
        })
        .finally(() => {
            hideLoadingState();
        });
}

// 更新甘特图统计信息
function updateGanttStats(data) {
    const tasks = data.tasks || [];
    const criticalTasks = tasks.filter(task => task.critical).length;
    const completedTasks = tasks.filter(task => task.progress >= 1).length;
    const avgProgress = tasks.length > 0 ?
        Math.round(tasks.reduce((sum, task) => sum + (task.progress || 0), 0) / tasks.length * 100) : 0;

    // 计算总工期
    let totalDuration = 0;
    if (tasks.length > 0) {
        const startDates = tasks.map(task => new Date(task.start));
        const endDates = tasks.map(task => new Date(task.end));
        const projectStart = new Date(Math.min(...startDates));
        const projectEnd = new Date(Math.max(...endDates));
        totalDuration = Math.ceil((projectEnd - projectStart) / (1000 * 60 * 60 * 24));
    }

    document.getElementById('task-count').textContent = `任务数: ${tasks.length}`;
    document.getElementById('critical-path').textContent = `关键路径: ${criticalTasks}`;
    document.getElementById('progress-stats').textContent = `完成度: ${avgProgress}%`;
    document.getElementById('duration-stats').textContent = `总工期: ${totalDuration}天`;
}

// 渲染甘特图
function renderGanttChart(data) {
    // 如果使用备用甘特图
    if (window.useSimpleGantt || typeof Highcharts === 'undefined' || typeof Highcharts.ganttChart !== 'function') {
        renderSimpleGanttChart(data);
        return;
    }

    try {
        // 如果没有任务数据，显示默认甘特图
        if (!data.tasks || data.tasks.length === 0) {
            renderDefaultGanttChart();
            return;
        }

        // 转换数据格式，处理父子任务关系
        const ganttData = data.tasks.map((task, index) => {
            const taskData = {
                id: task.id || `task_${index}`,
                name: task.summary ? `📁 ${task.wbs_code} ${task.name}` : `${task.wbs_code} ${task.name}`,
                wbs_code: task.wbs_code,
                start: new Date(task.start || task.start_date).getTime(),
                end: new Date(task.end || task.end_date).getTime(),
                completed: {
                    amount: (task.progress_percentage || task.progress || 0) / 100
                },
                parent: task.parent || undefined,
                owner: task.owner || '未分配',
                y: index,
                // 根据任务类型设置不同的颜色和样式
                color: task.critical ? '#ef4444' :
                       task.summary ? '#8b5cf6' :
                       (task.status === 'completed' ? '#10b981' : '#3b82f6'),
                milestone: task.milestone || false,
                summary: task.summary || false,
                // 添加更多任务信息
                quantity: task.quantity || 0,
                unit: task.unit || '',
                daily_efficiency: task.daily_efficiency || 0,
                duration: task.planned_duration || 0,
                total_cost: task.total_cost || 0,
                description: task.description || '',
                status: task.status,
                priority: task.priority,
                predecessor_dependencies: task.predecessor_dependencies || []
            };

            // 根据任务类型设置样式
            if (task.summary) {
                // 摘要任务样式 - 使用特殊的甘特图摘要样式
                taskData.color = '#8b5cf6';
                taskData.borderColor = '#7c3aed';
                taskData.borderWidth = 2;
                taskData.pointWidth = 20;
                // 摘要任务标记
                taskData.custom = {
                    isSummary: true
                };
            } else {
                // 子任务样式 - 标准矩形进度条
                taskData.color = task.critical ? '#ef4444' :
                                (task.status === 'completed' ? '#10b981' : '#3b82f6');
                taskData.borderColor = task.critical ? '#dc2626' :
                                     (task.status === 'completed' ? '#059669' : '#2563eb');
                taskData.borderWidth = 1;
                taskData.pointWidth = 15;
                // 子任务标记
                taskData.custom = {
                    isSummary: false
                };
            }

            // 处理依赖关系连线 - 使用dependency属性
            if (task.predecessor_dependencies && task.predecessor_dependencies.length > 0) {
                // Highcharts Gantt使用dependency属性来定义连接
                // 只使用第一个前置任务作为主要依赖
                const firstDep = task.predecessor_dependencies[0];
                taskData.dependency = firstDep.from_task;
            }

            return taskData;
        });

        // 处理依赖关系连线数据
        const dependencies = [];
        if (data.dependencies) {
            data.dependencies.forEach(dep => {
                dependencies.push({
                    from: dep.from,
                    to: dep.to,
                    type: dep.type || 'FS',
                    lag: dep.lag || 0
                });
            });
        }

        console.log('甘特图数据:', ganttData);
        console.log('依赖关系:', dependencies);

        // 创建高级甘特图
        console.log('检查 AdvancedGantt 可用性:', typeof AdvancedGantt);
        if (typeof AdvancedGantt !== 'undefined') {
            console.log('使用高级甘特图组件');
            // 销毁现有图表
            if (window.currentAdvancedGantt) {
                window.currentAdvancedGantt.destroy();
            }

            const ganttTitle = document.getElementById('gantt-title').textContent;
            window.currentAdvancedGantt = new AdvancedGantt('gantt-container', {
                title: ganttTitle,
                subtitle: '拖拽任务条可调整时间，点击任务查看详情',
                data: ganttData,
                dependencies: dependencies,
                enableDragDrop: true,
                enableInteraction: true,
                showCurrentDate: true,
                showWeekends: true,
                height: 600,
                onTaskClick: function(point) {
                    console.log('任务点击:', point.name);
                    showTaskDetails(point.id);
                },
                onTaskSelect: function(point) {
                    console.log('任务选中:', point.name);
                },
                onChartReady: function(chart) {
                    ganttChart = chart;
                    showNotification('高级甘特图加载成功', 'success');
                },
                onError: function(error) {
                    console.error('高级甘特图错误:', error);
                    showNotification('甘特图渲染失败: ' + error.message, 'error');
                    renderEmptyGanttChart();
                }
            });
        } else {
            console.log('AdvancedGantt 不可用，使用基础甘特图');
            // 回退到基础Highcharts甘特图
            renderBasicGanttChart(ganttData, dependencies, data);
        }

    } catch (error) {
        console.error('Error rendering gantt chart:', error);
        showNotification('甘特图渲染失败: ' + error.message, 'error');
        renderEmptyGanttChart();
    }
}

// 渲染基础甘特图（回退方案）
function renderBasicGanttChart(ganttData, dependencies = [], data = null) {
    // 如果没有传递依赖关系，从任务数据中提取
    if (dependencies.length === 0) {
        ganttData.forEach(task => {
            if (task.dependency && Array.isArray(task.dependency)) {
                task.dependency.forEach(dep => {
                    dependencies.push({
                        from: dep.from,
                        to: task.id,
                        type: dep.type,
                        lag: dep.lag
                    });
                });
            }
        });
    }

    const ganttTitle = document.getElementById('gantt-title').textContent;
    ganttChart = Highcharts.ganttChart('gantt-container', {
        title: {
            text: ganttTitle,
            style: {
                fontSize: '18px',
                fontWeight: 'bold'
            }
        },
        subtitle: {
            text: '拖拽任务条可调整时间，点击任务查看详情'
        },
        xAxis: {
            type: 'datetime',
            grid: {
                enabled: true,
                borderWidth: 1
            },
            dateTimeLabelFormats: {
                millisecond: '%H:%M:%S.%L',
                second: '%H:%M:%S',
                minute: '%H:%M',
                hour: '%H:%M',
                day: '%m-%d',
                week: '%m-%d',
                month: '%Y-%m',
                year: '%Y'
            },
            tickInterval: 24 * 3600 * 1000, // 每天一个刻度
            tickPixelInterval: 50, // 控制刻度密度
            labels: {
                style: {
                    fontSize: '11px'
                },
                rotation: 0,
                format: '{value:%m-%d}' // 强制使用月-日格式
            },
            min: data ? data.min_date : undefined,
            max: data ? data.max_date : undefined
        },
        yAxis: {
            type: 'category',
            grid: {
                enabled: true
            }
        },
        series: [{
            name: '项目任务',
            data: ganttData,
            type: 'gantt',
            // 自定义点渲染器
            point: {
                events: {
                    afterSetState: function() {
                        // 为摘要任务添加特殊样式
                        if (this.custom && this.custom.isSummary) {
                            if (this.graphic) {
                                // 添加箭头标记
                                this.graphic.attr({
                                    'stroke-width': 2,
                                    'stroke': '#7c3aed'
                                });
                            }
                        }
                    }
                }
            }
        }],
        plotOptions: {
            gantt: {
                dataLabels: {
                    enabled: true,
                    format: '{point.wbs_code} {point.name}',
                    style: {
                        fontSize: '11px',
                        fontWeight: 'bold',
                        color: 'white',
                        textOutline: '1px contrast'
                    },
                    align: 'left',
                    verticalAlign: 'middle',
                    inside: true
                },
                point: {
                    events: {
                        click: function() {
                            showTaskDetails(this.id);
                        }
                    }
                },
                // 设置任务条的基本样式
                borderRadius: 3,
                groupPadding: 0.1,
                pointPadding: 0.1
            }
        },
        tooltip: {
            useHTML: true,
            formatter: function() {
                const point = this.point;
                return `
                    <div class="gantt-tooltip">
                        <h6><strong>${point.name}</strong></h6>
                        <div><strong>WBS:</strong> ${point.wbs_code || 'N/A'}</div>
                        <div><strong>开始:</strong> ${Highcharts.dateFormat('%Y-%m-%d', point.start)}</div>
                        <div><strong>结束:</strong> ${Highcharts.dateFormat('%Y-%m-%d', point.end)}</div>
                        <div><strong>工期:</strong> ${point.duration || 0}天</div>
                        <div><strong>进度:</strong> ${Math.round((point.completed?.amount || 0) * 100)}%</div>
                        <div><strong>工程量:</strong> ${point.quantity || 0} ${point.unit || ''}</div>
                        <div><strong>每日工效:</strong> ${point.daily_efficiency || 0}</div>
                        ${point.total_cost ? `<div><strong>成本:</strong> ¥${point.total_cost.toLocaleString('zh-CN')}</div>` : ''}
                    </div>
                `;
            }
        },
        credits: {
            enabled: false
        },
        // 添加依赖关系连接线
        connectors: {
            enabled: true,
            lineWidth: 2,
            lineColor: '#666666',
            marker: {
                enabled: true,
                symbol: 'arrow',
                width: 8,
                height: 8
            }
        }
    });

    // 依赖关系连线由Highcharts自动处理，无需手动添加

    showNotification('基础甘特图加载成功', 'success');
}

// 渲染默认甘特图（无数据时）
function renderDefaultGanttChart() {
    console.log('渲染默认甘特图...');

    // 优先使用 AdvancedGantt
    if (typeof AdvancedGantt !== 'undefined') {
        try {
            console.log('使用 AdvancedGantt 创建默认甘特图');

            // 销毁现有图表
            if (window.currentAdvancedGantt) {
                window.currentAdvancedGantt.destroy();
            }

            // 创建示例数据
            const today = new Date();
            const day = 24 * 60 * 60 * 1000;

            const sampleData = [{
                name: '项目规划',
                id: 'planning',
                start: today.getTime(),
                end: today.getTime() + 5 * day,
                completed: {
                    amount: 0.8
                },
                owner: '项目经理',
                y: 0
            }, {
                name: '需求分析',
                id: 'analysis',
                dependency: 'planning',
                start: today.getTime() + 3 * day,
                end: today.getTime() + 8 * day,
                completed: {
                    amount: 0.6
                },
                owner: '业务分析师',
                y: 1
            }, {
                name: '系统设计',
                id: 'design',
                dependency: 'analysis',
                start: today.getTime() + 7 * day,
                end: today.getTime() + 12 * day,
                completed: {
                    amount: 0.3
                },
                owner: '架构师',
                y: 2
            }, {
                name: '开发实施',
                id: 'development',
                dependency: 'design',
                start: today.getTime() + 10 * day,
                end: today.getTime() + 20 * day,
                completed: {
                    amount: 0.1
                },
                owner: '开发团队',
                y: 3
            }];

            // 使用 AdvancedGantt 创建甘特图
            window.currentAdvancedGantt = new AdvancedGantt('gantt-container', {
                title: '项目甘特图示例',
                subtitle: '这是一个示例甘特图，展示了项目管理的基本功能',
                data: [{
                    name: '示例项目',
                    data: sampleData
                }],
                enableDragDrop: true,
                enableInteraction: true,
                showCurrentDate: true,
                showWeekends: true,
                height: 600,
                onChartReady: function(chart) {
                    ganttChart = chart;
                    console.log('✅ 高级默认甘特图创建成功');
                    showNotification('示例甘特图加载成功（高级模式）', 'success');
                },
                onError: function(error) {
                    console.error('高级甘特图创建失败:', error);
                    renderBasicDefaultGanttChart();
                }
            });

        } catch (error) {
            console.error('创建高级默认甘特图失败:', error);
            renderBasicDefaultGanttChart();
        }
    } else if (typeof Highcharts !== 'undefined' && typeof Highcharts.ganttChart === 'function') {
        renderBasicDefaultGanttChart();
    } else {
        console.log('Highcharts 不可用，显示空状态');
        renderEmptyGanttChart();
    }
}

// 渲染基础默认甘特图（回退方案）
function renderBasicDefaultGanttChart() {
    console.log('使用基础 Highcharts 创建默认甘特图');

    try {
        // 创建示例数据
        const today = new Date();
        const day = 24 * 60 * 60 * 1000;

        const sampleData = [{
            name: '项目规划',
            id: 'planning',
            start: today.getTime(),
            end: today.getTime() + 5 * day,
            completed: {
                amount: 0.8
            },
            y: 0
        }, {
            name: '需求分析',
            id: 'analysis',
            start: today.getTime() + 3 * day,
            end: today.getTime() + 8 * day,
            completed: {
                amount: 0.6
            },
            y: 1
        }, {
            name: '系统设计',
            id: 'design',
            start: today.getTime() + 7 * day,
            end: today.getTime() + 12 * day,
            completed: {
                amount: 0.3
            },
            y: 2
        }, {
            name: '开发实施',
            id: 'development',
            start: today.getTime() + 10 * day,
            end: today.getTime() + 20 * day,
            completed: {
                amount: 0.1
            },
            y: 3
        }];

        ganttChart = Highcharts.ganttChart('gantt-container', {
            title: {
                text: '项目甘特图示例',
                style: {
                    fontSize: '18px',
                    fontWeight: 'bold'
                }
            },
            subtitle: {
                text: '这是一个示例甘特图，展示了项目管理的基本功能'
            },
            xAxis: {
                type: 'datetime'
            },
            yAxis: {
                type: 'category',
                categories: ['项目规划', '需求分析', '系统设计', '开发实施']
            },
            series: [{
                name: '示例项目',
                data: sampleData
            }],
            plotOptions: {
                gantt: {
                    dataLabels: {
                        enabled: true,
                        format: '{point.name}',
                        style: {
                            fontSize: '12px'
                        }
                    }
                }
            },
            tooltip: {
                pointFormat: '<b>{point.name}</b><br/>开始: {point.start:%Y-%m-%d}<br/>结束: {point.end:%Y-%m-%d}<br/>进度: {point.completed.amount:.1%}'
            },
            credits: {
                enabled: false
            }
        });

        console.log('✅ 基础默认甘特图创建成功');
        showNotification('示例甘特图加载成功（基础模式）', 'success');

    } catch (error) {
        console.error('创建基础默认甘特图失败:', error);
        renderEmptyGanttChart();
    }
}

// 渲染空的甘特图
function renderEmptyGanttChart() {
    const container = document.getElementById('gantt-container');
    container.innerHTML = `
        <div class="empty-state">
            <i class="fas fa-chart-gantt"></i>
            <h4 class="mt-4 mb-3">暂无甘特图数据</h4>
            <p class="mb-4 text-muted">请先创建任务，然后重新加载甘特图</p>
            <button class="btn btn-primary" onclick="showAddTaskForm()">
                <i class="fas fa-plus me-2"></i>创建第一个任务
            </button>
        </div>
    `;
}

// 加载任务列表
function loadTaskList(projectId) {
    fetch(`/api/tasks/?project_id=${projectId}`)
        .then(response => response.json())
        .then(data => {
            currentTasks = data.results || [];
            renderTaskList(currentTasks);
        })
        .catch(error => {
            console.error('Error loading tasks:', error);
            showNotification('加载任务列表失败', 'error');
        });
}

// 渲染任务列表
function renderTaskList(tasks) {
    const taskList = document.getElementById('task-list');
    let html = '';

    if (tasks && tasks.length > 0) {
        // 添加表格头部
        html += `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th width="4%">序号</th>
                            <th width="8%">WBS标识</th>
                            <th width="18%">任务描述</th>
                            <th width="6%">单位</th>
                            <th width="8%">工程数量</th>
                            <th width="8%">每日工效</th>
                            <th width="6%">计划工期</th>
                            <th width="10%">开始时间</th>
                            <th width="10%">完成时间</th>
                            <th width="10%">前置任务</th>
                            <th width="6%">进度</th>
                            <th width="6%">操作</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        tasks.forEach((task, index) => {
            const progressPercent = Math.round(task.progress_percentage || 0);
            const statusColor = getStatusColor(task.status);
            const priorityIcon = getPriorityIcon(task.priority);
            const totalCost = task.total_cost || 0;

            // 处理前置任务显示
            let predecessorText = '-';
            if (task.predecessor_dependencies && task.predecessor_dependencies.length > 0) {
                const predecessors = task.predecessor_dependencies.map(dep =>
                    `${dep.from_task_wbs || dep.from_task}(${dep.dependency_type})`
                ).join(', ');
                predecessorText = `<small class="text-muted">${predecessors}</small>`;
            }

            html += `
                <tr class="task-row" data-task-id="${task.id}" onclick="showTaskDetails(${task.id})">
                    <td class="text-center">${index + 1}</td>
                    <td>
                        <span class="badge bg-light text-dark">${task.wbs_code}</span>
                        ${task.is_critical ? '<i class="fas fa-exclamation-triangle text-warning ms-1" title="关键路径"></i>' : ''}
                        ${task.summary ? '<i class="fas fa-folder text-primary ms-1" title="摘要任务"></i>' : ''}
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            ${priorityIcon}
                            <div>
                                <div class="fw-semibold">${task.summary ? '📁 ' : ''}${task.name}</div>
                                <small class="text-muted">${task.description || '暂无描述'}</small>
                            </div>
                        </div>
                    </td>
                    <td class="text-center">${task.unit || '-'}</td>
                    <td class="text-end">${task.quantity || 0}</td>
                    <td class="text-end">${task.daily_efficiency || 0}</td>
                    <td class="text-center">${task.planned_duration || 0}天</td>
                    <td class="text-center">
                        <small>${formatDate(task.start_date)}</small>
                    </td>
                    <td class="text-center">
                        <small>${formatDate(task.end_date)}</small>
                    </td>
                    <td class="text-center">
                        ${predecessorText}
                    </td>
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="progress me-2" style="width: 50px; height: 6px;">
                                <div class="progress-bar bg-${statusColor}" style="width: ${progressPercent}%"></div>
                            </div>
                            <small class="fw-semibold">${progressPercent}%</small>
                        </div>
                        <span class="badge bg-${statusColor} badge-sm">${getStatusText(task.status)}</span>
                    </td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary btn-sm" onclick="event.stopPropagation(); editTask(${task.id})" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="event.stopPropagation(); deleteTask(${task.id})" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;

            // 如果有关联的清单项，显示成本信息
            if (totalCost > 0) {
                html += `
                    <tr class="table-light">
                        <td colspan="12">
                            <small class="text-muted">
                                <i class="fas fa-coins me-1"></i>
                                关联成本: ¥${totalCost.toLocaleString('zh-CN', {minimumFractionDigits: 2})}
                            </small>
                        </td>
                    </tr>
                `;
            }
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;
    } else {
        html = `
            <div class="text-center py-5">
                <i class="fas fa-tasks fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无任务数据</h5>
                <p class="text-muted">请先选择项目并加载甘特图，或者创建新任务</p>
                <button class="btn btn-primary" onclick="showAddTaskForm()">
                    <i class="fas fa-plus me-1"></i>创建第一个任务
                </button>
            </div>
        `;
    }

    taskList.innerHTML = html;
}

// 搜索任务
function searchTasks() {
    const searchTerm = document.getElementById('task-search').value.toLowerCase();
    const filteredTasks = currentTasks.filter(task =>
        task.name.toLowerCase().includes(searchTerm) ||
        task.wbs_code.toLowerCase().includes(searchTerm) ||
        (task.description && task.description.toLowerCase().includes(searchTerm))
    );
    renderTaskList(filteredTasks);
}

// 筛选任务
function filterTasks(filterType) {
    taskFilter = filterType;
    let filteredTasks = currentTasks;

    switch (filterType) {
        case 'not_started':
            filteredTasks = currentTasks.filter(task => task.status === 'not_started');
            break;
        case 'in_progress':
            filteredTasks = currentTasks.filter(task => task.status === 'in_progress');
            break;
        case 'completed':
            filteredTasks = currentTasks.filter(task => task.status === 'completed');
            break;
        case 'critical':
            filteredTasks = currentTasks.filter(task => task.is_critical);
            break;
        default:
            filteredTasks = currentTasks;
    }

    renderTaskList(filteredTasks);
    showNotification(`已筛选${getFilterName(filterType)}任务`, 'info');
}

function getFilterName(filterType) {
    const names = {
        'all': '全部',
        'not_started': '未开始',
        'in_progress': '进行中',
        'completed': '已完成',
        'critical': '关键路径'
    };
    return names[filterType] || filterType;
}

// 显示任务详情
function showTaskDetails(taskId) {
    // 高亮选中的任务
    document.querySelectorAll('.task-item').forEach(item => {
        item.classList.remove('active');
    });
    document.querySelector(`[data-task-id="${taskId}"]`)?.classList.add('active');

    fetch(`/api/tasks/${taskId}/`)
        .then(response => response.json())
        .then(task => {
            const details = document.getElementById('task-details');
            const progressPercent = Math.round(task.progress_percentage || 0);
            const statusColor = getStatusColor(task.status);
            const priorityIcon = getPriorityIcon(task.priority);

            details.innerHTML = `
                <div class="task-details-header-info mb-4">
                    <div class="d-flex align-items-center mb-2">
                        <span class="badge bg-light text-dark me-2">${task.wbs_code}</span>
                        ${priorityIcon}
                    </div>
                    <h5 class="fw-bold mb-2">${task.name}</h5>
                    <p class="text-muted">${task.description || '暂无描述'}</p>
                </div>

                <div class="detail-item">
                    <span class="detail-label">任务状态</span>
                    <span class="badge bg-${statusColor}">${getStatusText(task.status)}</span>
                </div>

                <div class="detail-item">
                    <span class="detail-label">完成进度</span>
                    <div class="d-flex align-items-center">
                        <div class="progress me-2" style="width: 100px; height: 8px;">
                            <div class="progress-bar bg-${statusColor}" style="width: ${progressPercent}%"></div>
                        </div>
                        <span class="fw-bold">${progressPercent}%</span>
                    </div>
                </div>

                <div class="detail-item">
                    <span class="detail-label">开始时间</span>
                    <span class="detail-value">${formatDateTime(task.start_date)}</span>
                </div>

                <div class="detail-item">
                    <span class="detail-label">结束时间</span>
                    <span class="detail-value">${formatDateTime(task.end_date)}</span>
                </div>

                <div class="detail-item">
                    <span class="detail-label">计划工期</span>
                    <span class="detail-value">${task.planned_duration || 0} 天</span>
                </div>

                <div class="detail-item">
                    <span class="detail-label">工程数量</span>
                    <span class="detail-value">${task.quantity || 0} ${task.unit || ''}</span>
                </div>

                <div class="detail-item">
                    <span class="detail-label">每日工效</span>
                    <span class="detail-value">${task.daily_efficiency || 0}</span>
                </div>

                ${task.is_critical ? '<div class="alert alert-warning mt-3"><i class="fas fa-exclamation-triangle me-2"></i>关键路径任务</div>' : ''}

                <div class="mt-4 d-grid gap-2">
                    <button class="btn btn-primary" onclick="editTask(${task.id})">
                        <i class="fas fa-edit me-2"></i>编辑任务
                    </button>
                    <button class="btn btn-outline-success" onclick="updateTaskProgress(${task.id})">
                        <i class="fas fa-chart-line me-2"></i>更新进度
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteTask(${task.id})">
                        <i class="fas fa-trash me-2"></i>删除任务
                    </button>
                </div>
            `;
        })
        .catch(error => {
            console.error('Error loading task details:', error);
            showNotification('加载任务详情失败', 'error');
        });
}

// 显示添加任务表单
function showAddTaskForm() {
    if (!currentProjectId) {
        showNotification('请先选择一个项目', 'warning');
        return;
    }

    const form = document.getElementById('add-task-form');
    form.style.display = 'block';
    form.scrollIntoView({ behavior: 'smooth' });

    // 设置默认值
    const now = new Date();
    document.getElementById('task-start').value = now.toISOString().slice(0, 16);

    // 自动生成WBS编码
    generateNextWBSCode();

    // 加载前置任务选项
    loadPredecessorOptions();

    // 加载清单项选项
    loadListItemOptions();
}

// 隐藏添加任务表单
function hideAddTaskForm() {
    document.getElementById('add-task-form').style.display = 'none';
    document.getElementById('task-form').reset();
}

// 生成下一个WBS编码
function generateNextWBSCode() {
    if (currentTasks.length === 0) {
        document.getElementById('task-wbs').value = '1.1';
        return;
    }

    // 简单的WBS编码生成逻辑
    const maxWBS = currentTasks.reduce((max, task) => {
        const wbsParts = task.wbs_code.split('.');
        const lastPart = parseInt(wbsParts[wbsParts.length - 1]) || 0;
        return Math.max(max, lastPart);
    }, 0);

    document.getElementById('task-wbs').value = `1.${maxWBS + 1}`;
}

// 计算任务工期
function calculateDuration() {
    const quantity = parseFloat(document.getElementById('task-quantity').value) || 0;
    const efficiency = parseFloat(document.getElementById('task-efficiency').value) || 1;

    if (quantity > 0 && efficiency > 0) {
        const duration = Math.ceil(quantity / efficiency);
        document.getElementById('task-duration').value = duration;
        calculateEndDate();
    }
}

// 计算结束时间（排除非工作日）
function calculateEndDate() {
    const startDate = document.getElementById('task-start').value;
    const duration = parseInt(document.getElementById('task-duration').value) || 0;

    if (startDate && duration > 0) {
        const start = new Date(startDate);
        let endDate = new Date(start);
        let workDaysAdded = 0;

        // 添加工作日，跳过周末
        while (workDaysAdded < duration) {
            endDate.setDate(endDate.getDate() + 1);
            const dayOfWeek = endDate.getDay();

            // 0 = 周日, 6 = 周六，跳过周末
            if (dayOfWeek !== 0 && dayOfWeek !== 6) {
                workDaysAdded++;
            }
        }

        // 设置结束时间为当天的18:00
        endDate.setHours(18, 0, 0, 0);

        document.getElementById('task-end').value = endDate.toISOString().slice(0, 16);
    }
}

// 切换依赖关系设置显示
function toggleDependencySettings() {
    const predecessors = document.getElementById('task-predecessors');
    const dependencySettings = document.getElementById('dependency-settings');

    if (predecessors && predecessors.selectedOptions.length > 0) {
        if (dependencySettings) {
            dependencySettings.style.display = 'block';
        }
    } else {
        if (dependencySettings) {
            dependencySettings.style.display = 'none';
        }
    }
}

// 加载前置任务选项
function loadPredecessorOptions() {
    if (!currentProjectId) return;

    const select = document.getElementById('task-predecessors');
    if (!select) return;

    // 清空现有选项
    select.innerHTML = '<option value="">无前置任务</option>';

    // 加载当前项目的所有任务
    fetch(`/api/tasks/?project_id=${currentProjectId}`)
        .then(response => response.json())
        .then(data => {
            const tasks = data.results || [];
            tasks.forEach(task => {
                const option = document.createElement('option');
                option.value = task.id;
                option.textContent = `${task.wbs_code} - ${task.name}`;
                select.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Error loading predecessor options:', error);
        });
}

// 加载清单项选项
function loadListItemOptions() {
    if (!currentProjectId) return;

    const select = document.getElementById('task-list-items');
    if (!select) return;

    // 清空现有选项
    select.innerHTML = '';

    // 加载当前项目的清单项
    fetch(`/api/list-items/?project_id=${currentProjectId}`)
        .then(response => response.json())
        .then(data => {
            const listItems = data.results || [];
            listItems.forEach(item => {
                const option = document.createElement('option');
                option.value = item.id;
                option.textContent = `${item.code} - ${item.description} (¥${item.comprehensive_total_price || 0})`;
                option.dataset.cost = item.comprehensive_total_price || 0;
                option.dataset.unit = item.unit;
                option.dataset.quantity = item.quantity;
                select.appendChild(option);
            });
        })
        .catch(error => {
            console.error('Error loading list item options:', error);
        });
}

// 更新选中的清单项显示
function updateSelectedListItems() {
    const select = document.getElementById('task-list-items');
    const container = document.getElementById('selected-list-items');

    if (!select || !container) return;

    const selectedOptions = Array.from(select.selectedOptions);
    let totalCost = 0;
    let html = '';

    if (selectedOptions.length > 0) {
        html = '<div class="mt-2"><h6 class="fw-semibold">已选择的清单项：</h6>';
        selectedOptions.forEach(option => {
            const cost = parseFloat(option.dataset.cost) || 0;
            totalCost += cost;
            html += `
                <div class="badge bg-light text-dark me-2 mb-2">
                    ${option.textContent}
                </div>
            `;
        });
        html += `<div class="mt-2"><strong>总成本：¥${totalCost.toLocaleString('zh-CN', {minimumFractionDigits: 2})}</strong></div></div>`;
    }

    container.innerHTML = html;
}

// 保存草稿
function saveAsDraft() {
    showNotification('草稿保存功能开发中...', 'info');
}

// 快速开始
function showQuickStart() {
    showNotification('正在启动快速向导...', 'info');
    showTemplateSelector();
}

// 更新任务进度
function updateTaskProgress(taskId) {
    const newProgress = prompt('请输入新的完成进度 (0-100):');
    if (newProgress !== null && !isNaN(newProgress)) {
        const progress = Math.max(0, Math.min(100, parseInt(newProgress)));

        fetch(`/api/tasks/${taskId}/`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify({
                progress_percentage: progress
            })
        })
        .then(response => response.json())
        .then(task => {
            showNotification('进度更新成功', 'success');
            loadTaskList(currentProjectId);
            showTaskDetails(taskId);
        })
        .catch(error => {
            console.error('Error updating progress:', error);
            showNotification('进度更新失败', 'error');
        });
    }
}

// 导入项目
function importProject() {
    showNotification('项目导入功能开发中...', 'info');
}

// 显示导入对话框
function showImportDialog() {
    showNotification('文件导入功能开发中...', 'info');
}

// 切换时间刻度
function toggleTimeScale() {
    showNotification('时间刻度切换功能开发中...', 'info');
}

// 创建模板任务
function createTemplateTask(templateType) {
    const templates = {
        'construction': [
            { wbs: '1.1', name: '项目启动', description: '项目启动和准备工作' },
            { wbs: '1.2', name: '设计阶段', description: '方案设计和图纸绘制' },
            { wbs: '1.3', name: '施工准备', description: '材料采购和现场准备' },
            { wbs: '1.4', name: '主体施工', description: '主体结构施工' },
            { wbs: '1.5', name: '装修工程', description: '室内外装修' },
            { wbs: '1.6', name: '验收交付', description: '工程验收和交付' }
        ],
        'software': [
            { wbs: '1.1', name: '需求分析', description: '需求收集和分析' },
            { wbs: '1.2', name: '系统设计', description: '架构和详细设计' },
            { wbs: '1.3', name: '编码开发', description: '功能开发和实现' },
            { wbs: '1.4', name: '测试阶段', description: '单元测试和集成测试' },
            { wbs: '1.5', name: '部署上线', description: '系统部署和上线' },
            { wbs: '1.6', name: '维护支持', description: '运维和技术支持' }
        ]
    };

    const templateTasks = templates[templateType];
    if (templateTasks) {
        // 这里可以批量创建任务
        showNotification(`${getTemplateName(templateType)}模板任务创建功能开发中...`, 'info');
    }
}

// 创建任务
function createTask() {
    const formData = {
        project: currentProjectId,
        wbs_code: document.getElementById('task-wbs').value,
        name: document.getElementById('task-name').value,
        description: document.getElementById('task-description').value,
        unit: document.getElementById('task-unit').value,
        quantity: parseFloat(document.getElementById('task-quantity').value) || 1,
        daily_efficiency: parseFloat(document.getElementById('task-efficiency').value) || 1,
        start_date: document.getElementById('task-start').value,
        end_date: document.getElementById('task-end').value,
        status: document.getElementById('task-status')?.value || 'not_started',
        priority: document.getElementById('task-priority').value,
        order: 0
    };

    // 获取选中的清单项
    const listItemSelect = document.getElementById('task-list-items');
    const selectedListItems = Array.from(listItemSelect.selectedOptions).map(option => option.value);

    if (selectedListItems.length > 0) {
        formData.list_items = selectedListItems;
    }
    
    fetch('/api/tasks/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.id) {
            alert('任务创建成功');
            hideAddTaskForm();
            loadGanttChart(); // 重新加载甘特图
        } else {
            alert('任务创建失败: ' + JSON.stringify(data));
        }
    })
    .catch(error => {
        console.error('Error creating task:', error);
        alert('任务创建失败');
    });
}

// 辅助函数
function getStatusColor(status) {
    const colors = {
        'not_started': 'secondary',
        'in_progress': 'primary',
        'completed': 'success',
        'paused': 'warning',
        'cancelled': 'danger'
    };
    return colors[status] || 'secondary';
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

function zoomToFit() {
    if (ganttChart) {
        ganttChart.xAxis[0].setExtremes();
    }
}

function exportGantt() {
    if (ganttChart) {
        ganttChart.exportChart({
            type: 'image/png',
            filename: 'gantt-chart'
        });
    }
}

function editTask(taskId) {
    if (!taskId) {
        showNotification('任务ID无效', 'error');
        return;
    }

    // 获取任务详情
    fetch(`/api/tasks/${taskId}/`)
        .then(response => response.json())
        .then(task => {
            showEditTaskForm(task);
        })
        .catch(error => {
            console.error('Error loading task for edit:', error);
            showNotification('加载任务信息失败', 'error');
        });
}

// 显示编辑任务表单
function showEditTaskForm(task) {
    // 复制新增任务表单的HTML结构
    const editFormHtml = `
        <div class="task-form" id="edit-task-form" style="display: block;">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-gradient-warning text-white border-bottom-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0 fw-bold">
                            <i class="fas fa-edit me-2"></i>
                            编辑任务
                        </h4>
                        <button type="button" class="btn-close btn-close-white" onclick="hideEditTaskForm()"></button>
                    </div>
                    <p class="mb-0 mt-2 opacity-75">修改任务详细信息，系统将自动重新计算工期和依赖关系</p>
                </div>
                <div class="card-body p-4">
                    <form id="edit-task-form-inner">
                        <input type="hidden" id="edit-task-id" value="${task.id}">
                        <div class="row g-4">
                            <div class="col-lg-6">
                                <div class="mb-3">
                                    <label for="edit-task-wbs" class="form-label fw-semibold">
                                        <i class="fas fa-code me-2 text-primary"></i>
                                        WBS编码 *
                                    </label>
                                    <input type="text" class="form-control form-control-lg" id="edit-task-wbs" required value="${task.wbs_code || ''}">
                                    <div class="form-text">工作分解结构编码，用于任务层级管理</div>
                                </div>
                                <div class="mb-3">
                                    <label for="edit-task-name" class="form-label fw-semibold">
                                        <i class="fas fa-tag me-2 text-primary"></i>
                                        任务名称 *
                                    </label>
                                    <input type="text" class="form-control form-control-lg" id="edit-task-name" required value="${task.name || ''}">
                                </div>
                                <div class="mb-3">
                                    <label for="edit-task-priority" class="form-label fw-semibold">
                                        <i class="fas fa-flag me-2 text-primary"></i>
                                        优先级
                                    </label>
                                    <select class="form-select form-select-lg" id="edit-task-priority">
                                        <option value="low" ${task.priority === 'low' ? 'selected' : ''}>低优先级</option>
                                        <option value="normal" ${task.priority === 'normal' ? 'selected' : ''}>普通优先级</option>
                                        <option value="high" ${task.priority === 'high' ? 'selected' : ''}>高优先级</option>
                                        <option value="urgent" ${task.priority === 'urgent' ? 'selected' : ''}>紧急</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="edit-task-unit" class="form-label fw-semibold">
                                        <i class="fas fa-ruler me-2 text-primary"></i>
                                        计量单位
                                    </label>
                                    <input type="text" class="form-control" id="edit-task-unit" value="${task.unit || ''}" placeholder="例如: 立方米、平方米、个">
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <div class="mb-3">
                                    <label for="edit-task-quantity" class="form-label fw-semibold">
                                        <i class="fas fa-calculator me-2 text-primary"></i>
                                        工程数量
                                    </label>
                                    <input type="number" class="form-control" id="edit-task-quantity" step="0.01" value="${task.quantity || 0}" onchange="calculateEditDuration()">
                                </div>
                                <div class="mb-3">
                                    <label for="edit-task-efficiency" class="form-label fw-semibold">
                                        <i class="fas fa-tachometer-alt me-2 text-primary"></i>
                                        每日工效
                                    </label>
                                    <input type="number" class="form-control" id="edit-task-efficiency" step="0.01" value="${task.daily_efficiency || 0}" onchange="calculateEditDuration()">
                                    <div class="form-text">每日计划完成的工程量</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label fw-semibold">
                                        <i class="fas fa-clock me-2 text-primary"></i>
                                        计划工期
                                    </label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="edit-task-duration" value="${task.planned_duration || 0}" readonly>
                                        <span class="input-group-text">天</span>
                                    </div>
                                    <div class="form-text">自动计算：工程数量 ÷ 每日工效</div>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <label for="edit-task-start" class="form-label fw-semibold">
                                            <i class="fas fa-calendar-alt me-2 text-primary"></i>
                                            开始时间 *
                                        </label>
                                        <input type="datetime-local" class="form-control" id="edit-task-start" required value="${formatDateTimeForInput(task.start_date)}" onchange="calculateEditEndDate()">
                                    </div>
                                    <div class="col-6">
                                        <label for="edit-task-end" class="form-label fw-semibold">
                                            <i class="fas fa-calendar-check me-2 text-primary"></i>
                                            计划完成时间
                                        </label>
                                        <input type="datetime-local" class="form-control" id="edit-task-end" value="${formatDateTimeForInput(task.end_date)}" readonly>
                                        <div class="form-text">自动计算（排除非工作日）</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="mb-4">
                                    <label for="edit-task-description" class="form-label fw-semibold">
                                        <i class="fas fa-align-left me-2 text-primary"></i>
                                        任务描述
                                    </label>
                                    <textarea class="form-control" id="edit-task-description" rows="4">${task.description || ''}</textarea>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex gap-3 justify-content-end">
                                    <button type="button" class="btn btn-outline-secondary btn-lg" onclick="hideEditTaskForm()">
                                        <i class="fas fa-times me-2"></i>取消
                                    </button>
                                    <button type="submit" class="btn btn-warning btn-lg">
                                        <i class="fas fa-save me-2"></i>保存修改
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    // 移除现有的编辑表单
    const existingForm = document.getElementById('edit-task-form');
    if (existingForm) {
        existingForm.remove();
    }

    // 添加编辑表单到页面
    document.body.insertAdjacentHTML('beforeend', editFormHtml);

    // 绑定表单提交事件
    document.getElementById('edit-task-form-inner').addEventListener('submit', function(e) {
        e.preventDefault();
        updateTask();
    });

    // 滚动到表单位置
    document.getElementById('edit-task-form').scrollIntoView({ behavior: 'smooth' });
}

// 隐藏编辑任务表单
function hideEditTaskForm() {
    const form = document.getElementById('edit-task-form');
    if (form) {
        form.remove();
    }
}

// 计算编辑表单的工期
function calculateEditDuration() {
    const quantity = parseFloat(document.getElementById('edit-task-quantity').value) || 0;
    const efficiency = parseFloat(document.getElementById('edit-task-efficiency').value) || 1;

    if (quantity > 0 && efficiency > 0) {
        const duration = Math.ceil(quantity / efficiency);
        document.getElementById('edit-task-duration').value = duration;
        calculateEditEndDate();
    }
}

// 计算编辑表单的结束时间
function calculateEditEndDate() {
    const startDate = document.getElementById('edit-task-start').value;
    const duration = parseInt(document.getElementById('edit-task-duration').value) || 0;

    if (startDate && duration > 0) {
        const start = new Date(startDate);
        let endDate = new Date(start);
        let workDaysAdded = 0;

        // 添加工作日，跳过周末
        while (workDaysAdded < duration) {
            endDate.setDate(endDate.getDate() + 1);
            const dayOfWeek = endDate.getDay();

            // 0 = 周日, 6 = 周六，跳过周末
            if (dayOfWeek !== 0 && dayOfWeek !== 6) {
                workDaysAdded++;
            }
        }

        // 设置结束时间为当天的18:00
        endDate.setHours(18, 0, 0, 0);

        document.getElementById('edit-task-end').value = endDate.toISOString().slice(0, 16);
    }
}

// 格式化日期时间为输入框格式
function formatDateTimeForInput(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toISOString().slice(0, 16);
}

// 更新任务
function updateTask() {
    const taskId = document.getElementById('edit-task-id').value;
    const taskData = {
        wbs_code: document.getElementById('edit-task-wbs').value,
        name: document.getElementById('edit-task-name').value,
        description: document.getElementById('edit-task-description').value,
        unit: document.getElementById('edit-task-unit').value,
        quantity: parseFloat(document.getElementById('edit-task-quantity').value) || 0,
        daily_efficiency: parseFloat(document.getElementById('edit-task-efficiency').value) || 0,
        start_date: document.getElementById('edit-task-start').value,
        end_date: document.getElementById('edit-task-end').value,
        priority: document.getElementById('edit-task-priority').value,
        project: currentProjectId
    };

    fetch(`/api/tasks/${taskId}/`, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify(taskData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.id) {
            showNotification('任务更新成功', 'success');
            hideEditTaskForm();
            // 重新加载甘特图和任务列表
            loadGanttChart();
            loadTaskList(currentProjectId);
        } else {
            throw new Error(data.error || '更新失败');
        }
    })
    .catch(error => {
        console.error('Error updating task:', error);
        showNotification('更新任务失败: ' + error.message, 'error');
    });
}

// 编辑甘特图标题
function editGanttTitle() {
    const titleElement = document.getElementById('gantt-title');
    const currentTitle = titleElement.textContent;

    const newTitle = prompt('请输入新的甘特图标题:', currentTitle);
    if (newTitle && newTitle.trim() !== '' && newTitle !== currentTitle) {
        titleElement.textContent = newTitle.trim();

        // 如果甘特图已加载，更新甘特图标题
        if (ganttChart) {
            ganttChart.setTitle({ text: newTitle.trim() });
        }

        // 保存到本地存储
        if (currentProjectId) {
            localStorage.setItem(`gantt_title_${currentProjectId}`, newTitle.trim());
        }

        showNotification('甘特图标题已更新', 'success');
    }
}

// 加载保存的甘特图标题
function loadSavedGanttTitle() {
    if (currentProjectId) {
        const savedTitle = localStorage.getItem(`gantt_title_${currentProjectId}`);
        if (savedTitle) {
            document.getElementById('gantt-title').textContent = savedTitle;
        }
    }
}

function deleteTask(taskId) {
    if (confirm('确定要删除这个任务吗？此操作不可撤销。')) {
        fetch(`/api/tasks/${taskId}/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => {
            if (response.ok) {
                showNotification('任务删除成功', 'success');
                loadGanttChart(); // 重新加载甘特图
                // 清空任务详情
                document.getElementById('task-details').innerHTML = `
                    <div class="text-center py-5">
                        <i class="fas fa-mouse-pointer fa-3x text-muted mb-3"></i>
                        <h6 class="text-muted">选择任务查看详情</h6>
                        <p class="text-muted small">点击左侧任务列表中的任务项目</p>
                    </div>
                `;
            } else {
                showNotification('任务删除失败', 'error');
            }
        })
        .catch(error => {
            console.error('Error deleting task:', error);
            showNotification('任务删除失败', 'error');
        });
    }
}

// 更多辅助函数
function getStatusText(status) {
    const texts = {
        'not_started': '未开始',
        'in_progress': '进行中',
        'completed': '已完成',
        'paused': '暂停',
        'cancelled': '已取消'
    };
    return texts[status] || status;
}

function getPriorityIcon(priority) {
    const icons = {
        'low': '<i class="fas fa-flag text-secondary me-2" title="低优先级"></i>',
        'normal': '<i class="fas fa-flag text-primary me-2" title="普通优先级"></i>',
        'high': '<i class="fas fa-flag text-warning me-2" title="高优先级"></i>',
        'urgent': '<i class="fas fa-flag text-danger me-2" title="紧急"></i>'
    };
    return icons[priority] || icons['normal'];
}

function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('zh-CN');
}

function formatDateTime(dateString) {
    return new Date(dateString).toLocaleString('zh-CN');
}

// 通知系统
function showNotification(message, type = 'info') {
    const alertClass = {
        'success': 'alert-success',
        'error': 'alert-danger',
        'warning': 'alert-warning',
        'info': 'alert-info'
    }[type] || 'alert-info';

    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // 自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

// 备用甘特图渲染函数
function renderSimpleGanttChart(data) {
    console.log('Using simple gantt chart fallback');

    // 等待SimpleGantt加载
    if (typeof SimpleGantt === 'undefined') {
        setTimeout(() => renderSimpleGanttChart(data), 500);
        return;
    }

    try {
        // 转换数据格式
        const ganttData = data.tasks ? data.tasks.map(task => ({
            id: task.id,
            name: task.name || '未命名任务',
            start: task.start,
            end: task.end,
            completed: task.progress || 0,
            color: task.critical ? '#ef4444' : (task.status === 'completed' ? '#10b981' : '#3b82f6')
        })) : [];

        // 创建简单甘特图
        const simpleGantt = new SimpleGantt('gantt-container', {
            title: '项目甘特图 (备用版本)',
            data: ganttData,
            height: 500
        });

        // 保存引用以便导出
        window.currentGanttChart = simpleGantt;

        showNotification('备用甘特图渲染成功', 'success');

    } catch (error) {
        console.error('Error rendering simple gantt chart:', error);
        showNotification('备用甘特图渲染失败: ' + error.message, 'error');
        renderEmptyGanttChart();
    }
}

// 修改导出函数以支持备用甘特图
function exportGantt() {
    if (window.currentGanttChart) {
        if (typeof window.currentGanttChart.exportChart === 'function') {
            window.currentGanttChart.exportChart();
        } else {
            showNotification('备用甘特图导出功能开发中...', 'info');
        }
    } else if (ganttChart) {
        ganttChart.exportChart({
            type: 'image/png',
            filename: 'gantt-chart'
        });
    } else {
        showNotification('请先加载甘特图', 'warning');
    }
}

// 添加CSS样式
const style = document.createElement('style');
style.textContent = `
    /* 甘特图工具提示样式 */
    .gantt-tooltip {
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 12px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        font-size: 13px;
        line-height: 1.4;
        max-width: 300px;
    }

    .gantt-tooltip h6 {
        margin: 0 0 8px 0;
        color: #333;
        border-bottom: 1px solid #eee;
        padding-bottom: 4px;
    }

    .gantt-tooltip div {
        margin: 4px 0;
        color: #666;
    }

    /* 任务行样式 */
    .task-row {
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .task-row:hover {
        background-color: #f8f9fa !important;
    }

    .task-row.active {
        background-color: #e3f2fd !important;
    }

    /* 进度条样式 */
    .progress {
        background-color: #e9ecef;
        border-radius: 3px;
    }

    .badge-sm {
        font-size: 0.7em;
    }

    /* 表格响应式 */
    .table-responsive {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .table th {
        background-color: #f8f9fa;
        border-top: none;
        font-weight: 600;
        color: #495057;
        font-size: 0.9em;
    }

    .table td {
        vertical-align: middle;
        font-size: 0.9em;
    }
    .task-item.active {
        background: linear-gradient(135deg, #e0f2fe 0%, #f0f9ff 100%);
        border-left: 4px solid var(--primary-color);
        transform: translateX(8px);
    }

    .bg-gradient-primary {
        background: linear-gradient(135deg, var(--primary-color) 0%, #1d4ed8 100%);
    }

    .loading-pulse {
        animation: pulse 1.5s ease-in-out infinite;
    }

    @keyframes pulse {
        0% { opacity: 1; }
        50% { opacity: 0.5; }
        100% { opacity: 1; }
    }

    /* 简单甘特图样式 */
    .simple-gantt {
        font-family: 'Inter', sans-serif;
    }

    .simple-gantt .task-bar {
        transition: all 0.3s ease;
    }

    .simple-gantt .task-bar:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
