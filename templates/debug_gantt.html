<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>甘特图调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        .status-card {
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #ddd;
        }
        .status-success { border-left-color: #28a745; background-color: #d4edda; }
        .status-error { border-left-color: #dc3545; background-color: #f8d7da; }
        .status-warning { border-left-color: #ffc107; background-color: #fff3cd; }
        .status-info { border-left-color: #17a2b8; background-color: #d1ecf1; }
        
        .gantt-container {
            height: 500px;
            border: 2px solid #ddd;
            border-radius: 6px;
            margin: 20px 0;
        }
        
        .log-container {
            background: #1a1a1a;
            color: #00ff00;
            padding: 15px;
            border-radius: 6px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin-top: 20px;
        }
        
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
    </style>
</head>
<body>
    <div class="container">
        <h1>甘特图组件调试页面</h1>
        
        <div class="status-grid">
            <div id="jquery-status" class="status-card">
                <h4>jQuery</h4>
                <p id="jquery-info">检查中...</p>
            </div>
            <div id="highcharts-status" class="status-card">
                <h4>Highcharts</h4>
                <p id="highcharts-info">检查中...</p>
            </div>
            <div id="gantt-status" class="status-card">
                <h4>Highcharts Gantt</h4>
                <p id="gantt-info">检查中...</p>
            </div>
            <div id="advanced-status" class="status-card">
                <h4>AdvancedGantt</h4>
                <p id="advanced-info">检查中...</p>
            </div>
        </div>
        
        <div>
            <button class="btn" onclick="testBasicGantt()">测试基础甘特图</button>
            <button class="btn btn-success" onclick="testAdvancedGantt()">测试高级甘特图</button>
            <button class="btn" onclick="clearContainer()">清空容器</button>
        </div>
        
        <div id="gantt-container" class="gantt-container"></div>
        
        <div id="log-container" class="log-container"></div>
    </div>

    <!-- 加载脚本 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="/static/js/highcharts/highcharts.js"></script>
    <script src="/static/js/highcharts/gantt.js"></script>
    <script src="/static/js/highcharts/exporting.js"></script>
    <script src="/static/js/highcharts/draggable-points.js"></script>
    <script src="/static/js/highcharts/accessibility.js"></script>
    <script src="/static/js/advanced-gantt.js"></script>

    <script>
        // 日志函数
        function log(message, type = 'info') {
            const logContainer = document.getElementById('log-container');
            const timestamp = new Date().toLocaleTimeString();
            const colors = {
                info: '#00ff00',
                success: '#00ff88',
                warning: '#ffff00',
                error: '#ff4444'
            };
            
            const logEntry = document.createElement('div');
            logEntry.style.color = colors[type] || '#00ff00';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // 同时输出到控制台
            console.log(`[${timestamp}] ${message}`);
        }

        // 更新状态卡片
        function updateStatus(cardId, infoId, status, message) {
            const card = document.getElementById(cardId);
            const info = document.getElementById(infoId);
            
            card.className = `status-card status-${status}`;
            info.textContent = message;
        }

        // 检查组件状态
        function checkComponents() {
            log('开始检查组件状态...', 'info');
            
            // 检查 jQuery
            if (typeof $ !== 'undefined') {
                updateStatus('jquery-status', 'jquery-info', 'success', `已加载 (版本 ${$.fn.jquery})`);
                log('✅ jQuery 已加载', 'success');
            } else {
                updateStatus('jquery-status', 'jquery-info', 'error', '未加载');
                log('❌ jQuery 未加载', 'error');
            }
            
            // 检查 Highcharts
            if (typeof Highcharts !== 'undefined') {
                updateStatus('highcharts-status', 'highcharts-info', 'success', `已加载 (版本 ${Highcharts.version})`);
                log('✅ Highcharts 已加载', 'success');
                
                // 检查 Gantt 模块
                if (typeof Highcharts.ganttChart === 'function') {
                    updateStatus('gantt-status', 'gantt-info', 'success', '已加载');
                    log('✅ Highcharts Gantt 模块已加载', 'success');
                } else {
                    updateStatus('gantt-status', 'gantt-info', 'error', '未加载');
                    log('❌ Highcharts Gantt 模块未加载', 'error');
                }
            } else {
                updateStatus('highcharts-status', 'highcharts-info', 'error', '未加载');
                updateStatus('gantt-status', 'gantt-info', 'error', '依赖未满足');
                log('❌ Highcharts 未加载', 'error');
            }
            
            // 检查 AdvancedGantt
            if (typeof AdvancedGantt !== 'undefined') {
                updateStatus('advanced-status', 'advanced-info', 'success', '已加载');
                log('✅ AdvancedGantt 组件已加载', 'success');
            } else {
                updateStatus('advanced-status', 'advanced-info', 'error', '未加载');
                log('❌ AdvancedGantt 组件未加载', 'error');
            }
        }

        // 测试基础甘特图
        function testBasicGantt() {
            log('开始测试基础甘特图...', 'info');
            
            if (typeof Highcharts === 'undefined' || typeof Highcharts.ganttChart !== 'function') {
                log('❌ Highcharts Gantt 不可用，无法测试', 'error');
                return;
            }
            
            try {
                clearContainer();
                
                const today = Date.now();
                const day = 24 * 60 * 60 * 1000;
                
                const chart = Highcharts.ganttChart('gantt-container', {
                    title: {
                        text: '基础甘特图测试'
                    },
                    series: [{
                        name: '测试项目',
                        data: [{
                            name: '任务1',
                            start: today,
                            end: today + 3 * day,
                            y: 0
                        }, {
                            name: '任务2',
                            start: today + 1 * day,
                            end: today + 4 * day,
                            y: 1
                        }]
                    }]
                });
                
                log('✅ 基础甘特图创建成功', 'success');
                
            } catch (error) {
                log(`❌ 测试基础甘特图时发生错误: ${error.message}`, 'error');
            }
        }

        // 测试高级甘特图
        function testAdvancedGantt() {
            log('开始测试高级甘特图...', 'info');
            
            if (typeof AdvancedGantt === 'undefined') {
                log('❌ AdvancedGantt 不可用，无法测试', 'error');
                return;
            }
            
            try {
                clearContainer();
                
                const today = Date.now();
                const day = 24 * 60 * 60 * 1000;
                
                const testData = [{
                    name: '测试项目',
                    data: [{
                        name: '项目启动',
                        id: 'start',
                        start: today,
                        end: today + 2 * day,
                        completed: { amount: 1 },
                        owner: '项目经理'
                    }, {
                        name: '需求分析',
                        id: 'analysis',
                        dependency: 'start',
                        start: today + 2 * day,
                        end: today + 5 * day,
                        completed: { amount: 0.8 },
                        owner: '业务分析师'
                    }]
                }];
                
                const gantt = new AdvancedGantt('gantt-container', {
                    title: '高级甘特图测试',
                    subtitle: '这是一个测试甘特图',
                    data: testData,
                    height: 500,
                    onChartReady: function(chart) {
                        log('✅ 高级甘特图创建成功', 'success');
                    },
                    onError: function(error) {
                        log(`❌ 高级甘特图创建失败: ${error.message}`, 'error');
                    }
                });
                
            } catch (error) {
                log(`❌ 测试高级甘特图时发生错误: ${error.message}`, 'error');
            }
        }

        // 清空容器
        function clearContainer() {
            document.getElementById('gantt-container').innerHTML = '';
            log('容器已清空', 'info');
        }

        // 页面加载完成后执行
        window.addEventListener('load', function() {
            log('页面加载完成，开始检查组件...', 'info');
            setTimeout(checkComponents, 100);
        });
    </script>
</body>
</html>
