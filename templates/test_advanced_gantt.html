{% extends 'base.html' %}
{% load static %}

{% block title %}高级甘特图测试{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-3xl font-bold text-gray-800 mb-6">高级甘特图测试页面</h1>
        
        <!-- 状态检查 -->
        <div class="mb-6 p-4 bg-gray-50 rounded-lg">
            <h2 class="text-xl font-semibold mb-4">组件状态检查</h2>
            <div id="status-checks" class="space-y-2">
                <div id="jquery-status" class="flex items-center">
                    <span class="w-4 h-4 mr-2 rounded-full bg-gray-300"></span>
                    <span>jQuery: 检查中...</span>
                </div>
                <div id="highcharts-status" class="flex items-center">
                    <span class="w-4 h-4 mr-2 rounded-full bg-gray-300"></span>
                    <span>Highcharts: 检查中...</span>
                </div>
                <div id="gantt-status" class="flex items-center">
                    <span class="w-4 h-4 mr-2 rounded-full bg-gray-300"></span>
                    <span>Highcharts Gantt: 检查中...</span>
                </div>
                <div id="advanced-gantt-status" class="flex items-center">
                    <span class="w-4 h-4 mr-2 rounded-full bg-gray-300"></span>
                    <span>AdvancedGantt: 检查中...</span>
                </div>
            </div>
        </div>

        <!-- 测试按钮 -->
        <div class="mb-6">
            <button id="test-gantt-btn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg mr-4">
                测试高级甘特图
            </button>
            <button id="test-basic-btn" class="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg">
                测试基础甘特图
            </button>
        </div>

        <!-- 甘特图容器 -->
        <div class="mb-6">
            <h2 class="text-xl font-semibold mb-4">甘特图显示区域</h2>
            <div id="test-gantt-container" style="height: 400px;" class="border border-gray-300 rounded-lg"></div>
        </div>

        <!-- 日志输出 -->
        <div class="mb-6">
            <h2 class="text-xl font-semibold mb-4">测试日志</h2>
            <div id="test-log" class="bg-black text-green-400 p-4 rounded-lg h-64 overflow-y-auto font-mono text-sm"></div>
        </div>
    </div>
</div>

<script>
// 日志函数
function log(message, type = 'info') {
    const logDiv = document.getElementById('test-log');
    const timestamp = new Date().toLocaleTimeString();
    const colors = {
        info: 'text-green-400',
        success: 'text-green-300',
        warning: 'text-yellow-400',
        error: 'text-red-400'
    };
    
    const logEntry = document.createElement('div');
    logEntry.className = colors[type] || 'text-green-400';
    logEntry.textContent = `[${timestamp}] ${message}`;
    logDiv.appendChild(logEntry);
    logDiv.scrollTop = logDiv.scrollHeight;
}

// 更新状态指示器
function updateStatus(id, status, message) {
    const element = document.getElementById(id);
    const indicator = element.querySelector('.w-4');
    const text = element.querySelector('span:last-child');
    
    const colors = {
        success: 'bg-green-500',
        error: 'bg-red-500',
        warning: 'bg-yellow-500',
        loading: 'bg-blue-500'
    };
    
    indicator.className = `w-4 h-4 mr-2 rounded-full ${colors[status] || 'bg-gray-300'}`;
    text.textContent = message;
}

// 检查组件状态
function checkComponents() {
    log('开始检查组件状态...', 'info');
    
    // 检查 jQuery
    if (typeof $ !== 'undefined') {
        updateStatus('jquery-status', 'success', `jQuery: 已加载 (版本 ${$.fn.jquery})`);
        log('✅ jQuery 已加载', 'success');
    } else {
        updateStatus('jquery-status', 'error', 'jQuery: 未加载');
        log('❌ jQuery 未加载', 'error');
    }
    
    // 检查 Highcharts
    if (typeof Highcharts !== 'undefined') {
        updateStatus('highcharts-status', 'success', `Highcharts: 已加载 (版本 ${Highcharts.version})`);
        log('✅ Highcharts 已加载', 'success');
        
        // 检查 Gantt 模块
        if (typeof Highcharts.ganttChart === 'function') {
            updateStatus('gantt-status', 'success', 'Highcharts Gantt: 已加载');
            log('✅ Highcharts Gantt 模块已加载', 'success');
        } else {
            updateStatus('gantt-status', 'error', 'Highcharts Gantt: 未加载');
            log('❌ Highcharts Gantt 模块未加载', 'error');
        }
    } else {
        updateStatus('highcharts-status', 'error', 'Highcharts: 未加载');
        updateStatus('gantt-status', 'error', 'Highcharts Gantt: 依赖未满足');
        log('❌ Highcharts 未加载', 'error');
    }
    
    // 检查 AdvancedGantt
    if (typeof AdvancedGantt !== 'undefined') {
        updateStatus('advanced-gantt-status', 'success', 'AdvancedGantt: 已加载');
        log('✅ AdvancedGantt 组件已加载', 'success');
    } else {
        updateStatus('advanced-gantt-status', 'error', 'AdvancedGantt: 未加载');
        log('❌ AdvancedGantt 组件未加载', 'error');
    }
}

// 测试高级甘特图
function testAdvancedGantt() {
    log('开始测试高级甘特图...', 'info');
    
    if (typeof AdvancedGantt === 'undefined') {
        log('❌ AdvancedGantt 不可用，无法测试', 'error');
        return;
    }
    
    try {
        // 清空容器
        document.getElementById('test-gantt-container').innerHTML = '';
        
        // 创建测试数据
        const testData = [{
            name: '测试项目',
            data: [{
                name: '项目启动',
                id: 'start',
                start: Date.now(),
                end: Date.now() + 2 * 24 * 60 * 60 * 1000,
                completed: { amount: 1 },
                owner: '项目经理'
            }, {
                name: '需求分析',
                id: 'analysis',
                dependency: 'start',
                start: Date.now() + 2 * 24 * 60 * 60 * 1000,
                end: Date.now() + 5 * 24 * 60 * 60 * 1000,
                completed: { amount: 0.8 },
                owner: '业务分析师'
            }]
        }];
        
        // 创建甘特图
        const gantt = new AdvancedGantt('test-gantt-container', {
            title: '高级甘特图测试',
            subtitle: '这是一个测试甘特图',
            data: testData,
            height: 400,
            onChartReady: function(chart) {
                log('✅ 高级甘特图创建成功', 'success');
            },
            onError: function(error) {
                log(`❌ 高级甘特图创建失败: ${error.message}`, 'error');
            }
        });
        
    } catch (error) {
        log(`❌ 测试高级甘特图时发生错误: ${error.message}`, 'error');
    }
}

// 测试基础甘特图
function testBasicGantt() {
    log('开始测试基础甘特图...', 'info');
    
    if (typeof Highcharts === 'undefined' || typeof Highcharts.ganttChart !== 'function') {
        log('❌ Highcharts Gantt 不可用，无法测试', 'error');
        return;
    }
    
    try {
        // 清空容器
        document.getElementById('test-gantt-container').innerHTML = '';
        
        // 创建基础甘特图
        const chart = Highcharts.ganttChart('test-gantt-container', {
            title: {
                text: '基础甘特图测试'
            },
            series: [{
                name: '测试项目',
                data: [{
                    name: '任务1',
                    start: Date.now(),
                    end: Date.now() + 3 * 24 * 60 * 60 * 1000,
                    y: 0
                }, {
                    name: '任务2',
                    start: Date.now() + 1 * 24 * 60 * 60 * 1000,
                    end: Date.now() + 4 * 24 * 60 * 60 * 1000,
                    y: 1
                }]
            }]
        });
        
        log('✅ 基础甘特图创建成功', 'success');
        
    } catch (error) {
        log(`❌ 测试基础甘特图时发生错误: ${error.message}`, 'error');
    }
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    log('页面加载完成，开始检查组件...', 'info');
    
    // 延迟检查，确保所有脚本都已加载
    setTimeout(checkComponents, 100);
    
    // 绑定测试按钮事件
    document.getElementById('test-gantt-btn').addEventListener('click', testAdvancedGantt);
    document.getElementById('test-basic-btn').addEventListener('click', testBasicGantt);
});
</script>
{% endblock %}
