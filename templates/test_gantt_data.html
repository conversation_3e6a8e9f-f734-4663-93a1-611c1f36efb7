{% extends 'base.html' %}
{% load static %}

{% block title %}甘特图数据测试{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="bg-white rounded-lg shadow-lg p-6">
        <h1 class="text-3xl font-bold text-gray-800 mb-6">甘特图数据测试页面</h1>
        
        <!-- 项目选择 -->
        <div class="mb-6">
            <label for="project-select" class="block text-sm font-medium text-gray-700 mb-2">选择项目</label>
            <select id="project-select" class="form-select w-full">
                <option value="">请选择项目...</option>
            </select>
        </div>

        <!-- 测试按钮 -->
        <div class="mb-6">
            <button id="load-data-btn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg mr-4">
                加载项目数据
            </button>
            <button id="test-render-btn" class="bg-green-500 hover:bg-green-600 text-white px-6 py-2 rounded-lg mr-4">
                测试渲染甘特图
            </button>
            <button id="clear-btn" class="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg">
                清空
            </button>
        </div>

        <!-- 数据显示区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <div>
                <h3 class="text-lg font-semibold mb-3">原始数据</h3>
                <pre id="raw-data" class="bg-gray-100 p-4 rounded-lg text-sm overflow-auto h-64"></pre>
            </div>
            <div>
                <h3 class="text-lg font-semibold mb-3">处理后数据</h3>
                <pre id="processed-data" class="bg-gray-100 p-4 rounded-lg text-sm overflow-auto h-64"></pre>
            </div>
        </div>

        <!-- 甘特图容器 -->
        <div class="mb-6">
            <h3 class="text-lg font-semibold mb-3">甘特图渲染</h3>
            <div id="test-gantt-container" style="height: 400px;" class="border border-gray-300 rounded-lg"></div>
        </div>

        <!-- 日志输出 -->
        <div class="mb-6">
            <h3 class="text-lg font-semibold mb-3">测试日志</h3>
            <div id="test-log" class="bg-black text-green-400 p-4 rounded-lg h-64 overflow-y-auto font-mono text-sm"></div>
        </div>
    </div>
</div>

<script>
let currentProjectData = null;
let processedGanttData = null;

// 日志函数
function log(message, type = 'info') {
    const logDiv = document.getElementById('test-log');
    const timestamp = new Date().toLocaleTimeString();
    const colors = {
        info: 'text-green-400',
        success: 'text-green-300',
        warning: 'text-yellow-400',
        error: 'text-red-400'
    };
    
    const logEntry = document.createElement('div');
    logEntry.className = colors[type] || 'text-green-400';
    logEntry.textContent = `[${timestamp}] ${message}`;
    logDiv.appendChild(logEntry);
    logDiv.scrollTop = logDiv.scrollHeight;
    
    console.log(`[${timestamp}] ${message}`);
}

// 加载项目列表
function loadProjects() {
    fetch('/api/projects/')
        .then(response => response.json())
        .then(data => {
            const select = document.getElementById('project-select');
            select.innerHTML = '<option value="">请选择项目...</option>';

            if (data.results) {
                data.results.forEach(project => {
                    const option = document.createElement('option');
                    option.value = project.id;
                    option.textContent = `${project.name} (${project.status})`;
                    select.appendChild(option);
                });
                log(`加载了 ${data.results.length} 个项目`, 'success');
            }
        })
        .catch(error => {
            log(`加载项目列表失败: ${error.message}`, 'error');
        });
}

// 加载项目数据
function loadProjectData() {
    const projectId = document.getElementById('project-select').value;
    if (!projectId) {
        log('请先选择一个项目', 'warning');
        return;
    }

    log(`开始加载项目 ${projectId} 的甘特图数据...`, 'info');

    fetch(`/api/projects/${projectId}/gantt_data/`)
        .then(response => response.json())
        .then(data => {
            currentProjectData = data;
            
            // 显示原始数据
            document.getElementById('raw-data').textContent = JSON.stringify(data, null, 2);
            
            log(`成功加载项目数据，包含 ${data.tasks?.length || 0} 个任务`, 'success');
            
            // 处理数据
            processGanttData(data);
        })
        .catch(error => {
            log(`加载项目数据失败: ${error.message}`, 'error');
        });
}

// 处理甘特图数据
function processGanttData(data) {
    log('开始处理甘特图数据...', 'info');
    
    try {
        if (!data.tasks || data.tasks.length === 0) {
            log('没有任务数据', 'warning');
            return;
        }

        // 转换数据格式
        const ganttData = data.tasks.map((task, index) => {
            const taskData = {
                id: task.id || `task_${index}`,
                name: task.summary ? `📁 ${task.wbs_code} ${task.name}` : `${task.wbs_code} ${task.name}`,
                wbs_code: task.wbs_code,
                start: task.start ? new Date(task.start).getTime() : 
                       task.start_date ? new Date(task.start_date).getTime() : Date.now(),
                end: task.end ? new Date(task.end).getTime() : 
                     task.end_date ? new Date(task.end_date).getTime() : Date.now() + 24*60*60*1000,
                completed: {
                    amount: (task.progress_percentage || task.progress || 0) / 100
                },
                parent: task.parent || undefined,
                owner: task.owner || '未分配',
                y: index,
                color: task.critical ? '#ef4444' :
                       task.summary ? '#8b5cf6' :
                       (task.status === 'completed' ? '#10b981' : '#3b82f6'),
                milestone: task.milestone || false,
                summary: task.summary || false,
                status: task.status,
                priority: task.priority
            };

            // 验证日期
            if (isNaN(taskData.start) || isNaN(taskData.end)) {
                log(`任务 ${taskData.name} 日期无效`, 'error');
                log(`原始开始时间: ${task.start || task.start_date}`, 'error');
                log(`原始结束时间: ${task.end || task.end_date}`, 'error');
            } else {
                log(`任务 ${taskData.name} 日期有效: ${new Date(taskData.start).toLocaleDateString()} - ${new Date(taskData.end).toLocaleDateString()}`, 'info');
            }

            return taskData;
        });

        processedGanttData = ganttData;
        
        // 显示处理后数据
        document.getElementById('processed-data').textContent = JSON.stringify(ganttData, null, 2);
        
        log(`数据处理完成，生成 ${ganttData.length} 个甘特图任务`, 'success');
        
    } catch (error) {
        log(`处理甘特图数据失败: ${error.message}`, 'error');
    }
}

// 测试渲染甘特图
function testRenderGantt() {
    if (!processedGanttData) {
        log('请先加载并处理项目数据', 'warning');
        return;
    }

    log('开始测试渲染甘特图...', 'info');

    try {
        // 清空容器
        document.getElementById('test-gantt-container').innerHTML = '';

        if (typeof AdvancedGantt !== 'undefined') {
            log('使用 AdvancedGantt 渲染', 'info');
            
            const gantt = new AdvancedGantt('test-gantt-container', {
                title: '测试甘特图',
                subtitle: '数据测试渲染',
                data: [{
                    name: '项目任务',
                    data: processedGanttData
                }],
                height: 400,
                onChartReady: function(chart) {
                    log('✅ AdvancedGantt 渲染成功', 'success');
                },
                onError: function(error) {
                    log(`❌ AdvancedGantt 渲染失败: ${error.message}`, 'error');
                    testBasicGantt();
                }
            });
        } else {
            log('AdvancedGantt 不可用，使用基础甘特图', 'warning');
            testBasicGantt();
        }
    } catch (error) {
        log(`渲染甘特图时发生错误: ${error.message}`, 'error');
    }
}

// 测试基础甘特图
function testBasicGantt() {
    if (typeof Highcharts === 'undefined' || typeof Highcharts.ganttChart !== 'function') {
        log('Highcharts Gantt 不可用', 'error');
        return;
    }

    log('使用基础 Highcharts 渲染', 'info');

    try {
        const chart = Highcharts.ganttChart('test-gantt-container', {
            title: {
                text: '基础甘特图测试'
            },
            series: [{
                name: '项目任务',
                data: processedGanttData
            }]
        });

        log('✅ 基础甘特图渲染成功', 'success');
    } catch (error) {
        log(`❌ 基础甘特图渲染失败: ${error.message}`, 'error');
    }
}

// 清空
function clearAll() {
    document.getElementById('raw-data').textContent = '';
    document.getElementById('processed-data').textContent = '';
    document.getElementById('test-gantt-container').innerHTML = '';
    document.getElementById('test-log').innerHTML = '';
    currentProjectData = null;
    processedGanttData = null;
    log('已清空所有数据', 'info');
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    log('页面加载完成', 'info');
    
    // 加载项目列表
    loadProjects();
    
    // 绑定事件
    document.getElementById('load-data-btn').addEventListener('click', loadProjectData);
    document.getElementById('test-render-btn').addEventListener('click', testRenderGantt);
    document.getElementById('clear-btn').addEventListener('click', clearAll);
});
</script>
{% endblock %}
