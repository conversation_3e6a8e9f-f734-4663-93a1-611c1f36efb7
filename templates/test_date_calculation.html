<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日期计算测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-case {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            background: #f9f9f9;
        }
        .result {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
        }
        .error {
            background: #ffe8e8;
            color: #d00;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover { background: #0056b3; }
    </style>
</head>
<body>
    <div class="container">
        <h1>甘特图日期计算测试</h1>
        
        <button class="btn" onclick="runAllTests()">运行所有测试</button>
        <button class="btn" onclick="clearResults()">清空结果</button>
        
        <div id="test-results"></div>
    </div>

    <script>
        // 复制甘特图页面的日期计算函数
        function calculateTaskEndDate(task) {
            let startDate;
            
            // 获取开始日期
            if (task.start) {
                startDate = new Date(task.start);
            } else if (task.start_date) {
                startDate = new Date(task.start_date);
            } else {
                startDate = new Date(); // 默认今天
            }
            
            // 获取工期（天数）
            let duration = task.planned_duration || task.duration || 1;
            
            // 如果有工程数量和每日工效，重新计算工期
            if (task.quantity && task.daily_efficiency && task.quantity > 0 && task.daily_efficiency > 0) {
                duration = Math.ceil(task.quantity / task.daily_efficiency);
            }
            
            // 计算结束日期（排除周末）
            let endDate = new Date(startDate);
            let workDaysAdded = 0;
            
            while (workDaysAdded < duration) {
                endDate.setDate(endDate.getDate() + 1);
                const dayOfWeek = endDate.getDay();
                
                // 0 = 周日, 6 = 周六，跳过周末
                if (dayOfWeek !== 0 && dayOfWeek !== 6) {
                    workDaysAdded++;
                }
            }
            
            return endDate.getTime();
        }

        function calculateTaskDuration(task) {
            // 如果有明确的工期，使用它
            if (task.planned_duration && task.planned_duration > 0) {
                return task.planned_duration;
            }
            
            // 如果有工程数量和每日工效，计算工期
            if (task.quantity && task.daily_efficiency && task.quantity > 0 && task.daily_efficiency > 0) {
                return Math.ceil(task.quantity / task.daily_efficiency);
            }
            
            // 如果有开始和结束日期，计算实际工期
            if (task.start_date && task.end_date) {
                const start = new Date(task.start_date);
                const end = new Date(task.end_date);
                const diffTime = Math.abs(end - start);
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                return diffDays > 0 ? diffDays : 1;
            }
            
            // 默认1天
            return 1;
        }

        // 测试用例
        const testCases = [
            {
                name: "基础工期计算 - 工程数量和每日工效",
                task: {
                    name: "基础施工",
                    start_date: "2025-01-01",
                    quantity: 100,
                    daily_efficiency: 10
                },
                expectedDuration: 10,
                description: "100单位工程量，每日10单位工效，应该是10天工期"
            },
            {
                name: "明确工期优先",
                task: {
                    name: "设计任务",
                    start_date: "2025-01-01",
                    planned_duration: 5,
                    quantity: 100,
                    daily_efficiency: 10
                },
                expectedDuration: 5,
                description: "有明确工期时，应该优先使用明确工期"
            },
            {
                name: "跨周末计算",
                task: {
                    name: "周末跨越任务",
                    start_date: "2025-01-03", // 周五
                    quantity: 30,
                    daily_efficiency: 10
                },
                expectedDuration: 3,
                description: "周五开始，3天工期，应该跨过周末"
            },
            {
                name: "小数工程量向上取整",
                task: {
                    name: "精细工程",
                    start_date: "2025-01-01",
                    quantity: 25,
                    daily_efficiency: 10
                },
                expectedDuration: 3,
                description: "25/10=2.5天，应该向上取整为3天"
            },
            {
                name: "默认工期",
                task: {
                    name: "简单任务",
                    start_date: "2025-01-01"
                },
                expectedDuration: 1,
                description: "没有工期信息时，默认1天"
            }
        ];

        function runTest(testCase) {
            try {
                const calculatedDuration = calculateTaskDuration(testCase.task);
                const startDate = new Date(testCase.task.start_date || Date.now());
                const endDate = new Date(calculateTaskEndDate(testCase.task));
                
                const actualDays = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
                
                const result = {
                    success: calculatedDuration === testCase.expectedDuration,
                    calculatedDuration,
                    expectedDuration: testCase.expectedDuration,
                    startDate: startDate.toLocaleDateString('zh-CN'),
                    endDate: endDate.toLocaleDateString('zh-CN'),
                    actualDays,
                    task: testCase.task
                };
                
                return result;
            } catch (error) {
                return {
                    success: false,
                    error: error.message,
                    task: testCase.task
                };
            }
        }

        function runAllTests() {
            const resultsContainer = document.getElementById('test-results');
            resultsContainer.innerHTML = '';
            
            testCases.forEach((testCase, index) => {
                const testDiv = document.createElement('div');
                testDiv.className = 'test-case';
                
                const result = runTest(testCase);
                
                testDiv.innerHTML = `
                    <h3>测试 ${index + 1}: ${testCase.name}</h3>
                    <p><strong>描述:</strong> ${testCase.description}</p>
                    <p><strong>任务数据:</strong></p>
                    <pre>${JSON.stringify(testCase.task, null, 2)}</pre>
                    
                    <div class="result ${result.success ? '' : 'error'}">
                        <p><strong>测试结果:</strong> ${result.success ? '✅ 通过' : '❌ 失败'}</p>
                        ${result.error ? `<p><strong>错误:</strong> ${result.error}</p>` : ''}
                        <p><strong>计算工期:</strong> ${result.calculatedDuration} 天</p>
                        <p><strong>期望工期:</strong> ${result.expectedDuration} 天</p>
                        ${result.startDate ? `<p><strong>开始日期:</strong> ${result.startDate}</p>` : ''}
                        ${result.endDate ? `<p><strong>结束日期:</strong> ${result.endDate}</p>` : ''}
                        ${result.actualDays ? `<p><strong>实际跨度:</strong> ${result.actualDays} 天</p>` : ''}
                    </div>
                `;
                
                resultsContainer.appendChild(testDiv);
            });
            
            // 统计结果
            const passedTests = testCases.filter((_, index) => runTest(testCases[index]).success).length;
            const totalTests = testCases.length;
            
            const summaryDiv = document.createElement('div');
            summaryDiv.className = 'test-case';
            summaryDiv.innerHTML = `
                <h3>测试总结</h3>
                <p><strong>通过:</strong> ${passedTests}/${totalTests}</p>
                <p><strong>成功率:</strong> ${Math.round(passedTests/totalTests*100)}%</p>
            `;
            resultsContainer.insertBefore(summaryDiv, resultsContainer.firstChild);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        // 页面加载时自动运行测试
        window.addEventListener('load', function() {
            setTimeout(runAllTests, 500);
        });
    </script>
</body>
</html>
