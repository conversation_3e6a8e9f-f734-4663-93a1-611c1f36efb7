DEBUG=True
SECRET_KEY=django-insecure-*&m18@(p8+z_q(=6$9!fmo^58s5wnb@vd*hr#5*k&9@2g%cp3n
DATABASE_URL=sqlite:///db.sqlite3

# OAuth Settings
GOOGLE_OAUTH2_CLIENT_ID=your_google_client_id
GOOGLE_OAUTH2_CLIENT_SECRET=your_google_client_secret
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# Email Settings
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your_email_password

# Redis Settings
REDIS_URL=redis://localhost:6379/0
