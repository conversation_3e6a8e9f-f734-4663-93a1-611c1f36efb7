<?xml version="1.0"?>
<Project xmlns="http://schemas.microsoft.com/project">
	<Name>assignment-text-project2002-mspdi.xml</Name>
	<Title>assignment-text-project2002-mpp9</Title>
	<Author>Project User</Author>
	<CreationDate>2018-10-18T10:46:00</CreationDate>
	<LastSaved>2018-10-18T10:46:00</LastSaved>
	<ScheduleFromStart>1</ScheduleFromStart>
	<StartDate>2018-10-18T08:00:00</StartDate>
	<FinishDate>2016-01-15T17:00:00</FinishDate>
	<FYStartDate>1</FYStartDate>
	<CriticalSlackLimit>0</CriticalSlackLimit>
	<CurrencyDigits>2</CurrencyDigits>
	<CurrencySymbol>£</CurrencySymbol>
	<CurrencySymbolPosition>0</CurrencySymbolPosition>
	<CalendarUID>1</CalendarUID>
	<DefaultStartTime>08:00:00</DefaultStartTime>
	<DefaultFinishTime>17:00:00</DefaultFinishTime>
	<MinutesPerDay>480</MinutesPerDay>
	<MinutesPerWeek>2400</MinutesPerWeek>
	<DaysPerMonth>20</DaysPerMonth>
	<DefaultTaskType>0</DefaultTaskType>
	<DefaultFixedCostAccrual>3</DefaultFixedCostAccrual>
	<DefaultStandardRate>0</DefaultStandardRate>
	<DefaultOvertimeRate>0</DefaultOvertimeRate>
	<DurationFormat>7</DurationFormat>
	<WorkFormat>2</WorkFormat>
	<EditableActualCosts>0</EditableActualCosts>
	<HonorConstraints>0</HonorConstraints>
	<InsertedProjectsLikeSummary>1</InsertedProjectsLikeSummary>
	<MultipleCriticalPaths>0</MultipleCriticalPaths>
	<NewTasksEffortDriven>1</NewTasksEffortDriven>
	<NewTasksEstimated>1</NewTasksEstimated>
	<SplitsInProgressTasks>1</SplitsInProgressTasks>
	<SpreadActualCost>0</SpreadActualCost>
	<SpreadPercentComplete>0</SpreadPercentComplete>
	<TaskUpdatesResource>1</TaskUpdatesResource>
	<FiscalYearStart>0</FiscalYearStart>
	<WeekStartDay>1</WeekStartDay>
	<MoveCompletedEndsBack>0</MoveCompletedEndsBack>
	<MoveRemainingStartsBack>0</MoveRemainingStartsBack>
	<MoveRemainingStartsForward>0</MoveRemainingStartsForward>
	<MoveCompletedEndsForward>0</MoveCompletedEndsForward>
	<BaselineForEarnedValue>0</BaselineForEarnedValue>
	<AutoAddNewResourcesAndTasks>1</AutoAddNewResourcesAndTasks>
	<CurrentDate>2018-10-18T08:00:00</CurrentDate>
	<MicrosoftProjectServerURL>1</MicrosoftProjectServerURL>
	<Autolink>1</Autolink>
	<NewTaskStartDate>0</NewTaskStartDate>
	<DefaultTaskEVMethod>0</DefaultTaskEVMethod>
	<ProjectExternallyEdited>0</ProjectExternallyEdited>
	<OutlineCodes/>
	<WBSMasks/>
	<ExtendedAttributes>
		<ExtendedAttribute>
			<FieldID>255852632</FieldID>
			<FieldName>Text1</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852633</FieldID>
			<FieldName>Text2</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852634</FieldID>
			<FieldName>Text3</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852635</FieldID>
			<FieldName>Text4</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852636</FieldID>
			<FieldName>Text5</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852637</FieldID>
			<FieldName>Text6</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852638</FieldID>
			<FieldName>Text7</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852639</FieldID>
			<FieldName>Text8</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852640</FieldID>
			<FieldName>Text9</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852641</FieldID>
			<FieldName>Text10</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852762</FieldID>
			<FieldName>Text11</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852763</FieldID>
			<FieldName>Text12</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852764</FieldID>
			<FieldName>Text13</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852765</FieldID>
			<FieldName>Text14</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852766</FieldID>
			<FieldName>Text15</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852767</FieldID>
			<FieldName>Text16</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852768</FieldID>
			<FieldName>Text17</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852769</FieldID>
			<FieldName>Text18</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852770</FieldID>
			<FieldName>Text19</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852772</FieldID>
			<FieldName>Text21</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852773</FieldID>
			<FieldName>Text22</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852774</FieldID>
			<FieldName>Text23</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852775</FieldID>
			<FieldName>Text24</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852776</FieldID>
			<FieldName>Text25</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852777</FieldID>
			<FieldName>Text26</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852778</FieldID>
			<FieldName>Text27</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852779</FieldID>
			<FieldName>Text28</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852780</FieldID>
			<FieldName>Text29</FieldName>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>255852781</FieldID>
			<FieldName>Text30</FieldName>
		</ExtendedAttribute>
	</ExtendedAttributes>
	<Calendars>
		<Calendar>
			<UID>1</UID>
			<Name>Standard</Name>
			<IsBaseCalendar>1</IsBaseCalendar>
			<BaseCalendarUID>-1</BaseCalendarUID>
			<WeekDays>
				<WeekDay>
					<DayType>1</DayType>
					<DayWorking>0</DayWorking>
				</WeekDay>
				<WeekDay>
					<DayType>2</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>3</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>4</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>5</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>6</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>7</DayType>
					<DayWorking>0</DayWorking>
				</WeekDay>
			</WeekDays>
		</Calendar>
		<Calendar>
			<UID>3</UID>
			<Name>Resource 1</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>4</UID>
			<Name>Resource 2</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>5</UID>
			<Name>Resource 3</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>6</UID>
			<Name>Resource 4</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>7</UID>
			<Name>Resource 5</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>8</UID>
			<Name>Resource 6</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>9</UID>
			<Name>Resource 7</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>10</UID>
			<Name>Resource 8</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>11</UID>
			<Name>Resource 9</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>12</UID>
			<Name>Resource 10</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>13</UID>
			<Name>Resource 11</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>14</UID>
			<Name>Resource 12</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>15</UID>
			<Name>Resource 13</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>16</UID>
			<Name>Resource 14</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>17</UID>
			<Name>Resource 15</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>18</UID>
			<Name>Resource 16</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>19</UID>
			<Name>Resource 17</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>20</UID>
			<Name>Resource 18</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>21</UID>
			<Name>Resource 19</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>22</UID>
			<Name>Resource 20</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>23</UID>
			<Name>Resource 21</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>24</UID>
			<Name>Resource 22</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>25</UID>
			<Name>Resource 23</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>26</UID>
			<Name>Resource 24</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>27</UID>
			<Name>Resource 25</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>28</UID>
			<Name>Resource 26</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>29</UID>
			<Name>Resource 27</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>30</UID>
			<Name>Resource 28</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>31</UID>
			<Name>Resource 29</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>32</UID>
			<Name>Resource 30</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
	</Calendars>
	<Tasks>
		<Task>
			<UID>0</UID>
			<ID>0</ID>
			<Name>assignment-text-project2002-mpp9</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>0</WBS>
			<OutlineNumber>0</OutlineNumber>
			<OutlineLevel>0</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>21</DurationFormat>
			<Work>PT2400H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>1</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>144000000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT2400H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT2400H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>1</UID>
			<ID>1</ID>
			<Name>Task 1</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>1</WBS>
			<OutlineNumber>1</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>2</UID>
			<ID>2</ID>
			<Name>Task 2</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>2</WBS>
			<OutlineNumber>2</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>3</UID>
			<ID>3</ID>
			<Name>Task 3</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>3</WBS>
			<OutlineNumber>3</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>4</UID>
			<ID>4</ID>
			<Name>Task 4</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>4</WBS>
			<OutlineNumber>4</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>5</UID>
			<ID>5</ID>
			<Name>Task 5</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>5</WBS>
			<OutlineNumber>5</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>6</UID>
			<ID>6</ID>
			<Name>Task 6</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>6</WBS>
			<OutlineNumber>6</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>7</UID>
			<ID>7</ID>
			<Name>Task 7</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>7</WBS>
			<OutlineNumber>7</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>8</UID>
			<ID>8</ID>
			<Name>Task 8</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>8</WBS>
			<OutlineNumber>8</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>9</UID>
			<ID>9</ID>
			<Name>Task 9</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>9</WBS>
			<OutlineNumber>9</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>10</UID>
			<ID>10</ID>
			<Name>Task 10</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>10</WBS>
			<OutlineNumber>10</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>11</UID>
			<ID>11</ID>
			<Name>Task 11</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>11</WBS>
			<OutlineNumber>11</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>12</UID>
			<ID>12</ID>
			<Name>Task 12</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>12</WBS>
			<OutlineNumber>12</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>13</UID>
			<ID>13</ID>
			<Name>Task 13</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>13</WBS>
			<OutlineNumber>13</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>14</UID>
			<ID>14</ID>
			<Name>Task 14</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>14</WBS>
			<OutlineNumber>14</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>15</UID>
			<ID>15</ID>
			<Name>Task 15</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>15</WBS>
			<OutlineNumber>15</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>16</UID>
			<ID>16</ID>
			<Name>Task 16</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>16</WBS>
			<OutlineNumber>16</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>17</UID>
			<ID>17</ID>
			<Name>Task 17</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>17</WBS>
			<OutlineNumber>17</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>18</UID>
			<ID>18</ID>
			<Name>Task 18</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>18</WBS>
			<OutlineNumber>18</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>19</UID>
			<ID>19</ID>
			<Name>Task 19</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>19</WBS>
			<OutlineNumber>19</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>20</UID>
			<ID>20</ID>
			<Name>Task 20</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>20</WBS>
			<OutlineNumber>20</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>21</UID>
			<ID>21</ID>
			<Name>Task 21</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>21</WBS>
			<OutlineNumber>21</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>22</UID>
			<ID>22</ID>
			<Name>Task 22</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>22</WBS>
			<OutlineNumber>22</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>23</UID>
			<ID>23</ID>
			<Name>Task 23</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>23</WBS>
			<OutlineNumber>23</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>24</UID>
			<ID>24</ID>
			<Name>Task 24</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>24</WBS>
			<OutlineNumber>24</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>25</UID>
			<ID>25</ID>
			<Name>Task 25</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>25</WBS>
			<OutlineNumber>25</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>26</UID>
			<ID>26</ID>
			<Name>Task 26</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>26</WBS>
			<OutlineNumber>26</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>27</UID>
			<ID>27</ID>
			<Name>Task 27</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>27</WBS>
			<OutlineNumber>27</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>28</UID>
			<ID>28</ID>
			<Name>Task 28</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>28</WBS>
			<OutlineNumber>28</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>29</UID>
			<ID>29</ID>
			<Name>Task 29</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>29</WBS>
			<OutlineNumber>29</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
		<Task>
			<UID>30</UID>
			<ID>30</ID>
			<Name>Task 30</Name>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T10:46:00</CreateDate>
			<WBS>30</WBS>
			<OutlineNumber>30</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2016-01-04T08:00:00</Start>
			<Finish>2016-01-15T17:00:00</Finish>
			<Duration>PT80H0M0S</Duration>
			<DurationFormat>7</DurationFormat>
			<Work>PT80H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>1</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>0</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2016-01-04T08:00:00</EarlyStart>
			<EarlyFinish>2016-01-15T17:00:00</EarlyFinish>
			<LateStart>2016-01-04T08:00:00</LateStart>
			<LateFinish>2016-01-15T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingDuration>PT80H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0</ACWP>
			<CV>0</CV>
			<ConstraintType>4</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<ConstraintDate>2016-01-04T08:00:00</ConstraintDate>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
		</Task>
	</Tasks>
	<Resources>
		<Resource>
			<UID>0</UID>
			<ID>0</ID>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>0</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>2</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>1</UID>
			<ID>1</ID>
			<Name>Resource 1</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>3</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>2</UID>
			<ID>2</ID>
			<Name>Resource 2</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>4</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>3</UID>
			<ID>3</ID>
			<Name>Resource 3</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>5</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>4</UID>
			<ID>4</ID>
			<Name>Resource 4</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>6</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>5</UID>
			<ID>5</ID>
			<Name>Resource 5</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>7</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>6</UID>
			<ID>6</ID>
			<Name>Resource 6</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>8</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>7</UID>
			<ID>7</ID>
			<Name>Resource 7</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>9</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>8</UID>
			<ID>8</ID>
			<Name>Resource 8</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>10</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>9</UID>
			<ID>9</ID>
			<Name>Resource 9</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>11</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>10</UID>
			<ID>10</ID>
			<Name>Resource 10</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>12</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>11</UID>
			<ID>11</ID>
			<Name>Resource 11</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>13</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>12</UID>
			<ID>12</ID>
			<Name>Resource 12</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>14</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>13</UID>
			<ID>13</ID>
			<Name>Resource 13</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>15</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>14</UID>
			<ID>14</ID>
			<Name>Resource 14</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>16</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>15</UID>
			<ID>15</ID>
			<Name>Resource 15</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>17</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>16</UID>
			<ID>16</ID>
			<Name>Resource 16</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>18</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>17</UID>
			<ID>17</ID>
			<Name>Resource 17</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>19</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>18</UID>
			<ID>18</ID>
			<Name>Resource 18</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>20</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>19</UID>
			<ID>19</ID>
			<Name>Resource 19</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>21</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>20</UID>
			<ID>20</ID>
			<Name>Resource 20</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>22</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>21</UID>
			<ID>21</ID>
			<Name>Resource 21</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>23</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>22</UID>
			<ID>22</ID>
			<Name>Resource 22</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>24</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>23</UID>
			<ID>23</ID>
			<Name>Resource 23</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>25</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>24</UID>
			<ID>24</ID>
			<Name>Resource 24</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>26</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>25</UID>
			<ID>25</ID>
			<Name>Resource 25</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>27</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>26</UID>
			<ID>26</ID>
			<Name>Resource 26</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>28</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>27</UID>
			<ID>27</ID>
			<Name>Resource 27</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>29</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>28</UID>
			<ID>28</ID>
			<Name>Resource 28</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>30</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>29</UID>
			<ID>29</ID>
			<Name>Resource 29</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>31</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
		<Resource>
			<UID>30</UID>
			<ID>30</ID>
			<Name>Resource 30</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>R</Initials>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1</MaxUnits>
			<PeakUnits>1</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT80H0M0S</Work>
			<RegularWork>PT80H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>4800000</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0</SV>
			<CV>0</CV>
			<ACWP>0</ACWP>
			<CalendarUID>32</CalendarUID>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
		</Resource>
	</Resources>
	<Assignments>
		<Assignment>
			<UID>2</UID>
			<TaskUID>1</TaskUID>
			<ResourceUID>1</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>2</UID>
				<FieldID>255852632</FieldID>
				<Value>Text1</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>4</UID>
			<TaskUID>2</TaskUID>
			<ResourceUID>2</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>4</UID>
				<FieldID>255852633</FieldID>
				<Value>Text2</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>4</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>4</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>4</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>4</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>4</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>4</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>4</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>4</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>4</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>4</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>4</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>4</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>6</UID>
			<TaskUID>3</TaskUID>
			<ResourceUID>3</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>6</UID>
				<FieldID>255852634</FieldID>
				<Value>Text3</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>8</UID>
			<TaskUID>4</TaskUID>
			<ResourceUID>4</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>8</UID>
				<FieldID>255852635</FieldID>
				<Value>Text4</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>10</UID>
			<TaskUID>5</TaskUID>
			<ResourceUID>5</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>10</UID>
				<FieldID>255852636</FieldID>
				<Value>Text5</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>10</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>10</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>10</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>10</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>10</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>10</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>10</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>10</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>10</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>10</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>10</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>10</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>12</UID>
			<TaskUID>6</TaskUID>
			<ResourceUID>6</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>12</UID>
				<FieldID>255852637</FieldID>
				<Value>Text6</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>12</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>12</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>12</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>12</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>12</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>12</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>12</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>12</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>12</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>12</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>12</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>12</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>14</UID>
			<TaskUID>7</TaskUID>
			<ResourceUID>7</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>14</UID>
				<FieldID>255852638</FieldID>
				<Value>Text7</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>14</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>14</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>14</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>14</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>14</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>14</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>14</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>14</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>14</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>14</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>14</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>14</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>16</UID>
			<TaskUID>8</TaskUID>
			<ResourceUID>8</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>16</UID>
				<FieldID>255852639</FieldID>
				<Value>Text8</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>16</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>16</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>16</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>16</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>16</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>16</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>16</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>16</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>16</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>16</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>16</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>16</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>18</UID>
			<TaskUID>9</TaskUID>
			<ResourceUID>9</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>18</UID>
				<FieldID>255852640</FieldID>
				<Value>Text9</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>18</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>18</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>18</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>18</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>18</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>18</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>18</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>18</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>18</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>18</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>18</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>18</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>20</UID>
			<TaskUID>10</TaskUID>
			<ResourceUID>10</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>20</UID>
				<FieldID>255852641</FieldID>
				<Value>Text10</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>20</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>20</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>20</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>20</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>20</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>20</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>20</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>20</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>20</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>20</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>20</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>20</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>22</UID>
			<TaskUID>11</TaskUID>
			<ResourceUID>11</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>22</UID>
				<FieldID>255852762</FieldID>
				<Value>Text11</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>22</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>22</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>22</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>22</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>22</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>22</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>22</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>22</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>22</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>22</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>22</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>22</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>24</UID>
			<TaskUID>12</TaskUID>
			<ResourceUID>12</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>24</UID>
				<FieldID>255852763</FieldID>
				<Value>Text12</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>24</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>24</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>24</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>24</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>24</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>24</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>24</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>24</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>24</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>24</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>24</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>24</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>26</UID>
			<TaskUID>13</TaskUID>
			<ResourceUID>13</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>26</UID>
				<FieldID>255852764</FieldID>
				<Value>Text13</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>26</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>26</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>26</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>26</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>26</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>26</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>26</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>26</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>26</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>26</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>26</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>26</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>28</UID>
			<TaskUID>14</TaskUID>
			<ResourceUID>14</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>28</UID>
				<FieldID>255852765</FieldID>
				<Value>Text14</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>28</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>28</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>28</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>28</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>28</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>28</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>28</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>28</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>28</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>28</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>28</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>28</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>30</UID>
			<TaskUID>15</TaskUID>
			<ResourceUID>15</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>30</UID>
				<FieldID>255852766</FieldID>
				<Value>Text15</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>30</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>30</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>30</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>30</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>30</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>30</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>30</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>30</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>30</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>30</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>30</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>30</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>32</UID>
			<TaskUID>16</TaskUID>
			<ResourceUID>16</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>32</UID>
				<FieldID>255852767</FieldID>
				<Value>Text16</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>32</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>32</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>32</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>32</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>32</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>32</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>32</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>32</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>32</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>32</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>32</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>32</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>34</UID>
			<TaskUID>17</TaskUID>
			<ResourceUID>17</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>34</UID>
				<FieldID>255852768</FieldID>
				<Value>Text17</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>34</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>34</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>34</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>34</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>34</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>34</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>34</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>34</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>34</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>34</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>34</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>34</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>36</UID>
			<TaskUID>18</TaskUID>
			<ResourceUID>18</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>36</UID>
				<FieldID>255852769</FieldID>
				<Value>Text18</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>36</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>36</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>36</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>36</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>36</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>36</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>36</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>36</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>36</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>36</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>36</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>36</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>38</UID>
			<TaskUID>19</TaskUID>
			<ResourceUID>19</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>38</UID>
				<FieldID>255852770</FieldID>
				<Value>Text19</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>38</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>38</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>38</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>38</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>38</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>38</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>38</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>38</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>38</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>38</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>38</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>38</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>40</UID>
			<TaskUID>20</TaskUID>
			<ResourceUID>20</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<!--  Added manually: not written by Project 2002 -->
			<ExtendedAttribute>
				<UID>40</UID>
				<FieldID>255852771</FieldID>
				<Value>Text20</Value>
			</ExtendedAttribute>			
			<TimephasedData>
				<Type>1</Type>
				<UID>40</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>40</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>40</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>40</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>40</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>40</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>40</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>40</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>40</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>40</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>40</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>40</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>42</UID>
			<TaskUID>21</TaskUID>
			<ResourceUID>21</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>42</UID>
				<FieldID>255852772</FieldID>
				<Value>Text21</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>42</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>42</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>42</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>42</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>42</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>42</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>42</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>42</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>42</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>42</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>42</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>42</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>44</UID>
			<TaskUID>22</TaskUID>
			<ResourceUID>22</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>44</UID>
				<FieldID>255852773</FieldID>
				<Value>Text22</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>44</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>44</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>44</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>44</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>44</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>44</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>44</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>44</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>44</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>44</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>44</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>44</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>46</UID>
			<TaskUID>23</TaskUID>
			<ResourceUID>23</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>46</UID>
				<FieldID>255852774</FieldID>
				<Value>Text23</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>46</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>46</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>46</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>46</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>46</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>46</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>46</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>46</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>46</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>46</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>46</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>46</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>48</UID>
			<TaskUID>24</TaskUID>
			<ResourceUID>24</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>48</UID>
				<FieldID>255852775</FieldID>
				<Value>Text24</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>48</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>48</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>48</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>48</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>48</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>48</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>48</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>48</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>48</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>48</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>48</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>48</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>50</UID>
			<TaskUID>25</TaskUID>
			<ResourceUID>25</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>50</UID>
				<FieldID>255852776</FieldID>
				<Value>Text25</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>50</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>50</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>50</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>50</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>50</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>50</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>50</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>50</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>50</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>50</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>50</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>50</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>52</UID>
			<TaskUID>26</TaskUID>
			<ResourceUID>26</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>52</UID>
				<FieldID>255852777</FieldID>
				<Value>Text26</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>52</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>52</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>52</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>52</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>52</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>52</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>52</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>52</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>52</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>52</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>52</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>52</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>54</UID>
			<TaskUID>27</TaskUID>
			<ResourceUID>27</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>54</UID>
				<FieldID>255852778</FieldID>
				<Value>Text27</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>54</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>54</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>54</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>54</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>54</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>54</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>54</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>54</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>54</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>54</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>54</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>54</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>56</UID>
			<TaskUID>28</TaskUID>
			<ResourceUID>28</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>56</UID>
				<FieldID>255852779</FieldID>
				<Value>Text28</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>56</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>56</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>56</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>56</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>56</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>56</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>56</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>56</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>56</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>56</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>56</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>56</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>58</UID>
			<TaskUID>29</TaskUID>
			<ResourceUID>29</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>58</UID>
				<FieldID>255852780</FieldID>
				<Value>Text29</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>58</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>58</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>58</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>58</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>58</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>58</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>58</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>58</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>58</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>58</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>58</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>58</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>60</UID>
			<TaskUID>30</TaskUID>
			<ResourceUID>30</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<CostVariance>0</CostVariance>
			<CV>0</CV>
			<Delay>0</Delay>
			<Finish>2016-01-15T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>4800000</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT80H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT80H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2016-01-04T08:00:00</Start>
			<Stop>2016-01-04T08:00:00</Stop>
			<Resume>2016-01-04T08:00:00</Resume>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0</VAC>
			<Work>PT80H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0</BCWS>
			<BCWP>0</BCWP>
			<ExtendedAttribute>
				<UID>60</UID>
				<FieldID>255852781</FieldID>
				<Value>Text30</Value>
			</ExtendedAttribute>
			<TimephasedData>
				<Type>1</Type>
				<UID>60</UID>
				<Start>2016-01-04T08:00:00</Start>
				<Finish>2016-01-05T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>60</UID>
				<Start>2016-01-05T08:00:00</Start>
				<Finish>2016-01-06T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>60</UID>
				<Start>2016-01-06T08:00:00</Start>
				<Finish>2016-01-07T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>60</UID>
				<Start>2016-01-07T08:00:00</Start>
				<Finish>2016-01-08T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>60</UID>
				<Start>2016-01-08T08:00:00</Start>
				<Finish>2016-01-09T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>60</UID>
				<Start>2016-01-09T08:00:00</Start>
				<Finish>2016-01-10T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>60</UID>
				<Start>2016-01-10T08:00:00</Start>
				<Finish>2016-01-11T08:00:00</Finish>
				<Unit>2</Unit>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>60</UID>
				<Start>2016-01-11T08:00:00</Start>
				<Finish>2016-01-12T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>60</UID>
				<Start>2016-01-12T08:00:00</Start>
				<Finish>2016-01-13T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>60</UID>
				<Start>2016-01-13T08:00:00</Start>
				<Finish>2016-01-14T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>60</UID>
				<Start>2016-01-14T08:00:00</Start>
				<Finish>2016-01-15T08:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
			<TimephasedData>
				<Type>1</Type>
				<UID>60</UID>
				<Start>2016-01-15T08:00:00</Start>
				<Finish>2016-01-15T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
	</Assignments>
</Project>
