<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Project xmlns="http://schemas.microsoft.com/project">
	<SaveVersion>14</SaveVersion>
	<BuildNumber>16.0.10730.20102</BuildNumber>
	<Name>resource-text-project2019-mspdi.xml</Name>
	<GUID>75DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
	<CreationDate>2018-10-18T08:00:00</CreationDate>
	<LastSaved>2018-10-18T14:36:00</LastSaved>
	<ScheduleFromStart>1</ScheduleFromStart>
	<StartDate>2018-10-18T08:00:00</StartDate>
	<FinishDate>2018-10-18T08:00:00</FinishDate>
	<FYStartDate>1</FYStartDate>
	<CriticalSlackLimit>0</CriticalSlackLimit>
	<CurrencyDigits>2</CurrencyDigits>
	<CurrencySymbol>£</CurrencySymbol>
	<CurrencyCode>GBP</CurrencyCode>
	<CurrencySymbolPosition>0</CurrencySymbolPosition>
	<CalendarUID>1</CalendarUID>
	<DefaultStartTime>08:00:00</DefaultStartTime>
	<DefaultFinishTime>17:00:00</DefaultFinishTime>
	<MinutesPerDay>480</MinutesPerDay>
	<MinutesPerWeek>2400</MinutesPerWeek>
	<DaysPerMonth>20</DaysPerMonth>
	<DefaultTaskType>0</DefaultTaskType>
	<DefaultFixedCostAccrual>3</DefaultFixedCostAccrual>
	<DefaultStandardRate>0</DefaultStandardRate>
	<DefaultOvertimeRate>0</DefaultOvertimeRate>
	<DurationFormat>7</DurationFormat>
	<WorkFormat>2</WorkFormat>
	<EditableActualCosts>0</EditableActualCosts>
	<HonorConstraints>0</HonorConstraints>
	<InsertedProjectsLikeSummary>1</InsertedProjectsLikeSummary>
	<MultipleCriticalPaths>0</MultipleCriticalPaths>
	<NewTasksEffortDriven>0</NewTasksEffortDriven>
	<NewTasksEstimated>1</NewTasksEstimated>
	<SplitsInProgressTasks>1</SplitsInProgressTasks>
	<SpreadActualCost>0</SpreadActualCost>
	<SpreadPercentComplete>0</SpreadPercentComplete>
	<TaskUpdatesResource>1</TaskUpdatesResource>
	<FiscalYearStart>0</FiscalYearStart>
	<WeekStartDay>1</WeekStartDay>
	<MoveCompletedEndsBack>0</MoveCompletedEndsBack>
	<MoveRemainingStartsBack>0</MoveRemainingStartsBack>
	<MoveRemainingStartsForward>0</MoveRemainingStartsForward>
	<MoveCompletedEndsForward>0</MoveCompletedEndsForward>
	<BaselineForEarnedValue>0</BaselineForEarnedValue>
	<AutoAddNewResourcesAndTasks>1</AutoAddNewResourcesAndTasks>
	<CurrentDate>2018-10-18T08:00:00</CurrentDate>
	<MicrosoftProjectServerURL>1</MicrosoftProjectServerURL>
	<Autolink>0</Autolink>
	<NewTaskStartDate>0</NewTaskStartDate>
	<NewTasksAreManual>1</NewTasksAreManual>
	<DefaultTaskEVMethod>0</DefaultTaskEVMethod>
	<ProjectExternallyEdited>0</ProjectExternallyEdited>
	<ExtendedCreationDate>1984-01-01T00:00:00</ExtendedCreationDate>
	<ActualsInSync>0</ActualsInSync>
	<RemoveFileProperties>0</RemoveFileProperties>
	<AdminProject>0</AdminProject>
	<UpdateManuallyScheduledTasksWhenEditingLinks>1</UpdateManuallyScheduledTasksWhenEditingLinks>
	<KeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled>0</KeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled>
	<Views>
		<View>
			<Name>Gantt &amp;with Timeline</Name>
		</View>
		<View>
			<Name>&amp;Gantt Chart</Name>
			<IsCustomized>true</IsCustomized>
		</View>
		<View>
			<Name>Time&amp;line</Name>
			<IsCustomized>true</IsCustomized>
		</View>
		<View>
			<Name>Resource &amp;Sheet</Name>
			<IsCustomized>true</IsCustomized>
		</View>
	</Views>
	<Filters>
		<Filter>
			<Name>&amp;All Tasks</Name>
		</Filter>
		<Filter>
			<Name>&amp;All Resources</Name>
		</Filter>
	</Filters>
	<Groups>
		<Group>
			<Name>&amp;No Group</Name>
		</Group>
		<Group>
			<Name>&amp;No Group</Name>
		</Group>
	</Groups>
	<Tables>
		<Table>
			<Name>&amp;Entry</Name>
			<IsCustomized>true</IsCustomized>
		</Table>
		<Table>
			<Name>&amp;Entry</Name>
			<IsCustomized>true</IsCustomized>
		</Table>
	</Tables>
	<Maps/>
	<Reports/>
	<Drawings/>
	<DataLinks/>
	<VBAProjects>
		<VBAProject>
			<Name>ThisProject</Name>
			<IsCustomized>true</IsCustomized>
		</VBAProject>
	</VBAProjects>
	<OutlineCodes/>
	<WBSMasks/>
	<ExtendedAttributes>
		<ExtendedAttribute>
			<FieldID>188743731</FieldID>
			<FieldName>Text1</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743734</FieldID>
			<FieldName>Text2</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743737</FieldID>
			<FieldName>Text3</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743740</FieldID>
			<FieldName>Text4</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743743</FieldID>
			<FieldName>Text5</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743746</FieldID>
			<FieldName>Text6</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743747</FieldID>
			<FieldName>Text7</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743748</FieldID>
			<FieldName>Text8</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743749</FieldID>
			<FieldName>Text9</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743750</FieldID>
			<FieldName>Text10</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743997</FieldID>
			<FieldName>Text11</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743998</FieldID>
			<FieldName>Text12</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743999</FieldID>
			<FieldName>Text13</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188744000</FieldID>
			<FieldName>Text14</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188744001</FieldID>
			<FieldName>Text15</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188744002</FieldID>
			<FieldName>Text16</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188744003</FieldID>
			<FieldName>Text17</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188744004</FieldID>
			<FieldName>Text18</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188744005</FieldID>
			<FieldName>Text19</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188744006</FieldID>
			<FieldName>Text20</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188744007</FieldID>
			<FieldName>Text21</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188744008</FieldID>
			<FieldName>Text22</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188744009</FieldID>
			<FieldName>Text23</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188744010</FieldID>
			<FieldName>Text24</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188744011</FieldID>
			<FieldName>Text25</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188744012</FieldID>
			<FieldName>Text26</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188744013</FieldID>
			<FieldName>Text27</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188744014</FieldID>
			<FieldName>Text28</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188744015</FieldID>
			<FieldName>Text29</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188744016</FieldID>
			<FieldName>Text30</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205520904</FieldID>
			<FieldName>Text1</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852632</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205520905</FieldID>
			<FieldName>Text2</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852633</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205520926</FieldID>
			<FieldName>Text3</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852634</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205520927</FieldID>
			<FieldName>Text4</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852635</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205520928</FieldID>
			<FieldName>Text5</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852636</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205520993</FieldID>
			<FieldName>Text6</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852637</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205520994</FieldID>
			<FieldName>Text7</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852638</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205520995</FieldID>
			<FieldName>Text8</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852639</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205520996</FieldID>
			<FieldName>Text9</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852640</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205520997</FieldID>
			<FieldName>Text10</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852641</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521121</FieldID>
			<FieldName>Text11</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852762</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521122</FieldID>
			<FieldName>Text12</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852763</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521123</FieldID>
			<FieldName>Text13</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852764</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521124</FieldID>
			<FieldName>Text14</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852765</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521125</FieldID>
			<FieldName>Text15</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852766</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521126</FieldID>
			<FieldName>Text16</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852767</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521127</FieldID>
			<FieldName>Text17</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852768</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521128</FieldID>
			<FieldName>Text18</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852769</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521129</FieldID>
			<FieldName>Text19</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852770</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521130</FieldID>
			<FieldName>Text20</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852771</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521131</FieldID>
			<FieldName>Text21</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852772</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521132</FieldID>
			<FieldName>Text22</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852773</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521133</FieldID>
			<FieldName>Text23</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852774</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521134</FieldID>
			<FieldName>Text24</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852775</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521135</FieldID>
			<FieldName>Text25</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852776</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521136</FieldID>
			<FieldName>Text26</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852777</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521137</FieldID>
			<FieldName>Text27</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852778</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521138</FieldID>
			<FieldName>Text28</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852779</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521139</FieldID>
			<FieldName>Text29</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852780</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>205521140</FieldID>
			<FieldName>Text30</FieldName>
			<Guid>00000000-0000-0000-0000-000000000000</Guid>
			<SecondaryPID>255852781</SecondaryPID>
			<SecondaryGuid>00000000-0000-0000-0000-000000000000</SecondaryGuid>
		</ExtendedAttribute>
	</ExtendedAttributes>
	<Calendars>
		<Calendar>
			<UID>1</UID>
			<GUID>16F4FFF7-D8D2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Standard</Name>
			<IsBaseCalendar>1</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>0</BaseCalendarUID>
			<WeekDays>
				<WeekDay>
					<DayType>1</DayType>
					<DayWorking>0</DayWorking>
				</WeekDay>
				<WeekDay>
					<DayType>2</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>3</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>4</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>5</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>6</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>7</DayType>
					<DayWorking>0</DayWorking>
				</WeekDay>
			</WeekDays>
		</Calendar>
		<Calendar>
			<UID>3</UID>
			<GUID>89DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text1</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>4</UID>
			<GUID>8BDAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text2</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>5</UID>
			<GUID>8DDAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text3</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>6</UID>
			<GUID>8FDAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text4</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>7</UID>
			<GUID>91DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text5</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>8</UID>
			<GUID>93DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text6</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>9</UID>
			<GUID>95DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text7</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>10</UID>
			<GUID>97DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text8</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>11</UID>
			<GUID>99DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text9</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>12</UID>
			<GUID>9BDAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text10</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>13</UID>
			<GUID>9DDAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text11</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>14</UID>
			<GUID>9FDAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text12</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>15</UID>
			<GUID>A1DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text13</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>16</UID>
			<GUID>A3DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text14</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>17</UID>
			<GUID>A5DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text15</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>18</UID>
			<GUID>A7DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text16</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>19</UID>
			<GUID>A9DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text17</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>20</UID>
			<GUID>ABDAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text18</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>21</UID>
			<GUID>ADDAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text19</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>22</UID>
			<GUID>AFDAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text20</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>23</UID>
			<GUID>B1DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text21</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>24</UID>
			<GUID>B3DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text22</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>25</UID>
			<GUID>B5DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text23</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>26</UID>
			<GUID>B7DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text24</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>27</UID>
			<GUID>B9DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text25</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>28</UID>
			<GUID>BBDAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text26</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>29</UID>
			<GUID>BDDAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text27</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>30</UID>
			<GUID>BFDAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text28</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>31</UID>
			<GUID>C1DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text29</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
		<Calendar>
			<UID>32</UID>
			<GUID>C3DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<Name>Text30</Name>
			<IsBaseCalendar>0</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>1</BaseCalendarUID>
		</Calendar>
	</Calendars>
	<Tasks>
		<Task>
			<UID>0</UID>
			<GUID>77DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>0</ID>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<CreateDate>2018-10-18T14:35:00</CreateDate>
			<WBS>0</WBS>
			<OutlineNumber>0</OutlineNumber>
			<OutlineLevel>0</OutlineLevel>
			<Priority>500</Priority>
			<Start>2018-10-18T08:00:00</Start>
			<Finish>2018-10-18T08:00:00</Finish>
			<Duration>PT0H0M0S</Duration>
			<ManualStart>2018-10-18T08:00:00</ManualStart>
			<ManualFinish>2018-10-18T08:00:00</ManualFinish>
			<ManualDuration>PT0H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<FreeformDurationFormat>39</FreeformDurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>1</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2018-10-18T08:00:00</EarlyStart>
			<EarlyFinish>2018-10-18T08:00:00</EarlyFinish>
			<LateStart>2018-10-18T08:00:00</LateStart>
			<LateFinish>2018-10-18T08:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT0H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>0</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
	</Tasks>
	<Resources>
		<Resource>
			<UID>0</UID>
			<GUID>A0CB8B7E-2A8C-436D-0000-0000000000FF</GUID>
			<ID>0</ID>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>2</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:35:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
		<Resource>
			<UID>1</UID>
			<GUID>89DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>1</ID>
			<Name>Text1</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>3</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:35:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205520904</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>2</UID>
			<GUID>8BDAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>2</ID>
			<Name>Text2</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>4</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:35:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205520905</FieldID>
				<Value>2</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>3</UID>
			<GUID>8DDAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>3</ID>
			<Name>Text3</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>5</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:35:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205520926</FieldID>
				<Value>3</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>4</UID>
			<GUID>8FDAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>4</ID>
			<Name>Text4</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>6</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205520927</FieldID>
				<Value>4</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>5</UID>
			<GUID>91DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>5</ID>
			<Name>Text5</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>7</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205520928</FieldID>
				<Value>5</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>6</UID>
			<GUID>93DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>6</ID>
			<Name>Text6</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>8</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205520993</FieldID>
				<Value>6</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>7</UID>
			<GUID>95DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>7</ID>
			<Name>Text7</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>9</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205520994</FieldID>
				<Value>7</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>8</UID>
			<GUID>97DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>8</ID>
			<Name>Text8</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>10</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205520995</FieldID>
				<Value>8</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>9</UID>
			<GUID>99DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>9</ID>
			<Name>Text9</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>11</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205520996</FieldID>
				<Value>9</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>10</UID>
			<GUID>9BDAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>10</ID>
			<Name>Text10</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>12</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205520997</FieldID>
				<Value>10</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>11</UID>
			<GUID>9DDAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>11</ID>
			<Name>Text11</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>13</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521121</FieldID>
				<Value>11</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>12</UID>
			<GUID>9FDAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>12</ID>
			<Name>Text12</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>14</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521122</FieldID>
				<Value>12</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>13</UID>
			<GUID>A1DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>13</ID>
			<Name>Text13</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>15</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521123</FieldID>
				<Value>13</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>14</UID>
			<GUID>A3DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>14</ID>
			<Name>Text14</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>16</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521124</FieldID>
				<Value>14</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>15</UID>
			<GUID>A5DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>15</ID>
			<Name>Text15</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>17</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521125</FieldID>
				<Value>15</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>16</UID>
			<GUID>A7DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>16</ID>
			<Name>Text16</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>18</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521126</FieldID>
				<Value>16</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>17</UID>
			<GUID>A9DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>17</ID>
			<Name>Text17</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>19</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521127</FieldID>
				<Value>17</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>18</UID>
			<GUID>ABDAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>18</ID>
			<Name>Text18</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>20</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521128</FieldID>
				<Value>18</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>19</UID>
			<GUID>ADDAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>19</ID>
			<Name>Text19</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>21</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521129</FieldID>
				<Value>19</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>20</UID>
			<GUID>AFDAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>20</ID>
			<Name>Text20</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>22</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521130</FieldID>
				<Value>20</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>21</UID>
			<GUID>B1DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>21</ID>
			<Name>Text21</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>23</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521131</FieldID>
				<Value>21</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>22</UID>
			<GUID>B3DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>22</ID>
			<Name>Text22</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>24</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521132</FieldID>
				<Value>22</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>23</UID>
			<GUID>B5DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>23</ID>
			<Name>Text23</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>25</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521133</FieldID>
				<Value>23</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>24</UID>
			<GUID>B7DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>24</ID>
			<Name>Text24</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>26</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521134</FieldID>
				<Value>24</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>25</UID>
			<GUID>B9DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>25</ID>
			<Name>Text25</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>27</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521135</FieldID>
				<Value>25</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>26</UID>
			<GUID>BBDAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>26</ID>
			<Name>Text26</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>28</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521136</FieldID>
				<Value>26</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>27</UID>
			<GUID>BDDAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>27</ID>
			<Name>Text27</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>29</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521137</FieldID>
				<Value>27</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>28</UID>
			<GUID>BFDAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>28</ID>
			<Name>Text28</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>30</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521138</FieldID>
				<Value>28</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>29</UID>
			<GUID>C1DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>29</ID>
			<Name>Text29</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>31</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521139</FieldID>
				<Value>29</Value>
			</ExtendedAttribute>
		</Resource>
		<Resource>
			<UID>30</UID>
			<GUID>C3DAAABE-DAD2-E811-AC3F-4CEDDE6E5B57</GUID>
			<ID>30</ID>
			<Name>Text30</Name>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<Initials>T</Initials>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>32</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2018-10-18T14:36:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
			<ExtendedAttribute>
				<FieldID>205521140</FieldID>
				<Value>30</Value>
			</ExtendedAttribute>
		</Resource>
	</Resources>
	<Assignments/>
</Project>