<?xml version="1.0"?>
<Project xmlns="http://schemas.microsoft.com/project"><Name>resource-type-project2002-mspdi.xml</Name><Title>resource-type-project2002-mpp9</Title><Author>Project User</Author><CreationDate>2018-10-20T10:56:00</CreationDate><LastSaved>2018-10-20T10:56:00</LastSaved><ScheduleFromStart>1</ScheduleFromStart><StartDate>2018-10-20T08:00:00</StartDate><FinishDate>2018-10-20T08:00:00</FinishDate><FYStartDate>1</FYStartDate><CriticalSlackLimit>0</CriticalSlackLimit><CurrencyDigits>2</CurrencyDigits><CurrencySymbol>£</CurrencySymbol><CurrencySymbolPosition>0</CurrencySymbolPosition><CalendarUID>1</CalendarUID><DefaultStartTime>08:00:00</DefaultStartTime><DefaultFinishTime>17:00:00</DefaultFinishTime><MinutesPerDay>480</MinutesPerDay><MinutesPerWeek>2400</MinutesPerWeek><DaysPerMonth>20</DaysPerMonth><DefaultTaskType>0</DefaultTaskType><DefaultFixedCostAccrual>3</DefaultFixedCostAccrual><DefaultStandardRate>0</DefaultStandardRate><DefaultOvertimeRate>0</DefaultOvertimeRate><DurationFormat>7</DurationFormat><WorkFormat>2</WorkFormat><EditableActualCosts>0</EditableActualCosts><HonorConstraints>0</HonorConstraints><InsertedProjectsLikeSummary>1</InsertedProjectsLikeSummary><MultipleCriticalPaths>0</MultipleCriticalPaths><NewTasksEffortDriven>1</NewTasksEffortDriven><NewTasksEstimated>1</NewTasksEstimated><SplitsInProgressTasks>1</SplitsInProgressTasks><SpreadActualCost>0</SpreadActualCost><SpreadPercentComplete>0</SpreadPercentComplete><TaskUpdatesResource>1</TaskUpdatesResource><FiscalYearStart>0</FiscalYearStart><WeekStartDay>1</WeekStartDay><MoveCompletedEndsBack>0</MoveCompletedEndsBack><MoveRemainingStartsBack>0</MoveRemainingStartsBack><MoveRemainingStartsForward>0</MoveRemainingStartsForward><MoveCompletedEndsForward>0</MoveCompletedEndsForward><BaselineForEarnedValue>0</BaselineForEarnedValue><AutoAddNewResourcesAndTasks>1</AutoAddNewResourcesAndTasks><CurrentDate>2018-10-20T08:00:00</CurrentDate><MicrosoftProjectServerURL>1</MicrosoftProjectServerURL><Autolink>1</Autolink><NewTaskStartDate>0</NewTaskStartDate><DefaultTaskEVMethod>0</DefaultTaskEVMethod><ProjectExternallyEdited>0</ProjectExternallyEdited><OutlineCodes/><WBSMasks/><ExtendedAttributes/><Calendars><Calendar><UID>1</UID><Name>Standard</Name><IsBaseCalendar>1</IsBaseCalendar><BaseCalendarUID>-1</BaseCalendarUID><WeekDays><WeekDay><DayType>1</DayType><DayWorking>0</DayWorking></WeekDay><WeekDay><DayType>2</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>3</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>4</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>5</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>6</DayType><DayWorking>1</DayWorking><WorkingTimes><WorkingTime><FromTime>08:00:00</FromTime><ToTime>12:00:00</ToTime></WorkingTime><WorkingTime><FromTime>13:00:00</FromTime><ToTime>17:00:00</ToTime></WorkingTime></WorkingTimes></WeekDay><WeekDay><DayType>7</DayType><DayWorking>0</DayWorking></WeekDay></WeekDays></Calendar><Calendar><UID>3</UID><Name>Cost Resource 1</Name><IsBaseCalendar>0</IsBaseCalendar><BaseCalendarUID>1</BaseCalendarUID></Calendar><Calendar><UID>4</UID><Name>Cost Resource 2</Name><IsBaseCalendar>0</IsBaseCalendar><BaseCalendarUID>1</BaseCalendarUID></Calendar><Calendar><UID>5</UID><Name>Cost Resource 3</Name><IsBaseCalendar>0</IsBaseCalendar><BaseCalendarUID>1</BaseCalendarUID></Calendar><Calendar><UID>6</UID><Name>Cost Resource 4</Name><IsBaseCalendar>0</IsBaseCalendar><BaseCalendarUID>1</BaseCalendarUID></Calendar><Calendar><UID>7</UID><Name>Cost Resource 5</Name><IsBaseCalendar>0</IsBaseCalendar><BaseCalendarUID>1</BaseCalendarUID></Calendar><Calendar><UID>8</UID><Name>Material Resource 1</Name><IsBaseCalendar>0</IsBaseCalendar><BaseCalendarUID>1</BaseCalendarUID></Calendar><Calendar><UID>9</UID><Name>Material Resource 2</Name><IsBaseCalendar>0</IsBaseCalendar><BaseCalendarUID>1</BaseCalendarUID></Calendar><Calendar><UID>10</UID><Name>Material Resource 3</Name><IsBaseCalendar>0</IsBaseCalendar><BaseCalendarUID>1</BaseCalendarUID></Calendar><Calendar><UID>11</UID><Name>Material Resource 4</Name><IsBaseCalendar>0</IsBaseCalendar><BaseCalendarUID>1</BaseCalendarUID></Calendar><Calendar><UID>12</UID><Name>Material Resource 5</Name><IsBaseCalendar>0</IsBaseCalendar><BaseCalendarUID>1</BaseCalendarUID></Calendar><Calendar><UID>13</UID><Name>Work Resource 1</Name><IsBaseCalendar>0</IsBaseCalendar><BaseCalendarUID>1</BaseCalendarUID></Calendar><Calendar><UID>14</UID><Name>Work Resource 2</Name><IsBaseCalendar>0</IsBaseCalendar><BaseCalendarUID>1</BaseCalendarUID></Calendar><Calendar><UID>15</UID><Name>Work Resource 3</Name><IsBaseCalendar>0</IsBaseCalendar><BaseCalendarUID>1</BaseCalendarUID></Calendar><Calendar><UID>16</UID><Name>Work Resource 4</Name><IsBaseCalendar>0</IsBaseCalendar><BaseCalendarUID>1</BaseCalendarUID></Calendar><Calendar><UID>17</UID><Name>Work Resource 5</Name><IsBaseCalendar>0</IsBaseCalendar><BaseCalendarUID>1</BaseCalendarUID></Calendar></Calendars><Tasks><Task><UID>0</UID><ID>0</ID><Name>resource-type-project2002-mpp9</Name><Type>1</Type><IsNull>0</IsNull><CreateDate>2018-10-20T10:56:00</CreateDate><WBS>0</WBS><OutlineNumber>0</OutlineNumber><OutlineLevel>0</OutlineLevel><Priority>500</Priority><Start>2018-10-20T08:00:00</Start><Finish>2018-10-20T08:00:00</Finish><Duration>PT0H0M0S</Duration><DurationFormat>53</DurationFormat><Work>PT0H0M0S</Work><ResumeValid>0</ResumeValid><EffortDriven>0</EffortDriven><Recurring>0</Recurring><OverAllocated>0</OverAllocated><Estimated>1</Estimated><Milestone>0</Milestone><Summary>1</Summary><Critical>1</Critical><IsSubproject>0</IsSubproject><IsSubprojectReadOnly>0</IsSubprojectReadOnly><ExternalTask>0</ExternalTask><EarlyStart>2018-10-20T08:00:00</EarlyStart><EarlyFinish>2018-10-20T08:00:00</EarlyFinish><LateStart>2018-10-20T08:00:00</LateStart><LateFinish>2018-10-20T08:00:00</LateFinish><StartVariance>0</StartVariance><FinishVariance>0</FinishVariance><WorkVariance>0</WorkVariance><FreeSlack>0</FreeSlack><TotalSlack>0</TotalSlack><FixedCost>0</FixedCost><FixedCostAccrual>3</FixedCostAccrual><PercentComplete>0</PercentComplete><PercentWorkComplete>0</PercentWorkComplete><Cost>0</Cost><OvertimeCost>0</OvertimeCost><OvertimeWork>PT0H0M0S</OvertimeWork><ActualDuration>PT0H0M0S</ActualDuration><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><ActualWork>PT0H0M0S</ActualWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RegularWork>PT0H0M0S</RegularWork><RemainingDuration>PT0H0M0S</RemainingDuration><RemainingCost>0</RemainingCost><RemainingWork>PT0H0M0S</RemainingWork><RemainingOvertimeCost>0</RemainingOvertimeCost><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><ACWP>0</ACWP><CV>0</CV><ConstraintType>0</ConstraintType><CalendarUID>-1</CalendarUID><LevelAssignments>1</LevelAssignments><LevelingCanSplit>1</LevelingCanSplit><LevelingDelay>0</LevelingDelay><LevelingDelayFormat>8</LevelingDelayFormat><IgnoreResourceCalendar>0</IgnoreResourceCalendar><HideBar>0</HideBar><Rollup>0</Rollup><BCWS>0</BCWS><BCWP>0</BCWP><PhysicalPercentComplete>0</PhysicalPercentComplete><EarnedValueMethod>0</EarnedValueMethod></Task></Tasks><Resources><Resource><UID>0</UID><ID>0</ID><Type>1</Type><IsNull>0</IsNull><WorkGroup>0</WorkGroup><MaxUnits>1</MaxUnits><PeakUnits>0</PeakUnits><OverAllocated>0</OverAllocated><CanLevel>1</CanLevel><AccrueAt>3</AccrueAt><Work>PT0H0M0S</Work><RegularWork>PT0H0M0S</RegularWork><OvertimeWork>PT0H0M0S</OvertimeWork><ActualWork>PT0H0M0S</ActualWork><RemainingWork>PT0H0M0S</RemainingWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><PercentWorkComplete>0</PercentWorkComplete><StandardRate>0</StandardRate><StandardRateFormat>2</StandardRateFormat><Cost>0</Cost><OvertimeRate>0</OvertimeRate><OvertimeRateFormat>2</OvertimeRateFormat><OvertimeCost>0</OvertimeCost><CostPerUse>0</CostPerUse><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><WorkVariance>0</WorkVariance><CostVariance>0</CostVariance><SV>0</SV><CV>0</CV><ACWP>0</ACWP><CalendarUID>2</CalendarUID><BCWS>0</BCWS><BCWP>0</BCWP><IsGeneric>0</IsGeneric><IsInactive>0</IsInactive></Resource><Resource><UID>1</UID><ID>1</ID><Name>Cost Resource 1</Name><Type>0</Type><IsNull>0</IsNull><Initials>C</Initials><WorkGroup>1</WorkGroup><PeakUnits>0</PeakUnits><OverAllocated>0</OverAllocated><CanLevel>0</CanLevel><AccrueAt>3</AccrueAt><Work>PT0H0M0S</Work><RegularWork>PT0H0M0S</RegularWork><ActualWork>PT0H0M0S</ActualWork><RemainingWork>PT0H0M0S</RemainingWork><PercentWorkComplete>0</PercentWorkComplete><StandardRate>0</StandardRate><StandardRateFormat>8</StandardRateFormat><Cost>0</Cost><OvertimeRateFormat>2</OvertimeRateFormat><CostPerUse>0</CostPerUse><ActualCost>0</ActualCost><RemainingCost>0</RemainingCost><WorkVariance>0</WorkVariance><CostVariance>0</CostVariance><SV>0</SV><CV>0</CV><ACWP>0</ACWP><CalendarUID>3</CalendarUID><BCWS>0</BCWS><BCWP>0</BCWP><IsGeneric>0</IsGeneric><IsInactive>0</IsInactive></Resource><Resource><UID>2</UID><ID>2</ID><Name>Cost Resource 2</Name><Type>0</Type><IsNull>0</IsNull><Initials>C</Initials><WorkGroup>1</WorkGroup><PeakUnits>0</PeakUnits><OverAllocated>0</OverAllocated><CanLevel>0</CanLevel><AccrueAt>3</AccrueAt><Work>PT0H0M0S</Work><RegularWork>PT0H0M0S</RegularWork><ActualWork>PT0H0M0S</ActualWork><RemainingWork>PT0H0M0S</RemainingWork><PercentWorkComplete>0</PercentWorkComplete><StandardRate>0</StandardRate><StandardRateFormat>8</StandardRateFormat><Cost>0</Cost><OvertimeRateFormat>2</OvertimeRateFormat><CostPerUse>0</CostPerUse><ActualCost>0</ActualCost><RemainingCost>0</RemainingCost><WorkVariance>0</WorkVariance><CostVariance>0</CostVariance><SV>0</SV><CV>0</CV><ACWP>0</ACWP><CalendarUID>4</CalendarUID><BCWS>0</BCWS><BCWP>0</BCWP><IsGeneric>0</IsGeneric><IsInactive>0</IsInactive></Resource><Resource><UID>3</UID><ID>3</ID><Name>Cost Resource 3</Name><Type>0</Type><IsNull>0</IsNull><Initials>C</Initials><WorkGroup>1</WorkGroup><PeakUnits>0</PeakUnits><OverAllocated>0</OverAllocated><CanLevel>0</CanLevel><AccrueAt>3</AccrueAt><Work>PT0H0M0S</Work><RegularWork>PT0H0M0S</RegularWork><ActualWork>PT0H0M0S</ActualWork><RemainingWork>PT0H0M0S</RemainingWork><PercentWorkComplete>0</PercentWorkComplete><StandardRate>0</StandardRate><StandardRateFormat>8</StandardRateFormat><Cost>0</Cost><OvertimeRateFormat>2</OvertimeRateFormat><CostPerUse>0</CostPerUse><ActualCost>0</ActualCost><RemainingCost>0</RemainingCost><WorkVariance>0</WorkVariance><CostVariance>0</CostVariance><SV>0</SV><CV>0</CV><ACWP>0</ACWP><CalendarUID>5</CalendarUID><BCWS>0</BCWS><BCWP>0</BCWP><IsGeneric>0</IsGeneric><IsInactive>0</IsInactive></Resource><Resource><UID>4</UID><ID>4</ID><Name>Cost Resource 4</Name><Type>0</Type><IsNull>0</IsNull><Initials>C</Initials><WorkGroup>1</WorkGroup><PeakUnits>0</PeakUnits><OverAllocated>0</OverAllocated><CanLevel>0</CanLevel><AccrueAt>3</AccrueAt><Work>PT0H0M0S</Work><RegularWork>PT0H0M0S</RegularWork><ActualWork>PT0H0M0S</ActualWork><RemainingWork>PT0H0M0S</RemainingWork><PercentWorkComplete>0</PercentWorkComplete><StandardRate>0</StandardRate><StandardRateFormat>8</StandardRateFormat><Cost>0</Cost><OvertimeRateFormat>2</OvertimeRateFormat><CostPerUse>0</CostPerUse><ActualCost>0</ActualCost><RemainingCost>0</RemainingCost><WorkVariance>0</WorkVariance><CostVariance>0</CostVariance><SV>0</SV><CV>0</CV><ACWP>0</ACWP><CalendarUID>6</CalendarUID><BCWS>0</BCWS><BCWP>0</BCWP><IsGeneric>0</IsGeneric><IsInactive>0</IsInactive></Resource><Resource><UID>5</UID><ID>5</ID><Name>Cost Resource 5</Name><Type>0</Type><IsNull>0</IsNull><Initials>C</Initials><WorkGroup>1</WorkGroup><PeakUnits>0</PeakUnits><OverAllocated>0</OverAllocated><CanLevel>0</CanLevel><AccrueAt>3</AccrueAt><Work>PT0H0M0S</Work><RegularWork>PT0H0M0S</RegularWork><ActualWork>PT0H0M0S</ActualWork><RemainingWork>PT0H0M0S</RemainingWork><PercentWorkComplete>0</PercentWorkComplete><StandardRate>0</StandardRate><StandardRateFormat>8</StandardRateFormat><Cost>0</Cost><OvertimeRateFormat>2</OvertimeRateFormat><CostPerUse>0</CostPerUse><ActualCost>0</ActualCost><RemainingCost>0</RemainingCost><WorkVariance>0</WorkVariance><CostVariance>0</CostVariance><SV>0</SV><CV>0</CV><ACWP>0</ACWP><CalendarUID>7</CalendarUID><BCWS>0</BCWS><BCWP>0</BCWP><IsGeneric>0</IsGeneric><IsInactive>0</IsInactive></Resource><Resource><UID>6</UID><ID>6</ID><Name>Material Resource 1</Name><Type>0</Type><IsNull>0</IsNull><Initials>M</Initials><WorkGroup>1</WorkGroup><PeakUnits>0</PeakUnits><OverAllocated>0</OverAllocated><CanLevel>0</CanLevel><AccrueAt>3</AccrueAt><Work>PT0H0M0S</Work><RegularWork>PT0H0M0S</RegularWork><ActualWork>PT0H0M0S</ActualWork><RemainingWork>PT0H0M0S</RemainingWork><PercentWorkComplete>0</PercentWorkComplete><StandardRate>0</StandardRate><StandardRateFormat>8</StandardRateFormat><Cost>0</Cost><OvertimeRateFormat>2</OvertimeRateFormat><CostPerUse>0</CostPerUse><ActualCost>0</ActualCost><RemainingCost>0</RemainingCost><WorkVariance>0</WorkVariance><CostVariance>0</CostVariance><SV>0</SV><CV>0</CV><ACWP>0</ACWP><CalendarUID>8</CalendarUID><BCWS>0</BCWS><BCWP>0</BCWP><IsGeneric>0</IsGeneric><IsInactive>0</IsInactive></Resource><Resource><UID>7</UID><ID>7</ID><Name>Material Resource 2</Name><Type>0</Type><IsNull>0</IsNull><Initials>M</Initials><WorkGroup>1</WorkGroup><PeakUnits>0</PeakUnits><OverAllocated>0</OverAllocated><CanLevel>0</CanLevel><AccrueAt>3</AccrueAt><Work>PT0H0M0S</Work><RegularWork>PT0H0M0S</RegularWork><ActualWork>PT0H0M0S</ActualWork><RemainingWork>PT0H0M0S</RemainingWork><PercentWorkComplete>0</PercentWorkComplete><StandardRate>0</StandardRate><StandardRateFormat>8</StandardRateFormat><Cost>0</Cost><OvertimeRateFormat>2</OvertimeRateFormat><CostPerUse>0</CostPerUse><ActualCost>0</ActualCost><RemainingCost>0</RemainingCost><WorkVariance>0</WorkVariance><CostVariance>0</CostVariance><SV>0</SV><CV>0</CV><ACWP>0</ACWP><CalendarUID>9</CalendarUID><BCWS>0</BCWS><BCWP>0</BCWP><IsGeneric>0</IsGeneric><IsInactive>0</IsInactive></Resource><Resource><UID>8</UID><ID>8</ID><Name>Material Resource 3</Name><Type>0</Type><IsNull>0</IsNull><Initials>M</Initials><WorkGroup>1</WorkGroup><PeakUnits>0</PeakUnits><OverAllocated>0</OverAllocated><CanLevel>0</CanLevel><AccrueAt>3</AccrueAt><Work>PT0H0M0S</Work><RegularWork>PT0H0M0S</RegularWork><ActualWork>PT0H0M0S</ActualWork><RemainingWork>PT0H0M0S</RemainingWork><PercentWorkComplete>0</PercentWorkComplete><StandardRate>0</StandardRate><StandardRateFormat>8</StandardRateFormat><Cost>0</Cost><OvertimeRateFormat>2</OvertimeRateFormat><CostPerUse>0</CostPerUse><ActualCost>0</ActualCost><RemainingCost>0</RemainingCost><WorkVariance>0</WorkVariance><CostVariance>0</CostVariance><SV>0</SV><CV>0</CV><ACWP>0</ACWP><CalendarUID>10</CalendarUID><BCWS>0</BCWS><BCWP>0</BCWP><IsGeneric>0</IsGeneric><IsInactive>0</IsInactive></Resource><Resource><UID>9</UID><ID>9</ID><Name>Material Resource 4</Name><Type>0</Type><IsNull>0</IsNull><Initials>M</Initials><WorkGroup>1</WorkGroup><PeakUnits>0</PeakUnits><OverAllocated>0</OverAllocated><CanLevel>0</CanLevel><AccrueAt>3</AccrueAt><Work>PT0H0M0S</Work><RegularWork>PT0H0M0S</RegularWork><ActualWork>PT0H0M0S</ActualWork><RemainingWork>PT0H0M0S</RemainingWork><PercentWorkComplete>0</PercentWorkComplete><StandardRate>0</StandardRate><StandardRateFormat>8</StandardRateFormat><Cost>0</Cost><OvertimeRateFormat>2</OvertimeRateFormat><CostPerUse>0</CostPerUse><ActualCost>0</ActualCost><RemainingCost>0</RemainingCost><WorkVariance>0</WorkVariance><CostVariance>0</CostVariance><SV>0</SV><CV>0</CV><ACWP>0</ACWP><CalendarUID>11</CalendarUID><BCWS>0</BCWS><BCWP>0</BCWP><IsGeneric>0</IsGeneric><IsInactive>0</IsInactive></Resource><Resource><UID>10</UID><ID>10</ID><Name>Material Resource 5</Name><Type>0</Type><IsNull>0</IsNull><Initials>M</Initials><WorkGroup>1</WorkGroup><PeakUnits>0</PeakUnits><OverAllocated>0</OverAllocated><CanLevel>0</CanLevel><AccrueAt>3</AccrueAt><Work>PT0H0M0S</Work><RegularWork>PT0H0M0S</RegularWork><ActualWork>PT0H0M0S</ActualWork><RemainingWork>PT0H0M0S</RemainingWork><PercentWorkComplete>0</PercentWorkComplete><StandardRate>0</StandardRate><StandardRateFormat>8</StandardRateFormat><Cost>0</Cost><OvertimeRateFormat>2</OvertimeRateFormat><CostPerUse>0</CostPerUse><ActualCost>0</ActualCost><RemainingCost>0</RemainingCost><WorkVariance>0</WorkVariance><CostVariance>0</CostVariance><SV>0</SV><CV>0</CV><ACWP>0</ACWP><CalendarUID>12</CalendarUID><BCWS>0</BCWS><BCWP>0</BCWP><IsGeneric>0</IsGeneric><IsInactive>0</IsInactive></Resource><Resource><UID>11</UID><ID>11</ID><Name>Work Resource 1</Name><Type>1</Type><IsNull>0</IsNull><Initials>W</Initials><WorkGroup>0</WorkGroup><MaxUnits>1</MaxUnits><PeakUnits>0</PeakUnits><OverAllocated>0</OverAllocated><CanLevel>1</CanLevel><AccrueAt>3</AccrueAt><Work>PT0H0M0S</Work><RegularWork>PT0H0M0S</RegularWork><OvertimeWork>PT0H0M0S</OvertimeWork><ActualWork>PT0H0M0S</ActualWork><RemainingWork>PT0H0M0S</RemainingWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><PercentWorkComplete>0</PercentWorkComplete><StandardRate>0</StandardRate><StandardRateFormat>2</StandardRateFormat><Cost>0</Cost><OvertimeRate>0</OvertimeRate><OvertimeRateFormat>2</OvertimeRateFormat><OvertimeCost>0</OvertimeCost><CostPerUse>0</CostPerUse><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><WorkVariance>0</WorkVariance><CostVariance>0</CostVariance><SV>0</SV><CV>0</CV><ACWP>0</ACWP><CalendarUID>13</CalendarUID><BCWS>0</BCWS><BCWP>0</BCWP><IsGeneric>0</IsGeneric><IsInactive>0</IsInactive></Resource><Resource><UID>12</UID><ID>12</ID><Name>Work Resource 2</Name><Type>1</Type><IsNull>0</IsNull><Initials>W</Initials><WorkGroup>0</WorkGroup><MaxUnits>1</MaxUnits><PeakUnits>0</PeakUnits><OverAllocated>0</OverAllocated><CanLevel>1</CanLevel><AccrueAt>3</AccrueAt><Work>PT0H0M0S</Work><RegularWork>PT0H0M0S</RegularWork><OvertimeWork>PT0H0M0S</OvertimeWork><ActualWork>PT0H0M0S</ActualWork><RemainingWork>PT0H0M0S</RemainingWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><PercentWorkComplete>0</PercentWorkComplete><StandardRate>0</StandardRate><StandardRateFormat>2</StandardRateFormat><Cost>0</Cost><OvertimeRate>0</OvertimeRate><OvertimeRateFormat>2</OvertimeRateFormat><OvertimeCost>0</OvertimeCost><CostPerUse>0</CostPerUse><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><WorkVariance>0</WorkVariance><CostVariance>0</CostVariance><SV>0</SV><CV>0</CV><ACWP>0</ACWP><CalendarUID>14</CalendarUID><BCWS>0</BCWS><BCWP>0</BCWP><IsGeneric>0</IsGeneric><IsInactive>0</IsInactive></Resource><Resource><UID>13</UID><ID>13</ID><Name>Work Resource 3</Name><Type>1</Type><IsNull>0</IsNull><Initials>W</Initials><WorkGroup>0</WorkGroup><MaxUnits>1</MaxUnits><PeakUnits>0</PeakUnits><OverAllocated>0</OverAllocated><CanLevel>1</CanLevel><AccrueAt>3</AccrueAt><Work>PT0H0M0S</Work><RegularWork>PT0H0M0S</RegularWork><OvertimeWork>PT0H0M0S</OvertimeWork><ActualWork>PT0H0M0S</ActualWork><RemainingWork>PT0H0M0S</RemainingWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><PercentWorkComplete>0</PercentWorkComplete><StandardRate>0</StandardRate><StandardRateFormat>2</StandardRateFormat><Cost>0</Cost><OvertimeRate>0</OvertimeRate><OvertimeRateFormat>2</OvertimeRateFormat><OvertimeCost>0</OvertimeCost><CostPerUse>0</CostPerUse><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><WorkVariance>0</WorkVariance><CostVariance>0</CostVariance><SV>0</SV><CV>0</CV><ACWP>0</ACWP><CalendarUID>15</CalendarUID><BCWS>0</BCWS><BCWP>0</BCWP><IsGeneric>0</IsGeneric><IsInactive>0</IsInactive></Resource><Resource><UID>14</UID><ID>14</ID><Name>Work Resource 4</Name><Type>1</Type><IsNull>0</IsNull><Initials>W</Initials><WorkGroup>0</WorkGroup><MaxUnits>1</MaxUnits><PeakUnits>0</PeakUnits><OverAllocated>0</OverAllocated><CanLevel>1</CanLevel><AccrueAt>3</AccrueAt><Work>PT0H0M0S</Work><RegularWork>PT0H0M0S</RegularWork><OvertimeWork>PT0H0M0S</OvertimeWork><ActualWork>PT0H0M0S</ActualWork><RemainingWork>PT0H0M0S</RemainingWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><PercentWorkComplete>0</PercentWorkComplete><StandardRate>0</StandardRate><StandardRateFormat>2</StandardRateFormat><Cost>0</Cost><OvertimeRate>0</OvertimeRate><OvertimeRateFormat>2</OvertimeRateFormat><OvertimeCost>0</OvertimeCost><CostPerUse>0</CostPerUse><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><WorkVariance>0</WorkVariance><CostVariance>0</CostVariance><SV>0</SV><CV>0</CV><ACWP>0</ACWP><CalendarUID>16</CalendarUID><BCWS>0</BCWS><BCWP>0</BCWP><IsGeneric>0</IsGeneric><IsInactive>0</IsInactive></Resource><Resource><UID>15</UID><ID>15</ID><Name>Work Resource 5</Name><Type>1</Type><IsNull>0</IsNull><Initials>W</Initials><WorkGroup>0</WorkGroup><MaxUnits>1</MaxUnits><PeakUnits>0</PeakUnits><OverAllocated>0</OverAllocated><CanLevel>1</CanLevel><AccrueAt>3</AccrueAt><Work>PT0H0M0S</Work><RegularWork>PT0H0M0S</RegularWork><OvertimeWork>PT0H0M0S</OvertimeWork><ActualWork>PT0H0M0S</ActualWork><RemainingWork>PT0H0M0S</RemainingWork><ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork><RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork><PercentWorkComplete>0</PercentWorkComplete><StandardRate>0</StandardRate><StandardRateFormat>2</StandardRateFormat><Cost>0</Cost><OvertimeRate>0</OvertimeRate><OvertimeRateFormat>2</OvertimeRateFormat><OvertimeCost>0</OvertimeCost><CostPerUse>0</CostPerUse><ActualCost>0</ActualCost><ActualOvertimeCost>0</ActualOvertimeCost><RemainingCost>0</RemainingCost><RemainingOvertimeCost>0</RemainingOvertimeCost><WorkVariance>0</WorkVariance><CostVariance>0</CostVariance><SV>0</SV><CV>0</CV><ACWP>0</ACWP><CalendarUID>17</CalendarUID><BCWS>0</BCWS><BCWP>0</BCWP><IsGeneric>0</IsGeneric><IsInactive>0</IsInactive></Resource></Resources><Assignments/></Project>
