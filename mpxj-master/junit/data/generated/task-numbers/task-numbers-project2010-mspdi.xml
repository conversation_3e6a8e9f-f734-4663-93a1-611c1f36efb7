<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Project xmlns="http://schemas.microsoft.com/project">
	<SaveVersion>14</SaveVersion>
	<Name>task-numbers-project2010-mspdi.xml</Name>
	<Author>Project User</Author>
	<CreationDate>2014-10-17T08:00:00</CreationDate>
	<LastSaved>2014-10-17T19:37:00</LastSaved>
	<ScheduleFromStart>1</ScheduleFromStart>
	<StartDate>2014-10-17T08:00:00</StartDate>
	<FinishDate>2014-10-17T17:00:00</FinishDate>
	<FYStartDate>1</FYStartDate>
	<CriticalSlackLimit>0</CriticalSlackLimit>
	<CurrencyDigits>2</CurrencyDigits>
	<CurrencySymbol>£</CurrencySymbol>
	<CurrencyCode>GBP</CurrencyCode>
	<CurrencySymbolPosition>0</CurrencySymbolPosition>
	<CalendarUID>1</CalendarUID>
	<DefaultStartTime>08:00:00</DefaultStartTime>
	<DefaultFinishTime>17:00:00</DefaultFinishTime>
	<MinutesPerDay>480</MinutesPerDay>
	<MinutesPerWeek>2400</MinutesPerWeek>
	<DaysPerMonth>20</DaysPerMonth>
	<DefaultTaskType>0</DefaultTaskType>
	<DefaultFixedCostAccrual>3</DefaultFixedCostAccrual>
	<DefaultStandardRate>0</DefaultStandardRate>
	<DefaultOvertimeRate>0</DefaultOvertimeRate>
	<DurationFormat>7</DurationFormat>
	<WorkFormat>2</WorkFormat>
	<EditableActualCosts>0</EditableActualCosts>
	<HonorConstraints>0</HonorConstraints>
	<InsertedProjectsLikeSummary>1</InsertedProjectsLikeSummary>
	<MultipleCriticalPaths>0</MultipleCriticalPaths>
	<NewTasksEffortDriven>0</NewTasksEffortDriven>
	<NewTasksEstimated>1</NewTasksEstimated>
	<SplitsInProgressTasks>1</SplitsInProgressTasks>
	<SpreadActualCost>0</SpreadActualCost>
	<SpreadPercentComplete>0</SpreadPercentComplete>
	<TaskUpdatesResource>1</TaskUpdatesResource>
	<FiscalYearStart>0</FiscalYearStart>
	<WeekStartDay>1</WeekStartDay>
	<MoveCompletedEndsBack>0</MoveCompletedEndsBack>
	<MoveRemainingStartsBack>0</MoveRemainingStartsBack>
	<MoveRemainingStartsForward>0</MoveRemainingStartsForward>
	<MoveCompletedEndsForward>0</MoveCompletedEndsForward>
	<BaselineForEarnedValue>0</BaselineForEarnedValue>
	<AutoAddNewResourcesAndTasks>1</AutoAddNewResourcesAndTasks>
	<CurrentDate>2014-10-17T08:00:00</CurrentDate>
	<MicrosoftProjectServerURL>1</MicrosoftProjectServerURL>
	<Autolink>0</Autolink>
	<NewTaskStartDate>0</NewTaskStartDate>
	<NewTasksAreManual>0</NewTasksAreManual>
	<DefaultTaskEVMethod>0</DefaultTaskEVMethod>
	<ProjectExternallyEdited>0</ProjectExternallyEdited>
	<ExtendedCreationDate>1984-01-01T00:00:00</ExtendedCreationDate>
	<ActualsInSync>1</ActualsInSync>
	<RemoveFileProperties>0</RemoveFileProperties>
	<AdminProject>0</AdminProject>
	<UpdateManuallyScheduledTasksWhenEditingLinks>1</UpdateManuallyScheduledTasksWhenEditingLinks>
	<KeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled>0</KeepTaskOnNearestWorkingTimeWhenMadeAutoScheduled>
	<OutlineCodes/>
	<WBSMasks/>
	<ExtendedAttributes>
		<ExtendedAttribute>
			<FieldID>188743767</FieldID>
			<FieldName>Number1</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B400057</Guid>
			<SecondaryPID>255868988</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F40403C</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743768</FieldID>
			<FieldName>Number2</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B400058</Guid>
			<SecondaryPID>255868989</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F40403D</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743769</FieldID>
			<FieldName>Number3</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B400059</Guid>
			<SecondaryPID>255868990</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F40403E</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743770</FieldID>
			<FieldName>Number4</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B40005A</Guid>
			<SecondaryPID>255868991</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F40403F</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743771</FieldID>
			<FieldName>Number5</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B40005B</Guid>
			<SecondaryPID>255868992</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F404040</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743982</FieldID>
			<FieldName>Number6</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B40012E</Guid>
			<SecondaryPID>255868993</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F404041</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743983</FieldID>
			<FieldName>Number7</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B40012F</Guid>
			<SecondaryPID>255868994</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F404042</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743984</FieldID>
			<FieldName>Number8</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B400130</Guid>
			<SecondaryPID>255868995</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F404043</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743985</FieldID>
			<FieldName>Number9</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B400131</Guid>
			<SecondaryPID>255868996</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F404044</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743986</FieldID>
			<FieldName>Number10</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B400132</Guid>
			<SecondaryPID>255868997</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F404045</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743987</FieldID>
			<FieldName>Number11</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B400133</Guid>
			<SecondaryPID>255868998</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F404046</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743988</FieldID>
			<FieldName>Number12</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B400134</Guid>
			<SecondaryPID>255868999</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F404047</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743989</FieldID>
			<FieldName>Number13</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B400135</Guid>
			<SecondaryPID>255869000</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F404048</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743990</FieldID>
			<FieldName>Number14</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B400136</Guid>
			<SecondaryPID>255869001</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F404049</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743991</FieldID>
			<FieldName>Number15</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B400137</Guid>
			<SecondaryPID>255869002</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F40404A</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743992</FieldID>
			<FieldName>Number16</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B400138</Guid>
			<SecondaryPID>255869003</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F40404B</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743993</FieldID>
			<FieldName>Number17</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B400139</Guid>
			<SecondaryPID>255869004</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F40404C</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743994</FieldID>
			<FieldName>Number18</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B40013A</Guid>
			<SecondaryPID>255869005</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F40404D</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743995</FieldID>
			<FieldName>Number19</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B40013B</Guid>
			<SecondaryPID>255869006</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F40404E</SecondaryGuid>
		</ExtendedAttribute>
		<ExtendedAttribute>
			<FieldID>188743996</FieldID>
			<FieldName>Number20</FieldName>
			<Guid>000039B7-8BBE-4CEB-82C4-FA8C0B40013C</Guid>
			<SecondaryPID>255869007</SecondaryPID>
			<SecondaryGuid>000039B7-8BBE-4CEB-82C4-FA8C0F40404F</SecondaryGuid>
		</ExtendedAttribute>
	</ExtendedAttributes>
	<Calendars>
		<Calendar>
			<UID>1</UID>
			<Name>Standard</Name>
			<IsBaseCalendar>1</IsBaseCalendar>
			<IsBaselineCalendar>0</IsBaselineCalendar>
			<BaseCalendarUID>-1</BaseCalendarUID>
			<WeekDays>
				<WeekDay>
					<DayType>1</DayType>
					<DayWorking>0</DayWorking>
				</WeekDay>
				<WeekDay>
					<DayType>2</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>3</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>4</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>5</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>6</DayType>
					<DayWorking>1</DayWorking>
					<WorkingTimes>
						<WorkingTime>
							<FromTime>08:00:00</FromTime>
							<ToTime>12:00:00</ToTime>
						</WorkingTime>
						<WorkingTime>
							<FromTime>13:00:00</FromTime>
							<ToTime>17:00:00</ToTime>
						</WorkingTime>
					</WorkingTimes>
				</WeekDay>
				<WeekDay>
					<DayType>7</DayType>
					<DayWorking>0</DayWorking>
				</WeekDay>
			</WeekDays>
		</Calendar>
	</Calendars>
	<Tasks>
		<Task>
			<UID>0</UID>
			<ID>0</ID>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-10-17T19:37:00</CreateDate>
			<WBS>0</WBS>
			<OutlineNumber>0</OutlineNumber>
			<OutlineLevel>0</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-10-17T08:00:00</Start>
			<Finish>2014-10-17T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-10-17T08:00:00</ManualStart>
			<ManualFinish>2014-10-17T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>1</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-10-17T08:00:00</EarlyStart>
			<EarlyFinish>2014-10-17T17:00:00</EarlyFinish>
			<LateStart>2014-10-17T08:00:00</LateStart>
			<LateFinish>2014-10-17T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>0</IsPublished>
			<CommitmentType>0</CommitmentType>
		</Task>
		<Task>
			<UID>1</UID>
			<ID>1</ID>
			<Name>Number1</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-10-17T19:37:00</CreateDate>
			<WBS>1</WBS>
			<OutlineNumber>1</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-10-17T08:00:00</Start>
			<Finish>2014-10-17T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-10-17T08:00:00</ManualStart>
			<ManualFinish>2014-10-17T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-10-17T08:00:00</EarlyStart>
			<EarlyFinish>2014-10-17T17:00:00</EarlyFinish>
			<LateStart>2014-10-17T08:00:00</LateStart>
			<LateFinish>2014-10-17T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743767</FieldID>
				<Value>1</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>2</UID>
			<ID>2</ID>
			<Name>Number2</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-10-17T19:37:00</CreateDate>
			<WBS>2</WBS>
			<OutlineNumber>2</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-10-17T08:00:00</Start>
			<Finish>2014-10-17T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-10-17T08:00:00</ManualStart>
			<ManualFinish>2014-10-17T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-10-17T08:00:00</EarlyStart>
			<EarlyFinish>2014-10-17T17:00:00</EarlyFinish>
			<LateStart>2014-10-17T08:00:00</LateStart>
			<LateFinish>2014-10-17T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743768</FieldID>
				<Value>2</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>3</UID>
			<ID>3</ID>
			<Name>Number3</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-10-17T19:37:00</CreateDate>
			<WBS>3</WBS>
			<OutlineNumber>3</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-10-17T08:00:00</Start>
			<Finish>2014-10-17T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-10-17T08:00:00</ManualStart>
			<ManualFinish>2014-10-17T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-10-17T08:00:00</EarlyStart>
			<EarlyFinish>2014-10-17T17:00:00</EarlyFinish>
			<LateStart>2014-10-17T08:00:00</LateStart>
			<LateFinish>2014-10-17T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743769</FieldID>
				<Value>3</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>4</UID>
			<ID>4</ID>
			<Name>Number4</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-10-17T19:37:00</CreateDate>
			<WBS>4</WBS>
			<OutlineNumber>4</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-10-17T08:00:00</Start>
			<Finish>2014-10-17T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-10-17T08:00:00</ManualStart>
			<ManualFinish>2014-10-17T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-10-17T08:00:00</EarlyStart>
			<EarlyFinish>2014-10-17T17:00:00</EarlyFinish>
			<LateStart>2014-10-17T08:00:00</LateStart>
			<LateFinish>2014-10-17T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743770</FieldID>
				<Value>4</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>5</UID>
			<ID>5</ID>
			<Name>Number5</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-10-17T19:37:00</CreateDate>
			<WBS>5</WBS>
			<OutlineNumber>5</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-10-17T08:00:00</Start>
			<Finish>2014-10-17T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-10-17T08:00:00</ManualStart>
			<ManualFinish>2014-10-17T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-10-17T08:00:00</EarlyStart>
			<EarlyFinish>2014-10-17T17:00:00</EarlyFinish>
			<LateStart>2014-10-17T08:00:00</LateStart>
			<LateFinish>2014-10-17T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743771</FieldID>
				<Value>5</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>6</UID>
			<ID>6</ID>
			<Name>Number6</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-10-17T19:37:00</CreateDate>
			<WBS>6</WBS>
			<OutlineNumber>6</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-10-17T08:00:00</Start>
			<Finish>2014-10-17T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-10-17T08:00:00</ManualStart>
			<ManualFinish>2014-10-17T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-10-17T08:00:00</EarlyStart>
			<EarlyFinish>2014-10-17T17:00:00</EarlyFinish>
			<LateStart>2014-10-17T08:00:00</LateStart>
			<LateFinish>2014-10-17T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743982</FieldID>
				<Value>6</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>7</UID>
			<ID>7</ID>
			<Name>Number7</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-10-17T19:37:00</CreateDate>
			<WBS>7</WBS>
			<OutlineNumber>7</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-10-17T08:00:00</Start>
			<Finish>2014-10-17T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-10-17T08:00:00</ManualStart>
			<ManualFinish>2014-10-17T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-10-17T08:00:00</EarlyStart>
			<EarlyFinish>2014-10-17T17:00:00</EarlyFinish>
			<LateStart>2014-10-17T08:00:00</LateStart>
			<LateFinish>2014-10-17T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743983</FieldID>
				<Value>7</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>8</UID>
			<ID>8</ID>
			<Name>Number8</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-10-17T19:37:00</CreateDate>
			<WBS>8</WBS>
			<OutlineNumber>8</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-10-17T08:00:00</Start>
			<Finish>2014-10-17T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-10-17T08:00:00</ManualStart>
			<ManualFinish>2014-10-17T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-10-17T08:00:00</EarlyStart>
			<EarlyFinish>2014-10-17T17:00:00</EarlyFinish>
			<LateStart>2014-10-17T08:00:00</LateStart>
			<LateFinish>2014-10-17T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743984</FieldID>
				<Value>8</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>9</UID>
			<ID>9</ID>
			<Name>Number9</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-10-17T19:37:00</CreateDate>
			<WBS>9</WBS>
			<OutlineNumber>9</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-10-17T08:00:00</Start>
			<Finish>2014-10-17T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-10-17T08:00:00</ManualStart>
			<ManualFinish>2014-10-17T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-10-17T08:00:00</EarlyStart>
			<EarlyFinish>2014-10-17T17:00:00</EarlyFinish>
			<LateStart>2014-10-17T08:00:00</LateStart>
			<LateFinish>2014-10-17T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743985</FieldID>
				<Value>9</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>10</UID>
			<ID>10</ID>
			<Name>Number10</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-10-17T19:37:00</CreateDate>
			<WBS>10</WBS>
			<OutlineNumber>10</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-10-17T08:00:00</Start>
			<Finish>2014-10-17T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-10-17T08:00:00</ManualStart>
			<ManualFinish>2014-10-17T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-10-17T08:00:00</EarlyStart>
			<EarlyFinish>2014-10-17T17:00:00</EarlyFinish>
			<LateStart>2014-10-17T08:00:00</LateStart>
			<LateFinish>2014-10-17T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743986</FieldID>
				<Value>10</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>11</UID>
			<ID>11</ID>
			<Name>Number11</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-10-17T19:37:00</CreateDate>
			<WBS>11</WBS>
			<OutlineNumber>11</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-10-17T08:00:00</Start>
			<Finish>2014-10-17T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-10-17T08:00:00</ManualStart>
			<ManualFinish>2014-10-17T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-10-17T08:00:00</EarlyStart>
			<EarlyFinish>2014-10-17T17:00:00</EarlyFinish>
			<LateStart>2014-10-17T08:00:00</LateStart>
			<LateFinish>2014-10-17T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743987</FieldID>
				<Value>11</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>12</UID>
			<ID>12</ID>
			<Name>Number12</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-10-17T19:37:00</CreateDate>
			<WBS>12</WBS>
			<OutlineNumber>12</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-10-17T08:00:00</Start>
			<Finish>2014-10-17T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-10-17T08:00:00</ManualStart>
			<ManualFinish>2014-10-17T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-10-17T08:00:00</EarlyStart>
			<EarlyFinish>2014-10-17T17:00:00</EarlyFinish>
			<LateStart>2014-10-17T08:00:00</LateStart>
			<LateFinish>2014-10-17T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743988</FieldID>
				<Value>12</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>13</UID>
			<ID>13</ID>
			<Name>Number13</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-10-17T19:37:00</CreateDate>
			<WBS>13</WBS>
			<OutlineNumber>13</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-10-17T08:00:00</Start>
			<Finish>2014-10-17T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-10-17T08:00:00</ManualStart>
			<ManualFinish>2014-10-17T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-10-17T08:00:00</EarlyStart>
			<EarlyFinish>2014-10-17T17:00:00</EarlyFinish>
			<LateStart>2014-10-17T08:00:00</LateStart>
			<LateFinish>2014-10-17T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743989</FieldID>
				<Value>13</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>14</UID>
			<ID>14</ID>
			<Name>Number14</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-10-17T19:37:00</CreateDate>
			<WBS>14</WBS>
			<OutlineNumber>14</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-10-17T08:00:00</Start>
			<Finish>2014-10-17T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-10-17T08:00:00</ManualStart>
			<ManualFinish>2014-10-17T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-10-17T08:00:00</EarlyStart>
			<EarlyFinish>2014-10-17T17:00:00</EarlyFinish>
			<LateStart>2014-10-17T08:00:00</LateStart>
			<LateFinish>2014-10-17T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743990</FieldID>
				<Value>14</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>15</UID>
			<ID>15</ID>
			<Name>Number15</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-10-17T19:37:00</CreateDate>
			<WBS>15</WBS>
			<OutlineNumber>15</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-10-17T08:00:00</Start>
			<Finish>2014-10-17T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-10-17T08:00:00</ManualStart>
			<ManualFinish>2014-10-17T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-10-17T08:00:00</EarlyStart>
			<EarlyFinish>2014-10-17T17:00:00</EarlyFinish>
			<LateStart>2014-10-17T08:00:00</LateStart>
			<LateFinish>2014-10-17T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743991</FieldID>
				<Value>15</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>16</UID>
			<ID>16</ID>
			<Name>Number16</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-10-17T19:37:00</CreateDate>
			<WBS>16</WBS>
			<OutlineNumber>16</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-10-17T08:00:00</Start>
			<Finish>2014-10-17T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-10-17T08:00:00</ManualStart>
			<ManualFinish>2014-10-17T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-10-17T08:00:00</EarlyStart>
			<EarlyFinish>2014-10-17T17:00:00</EarlyFinish>
			<LateStart>2014-10-17T08:00:00</LateStart>
			<LateFinish>2014-10-17T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743992</FieldID>
				<Value>16</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>17</UID>
			<ID>17</ID>
			<Name>Number17</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-10-17T19:37:00</CreateDate>
			<WBS>17</WBS>
			<OutlineNumber>17</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-10-17T08:00:00</Start>
			<Finish>2014-10-17T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-10-17T08:00:00</ManualStart>
			<ManualFinish>2014-10-17T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-10-17T08:00:00</EarlyStart>
			<EarlyFinish>2014-10-17T17:00:00</EarlyFinish>
			<LateStart>2014-10-17T08:00:00</LateStart>
			<LateFinish>2014-10-17T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743993</FieldID>
				<Value>17</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>18</UID>
			<ID>18</ID>
			<Name>Number18</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-10-17T19:37:00</CreateDate>
			<WBS>18</WBS>
			<OutlineNumber>18</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-10-17T08:00:00</Start>
			<Finish>2014-10-17T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-10-17T08:00:00</ManualStart>
			<ManualFinish>2014-10-17T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-10-17T08:00:00</EarlyStart>
			<EarlyFinish>2014-10-17T17:00:00</EarlyFinish>
			<LateStart>2014-10-17T08:00:00</LateStart>
			<LateFinish>2014-10-17T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743994</FieldID>
				<Value>18</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>19</UID>
			<ID>19</ID>
			<Name>Number19</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-10-17T19:37:00</CreateDate>
			<WBS>19</WBS>
			<OutlineNumber>19</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-10-17T08:00:00</Start>
			<Finish>2014-10-17T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-10-17T08:00:00</ManualStart>
			<ManualFinish>2014-10-17T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-10-17T08:00:00</EarlyStart>
			<EarlyFinish>2014-10-17T17:00:00</EarlyFinish>
			<LateStart>2014-10-17T08:00:00</LateStart>
			<LateFinish>2014-10-17T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743995</FieldID>
				<Value>19</Value>
			</ExtendedAttribute>
		</Task>
		<Task>
			<UID>20</UID>
			<ID>20</ID>
			<Name>Number20</Name>
			<Active>1</Active>
			<Manual>0</Manual>
			<Type>0</Type>
			<IsNull>0</IsNull>
			<CreateDate>2014-10-17T19:37:00</CreateDate>
			<WBS>20</WBS>
			<OutlineNumber>20</OutlineNumber>
			<OutlineLevel>1</OutlineLevel>
			<Priority>500</Priority>
			<Start>2014-10-17T08:00:00</Start>
			<Finish>2014-10-17T17:00:00</Finish>
			<Duration>PT8H0M0S</Duration>
			<ManualStart>2014-10-17T08:00:00</ManualStart>
			<ManualFinish>2014-10-17T17:00:00</ManualFinish>
			<ManualDuration>PT8H0M0S</ManualDuration>
			<DurationFormat>53</DurationFormat>
			<Work>PT0H0M0S</Work>
			<ResumeValid>0</ResumeValid>
			<EffortDriven>0</EffortDriven>
			<Recurring>0</Recurring>
			<OverAllocated>0</OverAllocated>
			<Estimated>1</Estimated>
			<Milestone>0</Milestone>
			<Summary>0</Summary>
			<DisplayAsSummary>0</DisplayAsSummary>
			<Critical>1</Critical>
			<IsSubproject>0</IsSubproject>
			<IsSubprojectReadOnly>0</IsSubprojectReadOnly>
			<ExternalTask>0</ExternalTask>
			<EarlyStart>2014-10-17T08:00:00</EarlyStart>
			<EarlyFinish>2014-10-17T17:00:00</EarlyFinish>
			<LateStart>2014-10-17T08:00:00</LateStart>
			<LateFinish>2014-10-17T17:00:00</LateFinish>
			<StartVariance>0</StartVariance>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<FreeSlack>0</FreeSlack>
			<TotalSlack>0</TotalSlack>
			<StartSlack>0</StartSlack>
			<FinishSlack>0</FinishSlack>
			<FixedCost>0</FixedCost>
			<FixedCostAccrual>3</FixedCostAccrual>
			<PercentComplete>0</PercentComplete>
			<PercentWorkComplete>0</PercentWorkComplete>
			<Cost>0</Cost>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualDuration>PT0H0M0S</ActualDuration>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualWork>PT0H0M0S</ActualWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RegularWork>PT0H0M0S</RegularWork>
			<RemainingDuration>PT8H0M0S</RemainingDuration>
			<RemainingCost>0</RemainingCost>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<ACWP>0.00</ACWP>
			<CV>0.00</CV>
			<ConstraintType>0</ConstraintType>
			<CalendarUID>-1</CalendarUID>
			<LevelAssignments>1</LevelAssignments>
			<LevelingCanSplit>1</LevelingCanSplit>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>8</LevelingDelayFormat>
			<IgnoreResourceCalendar>0</IgnoreResourceCalendar>
			<HideBar>0</HideBar>
			<Rollup>0</Rollup>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<EarnedValueMethod>0</EarnedValueMethod>
			<IsPublished>1</IsPublished>
			<CommitmentType>0</CommitmentType>
			<ExtendedAttribute>
				<FieldID>188743996</FieldID>
				<Value>20</Value>
			</ExtendedAttribute>
		</Task>
	</Tasks>
	<Resources>
		<Resource>
			<UID>0</UID>
			<ID>0</ID>
			<Type>1</Type>
			<IsNull>0</IsNull>
			<WorkGroup>0</WorkGroup>
			<MaxUnits>1.00</MaxUnits>
			<PeakUnits>0.00</PeakUnits>
			<OverAllocated>0</OverAllocated>
			<CanLevel>1</CanLevel>
			<AccrueAt>3</AccrueAt>
			<Work>PT0H0M0S</Work>
			<RegularWork>PT0H0M0S</RegularWork>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<RemainingWork>PT0H0M0S</RemainingWork>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<PercentWorkComplete>0</PercentWorkComplete>
			<StandardRate>0</StandardRate>
			<StandardRateFormat>2</StandardRateFormat>
			<Cost>0</Cost>
			<OvertimeRate>0</OvertimeRate>
			<OvertimeRateFormat>2</OvertimeRateFormat>
			<OvertimeCost>0</OvertimeCost>
			<CostPerUse>0</CostPerUse>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<WorkVariance>0.00</WorkVariance>
			<CostVariance>0</CostVariance>
			<SV>0.00</SV>
			<CV>0.00</CV>
			<ACWP>0.00</ACWP>
			<CalendarUID>2</CalendarUID>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<IsGeneric>0</IsGeneric>
			<IsInactive>0</IsInactive>
			<IsEnterprise>0</IsEnterprise>
			<BookingType>0</BookingType>
			<CreationDate>2014-10-17T19:37:00</CreationDate>
			<IsCostResource>0</IsCostResource>
			<IsBudget>0</IsBudget>
		</Resource>
	</Resources>
	<Assignments>
		<Assignment>
			<UID>1</UID>
			<TaskUID>1</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-10-17T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-10-17T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-10-17T19:37:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>1</UID>
				<Start>2014-10-17T08:00:00</Start>
				<Finish>2014-10-17T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>2</UID>
			<TaskUID>2</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-10-17T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-10-17T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-10-17T19:37:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>2</UID>
				<Start>2014-10-17T08:00:00</Start>
				<Finish>2014-10-17T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>3</UID>
			<TaskUID>3</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-10-17T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-10-17T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-10-17T19:37:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>3</UID>
				<Start>2014-10-17T08:00:00</Start>
				<Finish>2014-10-17T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>4</UID>
			<TaskUID>4</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-10-17T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-10-17T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-10-17T19:37:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>4</UID>
				<Start>2014-10-17T08:00:00</Start>
				<Finish>2014-10-17T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>5</UID>
			<TaskUID>5</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-10-17T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-10-17T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-10-17T19:37:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>5</UID>
				<Start>2014-10-17T08:00:00</Start>
				<Finish>2014-10-17T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>6</UID>
			<TaskUID>6</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-10-17T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-10-17T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-10-17T19:37:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>6</UID>
				<Start>2014-10-17T08:00:00</Start>
				<Finish>2014-10-17T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>7</UID>
			<TaskUID>7</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-10-17T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-10-17T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-10-17T19:37:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>7</UID>
				<Start>2014-10-17T08:00:00</Start>
				<Finish>2014-10-17T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>8</UID>
			<TaskUID>8</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-10-17T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-10-17T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-10-17T19:37:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>8</UID>
				<Start>2014-10-17T08:00:00</Start>
				<Finish>2014-10-17T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>9</UID>
			<TaskUID>9</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-10-17T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-10-17T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-10-17T19:37:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>9</UID>
				<Start>2014-10-17T08:00:00</Start>
				<Finish>2014-10-17T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>10</UID>
			<TaskUID>10</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-10-17T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-10-17T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-10-17T19:37:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>10</UID>
				<Start>2014-10-17T08:00:00</Start>
				<Finish>2014-10-17T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>11</UID>
			<TaskUID>11</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-10-17T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-10-17T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-10-17T19:37:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>11</UID>
				<Start>2014-10-17T08:00:00</Start>
				<Finish>2014-10-17T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>12</UID>
			<TaskUID>12</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-10-17T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-10-17T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-10-17T19:37:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>12</UID>
				<Start>2014-10-17T08:00:00</Start>
				<Finish>2014-10-17T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>13</UID>
			<TaskUID>13</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-10-17T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-10-17T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-10-17T19:37:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>13</UID>
				<Start>2014-10-17T08:00:00</Start>
				<Finish>2014-10-17T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>14</UID>
			<TaskUID>14</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-10-17T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-10-17T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-10-17T19:37:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>14</UID>
				<Start>2014-10-17T08:00:00</Start>
				<Finish>2014-10-17T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>15</UID>
			<TaskUID>15</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-10-17T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-10-17T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-10-17T19:37:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>15</UID>
				<Start>2014-10-17T08:00:00</Start>
				<Finish>2014-10-17T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>16</UID>
			<TaskUID>16</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-10-17T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-10-17T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-10-17T19:37:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>16</UID>
				<Start>2014-10-17T08:00:00</Start>
				<Finish>2014-10-17T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>17</UID>
			<TaskUID>17</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-10-17T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-10-17T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-10-17T19:37:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>17</UID>
				<Start>2014-10-17T08:00:00</Start>
				<Finish>2014-10-17T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>18</UID>
			<TaskUID>18</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-10-17T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-10-17T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-10-17T19:37:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>18</UID>
				<Start>2014-10-17T08:00:00</Start>
				<Finish>2014-10-17T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>19</UID>
			<TaskUID>19</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-10-17T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-10-17T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-10-17T19:37:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>19</UID>
				<Start>2014-10-17T08:00:00</Start>
				<Finish>2014-10-17T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
		<Assignment>
			<UID>20</UID>
			<TaskUID>20</TaskUID>
			<ResourceUID>-65535</ResourceUID>
			<PercentWorkComplete>0</PercentWorkComplete>
			<ActualCost>0</ActualCost>
			<ActualOvertimeCost>0</ActualOvertimeCost>
			<ActualOvertimeWork>PT0H0M0S</ActualOvertimeWork>
			<ActualWork>PT0H0M0S</ActualWork>
			<ACWP>0.00</ACWP>
			<Confirmed>0</Confirmed>
			<Cost>0</Cost>
			<CostRateTable>0</CostRateTable>
			<RateScale>0</RateScale>
			<CostVariance>0</CostVariance>
			<CV>0.00</CV>
			<Delay>0</Delay>
			<Finish>2014-10-17T17:00:00</Finish>
			<FinishVariance>0</FinishVariance>
			<WorkVariance>0.00</WorkVariance>
			<HasFixedRateUnits>1</HasFixedRateUnits>
			<FixedMaterial>0</FixedMaterial>
			<LevelingDelay>0</LevelingDelay>
			<LevelingDelayFormat>7</LevelingDelayFormat>
			<LinkedFields>0</LinkedFields>
			<Milestone>0</Milestone>
			<Overallocated>0</Overallocated>
			<OvertimeCost>0</OvertimeCost>
			<OvertimeWork>PT0H0M0S</OvertimeWork>
			<RegularWork>PT8H0M0S</RegularWork>
			<RemainingCost>0</RemainingCost>
			<RemainingOvertimeCost>0</RemainingOvertimeCost>
			<RemainingOvertimeWork>PT0H0M0S</RemainingOvertimeWork>
			<RemainingWork>PT8H0M0S</RemainingWork>
			<ResponsePending>0</ResponsePending>
			<Start>2014-10-17T08:00:00</Start>
			<StartVariance>0</StartVariance>
			<Units>1</Units>
			<UpdateNeeded>0</UpdateNeeded>
			<VAC>0.00</VAC>
			<Work>PT8H0M0S</Work>
			<WorkContour>0</WorkContour>
			<BCWS>0.00</BCWS>
			<BCWP>0.00</BCWP>
			<BookingType>0</BookingType>
			<CreationDate>2014-10-17T19:37:00</CreationDate>
			<BudgetCost>0</BudgetCost>
			<BudgetWork>PT0H0M0S</BudgetWork>
			<TimephasedData>
				<Type>1</Type>
				<UID>20</UID>
				<Start>2014-10-17T08:00:00</Start>
				<Finish>2014-10-17T17:00:00</Finish>
				<Unit>2</Unit>
				<Value>PT8H0M0S</Value>
			</TimephasedData>
		</Assignment>
	</Assignments>
</Project>