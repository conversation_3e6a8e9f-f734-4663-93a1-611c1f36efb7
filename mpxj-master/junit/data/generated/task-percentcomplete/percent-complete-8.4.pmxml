<?xml version="1.0" encoding="UTF-8" ?>
<APIBusinessObjects xmlns="http://xmlns.oracle.com/Primavera/P6Professional/V8.4/API/BusinessObjects" 
                 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
                 xsi:schemaLocation="http://xmlns.oracle.com/Primavera/P6Professional/V8.4/API/BusinessObjects 
                                      http://xmlns.oracle.com/Primavera/P6Professional/V8.4/API/p6apibo.xsd">
	<OBS>
		<Description>&lt;html&gt;
  &lt;head&gt;
    
  &lt;/head&gt;

  &lt;body bgcolor=&quot;#ffffff&quot;&gt;
    Enterprise
  &lt;/body&gt;

&lt;/html&gt;</Description>
		<GUID />
		<Name>Enterprise</Name>
		<ObjectId>540</ObjectId>
		<ParentObjectId xsi:nil="true" />
		<SequenceNumber>0</SequenceNumber>
	</OBS>
	<OBS>
		<Description>&lt;html&gt;
  &lt;head&gt;
    
  &lt;/head&gt;

  &lt;body&gt;
    Engineering and Construction
  &lt;/body&gt;

&lt;/html&gt;</Description>
		<GUID>{FEF2263E-4E0B-D611-B004-00062970E589}</GUID>
		<Name>E&amp;C</Name>
		<ObjectId>636</ObjectId>
		<ParentObjectId>540</ParentObjectId>
		<SequenceNumber>0</SequenceNumber>
	</OBS>
	<Calendar>
		<BaseCalendarObjectId xsi:nil="true" />
		<HoursPerDay>8</HoursPerDay>
		<HoursPerMonth>172</HoursPerMonth>
		<HoursPerWeek>40</HoursPerWeek>
		<HoursPerYear>2000</HoursPerYear>
		<IsDefault>1</IsDefault>
		<IsPersonal>0</IsPersonal>
		<Name>Corporate - Standard Full Time</Name>
		<ObjectId>178</ObjectId>
		<ProjectObjectId xsi:nil="true" />
		<Type>Global</Type>
		<StandardWorkWeek>
			<StandardWorkHours>
				<DayOfWeek>Sunday</DayOfWeek>
				<WorkTime xsi:nil="true" />
			</StandardWorkHours>
			<StandardWorkHours>
				<DayOfWeek>Monday</DayOfWeek>
				<WorkTime>
					<Start>08:00:00</Start>
					<Finish>15:59:00</Finish>
				</WorkTime>
			</StandardWorkHours>
			<StandardWorkHours>
				<DayOfWeek>Tuesday</DayOfWeek>
				<WorkTime>
					<Start>08:00:00</Start>
					<Finish>15:59:00</Finish>
				</WorkTime>
			</StandardWorkHours>
			<StandardWorkHours>
				<DayOfWeek>Wednesday</DayOfWeek>
				<WorkTime>
					<Start>08:00:00</Start>
					<Finish>15:59:00</Finish>
				</WorkTime>
			</StandardWorkHours>
			<StandardWorkHours>
				<DayOfWeek>Thursday</DayOfWeek>
				<WorkTime>
					<Start>08:00:00</Start>
					<Finish>15:59:00</Finish>
				</WorkTime>
			</StandardWorkHours>
			<StandardWorkHours>
				<DayOfWeek>Friday</DayOfWeek>
				<WorkTime>
					<Start>08:00:00</Start>
					<Finish>15:59:00</Finish>
				</WorkTime>
			</StandardWorkHours>
			<StandardWorkHours>
				<DayOfWeek>Saturday</DayOfWeek>
				<WorkTime xsi:nil="true" />
			</StandardWorkHours>
		</StandardWorkWeek>
		<HolidayOrExceptions>
			<HolidayOrException>
				<Date>2010-01-01T00:00:00</Date>
				<WorkTime xsi:nil="true" />
			</HolidayOrException>
			<HolidayOrException>
				<Date>2010-05-31T00:00:00</Date>
				<WorkTime xsi:nil="true" />
			</HolidayOrException>
			<HolidayOrException>
				<Date>2010-07-05T00:00:00</Date>
				<WorkTime xsi:nil="true" />
			</HolidayOrException>
			<HolidayOrException>
				<Date>2010-09-06T00:00:00</Date>
				<WorkTime xsi:nil="true" />
			</HolidayOrException>
			<HolidayOrException>
				<Date>2010-11-25T00:00:00</Date>
				<WorkTime xsi:nil="true" />
			</HolidayOrException>
			<HolidayOrException>
				<Date>2010-12-24T00:00:00</Date>
				<WorkTime xsi:nil="true" />
			</HolidayOrException>
			<HolidayOrException>
				<Date>2010-12-31T00:00:00</Date>
				<WorkTime xsi:nil="true" />
			</HolidayOrException>
			<HolidayOrException>
				<Date>2011-05-30T00:00:00</Date>
				<WorkTime xsi:nil="true" />
			</HolidayOrException>
			<HolidayOrException>
				<Date>2011-07-04T00:00:00</Date>
				<WorkTime xsi:nil="true" />
			</HolidayOrException>
			<HolidayOrException>
				<Date>2011-09-05T00:00:00</Date>
				<WorkTime xsi:nil="true" />
			</HolidayOrException>
			<HolidayOrException>
				<Date>2011-11-24T00:00:00</Date>
				<WorkTime xsi:nil="true" />
			</HolidayOrException>
			<HolidayOrException>
				<Date>2011-12-26T00:00:00</Date>
				<WorkTime xsi:nil="true" />
			</HolidayOrException>
			<HolidayOrException>
				<Date>2012-01-02T00:00:00</Date>
				<WorkTime xsi:nil="true" />
			</HolidayOrException>
			<HolidayOrException>
				<Date>2012-05-28T00:00:00</Date>
				<WorkTime xsi:nil="true" />
			</HolidayOrException>
			<HolidayOrException>
				<Date>2012-07-04T00:00:00</Date>
				<WorkTime xsi:nil="true" />
			</HolidayOrException>
			<HolidayOrException>
				<Date>2012-09-03T00:00:00</Date>
				<WorkTime xsi:nil="true" />
			</HolidayOrException>
			<HolidayOrException>
				<Date>2012-11-22T00:00:00</Date>
				<WorkTime xsi:nil="true" />
			</HolidayOrException>
			<HolidayOrException>
				<Date>2012-12-25T00:00:00</Date>
				<WorkTime xsi:nil="true" />
			</HolidayOrException>
			<HolidayOrException>
				<Date>2013-01-01T00:00:00</Date>
				<WorkTime xsi:nil="true" />
			</HolidayOrException>
			<HolidayOrException>
				<Date>2013-05-27T00:00:00</Date>
				<WorkTime xsi:nil="true" />
			</HolidayOrException>
			<HolidayOrException>
				<Date>2013-07-04T00:00:00</Date>
				<WorkTime xsi:nil="true" />
			</HolidayOrException>
			<HolidayOrException>
				<Date>2013-09-02T00:00:00</Date>
				<WorkTime xsi:nil="true" />
			</HolidayOrException>
			<HolidayOrException>
				<Date>2013-11-28T00:00:00</Date>
				<WorkTime xsi:nil="true" />
			</HolidayOrException>
			<HolidayOrException>
				<Date>2013-12-25T00:00:00</Date>
				<WorkTime xsi:nil="true" />
			</HolidayOrException>
		</HolidayOrExceptions>
	</Calendar>
	<Project>
		<ActivityDefaultActivityType>Task Dependent</ActivityDefaultActivityType>
		<ActivityDefaultCalendarObjectId>178</ActivityDefaultCalendarObjectId>
		<ActivityDefaultCostAccountObjectId xsi:nil="true" />
		<ActivityDefaultDurationType>Fixed Duration and Units</ActivityDefaultDurationType>
		<ActivityDefaultPercentCompleteType>Duration</ActivityDefaultPercentCompleteType>
		<ActivityDefaultPricePerUnit>0</ActivityDefaultPricePerUnit>
		<ActivityIdBasedOnSelectedActivity>1</ActivityIdBasedOnSelectedActivity>
		<ActivityIdIncrement>10</ActivityIdIncrement>
		<ActivityIdPrefix>A</ActivityIdPrefix>
		<ActivityIdSuffix>1000</ActivityIdSuffix>
		<ActivityPercentCompleteBasedOnActivitySteps>0</ActivityPercentCompleteBasedOnActivitySteps>
		<AddActualToRemaining>0</AddActualToRemaining>
		<AddedBy>admin</AddedBy>
		<AllowNegativeActualUnitsFlag>0</AllowNegativeActualUnitsFlag>
		<AnnualDiscountRate xsi:nil="true" />
		<AnticipatedFinishDate xsi:nil="true" />
		<AnticipatedStartDate xsi:nil="true" />
		<AssignmentDefaultDrivingFlag>1</AssignmentDefaultDrivingFlag>
		<AssignmentDefaultRateType>Price / Unit</AssignmentDefaultRateType>
		<CheckOutStatus>0</CheckOutStatus>
		<CostQuantityRecalculateFlag>0</CostQuantityRecalculateFlag>
		<CriticalActivityFloatLimit>0</CriticalActivityFloatLimit>
		<CriticalActivityPathType>Critical Float</CriticalActivityPathType>
		<CurrentBaselineProjectObjectId xsi:nil="true" />
		<DataDate>2015-02-24T00:00:00</DataDate>
		<DateAdded>2015-02-24T00:00:00</DateAdded>
		<DefaultPriceTimeUnits>Hour</DefaultPriceTimeUnits>
		<Description />
		<DiscountApplicationPeriod>Month</DiscountApplicationPeriod>
		<EarnedValueComputeType>Activity Percent Complete</EarnedValueComputeType>
		<EarnedValueETCComputeType>PF = 1 / CPI</EarnedValueETCComputeType>
		<EarnedValueETCUserValue>0.88</EarnedValueETCUserValue>
		<EarnedValueUserPercent>0.06</EarnedValueUserPercent>
		<EnableSummarization>1</EnableSummarization>
		<FiscalYearStartMonth>1</FiscalYearStartMonth>
		<GUID>{E3A7E5D9-E03E-4843-B0CB-1C78ED75D819}</GUID>
		<Id>EC00640</Id>
		<IndependentETCLaborUnits xsi:nil="true" />
		<IndependentETCTotalCost xsi:nil="true" />
		<IntegratedType />
		<LastFinancialPeriodObjectId xsi:nil="true" />
		<LevelingPriority>10</LevelingPriority>
		<LinkActualToActualThisPeriod>1</LinkActualToActualThisPeriod>
		<LinkPercentCompleteWithActual>1</LinkPercentCompleteWithActual>
		<LinkPlannedAndAtCompletionFlag>1</LinkPlannedAndAtCompletionFlag>
		<MustFinishByDate xsi:nil="true" />
		<Name>Percent Complete Test</Name>
		<OBSObjectId>636</OBSObjectId>
		<ObjectId>4507</ObjectId>
		<OriginalBudget xsi:nil="true" />
		<OwnerResourceObjectId xsi:nil="true" />
		<ParentEPSObjectId>22417</ParentEPSObjectId>
		<PlannedStartDate>2015-02-24T00:00:00</PlannedStartDate>
		<PrimaryResourcesCanMarkActivitiesAsCompleted>1</PrimaryResourcesCanMarkActivitiesAsCompleted>
		<ProjectForecastStartDate xsi:nil="true" />
		<ResetPlannedToRemainingFlag>0</ResetPlannedToRemainingFlag>
		<ResourceCanBeAssignedToSameActivityMoreThanOnce>1</ResourceCanBeAssignedToSameActivityMoreThanOnce>
		<ResourcesCanAssignThemselvesToActivities>1</ResourcesCanAssignThemselvesToActivities>
		<ResourcesCanEditAssignmentPercentComplete>0</ResourcesCanEditAssignmentPercentComplete>
		<ResourcesCanMarkAssignmentAsCompleted>0</ResourcesCanMarkAssignmentAsCompleted>
		<ResourcesCanViewInactiveActivities>0</ResourcesCanViewInactiveActivities>
		<RiskLevel>Medium</RiskLevel>
		<ScheduledFinishDate xsi:nil="true" />
		<Status>Active</Status>
		<StrategicPriority>500</StrategicPriority>
		<SummarizeToWBSLevel>2</SummarizeToWBSLevel>
		<SummarizedDataDate xsi:nil="true" />
		<SummaryLevel>Assignment Level</SummaryLevel>
		<UseProjectBaselineForEarnedValue>1</UseProjectBaselineForEarnedValue>
		<WBSCodeSeparator>.</WBSCodeSeparator>
		<WBSObjectId>26010</WBSObjectId>
		<WebSiteRootDirectory />
		<WebSiteURL />
		<Activity>
			<ActualDuration>0</ActualDuration>
			<ActualFinishDate xsi:nil="true" />
			<ActualLaborCost>0</ActualLaborCost>
			<ActualLaborUnits>0</ActualLaborUnits>
			<ActualNonLaborCost>0</ActualNonLaborCost>
			<ActualNonLaborUnits>0</ActualNonLaborUnits>
			<ActualStartDate xsi:nil="true" />
			<ActualThisPeriodLaborCost>0</ActualThisPeriodLaborCost>
			<ActualThisPeriodLaborUnits>0</ActualThisPeriodLaborUnits>
			<ActualThisPeriodNonLaborCost>0</ActualThisPeriodNonLaborCost>
			<ActualThisPeriodNonLaborUnits>0</ActualThisPeriodNonLaborUnits>
			<AtCompletionDuration>800</AtCompletionDuration>
			<AtCompletionExpenseCost>0</AtCompletionExpenseCost>
			<AtCompletionLaborCost>0</AtCompletionLaborCost>
			<AtCompletionLaborUnits>0</AtCompletionLaborUnits>
			<AtCompletionNonLaborCost>0</AtCompletionNonLaborCost>
			<AtCompletionNonLaborUnits>0</AtCompletionNonLaborUnits>
			<AutoComputeActuals>0</AutoComputeActuals>
			<CalendarObjectId>178</CalendarObjectId>
			<DurationPercentComplete>0</DurationPercentComplete>
			<DurationType>Fixed Duration and Units</DurationType>
			<ExpectedFinishDate xsi:nil="true" />
			<ExternalEarlyStartDate xsi:nil="true" />
			<ExternalLateFinishDate xsi:nil="true" />
			<Feedback />
			<FinishDate>2015-07-13T16:00:00</FinishDate>
			<GUID>{A1F735F3-1449-2545-9FE0-989A99AB8374}</GUID>
			<Id>A1000</Id>
			<IsNewFeedback>0</IsNewFeedback>
			<LevelingPriority>Normal</LevelingPriority>
			<Name>Duration 0%</Name>
			<NonLaborUnitsPercentComplete>0</NonLaborUnitsPercentComplete>
			<NotesToResources />
			<ObjectId>101717</ObjectId>
			<PercentComplete>0</PercentComplete>
			<PercentCompleteType>Duration</PercentCompleteType>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<PlannedDuration>800</PlannedDuration>
			<PlannedFinishDate>2015-07-13T16:00:00</PlannedFinishDate>
			<PlannedLaborCost>0</PlannedLaborCost>
			<PlannedLaborUnits>0</PlannedLaborUnits>
			<PlannedNonLaborCost>0</PlannedNonLaborCost>
			<PlannedNonLaborUnits>0</PlannedNonLaborUnits>
			<PlannedStartDate>2015-02-24T08:00:00</PlannedStartDate>
			<PrimaryConstraintDate xsi:nil="true" />
			<PrimaryConstraintType />
			<PrimaryResourceObjectId xsi:nil="true" />
			<ProjectObjectId>4507</ProjectObjectId>
			<RemainingDuration>800</RemainingDuration>
			<RemainingEarlyFinishDate>2015-07-13T16:00:00</RemainingEarlyFinishDate>
			<RemainingEarlyStartDate>2015-02-24T08:00:00</RemainingEarlyStartDate>
			<RemainingLaborCost>0</RemainingLaborCost>
			<RemainingLaborUnits>0</RemainingLaborUnits>
			<RemainingLateFinishDate xsi:nil="true" />
			<RemainingLateStartDate xsi:nil="true" />
			<RemainingNonLaborCost>0</RemainingNonLaborCost>
			<RemainingNonLaborUnits>0</RemainingNonLaborUnits>
			<ResumeDate xsi:nil="true" />
			<ReviewStatus>OK</ReviewStatus>
			<SecondaryConstraintDate xsi:nil="true" />
			<SecondaryConstraintType />
			<StartDate>2015-02-24T08:00:00</StartDate>
			<Status>Not Started</Status>
			<SuspendDate xsi:nil="true" />
			<Type>Task Dependent</Type>
			<UnitsPercentComplete>0</UnitsPercentComplete>
			<WBSObjectId xsi:nil="true" />
		</Activity>
		<Activity>
			<ActualDuration>0</ActualDuration>
			<ActualFinishDate xsi:nil="true" />
			<ActualLaborCost>0</ActualLaborCost>
			<ActualLaborUnits>0</ActualLaborUnits>
			<ActualNonLaborCost>0</ActualNonLaborCost>
			<ActualNonLaborUnits>0</ActualNonLaborUnits>
			<ActualStartDate>2015-02-24T08:00:00</ActualStartDate>
			<ActualThisPeriodLaborCost>0</ActualThisPeriodLaborCost>
			<ActualThisPeriodLaborUnits>0</ActualThisPeriodLaborUnits>
			<ActualThisPeriodNonLaborCost>0</ActualThisPeriodNonLaborCost>
			<ActualThisPeriodNonLaborUnits>0</ActualThisPeriodNonLaborUnits>
			<AtCompletionDuration>600</AtCompletionDuration>
			<AtCompletionExpenseCost>0</AtCompletionExpenseCost>
			<AtCompletionLaborCost>0</AtCompletionLaborCost>
			<AtCompletionLaborUnits>0</AtCompletionLaborUnits>
			<AtCompletionNonLaborCost>0</AtCompletionNonLaborCost>
			<AtCompletionNonLaborUnits>0</AtCompletionNonLaborUnits>
			<AutoComputeActuals>0</AutoComputeActuals>
			<CalendarObjectId>178</CalendarObjectId>
			<DurationPercentComplete>0.25</DurationPercentComplete>
			<DurationType>Fixed Duration and Units</DurationType>
			<ExpectedFinishDate xsi:nil="true" />
			<ExternalEarlyStartDate xsi:nil="true" />
			<ExternalLateFinishDate xsi:nil="true" />
			<Feedback />
			<FinishDate>2015-06-08T16:00:00</FinishDate>
			<GUID>{C24D1B49-0DA5-114A-8783-804839A60697}</GUID>
			<Id>A1010</Id>
			<IsNewFeedback>0</IsNewFeedback>
			<LevelingPriority>Normal</LevelingPriority>
			<Name>Duration 25%</Name>
			<NonLaborUnitsPercentComplete>0</NonLaborUnitsPercentComplete>
			<NotesToResources />
			<ObjectId>101718</ObjectId>
			<PercentComplete>0.25</PercentComplete>
			<PercentCompleteType>Duration</PercentCompleteType>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<PlannedDuration>800</PlannedDuration>
			<PlannedFinishDate>2015-07-13T16:00:00</PlannedFinishDate>
			<PlannedLaborCost>0</PlannedLaborCost>
			<PlannedLaborUnits>0</PlannedLaborUnits>
			<PlannedNonLaborCost>0</PlannedNonLaborCost>
			<PlannedNonLaborUnits>0</PlannedNonLaborUnits>
			<PlannedStartDate>2015-02-24T08:00:00</PlannedStartDate>
			<PrimaryConstraintDate xsi:nil="true" />
			<PrimaryConstraintType />
			<PrimaryResourceObjectId xsi:nil="true" />
			<ProjectObjectId>4507</ProjectObjectId>
			<RemainingDuration>600</RemainingDuration>
			<RemainingEarlyFinishDate>2015-06-08T16:00:00</RemainingEarlyFinishDate>
			<RemainingEarlyStartDate>2015-02-24T08:00:00</RemainingEarlyStartDate>
			<RemainingLaborCost>0</RemainingLaborCost>
			<RemainingLaborUnits>0</RemainingLaborUnits>
			<RemainingLateFinishDate xsi:nil="true" />
			<RemainingLateStartDate xsi:nil="true" />
			<RemainingNonLaborCost>0</RemainingNonLaborCost>
			<RemainingNonLaborUnits>0</RemainingNonLaborUnits>
			<ResumeDate xsi:nil="true" />
			<ReviewStatus>OK</ReviewStatus>
			<SecondaryConstraintDate xsi:nil="true" />
			<SecondaryConstraintType />
			<StartDate>2015-02-24T08:00:00</StartDate>
			<Status>In Progress</Status>
			<SuspendDate xsi:nil="true" />
			<Type>Task Dependent</Type>
			<UnitsPercentComplete>0</UnitsPercentComplete>
			<WBSObjectId xsi:nil="true" />
		</Activity>
		<Activity>
			<ActualDuration>0</ActualDuration>
			<ActualFinishDate xsi:nil="true" />
			<ActualLaborCost>0</ActualLaborCost>
			<ActualLaborUnits>0</ActualLaborUnits>
			<ActualNonLaborCost>0</ActualNonLaborCost>
			<ActualNonLaborUnits>0</ActualNonLaborUnits>
			<ActualStartDate>2015-02-24T08:00:00</ActualStartDate>
			<ActualThisPeriodLaborCost>0</ActualThisPeriodLaborCost>
			<ActualThisPeriodLaborUnits>0</ActualThisPeriodLaborUnits>
			<ActualThisPeriodNonLaborCost>0</ActualThisPeriodNonLaborCost>
			<ActualThisPeriodNonLaborUnits>0</ActualThisPeriodNonLaborUnits>
			<AtCompletionDuration>200</AtCompletionDuration>
			<AtCompletionExpenseCost>0</AtCompletionExpenseCost>
			<AtCompletionLaborCost>0</AtCompletionLaborCost>
			<AtCompletionLaborUnits>0</AtCompletionLaborUnits>
			<AtCompletionNonLaborCost>0</AtCompletionNonLaborCost>
			<AtCompletionNonLaborUnits>0</AtCompletionNonLaborUnits>
			<AutoComputeActuals>0</AutoComputeActuals>
			<CalendarObjectId>178</CalendarObjectId>
			<DurationPercentComplete>0.75</DurationPercentComplete>
			<DurationType>Fixed Duration and Units</DurationType>
			<ExpectedFinishDate xsi:nil="true" />
			<ExternalEarlyStartDate xsi:nil="true" />
			<ExternalLateFinishDate xsi:nil="true" />
			<Feedback />
			<FinishDate>2015-03-30T16:00:00</FinishDate>
			<GUID>{B2E2694C-FDBA-B94C-AEA5-F7F5141A8B9A}</GUID>
			<Id>A1020</Id>
			<IsNewFeedback>0</IsNewFeedback>
			<LevelingPriority>Normal</LevelingPriority>
			<Name>Duration 75%</Name>
			<NonLaborUnitsPercentComplete>0</NonLaborUnitsPercentComplete>
			<NotesToResources />
			<ObjectId>101719</ObjectId>
			<PercentComplete>0.75</PercentComplete>
			<PercentCompleteType>Duration</PercentCompleteType>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<PlannedDuration>800</PlannedDuration>
			<PlannedFinishDate>2015-07-13T16:00:00</PlannedFinishDate>
			<PlannedLaborCost>0</PlannedLaborCost>
			<PlannedLaborUnits>0</PlannedLaborUnits>
			<PlannedNonLaborCost>0</PlannedNonLaborCost>
			<PlannedNonLaborUnits>0</PlannedNonLaborUnits>
			<PlannedStartDate>2015-02-24T08:00:00</PlannedStartDate>
			<PrimaryConstraintDate xsi:nil="true" />
			<PrimaryConstraintType />
			<PrimaryResourceObjectId xsi:nil="true" />
			<ProjectObjectId>4507</ProjectObjectId>
			<RemainingDuration>200</RemainingDuration>
			<RemainingEarlyFinishDate>2015-03-30T16:00:00</RemainingEarlyFinishDate>
			<RemainingEarlyStartDate>2015-02-24T08:00:00</RemainingEarlyStartDate>
			<RemainingLaborCost>0</RemainingLaborCost>
			<RemainingLaborUnits>0</RemainingLaborUnits>
			<RemainingLateFinishDate xsi:nil="true" />
			<RemainingLateStartDate xsi:nil="true" />
			<RemainingNonLaborCost>0</RemainingNonLaborCost>
			<RemainingNonLaborUnits>0</RemainingNonLaborUnits>
			<ResumeDate xsi:nil="true" />
			<ReviewStatus>OK</ReviewStatus>
			<SecondaryConstraintDate xsi:nil="true" />
			<SecondaryConstraintType />
			<StartDate>2015-02-24T08:00:00</StartDate>
			<Status>In Progress</Status>
			<SuspendDate xsi:nil="true" />
			<Type>Task Dependent</Type>
			<UnitsPercentComplete>0</UnitsPercentComplete>
			<WBSObjectId xsi:nil="true" />
		</Activity>
		<Activity>
			<ActualDuration>0</ActualDuration>
			<ActualFinishDate xsi:nil="true" />
			<ActualLaborCost>0</ActualLaborCost>
			<ActualLaborUnits>0</ActualLaborUnits>
			<ActualNonLaborCost>0</ActualNonLaborCost>
			<ActualNonLaborUnits>0</ActualNonLaborUnits>
			<ActualStartDate>2015-02-24T08:00:00</ActualStartDate>
			<ActualThisPeriodLaborCost>0</ActualThisPeriodLaborCost>
			<ActualThisPeriodLaborUnits>0</ActualThisPeriodLaborUnits>
			<ActualThisPeriodNonLaborCost>0</ActualThisPeriodNonLaborCost>
			<ActualThisPeriodNonLaborUnits>0</ActualThisPeriodNonLaborUnits>
			<AtCompletionDuration>0</AtCompletionDuration>
			<AtCompletionExpenseCost>0</AtCompletionExpenseCost>
			<AtCompletionLaborCost>0</AtCompletionLaborCost>
			<AtCompletionLaborUnits>0</AtCompletionLaborUnits>
			<AtCompletionNonLaborCost>0</AtCompletionNonLaborCost>
			<AtCompletionNonLaborUnits>0</AtCompletionNonLaborUnits>
			<AutoComputeActuals>0</AutoComputeActuals>
			<CalendarObjectId>178</CalendarObjectId>
			<DurationPercentComplete>1</DurationPercentComplete>
			<DurationType>Fixed Duration and Units</DurationType>
			<ExpectedFinishDate xsi:nil="true" />
			<ExternalEarlyStartDate xsi:nil="true" />
			<ExternalLateFinishDate xsi:nil="true" />
			<Feedback />
			<FinishDate>2015-02-24T08:00:00</FinishDate>
			<GUID>{BC9B549A-618A-BF42-8A73-F27ADAF8F0B0}</GUID>
			<Id>A1030</Id>
			<IsNewFeedback>0</IsNewFeedback>
			<LevelingPriority>Normal</LevelingPriority>
			<Name>Duration 100%</Name>
			<NonLaborUnitsPercentComplete>0</NonLaborUnitsPercentComplete>
			<NotesToResources />
			<ObjectId>101720</ObjectId>
			<PercentComplete>1</PercentComplete>
			<PercentCompleteType>Duration</PercentCompleteType>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<PlannedDuration>800</PlannedDuration>
			<PlannedFinishDate>2015-07-13T16:00:00</PlannedFinishDate>
			<PlannedLaborCost>0</PlannedLaborCost>
			<PlannedLaborUnits>0</PlannedLaborUnits>
			<PlannedNonLaborCost>0</PlannedNonLaborCost>
			<PlannedNonLaborUnits>0</PlannedNonLaborUnits>
			<PlannedStartDate>2015-02-24T08:00:00</PlannedStartDate>
			<PrimaryConstraintDate xsi:nil="true" />
			<PrimaryConstraintType />
			<PrimaryResourceObjectId xsi:nil="true" />
			<ProjectObjectId>4507</ProjectObjectId>
			<RemainingDuration>0</RemainingDuration>
			<RemainingEarlyFinishDate>2015-02-24T08:00:00</RemainingEarlyFinishDate>
			<RemainingEarlyStartDate>2015-02-24T08:00:00</RemainingEarlyStartDate>
			<RemainingLaborCost>0</RemainingLaborCost>
			<RemainingLaborUnits>0</RemainingLaborUnits>
			<RemainingLateFinishDate xsi:nil="true" />
			<RemainingLateStartDate xsi:nil="true" />
			<RemainingNonLaborCost>0</RemainingNonLaborCost>
			<RemainingNonLaborUnits>0</RemainingNonLaborUnits>
			<ResumeDate xsi:nil="true" />
			<ReviewStatus>OK</ReviewStatus>
			<SecondaryConstraintDate xsi:nil="true" />
			<SecondaryConstraintType />
			<StartDate>2015-02-24T08:00:00</StartDate>
			<Status>In Progress</Status>
			<SuspendDate xsi:nil="true" />
			<Type>Task Dependent</Type>
			<UnitsPercentComplete>0</UnitsPercentComplete>
			<WBSObjectId xsi:nil="true" />
		</Activity>
		<Activity>
			<ActualDuration>0</ActualDuration>
			<ActualFinishDate xsi:nil="true" />
			<ActualLaborCost>0</ActualLaborCost>
			<ActualLaborUnits>0</ActualLaborUnits>
			<ActualNonLaborCost>0</ActualNonLaborCost>
			<ActualNonLaborUnits>0</ActualNonLaborUnits>
			<ActualStartDate xsi:nil="true" />
			<ActualThisPeriodLaborCost>0</ActualThisPeriodLaborCost>
			<ActualThisPeriodLaborUnits>0</ActualThisPeriodLaborUnits>
			<ActualThisPeriodNonLaborCost>0</ActualThisPeriodNonLaborCost>
			<ActualThisPeriodNonLaborUnits>0</ActualThisPeriodNonLaborUnits>
			<AtCompletionDuration>800</AtCompletionDuration>
			<AtCompletionExpenseCost>0</AtCompletionExpenseCost>
			<AtCompletionLaborCost>0</AtCompletionLaborCost>
			<AtCompletionLaborUnits>0</AtCompletionLaborUnits>
			<AtCompletionNonLaborCost>0</AtCompletionNonLaborCost>
			<AtCompletionNonLaborUnits>0</AtCompletionNonLaborUnits>
			<AutoComputeActuals>0</AutoComputeActuals>
			<CalendarObjectId>178</CalendarObjectId>
			<DurationPercentComplete>0</DurationPercentComplete>
			<DurationType>Fixed Duration and Units</DurationType>
			<ExpectedFinishDate xsi:nil="true" />
			<ExternalEarlyStartDate xsi:nil="true" />
			<ExternalLateFinishDate xsi:nil="true" />
			<Feedback />
			<FinishDate>2015-07-13T16:00:00</FinishDate>
			<GUID>{1CD09424-B7AC-6047-AA98-F38B43C716B1}</GUID>
			<Id>A1040</Id>
			<IsNewFeedback>0</IsNewFeedback>
			<LevelingPriority>Normal</LevelingPriority>
			<Name>Physical 0%</Name>
			<NonLaborUnitsPercentComplete>0</NonLaborUnitsPercentComplete>
			<NotesToResources />
			<ObjectId>101721</ObjectId>
			<PercentComplete>0</PercentComplete>
			<PercentCompleteType>Physical</PercentCompleteType>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<PlannedDuration>800</PlannedDuration>
			<PlannedFinishDate>2015-07-13T16:00:00</PlannedFinishDate>
			<PlannedLaborCost>0</PlannedLaborCost>
			<PlannedLaborUnits>0</PlannedLaborUnits>
			<PlannedNonLaborCost>0</PlannedNonLaborCost>
			<PlannedNonLaborUnits>0</PlannedNonLaborUnits>
			<PlannedStartDate>2015-02-24T08:00:00</PlannedStartDate>
			<PrimaryConstraintDate xsi:nil="true" />
			<PrimaryConstraintType />
			<PrimaryResourceObjectId xsi:nil="true" />
			<ProjectObjectId>4507</ProjectObjectId>
			<RemainingDuration>800</RemainingDuration>
			<RemainingEarlyFinishDate>2015-07-13T16:00:00</RemainingEarlyFinishDate>
			<RemainingEarlyStartDate>2015-02-24T08:00:00</RemainingEarlyStartDate>
			<RemainingLaborCost>0</RemainingLaborCost>
			<RemainingLaborUnits>0</RemainingLaborUnits>
			<RemainingLateFinishDate xsi:nil="true" />
			<RemainingLateStartDate xsi:nil="true" />
			<RemainingNonLaborCost>0</RemainingNonLaborCost>
			<RemainingNonLaborUnits>0</RemainingNonLaborUnits>
			<ResumeDate xsi:nil="true" />
			<ReviewStatus>OK</ReviewStatus>
			<SecondaryConstraintDate xsi:nil="true" />
			<SecondaryConstraintType />
			<StartDate>2015-02-24T08:00:00</StartDate>
			<Status>Not Started</Status>
			<SuspendDate xsi:nil="true" />
			<Type>Task Dependent</Type>
			<UnitsPercentComplete>0</UnitsPercentComplete>
			<WBSObjectId xsi:nil="true" />
		</Activity>
		<Activity>
			<ActualDuration>0</ActualDuration>
			<ActualFinishDate xsi:nil="true" />
			<ActualLaborCost>0</ActualLaborCost>
			<ActualLaborUnits>0</ActualLaborUnits>
			<ActualNonLaborCost>0</ActualNonLaborCost>
			<ActualNonLaborUnits>0</ActualNonLaborUnits>
			<ActualStartDate>2015-02-24T08:00:00</ActualStartDate>
			<ActualThisPeriodLaborCost>0</ActualThisPeriodLaborCost>
			<ActualThisPeriodLaborUnits>0</ActualThisPeriodLaborUnits>
			<ActualThisPeriodNonLaborCost>0</ActualThisPeriodNonLaborCost>
			<ActualThisPeriodNonLaborUnits>0</ActualThisPeriodNonLaborUnits>
			<AtCompletionDuration>800</AtCompletionDuration>
			<AtCompletionExpenseCost>0</AtCompletionExpenseCost>
			<AtCompletionLaborCost>0</AtCompletionLaborCost>
			<AtCompletionLaborUnits>0</AtCompletionLaborUnits>
			<AtCompletionNonLaborCost>0</AtCompletionNonLaborCost>
			<AtCompletionNonLaborUnits>0</AtCompletionNonLaborUnits>
			<AutoComputeActuals>0</AutoComputeActuals>
			<CalendarObjectId>178</CalendarObjectId>
			<DurationPercentComplete>0</DurationPercentComplete>
			<DurationType>Fixed Duration and Units</DurationType>
			<ExpectedFinishDate xsi:nil="true" />
			<ExternalEarlyStartDate xsi:nil="true" />
			<ExternalLateFinishDate xsi:nil="true" />
			<Feedback />
			<FinishDate>2015-07-13T16:00:00</FinishDate>
			<GUID>{487087D4-FAC1-494F-BF40-39CB8EF5A5CB}</GUID>
			<Id>A1050</Id>
			<IsNewFeedback>0</IsNewFeedback>
			<LevelingPriority>Normal</LevelingPriority>
			<Name>Physical 25%</Name>
			<NonLaborUnitsPercentComplete>0</NonLaborUnitsPercentComplete>
			<NotesToResources />
			<ObjectId>101722</ObjectId>
			<PercentComplete>0.25</PercentComplete>
			<PercentCompleteType>Physical</PercentCompleteType>
			<PhysicalPercentComplete>0.25</PhysicalPercentComplete>
			<PlannedDuration>800</PlannedDuration>
			<PlannedFinishDate>2015-07-13T16:00:00</PlannedFinishDate>
			<PlannedLaborCost>0</PlannedLaborCost>
			<PlannedLaborUnits>0</PlannedLaborUnits>
			<PlannedNonLaborCost>0</PlannedNonLaborCost>
			<PlannedNonLaborUnits>0</PlannedNonLaborUnits>
			<PlannedStartDate>2015-02-24T08:00:00</PlannedStartDate>
			<PrimaryConstraintDate xsi:nil="true" />
			<PrimaryConstraintType />
			<PrimaryResourceObjectId xsi:nil="true" />
			<ProjectObjectId>4507</ProjectObjectId>
			<RemainingDuration>800</RemainingDuration>
			<RemainingEarlyFinishDate>2015-07-13T16:00:00</RemainingEarlyFinishDate>
			<RemainingEarlyStartDate>2015-02-24T08:00:00</RemainingEarlyStartDate>
			<RemainingLaborCost>0</RemainingLaborCost>
			<RemainingLaborUnits>0</RemainingLaborUnits>
			<RemainingLateFinishDate xsi:nil="true" />
			<RemainingLateStartDate xsi:nil="true" />
			<RemainingNonLaborCost>0</RemainingNonLaborCost>
			<RemainingNonLaborUnits>0</RemainingNonLaborUnits>
			<ResumeDate xsi:nil="true" />
			<ReviewStatus>OK</ReviewStatus>
			<SecondaryConstraintDate xsi:nil="true" />
			<SecondaryConstraintType />
			<StartDate>2015-02-24T08:00:00</StartDate>
			<Status>In Progress</Status>
			<SuspendDate xsi:nil="true" />
			<Type>Task Dependent</Type>
			<UnitsPercentComplete>0</UnitsPercentComplete>
			<WBSObjectId xsi:nil="true" />
		</Activity>
		<Activity>
			<ActualDuration>0</ActualDuration>
			<ActualFinishDate xsi:nil="true" />
			<ActualLaborCost>0</ActualLaborCost>
			<ActualLaborUnits>0</ActualLaborUnits>
			<ActualNonLaborCost>0</ActualNonLaborCost>
			<ActualNonLaborUnits>0</ActualNonLaborUnits>
			<ActualStartDate>2015-02-24T08:00:00</ActualStartDate>
			<ActualThisPeriodLaborCost>0</ActualThisPeriodLaborCost>
			<ActualThisPeriodLaborUnits>0</ActualThisPeriodLaborUnits>
			<ActualThisPeriodNonLaborCost>0</ActualThisPeriodNonLaborCost>
			<ActualThisPeriodNonLaborUnits>0</ActualThisPeriodNonLaborUnits>
			<AtCompletionDuration>800</AtCompletionDuration>
			<AtCompletionExpenseCost>0</AtCompletionExpenseCost>
			<AtCompletionLaborCost>0</AtCompletionLaborCost>
			<AtCompletionLaborUnits>0</AtCompletionLaborUnits>
			<AtCompletionNonLaborCost>0</AtCompletionNonLaborCost>
			<AtCompletionNonLaborUnits>0</AtCompletionNonLaborUnits>
			<AutoComputeActuals>0</AutoComputeActuals>
			<CalendarObjectId>178</CalendarObjectId>
			<DurationPercentComplete>0</DurationPercentComplete>
			<DurationType>Fixed Duration and Units</DurationType>
			<ExpectedFinishDate xsi:nil="true" />
			<ExternalEarlyStartDate xsi:nil="true" />
			<ExternalLateFinishDate xsi:nil="true" />
			<Feedback />
			<FinishDate>2015-07-13T16:00:00</FinishDate>
			<GUID>{6F13C53F-8496-694D-95B7-5244B073F7D2}</GUID>
			<Id>A1060</Id>
			<IsNewFeedback>0</IsNewFeedback>
			<LevelingPriority>Normal</LevelingPriority>
			<Name>Physical 75%</Name>
			<NonLaborUnitsPercentComplete>0</NonLaborUnitsPercentComplete>
			<NotesToResources />
			<ObjectId>101723</ObjectId>
			<PercentComplete>0.75</PercentComplete>
			<PercentCompleteType>Physical</PercentCompleteType>
			<PhysicalPercentComplete>0.75</PhysicalPercentComplete>
			<PlannedDuration>800</PlannedDuration>
			<PlannedFinishDate>2015-07-13T16:00:00</PlannedFinishDate>
			<PlannedLaborCost>0</PlannedLaborCost>
			<PlannedLaborUnits>0</PlannedLaborUnits>
			<PlannedNonLaborCost>0</PlannedNonLaborCost>
			<PlannedNonLaborUnits>0</PlannedNonLaborUnits>
			<PlannedStartDate>2015-02-24T08:00:00</PlannedStartDate>
			<PrimaryConstraintDate xsi:nil="true" />
			<PrimaryConstraintType />
			<PrimaryResourceObjectId xsi:nil="true" />
			<ProjectObjectId>4507</ProjectObjectId>
			<RemainingDuration>800</RemainingDuration>
			<RemainingEarlyFinishDate>2015-07-13T16:00:00</RemainingEarlyFinishDate>
			<RemainingEarlyStartDate>2015-02-24T08:00:00</RemainingEarlyStartDate>
			<RemainingLaborCost>0</RemainingLaborCost>
			<RemainingLaborUnits>0</RemainingLaborUnits>
			<RemainingLateFinishDate xsi:nil="true" />
			<RemainingLateStartDate xsi:nil="true" />
			<RemainingNonLaborCost>0</RemainingNonLaborCost>
			<RemainingNonLaborUnits>0</RemainingNonLaborUnits>
			<ResumeDate xsi:nil="true" />
			<ReviewStatus>OK</ReviewStatus>
			<SecondaryConstraintDate xsi:nil="true" />
			<SecondaryConstraintType />
			<StartDate>2015-02-24T08:00:00</StartDate>
			<Status>In Progress</Status>
			<SuspendDate xsi:nil="true" />
			<Type>Task Dependent</Type>
			<UnitsPercentComplete>0</UnitsPercentComplete>
			<WBSObjectId xsi:nil="true" />
		</Activity>
		<Activity>
			<ActualDuration>0</ActualDuration>
			<ActualFinishDate xsi:nil="true" />
			<ActualLaborCost>0</ActualLaborCost>
			<ActualLaborUnits>0</ActualLaborUnits>
			<ActualNonLaborCost>0</ActualNonLaborCost>
			<ActualNonLaborUnits>0</ActualNonLaborUnits>
			<ActualStartDate>2015-02-24T08:00:00</ActualStartDate>
			<ActualThisPeriodLaborCost>0</ActualThisPeriodLaborCost>
			<ActualThisPeriodLaborUnits>0</ActualThisPeriodLaborUnits>
			<ActualThisPeriodNonLaborCost>0</ActualThisPeriodNonLaborCost>
			<ActualThisPeriodNonLaborUnits>0</ActualThisPeriodNonLaborUnits>
			<AtCompletionDuration>800</AtCompletionDuration>
			<AtCompletionExpenseCost>0</AtCompletionExpenseCost>
			<AtCompletionLaborCost>0</AtCompletionLaborCost>
			<AtCompletionLaborUnits>0</AtCompletionLaborUnits>
			<AtCompletionNonLaborCost>0</AtCompletionNonLaborCost>
			<AtCompletionNonLaborUnits>0</AtCompletionNonLaborUnits>
			<AutoComputeActuals>0</AutoComputeActuals>
			<CalendarObjectId>178</CalendarObjectId>
			<DurationPercentComplete>0</DurationPercentComplete>
			<DurationType>Fixed Duration and Units</DurationType>
			<ExpectedFinishDate xsi:nil="true" />
			<ExternalEarlyStartDate xsi:nil="true" />
			<ExternalLateFinishDate xsi:nil="true" />
			<Feedback />
			<FinishDate>2015-07-13T16:00:00</FinishDate>
			<GUID>{3F083633-A845-0840-9E5C-BD89BEA5FE92}</GUID>
			<Id>A1070</Id>
			<IsNewFeedback>0</IsNewFeedback>
			<LevelingPriority>Normal</LevelingPriority>
			<Name>Physical 100%</Name>
			<NonLaborUnitsPercentComplete>0</NonLaborUnitsPercentComplete>
			<NotesToResources />
			<ObjectId>101724</ObjectId>
			<PercentComplete>1</PercentComplete>
			<PercentCompleteType>Physical</PercentCompleteType>
			<PhysicalPercentComplete>1</PhysicalPercentComplete>
			<PlannedDuration>800</PlannedDuration>
			<PlannedFinishDate>2015-07-13T16:00:00</PlannedFinishDate>
			<PlannedLaborCost>0</PlannedLaborCost>
			<PlannedLaborUnits>0</PlannedLaborUnits>
			<PlannedNonLaborCost>0</PlannedNonLaborCost>
			<PlannedNonLaborUnits>0</PlannedNonLaborUnits>
			<PlannedStartDate>2015-02-24T08:00:00</PlannedStartDate>
			<PrimaryConstraintDate xsi:nil="true" />
			<PrimaryConstraintType />
			<PrimaryResourceObjectId xsi:nil="true" />
			<ProjectObjectId>4507</ProjectObjectId>
			<RemainingDuration>800</RemainingDuration>
			<RemainingEarlyFinishDate>2015-07-13T16:00:00</RemainingEarlyFinishDate>
			<RemainingEarlyStartDate>2015-02-24T08:00:00</RemainingEarlyStartDate>
			<RemainingLaborCost>0</RemainingLaborCost>
			<RemainingLaborUnits>0</RemainingLaborUnits>
			<RemainingLateFinishDate xsi:nil="true" />
			<RemainingLateStartDate xsi:nil="true" />
			<RemainingNonLaborCost>0</RemainingNonLaborCost>
			<RemainingNonLaborUnits>0</RemainingNonLaborUnits>
			<ResumeDate xsi:nil="true" />
			<ReviewStatus>OK</ReviewStatus>
			<SecondaryConstraintDate xsi:nil="true" />
			<SecondaryConstraintType />
			<StartDate>2015-02-24T08:00:00</StartDate>
			<Status>In Progress</Status>
			<SuspendDate xsi:nil="true" />
			<Type>Task Dependent</Type>
			<UnitsPercentComplete>0</UnitsPercentComplete>
			<WBSObjectId xsi:nil="true" />
		</Activity>
		<Activity>
			<ActualDuration>0</ActualDuration>
			<ActualFinishDate xsi:nil="true" />
			<ActualLaborCost>0</ActualLaborCost>
			<ActualLaborUnits>0</ActualLaborUnits>
			<ActualNonLaborCost>0</ActualNonLaborCost>
			<ActualNonLaborUnits>0</ActualNonLaborUnits>
			<ActualStartDate xsi:nil="true" />
			<ActualThisPeriodLaborCost>0</ActualThisPeriodLaborCost>
			<ActualThisPeriodLaborUnits>0</ActualThisPeriodLaborUnits>
			<ActualThisPeriodNonLaborCost>0</ActualThisPeriodNonLaborCost>
			<ActualThisPeriodNonLaborUnits>0</ActualThisPeriodNonLaborUnits>
			<AtCompletionDuration>800</AtCompletionDuration>
			<AtCompletionExpenseCost>0</AtCompletionExpenseCost>
			<AtCompletionLaborCost>0</AtCompletionLaborCost>
			<AtCompletionLaborUnits>100</AtCompletionLaborUnits>
			<AtCompletionNonLaborCost>0</AtCompletionNonLaborCost>
			<AtCompletionNonLaborUnits>0</AtCompletionNonLaborUnits>
			<AutoComputeActuals>0</AutoComputeActuals>
			<CalendarObjectId>178</CalendarObjectId>
			<DurationPercentComplete>0</DurationPercentComplete>
			<DurationType>Fixed Duration and Units</DurationType>
			<ExpectedFinishDate xsi:nil="true" />
			<ExternalEarlyStartDate xsi:nil="true" />
			<ExternalLateFinishDate xsi:nil="true" />
			<Feedback />
			<FinishDate>2015-07-13T16:00:00</FinishDate>
			<GUID>{9FCC55D3-EFB1-B943-8023-C04492E29CC1}</GUID>
			<Id>A1080</Id>
			<IsNewFeedback>0</IsNewFeedback>
			<LevelingPriority>Normal</LevelingPriority>
			<Name>Units 0%</Name>
			<NonLaborUnitsPercentComplete>0</NonLaborUnitsPercentComplete>
			<NotesToResources />
			<ObjectId>101725</ObjectId>
			<PercentComplete>0</PercentComplete>
			<PercentCompleteType>Units</PercentCompleteType>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<PlannedDuration>800</PlannedDuration>
			<PlannedFinishDate>2015-07-13T16:00:00</PlannedFinishDate>
			<PlannedLaborCost>0</PlannedLaborCost>
			<PlannedLaborUnits>100</PlannedLaborUnits>
			<PlannedNonLaborCost>0</PlannedNonLaborCost>
			<PlannedNonLaborUnits>0</PlannedNonLaborUnits>
			<PlannedStartDate>2015-02-24T08:00:00</PlannedStartDate>
			<PrimaryConstraintDate xsi:nil="true" />
			<PrimaryConstraintType />
			<PrimaryResourceObjectId xsi:nil="true" />
			<ProjectObjectId>4507</ProjectObjectId>
			<RemainingDuration>800</RemainingDuration>
			<RemainingEarlyFinishDate>2015-07-13T16:00:00</RemainingEarlyFinishDate>
			<RemainingEarlyStartDate>2015-02-24T08:00:00</RemainingEarlyStartDate>
			<RemainingLaborCost>0</RemainingLaborCost>
			<RemainingLaborUnits>100</RemainingLaborUnits>
			<RemainingLateFinishDate xsi:nil="true" />
			<RemainingLateStartDate xsi:nil="true" />
			<RemainingNonLaborCost>0</RemainingNonLaborCost>
			<RemainingNonLaborUnits>0</RemainingNonLaborUnits>
			<ResumeDate xsi:nil="true" />
			<ReviewStatus>OK</ReviewStatus>
			<SecondaryConstraintDate xsi:nil="true" />
			<SecondaryConstraintType />
			<StartDate>2015-02-24T08:00:00</StartDate>
			<Status>Not Started</Status>
			<SuspendDate xsi:nil="true" />
			<Type>Task Dependent</Type>
			<UnitsPercentComplete>0</UnitsPercentComplete>
			<WBSObjectId xsi:nil="true" />
		</Activity>
		<Activity>
			<ActualDuration>0</ActualDuration>
			<ActualFinishDate xsi:nil="true" />
			<ActualLaborCost>0</ActualLaborCost>
			<ActualLaborUnits>25</ActualLaborUnits>
			<ActualNonLaborCost>0</ActualNonLaborCost>
			<ActualNonLaborUnits>0</ActualNonLaborUnits>
			<ActualStartDate>2015-02-24T08:00:00</ActualStartDate>
			<ActualThisPeriodLaborCost>0</ActualThisPeriodLaborCost>
			<ActualThisPeriodLaborUnits>25</ActualThisPeriodLaborUnits>
			<ActualThisPeriodNonLaborCost>0</ActualThisPeriodNonLaborCost>
			<ActualThisPeriodNonLaborUnits>0</ActualThisPeriodNonLaborUnits>
			<AtCompletionDuration>800</AtCompletionDuration>
			<AtCompletionExpenseCost>0</AtCompletionExpenseCost>
			<AtCompletionLaborCost>0</AtCompletionLaborCost>
			<AtCompletionLaborUnits>100</AtCompletionLaborUnits>
			<AtCompletionNonLaborCost>0</AtCompletionNonLaborCost>
			<AtCompletionNonLaborUnits>0</AtCompletionNonLaborUnits>
			<AutoComputeActuals>0</AutoComputeActuals>
			<CalendarObjectId>178</CalendarObjectId>
			<DurationPercentComplete>0</DurationPercentComplete>
			<DurationType>Fixed Duration and Units</DurationType>
			<ExpectedFinishDate xsi:nil="true" />
			<ExternalEarlyStartDate xsi:nil="true" />
			<ExternalLateFinishDate xsi:nil="true" />
			<Feedback />
			<FinishDate>2015-07-13T16:00:00</FinishDate>
			<GUID>{9FA35EB7-B462-DB4F-AADB-5192261C9DA9}</GUID>
			<Id>A1090</Id>
			<IsNewFeedback>0</IsNewFeedback>
			<LevelingPriority>Normal</LevelingPriority>
			<Name>Units 25%</Name>
			<NonLaborUnitsPercentComplete>0</NonLaborUnitsPercentComplete>
			<NotesToResources />
			<ObjectId>101726</ObjectId>
			<PercentComplete>0.25</PercentComplete>
			<PercentCompleteType>Units</PercentCompleteType>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<PlannedDuration>800</PlannedDuration>
			<PlannedFinishDate>2015-07-13T16:00:00</PlannedFinishDate>
			<PlannedLaborCost>0</PlannedLaborCost>
			<PlannedLaborUnits>100</PlannedLaborUnits>
			<PlannedNonLaborCost>0</PlannedNonLaborCost>
			<PlannedNonLaborUnits>0</PlannedNonLaborUnits>
			<PlannedStartDate>2015-02-24T08:00:00</PlannedStartDate>
			<PrimaryConstraintDate xsi:nil="true" />
			<PrimaryConstraintType />
			<PrimaryResourceObjectId xsi:nil="true" />
			<ProjectObjectId>4507</ProjectObjectId>
			<RemainingDuration>800</RemainingDuration>
			<RemainingEarlyFinishDate>2015-07-13T16:00:00</RemainingEarlyFinishDate>
			<RemainingEarlyStartDate>2015-02-24T08:00:00</RemainingEarlyStartDate>
			<RemainingLaborCost>0</RemainingLaborCost>
			<RemainingLaborUnits>75</RemainingLaborUnits>
			<RemainingLateFinishDate xsi:nil="true" />
			<RemainingLateStartDate xsi:nil="true" />
			<RemainingNonLaborCost>0</RemainingNonLaborCost>
			<RemainingNonLaborUnits>0</RemainingNonLaborUnits>
			<ResumeDate xsi:nil="true" />
			<ReviewStatus>OK</ReviewStatus>
			<SecondaryConstraintDate xsi:nil="true" />
			<SecondaryConstraintType />
			<StartDate>2015-02-24T08:00:00</StartDate>
			<Status>In Progress</Status>
			<SuspendDate xsi:nil="true" />
			<Type>Task Dependent</Type>
			<UnitsPercentComplete>0.25</UnitsPercentComplete>
			<WBSObjectId xsi:nil="true" />
		</Activity>
		<Activity>
			<ActualDuration>0</ActualDuration>
			<ActualFinishDate xsi:nil="true" />
			<ActualLaborCost>0</ActualLaborCost>
			<ActualLaborUnits>75</ActualLaborUnits>
			<ActualNonLaborCost>0</ActualNonLaborCost>
			<ActualNonLaborUnits>0</ActualNonLaborUnits>
			<ActualStartDate>2015-02-24T08:00:00</ActualStartDate>
			<ActualThisPeriodLaborCost>0</ActualThisPeriodLaborCost>
			<ActualThisPeriodLaborUnits>75</ActualThisPeriodLaborUnits>
			<ActualThisPeriodNonLaborCost>0</ActualThisPeriodNonLaborCost>
			<ActualThisPeriodNonLaborUnits>0</ActualThisPeriodNonLaborUnits>
			<AtCompletionDuration>800</AtCompletionDuration>
			<AtCompletionExpenseCost>0</AtCompletionExpenseCost>
			<AtCompletionLaborCost>0</AtCompletionLaborCost>
			<AtCompletionLaborUnits>100</AtCompletionLaborUnits>
			<AtCompletionNonLaborCost>0</AtCompletionNonLaborCost>
			<AtCompletionNonLaborUnits>0</AtCompletionNonLaborUnits>
			<AutoComputeActuals>0</AutoComputeActuals>
			<CalendarObjectId>178</CalendarObjectId>
			<DurationPercentComplete>0</DurationPercentComplete>
			<DurationType>Fixed Duration and Units</DurationType>
			<ExpectedFinishDate xsi:nil="true" />
			<ExternalEarlyStartDate xsi:nil="true" />
			<ExternalLateFinishDate xsi:nil="true" />
			<Feedback />
			<FinishDate>2015-07-13T16:00:00</FinishDate>
			<GUID>{A338DD7C-33A8-CD4B-974F-6BB78D888C3B}</GUID>
			<Id>A1100</Id>
			<IsNewFeedback>0</IsNewFeedback>
			<LevelingPriority>Normal</LevelingPriority>
			<Name>Units 75%</Name>
			<NonLaborUnitsPercentComplete>0</NonLaborUnitsPercentComplete>
			<NotesToResources />
			<ObjectId>101727</ObjectId>
			<PercentComplete>0.75</PercentComplete>
			<PercentCompleteType>Units</PercentCompleteType>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<PlannedDuration>800</PlannedDuration>
			<PlannedFinishDate>2015-07-13T16:00:00</PlannedFinishDate>
			<PlannedLaborCost>0</PlannedLaborCost>
			<PlannedLaborUnits>100</PlannedLaborUnits>
			<PlannedNonLaborCost>0</PlannedNonLaborCost>
			<PlannedNonLaborUnits>0</PlannedNonLaborUnits>
			<PlannedStartDate>2015-02-24T08:00:00</PlannedStartDate>
			<PrimaryConstraintDate xsi:nil="true" />
			<PrimaryConstraintType />
			<PrimaryResourceObjectId xsi:nil="true" />
			<ProjectObjectId>4507</ProjectObjectId>
			<RemainingDuration>800</RemainingDuration>
			<RemainingEarlyFinishDate>2015-07-13T16:00:00</RemainingEarlyFinishDate>
			<RemainingEarlyStartDate>2015-02-24T08:00:00</RemainingEarlyStartDate>
			<RemainingLaborCost>0</RemainingLaborCost>
			<RemainingLaborUnits>25</RemainingLaborUnits>
			<RemainingLateFinishDate xsi:nil="true" />
			<RemainingLateStartDate xsi:nil="true" />
			<RemainingNonLaborCost>0</RemainingNonLaborCost>
			<RemainingNonLaborUnits>0</RemainingNonLaborUnits>
			<ResumeDate xsi:nil="true" />
			<ReviewStatus>OK</ReviewStatus>
			<SecondaryConstraintDate xsi:nil="true" />
			<SecondaryConstraintType />
			<StartDate>2015-02-24T08:00:00</StartDate>
			<Status>In Progress</Status>
			<SuspendDate xsi:nil="true" />
			<Type>Task Dependent</Type>
			<UnitsPercentComplete>0.75</UnitsPercentComplete>
			<WBSObjectId xsi:nil="true" />
		</Activity>
		<Activity>
			<ActualDuration>0</ActualDuration>
			<ActualFinishDate xsi:nil="true" />
			<ActualLaborCost>0</ActualLaborCost>
			<ActualLaborUnits>100</ActualLaborUnits>
			<ActualNonLaborCost>0</ActualNonLaborCost>
			<ActualNonLaborUnits>0</ActualNonLaborUnits>
			<ActualStartDate>2015-02-24T00:00:00</ActualStartDate>
			<ActualThisPeriodLaborCost>0</ActualThisPeriodLaborCost>
			<ActualThisPeriodLaborUnits>200</ActualThisPeriodLaborUnits>
			<ActualThisPeriodNonLaborCost>0</ActualThisPeriodNonLaborCost>
			<ActualThisPeriodNonLaborUnits>0</ActualThisPeriodNonLaborUnits>
			<AtCompletionDuration>800</AtCompletionDuration>
			<AtCompletionExpenseCost>0</AtCompletionExpenseCost>
			<AtCompletionLaborCost>0</AtCompletionLaborCost>
			<AtCompletionLaborUnits>100</AtCompletionLaborUnits>
			<AtCompletionNonLaborCost>0</AtCompletionNonLaborCost>
			<AtCompletionNonLaborUnits>0</AtCompletionNonLaborUnits>
			<AutoComputeActuals>0</AutoComputeActuals>
			<CalendarObjectId>178</CalendarObjectId>
			<DurationPercentComplete>0</DurationPercentComplete>
			<DurationType>Fixed Duration and Units</DurationType>
			<ExpectedFinishDate xsi:nil="true" />
			<ExternalEarlyStartDate xsi:nil="true" />
			<ExternalLateFinishDate xsi:nil="true" />
			<Feedback />
			<FinishDate>2015-07-13T16:00:00</FinishDate>
			<GUID>{6D31FE09-EFE3-7C4C-8605-F507DB86A2E5}</GUID>
			<Id>A1110</Id>
			<IsNewFeedback>0</IsNewFeedback>
			<LevelingPriority>Normal</LevelingPriority>
			<Name>Units 100%</Name>
			<NonLaborUnitsPercentComplete>0</NonLaborUnitsPercentComplete>
			<NotesToResources />
			<ObjectId>101728</ObjectId>
			<PercentComplete>1</PercentComplete>
			<PercentCompleteType>Units</PercentCompleteType>
			<PhysicalPercentComplete>0</PhysicalPercentComplete>
			<PlannedDuration>800</PlannedDuration>
			<PlannedFinishDate>2015-07-13T16:00:00</PlannedFinishDate>
			<PlannedLaborCost>0</PlannedLaborCost>
			<PlannedLaborUnits>100</PlannedLaborUnits>
			<PlannedNonLaborCost>0</PlannedNonLaborCost>
			<PlannedNonLaborUnits>0</PlannedNonLaborUnits>
			<PlannedStartDate>2015-02-24T08:00:00</PlannedStartDate>
			<PrimaryConstraintDate xsi:nil="true" />
			<PrimaryConstraintType />
			<PrimaryResourceObjectId xsi:nil="true" />
			<ProjectObjectId>4507</ProjectObjectId>
			<RemainingDuration>800</RemainingDuration>
			<RemainingEarlyFinishDate>2015-07-13T16:00:00</RemainingEarlyFinishDate>
			<RemainingEarlyStartDate>2015-02-24T08:00:00</RemainingEarlyStartDate>
			<RemainingLaborCost>0</RemainingLaborCost>
			<RemainingLaborUnits>0</RemainingLaborUnits>
			<RemainingLateFinishDate xsi:nil="true" />
			<RemainingLateStartDate xsi:nil="true" />
			<RemainingNonLaborCost>0</RemainingNonLaborCost>
			<RemainingNonLaborUnits>0</RemainingNonLaborUnits>
			<ResumeDate xsi:nil="true" />
			<ReviewStatus>OK</ReviewStatus>
			<SecondaryConstraintDate xsi:nil="true" />
			<SecondaryConstraintType />
			<StartDate>2015-02-24T00:00:00</StartDate>
			<Status>In Progress</Status>
			<SuspendDate xsi:nil="true" />
			<Type>Task Dependent</Type>
			<UnitsPercentComplete>1</UnitsPercentComplete>
			<WBSObjectId xsi:nil="true" />
		</Activity>
	</Project>
</APIBusinessObjects>
