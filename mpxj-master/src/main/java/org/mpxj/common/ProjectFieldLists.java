/*
 * file:       ProjectFieldLists.java
 * author:     <PERSON>
 * copyright:  (c) Packwood Software 2015
 * date:       30/03/2015
 */

/*
 * This library is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as published by the
 * Free Software Foundation; either version 2.1 of the License, or (at your
 * option) any later version.
 *
 * This library is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 * or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public
 * License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this library; if not, write to the Free Software Foundation, Inc.,
 * 59 Temple Place, Suite 330, Boston, MA 02111-1307, USA.
 */

package org.mpxj.common;

import org.mpxj.ProjectField;

/**
 * Project fields grouped into logical collections.
 */
public final class ProjectFieldLists
{
   public static final ProjectField[] BASELINE_DATES =
   {
      ProjectField.BASELINE1_DATE,
      ProjectField.BASELINE2_DATE,
      ProjectField.BASELINE3_DATE,
      ProjectField.BASELINE4_DATE,
      ProjectField.BASELINE5_DATE,
      ProjectField.BASELINE6_DATE,
      ProjectField.BASELINE7_DATE,
      ProjectField.BASELINE8_DATE,
      ProjectField.BASELINE9_DATE,
      ProjectField.BASELINE10_DATE
   };
}
