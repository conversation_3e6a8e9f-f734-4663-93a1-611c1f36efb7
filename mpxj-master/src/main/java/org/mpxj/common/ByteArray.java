/*
 * file:       ByteArray.java
 * author:     <PERSON>
 * copyright:  (c) Packwood Software 2018
 * date:       2018-10-11
 */

/*
 * This library is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as published by the
 * Free Software Foundation; either version 2.1 of the License, or (at your
 * option) any later version.
 *
 * This library is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 * or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public
 * License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this library; if not, write to the Free Software Foundation, Inc.,
 * 59 Temple Place, Suite 330, Boston, MA 02111-1307, USA.
 */

package org.mpxj.common;

/**
 * Simple wrapper around a byte array which dumps the array as hex
 * when toString is called.
 */
public class ByteArray
{
   /**
    * Constructor.
    *
    * @param data byte array to wrap
    */
   public ByteArray(byte[] data)
   {
      m_data = data;
   }

   /**
    * Retrieve the byte array.
    *
    * @return byte array
    */
   public byte[] getData()
   {
      return m_data;
   }

   @Override public String toString()
   {
      return ByteArrayHelper.hexdump(m_data, false);
   }

   private final byte[] m_data;
}
