//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.2
// See https://eclipse-ee4j.github.io/jaxb-ri
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2025.04.09 at 09:56:52 AM BST
//

package org.mpxj.phoenix.schema.phoenix5;

import jakarta.xml.bind.annotation.adapters.XmlAdapter;
import org.mpxj.TimeUnit;
import org.mpxj.phoenix.DatatypeConverter;

public class Adapter6
         extends
            XmlAdapter<String, TimeUnit>
{

   @Override public TimeUnit unmarshal(String value)
   {
      return (DatatypeConverter.parseTimeUnits(value));
   }

   @Override public String marshal(TimeUnit value)
   {
      return (DatatypeConverter.printTimeUnits(value));
   }

}
