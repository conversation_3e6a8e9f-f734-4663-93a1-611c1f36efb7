//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.2
// See https://eclipse-ee4j.github.io/jaxb-ri
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2025.04.09 at 09:56:52 AM BST
//

package org.mpxj.phoenix.schema.phoenix5;

import java.time.LocalDateTime;
import jakarta.xml.bind.annotation.adapters.XmlAdapter;
import org.mpxj.phoenix.DatatypeConverter;

public class Adapter1
         extends
            XmlAdapter<String, LocalDateTime>
{

   @Override public LocalDateTime unmarshal(String value)
   {
      return (DatatypeConverter.parseDateTime(value));
   }

   @Override public String marshal(LocalDateTime value)
   {
      return (DatatypeConverter.printDateTime(value));
   }

}
