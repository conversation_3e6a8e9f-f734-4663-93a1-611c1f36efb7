//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.2
// See https://eclipse-ee4j.github.io/jaxb-ri
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2024.04.25 at 10:03:38 AM BST
//

package org.mpxj.planner.schema;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

/**
 * <p>Java class for anonymous complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;attribute name="mon" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *       &lt;attribute name="tue" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *       &lt;attribute name="wed" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *       &lt;attribute name="thu" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *       &lt;attribute name="fri" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *       &lt;attribute name="sat" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *       &lt;attribute name="sun" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") @XmlRootElement(name = "default-week") public class DefaultWeek
{

   @XmlAttribute(name = "mon") @XmlJavaTypeAdapter(Adapter1.class) protected String mon;
   @XmlAttribute(name = "tue") @XmlJavaTypeAdapter(Adapter1.class) protected String tue;
   @XmlAttribute(name = "wed") @XmlJavaTypeAdapter(Adapter1.class) protected String wed;
   @XmlAttribute(name = "thu") @XmlJavaTypeAdapter(Adapter1.class) protected String thu;
   @XmlAttribute(name = "fri") @XmlJavaTypeAdapter(Adapter1.class) protected String fri;
   @XmlAttribute(name = "sat") @XmlJavaTypeAdapter(Adapter1.class) protected String sat;
   @XmlAttribute(name = "sun") @XmlJavaTypeAdapter(Adapter1.class) protected String sun;

   /**
    * Gets the value of the mon property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getMon()
   {
      return mon;
   }

   /**
    * Sets the value of the mon property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setMon(String value)
   {
      this.mon = value;
   }

   /**
    * Gets the value of the tue property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTue()
   {
      return tue;
   }

   /**
    * Sets the value of the tue property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTue(String value)
   {
      this.tue = value;
   }

   /**
    * Gets the value of the wed property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getWed()
   {
      return wed;
   }

   /**
    * Sets the value of the wed property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setWed(String value)
   {
      this.wed = value;
   }

   /**
    * Gets the value of the thu property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getThu()
   {
      return thu;
   }

   /**
    * Sets the value of the thu property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setThu(String value)
   {
      this.thu = value;
   }

   /**
    * Gets the value of the fri property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getFri()
   {
      return fri;
   }

   /**
    * Sets the value of the fri property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setFri(String value)
   {
      this.fri = value;
   }

   /**
    * Gets the value of the sat property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getSat()
   {
      return sat;
   }

   /**
    * Sets the value of the sat property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSat(String value)
   {
      this.sat = value;
   }

   /**
    * Gets the value of the sun property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getSun()
   {
      return sun;
   }

   /**
    * Sets the value of the sun property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setSun(String value)
   {
      this.sun = value;
   }

}
