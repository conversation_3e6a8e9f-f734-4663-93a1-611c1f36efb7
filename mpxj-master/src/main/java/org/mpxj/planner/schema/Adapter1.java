//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.2
// See https://eclipse-ee4j.github.io/jaxb-ri
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2024.04.25 at 10:03:38 AM BST
//

package org.mpxj.planner.schema;

import jakarta.xml.bind.annotation.adapters.XmlAdapter;
import org.mpxj.planner.DatatypeConverter;

public class Adapter1 extends XmlAdapter<String, String>
{

   @Override public String unmarshal(String value)
   {
      return (DatatypeConverter.parseString(value));
   }

   @Override public String marshal(String value)
   {
      return (DatatypeConverter.printString(value));
   }

}
