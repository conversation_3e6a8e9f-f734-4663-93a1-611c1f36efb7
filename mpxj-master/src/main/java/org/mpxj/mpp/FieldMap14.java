/*
 * file:       FieldMap14.java
 * author:     <PERSON>
 * copyright:  (c) Packwood Software 2011
 * date:       03/05/2011
 */

/*
 * This library is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as published by the
 * Free Software Foundation; either version 2.1 of the License, or (at your
 * option) any later version.
 *
 * This library is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 * or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public
 * License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this library; if not, write to the Free Software Foundation, Inc.,
 * 59 Temple Place, Suite 330, Boston, MA 02111-1307, USA.
 */

package org.mpxj.mpp;

import java.util.HashMap;
import java.util.Map;

import org.mpxj.AssignmentField;
import org.mpxj.FieldType;
import org.mpxj.ProjectFile;
import org.mpxj.ResourceField;
import org.mpxj.TaskField;

/**
 * MPP14 field map.
 */
class FieldMap14 extends FieldMap
{
   /**
    * Constructor.
    *
    * @param file project file
    */
   public FieldMap14(ProjectFile file)
   {
      super(file);
   }

   @Override protected boolean useTypeAsVarDataKey()
   {
      return true;
   }

   @Override protected Integer substituteVarDataKey(FieldType type)
   {
      return VAR_DATA_MAP.get(type);
   }

   @Override protected FieldItem[] getDefaultTaskData()
   {
      return new FieldItem[]
      {
         new FieldItem(TaskField.UNIQUE_ID, FieldLocation.FIXED_DATA, 0, 0, 86, 0, 0),
         new FieldItem(TaskField.ID, FieldLocation.FIXED_DATA, 0, 4, 23, 0, 0),
         new FieldItem(TaskField.EARLY_FINISH, FieldLocation.FIXED_DATA, 0, 8, 38, 0, 0),
         new FieldItem(TaskField.LATE_START, FieldLocation.FIXED_DATA, 0, 12, 39, 0, 0),
         new FieldItem(TaskField.STOP, FieldLocation.FIXED_DATA, 0, 16, 100, 0, 0),
         new FieldItem(TaskField.RESUME, FieldLocation.FIXED_DATA, 0, 20, 99, 0, 0),
         new FieldItem(TaskField.FREE_SLACK, FieldLocation.FIXED_DATA, 0, 24, 21, 0, 0),
         new FieldItem(TaskField.START_SLACK, FieldLocation.FIXED_DATA, 0, 28, 438, 0, 0),
         new FieldItem(TaskField.FINISH_SLACK, FieldLocation.FIXED_DATA, 0, 32, 439, 0, 0),
         new FieldItem(TaskField.PARENT_TASK_UNIQUE_ID, FieldLocation.FIXED_DATA, 0, 36, 160, 0, 0),
         new FieldItem(TaskField.OUTLINE_LEVEL, FieldLocation.FIXED_DATA, 0, 40, 249, 0, 0),
         new FieldItem(TaskField.SCHEDULED_DURATION, FieldLocation.FIXED_DATA, 0, 42, 29, 0, 0),
         new FieldItem(TaskField.ACTUAL_DURATION_UNITS, FieldLocation.FIXED_DATA, 0, 46, 181, 0, 0),
         new FieldItem(TaskField.ACTUAL_DURATION, FieldLocation.FIXED_DATA, 0, 48, 28, 0, 0),
         new FieldItem(TaskField.REMAINING_DURATION, FieldLocation.FIXED_DATA, 0, 52, 31, 0, 0),
         new FieldItem(TaskField.CONSTRAINT_TYPE, FieldLocation.FIXED_DATA, 0, 56, 17, 0, 0),
         new FieldItem(TaskField.LEVELING_DELAY, FieldLocation.FIXED_DATA, 0, 58, 20, 0, 0),
         new FieldItem(TaskField.LEVELING_DELAY_UNITS, FieldLocation.FIXED_DATA, 0, 62, 178, 0, 0),
         new FieldItem(TaskField.SCHEDULED_START, FieldLocation.FIXED_DATA, 0, 64, 35, 0, 0),
         new FieldItem(TaskField.SCHEDULED_FINISH, FieldLocation.FIXED_DATA, 0, 68, 36, 0, 0),
         new FieldItem(TaskField.ACTUAL_START, FieldLocation.FIXED_DATA, 0, 72, 41, 0, 0),
         new FieldItem(TaskField.ACTUAL_FINISH, FieldLocation.FIXED_DATA, 0, 76, 42, 0, 0),
         new FieldItem(TaskField.CONSTRAINT_DATE, FieldLocation.FIXED_DATA, 0, 80, 18, 0, 0),
         new FieldItem(TaskField.RESUME_NO_EARLIER_THAN, FieldLocation.FIXED_DATA, 0, 84, 101, 0, 0),
         new FieldItem(TaskField.PRIORITY, FieldLocation.FIXED_DATA, 0, 88, 25, 0, 0),
         new FieldItem(TaskField.PERCENT_COMPLETE, FieldLocation.FIXED_DATA, 0, 90, 32, 0, 0),
         new FieldItem(TaskField.PERCENT_WORK_COMPLETE, FieldLocation.FIXED_DATA, 0, 92, 33, 0, 0),
         new FieldItem(TaskField.TYPE, FieldLocation.FIXED_DATA, 0, 94, 128, 0, 0),
         new FieldItem(TaskField.FIXED_COST_ACCRUAL, FieldLocation.FIXED_DATA, 0, 96, 200, 0, 0),
         new FieldItem(TaskField.CREATED, FieldLocation.FIXED_DATA, 0, 98, 93, 0, 0),
         new FieldItem(TaskField.EARLY_START, FieldLocation.FIXED_DATA, 0, 106, 37, 0, 0),
         new FieldItem(TaskField.LATE_FINISH, FieldLocation.FIXED_DATA, 0, 110, 40, 0, 0),
         new FieldItem(TaskField.SUMMARY_PROGRESS, FieldLocation.FIXED_DATA, 0, 114, 387, 0, 0),
         new FieldItem(TaskField.CALENDAR_UNIQUE_ID, FieldLocation.FIXED_DATA, 0, 118, 401, 0, 0),
         new FieldItem(TaskField.DEADLINE, FieldLocation.FIXED_DATA, 0, 122, 437, 0, 0),
         new FieldItem(TaskField.WORK, FieldLocation.FIXED_DATA, 0, 126, 0, 0, 0),
         new FieldItem(TaskField.ACTUAL_WORK, FieldLocation.FIXED_DATA, 0, 134, 2, 0, 0),
         new FieldItem(TaskField.REMAINING_WORK, FieldLocation.FIXED_DATA, 0, 142, 4, 0, 0),
         new FieldItem(TaskField.COST, FieldLocation.FIXED_DATA, 0, 150, 5, 0, 0),
         new FieldItem(TaskField.FIXED_COST, FieldLocation.FIXED_DATA, 0, 158, 8, 0, 0),
         new FieldItem(TaskField.ACTUAL_COST, FieldLocation.FIXED_DATA, 0, 166, 7, 0, 0),
         new FieldItem(TaskField.REMAINING_COST, FieldLocation.FIXED_DATA, 0, 174, 10, 0, 0),
         new FieldItem(TaskField.BASELINE_FIXED_COST, FieldLocation.FIXED_DATA, 0, 198, 480, 0, 0),
         new FieldItem(TaskField.ACTUAL_OVERTIME_WORK, FieldLocation.VAR_DATA, 0, 65535, 164, 0, 0),
         new FieldItem(TaskField.REMAINING_OVERTIME_WORK, FieldLocation.VAR_DATA, 0, 65535, 165, 0, 0),
         new FieldItem(TaskField.OVERTIME_COST, FieldLocation.VAR_DATA, 0, 65535, 168, 0, 0),
         new FieldItem(TaskField.ACTUAL_OVERTIME_COST, FieldLocation.VAR_DATA, 0, 65535, 169, 0, 0),
         new FieldItem(TaskField.REMAINING_OVERTIME_COST, FieldLocation.VAR_DATA, 0, 65535, 170, 0, 0),
         new FieldItem(TaskField.SUBPROJECT_TASKS_UNIQUEID_OFFSET, FieldLocation.VAR_DATA, 0, 65535, 458, 0, 0),
         new FieldItem(TaskField.SUBPROJECT_TASK_UNIQUE_ID, FieldLocation.VAR_DATA, 0, 65535, 242, 0, 0),
         new FieldItem(TaskField.WBS, FieldLocation.VAR_DATA, 0, 65535, 16, 0, 0),
         new FieldItem(TaskField.NAME, FieldLocation.VAR_DATA, 0, 65535, 14, 0, 0),
         new FieldItem(TaskField.CONTACT, FieldLocation.VAR_DATA, 0, 65535, 112, 0, 0),
         new FieldItem(TaskField.TEXT1, FieldLocation.VAR_DATA, 0, 65535, 51, 0, 0),
         new FieldItem(TaskField.TEXT2, FieldLocation.VAR_DATA, 0, 65535, 54, 0, 0),
         new FieldItem(TaskField.TEXT3, FieldLocation.VAR_DATA, 0, 65535, 57, 0, 0),
         new FieldItem(TaskField.TEXT4, FieldLocation.VAR_DATA, 0, 65535, 60, 0, 0),
         new FieldItem(TaskField.TEXT5, FieldLocation.VAR_DATA, 0, 65535, 63, 0, 0),
         new FieldItem(TaskField.TEXT6, FieldLocation.VAR_DATA, 0, 65535, 66, 0, 0),
         new FieldItem(TaskField.TEXT7, FieldLocation.VAR_DATA, 0, 65535, 67, 0, 0),
         new FieldItem(TaskField.TEXT8, FieldLocation.VAR_DATA, 0, 65535, 68, 0, 0),
         new FieldItem(TaskField.TEXT9, FieldLocation.VAR_DATA, 0, 65535, 69, 0, 0),
         new FieldItem(TaskField.TEXT10, FieldLocation.VAR_DATA, 0, 65535, 70, 0, 0),
         new FieldItem(TaskField.START1, FieldLocation.VAR_DATA, 0, 65535, 52, 0, 0),
         new FieldItem(TaskField.FINISH1, FieldLocation.VAR_DATA, 0, 65535, 53, 0, 0),
         new FieldItem(TaskField.START2, FieldLocation.VAR_DATA, 0, 65535, 55, 0, 0),
         new FieldItem(TaskField.FINISH2, FieldLocation.VAR_DATA, 0, 65535, 56, 0, 0),
         new FieldItem(TaskField.START3, FieldLocation.VAR_DATA, 0, 65535, 58, 0, 0),
         new FieldItem(TaskField.FINISH3, FieldLocation.VAR_DATA, 0, 65535, 59, 0, 0),
         new FieldItem(TaskField.START4, FieldLocation.VAR_DATA, 0, 65535, 61, 0, 0),
         new FieldItem(TaskField.FINISH4, FieldLocation.VAR_DATA, 0, 65535, 62, 0, 0),
         new FieldItem(TaskField.START5, FieldLocation.VAR_DATA, 0, 65535, 64, 0, 0),
         new FieldItem(TaskField.FINISH5, FieldLocation.VAR_DATA, 0, 65535, 65, 0, 0),
         new FieldItem(TaskField.START6, FieldLocation.VAR_DATA, 0, 65535, 282, 0, 0),
         new FieldItem(TaskField.FINISH6, FieldLocation.VAR_DATA, 0, 65535, 283, 0, 0),
         new FieldItem(TaskField.START7, FieldLocation.VAR_DATA, 0, 65535, 284, 0, 0),
         new FieldItem(TaskField.FINISH7, FieldLocation.VAR_DATA, 0, 65535, 285, 0, 0),
         new FieldItem(TaskField.START8, FieldLocation.VAR_DATA, 0, 65535, 286, 0, 0),
         new FieldItem(TaskField.FINISH8, FieldLocation.VAR_DATA, 0, 65535, 287, 0, 0),
         new FieldItem(TaskField.START9, FieldLocation.VAR_DATA, 0, 65535, 288, 0, 0),
         new FieldItem(TaskField.FINISH9, FieldLocation.VAR_DATA, 0, 65535, 289, 0, 0),
         new FieldItem(TaskField.START10, FieldLocation.VAR_DATA, 0, 65535, 290, 0, 0),
         new FieldItem(TaskField.FINISH10, FieldLocation.VAR_DATA, 0, 65535, 291, 0, 0),
         new FieldItem(TaskField.NUMBER1, FieldLocation.VAR_DATA, 0, 65535, 87, 0, 0),
         new FieldItem(TaskField.NUMBER2, FieldLocation.VAR_DATA, 0, 65535, 88, 0, 0),
         new FieldItem(TaskField.NUMBER3, FieldLocation.VAR_DATA, 0, 65535, 89, 0, 0),
         new FieldItem(TaskField.NUMBER4, FieldLocation.VAR_DATA, 0, 65535, 90, 0, 0),
         new FieldItem(TaskField.NUMBER5, FieldLocation.VAR_DATA, 0, 65535, 91, 0, 0),
         new FieldItem(TaskField.NUMBER6, FieldLocation.VAR_DATA, 0, 65535, 302, 0, 0),
         new FieldItem(TaskField.NUMBER7, FieldLocation.VAR_DATA, 0, 65535, 303, 0, 0),
         new FieldItem(TaskField.NUMBER8, FieldLocation.VAR_DATA, 0, 65535, 304, 0, 0),
         new FieldItem(TaskField.NUMBER9, FieldLocation.VAR_DATA, 0, 65535, 305, 0, 0),
         new FieldItem(TaskField.NUMBER10, FieldLocation.VAR_DATA, 0, 65535, 306, 0, 0),
         new FieldItem(TaskField.DURATION1, FieldLocation.VAR_DATA, 0, 65535, 103, 0, 0),
         new FieldItem(TaskField.DURATION1_UNITS, FieldLocation.VAR_DATA, 0, 65535, 183, 0, 0),
         new FieldItem(TaskField.DURATION2, FieldLocation.VAR_DATA, 0, 65535, 104, 0, 0),
         new FieldItem(TaskField.DURATION2_UNITS, FieldLocation.VAR_DATA, 0, 65535, 184, 0, 0),
         new FieldItem(TaskField.DURATION3, FieldLocation.VAR_DATA, 0, 65535, 105, 0, 0),
         new FieldItem(TaskField.DURATION3_UNITS, FieldLocation.VAR_DATA, 0, 65535, 185, 0, 0),
         new FieldItem(TaskField.DURATION4, FieldLocation.VAR_DATA, 0, 65535, 275, 0, 0),
         new FieldItem(TaskField.DURATION4_UNITS, FieldLocation.VAR_DATA, 0, 65535, 337, 0, 0),
         new FieldItem(TaskField.DURATION5, FieldLocation.VAR_DATA, 0, 65535, 276, 0, 0),
         new FieldItem(TaskField.DURATION5_UNITS, FieldLocation.VAR_DATA, 0, 65535, 338, 0, 0),
         new FieldItem(TaskField.DURATION6, FieldLocation.VAR_DATA, 0, 65535, 277, 0, 0),
         new FieldItem(TaskField.DURATION6_UNITS, FieldLocation.VAR_DATA, 0, 65535, 339, 0, 0),
         new FieldItem(TaskField.DURATION7, FieldLocation.VAR_DATA, 0, 65535, 278, 0, 0),
         new FieldItem(TaskField.DURATION7_UNITS, FieldLocation.VAR_DATA, 0, 65535, 340, 0, 0),
         new FieldItem(TaskField.DURATION8, FieldLocation.VAR_DATA, 0, 65535, 279, 0, 0),
         new FieldItem(TaskField.DURATION8_UNITS, FieldLocation.VAR_DATA, 0, 65535, 341, 0, 0),
         new FieldItem(TaskField.DURATION9, FieldLocation.VAR_DATA, 0, 65535, 280, 0, 0),
         new FieldItem(TaskField.DURATION9_UNITS, FieldLocation.VAR_DATA, 0, 65535, 342, 0, 0),
         new FieldItem(TaskField.DURATION10, FieldLocation.VAR_DATA, 0, 65535, 281, 0, 0),
         new FieldItem(TaskField.DURATION10_UNITS, FieldLocation.VAR_DATA, 0, 65535, 343, 0, 0),
         new FieldItem(TaskField.RECURRING_DATA, FieldLocation.VAR_DATA, 0, 65535, 203, 0, 0),
         new FieldItem(TaskField.SUBPROJECT_TASK_ID, FieldLocation.VAR_DATA, 0, 65535, 255, 0, 0),
         new FieldItem(TaskField.DATE1, FieldLocation.VAR_DATA, 0, 65535, 265, 0, 0),
         new FieldItem(TaskField.DATE2, FieldLocation.VAR_DATA, 0, 65535, 266, 0, 0),
         new FieldItem(TaskField.DATE3, FieldLocation.VAR_DATA, 0, 65535, 267, 0, 0),
         new FieldItem(TaskField.DATE4, FieldLocation.VAR_DATA, 0, 65535, 268, 0, 0),
         new FieldItem(TaskField.DATE5, FieldLocation.VAR_DATA, 0, 65535, 269, 0, 0),
         new FieldItem(TaskField.DATE6, FieldLocation.VAR_DATA, 0, 65535, 270, 0, 0),
         new FieldItem(TaskField.DATE7, FieldLocation.VAR_DATA, 0, 65535, 271, 0, 0),
         new FieldItem(TaskField.DATE8, FieldLocation.VAR_DATA, 0, 65535, 272, 0, 0),
         new FieldItem(TaskField.DATE9, FieldLocation.VAR_DATA, 0, 65535, 273, 0, 0),
         new FieldItem(TaskField.DATE10, FieldLocation.VAR_DATA, 0, 65535, 274, 0, 0),
         new FieldItem(TaskField.TEXT11, FieldLocation.VAR_DATA, 0, 65535, 317, 0, 0),
         new FieldItem(TaskField.TEXT12, FieldLocation.VAR_DATA, 0, 65535, 318, 0, 0),
         new FieldItem(TaskField.TEXT13, FieldLocation.VAR_DATA, 0, 65535, 319, 0, 0),
         new FieldItem(TaskField.TEXT14, FieldLocation.VAR_DATA, 0, 65535, 320, 0, 0),
         new FieldItem(TaskField.TEXT15, FieldLocation.VAR_DATA, 0, 65535, 321, 0, 0),
         new FieldItem(TaskField.TEXT16, FieldLocation.VAR_DATA, 0, 65535, 322, 0, 0),
         new FieldItem(TaskField.TEXT17, FieldLocation.VAR_DATA, 0, 65535, 323, 0, 0),
         new FieldItem(TaskField.TEXT18, FieldLocation.VAR_DATA, 0, 65535, 324, 0, 0),
         new FieldItem(TaskField.TEXT19, FieldLocation.VAR_DATA, 0, 65535, 325, 0, 0),
         new FieldItem(TaskField.TEXT20, FieldLocation.VAR_DATA, 0, 65535, 326, 0, 0),
         new FieldItem(TaskField.TEXT21, FieldLocation.VAR_DATA, 0, 65535, 327, 0, 0),
         new FieldItem(TaskField.TEXT22, FieldLocation.VAR_DATA, 0, 65535, 328, 0, 0),
         new FieldItem(TaskField.TEXT23, FieldLocation.VAR_DATA, 0, 65535, 329, 0, 0),
         new FieldItem(TaskField.TEXT24, FieldLocation.VAR_DATA, 0, 65535, 330, 0, 0),
         new FieldItem(TaskField.TEXT25, FieldLocation.VAR_DATA, 0, 65535, 331, 0, 0),
         new FieldItem(TaskField.TEXT26, FieldLocation.VAR_DATA, 0, 65535, 332, 0, 0),
         new FieldItem(TaskField.TEXT27, FieldLocation.VAR_DATA, 0, 65535, 333, 0, 0),
         new FieldItem(TaskField.TEXT28, FieldLocation.VAR_DATA, 0, 65535, 334, 0, 0),
         new FieldItem(TaskField.TEXT29, FieldLocation.VAR_DATA, 0, 65535, 335, 0, 0),
         new FieldItem(TaskField.TEXT30, FieldLocation.VAR_DATA, 0, 65535, 336, 0, 0),
         new FieldItem(TaskField.NUMBER11, FieldLocation.VAR_DATA, 0, 65535, 307, 0, 0),
         new FieldItem(TaskField.NUMBER12, FieldLocation.VAR_DATA, 0, 65535, 308, 0, 0),
         new FieldItem(TaskField.NUMBER13, FieldLocation.VAR_DATA, 0, 65535, 309, 0, 0),
         new FieldItem(TaskField.NUMBER14, FieldLocation.VAR_DATA, 0, 65535, 310, 0, 0),
         new FieldItem(TaskField.NUMBER15, FieldLocation.VAR_DATA, 0, 65535, 311, 0, 0),
         new FieldItem(TaskField.NUMBER16, FieldLocation.VAR_DATA, 0, 65535, 312, 0, 0),
         new FieldItem(TaskField.NUMBER17, FieldLocation.VAR_DATA, 0, 65535, 313, 0, 0),
         new FieldItem(TaskField.NUMBER18, FieldLocation.VAR_DATA, 0, 65535, 314, 0, 0),
         new FieldItem(TaskField.NUMBER19, FieldLocation.VAR_DATA, 0, 65535, 315, 0, 0),
         new FieldItem(TaskField.NUMBER20, FieldLocation.VAR_DATA, 0, 65535, 316, 0, 0),
         new FieldItem(TaskField.OUTLINE_CODE1_INDEX, FieldLocation.VAR_DATA, 0, 65535, 417, 0, 0),
         new FieldItem(TaskField.OUTLINE_CODE2_INDEX, FieldLocation.VAR_DATA, 0, 65535, 419, 0, 0),
         new FieldItem(TaskField.OUTLINE_CODE3_INDEX, FieldLocation.VAR_DATA, 0, 65535, 421, 0, 0),
         new FieldItem(TaskField.OUTLINE_CODE4_INDEX, FieldLocation.VAR_DATA, 0, 65535, 423, 0, 0),
         new FieldItem(TaskField.OUTLINE_CODE5_INDEX, FieldLocation.VAR_DATA, 0, 65535, 425, 0, 0),
         new FieldItem(TaskField.OUTLINE_CODE6_INDEX, FieldLocation.VAR_DATA, 0, 65535, 427, 0, 0),
         new FieldItem(TaskField.OUTLINE_CODE7_INDEX, FieldLocation.VAR_DATA, 0, 65535, 429, 0, 0),
         new FieldItem(TaskField.OUTLINE_CODE8_INDEX, FieldLocation.VAR_DATA, 0, 65535, 431, 0, 0),
         new FieldItem(TaskField.OUTLINE_CODE9_INDEX, FieldLocation.VAR_DATA, 0, 65535, 433, 0, 0),
         new FieldItem(TaskField.OUTLINE_CODE10_INDEX, FieldLocation.VAR_DATA, 0, 65535, 435, 0, 0),
         new FieldItem(TaskField.PRELEVELED_START, FieldLocation.VAR_DATA, 0, 65535, 369, 0, 0),
         new FieldItem(TaskField.PRELEVELED_FINISH, FieldLocation.VAR_DATA, 0, 65535, 370, 0, 0),
         new FieldItem(TaskField.BASELINE_ESTIMATED_START, FieldLocation.VAR_DATA, 0, 65535, 43, 0, 0),
         new FieldItem(TaskField.BASELINE_ESTIMATED_FINISH, FieldLocation.VAR_DATA, 0, 65535, 44, 0, 0),
         new FieldItem(TaskField.BASELINE_COST, FieldLocation.VAR_DATA, 0, 65535, 6, 0, 0),
         new FieldItem(TaskField.BASELINE_WORK, FieldLocation.VAR_DATA, 0, 65535, 1, 0, 0),
         new FieldItem(TaskField.BASELINE_ESTIMATED_DURATION, FieldLocation.VAR_DATA, 0, 65535, 27, 0, 0),
         new FieldItem(TaskField.BASELINE_DURATION_UNITS, FieldLocation.VAR_DATA, 0, 65535, 179, 0, 0),
         new FieldItem(TaskField.HYPERLINK_DATA, FieldLocation.VAR_DATA, 0, 65535, 215, 0, 0),
         new FieldItem(TaskField.COST1, FieldLocation.VAR_DATA, 0, 65535, 106, 0, 0),
         new FieldItem(TaskField.COST2, FieldLocation.VAR_DATA, 0, 65535, 107, 0, 0),
         new FieldItem(TaskField.COST3, FieldLocation.VAR_DATA, 0, 65535, 108, 0, 0),
         new FieldItem(TaskField.COST4, FieldLocation.VAR_DATA, 0, 65535, 258, 0, 0),
         new FieldItem(TaskField.COST5, FieldLocation.VAR_DATA, 0, 65535, 259, 0, 0),
         new FieldItem(TaskField.COST6, FieldLocation.VAR_DATA, 0, 65535, 260, 0, 0),
         new FieldItem(TaskField.COST7, FieldLocation.VAR_DATA, 0, 65535, 261, 0, 0),
         new FieldItem(TaskField.COST8, FieldLocation.VAR_DATA, 0, 65535, 262, 0, 0),
         new FieldItem(TaskField.COST9, FieldLocation.VAR_DATA, 0, 65535, 263, 0, 0),
         new FieldItem(TaskField.COST10, FieldLocation.VAR_DATA, 0, 65535, 264, 0, 0),
         new FieldItem(TaskField.NOTES, FieldLocation.VAR_DATA, 0, 65535, 15, 0, 0),
         new FieldItem(TaskField.SUBPROJECT_FILE, FieldLocation.VAR_DATA, 0, 65535, 26, 0, 0),
         new FieldItem(TaskField.GUID, FieldLocation.FIXED_DATA, 1, 0, 1143, 0, 0),
         new FieldItem(TaskField.BASELINE_FIXED_COST_ACCRUAL, FieldLocation.FIXED_DATA, 1, 40, 1173, 0, 0),
         new FieldItem(TaskField.RECALC_OUTLINE_CODES, FieldLocation.FIXED_DATA, 1, 42, 1250, 0, 0),
         new FieldItem(TaskField.START, FieldLocation.FIXED_DATA, 1, 50, 1283, 0, 0),
         new FieldItem(TaskField.FINISH, FieldLocation.FIXED_DATA, 1, 54, 1284, 0, 0),
         new FieldItem(TaskField.MANUAL_DURATION, FieldLocation.FIXED_DATA, 1, 58, 1288, 0, 0),
         new FieldItem(TaskField.MANUAL_DURATION_UNITS, FieldLocation.FIXED_DATA, 1, 62, 1289, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATA, FieldLocation.VAR_DATA, 1, 65535, 481, 0, 0),
         new FieldItem(TaskField.BASELINE1_ESTIMATED_START, FieldLocation.VAR_DATA, 1, 65535, 482, 0, 0),
         new FieldItem(TaskField.BASELINE1_ESTIMATED_FINISH, FieldLocation.VAR_DATA, 1, 65535, 483, 0, 0),
         new FieldItem(TaskField.BASELINE1_COST, FieldLocation.VAR_DATA, 1, 65535, 484, 0, 0),
         new FieldItem(TaskField.BASELINE1_WORK, FieldLocation.VAR_DATA, 1, 65535, 485, 0, 0),
         new FieldItem(TaskField.BASELINE1_ESTIMATED_DURATION, FieldLocation.VAR_DATA, 1, 65535, 487, 0, 0),
         new FieldItem(TaskField.BASELINE1_DURATION_UNITS, FieldLocation.VAR_DATA, 1, 65535, 488, 0, 0),
         new FieldItem(TaskField.BASELINE1_FIXED_COST, FieldLocation.VAR_DATA, 1, 65535, 489, 0, 0),
         new FieldItem(TaskField.BASELINE2_ESTIMATED_START, FieldLocation.VAR_DATA, 1, 65535, 493, 0, 0),
         new FieldItem(TaskField.BASELINE2_ESTIMATED_FINISH, FieldLocation.VAR_DATA, 1, 65535, 494, 0, 0),
         new FieldItem(TaskField.BASELINE2_COST, FieldLocation.VAR_DATA, 1, 65535, 495, 0, 0),
         new FieldItem(TaskField.BASELINE2_WORK, FieldLocation.VAR_DATA, 1, 65535, 496, 0, 0),
         new FieldItem(TaskField.BASELINE2_ESTIMATED_DURATION, FieldLocation.VAR_DATA, 1, 65535, 498, 0, 0),
         new FieldItem(TaskField.BASELINE2_DURATION_UNITS, FieldLocation.VAR_DATA, 1, 65535, 499, 0, 0),
         new FieldItem(TaskField.BASELINE2_FIXED_COST, FieldLocation.VAR_DATA, 1, 65535, 500, 0, 0),
         new FieldItem(TaskField.BASELINE3_ESTIMATED_START, FieldLocation.VAR_DATA, 1, 65535, 504, 0, 0),
         new FieldItem(TaskField.BASELINE3_ESTIMATED_FINISH, FieldLocation.VAR_DATA, 1, 65535, 505, 0, 0),
         new FieldItem(TaskField.BASELINE3_COST, FieldLocation.VAR_DATA, 1, 65535, 506, 0, 0),
         new FieldItem(TaskField.BASELINE3_WORK, FieldLocation.VAR_DATA, 1, 65535, 507, 0, 0),
         new FieldItem(TaskField.BASELINE3_ESTIMATED_DURATION, FieldLocation.VAR_DATA, 1, 65535, 509, 0, 0),
         new FieldItem(TaskField.BASELINE3_DURATION_UNITS, FieldLocation.VAR_DATA, 1, 65535, 510, 0, 0),
         new FieldItem(TaskField.BASELINE3_FIXED_COST, FieldLocation.VAR_DATA, 1, 65535, 511, 0, 0),
         new FieldItem(TaskField.BASELINE4_ESTIMATED_START, FieldLocation.VAR_DATA, 1, 65535, 515, 0, 0),
         new FieldItem(TaskField.BASELINE4_ESTIMATED_FINISH, FieldLocation.VAR_DATA, 1, 65535, 516, 0, 0),
         new FieldItem(TaskField.BASELINE4_COST, FieldLocation.VAR_DATA, 1, 65535, 517, 0, 0),
         new FieldItem(TaskField.BASELINE4_WORK, FieldLocation.VAR_DATA, 1, 65535, 518, 0, 0),
         new FieldItem(TaskField.BASELINE4_ESTIMATED_DURATION, FieldLocation.VAR_DATA, 1, 65535, 520, 0, 0),
         new FieldItem(TaskField.BASELINE4_DURATION_UNITS, FieldLocation.VAR_DATA, 1, 65535, 521, 0, 0),
         new FieldItem(TaskField.BASELINE4_FIXED_COST, FieldLocation.VAR_DATA, 1, 65535, 522, 0, 0),
         new FieldItem(TaskField.BASELINE5_ESTIMATED_START, FieldLocation.VAR_DATA, 1, 65535, 526, 0, 0),
         new FieldItem(TaskField.BASELINE5_ESTIMATED_FINISH, FieldLocation.VAR_DATA, 1, 65535, 527, 0, 0),
         new FieldItem(TaskField.BASELINE5_COST, FieldLocation.VAR_DATA, 1, 65535, 528, 0, 0),
         new FieldItem(TaskField.BASELINE5_WORK, FieldLocation.VAR_DATA, 1, 65535, 529, 0, 0),
         new FieldItem(TaskField.BASELINE5_ESTIMATED_DURATION, FieldLocation.VAR_DATA, 1, 65535, 531, 0, 0),
         new FieldItem(TaskField.BASELINE5_DURATION_UNITS, FieldLocation.VAR_DATA, 1, 65535, 532, 0, 0),
         new FieldItem(TaskField.BASELINE5_FIXED_COST, FieldLocation.VAR_DATA, 1, 65535, 533, 0, 0),
         new FieldItem(TaskField.BASELINE6_ESTIMATED_START, FieldLocation.VAR_DATA, 1, 65535, 544, 0, 0),
         new FieldItem(TaskField.BASELINE6_ESTIMATED_FINISH, FieldLocation.VAR_DATA, 1, 65535, 545, 0, 0),
         new FieldItem(TaskField.BASELINE6_COST, FieldLocation.VAR_DATA, 1, 65535, 546, 0, 0),
         new FieldItem(TaskField.BASELINE6_WORK, FieldLocation.VAR_DATA, 1, 65535, 547, 0, 0),
         new FieldItem(TaskField.BASELINE6_ESTIMATED_DURATION, FieldLocation.VAR_DATA, 1, 65535, 549, 0, 0),
         new FieldItem(TaskField.BASELINE6_DURATION_UNITS, FieldLocation.VAR_DATA, 1, 65535, 550, 0, 0),
         new FieldItem(TaskField.BASELINE6_FIXED_COST, FieldLocation.VAR_DATA, 1, 65535, 551, 0, 0),
         new FieldItem(TaskField.BASELINE7_ESTIMATED_START, FieldLocation.VAR_DATA, 1, 65535, 555, 0, 0),
         new FieldItem(TaskField.BASELINE7_ESTIMATED_FINISH, FieldLocation.VAR_DATA, 1, 65535, 556, 0, 0),
         new FieldItem(TaskField.BASELINE7_COST, FieldLocation.VAR_DATA, 1, 65535, 557, 0, 0),
         new FieldItem(TaskField.BASELINE7_WORK, FieldLocation.VAR_DATA, 1, 65535, 558, 0, 0),
         new FieldItem(TaskField.BASELINE7_ESTIMATED_DURATION, FieldLocation.VAR_DATA, 1, 65535, 560, 0, 0),
         new FieldItem(TaskField.BASELINE7_DURATION_UNITS, FieldLocation.VAR_DATA, 1, 65535, 561, 0, 0),
         new FieldItem(TaskField.BASELINE7_FIXED_COST, FieldLocation.VAR_DATA, 1, 65535, 562, 0, 0),
         new FieldItem(TaskField.BASELINE8_ESTIMATED_START, FieldLocation.VAR_DATA, 1, 65535, 566, 0, 0),
         new FieldItem(TaskField.BASELINE8_ESTIMATED_FINISH, FieldLocation.VAR_DATA, 1, 65535, 567, 0, 0),
         new FieldItem(TaskField.BASELINE8_COST, FieldLocation.VAR_DATA, 1, 65535, 568, 0, 0),
         new FieldItem(TaskField.BASELINE8_WORK, FieldLocation.VAR_DATA, 1, 65535, 569, 0, 0),
         new FieldItem(TaskField.BASELINE8_ESTIMATED_DURATION, FieldLocation.VAR_DATA, 1, 65535, 571, 0, 0),
         new FieldItem(TaskField.BASELINE8_DURATION_UNITS, FieldLocation.VAR_DATA, 1, 65535, 572, 0, 0),
         new FieldItem(TaskField.BASELINE8_FIXED_COST, FieldLocation.VAR_DATA, 1, 65535, 573, 0, 0),
         new FieldItem(TaskField.BASELINE9_ESTIMATED_START, FieldLocation.VAR_DATA, 1, 65535, 577, 0, 0),
         new FieldItem(TaskField.BASELINE9_ESTIMATED_FINISH, FieldLocation.VAR_DATA, 1, 65535, 578, 0, 0),
         new FieldItem(TaskField.BASELINE9_COST, FieldLocation.VAR_DATA, 1, 65535, 579, 0, 0),
         new FieldItem(TaskField.BASELINE9_WORK, FieldLocation.VAR_DATA, 1, 65535, 580, 0, 0),
         new FieldItem(TaskField.BASELINE9_ESTIMATED_DURATION, FieldLocation.VAR_DATA, 1, 65535, 582, 0, 0),
         new FieldItem(TaskField.BASELINE9_DURATION_UNITS, FieldLocation.VAR_DATA, 1, 65535, 583, 0, 0),
         new FieldItem(TaskField.BASELINE9_FIXED_COST, FieldLocation.VAR_DATA, 1, 65535, 584, 0, 0),
         new FieldItem(TaskField.BASELINE10_ESTIMATED_START, FieldLocation.VAR_DATA, 1, 65535, 588, 0, 0),
         new FieldItem(TaskField.BASELINE10_ESTIMATED_FINISH, FieldLocation.VAR_DATA, 1, 65535, 589, 0, 0),
         new FieldItem(TaskField.BASELINE10_COST, FieldLocation.VAR_DATA, 1, 65535, 590, 0, 0),
         new FieldItem(TaskField.BASELINE10_WORK, FieldLocation.VAR_DATA, 1, 65535, 591, 0, 0),
         new FieldItem(TaskField.BASELINE10_ESTIMATED_DURATION, FieldLocation.VAR_DATA, 1, 65535, 593, 0, 0),
         new FieldItem(TaskField.BASELINE10_DURATION_UNITS, FieldLocation.VAR_DATA, 1, 65535, 594, 0, 0),
         new FieldItem(TaskField.BASELINE10_FIXED_COST, FieldLocation.VAR_DATA, 1, 65535, 595, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_COST1, FieldLocation.VAR_DATA, 1, 65535, 599, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_COST2, FieldLocation.VAR_DATA, 1, 65535, 600, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_COST3, FieldLocation.VAR_DATA, 1, 65535, 601, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_COST4, FieldLocation.VAR_DATA, 1, 65535, 602, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_COST5, FieldLocation.VAR_DATA, 1, 65535, 603, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_COST6, FieldLocation.VAR_DATA, 1, 65535, 604, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_COST7, FieldLocation.VAR_DATA, 1, 65535, 605, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_COST8, FieldLocation.VAR_DATA, 1, 65535, 606, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_COST9, FieldLocation.VAR_DATA, 1, 65535, 607, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_COST10, FieldLocation.VAR_DATA, 1, 65535, 608, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE1, FieldLocation.VAR_DATA, 1, 65535, 609, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE2, FieldLocation.VAR_DATA, 1, 65535, 610, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE3, FieldLocation.VAR_DATA, 1, 65535, 611, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE4, FieldLocation.VAR_DATA, 1, 65535, 612, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE5, FieldLocation.VAR_DATA, 1, 65535, 613, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE6, FieldLocation.VAR_DATA, 1, 65535, 614, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE7, FieldLocation.VAR_DATA, 1, 65535, 615, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE8, FieldLocation.VAR_DATA, 1, 65535, 616, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE9, FieldLocation.VAR_DATA, 1, 65535, 617, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE10, FieldLocation.VAR_DATA, 1, 65535, 618, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE11, FieldLocation.VAR_DATA, 1, 65535, 619, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE12, FieldLocation.VAR_DATA, 1, 65535, 620, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE13, FieldLocation.VAR_DATA, 1, 65535, 621, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE14, FieldLocation.VAR_DATA, 1, 65535, 622, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE15, FieldLocation.VAR_DATA, 1, 65535, 623, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE16, FieldLocation.VAR_DATA, 1, 65535, 624, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE17, FieldLocation.VAR_DATA, 1, 65535, 625, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE18, FieldLocation.VAR_DATA, 1, 65535, 626, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE19, FieldLocation.VAR_DATA, 1, 65535, 627, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE20, FieldLocation.VAR_DATA, 1, 65535, 628, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE21, FieldLocation.VAR_DATA, 1, 65535, 629, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE22, FieldLocation.VAR_DATA, 1, 65535, 630, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE23, FieldLocation.VAR_DATA, 1, 65535, 631, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE24, FieldLocation.VAR_DATA, 1, 65535, 632, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE25, FieldLocation.VAR_DATA, 1, 65535, 633, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE26, FieldLocation.VAR_DATA, 1, 65535, 634, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE27, FieldLocation.VAR_DATA, 1, 65535, 635, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE28, FieldLocation.VAR_DATA, 1, 65535, 636, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE29, FieldLocation.VAR_DATA, 1, 65535, 637, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DATE30, FieldLocation.VAR_DATA, 1, 65535, 638, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DURATION1, FieldLocation.VAR_DATA, 1, 65535, 639, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DURATION1_UNITS, FieldLocation.VAR_DATA, 1, 65535, 649, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DURATION2, FieldLocation.VAR_DATA, 1, 65535, 640, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DURATION2_UNITS, FieldLocation.VAR_DATA, 1, 65535, 650, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DURATION3, FieldLocation.VAR_DATA, 1, 65535, 641, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DURATION3_UNITS, FieldLocation.VAR_DATA, 1, 65535, 651, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DURATION4, FieldLocation.VAR_DATA, 1, 65535, 642, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DURATION4_UNITS, FieldLocation.VAR_DATA, 1, 65535, 652, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DURATION5, FieldLocation.VAR_DATA, 1, 65535, 643, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DURATION5_UNITS, FieldLocation.VAR_DATA, 1, 65535, 653, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DURATION6, FieldLocation.VAR_DATA, 1, 65535, 644, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DURATION6_UNITS, FieldLocation.VAR_DATA, 1, 65535, 654, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DURATION7, FieldLocation.VAR_DATA, 1, 65535, 645, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DURATION7_UNITS, FieldLocation.VAR_DATA, 1, 65535, 655, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DURATION8, FieldLocation.VAR_DATA, 1, 65535, 646, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DURATION8_UNITS, FieldLocation.VAR_DATA, 1, 65535, 656, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DURATION9, FieldLocation.VAR_DATA, 1, 65535, 647, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DURATION9_UNITS, FieldLocation.VAR_DATA, 1, 65535, 657, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DURATION10, FieldLocation.VAR_DATA, 1, 65535, 648, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_DURATION10_UNITS, FieldLocation.VAR_DATA, 1, 65535, 658, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER1, FieldLocation.VAR_DATA, 1, 65535, 699, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER2, FieldLocation.VAR_DATA, 1, 65535, 700, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER3, FieldLocation.VAR_DATA, 1, 65535, 701, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER4, FieldLocation.VAR_DATA, 1, 65535, 702, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER5, FieldLocation.VAR_DATA, 1, 65535, 703, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER6, FieldLocation.VAR_DATA, 1, 65535, 704, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER7, FieldLocation.VAR_DATA, 1, 65535, 705, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER8, FieldLocation.VAR_DATA, 1, 65535, 706, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER9, FieldLocation.VAR_DATA, 1, 65535, 707, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER10, FieldLocation.VAR_DATA, 1, 65535, 708, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER11, FieldLocation.VAR_DATA, 1, 65535, 709, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER12, FieldLocation.VAR_DATA, 1, 65535, 710, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER13, FieldLocation.VAR_DATA, 1, 65535, 711, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER14, FieldLocation.VAR_DATA, 1, 65535, 712, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER15, FieldLocation.VAR_DATA, 1, 65535, 713, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER16, FieldLocation.VAR_DATA, 1, 65535, 714, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER17, FieldLocation.VAR_DATA, 1, 65535, 715, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER18, FieldLocation.VAR_DATA, 1, 65535, 716, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER19, FieldLocation.VAR_DATA, 1, 65535, 717, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER20, FieldLocation.VAR_DATA, 1, 65535, 718, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER21, FieldLocation.VAR_DATA, 1, 65535, 719, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER22, FieldLocation.VAR_DATA, 1, 65535, 720, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER23, FieldLocation.VAR_DATA, 1, 65535, 721, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER24, FieldLocation.VAR_DATA, 1, 65535, 722, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER25, FieldLocation.VAR_DATA, 1, 65535, 723, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER26, FieldLocation.VAR_DATA, 1, 65535, 724, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER27, FieldLocation.VAR_DATA, 1, 65535, 725, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER28, FieldLocation.VAR_DATA, 1, 65535, 726, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER29, FieldLocation.VAR_DATA, 1, 65535, 727, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER30, FieldLocation.VAR_DATA, 1, 65535, 728, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER31, FieldLocation.VAR_DATA, 1, 65535, 729, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER32, FieldLocation.VAR_DATA, 1, 65535, 730, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER33, FieldLocation.VAR_DATA, 1, 65535, 731, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER34, FieldLocation.VAR_DATA, 1, 65535, 732, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER35, FieldLocation.VAR_DATA, 1, 65535, 733, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER36, FieldLocation.VAR_DATA, 1, 65535, 734, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER37, FieldLocation.VAR_DATA, 1, 65535, 735, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER38, FieldLocation.VAR_DATA, 1, 65535, 736, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER39, FieldLocation.VAR_DATA, 1, 65535, 737, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_NUMBER40, FieldLocation.VAR_DATA, 1, 65535, 738, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT1, FieldLocation.VAR_DATA, 1, 65535, 799, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT2, FieldLocation.VAR_DATA, 1, 65535, 800, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT3, FieldLocation.VAR_DATA, 1, 65535, 801, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT4, FieldLocation.VAR_DATA, 1, 65535, 802, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT5, FieldLocation.VAR_DATA, 1, 65535, 803, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT6, FieldLocation.VAR_DATA, 1, 65535, 804, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT7, FieldLocation.VAR_DATA, 1, 65535, 805, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT8, FieldLocation.VAR_DATA, 1, 65535, 806, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT9, FieldLocation.VAR_DATA, 1, 65535, 807, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT10, FieldLocation.VAR_DATA, 1, 65535, 808, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT11, FieldLocation.VAR_DATA, 1, 65535, 809, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT12, FieldLocation.VAR_DATA, 1, 65535, 810, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT13, FieldLocation.VAR_DATA, 1, 65535, 811, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT14, FieldLocation.VAR_DATA, 1, 65535, 812, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT15, FieldLocation.VAR_DATA, 1, 65535, 813, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT16, FieldLocation.VAR_DATA, 1, 65535, 814, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT17, FieldLocation.VAR_DATA, 1, 65535, 815, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT18, FieldLocation.VAR_DATA, 1, 65535, 816, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT19, FieldLocation.VAR_DATA, 1, 65535, 817, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT20, FieldLocation.VAR_DATA, 1, 65535, 818, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT21, FieldLocation.VAR_DATA, 1, 65535, 819, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT22, FieldLocation.VAR_DATA, 1, 65535, 820, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT23, FieldLocation.VAR_DATA, 1, 65535, 821, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT24, FieldLocation.VAR_DATA, 1, 65535, 822, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT25, FieldLocation.VAR_DATA, 1, 65535, 823, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT26, FieldLocation.VAR_DATA, 1, 65535, 824, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT27, FieldLocation.VAR_DATA, 1, 65535, 825, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT28, FieldLocation.VAR_DATA, 1, 65535, 826, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT29, FieldLocation.VAR_DATA, 1, 65535, 827, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT30, FieldLocation.VAR_DATA, 1, 65535, 828, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT31, FieldLocation.VAR_DATA, 1, 65535, 829, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT32, FieldLocation.VAR_DATA, 1, 65535, 830, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT33, FieldLocation.VAR_DATA, 1, 65535, 831, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT34, FieldLocation.VAR_DATA, 1, 65535, 832, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT35, FieldLocation.VAR_DATA, 1, 65535, 833, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT36, FieldLocation.VAR_DATA, 1, 65535, 834, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT37, FieldLocation.VAR_DATA, 1, 65535, 835, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT38, FieldLocation.VAR_DATA, 1, 65535, 836, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT39, FieldLocation.VAR_DATA, 1, 65535, 837, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_TEXT40, FieldLocation.VAR_DATA, 1, 65535, 838, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_COST1, FieldLocation.VAR_DATA, 1, 65535, 849, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_COST2, FieldLocation.VAR_DATA, 1, 65535, 850, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_COST3, FieldLocation.VAR_DATA, 1, 65535, 851, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_COST4, FieldLocation.VAR_DATA, 1, 65535, 852, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_COST5, FieldLocation.VAR_DATA, 1, 65535, 853, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_COST6, FieldLocation.VAR_DATA, 1, 65535, 854, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_COST7, FieldLocation.VAR_DATA, 1, 65535, 855, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_COST8, FieldLocation.VAR_DATA, 1, 65535, 856, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_COST9, FieldLocation.VAR_DATA, 1, 65535, 857, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_COST10, FieldLocation.VAR_DATA, 1, 65535, 858, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE1, FieldLocation.VAR_DATA, 1, 65535, 859, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE2, FieldLocation.VAR_DATA, 1, 65535, 860, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE3, FieldLocation.VAR_DATA, 1, 65535, 861, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE4, FieldLocation.VAR_DATA, 1, 65535, 862, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE5, FieldLocation.VAR_DATA, 1, 65535, 863, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE6, FieldLocation.VAR_DATA, 1, 65535, 864, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE7, FieldLocation.VAR_DATA, 1, 65535, 865, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE8, FieldLocation.VAR_DATA, 1, 65535, 866, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE9, FieldLocation.VAR_DATA, 1, 65535, 867, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE10, FieldLocation.VAR_DATA, 1, 65535, 868, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE11, FieldLocation.VAR_DATA, 1, 65535, 869, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE12, FieldLocation.VAR_DATA, 1, 65535, 870, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE13, FieldLocation.VAR_DATA, 1, 65535, 871, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE14, FieldLocation.VAR_DATA, 1, 65535, 872, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE15, FieldLocation.VAR_DATA, 1, 65535, 873, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE16, FieldLocation.VAR_DATA, 1, 65535, 874, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE17, FieldLocation.VAR_DATA, 1, 65535, 875, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE18, FieldLocation.VAR_DATA, 1, 65535, 876, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE19, FieldLocation.VAR_DATA, 1, 65535, 877, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE20, FieldLocation.VAR_DATA, 1, 65535, 878, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE21, FieldLocation.VAR_DATA, 1, 65535, 879, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE22, FieldLocation.VAR_DATA, 1, 65535, 880, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE23, FieldLocation.VAR_DATA, 1, 65535, 881, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE24, FieldLocation.VAR_DATA, 1, 65535, 882, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE25, FieldLocation.VAR_DATA, 1, 65535, 883, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE26, FieldLocation.VAR_DATA, 1, 65535, 884, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE27, FieldLocation.VAR_DATA, 1, 65535, 885, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE28, FieldLocation.VAR_DATA, 1, 65535, 886, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE29, FieldLocation.VAR_DATA, 1, 65535, 887, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DATE30, FieldLocation.VAR_DATA, 1, 65535, 888, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DURATION1, FieldLocation.VAR_DATA, 1, 65535, 889, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DURATION2, FieldLocation.VAR_DATA, 1, 65535, 890, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DURATION3, FieldLocation.VAR_DATA, 1, 65535, 891, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DURATION4, FieldLocation.VAR_DATA, 1, 65535, 892, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DURATION5, FieldLocation.VAR_DATA, 1, 65535, 893, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DURATION6, FieldLocation.VAR_DATA, 1, 65535, 894, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DURATION7, FieldLocation.VAR_DATA, 1, 65535, 895, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DURATION8, FieldLocation.VAR_DATA, 1, 65535, 896, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DURATION9, FieldLocation.VAR_DATA, 1, 65535, 897, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_DURATION10, FieldLocation.VAR_DATA, 1, 65535, 898, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER1, FieldLocation.VAR_DATA, 1, 65535, 1009, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER2, FieldLocation.VAR_DATA, 1, 65535, 1010, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER3, FieldLocation.VAR_DATA, 1, 65535, 1011, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER4, FieldLocation.VAR_DATA, 1, 65535, 1012, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER5, FieldLocation.VAR_DATA, 1, 65535, 1013, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER6, FieldLocation.VAR_DATA, 1, 65535, 1014, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER7, FieldLocation.VAR_DATA, 1, 65535, 1015, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER8, FieldLocation.VAR_DATA, 1, 65535, 1016, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER9, FieldLocation.VAR_DATA, 1, 65535, 1017, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER10, FieldLocation.VAR_DATA, 1, 65535, 1018, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER11, FieldLocation.VAR_DATA, 1, 65535, 1019, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER12, FieldLocation.VAR_DATA, 1, 65535, 1020, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER13, FieldLocation.VAR_DATA, 1, 65535, 1021, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER14, FieldLocation.VAR_DATA, 1, 65535, 1022, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER15, FieldLocation.VAR_DATA, 1, 65535, 1023, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER16, FieldLocation.VAR_DATA, 1, 65535, 1024, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER17, FieldLocation.VAR_DATA, 1, 65535, 1025, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER18, FieldLocation.VAR_DATA, 1, 65535, 1026, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER19, FieldLocation.VAR_DATA, 1, 65535, 1027, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER20, FieldLocation.VAR_DATA, 1, 65535, 1028, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER21, FieldLocation.VAR_DATA, 1, 65535, 1029, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER22, FieldLocation.VAR_DATA, 1, 65535, 1030, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER23, FieldLocation.VAR_DATA, 1, 65535, 1031, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER24, FieldLocation.VAR_DATA, 1, 65535, 1032, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER25, FieldLocation.VAR_DATA, 1, 65535, 1033, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER26, FieldLocation.VAR_DATA, 1, 65535, 1034, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER27, FieldLocation.VAR_DATA, 1, 65535, 1035, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER28, FieldLocation.VAR_DATA, 1, 65535, 1036, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER29, FieldLocation.VAR_DATA, 1, 65535, 1037, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER30, FieldLocation.VAR_DATA, 1, 65535, 1038, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER31, FieldLocation.VAR_DATA, 1, 65535, 1039, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER32, FieldLocation.VAR_DATA, 1, 65535, 1040, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER33, FieldLocation.VAR_DATA, 1, 65535, 1041, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER34, FieldLocation.VAR_DATA, 1, 65535, 1042, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER35, FieldLocation.VAR_DATA, 1, 65535, 1043, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER36, FieldLocation.VAR_DATA, 1, 65535, 1044, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER37, FieldLocation.VAR_DATA, 1, 65535, 1045, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER38, FieldLocation.VAR_DATA, 1, 65535, 1046, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER39, FieldLocation.VAR_DATA, 1, 65535, 1047, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_NUMBER40, FieldLocation.VAR_DATA, 1, 65535, 1048, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT1, FieldLocation.VAR_DATA, 1, 65535, 1049, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT2, FieldLocation.VAR_DATA, 1, 65535, 1050, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT3, FieldLocation.VAR_DATA, 1, 65535, 1051, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT4, FieldLocation.VAR_DATA, 1, 65535, 1052, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT5, FieldLocation.VAR_DATA, 1, 65535, 1053, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT6, FieldLocation.VAR_DATA, 1, 65535, 1054, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT7, FieldLocation.VAR_DATA, 1, 65535, 1055, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT8, FieldLocation.VAR_DATA, 1, 65535, 1056, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT9, FieldLocation.VAR_DATA, 1, 65535, 1057, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT10, FieldLocation.VAR_DATA, 1, 65535, 1058, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT11, FieldLocation.VAR_DATA, 1, 65535, 1059, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT12, FieldLocation.VAR_DATA, 1, 65535, 1060, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT13, FieldLocation.VAR_DATA, 1, 65535, 1061, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT14, FieldLocation.VAR_DATA, 1, 65535, 1062, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT15, FieldLocation.VAR_DATA, 1, 65535, 1063, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT16, FieldLocation.VAR_DATA, 1, 65535, 1064, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT17, FieldLocation.VAR_DATA, 1, 65535, 1065, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT18, FieldLocation.VAR_DATA, 1, 65535, 1066, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT19, FieldLocation.VAR_DATA, 1, 65535, 1067, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT20, FieldLocation.VAR_DATA, 1, 65535, 1068, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT21, FieldLocation.VAR_DATA, 1, 65535, 1069, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT22, FieldLocation.VAR_DATA, 1, 65535, 1070, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT23, FieldLocation.VAR_DATA, 1, 65535, 1071, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT24, FieldLocation.VAR_DATA, 1, 65535, 1072, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT25, FieldLocation.VAR_DATA, 1, 65535, 1073, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT26, FieldLocation.VAR_DATA, 1, 65535, 1074, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT27, FieldLocation.VAR_DATA, 1, 65535, 1075, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT28, FieldLocation.VAR_DATA, 1, 65535, 1076, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT29, FieldLocation.VAR_DATA, 1, 65535, 1077, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT30, FieldLocation.VAR_DATA, 1, 65535, 1078, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT31, FieldLocation.VAR_DATA, 1, 65535, 1079, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT32, FieldLocation.VAR_DATA, 1, 65535, 1080, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT33, FieldLocation.VAR_DATA, 1, 65535, 1081, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT34, FieldLocation.VAR_DATA, 1, 65535, 1082, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT35, FieldLocation.VAR_DATA, 1, 65535, 1083, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT36, FieldLocation.VAR_DATA, 1, 65535, 1084, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT37, FieldLocation.VAR_DATA, 1, 65535, 1085, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT38, FieldLocation.VAR_DATA, 1, 65535, 1086, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT39, FieldLocation.VAR_DATA, 1, 65535, 1087, 0, 0),
         new FieldItem(TaskField.ENTERPRISE_PROJECT_TEXT40, FieldLocation.VAR_DATA, 1, 65535, 1088, 0, 0),
         new FieldItem(TaskField.PHYSICAL_PERCENT_COMPLETE, FieldLocation.VAR_DATA, 1, 65535, 1119, 0, 0),
         new FieldItem(TaskField.EARNED_VALUE_METHOD, FieldLocation.VAR_DATA, 26, 65535, 1122, 0, 0),
         new FieldItem(TaskField.ACTUAL_WORK_PROTECTED, FieldLocation.VAR_DATA, 1, 65535, 1139, 0, 0),
         new FieldItem(TaskField.ACTUAL_OVERTIME_WORK_PROTECTED, FieldLocation.VAR_DATA, 1, 65535, 1140, 0, 0),
         new FieldItem(TaskField.BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 1171, 0, 0),
         new FieldItem(TaskField.BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 1172, 0, 0),
         new FieldItem(TaskField.BASELINE_DELIVERABLE_START, FieldLocation.VAR_DATA, 1, 65535, 1174, 0, 0),
         new FieldItem(TaskField.BASELINE_DELIVERABLE_FINISH, FieldLocation.VAR_DATA, 1, 65535, 1175, 0, 0),
         new FieldItem(TaskField.BASELINE_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 1176, 0, 0),
         new FieldItem(TaskField.BASELINE_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 1177, 0, 0),
         new FieldItem(TaskField.BASELINE1_FIXED_COST_ACCRUAL, FieldLocation.VAR_DATA, 1, 65535, 1180, 0, 0),
         new FieldItem(TaskField.BASELINE1_DELIVERABLE_START, FieldLocation.VAR_DATA, 1, 65535, 1181, 0, 0),
         new FieldItem(TaskField.BASELINE1_DELIVERABLE_FINISH, FieldLocation.VAR_DATA, 1, 65535, 1182, 0, 0),
         new FieldItem(TaskField.BASELINE1_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 1183, 0, 0),
         new FieldItem(TaskField.BASELINE1_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 1184, 0, 0),
         new FieldItem(TaskField.BASELINE2_FIXED_COST_ACCRUAL, FieldLocation.VAR_DATA, 1, 65535, 1187, 0, 0),
         new FieldItem(TaskField.BASELINE2_DELIVERABLE_START, FieldLocation.VAR_DATA, 1, 65535, 1188, 0, 0),
         new FieldItem(TaskField.BASELINE2_DELIVERABLE_FINISH, FieldLocation.VAR_DATA, 1, 65535, 1189, 0, 0),
         new FieldItem(TaskField.BASELINE2_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 1190, 0, 0),
         new FieldItem(TaskField.BASELINE2_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 1191, 0, 0),
         new FieldItem(TaskField.BASELINE3_FIXED_COST_ACCRUAL, FieldLocation.VAR_DATA, 1, 65535, 1194, 0, 0),
         new FieldItem(TaskField.BASELINE3_DELIVERABLE_START, FieldLocation.VAR_DATA, 1, 65535, 1195, 0, 0),
         new FieldItem(TaskField.BASELINE3_DELIVERABLE_FINISH, FieldLocation.VAR_DATA, 1, 65535, 1196, 0, 0),
         new FieldItem(TaskField.BASELINE3_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 1197, 0, 0),
         new FieldItem(TaskField.BASELINE3_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 1198, 0, 0),
         new FieldItem(TaskField.BASELINE4_FIXED_COST_ACCRUAL, FieldLocation.VAR_DATA, 1, 65535, 1201, 0, 0),
         new FieldItem(TaskField.BASELINE4_DELIVERABLE_START, FieldLocation.VAR_DATA, 1, 65535, 1202, 0, 0),
         new FieldItem(TaskField.BASELINE4_DELIVERABLE_FINISH, FieldLocation.VAR_DATA, 1, 65535, 1203, 0, 0),
         new FieldItem(TaskField.BASELINE4_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 1204, 0, 0),
         new FieldItem(TaskField.BASELINE4_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 1205, 0, 0),
         new FieldItem(TaskField.BASELINE5_FIXED_COST_ACCRUAL, FieldLocation.VAR_DATA, 1, 65535, 1208, 0, 0),
         new FieldItem(TaskField.BASELINE5_DELIVERABLE_START, FieldLocation.VAR_DATA, 1, 65535, 1209, 0, 0),
         new FieldItem(TaskField.BASELINE5_DELIVERABLE_FINISH, FieldLocation.VAR_DATA, 1, 65535, 1210, 0, 0),
         new FieldItem(TaskField.BASELINE5_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 1211, 0, 0),
         new FieldItem(TaskField.BASELINE5_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 1212, 0, 0),
         new FieldItem(TaskField.BASELINE6_FIXED_COST_ACCRUAL, FieldLocation.VAR_DATA, 1, 65535, 1215, 0, 0),
         new FieldItem(TaskField.BASELINE6_DELIVERABLE_START, FieldLocation.VAR_DATA, 1, 65535, 1216, 0, 0),
         new FieldItem(TaskField.BASELINE6_DELIVERABLE_FINISH, FieldLocation.VAR_DATA, 1, 65535, 1217, 0, 0),
         new FieldItem(TaskField.BASELINE6_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 1218, 0, 0),
         new FieldItem(TaskField.BASELINE6_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 1219, 0, 0),
         new FieldItem(TaskField.BASELINE7_FIXED_COST_ACCRUAL, FieldLocation.VAR_DATA, 1, 65535, 1222, 0, 0),
         new FieldItem(TaskField.BASELINE7_DELIVERABLE_START, FieldLocation.VAR_DATA, 1, 65535, 1223, 0, 0),
         new FieldItem(TaskField.BASELINE7_DELIVERABLE_FINISH, FieldLocation.VAR_DATA, 1, 65535, 1224, 0, 0),
         new FieldItem(TaskField.BASELINE7_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 1225, 0, 0),
         new FieldItem(TaskField.BASELINE7_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 1226, 0, 0),
         new FieldItem(TaskField.BASELINE8_FIXED_COST_ACCRUAL, FieldLocation.VAR_DATA, 1, 65535, 1229, 0, 0),
         new FieldItem(TaskField.BASELINE8_DELIVERABLE_START, FieldLocation.VAR_DATA, 1, 65535, 1230, 0, 0),
         new FieldItem(TaskField.BASELINE8_DELIVERABLE_FINISH, FieldLocation.VAR_DATA, 1, 65535, 1231, 0, 0),
         new FieldItem(TaskField.BASELINE8_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 1232, 0, 0),
         new FieldItem(TaskField.BASELINE8_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 1233, 0, 0),
         new FieldItem(TaskField.BASELINE9_FIXED_COST_ACCRUAL, FieldLocation.VAR_DATA, 1, 65535, 1236, 0, 0),
         new FieldItem(TaskField.BASELINE9_DELIVERABLE_START, FieldLocation.VAR_DATA, 1, 65535, 1237, 0, 0),
         new FieldItem(TaskField.BASELINE9_DELIVERABLE_FINISH, FieldLocation.VAR_DATA, 1, 65535, 1238, 0, 0),
         new FieldItem(TaskField.BASELINE9_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 1239, 0, 0),
         new FieldItem(TaskField.BASELINE9_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 1240, 0, 0),
         new FieldItem(TaskField.BASELINE10_FIXED_COST_ACCRUAL, FieldLocation.VAR_DATA, 1, 65535, 1243, 0, 0),
         new FieldItem(TaskField.BASELINE10_DELIVERABLE_START, FieldLocation.VAR_DATA, 1, 65535, 1244, 0, 0),
         new FieldItem(TaskField.BASELINE10_DELIVERABLE_FINISH, FieldLocation.VAR_DATA, 1, 65535, 1245, 0, 0),
         new FieldItem(TaskField.BASELINE10_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 1246, 0, 0),
         new FieldItem(TaskField.BASELINE10_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 1247, 0, 0),
         new FieldItem(TaskField.DELIVERABLE_GUID, FieldLocation.VAR_DATA, 1, 65535, 1146, 0, 0),
         new FieldItem(TaskField.DELIVERABLE_NAME, FieldLocation.VAR_DATA, 1, 65535, 1276, 0, 0),
         new FieldItem(TaskField.DELIVERABLE_START, FieldLocation.VAR_DATA, 1, 65535, 1152, 0, 0),
         new FieldItem(TaskField.DELIVERABLE_FINISH, FieldLocation.VAR_DATA, 1, 65535, 1153, 0, 0),
         new FieldItem(TaskField.DELIVERABLE_TYPE, FieldLocation.VAR_DATA, 1, 65535, 1147, 0, 0),
         new FieldItem(TaskField.TASK_CALENDAR_GUID, FieldLocation.VAR_DATA, 1, 65535, 1144, 0, 0),
         new FieldItem(TaskField.START_TEXT, FieldLocation.VAR_DATA, 1, 65535, 1285, 0, 0),
         new FieldItem(TaskField.FINISH_TEXT, FieldLocation.VAR_DATA, 1, 65535, 1286, 0, 0),
         new FieldItem(TaskField.DURATION_TEXT, FieldLocation.VAR_DATA, 1, 65535, 1287, 0, 0),
         new FieldItem(TaskField.BASELINE_START, FieldLocation.VAR_DATA, 1, 65535, 1299, 0, 0),
         new FieldItem(TaskField.BASELINE_FINISH, FieldLocation.VAR_DATA, 1, 65535, 1300, 0, 0),
         new FieldItem(TaskField.BASELINE_DURATION, FieldLocation.VAR_DATA, 1, 65535, 1301, 0, 0),
         new FieldItem(TaskField.BASELINE1_START, FieldLocation.VAR_DATA, 1, 65535, 1302, 0, 0),
         new FieldItem(TaskField.BASELINE1_FINISH, FieldLocation.VAR_DATA, 1, 65535, 1303, 0, 0),
         new FieldItem(TaskField.BASELINE1_DURATION, FieldLocation.VAR_DATA, 1, 65535, 1304, 0, 0),
         new FieldItem(TaskField.BASELINE2_START, FieldLocation.VAR_DATA, 1, 65535, 1305, 0, 0),
         new FieldItem(TaskField.BASELINE2_FINISH, FieldLocation.VAR_DATA, 1, 65535, 1306, 0, 0),
         new FieldItem(TaskField.BASELINE2_DURATION, FieldLocation.VAR_DATA, 1, 65535, 1307, 0, 0),
         new FieldItem(TaskField.BASELINE3_START, FieldLocation.VAR_DATA, 1, 65535, 1308, 0, 0),
         new FieldItem(TaskField.BASELINE3_FINISH, FieldLocation.VAR_DATA, 1, 65535, 1309, 0, 0),
         new FieldItem(TaskField.BASELINE3_DURATION, FieldLocation.VAR_DATA, 1, 65535, 1310, 0, 0),
         new FieldItem(TaskField.BASELINE4_START, FieldLocation.VAR_DATA, 1, 65535, 1311, 0, 0),
         new FieldItem(TaskField.BASELINE4_FINISH, FieldLocation.VAR_DATA, 1, 65535, 1312, 0, 0),
         new FieldItem(TaskField.BASELINE4_DURATION, FieldLocation.VAR_DATA, 1, 65535, 1313, 0, 0),
         new FieldItem(TaskField.BASELINE5_START, FieldLocation.VAR_DATA, 1, 65535, 1314, 0, 0),
         new FieldItem(TaskField.BASELINE5_FINISH, FieldLocation.VAR_DATA, 1, 65535, 1315, 0, 0),
         new FieldItem(TaskField.BASELINE5_DURATION, FieldLocation.VAR_DATA, 1, 65535, 1316, 0, 0),
         new FieldItem(TaskField.BASELINE6_START, FieldLocation.VAR_DATA, 1, 65535, 1317, 0, 0),
         new FieldItem(TaskField.BASELINE6_FINISH, FieldLocation.VAR_DATA, 1, 65535, 1318, 0, 0),
         new FieldItem(TaskField.BASELINE6_DURATION, FieldLocation.VAR_DATA, 1, 65535, 1319, 0, 0),
         new FieldItem(TaskField.BASELINE7_START, FieldLocation.VAR_DATA, 1, 65535, 1320, 0, 0),
         new FieldItem(TaskField.BASELINE7_FINISH, FieldLocation.VAR_DATA, 1, 65535, 1321, 0, 0),
         new FieldItem(TaskField.BASELINE7_DURATION, FieldLocation.VAR_DATA, 1, 65535, 1322, 0, 0),
         new FieldItem(TaskField.BASELINE8_START, FieldLocation.VAR_DATA, 1, 65535, 1323, 0, 0),
         new FieldItem(TaskField.BASELINE8_FINISH, FieldLocation.VAR_DATA, 1, 65535, 1324, 0, 0),
         new FieldItem(TaskField.BASELINE8_DURATION, FieldLocation.VAR_DATA, 1, 65535, 1325, 0, 0),
         new FieldItem(TaskField.BASELINE9_START, FieldLocation.VAR_DATA, 1, 65535, 1326, 0, 0),
         new FieldItem(TaskField.BASELINE9_FINISH, FieldLocation.VAR_DATA, 1, 65535, 1327, 0, 0),
         new FieldItem(TaskField.BASELINE9_DURATION, FieldLocation.VAR_DATA, 1, 65535, 1328, 0, 0),
         new FieldItem(TaskField.BASELINE10_START, FieldLocation.VAR_DATA, 1, 65535, 1329, 0, 0),
         new FieldItem(TaskField.BASELINE10_FINISH, FieldLocation.VAR_DATA, 1, 65535, 1330, 0, 0),
         new FieldItem(TaskField.BASELINE10_DURATION, FieldLocation.VAR_DATA, 1, 65535, 1331, 0, 0)
      };
   }

   @Override protected FieldItem[] getDefaultResourceData()
   {
      return new FieldItem[]
      {
         new FieldItem(ResourceField.UNIQUE_ID, FieldLocation.FIXED_DATA, 0, 0, 27, 0, 0),
         new FieldItem(ResourceField.ID, FieldLocation.FIXED_DATA, 0, 4, 0, 0, 0),
         new FieldItem(ResourceField.STANDARD_RATE_UNITS, FieldLocation.FIXED_DATA, 0, 8, 70, 0, 0),
         new FieldItem(ResourceField.OVERTIME_RATE_UNITS, FieldLocation.FIXED_DATA, 0, 10, 71, 0, 0),
         new FieldItem(ResourceField.ACCRUE_AT, FieldLocation.FIXED_DATA, 0, 12, 19, 0, 0),
         new FieldItem(ResourceField.WORKGROUP, FieldLocation.FIXED_DATA, 0, 14, 272, 0, 0),
         new FieldItem(ResourceField.AVAILABLE_FROM, FieldLocation.FIXED_DATA, 0, 20, 57, 0, 0),
         new FieldItem(ResourceField.AVAILABLE_TO, FieldLocation.FIXED_DATA, 0, 24, 58, 0, 0),
         new FieldItem(ResourceField.STANDARD_RATE, FieldLocation.FIXED_DATA, 0, 28, 6, 0, 0),
         new FieldItem(ResourceField.OVERTIME_RATE, FieldLocation.FIXED_DATA, 0, 36, 7, 0, 0),
         new FieldItem(ResourceField.MAX_UNITS, FieldLocation.FIXED_DATA, 0, 44, 4, 0, 0),
         new FieldItem(ResourceField.WORK, FieldLocation.FIXED_DATA, 0, 52, 13, 0, 0),
         new FieldItem(ResourceField.ACTUAL_WORK, FieldLocation.FIXED_DATA, 0, 60, 14, 0, 0),
         new FieldItem(ResourceField.BASELINE_WORK, FieldLocation.FIXED_DATA, 0, 68, 15, 0, 0),
         new FieldItem(ResourceField.OVERTIME_WORK, FieldLocation.FIXED_DATA, 0, 76, 16, 0, 0),
         new FieldItem(ResourceField.COST_PER_USE, FieldLocation.FIXED_DATA, 0, 84, 18, 0, 0),
         new FieldItem(ResourceField.REMAINING_WORK, FieldLocation.FIXED_DATA, 0, 92, 22, 0, 0),
         new FieldItem(ResourceField.REGULAR_WORK, FieldLocation.FIXED_DATA, 0, 100, 38, 0, 0),
         new FieldItem(ResourceField.ACTUAL_OVERTIME_WORK, FieldLocation.FIXED_DATA, 0, 108, 39, 0, 0),
         new FieldItem(ResourceField.REMAINING_OVERTIME_WORK, FieldLocation.FIXED_DATA, 0, 116, 40, 0, 0),
         new FieldItem(ResourceField.PEAK, FieldLocation.FIXED_DATA, 0, 124, 26, 0, 0),
         new FieldItem(ResourceField.ACTUAL_COST, FieldLocation.FIXED_DATA, 0, 132, 11, 0, 0),
         new FieldItem(ResourceField.COST, FieldLocation.FIXED_DATA, 0, 140, 12, 0, 0),
         new FieldItem(ResourceField.BASELINE_COST, FieldLocation.FIXED_DATA, 0, 148, 17, 0, 0),
         new FieldItem(ResourceField.REMAINING_COST, FieldLocation.FIXED_DATA, 0, 156, 21, 0, 0),
         new FieldItem(ResourceField.OVERTIME_COST, FieldLocation.FIXED_DATA, 0, 164, 47, 0, 0),
         new FieldItem(ResourceField.ACTUAL_OVERTIME_COST, FieldLocation.FIXED_DATA, 0, 172, 48, 0, 0),
         new FieldItem(ResourceField.REMAINING_OVERTIME_COST, FieldLocation.FIXED_DATA, 0, 180, 49, 0, 0),
         new FieldItem(ResourceField.NAME, FieldLocation.VAR_DATA, 0, 65535, 1, 0, 0),
         new FieldItem(ResourceField.INITIALS, FieldLocation.VAR_DATA, 0, 65535, 2, 0, 0),
         new FieldItem(ResourceField.GROUP, FieldLocation.VAR_DATA, 0, 65535, 3, 0, 0),
         new FieldItem(ResourceField.CODE, FieldLocation.VAR_DATA, 0, 65535, 10, 0, 0),
         new FieldItem(ResourceField.EMAIL_ADDRESS, FieldLocation.VAR_DATA, 0, 65535, 35, 0, 0),
         new FieldItem(ResourceField.PHONETICS, FieldLocation.VAR_DATA, 0, 65535, 252, 0, 0),
         new FieldItem(ResourceField.MATERIAL_LABEL, FieldLocation.VAR_DATA, 0, 65535, 299, 0, 0),
         new FieldItem(ResourceField.WINDOWS_USER_ACCOUNT, FieldLocation.VAR_DATA, 0, 65535, 311, 0, 0),
         new FieldItem(ResourceField.TEXT1, FieldLocation.VAR_DATA, 0, 65535, 8, 0, 0),
         new FieldItem(ResourceField.TEXT2, FieldLocation.VAR_DATA, 0, 65535, 9, 0, 0),
         new FieldItem(ResourceField.TEXT3, FieldLocation.VAR_DATA, 0, 65535, 30, 0, 0),
         new FieldItem(ResourceField.TEXT4, FieldLocation.VAR_DATA, 0, 65535, 31, 0, 0),
         new FieldItem(ResourceField.TEXT5, FieldLocation.VAR_DATA, 0, 65535, 32, 0, 0),
         new FieldItem(ResourceField.TEXT6, FieldLocation.VAR_DATA, 0, 65535, 97, 0, 0),
         new FieldItem(ResourceField.TEXT7, FieldLocation.VAR_DATA, 0, 65535, 98, 0, 0),
         new FieldItem(ResourceField.TEXT8, FieldLocation.VAR_DATA, 0, 65535, 99, 0, 0),
         new FieldItem(ResourceField.TEXT9, FieldLocation.VAR_DATA, 0, 65535, 100, 0, 0),
         new FieldItem(ResourceField.TEXT10, FieldLocation.VAR_DATA, 0, 65535, 101, 0, 0),
         new FieldItem(ResourceField.TEXT11, FieldLocation.VAR_DATA, 0, 65535, 225, 0, 0),
         new FieldItem(ResourceField.TEXT12, FieldLocation.VAR_DATA, 0, 65535, 226, 0, 0),
         new FieldItem(ResourceField.TEXT13, FieldLocation.VAR_DATA, 0, 65535, 227, 0, 0),
         new FieldItem(ResourceField.TEXT14, FieldLocation.VAR_DATA, 0, 65535, 228, 0, 0),
         new FieldItem(ResourceField.TEXT15, FieldLocation.VAR_DATA, 0, 65535, 229, 0, 0),
         new FieldItem(ResourceField.TEXT16, FieldLocation.VAR_DATA, 0, 65535, 230, 0, 0),
         new FieldItem(ResourceField.TEXT17, FieldLocation.VAR_DATA, 0, 65535, 231, 0, 0),
         new FieldItem(ResourceField.TEXT18, FieldLocation.VAR_DATA, 0, 65535, 232, 0, 0),
         new FieldItem(ResourceField.TEXT19, FieldLocation.VAR_DATA, 0, 65535, 233, 0, 0),
         new FieldItem(ResourceField.TEXT20, FieldLocation.VAR_DATA, 0, 65535, 234, 0, 0),
         new FieldItem(ResourceField.TEXT21, FieldLocation.VAR_DATA, 0, 65535, 235, 0, 0),
         new FieldItem(ResourceField.TEXT22, FieldLocation.VAR_DATA, 0, 65535, 236, 0, 0),
         new FieldItem(ResourceField.TEXT23, FieldLocation.VAR_DATA, 0, 65535, 237, 0, 0),
         new FieldItem(ResourceField.TEXT24, FieldLocation.VAR_DATA, 0, 65535, 238, 0, 0),
         new FieldItem(ResourceField.TEXT25, FieldLocation.VAR_DATA, 0, 65535, 239, 0, 0),
         new FieldItem(ResourceField.TEXT26, FieldLocation.VAR_DATA, 0, 65535, 240, 0, 0),
         new FieldItem(ResourceField.TEXT27, FieldLocation.VAR_DATA, 0, 65535, 241, 0, 0),
         new FieldItem(ResourceField.TEXT28, FieldLocation.VAR_DATA, 0, 65535, 242, 0, 0),
         new FieldItem(ResourceField.TEXT29, FieldLocation.VAR_DATA, 0, 65535, 243, 0, 0),
         new FieldItem(ResourceField.TEXT30, FieldLocation.VAR_DATA, 0, 65535, 244, 0, 0),
         new FieldItem(ResourceField.START1, FieldLocation.VAR_DATA, 0, 65535, 102, 0, 0),
         new FieldItem(ResourceField.START2, FieldLocation.VAR_DATA, 0, 65535, 103, 0, 0),
         new FieldItem(ResourceField.START3, FieldLocation.VAR_DATA, 0, 65535, 104, 0, 0),
         new FieldItem(ResourceField.START4, FieldLocation.VAR_DATA, 0, 65535, 105, 0, 0),
         new FieldItem(ResourceField.START5, FieldLocation.VAR_DATA, 0, 65535, 106, 0, 0),
         new FieldItem(ResourceField.START6, FieldLocation.VAR_DATA, 0, 65535, 220, 0, 0),
         new FieldItem(ResourceField.START7, FieldLocation.VAR_DATA, 0, 65535, 221, 0, 0),
         new FieldItem(ResourceField.START8, FieldLocation.VAR_DATA, 0, 65535, 222, 0, 0),
         new FieldItem(ResourceField.START9, FieldLocation.VAR_DATA, 0, 65535, 223, 0, 0),
         new FieldItem(ResourceField.START10, FieldLocation.VAR_DATA, 0, 65535, 224, 0, 0),
         new FieldItem(ResourceField.FINISH1, FieldLocation.VAR_DATA, 0, 65535, 107, 0, 0),
         new FieldItem(ResourceField.FINISH2, FieldLocation.VAR_DATA, 0, 65535, 108, 0, 0),
         new FieldItem(ResourceField.FINISH3, FieldLocation.VAR_DATA, 0, 65535, 109, 0, 0),
         new FieldItem(ResourceField.FINISH4, FieldLocation.VAR_DATA, 0, 65535, 110, 0, 0),
         new FieldItem(ResourceField.FINISH5, FieldLocation.VAR_DATA, 0, 65535, 111, 0, 0),
         new FieldItem(ResourceField.FINISH6, FieldLocation.VAR_DATA, 0, 65535, 190, 0, 0),
         new FieldItem(ResourceField.FINISH7, FieldLocation.VAR_DATA, 0, 65535, 191, 0, 0),
         new FieldItem(ResourceField.FINISH8, FieldLocation.VAR_DATA, 0, 65535, 192, 0, 0),
         new FieldItem(ResourceField.FINISH9, FieldLocation.VAR_DATA, 0, 65535, 193, 0, 0),
         new FieldItem(ResourceField.FINISH10, FieldLocation.VAR_DATA, 0, 65535, 194, 0, 0),
         new FieldItem(ResourceField.NUMBER1, FieldLocation.VAR_DATA, 0, 65535, 112, 0, 0),
         new FieldItem(ResourceField.NUMBER2, FieldLocation.VAR_DATA, 0, 65535, 113, 0, 0),
         new FieldItem(ResourceField.NUMBER3, FieldLocation.VAR_DATA, 0, 65535, 114, 0, 0),
         new FieldItem(ResourceField.NUMBER4, FieldLocation.VAR_DATA, 0, 65535, 115, 0, 0),
         new FieldItem(ResourceField.NUMBER5, FieldLocation.VAR_DATA, 0, 65535, 116, 0, 0),
         new FieldItem(ResourceField.NUMBER6, FieldLocation.VAR_DATA, 0, 65535, 205, 0, 0),
         new FieldItem(ResourceField.NUMBER7, FieldLocation.VAR_DATA, 0, 65535, 206, 0, 0),
         new FieldItem(ResourceField.NUMBER8, FieldLocation.VAR_DATA, 0, 65535, 207, 0, 0),
         new FieldItem(ResourceField.NUMBER9, FieldLocation.VAR_DATA, 0, 65535, 208, 0, 0),
         new FieldItem(ResourceField.NUMBER10, FieldLocation.VAR_DATA, 0, 65535, 209, 0, 0),
         new FieldItem(ResourceField.NUMBER11, FieldLocation.VAR_DATA, 0, 65535, 210, 0, 0),
         new FieldItem(ResourceField.NUMBER12, FieldLocation.VAR_DATA, 0, 65535, 211, 0, 0),
         new FieldItem(ResourceField.NUMBER13, FieldLocation.VAR_DATA, 0, 65535, 212, 0, 0),
         new FieldItem(ResourceField.NUMBER14, FieldLocation.VAR_DATA, 0, 65535, 213, 0, 0),
         new FieldItem(ResourceField.NUMBER15, FieldLocation.VAR_DATA, 0, 65535, 214, 0, 0),
         new FieldItem(ResourceField.NUMBER16, FieldLocation.VAR_DATA, 0, 65535, 215, 0, 0),
         new FieldItem(ResourceField.NUMBER17, FieldLocation.VAR_DATA, 0, 65535, 216, 0, 0),
         new FieldItem(ResourceField.NUMBER18, FieldLocation.VAR_DATA, 0, 65535, 217, 0, 0),
         new FieldItem(ResourceField.NUMBER19, FieldLocation.VAR_DATA, 0, 65535, 218, 0, 0),
         new FieldItem(ResourceField.NUMBER20, FieldLocation.VAR_DATA, 0, 65535, 219, 0, 0),
         new FieldItem(ResourceField.DURATION1, FieldLocation.VAR_DATA, 0, 65535, 117, 0, 0),
         new FieldItem(ResourceField.DURATION2, FieldLocation.VAR_DATA, 0, 65535, 118, 0, 0),
         new FieldItem(ResourceField.DURATION3, FieldLocation.VAR_DATA, 0, 65535, 119, 0, 0),
         new FieldItem(ResourceField.DURATION4, FieldLocation.VAR_DATA, 0, 65535, 183, 0, 0),
         new FieldItem(ResourceField.DURATION5, FieldLocation.VAR_DATA, 0, 65535, 184, 0, 0),
         new FieldItem(ResourceField.DURATION6, FieldLocation.VAR_DATA, 0, 65535, 185, 0, 0),
         new FieldItem(ResourceField.DURATION7, FieldLocation.VAR_DATA, 0, 65535, 186, 0, 0),
         new FieldItem(ResourceField.DURATION8, FieldLocation.VAR_DATA, 0, 65535, 187, 0, 0),
         new FieldItem(ResourceField.DURATION9, FieldLocation.VAR_DATA, 0, 65535, 188, 0, 0),
         new FieldItem(ResourceField.DURATION10, FieldLocation.VAR_DATA, 0, 65535, 189, 0, 0),
         new FieldItem(ResourceField.DURATION1_UNITS, FieldLocation.VAR_DATA, 0, 65535, 120, 0, 0),
         new FieldItem(ResourceField.DURATION2_UNITS, FieldLocation.VAR_DATA, 0, 65535, 121, 0, 0),
         new FieldItem(ResourceField.DURATION3_UNITS, FieldLocation.VAR_DATA, 0, 65535, 122, 0, 0),
         new FieldItem(ResourceField.DURATION4_UNITS, FieldLocation.VAR_DATA, 0, 65535, 245, 0, 0),
         new FieldItem(ResourceField.DURATION5_UNITS, FieldLocation.VAR_DATA, 0, 65535, 246, 0, 0),
         new FieldItem(ResourceField.DURATION6_UNITS, FieldLocation.VAR_DATA, 0, 65535, 247, 0, 0),
         new FieldItem(ResourceField.DURATION7_UNITS, FieldLocation.VAR_DATA, 0, 65535, 248, 0, 0),
         new FieldItem(ResourceField.DURATION8_UNITS, FieldLocation.VAR_DATA, 0, 65535, 249, 0, 0),
         new FieldItem(ResourceField.DURATION9_UNITS, FieldLocation.VAR_DATA, 0, 65535, 250, 0, 0),
         new FieldItem(ResourceField.DURATION10_UNITS, FieldLocation.VAR_DATA, 0, 65535, 251, 0, 0),
         new FieldItem(ResourceField.DATE1, FieldLocation.VAR_DATA, 0, 65535, 173, 0, 0),
         new FieldItem(ResourceField.DATE2, FieldLocation.VAR_DATA, 0, 65535, 174, 0, 0),
         new FieldItem(ResourceField.DATE3, FieldLocation.VAR_DATA, 0, 65535, 175, 0, 0),
         new FieldItem(ResourceField.DATE4, FieldLocation.VAR_DATA, 0, 65535, 176, 0, 0),
         new FieldItem(ResourceField.DATE5, FieldLocation.VAR_DATA, 0, 65535, 177, 0, 0),
         new FieldItem(ResourceField.DATE6, FieldLocation.VAR_DATA, 0, 65535, 178, 0, 0),
         new FieldItem(ResourceField.DATE7, FieldLocation.VAR_DATA, 0, 65535, 179, 0, 0),
         new FieldItem(ResourceField.DATE8, FieldLocation.VAR_DATA, 0, 65535, 180, 0, 0),
         new FieldItem(ResourceField.DATE9, FieldLocation.VAR_DATA, 0, 65535, 181, 0, 0),
         new FieldItem(ResourceField.DATE10, FieldLocation.VAR_DATA, 0, 65535, 182, 0, 0),
         new FieldItem(ResourceField.OUTLINE_CODE1_INDEX, FieldLocation.VAR_DATA, 0, 65535, 279, 0, 0),
         new FieldItem(ResourceField.OUTLINE_CODE2_INDEX, FieldLocation.VAR_DATA, 0, 65535, 281, 0, 0),
         new FieldItem(ResourceField.OUTLINE_CODE3_INDEX, FieldLocation.VAR_DATA, 0, 65535, 283, 0, 0),
         new FieldItem(ResourceField.OUTLINE_CODE4_INDEX, FieldLocation.VAR_DATA, 0, 65535, 285, 0, 0),
         new FieldItem(ResourceField.OUTLINE_CODE5_INDEX, FieldLocation.VAR_DATA, 0, 65535, 287, 0, 0),
         new FieldItem(ResourceField.OUTLINE_CODE6_INDEX, FieldLocation.VAR_DATA, 0, 65535, 289, 0, 0),
         new FieldItem(ResourceField.OUTLINE_CODE7_INDEX, FieldLocation.VAR_DATA, 0, 65535, 291, 0, 0),
         new FieldItem(ResourceField.OUTLINE_CODE8_INDEX, FieldLocation.VAR_DATA, 0, 65535, 293, 0, 0),
         new FieldItem(ResourceField.OUTLINE_CODE9_INDEX, FieldLocation.VAR_DATA, 0, 65535, 295, 0, 0),
         new FieldItem(ResourceField.OUTLINE_CODE10_INDEX, FieldLocation.VAR_DATA, 0, 65535, 297, 0, 0),
         new FieldItem(ResourceField.HYPERLINK_DATA, FieldLocation.VAR_DATA, 0, 65535, 136, 0, 0),
         new FieldItem(ResourceField.NOTES, FieldLocation.VAR_DATA, 0, 65535, 20, 0, 0),
         new FieldItem(ResourceField.COST1, FieldLocation.VAR_DATA, 0, 65535, 123, 0, 0),
         new FieldItem(ResourceField.COST2, FieldLocation.VAR_DATA, 0, 65535, 124, 0, 0),
         new FieldItem(ResourceField.COST3, FieldLocation.VAR_DATA, 0, 65535, 125, 0, 0),
         new FieldItem(ResourceField.COST4, FieldLocation.VAR_DATA, 0, 65535, 166, 0, 0),
         new FieldItem(ResourceField.COST5, FieldLocation.VAR_DATA, 0, 65535, 167, 0, 0),
         new FieldItem(ResourceField.COST6, FieldLocation.VAR_DATA, 0, 65535, 168, 0, 0),
         new FieldItem(ResourceField.COST7, FieldLocation.VAR_DATA, 0, 65535, 169, 0, 0),
         new FieldItem(ResourceField.COST8, FieldLocation.VAR_DATA, 0, 65535, 170, 0, 0),
         new FieldItem(ResourceField.COST9, FieldLocation.VAR_DATA, 0, 65535, 171, 0, 0),
         new FieldItem(ResourceField.COST10, FieldLocation.VAR_DATA, 0, 65535, 172, 0, 0),
         new FieldItem(ResourceField.COST_RATE_A, FieldLocation.VAR_DATA, 0, 65535, 61, 0, 0),
         new FieldItem(ResourceField.COST_RATE_B, FieldLocation.VAR_DATA, 0, 65535, 62, 0, 0),
         new FieldItem(ResourceField.COST_RATE_C, FieldLocation.VAR_DATA, 0, 65535, 63, 0, 0),
         new FieldItem(ResourceField.COST_RATE_D, FieldLocation.VAR_DATA, 0, 65535, 64, 0, 0),
         new FieldItem(ResourceField.COST_RATE_E, FieldLocation.VAR_DATA, 0, 65535, 65, 0, 0),
         new FieldItem(ResourceField.AVAILABILITY_DATA, FieldLocation.VAR_DATA, 0, 65535, 276, 0, 0),
         new FieldItem(ResourceField.GUID, FieldLocation.FIXED_DATA, 1, 0, 728, 0, 0),
         new FieldItem(ResourceField.CALENDAR_GUID, FieldLocation.FIXED_DATA, 1, 24, 729, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_COST1, FieldLocation.VAR_DATA, 1, 65535, 446, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_COST2, FieldLocation.VAR_DATA, 1, 65535, 447, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_COST3, FieldLocation.VAR_DATA, 1, 65535, 448, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_COST4, FieldLocation.VAR_DATA, 1, 65535, 449, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_COST5, FieldLocation.VAR_DATA, 1, 65535, 450, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_COST6, FieldLocation.VAR_DATA, 1, 65535, 451, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_COST7, FieldLocation.VAR_DATA, 1, 65535, 452, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_COST8, FieldLocation.VAR_DATA, 1, 65535, 453, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_COST9, FieldLocation.VAR_DATA, 1, 65535, 454, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_COST10, FieldLocation.VAR_DATA, 1, 65535, 455, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE1, FieldLocation.VAR_DATA, 1, 65535, 456, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE2, FieldLocation.VAR_DATA, 1, 65535, 457, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE3, FieldLocation.VAR_DATA, 1, 65535, 458, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE4, FieldLocation.VAR_DATA, 1, 65535, 459, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE5, FieldLocation.VAR_DATA, 1, 65535, 460, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE6, FieldLocation.VAR_DATA, 1, 65535, 461, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE7, FieldLocation.VAR_DATA, 1, 65535, 462, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE8, FieldLocation.VAR_DATA, 1, 65535, 463, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE9, FieldLocation.VAR_DATA, 1, 65535, 464, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE10, FieldLocation.VAR_DATA, 1, 65535, 465, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE11, FieldLocation.VAR_DATA, 1, 65535, 466, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE12, FieldLocation.VAR_DATA, 1, 65535, 467, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE13, FieldLocation.VAR_DATA, 1, 65535, 468, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE14, FieldLocation.VAR_DATA, 1, 65535, 469, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE15, FieldLocation.VAR_DATA, 1, 65535, 470, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE16, FieldLocation.VAR_DATA, 1, 65535, 471, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE17, FieldLocation.VAR_DATA, 1, 65535, 472, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE18, FieldLocation.VAR_DATA, 1, 65535, 473, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE19, FieldLocation.VAR_DATA, 1, 65535, 474, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE20, FieldLocation.VAR_DATA, 1, 65535, 475, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE21, FieldLocation.VAR_DATA, 1, 65535, 476, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE22, FieldLocation.VAR_DATA, 1, 65535, 477, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE23, FieldLocation.VAR_DATA, 1, 65535, 478, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE24, FieldLocation.VAR_DATA, 1, 65535, 479, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE25, FieldLocation.VAR_DATA, 1, 65535, 480, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE26, FieldLocation.VAR_DATA, 1, 65535, 481, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE27, FieldLocation.VAR_DATA, 1, 65535, 482, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE28, FieldLocation.VAR_DATA, 1, 65535, 483, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE29, FieldLocation.VAR_DATA, 1, 65535, 484, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DATE30, FieldLocation.VAR_DATA, 1, 65535, 485, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DURATION1, FieldLocation.VAR_DATA, 1, 65535, 486, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DURATION2, FieldLocation.VAR_DATA, 1, 65535, 487, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DURATION3, FieldLocation.VAR_DATA, 1, 65535, 488, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DURATION4, FieldLocation.VAR_DATA, 1, 65535, 489, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DURATION5, FieldLocation.VAR_DATA, 1, 65535, 490, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DURATION6, FieldLocation.VAR_DATA, 1, 65535, 491, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DURATION7, FieldLocation.VAR_DATA, 1, 65535, 492, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DURATION8, FieldLocation.VAR_DATA, 1, 65535, 493, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DURATION9, FieldLocation.VAR_DATA, 1, 65535, 494, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DURATION10, FieldLocation.VAR_DATA, 1, 65535, 495, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DURATION1_UNITS, FieldLocation.VAR_DATA, 1, 65535, 496, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DURATION2_UNITS, FieldLocation.VAR_DATA, 1, 65535, 497, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DURATION3_UNITS, FieldLocation.VAR_DATA, 1, 65535, 498, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DURATION4_UNITS, FieldLocation.VAR_DATA, 1, 65535, 499, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DURATION5_UNITS, FieldLocation.VAR_DATA, 1, 65535, 500, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DURATION6_UNITS, FieldLocation.VAR_DATA, 1, 65535, 501, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DURATION7_UNITS, FieldLocation.VAR_DATA, 1, 65535, 502, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DURATION8_UNITS, FieldLocation.VAR_DATA, 1, 65535, 503, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DURATION9_UNITS, FieldLocation.VAR_DATA, 1, 65535, 504, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_DURATION10_UNITS, FieldLocation.VAR_DATA, 1, 65535, 505, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER1, FieldLocation.VAR_DATA, 1, 65535, 546, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER2, FieldLocation.VAR_DATA, 1, 65535, 547, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER3, FieldLocation.VAR_DATA, 1, 65535, 548, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER4, FieldLocation.VAR_DATA, 1, 65535, 549, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER5, FieldLocation.VAR_DATA, 1, 65535, 550, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER6, FieldLocation.VAR_DATA, 1, 65535, 551, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER7, FieldLocation.VAR_DATA, 1, 65535, 552, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER8, FieldLocation.VAR_DATA, 1, 65535, 553, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER9, FieldLocation.VAR_DATA, 1, 65535, 554, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER10, FieldLocation.VAR_DATA, 1, 65535, 555, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER11, FieldLocation.VAR_DATA, 1, 65535, 556, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER12, FieldLocation.VAR_DATA, 1, 65535, 557, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER13, FieldLocation.VAR_DATA, 1, 65535, 558, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER14, FieldLocation.VAR_DATA, 1, 65535, 559, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER15, FieldLocation.VAR_DATA, 1, 65535, 560, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER16, FieldLocation.VAR_DATA, 1, 65535, 561, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER17, FieldLocation.VAR_DATA, 1, 65535, 562, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER18, FieldLocation.VAR_DATA, 1, 65535, 563, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER19, FieldLocation.VAR_DATA, 1, 65535, 564, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER20, FieldLocation.VAR_DATA, 1, 65535, 565, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER21, FieldLocation.VAR_DATA, 1, 65535, 566, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER22, FieldLocation.VAR_DATA, 1, 65535, 567, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER23, FieldLocation.VAR_DATA, 1, 65535, 568, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER24, FieldLocation.VAR_DATA, 1, 65535, 569, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER25, FieldLocation.VAR_DATA, 1, 65535, 570, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER26, FieldLocation.VAR_DATA, 1, 65535, 571, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER27, FieldLocation.VAR_DATA, 1, 65535, 572, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER28, FieldLocation.VAR_DATA, 1, 65535, 573, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER29, FieldLocation.VAR_DATA, 1, 65535, 574, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER30, FieldLocation.VAR_DATA, 1, 65535, 575, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER31, FieldLocation.VAR_DATA, 1, 65535, 576, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER32, FieldLocation.VAR_DATA, 1, 65535, 577, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER33, FieldLocation.VAR_DATA, 1, 65535, 578, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER34, FieldLocation.VAR_DATA, 1, 65535, 579, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER35, FieldLocation.VAR_DATA, 1, 65535, 580, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER36, FieldLocation.VAR_DATA, 1, 65535, 581, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER37, FieldLocation.VAR_DATA, 1, 65535, 582, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER38, FieldLocation.VAR_DATA, 1, 65535, 583, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER39, FieldLocation.VAR_DATA, 1, 65535, 584, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_NUMBER40, FieldLocation.VAR_DATA, 1, 65535, 585, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT1, FieldLocation.VAR_DATA, 1, 65535, 646, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT2, FieldLocation.VAR_DATA, 1, 65535, 647, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT3, FieldLocation.VAR_DATA, 1, 65535, 648, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT4, FieldLocation.VAR_DATA, 1, 65535, 649, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT5, FieldLocation.VAR_DATA, 1, 65535, 650, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT6, FieldLocation.VAR_DATA, 1, 65535, 651, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT7, FieldLocation.VAR_DATA, 1, 65535, 652, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT8, FieldLocation.VAR_DATA, 1, 65535, 653, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT9, FieldLocation.VAR_DATA, 1, 65535, 654, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT10, FieldLocation.VAR_DATA, 1, 65535, 655, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT11, FieldLocation.VAR_DATA, 1, 65535, 656, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT12, FieldLocation.VAR_DATA, 1, 65535, 657, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT13, FieldLocation.VAR_DATA, 1, 65535, 658, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT14, FieldLocation.VAR_DATA, 1, 65535, 659, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT15, FieldLocation.VAR_DATA, 1, 65535, 660, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT16, FieldLocation.VAR_DATA, 1, 65535, 661, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT17, FieldLocation.VAR_DATA, 1, 65535, 662, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT18, FieldLocation.VAR_DATA, 1, 65535, 663, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT19, FieldLocation.VAR_DATA, 1, 65535, 664, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT20, FieldLocation.VAR_DATA, 1, 65535, 665, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT21, FieldLocation.VAR_DATA, 1, 65535, 666, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT22, FieldLocation.VAR_DATA, 1, 65535, 667, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT23, FieldLocation.VAR_DATA, 1, 65535, 668, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT24, FieldLocation.VAR_DATA, 1, 65535, 669, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT25, FieldLocation.VAR_DATA, 1, 65535, 670, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT26, FieldLocation.VAR_DATA, 1, 65535, 671, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT27, FieldLocation.VAR_DATA, 1, 65535, 672, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT28, FieldLocation.VAR_DATA, 1, 65535, 673, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT29, FieldLocation.VAR_DATA, 1, 65535, 674, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT30, FieldLocation.VAR_DATA, 1, 65535, 675, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT31, FieldLocation.VAR_DATA, 1, 65535, 676, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT32, FieldLocation.VAR_DATA, 1, 65535, 677, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT33, FieldLocation.VAR_DATA, 1, 65535, 678, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT34, FieldLocation.VAR_DATA, 1, 65535, 679, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT35, FieldLocation.VAR_DATA, 1, 65535, 680, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT36, FieldLocation.VAR_DATA, 1, 65535, 681, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT37, FieldLocation.VAR_DATA, 1, 65535, 682, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT38, FieldLocation.VAR_DATA, 1, 65535, 683, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT39, FieldLocation.VAR_DATA, 1, 65535, 684, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_TEXT40, FieldLocation.VAR_DATA, 1, 65535, 685, 0, 0),
         new FieldItem(ResourceField.BOOKING_TYPE, FieldLocation.VAR_DATA, 1, 65535, 699, 0, 0),
         new FieldItem(ResourceField.ACTUAL_WORK_PROTECTED, FieldLocation.VAR_DATA, 1, 65535, 720, 0, 0),
         new FieldItem(ResourceField.ACTUAL_OVERTIME_WORK_PROTECTED, FieldLocation.VAR_DATA, 1, 65535, 721, 0, 0),
         new FieldItem(ResourceField.BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 753, 0, 0),
         new FieldItem(ResourceField.BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 754, 0, 0),
         new FieldItem(ResourceField.BASELINE_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 756, 0, 0),
         new FieldItem(ResourceField.BASELINE_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 757, 0, 0),
         new FieldItem(ResourceField.BASELINE1_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 760, 0, 0),
         new FieldItem(ResourceField.BASELINE1_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 761, 0, 0),
         new FieldItem(ResourceField.BASELINE2_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 764, 0, 0),
         new FieldItem(ResourceField.BASELINE2_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 765, 0, 0),
         new FieldItem(ResourceField.BASELINE3_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 768, 0, 0),
         new FieldItem(ResourceField.BASELINE3_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 769, 0, 0),
         new FieldItem(ResourceField.BASELINE4_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 772, 0, 0),
         new FieldItem(ResourceField.BASELINE4_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 773, 0, 0),
         new FieldItem(ResourceField.BASELINE5_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 776, 0, 0),
         new FieldItem(ResourceField.BASELINE5_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 777, 0, 0),
         new FieldItem(ResourceField.BASELINE6_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 780, 0, 0),
         new FieldItem(ResourceField.BASELINE6_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 781, 0, 0),
         new FieldItem(ResourceField.BASELINE7_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 784, 0, 0),
         new FieldItem(ResourceField.BASELINE7_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 785, 0, 0),
         new FieldItem(ResourceField.BASELINE8_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 788, 0, 0),
         new FieldItem(ResourceField.BASELINE8_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 789, 0, 0),
         new FieldItem(ResourceField.BASELINE9_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 792, 0, 0),
         new FieldItem(ResourceField.BASELINE9_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 793, 0, 0),
         new FieldItem(ResourceField.BASELINE10_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 796, 0, 0),
         new FieldItem(ResourceField.BASELINE10_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 797, 0, 0),
         new FieldItem(ResourceField.CREATED, FieldLocation.VAR_DATA, 1, 65535, 726, 0, 0),
         new FieldItem(ResourceField.COST_CENTER, FieldLocation.VAR_DATA, 1, 65535, 801, 0, 0),
         new FieldItem(ResourceField.BASELINE1_WORK, FieldLocation.VAR_DATA, 1, 65535, 342, 0, 0),
         new FieldItem(ResourceField.BASELINE1_COST, FieldLocation.VAR_DATA, 1, 65535, 343, 0, 0),
         new FieldItem(ResourceField.BASELINE2_WORK, FieldLocation.VAR_DATA, 1, 65535, 352, 0, 0),
         new FieldItem(ResourceField.BASELINE2_COST, FieldLocation.VAR_DATA, 1, 65535, 353, 0, 0),
         new FieldItem(ResourceField.BASELINE3_WORK, FieldLocation.VAR_DATA, 1, 65535, 362, 0, 0),
         new FieldItem(ResourceField.BASELINE3_COST, FieldLocation.VAR_DATA, 1, 65535, 363, 0, 0),
         new FieldItem(ResourceField.BASELINE4_WORK, FieldLocation.VAR_DATA, 1, 65535, 372, 0, 0),
         new FieldItem(ResourceField.BASELINE4_COST, FieldLocation.VAR_DATA, 1, 65535, 373, 0, 0),
         new FieldItem(ResourceField.BASELINE5_WORK, FieldLocation.VAR_DATA, 1, 65535, 382, 0, 0),
         new FieldItem(ResourceField.BASELINE5_COST, FieldLocation.VAR_DATA, 1, 65535, 383, 0, 0),
         new FieldItem(ResourceField.BASELINE6_WORK, FieldLocation.VAR_DATA, 1, 65535, 392, 0, 0),
         new FieldItem(ResourceField.BASELINE6_COST, FieldLocation.VAR_DATA, 1, 65535, 393, 0, 0),
         new FieldItem(ResourceField.BASELINE7_WORK, FieldLocation.VAR_DATA, 1, 65535, 402, 0, 0),
         new FieldItem(ResourceField.BASELINE7_COST, FieldLocation.VAR_DATA, 1, 65535, 403, 0, 0),
         new FieldItem(ResourceField.BASELINE8_WORK, FieldLocation.VAR_DATA, 1, 65535, 412, 0, 0),
         new FieldItem(ResourceField.BASELINE8_COST, FieldLocation.VAR_DATA, 1, 65535, 413, 0, 0),
         new FieldItem(ResourceField.BASELINE9_WORK, FieldLocation.VAR_DATA, 1, 65535, 422, 0, 0),
         new FieldItem(ResourceField.BASELINE9_COST, FieldLocation.VAR_DATA, 1, 65535, 423, 0, 0),
         new FieldItem(ResourceField.BASELINE10_WORK, FieldLocation.VAR_DATA, 1, 65535, 432, 0, 0),
         new FieldItem(ResourceField.BASELINE10_COST, FieldLocation.VAR_DATA, 1, 65535, 433, 0, 0),
         new FieldItem(ResourceField.ENTERPRISE_UNIQUE_ID, FieldLocation.VAR_DATA, 1, 65535, 443, 0, 0),

      };
   }

   @Override protected FieldItem[] getDefaultAssignmentData()
   {
      return new FieldItem[]
      {
         new FieldItem(AssignmentField.UNIQUE_ID, FieldLocation.FIXED_DATA, 0, 0, 0, 0, 0),
         new FieldItem(AssignmentField.TASK_UNIQUE_ID, FieldLocation.FIXED_DATA, 0, 4, 1, 0, 0),
         new FieldItem(AssignmentField.RESOURCE_UNIQUE_ID, FieldLocation.FIXED_DATA, 0, 8, 2, 0, 0),
         new FieldItem(AssignmentField.START, FieldLocation.FIXED_DATA, 0, 12, 20, 0, 0),
         new FieldItem(AssignmentField.FINISH, FieldLocation.FIXED_DATA, 0, 16, 21, 0, 0),
         new FieldItem(AssignmentField.LEVELING_DELAY_UNITS, FieldLocation.FIXED_DATA, 0, 28, 55, 0, 0),
         new FieldItem(AssignmentField.LEVELING_DELAY, FieldLocation.FIXED_DATA, 0, 30, 145, 0, 0),
         new FieldItem(AssignmentField.COST_RATE_TABLE, FieldLocation.FIXED_DATA, 0, 34, 80, 0, 0),
         new FieldItem(AssignmentField.VARIABLE_RATE_UNITS, FieldLocation.FIXED_DATA, 0, 44, 270, 0, 0),
         new FieldItem(AssignmentField.ASSIGNMENT_UNITS, FieldLocation.FIXED_DATA, 0, 46, 7, 0, 0),
         new FieldItem(AssignmentField.WORK, FieldLocation.FIXED_DATA, 0, 54, 8, 0, 0),
         new FieldItem(AssignmentField.ACTUAL_WORK, FieldLocation.FIXED_DATA, 0, 62, 10, 0, 0),
         new FieldItem(AssignmentField.REGULAR_WORK, FieldLocation.FIXED_DATA, 0, 70, 11, 0, 0),
         new FieldItem(AssignmentField.REMAINING_WORK, FieldLocation.FIXED_DATA, 0, 78, 12, 0, 0),
         new FieldItem(AssignmentField.COST, FieldLocation.FIXED_DATA, 0, 86, 26, 0, 0),
         new FieldItem(AssignmentField.ACTUAL_COST, FieldLocation.FIXED_DATA, 0, 94, 28, 0, 0),
         new FieldItem(AssignmentField.REMAINING_COST, FieldLocation.FIXED_DATA, 0, 102, 29, 0, 0),
         new FieldItem(AssignmentField.OVERTIME_WORK, FieldLocation.VAR_DATA, 0, 65535, 9, 0, 0),
         new FieldItem(AssignmentField.ACTUAL_OVERTIME_WORK, FieldLocation.VAR_DATA, 0, 65535, 13, 0, 0),
         new FieldItem(AssignmentField.REMAINING_OVERTIME_WORK, FieldLocation.VAR_DATA, 0, 65535, 14, 0, 0),
         new FieldItem(AssignmentField.ACTUAL_OVERTIME_COST, FieldLocation.VAR_DATA, 0, 65535, 30, 0, 0),
         new FieldItem(AssignmentField.REMAINING_OVERTIME_COST, FieldLocation.VAR_DATA, 0, 65535, 31, 0, 0),
         new FieldItem(AssignmentField.NOTES, FieldLocation.VAR_DATA, 0, 65535, 71, 0, 0),
         new FieldItem(AssignmentField.TIMEPHASED_WORK, FieldLocation.VAR_DATA, 0, 65535, 49, 0, 0),
         new FieldItem(AssignmentField.TIMEPHASED_ACTUAL_WORK, FieldLocation.VAR_DATA, 0, 65535, 50, 0, 0),
         new FieldItem(AssignmentField.TEXT1, FieldLocation.VAR_DATA, 0, 65535, 88, 0, 0),
         new FieldItem(AssignmentField.TEXT2, FieldLocation.VAR_DATA, 0, 65535, 89, 0, 0),
         new FieldItem(AssignmentField.TEXT3, FieldLocation.VAR_DATA, 0, 65535, 90, 0, 0),
         new FieldItem(AssignmentField.TEXT4, FieldLocation.VAR_DATA, 0, 65535, 91, 0, 0),
         new FieldItem(AssignmentField.TEXT5, FieldLocation.VAR_DATA, 0, 65535, 92, 0, 0),
         new FieldItem(AssignmentField.TEXT6, FieldLocation.VAR_DATA, 0, 65535, 93, 0, 0),
         new FieldItem(AssignmentField.TEXT7, FieldLocation.VAR_DATA, 0, 65535, 94, 0, 0),
         new FieldItem(AssignmentField.TEXT8, FieldLocation.VAR_DATA, 0, 65535, 95, 0, 0),
         new FieldItem(AssignmentField.TEXT9, FieldLocation.VAR_DATA, 0, 65535, 96, 0, 0),
         new FieldItem(AssignmentField.TEXT10, FieldLocation.VAR_DATA, 0, 65535, 97, 0, 0),
         new FieldItem(AssignmentField.TEXT11, FieldLocation.VAR_DATA, 0, 65535, 218, 0, 0),
         new FieldItem(AssignmentField.TEXT12, FieldLocation.VAR_DATA, 0, 65535, 219, 0, 0),
         new FieldItem(AssignmentField.TEXT13, FieldLocation.VAR_DATA, 0, 65535, 220, 0, 0),
         new FieldItem(AssignmentField.TEXT14, FieldLocation.VAR_DATA, 0, 65535, 221, 0, 0),
         new FieldItem(AssignmentField.TEXT15, FieldLocation.VAR_DATA, 0, 65535, 222, 0, 0),
         new FieldItem(AssignmentField.TEXT16, FieldLocation.VAR_DATA, 0, 65535, 223, 0, 0),
         new FieldItem(AssignmentField.TEXT17, FieldLocation.VAR_DATA, 0, 65535, 224, 0, 0),
         new FieldItem(AssignmentField.TEXT18, FieldLocation.VAR_DATA, 0, 65535, 225, 0, 0),
         new FieldItem(AssignmentField.TEXT19, FieldLocation.VAR_DATA, 0, 65535, 226, 0, 0),
         new FieldItem(AssignmentField.TEXT20, FieldLocation.VAR_DATA, 0, 65535, 227, 0, 0),
         new FieldItem(AssignmentField.TEXT21, FieldLocation.VAR_DATA, 0, 65535, 228, 0, 0),
         new FieldItem(AssignmentField.TEXT22, FieldLocation.VAR_DATA, 0, 65535, 229, 0, 0),
         new FieldItem(AssignmentField.TEXT23, FieldLocation.VAR_DATA, 0, 65535, 230, 0, 0),
         new FieldItem(AssignmentField.TEXT24, FieldLocation.VAR_DATA, 0, 65535, 231, 0, 0),
         new FieldItem(AssignmentField.TEXT25, FieldLocation.VAR_DATA, 0, 65535, 232, 0, 0),
         new FieldItem(AssignmentField.TEXT26, FieldLocation.VAR_DATA, 0, 65535, 233, 0, 0),
         new FieldItem(AssignmentField.TEXT27, FieldLocation.VAR_DATA, 0, 65535, 234, 0, 0),
         new FieldItem(AssignmentField.TEXT28, FieldLocation.VAR_DATA, 0, 65535, 235, 0, 0),
         new FieldItem(AssignmentField.TEXT29, FieldLocation.VAR_DATA, 0, 65535, 236, 0, 0),
         new FieldItem(AssignmentField.TEXT30, FieldLocation.VAR_DATA, 0, 65535, 237, 0, 0),
         new FieldItem(AssignmentField.START1, FieldLocation.VAR_DATA, 0, 65535, 98, 0, 0),
         new FieldItem(AssignmentField.START2, FieldLocation.VAR_DATA, 0, 65535, 99, 0, 0),
         new FieldItem(AssignmentField.START3, FieldLocation.VAR_DATA, 0, 65535, 100, 0, 0),
         new FieldItem(AssignmentField.START4, FieldLocation.VAR_DATA, 0, 65535, 101, 0, 0),
         new FieldItem(AssignmentField.START5, FieldLocation.VAR_DATA, 0, 65535, 102, 0, 0),
         new FieldItem(AssignmentField.START6, FieldLocation.VAR_DATA, 0, 65535, 213, 0, 0),
         new FieldItem(AssignmentField.START7, FieldLocation.VAR_DATA, 0, 65535, 214, 0, 0),
         new FieldItem(AssignmentField.START8, FieldLocation.VAR_DATA, 0, 65535, 215, 0, 0),
         new FieldItem(AssignmentField.START9, FieldLocation.VAR_DATA, 0, 65535, 216, 0, 0),
         new FieldItem(AssignmentField.START10, FieldLocation.VAR_DATA, 0, 65535, 217, 0, 0),
         new FieldItem(AssignmentField.FINISH1, FieldLocation.VAR_DATA, 0, 65535, 103, 0, 0),
         new FieldItem(AssignmentField.FINISH2, FieldLocation.VAR_DATA, 0, 65535, 104, 0, 0),
         new FieldItem(AssignmentField.FINISH3, FieldLocation.VAR_DATA, 0, 65535, 105, 0, 0),
         new FieldItem(AssignmentField.FINISH4, FieldLocation.VAR_DATA, 0, 65535, 106, 0, 0),
         new FieldItem(AssignmentField.FINISH5, FieldLocation.VAR_DATA, 0, 65535, 107, 0, 0),
         new FieldItem(AssignmentField.FINISH6, FieldLocation.VAR_DATA, 0, 65535, 183, 0, 0),
         new FieldItem(AssignmentField.FINISH7, FieldLocation.VAR_DATA, 0, 65535, 184, 0, 0),
         new FieldItem(AssignmentField.FINISH8, FieldLocation.VAR_DATA, 0, 65535, 185, 0, 0),
         new FieldItem(AssignmentField.FINISH9, FieldLocation.VAR_DATA, 0, 65535, 186, 0, 0),
         new FieldItem(AssignmentField.FINISH10, FieldLocation.VAR_DATA, 0, 65535, 187, 0, 0),
         new FieldItem(AssignmentField.NUMBER1, FieldLocation.VAR_DATA, 0, 65535, 108, 0, 0),
         new FieldItem(AssignmentField.NUMBER2, FieldLocation.VAR_DATA, 0, 65535, 109, 0, 0),
         new FieldItem(AssignmentField.NUMBER3, FieldLocation.VAR_DATA, 0, 65535, 110, 0, 0),
         new FieldItem(AssignmentField.NUMBER4, FieldLocation.VAR_DATA, 0, 65535, 111, 0, 0),
         new FieldItem(AssignmentField.NUMBER5, FieldLocation.VAR_DATA, 0, 65535, 112, 0, 0),
         new FieldItem(AssignmentField.NUMBER6, FieldLocation.VAR_DATA, 0, 65535, 198, 0, 0),
         new FieldItem(AssignmentField.NUMBER7, FieldLocation.VAR_DATA, 0, 65535, 199, 0, 0),
         new FieldItem(AssignmentField.NUMBER8, FieldLocation.VAR_DATA, 0, 65535, 200, 0, 0),
         new FieldItem(AssignmentField.NUMBER9, FieldLocation.VAR_DATA, 0, 65535, 201, 0, 0),
         new FieldItem(AssignmentField.NUMBER10, FieldLocation.VAR_DATA, 0, 65535, 202, 0, 0),
         new FieldItem(AssignmentField.NUMBER11, FieldLocation.VAR_DATA, 0, 65535, 203, 0, 0),
         new FieldItem(AssignmentField.NUMBER12, FieldLocation.VAR_DATA, 0, 65535, 204, 0, 0),
         new FieldItem(AssignmentField.NUMBER13, FieldLocation.VAR_DATA, 0, 65535, 205, 0, 0),
         new FieldItem(AssignmentField.NUMBER14, FieldLocation.VAR_DATA, 0, 65535, 206, 0, 0),
         new FieldItem(AssignmentField.NUMBER15, FieldLocation.VAR_DATA, 0, 65535, 207, 0, 0),
         new FieldItem(AssignmentField.NUMBER16, FieldLocation.VAR_DATA, 0, 65535, 208, 0, 0),
         new FieldItem(AssignmentField.NUMBER17, FieldLocation.VAR_DATA, 0, 65535, 209, 0, 0),
         new FieldItem(AssignmentField.NUMBER18, FieldLocation.VAR_DATA, 0, 65535, 210, 0, 0),
         new FieldItem(AssignmentField.NUMBER19, FieldLocation.VAR_DATA, 0, 65535, 211, 0, 0),
         new FieldItem(AssignmentField.NUMBER20, FieldLocation.VAR_DATA, 0, 65535, 212, 0, 0),
         new FieldItem(AssignmentField.DURATION1, FieldLocation.VAR_DATA, 0, 65535, 113, 0, 0),
         new FieldItem(AssignmentField.DURATION2, FieldLocation.VAR_DATA, 0, 65535, 114, 0, 0),
         new FieldItem(AssignmentField.DURATION3, FieldLocation.VAR_DATA, 0, 65535, 115, 0, 0),
         new FieldItem(AssignmentField.DURATION4, FieldLocation.VAR_DATA, 0, 65535, 176, 0, 0),
         new FieldItem(AssignmentField.DURATION5, FieldLocation.VAR_DATA, 0, 65535, 177, 0, 0),
         new FieldItem(AssignmentField.DURATION6, FieldLocation.VAR_DATA, 0, 65535, 178, 0, 0),
         new FieldItem(AssignmentField.DURATION7, FieldLocation.VAR_DATA, 0, 65535, 179, 0, 0),
         new FieldItem(AssignmentField.DURATION8, FieldLocation.VAR_DATA, 0, 65535, 180, 0, 0),
         new FieldItem(AssignmentField.DURATION9, FieldLocation.VAR_DATA, 0, 65535, 181, 0, 0),
         new FieldItem(AssignmentField.DURATION10, FieldLocation.VAR_DATA, 0, 65535, 182, 0, 0),
         new FieldItem(AssignmentField.DATE1, FieldLocation.VAR_DATA, 0, 65535, 166, 0, 0),
         new FieldItem(AssignmentField.DATE2, FieldLocation.VAR_DATA, 0, 65535, 167, 0, 0),
         new FieldItem(AssignmentField.DATE3, FieldLocation.VAR_DATA, 0, 65535, 168, 0, 0),
         new FieldItem(AssignmentField.DATE4, FieldLocation.VAR_DATA, 0, 65535, 169, 0, 0),
         new FieldItem(AssignmentField.DATE5, FieldLocation.VAR_DATA, 0, 65535, 170, 0, 0),
         new FieldItem(AssignmentField.DATE6, FieldLocation.VAR_DATA, 0, 65535, 171, 0, 0),
         new FieldItem(AssignmentField.DATE7, FieldLocation.VAR_DATA, 0, 65535, 172, 0, 0),
         new FieldItem(AssignmentField.DATE8, FieldLocation.VAR_DATA, 0, 65535, 173, 0, 0),
         new FieldItem(AssignmentField.DATE9, FieldLocation.VAR_DATA, 0, 65535, 174, 0, 0),
         new FieldItem(AssignmentField.DATE10, FieldLocation.VAR_DATA, 0, 65535, 175, 0, 0),
         new FieldItem(AssignmentField.BASELINE_START, FieldLocation.VAR_DATA, 0, 65535, 146, 0, 0),
         new FieldItem(AssignmentField.BASELINE_FINISH, FieldLocation.VAR_DATA, 0, 65535, 147, 0, 0),
         new FieldItem(AssignmentField.BASELINE_WORK, FieldLocation.VAR_DATA, 0, 65535, 16, 0, 0),
         new FieldItem(AssignmentField.BASELINE_COST, FieldLocation.VAR_DATA, 0, 65535, 32, 0, 0),
         new FieldItem(AssignmentField.COST1, FieldLocation.VAR_DATA, 0, 65535, 119, 0, 0),
         new FieldItem(AssignmentField.COST2, FieldLocation.VAR_DATA, 0, 65535, 120, 0, 0),
         new FieldItem(AssignmentField.COST3, FieldLocation.VAR_DATA, 0, 65535, 121, 0, 0),
         new FieldItem(AssignmentField.COST4, FieldLocation.VAR_DATA, 0, 65535, 159, 0, 0),
         new FieldItem(AssignmentField.COST5, FieldLocation.VAR_DATA, 0, 65535, 160, 0, 0),
         new FieldItem(AssignmentField.COST6, FieldLocation.VAR_DATA, 0, 65535, 161, 0, 0),
         new FieldItem(AssignmentField.COST7, FieldLocation.VAR_DATA, 0, 65535, 162, 0, 0),
         new FieldItem(AssignmentField.COST8, FieldLocation.VAR_DATA, 0, 65535, 163, 0, 0),
         new FieldItem(AssignmentField.COST9, FieldLocation.VAR_DATA, 0, 65535, 164, 0, 0),
         new FieldItem(AssignmentField.COST10, FieldLocation.VAR_DATA, 0, 65535, 165, 0, 0),
         new FieldItem(AssignmentField.GUID, FieldLocation.FIXED_DATA, 1, 0, 636, 0, 0),
         new FieldItem(AssignmentField.ASSIGNMENT_TASK_GUID, FieldLocation.FIXED_DATA, 1, 16, 637, 0, 0),
         new FieldItem(AssignmentField.ASSIGNMENT_RESOURCE_GUID, FieldLocation.FIXED_DATA, 1, 32, 638, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_COST1, FieldLocation.VAR_DATA, 1, 65535, 381, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_COST2, FieldLocation.VAR_DATA, 1, 65535, 382, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_COST3, FieldLocation.VAR_DATA, 1, 65535, 383, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_COST4, FieldLocation.VAR_DATA, 1, 65535, 384, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_COST5, FieldLocation.VAR_DATA, 1, 65535, 385, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_COST6, FieldLocation.VAR_DATA, 1, 65535, 386, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_COST7, FieldLocation.VAR_DATA, 1, 65535, 387, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_COST8, FieldLocation.VAR_DATA, 1, 65535, 388, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_COST9, FieldLocation.VAR_DATA, 1, 65535, 389, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_COST10, FieldLocation.VAR_DATA, 1, 65535, 390, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE1, FieldLocation.VAR_DATA, 1, 65535, 391, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE2, FieldLocation.VAR_DATA, 1, 65535, 392, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE3, FieldLocation.VAR_DATA, 1, 65535, 393, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE4, FieldLocation.VAR_DATA, 1, 65535, 394, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE5, FieldLocation.VAR_DATA, 1, 65535, 395, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE6, FieldLocation.VAR_DATA, 1, 65535, 396, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE7, FieldLocation.VAR_DATA, 1, 65535, 397, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE8, FieldLocation.VAR_DATA, 1, 65535, 398, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE9, FieldLocation.VAR_DATA, 1, 65535, 399, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE10, FieldLocation.VAR_DATA, 1, 65535, 400, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE11, FieldLocation.VAR_DATA, 1, 65535, 401, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE12, FieldLocation.VAR_DATA, 1, 65535, 402, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE13, FieldLocation.VAR_DATA, 1, 65535, 403, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE14, FieldLocation.VAR_DATA, 1, 65535, 404, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE15, FieldLocation.VAR_DATA, 1, 65535, 405, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE16, FieldLocation.VAR_DATA, 1, 65535, 406, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE17, FieldLocation.VAR_DATA, 1, 65535, 407, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE18, FieldLocation.VAR_DATA, 1, 65535, 408, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE19, FieldLocation.VAR_DATA, 1, 65535, 409, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE20, FieldLocation.VAR_DATA, 1, 65535, 410, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE21, FieldLocation.VAR_DATA, 1, 65535, 411, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE22, FieldLocation.VAR_DATA, 1, 65535, 412, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE23, FieldLocation.VAR_DATA, 1, 65535, 413, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE24, FieldLocation.VAR_DATA, 1, 65535, 414, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE25, FieldLocation.VAR_DATA, 1, 65535, 415, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE26, FieldLocation.VAR_DATA, 1, 65535, 416, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE27, FieldLocation.VAR_DATA, 1, 65535, 417, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE28, FieldLocation.VAR_DATA, 1, 65535, 418, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE29, FieldLocation.VAR_DATA, 1, 65535, 419, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DATE30, FieldLocation.VAR_DATA, 1, 65535, 420, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DURATION1, FieldLocation.VAR_DATA, 1, 65535, 421, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DURATION2, FieldLocation.VAR_DATA, 1, 65535, 422, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DURATION3, FieldLocation.VAR_DATA, 1, 65535, 423, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DURATION4, FieldLocation.VAR_DATA, 1, 65535, 424, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DURATION5, FieldLocation.VAR_DATA, 1, 65535, 425, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DURATION6, FieldLocation.VAR_DATA, 1, 65535, 426, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DURATION7, FieldLocation.VAR_DATA, 1, 65535, 427, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DURATION8, FieldLocation.VAR_DATA, 1, 65535, 428, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DURATION9, FieldLocation.VAR_DATA, 1, 65535, 429, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_DURATION10, FieldLocation.VAR_DATA, 1, 65535, 430, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER1, FieldLocation.VAR_DATA, 1, 65535, 461, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER2, FieldLocation.VAR_DATA, 1, 65535, 462, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER3, FieldLocation.VAR_DATA, 1, 65535, 463, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER4, FieldLocation.VAR_DATA, 1, 65535, 464, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER5, FieldLocation.VAR_DATA, 1, 65535, 465, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER6, FieldLocation.VAR_DATA, 1, 65535, 466, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER7, FieldLocation.VAR_DATA, 1, 65535, 467, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER8, FieldLocation.VAR_DATA, 1, 65535, 468, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER9, FieldLocation.VAR_DATA, 1, 65535, 469, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER10, FieldLocation.VAR_DATA, 1, 65535, 470, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER11, FieldLocation.VAR_DATA, 1, 65535, 471, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER12, FieldLocation.VAR_DATA, 1, 65535, 472, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER13, FieldLocation.VAR_DATA, 1, 65535, 473, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER14, FieldLocation.VAR_DATA, 1, 65535, 474, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER15, FieldLocation.VAR_DATA, 1, 65535, 475, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER16, FieldLocation.VAR_DATA, 1, 65535, 476, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER17, FieldLocation.VAR_DATA, 1, 65535, 477, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER18, FieldLocation.VAR_DATA, 1, 65535, 478, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER19, FieldLocation.VAR_DATA, 1, 65535, 479, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER20, FieldLocation.VAR_DATA, 1, 65535, 480, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER21, FieldLocation.VAR_DATA, 1, 65535, 481, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER22, FieldLocation.VAR_DATA, 1, 65535, 482, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER23, FieldLocation.VAR_DATA, 1, 65535, 483, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER24, FieldLocation.VAR_DATA, 1, 65535, 484, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER25, FieldLocation.VAR_DATA, 1, 65535, 485, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER26, FieldLocation.VAR_DATA, 1, 65535, 486, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER27, FieldLocation.VAR_DATA, 1, 65535, 487, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER28, FieldLocation.VAR_DATA, 1, 65535, 488, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER29, FieldLocation.VAR_DATA, 1, 65535, 489, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER30, FieldLocation.VAR_DATA, 1, 65535, 490, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER31, FieldLocation.VAR_DATA, 1, 65535, 491, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER32, FieldLocation.VAR_DATA, 1, 65535, 492, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER33, FieldLocation.VAR_DATA, 1, 65535, 493, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER34, FieldLocation.VAR_DATA, 1, 65535, 494, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER35, FieldLocation.VAR_DATA, 1, 65535, 495, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER36, FieldLocation.VAR_DATA, 1, 65535, 496, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER37, FieldLocation.VAR_DATA, 1, 65535, 497, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER38, FieldLocation.VAR_DATA, 1, 65535, 498, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER39, FieldLocation.VAR_DATA, 1, 65535, 499, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_NUMBER40, FieldLocation.VAR_DATA, 1, 65535, 500, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT1, FieldLocation.VAR_DATA, 1, 65535, 501, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT2, FieldLocation.VAR_DATA, 1, 65535, 502, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT3, FieldLocation.VAR_DATA, 1, 65535, 503, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT4, FieldLocation.VAR_DATA, 1, 65535, 504, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT5, FieldLocation.VAR_DATA, 1, 65535, 505, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT6, FieldLocation.VAR_DATA, 1, 65535, 506, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT7, FieldLocation.VAR_DATA, 1, 65535, 507, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT8, FieldLocation.VAR_DATA, 1, 65535, 508, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT9, FieldLocation.VAR_DATA, 1, 65535, 509, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT10, FieldLocation.VAR_DATA, 1, 65535, 510, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT11, FieldLocation.VAR_DATA, 1, 65535, 511, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT12, FieldLocation.VAR_DATA, 1, 65535, 512, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT13, FieldLocation.VAR_DATA, 1, 65535, 513, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT14, FieldLocation.VAR_DATA, 1, 65535, 514, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT15, FieldLocation.VAR_DATA, 1, 65535, 515, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT16, FieldLocation.VAR_DATA, 1, 65535, 516, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT17, FieldLocation.VAR_DATA, 1, 65535, 517, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT18, FieldLocation.VAR_DATA, 1, 65535, 518, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT19, FieldLocation.VAR_DATA, 1, 65535, 519, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT20, FieldLocation.VAR_DATA, 1, 65535, 520, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT21, FieldLocation.VAR_DATA, 1, 65535, 521, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT22, FieldLocation.VAR_DATA, 1, 65535, 522, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT23, FieldLocation.VAR_DATA, 1, 65535, 523, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT24, FieldLocation.VAR_DATA, 1, 65535, 524, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT25, FieldLocation.VAR_DATA, 1, 65535, 525, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT26, FieldLocation.VAR_DATA, 1, 65535, 526, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT27, FieldLocation.VAR_DATA, 1, 65535, 527, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT28, FieldLocation.VAR_DATA, 1, 65535, 528, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT29, FieldLocation.VAR_DATA, 1, 65535, 529, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT30, FieldLocation.VAR_DATA, 1, 65535, 530, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT31, FieldLocation.VAR_DATA, 1, 65535, 531, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT32, FieldLocation.VAR_DATA, 1, 65535, 532, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT33, FieldLocation.VAR_DATA, 1, 65535, 533, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT34, FieldLocation.VAR_DATA, 1, 65535, 534, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT35, FieldLocation.VAR_DATA, 1, 65535, 535, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT36, FieldLocation.VAR_DATA, 1, 65535, 536, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT37, FieldLocation.VAR_DATA, 1, 65535, 537, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT38, FieldLocation.VAR_DATA, 1, 65535, 538, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT39, FieldLocation.VAR_DATA, 1, 65535, 539, 0, 0),
         new FieldItem(AssignmentField.ENTERPRISE_TEXT40, FieldLocation.VAR_DATA, 1, 65535, 540, 0, 0),
         new FieldItem(AssignmentField.RESOURCE_REQUEST_TYPE, FieldLocation.VAR_DATA, 1, 65535, 605, 0, 0),
         new FieldItem(AssignmentField.ACTUAL_WORK_PROTECTED, FieldLocation.VAR_DATA, 1, 65535, 630, 0, 0),
         new FieldItem(AssignmentField.ACTUAL_OVERTIME_WORK_PROTECTED, FieldLocation.VAR_DATA, 1, 65535, 631, 0, 0),
         new FieldItem(AssignmentField.CREATED, FieldLocation.VAR_DATA, 1, 65535, 634, 0, 0),
         new FieldItem(AssignmentField.BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 669, 0, 0),
         new FieldItem(AssignmentField.BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 670, 0, 0),
         new FieldItem(AssignmentField.BASELINE_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 673, 0, 0),
         new FieldItem(AssignmentField.BASELINE_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 674, 0, 0),
         new FieldItem(AssignmentField.BASELINE1_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 677, 0, 0),
         new FieldItem(AssignmentField.BASELINE1_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 678, 0, 0),
         new FieldItem(AssignmentField.BASELINE2_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 681, 0, 0),
         new FieldItem(AssignmentField.BASELINE2_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 682, 0, 0),
         new FieldItem(AssignmentField.BASELINE3_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 685, 0, 0),
         new FieldItem(AssignmentField.BASELINE3_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 686, 0, 0),
         new FieldItem(AssignmentField.BASELINE4_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 689, 0, 0),
         new FieldItem(AssignmentField.BASELINE4_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 690, 0, 0),
         new FieldItem(AssignmentField.BASELINE5_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 693, 0, 0),
         new FieldItem(AssignmentField.BASELINE5_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 694, 0, 0),
         new FieldItem(AssignmentField.BASELINE6_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 697, 0, 0),
         new FieldItem(AssignmentField.BASELINE6_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 698, 0, 0),
         new FieldItem(AssignmentField.BASELINE7_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 701, 0, 0),
         new FieldItem(AssignmentField.BASELINE7_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 702, 0, 0),
         new FieldItem(AssignmentField.BASELINE8_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 705, 0, 0),
         new FieldItem(AssignmentField.BASELINE8_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 706, 0, 0),
         new FieldItem(AssignmentField.BASELINE9_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 709, 0, 0),
         new FieldItem(AssignmentField.BASELINE9_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 710, 0, 0),
         new FieldItem(AssignmentField.BASELINE10_BUDGET_WORK, FieldLocation.VAR_DATA, 1, 65535, 713, 0, 0),
         new FieldItem(AssignmentField.BASELINE10_BUDGET_COST, FieldLocation.VAR_DATA, 1, 65535, 714, 0, 0),
         new FieldItem(AssignmentField.BASELINE1_START, FieldLocation.VAR_DATA, 1, 65535, 295, 0, 0),
         new FieldItem(AssignmentField.BASELINE1_FINISH, FieldLocation.VAR_DATA, 1, 65535, 296, 0, 0),
         new FieldItem(AssignmentField.BASELINE1_WORK, FieldLocation.VAR_DATA, 1, 65535, 289, 0, 0),
         new FieldItem(AssignmentField.BASELINE1_COST, FieldLocation.VAR_DATA, 1, 65535, 290, 0, 0),
         new FieldItem(AssignmentField.BASELINE2_START, FieldLocation.VAR_DATA, 1, 65535, 304, 0, 0),
         new FieldItem(AssignmentField.BASELINE2_FINISH, FieldLocation.VAR_DATA, 1, 65535, 305, 0, 0),
         new FieldItem(AssignmentField.BASELINE2_WORK, FieldLocation.VAR_DATA, 1, 65535, 298, 0, 0),
         new FieldItem(AssignmentField.BASELINE2_COST, FieldLocation.VAR_DATA, 1, 65535, 299, 0, 0),
         new FieldItem(AssignmentField.BASELINE3_START, FieldLocation.VAR_DATA, 1, 65535, 313, 0, 0),
         new FieldItem(AssignmentField.BASELINE3_FINISH, FieldLocation.VAR_DATA, 1, 65535, 314, 0, 0),
         new FieldItem(AssignmentField.BASELINE3_WORK, FieldLocation.VAR_DATA, 1, 65535, 307, 0, 0),
         new FieldItem(AssignmentField.BASELINE3_COST, FieldLocation.VAR_DATA, 1, 65535, 308, 0, 0),
         new FieldItem(AssignmentField.BASELINE4_START, FieldLocation.VAR_DATA, 1, 65535, 322, 0, 0),
         new FieldItem(AssignmentField.BASELINE4_FINISH, FieldLocation.VAR_DATA, 1, 65535, 323, 0, 0),
         new FieldItem(AssignmentField.BASELINE4_WORK, FieldLocation.VAR_DATA, 1, 65535, 316, 0, 0),
         new FieldItem(AssignmentField.BASELINE4_COST, FieldLocation.VAR_DATA, 1, 65535, 317, 0, 0),
         new FieldItem(AssignmentField.BASELINE5_START, FieldLocation.VAR_DATA, 1, 65535, 331, 0, 0),
         new FieldItem(AssignmentField.BASELINE5_FINISH, FieldLocation.VAR_DATA, 1, 65535, 332, 0, 0),
         new FieldItem(AssignmentField.BASELINE5_WORK, FieldLocation.VAR_DATA, 1, 65535, 325, 0, 0),
         new FieldItem(AssignmentField.BASELINE5_COST, FieldLocation.VAR_DATA, 1, 65535, 326, 0, 0),
         new FieldItem(AssignmentField.BASELINE6_START, FieldLocation.VAR_DATA, 1, 65535, 340, 0, 0),
         new FieldItem(AssignmentField.BASELINE6_FINISH, FieldLocation.VAR_DATA, 1, 65535, 341, 0, 0),
         new FieldItem(AssignmentField.BASELINE6_WORK, FieldLocation.VAR_DATA, 1, 65535, 334, 0, 0),
         new FieldItem(AssignmentField.BASELINE6_COST, FieldLocation.VAR_DATA, 1, 65535, 335, 0, 0),
         new FieldItem(AssignmentField.BASELINE7_START, FieldLocation.VAR_DATA, 1, 65535, 349, 0, 0),
         new FieldItem(AssignmentField.BASELINE7_FINISH, FieldLocation.VAR_DATA, 1, 65535, 350, 0, 0),
         new FieldItem(AssignmentField.BASELINE7_WORK, FieldLocation.VAR_DATA, 1, 65535, 343, 0, 0),
         new FieldItem(AssignmentField.BASELINE7_COST, FieldLocation.VAR_DATA, 1, 65535, 344, 0, 0),
         new FieldItem(AssignmentField.BASELINE8_START, FieldLocation.VAR_DATA, 1, 65535, 358, 0, 0),
         new FieldItem(AssignmentField.BASELINE8_FINISH, FieldLocation.VAR_DATA, 1, 65535, 359, 0, 0),
         new FieldItem(AssignmentField.BASELINE8_WORK, FieldLocation.VAR_DATA, 1, 65535, 352, 0, 0),
         new FieldItem(AssignmentField.BASELINE8_COST, FieldLocation.VAR_DATA, 1, 65535, 353, 0, 0),
         new FieldItem(AssignmentField.BASELINE9_START, FieldLocation.VAR_DATA, 1, 65535, 367, 0, 0),
         new FieldItem(AssignmentField.BASELINE9_FINISH, FieldLocation.VAR_DATA, 1, 65535, 368, 0, 0),
         new FieldItem(AssignmentField.BASELINE9_WORK, FieldLocation.VAR_DATA, 1, 65535, 361, 0, 0),
         new FieldItem(AssignmentField.BASELINE9_COST, FieldLocation.VAR_DATA, 1, 65535, 362, 0, 0),
         new FieldItem(AssignmentField.BASELINE10_START, FieldLocation.VAR_DATA, 1, 65535, 376, 0, 0),
         new FieldItem(AssignmentField.BASELINE10_FINISH, FieldLocation.VAR_DATA, 1, 65535, 377, 0, 0),
         new FieldItem(AssignmentField.BASELINE10_WORK, FieldLocation.VAR_DATA, 1, 65535, 370, 0, 0),
         new FieldItem(AssignmentField.BASELINE10_COST, FieldLocation.VAR_DATA, 1, 65535, 371, 0, 0),
         new FieldItem(AssignmentField.OWNER, FieldLocation.VAR_DATA, 1, 65535, 668, 0, 0)
      };
   }

   @Override protected FieldItem[] getDefaultRelationData()
   {
      return new FieldItem[0];
   }

   /**
    * The values for these field types as read from the file, don't
    * correspond to the values actually used in the var data blocks.
    * It's not clear why, or whether these values are actually
    * present somewhere in the file.
    */
   private static final Object[][] VAR_DATA_MAP_CONTENT =
   {
      {
         AssignmentField.COST1,
         Integer.valueOf(16414)
      },
      {
         AssignmentField.COST2,
         Integer.valueOf(16415)
      },
      {
         AssignmentField.COST3,
         Integer.valueOf(16416)
      },
      {
         AssignmentField.COST4,
         Integer.valueOf(16417)
      },
      {
         AssignmentField.COST5,
         Integer.valueOf(16418)
      },
      {
         AssignmentField.COST6,
         Integer.valueOf(16419)
      },
      {
         AssignmentField.COST7,
         Integer.valueOf(16420)
      },
      {
         AssignmentField.COST8,
         Integer.valueOf(16421)
      },
      {
         AssignmentField.COST9,
         Integer.valueOf(16422)
      },
      {
         AssignmentField.COST10,
         Integer.valueOf(16423)
      },

      {
         AssignmentField.FLAG1,
         Integer.valueOf(16394)
      },
      {
         AssignmentField.FLAG2,
         Integer.valueOf(16395)
      },
      {
         AssignmentField.FLAG3,
         Integer.valueOf(16396)
      },
      {
         AssignmentField.FLAG4,
         Integer.valueOf(16397)
      },
      {
         AssignmentField.FLAG5,
         Integer.valueOf(16398)
      },
      {
         AssignmentField.FLAG6,
         Integer.valueOf(16399)
      },
      {
         AssignmentField.FLAG7,
         Integer.valueOf(16400)
      },
      {
         AssignmentField.FLAG8,
         Integer.valueOf(16401)
      },
      {
         AssignmentField.FLAG9,
         Integer.valueOf(16402)
      },
      {
         AssignmentField.FLAG10,
         Integer.valueOf(16403)
      },
      {
         AssignmentField.FLAG11,
         Integer.valueOf(16404)
      },
      {
         AssignmentField.FLAG12,
         Integer.valueOf(16405)
      },
      {
         AssignmentField.FLAG13,
         Integer.valueOf(16406)
      },
      {
         AssignmentField.FLAG14,
         Integer.valueOf(16407)
      },
      {
         AssignmentField.FLAG15,
         Integer.valueOf(16408)
      },
      {
         AssignmentField.FLAG16,
         Integer.valueOf(16409)
      },
      {
         AssignmentField.FLAG17,
         Integer.valueOf(16410)
      },
      {
         AssignmentField.FLAG18,
         Integer.valueOf(16411)
      },
      {
         AssignmentField.FLAG19,
         Integer.valueOf(16412)
      },
      {
         AssignmentField.FLAG20,
         Integer.valueOf(16413)
      },
   };

   private static final Map<FieldType, Integer> VAR_DATA_MAP = new HashMap<>();
   static
   {
      for (Object[] item : VAR_DATA_MAP_CONTENT)
      {
         VAR_DATA_MAP.put((FieldType) item[0], (Integer) item[1]);
      }
   }

}
