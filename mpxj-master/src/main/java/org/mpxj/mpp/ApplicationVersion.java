/*
 * file:       ApplicationVersion.java
 * author:     <PERSON>
 * copyright:  (c) Packwood Software 2002-2015
 * date:       30/12/2015
 */

/*
 * This library is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as published by the
 * Free Software Foundation; either version 2.1 of the License, or (at your
 * option) any later version.
 *
 * This library is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 * or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public
 * License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this library; if not, write to the Free Software Foundation, Inc.,
 * 59 Temple Place, Suite 330, Boston, MA 02111-1307, USA.
 */

package org.mpxj.mpp;

/**
 * Constants representing the different versions of Microsoft Project.
 */
public class ApplicationVersion
{
   public static final int PROJECT_2016 = 16;
   public static final int PROJECT_2013 = 15;
   public static final int PROJECT_2010 = 14;
   public static final int PROJECT_2007 = 12;
   public static final int PROJECT_2003 = 11;
   public static final int PROJECT_2002 = 10;
   public static final int PROJECT_2000 = 9;
   public static final int PROJECT_98 = 8;
}
