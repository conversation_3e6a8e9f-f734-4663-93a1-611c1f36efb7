//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.2
// See https://eclipse-ee4j.github.io/jaxb-ri
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2025.01.02 at 04:27:10 PM GMT
//

package org.mpxj.primavera.schema;

import jakarta.xml.bind.annotation.XmlRegistry;

/**
 * This object contains factory methods for each
 * Java content interface and Java element interface
 * generated in the org.mpxj.primavera.schema package.
 * <p>An ObjectFactory allows you to programatically
 * construct new instances of the Java representation
 * for XML content. The Java representation of XML
 * content can consist of schema derived interfaces
 * and classes representing the binding of schema
 * type definitions, element declarations and model
 * groups.  Factory methods for each of these are
 * provided in this class.
 *
 */
@XmlRegistry public class ObjectFactory
{

   /**
    * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: org.mpxj.primavera.schema
    *
    */
   public ObjectFactory()
   {
   }

   /**
    * Create an instance of {@link ProjectRoleSpreadType }
    *
    */
   public ProjectRoleSpreadType createProjectRoleSpreadType()
   {
      return new ProjectRoleSpreadType();
   }

   /**
    * Create an instance of {@link ProjectResourceSpreadType }
    *
    */
   public ProjectResourceSpreadType createProjectResourceSpreadType()
   {
      return new ProjectResourceSpreadType();
   }

   /**
    * Create an instance of {@link ActivitySpreadType }
    *
    */
   public ActivitySpreadType createActivitySpreadType()
   {
      return new ActivitySpreadType();
   }

   /**
    * Create an instance of {@link ResourceAssignmentSpreadType }
    *
    */
   public ResourceAssignmentSpreadType createResourceAssignmentSpreadType()
   {
      return new ResourceAssignmentSpreadType();
   }

   /**
    * Create an instance of {@link EPSProjectWBSSpreadType }
    *
    */
   public EPSProjectWBSSpreadType createEPSProjectWBSSpreadType()
   {
      return new EPSProjectWBSSpreadType();
   }

   /**
    * Create an instance of {@link ResourceRequestType }
    *
    */
   public ResourceRequestType createResourceRequestType()
   {
      return new ResourceRequestType();
   }

   /**
    * Create an instance of {@link UserType }
    *
    */
   public UserType createUserType()
   {
      return new UserType();
   }

   /**
    * Create an instance of {@link CalendarType }
    *
    */
   public CalendarType createCalendarType()
   {
      return new CalendarType();
   }

   /**
    * Create an instance of {@link CalendarType.HolidayOrExceptions }
    *
    */
   public CalendarType.HolidayOrExceptions createCalendarTypeHolidayOrExceptions()
   {
      return new CalendarType.HolidayOrExceptions();
   }

   /**
    * Create an instance of {@link CalendarType.StandardWorkWeek }
    *
    */
   public CalendarType.StandardWorkWeek createCalendarTypeStandardWorkWeek()
   {
      return new CalendarType.StandardWorkWeek();
   }

   /**
    * Create an instance of {@link ProjectListType }
    *
    */
   public ProjectListType createProjectListType()
   {
      return new ProjectListType();
   }

   /**
    * Create an instance of {@link ProjectListType.Project }
    *
    */
   public ProjectListType.Project createProjectListTypeProject()
   {
      return new ProjectListType.Project();
   }

   /**
    * Create an instance of {@link APIBusinessObjects }
    *
    */
   public APIBusinessObjects createAPIBusinessObjects()
   {
      return new APIBusinessObjects();
   }

   /**
    * Create an instance of {@link DisplayCurrencyType }
    *
    */
   public DisplayCurrencyType createDisplayCurrencyType()
   {
      return new DisplayCurrencyType();
   }

   /**
    * Create an instance of {@link ProjectResourceCategoryType }
    *
    */
   public ProjectResourceCategoryType createProjectResourceCategoryType()
   {
      return new ProjectResourceCategoryType();
   }

   /**
    * Create an instance of {@link UnitOfMeasureType }
    *
    */
   public UnitOfMeasureType createUnitOfMeasureType()
   {
      return new UnitOfMeasureType();
   }

   /**
    * Create an instance of {@link CostAccountType }
    *
    */
   public CostAccountType createCostAccountType()
   {
      return new CostAccountType();
   }

   /**
    * Create an instance of {@link CurrencyType }
    *
    */
   public CurrencyType createCurrencyType()
   {
      return new CurrencyType();
   }

   /**
    * Create an instance of {@link UDFTypeType }
    *
    */
   public UDFTypeType createUDFTypeType()
   {
      return new UDFTypeType();
   }

   /**
    * Create an instance of {@link LocationType }
    *
    */
   public LocationType createLocationType()
   {
      return new LocationType();
   }

   /**
    * Create an instance of {@link UDFCodeType }
    *
    */
   public UDFCodeType createUDFCodeType()
   {
      return new UDFCodeType();
   }

   /**
    * Create an instance of {@link ExpenseCategoryType }
    *
    */
   public ExpenseCategoryType createExpenseCategoryType()
   {
      return new ExpenseCategoryType();
   }

   /**
    * Create an instance of {@link NotebookTopicType }
    *
    */
   public NotebookTopicType createNotebookTopicType()
   {
      return new NotebookTopicType();
   }

   /**
    * Create an instance of {@link WBSCategoryType }
    *
    */
   public WBSCategoryType createWBSCategoryType()
   {
      return new WBSCategoryType();
   }

   /**
    * Create an instance of {@link FundingSourceType }
    *
    */
   public FundingSourceType createFundingSourceType()
   {
      return new FundingSourceType();
   }

   /**
    * Create an instance of {@link ThresholdParameterType }
    *
    */
   public ThresholdParameterType createThresholdParameterType()
   {
      return new ThresholdParameterType();
   }

   /**
    * Create an instance of {@link OBSType }
    *
    */
   public OBSType createOBSType()
   {
      return new OBSType();
   }

   /**
    * Create an instance of {@link ShiftPeriodType }
    *
    */
   public ShiftPeriodType createShiftPeriodType()
   {
      return new ShiftPeriodType();
   }

   /**
    * Create an instance of {@link ShiftType }
    *
    */
   public ShiftType createShiftType()
   {
      return new ShiftType();
   }

   /**
    * Create an instance of {@link ProjectCodeTypeType }
    *
    */
   public ProjectCodeTypeType createProjectCodeTypeType()
   {
      return new ProjectCodeTypeType();
   }

   /**
    * Create an instance of {@link ProjectCodeType }
    *
    */
   public ProjectCodeType createProjectCodeType()
   {
      return new ProjectCodeType();
   }

   /**
    * Create an instance of {@link ResourceCodeTypeType }
    *
    */
   public ResourceCodeTypeType createResourceCodeTypeType()
   {
      return new ResourceCodeTypeType();
   }

   /**
    * Create an instance of {@link ResourceCodeType }
    *
    */
   public ResourceCodeType createResourceCodeType()
   {
      return new ResourceCodeType();
   }

   /**
    * Create an instance of {@link RoleCodeTypeType }
    *
    */
   public RoleCodeTypeType createRoleCodeTypeType()
   {
      return new RoleCodeTypeType();
   }

   /**
    * Create an instance of {@link RoleCodeType }
    *
    */
   public RoleCodeType createRoleCodeType()
   {
      return new RoleCodeType();
   }

   /**
    * Create an instance of {@link ResourceCurveType }
    *
    */
   public ResourceCurveType createResourceCurveType()
   {
      return new ResourceCurveType();
   }

   /**
    * Create an instance of {@link RoleType }
    *
    */
   public RoleType createRoleType()
   {
      return new RoleType();
   }

   /**
    * Create an instance of {@link RoleRateType }
    *
    */
   public RoleRateType createRoleRateType()
   {
      return new RoleRateType();
   }

   /**
    * Create an instance of {@link RoleLimitType }
    *
    */
   public RoleLimitType createRoleLimitType()
   {
      return new RoleLimitType();
   }

   /**
    * Create an instance of {@link ResourceType }
    *
    */
   public ResourceType createResourceType()
   {
      return new ResourceType();
   }

   /**
    * Create an instance of {@link ResourceRateType }
    *
    */
   public ResourceRateType createResourceRateType()
   {
      return new ResourceRateType();
   }

   /**
    * Create an instance of {@link ActivityCodeTypeType }
    *
    */
   public ActivityCodeTypeType createActivityCodeTypeType()
   {
      return new ActivityCodeTypeType();
   }

   /**
    * Create an instance of {@link ActivityCodeType }
    *
    */
   public ActivityCodeType createActivityCodeType()
   {
      return new ActivityCodeType();
   }

   /**
    * Create an instance of {@link ResourceAssignmentCodeTypeType }
    *
    */
   public ResourceAssignmentCodeTypeType createResourceAssignmentCodeTypeType()
   {
      return new ResourceAssignmentCodeTypeType();
   }

   /**
    * Create an instance of {@link ResourceAssignmentCodeType }
    *
    */
   public ResourceAssignmentCodeType createResourceAssignmentCodeType()
   {
      return new ResourceAssignmentCodeType();
   }

   /**
    * Create an instance of {@link FinancialPeriodType }
    *
    */
   public FinancialPeriodType createFinancialPeriodType()
   {
      return new FinancialPeriodType();
   }

   /**
    * Create an instance of {@link ResourceRoleType }
    *
    */
   public ResourceRoleType createResourceRoleType()
   {
      return new ResourceRoleType();
   }

   /**
    * Create an instance of {@link EPSType }
    *
    */
   public EPSType createEPSType()
   {
      return new EPSType();
   }

   /**
    * Create an instance of {@link DocumentCategoryType }
    *
    */
   public DocumentCategoryType createDocumentCategoryType()
   {
      return new DocumentCategoryType();
   }

   /**
    * Create an instance of {@link DocumentStatusCodeType }
    *
    */
   public DocumentStatusCodeType createDocumentStatusCodeType()
   {
      return new DocumentStatusCodeType();
   }

   /**
    * Create an instance of {@link RiskCategoryType }
    *
    */
   public RiskCategoryType createRiskCategoryType()
   {
      return new RiskCategoryType();
   }

   /**
    * Create an instance of {@link RiskThresholdType }
    *
    */
   public RiskThresholdType createRiskThresholdType()
   {
      return new RiskThresholdType();
   }

   /**
    * Create an instance of {@link RiskThresholdLevelType }
    *
    */
   public RiskThresholdLevelType createRiskThresholdLevelType()
   {
      return new RiskThresholdLevelType();
   }

   /**
    * Create an instance of {@link RiskMatrixType }
    *
    */
   public RiskMatrixType createRiskMatrixType()
   {
      return new RiskMatrixType();
   }

   /**
    * Create an instance of {@link RiskMatrixScoreType }
    *
    */
   public RiskMatrixScoreType createRiskMatrixScoreType()
   {
      return new RiskMatrixScoreType();
   }

   /**
    * Create an instance of {@link RiskMatrixThresholdType }
    *
    */
   public RiskMatrixThresholdType createRiskMatrixThresholdType()
   {
      return new RiskMatrixThresholdType();
   }

   /**
    * Create an instance of {@link ActivityType }
    *
    */
   public ActivityType createActivityType()
   {
      return new ActivityType();
   }

   /**
    * Create an instance of {@link ActivityCodeAssignmentType }
    *
    */
   public ActivityCodeAssignmentType createActivityCodeAssignmentType()
   {
      return new ActivityCodeAssignmentType();
   }

   /**
    * Create an instance of {@link ActivityCodeUpdateType }
    *
    */
   public ActivityCodeUpdateType createActivityCodeUpdateType()
   {
      return new ActivityCodeUpdateType();
   }

   /**
    * Create an instance of {@link ActivityCommentType }
    *
    */
   public ActivityCommentType createActivityCommentType()
   {
      return new ActivityCommentType();
   }

   /**
    * Create an instance of {@link ActivityExpenseType }
    *
    */
   public ActivityExpenseType createActivityExpenseType()
   {
      return new ActivityExpenseType();
   }

   /**
    * Create an instance of {@link ActivityFilterType }
    *
    */
   public ActivityFilterType createActivityFilterType()
   {
      return new ActivityFilterType();
   }

   /**
    * Create an instance of {@link ActivityNoteType }
    *
    */
   public ActivityNoteType createActivityNoteType()
   {
      return new ActivityNoteType();
   }

   /**
    * Create an instance of {@link ActivityNoteUpdateType }
    *
    */
   public ActivityNoteUpdateType createActivityNoteUpdateType()
   {
      return new ActivityNoteUpdateType();
   }

   /**
    * Create an instance of {@link ActivityOwnerType }
    *
    */
   public ActivityOwnerType createActivityOwnerType()
   {
      return new ActivityOwnerType();
   }

   /**
    * Create an instance of {@link ActivityPeriodActualType }
    *
    */
   public ActivityPeriodActualType createActivityPeriodActualType()
   {
      return new ActivityPeriodActualType();
   }

   /**
    * Create an instance of {@link ActivityRiskType }
    *
    */
   public ActivityRiskType createActivityRiskType()
   {
      return new ActivityRiskType();
   }

   /**
    * Create an instance of {@link ActivityStepType }
    *
    */
   public ActivityStepType createActivityStepType()
   {
      return new ActivityStepType();
   }

   /**
    * Create an instance of {@link ActivityStepCreateType }
    *
    */
   public ActivityStepCreateType createActivityStepCreateType()
   {
      return new ActivityStepCreateType();
   }

   /**
    * Create an instance of {@link ActivityStepDeleteType }
    *
    */
   public ActivityStepDeleteType createActivityStepDeleteType()
   {
      return new ActivityStepDeleteType();
   }

   /**
    * Create an instance of {@link ActivityStepTemplateType }
    *
    */
   public ActivityStepTemplateType createActivityStepTemplateType()
   {
      return new ActivityStepTemplateType();
   }

   /**
    * Create an instance of {@link ActivityStepTemplateItemType }
    *
    */
   public ActivityStepTemplateItemType createActivityStepTemplateItemType()
   {
      return new ActivityStepTemplateItemType();
   }

   /**
    * Create an instance of {@link ActivityStepUpdateType }
    *
    */
   public ActivityStepUpdateType createActivityStepUpdateType()
   {
      return new ActivityStepUpdateType();
   }

   /**
    * Create an instance of {@link ActivityUpdateType }
    *
    */
   public ActivityUpdateType createActivityUpdateType()
   {
      return new ActivityUpdateType();
   }

   /**
    * Create an instance of {@link AlertType }
    *
    */
   public AlertType createAlertType()
   {
      return new AlertType();
   }

   /**
    * Create an instance of {@link AutovueAttrType }
    *
    */
   public AutovueAttrType createAutovueAttrType()
   {
      return new AutovueAttrType();
   }

   /**
    * Create an instance of {@link BaselineTypeType }
    *
    */
   public BaselineTypeType createBaselineTypeType()
   {
      return new BaselineTypeType();
   }

   /**
    * Create an instance of {@link CBSType }
    *
    */
   public CBSType createCBSType()
   {
      return new CBSType();
   }

   /**
    * Create an instance of {@link CBSDurationSummaryType }
    *
    */
   public CBSDurationSummaryType createCBSDurationSummaryType()
   {
      return new CBSDurationSummaryType();
   }

   /**
    * Create an instance of {@link ChangeSetType }
    *
    */
   public ChangeSetType createChangeSetType()
   {
      return new ChangeSetType();
   }

   /**
    * Create an instance of {@link DocumentType }
    *
    */
   public DocumentType createDocumentType()
   {
      return new DocumentType();
   }

   /**
    * Create an instance of {@link EPSBudgetChangeLogType }
    *
    */
   public EPSBudgetChangeLogType createEPSBudgetChangeLogType()
   {
      return new EPSBudgetChangeLogType();
   }

   /**
    * Create an instance of {@link EPSFundingType }
    *
    */
   public EPSFundingType createEPSFundingType()
   {
      return new EPSFundingType();
   }

   /**
    * Create an instance of {@link EPSNoteType }
    *
    */
   public EPSNoteType createEPSNoteType()
   {
      return new EPSNoteType();
   }

   /**
    * Create an instance of {@link EPSSpendingPlanType }
    *
    */
   public EPSSpendingPlanType createEPSSpendingPlanType()
   {
      return new EPSSpendingPlanType();
   }

   /**
    * Create an instance of {@link FinancialPeriodTemplateType }
    *
    */
   public FinancialPeriodTemplateType createFinancialPeriodTemplateType()
   {
      return new FinancialPeriodTemplateType();
   }

   /**
    * Create an instance of {@link GatewayDeploymentType }
    *
    */
   public GatewayDeploymentType createGatewayDeploymentType()
   {
      return new GatewayDeploymentType();
   }

   /**
    * Create an instance of {@link GlobalPreferencesType }
    *
    */
   public GlobalPreferencesType createGlobalPreferencesType()
   {
      return new GlobalPreferencesType();
   }

   /**
    * Create an instance of {@link GlobalProfileType }
    *
    */
   public GlobalProfileType createGlobalProfileType()
   {
      return new GlobalProfileType();
   }

   /**
    * Create an instance of {@link GlobalReplaceType }
    *
    */
   public GlobalReplaceType createGlobalReplaceType()
   {
      return new GlobalReplaceType();
   }

   /**
    * Create an instance of {@link ImportOptionsTemplateType }
    *
    */
   public ImportOptionsTemplateType createImportOptionsTemplateType()
   {
      return new ImportOptionsTemplateType();
   }

   /**
    * Create an instance of {@link IssueHistoryType }
    *
    */
   public IssueHistoryType createIssueHistoryType()
   {
      return new IssueHistoryType();
   }

   /**
    * Create an instance of {@link JobServiceType }
    *
    */
   public JobServiceType createJobServiceType()
   {
      return new JobServiceType();
   }

   /**
    * Create an instance of {@link LeanTaskType }
    *
    */
   public LeanTaskType createLeanTaskType()
   {
      return new LeanTaskType();
   }

   /**
    * Create an instance of {@link MSPTemplateType }
    *
    */
   public MSPTemplateType createMSPTemplateType()
   {
      return new MSPTemplateType();
   }

   /**
    * Create an instance of {@link OverheadCodeType }
    *
    */
   public OverheadCodeType createOverheadCodeType()
   {
      return new OverheadCodeType();
   }

   /**
    * Create an instance of {@link PAuditXType }
    *
    */
   public PAuditXType createPAuditXType()
   {
      return new PAuditXType();
   }

   /**
    * Create an instance of {@link ProfileType }
    *
    */
   public ProfileType createProfileType()
   {
      return new ProfileType();
   }

   /**
    * Create an instance of {@link ProjectType }
    *
    */
   public ProjectType createProjectType()
   {
      return new ProjectType();
   }

   /**
    * Create an instance of {@link BaselineProjectType }
    *
    */
   public BaselineProjectType createBaselineProjectType()
   {
      return new BaselineProjectType();
   }

   /**
    * Create an instance of {@link ProjectBudgetChangeLogType }
    *
    */
   public ProjectBudgetChangeLogType createProjectBudgetChangeLogType()
   {
      return new ProjectBudgetChangeLogType();
   }

   /**
    * Create an instance of {@link ProjectCodeAssignmentType }
    *
    */
   public ProjectCodeAssignmentType createProjectCodeAssignmentType()
   {
      return new ProjectCodeAssignmentType();
   }

   /**
    * Create an instance of {@link ProjectDeploymentType }
    *
    */
   public ProjectDeploymentType createProjectDeploymentType()
   {
      return new ProjectDeploymentType();
   }

   /**
    * Create an instance of {@link ProjectDocumentType }
    *
    */
   public ProjectDocumentType createProjectDocumentType()
   {
      return new ProjectDocumentType();
   }

   /**
    * Create an instance of {@link ProjectFundingType }
    *
    */
   public ProjectFundingType createProjectFundingType()
   {
      return new ProjectFundingType();
   }

   /**
    * Create an instance of {@link ProjectIssueType }
    *
    */
   public ProjectIssueType createProjectIssueType()
   {
      return new ProjectIssueType();
   }

   /**
    * Create an instance of {@link ProjectNoteType }
    *
    */
   public ProjectNoteType createProjectNoteType()
   {
      return new ProjectNoteType();
   }

   /**
    * Create an instance of {@link ProjectPortfolioType }
    *
    */
   public ProjectPortfolioType createProjectPortfolioType()
   {
      return new ProjectPortfolioType();
   }

   /**
    * Create an instance of {@link ProjectProfileType }
    *
    */
   public ProjectProfileType createProjectProfileType()
   {
      return new ProjectProfileType();
   }

   /**
    * Create an instance of {@link ProjectResourceType }
    *
    */
   public ProjectResourceType createProjectResourceType()
   {
      return new ProjectResourceType();
   }

   /**
    * Create an instance of {@link ProjectResourceQuantityType }
    *
    */
   public ProjectResourceQuantityType createProjectResourceQuantityType()
   {
      return new ProjectResourceQuantityType();
   }

   /**
    * Create an instance of {@link ProjectSpendingPlanType }
    *
    */
   public ProjectSpendingPlanType createProjectSpendingPlanType()
   {
      return new ProjectSpendingPlanType();
   }

   /**
    * Create an instance of {@link ProjectThresholdType }
    *
    */
   public ProjectThresholdType createProjectThresholdType()
   {
      return new ProjectThresholdType();
   }

   /**
    * Create an instance of {@link RelationshipType }
    *
    */
   public RelationshipType createRelationshipType()
   {
      return new RelationshipType();
   }

   /**
    * Create an instance of {@link ResourceAccessType }
    *
    */
   public ResourceAccessType createResourceAccessType()
   {
      return new ResourceAccessType();
   }

   /**
    * Create an instance of {@link ResourceAssignmentType }
    *
    */
   public ResourceAssignmentType createResourceAssignmentType()
   {
      return new ResourceAssignmentType();
   }

   /**
    * Create an instance of {@link ResourceAssignmentCodeAssignmentType }
    *
    */
   public ResourceAssignmentCodeAssignmentType createResourceAssignmentCodeAssignmentType()
   {
      return new ResourceAssignmentCodeAssignmentType();
   }

   /**
    * Create an instance of {@link ResourceAssignmentCreateType }
    *
    */
   public ResourceAssignmentCreateType createResourceAssignmentCreateType()
   {
      return new ResourceAssignmentCreateType();
   }

   /**
    * Create an instance of {@link ResourceAssignmentPeriodActualType }
    *
    */
   public ResourceAssignmentPeriodActualType createResourceAssignmentPeriodActualType()
   {
      return new ResourceAssignmentPeriodActualType();
   }

   /**
    * Create an instance of {@link ResourceAssignmentUpdateType }
    *
    */
   public ResourceAssignmentUpdateType createResourceAssignmentUpdateType()
   {
      return new ResourceAssignmentUpdateType();
   }

   /**
    * Create an instance of {@link ResourceCodeAssignmentType }
    *
    */
   public ResourceCodeAssignmentType createResourceCodeAssignmentType()
   {
      return new ResourceCodeAssignmentType();
   }

   /**
    * Create an instance of {@link ResourceHourType }
    *
    */
   public ResourceHourType createResourceHourType()
   {
      return new ResourceHourType();
   }

   /**
    * Create an instance of {@link ResourceLocationType }
    *
    */
   public ResourceLocationType createResourceLocationType()
   {
      return new ResourceLocationType();
   }

   /**
    * Create an instance of {@link ResourceTeamType }
    *
    */
   public ResourceTeamType createResourceTeamType()
   {
      return new ResourceTeamType();
   }

   /**
    * Create an instance of {@link RiskType }
    *
    */
   public RiskType createRiskType()
   {
      return new RiskType();
   }

   /**
    * Create an instance of {@link RiskImpactType }
    *
    */
   public RiskImpactType createRiskImpactType()
   {
      return new RiskImpactType();
   }

   /**
    * Create an instance of {@link RiskResponseActionType }
    *
    */
   public RiskResponseActionType createRiskResponseActionType()
   {
      return new RiskResponseActionType();
   }

   /**
    * Create an instance of {@link RiskResponseActionImpactType }
    *
    */
   public RiskResponseActionImpactType createRiskResponseActionImpactType()
   {
      return new RiskResponseActionImpactType();
   }

   /**
    * Create an instance of {@link RiskResponsePlanType }
    *
    */
   public RiskResponsePlanType createRiskResponsePlanType()
   {
      return new RiskResponsePlanType();
   }

   /**
    * Create an instance of {@link RoleCodeAssignmentType }
    *
    */
   public RoleCodeAssignmentType createRoleCodeAssignmentType()
   {
      return new RoleCodeAssignmentType();
   }

   /**
    * Create an instance of {@link RoleTeamType }
    *
    */
   public RoleTeamType createRoleTeamType()
   {
      return new RoleTeamType();
   }

   /**
    * Create an instance of {@link ScheduleCheckOptionType }
    *
    */
   public ScheduleCheckOptionType createScheduleCheckOptionType()
   {
      return new ScheduleCheckOptionType();
   }

   /**
    * Create an instance of {@link ScheduleOptionsType }
    *
    */
   public ScheduleOptionsType createScheduleOptionsType()
   {
      return new ScheduleOptionsType();
   }

   /**
    * Create an instance of {@link StepUserDefinedValueUpdateType }
    *
    */
   public StepUserDefinedValueUpdateType createStepUserDefinedValueUpdateType()
   {
      return new StepUserDefinedValueUpdateType();
   }

   /**
    * Create an instance of {@link TimesheetType }
    *
    */
   public TimesheetType createTimesheetType()
   {
      return new TimesheetType();
   }

   /**
    * Create an instance of {@link TimesheetAuditType }
    *
    */
   public TimesheetAuditType createTimesheetAuditType()
   {
      return new TimesheetAuditType();
   }

   /**
    * Create an instance of {@link TimesheetDelegateType }
    *
    */
   public TimesheetDelegateType createTimesheetDelegateType()
   {
      return new TimesheetDelegateType();
   }

   /**
    * Create an instance of {@link TimesheetPeriodType }
    *
    */
   public TimesheetPeriodType createTimesheetPeriodType()
   {
      return new TimesheetPeriodType();
   }

   /**
    * Create an instance of {@link UDFValueType }
    *
    */
   public UDFValueType createUDFValueType()
   {
      return new UDFValueType();
   }

   /**
    * Create an instance of {@link UpdateBaselineOptionType }
    *
    */
   public UpdateBaselineOptionType createUpdateBaselineOptionType()
   {
      return new UpdateBaselineOptionType();
   }

   /**
    * Create an instance of {@link UserConsentType }
    *
    */
   public UserConsentType createUserConsentType()
   {
      return new UserConsentType();
   }

   /**
    * Create an instance of {@link UserDefinedValueUpdateType }
    *
    */
   public UserDefinedValueUpdateType createUserDefinedValueUpdateType()
   {
      return new UserDefinedValueUpdateType();
   }

   /**
    * Create an instance of {@link UserFieldTitleType }
    *
    */
   public UserFieldTitleType createUserFieldTitleType()
   {
      return new UserFieldTitleType();
   }

   /**
    * Create an instance of {@link UserInterfaceViewType }
    *
    */
   public UserInterfaceViewType createUserInterfaceViewType()
   {
      return new UserInterfaceViewType();
   }

   /**
    * Create an instance of {@link UserLicenseType }
    *
    */
   public UserLicenseType createUserLicenseType()
   {
      return new UserLicenseType();
   }

   /**
    * Create an instance of {@link UserOBSType }
    *
    */
   public UserOBSType createUserOBSType()
   {
      return new UserOBSType();
   }

   /**
    * Create an instance of {@link WBSType }
    *
    */
   public WBSType createWBSType()
   {
      return new WBSType();
   }

   /**
    * Create an instance of {@link WBSMilestoneType }
    *
    */
   public WBSMilestoneType createWBSMilestoneType()
   {
      return new WBSMilestoneType();
   }

   /**
    * Create an instance of {@link WbsReviewersType }
    *
    */
   public WbsReviewersType createWbsReviewersType()
   {
      return new WbsReviewersType();
   }

   /**
    * Create an instance of {@link CodeAssignmentType }
    *
    */
   public CodeAssignmentType createCodeAssignmentType()
   {
      return new CodeAssignmentType();
   }

   /**
    * Create an instance of {@link UDFAssignmentType }
    *
    */
   public UDFAssignmentType createUDFAssignmentType()
   {
      return new UDFAssignmentType();
   }

   /**
    * Create an instance of {@link GlobalPrivilegesType }
    *
    */
   public GlobalPrivilegesType createGlobalPrivilegesType()
   {
      return new GlobalPrivilegesType();
   }

   /**
    * Create an instance of {@link ProjectPrivilegesType }
    *
    */
   public ProjectPrivilegesType createProjectPrivilegesType()
   {
      return new ProjectPrivilegesType();
   }

   /**
    * Create an instance of {@link PortfolioTeamMemberType }
    *
    */
   public PortfolioTeamMemberType createPortfolioTeamMemberType()
   {
      return new PortfolioTeamMemberType();
   }

   /**
    * Create an instance of {@link ResourceCurveValuesType }
    *
    */
   public ResourceCurveValuesType createResourceCurveValuesType()
   {
      return new ResourceCurveValuesType();
   }

   /**
    * Create an instance of {@link WorkTimeType }
    *
    */
   public WorkTimeType createWorkTimeType()
   {
      return new WorkTimeType();
   }

   /**
    * Create an instance of {@link ProjectRoleSpreadType.Period }
    *
    */
   public ProjectRoleSpreadType.Period createProjectRoleSpreadTypePeriod()
   {
      return new ProjectRoleSpreadType.Period();
   }

   /**
    * Create an instance of {@link ProjectResourceSpreadType.Period }
    *
    */
   public ProjectResourceSpreadType.Period createProjectResourceSpreadTypePeriod()
   {
      return new ProjectResourceSpreadType.Period();
   }

   /**
    * Create an instance of {@link ActivitySpreadType.Period }
    *
    */
   public ActivitySpreadType.Period createActivitySpreadTypePeriod()
   {
      return new ActivitySpreadType.Period();
   }

   /**
    * Create an instance of {@link ResourceAssignmentSpreadType.Period }
    *
    */
   public ResourceAssignmentSpreadType.Period createResourceAssignmentSpreadTypePeriod()
   {
      return new ResourceAssignmentSpreadType.Period();
   }

   /**
    * Create an instance of {@link EPSProjectWBSSpreadType.Period }
    *
    */
   public EPSProjectWBSSpreadType.Period createEPSProjectWBSSpreadTypePeriod()
   {
      return new EPSProjectWBSSpreadType.Period();
   }

   /**
    * Create an instance of {@link ResourceRequestType.ResourceRequestCriterion }
    *
    */
   public ResourceRequestType.ResourceRequestCriterion createResourceRequestTypeResourceRequestCriterion()
   {
      return new ResourceRequestType.ResourceRequestCriterion();
   }

   /**
    * Create an instance of {@link UserType.ResourceRequests }
    *
    */
   public UserType.ResourceRequests createUserTypeResourceRequests()
   {
      return new UserType.ResourceRequests();
   }

   /**
    * Create an instance of {@link CalendarType.HolidayOrExceptions.HolidayOrException }
    *
    */
   public CalendarType.HolidayOrExceptions.HolidayOrException createCalendarTypeHolidayOrExceptionsHolidayOrException()
   {
      return new CalendarType.HolidayOrExceptions.HolidayOrException();
   }

   /**
    * Create an instance of {@link CalendarType.StandardWorkWeek.StandardWorkHours }
    *
    */
   public CalendarType.StandardWorkWeek.StandardWorkHours createCalendarTypeStandardWorkWeekStandardWorkHours()
   {
      return new CalendarType.StandardWorkWeek.StandardWorkHours();
   }

   /**
    * Create an instance of {@link ProjectListType.Project.BaselineProject }
    *
    */
   public ProjectListType.Project.BaselineProject createProjectListTypeProjectBaselineProject()
   {
      return new ProjectListType.Project.BaselineProject();
   }

}
