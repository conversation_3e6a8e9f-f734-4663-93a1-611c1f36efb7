//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.2
// See https://eclipse-ee4j.github.io/jaxb-ri
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2025.01.02 at 04:27:10 PM GMT
//

package org.mpxj.primavera.schema;

import java.time.LocalDateTime;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

/**
 * <p>Java class for ActivityCommentType complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="ActivityCommentType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="ActivityObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="AssignmentObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="CommentDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="CommentText" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="4000"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="CreateDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="CreateUser" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="DeleteDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="DeleteUser" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="IsRejected" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="LastUpdateDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="LastUpdateUser" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;maxLength value="255"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="ObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="PersonalName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="ProjectObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="ReadFlag" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
 *         &lt;element name="RejectionComments" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="TimeDiff" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="UserObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "ActivityCommentType", propOrder =
{
   "activityObjectId",
   "assignmentObjectId",
   "commentDate",
   "commentText",
   "createDate",
   "createUser",
   "deleteDate",
   "deleteUser",
   "isRejected",
   "lastUpdateDate",
   "lastUpdateUser",
   "objectId",
   "personalName",
   "projectObjectId",
   "readFlag",
   "rejectionComments",
   "timeDiff",
   "userObjectId"
}) public class ActivityCommentType
{

   @XmlElement(name = "ActivityObjectId") protected Integer activityObjectId;
   @XmlElement(name = "AssignmentObjectId") protected Integer assignmentObjectId;
   @XmlElement(name = "CommentDate", type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime commentDate;
   @XmlElement(name = "CommentText") @XmlJavaTypeAdapter(Adapter1.class) protected String commentText;
   @XmlElement(name = "CreateDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime createDate;
   @XmlElement(name = "CreateUser") @XmlJavaTypeAdapter(Adapter1.class) protected String createUser;
   @XmlElement(name = "DeleteDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime deleteDate;
   @XmlElement(name = "DeleteUser") @XmlJavaTypeAdapter(Adapter1.class) protected String deleteUser;
   @XmlElement(name = "IsRejected", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean isRejected;
   @XmlElement(name = "LastUpdateDate", type = String.class, nillable = true) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime lastUpdateDate;
   @XmlElement(name = "LastUpdateUser") @XmlJavaTypeAdapter(Adapter1.class) protected String lastUpdateUser;
   @XmlElement(name = "ObjectId") protected Integer objectId;
   @XmlElement(name = "PersonalName") @XmlJavaTypeAdapter(Adapter1.class) protected String personalName;
   @XmlElement(name = "ProjectObjectId") protected Integer projectObjectId;
   @XmlElement(name = "ReadFlag", type = String.class) @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "boolean") protected Boolean readFlag;
   @XmlElement(name = "RejectionComments") @XmlJavaTypeAdapter(Adapter1.class) protected String rejectionComments;
   @XmlElement(name = "TimeDiff") @XmlJavaTypeAdapter(Adapter1.class) protected String timeDiff;
   @XmlElement(name = "UserObjectId") protected Integer userObjectId;

   /**
    * Gets the value of the activityObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getActivityObjectId()
   {
      return activityObjectId;
   }

   /**
    * Sets the value of the activityObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setActivityObjectId(Integer value)
   {
      this.activityObjectId = value;
   }

   /**
    * Gets the value of the assignmentObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getAssignmentObjectId()
   {
      return assignmentObjectId;
   }

   /**
    * Sets the value of the assignmentObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setAssignmentObjectId(Integer value)
   {
      this.assignmentObjectId = value;
   }

   /**
    * Gets the value of the commentDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getCommentDate()
   {
      return commentDate;
   }

   /**
    * Sets the value of the commentDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCommentDate(LocalDateTime value)
   {
      this.commentDate = value;
   }

   /**
    * Gets the value of the commentText property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getCommentText()
   {
      return commentText;
   }

   /**
    * Sets the value of the commentText property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCommentText(String value)
   {
      this.commentText = value;
   }

   /**
    * Gets the value of the createDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getCreateDate()
   {
      return createDate;
   }

   /**
    * Sets the value of the createDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCreateDate(LocalDateTime value)
   {
      this.createDate = value;
   }

   /**
    * Gets the value of the createUser property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getCreateUser()
   {
      return createUser;
   }

   /**
    * Sets the value of the createUser property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCreateUser(String value)
   {
      this.createUser = value;
   }

   /**
    * Gets the value of the deleteDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getDeleteDate()
   {
      return deleteDate;
   }

   /**
    * Sets the value of the deleteDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDeleteDate(LocalDateTime value)
   {
      this.deleteDate = value;
   }

   /**
    * Gets the value of the deleteUser property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getDeleteUser()
   {
      return deleteUser;
   }

   /**
    * Sets the value of the deleteUser property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDeleteUser(String value)
   {
      this.deleteUser = value;
   }

   /**
    * Gets the value of the isRejected property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isIsRejected()
   {
      return isRejected;
   }

   /**
    * Sets the value of the isRejected property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setIsRejected(Boolean value)
   {
      this.isRejected = value;
   }

   /**
    * Gets the value of the lastUpdateDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getLastUpdateDate()
   {
      return lastUpdateDate;
   }

   /**
    * Sets the value of the lastUpdateDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLastUpdateDate(LocalDateTime value)
   {
      this.lastUpdateDate = value;
   }

   /**
    * Gets the value of the lastUpdateUser property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getLastUpdateUser()
   {
      return lastUpdateUser;
   }

   /**
    * Sets the value of the lastUpdateUser property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setLastUpdateUser(String value)
   {
      this.lastUpdateUser = value;
   }

   /**
    * Gets the value of the objectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getObjectId()
   {
      return objectId;
   }

   /**
    * Sets the value of the objectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setObjectId(Integer value)
   {
      this.objectId = value;
   }

   /**
    * Gets the value of the personalName property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getPersonalName()
   {
      return personalName;
   }

   /**
    * Sets the value of the personalName property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPersonalName(String value)
   {
      this.personalName = value;
   }

   /**
    * Gets the value of the projectObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getProjectObjectId()
   {
      return projectObjectId;
   }

   /**
    * Sets the value of the projectObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setProjectObjectId(Integer value)
   {
      this.projectObjectId = value;
   }

   /**
    * Gets the value of the readFlag property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Boolean isReadFlag()
   {
      return readFlag;
   }

   /**
    * Sets the value of the readFlag property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setReadFlag(Boolean value)
   {
      this.readFlag = value;
   }

   /**
    * Gets the value of the rejectionComments property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getRejectionComments()
   {
      return rejectionComments;
   }

   /**
    * Sets the value of the rejectionComments property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setRejectionComments(String value)
   {
      this.rejectionComments = value;
   }

   /**
    * Gets the value of the timeDiff property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTimeDiff()
   {
      return timeDiff;
   }

   /**
    * Sets the value of the timeDiff property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTimeDiff(String value)
   {
      this.timeDiff = value;
   }

   /**
    * Gets the value of the userObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getUserObjectId()
   {
      return userObjectId;
   }

   /**
    * Sets the value of the userObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setUserObjectId(Integer value)
   {
      this.userObjectId = value;
   }

}
