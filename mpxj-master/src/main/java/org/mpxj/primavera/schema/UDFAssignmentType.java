//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.2
// See https://eclipse-ee4j.github.io/jaxb-ri
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2025.01.02 at 04:27:10 PM GMT
//

package org.mpxj.primavera.schema;

import java.time.LocalDateTime;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

/**
 * <p>Java class for UDFAssignmentType complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="UDFAssignmentType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="TypeObjectId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="CodeObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="CostValue" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="IntegerValue" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
 *         &lt;element name="IndicatorValue" minOccurs="0"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="None"/&gt;
 *               &lt;enumeration value="Red"/&gt;
 *               &lt;enumeration value="Yellow"/&gt;
 *               &lt;enumeration value="Green"/&gt;
 *               &lt;enumeration value="Blue"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="DoubleValue" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *         &lt;element name="TextValue" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="StartDateValue" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *         &lt;element name="FinishDateValue" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "UDFAssignmentType", propOrder =
{
   "typeObjectId",
   "codeObjectId",
   "costValue",
   "integerValue",
   "indicatorValue",
   "doubleValue",
   "textValue",
   "startDateValue",
   "finishDateValue"
}) public class UDFAssignmentType
{

   @XmlElement(name = "TypeObjectId") protected int typeObjectId;
   @XmlElement(name = "CodeObjectId") protected Integer codeObjectId;
   @XmlElement(name = "CostValue", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double costValue;
   @XmlElement(name = "IntegerValue") protected Integer integerValue;
   @XmlElement(name = "IndicatorValue") @XmlJavaTypeAdapter(Adapter1.class) protected String indicatorValue;
   @XmlElement(name = "DoubleValue", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double doubleValue;
   @XmlElement(name = "TextValue") @XmlJavaTypeAdapter(Adapter1.class) protected String textValue;
   @XmlElement(name = "StartDateValue", type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime startDateValue;
   @XmlElement(name = "FinishDateValue", type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime finishDateValue;

   /**
    * Gets the value of the typeObjectId property.
    *
    */
   public int getTypeObjectId()
   {
      return typeObjectId;
   }

   /**
    * Sets the value of the typeObjectId property.
    *
    */
   public void setTypeObjectId(int value)
   {
      this.typeObjectId = value;
   }

   /**
    * Gets the value of the codeObjectId property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getCodeObjectId()
   {
      return codeObjectId;
   }

   /**
    * Sets the value of the codeObjectId property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setCodeObjectId(Integer value)
   {
      this.codeObjectId = value;
   }

   /**
    * Gets the value of the costValue property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getCostValue()
   {
      return costValue;
   }

   /**
    * Sets the value of the costValue property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setCostValue(Double value)
   {
      this.costValue = value;
   }

   /**
    * Gets the value of the integerValue property.
    *
    * @return
    *     possible object is
    *     {@link Integer }
    *
    */
   public Integer getIntegerValue()
   {
      return integerValue;
   }

   /**
    * Sets the value of the integerValue property.
    *
    * @param value
    *     allowed object is
    *     {@link Integer }
    *
    */
   public void setIntegerValue(Integer value)
   {
      this.integerValue = value;
   }

   /**
    * Gets the value of the indicatorValue property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getIndicatorValue()
   {
      return indicatorValue;
   }

   /**
    * Sets the value of the indicatorValue property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setIndicatorValue(String value)
   {
      this.indicatorValue = value;
   }

   /**
    * Gets the value of the doubleValue property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public Double getDoubleValue()
   {
      return doubleValue;
   }

   /**
    * Sets the value of the doubleValue property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDoubleValue(Double value)
   {
      this.doubleValue = value;
   }

   /**
    * Gets the value of the textValue property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getTextValue()
   {
      return textValue;
   }

   /**
    * Sets the value of the textValue property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setTextValue(String value)
   {
      this.textValue = value;
   }

   /**
    * Gets the value of the startDateValue property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getStartDateValue()
   {
      return startDateValue;
   }

   /**
    * Sets the value of the startDateValue property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setStartDateValue(LocalDateTime value)
   {
      this.startDateValue = value;
   }

   /**
    * Gets the value of the finishDateValue property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getFinishDateValue()
   {
      return finishDateValue;
   }

   /**
    * Sets the value of the finishDateValue property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setFinishDateValue(LocalDateTime value)
   {
      this.finishDateValue = value;
   }

}
