//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.2
// See https://eclipse-ee4j.github.io/jaxb-ri
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2025.01.02 at 04:27:10 PM GMT
//

package org.mpxj.primavera.schema;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

/**
 * <p>Java class for ResourceAssignmentSpreadType complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="ResourceAssignmentSpreadType"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="StartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *         &lt;element name="EndDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *         &lt;element name="PeriodType"&gt;
 *           &lt;simpleType&gt;
 *             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *               &lt;enumeration value="Hour"/&gt;
 *               &lt;enumeration value="Day"/&gt;
 *               &lt;enumeration value="Week"/&gt;
 *               &lt;enumeration value="Month"/&gt;
 *               &lt;enumeration value="Quarter"/&gt;
 *               &lt;enumeration value="Year"/&gt;
 *               &lt;enumeration value="Financial Period"/&gt;
 *             &lt;/restriction&gt;
 *           &lt;/simpleType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Period" maxOccurs="unbounded" minOccurs="0"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="StartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *                   &lt;element name="EndDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *                   &lt;element name="ActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="ActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="ActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="AtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="StaffedRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeStaffedRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="StaffedRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeStaffedRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="UnstaffedRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeUnstaffedRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="UnstaffedRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeUnstaffedRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="ActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="ActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="ActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="AtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="PlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativePlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="RemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="StaffedRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeStaffedRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="StaffedRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeStaffedRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="UnstaffedRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeUnstaffedRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="UnstaffedRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                   &lt;element name="CumulativeUnstaffedRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "ResourceAssignmentSpreadType", propOrder =
{
   "startDate",
   "endDate",
   "periodType",
   "period"
}) public class ResourceAssignmentSpreadType
{

   @XmlElement(name = "StartDate", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime startDate;
   @XmlElement(name = "EndDate", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime endDate;
   @XmlElement(name = "PeriodType", required = true) @XmlJavaTypeAdapter(Adapter1.class) protected String periodType;
   @XmlElement(name = "Period") protected List<ResourceAssignmentSpreadType.Period> period;

   /**
    * Gets the value of the startDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getStartDate()
   {
      return startDate;
   }

   /**
    * Sets the value of the startDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setStartDate(LocalDateTime value)
   {
      this.startDate = value;
   }

   /**
    * Gets the value of the endDate property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public LocalDateTime getEndDate()
   {
      return endDate;
   }

   /**
    * Sets the value of the endDate property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setEndDate(LocalDateTime value)
   {
      this.endDate = value;
   }

   /**
    * Gets the value of the periodType property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getPeriodType()
   {
      return periodType;
   }

   /**
    * Sets the value of the periodType property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setPeriodType(String value)
   {
      this.periodType = value;
   }

   /**
    * Gets the value of the period property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the period property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getPeriod().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link ResourceAssignmentSpreadType.Period }
    *
    *
    */
   public List<ResourceAssignmentSpreadType.Period> getPeriod()
   {
      if (period == null)
      {
         period = new ArrayList<>();
      }
      return this.period;
   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element name="StartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
    *         &lt;element name="EndDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
    *         &lt;element name="ActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="ActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="ActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="AtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="StaffedRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeStaffedRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="StaffedRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeStaffedRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="UnstaffedRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeUnstaffedRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="UnstaffedRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeUnstaffedRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="ActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="ActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="ActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="AtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="PlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativePlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="RemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="StaffedRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeStaffedRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="StaffedRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeStaffedRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="UnstaffedRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeUnstaffedRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="UnstaffedRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *         &lt;element name="CumulativeUnstaffedRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
    *       &lt;/sequence&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "startDate",
      "endDate",
      "actualOvertimeUnits",
      "cumulativeActualOvertimeUnits",
      "actualRegularUnits",
      "cumulativeActualRegularUnits",
      "actualUnits",
      "cumulativeActualUnits",
      "atCompletionUnits",
      "cumulativeAtCompletionUnits",
      "plannedUnits",
      "cumulativePlannedUnits",
      "remainingLateUnits",
      "cumulativeRemainingLateUnits",
      "remainingUnits",
      "cumulativeRemainingUnits",
      "staffedRemainingLateUnits",
      "cumulativeStaffedRemainingLateUnits",
      "staffedRemainingUnits",
      "cumulativeStaffedRemainingUnits",
      "unstaffedRemainingLateUnits",
      "cumulativeUnstaffedRemainingLateUnits",
      "unstaffedRemainingUnits",
      "cumulativeUnstaffedRemainingUnits",
      "actualCost",
      "cumulativeActualCost",
      "actualOvertimeCost",
      "cumulativeActualOvertimeCost",
      "actualRegularCost",
      "cumulativeActualRegularCost",
      "atCompletionCost",
      "cumulativeAtCompletionCost",
      "plannedCost",
      "cumulativePlannedCost",
      "remainingCost",
      "cumulativeRemainingCost",
      "remainingLateCost",
      "cumulativeRemainingLateCost",
      "staffedRemainingCost",
      "cumulativeStaffedRemainingCost",
      "staffedRemainingLateCost",
      "cumulativeStaffedRemainingLateCost",
      "unstaffedRemainingCost",
      "cumulativeUnstaffedRemainingCost",
      "unstaffedRemainingLateCost",
      "cumulativeUnstaffedRemainingLateCost"
   }) public static class Period
   {

      @XmlElement(name = "StartDate", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime startDate;
      @XmlElement(name = "EndDate", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime endDate;
      @XmlElement(name = "ActualOvertimeUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualOvertimeUnits;
      @XmlElement(name = "CumulativeActualOvertimeUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualOvertimeUnits;
      @XmlElement(name = "ActualRegularUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualRegularUnits;
      @XmlElement(name = "CumulativeActualRegularUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualRegularUnits;
      @XmlElement(name = "ActualUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualUnits;
      @XmlElement(name = "CumulativeActualUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualUnits;
      @XmlElement(name = "AtCompletionUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionUnits;
      @XmlElement(name = "CumulativeAtCompletionUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeAtCompletionUnits;
      @XmlElement(name = "PlannedUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedUnits;
      @XmlElement(name = "CumulativePlannedUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePlannedUnits;
      @XmlElement(name = "RemainingLateUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingLateUnits;
      @XmlElement(name = "CumulativeRemainingLateUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingLateUnits;
      @XmlElement(name = "RemainingUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingUnits;
      @XmlElement(name = "CumulativeRemainingUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingUnits;
      @XmlElement(name = "StaffedRemainingLateUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double staffedRemainingLateUnits;
      @XmlElement(name = "CumulativeStaffedRemainingLateUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeStaffedRemainingLateUnits;
      @XmlElement(name = "StaffedRemainingUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double staffedRemainingUnits;
      @XmlElement(name = "CumulativeStaffedRemainingUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeStaffedRemainingUnits;
      @XmlElement(name = "UnstaffedRemainingLateUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double unstaffedRemainingLateUnits;
      @XmlElement(name = "CumulativeUnstaffedRemainingLateUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeUnstaffedRemainingLateUnits;
      @XmlElement(name = "UnstaffedRemainingUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double unstaffedRemainingUnits;
      @XmlElement(name = "CumulativeUnstaffedRemainingUnits", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeUnstaffedRemainingUnits;
      @XmlElement(name = "ActualCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualCost;
      @XmlElement(name = "CumulativeActualCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualCost;
      @XmlElement(name = "ActualOvertimeCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualOvertimeCost;
      @XmlElement(name = "CumulativeActualOvertimeCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualOvertimeCost;
      @XmlElement(name = "ActualRegularCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double actualRegularCost;
      @XmlElement(name = "CumulativeActualRegularCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeActualRegularCost;
      @XmlElement(name = "AtCompletionCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double atCompletionCost;
      @XmlElement(name = "CumulativeAtCompletionCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeAtCompletionCost;
      @XmlElement(name = "PlannedCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double plannedCost;
      @XmlElement(name = "CumulativePlannedCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativePlannedCost;
      @XmlElement(name = "RemainingCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingCost;
      @XmlElement(name = "CumulativeRemainingCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingCost;
      @XmlElement(name = "RemainingLateCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double remainingLateCost;
      @XmlElement(name = "CumulativeRemainingLateCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeRemainingLateCost;
      @XmlElement(name = "StaffedRemainingCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double staffedRemainingCost;
      @XmlElement(name = "CumulativeStaffedRemainingCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeStaffedRemainingCost;
      @XmlElement(name = "StaffedRemainingLateCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double staffedRemainingLateCost;
      @XmlElement(name = "CumulativeStaffedRemainingLateCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeStaffedRemainingLateCost;
      @XmlElement(name = "UnstaffedRemainingCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double unstaffedRemainingCost;
      @XmlElement(name = "CumulativeUnstaffedRemainingCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeUnstaffedRemainingCost;
      @XmlElement(name = "UnstaffedRemainingLateCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double unstaffedRemainingLateCost;
      @XmlElement(name = "CumulativeUnstaffedRemainingLateCost", type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "double") protected Double cumulativeUnstaffedRemainingLateCost;

      /**
       * Gets the value of the startDate property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public LocalDateTime getStartDate()
      {
         return startDate;
      }

      /**
       * Sets the value of the startDate property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setStartDate(LocalDateTime value)
      {
         this.startDate = value;
      }

      /**
       * Gets the value of the endDate property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public LocalDateTime getEndDate()
      {
         return endDate;
      }

      /**
       * Sets the value of the endDate property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setEndDate(LocalDateTime value)
      {
         this.endDate = value;
      }

      /**
       * Gets the value of the actualOvertimeUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualOvertimeUnits()
      {
         return actualOvertimeUnits;
      }

      /**
       * Sets the value of the actualOvertimeUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualOvertimeUnits(Double value)
      {
         this.actualOvertimeUnits = value;
      }

      /**
       * Gets the value of the cumulativeActualOvertimeUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualOvertimeUnits()
      {
         return cumulativeActualOvertimeUnits;
      }

      /**
       * Sets the value of the cumulativeActualOvertimeUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualOvertimeUnits(Double value)
      {
         this.cumulativeActualOvertimeUnits = value;
      }

      /**
       * Gets the value of the actualRegularUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualRegularUnits()
      {
         return actualRegularUnits;
      }

      /**
       * Sets the value of the actualRegularUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualRegularUnits(Double value)
      {
         this.actualRegularUnits = value;
      }

      /**
       * Gets the value of the cumulativeActualRegularUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualRegularUnits()
      {
         return cumulativeActualRegularUnits;
      }

      /**
       * Sets the value of the cumulativeActualRegularUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualRegularUnits(Double value)
      {
         this.cumulativeActualRegularUnits = value;
      }

      /**
       * Gets the value of the actualUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualUnits()
      {
         return actualUnits;
      }

      /**
       * Sets the value of the actualUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualUnits(Double value)
      {
         this.actualUnits = value;
      }

      /**
       * Gets the value of the cumulativeActualUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualUnits()
      {
         return cumulativeActualUnits;
      }

      /**
       * Sets the value of the cumulativeActualUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualUnits(Double value)
      {
         this.cumulativeActualUnits = value;
      }

      /**
       * Gets the value of the atCompletionUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getAtCompletionUnits()
      {
         return atCompletionUnits;
      }

      /**
       * Sets the value of the atCompletionUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setAtCompletionUnits(Double value)
      {
         this.atCompletionUnits = value;
      }

      /**
       * Gets the value of the cumulativeAtCompletionUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeAtCompletionUnits()
      {
         return cumulativeAtCompletionUnits;
      }

      /**
       * Sets the value of the cumulativeAtCompletionUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeAtCompletionUnits(Double value)
      {
         this.cumulativeAtCompletionUnits = value;
      }

      /**
       * Gets the value of the plannedUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPlannedUnits()
      {
         return plannedUnits;
      }

      /**
       * Sets the value of the plannedUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPlannedUnits(Double value)
      {
         this.plannedUnits = value;
      }

      /**
       * Gets the value of the cumulativePlannedUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePlannedUnits()
      {
         return cumulativePlannedUnits;
      }

      /**
       * Sets the value of the cumulativePlannedUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePlannedUnits(Double value)
      {
         this.cumulativePlannedUnits = value;
      }

      /**
       * Gets the value of the remainingLateUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingLateUnits()
      {
         return remainingLateUnits;
      }

      /**
       * Sets the value of the remainingLateUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingLateUnits(Double value)
      {
         this.remainingLateUnits = value;
      }

      /**
       * Gets the value of the cumulativeRemainingLateUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingLateUnits()
      {
         return cumulativeRemainingLateUnits;
      }

      /**
       * Sets the value of the cumulativeRemainingLateUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingLateUnits(Double value)
      {
         this.cumulativeRemainingLateUnits = value;
      }

      /**
       * Gets the value of the remainingUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingUnits()
      {
         return remainingUnits;
      }

      /**
       * Sets the value of the remainingUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingUnits(Double value)
      {
         this.remainingUnits = value;
      }

      /**
       * Gets the value of the cumulativeRemainingUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingUnits()
      {
         return cumulativeRemainingUnits;
      }

      /**
       * Sets the value of the cumulativeRemainingUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingUnits(Double value)
      {
         this.cumulativeRemainingUnits = value;
      }

      /**
       * Gets the value of the staffedRemainingLateUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getStaffedRemainingLateUnits()
      {
         return staffedRemainingLateUnits;
      }

      /**
       * Sets the value of the staffedRemainingLateUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setStaffedRemainingLateUnits(Double value)
      {
         this.staffedRemainingLateUnits = value;
      }

      /**
       * Gets the value of the cumulativeStaffedRemainingLateUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeStaffedRemainingLateUnits()
      {
         return cumulativeStaffedRemainingLateUnits;
      }

      /**
       * Sets the value of the cumulativeStaffedRemainingLateUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeStaffedRemainingLateUnits(Double value)
      {
         this.cumulativeStaffedRemainingLateUnits = value;
      }

      /**
       * Gets the value of the staffedRemainingUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getStaffedRemainingUnits()
      {
         return staffedRemainingUnits;
      }

      /**
       * Sets the value of the staffedRemainingUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setStaffedRemainingUnits(Double value)
      {
         this.staffedRemainingUnits = value;
      }

      /**
       * Gets the value of the cumulativeStaffedRemainingUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeStaffedRemainingUnits()
      {
         return cumulativeStaffedRemainingUnits;
      }

      /**
       * Sets the value of the cumulativeStaffedRemainingUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeStaffedRemainingUnits(Double value)
      {
         this.cumulativeStaffedRemainingUnits = value;
      }

      /**
       * Gets the value of the unstaffedRemainingLateUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getUnstaffedRemainingLateUnits()
      {
         return unstaffedRemainingLateUnits;
      }

      /**
       * Sets the value of the unstaffedRemainingLateUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setUnstaffedRemainingLateUnits(Double value)
      {
         this.unstaffedRemainingLateUnits = value;
      }

      /**
       * Gets the value of the cumulativeUnstaffedRemainingLateUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeUnstaffedRemainingLateUnits()
      {
         return cumulativeUnstaffedRemainingLateUnits;
      }

      /**
       * Sets the value of the cumulativeUnstaffedRemainingLateUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeUnstaffedRemainingLateUnits(Double value)
      {
         this.cumulativeUnstaffedRemainingLateUnits = value;
      }

      /**
       * Gets the value of the unstaffedRemainingUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getUnstaffedRemainingUnits()
      {
         return unstaffedRemainingUnits;
      }

      /**
       * Sets the value of the unstaffedRemainingUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setUnstaffedRemainingUnits(Double value)
      {
         this.unstaffedRemainingUnits = value;
      }

      /**
       * Gets the value of the cumulativeUnstaffedRemainingUnits property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeUnstaffedRemainingUnits()
      {
         return cumulativeUnstaffedRemainingUnits;
      }

      /**
       * Sets the value of the cumulativeUnstaffedRemainingUnits property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeUnstaffedRemainingUnits(Double value)
      {
         this.cumulativeUnstaffedRemainingUnits = value;
      }

      /**
       * Gets the value of the actualCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualCost()
      {
         return actualCost;
      }

      /**
       * Sets the value of the actualCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualCost(Double value)
      {
         this.actualCost = value;
      }

      /**
       * Gets the value of the cumulativeActualCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualCost()
      {
         return cumulativeActualCost;
      }

      /**
       * Sets the value of the cumulativeActualCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualCost(Double value)
      {
         this.cumulativeActualCost = value;
      }

      /**
       * Gets the value of the actualOvertimeCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualOvertimeCost()
      {
         return actualOvertimeCost;
      }

      /**
       * Sets the value of the actualOvertimeCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualOvertimeCost(Double value)
      {
         this.actualOvertimeCost = value;
      }

      /**
       * Gets the value of the cumulativeActualOvertimeCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualOvertimeCost()
      {
         return cumulativeActualOvertimeCost;
      }

      /**
       * Sets the value of the cumulativeActualOvertimeCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualOvertimeCost(Double value)
      {
         this.cumulativeActualOvertimeCost = value;
      }

      /**
       * Gets the value of the actualRegularCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getActualRegularCost()
      {
         return actualRegularCost;
      }

      /**
       * Sets the value of the actualRegularCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setActualRegularCost(Double value)
      {
         this.actualRegularCost = value;
      }

      /**
       * Gets the value of the cumulativeActualRegularCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeActualRegularCost()
      {
         return cumulativeActualRegularCost;
      }

      /**
       * Sets the value of the cumulativeActualRegularCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeActualRegularCost(Double value)
      {
         this.cumulativeActualRegularCost = value;
      }

      /**
       * Gets the value of the atCompletionCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getAtCompletionCost()
      {
         return atCompletionCost;
      }

      /**
       * Sets the value of the atCompletionCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setAtCompletionCost(Double value)
      {
         this.atCompletionCost = value;
      }

      /**
       * Gets the value of the cumulativeAtCompletionCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeAtCompletionCost()
      {
         return cumulativeAtCompletionCost;
      }

      /**
       * Sets the value of the cumulativeAtCompletionCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeAtCompletionCost(Double value)
      {
         this.cumulativeAtCompletionCost = value;
      }

      /**
       * Gets the value of the plannedCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getPlannedCost()
      {
         return plannedCost;
      }

      /**
       * Sets the value of the plannedCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setPlannedCost(Double value)
      {
         this.plannedCost = value;
      }

      /**
       * Gets the value of the cumulativePlannedCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativePlannedCost()
      {
         return cumulativePlannedCost;
      }

      /**
       * Sets the value of the cumulativePlannedCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativePlannedCost(Double value)
      {
         this.cumulativePlannedCost = value;
      }

      /**
       * Gets the value of the remainingCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingCost()
      {
         return remainingCost;
      }

      /**
       * Sets the value of the remainingCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingCost(Double value)
      {
         this.remainingCost = value;
      }

      /**
       * Gets the value of the cumulativeRemainingCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingCost()
      {
         return cumulativeRemainingCost;
      }

      /**
       * Sets the value of the cumulativeRemainingCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingCost(Double value)
      {
         this.cumulativeRemainingCost = value;
      }

      /**
       * Gets the value of the remainingLateCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getRemainingLateCost()
      {
         return remainingLateCost;
      }

      /**
       * Sets the value of the remainingLateCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setRemainingLateCost(Double value)
      {
         this.remainingLateCost = value;
      }

      /**
       * Gets the value of the cumulativeRemainingLateCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeRemainingLateCost()
      {
         return cumulativeRemainingLateCost;
      }

      /**
       * Sets the value of the cumulativeRemainingLateCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeRemainingLateCost(Double value)
      {
         this.cumulativeRemainingLateCost = value;
      }

      /**
       * Gets the value of the staffedRemainingCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getStaffedRemainingCost()
      {
         return staffedRemainingCost;
      }

      /**
       * Sets the value of the staffedRemainingCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setStaffedRemainingCost(Double value)
      {
         this.staffedRemainingCost = value;
      }

      /**
       * Gets the value of the cumulativeStaffedRemainingCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeStaffedRemainingCost()
      {
         return cumulativeStaffedRemainingCost;
      }

      /**
       * Sets the value of the cumulativeStaffedRemainingCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeStaffedRemainingCost(Double value)
      {
         this.cumulativeStaffedRemainingCost = value;
      }

      /**
       * Gets the value of the staffedRemainingLateCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getStaffedRemainingLateCost()
      {
         return staffedRemainingLateCost;
      }

      /**
       * Sets the value of the staffedRemainingLateCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setStaffedRemainingLateCost(Double value)
      {
         this.staffedRemainingLateCost = value;
      }

      /**
       * Gets the value of the cumulativeStaffedRemainingLateCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeStaffedRemainingLateCost()
      {
         return cumulativeStaffedRemainingLateCost;
      }

      /**
       * Sets the value of the cumulativeStaffedRemainingLateCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeStaffedRemainingLateCost(Double value)
      {
         this.cumulativeStaffedRemainingLateCost = value;
      }

      /**
       * Gets the value of the unstaffedRemainingCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getUnstaffedRemainingCost()
      {
         return unstaffedRemainingCost;
      }

      /**
       * Sets the value of the unstaffedRemainingCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setUnstaffedRemainingCost(Double value)
      {
         this.unstaffedRemainingCost = value;
      }

      /**
       * Gets the value of the cumulativeUnstaffedRemainingCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeUnstaffedRemainingCost()
      {
         return cumulativeUnstaffedRemainingCost;
      }

      /**
       * Sets the value of the cumulativeUnstaffedRemainingCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeUnstaffedRemainingCost(Double value)
      {
         this.cumulativeUnstaffedRemainingCost = value;
      }

      /**
       * Gets the value of the unstaffedRemainingLateCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getUnstaffedRemainingLateCost()
      {
         return unstaffedRemainingLateCost;
      }

      /**
       * Sets the value of the unstaffedRemainingLateCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setUnstaffedRemainingLateCost(Double value)
      {
         this.unstaffedRemainingLateCost = value;
      }

      /**
       * Gets the value of the cumulativeUnstaffedRemainingLateCost property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getCumulativeUnstaffedRemainingLateCost()
      {
         return cumulativeUnstaffedRemainingLateCost;
      }

      /**
       * Sets the value of the cumulativeUnstaffedRemainingLateCost property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setCumulativeUnstaffedRemainingLateCost(Double value)
      {
         this.cumulativeUnstaffedRemainingLateCost = value;
      }

   }

}
