//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.2
// See https://eclipse-ee4j.github.io/jaxb-ri
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2024.04.25 at 10:03:49 AM BST
//

package org.mpxj.conceptdraw.schema;

import jakarta.xml.bind.annotation.adapters.XmlAdapter;
import org.mpxj.conceptdraw.DatatypeConverter;

public class Adapter1 extends XmlAdapter<String, Double>
{

   @Override public Double unmarshal(String value)
   {
      return (DatatypeConverter.parseDouble(value));
   }

   @Override public String marshal(Double value)
   {
      return (DatatypeConverter.printDouble(value));
   }

}
