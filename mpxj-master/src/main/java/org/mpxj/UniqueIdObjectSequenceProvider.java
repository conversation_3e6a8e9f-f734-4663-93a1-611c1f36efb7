/*
 * file:       UniqueIdObjectSequenceProvider.java
 * author:     <PERSON>
 * date:       2024-08-16
 */

/*
 * This library is free software; you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as published by the
 * Free Software Foundation; either version 2.1 of the License, or (at your
 * option) any later version.
 *
 * This library is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 * or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public
 * License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with this library; if not, write to the Free Software Foundation, Inc.,
 * 59 Temple Place, Suite 330, Boston, MA 02111-1307, USA.
 */

package org.mpxj;

import org.mpxj.common.ObjectSequence;

/**
 * Classes implementing this interface provide a method which allows
 * unique ID object sequences to be retrieved for the requested class.
 */
public interface UniqueIdObjectSequenceProvider
{
   /**
    * Retrieve an `ObjectSequence` for the requested class.
    *
    * @param c class
    * @return ObjectSequence instance
    */
   ObjectSequence getUniqueIdObjectSequence(Class<?> c);
}
