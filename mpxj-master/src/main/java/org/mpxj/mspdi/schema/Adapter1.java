//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.2
// See https://eclipse-ee4j.github.io/jaxb-ri
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2024.11.28 at 04:58:44 PM GMT
//

package org.mpxj.mspdi.schema;

import jakarta.xml.bind.annotation.adapters.XmlAdapter;
import org.mpxj.mspdi.DatatypeConverter;

public class Adapter1
         extends
            XmlAdapter<String, Boolean>
{

   @Override public Boolean unmarshal(String value)
   {
      return (DatatypeConverter.parseBoolean(value));
   }

   @Override public String marshal(Boolean value)
   {
      return (DatatypeConverter.printBoolean(value));
   }

}
