//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.2
// See https://eclipse-ee4j.github.io/jaxb-ri
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2024.11.28 at 04:58:44 PM GMT
//

package org.mpxj.mspdi.schema;

import jakarta.xml.bind.annotation.adapters.XmlAdapter;
import org.mpxj.mspdi.DatatypeConverter;

public class Adapter22
         extends
            XmlAdapter<String, Integer>
{

   @Override public Integer unmarshal(String value)
   {
      return (DatatypeConverter.parseTaskUID(value));
   }

   @Override public String marshal(Integer value)
   {
      return (DatatypeConverter.printTaskUID(value));
   }

}
