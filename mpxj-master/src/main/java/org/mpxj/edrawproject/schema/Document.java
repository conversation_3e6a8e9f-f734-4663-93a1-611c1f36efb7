//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.2
// See https://eclipse-ee4j.github.io/jaxb-ri
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2025.05.06 at 12:54:08 PM BST
//

package org.mpxj.edrawproject.schema;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import javax.xml.datatype.XMLGregorianCalendar;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;
import jakarta.xml.bind.annotation.XmlSchemaType;
import jakarta.xml.bind.annotation.XmlType;
import jakarta.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

/**
 * <p>Java class for anonymous complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="CreatedVersion"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="CreationDate"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Creator"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="LastSaved"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Modifier"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="DPi"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="ScreenWidth"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="ScreenHeight"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="CalendarUID"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="MinutesPerDay"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="MinutesPerWeek"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="DaysPerMonth"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="DateFormat"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="GanttViewSplitterRate"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="m_isShowSpecificTime"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="GanttOption"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="MajorUnit"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                   &lt;element name="MinorUnit"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                   &lt;element name="ProjectUnit"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                   &lt;element name="BaselineCost"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                   &lt;element name="StartDate"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                   &lt;element name="FinishDate"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                   &lt;element name="Auto"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                   &lt;element name="ThemeIndex"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="RowColumn"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="ColumnList"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;sequence&gt;
 *                             &lt;element name="Column" maxOccurs="unbounded"&gt;
 *                               &lt;complexType&gt;
 *                                 &lt;complexContent&gt;
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                     &lt;sequence&gt;
 *                                       &lt;element name="Text"&gt;
 *                                         &lt;complexType&gt;
 *                                           &lt;complexContent&gt;
 *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                               &lt;sequence&gt;
 *                                                 &lt;element name="Varient" type="{http://www.w3.org/2001/XMLSchema}anyType"/&gt;
 *                                                 &lt;element name="TextBlock"&gt;
 *                                                   &lt;complexType&gt;
 *                                                     &lt;complexContent&gt;
 *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                                         &lt;sequence&gt;
 *                                                           &lt;element name="Character"&gt;
 *                                                             &lt;complexType&gt;
 *                                                               &lt;complexContent&gt;
 *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                                                   &lt;attribute name="Family" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                                                                   &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                                                   &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                                                 &lt;/restriction&gt;
 *                                                               &lt;/complexContent&gt;
 *                                                             &lt;/complexType&gt;
 *                                                           &lt;/element&gt;
 *                                                           &lt;element name="Paragraph"&gt;
 *                                                             &lt;complexType&gt;
 *                                                               &lt;complexContent&gt;
 *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                                                   &lt;attribute name="Align" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                                                   &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                                                   &lt;attribute name="SpLine" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                                                 &lt;/restriction&gt;
 *                                                               &lt;/complexContent&gt;
 *                                                             &lt;/complexType&gt;
 *                                                           &lt;/element&gt;
 *                                                           &lt;element name="WrapMode"&gt;
 *                                                             &lt;complexType&gt;
 *                                                               &lt;complexContent&gt;
 *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                                                   &lt;attribute name="Value" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                                                 &lt;/restriction&gt;
 *                                                               &lt;/complexContent&gt;
 *                                                             &lt;/complexType&gt;
 *                                                           &lt;/element&gt;
 *                                                           &lt;element name="FillColor"&gt;
 *                                                             &lt;complexType&gt;
 *                                                               &lt;complexContent&gt;
 *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                                                   &lt;attribute name="Color" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                                                                 &lt;/restriction&gt;
 *                                                               &lt;/complexContent&gt;
 *                                                             &lt;/complexType&gt;
 *                                                           &lt;/element&gt;
 *                                                           &lt;element name="Color"&gt;
 *                                                             &lt;complexType&gt;
 *                                                               &lt;complexContent&gt;
 *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                                                   &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                                                                 &lt;/restriction&gt;
 *                                                               &lt;/complexContent&gt;
 *                                                             &lt;/complexType&gt;
 *                                                           &lt;/element&gt;
 *                                                         &lt;/sequence&gt;
 *                                                         &lt;attribute name="VAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                                                         &lt;attribute name="HAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                                                         &lt;attribute name="TextFormatMask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                                       &lt;/restriction&gt;
 *                                                     &lt;/complexContent&gt;
 *                                                   &lt;/complexType&gt;
 *                                                 &lt;/element&gt;
 *                                               &lt;/sequence&gt;
 *                                               &lt;attribute name="FieldType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                               &lt;attribute name="PlainText" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                                               &lt;attribute name="FieldID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                               &lt;attribute name="DataType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                             &lt;/restriction&gt;
 *                                           &lt;/complexContent&gt;
 *                                         &lt;/complexType&gt;
 *                                       &lt;/element&gt;
 *                                     &lt;/sequence&gt;
 *                                     &lt;attribute name="FieldType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                     &lt;attribute name="Key" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                     &lt;attribute name="IsHide" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
 *                                     &lt;attribute name="FilterType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                     &lt;attribute name="ColSelectALL" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                     &lt;attribute name="Width" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                     &lt;attribute name="SortStatus" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                   &lt;/restriction&gt;
 *                                 &lt;/complexContent&gt;
 *                               &lt;/complexType&gt;
 *                             &lt;/element&gt;
 *                           &lt;/sequence&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="TaskList"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="Task" maxOccurs="unbounded"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;sequence&gt;
 *                             &lt;element name="Format"&gt;
 *                               &lt;complexType&gt;
 *                                 &lt;complexContent&gt;
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                     &lt;attribute name="Family" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                                     &lt;attribute name="Bold" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                     &lt;attribute name="Underline" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                     &lt;attribute name="StrikeOut" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                     &lt;attribute name="PointSize" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                     &lt;attribute name="Italic" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                   &lt;/restriction&gt;
 *                                 &lt;/complexContent&gt;
 *                               &lt;/complexType&gt;
 *                             &lt;/element&gt;
 *                             &lt;element name="ResourceList"&gt;
 *                               &lt;complexType&gt;
 *                                 &lt;complexContent&gt;
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                     &lt;sequence&gt;
 *                                       &lt;element name="Resource" maxOccurs="unbounded"&gt;
 *                                         &lt;complexType&gt;
 *                                           &lt;complexContent&gt;
 *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                               &lt;attribute name="ID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                               &lt;attribute name="CostUnit" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                               &lt;attribute name="Cost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
 *                                               &lt;attribute name="CostPer" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
 *                                               &lt;attribute name="Type" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                               &lt;attribute name="WorkSecs" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
 *                                               &lt;attribute name="OvertimeUnit" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                               &lt;attribute name="Percent" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
 *                                               &lt;attribute name="OvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
 *                                               &lt;attribute name="Name" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                                             &lt;/restriction&gt;
 *                                           &lt;/complexContent&gt;
 *                                         &lt;/complexType&gt;
 *                                       &lt;/element&gt;
 *                                     &lt;/sequence&gt;
 *                                   &lt;/restriction&gt;
 *                                 &lt;/complexContent&gt;
 *                               &lt;/complexType&gt;
 *                             &lt;/element&gt;
 *                             &lt;element name="PredecessorLink" maxOccurs="unbounded"&gt;
 *                               &lt;complexType&gt;
 *                                 &lt;complexContent&gt;
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                     &lt;sequence&gt;
 *                                       &lt;element name="PredecessorUID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                                       &lt;element name="Type" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                                       &lt;element name="LinkLag" type="{http://www.w3.org/2001/XMLSchema}long"/&gt;
 *                                       &lt;element name="LagFormat" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                                       &lt;element name="CrossProject" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *                                     &lt;/sequence&gt;
 *                                   &lt;/restriction&gt;
 *                                 &lt;/complexContent&gt;
 *                               &lt;/complexType&gt;
 *                             &lt;/element&gt;
 *                             &lt;element name="Texts"&gt;
 *                               &lt;complexType&gt;
 *                                 &lt;complexContent&gt;
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                     &lt;sequence&gt;
 *                                       &lt;element name="TextCell" maxOccurs="unbounded"&gt;
 *                                         &lt;complexType&gt;
 *                                           &lt;complexContent&gt;
 *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                               &lt;sequence&gt;
 *                                                 &lt;element name="Varient" type="{http://www.w3.org/2001/XMLSchema}anyType"/&gt;
 *                                                 &lt;element name="TextBlock"&gt;
 *                                                   &lt;complexType&gt;
 *                                                     &lt;complexContent&gt;
 *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                                         &lt;sequence&gt;
 *                                                           &lt;element name="Character"&gt;
 *                                                             &lt;complexType&gt;
 *                                                               &lt;complexContent&gt;
 *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                                                   &lt;attribute name="Family" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                                                                   &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                                                   &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                                                 &lt;/restriction&gt;
 *                                                               &lt;/complexContent&gt;
 *                                                             &lt;/complexType&gt;
 *                                                           &lt;/element&gt;
 *                                                           &lt;element name="Paragraph"&gt;
 *                                                             &lt;complexType&gt;
 *                                                               &lt;complexContent&gt;
 *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                                                   &lt;attribute name="Align" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                                                   &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                                                   &lt;attribute name="SpLine" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                                                 &lt;/restriction&gt;
 *                                                               &lt;/complexContent&gt;
 *                                                             &lt;/complexType&gt;
 *                                                           &lt;/element&gt;
 *                                                           &lt;element name="WrapMode"&gt;
 *                                                             &lt;complexType&gt;
 *                                                               &lt;complexContent&gt;
 *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                                                   &lt;attribute name="Value" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                                                 &lt;/restriction&gt;
 *                                                               &lt;/complexContent&gt;
 *                                                             &lt;/complexType&gt;
 *                                                           &lt;/element&gt;
 *                                                           &lt;element name="FillColor"&gt;
 *                                                             &lt;complexType&gt;
 *                                                               &lt;complexContent&gt;
 *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                                                   &lt;attribute name="Color" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                                                                 &lt;/restriction&gt;
 *                                                               &lt;/complexContent&gt;
 *                                                             &lt;/complexType&gt;
 *                                                           &lt;/element&gt;
 *                                                           &lt;element name="Color"&gt;
 *                                                             &lt;complexType&gt;
 *                                                               &lt;complexContent&gt;
 *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                                                   &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                                                                 &lt;/restriction&gt;
 *                                                               &lt;/complexContent&gt;
 *                                                             &lt;/complexType&gt;
 *                                                           &lt;/element&gt;
 *                                                         &lt;/sequence&gt;
 *                                                         &lt;attribute name="VAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                                                         &lt;attribute name="HAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                                                         &lt;attribute name="TextFormatMask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                                       &lt;/restriction&gt;
 *                                                     &lt;/complexContent&gt;
 *                                                   &lt;/complexType&gt;
 *                                                 &lt;/element&gt;
 *                                               &lt;/sequence&gt;
 *                                               &lt;attribute name="FieldType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                               &lt;attribute name="PlainText" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                                               &lt;attribute name="FieldID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                               &lt;attribute name="DataType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                                             &lt;/restriction&gt;
 *                                           &lt;/complexContent&gt;
 *                                         &lt;/complexType&gt;
 *                                       &lt;/element&gt;
 *                                     &lt;/sequence&gt;
 *                                   &lt;/restriction&gt;
 *                                 &lt;/complexContent&gt;
 *                               &lt;/complexType&gt;
 *                             &lt;/element&gt;
 *                             &lt;element name="BarChart" type="{http://www.w3.org/2001/XMLSchema}anyType"/&gt;
 *                           &lt;/sequence&gt;
 *                           &lt;attribute name="Milestone" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
 *                           &lt;attribute name="BackGroundColor" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="ProgressStatus" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="BaseLinePointxEnd" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="m_Bestyletext" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="RowHeight" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="CriticalPath" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
 *                           &lt;attribute name="ID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="ActualDuration" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
 *                           &lt;attribute name="DateBaseStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
 *                           &lt;attribute name="Resources" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="FontColor" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="DurationUnits" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="DateManualFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
 *                           &lt;attribute name="m_bebartextIndex" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="m_Istyletext" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="BaseLinePointxBegin" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="Manual" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
 *                           &lt;attribute name="HideByColumns" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="Level" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="IsToggler" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
 *                           &lt;attribute name="BaselineCost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
 *                           &lt;attribute name="DurationSecs" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
 *                           &lt;attribute name="ManualDurationSecs" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
 *                           &lt;attribute name="DateLateStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
 *                           &lt;attribute name="LastSaveDate" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
 *                           &lt;attribute name="m_IbartextIndex" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="ActualStart" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
 *                           &lt;attribute name="Work" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
 *                           &lt;attribute name="Cost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
 *                           &lt;attribute name="SplitOffsetDuration" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="DateStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
 *                           &lt;attribute name="RowID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="StartText" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="Name" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="ActualFinish" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
 *                           &lt;attribute name="FirstWidth" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="DateLateFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
 *                           &lt;attribute name="CountWidth" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="Priority" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="DateManualStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
 *                           &lt;attribute name="DateBaseFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
 *                           &lt;attribute name="ShowByChild" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="ActualMilestone" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
 *                           &lt;attribute name="ParentID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="LateSlack" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="DateFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
 *                           &lt;attribute name="Wbs" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="Notes" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="BaseLineNumber" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="HideID" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
 *                           &lt;attribute name="SplitPointList" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="Percent" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
 *                           &lt;attribute name="RemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
 *                           &lt;attribute name="EarlySlack" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="Childs" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="ResourceInfo"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="Column" maxOccurs="unbounded"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;attribute name="ID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="CostUnit" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="Cost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
 *                           &lt;attribute name="CostPer" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
 *                           &lt;attribute name="Type" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="OvertimeUnit" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="Notes" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="Unit" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                           &lt;attribute name="Email" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="OvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
 *                           &lt;attribute name="Name" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                           &lt;attribute name="Group" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="Calendars"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;sequence&gt;
 *                   &lt;element name="Calendar" maxOccurs="unbounded"&gt;
 *                     &lt;complexType&gt;
 *                       &lt;complexContent&gt;
 *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                           &lt;sequence&gt;
 *                             &lt;element name="UID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                             &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *                             &lt;element name="NameU" type="{http://www.w3.org/2001/XMLSchema}anyType"/&gt;
 *                             &lt;element name="IsBaseCalendar" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *                             &lt;element name="BaseCalendarUID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                             &lt;element name="IsTemplateCalendar" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *                             &lt;element name="MachineInfo" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *                             &lt;element name="WeekDays"&gt;
 *                               &lt;complexType&gt;
 *                                 &lt;complexContent&gt;
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                     &lt;sequence&gt;
 *                                       &lt;element name="WeekDay" maxOccurs="unbounded"&gt;
 *                                         &lt;complexType&gt;
 *                                           &lt;complexContent&gt;
 *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                               &lt;sequence&gt;
 *                                                 &lt;element name="DayType" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                                                 &lt;element name="DayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *                                                 &lt;element name="WorkingTimes"&gt;
 *                                                   &lt;complexType&gt;
 *                                                     &lt;complexContent&gt;
 *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                                         &lt;sequence&gt;
 *                                                           &lt;element name="WorkingTime" maxOccurs="unbounded"&gt;
 *                                                             &lt;complexType&gt;
 *                                                               &lt;complexContent&gt;
 *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                                                   &lt;sequence&gt;
 *                                                                     &lt;element name="FromTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
 *                                                                     &lt;element name="ToTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
 *                                                                   &lt;/sequence&gt;
 *                                                                 &lt;/restriction&gt;
 *                                                               &lt;/complexContent&gt;
 *                                                             &lt;/complexType&gt;
 *                                                           &lt;/element&gt;
 *                                                         &lt;/sequence&gt;
 *                                                       &lt;/restriction&gt;
 *                                                     &lt;/complexContent&gt;
 *                                                   &lt;/complexType&gt;
 *                                                 &lt;/element&gt;
 *                                               &lt;/sequence&gt;
 *                                             &lt;/restriction&gt;
 *                                           &lt;/complexContent&gt;
 *                                         &lt;/complexType&gt;
 *                                       &lt;/element&gt;
 *                                     &lt;/sequence&gt;
 *                                   &lt;/restriction&gt;
 *                                 &lt;/complexContent&gt;
 *                               &lt;/complexType&gt;
 *                             &lt;/element&gt;
 *                             &lt;element name="Exceptions"&gt;
 *                               &lt;complexType&gt;
 *                                 &lt;complexContent&gt;
 *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                     &lt;sequence&gt;
 *                                       &lt;element name="Exception" maxOccurs="unbounded"&gt;
 *                                         &lt;complexType&gt;
 *                                           &lt;complexContent&gt;
 *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                               &lt;sequence&gt;
 *                                                 &lt;element name="EnteredByOccurrences" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *                                                 &lt;element name="Holiday" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *                                                 &lt;element name="TimePeriod"&gt;
 *                                                   &lt;complexType&gt;
 *                                                     &lt;complexContent&gt;
 *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                                         &lt;sequence&gt;
 *                                                           &lt;element name="FromDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *                                                           &lt;element name="ToDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *                                                         &lt;/sequence&gt;
 *                                                       &lt;/restriction&gt;
 *                                                     &lt;/complexContent&gt;
 *                                                   &lt;/complexType&gt;
 *                                                 &lt;/element&gt;
 *                                                 &lt;element name="Occurrences" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                                                 &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *                                                 &lt;element name="Type" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *                                                 &lt;element name="DayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *                                                 &lt;element name="WorkingTimes"&gt;
 *                                                   &lt;complexType&gt;
 *                                                     &lt;complexContent&gt;
 *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                                         &lt;sequence&gt;
 *                                                           &lt;element name="WorkingTime" maxOccurs="unbounded"&gt;
 *                                                             &lt;complexType&gt;
 *                                                               &lt;complexContent&gt;
 *                                                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                                                                   &lt;sequence&gt;
 *                                                                     &lt;element name="FromTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
 *                                                                     &lt;element name="ToTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
 *                                                                   &lt;/sequence&gt;
 *                                                                 &lt;/restriction&gt;
 *                                                               &lt;/complexContent&gt;
 *                                                             &lt;/complexType&gt;
 *                                                           &lt;/element&gt;
 *                                                         &lt;/sequence&gt;
 *                                                       &lt;/restriction&gt;
 *                                                     &lt;/complexContent&gt;
 *                                                   &lt;/complexType&gt;
 *                                                 &lt;/element&gt;
 *                                               &lt;/sequence&gt;
 *                                             &lt;/restriction&gt;
 *                                           &lt;/complexContent&gt;
 *                                         &lt;/complexType&gt;
 *                                       &lt;/element&gt;
 *                                     &lt;/sequence&gt;
 *                                   &lt;/restriction&gt;
 *                                 &lt;/complexContent&gt;
 *                               &lt;/complexType&gt;
 *                             &lt;/element&gt;
 *                           &lt;/sequence&gt;
 *                         &lt;/restriction&gt;
 *                       &lt;/complexContent&gt;
 *                     &lt;/complexType&gt;
 *                   &lt;/element&gt;
 *                 &lt;/sequence&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="WaterMark"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="Type" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *         &lt;element name="lineStyleInformation"&gt;
 *           &lt;complexType&gt;
 *             &lt;complexContent&gt;
 *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *                 &lt;attribute name="line_roundsize" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="line_width" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *                 &lt;attribute name="line_Color" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *                 &lt;attribute name="line_index" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
 *               &lt;/restriction&gt;
 *             &lt;/complexContent&gt;
 *           &lt;/complexType&gt;
 *         &lt;/element&gt;
 *       &lt;/sequence&gt;
 *       &lt;attribute name="DocGuid" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *       &lt;attribute name="Version" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *       &lt;attribute name="OS" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@SuppressWarnings("all") @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
{
   "createdVersion",
   "creationDate",
   "creator",
   "lastSaved",
   "modifier",
   "dPi",
   "screenWidth",
   "screenHeight",
   "calendarUID",
   "minutesPerDay",
   "minutesPerWeek",
   "daysPerMonth",
   "dateFormat",
   "ganttViewSplitterRate",
   "mIsShowSpecificTime",
   "ganttOption",
   "rowColumn",
   "taskList",
   "resourceInfo",
   "calendars",
   "waterMark",
   "lineStyleInformation"
}) @XmlRootElement(name = "Document") public class Document
{

   @XmlElement(name = "CreatedVersion", required = true) protected Document.CreatedVersion createdVersion;
   @XmlElement(name = "CreationDate", required = true) protected Document.CreationDate creationDate;
   @XmlElement(name = "Creator", required = true) protected Document.Creator creator;
   @XmlElement(name = "LastSaved", required = true) protected Document.LastSaved lastSaved;
   @XmlElement(name = "Modifier", required = true) protected Document.Modifier modifier;
   @XmlElement(name = "DPi", required = true) protected Document.DPi dPi;
   @XmlElement(name = "ScreenWidth", required = true) protected Document.ScreenWidth screenWidth;
   @XmlElement(name = "ScreenHeight", required = true) protected Document.ScreenHeight screenHeight;
   @XmlElement(name = "CalendarUID", required = true) protected Document.CalendarUID calendarUID;
   @XmlElement(name = "MinutesPerDay", required = true) protected Document.MinutesPerDay minutesPerDay;
   @XmlElement(name = "MinutesPerWeek", required = true) protected Document.MinutesPerWeek minutesPerWeek;
   @XmlElement(name = "DaysPerMonth", required = true) protected Document.DaysPerMonth daysPerMonth;
   @XmlElement(name = "DateFormat", required = true) protected Document.DateFormat dateFormat;
   @XmlElement(name = "GanttViewSplitterRate", required = true) protected Document.GanttViewSplitterRate ganttViewSplitterRate;
   @XmlElement(name = "m_isShowSpecificTime", required = true) protected Document.MIsShowSpecificTime mIsShowSpecificTime;
   @XmlElement(name = "GanttOption", required = true) protected Document.GanttOption ganttOption;
   @XmlElement(name = "RowColumn", required = true) protected Document.RowColumn rowColumn;
   @XmlElement(name = "TaskList", required = true) protected Document.TaskList taskList;
   @XmlElement(name = "ResourceInfo", required = true) protected Document.ResourceInfo resourceInfo;
   @XmlElement(name = "Calendars", required = true) protected Document.Calendars calendars;
   @XmlElement(name = "WaterMark", required = true) protected Document.WaterMark waterMark;
   @XmlElement(required = true) protected Document.LineStyleInformation lineStyleInformation;
   @XmlAttribute(name = "DocGuid") protected String docGuid;
   @XmlAttribute(name = "Version") protected String version;
   @XmlAttribute(name = "OS") protected String os;

   /**
    * Gets the value of the createdVersion property.
    *
    * @return
    *     possible object is
    *     {@link Document.CreatedVersion }
    *
    */
   public Document.CreatedVersion getCreatedVersion()
   {
      return createdVersion;
   }

   /**
    * Sets the value of the createdVersion property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.CreatedVersion }
    *
    */
   public void setCreatedVersion(Document.CreatedVersion value)
   {
      this.createdVersion = value;
   }

   /**
    * Gets the value of the creationDate property.
    *
    * @return
    *     possible object is
    *     {@link Document.CreationDate }
    *
    */
   public Document.CreationDate getCreationDate()
   {
      return creationDate;
   }

   /**
    * Sets the value of the creationDate property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.CreationDate }
    *
    */
   public void setCreationDate(Document.CreationDate value)
   {
      this.creationDate = value;
   }

   /**
    * Gets the value of the creator property.
    *
    * @return
    *     possible object is
    *     {@link Document.Creator }
    *
    */
   public Document.Creator getCreator()
   {
      return creator;
   }

   /**
    * Sets the value of the creator property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.Creator }
    *
    */
   public void setCreator(Document.Creator value)
   {
      this.creator = value;
   }

   /**
    * Gets the value of the lastSaved property.
    *
    * @return
    *     possible object is
    *     {@link Document.LastSaved }
    *
    */
   public Document.LastSaved getLastSaved()
   {
      return lastSaved;
   }

   /**
    * Sets the value of the lastSaved property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.LastSaved }
    *
    */
   public void setLastSaved(Document.LastSaved value)
   {
      this.lastSaved = value;
   }

   /**
    * Gets the value of the modifier property.
    *
    * @return
    *     possible object is
    *     {@link Document.Modifier }
    *
    */
   public Document.Modifier getModifier()
   {
      return modifier;
   }

   /**
    * Sets the value of the modifier property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.Modifier }
    *
    */
   public void setModifier(Document.Modifier value)
   {
      this.modifier = value;
   }

   /**
    * Gets the value of the dPi property.
    *
    * @return
    *     possible object is
    *     {@link Document.DPi }
    *
    */
   public Document.DPi getDPi()
   {
      return dPi;
   }

   /**
    * Sets the value of the dPi property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.DPi }
    *
    */
   public void setDPi(Document.DPi value)
   {
      this.dPi = value;
   }

   /**
    * Gets the value of the screenWidth property.
    *
    * @return
    *     possible object is
    *     {@link Document.ScreenWidth }
    *
    */
   public Document.ScreenWidth getScreenWidth()
   {
      return screenWidth;
   }

   /**
    * Sets the value of the screenWidth property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.ScreenWidth }
    *
    */
   public void setScreenWidth(Document.ScreenWidth value)
   {
      this.screenWidth = value;
   }

   /**
    * Gets the value of the screenHeight property.
    *
    * @return
    *     possible object is
    *     {@link Document.ScreenHeight }
    *
    */
   public Document.ScreenHeight getScreenHeight()
   {
      return screenHeight;
   }

   /**
    * Sets the value of the screenHeight property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.ScreenHeight }
    *
    */
   public void setScreenHeight(Document.ScreenHeight value)
   {
      this.screenHeight = value;
   }

   /**
    * Gets the value of the calendarUID property.
    *
    * @return
    *     possible object is
    *     {@link Document.CalendarUID }
    *
    */
   public Document.CalendarUID getCalendarUID()
   {
      return calendarUID;
   }

   /**
    * Sets the value of the calendarUID property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.CalendarUID }
    *
    */
   public void setCalendarUID(Document.CalendarUID value)
   {
      this.calendarUID = value;
   }

   /**
    * Gets the value of the minutesPerDay property.
    *
    * @return
    *     possible object is
    *     {@link Document.MinutesPerDay }
    *
    */
   public Document.MinutesPerDay getMinutesPerDay()
   {
      return minutesPerDay;
   }

   /**
    * Sets the value of the minutesPerDay property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.MinutesPerDay }
    *
    */
   public void setMinutesPerDay(Document.MinutesPerDay value)
   {
      this.minutesPerDay = value;
   }

   /**
    * Gets the value of the minutesPerWeek property.
    *
    * @return
    *     possible object is
    *     {@link Document.MinutesPerWeek }
    *
    */
   public Document.MinutesPerWeek getMinutesPerWeek()
   {
      return minutesPerWeek;
   }

   /**
    * Sets the value of the minutesPerWeek property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.MinutesPerWeek }
    *
    */
   public void setMinutesPerWeek(Document.MinutesPerWeek value)
   {
      this.minutesPerWeek = value;
   }

   /**
    * Gets the value of the daysPerMonth property.
    *
    * @return
    *     possible object is
    *     {@link Document.DaysPerMonth }
    *
    */
   public Document.DaysPerMonth getDaysPerMonth()
   {
      return daysPerMonth;
   }

   /**
    * Sets the value of the daysPerMonth property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.DaysPerMonth }
    *
    */
   public void setDaysPerMonth(Document.DaysPerMonth value)
   {
      this.daysPerMonth = value;
   }

   /**
    * Gets the value of the dateFormat property.
    *
    * @return
    *     possible object is
    *     {@link Document.DateFormat }
    *
    */
   public Document.DateFormat getDateFormat()
   {
      return dateFormat;
   }

   /**
    * Sets the value of the dateFormat property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.DateFormat }
    *
    */
   public void setDateFormat(Document.DateFormat value)
   {
      this.dateFormat = value;
   }

   /**
    * Gets the value of the ganttViewSplitterRate property.
    *
    * @return
    *     possible object is
    *     {@link Document.GanttViewSplitterRate }
    *
    */
   public Document.GanttViewSplitterRate getGanttViewSplitterRate()
   {
      return ganttViewSplitterRate;
   }

   /**
    * Sets the value of the ganttViewSplitterRate property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.GanttViewSplitterRate }
    *
    */
   public void setGanttViewSplitterRate(Document.GanttViewSplitterRate value)
   {
      this.ganttViewSplitterRate = value;
   }

   /**
    * Gets the value of the mIsShowSpecificTime property.
    *
    * @return
    *     possible object is
    *     {@link Document.MIsShowSpecificTime }
    *
    */
   public Document.MIsShowSpecificTime getMIsShowSpecificTime()
   {
      return mIsShowSpecificTime;
   }

   /**
    * Sets the value of the mIsShowSpecificTime property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.MIsShowSpecificTime }
    *
    */
   public void setMIsShowSpecificTime(Document.MIsShowSpecificTime value)
   {
      this.mIsShowSpecificTime = value;
   }

   /**
    * Gets the value of the ganttOption property.
    *
    * @return
    *     possible object is
    *     {@link Document.GanttOption }
    *
    */
   public Document.GanttOption getGanttOption()
   {
      return ganttOption;
   }

   /**
    * Sets the value of the ganttOption property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.GanttOption }
    *
    */
   public void setGanttOption(Document.GanttOption value)
   {
      this.ganttOption = value;
   }

   /**
    * Gets the value of the rowColumn property.
    *
    * @return
    *     possible object is
    *     {@link Document.RowColumn }
    *
    */
   public Document.RowColumn getRowColumn()
   {
      return rowColumn;
   }

   /**
    * Sets the value of the rowColumn property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.RowColumn }
    *
    */
   public void setRowColumn(Document.RowColumn value)
   {
      this.rowColumn = value;
   }

   /**
    * Gets the value of the taskList property.
    *
    * @return
    *     possible object is
    *     {@link Document.TaskList }
    *
    */
   public Document.TaskList getTaskList()
   {
      return taskList;
   }

   /**
    * Sets the value of the taskList property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.TaskList }
    *
    */
   public void setTaskList(Document.TaskList value)
   {
      this.taskList = value;
   }

   /**
    * Gets the value of the resourceInfo property.
    *
    * @return
    *     possible object is
    *     {@link Document.ResourceInfo }
    *
    */
   public Document.ResourceInfo getResourceInfo()
   {
      return resourceInfo;
   }

   /**
    * Sets the value of the resourceInfo property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.ResourceInfo }
    *
    */
   public void setResourceInfo(Document.ResourceInfo value)
   {
      this.resourceInfo = value;
   }

   /**
    * Gets the value of the calendars property.
    *
    * @return
    *     possible object is
    *     {@link Document.Calendars }
    *
    */
   public Document.Calendars getCalendars()
   {
      return calendars;
   }

   /**
    * Sets the value of the calendars property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.Calendars }
    *
    */
   public void setCalendars(Document.Calendars value)
   {
      this.calendars = value;
   }

   /**
    * Gets the value of the waterMark property.
    *
    * @return
    *     possible object is
    *     {@link Document.WaterMark }
    *
    */
   public Document.WaterMark getWaterMark()
   {
      return waterMark;
   }

   /**
    * Sets the value of the waterMark property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.WaterMark }
    *
    */
   public void setWaterMark(Document.WaterMark value)
   {
      this.waterMark = value;
   }

   /**
    * Gets the value of the lineStyleInformation property.
    *
    * @return
    *     possible object is
    *     {@link Document.LineStyleInformation }
    *
    */
   public Document.LineStyleInformation getLineStyleInformation()
   {
      return lineStyleInformation;
   }

   /**
    * Sets the value of the lineStyleInformation property.
    *
    * @param value
    *     allowed object is
    *     {@link Document.LineStyleInformation }
    *
    */
   public void setLineStyleInformation(Document.LineStyleInformation value)
   {
      this.lineStyleInformation = value;
   }

   /**
    * Gets the value of the docGuid property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getDocGuid()
   {
      return docGuid;
   }

   /**
    * Sets the value of the docGuid property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setDocGuid(String value)
   {
      this.docGuid = value;
   }

   /**
    * Gets the value of the version property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getVersion()
   {
      return version;
   }

   /**
    * Sets the value of the version property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setVersion(String value)
   {
      this.version = value;
   }

   /**
    * Gets the value of the os property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getOS()
   {
      return os;
   }

   /**
    * Sets the value of the os property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setOS(String value)
   {
      this.os = value;
   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element name="Calendar" maxOccurs="unbounded"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;sequence&gt;
    *                   &lt;element name="UID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *                   &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
    *                   &lt;element name="NameU" type="{http://www.w3.org/2001/XMLSchema}anyType"/&gt;
    *                   &lt;element name="IsBaseCalendar" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
    *                   &lt;element name="BaseCalendarUID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *                   &lt;element name="IsTemplateCalendar" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
    *                   &lt;element name="MachineInfo" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
    *                   &lt;element name="WeekDays"&gt;
    *                     &lt;complexType&gt;
    *                       &lt;complexContent&gt;
    *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                           &lt;sequence&gt;
    *                             &lt;element name="WeekDay" maxOccurs="unbounded"&gt;
    *                               &lt;complexType&gt;
    *                                 &lt;complexContent&gt;
    *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                     &lt;sequence&gt;
    *                                       &lt;element name="DayType" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *                                       &lt;element name="DayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
    *                                       &lt;element name="WorkingTimes"&gt;
    *                                         &lt;complexType&gt;
    *                                           &lt;complexContent&gt;
    *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                               &lt;sequence&gt;
    *                                                 &lt;element name="WorkingTime" maxOccurs="unbounded"&gt;
    *                                                   &lt;complexType&gt;
    *                                                     &lt;complexContent&gt;
    *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                                         &lt;sequence&gt;
    *                                                           &lt;element name="FromTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
    *                                                           &lt;element name="ToTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
    *                                                         &lt;/sequence&gt;
    *                                                       &lt;/restriction&gt;
    *                                                     &lt;/complexContent&gt;
    *                                                   &lt;/complexType&gt;
    *                                                 &lt;/element&gt;
    *                                               &lt;/sequence&gt;
    *                                             &lt;/restriction&gt;
    *                                           &lt;/complexContent&gt;
    *                                         &lt;/complexType&gt;
    *                                       &lt;/element&gt;
    *                                     &lt;/sequence&gt;
    *                                   &lt;/restriction&gt;
    *                                 &lt;/complexContent&gt;
    *                               &lt;/complexType&gt;
    *                             &lt;/element&gt;
    *                           &lt;/sequence&gt;
    *                         &lt;/restriction&gt;
    *                       &lt;/complexContent&gt;
    *                     &lt;/complexType&gt;
    *                   &lt;/element&gt;
    *                   &lt;element name="Exceptions"&gt;
    *                     &lt;complexType&gt;
    *                       &lt;complexContent&gt;
    *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                           &lt;sequence&gt;
    *                             &lt;element name="Exception" maxOccurs="unbounded"&gt;
    *                               &lt;complexType&gt;
    *                                 &lt;complexContent&gt;
    *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                     &lt;sequence&gt;
    *                                       &lt;element name="EnteredByOccurrences" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
    *                                       &lt;element name="Holiday" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
    *                                       &lt;element name="TimePeriod"&gt;
    *                                         &lt;complexType&gt;
    *                                           &lt;complexContent&gt;
    *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                               &lt;sequence&gt;
    *                                                 &lt;element name="FromDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
    *                                                 &lt;element name="ToDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
    *                                               &lt;/sequence&gt;
    *                                             &lt;/restriction&gt;
    *                                           &lt;/complexContent&gt;
    *                                         &lt;/complexType&gt;
    *                                       &lt;/element&gt;
    *                                       &lt;element name="Occurrences" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *                                       &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
    *                                       &lt;element name="Type" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *                                       &lt;element name="DayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
    *                                       &lt;element name="WorkingTimes"&gt;
    *                                         &lt;complexType&gt;
    *                                           &lt;complexContent&gt;
    *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                               &lt;sequence&gt;
    *                                                 &lt;element name="WorkingTime" maxOccurs="unbounded"&gt;
    *                                                   &lt;complexType&gt;
    *                                                     &lt;complexContent&gt;
    *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                                         &lt;sequence&gt;
    *                                                           &lt;element name="FromTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
    *                                                           &lt;element name="ToTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
    *                                                         &lt;/sequence&gt;
    *                                                       &lt;/restriction&gt;
    *                                                     &lt;/complexContent&gt;
    *                                                   &lt;/complexType&gt;
    *                                                 &lt;/element&gt;
    *                                               &lt;/sequence&gt;
    *                                             &lt;/restriction&gt;
    *                                           &lt;/complexContent&gt;
    *                                         &lt;/complexType&gt;
    *                                       &lt;/element&gt;
    *                                     &lt;/sequence&gt;
    *                                   &lt;/restriction&gt;
    *                                 &lt;/complexContent&gt;
    *                               &lt;/complexType&gt;
    *                             &lt;/element&gt;
    *                           &lt;/sequence&gt;
    *                         &lt;/restriction&gt;
    *                       &lt;/complexContent&gt;
    *                     &lt;/complexType&gt;
    *                   &lt;/element&gt;
    *                 &lt;/sequence&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *       &lt;/sequence&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "calendar"
   }) public static class Calendars
   {

      @XmlElement(name = "Calendar", required = true) protected List<Document.Calendars.Calendar> calendar;

      /**
       * Gets the value of the calendar property.
       *
       * <p>
       * This accessor method returns a reference to the live list,
       * not a snapshot. Therefore any modification you make to the
       * returned list will be present inside the Jakarta XML Binding object.
       * This is why there is not a <CODE>set</CODE> method for the calendar property.
       *
       * <p>
       * For example, to add a new item, do as follows:
       * <pre>
       *    getCalendar().add(newItem);
       * </pre>
       *
       *
       * <p>
       * Objects of the following type(s) are allowed in the list
       * {@link Document.Calendars.Calendar }
       *
       *
       */
      public List<Document.Calendars.Calendar> getCalendar()
      {
         if (calendar == null)
         {
            calendar = new ArrayList<>();
         }
         return this.calendar;
      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;sequence&gt;
       *         &lt;element name="UID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
       *         &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
       *         &lt;element name="NameU" type="{http://www.w3.org/2001/XMLSchema}anyType"/&gt;
       *         &lt;element name="IsBaseCalendar" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
       *         &lt;element name="BaseCalendarUID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
       *         &lt;element name="IsTemplateCalendar" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
       *         &lt;element name="MachineInfo" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
       *         &lt;element name="WeekDays"&gt;
       *           &lt;complexType&gt;
       *             &lt;complexContent&gt;
       *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                 &lt;sequence&gt;
       *                   &lt;element name="WeekDay" maxOccurs="unbounded"&gt;
       *                     &lt;complexType&gt;
       *                       &lt;complexContent&gt;
       *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                           &lt;sequence&gt;
       *                             &lt;element name="DayType" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
       *                             &lt;element name="DayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
       *                             &lt;element name="WorkingTimes"&gt;
       *                               &lt;complexType&gt;
       *                                 &lt;complexContent&gt;
       *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                                     &lt;sequence&gt;
       *                                       &lt;element name="WorkingTime" maxOccurs="unbounded"&gt;
       *                                         &lt;complexType&gt;
       *                                           &lt;complexContent&gt;
       *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                                               &lt;sequence&gt;
       *                                                 &lt;element name="FromTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
       *                                                 &lt;element name="ToTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
       *                                               &lt;/sequence&gt;
       *                                             &lt;/restriction&gt;
       *                                           &lt;/complexContent&gt;
       *                                         &lt;/complexType&gt;
       *                                       &lt;/element&gt;
       *                                     &lt;/sequence&gt;
       *                                   &lt;/restriction&gt;
       *                                 &lt;/complexContent&gt;
       *                               &lt;/complexType&gt;
       *                             &lt;/element&gt;
       *                           &lt;/sequence&gt;
       *                         &lt;/restriction&gt;
       *                       &lt;/complexContent&gt;
       *                     &lt;/complexType&gt;
       *                   &lt;/element&gt;
       *                 &lt;/sequence&gt;
       *               &lt;/restriction&gt;
       *             &lt;/complexContent&gt;
       *           &lt;/complexType&gt;
       *         &lt;/element&gt;
       *         &lt;element name="Exceptions"&gt;
       *           &lt;complexType&gt;
       *             &lt;complexContent&gt;
       *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                 &lt;sequence&gt;
       *                   &lt;element name="Exception" maxOccurs="unbounded"&gt;
       *                     &lt;complexType&gt;
       *                       &lt;complexContent&gt;
       *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                           &lt;sequence&gt;
       *                             &lt;element name="EnteredByOccurrences" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
       *                             &lt;element name="Holiday" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
       *                             &lt;element name="TimePeriod"&gt;
       *                               &lt;complexType&gt;
       *                                 &lt;complexContent&gt;
       *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                                     &lt;sequence&gt;
       *                                       &lt;element name="FromDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
       *                                       &lt;element name="ToDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
       *                                     &lt;/sequence&gt;
       *                                   &lt;/restriction&gt;
       *                                 &lt;/complexContent&gt;
       *                               &lt;/complexType&gt;
       *                             &lt;/element&gt;
       *                             &lt;element name="Occurrences" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
       *                             &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
       *                             &lt;element name="Type" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
       *                             &lt;element name="DayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
       *                             &lt;element name="WorkingTimes"&gt;
       *                               &lt;complexType&gt;
       *                                 &lt;complexContent&gt;
       *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                                     &lt;sequence&gt;
       *                                       &lt;element name="WorkingTime" maxOccurs="unbounded"&gt;
       *                                         &lt;complexType&gt;
       *                                           &lt;complexContent&gt;
       *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                                               &lt;sequence&gt;
       *                                                 &lt;element name="FromTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
       *                                                 &lt;element name="ToTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
       *                                               &lt;/sequence&gt;
       *                                             &lt;/restriction&gt;
       *                                           &lt;/complexContent&gt;
       *                                         &lt;/complexType&gt;
       *                                       &lt;/element&gt;
       *                                     &lt;/sequence&gt;
       *                                   &lt;/restriction&gt;
       *                                 &lt;/complexContent&gt;
       *                               &lt;/complexType&gt;
       *                             &lt;/element&gt;
       *                           &lt;/sequence&gt;
       *                         &lt;/restriction&gt;
       *                       &lt;/complexContent&gt;
       *                     &lt;/complexType&gt;
       *                   &lt;/element&gt;
       *                 &lt;/sequence&gt;
       *               &lt;/restriction&gt;
       *             &lt;/complexContent&gt;
       *           &lt;/complexType&gt;
       *         &lt;/element&gt;
       *       &lt;/sequence&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
      {
         "uid",
         "name",
         "nameU",
         "isBaseCalendar",
         "baseCalendarUID",
         "isTemplateCalendar",
         "machineInfo",
         "weekDays",
         "exceptions"
      }) public static class Calendar
      {

         @XmlElement(name = "UID", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer uid;
         @XmlElement(name = "Name", required = true) protected String name;
         @XmlElement(name = "NameU", required = true) protected Object nameU;
         @XmlElement(name = "IsBaseCalendar", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "boolean") protected Boolean isBaseCalendar;
         @XmlElement(name = "BaseCalendarUID", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer baseCalendarUID;
         @XmlElement(name = "IsTemplateCalendar", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "boolean") protected Boolean isTemplateCalendar;
         @XmlElement(name = "MachineInfo", required = true) protected String machineInfo;
         @XmlElement(name = "WeekDays", required = true) protected Document.Calendars.Calendar.WeekDays weekDays;
         @XmlElement(name = "Exceptions", required = true) protected Document.Calendars.Calendar.Exceptions exceptions;

         /**
          * Gets the value of the uid property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getUID()
         {
            return uid;
         }

         /**
          * Sets the value of the uid property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setUID(Integer value)
         {
            this.uid = value;
         }

         /**
          * Gets the value of the name property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getName()
         {
            return name;
         }

         /**
          * Sets the value of the name property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setName(String value)
         {
            this.name = value;
         }

         /**
          * Gets the value of the nameU property.
          *
          * @return
          *     possible object is
          *     {@link Object }
          *
          */
         public Object getNameU()
         {
            return nameU;
         }

         /**
          * Sets the value of the nameU property.
          *
          * @param value
          *     allowed object is
          *     {@link Object }
          *
          */
         public void setNameU(Object value)
         {
            this.nameU = value;
         }

         /**
          * Gets the value of the isBaseCalendar property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Boolean isIsBaseCalendar()
         {
            return isBaseCalendar;
         }

         /**
          * Sets the value of the isBaseCalendar property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setIsBaseCalendar(Boolean value)
         {
            this.isBaseCalendar = value;
         }

         /**
          * Gets the value of the baseCalendarUID property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getBaseCalendarUID()
         {
            return baseCalendarUID;
         }

         /**
          * Sets the value of the baseCalendarUID property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setBaseCalendarUID(Integer value)
         {
            this.baseCalendarUID = value;
         }

         /**
          * Gets the value of the isTemplateCalendar property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Boolean isIsTemplateCalendar()
         {
            return isTemplateCalendar;
         }

         /**
          * Sets the value of the isTemplateCalendar property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setIsTemplateCalendar(Boolean value)
         {
            this.isTemplateCalendar = value;
         }

         /**
          * Gets the value of the machineInfo property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getMachineInfo()
         {
            return machineInfo;
         }

         /**
          * Sets the value of the machineInfo property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setMachineInfo(String value)
         {
            this.machineInfo = value;
         }

         /**
          * Gets the value of the weekDays property.
          *
          * @return
          *     possible object is
          *     {@link Document.Calendars.Calendar.WeekDays }
          *
          */
         public Document.Calendars.Calendar.WeekDays getWeekDays()
         {
            return weekDays;
         }

         /**
          * Sets the value of the weekDays property.
          *
          * @param value
          *     allowed object is
          *     {@link Document.Calendars.Calendar.WeekDays }
          *
          */
         public void setWeekDays(Document.Calendars.Calendar.WeekDays value)
         {
            this.weekDays = value;
         }

         /**
          * Gets the value of the exceptions property.
          *
          * @return
          *     possible object is
          *     {@link Document.Calendars.Calendar.Exceptions }
          *
          */
         public Document.Calendars.Calendar.Exceptions getExceptions()
         {
            return exceptions;
         }

         /**
          * Sets the value of the exceptions property.
          *
          * @param value
          *     allowed object is
          *     {@link Document.Calendars.Calendar.Exceptions }
          *
          */
         public void setExceptions(Document.Calendars.Calendar.Exceptions value)
         {
            this.exceptions = value;
         }

         /**
          * <p>Java class for anonymous complex type.
          *
          * <p>The following schema fragment specifies the expected content contained within this class.
          *
          * <pre>
          * &lt;complexType&gt;
          *   &lt;complexContent&gt;
          *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *       &lt;sequence&gt;
          *         &lt;element name="Exception" maxOccurs="unbounded"&gt;
          *           &lt;complexType&gt;
          *             &lt;complexContent&gt;
          *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                 &lt;sequence&gt;
          *                   &lt;element name="EnteredByOccurrences" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
          *                   &lt;element name="Holiday" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
          *                   &lt;element name="TimePeriod"&gt;
          *                     &lt;complexType&gt;
          *                       &lt;complexContent&gt;
          *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                           &lt;sequence&gt;
          *                             &lt;element name="FromDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
          *                             &lt;element name="ToDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
          *                           &lt;/sequence&gt;
          *                         &lt;/restriction&gt;
          *                       &lt;/complexContent&gt;
          *                     &lt;/complexType&gt;
          *                   &lt;/element&gt;
          *                   &lt;element name="Occurrences" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
          *                   &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
          *                   &lt;element name="Type" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
          *                   &lt;element name="DayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
          *                   &lt;element name="WorkingTimes"&gt;
          *                     &lt;complexType&gt;
          *                       &lt;complexContent&gt;
          *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                           &lt;sequence&gt;
          *                             &lt;element name="WorkingTime" maxOccurs="unbounded"&gt;
          *                               &lt;complexType&gt;
          *                                 &lt;complexContent&gt;
          *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                                     &lt;sequence&gt;
          *                                       &lt;element name="FromTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
          *                                       &lt;element name="ToTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
          *                                     &lt;/sequence&gt;
          *                                   &lt;/restriction&gt;
          *                                 &lt;/complexContent&gt;
          *                               &lt;/complexType&gt;
          *                             &lt;/element&gt;
          *                           &lt;/sequence&gt;
          *                         &lt;/restriction&gt;
          *                       &lt;/complexContent&gt;
          *                     &lt;/complexType&gt;
          *                   &lt;/element&gt;
          *                 &lt;/sequence&gt;
          *               &lt;/restriction&gt;
          *             &lt;/complexContent&gt;
          *           &lt;/complexType&gt;
          *         &lt;/element&gt;
          *       &lt;/sequence&gt;
          *     &lt;/restriction&gt;
          *   &lt;/complexContent&gt;
          * &lt;/complexType&gt;
          * </pre>
          *
          *
          */
         @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
         {
            "exception"
         }) public static class Exceptions
         {

            @XmlElement(name = "Exception", required = true) protected List<Document.Calendars.Calendar.Exceptions.Exception> exception;

            /**
             * Gets the value of the exception property.
             *
             * <p>
             * This accessor method returns a reference to the live list,
             * not a snapshot. Therefore any modification you make to the
             * returned list will be present inside the Jakarta XML Binding object.
             * This is why there is not a <CODE>set</CODE> method for the exception property.
             *
             * <p>
             * For example, to add a new item, do as follows:
             * <pre>
             *    getException().add(newItem);
             * </pre>
             *
             *
             * <p>
             * Objects of the following type(s) are allowed in the list
             * {@link Document.Calendars.Calendar.Exceptions.Exception }
             *
             *
             */
            public List<Document.Calendars.Calendar.Exceptions.Exception> getException()
            {
               if (exception == null)
               {
                  exception = new ArrayList<>();
               }
               return this.exception;
            }

            /**
             * <p>Java class for anonymous complex type.
             *
             * <p>The following schema fragment specifies the expected content contained within this class.
             *
             * <pre>
             * &lt;complexType&gt;
             *   &lt;complexContent&gt;
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *       &lt;sequence&gt;
             *         &lt;element name="EnteredByOccurrences" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
             *         &lt;element name="Holiday" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
             *         &lt;element name="TimePeriod"&gt;
             *           &lt;complexType&gt;
             *             &lt;complexContent&gt;
             *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *                 &lt;sequence&gt;
             *                   &lt;element name="FromDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
             *                   &lt;element name="ToDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
             *                 &lt;/sequence&gt;
             *               &lt;/restriction&gt;
             *             &lt;/complexContent&gt;
             *           &lt;/complexType&gt;
             *         &lt;/element&gt;
             *         &lt;element name="Occurrences" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
             *         &lt;element name="Name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
             *         &lt;element name="Type" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
             *         &lt;element name="DayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
             *         &lt;element name="WorkingTimes"&gt;
             *           &lt;complexType&gt;
             *             &lt;complexContent&gt;
             *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *                 &lt;sequence&gt;
             *                   &lt;element name="WorkingTime" maxOccurs="unbounded"&gt;
             *                     &lt;complexType&gt;
             *                       &lt;complexContent&gt;
             *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *                           &lt;sequence&gt;
             *                             &lt;element name="FromTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
             *                             &lt;element name="ToTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
             *                           &lt;/sequence&gt;
             *                         &lt;/restriction&gt;
             *                       &lt;/complexContent&gt;
             *                     &lt;/complexType&gt;
             *                   &lt;/element&gt;
             *                 &lt;/sequence&gt;
             *               &lt;/restriction&gt;
             *             &lt;/complexContent&gt;
             *           &lt;/complexType&gt;
             *         &lt;/element&gt;
             *       &lt;/sequence&gt;
             *     &lt;/restriction&gt;
             *   &lt;/complexContent&gt;
             * &lt;/complexType&gt;
             * </pre>
             *
             *
             */
            @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
            {
               "enteredByOccurrences",
               "holiday",
               "timePeriod",
               "occurrences",
               "name",
               "type",
               "dayWorking",
               "workingTimes"
            }) public static class Exception
            {

               @XmlElement(name = "EnteredByOccurrences", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "boolean") protected Boolean enteredByOccurrences;
               @XmlElement(name = "Holiday", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "boolean") protected Boolean holiday;
               @XmlElement(name = "TimePeriod", required = true) protected Document.Calendars.Calendar.Exceptions.Exception.TimePeriod timePeriod;
               @XmlElement(name = "Occurrences", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer occurrences;
               @XmlElement(name = "Name", required = true) protected String name;
               @XmlElement(name = "Type", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer type;
               @XmlElement(name = "DayWorking", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "boolean") protected Boolean dayWorking;
               @XmlElement(name = "WorkingTimes", required = true) protected Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes workingTimes;

               /**
                * Gets the value of the enteredByOccurrences property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public Boolean isEnteredByOccurrences()
               {
                  return enteredByOccurrences;
               }

               /**
                * Sets the value of the enteredByOccurrences property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setEnteredByOccurrences(Boolean value)
               {
                  this.enteredByOccurrences = value;
               }

               /**
                * Gets the value of the holiday property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public Boolean isHoliday()
               {
                  return holiday;
               }

               /**
                * Sets the value of the holiday property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setHoliday(Boolean value)
               {
                  this.holiday = value;
               }

               /**
                * Gets the value of the timePeriod property.
                *
                * @return
                *     possible object is
                *     {@link Document.Calendars.Calendar.Exceptions.Exception.TimePeriod }
                *
                */
               public Document.Calendars.Calendar.Exceptions.Exception.TimePeriod getTimePeriod()
               {
                  return timePeriod;
               }

               /**
                * Sets the value of the timePeriod property.
                *
                * @param value
                *     allowed object is
                *     {@link Document.Calendars.Calendar.Exceptions.Exception.TimePeriod }
                *
                */
               public void setTimePeriod(Document.Calendars.Calendar.Exceptions.Exception.TimePeriod value)
               {
                  this.timePeriod = value;
               }

               /**
                * Gets the value of the occurrences property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public Integer getOccurrences()
               {
                  return occurrences;
               }

               /**
                * Sets the value of the occurrences property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setOccurrences(Integer value)
               {
                  this.occurrences = value;
               }

               /**
                * Gets the value of the name property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public String getName()
               {
                  return name;
               }

               /**
                * Sets the value of the name property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setName(String value)
               {
                  this.name = value;
               }

               /**
                * Gets the value of the type property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public Integer getType()
               {
                  return type;
               }

               /**
                * Sets the value of the type property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setType(Integer value)
               {
                  this.type = value;
               }

               /**
                * Gets the value of the dayWorking property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public Boolean isDayWorking()
               {
                  return dayWorking;
               }

               /**
                * Sets the value of the dayWorking property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setDayWorking(Boolean value)
               {
                  this.dayWorking = value;
               }

               /**
                * Gets the value of the workingTimes property.
                *
                * @return
                *     possible object is
                *     {@link Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes }
                *
                */
               public Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes getWorkingTimes()
               {
                  return workingTimes;
               }

               /**
                * Sets the value of the workingTimes property.
                *
                * @param value
                *     allowed object is
                *     {@link Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes }
                *
                */
               public void setWorkingTimes(Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes value)
               {
                  this.workingTimes = value;
               }

               /**
                * <p>Java class for anonymous complex type.
                *
                * <p>The following schema fragment specifies the expected content contained within this class.
                *
                * <pre>
                * &lt;complexType&gt;
                *   &lt;complexContent&gt;
                *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                *       &lt;sequence&gt;
                *         &lt;element name="FromDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
                *         &lt;element name="ToDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
                *       &lt;/sequence&gt;
                *     &lt;/restriction&gt;
                *   &lt;/complexContent&gt;
                * &lt;/complexType&gt;
                * </pre>
                *
                *
                */
               @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
               {
                  "fromDate",
                  "toDate"
               }) public static class TimePeriod
               {

                  @XmlElement(name = "FromDate", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime fromDate;
                  @XmlElement(name = "ToDate", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime toDate;

                  /**
                   * Gets the value of the fromDate property.
                   *
                   * @return
                   *     possible object is
                   *     {@link String }
                   *
                   */
                  public LocalDateTime getFromDate()
                  {
                     return fromDate;
                  }

                  /**
                   * Sets the value of the fromDate property.
                   *
                   * @param value
                   *     allowed object is
                   *     {@link String }
                   *
                   */
                  public void setFromDate(LocalDateTime value)
                  {
                     this.fromDate = value;
                  }

                  /**
                   * Gets the value of the toDate property.
                   *
                   * @return
                   *     possible object is
                   *     {@link String }
                   *
                   */
                  public LocalDateTime getToDate()
                  {
                     return toDate;
                  }

                  /**
                   * Sets the value of the toDate property.
                   *
                   * @param value
                   *     allowed object is
                   *     {@link String }
                   *
                   */
                  public void setToDate(LocalDateTime value)
                  {
                     this.toDate = value;
                  }

               }

               /**
                * <p>Java class for anonymous complex type.
                *
                * <p>The following schema fragment specifies the expected content contained within this class.
                *
                * <pre>
                * &lt;complexType&gt;
                *   &lt;complexContent&gt;
                *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                *       &lt;sequence&gt;
                *         &lt;element name="WorkingTime" maxOccurs="unbounded"&gt;
                *           &lt;complexType&gt;
                *             &lt;complexContent&gt;
                *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                *                 &lt;sequence&gt;
                *                   &lt;element name="FromTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                *                   &lt;element name="ToTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                *                 &lt;/sequence&gt;
                *               &lt;/restriction&gt;
                *             &lt;/complexContent&gt;
                *           &lt;/complexType&gt;
                *         &lt;/element&gt;
                *       &lt;/sequence&gt;
                *     &lt;/restriction&gt;
                *   &lt;/complexContent&gt;
                * &lt;/complexType&gt;
                * </pre>
                *
                *
                */
               @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
               {
                  "workingTime"
               }) public static class WorkingTimes
               {

                  @XmlElement(name = "WorkingTime", required = true) protected List<Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes.WorkingTime> workingTime;

                  /**
                   * Gets the value of the workingTime property.
                   *
                   * <p>
                   * This accessor method returns a reference to the live list,
                   * not a snapshot. Therefore any modification you make to the
                   * returned list will be present inside the Jakarta XML Binding object.
                   * This is why there is not a <CODE>set</CODE> method for the workingTime property.
                   *
                   * <p>
                   * For example, to add a new item, do as follows:
                   * <pre>
                   *    getWorkingTime().add(newItem);
                   * </pre>
                   *
                   *
                   * <p>
                   * Objects of the following type(s) are allowed in the list
                   * {@link Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes.WorkingTime }
                   *
                   *
                   */
                  public List<Document.Calendars.Calendar.Exceptions.Exception.WorkingTimes.WorkingTime> getWorkingTime()
                  {
                     if (workingTime == null)
                     {
                        workingTime = new ArrayList<>();
                     }
                     return this.workingTime;
                  }

                  /**
                   * <p>Java class for anonymous complex type.
                   *
                   * <p>The following schema fragment specifies the expected content contained within this class.
                   *
                   * <pre>
                   * &lt;complexType&gt;
                   *   &lt;complexContent&gt;
                   *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                   *       &lt;sequence&gt;
                   *         &lt;element name="FromTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                   *         &lt;element name="ToTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                   *       &lt;/sequence&gt;
                   *     &lt;/restriction&gt;
                   *   &lt;/complexContent&gt;
                   * &lt;/complexType&gt;
                   * </pre>
                   *
                   *
                   */
                  @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
                  {
                     "fromTime",
                     "toTime"
                  }) public static class WorkingTime
                  {

                     @XmlElement(name = "FromTime", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "time") protected LocalTime fromTime;
                     @XmlElement(name = "ToTime", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "time") protected LocalTime toTime;

                     /**
                      * Gets the value of the fromTime property.
                      *
                      * @return
                      *     possible object is
                      *     {@link String }
                      *
                      */
                     public LocalTime getFromTime()
                     {
                        return fromTime;
                     }

                     /**
                      * Sets the value of the fromTime property.
                      *
                      * @param value
                      *     allowed object is
                      *     {@link String }
                      *
                      */
                     public void setFromTime(LocalTime value)
                     {
                        this.fromTime = value;
                     }

                     /**
                      * Gets the value of the toTime property.
                      *
                      * @return
                      *     possible object is
                      *     {@link String }
                      *
                      */
                     public LocalTime getToTime()
                     {
                        return toTime;
                     }

                     /**
                      * Sets the value of the toTime property.
                      *
                      * @param value
                      *     allowed object is
                      *     {@link String }
                      *
                      */
                     public void setToTime(LocalTime value)
                     {
                        this.toTime = value;
                     }

                  }

               }

            }

         }

         /**
          * <p>Java class for anonymous complex type.
          *
          * <p>The following schema fragment specifies the expected content contained within this class.
          *
          * <pre>
          * &lt;complexType&gt;
          *   &lt;complexContent&gt;
          *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *       &lt;sequence&gt;
          *         &lt;element name="WeekDay" maxOccurs="unbounded"&gt;
          *           &lt;complexType&gt;
          *             &lt;complexContent&gt;
          *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                 &lt;sequence&gt;
          *                   &lt;element name="DayType" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
          *                   &lt;element name="DayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
          *                   &lt;element name="WorkingTimes"&gt;
          *                     &lt;complexType&gt;
          *                       &lt;complexContent&gt;
          *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                           &lt;sequence&gt;
          *                             &lt;element name="WorkingTime" maxOccurs="unbounded"&gt;
          *                               &lt;complexType&gt;
          *                                 &lt;complexContent&gt;
          *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                                     &lt;sequence&gt;
          *                                       &lt;element name="FromTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
          *                                       &lt;element name="ToTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
          *                                     &lt;/sequence&gt;
          *                                   &lt;/restriction&gt;
          *                                 &lt;/complexContent&gt;
          *                               &lt;/complexType&gt;
          *                             &lt;/element&gt;
          *                           &lt;/sequence&gt;
          *                         &lt;/restriction&gt;
          *                       &lt;/complexContent&gt;
          *                     &lt;/complexType&gt;
          *                   &lt;/element&gt;
          *                 &lt;/sequence&gt;
          *               &lt;/restriction&gt;
          *             &lt;/complexContent&gt;
          *           &lt;/complexType&gt;
          *         &lt;/element&gt;
          *       &lt;/sequence&gt;
          *     &lt;/restriction&gt;
          *   &lt;/complexContent&gt;
          * &lt;/complexType&gt;
          * </pre>
          *
          *
          */
         @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
         {
            "weekDay"
         }) public static class WeekDays
         {

            @XmlElement(name = "WeekDay", required = true) protected List<Document.Calendars.Calendar.WeekDays.WeekDay> weekDay;

            /**
             * Gets the value of the weekDay property.
             *
             * <p>
             * This accessor method returns a reference to the live list,
             * not a snapshot. Therefore any modification you make to the
             * returned list will be present inside the Jakarta XML Binding object.
             * This is why there is not a <CODE>set</CODE> method for the weekDay property.
             *
             * <p>
             * For example, to add a new item, do as follows:
             * <pre>
             *    getWeekDay().add(newItem);
             * </pre>
             *
             *
             * <p>
             * Objects of the following type(s) are allowed in the list
             * {@link Document.Calendars.Calendar.WeekDays.WeekDay }
             *
             *
             */
            public List<Document.Calendars.Calendar.WeekDays.WeekDay> getWeekDay()
            {
               if (weekDay == null)
               {
                  weekDay = new ArrayList<>();
               }
               return this.weekDay;
            }

            /**
             * <p>Java class for anonymous complex type.
             *
             * <p>The following schema fragment specifies the expected content contained within this class.
             *
             * <pre>
             * &lt;complexType&gt;
             *   &lt;complexContent&gt;
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *       &lt;sequence&gt;
             *         &lt;element name="DayType" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
             *         &lt;element name="DayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
             *         &lt;element name="WorkingTimes"&gt;
             *           &lt;complexType&gt;
             *             &lt;complexContent&gt;
             *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *                 &lt;sequence&gt;
             *                   &lt;element name="WorkingTime" maxOccurs="unbounded"&gt;
             *                     &lt;complexType&gt;
             *                       &lt;complexContent&gt;
             *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *                           &lt;sequence&gt;
             *                             &lt;element name="FromTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
             *                             &lt;element name="ToTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
             *                           &lt;/sequence&gt;
             *                         &lt;/restriction&gt;
             *                       &lt;/complexContent&gt;
             *                     &lt;/complexType&gt;
             *                   &lt;/element&gt;
             *                 &lt;/sequence&gt;
             *               &lt;/restriction&gt;
             *             &lt;/complexContent&gt;
             *           &lt;/complexType&gt;
             *         &lt;/element&gt;
             *       &lt;/sequence&gt;
             *     &lt;/restriction&gt;
             *   &lt;/complexContent&gt;
             * &lt;/complexType&gt;
             * </pre>
             *
             *
             */
            @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
            {
               "dayType",
               "dayWorking",
               "workingTimes"
            }) public static class WeekDay
            {

               @XmlElement(name = "DayType", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer dayType;
               @XmlElement(name = "DayWorking", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "boolean") protected Boolean dayWorking;
               @XmlElement(name = "WorkingTimes", required = true) protected Document.Calendars.Calendar.WeekDays.WeekDay.WorkingTimes workingTimes;

               /**
                * Gets the value of the dayType property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public Integer getDayType()
               {
                  return dayType;
               }

               /**
                * Sets the value of the dayType property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setDayType(Integer value)
               {
                  this.dayType = value;
               }

               /**
                * Gets the value of the dayWorking property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public Boolean isDayWorking()
               {
                  return dayWorking;
               }

               /**
                * Sets the value of the dayWorking property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setDayWorking(Boolean value)
               {
                  this.dayWorking = value;
               }

               /**
                * Gets the value of the workingTimes property.
                *
                * @return
                *     possible object is
                *     {@link Document.Calendars.Calendar.WeekDays.WeekDay.WorkingTimes }
                *
                */
               public Document.Calendars.Calendar.WeekDays.WeekDay.WorkingTimes getWorkingTimes()
               {
                  return workingTimes;
               }

               /**
                * Sets the value of the workingTimes property.
                *
                * @param value
                *     allowed object is
                *     {@link Document.Calendars.Calendar.WeekDays.WeekDay.WorkingTimes }
                *
                */
               public void setWorkingTimes(Document.Calendars.Calendar.WeekDays.WeekDay.WorkingTimes value)
               {
                  this.workingTimes = value;
               }

               /**
                * <p>Java class for anonymous complex type.
                *
                * <p>The following schema fragment specifies the expected content contained within this class.
                *
                * <pre>
                * &lt;complexType&gt;
                *   &lt;complexContent&gt;
                *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                *       &lt;sequence&gt;
                *         &lt;element name="WorkingTime" maxOccurs="unbounded"&gt;
                *           &lt;complexType&gt;
                *             &lt;complexContent&gt;
                *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                *                 &lt;sequence&gt;
                *                   &lt;element name="FromTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                *                   &lt;element name="ToTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                *                 &lt;/sequence&gt;
                *               &lt;/restriction&gt;
                *             &lt;/complexContent&gt;
                *           &lt;/complexType&gt;
                *         &lt;/element&gt;
                *       &lt;/sequence&gt;
                *     &lt;/restriction&gt;
                *   &lt;/complexContent&gt;
                * &lt;/complexType&gt;
                * </pre>
                *
                *
                */
               @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
               {
                  "workingTime"
               }) public static class WorkingTimes
               {

                  @XmlElement(name = "WorkingTime", required = true) protected List<Document.Calendars.Calendar.WeekDays.WeekDay.WorkingTimes.WorkingTime> workingTime;

                  /**
                   * Gets the value of the workingTime property.
                   *
                   * <p>
                   * This accessor method returns a reference to the live list,
                   * not a snapshot. Therefore any modification you make to the
                   * returned list will be present inside the Jakarta XML Binding object.
                   * This is why there is not a <CODE>set</CODE> method for the workingTime property.
                   *
                   * <p>
                   * For example, to add a new item, do as follows:
                   * <pre>
                   *    getWorkingTime().add(newItem);
                   * </pre>
                   *
                   *
                   * <p>
                   * Objects of the following type(s) are allowed in the list
                   * {@link Document.Calendars.Calendar.WeekDays.WeekDay.WorkingTimes.WorkingTime }
                   *
                   *
                   */
                  public List<Document.Calendars.Calendar.WeekDays.WeekDay.WorkingTimes.WorkingTime> getWorkingTime()
                  {
                     if (workingTime == null)
                     {
                        workingTime = new ArrayList<>();
                     }
                     return this.workingTime;
                  }

                  /**
                   * <p>Java class for anonymous complex type.
                   *
                   * <p>The following schema fragment specifies the expected content contained within this class.
                   *
                   * <pre>
                   * &lt;complexType&gt;
                   *   &lt;complexContent&gt;
                   *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                   *       &lt;sequence&gt;
                   *         &lt;element name="FromTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                   *         &lt;element name="ToTime" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                   *       &lt;/sequence&gt;
                   *     &lt;/restriction&gt;
                   *   &lt;/complexContent&gt;
                   * &lt;/complexType&gt;
                   * </pre>
                   *
                   *
                   */
                  @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
                  {
                     "fromTime",
                     "toTime"
                  }) public static class WorkingTime
                  {

                     @XmlElement(name = "FromTime", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "time") protected LocalTime fromTime;
                     @XmlElement(name = "ToTime", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter4.class) @XmlSchemaType(name = "time") protected LocalTime toTime;

                     /**
                      * Gets the value of the fromTime property.
                      *
                      * @return
                      *     possible object is
                      *     {@link String }
                      *
                      */
                     public LocalTime getFromTime()
                     {
                        return fromTime;
                     }

                     /**
                      * Sets the value of the fromTime property.
                      *
                      * @param value
                      *     allowed object is
                      *     {@link String }
                      *
                      */
                     public void setFromTime(LocalTime value)
                     {
                        this.fromTime = value;
                     }

                     /**
                      * Gets the value of the toTime property.
                      *
                      * @return
                      *     possible object is
                      *     {@link String }
                      *
                      */
                     public LocalTime getToTime()
                     {
                        return toTime;
                     }

                     /**
                      * Sets the value of the toTime property.
                      *
                      * @param value
                      *     allowed object is
                      *     {@link String }
                      *
                      */
                     public void setToTime(LocalTime value)
                     {
                        this.toTime = value;
                     }

                  }

               }

            }

         }

      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class CalendarUID
   {

      @XmlAttribute(name = "V") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer v;

      /**
       * Gets the value of the v property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Integer getV()
      {
         return v;
      }

      /**
       * Sets the value of the v property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setV(Integer value)
      {
         this.v = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class CreatedVersion
   {

      @XmlAttribute(name = "V") protected String v;

      /**
       * Gets the value of the v property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getV()
      {
         return v;
      }

      /**
       * Sets the value of the v property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setV(String value)
      {
         this.v = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class CreationDate
   {

      @XmlAttribute(name = "V") @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime v;

      /**
       * Gets the value of the v property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public LocalDateTime getV()
      {
         return v;
      }

      /**
       * Sets the value of the v property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setV(LocalDateTime value)
      {
         this.v = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Creator
   {

      @XmlAttribute(name = "V") protected String v;

      /**
       * Gets the value of the v property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getV()
      {
         return v;
      }

      /**
       * Sets the value of the v property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setV(String value)
      {
         this.v = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class DateFormat
   {

      @XmlAttribute(name = "V") protected String v;

      /**
       * Gets the value of the v property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getV()
      {
         return v;
      }

      /**
       * Sets the value of the v property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setV(String value)
      {
         this.v = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class DaysPerMonth
   {

      @XmlAttribute(name = "V") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer v;

      /**
       * Gets the value of the v property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Integer getV()
      {
         return v;
      }

      /**
       * Sets the value of the v property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setV(Integer value)
      {
         this.v = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class DPi
   {

      @XmlAttribute(name = "V") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer v;

      /**
       * Gets the value of the v property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Integer getV()
      {
         return v;
      }

      /**
       * Sets the value of the v property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setV(Integer value)
      {
         this.v = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element name="MajorUnit"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *         &lt;element name="MinorUnit"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *         &lt;element name="ProjectUnit"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *         &lt;element name="BaselineCost"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *         &lt;element name="StartDate"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *         &lt;element name="FinishDate"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *         &lt;element name="Auto"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *         &lt;element name="ThemeIndex"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *       &lt;/sequence&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "majorUnit",
      "minorUnit",
      "projectUnit",
      "baselineCost",
      "startDate",
      "finishDate",
      "auto",
      "themeIndex"
   }) public static class GanttOption
   {

      @XmlElement(name = "MajorUnit", required = true) protected Document.GanttOption.MajorUnit majorUnit;
      @XmlElement(name = "MinorUnit", required = true) protected Document.GanttOption.MinorUnit minorUnit;
      @XmlElement(name = "ProjectUnit", required = true) protected Document.GanttOption.ProjectUnit projectUnit;
      @XmlElement(name = "BaselineCost", required = true) protected Document.GanttOption.BaselineCost baselineCost;
      @XmlElement(name = "StartDate", required = true) protected Document.GanttOption.StartDate startDate;
      @XmlElement(name = "FinishDate", required = true) protected Document.GanttOption.FinishDate finishDate;
      @XmlElement(name = "Auto", required = true) protected Document.GanttOption.Auto auto;
      @XmlElement(name = "ThemeIndex", required = true) protected Document.GanttOption.ThemeIndex themeIndex;

      /**
       * Gets the value of the majorUnit property.
       *
       * @return
       *     possible object is
       *     {@link Document.GanttOption.MajorUnit }
       *
       */
      public Document.GanttOption.MajorUnit getMajorUnit()
      {
         return majorUnit;
      }

      /**
       * Sets the value of the majorUnit property.
       *
       * @param value
       *     allowed object is
       *     {@link Document.GanttOption.MajorUnit }
       *
       */
      public void setMajorUnit(Document.GanttOption.MajorUnit value)
      {
         this.majorUnit = value;
      }

      /**
       * Gets the value of the minorUnit property.
       *
       * @return
       *     possible object is
       *     {@link Document.GanttOption.MinorUnit }
       *
       */
      public Document.GanttOption.MinorUnit getMinorUnit()
      {
         return minorUnit;
      }

      /**
       * Sets the value of the minorUnit property.
       *
       * @param value
       *     allowed object is
       *     {@link Document.GanttOption.MinorUnit }
       *
       */
      public void setMinorUnit(Document.GanttOption.MinorUnit value)
      {
         this.minorUnit = value;
      }

      /**
       * Gets the value of the projectUnit property.
       *
       * @return
       *     possible object is
       *     {@link Document.GanttOption.ProjectUnit }
       *
       */
      public Document.GanttOption.ProjectUnit getProjectUnit()
      {
         return projectUnit;
      }

      /**
       * Sets the value of the projectUnit property.
       *
       * @param value
       *     allowed object is
       *     {@link Document.GanttOption.ProjectUnit }
       *
       */
      public void setProjectUnit(Document.GanttOption.ProjectUnit value)
      {
         this.projectUnit = value;
      }

      /**
       * Gets the value of the baselineCost property.
       *
       * @return
       *     possible object is
       *     {@link Document.GanttOption.BaselineCost }
       *
       */
      public Document.GanttOption.BaselineCost getBaselineCost()
      {
         return baselineCost;
      }

      /**
       * Sets the value of the baselineCost property.
       *
       * @param value
       *     allowed object is
       *     {@link Document.GanttOption.BaselineCost }
       *
       */
      public void setBaselineCost(Document.GanttOption.BaselineCost value)
      {
         this.baselineCost = value;
      }

      /**
       * Gets the value of the startDate property.
       *
       * @return
       *     possible object is
       *     {@link Document.GanttOption.StartDate }
       *
       */
      public Document.GanttOption.StartDate getStartDate()
      {
         return startDate;
      }

      /**
       * Sets the value of the startDate property.
       *
       * @param value
       *     allowed object is
       *     {@link Document.GanttOption.StartDate }
       *
       */
      public void setStartDate(Document.GanttOption.StartDate value)
      {
         this.startDate = value;
      }

      /**
       * Gets the value of the finishDate property.
       *
       * @return
       *     possible object is
       *     {@link Document.GanttOption.FinishDate }
       *
       */
      public Document.GanttOption.FinishDate getFinishDate()
      {
         return finishDate;
      }

      /**
       * Sets the value of the finishDate property.
       *
       * @param value
       *     allowed object is
       *     {@link Document.GanttOption.FinishDate }
       *
       */
      public void setFinishDate(Document.GanttOption.FinishDate value)
      {
         this.finishDate = value;
      }

      /**
       * Gets the value of the auto property.
       *
       * @return
       *     possible object is
       *     {@link Document.GanttOption.Auto }
       *
       */
      public Document.GanttOption.Auto getAuto()
      {
         return auto;
      }

      /**
       * Sets the value of the auto property.
       *
       * @param value
       *     allowed object is
       *     {@link Document.GanttOption.Auto }
       *
       */
      public void setAuto(Document.GanttOption.Auto value)
      {
         this.auto = value;
      }

      /**
       * Gets the value of the themeIndex property.
       *
       * @return
       *     possible object is
       *     {@link Document.GanttOption.ThemeIndex }
       *
       */
      public Document.GanttOption.ThemeIndex getThemeIndex()
      {
         return themeIndex;
      }

      /**
       * Sets the value of the themeIndex property.
       *
       * @param value
       *     allowed object is
       *     {@link Document.GanttOption.ThemeIndex }
       *
       */
      public void setThemeIndex(Document.GanttOption.ThemeIndex value)
      {
         this.themeIndex = value;
      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Auto
      {

         @XmlAttribute(name = "V") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer v;

         /**
          * Gets the value of the v property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getV()
         {
            return v;
         }

         /**
          * Sets the value of the v property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setV(Integer value)
         {
            this.v = value;
         }

      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class BaselineCost
      {

         @XmlAttribute(name = "V") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer v;

         /**
          * Gets the value of the v property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getV()
         {
            return v;
         }

         /**
          * Sets the value of the v property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setV(Integer value)
         {
            this.v = value;
         }

      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class FinishDate
      {

         @XmlAttribute(name = "V") @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime v;

         /**
          * Gets the value of the v property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public LocalDateTime getV()
         {
            return v;
         }

         /**
          * Sets the value of the v property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setV(LocalDateTime value)
         {
            this.v = value;
         }

      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class MajorUnit
      {

         @XmlAttribute(name = "V") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer v;

         /**
          * Gets the value of the v property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getV()
         {
            return v;
         }

         /**
          * Sets the value of the v property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setV(Integer value)
         {
            this.v = value;
         }

      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class MinorUnit
      {

         @XmlAttribute(name = "V") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer v;

         /**
          * Gets the value of the v property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getV()
         {
            return v;
         }

         /**
          * Sets the value of the v property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setV(Integer value)
         {
            this.v = value;
         }

      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class ProjectUnit
      {

         @XmlAttribute(name = "V") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer v;

         /**
          * Gets the value of the v property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getV()
         {
            return v;
         }

         /**
          * Sets the value of the v property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setV(Integer value)
         {
            this.v = value;
         }

      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class StartDate
      {

         @XmlAttribute(name = "V") @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime v;

         /**
          * Gets the value of the v property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public LocalDateTime getV()
         {
            return v;
         }

         /**
          * Sets the value of the v property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setV(LocalDateTime value)
         {
            this.v = value;
         }

      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class ThemeIndex
      {

         @XmlAttribute(name = "V") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer v;

         /**
          * Gets the value of the v property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getV()
         {
            return v;
         }

         /**
          * Sets the value of the v property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setV(Integer value)
         {
            this.v = value;
         }

      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class GanttViewSplitterRate
   {

      @XmlAttribute(name = "V") @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "double") protected Double v;

      /**
       * Gets the value of the v property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Double getV()
      {
         return v;
      }

      /**
       * Sets the value of the v property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setV(Double value)
      {
         this.v = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class LastSaved
   {

      @XmlAttribute(name = "V") @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime v;

      /**
       * Gets the value of the v property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public LocalDateTime getV()
      {
         return v;
      }

      /**
       * Sets the value of the v property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setV(LocalDateTime value)
      {
         this.v = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="line_roundsize" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="line_width" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *       &lt;attribute name="line_Color" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *       &lt;attribute name="line_index" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class LineStyleInformation
   {

      @XmlAttribute(name = "line_roundsize") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer lineRoundsize;
      @XmlAttribute(name = "line_width") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer lineWidth;
      @XmlAttribute(name = "line_Color") protected String lineColor;
      @XmlAttribute(name = "line_index") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer lineIndex;

      /**
       * Gets the value of the lineRoundsize property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Integer getLineRoundsize()
      {
         return lineRoundsize;
      }

      /**
       * Sets the value of the lineRoundsize property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setLineRoundsize(Integer value)
      {
         this.lineRoundsize = value;
      }

      /**
       * Gets the value of the lineWidth property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Integer getLineWidth()
      {
         return lineWidth;
      }

      /**
       * Sets the value of the lineWidth property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setLineWidth(Integer value)
      {
         this.lineWidth = value;
      }

      /**
       * Gets the value of the lineColor property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getLineColor()
      {
         return lineColor;
      }

      /**
       * Sets the value of the lineColor property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setLineColor(String value)
      {
         this.lineColor = value;
      }

      /**
       * Gets the value of the lineIndex property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Integer getLineIndex()
      {
         return lineIndex;
      }

      /**
       * Sets the value of the lineIndex property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setLineIndex(Integer value)
      {
         this.lineIndex = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class MinutesPerDay
   {

      @XmlAttribute(name = "V") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer v;

      /**
       * Gets the value of the v property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Integer getV()
      {
         return v;
      }

      /**
       * Sets the value of the v property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setV(Integer value)
      {
         this.v = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class MinutesPerWeek
   {

      @XmlAttribute(name = "V") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer v;

      /**
       * Gets the value of the v property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Integer getV()
      {
         return v;
      }

      /**
       * Sets the value of the v property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setV(Integer value)
      {
         this.v = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class MIsShowSpecificTime
   {

      @XmlAttribute(name = "V") @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "boolean") protected Boolean v;

      /**
       * Gets the value of the v property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Boolean isV()
      {
         return v;
      }

      /**
       * Sets the value of the v property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setV(Boolean value)
      {
         this.v = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Modifier
   {

      @XmlAttribute(name = "V") protected String v;

      /**
       * Gets the value of the v property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getV()
      {
         return v;
      }

      /**
       * Sets the value of the v property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setV(String value)
      {
         this.v = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element name="Column" maxOccurs="unbounded"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;attribute name="ID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="CostUnit" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="Cost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
    *                 &lt;attribute name="CostPer" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
    *                 &lt;attribute name="Type" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="OvertimeUnit" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="Notes" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="Unit" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="Email" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="OvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
    *                 &lt;attribute name="Name" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="Group" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *       &lt;/sequence&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "column"
   }) public static class ResourceInfo
   {

      @XmlElement(name = "Column", required = true) protected List<Document.ResourceInfo.Column> column;

      /**
       * Gets the value of the column property.
       *
       * <p>
       * This accessor method returns a reference to the live list,
       * not a snapshot. Therefore any modification you make to the
       * returned list will be present inside the Jakarta XML Binding object.
       * This is why there is not a <CODE>set</CODE> method for the column property.
       *
       * <p>
       * For example, to add a new item, do as follows:
       * <pre>
       *    getColumn().add(newItem);
       * </pre>
       *
       *
       * <p>
       * Objects of the following type(s) are allowed in the list
       * {@link Document.ResourceInfo.Column }
       *
       *
       */
      public List<Document.ResourceInfo.Column> getColumn()
      {
         if (column == null)
         {
            column = new ArrayList<>();
         }
         return this.column;
      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;attribute name="ID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="CostUnit" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="Cost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
       *       &lt;attribute name="CostPer" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
       *       &lt;attribute name="Type" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="OvertimeUnit" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="Notes" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="Unit" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="Email" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="OvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
       *       &lt;attribute name="Name" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="Group" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Column
      {

         @XmlAttribute(name = "ID") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer id;
         @XmlAttribute(name = "CostUnit") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer costUnit;
         @XmlAttribute(name = "Cost") @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "double") protected Double cost;
         @XmlAttribute(name = "CostPer") @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "double") protected Double costPer;
         @XmlAttribute(name = "Type") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer type;
         @XmlAttribute(name = "OvertimeUnit") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer overtimeUnit;
         @XmlAttribute(name = "Notes") protected String notes;
         @XmlAttribute(name = "Unit") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer unit;
         @XmlAttribute(name = "Email") protected String email;
         @XmlAttribute(name = "OvertimeCost") @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "double") protected Double overtimeCost;
         @XmlAttribute(name = "Name") protected String name;
         @XmlAttribute(name = "Group") protected String group;

         /**
          * Gets the value of the id property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getID()
         {
            return id;
         }

         /**
          * Sets the value of the id property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setID(Integer value)
         {
            this.id = value;
         }

         /**
          * Gets the value of the costUnit property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getCostUnit()
         {
            return costUnit;
         }

         /**
          * Sets the value of the costUnit property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setCostUnit(Integer value)
         {
            this.costUnit = value;
         }

         /**
          * Gets the value of the cost property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Double getCost()
         {
            return cost;
         }

         /**
          * Sets the value of the cost property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setCost(Double value)
         {
            this.cost = value;
         }

         /**
          * Gets the value of the costPer property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Double getCostPer()
         {
            return costPer;
         }

         /**
          * Sets the value of the costPer property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setCostPer(Double value)
         {
            this.costPer = value;
         }

         /**
          * Gets the value of the type property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getType()
         {
            return type;
         }

         /**
          * Sets the value of the type property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setType(Integer value)
         {
            this.type = value;
         }

         /**
          * Gets the value of the overtimeUnit property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getOvertimeUnit()
         {
            return overtimeUnit;
         }

         /**
          * Sets the value of the overtimeUnit property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setOvertimeUnit(Integer value)
         {
            this.overtimeUnit = value;
         }

         /**
          * Gets the value of the notes property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getNotes()
         {
            return notes;
         }

         /**
          * Sets the value of the notes property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setNotes(String value)
         {
            this.notes = value;
         }

         /**
          * Gets the value of the unit property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getUnit()
         {
            return unit;
         }

         /**
          * Sets the value of the unit property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setUnit(Integer value)
         {
            this.unit = value;
         }

         /**
          * Gets the value of the email property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getEmail()
         {
            return email;
         }

         /**
          * Sets the value of the email property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setEmail(String value)
         {
            this.email = value;
         }

         /**
          * Gets the value of the overtimeCost property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Double getOvertimeCost()
         {
            return overtimeCost;
         }

         /**
          * Sets the value of the overtimeCost property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setOvertimeCost(Double value)
         {
            this.overtimeCost = value;
         }

         /**
          * Gets the value of the name property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getName()
         {
            return name;
         }

         /**
          * Sets the value of the name property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setName(String value)
         {
            this.name = value;
         }

         /**
          * Gets the value of the group property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getGroup()
         {
            return group;
         }

         /**
          * Sets the value of the group property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setGroup(String value)
         {
            this.group = value;
         }

      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element name="ColumnList"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;sequence&gt;
    *                   &lt;element name="Column" maxOccurs="unbounded"&gt;
    *                     &lt;complexType&gt;
    *                       &lt;complexContent&gt;
    *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                           &lt;sequence&gt;
    *                             &lt;element name="Text"&gt;
    *                               &lt;complexType&gt;
    *                                 &lt;complexContent&gt;
    *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                     &lt;sequence&gt;
    *                                       &lt;element name="Varient" type="{http://www.w3.org/2001/XMLSchema}anyType"/&gt;
    *                                       &lt;element name="TextBlock"&gt;
    *                                         &lt;complexType&gt;
    *                                           &lt;complexContent&gt;
    *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                               &lt;sequence&gt;
    *                                                 &lt;element name="Character"&gt;
    *                                                   &lt;complexType&gt;
    *                                                     &lt;complexContent&gt;
    *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                                         &lt;attribute name="Family" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                                                         &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                                                         &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                                                       &lt;/restriction&gt;
    *                                                     &lt;/complexContent&gt;
    *                                                   &lt;/complexType&gt;
    *                                                 &lt;/element&gt;
    *                                                 &lt;element name="Paragraph"&gt;
    *                                                   &lt;complexType&gt;
    *                                                     &lt;complexContent&gt;
    *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                                         &lt;attribute name="Align" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                                                         &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                                                         &lt;attribute name="SpLine" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                                                       &lt;/restriction&gt;
    *                                                     &lt;/complexContent&gt;
    *                                                   &lt;/complexType&gt;
    *                                                 &lt;/element&gt;
    *                                                 &lt;element name="WrapMode"&gt;
    *                                                   &lt;complexType&gt;
    *                                                     &lt;complexContent&gt;
    *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                                         &lt;attribute name="Value" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                                                       &lt;/restriction&gt;
    *                                                     &lt;/complexContent&gt;
    *                                                   &lt;/complexType&gt;
    *                                                 &lt;/element&gt;
    *                                                 &lt;element name="FillColor"&gt;
    *                                                   &lt;complexType&gt;
    *                                                     &lt;complexContent&gt;
    *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                                         &lt;attribute name="Color" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                                                       &lt;/restriction&gt;
    *                                                     &lt;/complexContent&gt;
    *                                                   &lt;/complexType&gt;
    *                                                 &lt;/element&gt;
    *                                                 &lt;element name="Color"&gt;
    *                                                   &lt;complexType&gt;
    *                                                     &lt;complexContent&gt;
    *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                                         &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                                                       &lt;/restriction&gt;
    *                                                     &lt;/complexContent&gt;
    *                                                   &lt;/complexType&gt;
    *                                                 &lt;/element&gt;
    *                                               &lt;/sequence&gt;
    *                                               &lt;attribute name="VAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                                               &lt;attribute name="HAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                                               &lt;attribute name="TextFormatMask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                                             &lt;/restriction&gt;
    *                                           &lt;/complexContent&gt;
    *                                         &lt;/complexType&gt;
    *                                       &lt;/element&gt;
    *                                     &lt;/sequence&gt;
    *                                     &lt;attribute name="FieldType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                                     &lt;attribute name="PlainText" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                                     &lt;attribute name="FieldID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                                     &lt;attribute name="DataType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                                   &lt;/restriction&gt;
    *                                 &lt;/complexContent&gt;
    *                               &lt;/complexType&gt;
    *                             &lt;/element&gt;
    *                           &lt;/sequence&gt;
    *                           &lt;attribute name="FieldType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                           &lt;attribute name="Key" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                           &lt;attribute name="IsHide" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
    *                           &lt;attribute name="FilterType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                           &lt;attribute name="ColSelectALL" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                           &lt;attribute name="Width" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                           &lt;attribute name="SortStatus" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                         &lt;/restriction&gt;
    *                       &lt;/complexContent&gt;
    *                     &lt;/complexType&gt;
    *                   &lt;/element&gt;
    *                 &lt;/sequence&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *       &lt;/sequence&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "columnList"
   }) public static class RowColumn
   {

      @XmlElement(name = "ColumnList", required = true) protected Document.RowColumn.ColumnList columnList;

      /**
       * Gets the value of the columnList property.
       *
       * @return
       *     possible object is
       *     {@link Document.RowColumn.ColumnList }
       *
       */
      public Document.RowColumn.ColumnList getColumnList()
      {
         return columnList;
      }

      /**
       * Sets the value of the columnList property.
       *
       * @param value
       *     allowed object is
       *     {@link Document.RowColumn.ColumnList }
       *
       */
      public void setColumnList(Document.RowColumn.ColumnList value)
      {
         this.columnList = value;
      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;sequence&gt;
       *         &lt;element name="Column" maxOccurs="unbounded"&gt;
       *           &lt;complexType&gt;
       *             &lt;complexContent&gt;
       *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                 &lt;sequence&gt;
       *                   &lt;element name="Text"&gt;
       *                     &lt;complexType&gt;
       *                       &lt;complexContent&gt;
       *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                           &lt;sequence&gt;
       *                             &lt;element name="Varient" type="{http://www.w3.org/2001/XMLSchema}anyType"/&gt;
       *                             &lt;element name="TextBlock"&gt;
       *                               &lt;complexType&gt;
       *                                 &lt;complexContent&gt;
       *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                                     &lt;sequence&gt;
       *                                       &lt;element name="Character"&gt;
       *                                         &lt;complexType&gt;
       *                                           &lt;complexContent&gt;
       *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                                               &lt;attribute name="Family" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *                                               &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                                               &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                                             &lt;/restriction&gt;
       *                                           &lt;/complexContent&gt;
       *                                         &lt;/complexType&gt;
       *                                       &lt;/element&gt;
       *                                       &lt;element name="Paragraph"&gt;
       *                                         &lt;complexType&gt;
       *                                           &lt;complexContent&gt;
       *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                                               &lt;attribute name="Align" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                                               &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                                               &lt;attribute name="SpLine" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                                             &lt;/restriction&gt;
       *                                           &lt;/complexContent&gt;
       *                                         &lt;/complexType&gt;
       *                                       &lt;/element&gt;
       *                                       &lt;element name="WrapMode"&gt;
       *                                         &lt;complexType&gt;
       *                                           &lt;complexContent&gt;
       *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                                               &lt;attribute name="Value" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                                             &lt;/restriction&gt;
       *                                           &lt;/complexContent&gt;
       *                                         &lt;/complexType&gt;
       *                                       &lt;/element&gt;
       *                                       &lt;element name="FillColor"&gt;
       *                                         &lt;complexType&gt;
       *                                           &lt;complexContent&gt;
       *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                                               &lt;attribute name="Color" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *                                             &lt;/restriction&gt;
       *                                           &lt;/complexContent&gt;
       *                                         &lt;/complexType&gt;
       *                                       &lt;/element&gt;
       *                                       &lt;element name="Color"&gt;
       *                                         &lt;complexType&gt;
       *                                           &lt;complexContent&gt;
       *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                                               &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *                                             &lt;/restriction&gt;
       *                                           &lt;/complexContent&gt;
       *                                         &lt;/complexType&gt;
       *                                       &lt;/element&gt;
       *                                     &lt;/sequence&gt;
       *                                     &lt;attribute name="VAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *                                     &lt;attribute name="HAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *                                     &lt;attribute name="TextFormatMask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                                   &lt;/restriction&gt;
       *                                 &lt;/complexContent&gt;
       *                               &lt;/complexType&gt;
       *                             &lt;/element&gt;
       *                           &lt;/sequence&gt;
       *                           &lt;attribute name="FieldType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                           &lt;attribute name="PlainText" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *                           &lt;attribute name="FieldID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                           &lt;attribute name="DataType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                         &lt;/restriction&gt;
       *                       &lt;/complexContent&gt;
       *                     &lt;/complexType&gt;
       *                   &lt;/element&gt;
       *                 &lt;/sequence&gt;
       *                 &lt;attribute name="FieldType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                 &lt;attribute name="Key" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                 &lt;attribute name="IsHide" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
       *                 &lt;attribute name="FilterType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                 &lt;attribute name="ColSelectALL" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                 &lt;attribute name="Width" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                 &lt;attribute name="SortStatus" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *               &lt;/restriction&gt;
       *             &lt;/complexContent&gt;
       *           &lt;/complexType&gt;
       *         &lt;/element&gt;
       *       &lt;/sequence&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
      {
         "column"
      }) public static class ColumnList
      {

         @XmlElement(name = "Column", required = true) protected List<Document.RowColumn.ColumnList.Column> column;

         /**
          * Gets the value of the column property.
          *
          * <p>
          * This accessor method returns a reference to the live list,
          * not a snapshot. Therefore any modification you make to the
          * returned list will be present inside the Jakarta XML Binding object.
          * This is why there is not a <CODE>set</CODE> method for the column property.
          *
          * <p>
          * For example, to add a new item, do as follows:
          * <pre>
          *    getColumn().add(newItem);
          * </pre>
          *
          *
          * <p>
          * Objects of the following type(s) are allowed in the list
          * {@link Document.RowColumn.ColumnList.Column }
          *
          *
          */
         public List<Document.RowColumn.ColumnList.Column> getColumn()
         {
            if (column == null)
            {
               column = new ArrayList<>();
            }
            return this.column;
         }

         /**
          * <p>Java class for anonymous complex type.
          *
          * <p>The following schema fragment specifies the expected content contained within this class.
          *
          * <pre>
          * &lt;complexType&gt;
          *   &lt;complexContent&gt;
          *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *       &lt;sequence&gt;
          *         &lt;element name="Text"&gt;
          *           &lt;complexType&gt;
          *             &lt;complexContent&gt;
          *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                 &lt;sequence&gt;
          *                   &lt;element name="Varient" type="{http://www.w3.org/2001/XMLSchema}anyType"/&gt;
          *                   &lt;element name="TextBlock"&gt;
          *                     &lt;complexType&gt;
          *                       &lt;complexContent&gt;
          *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                           &lt;sequence&gt;
          *                             &lt;element name="Character"&gt;
          *                               &lt;complexType&gt;
          *                                 &lt;complexContent&gt;
          *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                                     &lt;attribute name="Family" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
          *                                     &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *                                     &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *                                   &lt;/restriction&gt;
          *                                 &lt;/complexContent&gt;
          *                               &lt;/complexType&gt;
          *                             &lt;/element&gt;
          *                             &lt;element name="Paragraph"&gt;
          *                               &lt;complexType&gt;
          *                                 &lt;complexContent&gt;
          *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                                     &lt;attribute name="Align" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *                                     &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *                                     &lt;attribute name="SpLine" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *                                   &lt;/restriction&gt;
          *                                 &lt;/complexContent&gt;
          *                               &lt;/complexType&gt;
          *                             &lt;/element&gt;
          *                             &lt;element name="WrapMode"&gt;
          *                               &lt;complexType&gt;
          *                                 &lt;complexContent&gt;
          *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                                     &lt;attribute name="Value" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *                                   &lt;/restriction&gt;
          *                                 &lt;/complexContent&gt;
          *                               &lt;/complexType&gt;
          *                             &lt;/element&gt;
          *                             &lt;element name="FillColor"&gt;
          *                               &lt;complexType&gt;
          *                                 &lt;complexContent&gt;
          *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                                     &lt;attribute name="Color" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
          *                                   &lt;/restriction&gt;
          *                                 &lt;/complexContent&gt;
          *                               &lt;/complexType&gt;
          *                             &lt;/element&gt;
          *                             &lt;element name="Color"&gt;
          *                               &lt;complexType&gt;
          *                                 &lt;complexContent&gt;
          *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                                     &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
          *                                   &lt;/restriction&gt;
          *                                 &lt;/complexContent&gt;
          *                               &lt;/complexType&gt;
          *                             &lt;/element&gt;
          *                           &lt;/sequence&gt;
          *                           &lt;attribute name="VAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
          *                           &lt;attribute name="HAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
          *                           &lt;attribute name="TextFormatMask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *                         &lt;/restriction&gt;
          *                       &lt;/complexContent&gt;
          *                     &lt;/complexType&gt;
          *                   &lt;/element&gt;
          *                 &lt;/sequence&gt;
          *                 &lt;attribute name="FieldType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *                 &lt;attribute name="PlainText" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
          *                 &lt;attribute name="FieldID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *                 &lt;attribute name="DataType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *               &lt;/restriction&gt;
          *             &lt;/complexContent&gt;
          *           &lt;/complexType&gt;
          *         &lt;/element&gt;
          *       &lt;/sequence&gt;
          *       &lt;attribute name="FieldType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *       &lt;attribute name="Key" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *       &lt;attribute name="IsHide" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
          *       &lt;attribute name="FilterType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *       &lt;attribute name="ColSelectALL" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *       &lt;attribute name="Width" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *       &lt;attribute name="SortStatus" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *     &lt;/restriction&gt;
          *   &lt;/complexContent&gt;
          * &lt;/complexType&gt;
          * </pre>
          *
          *
          */
         @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
         {
            "text"
         }) public static class Column
         {

            @XmlElement(name = "Text", required = true) protected Document.RowColumn.ColumnList.Column.Text text;
            @XmlAttribute(name = "FieldType") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer fieldType;
            @XmlAttribute(name = "Key") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer key;
            @XmlAttribute(name = "IsHide") @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "boolean") protected Boolean isHide;
            @XmlAttribute(name = "FilterType") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer filterType;
            @XmlAttribute(name = "ColSelectALL") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer colSelectALL;
            @XmlAttribute(name = "Width") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer width;
            @XmlAttribute(name = "SortStatus") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer sortStatus;

            /**
             * Gets the value of the text property.
             *
             * @return
             *     possible object is
             *     {@link Document.RowColumn.ColumnList.Column.Text }
             *
             */
            public Document.RowColumn.ColumnList.Column.Text getText()
            {
               return text;
            }

            /**
             * Sets the value of the text property.
             *
             * @param value
             *     allowed object is
             *     {@link Document.RowColumn.ColumnList.Column.Text }
             *
             */
            public void setText(Document.RowColumn.ColumnList.Column.Text value)
            {
               this.text = value;
            }

            /**
             * Gets the value of the fieldType property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Integer getFieldType()
            {
               return fieldType;
            }

            /**
             * Sets the value of the fieldType property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setFieldType(Integer value)
            {
               this.fieldType = value;
            }

            /**
             * Gets the value of the key property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Integer getKey()
            {
               return key;
            }

            /**
             * Sets the value of the key property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setKey(Integer value)
            {
               this.key = value;
            }

            /**
             * Gets the value of the isHide property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Boolean isIsHide()
            {
               return isHide;
            }

            /**
             * Sets the value of the isHide property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setIsHide(Boolean value)
            {
               this.isHide = value;
            }

            /**
             * Gets the value of the filterType property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Integer getFilterType()
            {
               return filterType;
            }

            /**
             * Sets the value of the filterType property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setFilterType(Integer value)
            {
               this.filterType = value;
            }

            /**
             * Gets the value of the colSelectALL property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Integer getColSelectALL()
            {
               return colSelectALL;
            }

            /**
             * Sets the value of the colSelectALL property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setColSelectALL(Integer value)
            {
               this.colSelectALL = value;
            }

            /**
             * Gets the value of the width property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Integer getWidth()
            {
               return width;
            }

            /**
             * Sets the value of the width property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setWidth(Integer value)
            {
               this.width = value;
            }

            /**
             * Gets the value of the sortStatus property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Integer getSortStatus()
            {
               return sortStatus;
            }

            /**
             * Sets the value of the sortStatus property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setSortStatus(Integer value)
            {
               this.sortStatus = value;
            }

            /**
             * <p>Java class for anonymous complex type.
             *
             * <p>The following schema fragment specifies the expected content contained within this class.
             *
             * <pre>
             * &lt;complexType&gt;
             *   &lt;complexContent&gt;
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *       &lt;sequence&gt;
             *         &lt;element name="Varient" type="{http://www.w3.org/2001/XMLSchema}anyType"/&gt;
             *         &lt;element name="TextBlock"&gt;
             *           &lt;complexType&gt;
             *             &lt;complexContent&gt;
             *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *                 &lt;sequence&gt;
             *                   &lt;element name="Character"&gt;
             *                     &lt;complexType&gt;
             *                       &lt;complexContent&gt;
             *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *                           &lt;attribute name="Family" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
             *                           &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
             *                           &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
             *                         &lt;/restriction&gt;
             *                       &lt;/complexContent&gt;
             *                     &lt;/complexType&gt;
             *                   &lt;/element&gt;
             *                   &lt;element name="Paragraph"&gt;
             *                     &lt;complexType&gt;
             *                       &lt;complexContent&gt;
             *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *                           &lt;attribute name="Align" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
             *                           &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
             *                           &lt;attribute name="SpLine" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
             *                         &lt;/restriction&gt;
             *                       &lt;/complexContent&gt;
             *                     &lt;/complexType&gt;
             *                   &lt;/element&gt;
             *                   &lt;element name="WrapMode"&gt;
             *                     &lt;complexType&gt;
             *                       &lt;complexContent&gt;
             *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *                           &lt;attribute name="Value" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
             *                         &lt;/restriction&gt;
             *                       &lt;/complexContent&gt;
             *                     &lt;/complexType&gt;
             *                   &lt;/element&gt;
             *                   &lt;element name="FillColor"&gt;
             *                     &lt;complexType&gt;
             *                       &lt;complexContent&gt;
             *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *                           &lt;attribute name="Color" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
             *                         &lt;/restriction&gt;
             *                       &lt;/complexContent&gt;
             *                     &lt;/complexType&gt;
             *                   &lt;/element&gt;
             *                   &lt;element name="Color"&gt;
             *                     &lt;complexType&gt;
             *                       &lt;complexContent&gt;
             *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *                           &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
             *                         &lt;/restriction&gt;
             *                       &lt;/complexContent&gt;
             *                     &lt;/complexType&gt;
             *                   &lt;/element&gt;
             *                 &lt;/sequence&gt;
             *                 &lt;attribute name="VAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
             *                 &lt;attribute name="HAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
             *                 &lt;attribute name="TextFormatMask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
             *               &lt;/restriction&gt;
             *             &lt;/complexContent&gt;
             *           &lt;/complexType&gt;
             *         &lt;/element&gt;
             *       &lt;/sequence&gt;
             *       &lt;attribute name="FieldType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
             *       &lt;attribute name="PlainText" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
             *       &lt;attribute name="FieldID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
             *       &lt;attribute name="DataType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
             *     &lt;/restriction&gt;
             *   &lt;/complexContent&gt;
             * &lt;/complexType&gt;
             * </pre>
             *
             *
             */
            @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
            {
               "varient",
               "textBlock"
            }) public static class Text
            {

               @XmlElement(name = "Varient", required = true) protected Object varient;
               @XmlElement(name = "TextBlock", required = true) protected Document.RowColumn.ColumnList.Column.Text.TextBlock textBlock;
               @XmlAttribute(name = "FieldType") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer fieldType;
               @XmlAttribute(name = "PlainText") protected String plainText;
               @XmlAttribute(name = "FieldID") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer fieldID;
               @XmlAttribute(name = "DataType") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer dataType;

               /**
                * Gets the value of the varient property.
                *
                * @return
                *     possible object is
                *     {@link Object }
                *
                */
               public Object getVarient()
               {
                  return varient;
               }

               /**
                * Sets the value of the varient property.
                *
                * @param value
                *     allowed object is
                *     {@link Object }
                *
                */
               public void setVarient(Object value)
               {
                  this.varient = value;
               }

               /**
                * Gets the value of the textBlock property.
                *
                * @return
                *     possible object is
                *     {@link Document.RowColumn.ColumnList.Column.Text.TextBlock }
                *
                */
               public Document.RowColumn.ColumnList.Column.Text.TextBlock getTextBlock()
               {
                  return textBlock;
               }

               /**
                * Sets the value of the textBlock property.
                *
                * @param value
                *     allowed object is
                *     {@link Document.RowColumn.ColumnList.Column.Text.TextBlock }
                *
                */
               public void setTextBlock(Document.RowColumn.ColumnList.Column.Text.TextBlock value)
               {
                  this.textBlock = value;
               }

               /**
                * Gets the value of the fieldType property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public Integer getFieldType()
               {
                  return fieldType;
               }

               /**
                * Sets the value of the fieldType property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setFieldType(Integer value)
               {
                  this.fieldType = value;
               }

               /**
                * Gets the value of the plainText property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public String getPlainText()
               {
                  return plainText;
               }

               /**
                * Sets the value of the plainText property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setPlainText(String value)
               {
                  this.plainText = value;
               }

               /**
                * Gets the value of the fieldID property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public Integer getFieldID()
               {
                  return fieldID;
               }

               /**
                * Sets the value of the fieldID property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setFieldID(Integer value)
               {
                  this.fieldID = value;
               }

               /**
                * Gets the value of the dataType property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public Integer getDataType()
               {
                  return dataType;
               }

               /**
                * Sets the value of the dataType property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setDataType(Integer value)
               {
                  this.dataType = value;
               }

               /**
                * <p>Java class for anonymous complex type.
                *
                * <p>The following schema fragment specifies the expected content contained within this class.
                *
                * <pre>
                * &lt;complexType&gt;
                *   &lt;complexContent&gt;
                *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                *       &lt;sequence&gt;
                *         &lt;element name="Character"&gt;
                *           &lt;complexType&gt;
                *             &lt;complexContent&gt;
                *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                *                 &lt;attribute name="Family" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                *                 &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                *                 &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                *               &lt;/restriction&gt;
                *             &lt;/complexContent&gt;
                *           &lt;/complexType&gt;
                *         &lt;/element&gt;
                *         &lt;element name="Paragraph"&gt;
                *           &lt;complexType&gt;
                *             &lt;complexContent&gt;
                *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                *                 &lt;attribute name="Align" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                *                 &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                *                 &lt;attribute name="SpLine" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                *               &lt;/restriction&gt;
                *             &lt;/complexContent&gt;
                *           &lt;/complexType&gt;
                *         &lt;/element&gt;
                *         &lt;element name="WrapMode"&gt;
                *           &lt;complexType&gt;
                *             &lt;complexContent&gt;
                *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                *                 &lt;attribute name="Value" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                *               &lt;/restriction&gt;
                *             &lt;/complexContent&gt;
                *           &lt;/complexType&gt;
                *         &lt;/element&gt;
                *         &lt;element name="FillColor"&gt;
                *           &lt;complexType&gt;
                *             &lt;complexContent&gt;
                *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                *                 &lt;attribute name="Color" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                *               &lt;/restriction&gt;
                *             &lt;/complexContent&gt;
                *           &lt;/complexType&gt;
                *         &lt;/element&gt;
                *         &lt;element name="Color"&gt;
                *           &lt;complexType&gt;
                *             &lt;complexContent&gt;
                *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                *                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                *               &lt;/restriction&gt;
                *             &lt;/complexContent&gt;
                *           &lt;/complexType&gt;
                *         &lt;/element&gt;
                *       &lt;/sequence&gt;
                *       &lt;attribute name="VAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                *       &lt;attribute name="HAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                *       &lt;attribute name="TextFormatMask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                *     &lt;/restriction&gt;
                *   &lt;/complexContent&gt;
                * &lt;/complexType&gt;
                * </pre>
                *
                *
                */
               @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
               {
                  "character",
                  "paragraph",
                  "wrapMode",
                  "fillColor",
                  "color"
               }) public static class TextBlock
               {

                  @XmlElement(name = "Character", required = true) protected Document.RowColumn.ColumnList.Column.Text.TextBlock.Character character;
                  @XmlElement(name = "Paragraph", required = true) protected Document.RowColumn.ColumnList.Column.Text.TextBlock.Paragraph paragraph;
                  @XmlElement(name = "WrapMode", required = true) protected Document.RowColumn.ColumnList.Column.Text.TextBlock.WrapMode wrapMode;
                  @XmlElement(name = "FillColor", required = true) protected Document.RowColumn.ColumnList.Column.Text.TextBlock.FillColor fillColor;
                  @XmlElement(name = "Color", required = true) protected Document.RowColumn.ColumnList.Column.Text.TextBlock.Color color;
                  @XmlAttribute(name = "VAlign") protected String vAlign;
                  @XmlAttribute(name = "HAlign") protected String hAlign;
                  @XmlAttribute(name = "TextFormatMask") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer textFormatMask;

                  /**
                   * Gets the value of the character property.
                   *
                   * @return
                   *     possible object is
                   *     {@link Document.RowColumn.ColumnList.Column.Text.TextBlock.Character }
                   *
                   */
                  public Document.RowColumn.ColumnList.Column.Text.TextBlock.Character getCharacter()
                  {
                     return character;
                  }

                  /**
                   * Sets the value of the character property.
                   *
                   * @param value
                   *     allowed object is
                   *     {@link Document.RowColumn.ColumnList.Column.Text.TextBlock.Character }
                   *
                   */
                  public void setCharacter(Document.RowColumn.ColumnList.Column.Text.TextBlock.Character value)
                  {
                     this.character = value;
                  }

                  /**
                   * Gets the value of the paragraph property.
                   *
                   * @return
                   *     possible object is
                   *     {@link Document.RowColumn.ColumnList.Column.Text.TextBlock.Paragraph }
                   *
                   */
                  public Document.RowColumn.ColumnList.Column.Text.TextBlock.Paragraph getParagraph()
                  {
                     return paragraph;
                  }

                  /**
                   * Sets the value of the paragraph property.
                   *
                   * @param value
                   *     allowed object is
                   *     {@link Document.RowColumn.ColumnList.Column.Text.TextBlock.Paragraph }
                   *
                   */
                  public void setParagraph(Document.RowColumn.ColumnList.Column.Text.TextBlock.Paragraph value)
                  {
                     this.paragraph = value;
                  }

                  /**
                   * Gets the value of the wrapMode property.
                   *
                   * @return
                   *     possible object is
                   *     {@link Document.RowColumn.ColumnList.Column.Text.TextBlock.WrapMode }
                   *
                   */
                  public Document.RowColumn.ColumnList.Column.Text.TextBlock.WrapMode getWrapMode()
                  {
                     return wrapMode;
                  }

                  /**
                   * Sets the value of the wrapMode property.
                   *
                   * @param value
                   *     allowed object is
                   *     {@link Document.RowColumn.ColumnList.Column.Text.TextBlock.WrapMode }
                   *
                   */
                  public void setWrapMode(Document.RowColumn.ColumnList.Column.Text.TextBlock.WrapMode value)
                  {
                     this.wrapMode = value;
                  }

                  /**
                   * Gets the value of the fillColor property.
                   *
                   * @return
                   *     possible object is
                   *     {@link Document.RowColumn.ColumnList.Column.Text.TextBlock.FillColor }
                   *
                   */
                  public Document.RowColumn.ColumnList.Column.Text.TextBlock.FillColor getFillColor()
                  {
                     return fillColor;
                  }

                  /**
                   * Sets the value of the fillColor property.
                   *
                   * @param value
                   *     allowed object is
                   *     {@link Document.RowColumn.ColumnList.Column.Text.TextBlock.FillColor }
                   *
                   */
                  public void setFillColor(Document.RowColumn.ColumnList.Column.Text.TextBlock.FillColor value)
                  {
                     this.fillColor = value;
                  }

                  /**
                   * Gets the value of the color property.
                   *
                   * @return
                   *     possible object is
                   *     {@link Document.RowColumn.ColumnList.Column.Text.TextBlock.Color }
                   *
                   */
                  public Document.RowColumn.ColumnList.Column.Text.TextBlock.Color getColor()
                  {
                     return color;
                  }

                  /**
                   * Sets the value of the color property.
                   *
                   * @param value
                   *     allowed object is
                   *     {@link Document.RowColumn.ColumnList.Column.Text.TextBlock.Color }
                   *
                   */
                  public void setColor(Document.RowColumn.ColumnList.Column.Text.TextBlock.Color value)
                  {
                     this.color = value;
                  }

                  /**
                   * Gets the value of the vAlign property.
                   *
                   * @return
                   *     possible object is
                   *     {@link String }
                   *
                   */
                  public String getVAlign()
                  {
                     return vAlign;
                  }

                  /**
                   * Sets the value of the vAlign property.
                   *
                   * @param value
                   *     allowed object is
                   *     {@link String }
                   *
                   */
                  public void setVAlign(String value)
                  {
                     this.vAlign = value;
                  }

                  /**
                   * Gets the value of the hAlign property.
                   *
                   * @return
                   *     possible object is
                   *     {@link String }
                   *
                   */
                  public String getHAlign()
                  {
                     return hAlign;
                  }

                  /**
                   * Sets the value of the hAlign property.
                   *
                   * @param value
                   *     allowed object is
                   *     {@link String }
                   *
                   */
                  public void setHAlign(String value)
                  {
                     this.hAlign = value;
                  }

                  /**
                   * Gets the value of the textFormatMask property.
                   *
                   * @return
                   *     possible object is
                   *     {@link String }
                   *
                   */
                  public Integer getTextFormatMask()
                  {
                     return textFormatMask;
                  }

                  /**
                   * Sets the value of the textFormatMask property.
                   *
                   * @param value
                   *     allowed object is
                   *     {@link String }
                   *
                   */
                  public void setTextFormatMask(Integer value)
                  {
                     this.textFormatMask = value;
                  }

                  /**
                   * <p>Java class for anonymous complex type.
                   *
                   * <p>The following schema fragment specifies the expected content contained within this class.
                   *
                   * <pre>
                   * &lt;complexType&gt;
                   *   &lt;complexContent&gt;
                   *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                   *       &lt;attribute name="Family" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                   *       &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                   *       &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                   *     &lt;/restriction&gt;
                   *   &lt;/complexContent&gt;
                   * &lt;/complexType&gt;
                   * </pre>
                   *
                   *
                   */
                  @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Character
                  {

                     @XmlAttribute(name = "Family") protected String family;
                     @XmlAttribute(name = "Size") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer size;
                     @XmlAttribute(name = "IX") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer ix;

                     /**
                      * Gets the value of the family property.
                      *
                      * @return
                      *     possible object is
                      *     {@link String }
                      *
                      */
                     public String getFamily()
                     {
                        return family;
                     }

                     /**
                      * Sets the value of the family property.
                      *
                      * @param value
                      *     allowed object is
                      *     {@link String }
                      *
                      */
                     public void setFamily(String value)
                     {
                        this.family = value;
                     }

                     /**
                      * Gets the value of the size property.
                      *
                      * @return
                      *     possible object is
                      *     {@link String }
                      *
                      */
                     public Integer getSize()
                     {
                        return size;
                     }

                     /**
                      * Sets the value of the size property.
                      *
                      * @param value
                      *     allowed object is
                      *     {@link String }
                      *
                      */
                     public void setSize(Integer value)
                     {
                        this.size = value;
                     }

                     /**
                      * Gets the value of the ix property.
                      *
                      * @return
                      *     possible object is
                      *     {@link String }
                      *
                      */
                     public Integer getIX()
                     {
                        return ix;
                     }

                     /**
                      * Sets the value of the ix property.
                      *
                      * @param value
                      *     allowed object is
                      *     {@link String }
                      *
                      */
                     public void setIX(Integer value)
                     {
                        this.ix = value;
                     }

                  }

                  /**
                   * <p>Java class for anonymous complex type.
                   *
                   * <p>The following schema fragment specifies the expected content contained within this class.
                   *
                   * <pre>
                   * &lt;complexType&gt;
                   *   &lt;complexContent&gt;
                   *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                   *       &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                   *     &lt;/restriction&gt;
                   *   &lt;/complexContent&gt;
                   * &lt;/complexType&gt;
                   * </pre>
                   *
                   *
                   */
                  @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Color
                  {

                     @XmlAttribute(name = "V") protected String v;

                     /**
                      * Gets the value of the v property.
                      *
                      * @return
                      *     possible object is
                      *     {@link String }
                      *
                      */
                     public String getV()
                     {
                        return v;
                     }

                     /**
                      * Sets the value of the v property.
                      *
                      * @param value
                      *     allowed object is
                      *     {@link String }
                      *
                      */
                     public void setV(String value)
                     {
                        this.v = value;
                     }

                  }

                  /**
                   * <p>Java class for anonymous complex type.
                   *
                   * <p>The following schema fragment specifies the expected content contained within this class.
                   *
                   * <pre>
                   * &lt;complexType&gt;
                   *   &lt;complexContent&gt;
                   *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                   *       &lt;attribute name="Color" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                   *     &lt;/restriction&gt;
                   *   &lt;/complexContent&gt;
                   * &lt;/complexType&gt;
                   * </pre>
                   *
                   *
                   */
                  @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class FillColor
                  {

                     @XmlAttribute(name = "Color") protected String color;

                     /**
                      * Gets the value of the color property.
                      *
                      * @return
                      *     possible object is
                      *     {@link String }
                      *
                      */
                     public String getColor()
                     {
                        return color;
                     }

                     /**
                      * Sets the value of the color property.
                      *
                      * @param value
                      *     allowed object is
                      *     {@link String }
                      *
                      */
                     public void setColor(String value)
                     {
                        this.color = value;
                     }

                  }

                  /**
                   * <p>Java class for anonymous complex type.
                   *
                   * <p>The following schema fragment specifies the expected content contained within this class.
                   *
                   * <pre>
                   * &lt;complexType&gt;
                   *   &lt;complexContent&gt;
                   *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                   *       &lt;attribute name="Align" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                   *       &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                   *       &lt;attribute name="SpLine" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                   *     &lt;/restriction&gt;
                   *   &lt;/complexContent&gt;
                   * &lt;/complexType&gt;
                   * </pre>
                   *
                   *
                   */
                  @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Paragraph
                  {

                     @XmlAttribute(name = "Align") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer align;
                     @XmlAttribute(name = "IX") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer ix;
                     @XmlAttribute(name = "SpLine") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer spLine;

                     /**
                      * Gets the value of the align property.
                      *
                      * @return
                      *     possible object is
                      *     {@link String }
                      *
                      */
                     public Integer getAlign()
                     {
                        return align;
                     }

                     /**
                      * Sets the value of the align property.
                      *
                      * @param value
                      *     allowed object is
                      *     {@link String }
                      *
                      */
                     public void setAlign(Integer value)
                     {
                        this.align = value;
                     }

                     /**
                      * Gets the value of the ix property.
                      *
                      * @return
                      *     possible object is
                      *     {@link String }
                      *
                      */
                     public Integer getIX()
                     {
                        return ix;
                     }

                     /**
                      * Sets the value of the ix property.
                      *
                      * @param value
                      *     allowed object is
                      *     {@link String }
                      *
                      */
                     public void setIX(Integer value)
                     {
                        this.ix = value;
                     }

                     /**
                      * Gets the value of the spLine property.
                      *
                      * @return
                      *     possible object is
                      *     {@link String }
                      *
                      */
                     public Integer getSpLine()
                     {
                        return spLine;
                     }

                     /**
                      * Sets the value of the spLine property.
                      *
                      * @param value
                      *     allowed object is
                      *     {@link String }
                      *
                      */
                     public void setSpLine(Integer value)
                     {
                        this.spLine = value;
                     }

                  }

                  /**
                   * <p>Java class for anonymous complex type.
                   *
                   * <p>The following schema fragment specifies the expected content contained within this class.
                   *
                   * <pre>
                   * &lt;complexType&gt;
                   *   &lt;complexContent&gt;
                   *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                   *       &lt;attribute name="Value" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                   *     &lt;/restriction&gt;
                   *   &lt;/complexContent&gt;
                   * &lt;/complexType&gt;
                   * </pre>
                   *
                   *
                   */
                  @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class WrapMode
                  {

                     @XmlAttribute(name = "Value") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer value;

                     /**
                      * Gets the value of the value property.
                      *
                      * @return
                      *     possible object is
                      *     {@link String }
                      *
                      */
                     public Integer getValue()
                     {
                        return value;
                     }

                     /**
                      * Sets the value of the value property.
                      *
                      * @param value
                      *     allowed object is
                      *     {@link String }
                      *
                      */
                     public void setValue(Integer value)
                     {
                        this.value = value;
                     }

                  }

               }

            }

         }

      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class ScreenHeight
   {

      @XmlAttribute(name = "V") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer v;

      /**
       * Gets the value of the v property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Integer getV()
      {
         return v;
      }

      /**
       * Sets the value of the v property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setV(Integer value)
      {
         this.v = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class ScreenWidth
   {

      @XmlAttribute(name = "V") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer v;

      /**
       * Gets the value of the v property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public Integer getV()
      {
         return v;
      }

      /**
       * Sets the value of the v property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setV(Integer value)
      {
         this.v = value;
      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;sequence&gt;
    *         &lt;element name="Task" maxOccurs="unbounded"&gt;
    *           &lt;complexType&gt;
    *             &lt;complexContent&gt;
    *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                 &lt;sequence&gt;
    *                   &lt;element name="Format"&gt;
    *                     &lt;complexType&gt;
    *                       &lt;complexContent&gt;
    *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                           &lt;attribute name="Family" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                           &lt;attribute name="Bold" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                           &lt;attribute name="Underline" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                           &lt;attribute name="StrikeOut" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                           &lt;attribute name="PointSize" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                           &lt;attribute name="Italic" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                         &lt;/restriction&gt;
    *                       &lt;/complexContent&gt;
    *                     &lt;/complexType&gt;
    *                   &lt;/element&gt;
    *                   &lt;element name="ResourceList"&gt;
    *                     &lt;complexType&gt;
    *                       &lt;complexContent&gt;
    *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                           &lt;sequence&gt;
    *                             &lt;element name="Resource" maxOccurs="unbounded"&gt;
    *                               &lt;complexType&gt;
    *                                 &lt;complexContent&gt;
    *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                     &lt;attribute name="ID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                                     &lt;attribute name="CostUnit" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                                     &lt;attribute name="Cost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
    *                                     &lt;attribute name="CostPer" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
    *                                     &lt;attribute name="Type" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                                     &lt;attribute name="WorkSecs" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
    *                                     &lt;attribute name="OvertimeUnit" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                                     &lt;attribute name="Percent" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
    *                                     &lt;attribute name="OvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
    *                                     &lt;attribute name="Name" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                                   &lt;/restriction&gt;
    *                                 &lt;/complexContent&gt;
    *                               &lt;/complexType&gt;
    *                             &lt;/element&gt;
    *                           &lt;/sequence&gt;
    *                         &lt;/restriction&gt;
    *                       &lt;/complexContent&gt;
    *                     &lt;/complexType&gt;
    *                   &lt;/element&gt;
    *                   &lt;element name="PredecessorLink" maxOccurs="unbounded"&gt;
    *                     &lt;complexType&gt;
    *                       &lt;complexContent&gt;
    *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                           &lt;sequence&gt;
    *                             &lt;element name="PredecessorUID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *                             &lt;element name="Type" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *                             &lt;element name="LinkLag" type="{http://www.w3.org/2001/XMLSchema}long"/&gt;
    *                             &lt;element name="LagFormat" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
    *                             &lt;element name="CrossProject" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
    *                           &lt;/sequence&gt;
    *                         &lt;/restriction&gt;
    *                       &lt;/complexContent&gt;
    *                     &lt;/complexType&gt;
    *                   &lt;/element&gt;
    *                   &lt;element name="Texts"&gt;
    *                     &lt;complexType&gt;
    *                       &lt;complexContent&gt;
    *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                           &lt;sequence&gt;
    *                             &lt;element name="TextCell" maxOccurs="unbounded"&gt;
    *                               &lt;complexType&gt;
    *                                 &lt;complexContent&gt;
    *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                     &lt;sequence&gt;
    *                                       &lt;element name="Varient" type="{http://www.w3.org/2001/XMLSchema}anyType"/&gt;
    *                                       &lt;element name="TextBlock"&gt;
    *                                         &lt;complexType&gt;
    *                                           &lt;complexContent&gt;
    *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                               &lt;sequence&gt;
    *                                                 &lt;element name="Character"&gt;
    *                                                   &lt;complexType&gt;
    *                                                     &lt;complexContent&gt;
    *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                                         &lt;attribute name="Family" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                                                         &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                                                         &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                                                       &lt;/restriction&gt;
    *                                                     &lt;/complexContent&gt;
    *                                                   &lt;/complexType&gt;
    *                                                 &lt;/element&gt;
    *                                                 &lt;element name="Paragraph"&gt;
    *                                                   &lt;complexType&gt;
    *                                                     &lt;complexContent&gt;
    *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                                         &lt;attribute name="Align" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                                                         &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                                                         &lt;attribute name="SpLine" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                                                       &lt;/restriction&gt;
    *                                                     &lt;/complexContent&gt;
    *                                                   &lt;/complexType&gt;
    *                                                 &lt;/element&gt;
    *                                                 &lt;element name="WrapMode"&gt;
    *                                                   &lt;complexType&gt;
    *                                                     &lt;complexContent&gt;
    *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                                         &lt;attribute name="Value" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                                                       &lt;/restriction&gt;
    *                                                     &lt;/complexContent&gt;
    *                                                   &lt;/complexType&gt;
    *                                                 &lt;/element&gt;
    *                                                 &lt;element name="FillColor"&gt;
    *                                                   &lt;complexType&gt;
    *                                                     &lt;complexContent&gt;
    *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                                         &lt;attribute name="Color" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                                                       &lt;/restriction&gt;
    *                                                     &lt;/complexContent&gt;
    *                                                   &lt;/complexType&gt;
    *                                                 &lt;/element&gt;
    *                                                 &lt;element name="Color"&gt;
    *                                                   &lt;complexType&gt;
    *                                                     &lt;complexContent&gt;
    *                                                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *                                                         &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                                                       &lt;/restriction&gt;
    *                                                     &lt;/complexContent&gt;
    *                                                   &lt;/complexType&gt;
    *                                                 &lt;/element&gt;
    *                                               &lt;/sequence&gt;
    *                                               &lt;attribute name="VAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                                               &lt;attribute name="HAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                                               &lt;attribute name="TextFormatMask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                                             &lt;/restriction&gt;
    *                                           &lt;/complexContent&gt;
    *                                         &lt;/complexType&gt;
    *                                       &lt;/element&gt;
    *                                     &lt;/sequence&gt;
    *                                     &lt;attribute name="FieldType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                                     &lt;attribute name="PlainText" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                                     &lt;attribute name="FieldID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                                     &lt;attribute name="DataType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                                   &lt;/restriction&gt;
    *                                 &lt;/complexContent&gt;
    *                               &lt;/complexType&gt;
    *                             &lt;/element&gt;
    *                           &lt;/sequence&gt;
    *                         &lt;/restriction&gt;
    *                       &lt;/complexContent&gt;
    *                     &lt;/complexType&gt;
    *                   &lt;/element&gt;
    *                   &lt;element name="BarChart" type="{http://www.w3.org/2001/XMLSchema}anyType"/&gt;
    *                 &lt;/sequence&gt;
    *                 &lt;attribute name="Milestone" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
    *                 &lt;attribute name="BackGroundColor" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="ProgressStatus" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="BaseLinePointxEnd" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="m_Bestyletext" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="RowHeight" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="CriticalPath" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
    *                 &lt;attribute name="ID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="ActualDuration" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
    *                 &lt;attribute name="DateBaseStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
    *                 &lt;attribute name="Resources" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="FontColor" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="DurationUnits" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="DateManualFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
    *                 &lt;attribute name="m_bebartextIndex" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="m_Istyletext" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="BaseLinePointxBegin" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="Manual" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
    *                 &lt;attribute name="HideByColumns" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="Level" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="IsToggler" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
    *                 &lt;attribute name="BaselineCost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
    *                 &lt;attribute name="DurationSecs" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
    *                 &lt;attribute name="ManualDurationSecs" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
    *                 &lt;attribute name="DateLateStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
    *                 &lt;attribute name="LastSaveDate" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
    *                 &lt;attribute name="m_IbartextIndex" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="ActualStart" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
    *                 &lt;attribute name="Work" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
    *                 &lt;attribute name="Cost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
    *                 &lt;attribute name="SplitOffsetDuration" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="DateStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
    *                 &lt;attribute name="RowID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="StartText" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="Name" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="ActualFinish" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
    *                 &lt;attribute name="FirstWidth" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="DateLateFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
    *                 &lt;attribute name="CountWidth" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="Priority" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="DateManualStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
    *                 &lt;attribute name="DateBaseFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
    *                 &lt;attribute name="ShowByChild" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="ActualMilestone" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
    *                 &lt;attribute name="ParentID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="LateSlack" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="DateFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
    *                 &lt;attribute name="Wbs" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="Notes" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="BaseLineNumber" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
    *                 &lt;attribute name="HideID" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
    *                 &lt;attribute name="SplitPointList" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="Percent" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
    *                 &lt;attribute name="RemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
    *                 &lt;attribute name="EarlySlack" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *                 &lt;attribute name="Childs" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *               &lt;/restriction&gt;
    *             &lt;/complexContent&gt;
    *           &lt;/complexType&gt;
    *         &lt;/element&gt;
    *       &lt;/sequence&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
   {
      "task"
   }) public static class TaskList
   {

      @XmlElement(name = "Task", required = true) protected List<Document.TaskList.Task> task;

      /**
       * Gets the value of the task property.
       *
       * <p>
       * This accessor method returns a reference to the live list,
       * not a snapshot. Therefore any modification you make to the
       * returned list will be present inside the Jakarta XML Binding object.
       * This is why there is not a <CODE>set</CODE> method for the task property.
       *
       * <p>
       * For example, to add a new item, do as follows:
       * <pre>
       *    getTask().add(newItem);
       * </pre>
       *
       *
       * <p>
       * Objects of the following type(s) are allowed in the list
       * {@link Document.TaskList.Task }
       *
       *
       */
      public List<Document.TaskList.Task> getTask()
      {
         if (task == null)
         {
            task = new ArrayList<>();
         }
         return this.task;
      }

      /**
       * <p>Java class for anonymous complex type.
       *
       * <p>The following schema fragment specifies the expected content contained within this class.
       *
       * <pre>
       * &lt;complexType&gt;
       *   &lt;complexContent&gt;
       *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *       &lt;sequence&gt;
       *         &lt;element name="Format"&gt;
       *           &lt;complexType&gt;
       *             &lt;complexContent&gt;
       *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                 &lt;attribute name="Family" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *                 &lt;attribute name="Bold" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                 &lt;attribute name="Underline" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                 &lt;attribute name="StrikeOut" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                 &lt;attribute name="PointSize" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                 &lt;attribute name="Italic" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *               &lt;/restriction&gt;
       *             &lt;/complexContent&gt;
       *           &lt;/complexType&gt;
       *         &lt;/element&gt;
       *         &lt;element name="ResourceList"&gt;
       *           &lt;complexType&gt;
       *             &lt;complexContent&gt;
       *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                 &lt;sequence&gt;
       *                   &lt;element name="Resource" maxOccurs="unbounded"&gt;
       *                     &lt;complexType&gt;
       *                       &lt;complexContent&gt;
       *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                           &lt;attribute name="ID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                           &lt;attribute name="CostUnit" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                           &lt;attribute name="Cost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
       *                           &lt;attribute name="CostPer" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
       *                           &lt;attribute name="Type" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                           &lt;attribute name="WorkSecs" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
       *                           &lt;attribute name="OvertimeUnit" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                           &lt;attribute name="Percent" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
       *                           &lt;attribute name="OvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
       *                           &lt;attribute name="Name" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *                         &lt;/restriction&gt;
       *                       &lt;/complexContent&gt;
       *                     &lt;/complexType&gt;
       *                   &lt;/element&gt;
       *                 &lt;/sequence&gt;
       *               &lt;/restriction&gt;
       *             &lt;/complexContent&gt;
       *           &lt;/complexType&gt;
       *         &lt;/element&gt;
       *         &lt;element name="PredecessorLink" maxOccurs="unbounded"&gt;
       *           &lt;complexType&gt;
       *             &lt;complexContent&gt;
       *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                 &lt;sequence&gt;
       *                   &lt;element name="PredecessorUID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
       *                   &lt;element name="Type" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
       *                   &lt;element name="LinkLag" type="{http://www.w3.org/2001/XMLSchema}long"/&gt;
       *                   &lt;element name="LagFormat" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
       *                   &lt;element name="CrossProject" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
       *                 &lt;/sequence&gt;
       *               &lt;/restriction&gt;
       *             &lt;/complexContent&gt;
       *           &lt;/complexType&gt;
       *         &lt;/element&gt;
       *         &lt;element name="Texts"&gt;
       *           &lt;complexType&gt;
       *             &lt;complexContent&gt;
       *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                 &lt;sequence&gt;
       *                   &lt;element name="TextCell" maxOccurs="unbounded"&gt;
       *                     &lt;complexType&gt;
       *                       &lt;complexContent&gt;
       *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                           &lt;sequence&gt;
       *                             &lt;element name="Varient" type="{http://www.w3.org/2001/XMLSchema}anyType"/&gt;
       *                             &lt;element name="TextBlock"&gt;
       *                               &lt;complexType&gt;
       *                                 &lt;complexContent&gt;
       *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                                     &lt;sequence&gt;
       *                                       &lt;element name="Character"&gt;
       *                                         &lt;complexType&gt;
       *                                           &lt;complexContent&gt;
       *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                                               &lt;attribute name="Family" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *                                               &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                                               &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                                             &lt;/restriction&gt;
       *                                           &lt;/complexContent&gt;
       *                                         &lt;/complexType&gt;
       *                                       &lt;/element&gt;
       *                                       &lt;element name="Paragraph"&gt;
       *                                         &lt;complexType&gt;
       *                                           &lt;complexContent&gt;
       *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                                               &lt;attribute name="Align" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                                               &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                                               &lt;attribute name="SpLine" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                                             &lt;/restriction&gt;
       *                                           &lt;/complexContent&gt;
       *                                         &lt;/complexType&gt;
       *                                       &lt;/element&gt;
       *                                       &lt;element name="WrapMode"&gt;
       *                                         &lt;complexType&gt;
       *                                           &lt;complexContent&gt;
       *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                                               &lt;attribute name="Value" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                                             &lt;/restriction&gt;
       *                                           &lt;/complexContent&gt;
       *                                         &lt;/complexType&gt;
       *                                       &lt;/element&gt;
       *                                       &lt;element name="FillColor"&gt;
       *                                         &lt;complexType&gt;
       *                                           &lt;complexContent&gt;
       *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                                               &lt;attribute name="Color" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *                                             &lt;/restriction&gt;
       *                                           &lt;/complexContent&gt;
       *                                         &lt;/complexType&gt;
       *                                       &lt;/element&gt;
       *                                       &lt;element name="Color"&gt;
       *                                         &lt;complexType&gt;
       *                                           &lt;complexContent&gt;
       *                                             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       *                                               &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *                                             &lt;/restriction&gt;
       *                                           &lt;/complexContent&gt;
       *                                         &lt;/complexType&gt;
       *                                       &lt;/element&gt;
       *                                     &lt;/sequence&gt;
       *                                     &lt;attribute name="VAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *                                     &lt;attribute name="HAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *                                     &lt;attribute name="TextFormatMask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                                   &lt;/restriction&gt;
       *                                 &lt;/complexContent&gt;
       *                               &lt;/complexType&gt;
       *                             &lt;/element&gt;
       *                           &lt;/sequence&gt;
       *                           &lt;attribute name="FieldType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                           &lt;attribute name="PlainText" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *                           &lt;attribute name="FieldID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                           &lt;attribute name="DataType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *                         &lt;/restriction&gt;
       *                       &lt;/complexContent&gt;
       *                     &lt;/complexType&gt;
       *                   &lt;/element&gt;
       *                 &lt;/sequence&gt;
       *               &lt;/restriction&gt;
       *             &lt;/complexContent&gt;
       *           &lt;/complexType&gt;
       *         &lt;/element&gt;
       *         &lt;element name="BarChart" type="{http://www.w3.org/2001/XMLSchema}anyType"/&gt;
       *       &lt;/sequence&gt;
       *       &lt;attribute name="Milestone" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
       *       &lt;attribute name="BackGroundColor" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="ProgressStatus" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="BaseLinePointxEnd" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="m_Bestyletext" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="RowHeight" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="CriticalPath" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
       *       &lt;attribute name="ID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="ActualDuration" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
       *       &lt;attribute name="DateBaseStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
       *       &lt;attribute name="Resources" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="FontColor" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="DurationUnits" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="DateManualFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
       *       &lt;attribute name="m_bebartextIndex" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="m_Istyletext" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="BaseLinePointxBegin" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="Manual" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
       *       &lt;attribute name="HideByColumns" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="Level" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="IsToggler" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
       *       &lt;attribute name="BaselineCost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
       *       &lt;attribute name="DurationSecs" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
       *       &lt;attribute name="ManualDurationSecs" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
       *       &lt;attribute name="DateLateStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
       *       &lt;attribute name="LastSaveDate" type="{http://www.w3.org/2001/XMLSchema}date" /&gt;
       *       &lt;attribute name="m_IbartextIndex" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="ActualStart" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
       *       &lt;attribute name="Work" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
       *       &lt;attribute name="Cost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
       *       &lt;attribute name="SplitOffsetDuration" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="DateStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
       *       &lt;attribute name="RowID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="StartText" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="Name" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="ActualFinish" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
       *       &lt;attribute name="FirstWidth" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="DateLateFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
       *       &lt;attribute name="CountWidth" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="Priority" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="DateManualStart" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
       *       &lt;attribute name="DateBaseFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
       *       &lt;attribute name="ShowByChild" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="ActualMilestone" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
       *       &lt;attribute name="ParentID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="LateSlack" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="DateFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" /&gt;
       *       &lt;attribute name="Wbs" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="Notes" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="BaseLineNumber" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
       *       &lt;attribute name="HideID" type="{http://www.w3.org/2001/XMLSchema}boolean" /&gt;
       *       &lt;attribute name="SplitPointList" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="Percent" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
       *       &lt;attribute name="RemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
       *       &lt;attribute name="EarlySlack" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *       &lt;attribute name="Childs" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       *     &lt;/restriction&gt;
       *   &lt;/complexContent&gt;
       * &lt;/complexType&gt;
       * </pre>
       *
       *
       */
      @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
      {
         "format",
         "resourceList",
         "predecessorLink",
         "texts",
         "barChart"
      }) public static class Task
      {

         @XmlElement(name = "Format", required = true) protected Document.TaskList.Task.Format format;
         @XmlElement(name = "ResourceList", required = true) protected Document.TaskList.Task.ResourceList resourceList;
         @XmlElement(name = "PredecessorLink", required = true) protected List<Document.TaskList.Task.PredecessorLink> predecessorLink;
         @XmlElement(name = "Texts", required = true) protected Document.TaskList.Task.Texts texts;
         @XmlElement(name = "BarChart", required = true) protected Object barChart;
         @XmlAttribute(name = "Milestone") @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "boolean") protected Boolean milestone;
         @XmlAttribute(name = "BackGroundColor") protected String backGroundColor;
         @XmlAttribute(name = "ProgressStatus") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer progressStatus;
         @XmlAttribute(name = "BaseLinePointxEnd") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer baseLinePointxEnd;
         @XmlAttribute(name = "m_Bestyletext") protected String mBestyletext;
         @XmlAttribute(name = "RowHeight") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer rowHeight;
         @XmlAttribute(name = "CriticalPath") @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "boolean") protected Boolean criticalPath;
         @XmlAttribute(name = "ID") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer id;
         @XmlAttribute(name = "ActualDuration") protected Long actualDuration;
         @XmlAttribute(name = "DateBaseStart") @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime dateBaseStart;
         @XmlAttribute(name = "Resources") protected String resources;
         @XmlAttribute(name = "FontColor") protected String fontColor;
         @XmlAttribute(name = "DurationUnits") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer durationUnits;
         @XmlAttribute(name = "DateManualFinish") @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime dateManualFinish;
         @XmlAttribute(name = "m_bebartextIndex") protected String mBebartextIndex;
         @XmlAttribute(name = "m_Istyletext") protected String mIstyletext;
         @XmlAttribute(name = "BaseLinePointxBegin") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer baseLinePointxBegin;
         @XmlAttribute(name = "Manual") @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "boolean") protected Boolean manual;
         @XmlAttribute(name = "HideByColumns") protected String hideByColumns;
         @XmlAttribute(name = "Level") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer level;
         @XmlAttribute(name = "IsToggler") @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "boolean") protected Boolean isToggler;
         @XmlAttribute(name = "BaselineCost") @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "double") protected Double baselineCost;
         @XmlAttribute(name = "DurationSecs") protected Long durationSecs;
         @XmlAttribute(name = "ManualDurationSecs") protected Long manualDurationSecs;
         @XmlAttribute(name = "DateLateStart") @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime dateLateStart;
         @XmlAttribute(name = "LastSaveDate") @XmlSchemaType(name = "date") protected XMLGregorianCalendar lastSaveDate;
         @XmlAttribute(name = "m_IbartextIndex") protected String mIbartextIndex;
         @XmlAttribute(name = "ActualStart") protected Long actualStart;
         @XmlAttribute(name = "Work") @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "double") protected Double work;
         @XmlAttribute(name = "Cost") @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "double") protected Double cost;
         @XmlAttribute(name = "SplitOffsetDuration") protected String splitOffsetDuration;
         @XmlAttribute(name = "DateStart") @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime dateStart;
         @XmlAttribute(name = "RowID") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer rowID;
         @XmlAttribute(name = "StartText") protected String startText;
         @XmlAttribute(name = "Name") protected String name;
         @XmlAttribute(name = "ActualFinish") protected Long actualFinish;
         @XmlAttribute(name = "FirstWidth") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer firstWidth;
         @XmlAttribute(name = "DateLateFinish") @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime dateLateFinish;
         @XmlAttribute(name = "CountWidth") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer countWidth;
         @XmlAttribute(name = "Priority") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer priority;
         @XmlAttribute(name = "DateManualStart") @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime dateManualStart;
         @XmlAttribute(name = "DateBaseFinish") @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime dateBaseFinish;
         @XmlAttribute(name = "ShowByChild") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer showByChild;
         @XmlAttribute(name = "ActualMilestone") @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "boolean") protected Boolean actualMilestone;
         @XmlAttribute(name = "ParentID") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer parentID;
         @XmlAttribute(name = "LateSlack") protected String lateSlack;
         @XmlAttribute(name = "DateFinish") @XmlJavaTypeAdapter(Adapter3.class) @XmlSchemaType(name = "dateTime") protected LocalDateTime dateFinish;
         @XmlAttribute(name = "Wbs") protected String wbs;
         @XmlAttribute(name = "Notes") protected String notes;
         @XmlAttribute(name = "BaseLineNumber") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer baseLineNumber;
         @XmlAttribute(name = "HideID") @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "boolean") protected Boolean hideID;
         @XmlAttribute(name = "SplitPointList") protected String splitPointList;
         @XmlAttribute(name = "Percent") @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "double") protected Double percent;
         @XmlAttribute(name = "RemainingCost") @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "double") protected Double remainingCost;
         @XmlAttribute(name = "EarlySlack") protected String earlySlack;
         @XmlAttribute(name = "Childs") protected String childs;

         /**
          * Gets the value of the format property.
          *
          * @return
          *     possible object is
          *     {@link Document.TaskList.Task.Format }
          *
          */
         public Document.TaskList.Task.Format getFormat()
         {
            return format;
         }

         /**
          * Sets the value of the format property.
          *
          * @param value
          *     allowed object is
          *     {@link Document.TaskList.Task.Format }
          *
          */
         public void setFormat(Document.TaskList.Task.Format value)
         {
            this.format = value;
         }

         /**
          * Gets the value of the resourceList property.
          *
          * @return
          *     possible object is
          *     {@link Document.TaskList.Task.ResourceList }
          *
          */
         public Document.TaskList.Task.ResourceList getResourceList()
         {
            return resourceList;
         }

         /**
          * Sets the value of the resourceList property.
          *
          * @param value
          *     allowed object is
          *     {@link Document.TaskList.Task.ResourceList }
          *
          */
         public void setResourceList(Document.TaskList.Task.ResourceList value)
         {
            this.resourceList = value;
         }

         /**
          * Gets the value of the predecessorLink property.
          *
          * <p>
          * This accessor method returns a reference to the live list,
          * not a snapshot. Therefore any modification you make to the
          * returned list will be present inside the Jakarta XML Binding object.
          * This is why there is not a <CODE>set</CODE> method for the predecessorLink property.
          *
          * <p>
          * For example, to add a new item, do as follows:
          * <pre>
          *    getPredecessorLink().add(newItem);
          * </pre>
          *
          *
          * <p>
          * Objects of the following type(s) are allowed in the list
          * {@link Document.TaskList.Task.PredecessorLink }
          *
          *
          */
         public List<Document.TaskList.Task.PredecessorLink> getPredecessorLink()
         {
            if (predecessorLink == null)
            {
               predecessorLink = new ArrayList<>();
            }
            return this.predecessorLink;
         }

         /**
          * Gets the value of the texts property.
          *
          * @return
          *     possible object is
          *     {@link Document.TaskList.Task.Texts }
          *
          */
         public Document.TaskList.Task.Texts getTexts()
         {
            return texts;
         }

         /**
          * Sets the value of the texts property.
          *
          * @param value
          *     allowed object is
          *     {@link Document.TaskList.Task.Texts }
          *
          */
         public void setTexts(Document.TaskList.Task.Texts value)
         {
            this.texts = value;
         }

         /**
          * Gets the value of the barChart property.
          *
          * @return
          *     possible object is
          *     {@link Object }
          *
          */
         public Object getBarChart()
         {
            return barChart;
         }

         /**
          * Sets the value of the barChart property.
          *
          * @param value
          *     allowed object is
          *     {@link Object }
          *
          */
         public void setBarChart(Object value)
         {
            this.barChart = value;
         }

         /**
          * Gets the value of the milestone property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Boolean isMilestone()
         {
            return milestone;
         }

         /**
          * Sets the value of the milestone property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setMilestone(Boolean value)
         {
            this.milestone = value;
         }

         /**
          * Gets the value of the backGroundColor property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getBackGroundColor()
         {
            return backGroundColor;
         }

         /**
          * Sets the value of the backGroundColor property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setBackGroundColor(String value)
         {
            this.backGroundColor = value;
         }

         /**
          * Gets the value of the progressStatus property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getProgressStatus()
         {
            return progressStatus;
         }

         /**
          * Sets the value of the progressStatus property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setProgressStatus(Integer value)
         {
            this.progressStatus = value;
         }

         /**
          * Gets the value of the baseLinePointxEnd property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getBaseLinePointxEnd()
         {
            return baseLinePointxEnd;
         }

         /**
          * Sets the value of the baseLinePointxEnd property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setBaseLinePointxEnd(Integer value)
         {
            this.baseLinePointxEnd = value;
         }

         /**
          * Gets the value of the mBestyletext property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getMBestyletext()
         {
            return mBestyletext;
         }

         /**
          * Sets the value of the mBestyletext property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setMBestyletext(String value)
         {
            this.mBestyletext = value;
         }

         /**
          * Gets the value of the rowHeight property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getRowHeight()
         {
            return rowHeight;
         }

         /**
          * Sets the value of the rowHeight property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setRowHeight(Integer value)
         {
            this.rowHeight = value;
         }

         /**
          * Gets the value of the criticalPath property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Boolean isCriticalPath()
         {
            return criticalPath;
         }

         /**
          * Sets the value of the criticalPath property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setCriticalPath(Boolean value)
         {
            this.criticalPath = value;
         }

         /**
          * Gets the value of the id property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getID()
         {
            return id;
         }

         /**
          * Sets the value of the id property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setID(Integer value)
         {
            this.id = value;
         }

         /**
          * Gets the value of the actualDuration property.
          *
          * @return
          *     possible object is
          *     {@link Long }
          *
          */
         public Long getActualDuration()
         {
            return actualDuration;
         }

         /**
          * Sets the value of the actualDuration property.
          *
          * @param value
          *     allowed object is
          *     {@link Long }
          *
          */
         public void setActualDuration(Long value)
         {
            this.actualDuration = value;
         }

         /**
          * Gets the value of the dateBaseStart property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public LocalDateTime getDateBaseStart()
         {
            return dateBaseStart;
         }

         /**
          * Sets the value of the dateBaseStart property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setDateBaseStart(LocalDateTime value)
         {
            this.dateBaseStart = value;
         }

         /**
          * Gets the value of the resources property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getResources()
         {
            return resources;
         }

         /**
          * Sets the value of the resources property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setResources(String value)
         {
            this.resources = value;
         }

         /**
          * Gets the value of the fontColor property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getFontColor()
         {
            return fontColor;
         }

         /**
          * Sets the value of the fontColor property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setFontColor(String value)
         {
            this.fontColor = value;
         }

         /**
          * Gets the value of the durationUnits property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getDurationUnits()
         {
            return durationUnits;
         }

         /**
          * Sets the value of the durationUnits property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setDurationUnits(Integer value)
         {
            this.durationUnits = value;
         }

         /**
          * Gets the value of the dateManualFinish property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public LocalDateTime getDateManualFinish()
         {
            return dateManualFinish;
         }

         /**
          * Sets the value of the dateManualFinish property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setDateManualFinish(LocalDateTime value)
         {
            this.dateManualFinish = value;
         }

         /**
          * Gets the value of the mBebartextIndex property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getMBebartextIndex()
         {
            return mBebartextIndex;
         }

         /**
          * Sets the value of the mBebartextIndex property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setMBebartextIndex(String value)
         {
            this.mBebartextIndex = value;
         }

         /**
          * Gets the value of the mIstyletext property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getMIstyletext()
         {
            return mIstyletext;
         }

         /**
          * Sets the value of the mIstyletext property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setMIstyletext(String value)
         {
            this.mIstyletext = value;
         }

         /**
          * Gets the value of the baseLinePointxBegin property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getBaseLinePointxBegin()
         {
            return baseLinePointxBegin;
         }

         /**
          * Sets the value of the baseLinePointxBegin property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setBaseLinePointxBegin(Integer value)
         {
            this.baseLinePointxBegin = value;
         }

         /**
          * Gets the value of the manual property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Boolean isManual()
         {
            return manual;
         }

         /**
          * Sets the value of the manual property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setManual(Boolean value)
         {
            this.manual = value;
         }

         /**
          * Gets the value of the hideByColumns property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getHideByColumns()
         {
            return hideByColumns;
         }

         /**
          * Sets the value of the hideByColumns property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setHideByColumns(String value)
         {
            this.hideByColumns = value;
         }

         /**
          * Gets the value of the level property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getLevel()
         {
            return level;
         }

         /**
          * Sets the value of the level property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setLevel(Integer value)
         {
            this.level = value;
         }

         /**
          * Gets the value of the isToggler property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Boolean isIsToggler()
         {
            return isToggler;
         }

         /**
          * Sets the value of the isToggler property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setIsToggler(Boolean value)
         {
            this.isToggler = value;
         }

         /**
          * Gets the value of the baselineCost property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Double getBaselineCost()
         {
            return baselineCost;
         }

         /**
          * Sets the value of the baselineCost property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setBaselineCost(Double value)
         {
            this.baselineCost = value;
         }

         /**
          * Gets the value of the durationSecs property.
          *
          * @return
          *     possible object is
          *     {@link Long }
          *
          */
         public Long getDurationSecs()
         {
            return durationSecs;
         }

         /**
          * Sets the value of the durationSecs property.
          *
          * @param value
          *     allowed object is
          *     {@link Long }
          *
          */
         public void setDurationSecs(Long value)
         {
            this.durationSecs = value;
         }

         /**
          * Gets the value of the manualDurationSecs property.
          *
          * @return
          *     possible object is
          *     {@link Long }
          *
          */
         public Long getManualDurationSecs()
         {
            return manualDurationSecs;
         }

         /**
          * Sets the value of the manualDurationSecs property.
          *
          * @param value
          *     allowed object is
          *     {@link Long }
          *
          */
         public void setManualDurationSecs(Long value)
         {
            this.manualDurationSecs = value;
         }

         /**
          * Gets the value of the dateLateStart property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public LocalDateTime getDateLateStart()
         {
            return dateLateStart;
         }

         /**
          * Sets the value of the dateLateStart property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setDateLateStart(LocalDateTime value)
         {
            this.dateLateStart = value;
         }

         /**
          * Gets the value of the lastSaveDate property.
          *
          * @return
          *     possible object is
          *     {@link XMLGregorianCalendar }
          *
          */
         public XMLGregorianCalendar getLastSaveDate()
         {
            return lastSaveDate;
         }

         /**
          * Sets the value of the lastSaveDate property.
          *
          * @param value
          *     allowed object is
          *     {@link XMLGregorianCalendar }
          *
          */
         public void setLastSaveDate(XMLGregorianCalendar value)
         {
            this.lastSaveDate = value;
         }

         /**
          * Gets the value of the mIbartextIndex property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getMIbartextIndex()
         {
            return mIbartextIndex;
         }

         /**
          * Sets the value of the mIbartextIndex property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setMIbartextIndex(String value)
         {
            this.mIbartextIndex = value;
         }

         /**
          * Gets the value of the actualStart property.
          *
          * @return
          *     possible object is
          *     {@link Long }
          *
          */
         public Long getActualStart()
         {
            return actualStart;
         }

         /**
          * Sets the value of the actualStart property.
          *
          * @param value
          *     allowed object is
          *     {@link Long }
          *
          */
         public void setActualStart(Long value)
         {
            this.actualStart = value;
         }

         /**
          * Gets the value of the work property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Double getWork()
         {
            return work;
         }

         /**
          * Sets the value of the work property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setWork(Double value)
         {
            this.work = value;
         }

         /**
          * Gets the value of the cost property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Double getCost()
         {
            return cost;
         }

         /**
          * Sets the value of the cost property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setCost(Double value)
         {
            this.cost = value;
         }

         /**
          * Gets the value of the splitOffsetDuration property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getSplitOffsetDuration()
         {
            return splitOffsetDuration;
         }

         /**
          * Sets the value of the splitOffsetDuration property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setSplitOffsetDuration(String value)
         {
            this.splitOffsetDuration = value;
         }

         /**
          * Gets the value of the dateStart property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public LocalDateTime getDateStart()
         {
            return dateStart;
         }

         /**
          * Sets the value of the dateStart property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setDateStart(LocalDateTime value)
         {
            this.dateStart = value;
         }

         /**
          * Gets the value of the rowID property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getRowID()
         {
            return rowID;
         }

         /**
          * Sets the value of the rowID property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setRowID(Integer value)
         {
            this.rowID = value;
         }

         /**
          * Gets the value of the startText property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getStartText()
         {
            return startText;
         }

         /**
          * Sets the value of the startText property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setStartText(String value)
         {
            this.startText = value;
         }

         /**
          * Gets the value of the name property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getName()
         {
            return name;
         }

         /**
          * Sets the value of the name property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setName(String value)
         {
            this.name = value;
         }

         /**
          * Gets the value of the actualFinish property.
          *
          * @return
          *     possible object is
          *     {@link Long }
          *
          */
         public Long getActualFinish()
         {
            return actualFinish;
         }

         /**
          * Sets the value of the actualFinish property.
          *
          * @param value
          *     allowed object is
          *     {@link Long }
          *
          */
         public void setActualFinish(Long value)
         {
            this.actualFinish = value;
         }

         /**
          * Gets the value of the firstWidth property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getFirstWidth()
         {
            return firstWidth;
         }

         /**
          * Sets the value of the firstWidth property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setFirstWidth(Integer value)
         {
            this.firstWidth = value;
         }

         /**
          * Gets the value of the dateLateFinish property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public LocalDateTime getDateLateFinish()
         {
            return dateLateFinish;
         }

         /**
          * Sets the value of the dateLateFinish property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setDateLateFinish(LocalDateTime value)
         {
            this.dateLateFinish = value;
         }

         /**
          * Gets the value of the countWidth property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getCountWidth()
         {
            return countWidth;
         }

         /**
          * Sets the value of the countWidth property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setCountWidth(Integer value)
         {
            this.countWidth = value;
         }

         /**
          * Gets the value of the priority property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getPriority()
         {
            return priority;
         }

         /**
          * Sets the value of the priority property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setPriority(Integer value)
         {
            this.priority = value;
         }

         /**
          * Gets the value of the dateManualStart property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public LocalDateTime getDateManualStart()
         {
            return dateManualStart;
         }

         /**
          * Sets the value of the dateManualStart property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setDateManualStart(LocalDateTime value)
         {
            this.dateManualStart = value;
         }

         /**
          * Gets the value of the dateBaseFinish property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public LocalDateTime getDateBaseFinish()
         {
            return dateBaseFinish;
         }

         /**
          * Sets the value of the dateBaseFinish property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setDateBaseFinish(LocalDateTime value)
         {
            this.dateBaseFinish = value;
         }

         /**
          * Gets the value of the showByChild property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getShowByChild()
         {
            return showByChild;
         }

         /**
          * Sets the value of the showByChild property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setShowByChild(Integer value)
         {
            this.showByChild = value;
         }

         /**
          * Gets the value of the actualMilestone property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Boolean isActualMilestone()
         {
            return actualMilestone;
         }

         /**
          * Sets the value of the actualMilestone property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setActualMilestone(Boolean value)
         {
            this.actualMilestone = value;
         }

         /**
          * Gets the value of the parentID property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getParentID()
         {
            return parentID;
         }

         /**
          * Sets the value of the parentID property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setParentID(Integer value)
         {
            this.parentID = value;
         }

         /**
          * Gets the value of the lateSlack property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getLateSlack()
         {
            return lateSlack;
         }

         /**
          * Sets the value of the lateSlack property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setLateSlack(String value)
         {
            this.lateSlack = value;
         }

         /**
          * Gets the value of the dateFinish property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public LocalDateTime getDateFinish()
         {
            return dateFinish;
         }

         /**
          * Sets the value of the dateFinish property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setDateFinish(LocalDateTime value)
         {
            this.dateFinish = value;
         }

         /**
          * Gets the value of the wbs property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getWbs()
         {
            return wbs;
         }

         /**
          * Sets the value of the wbs property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setWbs(String value)
         {
            this.wbs = value;
         }

         /**
          * Gets the value of the notes property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getNotes()
         {
            return notes;
         }

         /**
          * Sets the value of the notes property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setNotes(String value)
         {
            this.notes = value;
         }

         /**
          * Gets the value of the baseLineNumber property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Integer getBaseLineNumber()
         {
            return baseLineNumber;
         }

         /**
          * Sets the value of the baseLineNumber property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setBaseLineNumber(Integer value)
         {
            this.baseLineNumber = value;
         }

         /**
          * Gets the value of the hideID property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Boolean isHideID()
         {
            return hideID;
         }

         /**
          * Sets the value of the hideID property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setHideID(Boolean value)
         {
            this.hideID = value;
         }

         /**
          * Gets the value of the splitPointList property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getSplitPointList()
         {
            return splitPointList;
         }

         /**
          * Sets the value of the splitPointList property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setSplitPointList(String value)
         {
            this.splitPointList = value;
         }

         /**
          * Gets the value of the percent property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Double getPercent()
         {
            return percent;
         }

         /**
          * Sets the value of the percent property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setPercent(Double value)
         {
            this.percent = value;
         }

         /**
          * Gets the value of the remainingCost property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public Double getRemainingCost()
         {
            return remainingCost;
         }

         /**
          * Sets the value of the remainingCost property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setRemainingCost(Double value)
         {
            this.remainingCost = value;
         }

         /**
          * Gets the value of the earlySlack property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getEarlySlack()
         {
            return earlySlack;
         }

         /**
          * Sets the value of the earlySlack property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setEarlySlack(String value)
         {
            this.earlySlack = value;
         }

         /**
          * Gets the value of the childs property.
          *
          * @return
          *     possible object is
          *     {@link String }
          *
          */
         public String getChilds()
         {
            return childs;
         }

         /**
          * Sets the value of the childs property.
          *
          * @param value
          *     allowed object is
          *     {@link String }
          *
          */
         public void setChilds(String value)
         {
            this.childs = value;
         }

         /**
          * <p>Java class for anonymous complex type.
          *
          * <p>The following schema fragment specifies the expected content contained within this class.
          *
          * <pre>
          * &lt;complexType&gt;
          *   &lt;complexContent&gt;
          *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *       &lt;attribute name="Family" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
          *       &lt;attribute name="Bold" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *       &lt;attribute name="Underline" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *       &lt;attribute name="StrikeOut" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *       &lt;attribute name="PointSize" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *       &lt;attribute name="Italic" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *     &lt;/restriction&gt;
          *   &lt;/complexContent&gt;
          * &lt;/complexType&gt;
          * </pre>
          *
          *
          */
         @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Format
         {

            @XmlAttribute(name = "Family") protected String family;
            @XmlAttribute(name = "Bold") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer bold;
            @XmlAttribute(name = "Underline") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer underline;
            @XmlAttribute(name = "StrikeOut") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer strikeOut;
            @XmlAttribute(name = "PointSize") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer pointSize;
            @XmlAttribute(name = "Italic") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer italic;

            /**
             * Gets the value of the family property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public String getFamily()
            {
               return family;
            }

            /**
             * Sets the value of the family property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setFamily(String value)
            {
               this.family = value;
            }

            /**
             * Gets the value of the bold property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Integer getBold()
            {
               return bold;
            }

            /**
             * Sets the value of the bold property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setBold(Integer value)
            {
               this.bold = value;
            }

            /**
             * Gets the value of the underline property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Integer getUnderline()
            {
               return underline;
            }

            /**
             * Sets the value of the underline property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setUnderline(Integer value)
            {
               this.underline = value;
            }

            /**
             * Gets the value of the strikeOut property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Integer getStrikeOut()
            {
               return strikeOut;
            }

            /**
             * Sets the value of the strikeOut property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setStrikeOut(Integer value)
            {
               this.strikeOut = value;
            }

            /**
             * Gets the value of the pointSize property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Integer getPointSize()
            {
               return pointSize;
            }

            /**
             * Sets the value of the pointSize property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setPointSize(Integer value)
            {
               this.pointSize = value;
            }

            /**
             * Gets the value of the italic property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Integer getItalic()
            {
               return italic;
            }

            /**
             * Sets the value of the italic property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setItalic(Integer value)
            {
               this.italic = value;
            }

         }

         /**
          * <p>Java class for anonymous complex type.
          *
          * <p>The following schema fragment specifies the expected content contained within this class.
          *
          * <pre>
          * &lt;complexType&gt;
          *   &lt;complexContent&gt;
          *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *       &lt;sequence&gt;
          *         &lt;element name="PredecessorUID" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
          *         &lt;element name="Type" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
          *         &lt;element name="LinkLag" type="{http://www.w3.org/2001/XMLSchema}long"/&gt;
          *         &lt;element name="LagFormat" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
          *         &lt;element name="CrossProject" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
          *       &lt;/sequence&gt;
          *     &lt;/restriction&gt;
          *   &lt;/complexContent&gt;
          * &lt;/complexType&gt;
          * </pre>
          *
          *
          */
         @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
         {
            "predecessorUID",
            "type",
            "linkLag",
            "lagFormat",
            "crossProject"
         }) public static class PredecessorLink
         {

            @XmlElement(name = "PredecessorUID", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer predecessorUID;
            @XmlElement(name = "Type", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer type;
            @XmlElement(name = "LinkLag") protected long linkLag;
            @XmlElement(name = "LagFormat", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer lagFormat;
            @XmlElement(name = "CrossProject", required = true, type = String.class) @XmlJavaTypeAdapter(Adapter1.class) @XmlSchemaType(name = "boolean") protected Boolean crossProject;

            /**
             * Gets the value of the predecessorUID property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Integer getPredecessorUID()
            {
               return predecessorUID;
            }

            /**
             * Sets the value of the predecessorUID property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setPredecessorUID(Integer value)
            {
               this.predecessorUID = value;
            }

            /**
             * Gets the value of the type property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Integer getType()
            {
               return type;
            }

            /**
             * Sets the value of the type property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setType(Integer value)
            {
               this.type = value;
            }

            /**
             * Gets the value of the linkLag property.
             *
             */
            public long getLinkLag()
            {
               return linkLag;
            }

            /**
             * Sets the value of the linkLag property.
             *
             */
            public void setLinkLag(long value)
            {
               this.linkLag = value;
            }

            /**
             * Gets the value of the lagFormat property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Integer getLagFormat()
            {
               return lagFormat;
            }

            /**
             * Sets the value of the lagFormat property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setLagFormat(Integer value)
            {
               this.lagFormat = value;
            }

            /**
             * Gets the value of the crossProject property.
             *
             * @return
             *     possible object is
             *     {@link String }
             *
             */
            public Boolean isCrossProject()
            {
               return crossProject;
            }

            /**
             * Sets the value of the crossProject property.
             *
             * @param value
             *     allowed object is
             *     {@link String }
             *
             */
            public void setCrossProject(Boolean value)
            {
               this.crossProject = value;
            }

         }

         /**
          * <p>Java class for anonymous complex type.
          *
          * <p>The following schema fragment specifies the expected content contained within this class.
          *
          * <pre>
          * &lt;complexType&gt;
          *   &lt;complexContent&gt;
          *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *       &lt;sequence&gt;
          *         &lt;element name="Resource" maxOccurs="unbounded"&gt;
          *           &lt;complexType&gt;
          *             &lt;complexContent&gt;
          *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                 &lt;attribute name="ID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *                 &lt;attribute name="CostUnit" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *                 &lt;attribute name="Cost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
          *                 &lt;attribute name="CostPer" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
          *                 &lt;attribute name="Type" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *                 &lt;attribute name="WorkSecs" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
          *                 &lt;attribute name="OvertimeUnit" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *                 &lt;attribute name="Percent" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
          *                 &lt;attribute name="OvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
          *                 &lt;attribute name="Name" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
          *               &lt;/restriction&gt;
          *             &lt;/complexContent&gt;
          *           &lt;/complexType&gt;
          *         &lt;/element&gt;
          *       &lt;/sequence&gt;
          *     &lt;/restriction&gt;
          *   &lt;/complexContent&gt;
          * &lt;/complexType&gt;
          * </pre>
          *
          *
          */
         @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
         {
            "resource"
         }) public static class ResourceList
         {

            @XmlElement(name = "Resource", required = true) protected List<Document.TaskList.Task.ResourceList.Resource> resource;

            /**
             * Gets the value of the resource property.
             *
             * <p>
             * This accessor method returns a reference to the live list,
             * not a snapshot. Therefore any modification you make to the
             * returned list will be present inside the Jakarta XML Binding object.
             * This is why there is not a <CODE>set</CODE> method for the resource property.
             *
             * <p>
             * For example, to add a new item, do as follows:
             * <pre>
             *    getResource().add(newItem);
             * </pre>
             *
             *
             * <p>
             * Objects of the following type(s) are allowed in the list
             * {@link Document.TaskList.Task.ResourceList.Resource }
             *
             *
             */
            public List<Document.TaskList.Task.ResourceList.Resource> getResource()
            {
               if (resource == null)
               {
                  resource = new ArrayList<>();
               }
               return this.resource;
            }

            /**
             * <p>Java class for anonymous complex type.
             *
             * <p>The following schema fragment specifies the expected content contained within this class.
             *
             * <pre>
             * &lt;complexType&gt;
             *   &lt;complexContent&gt;
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *       &lt;attribute name="ID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
             *       &lt;attribute name="CostUnit" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
             *       &lt;attribute name="Cost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
             *       &lt;attribute name="CostPer" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
             *       &lt;attribute name="Type" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
             *       &lt;attribute name="WorkSecs" type="{http://www.w3.org/2001/XMLSchema}long" /&gt;
             *       &lt;attribute name="OvertimeUnit" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
             *       &lt;attribute name="Percent" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
             *       &lt;attribute name="OvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" /&gt;
             *       &lt;attribute name="Name" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
             *     &lt;/restriction&gt;
             *   &lt;/complexContent&gt;
             * &lt;/complexType&gt;
             * </pre>
             *
             *
             */
            @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Resource
            {

               @XmlAttribute(name = "ID") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer id;
               @XmlAttribute(name = "CostUnit") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer costUnit;
               @XmlAttribute(name = "Cost") @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "double") protected Double cost;
               @XmlAttribute(name = "CostPer") @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "double") protected Double costPer;
               @XmlAttribute(name = "Type") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer type;
               @XmlAttribute(name = "WorkSecs") protected Long workSecs;
               @XmlAttribute(name = "OvertimeUnit") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer overtimeUnit;
               @XmlAttribute(name = "Percent") @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "double") protected Double percent;
               @XmlAttribute(name = "OvertimeCost") @XmlJavaTypeAdapter(Adapter2.class) @XmlSchemaType(name = "double") protected Double overtimeCost;
               @XmlAttribute(name = "Name") protected String name;

               /**
                * Gets the value of the id property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public Integer getID()
               {
                  return id;
               }

               /**
                * Sets the value of the id property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setID(Integer value)
               {
                  this.id = value;
               }

               /**
                * Gets the value of the costUnit property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public Integer getCostUnit()
               {
                  return costUnit;
               }

               /**
                * Sets the value of the costUnit property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setCostUnit(Integer value)
               {
                  this.costUnit = value;
               }

               /**
                * Gets the value of the cost property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public Double getCost()
               {
                  return cost;
               }

               /**
                * Sets the value of the cost property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setCost(Double value)
               {
                  this.cost = value;
               }

               /**
                * Gets the value of the costPer property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public Double getCostPer()
               {
                  return costPer;
               }

               /**
                * Sets the value of the costPer property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setCostPer(Double value)
               {
                  this.costPer = value;
               }

               /**
                * Gets the value of the type property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public Integer getType()
               {
                  return type;
               }

               /**
                * Sets the value of the type property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setType(Integer value)
               {
                  this.type = value;
               }

               /**
                * Gets the value of the workSecs property.
                *
                * @return
                *     possible object is
                *     {@link Long }
                *
                */
               public Long getWorkSecs()
               {
                  return workSecs;
               }

               /**
                * Sets the value of the workSecs property.
                *
                * @param value
                *     allowed object is
                *     {@link Long }
                *
                */
               public void setWorkSecs(Long value)
               {
                  this.workSecs = value;
               }

               /**
                * Gets the value of the overtimeUnit property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public Integer getOvertimeUnit()
               {
                  return overtimeUnit;
               }

               /**
                * Sets the value of the overtimeUnit property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setOvertimeUnit(Integer value)
               {
                  this.overtimeUnit = value;
               }

               /**
                * Gets the value of the percent property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public Double getPercent()
               {
                  return percent;
               }

               /**
                * Sets the value of the percent property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setPercent(Double value)
               {
                  this.percent = value;
               }

               /**
                * Gets the value of the overtimeCost property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public Double getOvertimeCost()
               {
                  return overtimeCost;
               }

               /**
                * Sets the value of the overtimeCost property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setOvertimeCost(Double value)
               {
                  this.overtimeCost = value;
               }

               /**
                * Gets the value of the name property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public String getName()
               {
                  return name;
               }

               /**
                * Sets the value of the name property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setName(String value)
               {
                  this.name = value;
               }

            }

         }

         /**
          * <p>Java class for anonymous complex type.
          *
          * <p>The following schema fragment specifies the expected content contained within this class.
          *
          * <pre>
          * &lt;complexType&gt;
          *   &lt;complexContent&gt;
          *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *       &lt;sequence&gt;
          *         &lt;element name="TextCell" maxOccurs="unbounded"&gt;
          *           &lt;complexType&gt;
          *             &lt;complexContent&gt;
          *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                 &lt;sequence&gt;
          *                   &lt;element name="Varient" type="{http://www.w3.org/2001/XMLSchema}anyType"/&gt;
          *                   &lt;element name="TextBlock"&gt;
          *                     &lt;complexType&gt;
          *                       &lt;complexContent&gt;
          *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                           &lt;sequence&gt;
          *                             &lt;element name="Character"&gt;
          *                               &lt;complexType&gt;
          *                                 &lt;complexContent&gt;
          *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                                     &lt;attribute name="Family" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
          *                                     &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *                                     &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *                                   &lt;/restriction&gt;
          *                                 &lt;/complexContent&gt;
          *                               &lt;/complexType&gt;
          *                             &lt;/element&gt;
          *                             &lt;element name="Paragraph"&gt;
          *                               &lt;complexType&gt;
          *                                 &lt;complexContent&gt;
          *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                                     &lt;attribute name="Align" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *                                     &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *                                     &lt;attribute name="SpLine" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *                                   &lt;/restriction&gt;
          *                                 &lt;/complexContent&gt;
          *                               &lt;/complexType&gt;
          *                             &lt;/element&gt;
          *                             &lt;element name="WrapMode"&gt;
          *                               &lt;complexType&gt;
          *                                 &lt;complexContent&gt;
          *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                                     &lt;attribute name="Value" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *                                   &lt;/restriction&gt;
          *                                 &lt;/complexContent&gt;
          *                               &lt;/complexType&gt;
          *                             &lt;/element&gt;
          *                             &lt;element name="FillColor"&gt;
          *                               &lt;complexType&gt;
          *                                 &lt;complexContent&gt;
          *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                                     &lt;attribute name="Color" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
          *                                   &lt;/restriction&gt;
          *                                 &lt;/complexContent&gt;
          *                               &lt;/complexType&gt;
          *                             &lt;/element&gt;
          *                             &lt;element name="Color"&gt;
          *                               &lt;complexType&gt;
          *                                 &lt;complexContent&gt;
          *                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
          *                                     &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
          *                                   &lt;/restriction&gt;
          *                                 &lt;/complexContent&gt;
          *                               &lt;/complexType&gt;
          *                             &lt;/element&gt;
          *                           &lt;/sequence&gt;
          *                           &lt;attribute name="VAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
          *                           &lt;attribute name="HAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
          *                           &lt;attribute name="TextFormatMask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *                         &lt;/restriction&gt;
          *                       &lt;/complexContent&gt;
          *                     &lt;/complexType&gt;
          *                   &lt;/element&gt;
          *                 &lt;/sequence&gt;
          *                 &lt;attribute name="FieldType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *                 &lt;attribute name="PlainText" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
          *                 &lt;attribute name="FieldID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *                 &lt;attribute name="DataType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
          *               &lt;/restriction&gt;
          *             &lt;/complexContent&gt;
          *           &lt;/complexType&gt;
          *         &lt;/element&gt;
          *       &lt;/sequence&gt;
          *     &lt;/restriction&gt;
          *   &lt;/complexContent&gt;
          * &lt;/complexType&gt;
          * </pre>
          *
          *
          */
         @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
         {
            "textCell"
         }) public static class Texts
         {

            @XmlElement(name = "TextCell", required = true) protected List<Document.TaskList.Task.Texts.TextCell> textCell;

            /**
             * Gets the value of the textCell property.
             *
             * <p>
             * This accessor method returns a reference to the live list,
             * not a snapshot. Therefore any modification you make to the
             * returned list will be present inside the Jakarta XML Binding object.
             * This is why there is not a <CODE>set</CODE> method for the textCell property.
             *
             * <p>
             * For example, to add a new item, do as follows:
             * <pre>
             *    getTextCell().add(newItem);
             * </pre>
             *
             *
             * <p>
             * Objects of the following type(s) are allowed in the list
             * {@link Document.TaskList.Task.Texts.TextCell }
             *
             *
             */
            public List<Document.TaskList.Task.Texts.TextCell> getTextCell()
            {
               if (textCell == null)
               {
                  textCell = new ArrayList<>();
               }
               return this.textCell;
            }

            /**
             * <p>Java class for anonymous complex type.
             *
             * <p>The following schema fragment specifies the expected content contained within this class.
             *
             * <pre>
             * &lt;complexType&gt;
             *   &lt;complexContent&gt;
             *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *       &lt;sequence&gt;
             *         &lt;element name="Varient" type="{http://www.w3.org/2001/XMLSchema}anyType"/&gt;
             *         &lt;element name="TextBlock"&gt;
             *           &lt;complexType&gt;
             *             &lt;complexContent&gt;
             *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *                 &lt;sequence&gt;
             *                   &lt;element name="Character"&gt;
             *                     &lt;complexType&gt;
             *                       &lt;complexContent&gt;
             *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *                           &lt;attribute name="Family" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
             *                           &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
             *                           &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
             *                         &lt;/restriction&gt;
             *                       &lt;/complexContent&gt;
             *                     &lt;/complexType&gt;
             *                   &lt;/element&gt;
             *                   &lt;element name="Paragraph"&gt;
             *                     &lt;complexType&gt;
             *                       &lt;complexContent&gt;
             *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *                           &lt;attribute name="Align" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
             *                           &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
             *                           &lt;attribute name="SpLine" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
             *                         &lt;/restriction&gt;
             *                       &lt;/complexContent&gt;
             *                     &lt;/complexType&gt;
             *                   &lt;/element&gt;
             *                   &lt;element name="WrapMode"&gt;
             *                     &lt;complexType&gt;
             *                       &lt;complexContent&gt;
             *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *                           &lt;attribute name="Value" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
             *                         &lt;/restriction&gt;
             *                       &lt;/complexContent&gt;
             *                     &lt;/complexType&gt;
             *                   &lt;/element&gt;
             *                   &lt;element name="FillColor"&gt;
             *                     &lt;complexType&gt;
             *                       &lt;complexContent&gt;
             *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *                           &lt;attribute name="Color" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
             *                         &lt;/restriction&gt;
             *                       &lt;/complexContent&gt;
             *                     &lt;/complexType&gt;
             *                   &lt;/element&gt;
             *                   &lt;element name="Color"&gt;
             *                     &lt;complexType&gt;
             *                       &lt;complexContent&gt;
             *                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
             *                           &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
             *                         &lt;/restriction&gt;
             *                       &lt;/complexContent&gt;
             *                     &lt;/complexType&gt;
             *                   &lt;/element&gt;
             *                 &lt;/sequence&gt;
             *                 &lt;attribute name="VAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
             *                 &lt;attribute name="HAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
             *                 &lt;attribute name="TextFormatMask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
             *               &lt;/restriction&gt;
             *             &lt;/complexContent&gt;
             *           &lt;/complexType&gt;
             *         &lt;/element&gt;
             *       &lt;/sequence&gt;
             *       &lt;attribute name="FieldType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
             *       &lt;attribute name="PlainText" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
             *       &lt;attribute name="FieldID" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
             *       &lt;attribute name="DataType" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
             *     &lt;/restriction&gt;
             *   &lt;/complexContent&gt;
             * &lt;/complexType&gt;
             * </pre>
             *
             *
             */
            @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
            {
               "varient",
               "textBlock"
            }) public static class TextCell
            {

               @XmlElement(name = "Varient", required = true) protected Object varient;
               @XmlElement(name = "TextBlock", required = true) protected Document.TaskList.Task.Texts.TextCell.TextBlock textBlock;
               @XmlAttribute(name = "FieldType") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer fieldType;
               @XmlAttribute(name = "PlainText") protected String plainText;
               @XmlAttribute(name = "FieldID") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer fieldID;
               @XmlAttribute(name = "DataType") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer dataType;

               /**
                * Gets the value of the varient property.
                *
                * @return
                *     possible object is
                *     {@link Object }
                *
                */
               public Object getVarient()
               {
                  return varient;
               }

               /**
                * Sets the value of the varient property.
                *
                * @param value
                *     allowed object is
                *     {@link Object }
                *
                */
               public void setVarient(Object value)
               {
                  this.varient = value;
               }

               /**
                * Gets the value of the textBlock property.
                *
                * @return
                *     possible object is
                *     {@link Document.TaskList.Task.Texts.TextCell.TextBlock }
                *
                */
               public Document.TaskList.Task.Texts.TextCell.TextBlock getTextBlock()
               {
                  return textBlock;
               }

               /**
                * Sets the value of the textBlock property.
                *
                * @param value
                *     allowed object is
                *     {@link Document.TaskList.Task.Texts.TextCell.TextBlock }
                *
                */
               public void setTextBlock(Document.TaskList.Task.Texts.TextCell.TextBlock value)
               {
                  this.textBlock = value;
               }

               /**
                * Gets the value of the fieldType property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public Integer getFieldType()
               {
                  return fieldType;
               }

               /**
                * Sets the value of the fieldType property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setFieldType(Integer value)
               {
                  this.fieldType = value;
               }

               /**
                * Gets the value of the plainText property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public String getPlainText()
               {
                  return plainText;
               }

               /**
                * Sets the value of the plainText property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setPlainText(String value)
               {
                  this.plainText = value;
               }

               /**
                * Gets the value of the fieldID property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public Integer getFieldID()
               {
                  return fieldID;
               }

               /**
                * Sets the value of the fieldID property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setFieldID(Integer value)
               {
                  this.fieldID = value;
               }

               /**
                * Gets the value of the dataType property.
                *
                * @return
                *     possible object is
                *     {@link String }
                *
                */
               public Integer getDataType()
               {
                  return dataType;
               }

               /**
                * Sets the value of the dataType property.
                *
                * @param value
                *     allowed object is
                *     {@link String }
                *
                */
               public void setDataType(Integer value)
               {
                  this.dataType = value;
               }

               /**
                * <p>Java class for anonymous complex type.
                *
                * <p>The following schema fragment specifies the expected content contained within this class.
                *
                * <pre>
                * &lt;complexType&gt;
                *   &lt;complexContent&gt;
                *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                *       &lt;sequence&gt;
                *         &lt;element name="Character"&gt;
                *           &lt;complexType&gt;
                *             &lt;complexContent&gt;
                *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                *                 &lt;attribute name="Family" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                *                 &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                *                 &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                *               &lt;/restriction&gt;
                *             &lt;/complexContent&gt;
                *           &lt;/complexType&gt;
                *         &lt;/element&gt;
                *         &lt;element name="Paragraph"&gt;
                *           &lt;complexType&gt;
                *             &lt;complexContent&gt;
                *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                *                 &lt;attribute name="Align" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                *                 &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                *                 &lt;attribute name="SpLine" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                *               &lt;/restriction&gt;
                *             &lt;/complexContent&gt;
                *           &lt;/complexType&gt;
                *         &lt;/element&gt;
                *         &lt;element name="WrapMode"&gt;
                *           &lt;complexType&gt;
                *             &lt;complexContent&gt;
                *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                *                 &lt;attribute name="Value" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                *               &lt;/restriction&gt;
                *             &lt;/complexContent&gt;
                *           &lt;/complexType&gt;
                *         &lt;/element&gt;
                *         &lt;element name="FillColor"&gt;
                *           &lt;complexType&gt;
                *             &lt;complexContent&gt;
                *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                *                 &lt;attribute name="Color" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                *               &lt;/restriction&gt;
                *             &lt;/complexContent&gt;
                *           &lt;/complexType&gt;
                *         &lt;/element&gt;
                *         &lt;element name="Color"&gt;
                *           &lt;complexType&gt;
                *             &lt;complexContent&gt;
                *               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                *                 &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                *               &lt;/restriction&gt;
                *             &lt;/complexContent&gt;
                *           &lt;/complexType&gt;
                *         &lt;/element&gt;
                *       &lt;/sequence&gt;
                *       &lt;attribute name="VAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                *       &lt;attribute name="HAlign" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                *       &lt;attribute name="TextFormatMask" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                *     &lt;/restriction&gt;
                *   &lt;/complexContent&gt;
                * &lt;/complexType&gt;
                * </pre>
                *
                *
                */
               @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "", propOrder =
               {
                  "character",
                  "paragraph",
                  "wrapMode",
                  "fillColor",
                  "color"
               }) public static class TextBlock
               {

                  @XmlElement(name = "Character", required = true) protected Document.TaskList.Task.Texts.TextCell.TextBlock.Character character;
                  @XmlElement(name = "Paragraph", required = true) protected Document.TaskList.Task.Texts.TextCell.TextBlock.Paragraph paragraph;
                  @XmlElement(name = "WrapMode", required = true) protected Document.TaskList.Task.Texts.TextCell.TextBlock.WrapMode wrapMode;
                  @XmlElement(name = "FillColor", required = true) protected Document.TaskList.Task.Texts.TextCell.TextBlock.FillColor fillColor;
                  @XmlElement(name = "Color", required = true) protected Document.TaskList.Task.Texts.TextCell.TextBlock.Color color;
                  @XmlAttribute(name = "VAlign") protected String vAlign;
                  @XmlAttribute(name = "HAlign") protected String hAlign;
                  @XmlAttribute(name = "TextFormatMask") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer textFormatMask;

                  /**
                   * Gets the value of the character property.
                   *
                   * @return
                   *     possible object is
                   *     {@link Document.TaskList.Task.Texts.TextCell.TextBlock.Character }
                   *
                   */
                  public Document.TaskList.Task.Texts.TextCell.TextBlock.Character getCharacter()
                  {
                     return character;
                  }

                  /**
                   * Sets the value of the character property.
                   *
                   * @param value
                   *     allowed object is
                   *     {@link Document.TaskList.Task.Texts.TextCell.TextBlock.Character }
                   *
                   */
                  public void setCharacter(Document.TaskList.Task.Texts.TextCell.TextBlock.Character value)
                  {
                     this.character = value;
                  }

                  /**
                   * Gets the value of the paragraph property.
                   *
                   * @return
                   *     possible object is
                   *     {@link Document.TaskList.Task.Texts.TextCell.TextBlock.Paragraph }
                   *
                   */
                  public Document.TaskList.Task.Texts.TextCell.TextBlock.Paragraph getParagraph()
                  {
                     return paragraph;
                  }

                  /**
                   * Sets the value of the paragraph property.
                   *
                   * @param value
                   *     allowed object is
                   *     {@link Document.TaskList.Task.Texts.TextCell.TextBlock.Paragraph }
                   *
                   */
                  public void setParagraph(Document.TaskList.Task.Texts.TextCell.TextBlock.Paragraph value)
                  {
                     this.paragraph = value;
                  }

                  /**
                   * Gets the value of the wrapMode property.
                   *
                   * @return
                   *     possible object is
                   *     {@link Document.TaskList.Task.Texts.TextCell.TextBlock.WrapMode }
                   *
                   */
                  public Document.TaskList.Task.Texts.TextCell.TextBlock.WrapMode getWrapMode()
                  {
                     return wrapMode;
                  }

                  /**
                   * Sets the value of the wrapMode property.
                   *
                   * @param value
                   *     allowed object is
                   *     {@link Document.TaskList.Task.Texts.TextCell.TextBlock.WrapMode }
                   *
                   */
                  public void setWrapMode(Document.TaskList.Task.Texts.TextCell.TextBlock.WrapMode value)
                  {
                     this.wrapMode = value;
                  }

                  /**
                   * Gets the value of the fillColor property.
                   *
                   * @return
                   *     possible object is
                   *     {@link Document.TaskList.Task.Texts.TextCell.TextBlock.FillColor }
                   *
                   */
                  public Document.TaskList.Task.Texts.TextCell.TextBlock.FillColor getFillColor()
                  {
                     return fillColor;
                  }

                  /**
                   * Sets the value of the fillColor property.
                   *
                   * @param value
                   *     allowed object is
                   *     {@link Document.TaskList.Task.Texts.TextCell.TextBlock.FillColor }
                   *
                   */
                  public void setFillColor(Document.TaskList.Task.Texts.TextCell.TextBlock.FillColor value)
                  {
                     this.fillColor = value;
                  }

                  /**
                   * Gets the value of the color property.
                   *
                   * @return
                   *     possible object is
                   *     {@link Document.TaskList.Task.Texts.TextCell.TextBlock.Color }
                   *
                   */
                  public Document.TaskList.Task.Texts.TextCell.TextBlock.Color getColor()
                  {
                     return color;
                  }

                  /**
                   * Sets the value of the color property.
                   *
                   * @param value
                   *     allowed object is
                   *     {@link Document.TaskList.Task.Texts.TextCell.TextBlock.Color }
                   *
                   */
                  public void setColor(Document.TaskList.Task.Texts.TextCell.TextBlock.Color value)
                  {
                     this.color = value;
                  }

                  /**
                   * Gets the value of the vAlign property.
                   *
                   * @return
                   *     possible object is
                   *     {@link String }
                   *
                   */
                  public String getVAlign()
                  {
                     return vAlign;
                  }

                  /**
                   * Sets the value of the vAlign property.
                   *
                   * @param value
                   *     allowed object is
                   *     {@link String }
                   *
                   */
                  public void setVAlign(String value)
                  {
                     this.vAlign = value;
                  }

                  /**
                   * Gets the value of the hAlign property.
                   *
                   * @return
                   *     possible object is
                   *     {@link String }
                   *
                   */
                  public String getHAlign()
                  {
                     return hAlign;
                  }

                  /**
                   * Sets the value of the hAlign property.
                   *
                   * @param value
                   *     allowed object is
                   *     {@link String }
                   *
                   */
                  public void setHAlign(String value)
                  {
                     this.hAlign = value;
                  }

                  /**
                   * Gets the value of the textFormatMask property.
                   *
                   * @return
                   *     possible object is
                   *     {@link String }
                   *
                   */
                  public Integer getTextFormatMask()
                  {
                     return textFormatMask;
                  }

                  /**
                   * Sets the value of the textFormatMask property.
                   *
                   * @param value
                   *     allowed object is
                   *     {@link String }
                   *
                   */
                  public void setTextFormatMask(Integer value)
                  {
                     this.textFormatMask = value;
                  }

                  /**
                   * <p>Java class for anonymous complex type.
                   *
                   * <p>The following schema fragment specifies the expected content contained within this class.
                   *
                   * <pre>
                   * &lt;complexType&gt;
                   *   &lt;complexContent&gt;
                   *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                   *       &lt;attribute name="Family" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                   *       &lt;attribute name="Size" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                   *       &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                   *     &lt;/restriction&gt;
                   *   &lt;/complexContent&gt;
                   * &lt;/complexType&gt;
                   * </pre>
                   *
                   *
                   */
                  @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Character
                  {

                     @XmlAttribute(name = "Family") protected String family;
                     @XmlAttribute(name = "Size") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer size;
                     @XmlAttribute(name = "IX") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer ix;

                     /**
                      * Gets the value of the family property.
                      *
                      * @return
                      *     possible object is
                      *     {@link String }
                      *
                      */
                     public String getFamily()
                     {
                        return family;
                     }

                     /**
                      * Sets the value of the family property.
                      *
                      * @param value
                      *     allowed object is
                      *     {@link String }
                      *
                      */
                     public void setFamily(String value)
                     {
                        this.family = value;
                     }

                     /**
                      * Gets the value of the size property.
                      *
                      * @return
                      *     possible object is
                      *     {@link String }
                      *
                      */
                     public Integer getSize()
                     {
                        return size;
                     }

                     /**
                      * Sets the value of the size property.
                      *
                      * @param value
                      *     allowed object is
                      *     {@link String }
                      *
                      */
                     public void setSize(Integer value)
                     {
                        this.size = value;
                     }

                     /**
                      * Gets the value of the ix property.
                      *
                      * @return
                      *     possible object is
                      *     {@link String }
                      *
                      */
                     public Integer getIX()
                     {
                        return ix;
                     }

                     /**
                      * Sets the value of the ix property.
                      *
                      * @param value
                      *     allowed object is
                      *     {@link String }
                      *
                      */
                     public void setIX(Integer value)
                     {
                        this.ix = value;
                     }

                  }

                  /**
                   * <p>Java class for anonymous complex type.
                   *
                   * <p>The following schema fragment specifies the expected content contained within this class.
                   *
                   * <pre>
                   * &lt;complexType&gt;
                   *   &lt;complexContent&gt;
                   *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                   *       &lt;attribute name="V" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                   *     &lt;/restriction&gt;
                   *   &lt;/complexContent&gt;
                   * &lt;/complexType&gt;
                   * </pre>
                   *
                   *
                   */
                  @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Color
                  {

                     @XmlAttribute(name = "V") protected String v;

                     /**
                      * Gets the value of the v property.
                      *
                      * @return
                      *     possible object is
                      *     {@link String }
                      *
                      */
                     public String getV()
                     {
                        return v;
                     }

                     /**
                      * Sets the value of the v property.
                      *
                      * @param value
                      *     allowed object is
                      *     {@link String }
                      *
                      */
                     public void setV(String value)
                     {
                        this.v = value;
                     }

                  }

                  /**
                   * <p>Java class for anonymous complex type.
                   *
                   * <p>The following schema fragment specifies the expected content contained within this class.
                   *
                   * <pre>
                   * &lt;complexType&gt;
                   *   &lt;complexContent&gt;
                   *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                   *       &lt;attribute name="Color" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
                   *     &lt;/restriction&gt;
                   *   &lt;/complexContent&gt;
                   * &lt;/complexType&gt;
                   * </pre>
                   *
                   *
                   */
                  @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class FillColor
                  {

                     @XmlAttribute(name = "Color") protected String color;

                     /**
                      * Gets the value of the color property.
                      *
                      * @return
                      *     possible object is
                      *     {@link String }
                      *
                      */
                     public String getColor()
                     {
                        return color;
                     }

                     /**
                      * Sets the value of the color property.
                      *
                      * @param value
                      *     allowed object is
                      *     {@link String }
                      *
                      */
                     public void setColor(String value)
                     {
                        this.color = value;
                     }

                  }

                  /**
                   * <p>Java class for anonymous complex type.
                   *
                   * <p>The following schema fragment specifies the expected content contained within this class.
                   *
                   * <pre>
                   * &lt;complexType&gt;
                   *   &lt;complexContent&gt;
                   *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                   *       &lt;attribute name="Align" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                   *       &lt;attribute name="IX" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                   *       &lt;attribute name="SpLine" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                   *     &lt;/restriction&gt;
                   *   &lt;/complexContent&gt;
                   * &lt;/complexType&gt;
                   * </pre>
                   *
                   *
                   */
                  @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class Paragraph
                  {

                     @XmlAttribute(name = "Align") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer align;
                     @XmlAttribute(name = "IX") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer ix;
                     @XmlAttribute(name = "SpLine") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer spLine;

                     /**
                      * Gets the value of the align property.
                      *
                      * @return
                      *     possible object is
                      *     {@link String }
                      *
                      */
                     public Integer getAlign()
                     {
                        return align;
                     }

                     /**
                      * Sets the value of the align property.
                      *
                      * @param value
                      *     allowed object is
                      *     {@link String }
                      *
                      */
                     public void setAlign(Integer value)
                     {
                        this.align = value;
                     }

                     /**
                      * Gets the value of the ix property.
                      *
                      * @return
                      *     possible object is
                      *     {@link String }
                      *
                      */
                     public Integer getIX()
                     {
                        return ix;
                     }

                     /**
                      * Sets the value of the ix property.
                      *
                      * @param value
                      *     allowed object is
                      *     {@link String }
                      *
                      */
                     public void setIX(Integer value)
                     {
                        this.ix = value;
                     }

                     /**
                      * Gets the value of the spLine property.
                      *
                      * @return
                      *     possible object is
                      *     {@link String }
                      *
                      */
                     public Integer getSpLine()
                     {
                        return spLine;
                     }

                     /**
                      * Sets the value of the spLine property.
                      *
                      * @param value
                      *     allowed object is
                      *     {@link String }
                      *
                      */
                     public void setSpLine(Integer value)
                     {
                        this.spLine = value;
                     }

                  }

                  /**
                   * <p>Java class for anonymous complex type.
                   *
                   * <p>The following schema fragment specifies the expected content contained within this class.
                   *
                   * <pre>
                   * &lt;complexType&gt;
                   *   &lt;complexContent&gt;
                   *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                   *       &lt;attribute name="Value" type="{http://www.w3.org/2001/XMLSchema}int" /&gt;
                   *     &lt;/restriction&gt;
                   *   &lt;/complexContent&gt;
                   * &lt;/complexType&gt;
                   * </pre>
                   *
                   *
                   */
                  @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class WrapMode
                  {

                     @XmlAttribute(name = "Value") @XmlJavaTypeAdapter(Adapter5.class) @XmlSchemaType(name = "int") protected Integer value;

                     /**
                      * Gets the value of the value property.
                      *
                      * @return
                      *     possible object is
                      *     {@link String }
                      *
                      */
                     public Integer getValue()
                     {
                        return value;
                     }

                     /**
                      * Sets the value of the value property.
                      *
                      * @param value
                      *     allowed object is
                      *     {@link String }
                      *
                      */
                     public void setValue(Integer value)
                     {
                        this.value = value;
                     }

                  }

               }

            }

         }

      }

   }

   /**
    * <p>Java class for anonymous complex type.
    *
    * <p>The following schema fragment specifies the expected content contained within this class.
    *
    * <pre>
    * &lt;complexType&gt;
    *   &lt;complexContent&gt;
    *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
    *       &lt;attribute name="Type" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
    *     &lt;/restriction&gt;
    *   &lt;/complexContent&gt;
    * &lt;/complexType&gt;
    * </pre>
    *
    *
    */
   @XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "") public static class WaterMark
   {

      @XmlAttribute(name = "Type") protected String type;

      /**
       * Gets the value of the type property.
       *
       * @return
       *     possible object is
       *     {@link String }
       *
       */
      public String getType()
      {
         return type;
      }

      /**
       * Sets the value of the type property.
       *
       * @param value
       *     allowed object is
       *     {@link String }
       *
       */
      public void setType(String value)
      {
         this.type = value;
      }

   }

}
