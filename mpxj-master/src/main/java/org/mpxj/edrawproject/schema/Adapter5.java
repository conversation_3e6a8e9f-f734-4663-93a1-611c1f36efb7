//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.2
// See https://eclipse-ee4j.github.io/jaxb-ri
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2025.05.06 at 12:54:08 PM BST
//

package org.mpxj.edrawproject.schema;

import jakarta.xml.bind.annotation.adapters.XmlAdapter;

public class Adapter5
         extends
            XmlAdapter<String, Integer>
{

   @Override public Integer unmarshal(String value)
   {
      return (org.mpxj.edrawproject.DatatypeConverter.parseInteger(value));
   }

   @Override public String marshal(Integer value)
   {
      return (org.mpxj.edrawproject.DatatypeConverter.printInteger(value));
   }

}
