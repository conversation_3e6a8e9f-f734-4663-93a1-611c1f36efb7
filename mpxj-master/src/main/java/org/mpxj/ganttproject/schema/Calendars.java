//
// This file was generated by the Eclipse Implementation of JAXB, v3.0.2
// See https://eclipse-ee4j.github.io/jaxb-ri
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2024.04.25 at 10:03:47 AM BST
//

package org.mpxj.ganttproject.schema;

import java.util.ArrayList;
import java.util.List;
import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlAttribute;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlType;

/**
 * <p>Java class for calendars complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="calendars"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="day-types" type="{}day-types"/&gt;
 *         &lt;element name="date" type="{}date" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *       &lt;attribute name="base-id" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD) @XmlType(name = "calendars", propOrder =
{
   "dayTypes",
   "date"
}) public class Calendars
{

   @XmlElement(name = "day-types", required = true) protected DayTypes dayTypes;
   protected List<Date> date;
   @XmlAttribute(name = "base-id") protected String baseId;

   /**
    * Gets the value of the dayTypes property.
    *
    * @return
    *     possible object is
    *     {@link DayTypes }
    *
    */
   public DayTypes getDayTypes()
   {
      return dayTypes;
   }

   /**
    * Sets the value of the dayTypes property.
    *
    * @param value
    *     allowed object is
    *     {@link DayTypes }
    *
    */
   public void setDayTypes(DayTypes value)
   {
      this.dayTypes = value;
   }

   /**
    * Gets the value of the date property.
    *
    * <p>
    * This accessor method returns a reference to the live list,
    * not a snapshot. Therefore any modification you make to the
    * returned list will be present inside the Jakarta XML Binding object.
    * This is why there is not a <CODE>set</CODE> method for the date property.
    *
    * <p>
    * For example, to add a new item, do as follows:
    * <pre>
    *    getDate().add(newItem);
    * </pre>
    *
    *
    * <p>
    * Objects of the following type(s) are allowed in the list
    * {@link Date }
    *
    *
    */
   public List<Date> getDate()
   {
      if (date == null)
      {
         date = new ArrayList<>();
      }
      return this.date;
   }

   /**
    * Gets the value of the baseId property.
    *
    * @return
    *     possible object is
    *     {@link String }
    *
    */
   public String getBaseId()
   {
      return baseId;
   }

   /**
    * Sets the value of the baseId property.
    *
    * @param value
    *     allowed object is
    *     {@link String }
    *
    */
   public void setBaseId(String value)
   {
      this.baseId = value;
   }

}
