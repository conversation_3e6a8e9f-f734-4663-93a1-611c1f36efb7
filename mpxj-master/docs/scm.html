<!DOCTYPE html>
<!--
 | Generated by Apache Maven Doxia Site Renderer 1.9.2 from org.apache.maven.plugins:maven-project-info-reports-plugin:3.1.1:scm
 | Rendered using Apache Maven Fluido Skin 1.9
-->
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="generator" content="Apache Maven Doxia Site Renderer 1.9.2" />
    <title>MPXJ &#x2013; Source Code Management</title>
    <link rel="stylesheet" href="./css/apache-maven-fluido-1.9.min.css" />
    <link rel="stylesheet" href="./css/site.css" />
    <link rel="stylesheet" href="./css/print.css" media="print" />
    <script src="./js/apache-maven-fluido-1.9.min.js"></script>
  </head>
  <body class="topBarDisabled">
    <a href="https://github.com/joniles/mpxj">
      <img style="position: absolute; top: 0; right: 0; border: 0; z-index: 10000;"
        src="https://s3.amazonaws.com/github/ribbons/forkme_right_darkblue_121621.png"
        alt="Fork me on GitHub">
    </a>
    <div class="container-fluid">
      <header>
        <div id="banner">
          <div class="pull-left"><div id="bannerLeft"><h2>MPXJ</h2>
</div>
</div>
          <div class="pull-right"></div>
          <div class="clear"><hr/></div>
        </div>

        <div id="breadcrumbs">
          <ul class="breadcrumb">
        <li id="publishDate">Last Published: 2025-06-05</li>
          </ul>
        </div>
      </header>
      <div class="row-fluid">
        <header id="leftColumn" class="span3">
          <nav class="well sidebar-nav">
  <ul class="nav nav-list">
   <li class="nav-header">MPXJ</li>
    <li><a href="summary.html" title="Summary"><span class="none"></span>Summary</a></li>
    <li><a href="changes-report.html" title="Changes"><span class="none"></span>Changes</a></li>
    <li><a href="team.html" title="Team"><span class="none"></span>Team</a></li>
    <li><a href="mailing-lists.html" title="Mailing List"><span class="none"></span>Mailing List</a></li>
    <li><a href="issue-management.html" title="Issues"><span class="none"></span>Issues</a></li>
    <li class="active"><a href="#"><span class="none"></span>Source</a></li>
    <li><a href="apidocs/index.html" title="Javadoc"><span class="none"></span>Javadoc</a></li>
    <li><a href="dependency-updates-report.html" title="Dependencies"><span class="none"></span>Dependencies</a></li>
    <li><a href="plugin-updates-report.html" title="Plugins"><span class="none"></span>Plugins</a></li>
  </ul>
          </nav>
          <div class="well sidebar-nav">
            <hr />
            <div id="poweredBy">
              <div class="clear"></div>
              <div class="clear"></div>
              <div class="clear"></div>
<a href="http://maven.apache.org/" title="Built by Maven" class="poweredBy"><img class="builtBy" alt="Built by Maven" src="./images/logos/maven-feather.png" /></a>
            </div>
          </div>
        </header>
        <main id="bodyColumn"  class="span9" >
<section>
<h2><a name="Overview"></a>Overview</h2><a name="Overview"></a>
<p>This project uses <a class="externalLink" href="https://git-scm.com/">Git</a> to manage its source code. Instructions on Git use can be found at <a class="externalLink" href="https://git-scm.com/documentation">https://git-scm.com/documentation</a>.</p></section><section>
<h2><a name="Web_Browser_Access"></a>Web Browser Access</h2><a name="Web_Browser_Access"></a>
<p>The following is a link to a browsable version of the source repository:</p>
<div class="source"><pre class="prettyprint"><a class="externalLink" href="https://github.com/joniles/mpxj">https://github.com/joniles/mpxj</a></pre></div></section><section>
<h2><a name="Anonymous_Access"></a>Anonymous Access</h2><a name="Anonymous_Access"></a>
<p>The source can be checked out anonymously from Git with this command (See <a class="externalLink" href="https://git-scm.com/docs/git-clone">https://git-scm.com/docs/git-clone</a>):</p>
<div class="source"><pre class="prettyprint">$ git clone git://github.com/joniles/mpxj.git</pre></div></section><section>
<h2><a name="Developer_Access"></a>Developer Access</h2><a name="Developer_Access"></a>
<p>Only project developers can access the Git tree via this method (See <a class="externalLink" href="https://git-scm.com/docs/git-clone">https://git-scm.com/docs/git-clone</a>).</p>
<div class="source"><pre class="prettyprint">$ <NAME_EMAIL>:joniles/mpxj.git</pre></div></section><section>
<h2><a name="Access_from_Behind_a_Firewall"></a>Access from Behind a Firewall</h2><a name="Access_from_Behind_a_Firewall"></a>
<p>Refer to the documentation of the SCM used for more information about access behind a firewall.</p></section>
        </main>
      </div>
    </div>
    <hr/>
    <footer>
      <div class="container-fluid">
        <div class="row-fluid">
            <p>&#169;      2000&#x2013;2025
<a href="http://mpxj.org">MPXJ</a>
</p>
        </div>
      </div>
    </footer>
  </body>
</html>
