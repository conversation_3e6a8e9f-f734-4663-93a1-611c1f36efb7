<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>EventManager (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="EventManager (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/EventManager.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../org/mpxj/EpsProjectNode.html" title="class in org.mpxj"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../org/mpxj/ExpenseCategory.html" title="class in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/EventManager.html" target="_top">Frames</a></li>
<li><a href="EventManager.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj</div>
<h2 title="Class EventManager" class="title">Class EventManager</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.EventManager</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">EventManager</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">Provides subscriptions to events raised when project files are written and read.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/EventManager.html#EventManager--">EventManager</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/EventManager.html#addProjectListener-org.mpxj.listener.ProjectListener-">addProjectListener</a></span>(<a href="../../org/mpxj/listener/ProjectListener.html" title="interface in org.mpxj.listener">ProjectListener</a>&nbsp;listener)</code>
<div class="block">Adds a listener to this project file.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/EventManager.html#addProjectListeners-java.util.List-">addProjectListeners</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/listener/ProjectListener.html" title="interface in org.mpxj.listener">ProjectListener</a>&gt;&nbsp;listeners)</code>
<div class="block">Adds a collection of listeners to the current project.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/EventManager.html#fireAssignmentReadEvent-org.mpxj.ResourceAssignment-">fireAssignmentReadEvent</a></span>(<a href="../../org/mpxj/ResourceAssignment.html" title="class in org.mpxj">ResourceAssignment</a>&nbsp;resourceAssignment)</code>
<div class="block">This method is called to alert project listeners to the fact that
 a resource assignment has been read from a project file.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/EventManager.html#fireAssignmentWrittenEvent-org.mpxj.ResourceAssignment-">fireAssignmentWrittenEvent</a></span>(<a href="../../org/mpxj/ResourceAssignment.html" title="class in org.mpxj">ResourceAssignment</a>&nbsp;resourceAssignment)</code>
<div class="block">This method is called to alert project listeners to the fact that
 a resource assignment has been written to a project file.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/EventManager.html#fireCalendarReadEvent-org.mpxj.ProjectCalendar-">fireCalendarReadEvent</a></span>(<a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;calendar)</code>
<div class="block">This method is called to alert project listeners to the fact that
 a calendar has been read from a project file.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/EventManager.html#fireCalendarWrittenEvent-org.mpxj.ProjectCalendar-">fireCalendarWrittenEvent</a></span>(<a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;calendar)</code>
<div class="block">This method is called to alert project listeners to the fact that
 a calendar has been written to a project file.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/EventManager.html#fireRelationReadEvent-org.mpxj.Relation-">fireRelationReadEvent</a></span>(<a href="../../org/mpxj/Relation.html" title="class in org.mpxj">Relation</a>&nbsp;relation)</code>
<div class="block">This method is called to alert project listeners to the fact that
 a relation has been read from a project file.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/EventManager.html#fireRelationWrittenEvent-org.mpxj.Relation-">fireRelationWrittenEvent</a></span>(<a href="../../org/mpxj/Relation.html" title="class in org.mpxj">Relation</a>&nbsp;relation)</code>
<div class="block">This method is called to alert project listeners to the fact that
 a relation has been written to a project file.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/EventManager.html#fireResourceReadEvent-org.mpxj.Resource-">fireResourceReadEvent</a></span>(<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;resource)</code>
<div class="block">This method is called to alert project listeners to the fact that
 a resource has been read from a project file.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/EventManager.html#fireResourceWrittenEvent-org.mpxj.Resource-">fireResourceWrittenEvent</a></span>(<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;resource)</code>
<div class="block">This method is called to alert project listeners to the fact that
 a resource has been written to a project file.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/EventManager.html#fireTaskReadEvent-org.mpxj.Task-">fireTaskReadEvent</a></span>(<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;task)</code>
<div class="block">This method is called to alert project listeners to the fact that
 a task has been read from a project file.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/EventManager.html#fireTaskWrittenEvent-org.mpxj.Task-">fireTaskWrittenEvent</a></span>(<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;task)</code>
<div class="block">This method is called to alert project listeners to the fact that
 a task has been written to a project file.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/EventManager.html#removeProjectListener-org.mpxj.listener.ProjectListener-">removeProjectListener</a></span>(<a href="../../org/mpxj/listener/ProjectListener.html" title="interface in org.mpxj.listener">ProjectListener</a>&nbsp;listener)</code>
<div class="block">Removes a listener from this project file.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="EventManager--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>EventManager</h4>
<pre>public&nbsp;EventManager()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="fireTaskReadEvent-org.mpxj.Task-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fireTaskReadEvent</h4>
<pre>public&nbsp;void&nbsp;fireTaskReadEvent(<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;task)</pre>
<div class="block">This method is called to alert project listeners to the fact that
 a task has been read from a project file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>task</code> - task instance</dd>
</dl>
</li>
</ul>
<a name="fireTaskWrittenEvent-org.mpxj.Task-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fireTaskWrittenEvent</h4>
<pre>public&nbsp;void&nbsp;fireTaskWrittenEvent(<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;task)</pre>
<div class="block">This method is called to alert project listeners to the fact that
 a task has been written to a project file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>task</code> - task instance</dd>
</dl>
</li>
</ul>
<a name="fireResourceReadEvent-org.mpxj.Resource-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fireResourceReadEvent</h4>
<pre>public&nbsp;void&nbsp;fireResourceReadEvent(<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;resource)</pre>
<div class="block">This method is called to alert project listeners to the fact that
 a resource has been read from a project file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>resource</code> - resource instance</dd>
</dl>
</li>
</ul>
<a name="fireResourceWrittenEvent-org.mpxj.Resource-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fireResourceWrittenEvent</h4>
<pre>public&nbsp;void&nbsp;fireResourceWrittenEvent(<a href="../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;resource)</pre>
<div class="block">This method is called to alert project listeners to the fact that
 a resource has been written to a project file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>resource</code> - resource instance</dd>
</dl>
</li>
</ul>
<a name="fireCalendarReadEvent-org.mpxj.ProjectCalendar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fireCalendarReadEvent</h4>
<pre>public&nbsp;void&nbsp;fireCalendarReadEvent(<a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;calendar)</pre>
<div class="block">This method is called to alert project listeners to the fact that
 a calendar has been read from a project file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>calendar</code> - calendar instance</dd>
</dl>
</li>
</ul>
<a name="fireAssignmentReadEvent-org.mpxj.ResourceAssignment-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fireAssignmentReadEvent</h4>
<pre>public&nbsp;void&nbsp;fireAssignmentReadEvent(<a href="../../org/mpxj/ResourceAssignment.html" title="class in org.mpxj">ResourceAssignment</a>&nbsp;resourceAssignment)</pre>
<div class="block">This method is called to alert project listeners to the fact that
 a resource assignment has been read from a project file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>resourceAssignment</code> - resourceAssignment instance</dd>
</dl>
</li>
</ul>
<a name="fireAssignmentWrittenEvent-org.mpxj.ResourceAssignment-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fireAssignmentWrittenEvent</h4>
<pre>public&nbsp;void&nbsp;fireAssignmentWrittenEvent(<a href="../../org/mpxj/ResourceAssignment.html" title="class in org.mpxj">ResourceAssignment</a>&nbsp;resourceAssignment)</pre>
<div class="block">This method is called to alert project listeners to the fact that
 a resource assignment has been written to a project file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>resourceAssignment</code> - resourceAssignment instance</dd>
</dl>
</li>
</ul>
<a name="fireRelationReadEvent-org.mpxj.Relation-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fireRelationReadEvent</h4>
<pre>public&nbsp;void&nbsp;fireRelationReadEvent(<a href="../../org/mpxj/Relation.html" title="class in org.mpxj">Relation</a>&nbsp;relation)</pre>
<div class="block">This method is called to alert project listeners to the fact that
 a relation has been read from a project file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>relation</code> - relation instance</dd>
</dl>
</li>
</ul>
<a name="fireRelationWrittenEvent-org.mpxj.Relation-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fireRelationWrittenEvent</h4>
<pre>public&nbsp;void&nbsp;fireRelationWrittenEvent(<a href="../../org/mpxj/Relation.html" title="class in org.mpxj">Relation</a>&nbsp;relation)</pre>
<div class="block">This method is called to alert project listeners to the fact that
 a relation has been written to a project file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>relation</code> - relation instance</dd>
</dl>
</li>
</ul>
<a name="fireCalendarWrittenEvent-org.mpxj.ProjectCalendar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>fireCalendarWrittenEvent</h4>
<pre>public&nbsp;void&nbsp;fireCalendarWrittenEvent(<a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;calendar)</pre>
<div class="block">This method is called to alert project listeners to the fact that
 a calendar has been written to a project file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>calendar</code> - calendar instance</dd>
</dl>
</li>
</ul>
<a name="addProjectListener-org.mpxj.listener.ProjectListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addProjectListener</h4>
<pre>public&nbsp;void&nbsp;addProjectListener(<a href="../../org/mpxj/listener/ProjectListener.html" title="interface in org.mpxj.listener">ProjectListener</a>&nbsp;listener)</pre>
<div class="block">Adds a listener to this project file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - listener instance</dd>
</dl>
</li>
</ul>
<a name="addProjectListeners-java.util.List-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addProjectListeners</h4>
<pre>public&nbsp;void&nbsp;addProjectListeners(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../org/mpxj/listener/ProjectListener.html" title="interface in org.mpxj.listener">ProjectListener</a>&gt;&nbsp;listeners)</pre>
<div class="block">Adds a collection of listeners to the current project.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listeners</code> - collection of listeners</dd>
</dl>
</li>
</ul>
<a name="removeProjectListener-org.mpxj.listener.ProjectListener-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>removeProjectListener</h4>
<pre>public&nbsp;void&nbsp;removeProjectListener(<a href="../../org/mpxj/listener/ProjectListener.html" title="interface in org.mpxj.listener">ProjectListener</a>&nbsp;listener)</pre>
<div class="block">Removes a listener from this project file.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - listener instance</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/EventManager.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../org/mpxj/EpsProjectNode.html" title="class in org.mpxj"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../org/mpxj/ExpenseCategory.html" title="class in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/EventManager.html" target="_top">Frames</a></li>
<li><a href="EventManager.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
