<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>org.mpxj.ganttdesigner.schema Class Hierarchy (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.mpxj.ganttdesigner.schema Class Hierarchy (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/ganttdesigner/package-tree.html">Prev</a></li>
<li><a href="../../../../org/mpxj/ganttproject/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/ganttdesigner/schema/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">Hierarchy For Package org.mpxj.ganttdesigner.schema</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li type="circle">java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang"><span class="typeNameLink">Object</span></a>
<ul>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.BarStyles.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.BarStyles</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Calendar.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.Calendar</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.ChartColor.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.ChartColor</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Columns.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.Columns</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Columns.Header.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.Columns.Header</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Copy.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.Copy</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.DateHeader</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.Reference.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.DateHeader.Reference</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.DateHeader.Tier.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.DateHeader.Tier</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Display.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.Display</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.File.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.File</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.FirstDay.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.FirstDay</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Footers.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.Footers</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.FootersFonts.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.FootersFonts</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.FootersFonts.Font.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.FootersFonts.Font</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Globalization.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.Globalization</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Globalization.Culture.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.Globalization.Culture</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Globalization.Currency.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.Globalization.Currency</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Globalization.UICulture.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.Globalization.UICulture</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Headers.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.Headers</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.HeadersFonts.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.HeadersFonts</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.HeadersFonts.Font.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.HeadersFonts.Font</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Holidays.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.Holidays</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Holidays.Holiday.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.Holidays.Holiday</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.LastDay.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.LastDay</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Padding.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.Padding</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Print.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.Print</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.PrintToImageFile.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.PrintToImageFile</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Tasks.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.Tasks</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.Tasks.Task.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.Tasks.Task</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.TextStyles.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.TextStyles</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Gantt.TextStyles.Font.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Gantt.TextStyles.Font</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">GanttDesignerRemark</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/GanttDesignerRemark.Task.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">GanttDesignerRemark.Task</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/ObjectFactory.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">ObjectFactory</span></a></li>
<li type="circle">jakarta.xml.bind.annotation.adapters.XmlAdapter&lt;ValueType,BoundType&gt;
<ul>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Adapter1.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Adapter1</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Adapter2.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Adapter2</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Adapter3.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Adapter3</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Adapter4.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Adapter4</span></a></li>
<li type="circle">org.mpxj.ganttdesigner.schema.<a href="../../../../org/mpxj/ganttdesigner/schema/Adapter5.html" title="class in org.mpxj.ganttdesigner.schema"><span class="typeNameLink">Adapter5</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/ganttdesigner/package-tree.html">Prev</a></li>
<li><a href="../../../../org/mpxj/ganttproject/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/ganttdesigner/schema/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
