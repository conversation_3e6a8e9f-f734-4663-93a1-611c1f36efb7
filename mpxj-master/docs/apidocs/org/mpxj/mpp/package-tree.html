<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>org.mpxj.mpp Class Hierarchy (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.mpxj.mpp Class Hierarchy (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/mpxj/mpd/package-tree.html">Prev</a></li>
<li><a href="../../../org/mpxj/mpx/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/mpp/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">Hierarchy For Package org.mpxj.mpp</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li type="circle">java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang"><span class="typeNameLink">Object</span></a>
<ul>
<li type="circle">org.mpxj.reader.<a href="../../../org/mpxj/reader/AbstractProjectReader.html" title="class in org.mpxj.reader"><span class="typeNameLink">AbstractProjectReader</span></a> (implements org.mpxj.reader.<a href="../../../org/mpxj/reader/ProjectReader.html" title="interface in org.mpxj.reader">ProjectReader</a>)
<ul>
<li type="circle">org.mpxj.reader.<a href="../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader"><span class="typeNameLink">AbstractProjectStreamReader</span></a>
<ul>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/MPPReader.html" title="class in org.mpxj.mpp"><span class="typeNameLink">MPPReader</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">org.mpxj.common.<a href="../../../org/mpxj/common/AbstractTimephasedWorkNormaliser.html" title="class in org.mpxj.common"><span class="typeNameLink">AbstractTimephasedWorkNormaliser</span></a> (implements org.mpxj.common.<a href="../../../org/mpxj/common/TimephasedNormaliser.html" title="interface in org.mpxj.common">TimephasedNormaliser</a>&lt;T&gt;)
<ul>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/MPPAbstractTimephasedWorkNormaliser.html" title="class in org.mpxj.mpp"><span class="typeNameLink">MPPAbstractTimephasedWorkNormaliser</span></a>
<ul>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/MPPTimephasedBaselineWorkNormaliser.html" title="class in org.mpxj.mpp"><span class="typeNameLink">MPPTimephasedBaselineWorkNormaliser</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/MPPTimephasedWorkNormaliser.html" title="class in org.mpxj.mpp"><span class="typeNameLink">MPPTimephasedWorkNormaliser</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/AbstractView.html" title="class in org.mpxj.mpp"><span class="typeNameLink">AbstractView</span></a> (implements org.mpxj.<a href="../../../org/mpxj/View.html" title="interface in org.mpxj">View</a>)
<ul>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/AbstractMppView.html" title="class in org.mpxj.mpp"><span class="typeNameLink">AbstractMppView</span></a>
<ul>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GenericView.html" title="class in org.mpxj.mpp"><span class="typeNameLink">GenericView</span></a>
<ul>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GanttChartView.html" title="class in org.mpxj.mpp"><span class="typeNameLink">GanttChartView</span></a>
<ul>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GanttChartView12.html" title="class in org.mpxj.mpp"><span class="typeNameLink">GanttChartView12</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GanttChartView14.html" title="class in org.mpxj.mpp"><span class="typeNameLink">GanttChartView14</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GanttChartView9.html" title="class in org.mpxj.mpp"><span class="typeNameLink">GanttChartView9</span></a></li>
</ul>
</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GenericView12.html" title="class in org.mpxj.mpp"><span class="typeNameLink">GenericView12</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GenericView14.html" title="class in org.mpxj.mpp"><span class="typeNameLink">GenericView14</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GenericView9.html" title="class in org.mpxj.mpp"><span class="typeNameLink">GenericView9</span></a>
<ul>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/SplitView9.html" title="class in org.mpxj.mpp"><span class="typeNameLink">SplitView9</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/View8.html" title="class in org.mpxj.mpp"><span class="typeNameLink">View8</span></a></li>
</ul>
</li>
</ul>
</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/ApplicationVersion.html" title="class in org.mpxj.mpp"><span class="typeNameLink">ApplicationVersion</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/ConstraintFactory.html" title="class in org.mpxj.mpp"><span class="typeNameLink">ConstraintFactory</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/CriteriaReader.html" title="class in org.mpxj.mpp"><span class="typeNameLink">CriteriaReader</span></a>
<ul>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/FilterCriteriaReader12.html" title="class in org.mpxj.mpp"><span class="typeNameLink">FilterCriteriaReader12</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/FilterCriteriaReader14.html" title="class in org.mpxj.mpp"><span class="typeNameLink">FilterCriteriaReader14</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/FilterCriteriaReader9.html" title="class in org.mpxj.mpp"><span class="typeNameLink">FilterCriteriaReader9</span></a></li>
</ul>
</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/CustomFieldValueItem.html" title="class in org.mpxj.mpp"><span class="typeNameLink">CustomFieldValueItem</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/CustomFieldValueReader9.html" title="class in org.mpxj.mpp"><span class="typeNameLink">CustomFieldValueReader9</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/EnterpriseCustomFieldDataType.html" title="class in org.mpxj.mpp"><span class="typeNameLink">EnterpriseCustomFieldDataType</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/FilterReader.html" title="class in org.mpxj.mpp"><span class="typeNameLink">FilterReader</span></a>
<ul>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/FilterReader12.html" title="class in org.mpxj.mpp"><span class="typeNameLink">FilterReader12</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/FilterReader14.html" title="class in org.mpxj.mpp"><span class="typeNameLink">FilterReader14</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/FilterReader9.html" title="class in org.mpxj.mpp"><span class="typeNameLink">FilterReader9</span></a></li>
</ul>
</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/FontBase.html" title="class in org.mpxj.mpp"><span class="typeNameLink">FontBase</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/FontStyle.html" title="class in org.mpxj.mpp"><span class="typeNameLink">FontStyle</span></a>
<ul>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/TableFontStyle.html" title="class in org.mpxj.mpp"><span class="typeNameLink">TableFontStyle</span></a></li>
</ul>
</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GanttBarCommonStyle.html" title="class in org.mpxj.mpp"><span class="typeNameLink">GanttBarCommonStyle</span></a>
<ul>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GanttBarStyle.html" title="class in org.mpxj.mpp"><span class="typeNameLink">GanttBarStyle</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GanttBarStyleException.html" title="class in org.mpxj.mpp"><span class="typeNameLink">GanttBarStyleException</span></a></li>
</ul>
</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GanttBarStyleFactory14.html" title="class in org.mpxj.mpp"><span class="typeNameLink">GanttBarStyleFactory14</span></a> (implements org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GanttBarStyleFactory.html" title="interface in org.mpxj.mpp">GanttBarStyleFactory</a>)</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GanttBarStyleFactoryCommon.html" title="class in org.mpxj.mpp"><span class="typeNameLink">GanttBarStyleFactoryCommon</span></a> (implements org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GanttBarStyleFactory.html" title="interface in org.mpxj.mpp">GanttBarStyleFactory</a>)</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GraphicalIndicatorReader.html" title="class in org.mpxj.mpp"><span class="typeNameLink">GraphicalIndicatorReader</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GridLines.html" title="class in org.mpxj.mpp"><span class="typeNameLink">GridLines</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GroupReader.html" title="class in org.mpxj.mpp"><span class="typeNameLink">GroupReader</span></a>
<ul>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GroupReader12.html" title="class in org.mpxj.mpp"><span class="typeNameLink">GroupReader12</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GroupReader9.html" title="class in org.mpxj.mpp"><span class="typeNameLink">GroupReader9</span></a></li>
</ul>
</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GroupReader14.html" title="class in org.mpxj.mpp"><span class="typeNameLink">GroupReader14</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/MppBitFlag.html" title="class in org.mpxj.mpp"><span class="typeNameLink">MppBitFlag</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/MPPTimephasedBaselineCostNormaliser.html" title="class in org.mpxj.mpp"><span class="typeNameLink">MPPTimephasedBaselineCostNormaliser</span></a> (implements org.mpxj.common.<a href="../../../org/mpxj/common/TimephasedNormaliser.html" title="interface in org.mpxj.common">TimephasedNormaliser</a>&lt;T&gt;)</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/MPPUtility.html" title="class in org.mpxj.mpp"><span class="typeNameLink">MPPUtility</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/ProjectPropertiesReader.html" title="class in org.mpxj.mpp"><span class="typeNameLink">ProjectPropertiesReader</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/ResourceAssignmentFactory.html" title="class in org.mpxj.mpp"><span class="typeNameLink">ResourceAssignmentFactory</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/RTFEmbeddedObject.html" title="class in org.mpxj.mpp"><span class="typeNameLink">RTFEmbeddedObject</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/TaskTypeHelper.html" title="class in org.mpxj.mpp"><span class="typeNameLink">TaskTypeHelper</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/TimescaleTier.html" title="class in org.mpxj.mpp"><span class="typeNameLink">TimescaleTier</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/UserDefinedFieldMap.html" title="class in org.mpxj.mpp"><span class="typeNameLink">UserDefinedFieldMap</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/ViewStateReader.html" title="class in org.mpxj.mpp"><span class="typeNameLink">ViewStateReader</span></a>
<ul>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/ViewStateReader12.html" title="class in org.mpxj.mpp"><span class="typeNameLink">ViewStateReader12</span></a></li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/ViewStateReader9.html" title="class in org.mpxj.mpp"><span class="typeNameLink">ViewStateReader9</span></a></li>
</ul>
</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/WorkContourHelper.html" title="class in org.mpxj.mpp"><span class="typeNameLink">WorkContourHelper</span></a></li>
</ul>
</li>
</ul>
<h2 title="Interface Hierarchy">Interface Hierarchy</h2>
<ul>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GanttBarStyleFactory.html" title="interface in org.mpxj.mpp"><span class="typeNameLink">GanttBarStyleFactory</span></a></li>
</ul>
<h2 title="Enum Hierarchy">Enum Hierarchy</h2>
<ul>
<li type="circle">java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang"><span class="typeNameLink">Object</span></a>
<ul>
<li type="circle">java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Enum.html?is-external=true" title="class or interface in java.lang"><span class="typeNameLink">Enum</span></a>&lt;E&gt; (implements java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Comparable.html?is-external=true" title="class or interface in java.lang">Comparable</a>&lt;T&gt;, java.io.<a href="https://docs.oracle.com/javase/8/docs/api/java/io/Serializable.html?is-external=true" title="class or interface in java.io">Serializable</a>)
<ul>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/ProgressLineDay.html" title="enum in org.mpxj.mpp"><span class="typeNameLink">ProgressLineDay</span></a> (implements org.mpxj.<a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a>)</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/BackgroundPattern.html" title="enum in org.mpxj.mpp"><span class="typeNameLink">BackgroundPattern</span></a> (implements org.mpxj.<a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a>)</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/ChartPattern.html" title="enum in org.mpxj.mpp"><span class="typeNameLink">ChartPattern</span></a> (implements org.mpxj.<a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a>)</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/Interval.html" title="enum in org.mpxj.mpp"><span class="typeNameLink">Interval</span></a> (implements org.mpxj.<a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a>)</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/TimescaleAlignment.html" title="enum in org.mpxj.mpp"><span class="typeNameLink">TimescaleAlignment</span></a> (implements org.mpxj.<a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a>)</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GanttBarDateFormat.html" title="enum in org.mpxj.mpp"><span class="typeNameLink">GanttBarDateFormat</span></a> (implements org.mpxj.<a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a>)</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/LineStyle.html" title="enum in org.mpxj.mpp"><span class="typeNameLink">LineStyle</span></a> (implements org.mpxj.<a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a>)</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GanttBarShowForTasks.html" title="enum in org.mpxj.mpp"><span class="typeNameLink">GanttBarShowForTasks</span></a> (implements org.mpxj.<a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a>)</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/ColorType.html" title="enum in org.mpxj.mpp"><span class="typeNameLink">ColorType</span></a> (implements org.mpxj.<a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a>)</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GanttBarStartEndShape.html" title="enum in org.mpxj.mpp"><span class="typeNameLink">GanttBarStartEndShape</span></a> (implements org.mpxj.<a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a>)</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/TimescaleFormat.html" title="enum in org.mpxj.mpp"><span class="typeNameLink">TimescaleFormat</span></a> (implements org.mpxj.<a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a>)</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GanttBarMiddleShape.html" title="enum in org.mpxj.mpp"><span class="typeNameLink">GanttBarMiddleShape</span></a> (implements org.mpxj.<a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a>)</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/GanttBarStartEndType.html" title="enum in org.mpxj.mpp"><span class="typeNameLink">GanttBarStartEndType</span></a> (implements org.mpxj.<a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a>)</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/NonWorkingTimeStyle.html" title="enum in org.mpxj.mpp"><span class="typeNameLink">NonWorkingTimeStyle</span></a> (implements org.mpxj.<a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a>)</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/LinkStyle.html" title="enum in org.mpxj.mpp"><span class="typeNameLink">LinkStyle</span></a> (implements org.mpxj.<a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a>)</li>
<li type="circle">org.mpxj.mpp.<a href="../../../org/mpxj/mpp/TimescaleUnits.html" title="enum in org.mpxj.mpp"><span class="typeNameLink">TimescaleUnits</span></a> (implements org.mpxj.<a href="../../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a>)</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/mpxj/mpd/package-tree.html">Prev</a></li>
<li><a href="../../../org/mpxj/mpx/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/mpp/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
