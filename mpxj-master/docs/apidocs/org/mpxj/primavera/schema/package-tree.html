<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>org.mpxj.primavera.schema Class Hierarchy (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.mpxj.primavera.schema Class Hierarchy (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/primavera/p3/package-tree.html">Prev</a></li>
<li><a href="../../../../org/mpxj/primavera/suretrak/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/primavera/schema/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">Hierarchy For Package org.mpxj.primavera.schema</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li type="circle">java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang"><span class="typeNameLink">Object</span></a>
<ul>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ActivityCodeAssignmentType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ActivityCodeAssignmentType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ActivityCodeType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ActivityCodeType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ActivityCodeTypeType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ActivityCodeTypeType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ActivityCodeUpdateType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ActivityCodeUpdateType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ActivityCommentType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ActivityCommentType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ActivityExpenseType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ActivityExpenseType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ActivityFilterType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ActivityFilterType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ActivityNoteType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ActivityNoteType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ActivityNoteUpdateType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ActivityNoteUpdateType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ActivityOwnerType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ActivityOwnerType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ActivityPeriodActualType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ActivityPeriodActualType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ActivityRiskType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ActivityRiskType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ActivitySpreadType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ActivitySpreadType.Period.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ActivitySpreadType.Period</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ActivityStepCreateType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ActivityStepCreateType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ActivityStepDeleteType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ActivityStepDeleteType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ActivityStepTemplateItemType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ActivityStepTemplateItemType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ActivityStepTemplateType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ActivityStepTemplateType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ActivityStepType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ActivityStepType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ActivityStepUpdateType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ActivityStepUpdateType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ActivityType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ActivityType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ActivityUpdateType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ActivityUpdateType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/AlertType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">AlertType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/APIBusinessObjects.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">APIBusinessObjects</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/AutovueAttrType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">AutovueAttrType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/BaselineProjectType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">BaselineProjectType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/BaselineTypeType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">BaselineTypeType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/CalendarType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">CalendarType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/CalendarType.HolidayOrExceptions.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">CalendarType.HolidayOrExceptions</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/CalendarType.HolidayOrExceptions.HolidayOrException.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">CalendarType.HolidayOrExceptions.HolidayOrException</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/CalendarType.StandardWorkWeek.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">CalendarType.StandardWorkWeek</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/CalendarType.StandardWorkWeek.StandardWorkHours.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">CalendarType.StandardWorkWeek.StandardWorkHours</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/CBSDurationSummaryType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">CBSDurationSummaryType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/CBSType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">CBSType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ChangeSetType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ChangeSetType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/CodeAssignmentType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">CodeAssignmentType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/CostAccountType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">CostAccountType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/CurrencyType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">CurrencyType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/DisplayCurrencyType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">DisplayCurrencyType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/DocumentCategoryType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">DocumentCategoryType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/DocumentStatusCodeType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">DocumentStatusCodeType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/DocumentType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">DocumentType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/EPSBudgetChangeLogType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">EPSBudgetChangeLogType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/EPSFundingType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">EPSFundingType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/EPSNoteType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">EPSNoteType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">EPSProjectWBSSpreadType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">EPSProjectWBSSpreadType.Period</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/EPSSpendingPlanType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">EPSSpendingPlanType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/EPSType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">EPSType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ExpenseCategoryType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ExpenseCategoryType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/FinancialPeriodTemplateType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">FinancialPeriodTemplateType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/FinancialPeriodType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">FinancialPeriodType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/FundingSourceType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">FundingSourceType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/GatewayDeploymentType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">GatewayDeploymentType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/GlobalPreferencesType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">GlobalPreferencesType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/GlobalPrivilegesType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">GlobalPrivilegesType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/GlobalProfileType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">GlobalProfileType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/GlobalReplaceType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">GlobalReplaceType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ImportOptionsTemplateType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ImportOptionsTemplateType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/IssueHistoryType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">IssueHistoryType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/JobServiceType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">JobServiceType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/LeanTaskType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">LeanTaskType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/LocationType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">LocationType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/MSPTemplateType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">MSPTemplateType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/NotebookTopicType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">NotebookTopicType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ObjectFactory.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ObjectFactory</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/OBSType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">OBSType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/OverheadCodeType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">OverheadCodeType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/PAuditXType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">PAuditXType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/PortfolioTeamMemberType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">PortfolioTeamMemberType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ProfileType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ProfileType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ProjectBudgetChangeLogType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ProjectBudgetChangeLogType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ProjectCodeAssignmentType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ProjectCodeAssignmentType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ProjectCodeType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ProjectCodeType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ProjectCodeTypeType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ProjectCodeTypeType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ProjectDeploymentType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ProjectDeploymentType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ProjectDocumentType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ProjectDocumentType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ProjectFundingType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ProjectFundingType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ProjectIssueType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ProjectIssueType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ProjectListType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ProjectListType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ProjectListType.Project.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ProjectListType.Project</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ProjectListType.Project.BaselineProject.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ProjectListType.Project.BaselineProject</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ProjectNoteType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ProjectNoteType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ProjectPortfolioType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ProjectPortfolioType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ProjectPrivilegesType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ProjectPrivilegesType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ProjectProfileType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ProjectProfileType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ProjectResourceCategoryType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ProjectResourceCategoryType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ProjectResourceQuantityType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ProjectResourceQuantityType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ProjectResourceSpreadType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.Period.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ProjectResourceSpreadType.Period</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ProjectResourceType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ProjectResourceType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ProjectRoleSpreadType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ProjectRoleSpreadType.Period.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ProjectRoleSpreadType.Period</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ProjectSpendingPlanType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ProjectSpendingPlanType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ProjectThresholdType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ProjectThresholdType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ProjectType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ProjectType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/RelationshipType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">RelationshipType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ResourceAccessType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ResourceAccessType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentCodeAssignmentType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ResourceAssignmentCodeAssignmentType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentCodeType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ResourceAssignmentCodeType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentCodeTypeType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ResourceAssignmentCodeTypeType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentCreateType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ResourceAssignmentCreateType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentPeriodActualType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ResourceAssignmentPeriodActualType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentSpreadType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ResourceAssignmentSpreadType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentSpreadType.Period.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ResourceAssignmentSpreadType.Period</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ResourceAssignmentType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentUpdateType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ResourceAssignmentUpdateType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ResourceCodeAssignmentType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ResourceCodeAssignmentType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ResourceCodeType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ResourceCodeType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ResourceCodeTypeType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ResourceCodeTypeType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ResourceCurveType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ResourceCurveType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ResourceCurveValuesType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ResourceCurveValuesType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ResourceHourType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ResourceHourType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ResourceLocationType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ResourceLocationType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ResourceRateType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ResourceRateType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ResourceRequestType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ResourceRequestType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ResourceRequestType.ResourceRequestCriterion.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ResourceRequestType.ResourceRequestCriterion</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ResourceRoleType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ResourceRoleType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ResourceTeamType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ResourceTeamType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ResourceType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ResourceType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/RiskCategoryType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">RiskCategoryType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/RiskImpactType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">RiskImpactType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/RiskMatrixScoreType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">RiskMatrixScoreType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/RiskMatrixThresholdType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">RiskMatrixThresholdType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/RiskMatrixType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">RiskMatrixType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/RiskResponseActionImpactType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">RiskResponseActionImpactType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/RiskResponseActionType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">RiskResponseActionType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/RiskResponsePlanType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">RiskResponsePlanType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/RiskThresholdLevelType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">RiskThresholdLevelType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/RiskThresholdType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">RiskThresholdType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/RiskType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">RiskType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/RoleCodeAssignmentType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">RoleCodeAssignmentType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/RoleCodeType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">RoleCodeType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/RoleCodeTypeType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">RoleCodeTypeType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/RoleLimitType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">RoleLimitType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/RoleRateType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">RoleRateType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/RoleTeamType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">RoleTeamType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/RoleType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">RoleType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ScheduleCheckOptionType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ScheduleCheckOptionType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ScheduleOptionsType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ScheduleOptionsType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ShiftPeriodType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ShiftPeriodType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ShiftType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ShiftType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/StepUserDefinedValueUpdateType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">StepUserDefinedValueUpdateType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/ThresholdParameterType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">ThresholdParameterType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/TimesheetAuditType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">TimesheetAuditType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/TimesheetDelegateType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">TimesheetDelegateType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/TimesheetPeriodType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">TimesheetPeriodType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/TimesheetType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">TimesheetType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/UDFAssignmentType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">UDFAssignmentType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/UDFCodeType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">UDFCodeType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/UDFTypeType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">UDFTypeType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/UDFValueType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">UDFValueType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/UnitOfMeasureType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">UnitOfMeasureType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/UpdateBaselineOptionType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">UpdateBaselineOptionType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/UserConsentType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">UserConsentType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/UserDefinedValueUpdateType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">UserDefinedValueUpdateType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/UserFieldTitleType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">UserFieldTitleType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/UserInterfaceViewType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">UserInterfaceViewType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/UserLicenseType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">UserLicenseType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/UserOBSType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">UserOBSType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/UserType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">UserType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/UserType.ResourceRequests.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">UserType.ResourceRequests</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/WBSCategoryType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">WBSCategoryType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/WBSMilestoneType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">WBSMilestoneType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/WbsReviewersType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">WbsReviewersType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/WBSType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">WBSType</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/WorkTimeType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">WorkTimeType</span></a></li>
<li type="circle">jakarta.xml.bind.annotation.adapters.XmlAdapter&lt;ValueType,BoundType&gt;
<ul>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/Adapter1.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Adapter1</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/Adapter2.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Adapter2</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/Adapter3.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Adapter3</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/Adapter4.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Adapter4</span></a></li>
<li type="circle">org.mpxj.primavera.schema.<a href="../../../../org/mpxj/primavera/schema/Adapter5.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Adapter5</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/primavera/p3/package-tree.html">Prev</a></li>
<li><a href="../../../../org/mpxj/primavera/suretrak/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/primavera/schema/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
