<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ResourceAssignmentType (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ResourceAssignmentType (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10,"i76":10,"i77":10,"i78":10,"i79":10,"i80":10,"i81":10,"i82":10,"i83":10,"i84":10,"i85":10,"i86":10,"i87":10,"i88":10,"i89":10,"i90":10,"i91":10,"i92":10,"i93":10,"i94":10,"i95":10,"i96":10,"i97":10,"i98":10,"i99":10,"i100":10,"i101":10,"i102":10,"i103":10,"i104":10,"i105":10,"i106":10,"i107":10,"i108":10,"i109":10,"i110":10,"i111":10,"i112":10,"i113":10,"i114":10,"i115":10,"i116":10,"i117":10,"i118":10,"i119":10,"i120":10,"i121":10,"i122":10,"i123":10,"i124":10,"i125":10,"i126":10,"i127":10,"i128":10,"i129":10,"i130":10,"i131":10,"i132":10,"i133":10,"i134":10,"i135":10,"i136":10,"i137":10,"i138":10,"i139":10,"i140":10,"i141":10,"i142":10,"i143":10,"i144":10,"i145":10,"i146":10,"i147":10,"i148":10,"i149":10,"i150":10,"i151":10,"i152":10,"i153":10,"i154":10,"i155":10,"i156":10,"i157":10,"i158":10,"i159":10,"i160":10,"i161":10,"i162":10,"i163":10,"i164":10,"i165":10,"i166":10,"i167":10,"i168":10,"i169":10,"i170":10,"i171":10,"i172":10,"i173":10,"i174":10,"i175":10,"i176":10,"i177":10,"i178":10,"i179":10,"i180":10,"i181":10,"i182":10,"i183":10,"i184":10,"i185":10,"i186":10,"i187":10,"i188":10,"i189":10,"i190":10,"i191":10,"i192":10,"i193":10,"i194":10,"i195":10,"i196":10,"i197":10,"i198":10,"i199":10,"i200":10,"i201":10,"i202":10,"i203":10,"i204":10,"i205":10,"i206":10,"i207":10,"i208":10,"i209":10,"i210":10,"i211":10,"i212":10,"i213":10,"i214":10,"i215":10,"i216":10,"i217":10,"i218":10,"i219":10,"i220":10,"i221":10,"i222":10,"i223":10,"i224":10,"i225":10,"i226":10,"i227":10,"i228":10,"i229":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ResourceAssignmentType.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentSpreadType.Period.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentUpdateType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/primavera/schema/ResourceAssignmentType.html" target="_top">Frames</a></li>
<li><a href="ResourceAssignmentType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.primavera.schema</div>
<h2 title="Class ResourceAssignmentType" class="title">Class ResourceAssignmentType</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.primavera.schema.ResourceAssignmentType</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ResourceAssignmentType</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block"><p>Java class for ResourceAssignmentType complex type.

 <p>The following schema fragment specifies the expected content contained within this class.

 <pre>
 &lt;complexType name="ResourceAssignmentType"&gt;
   &lt;complexContent&gt;
     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       &lt;sequence&gt;
         &lt;element name="ActivityActualFinish" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
         &lt;element name="ActivityId" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="40"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="ActivityName" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="120"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="ActivityObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
         &lt;element name="ActivityType" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;enumeration value="Task Dependent"/&gt;
               &lt;enumeration value="Resource Dependent"/&gt;
               &lt;enumeration value="Level of Effort"/&gt;
               &lt;enumeration value="Start Milestone"/&gt;
               &lt;enumeration value="Finish Milestone"/&gt;
               &lt;enumeration value="WBS Summary"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="ActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualCurve" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
         &lt;element name="ActualDuration" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
         &lt;element name="ActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
         &lt;element name="ActualThisPeriodCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualThisPeriodUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="AssignmentPercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="AtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="AtCompletionDuration" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="AtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="AutoComputeActuals" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="BudgetAtCompletionCosts" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="BudgetAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CBSCode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
         &lt;element name="CBSId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
         &lt;element name="CalendarName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
         &lt;element name="CalendarObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
         &lt;element name="CostAccountId" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="40"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="CostAccountName" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="100"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="CostAccountObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
         &lt;element name="CostPerQuantity" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CreateDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
         &lt;element name="CreateUser" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
         &lt;element name="DrivingActivityDatesFlag" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="DurationPercentComplete" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
               &lt;minInclusive value="0.0"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="EstimateToCompletionCosts" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="EstimateToCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="FinancialPeriodTmplId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
         &lt;element name="FinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
         &lt;element name="GUID" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;pattern value="\{[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}\}|"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="HasFutureBucketData" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="IsActive" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="IsActivityFlagged" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="IsBaseline" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="IsCostUnitsLinked" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="IsOvertimeAllowed" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="IsPrimaryResource" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="IsTemplate" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="LastUpdateDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
         &lt;element name="LastUpdateUser" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="255"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="ObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
         &lt;element name="OvertimeFactor" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
               &lt;minInclusive value="0.0"/&gt;
               &lt;maxInclusive value="10.0"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="PendingActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PendingActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PendingPercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PendingRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PercentCompleteType" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;enumeration value=""/&gt;
               &lt;enumeration value="Physical"/&gt;
               &lt;enumeration value="Duration"/&gt;
               &lt;enumeration value="Units"/&gt;
               &lt;enumeration value="Scope"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="PlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PlannedCurve" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
         &lt;element name="PlannedDuration" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
               &lt;minInclusive value="0.0"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="PlannedFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
         &lt;element name="PlannedLag" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
               &lt;minInclusive value="0.0"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="PlannedStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
         &lt;element name="PlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PlannedUnitsPerTime" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PricePerUnit" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
               &lt;minInclusive value="0.0"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="PriorActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PriorActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="Proficiency" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;enumeration value="1 - Master"/&gt;
               &lt;enumeration value="2 - Expert"/&gt;
               &lt;enumeration value="3 - Skilled"/&gt;
               &lt;enumeration value="4 - Proficient"/&gt;
               &lt;enumeration value="5 - Inexperienced"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="ProjectFlag" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
         &lt;element name="ProjectId" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="40"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="ProjectName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
         &lt;element name="ProjectObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
         &lt;element name="ProjectProjectFlag" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
         &lt;element name="RateSource" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;enumeration value="Resource"/&gt;
               &lt;enumeration value="Role"/&gt;
               &lt;enumeration value="Override"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="RateType" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;enumeration value="Price / Unit"/&gt;
               &lt;enumeration value="Price / Unit 2"/&gt;
               &lt;enumeration value="Price / Unit 3"/&gt;
               &lt;enumeration value="Price / Unit 4"/&gt;
               &lt;enumeration value="Price / Unit 5"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="RemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingCurve" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
         &lt;element name="RemainingDuration" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
               &lt;minInclusive value="0.0"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="RemainingFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
         &lt;element name="RemainingLag" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}double"&gt;
               &lt;minInclusive value="0.0"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="RemainingLateFinishDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
         &lt;element name="RemainingLateStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
         &lt;element name="RemainingStartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
         &lt;element name="RemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingUnitsPerTime" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ResourceCalendarName" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
         &lt;element name="ResourceCurveName" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="60"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="ResourceCurveObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
         &lt;element name="ResourceId" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="255"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="ResourceName" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="255"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="ResourceObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
         &lt;element name="ResourceType" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;enumeration value="Labor"/&gt;
               &lt;enumeration value="Nonlabor"/&gt;
               &lt;enumeration value="Material"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="ReviewRequired" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
         &lt;element name="RoleId" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="40"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="RoleName" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="100"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="RoleObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
         &lt;element name="RoleShortName" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;maxLength value="40"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="StaffedRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="StaffedRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="StartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
         &lt;element name="StatusCode" minOccurs="0"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;enumeration value="Planned"/&gt;
               &lt;enumeration value="Active"/&gt;
               &lt;enumeration value="Inactive"/&gt;
               &lt;enumeration value="What-If"/&gt;
               &lt;enumeration value="Requested"/&gt;
               &lt;enumeration value="Template"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="TotalPastPeriodCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="TotalPastPeriodUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="UnitsPercentComplete" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="UnreadCommentCount" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
         &lt;element name="UnstaffedRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="UnstaffedRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="WBSNamePath" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
         &lt;element name="WBSObjectId" type="{http://www.w3.org/2001/XMLSchema}int" minOccurs="0"/&gt;
         &lt;element name="UDF" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}UDFAssignmentType" maxOccurs="unbounded" minOccurs="0"/&gt;
         &lt;element name="Spread" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ResourceAssignmentSpreadType" minOccurs="0"/&gt;
         &lt;element name="ResourceRequest" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}ResourceRequestType" minOccurs="0"/&gt;
         &lt;element name="Code" type="{http://xmlns.oracle.com/Primavera/P6/V24.12/API/BusinessObjects}CodeAssignmentType" maxOccurs="unbounded" minOccurs="0"/&gt;
       &lt;/sequence&gt;
     &lt;/restriction&gt;
   &lt;/complexContent&gt;
 &lt;/complexType&gt;
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#activityActualFinish">activityActualFinish</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#activityId">activityId</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#activityName">activityName</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#activityObjectId">activityObjectId</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#activityType">activityType</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#actualCost">actualCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#actualCurve">actualCurve</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#actualDuration">actualDuration</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#actualFinishDate">actualFinishDate</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#actualOvertimeCost">actualOvertimeCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#actualOvertimeUnits">actualOvertimeUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#actualRegularCost">actualRegularCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#actualRegularUnits">actualRegularUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#actualStartDate">actualStartDate</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#actualThisPeriodCost">actualThisPeriodCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#actualThisPeriodUnits">actualThisPeriodUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#actualUnits">actualUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#assignmentPercentComplete">assignmentPercentComplete</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#atCompletionCost">atCompletionCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#atCompletionDuration">atCompletionDuration</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#atCompletionUnits">atCompletionUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#autoComputeActuals">autoComputeActuals</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#budgetAtCompletionCosts">budgetAtCompletionCosts</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#budgetAtCompletionUnits">budgetAtCompletionUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#calendarName">calendarName</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#calendarObjectId">calendarObjectId</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#cbsCode">cbsCode</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#cbsId">cbsId</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/primavera/schema/CodeAssignmentType.html" title="class in org.mpxj.primavera.schema">CodeAssignmentType</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#code">code</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#costAccountId">costAccountId</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#costAccountName">costAccountName</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#costAccountObjectId">costAccountObjectId</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#costPerQuantity">costPerQuantity</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#createDate">createDate</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#createUser">createUser</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#drivingActivityDatesFlag">drivingActivityDatesFlag</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#durationPercentComplete">durationPercentComplete</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#estimateToCompletionCosts">estimateToCompletionCosts</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#estimateToCompletionUnits">estimateToCompletionUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#financialPeriodTmplId">financialPeriodTmplId</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#finishDate">finishDate</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#guid">guid</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#hasFutureBucketData">hasFutureBucketData</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#isActive">isActive</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#isActivityFlagged">isActivityFlagged</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#isBaseline">isBaseline</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#isCostUnitsLinked">isCostUnitsLinked</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#isOvertimeAllowed">isOvertimeAllowed</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#isPrimaryResource">isPrimaryResource</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#isTemplate">isTemplate</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#lastUpdateDate">lastUpdateDate</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#lastUpdateUser">lastUpdateUser</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#objectId">objectId</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#overtimeFactor">overtimeFactor</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#pendingActualOvertimeUnits">pendingActualOvertimeUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#pendingActualRegularUnits">pendingActualRegularUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#pendingPercentComplete">pendingPercentComplete</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#pendingRemainingUnits">pendingRemainingUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#percentComplete">percentComplete</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#percentCompleteType">percentCompleteType</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#plannedCost">plannedCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#plannedCurve">plannedCurve</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#plannedDuration">plannedDuration</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#plannedFinishDate">plannedFinishDate</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#plannedLag">plannedLag</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#plannedStartDate">plannedStartDate</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#plannedUnits">plannedUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#plannedUnitsPerTime">plannedUnitsPerTime</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#pricePerUnit">pricePerUnit</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#priorActualOvertimeUnits">priorActualOvertimeUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#priorActualRegularUnits">priorActualRegularUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#proficiency">proficiency</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#projectFlag">projectFlag</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#projectId">projectId</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#projectName">projectName</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#projectObjectId">projectObjectId</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#projectProjectFlag">projectProjectFlag</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#rateSource">rateSource</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#rateType">rateType</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#remainingCost">remainingCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#remainingCurve">remainingCurve</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#remainingDuration">remainingDuration</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#remainingFinishDate">remainingFinishDate</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#remainingLag">remainingLag</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#remainingLateFinishDate">remainingLateFinishDate</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#remainingLateStartDate">remainingLateStartDate</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#remainingStartDate">remainingStartDate</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#remainingUnits">remainingUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#remainingUnitsPerTime">remainingUnitsPerTime</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#resourceCalendarName">resourceCalendarName</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#resourceCurveName">resourceCurveName</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#resourceCurveObjectId">resourceCurveObjectId</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#resourceId">resourceId</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#resourceName">resourceName</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#resourceObjectId">resourceObjectId</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/primavera/schema/ResourceRequestType.html" title="class in org.mpxj.primavera.schema">ResourceRequestType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#resourceRequest">resourceRequest</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#resourceType">resourceType</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#reviewRequired">reviewRequired</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#roleId">roleId</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#roleName">roleName</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#roleObjectId">roleObjectId</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#roleShortName">roleShortName</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentSpreadType.html" title="class in org.mpxj.primavera.schema">ResourceAssignmentSpreadType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#spread">spread</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#staffedRemainingCost">staffedRemainingCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#staffedRemainingUnits">staffedRemainingUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#startDate">startDate</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#statusCode">statusCode</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#totalPastPeriodCost">totalPastPeriodCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#totalPastPeriodUnits">totalPastPeriodUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/primavera/schema/UDFAssignmentType.html" title="class in org.mpxj.primavera.schema">UDFAssignmentType</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#udf">udf</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#unitsPercentComplete">unitsPercentComplete</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#unreadCommentCount">unreadCommentCount</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#unstaffedRemainingCost">unstaffedRemainingCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#unstaffedRemainingUnits">unstaffedRemainingUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#wbsNamePath">wbsNamePath</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#wbsObjectId">wbsObjectId</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#ResourceAssignmentType--">ResourceAssignmentType</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getActivityActualFinish--">getActivityActualFinish</a></span>()</code>
<div class="block">Gets the value of the activityActualFinish property.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getActivityId--">getActivityId</a></span>()</code>
<div class="block">Gets the value of the activityId property.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getActivityName--">getActivityName</a></span>()</code>
<div class="block">Gets the value of the activityName property.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getActivityObjectId--">getActivityObjectId</a></span>()</code>
<div class="block">Gets the value of the activityObjectId property.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getActivityType--">getActivityType</a></span>()</code>
<div class="block">Gets the value of the activityType property.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getActualCost--">getActualCost</a></span>()</code>
<div class="block">Gets the value of the actualCost property.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getActualCurve--">getActualCurve</a></span>()</code>
<div class="block">Gets the value of the actualCurve property.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getActualDuration--">getActualDuration</a></span>()</code>
<div class="block">Gets the value of the actualDuration property.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getActualFinishDate--">getActualFinishDate</a></span>()</code>
<div class="block">Gets the value of the actualFinishDate property.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getActualOvertimeCost--">getActualOvertimeCost</a></span>()</code>
<div class="block">Gets the value of the actualOvertimeCost property.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getActualOvertimeUnits--">getActualOvertimeUnits</a></span>()</code>
<div class="block">Gets the value of the actualOvertimeUnits property.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getActualRegularCost--">getActualRegularCost</a></span>()</code>
<div class="block">Gets the value of the actualRegularCost property.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getActualRegularUnits--">getActualRegularUnits</a></span>()</code>
<div class="block">Gets the value of the actualRegularUnits property.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getActualStartDate--">getActualStartDate</a></span>()</code>
<div class="block">Gets the value of the actualStartDate property.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getActualThisPeriodCost--">getActualThisPeriodCost</a></span>()</code>
<div class="block">Gets the value of the actualThisPeriodCost property.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getActualThisPeriodUnits--">getActualThisPeriodUnits</a></span>()</code>
<div class="block">Gets the value of the actualThisPeriodUnits property.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getActualUnits--">getActualUnits</a></span>()</code>
<div class="block">Gets the value of the actualUnits property.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getAssignmentPercentComplete--">getAssignmentPercentComplete</a></span>()</code>
<div class="block">Gets the value of the assignmentPercentComplete property.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getAtCompletionCost--">getAtCompletionCost</a></span>()</code>
<div class="block">Gets the value of the atCompletionCost property.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getAtCompletionDuration--">getAtCompletionDuration</a></span>()</code>
<div class="block">Gets the value of the atCompletionDuration property.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getAtCompletionUnits--">getAtCompletionUnits</a></span>()</code>
<div class="block">Gets the value of the atCompletionUnits property.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getBudgetAtCompletionCosts--">getBudgetAtCompletionCosts</a></span>()</code>
<div class="block">Gets the value of the budgetAtCompletionCosts property.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getBudgetAtCompletionUnits--">getBudgetAtCompletionUnits</a></span>()</code>
<div class="block">Gets the value of the budgetAtCompletionUnits property.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getCalendarName--">getCalendarName</a></span>()</code>
<div class="block">Gets the value of the calendarName property.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getCalendarObjectId--">getCalendarObjectId</a></span>()</code>
<div class="block">Gets the value of the calendarObjectId property.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getCBSCode--">getCBSCode</a></span>()</code>
<div class="block">Gets the value of the cbsCode property.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getCBSId--">getCBSId</a></span>()</code>
<div class="block">Gets the value of the cbsId property.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/primavera/schema/CodeAssignmentType.html" title="class in org.mpxj.primavera.schema">CodeAssignmentType</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getCode--">getCode</a></span>()</code>
<div class="block">Gets the value of the code property.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getCostAccountId--">getCostAccountId</a></span>()</code>
<div class="block">Gets the value of the costAccountId property.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getCostAccountName--">getCostAccountName</a></span>()</code>
<div class="block">Gets the value of the costAccountName property.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getCostAccountObjectId--">getCostAccountObjectId</a></span>()</code>
<div class="block">Gets the value of the costAccountObjectId property.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getCostPerQuantity--">getCostPerQuantity</a></span>()</code>
<div class="block">Gets the value of the costPerQuantity property.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getCreateDate--">getCreateDate</a></span>()</code>
<div class="block">Gets the value of the createDate property.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getCreateUser--">getCreateUser</a></span>()</code>
<div class="block">Gets the value of the createUser property.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getDurationPercentComplete--">getDurationPercentComplete</a></span>()</code>
<div class="block">Gets the value of the durationPercentComplete property.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getEstimateToCompletionCosts--">getEstimateToCompletionCosts</a></span>()</code>
<div class="block">Gets the value of the estimateToCompletionCosts property.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getEstimateToCompletionUnits--">getEstimateToCompletionUnits</a></span>()</code>
<div class="block">Gets the value of the estimateToCompletionUnits property.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getFinancialPeriodTmplId--">getFinancialPeriodTmplId</a></span>()</code>
<div class="block">Gets the value of the financialPeriodTmplId property.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getFinishDate--">getFinishDate</a></span>()</code>
<div class="block">Gets the value of the finishDate property.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getGUID--">getGUID</a></span>()</code>
<div class="block">Gets the value of the guid property.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getLastUpdateDate--">getLastUpdateDate</a></span>()</code>
<div class="block">Gets the value of the lastUpdateDate property.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getLastUpdateUser--">getLastUpdateUser</a></span>()</code>
<div class="block">Gets the value of the lastUpdateUser property.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getObjectId--">getObjectId</a></span>()</code>
<div class="block">Gets the value of the objectId property.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getOvertimeFactor--">getOvertimeFactor</a></span>()</code>
<div class="block">Gets the value of the overtimeFactor property.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getPendingActualOvertimeUnits--">getPendingActualOvertimeUnits</a></span>()</code>
<div class="block">Gets the value of the pendingActualOvertimeUnits property.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getPendingActualRegularUnits--">getPendingActualRegularUnits</a></span>()</code>
<div class="block">Gets the value of the pendingActualRegularUnits property.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getPendingPercentComplete--">getPendingPercentComplete</a></span>()</code>
<div class="block">Gets the value of the pendingPercentComplete property.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getPendingRemainingUnits--">getPendingRemainingUnits</a></span>()</code>
<div class="block">Gets the value of the pendingRemainingUnits property.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getPercentComplete--">getPercentComplete</a></span>()</code>
<div class="block">Gets the value of the percentComplete property.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getPercentCompleteType--">getPercentCompleteType</a></span>()</code>
<div class="block">Gets the value of the percentCompleteType property.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getPlannedCost--">getPlannedCost</a></span>()</code>
<div class="block">Gets the value of the plannedCost property.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getPlannedCurve--">getPlannedCurve</a></span>()</code>
<div class="block">Gets the value of the plannedCurve property.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getPlannedDuration--">getPlannedDuration</a></span>()</code>
<div class="block">Gets the value of the plannedDuration property.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getPlannedFinishDate--">getPlannedFinishDate</a></span>()</code>
<div class="block">Gets the value of the plannedFinishDate property.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getPlannedLag--">getPlannedLag</a></span>()</code>
<div class="block">Gets the value of the plannedLag property.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getPlannedStartDate--">getPlannedStartDate</a></span>()</code>
<div class="block">Gets the value of the plannedStartDate property.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getPlannedUnits--">getPlannedUnits</a></span>()</code>
<div class="block">Gets the value of the plannedUnits property.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getPlannedUnitsPerTime--">getPlannedUnitsPerTime</a></span>()</code>
<div class="block">Gets the value of the plannedUnitsPerTime property.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getPricePerUnit--">getPricePerUnit</a></span>()</code>
<div class="block">Gets the value of the pricePerUnit property.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getPriorActualOvertimeUnits--">getPriorActualOvertimeUnits</a></span>()</code>
<div class="block">Gets the value of the priorActualOvertimeUnits property.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getPriorActualRegularUnits--">getPriorActualRegularUnits</a></span>()</code>
<div class="block">Gets the value of the priorActualRegularUnits property.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getProficiency--">getProficiency</a></span>()</code>
<div class="block">Gets the value of the proficiency property.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getProjectFlag--">getProjectFlag</a></span>()</code>
<div class="block">Gets the value of the projectFlag property.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getProjectId--">getProjectId</a></span>()</code>
<div class="block">Gets the value of the projectId property.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getProjectName--">getProjectName</a></span>()</code>
<div class="block">Gets the value of the projectName property.</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getProjectObjectId--">getProjectObjectId</a></span>()</code>
<div class="block">Gets the value of the projectObjectId property.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getProjectProjectFlag--">getProjectProjectFlag</a></span>()</code>
<div class="block">Gets the value of the projectProjectFlag property.</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getRateSource--">getRateSource</a></span>()</code>
<div class="block">Gets the value of the rateSource property.</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getRateType--">getRateType</a></span>()</code>
<div class="block">Gets the value of the rateType property.</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getRemainingCost--">getRemainingCost</a></span>()</code>
<div class="block">Gets the value of the remainingCost property.</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getRemainingCurve--">getRemainingCurve</a></span>()</code>
<div class="block">Gets the value of the remainingCurve property.</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getRemainingDuration--">getRemainingDuration</a></span>()</code>
<div class="block">Gets the value of the remainingDuration property.</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getRemainingFinishDate--">getRemainingFinishDate</a></span>()</code>
<div class="block">Gets the value of the remainingFinishDate property.</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getRemainingLag--">getRemainingLag</a></span>()</code>
<div class="block">Gets the value of the remainingLag property.</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getRemainingLateFinishDate--">getRemainingLateFinishDate</a></span>()</code>
<div class="block">Gets the value of the remainingLateFinishDate property.</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getRemainingLateStartDate--">getRemainingLateStartDate</a></span>()</code>
<div class="block">Gets the value of the remainingLateStartDate property.</div>
</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getRemainingStartDate--">getRemainingStartDate</a></span>()</code>
<div class="block">Gets the value of the remainingStartDate property.</div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getRemainingUnits--">getRemainingUnits</a></span>()</code>
<div class="block">Gets the value of the remainingUnits property.</div>
</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getRemainingUnitsPerTime--">getRemainingUnitsPerTime</a></span>()</code>
<div class="block">Gets the value of the remainingUnitsPerTime property.</div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getResourceCalendarName--">getResourceCalendarName</a></span>()</code>
<div class="block">Gets the value of the resourceCalendarName property.</div>
</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getResourceCurveName--">getResourceCurveName</a></span>()</code>
<div class="block">Gets the value of the resourceCurveName property.</div>
</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getResourceCurveObjectId--">getResourceCurveObjectId</a></span>()</code>
<div class="block">Gets the value of the resourceCurveObjectId property.</div>
</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getResourceId--">getResourceId</a></span>()</code>
<div class="block">Gets the value of the resourceId property.</div>
</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getResourceName--">getResourceName</a></span>()</code>
<div class="block">Gets the value of the resourceName property.</div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getResourceObjectId--">getResourceObjectId</a></span>()</code>
<div class="block">Gets the value of the resourceObjectId property.</div>
</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ResourceRequestType.html" title="class in org.mpxj.primavera.schema">ResourceRequestType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getResourceRequest--">getResourceRequest</a></span>()</code>
<div class="block">Gets the value of the resourceRequest property.</div>
</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getResourceType--">getResourceType</a></span>()</code>
<div class="block">Gets the value of the resourceType property.</div>
</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getRoleId--">getRoleId</a></span>()</code>
<div class="block">Gets the value of the roleId property.</div>
</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getRoleName--">getRoleName</a></span>()</code>
<div class="block">Gets the value of the roleName property.</div>
</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getRoleObjectId--">getRoleObjectId</a></span>()</code>
<div class="block">Gets the value of the roleObjectId property.</div>
</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getRoleShortName--">getRoleShortName</a></span>()</code>
<div class="block">Gets the value of the roleShortName property.</div>
</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentSpreadType.html" title="class in org.mpxj.primavera.schema">ResourceAssignmentSpreadType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getSpread--">getSpread</a></span>()</code>
<div class="block">Gets the value of the spread property.</div>
</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getStaffedRemainingCost--">getStaffedRemainingCost</a></span>()</code>
<div class="block">Gets the value of the staffedRemainingCost property.</div>
</td>
</tr>
<tr id="i93" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getStaffedRemainingUnits--">getStaffedRemainingUnits</a></span>()</code>
<div class="block">Gets the value of the staffedRemainingUnits property.</div>
</td>
</tr>
<tr id="i94" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getStartDate--">getStartDate</a></span>()</code>
<div class="block">Gets the value of the startDate property.</div>
</td>
</tr>
<tr id="i95" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getStatusCode--">getStatusCode</a></span>()</code>
<div class="block">Gets the value of the statusCode property.</div>
</td>
</tr>
<tr id="i96" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getTotalPastPeriodCost--">getTotalPastPeriodCost</a></span>()</code>
<div class="block">Gets the value of the totalPastPeriodCost property.</div>
</td>
</tr>
<tr id="i97" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getTotalPastPeriodUnits--">getTotalPastPeriodUnits</a></span>()</code>
<div class="block">Gets the value of the totalPastPeriodUnits property.</div>
</td>
</tr>
<tr id="i98" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/primavera/schema/UDFAssignmentType.html" title="class in org.mpxj.primavera.schema">UDFAssignmentType</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getUDF--">getUDF</a></span>()</code>
<div class="block">Gets the value of the udf property.</div>
</td>
</tr>
<tr id="i99" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getUnitsPercentComplete--">getUnitsPercentComplete</a></span>()</code>
<div class="block">Gets the value of the unitsPercentComplete property.</div>
</td>
</tr>
<tr id="i100" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getUnreadCommentCount--">getUnreadCommentCount</a></span>()</code>
<div class="block">Gets the value of the unreadCommentCount property.</div>
</td>
</tr>
<tr id="i101" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getUnstaffedRemainingCost--">getUnstaffedRemainingCost</a></span>()</code>
<div class="block">Gets the value of the unstaffedRemainingCost property.</div>
</td>
</tr>
<tr id="i102" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getUnstaffedRemainingUnits--">getUnstaffedRemainingUnits</a></span>()</code>
<div class="block">Gets the value of the unstaffedRemainingUnits property.</div>
</td>
</tr>
<tr id="i103" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getWBSNamePath--">getWBSNamePath</a></span>()</code>
<div class="block">Gets the value of the wbsNamePath property.</div>
</td>
</tr>
<tr id="i104" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#getWBSObjectId--">getWBSObjectId</a></span>()</code>
<div class="block">Gets the value of the wbsObjectId property.</div>
</td>
</tr>
<tr id="i105" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#isAutoComputeActuals--">isAutoComputeActuals</a></span>()</code>
<div class="block">Gets the value of the autoComputeActuals property.</div>
</td>
</tr>
<tr id="i106" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#isDrivingActivityDatesFlag--">isDrivingActivityDatesFlag</a></span>()</code>
<div class="block">Gets the value of the drivingActivityDatesFlag property.</div>
</td>
</tr>
<tr id="i107" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#isHasFutureBucketData--">isHasFutureBucketData</a></span>()</code>
<div class="block">Gets the value of the hasFutureBucketData property.</div>
</td>
</tr>
<tr id="i108" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#isIsActive--">isIsActive</a></span>()</code>
<div class="block">Gets the value of the isActive property.</div>
</td>
</tr>
<tr id="i109" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#isIsActivityFlagged--">isIsActivityFlagged</a></span>()</code>
<div class="block">Gets the value of the isActivityFlagged property.</div>
</td>
</tr>
<tr id="i110" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#isIsBaseline--">isIsBaseline</a></span>()</code>
<div class="block">Gets the value of the isBaseline property.</div>
</td>
</tr>
<tr id="i111" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#isIsCostUnitsLinked--">isIsCostUnitsLinked</a></span>()</code>
<div class="block">Gets the value of the isCostUnitsLinked property.</div>
</td>
</tr>
<tr id="i112" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#isIsOvertimeAllowed--">isIsOvertimeAllowed</a></span>()</code>
<div class="block">Gets the value of the isOvertimeAllowed property.</div>
</td>
</tr>
<tr id="i113" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#isIsPrimaryResource--">isIsPrimaryResource</a></span>()</code>
<div class="block">Gets the value of the isPrimaryResource property.</div>
</td>
</tr>
<tr id="i114" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#isIsTemplate--">isIsTemplate</a></span>()</code>
<div class="block">Gets the value of the isTemplate property.</div>
</td>
</tr>
<tr id="i115" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#isReviewRequired--">isReviewRequired</a></span>()</code>
<div class="block">Gets the value of the reviewRequired property.</div>
</td>
</tr>
<tr id="i116" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setActivityActualFinish-java.time.LocalDateTime-">setActivityActualFinish</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the activityActualFinish property.</div>
</td>
</tr>
<tr id="i117" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setActivityId-java.lang.String-">setActivityId</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the activityId property.</div>
</td>
</tr>
<tr id="i118" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setActivityName-java.lang.String-">setActivityName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the activityName property.</div>
</td>
</tr>
<tr id="i119" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setActivityObjectId-java.lang.Integer-">setActivityObjectId</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the activityObjectId property.</div>
</td>
</tr>
<tr id="i120" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setActivityType-java.lang.String-">setActivityType</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the activityType property.</div>
</td>
</tr>
<tr id="i121" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setActualCost-java.lang.Double-">setActualCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualCost property.</div>
</td>
</tr>
<tr id="i122" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setActualCurve-java.lang.String-">setActualCurve</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualCurve property.</div>
</td>
</tr>
<tr id="i123" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setActualDuration-java.lang.Double-">setActualDuration</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualDuration property.</div>
</td>
</tr>
<tr id="i124" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setActualFinishDate-java.time.LocalDateTime-">setActualFinishDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualFinishDate property.</div>
</td>
</tr>
<tr id="i125" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setActualOvertimeCost-java.lang.Double-">setActualOvertimeCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualOvertimeCost property.</div>
</td>
</tr>
<tr id="i126" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setActualOvertimeUnits-java.lang.Double-">setActualOvertimeUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualOvertimeUnits property.</div>
</td>
</tr>
<tr id="i127" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setActualRegularCost-java.lang.Double-">setActualRegularCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualRegularCost property.</div>
</td>
</tr>
<tr id="i128" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setActualRegularUnits-java.lang.Double-">setActualRegularUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualRegularUnits property.</div>
</td>
</tr>
<tr id="i129" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setActualStartDate-java.time.LocalDateTime-">setActualStartDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualStartDate property.</div>
</td>
</tr>
<tr id="i130" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setActualThisPeriodCost-java.lang.Double-">setActualThisPeriodCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualThisPeriodCost property.</div>
</td>
</tr>
<tr id="i131" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setActualThisPeriodUnits-java.lang.Double-">setActualThisPeriodUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualThisPeriodUnits property.</div>
</td>
</tr>
<tr id="i132" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setActualUnits-java.lang.Double-">setActualUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualUnits property.</div>
</td>
</tr>
<tr id="i133" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setAssignmentPercentComplete-java.lang.Double-">setAssignmentPercentComplete</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the assignmentPercentComplete property.</div>
</td>
</tr>
<tr id="i134" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setAtCompletionCost-java.lang.Double-">setAtCompletionCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the atCompletionCost property.</div>
</td>
</tr>
<tr id="i135" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setAtCompletionDuration-java.lang.Double-">setAtCompletionDuration</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the atCompletionDuration property.</div>
</td>
</tr>
<tr id="i136" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setAtCompletionUnits-java.lang.Double-">setAtCompletionUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the atCompletionUnits property.</div>
</td>
</tr>
<tr id="i137" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setAutoComputeActuals-java.lang.Boolean-">setAutoComputeActuals</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the autoComputeActuals property.</div>
</td>
</tr>
<tr id="i138" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setBudgetAtCompletionCosts-java.lang.Double-">setBudgetAtCompletionCosts</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the budgetAtCompletionCosts property.</div>
</td>
</tr>
<tr id="i139" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setBudgetAtCompletionUnits-java.lang.Double-">setBudgetAtCompletionUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the budgetAtCompletionUnits property.</div>
</td>
</tr>
<tr id="i140" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setCalendarName-java.lang.String-">setCalendarName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the calendarName property.</div>
</td>
</tr>
<tr id="i141" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setCalendarObjectId-java.lang.Integer-">setCalendarObjectId</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the calendarObjectId property.</div>
</td>
</tr>
<tr id="i142" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setCBSCode-java.lang.String-">setCBSCode</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the cbsCode property.</div>
</td>
</tr>
<tr id="i143" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setCBSId-java.lang.Integer-">setCBSId</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the cbsId property.</div>
</td>
</tr>
<tr id="i144" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setCostAccountId-java.lang.String-">setCostAccountId</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the costAccountId property.</div>
</td>
</tr>
<tr id="i145" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setCostAccountName-java.lang.String-">setCostAccountName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the costAccountName property.</div>
</td>
</tr>
<tr id="i146" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setCostAccountObjectId-java.lang.Integer-">setCostAccountObjectId</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the costAccountObjectId property.</div>
</td>
</tr>
<tr id="i147" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setCostPerQuantity-java.lang.Double-">setCostPerQuantity</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the costPerQuantity property.</div>
</td>
</tr>
<tr id="i148" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setCreateDate-java.time.LocalDateTime-">setCreateDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the createDate property.</div>
</td>
</tr>
<tr id="i149" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setCreateUser-java.lang.String-">setCreateUser</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the createUser property.</div>
</td>
</tr>
<tr id="i150" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setDrivingActivityDatesFlag-java.lang.Boolean-">setDrivingActivityDatesFlag</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the drivingActivityDatesFlag property.</div>
</td>
</tr>
<tr id="i151" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setDurationPercentComplete-java.lang.Double-">setDurationPercentComplete</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the durationPercentComplete property.</div>
</td>
</tr>
<tr id="i152" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setEstimateToCompletionCosts-java.lang.Double-">setEstimateToCompletionCosts</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the estimateToCompletionCosts property.</div>
</td>
</tr>
<tr id="i153" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setEstimateToCompletionUnits-java.lang.Double-">setEstimateToCompletionUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the estimateToCompletionUnits property.</div>
</td>
</tr>
<tr id="i154" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setFinancialPeriodTmplId-java.lang.Integer-">setFinancialPeriodTmplId</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the financialPeriodTmplId property.</div>
</td>
</tr>
<tr id="i155" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setFinishDate-java.time.LocalDateTime-">setFinishDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the finishDate property.</div>
</td>
</tr>
<tr id="i156" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setGUID-java.lang.String-">setGUID</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the guid property.</div>
</td>
</tr>
<tr id="i157" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setHasFutureBucketData-java.lang.Boolean-">setHasFutureBucketData</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the hasFutureBucketData property.</div>
</td>
</tr>
<tr id="i158" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setIsActive-java.lang.Boolean-">setIsActive</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the isActive property.</div>
</td>
</tr>
<tr id="i159" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setIsActivityFlagged-java.lang.Boolean-">setIsActivityFlagged</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the isActivityFlagged property.</div>
</td>
</tr>
<tr id="i160" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setIsBaseline-java.lang.Boolean-">setIsBaseline</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the isBaseline property.</div>
</td>
</tr>
<tr id="i161" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setIsCostUnitsLinked-java.lang.Boolean-">setIsCostUnitsLinked</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the isCostUnitsLinked property.</div>
</td>
</tr>
<tr id="i162" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setIsOvertimeAllowed-java.lang.Boolean-">setIsOvertimeAllowed</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the isOvertimeAllowed property.</div>
</td>
</tr>
<tr id="i163" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setIsPrimaryResource-java.lang.Boolean-">setIsPrimaryResource</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the isPrimaryResource property.</div>
</td>
</tr>
<tr id="i164" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setIsTemplate-java.lang.Boolean-">setIsTemplate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the isTemplate property.</div>
</td>
</tr>
<tr id="i165" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setLastUpdateDate-java.time.LocalDateTime-">setLastUpdateDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the lastUpdateDate property.</div>
</td>
</tr>
<tr id="i166" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setLastUpdateUser-java.lang.String-">setLastUpdateUser</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the lastUpdateUser property.</div>
</td>
</tr>
<tr id="i167" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setObjectId-java.lang.Integer-">setObjectId</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the objectId property.</div>
</td>
</tr>
<tr id="i168" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setOvertimeFactor-java.lang.Double-">setOvertimeFactor</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the overtimeFactor property.</div>
</td>
</tr>
<tr id="i169" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setPendingActualOvertimeUnits-java.lang.Double-">setPendingActualOvertimeUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the pendingActualOvertimeUnits property.</div>
</td>
</tr>
<tr id="i170" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setPendingActualRegularUnits-java.lang.Double-">setPendingActualRegularUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the pendingActualRegularUnits property.</div>
</td>
</tr>
<tr id="i171" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setPendingPercentComplete-java.lang.Double-">setPendingPercentComplete</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the pendingPercentComplete property.</div>
</td>
</tr>
<tr id="i172" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setPendingRemainingUnits-java.lang.Double-">setPendingRemainingUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the pendingRemainingUnits property.</div>
</td>
</tr>
<tr id="i173" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setPercentComplete-java.lang.Double-">setPercentComplete</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the percentComplete property.</div>
</td>
</tr>
<tr id="i174" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setPercentCompleteType-java.lang.String-">setPercentCompleteType</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the percentCompleteType property.</div>
</td>
</tr>
<tr id="i175" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setPlannedCost-java.lang.Double-">setPlannedCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedCost property.</div>
</td>
</tr>
<tr id="i176" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setPlannedCurve-java.lang.String-">setPlannedCurve</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedCurve property.</div>
</td>
</tr>
<tr id="i177" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setPlannedDuration-java.lang.Double-">setPlannedDuration</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedDuration property.</div>
</td>
</tr>
<tr id="i178" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setPlannedFinishDate-java.time.LocalDateTime-">setPlannedFinishDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedFinishDate property.</div>
</td>
</tr>
<tr id="i179" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setPlannedLag-java.lang.Double-">setPlannedLag</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedLag property.</div>
</td>
</tr>
<tr id="i180" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setPlannedStartDate-java.time.LocalDateTime-">setPlannedStartDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedStartDate property.</div>
</td>
</tr>
<tr id="i181" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setPlannedUnits-java.lang.Double-">setPlannedUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedUnits property.</div>
</td>
</tr>
<tr id="i182" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setPlannedUnitsPerTime-java.lang.Double-">setPlannedUnitsPerTime</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedUnitsPerTime property.</div>
</td>
</tr>
<tr id="i183" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setPricePerUnit-java.lang.Double-">setPricePerUnit</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the pricePerUnit property.</div>
</td>
</tr>
<tr id="i184" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setPriorActualOvertimeUnits-java.lang.Double-">setPriorActualOvertimeUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the priorActualOvertimeUnits property.</div>
</td>
</tr>
<tr id="i185" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setPriorActualRegularUnits-java.lang.Double-">setPriorActualRegularUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the priorActualRegularUnits property.</div>
</td>
</tr>
<tr id="i186" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setProficiency-java.lang.String-">setProficiency</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the proficiency property.</div>
</td>
</tr>
<tr id="i187" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setProjectFlag-java.lang.String-">setProjectFlag</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the projectFlag property.</div>
</td>
</tr>
<tr id="i188" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setProjectId-java.lang.String-">setProjectId</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the projectId property.</div>
</td>
</tr>
<tr id="i189" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setProjectName-java.lang.String-">setProjectName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the projectName property.</div>
</td>
</tr>
<tr id="i190" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setProjectObjectId-java.lang.Integer-">setProjectObjectId</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the projectObjectId property.</div>
</td>
</tr>
<tr id="i191" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setProjectProjectFlag-java.lang.String-">setProjectProjectFlag</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the projectProjectFlag property.</div>
</td>
</tr>
<tr id="i192" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setRateSource-java.lang.String-">setRateSource</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the rateSource property.</div>
</td>
</tr>
<tr id="i193" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setRateType-java.lang.String-">setRateType</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the rateType property.</div>
</td>
</tr>
<tr id="i194" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setRemainingCost-java.lang.Double-">setRemainingCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingCost property.</div>
</td>
</tr>
<tr id="i195" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setRemainingCurve-java.lang.String-">setRemainingCurve</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingCurve property.</div>
</td>
</tr>
<tr id="i196" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setRemainingDuration-java.lang.Double-">setRemainingDuration</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingDuration property.</div>
</td>
</tr>
<tr id="i197" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setRemainingFinishDate-java.time.LocalDateTime-">setRemainingFinishDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingFinishDate property.</div>
</td>
</tr>
<tr id="i198" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setRemainingLag-java.lang.Double-">setRemainingLag</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingLag property.</div>
</td>
</tr>
<tr id="i199" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setRemainingLateFinishDate-java.time.LocalDateTime-">setRemainingLateFinishDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingLateFinishDate property.</div>
</td>
</tr>
<tr id="i200" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setRemainingLateStartDate-java.time.LocalDateTime-">setRemainingLateStartDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingLateStartDate property.</div>
</td>
</tr>
<tr id="i201" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setRemainingStartDate-java.time.LocalDateTime-">setRemainingStartDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingStartDate property.</div>
</td>
</tr>
<tr id="i202" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setRemainingUnits-java.lang.Double-">setRemainingUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingUnits property.</div>
</td>
</tr>
<tr id="i203" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setRemainingUnitsPerTime-java.lang.Double-">setRemainingUnitsPerTime</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingUnitsPerTime property.</div>
</td>
</tr>
<tr id="i204" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setResourceCalendarName-java.lang.String-">setResourceCalendarName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the resourceCalendarName property.</div>
</td>
</tr>
<tr id="i205" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setResourceCurveName-java.lang.String-">setResourceCurveName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the resourceCurveName property.</div>
</td>
</tr>
<tr id="i206" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setResourceCurveObjectId-java.lang.Integer-">setResourceCurveObjectId</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the resourceCurveObjectId property.</div>
</td>
</tr>
<tr id="i207" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setResourceId-java.lang.String-">setResourceId</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the resourceId property.</div>
</td>
</tr>
<tr id="i208" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setResourceName-java.lang.String-">setResourceName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the resourceName property.</div>
</td>
</tr>
<tr id="i209" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setResourceObjectId-java.lang.Integer-">setResourceObjectId</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the resourceObjectId property.</div>
</td>
</tr>
<tr id="i210" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setResourceRequest-org.mpxj.primavera.schema.ResourceRequestType-">setResourceRequest</a></span>(<a href="../../../../org/mpxj/primavera/schema/ResourceRequestType.html" title="class in org.mpxj.primavera.schema">ResourceRequestType</a>&nbsp;value)</code>
<div class="block">Sets the value of the resourceRequest property.</div>
</td>
</tr>
<tr id="i211" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setResourceType-java.lang.String-">setResourceType</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the resourceType property.</div>
</td>
</tr>
<tr id="i212" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setReviewRequired-java.lang.Boolean-">setReviewRequired</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</code>
<div class="block">Sets the value of the reviewRequired property.</div>
</td>
</tr>
<tr id="i213" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setRoleId-java.lang.String-">setRoleId</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the roleId property.</div>
</td>
</tr>
<tr id="i214" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setRoleName-java.lang.String-">setRoleName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the roleName property.</div>
</td>
</tr>
<tr id="i215" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setRoleObjectId-java.lang.Integer-">setRoleObjectId</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the roleObjectId property.</div>
</td>
</tr>
<tr id="i216" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setRoleShortName-java.lang.String-">setRoleShortName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the roleShortName property.</div>
</td>
</tr>
<tr id="i217" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setSpread-org.mpxj.primavera.schema.ResourceAssignmentSpreadType-">setSpread</a></span>(<a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentSpreadType.html" title="class in org.mpxj.primavera.schema">ResourceAssignmentSpreadType</a>&nbsp;value)</code>
<div class="block">Sets the value of the spread property.</div>
</td>
</tr>
<tr id="i218" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setStaffedRemainingCost-java.lang.Double-">setStaffedRemainingCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the staffedRemainingCost property.</div>
</td>
</tr>
<tr id="i219" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setStaffedRemainingUnits-java.lang.Double-">setStaffedRemainingUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the staffedRemainingUnits property.</div>
</td>
</tr>
<tr id="i220" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setStartDate-java.time.LocalDateTime-">setStartDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the startDate property.</div>
</td>
</tr>
<tr id="i221" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setStatusCode-java.lang.String-">setStatusCode</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the statusCode property.</div>
</td>
</tr>
<tr id="i222" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setTotalPastPeriodCost-java.lang.Double-">setTotalPastPeriodCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the totalPastPeriodCost property.</div>
</td>
</tr>
<tr id="i223" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setTotalPastPeriodUnits-java.lang.Double-">setTotalPastPeriodUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the totalPastPeriodUnits property.</div>
</td>
</tr>
<tr id="i224" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setUnitsPercentComplete-java.lang.Double-">setUnitsPercentComplete</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the unitsPercentComplete property.</div>
</td>
</tr>
<tr id="i225" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setUnreadCommentCount-java.lang.Integer-">setUnreadCommentCount</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the unreadCommentCount property.</div>
</td>
</tr>
<tr id="i226" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setUnstaffedRemainingCost-java.lang.Double-">setUnstaffedRemainingCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the unstaffedRemainingCost property.</div>
</td>
</tr>
<tr id="i227" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setUnstaffedRemainingUnits-java.lang.Double-">setUnstaffedRemainingUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the unstaffedRemainingUnits property.</div>
</td>
</tr>
<tr id="i228" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setWBSNamePath-java.lang.String-">setWBSNamePath</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the wbsNamePath property.</div>
</td>
</tr>
<tr id="i229" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentType.html#setWBSObjectId-java.lang.Integer-">setWBSObjectId</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</code>
<div class="block">Sets the value of the wbsObjectId property.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="activityActualFinish">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>activityActualFinish</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> activityActualFinish</pre>
</li>
</ul>
<a name="activityId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>activityId</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> activityId</pre>
</li>
</ul>
<a name="activityName">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>activityName</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> activityName</pre>
</li>
</ul>
<a name="activityObjectId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>activityObjectId</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> activityObjectId</pre>
</li>
</ul>
<a name="activityType">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>activityType</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> activityType</pre>
</li>
</ul>
<a name="actualCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualCost</pre>
</li>
</ul>
<a name="actualCurve">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualCurve</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> actualCurve</pre>
</li>
</ul>
<a name="actualDuration">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualDuration</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualDuration</pre>
</li>
</ul>
<a name="actualFinishDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualFinishDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> actualFinishDate</pre>
</li>
</ul>
<a name="actualOvertimeCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualOvertimeCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualOvertimeCost</pre>
</li>
</ul>
<a name="actualOvertimeUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualOvertimeUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualOvertimeUnits</pre>
</li>
</ul>
<a name="actualRegularCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualRegularCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualRegularCost</pre>
</li>
</ul>
<a name="actualRegularUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualRegularUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualRegularUnits</pre>
</li>
</ul>
<a name="actualStartDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualStartDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> actualStartDate</pre>
</li>
</ul>
<a name="actualThisPeriodCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualThisPeriodCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualThisPeriodCost</pre>
</li>
</ul>
<a name="actualThisPeriodUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualThisPeriodUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualThisPeriodUnits</pre>
</li>
</ul>
<a name="actualUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualUnits</pre>
</li>
</ul>
<a name="assignmentPercentComplete">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>assignmentPercentComplete</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> assignmentPercentComplete</pre>
</li>
</ul>
<a name="atCompletionCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>atCompletionCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> atCompletionCost</pre>
</li>
</ul>
<a name="atCompletionDuration">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>atCompletionDuration</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> atCompletionDuration</pre>
</li>
</ul>
<a name="atCompletionUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>atCompletionUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> atCompletionUnits</pre>
</li>
</ul>
<a name="autoComputeActuals">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>autoComputeActuals</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> autoComputeActuals</pre>
</li>
</ul>
<a name="budgetAtCompletionCosts">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>budgetAtCompletionCosts</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> budgetAtCompletionCosts</pre>
</li>
</ul>
<a name="budgetAtCompletionUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>budgetAtCompletionUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> budgetAtCompletionUnits</pre>
</li>
</ul>
<a name="cbsCode">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cbsCode</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> cbsCode</pre>
</li>
</ul>
<a name="cbsId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cbsId</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> cbsId</pre>
</li>
</ul>
<a name="calendarName">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calendarName</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> calendarName</pre>
</li>
</ul>
<a name="calendarObjectId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calendarObjectId</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> calendarObjectId</pre>
</li>
</ul>
<a name="costAccountId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>costAccountId</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> costAccountId</pre>
</li>
</ul>
<a name="costAccountName">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>costAccountName</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> costAccountName</pre>
</li>
</ul>
<a name="costAccountObjectId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>costAccountObjectId</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> costAccountObjectId</pre>
</li>
</ul>
<a name="costPerQuantity">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>costPerQuantity</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> costPerQuantity</pre>
</li>
</ul>
<a name="createDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> createDate</pre>
</li>
</ul>
<a name="createUser">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createUser</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> createUser</pre>
</li>
</ul>
<a name="drivingActivityDatesFlag">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>drivingActivityDatesFlag</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> drivingActivityDatesFlag</pre>
</li>
</ul>
<a name="durationPercentComplete">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>durationPercentComplete</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> durationPercentComplete</pre>
</li>
</ul>
<a name="estimateToCompletionCosts">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>estimateToCompletionCosts</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> estimateToCompletionCosts</pre>
</li>
</ul>
<a name="estimateToCompletionUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>estimateToCompletionUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> estimateToCompletionUnits</pre>
</li>
</ul>
<a name="financialPeriodTmplId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>financialPeriodTmplId</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> financialPeriodTmplId</pre>
</li>
</ul>
<a name="finishDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>finishDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> finishDate</pre>
</li>
</ul>
<a name="guid">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>guid</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> guid</pre>
</li>
</ul>
<a name="hasFutureBucketData">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hasFutureBucketData</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> hasFutureBucketData</pre>
</li>
</ul>
<a name="isActive">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isActive</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> isActive</pre>
</li>
</ul>
<a name="isActivityFlagged">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isActivityFlagged</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> isActivityFlagged</pre>
</li>
</ul>
<a name="isBaseline">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isBaseline</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> isBaseline</pre>
</li>
</ul>
<a name="isCostUnitsLinked">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isCostUnitsLinked</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> isCostUnitsLinked</pre>
</li>
</ul>
<a name="isOvertimeAllowed">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isOvertimeAllowed</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> isOvertimeAllowed</pre>
</li>
</ul>
<a name="isPrimaryResource">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isPrimaryResource</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> isPrimaryResource</pre>
</li>
</ul>
<a name="isTemplate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isTemplate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> isTemplate</pre>
</li>
</ul>
<a name="lastUpdateDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>lastUpdateDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> lastUpdateDate</pre>
</li>
</ul>
<a name="lastUpdateUser">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>lastUpdateUser</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> lastUpdateUser</pre>
</li>
</ul>
<a name="objectId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>objectId</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> objectId</pre>
</li>
</ul>
<a name="overtimeFactor">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>overtimeFactor</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> overtimeFactor</pre>
</li>
</ul>
<a name="pendingActualOvertimeUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pendingActualOvertimeUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> pendingActualOvertimeUnits</pre>
</li>
</ul>
<a name="pendingActualRegularUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pendingActualRegularUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> pendingActualRegularUnits</pre>
</li>
</ul>
<a name="pendingPercentComplete">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pendingPercentComplete</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> pendingPercentComplete</pre>
</li>
</ul>
<a name="pendingRemainingUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pendingRemainingUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> pendingRemainingUnits</pre>
</li>
</ul>
<a name="percentComplete">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>percentComplete</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> percentComplete</pre>
</li>
</ul>
<a name="percentCompleteType">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>percentCompleteType</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> percentCompleteType</pre>
</li>
</ul>
<a name="plannedCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> plannedCost</pre>
</li>
</ul>
<a name="plannedCurve">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedCurve</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> plannedCurve</pre>
</li>
</ul>
<a name="plannedDuration">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedDuration</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> plannedDuration</pre>
</li>
</ul>
<a name="plannedFinishDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedFinishDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> plannedFinishDate</pre>
</li>
</ul>
<a name="plannedLag">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedLag</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> plannedLag</pre>
</li>
</ul>
<a name="plannedStartDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedStartDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> plannedStartDate</pre>
</li>
</ul>
<a name="plannedUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> plannedUnits</pre>
</li>
</ul>
<a name="plannedUnitsPerTime">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedUnitsPerTime</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> plannedUnitsPerTime</pre>
</li>
</ul>
<a name="pricePerUnit">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>pricePerUnit</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> pricePerUnit</pre>
</li>
</ul>
<a name="priorActualOvertimeUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>priorActualOvertimeUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> priorActualOvertimeUnits</pre>
</li>
</ul>
<a name="priorActualRegularUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>priorActualRegularUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> priorActualRegularUnits</pre>
</li>
</ul>
<a name="proficiency">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>proficiency</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> proficiency</pre>
</li>
</ul>
<a name="projectFlag">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>projectFlag</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> projectFlag</pre>
</li>
</ul>
<a name="projectId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>projectId</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> projectId</pre>
</li>
</ul>
<a name="projectName">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>projectName</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> projectName</pre>
</li>
</ul>
<a name="projectObjectId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>projectObjectId</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> projectObjectId</pre>
</li>
</ul>
<a name="projectProjectFlag">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>projectProjectFlag</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> projectProjectFlag</pre>
</li>
</ul>
<a name="rateSource">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rateSource</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> rateSource</pre>
</li>
</ul>
<a name="rateType">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>rateType</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> rateType</pre>
</li>
</ul>
<a name="remainingCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingCost</pre>
</li>
</ul>
<a name="remainingCurve">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingCurve</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> remainingCurve</pre>
</li>
</ul>
<a name="remainingDuration">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingDuration</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingDuration</pre>
</li>
</ul>
<a name="remainingFinishDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingFinishDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> remainingFinishDate</pre>
</li>
</ul>
<a name="remainingLag">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingLag</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingLag</pre>
</li>
</ul>
<a name="remainingLateFinishDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingLateFinishDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> remainingLateFinishDate</pre>
</li>
</ul>
<a name="remainingLateStartDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingLateStartDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> remainingLateStartDate</pre>
</li>
</ul>
<a name="remainingStartDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingStartDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> remainingStartDate</pre>
</li>
</ul>
<a name="remainingUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingUnits</pre>
</li>
</ul>
<a name="remainingUnitsPerTime">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingUnitsPerTime</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingUnitsPerTime</pre>
</li>
</ul>
<a name="resourceCalendarName">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resourceCalendarName</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> resourceCalendarName</pre>
</li>
</ul>
<a name="resourceCurveName">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resourceCurveName</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> resourceCurveName</pre>
</li>
</ul>
<a name="resourceCurveObjectId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resourceCurveObjectId</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> resourceCurveObjectId</pre>
</li>
</ul>
<a name="resourceId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resourceId</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> resourceId</pre>
</li>
</ul>
<a name="resourceName">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resourceName</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> resourceName</pre>
</li>
</ul>
<a name="resourceObjectId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resourceObjectId</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> resourceObjectId</pre>
</li>
</ul>
<a name="resourceType">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resourceType</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> resourceType</pre>
</li>
</ul>
<a name="reviewRequired">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>reviewRequired</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a> reviewRequired</pre>
</li>
</ul>
<a name="roleId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>roleId</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> roleId</pre>
</li>
</ul>
<a name="roleName">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>roleName</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> roleName</pre>
</li>
</ul>
<a name="roleObjectId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>roleObjectId</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> roleObjectId</pre>
</li>
</ul>
<a name="roleShortName">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>roleShortName</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> roleShortName</pre>
</li>
</ul>
<a name="staffedRemainingCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>staffedRemainingCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> staffedRemainingCost</pre>
</li>
</ul>
<a name="staffedRemainingUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>staffedRemainingUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> staffedRemainingUnits</pre>
</li>
</ul>
<a name="startDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> startDate</pre>
</li>
</ul>
<a name="statusCode">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>statusCode</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> statusCode</pre>
</li>
</ul>
<a name="totalPastPeriodCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>totalPastPeriodCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> totalPastPeriodCost</pre>
</li>
</ul>
<a name="totalPastPeriodUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>totalPastPeriodUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> totalPastPeriodUnits</pre>
</li>
</ul>
<a name="unitsPercentComplete">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unitsPercentComplete</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> unitsPercentComplete</pre>
</li>
</ul>
<a name="unreadCommentCount">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unreadCommentCount</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> unreadCommentCount</pre>
</li>
</ul>
<a name="unstaffedRemainingCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unstaffedRemainingCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> unstaffedRemainingCost</pre>
</li>
</ul>
<a name="unstaffedRemainingUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>unstaffedRemainingUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> unstaffedRemainingUnits</pre>
</li>
</ul>
<a name="wbsNamePath">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>wbsNamePath</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> wbsNamePath</pre>
</li>
</ul>
<a name="wbsObjectId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>wbsObjectId</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a> wbsObjectId</pre>
</li>
</ul>
<a name="udf">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>udf</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/primavera/schema/UDFAssignmentType.html" title="class in org.mpxj.primavera.schema">UDFAssignmentType</a>&gt; udf</pre>
</li>
</ul>
<a name="spread">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>spread</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentSpreadType.html" title="class in org.mpxj.primavera.schema">ResourceAssignmentSpreadType</a> spread</pre>
</li>
</ul>
<a name="resourceRequest">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resourceRequest</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceRequestType.html" title="class in org.mpxj.primavera.schema">ResourceRequestType</a> resourceRequest</pre>
</li>
</ul>
<a name="code">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>code</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/primavera/schema/CodeAssignmentType.html" title="class in org.mpxj.primavera.schema">CodeAssignmentType</a>&gt; code</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ResourceAssignmentType--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ResourceAssignmentType</h4>
<pre>public&nbsp;ResourceAssignmentType()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getActivityActualFinish--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActivityActualFinish</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getActivityActualFinish()</pre>
<div class="block">Gets the value of the activityActualFinish property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActivityActualFinish-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActivityActualFinish</h4>
<pre>public&nbsp;void&nbsp;setActivityActualFinish(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the activityActualFinish property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActivityId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActivityId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getActivityId()</pre>
<div class="block">Gets the value of the activityId property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActivityId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActivityId</h4>
<pre>public&nbsp;void&nbsp;setActivityId(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the activityId property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActivityName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActivityName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getActivityName()</pre>
<div class="block">Gets the value of the activityName property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActivityName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActivityName</h4>
<pre>public&nbsp;void&nbsp;setActivityName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the activityName property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActivityObjectId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActivityObjectId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getActivityObjectId()</pre>
<div class="block">Gets the value of the activityObjectId property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="setActivityObjectId-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActivityObjectId</h4>
<pre>public&nbsp;void&nbsp;setActivityObjectId(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the activityObjectId property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="getActivityType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActivityType</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getActivityType()</pre>
<div class="block">Gets the value of the activityType property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActivityType-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActivityType</h4>
<pre>public&nbsp;void&nbsp;setActivityType(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the activityType property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualCost()</pre>
<div class="block">Gets the value of the actualCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualCost</h4>
<pre>public&nbsp;void&nbsp;setActualCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualCurve--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualCurve</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getActualCurve()</pre>
<div class="block">Gets the value of the actualCurve property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualCurve-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualCurve</h4>
<pre>public&nbsp;void&nbsp;setActualCurve(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualCurve property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualDuration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualDuration</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualDuration()</pre>
<div class="block">Gets the value of the actualDuration property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualDuration-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualDuration</h4>
<pre>public&nbsp;void&nbsp;setActualDuration(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualDuration property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualFinishDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualFinishDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getActualFinishDate()</pre>
<div class="block">Gets the value of the actualFinishDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualFinishDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualFinishDate</h4>
<pre>public&nbsp;void&nbsp;setActualFinishDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualFinishDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualOvertimeCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualOvertimeCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualOvertimeCost()</pre>
<div class="block">Gets the value of the actualOvertimeCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualOvertimeCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualOvertimeCost</h4>
<pre>public&nbsp;void&nbsp;setActualOvertimeCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualOvertimeCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualOvertimeUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualOvertimeUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualOvertimeUnits()</pre>
<div class="block">Gets the value of the actualOvertimeUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualOvertimeUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualOvertimeUnits</h4>
<pre>public&nbsp;void&nbsp;setActualOvertimeUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualOvertimeUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualRegularCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualRegularCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualRegularCost()</pre>
<div class="block">Gets the value of the actualRegularCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualRegularCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualRegularCost</h4>
<pre>public&nbsp;void&nbsp;setActualRegularCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualRegularCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualRegularUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualRegularUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualRegularUnits()</pre>
<div class="block">Gets the value of the actualRegularUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualRegularUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualRegularUnits</h4>
<pre>public&nbsp;void&nbsp;setActualRegularUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualRegularUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualStartDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualStartDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getActualStartDate()</pre>
<div class="block">Gets the value of the actualStartDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualStartDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualStartDate</h4>
<pre>public&nbsp;void&nbsp;setActualStartDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualStartDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualThisPeriodCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualThisPeriodCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualThisPeriodCost()</pre>
<div class="block">Gets the value of the actualThisPeriodCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualThisPeriodCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualThisPeriodCost</h4>
<pre>public&nbsp;void&nbsp;setActualThisPeriodCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualThisPeriodCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualThisPeriodUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualThisPeriodUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualThisPeriodUnits()</pre>
<div class="block">Gets the value of the actualThisPeriodUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualThisPeriodUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualThisPeriodUnits</h4>
<pre>public&nbsp;void&nbsp;setActualThisPeriodUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualThisPeriodUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualUnits()</pre>
<div class="block">Gets the value of the actualUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualUnits</h4>
<pre>public&nbsp;void&nbsp;setActualUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAssignmentPercentComplete--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAssignmentPercentComplete</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getAssignmentPercentComplete()</pre>
<div class="block">Gets the value of the assignmentPercentComplete property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAssignmentPercentComplete-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAssignmentPercentComplete</h4>
<pre>public&nbsp;void&nbsp;setAssignmentPercentComplete(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the assignmentPercentComplete property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAtCompletionCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAtCompletionCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getAtCompletionCost()</pre>
<div class="block">Gets the value of the atCompletionCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAtCompletionCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAtCompletionCost</h4>
<pre>public&nbsp;void&nbsp;setAtCompletionCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the atCompletionCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAtCompletionDuration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAtCompletionDuration</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getAtCompletionDuration()</pre>
<div class="block">Gets the value of the atCompletionDuration property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAtCompletionDuration-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAtCompletionDuration</h4>
<pre>public&nbsp;void&nbsp;setAtCompletionDuration(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the atCompletionDuration property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAtCompletionUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAtCompletionUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getAtCompletionUnits()</pre>
<div class="block">Gets the value of the atCompletionUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAtCompletionUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAtCompletionUnits</h4>
<pre>public&nbsp;void&nbsp;setAtCompletionUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the atCompletionUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isAutoComputeActuals--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isAutoComputeActuals</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isAutoComputeActuals()</pre>
<div class="block">Gets the value of the autoComputeActuals property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAutoComputeActuals-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAutoComputeActuals</h4>
<pre>public&nbsp;void&nbsp;setAutoComputeActuals(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the autoComputeActuals property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBudgetAtCompletionCosts--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBudgetAtCompletionCosts</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBudgetAtCompletionCosts()</pre>
<div class="block">Gets the value of the budgetAtCompletionCosts property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBudgetAtCompletionCosts-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBudgetAtCompletionCosts</h4>
<pre>public&nbsp;void&nbsp;setBudgetAtCompletionCosts(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the budgetAtCompletionCosts property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBudgetAtCompletionUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBudgetAtCompletionUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBudgetAtCompletionUnits()</pre>
<div class="block">Gets the value of the budgetAtCompletionUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBudgetAtCompletionUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBudgetAtCompletionUnits</h4>
<pre>public&nbsp;void&nbsp;setBudgetAtCompletionUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the budgetAtCompletionUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCBSCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCBSCode</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getCBSCode()</pre>
<div class="block">Gets the value of the cbsCode property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCBSCode-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCBSCode</h4>
<pre>public&nbsp;void&nbsp;setCBSCode(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cbsCode property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCBSId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCBSId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getCBSId()</pre>
<div class="block">Gets the value of the cbsId property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="setCBSId-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCBSId</h4>
<pre>public&nbsp;void&nbsp;setCBSId(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cbsId property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="getCalendarName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCalendarName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getCalendarName()</pre>
<div class="block">Gets the value of the calendarName property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCalendarName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCalendarName</h4>
<pre>public&nbsp;void&nbsp;setCalendarName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the calendarName property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCalendarObjectId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCalendarObjectId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getCalendarObjectId()</pre>
<div class="block">Gets the value of the calendarObjectId property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="setCalendarObjectId-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCalendarObjectId</h4>
<pre>public&nbsp;void&nbsp;setCalendarObjectId(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the calendarObjectId property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="getCostAccountId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCostAccountId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getCostAccountId()</pre>
<div class="block">Gets the value of the costAccountId property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCostAccountId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCostAccountId</h4>
<pre>public&nbsp;void&nbsp;setCostAccountId(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the costAccountId property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCostAccountName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCostAccountName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getCostAccountName()</pre>
<div class="block">Gets the value of the costAccountName property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCostAccountName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCostAccountName</h4>
<pre>public&nbsp;void&nbsp;setCostAccountName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the costAccountName property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCostAccountObjectId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCostAccountObjectId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getCostAccountObjectId()</pre>
<div class="block">Gets the value of the costAccountObjectId property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="setCostAccountObjectId-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCostAccountObjectId</h4>
<pre>public&nbsp;void&nbsp;setCostAccountObjectId(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the costAccountObjectId property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="getCostPerQuantity--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCostPerQuantity</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCostPerQuantity()</pre>
<div class="block">Gets the value of the costPerQuantity property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCostPerQuantity-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCostPerQuantity</h4>
<pre>public&nbsp;void&nbsp;setCostPerQuantity(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the costPerQuantity property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCreateDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCreateDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getCreateDate()</pre>
<div class="block">Gets the value of the createDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCreateDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCreateDate</h4>
<pre>public&nbsp;void&nbsp;setCreateDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the createDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCreateUser--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCreateUser</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getCreateUser()</pre>
<div class="block">Gets the value of the createUser property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCreateUser-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCreateUser</h4>
<pre>public&nbsp;void&nbsp;setCreateUser(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the createUser property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isDrivingActivityDatesFlag--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDrivingActivityDatesFlag</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isDrivingActivityDatesFlag()</pre>
<div class="block">Gets the value of the drivingActivityDatesFlag property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDrivingActivityDatesFlag-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDrivingActivityDatesFlag</h4>
<pre>public&nbsp;void&nbsp;setDrivingActivityDatesFlag(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the drivingActivityDatesFlag property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getDurationPercentComplete--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDurationPercentComplete</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getDurationPercentComplete()</pre>
<div class="block">Gets the value of the durationPercentComplete property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDurationPercentComplete-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDurationPercentComplete</h4>
<pre>public&nbsp;void&nbsp;setDurationPercentComplete(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the durationPercentComplete property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getEstimateToCompletionCosts--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEstimateToCompletionCosts</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getEstimateToCompletionCosts()</pre>
<div class="block">Gets the value of the estimateToCompletionCosts property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEstimateToCompletionCosts-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEstimateToCompletionCosts</h4>
<pre>public&nbsp;void&nbsp;setEstimateToCompletionCosts(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the estimateToCompletionCosts property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getEstimateToCompletionUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEstimateToCompletionUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getEstimateToCompletionUnits()</pre>
<div class="block">Gets the value of the estimateToCompletionUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEstimateToCompletionUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEstimateToCompletionUnits</h4>
<pre>public&nbsp;void&nbsp;setEstimateToCompletionUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the estimateToCompletionUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getFinancialPeriodTmplId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFinancialPeriodTmplId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getFinancialPeriodTmplId()</pre>
<div class="block">Gets the value of the financialPeriodTmplId property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="setFinancialPeriodTmplId-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFinancialPeriodTmplId</h4>
<pre>public&nbsp;void&nbsp;setFinancialPeriodTmplId(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the financialPeriodTmplId property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="getFinishDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFinishDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getFinishDate()</pre>
<div class="block">Gets the value of the finishDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setFinishDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFinishDate</h4>
<pre>public&nbsp;void&nbsp;setFinishDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the finishDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getGUID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getGUID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getGUID()</pre>
<div class="block">Gets the value of the guid property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setGUID-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setGUID</h4>
<pre>public&nbsp;void&nbsp;setGUID(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the guid property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isHasFutureBucketData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isHasFutureBucketData</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isHasFutureBucketData()</pre>
<div class="block">Gets the value of the hasFutureBucketData property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setHasFutureBucketData-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setHasFutureBucketData</h4>
<pre>public&nbsp;void&nbsp;setHasFutureBucketData(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the hasFutureBucketData property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isIsActive--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isIsActive</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isIsActive()</pre>
<div class="block">Gets the value of the isActive property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setIsActive-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIsActive</h4>
<pre>public&nbsp;void&nbsp;setIsActive(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the isActive property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isIsActivityFlagged--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isIsActivityFlagged</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isIsActivityFlagged()</pre>
<div class="block">Gets the value of the isActivityFlagged property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setIsActivityFlagged-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIsActivityFlagged</h4>
<pre>public&nbsp;void&nbsp;setIsActivityFlagged(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the isActivityFlagged property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isIsBaseline--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isIsBaseline</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isIsBaseline()</pre>
<div class="block">Gets the value of the isBaseline property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setIsBaseline-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIsBaseline</h4>
<pre>public&nbsp;void&nbsp;setIsBaseline(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the isBaseline property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isIsCostUnitsLinked--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isIsCostUnitsLinked</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isIsCostUnitsLinked()</pre>
<div class="block">Gets the value of the isCostUnitsLinked property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setIsCostUnitsLinked-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIsCostUnitsLinked</h4>
<pre>public&nbsp;void&nbsp;setIsCostUnitsLinked(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the isCostUnitsLinked property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isIsOvertimeAllowed--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isIsOvertimeAllowed</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isIsOvertimeAllowed()</pre>
<div class="block">Gets the value of the isOvertimeAllowed property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setIsOvertimeAllowed-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIsOvertimeAllowed</h4>
<pre>public&nbsp;void&nbsp;setIsOvertimeAllowed(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the isOvertimeAllowed property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isIsPrimaryResource--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isIsPrimaryResource</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isIsPrimaryResource()</pre>
<div class="block">Gets the value of the isPrimaryResource property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setIsPrimaryResource-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIsPrimaryResource</h4>
<pre>public&nbsp;void&nbsp;setIsPrimaryResource(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the isPrimaryResource property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isIsTemplate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isIsTemplate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isIsTemplate()</pre>
<div class="block">Gets the value of the isTemplate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setIsTemplate-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIsTemplate</h4>
<pre>public&nbsp;void&nbsp;setIsTemplate(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the isTemplate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getLastUpdateDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastUpdateDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getLastUpdateDate()</pre>
<div class="block">Gets the value of the lastUpdateDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setLastUpdateDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLastUpdateDate</h4>
<pre>public&nbsp;void&nbsp;setLastUpdateDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the lastUpdateDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getLastUpdateUser--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLastUpdateUser</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getLastUpdateUser()</pre>
<div class="block">Gets the value of the lastUpdateUser property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setLastUpdateUser-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setLastUpdateUser</h4>
<pre>public&nbsp;void&nbsp;setLastUpdateUser(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the lastUpdateUser property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getObjectId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getObjectId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getObjectId()</pre>
<div class="block">Gets the value of the objectId property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="setObjectId-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setObjectId</h4>
<pre>public&nbsp;void&nbsp;setObjectId(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the objectId property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="getOvertimeFactor--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getOvertimeFactor</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getOvertimeFactor()</pre>
<div class="block">Gets the value of the overtimeFactor property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setOvertimeFactor-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setOvertimeFactor</h4>
<pre>public&nbsp;void&nbsp;setOvertimeFactor(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the overtimeFactor property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPendingActualOvertimeUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPendingActualOvertimeUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPendingActualOvertimeUnits()</pre>
<div class="block">Gets the value of the pendingActualOvertimeUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPendingActualOvertimeUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPendingActualOvertimeUnits</h4>
<pre>public&nbsp;void&nbsp;setPendingActualOvertimeUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the pendingActualOvertimeUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPendingActualRegularUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPendingActualRegularUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPendingActualRegularUnits()</pre>
<div class="block">Gets the value of the pendingActualRegularUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPendingActualRegularUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPendingActualRegularUnits</h4>
<pre>public&nbsp;void&nbsp;setPendingActualRegularUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the pendingActualRegularUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPendingPercentComplete--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPendingPercentComplete</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPendingPercentComplete()</pre>
<div class="block">Gets the value of the pendingPercentComplete property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPendingPercentComplete-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPendingPercentComplete</h4>
<pre>public&nbsp;void&nbsp;setPendingPercentComplete(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the pendingPercentComplete property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPendingRemainingUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPendingRemainingUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPendingRemainingUnits()</pre>
<div class="block">Gets the value of the pendingRemainingUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPendingRemainingUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPendingRemainingUnits</h4>
<pre>public&nbsp;void&nbsp;setPendingRemainingUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the pendingRemainingUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPercentComplete--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPercentComplete</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPercentComplete()</pre>
<div class="block">Gets the value of the percentComplete property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPercentComplete-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPercentComplete</h4>
<pre>public&nbsp;void&nbsp;setPercentComplete(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the percentComplete property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPercentCompleteType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPercentCompleteType</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getPercentCompleteType()</pre>
<div class="block">Gets the value of the percentCompleteType property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPercentCompleteType-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPercentCompleteType</h4>
<pre>public&nbsp;void&nbsp;setPercentCompleteType(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the percentCompleteType property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPlannedCost()</pre>
<div class="block">Gets the value of the plannedCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedCost</h4>
<pre>public&nbsp;void&nbsp;setPlannedCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedCurve--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedCurve</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getPlannedCurve()</pre>
<div class="block">Gets the value of the plannedCurve property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedCurve-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedCurve</h4>
<pre>public&nbsp;void&nbsp;setPlannedCurve(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedCurve property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedDuration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedDuration</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPlannedDuration()</pre>
<div class="block">Gets the value of the plannedDuration property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedDuration-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedDuration</h4>
<pre>public&nbsp;void&nbsp;setPlannedDuration(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedDuration property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedFinishDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedFinishDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getPlannedFinishDate()</pre>
<div class="block">Gets the value of the plannedFinishDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedFinishDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedFinishDate</h4>
<pre>public&nbsp;void&nbsp;setPlannedFinishDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedFinishDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedLag--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedLag</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPlannedLag()</pre>
<div class="block">Gets the value of the plannedLag property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedLag-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedLag</h4>
<pre>public&nbsp;void&nbsp;setPlannedLag(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedLag property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedStartDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedStartDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getPlannedStartDate()</pre>
<div class="block">Gets the value of the plannedStartDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedStartDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedStartDate</h4>
<pre>public&nbsp;void&nbsp;setPlannedStartDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedStartDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPlannedUnits()</pre>
<div class="block">Gets the value of the plannedUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedUnits</h4>
<pre>public&nbsp;void&nbsp;setPlannedUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedUnitsPerTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedUnitsPerTime</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPlannedUnitsPerTime()</pre>
<div class="block">Gets the value of the plannedUnitsPerTime property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedUnitsPerTime-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedUnitsPerTime</h4>
<pre>public&nbsp;void&nbsp;setPlannedUnitsPerTime(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedUnitsPerTime property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPricePerUnit--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPricePerUnit</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPricePerUnit()</pre>
<div class="block">Gets the value of the pricePerUnit property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPricePerUnit-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPricePerUnit</h4>
<pre>public&nbsp;void&nbsp;setPricePerUnit(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the pricePerUnit property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPriorActualOvertimeUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPriorActualOvertimeUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPriorActualOvertimeUnits()</pre>
<div class="block">Gets the value of the priorActualOvertimeUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPriorActualOvertimeUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPriorActualOvertimeUnits</h4>
<pre>public&nbsp;void&nbsp;setPriorActualOvertimeUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the priorActualOvertimeUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPriorActualRegularUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPriorActualRegularUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPriorActualRegularUnits()</pre>
<div class="block">Gets the value of the priorActualRegularUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPriorActualRegularUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPriorActualRegularUnits</h4>
<pre>public&nbsp;void&nbsp;setPriorActualRegularUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the priorActualRegularUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getProficiency--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProficiency</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getProficiency()</pre>
<div class="block">Gets the value of the proficiency property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setProficiency-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProficiency</h4>
<pre>public&nbsp;void&nbsp;setProficiency(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the proficiency property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getProjectFlag--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectFlag</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getProjectFlag()</pre>
<div class="block">Gets the value of the projectFlag property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setProjectFlag-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProjectFlag</h4>
<pre>public&nbsp;void&nbsp;setProjectFlag(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the projectFlag property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getProjectId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getProjectId()</pre>
<div class="block">Gets the value of the projectId property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setProjectId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProjectId</h4>
<pre>public&nbsp;void&nbsp;setProjectId(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the projectId property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getProjectName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getProjectName()</pre>
<div class="block">Gets the value of the projectName property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setProjectName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProjectName</h4>
<pre>public&nbsp;void&nbsp;setProjectName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the projectName property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getProjectObjectId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectObjectId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getProjectObjectId()</pre>
<div class="block">Gets the value of the projectObjectId property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="setProjectObjectId-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProjectObjectId</h4>
<pre>public&nbsp;void&nbsp;setProjectObjectId(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the projectObjectId property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="getProjectProjectFlag--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectProjectFlag</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getProjectProjectFlag()</pre>
<div class="block">Gets the value of the projectProjectFlag property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setProjectProjectFlag-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProjectProjectFlag</h4>
<pre>public&nbsp;void&nbsp;setProjectProjectFlag(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the projectProjectFlag property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRateSource--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRateSource</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getRateSource()</pre>
<div class="block">Gets the value of the rateSource property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRateSource-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRateSource</h4>
<pre>public&nbsp;void&nbsp;setRateSource(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the rateSource property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRateType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRateType</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getRateType()</pre>
<div class="block">Gets the value of the rateType property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRateType-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRateType</h4>
<pre>public&nbsp;void&nbsp;setRateType(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the rateType property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingCost()</pre>
<div class="block">Gets the value of the remainingCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingCurve--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingCurve</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getRemainingCurve()</pre>
<div class="block">Gets the value of the remainingCurve property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingCurve-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingCurve</h4>
<pre>public&nbsp;void&nbsp;setRemainingCurve(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingCurve property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingDuration--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingDuration</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingDuration()</pre>
<div class="block">Gets the value of the remainingDuration property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingDuration-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingDuration</h4>
<pre>public&nbsp;void&nbsp;setRemainingDuration(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingDuration property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingFinishDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingFinishDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getRemainingFinishDate()</pre>
<div class="block">Gets the value of the remainingFinishDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingFinishDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingFinishDate</h4>
<pre>public&nbsp;void&nbsp;setRemainingFinishDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingFinishDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingLag--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLag</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingLag()</pre>
<div class="block">Gets the value of the remainingLag property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingLag-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLag</h4>
<pre>public&nbsp;void&nbsp;setRemainingLag(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingLag property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingLateFinishDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLateFinishDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getRemainingLateFinishDate()</pre>
<div class="block">Gets the value of the remainingLateFinishDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingLateFinishDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLateFinishDate</h4>
<pre>public&nbsp;void&nbsp;setRemainingLateFinishDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingLateFinishDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingLateStartDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLateStartDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getRemainingLateStartDate()</pre>
<div class="block">Gets the value of the remainingLateStartDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingLateStartDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLateStartDate</h4>
<pre>public&nbsp;void&nbsp;setRemainingLateStartDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingLateStartDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingStartDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingStartDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getRemainingStartDate()</pre>
<div class="block">Gets the value of the remainingStartDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingStartDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingStartDate</h4>
<pre>public&nbsp;void&nbsp;setRemainingStartDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingStartDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingUnits()</pre>
<div class="block">Gets the value of the remainingUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingUnits</h4>
<pre>public&nbsp;void&nbsp;setRemainingUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingUnitsPerTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingUnitsPerTime</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingUnitsPerTime()</pre>
<div class="block">Gets the value of the remainingUnitsPerTime property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingUnitsPerTime-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingUnitsPerTime</h4>
<pre>public&nbsp;void&nbsp;setRemainingUnitsPerTime(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingUnitsPerTime property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getResourceCalendarName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceCalendarName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getResourceCalendarName()</pre>
<div class="block">Gets the value of the resourceCalendarName property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setResourceCalendarName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResourceCalendarName</h4>
<pre>public&nbsp;void&nbsp;setResourceCalendarName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the resourceCalendarName property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getResourceCurveName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceCurveName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getResourceCurveName()</pre>
<div class="block">Gets the value of the resourceCurveName property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setResourceCurveName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResourceCurveName</h4>
<pre>public&nbsp;void&nbsp;setResourceCurveName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the resourceCurveName property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getResourceCurveObjectId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceCurveObjectId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getResourceCurveObjectId()</pre>
<div class="block">Gets the value of the resourceCurveObjectId property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="setResourceCurveObjectId-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResourceCurveObjectId</h4>
<pre>public&nbsp;void&nbsp;setResourceCurveObjectId(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the resourceCurveObjectId property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="getResourceId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getResourceId()</pre>
<div class="block">Gets the value of the resourceId property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setResourceId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResourceId</h4>
<pre>public&nbsp;void&nbsp;setResourceId(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the resourceId property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getResourceName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getResourceName()</pre>
<div class="block">Gets the value of the resourceName property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setResourceName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResourceName</h4>
<pre>public&nbsp;void&nbsp;setResourceName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the resourceName property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getResourceObjectId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceObjectId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getResourceObjectId()</pre>
<div class="block">Gets the value of the resourceObjectId property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="setResourceObjectId-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResourceObjectId</h4>
<pre>public&nbsp;void&nbsp;setResourceObjectId(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the resourceObjectId property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="getResourceType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceType</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getResourceType()</pre>
<div class="block">Gets the value of the resourceType property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setResourceType-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResourceType</h4>
<pre>public&nbsp;void&nbsp;setResourceType(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the resourceType property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isReviewRequired--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isReviewRequired</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;isReviewRequired()</pre>
<div class="block">Gets the value of the reviewRequired property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setReviewRequired-java.lang.Boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setReviewRequired</h4>
<pre>public&nbsp;void&nbsp;setReviewRequired(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Boolean.html?is-external=true" title="class or interface in java.lang">Boolean</a>&nbsp;value)</pre>
<div class="block">Sets the value of the reviewRequired property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRoleId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRoleId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getRoleId()</pre>
<div class="block">Gets the value of the roleId property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRoleId-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRoleId</h4>
<pre>public&nbsp;void&nbsp;setRoleId(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the roleId property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRoleName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRoleName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getRoleName()</pre>
<div class="block">Gets the value of the roleName property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRoleName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRoleName</h4>
<pre>public&nbsp;void&nbsp;setRoleName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the roleName property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRoleObjectId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRoleObjectId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getRoleObjectId()</pre>
<div class="block">Gets the value of the roleObjectId property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="setRoleObjectId-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRoleObjectId</h4>
<pre>public&nbsp;void&nbsp;setRoleObjectId(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the roleObjectId property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="getRoleShortName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRoleShortName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getRoleShortName()</pre>
<div class="block">Gets the value of the roleShortName property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRoleShortName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRoleShortName</h4>
<pre>public&nbsp;void&nbsp;setRoleShortName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the roleShortName property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getStaffedRemainingCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStaffedRemainingCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getStaffedRemainingCost()</pre>
<div class="block">Gets the value of the staffedRemainingCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStaffedRemainingCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStaffedRemainingCost</h4>
<pre>public&nbsp;void&nbsp;setStaffedRemainingCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the staffedRemainingCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getStaffedRemainingUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStaffedRemainingUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getStaffedRemainingUnits()</pre>
<div class="block">Gets the value of the staffedRemainingUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStaffedRemainingUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStaffedRemainingUnits</h4>
<pre>public&nbsp;void&nbsp;setStaffedRemainingUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the staffedRemainingUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getStartDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getStartDate()</pre>
<div class="block">Gets the value of the startDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStartDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStartDate</h4>
<pre>public&nbsp;void&nbsp;setStartDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the startDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getStatusCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStatusCode</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getStatusCode()</pre>
<div class="block">Gets the value of the statusCode property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStatusCode-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStatusCode</h4>
<pre>public&nbsp;void&nbsp;setStatusCode(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the statusCode property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getTotalPastPeriodCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTotalPastPeriodCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getTotalPastPeriodCost()</pre>
<div class="block">Gets the value of the totalPastPeriodCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setTotalPastPeriodCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTotalPastPeriodCost</h4>
<pre>public&nbsp;void&nbsp;setTotalPastPeriodCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the totalPastPeriodCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getTotalPastPeriodUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTotalPastPeriodUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getTotalPastPeriodUnits()</pre>
<div class="block">Gets the value of the totalPastPeriodUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setTotalPastPeriodUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTotalPastPeriodUnits</h4>
<pre>public&nbsp;void&nbsp;setTotalPastPeriodUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the totalPastPeriodUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getUnitsPercentComplete--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnitsPercentComplete</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getUnitsPercentComplete()</pre>
<div class="block">Gets the value of the unitsPercentComplete property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setUnitsPercentComplete-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUnitsPercentComplete</h4>
<pre>public&nbsp;void&nbsp;setUnitsPercentComplete(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the unitsPercentComplete property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getUnreadCommentCount--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnreadCommentCount</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getUnreadCommentCount()</pre>
<div class="block">Gets the value of the unreadCommentCount property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="setUnreadCommentCount-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUnreadCommentCount</h4>
<pre>public&nbsp;void&nbsp;setUnreadCommentCount(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the unreadCommentCount property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="getUnstaffedRemainingCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnstaffedRemainingCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getUnstaffedRemainingCost()</pre>
<div class="block">Gets the value of the unstaffedRemainingCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setUnstaffedRemainingCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUnstaffedRemainingCost</h4>
<pre>public&nbsp;void&nbsp;setUnstaffedRemainingCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the unstaffedRemainingCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getUnstaffedRemainingUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnstaffedRemainingUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getUnstaffedRemainingUnits()</pre>
<div class="block">Gets the value of the unstaffedRemainingUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setUnstaffedRemainingUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setUnstaffedRemainingUnits</h4>
<pre>public&nbsp;void&nbsp;setUnstaffedRemainingUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the unstaffedRemainingUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getWBSNamePath--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWBSNamePath</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getWBSNamePath()</pre>
<div class="block">Gets the value of the wbsNamePath property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setWBSNamePath-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWBSNamePath</h4>
<pre>public&nbsp;void&nbsp;setWBSNamePath(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the wbsNamePath property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getWBSObjectId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWBSObjectId</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getWBSObjectId()</pre>
<div class="block">Gets the value of the wbsObjectId property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="setWBSObjectId-java.lang.Integer-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWBSObjectId</h4>
<pre>public&nbsp;void&nbsp;setWBSObjectId(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;value)</pre>
<div class="block">Sets the value of the wbsObjectId property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang"><code>Integer</code></a></dd>
</dl>
</li>
</ul>
<a name="getUDF--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUDF</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/primavera/schema/UDFAssignmentType.html" title="class in org.mpxj.primavera.schema">UDFAssignmentType</a>&gt;&nbsp;getUDF()</pre>
<div class="block">Gets the value of the udf property.

 <p>
 This accessor method returns a reference to the live list,
 not a snapshot. Therefore any modification you make to the
 returned list will be present inside the Jakarta XML Binding object.
 This is why there is not a <CODE>set</CODE> method for the udf property.

 <p>
 For example, to add a new item, do as follows:
 <pre>
    getUDF().add(newItem);
 </pre>


 <p>
 Objects of the following type(s) are allowed in the list
 <a href="../../../../org/mpxj/primavera/schema/UDFAssignmentType.html" title="class in org.mpxj.primavera.schema"><code>UDFAssignmentType</code></a></div>
</li>
</ul>
<a name="getSpread--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSpread</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentSpreadType.html" title="class in org.mpxj.primavera.schema">ResourceAssignmentSpreadType</a>&nbsp;getSpread()</pre>
<div class="block">Gets the value of the spread property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentSpreadType.html" title="class in org.mpxj.primavera.schema"><code>ResourceAssignmentSpreadType</code></a></dd>
</dl>
</li>
</ul>
<a name="setSpread-org.mpxj.primavera.schema.ResourceAssignmentSpreadType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSpread</h4>
<pre>public&nbsp;void&nbsp;setSpread(<a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentSpreadType.html" title="class in org.mpxj.primavera.schema">ResourceAssignmentSpreadType</a>&nbsp;value)</pre>
<div class="block">Sets the value of the spread property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentSpreadType.html" title="class in org.mpxj.primavera.schema"><code>ResourceAssignmentSpreadType</code></a></dd>
</dl>
</li>
</ul>
<a name="getResourceRequest--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceRequest</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/primavera/schema/ResourceRequestType.html" title="class in org.mpxj.primavera.schema">ResourceRequestType</a>&nbsp;getResourceRequest()</pre>
<div class="block">Gets the value of the resourceRequest property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/primavera/schema/ResourceRequestType.html" title="class in org.mpxj.primavera.schema"><code>ResourceRequestType</code></a></dd>
</dl>
</li>
</ul>
<a name="setResourceRequest-org.mpxj.primavera.schema.ResourceRequestType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResourceRequest</h4>
<pre>public&nbsp;void&nbsp;setResourceRequest(<a href="../../../../org/mpxj/primavera/schema/ResourceRequestType.html" title="class in org.mpxj.primavera.schema">ResourceRequestType</a>&nbsp;value)</pre>
<div class="block">Sets the value of the resourceRequest property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/primavera/schema/ResourceRequestType.html" title="class in org.mpxj.primavera.schema"><code>ResourceRequestType</code></a></dd>
</dl>
</li>
</ul>
<a name="getCode--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getCode</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/primavera/schema/CodeAssignmentType.html" title="class in org.mpxj.primavera.schema">CodeAssignmentType</a>&gt;&nbsp;getCode()</pre>
<div class="block">Gets the value of the code property.

 <p>
 This accessor method returns a reference to the live list,
 not a snapshot. Therefore any modification you make to the
 returned list will be present inside the Jakarta XML Binding object.
 This is why there is not a <CODE>set</CODE> method for the code property.

 <p>
 For example, to add a new item, do as follows:
 <pre>
    getCode().add(newItem);
 </pre>


 <p>
 Objects of the following type(s) are allowed in the list
 <a href="../../../../org/mpxj/primavera/schema/CodeAssignmentType.html" title="class in org.mpxj.primavera.schema"><code>CodeAssignmentType</code></a></div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ResourceAssignmentType.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentSpreadType.Period.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/primavera/schema/ResourceAssignmentUpdateType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/primavera/schema/ResourceAssignmentType.html" target="_top">Frames</a></li>
<li><a href="ResourceAssignmentType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
