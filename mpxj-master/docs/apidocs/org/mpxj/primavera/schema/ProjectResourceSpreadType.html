<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ProjectResourceSpreadType (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ProjectResourceSpreadType (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProjectResourceSpreadType.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/primavera/schema/ProjectResourceQuantityType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.Period.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/primavera/schema/ProjectResourceSpreadType.html" target="_top">Frames</a></li>
<li><a href="ProjectResourceSpreadType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.primavera.schema</div>
<h2 title="Class ProjectResourceSpreadType" class="title">Class ProjectResourceSpreadType</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.primavera.schema.ProjectResourceSpreadType</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ProjectResourceSpreadType</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block"><p>Java class for ProjectResourceSpreadType complex type.

 <p>The following schema fragment specifies the expected content contained within this class.

 <pre>
 &lt;complexType name="ProjectResourceSpreadType"&gt;
   &lt;complexContent&gt;
     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       &lt;sequence&gt;
         &lt;element name="WBSObjectId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
         &lt;element name="FinancialPeriodObjectId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
         &lt;element name="ProjectObjectId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
         &lt;element name="ResourceType"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;enumeration value="Labor"/&gt;
               &lt;enumeration value="Nonlabor"/&gt;
               &lt;enumeration value="Material"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="ResourceObjectId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
         &lt;element name="StartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
         &lt;element name="EndDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
         &lt;element name="PeriodType"&gt;
           &lt;simpleType&gt;
             &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
               &lt;enumeration value="Hour"/&gt;
               &lt;enumeration value="Day"/&gt;
               &lt;enumeration value="Week"/&gt;
               &lt;enumeration value="Month"/&gt;
               &lt;enumeration value="Quarter"/&gt;
               &lt;enumeration value="Year"/&gt;
               &lt;enumeration value="Financial Period"/&gt;
             &lt;/restriction&gt;
           &lt;/simpleType&gt;
         &lt;/element&gt;
         &lt;element name="Period" maxOccurs="unbounded" minOccurs="0"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="StartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
                   &lt;element name="EndDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
                   &lt;element name="ActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="ActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="ActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="AtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="Limit" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeLimit" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="PeriodActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativePeriodActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="PeriodAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativePeriodAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="PlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativePlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="RemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="RemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="StaffedActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeStaffedActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="StaffedActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeStaffedActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="StaffedActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeStaffedActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="StaffedAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeStaffedAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="StaffedPlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeStaffedPlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="StaffedRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeStaffedRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="StaffedRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeStaffedRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="UnstaffedActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeUnstaffedActualOvertimeUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="UnstaffedActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeUnstaffedActualRegularUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="UnstaffedActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeUnstaffedActualUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="UnstaffedAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeUnstaffedAtCompletionUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="UnstaffedPlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeUnstaffedPlannedUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="UnstaffedRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeUnstaffedRemainingLateUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="UnstaffedRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeUnstaffedRemainingUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="ActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="ActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="ActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="AtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="PeriodActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativePeriodActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="PeriodAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativePeriodAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="PlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativePlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="RemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="RemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="StaffedActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeStaffedActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="StaffedActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeStaffedActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="StaffedActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeStaffedActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="StaffedAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeStaffedAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="StaffedPlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeStaffedPlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="StaffedRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeStaffedRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="StaffedRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeStaffedRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="UnstaffedActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeUnstaffedActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="UnstaffedActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeUnstaffedActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="UnstaffedActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeUnstaffedActualRegularCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="UnstaffedAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeUnstaffedAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="UnstaffedPlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeUnstaffedPlannedCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="UnstaffedRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeUnstaffedRemainingCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="UnstaffedRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                   &lt;element name="CumulativeUnstaffedRemainingLateCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
       &lt;/sequence&gt;
     &lt;/restriction&gt;
   &lt;/complexContent&gt;
 &lt;/complexType&gt;
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.Period.html" title="class in org.mpxj.primavera.schema">ProjectResourceSpreadType.Period</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#endDate">endDate</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#financialPeriodObjectId">financialPeriodObjectId</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.Period.html" title="class in org.mpxj.primavera.schema">ProjectResourceSpreadType.Period</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#period">period</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#periodType">periodType</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#projectObjectId">projectObjectId</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#resourceObjectId">resourceObjectId</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#resourceType">resourceType</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#startDate">startDate</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#wbsObjectId">wbsObjectId</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#ProjectResourceSpreadType--">ProjectResourceSpreadType</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#getEndDate--">getEndDate</a></span>()</code>
<div class="block">Gets the value of the endDate property.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#getFinancialPeriodObjectId--">getFinancialPeriodObjectId</a></span>()</code>
<div class="block">Gets the value of the financialPeriodObjectId property.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.Period.html" title="class in org.mpxj.primavera.schema">ProjectResourceSpreadType.Period</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#getPeriod--">getPeriod</a></span>()</code>
<div class="block">Gets the value of the period property.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#getPeriodType--">getPeriodType</a></span>()</code>
<div class="block">Gets the value of the periodType property.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#getProjectObjectId--">getProjectObjectId</a></span>()</code>
<div class="block">Gets the value of the projectObjectId property.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#getResourceObjectId--">getResourceObjectId</a></span>()</code>
<div class="block">Gets the value of the resourceObjectId property.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#getResourceType--">getResourceType</a></span>()</code>
<div class="block">Gets the value of the resourceType property.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#getStartDate--">getStartDate</a></span>()</code>
<div class="block">Gets the value of the startDate property.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#getWBSObjectId--">getWBSObjectId</a></span>()</code>
<div class="block">Gets the value of the wbsObjectId property.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#setEndDate-java.time.LocalDateTime-">setEndDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the endDate property.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#setFinancialPeriodObjectId-int-">setFinancialPeriodObjectId</a></span>(int&nbsp;value)</code>
<div class="block">Sets the value of the financialPeriodObjectId property.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#setPeriodType-java.lang.String-">setPeriodType</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the periodType property.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#setProjectObjectId-int-">setProjectObjectId</a></span>(int&nbsp;value)</code>
<div class="block">Sets the value of the projectObjectId property.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#setResourceObjectId-int-">setResourceObjectId</a></span>(int&nbsp;value)</code>
<div class="block">Sets the value of the resourceObjectId property.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#setResourceType-java.lang.String-">setResourceType</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the resourceType property.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#setStartDate-java.time.LocalDateTime-">setStartDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the startDate property.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.html#setWBSObjectId-int-">setWBSObjectId</a></span>(int&nbsp;value)</code>
<div class="block">Sets the value of the wbsObjectId property.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="wbsObjectId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>wbsObjectId</h4>
<pre>protected&nbsp;int wbsObjectId</pre>
</li>
</ul>
<a name="financialPeriodObjectId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>financialPeriodObjectId</h4>
<pre>protected&nbsp;int financialPeriodObjectId</pre>
</li>
</ul>
<a name="projectObjectId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>projectObjectId</h4>
<pre>protected&nbsp;int projectObjectId</pre>
</li>
</ul>
<a name="resourceType">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resourceType</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> resourceType</pre>
</li>
</ul>
<a name="resourceObjectId">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resourceObjectId</h4>
<pre>protected&nbsp;int resourceObjectId</pre>
</li>
</ul>
<a name="startDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> startDate</pre>
</li>
</ul>
<a name="endDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>endDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> endDate</pre>
</li>
</ul>
<a name="periodType">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>periodType</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> periodType</pre>
</li>
</ul>
<a name="period">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>period</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.Period.html" title="class in org.mpxj.primavera.schema">ProjectResourceSpreadType.Period</a>&gt; period</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ProjectResourceSpreadType--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ProjectResourceSpreadType</h4>
<pre>public&nbsp;ProjectResourceSpreadType()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getWBSObjectId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWBSObjectId</h4>
<pre>public&nbsp;int&nbsp;getWBSObjectId()</pre>
<div class="block">Gets the value of the wbsObjectId property.</div>
</li>
</ul>
<a name="setWBSObjectId-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWBSObjectId</h4>
<pre>public&nbsp;void&nbsp;setWBSObjectId(int&nbsp;value)</pre>
<div class="block">Sets the value of the wbsObjectId property.</div>
</li>
</ul>
<a name="getFinancialPeriodObjectId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFinancialPeriodObjectId</h4>
<pre>public&nbsp;int&nbsp;getFinancialPeriodObjectId()</pre>
<div class="block">Gets the value of the financialPeriodObjectId property.</div>
</li>
</ul>
<a name="setFinancialPeriodObjectId-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFinancialPeriodObjectId</h4>
<pre>public&nbsp;void&nbsp;setFinancialPeriodObjectId(int&nbsp;value)</pre>
<div class="block">Sets the value of the financialPeriodObjectId property.</div>
</li>
</ul>
<a name="getProjectObjectId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectObjectId</h4>
<pre>public&nbsp;int&nbsp;getProjectObjectId()</pre>
<div class="block">Gets the value of the projectObjectId property.</div>
</li>
</ul>
<a name="setProjectObjectId-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProjectObjectId</h4>
<pre>public&nbsp;void&nbsp;setProjectObjectId(int&nbsp;value)</pre>
<div class="block">Sets the value of the projectObjectId property.</div>
</li>
</ul>
<a name="getResourceType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceType</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getResourceType()</pre>
<div class="block">Gets the value of the resourceType property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setResourceType-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResourceType</h4>
<pre>public&nbsp;void&nbsp;setResourceType(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the resourceType property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getResourceObjectId--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceObjectId</h4>
<pre>public&nbsp;int&nbsp;getResourceObjectId()</pre>
<div class="block">Gets the value of the resourceObjectId property.</div>
</li>
</ul>
<a name="setResourceObjectId-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResourceObjectId</h4>
<pre>public&nbsp;void&nbsp;setResourceObjectId(int&nbsp;value)</pre>
<div class="block">Sets the value of the resourceObjectId property.</div>
</li>
</ul>
<a name="getStartDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getStartDate()</pre>
<div class="block">Gets the value of the startDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStartDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStartDate</h4>
<pre>public&nbsp;void&nbsp;setStartDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the startDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getEndDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEndDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getEndDate()</pre>
<div class="block">Gets the value of the endDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEndDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEndDate</h4>
<pre>public&nbsp;void&nbsp;setEndDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the endDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeriodType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriodType</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getPeriodType()</pre>
<div class="block">Gets the value of the periodType property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPeriodType-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriodType</h4>
<pre>public&nbsp;void&nbsp;setPeriodType(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the periodType property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeriod--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getPeriod</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.Period.html" title="class in org.mpxj.primavera.schema">ProjectResourceSpreadType.Period</a>&gt;&nbsp;getPeriod()</pre>
<div class="block">Gets the value of the period property.

 <p>
 This accessor method returns a reference to the live list,
 not a snapshot. Therefore any modification you make to the
 returned list will be present inside the Jakarta XML Binding object.
 This is why there is not a <CODE>set</CODE> method for the period property.

 <p>
 For example, to add a new item, do as follows:
 <pre>
    getPeriod().add(newItem);
 </pre>


 <p>
 Objects of the following type(s) are allowed in the list
 <a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.Period.html" title="class in org.mpxj.primavera.schema"><code>ProjectResourceSpreadType.Period</code></a></div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProjectResourceSpreadType.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/primavera/schema/ProjectResourceQuantityType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/primavera/schema/ProjectResourceSpreadType.Period.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/primavera/schema/ProjectResourceSpreadType.html" target="_top">Frames</a></li>
<li><a href="ProjectResourceSpreadType.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
