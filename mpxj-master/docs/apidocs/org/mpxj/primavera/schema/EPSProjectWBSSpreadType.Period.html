<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>EPSProjectWBSSpreadType.Period (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="EPSProjectWBSSpreadType.Period (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10,"i53":10,"i54":10,"i55":10,"i56":10,"i57":10,"i58":10,"i59":10,"i60":10,"i61":10,"i62":10,"i63":10,"i64":10,"i65":10,"i66":10,"i67":10,"i68":10,"i69":10,"i70":10,"i71":10,"i72":10,"i73":10,"i74":10,"i75":10,"i76":10,"i77":10,"i78":10,"i79":10,"i80":10,"i81":10,"i82":10,"i83":10,"i84":10,"i85":10,"i86":10,"i87":10,"i88":10,"i89":10,"i90":10,"i91":10,"i92":10,"i93":10,"i94":10,"i95":10,"i96":10,"i97":10,"i98":10,"i99":10,"i100":10,"i101":10,"i102":10,"i103":10,"i104":10,"i105":10,"i106":10,"i107":10,"i108":10,"i109":10,"i110":10,"i111":10,"i112":10,"i113":10,"i114":10,"i115":10,"i116":10,"i117":10,"i118":10,"i119":10,"i120":10,"i121":10,"i122":10,"i123":10,"i124":10,"i125":10,"i126":10,"i127":10,"i128":10,"i129":10,"i130":10,"i131":10,"i132":10,"i133":10,"i134":10,"i135":10,"i136":10,"i137":10,"i138":10,"i139":10,"i140":10,"i141":10,"i142":10,"i143":10,"i144":10,"i145":10,"i146":10,"i147":10,"i148":10,"i149":10,"i150":10,"i151":10,"i152":10,"i153":10,"i154":10,"i155":10,"i156":10,"i157":10,"i158":10,"i159":10,"i160":10,"i161":10,"i162":10,"i163":10,"i164":10,"i165":10,"i166":10,"i167":10,"i168":10,"i169":10,"i170":10,"i171":10,"i172":10,"i173":10,"i174":10,"i175":10,"i176":10,"i177":10,"i178":10,"i179":10,"i180":10,"i181":10,"i182":10,"i183":10,"i184":10,"i185":10,"i186":10,"i187":10,"i188":10,"i189":10,"i190":10,"i191":10,"i192":10,"i193":10,"i194":10,"i195":10,"i196":10,"i197":10,"i198":10,"i199":10,"i200":10,"i201":10,"i202":10,"i203":10,"i204":10,"i205":10,"i206":10,"i207":10,"i208":10,"i209":10,"i210":10,"i211":10,"i212":10,"i213":10,"i214":10,"i215":10,"i216":10,"i217":10,"i218":10,"i219":10,"i220":10,"i221":10,"i222":10,"i223":10,"i224":10,"i225":10,"i226":10,"i227":10,"i228":10,"i229":10,"i230":10,"i231":10,"i232":10,"i233":10,"i234":10,"i235":10,"i236":10,"i237":10,"i238":10,"i239":10,"i240":10,"i241":10,"i242":10,"i243":10,"i244":10,"i245":10,"i246":10,"i247":10,"i248":10,"i249":10,"i250":10,"i251":10,"i252":10,"i253":10,"i254":10,"i255":10,"i256":10,"i257":10,"i258":10,"i259":10,"i260":10,"i261":10,"i262":10,"i263":10,"i264":10,"i265":10,"i266":10,"i267":10,"i268":10,"i269":10,"i270":10,"i271":10,"i272":10,"i273":10,"i274":10,"i275":10,"i276":10,"i277":10,"i278":10,"i279":10,"i280":10,"i281":10,"i282":10,"i283":10,"i284":10,"i285":10,"i286":10,"i287":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/EPSProjectWBSSpreadType.Period.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/primavera/schema/EPSSpendingPlanType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html" target="_top">Frames</a></li>
<li><a href="EPSProjectWBSSpreadType.Period.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.primavera.schema</div>
<h2 title="Class EPSProjectWBSSpreadType.Period" class="title">Class EPSProjectWBSSpreadType.Period</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.primavera.schema.EPSProjectWBSSpreadType.Period</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.html" title="class in org.mpxj.primavera.schema">EPSProjectWBSSpreadType</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">EPSProjectWBSSpreadType.Period</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block"><p>Java class for anonymous complex type.

 <p>The following schema fragment specifies the expected content contained within this class.

 <pre>
 &lt;complexType&gt;
   &lt;complexContent&gt;
     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       &lt;sequence&gt;
         &lt;element name="StartDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
         &lt;element name="EndDate" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
         &lt;element name="ActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeActualNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="AtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="AtCompletionNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeAtCompletionNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="BaselinePlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaselinePlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="BaselinePlannedNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaselinePlannedNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="EarnedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeEarnedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="EstimateAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeEstimateAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="EstimateToCompleteLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeEstimateToCompleteLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PeriodActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePeriodActualLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PeriodActualNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePeriodActualNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PeriodAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePeriodAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PeriodAtCompletionNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePeriodAtCompletionNonLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PeriodEarnedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePeriodEarnedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PeriodEstimateAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePeriodEstimateAtCompletionLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PeriodPlannedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePeriodPlannedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePlannedLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PlannedNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePlannedNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PlannedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePlannedValueLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingLateLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingLateLaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingLateNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingLateNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingNonlaborUnits" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeActualNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="ActualTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeActualTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="AtCompletionExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeAtCompletionExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="AtCompletionLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeAtCompletionLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="AtCompletionMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeAtCompletionMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="AtCompletionNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeAtCompletionNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="AtCompletionTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeAtCompletionTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="BaselinePlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaselinePlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="BaselinePlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaselinePlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="BaselinePlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaselinePlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="BaselinePlannedNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaselinePlannedNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="BaselinePlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeBaselinePlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="EarnedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeEarnedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="EstimateAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeEstimateAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="EstimateToCompleteCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeEstimateToCompleteCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PeriodActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePeriodActualCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PeriodActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePeriodActualExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PeriodActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePeriodActualLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PeriodActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePeriodActualMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PeriodActualNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePeriodActualNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PeriodAtCompletionExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePeriodAtCompletionExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PeriodAtCompletionLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePeriodAtCompletionLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PeriodAtCompletionMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePeriodAtCompletionMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PeriodAtCompletionNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePeriodAtCompletionNonLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PeriodAtCompletionTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePeriodAtCompletionTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PeriodEarnedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePeriodEarnedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PeriodEstimateAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePeriodEstimateAtCompletionCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PeriodPlannedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePeriodPlannedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePlannedExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePlannedLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePlannedMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PlannedNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePlannedNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePlannedTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="PlannedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativePlannedValueCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingLateExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingLateExpenseCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingLateLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingLateLaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingLateMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingLateMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingLateNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingLateNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingLateTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingLateTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingMaterialCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingNonlaborCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="RemainingTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
         &lt;element name="CumulativeRemainingTotalCost" type="{http://www.w3.org/2001/XMLSchema}double" minOccurs="0"/&gt;
       &lt;/sequence&gt;
     &lt;/restriction&gt;
   &lt;/complexContent&gt;
 &lt;/complexType&gt;
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#actualCost">actualCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#actualExpenseCost">actualExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#actualLaborCost">actualLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#actualLaborUnits">actualLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#actualMaterialCost">actualMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#actualNonlaborCost">actualNonlaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#actualNonlaborUnits">actualNonlaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#actualTotalCost">actualTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#atCompletionExpenseCost">atCompletionExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#atCompletionLaborCost">atCompletionLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#atCompletionLaborUnits">atCompletionLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#atCompletionMaterialCost">atCompletionMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#atCompletionNonlaborCost">atCompletionNonlaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#atCompletionNonlaborUnits">atCompletionNonlaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#atCompletionTotalCost">atCompletionTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#baselinePlannedExpenseCost">baselinePlannedExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#baselinePlannedLaborCost">baselinePlannedLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#baselinePlannedLaborUnits">baselinePlannedLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#baselinePlannedMaterialCost">baselinePlannedMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#baselinePlannedNonlaborCost">baselinePlannedNonlaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#baselinePlannedNonlaborUnits">baselinePlannedNonlaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#baselinePlannedTotalCost">baselinePlannedTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeActualCost">cumulativeActualCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeActualExpenseCost">cumulativeActualExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeActualLaborCost">cumulativeActualLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeActualLaborUnits">cumulativeActualLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeActualMaterialCost">cumulativeActualMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeActualNonlaborCost">cumulativeActualNonlaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeActualNonlaborUnits">cumulativeActualNonlaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeActualTotalCost">cumulativeActualTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeAtCompletionExpenseCost">cumulativeAtCompletionExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeAtCompletionLaborCost">cumulativeAtCompletionLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeAtCompletionLaborUnits">cumulativeAtCompletionLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeAtCompletionMaterialCost">cumulativeAtCompletionMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeAtCompletionNonlaborCost">cumulativeAtCompletionNonlaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeAtCompletionNonlaborUnits">cumulativeAtCompletionNonlaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeAtCompletionTotalCost">cumulativeAtCompletionTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeBaselinePlannedExpenseCost">cumulativeBaselinePlannedExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeBaselinePlannedLaborCost">cumulativeBaselinePlannedLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeBaselinePlannedLaborUnits">cumulativeBaselinePlannedLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeBaselinePlannedMaterialCost">cumulativeBaselinePlannedMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeBaselinePlannedNonlaborCost">cumulativeBaselinePlannedNonlaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeBaselinePlannedNonlaborUnits">cumulativeBaselinePlannedNonlaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeBaselinePlannedTotalCost">cumulativeBaselinePlannedTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeEarnedValueCost">cumulativeEarnedValueCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeEarnedValueLaborUnits">cumulativeEarnedValueLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeEstimateAtCompletionCost">cumulativeEstimateAtCompletionCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeEstimateAtCompletionLaborUnits">cumulativeEstimateAtCompletionLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeEstimateToCompleteCost">cumulativeEstimateToCompleteCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeEstimateToCompleteLaborUnits">cumulativeEstimateToCompleteLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePeriodActualCost">cumulativePeriodActualCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePeriodActualExpenseCost">cumulativePeriodActualExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePeriodActualLaborCost">cumulativePeriodActualLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePeriodActualLaborUnits">cumulativePeriodActualLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePeriodActualMaterialCost">cumulativePeriodActualMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePeriodActualNonLaborCost">cumulativePeriodActualNonLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePeriodActualNonLaborUnits">cumulativePeriodActualNonLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePeriodAtCompletionExpenseCost">cumulativePeriodAtCompletionExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePeriodAtCompletionLaborCost">cumulativePeriodAtCompletionLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePeriodAtCompletionLaborUnits">cumulativePeriodAtCompletionLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePeriodAtCompletionMaterialCost">cumulativePeriodAtCompletionMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePeriodAtCompletionNonLaborCost">cumulativePeriodAtCompletionNonLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePeriodAtCompletionNonLaborUnits">cumulativePeriodAtCompletionNonLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePeriodAtCompletionTotalCost">cumulativePeriodAtCompletionTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePeriodEarnedValueCost">cumulativePeriodEarnedValueCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePeriodEarnedValueLaborUnits">cumulativePeriodEarnedValueLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePeriodEstimateAtCompletionCost">cumulativePeriodEstimateAtCompletionCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePeriodEstimateAtCompletionLaborUnits">cumulativePeriodEstimateAtCompletionLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePeriodPlannedValueCost">cumulativePeriodPlannedValueCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePeriodPlannedValueLaborUnits">cumulativePeriodPlannedValueLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePlannedExpenseCost">cumulativePlannedExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePlannedLaborCost">cumulativePlannedLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePlannedLaborUnits">cumulativePlannedLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePlannedMaterialCost">cumulativePlannedMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePlannedNonlaborCost">cumulativePlannedNonlaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePlannedNonlaborUnits">cumulativePlannedNonlaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePlannedTotalCost">cumulativePlannedTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePlannedValueCost">cumulativePlannedValueCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativePlannedValueLaborUnits">cumulativePlannedValueLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeRemainingExpenseCost">cumulativeRemainingExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeRemainingLaborCost">cumulativeRemainingLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeRemainingLaborUnits">cumulativeRemainingLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeRemainingLateExpenseCost">cumulativeRemainingLateExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeRemainingLateLaborCost">cumulativeRemainingLateLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeRemainingLateLaborUnits">cumulativeRemainingLateLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeRemainingLateMaterialCost">cumulativeRemainingLateMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeRemainingLateNonlaborCost">cumulativeRemainingLateNonlaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeRemainingLateNonlaborUnits">cumulativeRemainingLateNonlaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeRemainingLateTotalCost">cumulativeRemainingLateTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeRemainingMaterialCost">cumulativeRemainingMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeRemainingNonlaborCost">cumulativeRemainingNonlaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeRemainingNonlaborUnits">cumulativeRemainingNonlaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#cumulativeRemainingTotalCost">cumulativeRemainingTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#earnedValueCost">earnedValueCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#earnedValueLaborUnits">earnedValueLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#endDate">endDate</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#estimateAtCompletionCost">estimateAtCompletionCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#estimateAtCompletionLaborUnits">estimateAtCompletionLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#estimateToCompleteCost">estimateToCompleteCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#estimateToCompleteLaborUnits">estimateToCompleteLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#periodActualCost">periodActualCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#periodActualExpenseCost">periodActualExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#periodActualLaborCost">periodActualLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#periodActualLaborUnits">periodActualLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#periodActualMaterialCost">periodActualMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#periodActualNonLaborCost">periodActualNonLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#periodActualNonLaborUnits">periodActualNonLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#periodAtCompletionExpenseCost">periodAtCompletionExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#periodAtCompletionLaborCost">periodAtCompletionLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#periodAtCompletionLaborUnits">periodAtCompletionLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#periodAtCompletionMaterialCost">periodAtCompletionMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#periodAtCompletionNonLaborCost">periodAtCompletionNonLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#periodAtCompletionNonLaborUnits">periodAtCompletionNonLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#periodAtCompletionTotalCost">periodAtCompletionTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#periodEarnedValueCost">periodEarnedValueCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#periodEarnedValueLaborUnits">periodEarnedValueLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#periodEstimateAtCompletionCost">periodEstimateAtCompletionCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#periodEstimateAtCompletionLaborUnits">periodEstimateAtCompletionLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#periodPlannedValueCost">periodPlannedValueCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#periodPlannedValueLaborUnits">periodPlannedValueLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#plannedExpenseCost">plannedExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#plannedLaborCost">plannedLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#plannedLaborUnits">plannedLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#plannedMaterialCost">plannedMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#plannedNonlaborCost">plannedNonlaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#plannedNonlaborUnits">plannedNonlaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#plannedTotalCost">plannedTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#plannedValueCost">plannedValueCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#plannedValueLaborUnits">plannedValueLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#remainingExpenseCost">remainingExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#remainingLaborCost">remainingLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#remainingLaborUnits">remainingLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#remainingLateExpenseCost">remainingLateExpenseCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#remainingLateLaborCost">remainingLateLaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#remainingLateLaborUnits">remainingLateLaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#remainingLateMaterialCost">remainingLateMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#remainingLateNonlaborCost">remainingLateNonlaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#remainingLateNonlaborUnits">remainingLateNonlaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#remainingLateTotalCost">remainingLateTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#remainingMaterialCost">remainingMaterialCost</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#remainingNonlaborCost">remainingNonlaborCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#remainingNonlaborUnits">remainingNonlaborUnits</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#remainingTotalCost">remainingTotalCost</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#startDate">startDate</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#Period--">Period</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getActualCost--">getActualCost</a></span>()</code>
<div class="block">Gets the value of the actualCost property.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getActualExpenseCost--">getActualExpenseCost</a></span>()</code>
<div class="block">Gets the value of the actualExpenseCost property.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getActualLaborCost--">getActualLaborCost</a></span>()</code>
<div class="block">Gets the value of the actualLaborCost property.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getActualLaborUnits--">getActualLaborUnits</a></span>()</code>
<div class="block">Gets the value of the actualLaborUnits property.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getActualMaterialCost--">getActualMaterialCost</a></span>()</code>
<div class="block">Gets the value of the actualMaterialCost property.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getActualNonlaborCost--">getActualNonlaborCost</a></span>()</code>
<div class="block">Gets the value of the actualNonlaborCost property.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getActualNonlaborUnits--">getActualNonlaborUnits</a></span>()</code>
<div class="block">Gets the value of the actualNonlaborUnits property.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getActualTotalCost--">getActualTotalCost</a></span>()</code>
<div class="block">Gets the value of the actualTotalCost property.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getAtCompletionExpenseCost--">getAtCompletionExpenseCost</a></span>()</code>
<div class="block">Gets the value of the atCompletionExpenseCost property.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getAtCompletionLaborCost--">getAtCompletionLaborCost</a></span>()</code>
<div class="block">Gets the value of the atCompletionLaborCost property.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getAtCompletionLaborUnits--">getAtCompletionLaborUnits</a></span>()</code>
<div class="block">Gets the value of the atCompletionLaborUnits property.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getAtCompletionMaterialCost--">getAtCompletionMaterialCost</a></span>()</code>
<div class="block">Gets the value of the atCompletionMaterialCost property.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getAtCompletionNonlaborCost--">getAtCompletionNonlaborCost</a></span>()</code>
<div class="block">Gets the value of the atCompletionNonlaborCost property.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getAtCompletionNonlaborUnits--">getAtCompletionNonlaborUnits</a></span>()</code>
<div class="block">Gets the value of the atCompletionNonlaborUnits property.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getAtCompletionTotalCost--">getAtCompletionTotalCost</a></span>()</code>
<div class="block">Gets the value of the atCompletionTotalCost property.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getBaselinePlannedExpenseCost--">getBaselinePlannedExpenseCost</a></span>()</code>
<div class="block">Gets the value of the baselinePlannedExpenseCost property.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getBaselinePlannedLaborCost--">getBaselinePlannedLaborCost</a></span>()</code>
<div class="block">Gets the value of the baselinePlannedLaborCost property.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getBaselinePlannedLaborUnits--">getBaselinePlannedLaborUnits</a></span>()</code>
<div class="block">Gets the value of the baselinePlannedLaborUnits property.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getBaselinePlannedMaterialCost--">getBaselinePlannedMaterialCost</a></span>()</code>
<div class="block">Gets the value of the baselinePlannedMaterialCost property.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getBaselinePlannedNonlaborCost--">getBaselinePlannedNonlaborCost</a></span>()</code>
<div class="block">Gets the value of the baselinePlannedNonlaborCost property.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getBaselinePlannedNonlaborUnits--">getBaselinePlannedNonlaborUnits</a></span>()</code>
<div class="block">Gets the value of the baselinePlannedNonlaborUnits property.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getBaselinePlannedTotalCost--">getBaselinePlannedTotalCost</a></span>()</code>
<div class="block">Gets the value of the baselinePlannedTotalCost property.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeActualCost--">getCumulativeActualCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeActualCost property.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeActualExpenseCost--">getCumulativeActualExpenseCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeActualExpenseCost property.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeActualLaborCost--">getCumulativeActualLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeActualLaborCost property.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeActualLaborUnits--">getCumulativeActualLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeActualLaborUnits property.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeActualMaterialCost--">getCumulativeActualMaterialCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeActualMaterialCost property.</div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeActualNonlaborCost--">getCumulativeActualNonlaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeActualNonlaborCost property.</div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeActualNonlaborUnits--">getCumulativeActualNonlaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeActualNonlaborUnits property.</div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeActualTotalCost--">getCumulativeActualTotalCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeActualTotalCost property.</div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeAtCompletionExpenseCost--">getCumulativeAtCompletionExpenseCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeAtCompletionExpenseCost property.</div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeAtCompletionLaborCost--">getCumulativeAtCompletionLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeAtCompletionLaborCost property.</div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeAtCompletionLaborUnits--">getCumulativeAtCompletionLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeAtCompletionLaborUnits property.</div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeAtCompletionMaterialCost--">getCumulativeAtCompletionMaterialCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeAtCompletionMaterialCost property.</div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeAtCompletionNonlaborCost--">getCumulativeAtCompletionNonlaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeAtCompletionNonlaborCost property.</div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeAtCompletionNonlaborUnits--">getCumulativeAtCompletionNonlaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeAtCompletionNonlaborUnits property.</div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeAtCompletionTotalCost--">getCumulativeAtCompletionTotalCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeAtCompletionTotalCost property.</div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeBaselinePlannedExpenseCost--">getCumulativeBaselinePlannedExpenseCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaselinePlannedExpenseCost property.</div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeBaselinePlannedLaborCost--">getCumulativeBaselinePlannedLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaselinePlannedLaborCost property.</div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeBaselinePlannedLaborUnits--">getCumulativeBaselinePlannedLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaselinePlannedLaborUnits property.</div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeBaselinePlannedMaterialCost--">getCumulativeBaselinePlannedMaterialCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaselinePlannedMaterialCost property.</div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeBaselinePlannedNonlaborCost--">getCumulativeBaselinePlannedNonlaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaselinePlannedNonlaborCost property.</div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeBaselinePlannedNonlaborUnits--">getCumulativeBaselinePlannedNonlaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaselinePlannedNonlaborUnits property.</div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeBaselinePlannedTotalCost--">getCumulativeBaselinePlannedTotalCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeBaselinePlannedTotalCost property.</div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeEarnedValueCost--">getCumulativeEarnedValueCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeEarnedValueCost property.</div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeEarnedValueLaborUnits--">getCumulativeEarnedValueLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeEarnedValueLaborUnits property.</div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeEstimateAtCompletionCost--">getCumulativeEstimateAtCompletionCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeEstimateAtCompletionCost property.</div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeEstimateAtCompletionLaborUnits--">getCumulativeEstimateAtCompletionLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeEstimateAtCompletionLaborUnits property.</div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeEstimateToCompleteCost--">getCumulativeEstimateToCompleteCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeEstimateToCompleteCost property.</div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeEstimateToCompleteLaborUnits--">getCumulativeEstimateToCompleteLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeEstimateToCompleteLaborUnits property.</div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePeriodActualCost--">getCumulativePeriodActualCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePeriodActualCost property.</div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePeriodActualExpenseCost--">getCumulativePeriodActualExpenseCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePeriodActualExpenseCost property.</div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePeriodActualLaborCost--">getCumulativePeriodActualLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePeriodActualLaborCost property.</div>
</td>
</tr>
<tr id="i53" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePeriodActualLaborUnits--">getCumulativePeriodActualLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativePeriodActualLaborUnits property.</div>
</td>
</tr>
<tr id="i54" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePeriodActualMaterialCost--">getCumulativePeriodActualMaterialCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePeriodActualMaterialCost property.</div>
</td>
</tr>
<tr id="i55" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePeriodActualNonLaborCost--">getCumulativePeriodActualNonLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePeriodActualNonLaborCost property.</div>
</td>
</tr>
<tr id="i56" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePeriodActualNonLaborUnits--">getCumulativePeriodActualNonLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativePeriodActualNonLaborUnits property.</div>
</td>
</tr>
<tr id="i57" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePeriodAtCompletionExpenseCost--">getCumulativePeriodAtCompletionExpenseCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePeriodAtCompletionExpenseCost property.</div>
</td>
</tr>
<tr id="i58" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePeriodAtCompletionLaborCost--">getCumulativePeriodAtCompletionLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePeriodAtCompletionLaborCost property.</div>
</td>
</tr>
<tr id="i59" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePeriodAtCompletionLaborUnits--">getCumulativePeriodAtCompletionLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativePeriodAtCompletionLaborUnits property.</div>
</td>
</tr>
<tr id="i60" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePeriodAtCompletionMaterialCost--">getCumulativePeriodAtCompletionMaterialCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePeriodAtCompletionMaterialCost property.</div>
</td>
</tr>
<tr id="i61" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePeriodAtCompletionNonLaborCost--">getCumulativePeriodAtCompletionNonLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePeriodAtCompletionNonLaborCost property.</div>
</td>
</tr>
<tr id="i62" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePeriodAtCompletionNonLaborUnits--">getCumulativePeriodAtCompletionNonLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativePeriodAtCompletionNonLaborUnits property.</div>
</td>
</tr>
<tr id="i63" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePeriodAtCompletionTotalCost--">getCumulativePeriodAtCompletionTotalCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePeriodAtCompletionTotalCost property.</div>
</td>
</tr>
<tr id="i64" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePeriodEarnedValueCost--">getCumulativePeriodEarnedValueCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePeriodEarnedValueCost property.</div>
</td>
</tr>
<tr id="i65" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePeriodEarnedValueLaborUnits--">getCumulativePeriodEarnedValueLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativePeriodEarnedValueLaborUnits property.</div>
</td>
</tr>
<tr id="i66" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePeriodEstimateAtCompletionCost--">getCumulativePeriodEstimateAtCompletionCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePeriodEstimateAtCompletionCost property.</div>
</td>
</tr>
<tr id="i67" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePeriodEstimateAtCompletionLaborUnits--">getCumulativePeriodEstimateAtCompletionLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativePeriodEstimateAtCompletionLaborUnits property.</div>
</td>
</tr>
<tr id="i68" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePeriodPlannedValueCost--">getCumulativePeriodPlannedValueCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePeriodPlannedValueCost property.</div>
</td>
</tr>
<tr id="i69" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePeriodPlannedValueLaborUnits--">getCumulativePeriodPlannedValueLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativePeriodPlannedValueLaborUnits property.</div>
</td>
</tr>
<tr id="i70" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePlannedExpenseCost--">getCumulativePlannedExpenseCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePlannedExpenseCost property.</div>
</td>
</tr>
<tr id="i71" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePlannedLaborCost--">getCumulativePlannedLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePlannedLaborCost property.</div>
</td>
</tr>
<tr id="i72" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePlannedLaborUnits--">getCumulativePlannedLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativePlannedLaborUnits property.</div>
</td>
</tr>
<tr id="i73" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePlannedMaterialCost--">getCumulativePlannedMaterialCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePlannedMaterialCost property.</div>
</td>
</tr>
<tr id="i74" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePlannedNonlaborCost--">getCumulativePlannedNonlaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePlannedNonlaborCost property.</div>
</td>
</tr>
<tr id="i75" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePlannedNonlaborUnits--">getCumulativePlannedNonlaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativePlannedNonlaborUnits property.</div>
</td>
</tr>
<tr id="i76" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePlannedTotalCost--">getCumulativePlannedTotalCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePlannedTotalCost property.</div>
</td>
</tr>
<tr id="i77" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePlannedValueCost--">getCumulativePlannedValueCost</a></span>()</code>
<div class="block">Gets the value of the cumulativePlannedValueCost property.</div>
</td>
</tr>
<tr id="i78" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativePlannedValueLaborUnits--">getCumulativePlannedValueLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativePlannedValueLaborUnits property.</div>
</td>
</tr>
<tr id="i79" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeRemainingExpenseCost--">getCumulativeRemainingExpenseCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingExpenseCost property.</div>
</td>
</tr>
<tr id="i80" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeRemainingLaborCost--">getCumulativeRemainingLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingLaborCost property.</div>
</td>
</tr>
<tr id="i81" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeRemainingLaborUnits--">getCumulativeRemainingLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingLaborUnits property.</div>
</td>
</tr>
<tr id="i82" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeRemainingLateExpenseCost--">getCumulativeRemainingLateExpenseCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingLateExpenseCost property.</div>
</td>
</tr>
<tr id="i83" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeRemainingLateLaborCost--">getCumulativeRemainingLateLaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingLateLaborCost property.</div>
</td>
</tr>
<tr id="i84" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeRemainingLateLaborUnits--">getCumulativeRemainingLateLaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingLateLaborUnits property.</div>
</td>
</tr>
<tr id="i85" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeRemainingLateMaterialCost--">getCumulativeRemainingLateMaterialCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingLateMaterialCost property.</div>
</td>
</tr>
<tr id="i86" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeRemainingLateNonlaborCost--">getCumulativeRemainingLateNonlaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingLateNonlaborCost property.</div>
</td>
</tr>
<tr id="i87" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeRemainingLateNonlaborUnits--">getCumulativeRemainingLateNonlaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingLateNonlaborUnits property.</div>
</td>
</tr>
<tr id="i88" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeRemainingLateTotalCost--">getCumulativeRemainingLateTotalCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingLateTotalCost property.</div>
</td>
</tr>
<tr id="i89" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeRemainingMaterialCost--">getCumulativeRemainingMaterialCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingMaterialCost property.</div>
</td>
</tr>
<tr id="i90" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeRemainingNonlaborCost--">getCumulativeRemainingNonlaborCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingNonlaborCost property.</div>
</td>
</tr>
<tr id="i91" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeRemainingNonlaborUnits--">getCumulativeRemainingNonlaborUnits</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingNonlaborUnits property.</div>
</td>
</tr>
<tr id="i92" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getCumulativeRemainingTotalCost--">getCumulativeRemainingTotalCost</a></span>()</code>
<div class="block">Gets the value of the cumulativeRemainingTotalCost property.</div>
</td>
</tr>
<tr id="i93" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getEarnedValueCost--">getEarnedValueCost</a></span>()</code>
<div class="block">Gets the value of the earnedValueCost property.</div>
</td>
</tr>
<tr id="i94" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getEarnedValueLaborUnits--">getEarnedValueLaborUnits</a></span>()</code>
<div class="block">Gets the value of the earnedValueLaborUnits property.</div>
</td>
</tr>
<tr id="i95" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getEndDate--">getEndDate</a></span>()</code>
<div class="block">Gets the value of the endDate property.</div>
</td>
</tr>
<tr id="i96" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getEstimateAtCompletionCost--">getEstimateAtCompletionCost</a></span>()</code>
<div class="block">Gets the value of the estimateAtCompletionCost property.</div>
</td>
</tr>
<tr id="i97" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getEstimateAtCompletionLaborUnits--">getEstimateAtCompletionLaborUnits</a></span>()</code>
<div class="block">Gets the value of the estimateAtCompletionLaborUnits property.</div>
</td>
</tr>
<tr id="i98" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getEstimateToCompleteCost--">getEstimateToCompleteCost</a></span>()</code>
<div class="block">Gets the value of the estimateToCompleteCost property.</div>
</td>
</tr>
<tr id="i99" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getEstimateToCompleteLaborUnits--">getEstimateToCompleteLaborUnits</a></span>()</code>
<div class="block">Gets the value of the estimateToCompleteLaborUnits property.</div>
</td>
</tr>
<tr id="i100" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPeriodActualCost--">getPeriodActualCost</a></span>()</code>
<div class="block">Gets the value of the periodActualCost property.</div>
</td>
</tr>
<tr id="i101" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPeriodActualExpenseCost--">getPeriodActualExpenseCost</a></span>()</code>
<div class="block">Gets the value of the periodActualExpenseCost property.</div>
</td>
</tr>
<tr id="i102" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPeriodActualLaborCost--">getPeriodActualLaborCost</a></span>()</code>
<div class="block">Gets the value of the periodActualLaborCost property.</div>
</td>
</tr>
<tr id="i103" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPeriodActualLaborUnits--">getPeriodActualLaborUnits</a></span>()</code>
<div class="block">Gets the value of the periodActualLaborUnits property.</div>
</td>
</tr>
<tr id="i104" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPeriodActualMaterialCost--">getPeriodActualMaterialCost</a></span>()</code>
<div class="block">Gets the value of the periodActualMaterialCost property.</div>
</td>
</tr>
<tr id="i105" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPeriodActualNonLaborCost--">getPeriodActualNonLaborCost</a></span>()</code>
<div class="block">Gets the value of the periodActualNonLaborCost property.</div>
</td>
</tr>
<tr id="i106" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPeriodActualNonLaborUnits--">getPeriodActualNonLaborUnits</a></span>()</code>
<div class="block">Gets the value of the periodActualNonLaborUnits property.</div>
</td>
</tr>
<tr id="i107" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPeriodAtCompletionExpenseCost--">getPeriodAtCompletionExpenseCost</a></span>()</code>
<div class="block">Gets the value of the periodAtCompletionExpenseCost property.</div>
</td>
</tr>
<tr id="i108" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPeriodAtCompletionLaborCost--">getPeriodAtCompletionLaborCost</a></span>()</code>
<div class="block">Gets the value of the periodAtCompletionLaborCost property.</div>
</td>
</tr>
<tr id="i109" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPeriodAtCompletionLaborUnits--">getPeriodAtCompletionLaborUnits</a></span>()</code>
<div class="block">Gets the value of the periodAtCompletionLaborUnits property.</div>
</td>
</tr>
<tr id="i110" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPeriodAtCompletionMaterialCost--">getPeriodAtCompletionMaterialCost</a></span>()</code>
<div class="block">Gets the value of the periodAtCompletionMaterialCost property.</div>
</td>
</tr>
<tr id="i111" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPeriodAtCompletionNonLaborCost--">getPeriodAtCompletionNonLaborCost</a></span>()</code>
<div class="block">Gets the value of the periodAtCompletionNonLaborCost property.</div>
</td>
</tr>
<tr id="i112" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPeriodAtCompletionNonLaborUnits--">getPeriodAtCompletionNonLaborUnits</a></span>()</code>
<div class="block">Gets the value of the periodAtCompletionNonLaborUnits property.</div>
</td>
</tr>
<tr id="i113" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPeriodAtCompletionTotalCost--">getPeriodAtCompletionTotalCost</a></span>()</code>
<div class="block">Gets the value of the periodAtCompletionTotalCost property.</div>
</td>
</tr>
<tr id="i114" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPeriodEarnedValueCost--">getPeriodEarnedValueCost</a></span>()</code>
<div class="block">Gets the value of the periodEarnedValueCost property.</div>
</td>
</tr>
<tr id="i115" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPeriodEarnedValueLaborUnits--">getPeriodEarnedValueLaborUnits</a></span>()</code>
<div class="block">Gets the value of the periodEarnedValueLaborUnits property.</div>
</td>
</tr>
<tr id="i116" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPeriodEstimateAtCompletionCost--">getPeriodEstimateAtCompletionCost</a></span>()</code>
<div class="block">Gets the value of the periodEstimateAtCompletionCost property.</div>
</td>
</tr>
<tr id="i117" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPeriodEstimateAtCompletionLaborUnits--">getPeriodEstimateAtCompletionLaborUnits</a></span>()</code>
<div class="block">Gets the value of the periodEstimateAtCompletionLaborUnits property.</div>
</td>
</tr>
<tr id="i118" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPeriodPlannedValueCost--">getPeriodPlannedValueCost</a></span>()</code>
<div class="block">Gets the value of the periodPlannedValueCost property.</div>
</td>
</tr>
<tr id="i119" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPeriodPlannedValueLaborUnits--">getPeriodPlannedValueLaborUnits</a></span>()</code>
<div class="block">Gets the value of the periodPlannedValueLaborUnits property.</div>
</td>
</tr>
<tr id="i120" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPlannedExpenseCost--">getPlannedExpenseCost</a></span>()</code>
<div class="block">Gets the value of the plannedExpenseCost property.</div>
</td>
</tr>
<tr id="i121" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPlannedLaborCost--">getPlannedLaborCost</a></span>()</code>
<div class="block">Gets the value of the plannedLaborCost property.</div>
</td>
</tr>
<tr id="i122" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPlannedLaborUnits--">getPlannedLaborUnits</a></span>()</code>
<div class="block">Gets the value of the plannedLaborUnits property.</div>
</td>
</tr>
<tr id="i123" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPlannedMaterialCost--">getPlannedMaterialCost</a></span>()</code>
<div class="block">Gets the value of the plannedMaterialCost property.</div>
</td>
</tr>
<tr id="i124" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPlannedNonlaborCost--">getPlannedNonlaborCost</a></span>()</code>
<div class="block">Gets the value of the plannedNonlaborCost property.</div>
</td>
</tr>
<tr id="i125" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPlannedNonlaborUnits--">getPlannedNonlaborUnits</a></span>()</code>
<div class="block">Gets the value of the plannedNonlaborUnits property.</div>
</td>
</tr>
<tr id="i126" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPlannedTotalCost--">getPlannedTotalCost</a></span>()</code>
<div class="block">Gets the value of the plannedTotalCost property.</div>
</td>
</tr>
<tr id="i127" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPlannedValueCost--">getPlannedValueCost</a></span>()</code>
<div class="block">Gets the value of the plannedValueCost property.</div>
</td>
</tr>
<tr id="i128" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getPlannedValueLaborUnits--">getPlannedValueLaborUnits</a></span>()</code>
<div class="block">Gets the value of the plannedValueLaborUnits property.</div>
</td>
</tr>
<tr id="i129" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getRemainingExpenseCost--">getRemainingExpenseCost</a></span>()</code>
<div class="block">Gets the value of the remainingExpenseCost property.</div>
</td>
</tr>
<tr id="i130" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getRemainingLaborCost--">getRemainingLaborCost</a></span>()</code>
<div class="block">Gets the value of the remainingLaborCost property.</div>
</td>
</tr>
<tr id="i131" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getRemainingLaborUnits--">getRemainingLaborUnits</a></span>()</code>
<div class="block">Gets the value of the remainingLaborUnits property.</div>
</td>
</tr>
<tr id="i132" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getRemainingLateExpenseCost--">getRemainingLateExpenseCost</a></span>()</code>
<div class="block">Gets the value of the remainingLateExpenseCost property.</div>
</td>
</tr>
<tr id="i133" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getRemainingLateLaborCost--">getRemainingLateLaborCost</a></span>()</code>
<div class="block">Gets the value of the remainingLateLaborCost property.</div>
</td>
</tr>
<tr id="i134" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getRemainingLateLaborUnits--">getRemainingLateLaborUnits</a></span>()</code>
<div class="block">Gets the value of the remainingLateLaborUnits property.</div>
</td>
</tr>
<tr id="i135" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getRemainingLateMaterialCost--">getRemainingLateMaterialCost</a></span>()</code>
<div class="block">Gets the value of the remainingLateMaterialCost property.</div>
</td>
</tr>
<tr id="i136" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getRemainingLateNonlaborCost--">getRemainingLateNonlaborCost</a></span>()</code>
<div class="block">Gets the value of the remainingLateNonlaborCost property.</div>
</td>
</tr>
<tr id="i137" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getRemainingLateNonlaborUnits--">getRemainingLateNonlaborUnits</a></span>()</code>
<div class="block">Gets the value of the remainingLateNonlaborUnits property.</div>
</td>
</tr>
<tr id="i138" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getRemainingLateTotalCost--">getRemainingLateTotalCost</a></span>()</code>
<div class="block">Gets the value of the remainingLateTotalCost property.</div>
</td>
</tr>
<tr id="i139" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getRemainingMaterialCost--">getRemainingMaterialCost</a></span>()</code>
<div class="block">Gets the value of the remainingMaterialCost property.</div>
</td>
</tr>
<tr id="i140" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getRemainingNonlaborCost--">getRemainingNonlaborCost</a></span>()</code>
<div class="block">Gets the value of the remainingNonlaborCost property.</div>
</td>
</tr>
<tr id="i141" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getRemainingNonlaborUnits--">getRemainingNonlaborUnits</a></span>()</code>
<div class="block">Gets the value of the remainingNonlaborUnits property.</div>
</td>
</tr>
<tr id="i142" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getRemainingTotalCost--">getRemainingTotalCost</a></span>()</code>
<div class="block">Gets the value of the remainingTotalCost property.</div>
</td>
</tr>
<tr id="i143" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#getStartDate--">getStartDate</a></span>()</code>
<div class="block">Gets the value of the startDate property.</div>
</td>
</tr>
<tr id="i144" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setActualCost-java.lang.Double-">setActualCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualCost property.</div>
</td>
</tr>
<tr id="i145" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setActualExpenseCost-java.lang.Double-">setActualExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualExpenseCost property.</div>
</td>
</tr>
<tr id="i146" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setActualLaborCost-java.lang.Double-">setActualLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualLaborCost property.</div>
</td>
</tr>
<tr id="i147" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setActualLaborUnits-java.lang.Double-">setActualLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualLaborUnits property.</div>
</td>
</tr>
<tr id="i148" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setActualMaterialCost-java.lang.Double-">setActualMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualMaterialCost property.</div>
</td>
</tr>
<tr id="i149" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setActualNonlaborCost-java.lang.Double-">setActualNonlaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualNonlaborCost property.</div>
</td>
</tr>
<tr id="i150" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setActualNonlaborUnits-java.lang.Double-">setActualNonlaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualNonlaborUnits property.</div>
</td>
</tr>
<tr id="i151" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setActualTotalCost-java.lang.Double-">setActualTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the actualTotalCost property.</div>
</td>
</tr>
<tr id="i152" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setAtCompletionExpenseCost-java.lang.Double-">setAtCompletionExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the atCompletionExpenseCost property.</div>
</td>
</tr>
<tr id="i153" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setAtCompletionLaborCost-java.lang.Double-">setAtCompletionLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the atCompletionLaborCost property.</div>
</td>
</tr>
<tr id="i154" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setAtCompletionLaborUnits-java.lang.Double-">setAtCompletionLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the atCompletionLaborUnits property.</div>
</td>
</tr>
<tr id="i155" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setAtCompletionMaterialCost-java.lang.Double-">setAtCompletionMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the atCompletionMaterialCost property.</div>
</td>
</tr>
<tr id="i156" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setAtCompletionNonlaborCost-java.lang.Double-">setAtCompletionNonlaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the atCompletionNonlaborCost property.</div>
</td>
</tr>
<tr id="i157" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setAtCompletionNonlaborUnits-java.lang.Double-">setAtCompletionNonlaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the atCompletionNonlaborUnits property.</div>
</td>
</tr>
<tr id="i158" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setAtCompletionTotalCost-java.lang.Double-">setAtCompletionTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the atCompletionTotalCost property.</div>
</td>
</tr>
<tr id="i159" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setBaselinePlannedExpenseCost-java.lang.Double-">setBaselinePlannedExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baselinePlannedExpenseCost property.</div>
</td>
</tr>
<tr id="i160" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setBaselinePlannedLaborCost-java.lang.Double-">setBaselinePlannedLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baselinePlannedLaborCost property.</div>
</td>
</tr>
<tr id="i161" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setBaselinePlannedLaborUnits-java.lang.Double-">setBaselinePlannedLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baselinePlannedLaborUnits property.</div>
</td>
</tr>
<tr id="i162" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setBaselinePlannedMaterialCost-java.lang.Double-">setBaselinePlannedMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baselinePlannedMaterialCost property.</div>
</td>
</tr>
<tr id="i163" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setBaselinePlannedNonlaborCost-java.lang.Double-">setBaselinePlannedNonlaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baselinePlannedNonlaborCost property.</div>
</td>
</tr>
<tr id="i164" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setBaselinePlannedNonlaborUnits-java.lang.Double-">setBaselinePlannedNonlaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baselinePlannedNonlaborUnits property.</div>
</td>
</tr>
<tr id="i165" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setBaselinePlannedTotalCost-java.lang.Double-">setBaselinePlannedTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the baselinePlannedTotalCost property.</div>
</td>
</tr>
<tr id="i166" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeActualCost-java.lang.Double-">setCumulativeActualCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeActualCost property.</div>
</td>
</tr>
<tr id="i167" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeActualExpenseCost-java.lang.Double-">setCumulativeActualExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeActualExpenseCost property.</div>
</td>
</tr>
<tr id="i168" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeActualLaborCost-java.lang.Double-">setCumulativeActualLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeActualLaborCost property.</div>
</td>
</tr>
<tr id="i169" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeActualLaborUnits-java.lang.Double-">setCumulativeActualLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeActualLaborUnits property.</div>
</td>
</tr>
<tr id="i170" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeActualMaterialCost-java.lang.Double-">setCumulativeActualMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeActualMaterialCost property.</div>
</td>
</tr>
<tr id="i171" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeActualNonlaborCost-java.lang.Double-">setCumulativeActualNonlaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeActualNonlaborCost property.</div>
</td>
</tr>
<tr id="i172" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeActualNonlaborUnits-java.lang.Double-">setCumulativeActualNonlaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeActualNonlaborUnits property.</div>
</td>
</tr>
<tr id="i173" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeActualTotalCost-java.lang.Double-">setCumulativeActualTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeActualTotalCost property.</div>
</td>
</tr>
<tr id="i174" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeAtCompletionExpenseCost-java.lang.Double-">setCumulativeAtCompletionExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeAtCompletionExpenseCost property.</div>
</td>
</tr>
<tr id="i175" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeAtCompletionLaborCost-java.lang.Double-">setCumulativeAtCompletionLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeAtCompletionLaborCost property.</div>
</td>
</tr>
<tr id="i176" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeAtCompletionLaborUnits-java.lang.Double-">setCumulativeAtCompletionLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeAtCompletionLaborUnits property.</div>
</td>
</tr>
<tr id="i177" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeAtCompletionMaterialCost-java.lang.Double-">setCumulativeAtCompletionMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeAtCompletionMaterialCost property.</div>
</td>
</tr>
<tr id="i178" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeAtCompletionNonlaborCost-java.lang.Double-">setCumulativeAtCompletionNonlaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeAtCompletionNonlaborCost property.</div>
</td>
</tr>
<tr id="i179" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeAtCompletionNonlaborUnits-java.lang.Double-">setCumulativeAtCompletionNonlaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeAtCompletionNonlaborUnits property.</div>
</td>
</tr>
<tr id="i180" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeAtCompletionTotalCost-java.lang.Double-">setCumulativeAtCompletionTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeAtCompletionTotalCost property.</div>
</td>
</tr>
<tr id="i181" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeBaselinePlannedExpenseCost-java.lang.Double-">setCumulativeBaselinePlannedExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaselinePlannedExpenseCost property.</div>
</td>
</tr>
<tr id="i182" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeBaselinePlannedLaborCost-java.lang.Double-">setCumulativeBaselinePlannedLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaselinePlannedLaborCost property.</div>
</td>
</tr>
<tr id="i183" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeBaselinePlannedLaborUnits-java.lang.Double-">setCumulativeBaselinePlannedLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaselinePlannedLaborUnits property.</div>
</td>
</tr>
<tr id="i184" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeBaselinePlannedMaterialCost-java.lang.Double-">setCumulativeBaselinePlannedMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaselinePlannedMaterialCost property.</div>
</td>
</tr>
<tr id="i185" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeBaselinePlannedNonlaborCost-java.lang.Double-">setCumulativeBaselinePlannedNonlaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaselinePlannedNonlaborCost property.</div>
</td>
</tr>
<tr id="i186" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeBaselinePlannedNonlaborUnits-java.lang.Double-">setCumulativeBaselinePlannedNonlaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaselinePlannedNonlaborUnits property.</div>
</td>
</tr>
<tr id="i187" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeBaselinePlannedTotalCost-java.lang.Double-">setCumulativeBaselinePlannedTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeBaselinePlannedTotalCost property.</div>
</td>
</tr>
<tr id="i188" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeEarnedValueCost-java.lang.Double-">setCumulativeEarnedValueCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeEarnedValueCost property.</div>
</td>
</tr>
<tr id="i189" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeEarnedValueLaborUnits-java.lang.Double-">setCumulativeEarnedValueLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeEarnedValueLaborUnits property.</div>
</td>
</tr>
<tr id="i190" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeEstimateAtCompletionCost-java.lang.Double-">setCumulativeEstimateAtCompletionCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeEstimateAtCompletionCost property.</div>
</td>
</tr>
<tr id="i191" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeEstimateAtCompletionLaborUnits-java.lang.Double-">setCumulativeEstimateAtCompletionLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeEstimateAtCompletionLaborUnits property.</div>
</td>
</tr>
<tr id="i192" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeEstimateToCompleteCost-java.lang.Double-">setCumulativeEstimateToCompleteCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeEstimateToCompleteCost property.</div>
</td>
</tr>
<tr id="i193" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeEstimateToCompleteLaborUnits-java.lang.Double-">setCumulativeEstimateToCompleteLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeEstimateToCompleteLaborUnits property.</div>
</td>
</tr>
<tr id="i194" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePeriodActualCost-java.lang.Double-">setCumulativePeriodActualCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePeriodActualCost property.</div>
</td>
</tr>
<tr id="i195" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePeriodActualExpenseCost-java.lang.Double-">setCumulativePeriodActualExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePeriodActualExpenseCost property.</div>
</td>
</tr>
<tr id="i196" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePeriodActualLaborCost-java.lang.Double-">setCumulativePeriodActualLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePeriodActualLaborCost property.</div>
</td>
</tr>
<tr id="i197" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePeriodActualLaborUnits-java.lang.Double-">setCumulativePeriodActualLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePeriodActualLaborUnits property.</div>
</td>
</tr>
<tr id="i198" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePeriodActualMaterialCost-java.lang.Double-">setCumulativePeriodActualMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePeriodActualMaterialCost property.</div>
</td>
</tr>
<tr id="i199" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePeriodActualNonLaborCost-java.lang.Double-">setCumulativePeriodActualNonLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePeriodActualNonLaborCost property.</div>
</td>
</tr>
<tr id="i200" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePeriodActualNonLaborUnits-java.lang.Double-">setCumulativePeriodActualNonLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePeriodActualNonLaborUnits property.</div>
</td>
</tr>
<tr id="i201" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePeriodAtCompletionExpenseCost-java.lang.Double-">setCumulativePeriodAtCompletionExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePeriodAtCompletionExpenseCost property.</div>
</td>
</tr>
<tr id="i202" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePeriodAtCompletionLaborCost-java.lang.Double-">setCumulativePeriodAtCompletionLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePeriodAtCompletionLaborCost property.</div>
</td>
</tr>
<tr id="i203" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePeriodAtCompletionLaborUnits-java.lang.Double-">setCumulativePeriodAtCompletionLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePeriodAtCompletionLaborUnits property.</div>
</td>
</tr>
<tr id="i204" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePeriodAtCompletionMaterialCost-java.lang.Double-">setCumulativePeriodAtCompletionMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePeriodAtCompletionMaterialCost property.</div>
</td>
</tr>
<tr id="i205" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePeriodAtCompletionNonLaborCost-java.lang.Double-">setCumulativePeriodAtCompletionNonLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePeriodAtCompletionNonLaborCost property.</div>
</td>
</tr>
<tr id="i206" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePeriodAtCompletionNonLaborUnits-java.lang.Double-">setCumulativePeriodAtCompletionNonLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePeriodAtCompletionNonLaborUnits property.</div>
</td>
</tr>
<tr id="i207" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePeriodAtCompletionTotalCost-java.lang.Double-">setCumulativePeriodAtCompletionTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePeriodAtCompletionTotalCost property.</div>
</td>
</tr>
<tr id="i208" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePeriodEarnedValueCost-java.lang.Double-">setCumulativePeriodEarnedValueCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePeriodEarnedValueCost property.</div>
</td>
</tr>
<tr id="i209" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePeriodEarnedValueLaborUnits-java.lang.Double-">setCumulativePeriodEarnedValueLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePeriodEarnedValueLaborUnits property.</div>
</td>
</tr>
<tr id="i210" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePeriodEstimateAtCompletionCost-java.lang.Double-">setCumulativePeriodEstimateAtCompletionCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePeriodEstimateAtCompletionCost property.</div>
</td>
</tr>
<tr id="i211" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePeriodEstimateAtCompletionLaborUnits-java.lang.Double-">setCumulativePeriodEstimateAtCompletionLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePeriodEstimateAtCompletionLaborUnits property.</div>
</td>
</tr>
<tr id="i212" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePeriodPlannedValueCost-java.lang.Double-">setCumulativePeriodPlannedValueCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePeriodPlannedValueCost property.</div>
</td>
</tr>
<tr id="i213" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePeriodPlannedValueLaborUnits-java.lang.Double-">setCumulativePeriodPlannedValueLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePeriodPlannedValueLaborUnits property.</div>
</td>
</tr>
<tr id="i214" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePlannedExpenseCost-java.lang.Double-">setCumulativePlannedExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePlannedExpenseCost property.</div>
</td>
</tr>
<tr id="i215" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePlannedLaborCost-java.lang.Double-">setCumulativePlannedLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePlannedLaborCost property.</div>
</td>
</tr>
<tr id="i216" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePlannedLaborUnits-java.lang.Double-">setCumulativePlannedLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePlannedLaborUnits property.</div>
</td>
</tr>
<tr id="i217" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePlannedMaterialCost-java.lang.Double-">setCumulativePlannedMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePlannedMaterialCost property.</div>
</td>
</tr>
<tr id="i218" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePlannedNonlaborCost-java.lang.Double-">setCumulativePlannedNonlaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePlannedNonlaborCost property.</div>
</td>
</tr>
<tr id="i219" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePlannedNonlaborUnits-java.lang.Double-">setCumulativePlannedNonlaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePlannedNonlaborUnits property.</div>
</td>
</tr>
<tr id="i220" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePlannedTotalCost-java.lang.Double-">setCumulativePlannedTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePlannedTotalCost property.</div>
</td>
</tr>
<tr id="i221" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePlannedValueCost-java.lang.Double-">setCumulativePlannedValueCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePlannedValueCost property.</div>
</td>
</tr>
<tr id="i222" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativePlannedValueLaborUnits-java.lang.Double-">setCumulativePlannedValueLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativePlannedValueLaborUnits property.</div>
</td>
</tr>
<tr id="i223" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeRemainingExpenseCost-java.lang.Double-">setCumulativeRemainingExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingExpenseCost property.</div>
</td>
</tr>
<tr id="i224" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeRemainingLaborCost-java.lang.Double-">setCumulativeRemainingLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingLaborCost property.</div>
</td>
</tr>
<tr id="i225" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeRemainingLaborUnits-java.lang.Double-">setCumulativeRemainingLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingLaborUnits property.</div>
</td>
</tr>
<tr id="i226" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeRemainingLateExpenseCost-java.lang.Double-">setCumulativeRemainingLateExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingLateExpenseCost property.</div>
</td>
</tr>
<tr id="i227" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeRemainingLateLaborCost-java.lang.Double-">setCumulativeRemainingLateLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingLateLaborCost property.</div>
</td>
</tr>
<tr id="i228" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeRemainingLateLaborUnits-java.lang.Double-">setCumulativeRemainingLateLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingLateLaborUnits property.</div>
</td>
</tr>
<tr id="i229" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeRemainingLateMaterialCost-java.lang.Double-">setCumulativeRemainingLateMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingLateMaterialCost property.</div>
</td>
</tr>
<tr id="i230" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeRemainingLateNonlaborCost-java.lang.Double-">setCumulativeRemainingLateNonlaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingLateNonlaborCost property.</div>
</td>
</tr>
<tr id="i231" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeRemainingLateNonlaborUnits-java.lang.Double-">setCumulativeRemainingLateNonlaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingLateNonlaborUnits property.</div>
</td>
</tr>
<tr id="i232" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeRemainingLateTotalCost-java.lang.Double-">setCumulativeRemainingLateTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingLateTotalCost property.</div>
</td>
</tr>
<tr id="i233" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeRemainingMaterialCost-java.lang.Double-">setCumulativeRemainingMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingMaterialCost property.</div>
</td>
</tr>
<tr id="i234" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeRemainingNonlaborCost-java.lang.Double-">setCumulativeRemainingNonlaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingNonlaborCost property.</div>
</td>
</tr>
<tr id="i235" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeRemainingNonlaborUnits-java.lang.Double-">setCumulativeRemainingNonlaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingNonlaborUnits property.</div>
</td>
</tr>
<tr id="i236" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setCumulativeRemainingTotalCost-java.lang.Double-">setCumulativeRemainingTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the cumulativeRemainingTotalCost property.</div>
</td>
</tr>
<tr id="i237" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setEarnedValueCost-java.lang.Double-">setEarnedValueCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the earnedValueCost property.</div>
</td>
</tr>
<tr id="i238" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setEarnedValueLaborUnits-java.lang.Double-">setEarnedValueLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the earnedValueLaborUnits property.</div>
</td>
</tr>
<tr id="i239" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setEndDate-java.time.LocalDateTime-">setEndDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the endDate property.</div>
</td>
</tr>
<tr id="i240" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setEstimateAtCompletionCost-java.lang.Double-">setEstimateAtCompletionCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the estimateAtCompletionCost property.</div>
</td>
</tr>
<tr id="i241" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setEstimateAtCompletionLaborUnits-java.lang.Double-">setEstimateAtCompletionLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the estimateAtCompletionLaborUnits property.</div>
</td>
</tr>
<tr id="i242" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setEstimateToCompleteCost-java.lang.Double-">setEstimateToCompleteCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the estimateToCompleteCost property.</div>
</td>
</tr>
<tr id="i243" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setEstimateToCompleteLaborUnits-java.lang.Double-">setEstimateToCompleteLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the estimateToCompleteLaborUnits property.</div>
</td>
</tr>
<tr id="i244" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPeriodActualCost-java.lang.Double-">setPeriodActualCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the periodActualCost property.</div>
</td>
</tr>
<tr id="i245" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPeriodActualExpenseCost-java.lang.Double-">setPeriodActualExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the periodActualExpenseCost property.</div>
</td>
</tr>
<tr id="i246" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPeriodActualLaborCost-java.lang.Double-">setPeriodActualLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the periodActualLaborCost property.</div>
</td>
</tr>
<tr id="i247" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPeriodActualLaborUnits-java.lang.Double-">setPeriodActualLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the periodActualLaborUnits property.</div>
</td>
</tr>
<tr id="i248" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPeriodActualMaterialCost-java.lang.Double-">setPeriodActualMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the periodActualMaterialCost property.</div>
</td>
</tr>
<tr id="i249" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPeriodActualNonLaborCost-java.lang.Double-">setPeriodActualNonLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the periodActualNonLaborCost property.</div>
</td>
</tr>
<tr id="i250" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPeriodActualNonLaborUnits-java.lang.Double-">setPeriodActualNonLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the periodActualNonLaborUnits property.</div>
</td>
</tr>
<tr id="i251" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPeriodAtCompletionExpenseCost-java.lang.Double-">setPeriodAtCompletionExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the periodAtCompletionExpenseCost property.</div>
</td>
</tr>
<tr id="i252" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPeriodAtCompletionLaborCost-java.lang.Double-">setPeriodAtCompletionLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the periodAtCompletionLaborCost property.</div>
</td>
</tr>
<tr id="i253" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPeriodAtCompletionLaborUnits-java.lang.Double-">setPeriodAtCompletionLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the periodAtCompletionLaborUnits property.</div>
</td>
</tr>
<tr id="i254" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPeriodAtCompletionMaterialCost-java.lang.Double-">setPeriodAtCompletionMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the periodAtCompletionMaterialCost property.</div>
</td>
</tr>
<tr id="i255" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPeriodAtCompletionNonLaborCost-java.lang.Double-">setPeriodAtCompletionNonLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the periodAtCompletionNonLaborCost property.</div>
</td>
</tr>
<tr id="i256" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPeriodAtCompletionNonLaborUnits-java.lang.Double-">setPeriodAtCompletionNonLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the periodAtCompletionNonLaborUnits property.</div>
</td>
</tr>
<tr id="i257" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPeriodAtCompletionTotalCost-java.lang.Double-">setPeriodAtCompletionTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the periodAtCompletionTotalCost property.</div>
</td>
</tr>
<tr id="i258" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPeriodEarnedValueCost-java.lang.Double-">setPeriodEarnedValueCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the periodEarnedValueCost property.</div>
</td>
</tr>
<tr id="i259" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPeriodEarnedValueLaborUnits-java.lang.Double-">setPeriodEarnedValueLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the periodEarnedValueLaborUnits property.</div>
</td>
</tr>
<tr id="i260" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPeriodEstimateAtCompletionCost-java.lang.Double-">setPeriodEstimateAtCompletionCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the periodEstimateAtCompletionCost property.</div>
</td>
</tr>
<tr id="i261" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPeriodEstimateAtCompletionLaborUnits-java.lang.Double-">setPeriodEstimateAtCompletionLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the periodEstimateAtCompletionLaborUnits property.</div>
</td>
</tr>
<tr id="i262" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPeriodPlannedValueCost-java.lang.Double-">setPeriodPlannedValueCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the periodPlannedValueCost property.</div>
</td>
</tr>
<tr id="i263" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPeriodPlannedValueLaborUnits-java.lang.Double-">setPeriodPlannedValueLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the periodPlannedValueLaborUnits property.</div>
</td>
</tr>
<tr id="i264" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPlannedExpenseCost-java.lang.Double-">setPlannedExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedExpenseCost property.</div>
</td>
</tr>
<tr id="i265" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPlannedLaborCost-java.lang.Double-">setPlannedLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedLaborCost property.</div>
</td>
</tr>
<tr id="i266" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPlannedLaborUnits-java.lang.Double-">setPlannedLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedLaborUnits property.</div>
</td>
</tr>
<tr id="i267" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPlannedMaterialCost-java.lang.Double-">setPlannedMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedMaterialCost property.</div>
</td>
</tr>
<tr id="i268" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPlannedNonlaborCost-java.lang.Double-">setPlannedNonlaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedNonlaborCost property.</div>
</td>
</tr>
<tr id="i269" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPlannedNonlaborUnits-java.lang.Double-">setPlannedNonlaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedNonlaborUnits property.</div>
</td>
</tr>
<tr id="i270" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPlannedTotalCost-java.lang.Double-">setPlannedTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedTotalCost property.</div>
</td>
</tr>
<tr id="i271" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPlannedValueCost-java.lang.Double-">setPlannedValueCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedValueCost property.</div>
</td>
</tr>
<tr id="i272" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setPlannedValueLaborUnits-java.lang.Double-">setPlannedValueLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the plannedValueLaborUnits property.</div>
</td>
</tr>
<tr id="i273" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setRemainingExpenseCost-java.lang.Double-">setRemainingExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingExpenseCost property.</div>
</td>
</tr>
<tr id="i274" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setRemainingLaborCost-java.lang.Double-">setRemainingLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingLaborCost property.</div>
</td>
</tr>
<tr id="i275" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setRemainingLaborUnits-java.lang.Double-">setRemainingLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingLaborUnits property.</div>
</td>
</tr>
<tr id="i276" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setRemainingLateExpenseCost-java.lang.Double-">setRemainingLateExpenseCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingLateExpenseCost property.</div>
</td>
</tr>
<tr id="i277" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setRemainingLateLaborCost-java.lang.Double-">setRemainingLateLaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingLateLaborCost property.</div>
</td>
</tr>
<tr id="i278" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setRemainingLateLaborUnits-java.lang.Double-">setRemainingLateLaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingLateLaborUnits property.</div>
</td>
</tr>
<tr id="i279" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setRemainingLateMaterialCost-java.lang.Double-">setRemainingLateMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingLateMaterialCost property.</div>
</td>
</tr>
<tr id="i280" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setRemainingLateNonlaborCost-java.lang.Double-">setRemainingLateNonlaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingLateNonlaborCost property.</div>
</td>
</tr>
<tr id="i281" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setRemainingLateNonlaborUnits-java.lang.Double-">setRemainingLateNonlaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingLateNonlaborUnits property.</div>
</td>
</tr>
<tr id="i282" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setRemainingLateTotalCost-java.lang.Double-">setRemainingLateTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingLateTotalCost property.</div>
</td>
</tr>
<tr id="i283" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setRemainingMaterialCost-java.lang.Double-">setRemainingMaterialCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingMaterialCost property.</div>
</td>
</tr>
<tr id="i284" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setRemainingNonlaborCost-java.lang.Double-">setRemainingNonlaborCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingNonlaborCost property.</div>
</td>
</tr>
<tr id="i285" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setRemainingNonlaborUnits-java.lang.Double-">setRemainingNonlaborUnits</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingNonlaborUnits property.</div>
</td>
</tr>
<tr id="i286" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setRemainingTotalCost-java.lang.Double-">setRemainingTotalCost</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</code>
<div class="block">Sets the value of the remainingTotalCost property.</div>
</td>
</tr>
<tr id="i287" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html#setStartDate-java.time.LocalDateTime-">setStartDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</code>
<div class="block">Sets the value of the startDate property.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="startDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>startDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> startDate</pre>
</li>
</ul>
<a name="endDate">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>endDate</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a> endDate</pre>
</li>
</ul>
<a name="actualLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualLaborUnits</pre>
</li>
</ul>
<a name="cumulativeActualLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeActualLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeActualLaborUnits</pre>
</li>
</ul>
<a name="actualNonlaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualNonlaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualNonlaborUnits</pre>
</li>
</ul>
<a name="cumulativeActualNonlaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeActualNonlaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeActualNonlaborUnits</pre>
</li>
</ul>
<a name="atCompletionLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>atCompletionLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> atCompletionLaborUnits</pre>
</li>
</ul>
<a name="cumulativeAtCompletionLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeAtCompletionLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeAtCompletionLaborUnits</pre>
</li>
</ul>
<a name="atCompletionNonlaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>atCompletionNonlaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> atCompletionNonlaborUnits</pre>
</li>
</ul>
<a name="cumulativeAtCompletionNonlaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeAtCompletionNonlaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeAtCompletionNonlaborUnits</pre>
</li>
</ul>
<a name="baselinePlannedLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baselinePlannedLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baselinePlannedLaborUnits</pre>
</li>
</ul>
<a name="cumulativeBaselinePlannedLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaselinePlannedLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaselinePlannedLaborUnits</pre>
</li>
</ul>
<a name="baselinePlannedNonlaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baselinePlannedNonlaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baselinePlannedNonlaborUnits</pre>
</li>
</ul>
<a name="cumulativeBaselinePlannedNonlaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaselinePlannedNonlaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaselinePlannedNonlaborUnits</pre>
</li>
</ul>
<a name="earnedValueLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>earnedValueLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> earnedValueLaborUnits</pre>
</li>
</ul>
<a name="cumulativeEarnedValueLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeEarnedValueLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeEarnedValueLaborUnits</pre>
</li>
</ul>
<a name="estimateAtCompletionLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>estimateAtCompletionLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> estimateAtCompletionLaborUnits</pre>
</li>
</ul>
<a name="cumulativeEstimateAtCompletionLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeEstimateAtCompletionLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeEstimateAtCompletionLaborUnits</pre>
</li>
</ul>
<a name="estimateToCompleteLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>estimateToCompleteLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> estimateToCompleteLaborUnits</pre>
</li>
</ul>
<a name="cumulativeEstimateToCompleteLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeEstimateToCompleteLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeEstimateToCompleteLaborUnits</pre>
</li>
</ul>
<a name="periodActualLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>periodActualLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> periodActualLaborUnits</pre>
</li>
</ul>
<a name="cumulativePeriodActualLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePeriodActualLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePeriodActualLaborUnits</pre>
</li>
</ul>
<a name="periodActualNonLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>periodActualNonLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> periodActualNonLaborUnits</pre>
</li>
</ul>
<a name="cumulativePeriodActualNonLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePeriodActualNonLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePeriodActualNonLaborUnits</pre>
</li>
</ul>
<a name="periodAtCompletionLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>periodAtCompletionLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> periodAtCompletionLaborUnits</pre>
</li>
</ul>
<a name="cumulativePeriodAtCompletionLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePeriodAtCompletionLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePeriodAtCompletionLaborUnits</pre>
</li>
</ul>
<a name="periodAtCompletionNonLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>periodAtCompletionNonLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> periodAtCompletionNonLaborUnits</pre>
</li>
</ul>
<a name="cumulativePeriodAtCompletionNonLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePeriodAtCompletionNonLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePeriodAtCompletionNonLaborUnits</pre>
</li>
</ul>
<a name="periodEarnedValueLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>periodEarnedValueLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> periodEarnedValueLaborUnits</pre>
</li>
</ul>
<a name="cumulativePeriodEarnedValueLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePeriodEarnedValueLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePeriodEarnedValueLaborUnits</pre>
</li>
</ul>
<a name="periodEstimateAtCompletionLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>periodEstimateAtCompletionLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> periodEstimateAtCompletionLaborUnits</pre>
</li>
</ul>
<a name="cumulativePeriodEstimateAtCompletionLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePeriodEstimateAtCompletionLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePeriodEstimateAtCompletionLaborUnits</pre>
</li>
</ul>
<a name="periodPlannedValueLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>periodPlannedValueLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> periodPlannedValueLaborUnits</pre>
</li>
</ul>
<a name="cumulativePeriodPlannedValueLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePeriodPlannedValueLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePeriodPlannedValueLaborUnits</pre>
</li>
</ul>
<a name="plannedLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> plannedLaborUnits</pre>
</li>
</ul>
<a name="cumulativePlannedLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePlannedLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePlannedLaborUnits</pre>
</li>
</ul>
<a name="plannedNonlaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedNonlaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> plannedNonlaborUnits</pre>
</li>
</ul>
<a name="cumulativePlannedNonlaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePlannedNonlaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePlannedNonlaborUnits</pre>
</li>
</ul>
<a name="plannedValueLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedValueLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> plannedValueLaborUnits</pre>
</li>
</ul>
<a name="cumulativePlannedValueLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePlannedValueLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePlannedValueLaborUnits</pre>
</li>
</ul>
<a name="remainingLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingLaborUnits</pre>
</li>
</ul>
<a name="cumulativeRemainingLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingLaborUnits</pre>
</li>
</ul>
<a name="remainingLateLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingLateLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingLateLaborUnits</pre>
</li>
</ul>
<a name="cumulativeRemainingLateLaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingLateLaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingLateLaborUnits</pre>
</li>
</ul>
<a name="remainingLateNonlaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingLateNonlaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingLateNonlaborUnits</pre>
</li>
</ul>
<a name="cumulativeRemainingLateNonlaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingLateNonlaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingLateNonlaborUnits</pre>
</li>
</ul>
<a name="remainingNonlaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingNonlaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingNonlaborUnits</pre>
</li>
</ul>
<a name="cumulativeRemainingNonlaborUnits">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingNonlaborUnits</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingNonlaborUnits</pre>
</li>
</ul>
<a name="actualCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualCost</pre>
</li>
</ul>
<a name="cumulativeActualCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeActualCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeActualCost</pre>
</li>
</ul>
<a name="actualExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualExpenseCost</pre>
</li>
</ul>
<a name="cumulativeActualExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeActualExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeActualExpenseCost</pre>
</li>
</ul>
<a name="actualLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualLaborCost</pre>
</li>
</ul>
<a name="cumulativeActualLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeActualLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeActualLaborCost</pre>
</li>
</ul>
<a name="actualMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualMaterialCost</pre>
</li>
</ul>
<a name="cumulativeActualMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeActualMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeActualMaterialCost</pre>
</li>
</ul>
<a name="actualNonlaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualNonlaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualNonlaborCost</pre>
</li>
</ul>
<a name="cumulativeActualNonlaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeActualNonlaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeActualNonlaborCost</pre>
</li>
</ul>
<a name="actualTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>actualTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> actualTotalCost</pre>
</li>
</ul>
<a name="cumulativeActualTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeActualTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeActualTotalCost</pre>
</li>
</ul>
<a name="atCompletionExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>atCompletionExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> atCompletionExpenseCost</pre>
</li>
</ul>
<a name="cumulativeAtCompletionExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeAtCompletionExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeAtCompletionExpenseCost</pre>
</li>
</ul>
<a name="atCompletionLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>atCompletionLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> atCompletionLaborCost</pre>
</li>
</ul>
<a name="cumulativeAtCompletionLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeAtCompletionLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeAtCompletionLaborCost</pre>
</li>
</ul>
<a name="atCompletionMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>atCompletionMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> atCompletionMaterialCost</pre>
</li>
</ul>
<a name="cumulativeAtCompletionMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeAtCompletionMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeAtCompletionMaterialCost</pre>
</li>
</ul>
<a name="atCompletionNonlaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>atCompletionNonlaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> atCompletionNonlaborCost</pre>
</li>
</ul>
<a name="cumulativeAtCompletionNonlaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeAtCompletionNonlaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeAtCompletionNonlaborCost</pre>
</li>
</ul>
<a name="atCompletionTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>atCompletionTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> atCompletionTotalCost</pre>
</li>
</ul>
<a name="cumulativeAtCompletionTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeAtCompletionTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeAtCompletionTotalCost</pre>
</li>
</ul>
<a name="baselinePlannedExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baselinePlannedExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baselinePlannedExpenseCost</pre>
</li>
</ul>
<a name="cumulativeBaselinePlannedExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaselinePlannedExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaselinePlannedExpenseCost</pre>
</li>
</ul>
<a name="baselinePlannedLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baselinePlannedLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baselinePlannedLaborCost</pre>
</li>
</ul>
<a name="cumulativeBaselinePlannedLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaselinePlannedLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaselinePlannedLaborCost</pre>
</li>
</ul>
<a name="baselinePlannedMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baselinePlannedMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baselinePlannedMaterialCost</pre>
</li>
</ul>
<a name="cumulativeBaselinePlannedMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaselinePlannedMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaselinePlannedMaterialCost</pre>
</li>
</ul>
<a name="baselinePlannedNonlaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baselinePlannedNonlaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baselinePlannedNonlaborCost</pre>
</li>
</ul>
<a name="cumulativeBaselinePlannedNonlaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaselinePlannedNonlaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaselinePlannedNonlaborCost</pre>
</li>
</ul>
<a name="baselinePlannedTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>baselinePlannedTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> baselinePlannedTotalCost</pre>
</li>
</ul>
<a name="cumulativeBaselinePlannedTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeBaselinePlannedTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeBaselinePlannedTotalCost</pre>
</li>
</ul>
<a name="earnedValueCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>earnedValueCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> earnedValueCost</pre>
</li>
</ul>
<a name="cumulativeEarnedValueCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeEarnedValueCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeEarnedValueCost</pre>
</li>
</ul>
<a name="estimateAtCompletionCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>estimateAtCompletionCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> estimateAtCompletionCost</pre>
</li>
</ul>
<a name="cumulativeEstimateAtCompletionCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeEstimateAtCompletionCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeEstimateAtCompletionCost</pre>
</li>
</ul>
<a name="estimateToCompleteCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>estimateToCompleteCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> estimateToCompleteCost</pre>
</li>
</ul>
<a name="cumulativeEstimateToCompleteCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeEstimateToCompleteCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeEstimateToCompleteCost</pre>
</li>
</ul>
<a name="periodActualCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>periodActualCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> periodActualCost</pre>
</li>
</ul>
<a name="cumulativePeriodActualCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePeriodActualCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePeriodActualCost</pre>
</li>
</ul>
<a name="periodActualExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>periodActualExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> periodActualExpenseCost</pre>
</li>
</ul>
<a name="cumulativePeriodActualExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePeriodActualExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePeriodActualExpenseCost</pre>
</li>
</ul>
<a name="periodActualLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>periodActualLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> periodActualLaborCost</pre>
</li>
</ul>
<a name="cumulativePeriodActualLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePeriodActualLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePeriodActualLaborCost</pre>
</li>
</ul>
<a name="periodActualMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>periodActualMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> periodActualMaterialCost</pre>
</li>
</ul>
<a name="cumulativePeriodActualMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePeriodActualMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePeriodActualMaterialCost</pre>
</li>
</ul>
<a name="periodActualNonLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>periodActualNonLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> periodActualNonLaborCost</pre>
</li>
</ul>
<a name="cumulativePeriodActualNonLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePeriodActualNonLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePeriodActualNonLaborCost</pre>
</li>
</ul>
<a name="periodAtCompletionExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>periodAtCompletionExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> periodAtCompletionExpenseCost</pre>
</li>
</ul>
<a name="cumulativePeriodAtCompletionExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePeriodAtCompletionExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePeriodAtCompletionExpenseCost</pre>
</li>
</ul>
<a name="periodAtCompletionLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>periodAtCompletionLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> periodAtCompletionLaborCost</pre>
</li>
</ul>
<a name="cumulativePeriodAtCompletionLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePeriodAtCompletionLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePeriodAtCompletionLaborCost</pre>
</li>
</ul>
<a name="periodAtCompletionMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>periodAtCompletionMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> periodAtCompletionMaterialCost</pre>
</li>
</ul>
<a name="cumulativePeriodAtCompletionMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePeriodAtCompletionMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePeriodAtCompletionMaterialCost</pre>
</li>
</ul>
<a name="periodAtCompletionNonLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>periodAtCompletionNonLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> periodAtCompletionNonLaborCost</pre>
</li>
</ul>
<a name="cumulativePeriodAtCompletionNonLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePeriodAtCompletionNonLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePeriodAtCompletionNonLaborCost</pre>
</li>
</ul>
<a name="periodAtCompletionTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>periodAtCompletionTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> periodAtCompletionTotalCost</pre>
</li>
</ul>
<a name="cumulativePeriodAtCompletionTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePeriodAtCompletionTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePeriodAtCompletionTotalCost</pre>
</li>
</ul>
<a name="periodEarnedValueCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>periodEarnedValueCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> periodEarnedValueCost</pre>
</li>
</ul>
<a name="cumulativePeriodEarnedValueCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePeriodEarnedValueCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePeriodEarnedValueCost</pre>
</li>
</ul>
<a name="periodEstimateAtCompletionCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>periodEstimateAtCompletionCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> periodEstimateAtCompletionCost</pre>
</li>
</ul>
<a name="cumulativePeriodEstimateAtCompletionCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePeriodEstimateAtCompletionCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePeriodEstimateAtCompletionCost</pre>
</li>
</ul>
<a name="periodPlannedValueCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>periodPlannedValueCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> periodPlannedValueCost</pre>
</li>
</ul>
<a name="cumulativePeriodPlannedValueCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePeriodPlannedValueCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePeriodPlannedValueCost</pre>
</li>
</ul>
<a name="plannedExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> plannedExpenseCost</pre>
</li>
</ul>
<a name="cumulativePlannedExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePlannedExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePlannedExpenseCost</pre>
</li>
</ul>
<a name="plannedLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> plannedLaborCost</pre>
</li>
</ul>
<a name="cumulativePlannedLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePlannedLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePlannedLaborCost</pre>
</li>
</ul>
<a name="plannedMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> plannedMaterialCost</pre>
</li>
</ul>
<a name="cumulativePlannedMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePlannedMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePlannedMaterialCost</pre>
</li>
</ul>
<a name="plannedNonlaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedNonlaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> plannedNonlaborCost</pre>
</li>
</ul>
<a name="cumulativePlannedNonlaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePlannedNonlaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePlannedNonlaborCost</pre>
</li>
</ul>
<a name="plannedTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> plannedTotalCost</pre>
</li>
</ul>
<a name="cumulativePlannedTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePlannedTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePlannedTotalCost</pre>
</li>
</ul>
<a name="plannedValueCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>plannedValueCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> plannedValueCost</pre>
</li>
</ul>
<a name="cumulativePlannedValueCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativePlannedValueCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativePlannedValueCost</pre>
</li>
</ul>
<a name="remainingExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingExpenseCost</pre>
</li>
</ul>
<a name="cumulativeRemainingExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingExpenseCost</pre>
</li>
</ul>
<a name="remainingLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingLaborCost</pre>
</li>
</ul>
<a name="cumulativeRemainingLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingLaborCost</pre>
</li>
</ul>
<a name="remainingLateExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingLateExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingLateExpenseCost</pre>
</li>
</ul>
<a name="cumulativeRemainingLateExpenseCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingLateExpenseCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingLateExpenseCost</pre>
</li>
</ul>
<a name="remainingLateLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingLateLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingLateLaborCost</pre>
</li>
</ul>
<a name="cumulativeRemainingLateLaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingLateLaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingLateLaborCost</pre>
</li>
</ul>
<a name="remainingLateMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingLateMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingLateMaterialCost</pre>
</li>
</ul>
<a name="cumulativeRemainingLateMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingLateMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingLateMaterialCost</pre>
</li>
</ul>
<a name="remainingLateNonlaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingLateNonlaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingLateNonlaborCost</pre>
</li>
</ul>
<a name="cumulativeRemainingLateNonlaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingLateNonlaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingLateNonlaborCost</pre>
</li>
</ul>
<a name="remainingLateTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingLateTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingLateTotalCost</pre>
</li>
</ul>
<a name="cumulativeRemainingLateTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingLateTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingLateTotalCost</pre>
</li>
</ul>
<a name="remainingMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingMaterialCost</pre>
</li>
</ul>
<a name="cumulativeRemainingMaterialCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingMaterialCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingMaterialCost</pre>
</li>
</ul>
<a name="remainingNonlaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingNonlaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingNonlaborCost</pre>
</li>
</ul>
<a name="cumulativeRemainingNonlaborCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>cumulativeRemainingNonlaborCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingNonlaborCost</pre>
</li>
</ul>
<a name="remainingTotalCost">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>remainingTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> remainingTotalCost</pre>
</li>
</ul>
<a name="cumulativeRemainingTotalCost">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>cumulativeRemainingTotalCost</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a> cumulativeRemainingTotalCost</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Period--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Period</h4>
<pre>public&nbsp;Period()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getStartDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getStartDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getStartDate()</pre>
<div class="block">Gets the value of the startDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setStartDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setStartDate</h4>
<pre>public&nbsp;void&nbsp;setStartDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the startDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getEndDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEndDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;getEndDate()</pre>
<div class="block">Gets the value of the endDate property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEndDate-java.time.LocalDateTime-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEndDate</h4>
<pre>public&nbsp;void&nbsp;setEndDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDateTime.html?is-external=true" title="class or interface in java.time">LocalDateTime</a>&nbsp;value)</pre>
<div class="block">Sets the value of the endDate property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualLaborUnits()</pre>
<div class="block">Gets the value of the actualLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setActualLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeActualLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeActualLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeActualLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeActualLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeActualLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeActualLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeActualLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeActualLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualNonlaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualNonlaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualNonlaborUnits()</pre>
<div class="block">Gets the value of the actualNonlaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualNonlaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualNonlaborUnits</h4>
<pre>public&nbsp;void&nbsp;setActualNonlaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualNonlaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeActualNonlaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeActualNonlaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeActualNonlaborUnits()</pre>
<div class="block">Gets the value of the cumulativeActualNonlaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeActualNonlaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeActualNonlaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeActualNonlaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeActualNonlaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAtCompletionLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAtCompletionLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getAtCompletionLaborUnits()</pre>
<div class="block">Gets the value of the atCompletionLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAtCompletionLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAtCompletionLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setAtCompletionLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the atCompletionLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeAtCompletionLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeAtCompletionLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeAtCompletionLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeAtCompletionLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeAtCompletionLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeAtCompletionLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeAtCompletionLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeAtCompletionLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAtCompletionNonlaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAtCompletionNonlaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getAtCompletionNonlaborUnits()</pre>
<div class="block">Gets the value of the atCompletionNonlaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAtCompletionNonlaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAtCompletionNonlaborUnits</h4>
<pre>public&nbsp;void&nbsp;setAtCompletionNonlaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the atCompletionNonlaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeAtCompletionNonlaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeAtCompletionNonlaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeAtCompletionNonlaborUnits()</pre>
<div class="block">Gets the value of the cumulativeAtCompletionNonlaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeAtCompletionNonlaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeAtCompletionNonlaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeAtCompletionNonlaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeAtCompletionNonlaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaselinePlannedLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselinePlannedLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaselinePlannedLaborUnits()</pre>
<div class="block">Gets the value of the baselinePlannedLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaselinePlannedLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselinePlannedLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setBaselinePlannedLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baselinePlannedLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaselinePlannedLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaselinePlannedLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaselinePlannedLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeBaselinePlannedLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaselinePlannedLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaselinePlannedLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaselinePlannedLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaselinePlannedLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaselinePlannedNonlaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselinePlannedNonlaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaselinePlannedNonlaborUnits()</pre>
<div class="block">Gets the value of the baselinePlannedNonlaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaselinePlannedNonlaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselinePlannedNonlaborUnits</h4>
<pre>public&nbsp;void&nbsp;setBaselinePlannedNonlaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baselinePlannedNonlaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaselinePlannedNonlaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaselinePlannedNonlaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaselinePlannedNonlaborUnits()</pre>
<div class="block">Gets the value of the cumulativeBaselinePlannedNonlaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaselinePlannedNonlaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaselinePlannedNonlaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaselinePlannedNonlaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaselinePlannedNonlaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getEarnedValueLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEarnedValueLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getEarnedValueLaborUnits()</pre>
<div class="block">Gets the value of the earnedValueLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEarnedValueLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEarnedValueLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setEarnedValueLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the earnedValueLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeEarnedValueLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeEarnedValueLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeEarnedValueLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeEarnedValueLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeEarnedValueLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeEarnedValueLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeEarnedValueLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeEarnedValueLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getEstimateAtCompletionLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEstimateAtCompletionLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getEstimateAtCompletionLaborUnits()</pre>
<div class="block">Gets the value of the estimateAtCompletionLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEstimateAtCompletionLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEstimateAtCompletionLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setEstimateAtCompletionLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the estimateAtCompletionLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeEstimateAtCompletionLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeEstimateAtCompletionLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeEstimateAtCompletionLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeEstimateAtCompletionLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeEstimateAtCompletionLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeEstimateAtCompletionLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeEstimateAtCompletionLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeEstimateAtCompletionLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getEstimateToCompleteLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEstimateToCompleteLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getEstimateToCompleteLaborUnits()</pre>
<div class="block">Gets the value of the estimateToCompleteLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEstimateToCompleteLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEstimateToCompleteLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setEstimateToCompleteLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the estimateToCompleteLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeEstimateToCompleteLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeEstimateToCompleteLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeEstimateToCompleteLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeEstimateToCompleteLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeEstimateToCompleteLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeEstimateToCompleteLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeEstimateToCompleteLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeEstimateToCompleteLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeriodActualLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriodActualLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPeriodActualLaborUnits()</pre>
<div class="block">Gets the value of the periodActualLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPeriodActualLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriodActualLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setPeriodActualLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the periodActualLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePeriodActualLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePeriodActualLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePeriodActualLaborUnits()</pre>
<div class="block">Gets the value of the cumulativePeriodActualLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePeriodActualLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePeriodActualLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativePeriodActualLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePeriodActualLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeriodActualNonLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriodActualNonLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPeriodActualNonLaborUnits()</pre>
<div class="block">Gets the value of the periodActualNonLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPeriodActualNonLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriodActualNonLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setPeriodActualNonLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the periodActualNonLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePeriodActualNonLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePeriodActualNonLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePeriodActualNonLaborUnits()</pre>
<div class="block">Gets the value of the cumulativePeriodActualNonLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePeriodActualNonLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePeriodActualNonLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativePeriodActualNonLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePeriodActualNonLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeriodAtCompletionLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriodAtCompletionLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPeriodAtCompletionLaborUnits()</pre>
<div class="block">Gets the value of the periodAtCompletionLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPeriodAtCompletionLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriodAtCompletionLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setPeriodAtCompletionLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the periodAtCompletionLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePeriodAtCompletionLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePeriodAtCompletionLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePeriodAtCompletionLaborUnits()</pre>
<div class="block">Gets the value of the cumulativePeriodAtCompletionLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePeriodAtCompletionLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePeriodAtCompletionLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativePeriodAtCompletionLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePeriodAtCompletionLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeriodAtCompletionNonLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriodAtCompletionNonLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPeriodAtCompletionNonLaborUnits()</pre>
<div class="block">Gets the value of the periodAtCompletionNonLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPeriodAtCompletionNonLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriodAtCompletionNonLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setPeriodAtCompletionNonLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the periodAtCompletionNonLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePeriodAtCompletionNonLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePeriodAtCompletionNonLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePeriodAtCompletionNonLaborUnits()</pre>
<div class="block">Gets the value of the cumulativePeriodAtCompletionNonLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePeriodAtCompletionNonLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePeriodAtCompletionNonLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativePeriodAtCompletionNonLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePeriodAtCompletionNonLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeriodEarnedValueLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriodEarnedValueLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPeriodEarnedValueLaborUnits()</pre>
<div class="block">Gets the value of the periodEarnedValueLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPeriodEarnedValueLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriodEarnedValueLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setPeriodEarnedValueLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the periodEarnedValueLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePeriodEarnedValueLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePeriodEarnedValueLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePeriodEarnedValueLaborUnits()</pre>
<div class="block">Gets the value of the cumulativePeriodEarnedValueLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePeriodEarnedValueLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePeriodEarnedValueLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativePeriodEarnedValueLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePeriodEarnedValueLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeriodEstimateAtCompletionLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriodEstimateAtCompletionLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPeriodEstimateAtCompletionLaborUnits()</pre>
<div class="block">Gets the value of the periodEstimateAtCompletionLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPeriodEstimateAtCompletionLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriodEstimateAtCompletionLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setPeriodEstimateAtCompletionLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the periodEstimateAtCompletionLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePeriodEstimateAtCompletionLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePeriodEstimateAtCompletionLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePeriodEstimateAtCompletionLaborUnits()</pre>
<div class="block">Gets the value of the cumulativePeriodEstimateAtCompletionLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePeriodEstimateAtCompletionLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePeriodEstimateAtCompletionLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativePeriodEstimateAtCompletionLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePeriodEstimateAtCompletionLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeriodPlannedValueLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriodPlannedValueLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPeriodPlannedValueLaborUnits()</pre>
<div class="block">Gets the value of the periodPlannedValueLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPeriodPlannedValueLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriodPlannedValueLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setPeriodPlannedValueLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the periodPlannedValueLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePeriodPlannedValueLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePeriodPlannedValueLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePeriodPlannedValueLaborUnits()</pre>
<div class="block">Gets the value of the cumulativePeriodPlannedValueLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePeriodPlannedValueLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePeriodPlannedValueLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativePeriodPlannedValueLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePeriodPlannedValueLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPlannedLaborUnits()</pre>
<div class="block">Gets the value of the plannedLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setPlannedLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePlannedLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePlannedLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePlannedLaborUnits()</pre>
<div class="block">Gets the value of the cumulativePlannedLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePlannedLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePlannedLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativePlannedLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePlannedLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedNonlaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedNonlaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPlannedNonlaborUnits()</pre>
<div class="block">Gets the value of the plannedNonlaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedNonlaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedNonlaborUnits</h4>
<pre>public&nbsp;void&nbsp;setPlannedNonlaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedNonlaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePlannedNonlaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePlannedNonlaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePlannedNonlaborUnits()</pre>
<div class="block">Gets the value of the cumulativePlannedNonlaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePlannedNonlaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePlannedNonlaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativePlannedNonlaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePlannedNonlaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedValueLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedValueLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPlannedValueLaborUnits()</pre>
<div class="block">Gets the value of the plannedValueLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedValueLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedValueLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setPlannedValueLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedValueLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePlannedValueLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePlannedValueLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePlannedValueLaborUnits()</pre>
<div class="block">Gets the value of the cumulativePlannedValueLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePlannedValueLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePlannedValueLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativePlannedValueLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePlannedValueLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingLaborUnits()</pre>
<div class="block">Gets the value of the remainingLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setRemainingLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeRemainingLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingLateLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLateLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingLateLaborUnits()</pre>
<div class="block">Gets the value of the remainingLateLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingLateLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLateLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setRemainingLateLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingLateLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingLateLaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingLateLaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingLateLaborUnits()</pre>
<div class="block">Gets the value of the cumulativeRemainingLateLaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingLateLaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingLateLaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingLateLaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingLateLaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingLateNonlaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLateNonlaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingLateNonlaborUnits()</pre>
<div class="block">Gets the value of the remainingLateNonlaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingLateNonlaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLateNonlaborUnits</h4>
<pre>public&nbsp;void&nbsp;setRemainingLateNonlaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingLateNonlaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingLateNonlaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingLateNonlaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingLateNonlaborUnits()</pre>
<div class="block">Gets the value of the cumulativeRemainingLateNonlaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingLateNonlaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingLateNonlaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingLateNonlaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingLateNonlaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingNonlaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingNonlaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingNonlaborUnits()</pre>
<div class="block">Gets the value of the remainingNonlaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingNonlaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingNonlaborUnits</h4>
<pre>public&nbsp;void&nbsp;setRemainingNonlaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingNonlaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingNonlaborUnits--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingNonlaborUnits</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingNonlaborUnits()</pre>
<div class="block">Gets the value of the cumulativeRemainingNonlaborUnits property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingNonlaborUnits-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingNonlaborUnits</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingNonlaborUnits(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingNonlaborUnits property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualCost()</pre>
<div class="block">Gets the value of the actualCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualCost</h4>
<pre>public&nbsp;void&nbsp;setActualCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeActualCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeActualCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeActualCost()</pre>
<div class="block">Gets the value of the cumulativeActualCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeActualCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeActualCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeActualCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeActualCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualExpenseCost()</pre>
<div class="block">Gets the value of the actualExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setActualExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeActualExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeActualExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeActualExpenseCost()</pre>
<div class="block">Gets the value of the cumulativeActualExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeActualExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeActualExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeActualExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeActualExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualLaborCost()</pre>
<div class="block">Gets the value of the actualLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualLaborCost</h4>
<pre>public&nbsp;void&nbsp;setActualLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeActualLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeActualLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeActualLaborCost()</pre>
<div class="block">Gets the value of the cumulativeActualLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeActualLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeActualLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeActualLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeActualLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualMaterialCost()</pre>
<div class="block">Gets the value of the actualMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setActualMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeActualMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeActualMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeActualMaterialCost()</pre>
<div class="block">Gets the value of the cumulativeActualMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeActualMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeActualMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeActualMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeActualMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualNonlaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualNonlaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualNonlaborCost()</pre>
<div class="block">Gets the value of the actualNonlaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualNonlaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualNonlaborCost</h4>
<pre>public&nbsp;void&nbsp;setActualNonlaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualNonlaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeActualNonlaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeActualNonlaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeActualNonlaborCost()</pre>
<div class="block">Gets the value of the cumulativeActualNonlaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeActualNonlaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeActualNonlaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeActualNonlaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeActualNonlaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getActualTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getActualTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getActualTotalCost()</pre>
<div class="block">Gets the value of the actualTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setActualTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setActualTotalCost</h4>
<pre>public&nbsp;void&nbsp;setActualTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the actualTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeActualTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeActualTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeActualTotalCost()</pre>
<div class="block">Gets the value of the cumulativeActualTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeActualTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeActualTotalCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeActualTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeActualTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAtCompletionExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAtCompletionExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getAtCompletionExpenseCost()</pre>
<div class="block">Gets the value of the atCompletionExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAtCompletionExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAtCompletionExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setAtCompletionExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the atCompletionExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeAtCompletionExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeAtCompletionExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeAtCompletionExpenseCost()</pre>
<div class="block">Gets the value of the cumulativeAtCompletionExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeAtCompletionExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeAtCompletionExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeAtCompletionExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeAtCompletionExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAtCompletionLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAtCompletionLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getAtCompletionLaborCost()</pre>
<div class="block">Gets the value of the atCompletionLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAtCompletionLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAtCompletionLaborCost</h4>
<pre>public&nbsp;void&nbsp;setAtCompletionLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the atCompletionLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeAtCompletionLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeAtCompletionLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeAtCompletionLaborCost()</pre>
<div class="block">Gets the value of the cumulativeAtCompletionLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeAtCompletionLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeAtCompletionLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeAtCompletionLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeAtCompletionLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAtCompletionMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAtCompletionMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getAtCompletionMaterialCost()</pre>
<div class="block">Gets the value of the atCompletionMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAtCompletionMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAtCompletionMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setAtCompletionMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the atCompletionMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeAtCompletionMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeAtCompletionMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeAtCompletionMaterialCost()</pre>
<div class="block">Gets the value of the cumulativeAtCompletionMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeAtCompletionMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeAtCompletionMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeAtCompletionMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeAtCompletionMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAtCompletionNonlaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAtCompletionNonlaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getAtCompletionNonlaborCost()</pre>
<div class="block">Gets the value of the atCompletionNonlaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAtCompletionNonlaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAtCompletionNonlaborCost</h4>
<pre>public&nbsp;void&nbsp;setAtCompletionNonlaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the atCompletionNonlaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeAtCompletionNonlaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeAtCompletionNonlaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeAtCompletionNonlaborCost()</pre>
<div class="block">Gets the value of the cumulativeAtCompletionNonlaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeAtCompletionNonlaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeAtCompletionNonlaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeAtCompletionNonlaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeAtCompletionNonlaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getAtCompletionTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAtCompletionTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getAtCompletionTotalCost()</pre>
<div class="block">Gets the value of the atCompletionTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setAtCompletionTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAtCompletionTotalCost</h4>
<pre>public&nbsp;void&nbsp;setAtCompletionTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the atCompletionTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeAtCompletionTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeAtCompletionTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeAtCompletionTotalCost()</pre>
<div class="block">Gets the value of the cumulativeAtCompletionTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeAtCompletionTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeAtCompletionTotalCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeAtCompletionTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeAtCompletionTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaselinePlannedExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselinePlannedExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaselinePlannedExpenseCost()</pre>
<div class="block">Gets the value of the baselinePlannedExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaselinePlannedExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselinePlannedExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setBaselinePlannedExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baselinePlannedExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaselinePlannedExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaselinePlannedExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaselinePlannedExpenseCost()</pre>
<div class="block">Gets the value of the cumulativeBaselinePlannedExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaselinePlannedExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaselinePlannedExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaselinePlannedExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaselinePlannedExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaselinePlannedLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselinePlannedLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaselinePlannedLaborCost()</pre>
<div class="block">Gets the value of the baselinePlannedLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaselinePlannedLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselinePlannedLaborCost</h4>
<pre>public&nbsp;void&nbsp;setBaselinePlannedLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baselinePlannedLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaselinePlannedLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaselinePlannedLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaselinePlannedLaborCost()</pre>
<div class="block">Gets the value of the cumulativeBaselinePlannedLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaselinePlannedLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaselinePlannedLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaselinePlannedLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaselinePlannedLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaselinePlannedMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselinePlannedMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaselinePlannedMaterialCost()</pre>
<div class="block">Gets the value of the baselinePlannedMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaselinePlannedMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselinePlannedMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setBaselinePlannedMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baselinePlannedMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaselinePlannedMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaselinePlannedMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaselinePlannedMaterialCost()</pre>
<div class="block">Gets the value of the cumulativeBaselinePlannedMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaselinePlannedMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaselinePlannedMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaselinePlannedMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaselinePlannedMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaselinePlannedNonlaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselinePlannedNonlaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaselinePlannedNonlaborCost()</pre>
<div class="block">Gets the value of the baselinePlannedNonlaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaselinePlannedNonlaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselinePlannedNonlaborCost</h4>
<pre>public&nbsp;void&nbsp;setBaselinePlannedNonlaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baselinePlannedNonlaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaselinePlannedNonlaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaselinePlannedNonlaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaselinePlannedNonlaborCost()</pre>
<div class="block">Gets the value of the cumulativeBaselinePlannedNonlaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaselinePlannedNonlaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaselinePlannedNonlaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaselinePlannedNonlaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaselinePlannedNonlaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getBaselinePlannedTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselinePlannedTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getBaselinePlannedTotalCost()</pre>
<div class="block">Gets the value of the baselinePlannedTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setBaselinePlannedTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselinePlannedTotalCost</h4>
<pre>public&nbsp;void&nbsp;setBaselinePlannedTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the baselinePlannedTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeBaselinePlannedTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeBaselinePlannedTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeBaselinePlannedTotalCost()</pre>
<div class="block">Gets the value of the cumulativeBaselinePlannedTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeBaselinePlannedTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeBaselinePlannedTotalCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeBaselinePlannedTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeBaselinePlannedTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getEarnedValueCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEarnedValueCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getEarnedValueCost()</pre>
<div class="block">Gets the value of the earnedValueCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEarnedValueCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEarnedValueCost</h4>
<pre>public&nbsp;void&nbsp;setEarnedValueCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the earnedValueCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeEarnedValueCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeEarnedValueCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeEarnedValueCost()</pre>
<div class="block">Gets the value of the cumulativeEarnedValueCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeEarnedValueCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeEarnedValueCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeEarnedValueCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeEarnedValueCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getEstimateAtCompletionCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEstimateAtCompletionCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getEstimateAtCompletionCost()</pre>
<div class="block">Gets the value of the estimateAtCompletionCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEstimateAtCompletionCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEstimateAtCompletionCost</h4>
<pre>public&nbsp;void&nbsp;setEstimateAtCompletionCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the estimateAtCompletionCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeEstimateAtCompletionCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeEstimateAtCompletionCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeEstimateAtCompletionCost()</pre>
<div class="block">Gets the value of the cumulativeEstimateAtCompletionCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeEstimateAtCompletionCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeEstimateAtCompletionCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeEstimateAtCompletionCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeEstimateAtCompletionCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getEstimateToCompleteCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getEstimateToCompleteCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getEstimateToCompleteCost()</pre>
<div class="block">Gets the value of the estimateToCompleteCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setEstimateToCompleteCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setEstimateToCompleteCost</h4>
<pre>public&nbsp;void&nbsp;setEstimateToCompleteCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the estimateToCompleteCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeEstimateToCompleteCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeEstimateToCompleteCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeEstimateToCompleteCost()</pre>
<div class="block">Gets the value of the cumulativeEstimateToCompleteCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeEstimateToCompleteCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeEstimateToCompleteCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeEstimateToCompleteCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeEstimateToCompleteCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeriodActualCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriodActualCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPeriodActualCost()</pre>
<div class="block">Gets the value of the periodActualCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPeriodActualCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriodActualCost</h4>
<pre>public&nbsp;void&nbsp;setPeriodActualCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the periodActualCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePeriodActualCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePeriodActualCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePeriodActualCost()</pre>
<div class="block">Gets the value of the cumulativePeriodActualCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePeriodActualCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePeriodActualCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePeriodActualCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePeriodActualCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeriodActualExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriodActualExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPeriodActualExpenseCost()</pre>
<div class="block">Gets the value of the periodActualExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPeriodActualExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriodActualExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setPeriodActualExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the periodActualExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePeriodActualExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePeriodActualExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePeriodActualExpenseCost()</pre>
<div class="block">Gets the value of the cumulativePeriodActualExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePeriodActualExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePeriodActualExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePeriodActualExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePeriodActualExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeriodActualLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriodActualLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPeriodActualLaborCost()</pre>
<div class="block">Gets the value of the periodActualLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPeriodActualLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriodActualLaborCost</h4>
<pre>public&nbsp;void&nbsp;setPeriodActualLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the periodActualLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePeriodActualLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePeriodActualLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePeriodActualLaborCost()</pre>
<div class="block">Gets the value of the cumulativePeriodActualLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePeriodActualLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePeriodActualLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePeriodActualLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePeriodActualLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeriodActualMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriodActualMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPeriodActualMaterialCost()</pre>
<div class="block">Gets the value of the periodActualMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPeriodActualMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriodActualMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setPeriodActualMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the periodActualMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePeriodActualMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePeriodActualMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePeriodActualMaterialCost()</pre>
<div class="block">Gets the value of the cumulativePeriodActualMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePeriodActualMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePeriodActualMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePeriodActualMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePeriodActualMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeriodActualNonLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriodActualNonLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPeriodActualNonLaborCost()</pre>
<div class="block">Gets the value of the periodActualNonLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPeriodActualNonLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriodActualNonLaborCost</h4>
<pre>public&nbsp;void&nbsp;setPeriodActualNonLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the periodActualNonLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePeriodActualNonLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePeriodActualNonLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePeriodActualNonLaborCost()</pre>
<div class="block">Gets the value of the cumulativePeriodActualNonLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePeriodActualNonLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePeriodActualNonLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePeriodActualNonLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePeriodActualNonLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeriodAtCompletionExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriodAtCompletionExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPeriodAtCompletionExpenseCost()</pre>
<div class="block">Gets the value of the periodAtCompletionExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPeriodAtCompletionExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriodAtCompletionExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setPeriodAtCompletionExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the periodAtCompletionExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePeriodAtCompletionExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePeriodAtCompletionExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePeriodAtCompletionExpenseCost()</pre>
<div class="block">Gets the value of the cumulativePeriodAtCompletionExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePeriodAtCompletionExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePeriodAtCompletionExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePeriodAtCompletionExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePeriodAtCompletionExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeriodAtCompletionLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriodAtCompletionLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPeriodAtCompletionLaborCost()</pre>
<div class="block">Gets the value of the periodAtCompletionLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPeriodAtCompletionLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriodAtCompletionLaborCost</h4>
<pre>public&nbsp;void&nbsp;setPeriodAtCompletionLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the periodAtCompletionLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePeriodAtCompletionLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePeriodAtCompletionLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePeriodAtCompletionLaborCost()</pre>
<div class="block">Gets the value of the cumulativePeriodAtCompletionLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePeriodAtCompletionLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePeriodAtCompletionLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePeriodAtCompletionLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePeriodAtCompletionLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeriodAtCompletionMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriodAtCompletionMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPeriodAtCompletionMaterialCost()</pre>
<div class="block">Gets the value of the periodAtCompletionMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPeriodAtCompletionMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriodAtCompletionMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setPeriodAtCompletionMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the periodAtCompletionMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePeriodAtCompletionMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePeriodAtCompletionMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePeriodAtCompletionMaterialCost()</pre>
<div class="block">Gets the value of the cumulativePeriodAtCompletionMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePeriodAtCompletionMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePeriodAtCompletionMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePeriodAtCompletionMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePeriodAtCompletionMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeriodAtCompletionNonLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriodAtCompletionNonLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPeriodAtCompletionNonLaborCost()</pre>
<div class="block">Gets the value of the periodAtCompletionNonLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPeriodAtCompletionNonLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriodAtCompletionNonLaborCost</h4>
<pre>public&nbsp;void&nbsp;setPeriodAtCompletionNonLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the periodAtCompletionNonLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePeriodAtCompletionNonLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePeriodAtCompletionNonLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePeriodAtCompletionNonLaborCost()</pre>
<div class="block">Gets the value of the cumulativePeriodAtCompletionNonLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePeriodAtCompletionNonLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePeriodAtCompletionNonLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePeriodAtCompletionNonLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePeriodAtCompletionNonLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeriodAtCompletionTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriodAtCompletionTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPeriodAtCompletionTotalCost()</pre>
<div class="block">Gets the value of the periodAtCompletionTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPeriodAtCompletionTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriodAtCompletionTotalCost</h4>
<pre>public&nbsp;void&nbsp;setPeriodAtCompletionTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the periodAtCompletionTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePeriodAtCompletionTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePeriodAtCompletionTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePeriodAtCompletionTotalCost()</pre>
<div class="block">Gets the value of the cumulativePeriodAtCompletionTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePeriodAtCompletionTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePeriodAtCompletionTotalCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePeriodAtCompletionTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePeriodAtCompletionTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeriodEarnedValueCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriodEarnedValueCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPeriodEarnedValueCost()</pre>
<div class="block">Gets the value of the periodEarnedValueCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPeriodEarnedValueCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriodEarnedValueCost</h4>
<pre>public&nbsp;void&nbsp;setPeriodEarnedValueCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the periodEarnedValueCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePeriodEarnedValueCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePeriodEarnedValueCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePeriodEarnedValueCost()</pre>
<div class="block">Gets the value of the cumulativePeriodEarnedValueCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePeriodEarnedValueCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePeriodEarnedValueCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePeriodEarnedValueCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePeriodEarnedValueCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeriodEstimateAtCompletionCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriodEstimateAtCompletionCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPeriodEstimateAtCompletionCost()</pre>
<div class="block">Gets the value of the periodEstimateAtCompletionCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPeriodEstimateAtCompletionCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriodEstimateAtCompletionCost</h4>
<pre>public&nbsp;void&nbsp;setPeriodEstimateAtCompletionCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the periodEstimateAtCompletionCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePeriodEstimateAtCompletionCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePeriodEstimateAtCompletionCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePeriodEstimateAtCompletionCost()</pre>
<div class="block">Gets the value of the cumulativePeriodEstimateAtCompletionCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePeriodEstimateAtCompletionCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePeriodEstimateAtCompletionCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePeriodEstimateAtCompletionCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePeriodEstimateAtCompletionCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPeriodPlannedValueCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPeriodPlannedValueCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPeriodPlannedValueCost()</pre>
<div class="block">Gets the value of the periodPlannedValueCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPeriodPlannedValueCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPeriodPlannedValueCost</h4>
<pre>public&nbsp;void&nbsp;setPeriodPlannedValueCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the periodPlannedValueCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePeriodPlannedValueCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePeriodPlannedValueCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePeriodPlannedValueCost()</pre>
<div class="block">Gets the value of the cumulativePeriodPlannedValueCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePeriodPlannedValueCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePeriodPlannedValueCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePeriodPlannedValueCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePeriodPlannedValueCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPlannedExpenseCost()</pre>
<div class="block">Gets the value of the plannedExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setPlannedExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePlannedExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePlannedExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePlannedExpenseCost()</pre>
<div class="block">Gets the value of the cumulativePlannedExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePlannedExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePlannedExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePlannedExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePlannedExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPlannedLaborCost()</pre>
<div class="block">Gets the value of the plannedLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedLaborCost</h4>
<pre>public&nbsp;void&nbsp;setPlannedLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePlannedLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePlannedLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePlannedLaborCost()</pre>
<div class="block">Gets the value of the cumulativePlannedLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePlannedLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePlannedLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePlannedLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePlannedLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPlannedMaterialCost()</pre>
<div class="block">Gets the value of the plannedMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setPlannedMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePlannedMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePlannedMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePlannedMaterialCost()</pre>
<div class="block">Gets the value of the cumulativePlannedMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePlannedMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePlannedMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePlannedMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePlannedMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedNonlaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedNonlaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPlannedNonlaborCost()</pre>
<div class="block">Gets the value of the plannedNonlaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedNonlaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedNonlaborCost</h4>
<pre>public&nbsp;void&nbsp;setPlannedNonlaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedNonlaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePlannedNonlaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePlannedNonlaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePlannedNonlaborCost()</pre>
<div class="block">Gets the value of the cumulativePlannedNonlaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePlannedNonlaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePlannedNonlaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePlannedNonlaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePlannedNonlaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPlannedTotalCost()</pre>
<div class="block">Gets the value of the plannedTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedTotalCost</h4>
<pre>public&nbsp;void&nbsp;setPlannedTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePlannedTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePlannedTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePlannedTotalCost()</pre>
<div class="block">Gets the value of the cumulativePlannedTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePlannedTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePlannedTotalCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePlannedTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePlannedTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPlannedValueCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPlannedValueCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getPlannedValueCost()</pre>
<div class="block">Gets the value of the plannedValueCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPlannedValueCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPlannedValueCost</h4>
<pre>public&nbsp;void&nbsp;setPlannedValueCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the plannedValueCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativePlannedValueCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativePlannedValueCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativePlannedValueCost()</pre>
<div class="block">Gets the value of the cumulativePlannedValueCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativePlannedValueCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativePlannedValueCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativePlannedValueCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativePlannedValueCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingExpenseCost()</pre>
<div class="block">Gets the value of the remainingExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingExpenseCost()</pre>
<div class="block">Gets the value of the cumulativeRemainingExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingLaborCost()</pre>
<div class="block">Gets the value of the remainingLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLaborCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingLaborCost()</pre>
<div class="block">Gets the value of the cumulativeRemainingLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingLateExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLateExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingLateExpenseCost()</pre>
<div class="block">Gets the value of the remainingLateExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingLateExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLateExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingLateExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingLateExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingLateExpenseCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingLateExpenseCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingLateExpenseCost()</pre>
<div class="block">Gets the value of the cumulativeRemainingLateExpenseCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingLateExpenseCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingLateExpenseCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingLateExpenseCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingLateExpenseCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingLateLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLateLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingLateLaborCost()</pre>
<div class="block">Gets the value of the remainingLateLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingLateLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLateLaborCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingLateLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingLateLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingLateLaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingLateLaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingLateLaborCost()</pre>
<div class="block">Gets the value of the cumulativeRemainingLateLaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingLateLaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingLateLaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingLateLaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingLateLaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingLateMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLateMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingLateMaterialCost()</pre>
<div class="block">Gets the value of the remainingLateMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingLateMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLateMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingLateMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingLateMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingLateMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingLateMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingLateMaterialCost()</pre>
<div class="block">Gets the value of the cumulativeRemainingLateMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingLateMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingLateMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingLateMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingLateMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingLateNonlaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLateNonlaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingLateNonlaborCost()</pre>
<div class="block">Gets the value of the remainingLateNonlaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingLateNonlaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLateNonlaborCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingLateNonlaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingLateNonlaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingLateNonlaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingLateNonlaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingLateNonlaborCost()</pre>
<div class="block">Gets the value of the cumulativeRemainingLateNonlaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingLateNonlaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingLateNonlaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingLateNonlaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingLateNonlaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingLateTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingLateTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingLateTotalCost()</pre>
<div class="block">Gets the value of the remainingLateTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingLateTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingLateTotalCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingLateTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingLateTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingLateTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingLateTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingLateTotalCost()</pre>
<div class="block">Gets the value of the cumulativeRemainingLateTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingLateTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingLateTotalCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingLateTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingLateTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingMaterialCost()</pre>
<div class="block">Gets the value of the remainingMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingMaterialCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingMaterialCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingMaterialCost()</pre>
<div class="block">Gets the value of the cumulativeRemainingMaterialCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingMaterialCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingMaterialCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingMaterialCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingMaterialCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingNonlaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingNonlaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingNonlaborCost()</pre>
<div class="block">Gets the value of the remainingNonlaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingNonlaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingNonlaborCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingNonlaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingNonlaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingNonlaborCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingNonlaborCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingNonlaborCost()</pre>
<div class="block">Gets the value of the cumulativeRemainingNonlaborCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingNonlaborCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCumulativeRemainingNonlaborCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingNonlaborCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingNonlaborCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getRemainingTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRemainingTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getRemainingTotalCost()</pre>
<div class="block">Gets the value of the remainingTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setRemainingTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setRemainingTotalCost</h4>
<pre>public&nbsp;void&nbsp;setRemainingTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the remainingTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCumulativeRemainingTotalCost--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCumulativeRemainingTotalCost</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;getCumulativeRemainingTotalCost()</pre>
<div class="block">Gets the value of the cumulativeRemainingTotalCost property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCumulativeRemainingTotalCost-java.lang.Double-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setCumulativeRemainingTotalCost</h4>
<pre>public&nbsp;void&nbsp;setCumulativeRemainingTotalCost(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Double.html?is-external=true" title="class or interface in java.lang">Double</a>&nbsp;value)</pre>
<div class="block">Sets the value of the cumulativeRemainingTotalCost property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/EPSProjectWBSSpreadType.Period.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/primavera/schema/EPSProjectWBSSpreadType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/primavera/schema/EPSSpendingPlanType.html" title="class in org.mpxj.primavera.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/primavera/schema/EPSProjectWBSSpreadType.Period.html" target="_top">Frames</a></li>
<li><a href="EPSProjectWBSSpreadType.Period.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
