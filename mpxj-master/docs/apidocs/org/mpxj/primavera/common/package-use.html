<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Package org.mpxj.primavera.common (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Package org.mpxj.primavera.common (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/primavera/common/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Uses of Package org.mpxj.primavera.common" class="title">Uses of Package<br>org.mpxj.primavera.common</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../org/mpxj/primavera/common/package-summary.html">org.mpxj.primavera.common</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.primavera.common">org.mpxj.primavera.common</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.primavera.suretrak">org.mpxj.primavera.suretrak</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.primavera.common">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../org/mpxj/primavera/common/package-summary.html">org.mpxj.primavera.common</a> used by <a href="../../../../org/mpxj/primavera/common/package-summary.html">org.mpxj.primavera.common</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../org/mpxj/primavera/common/class-use/AbstractColumn.html#org.mpxj.primavera.common">AbstractColumn</a>
<div class="block">Common column implementation methods.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../org/mpxj/primavera/common/class-use/AbstractIntColumn.html#org.mpxj.primavera.common">AbstractIntColumn</a>
<div class="block">Extract column data from a table.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../org/mpxj/primavera/common/class-use/AbstractShortColumn.html#org.mpxj.primavera.common">AbstractShortColumn</a>
<div class="block">Common methods for columns based on short integers.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../org/mpxj/primavera/common/class-use/ColumnDefinition.html#org.mpxj.primavera.common">ColumnDefinition</a>
<div class="block">Classes which implement this interface define how columns
 of a specific type can be read from the P3 database.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../org/mpxj/primavera/common/class-use/MapRow.html#org.mpxj.primavera.common">MapRow</a>
<div class="block">Wraps a simple map which contains name value
 pairs representing the column values
 from an individual row.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../org/mpxj/primavera/common/class-use/RowValidator.html#org.mpxj.primavera.common">RowValidator</a>
<div class="block">Implementations of this interface allow additional
 validation checks to be supplied in order to determine
 if a row contains valid data.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.primavera.suretrak">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../org/mpxj/primavera/common/package-summary.html">org.mpxj.primavera.common</a> used by <a href="../../../../org/mpxj/primavera/suretrak/package-summary.html">org.mpxj.primavera.suretrak</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../org/mpxj/primavera/common/class-use/AbstractWbsFormat.html#org.mpxj.primavera.suretrak">AbstractWbsFormat</a>
<div class="block">Common methods to support reading the WBS format definition from P3 and SureTrak.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../org/mpxj/primavera/common/class-use/MapRow.html#org.mpxj.primavera.suretrak">MapRow</a>
<div class="block">Wraps a simple map which contains name value
 pairs representing the column values
 from an individual row.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/primavera/common/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
