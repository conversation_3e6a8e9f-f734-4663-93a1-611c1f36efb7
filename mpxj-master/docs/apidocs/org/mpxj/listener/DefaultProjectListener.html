<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>DefaultProjectListener (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="DefaultProjectListener (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DefaultProjectListener.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../org/mpxj/listener/FieldListener.html" title="interface in org.mpxj.listener"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/listener/DefaultProjectListener.html" target="_top">Frames</a></li>
<li><a href="DefaultProjectListener.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.listener</div>
<h2 title="Class DefaultProjectListener" class="title">Class DefaultProjectListener</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.listener.DefaultProjectListener</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../org/mpxj/listener/ProjectListener.html" title="interface in org.mpxj.listener">ProjectListener</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">DefaultProjectListener</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>
implements <a href="../../../org/mpxj/listener/ProjectListener.html" title="interface in org.mpxj.listener">ProjectListener</a></pre>
<div class="block">This class is a default implementation of the ProjectListener interface.
 This is designed to be subclassed by developers to allow them to implement
 the event methods they require, without having to provide implementations for
 other methods which they are not interested in.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/listener/DefaultProjectListener.html#DefaultProjectListener--">DefaultProjectListener</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/listener/DefaultProjectListener.html#assignmentRead-org.mpxj.ResourceAssignment-">assignmentRead</a></span>(<a href="../../../org/mpxj/ResourceAssignment.html" title="class in org.mpxj">ResourceAssignment</a>&nbsp;assignment)</code>
<div class="block">This method is called when an assignment is read from a file.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/listener/DefaultProjectListener.html#assignmentWritten-org.mpxj.ResourceAssignment-">assignmentWritten</a></span>(<a href="../../../org/mpxj/ResourceAssignment.html" title="class in org.mpxj">ResourceAssignment</a>&nbsp;assignment)</code>
<div class="block">This method is called when an assignment is written to a file.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/listener/DefaultProjectListener.html#calendarRead-org.mpxj.ProjectCalendar-">calendarRead</a></span>(<a href="../../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;calendar)</code>
<div class="block">This method is called when a calendar is read from a file.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/listener/DefaultProjectListener.html#calendarWritten-org.mpxj.ProjectCalendar-">calendarWritten</a></span>(<a href="../../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;calendar)</code>
<div class="block">This method is called when a calendar is written to a file.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/listener/DefaultProjectListener.html#relationRead-org.mpxj.Relation-">relationRead</a></span>(<a href="../../../org/mpxj/Relation.html" title="class in org.mpxj">Relation</a>&nbsp;relation)</code>
<div class="block">This method is called when a relation is read from a file.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/listener/DefaultProjectListener.html#relationWritten-org.mpxj.Relation-">relationWritten</a></span>(<a href="../../../org/mpxj/Relation.html" title="class in org.mpxj">Relation</a>&nbsp;relation)</code>
<div class="block">This method is called when a relation is written to a file.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/listener/DefaultProjectListener.html#resourceRead-org.mpxj.Resource-">resourceRead</a></span>(<a href="../../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;resource)</code>
<div class="block">This method is called when a resource is read from a file.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/listener/DefaultProjectListener.html#resourceWritten-org.mpxj.Resource-">resourceWritten</a></span>(<a href="../../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;resource)</code>
<div class="block">This method is called when a resource is written to a file.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/listener/DefaultProjectListener.html#taskRead-org.mpxj.Task-">taskRead</a></span>(<a href="../../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;task)</code>
<div class="block">This method is called when a task is read from a file.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/listener/DefaultProjectListener.html#taskWritten-org.mpxj.Task-">taskWritten</a></span>(<a href="../../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;task)</code>
<div class="block">This method is called when a task is written to a file.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="DefaultProjectListener--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>DefaultProjectListener</h4>
<pre>public&nbsp;DefaultProjectListener()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="taskRead-org.mpxj.Task-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskRead</h4>
<pre>public&nbsp;void&nbsp;taskRead(<a href="../../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;task)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../org/mpxj/listener/ProjectListener.html#taskRead-org.mpxj.Task-">ProjectListener</a></code></span></div>
<div class="block">This method is called when a task is read from a file.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../org/mpxj/listener/ProjectListener.html#taskRead-org.mpxj.Task-">taskRead</a></code>&nbsp;in interface&nbsp;<code><a href="../../../org/mpxj/listener/ProjectListener.html" title="interface in org.mpxj.listener">ProjectListener</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>task</code> - task instance</dd>
</dl>
</li>
</ul>
<a name="taskWritten-org.mpxj.Task-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>taskWritten</h4>
<pre>public&nbsp;void&nbsp;taskWritten(<a href="../../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;task)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../org/mpxj/listener/ProjectListener.html#taskWritten-org.mpxj.Task-">ProjectListener</a></code></span></div>
<div class="block">This method is called when a task is written to a file.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../org/mpxj/listener/ProjectListener.html#taskWritten-org.mpxj.Task-">taskWritten</a></code>&nbsp;in interface&nbsp;<code><a href="../../../org/mpxj/listener/ProjectListener.html" title="interface in org.mpxj.listener">ProjectListener</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>task</code> - task instance</dd>
</dl>
</li>
</ul>
<a name="resourceRead-org.mpxj.Resource-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resourceRead</h4>
<pre>public&nbsp;void&nbsp;resourceRead(<a href="../../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;resource)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../org/mpxj/listener/ProjectListener.html#resourceRead-org.mpxj.Resource-">ProjectListener</a></code></span></div>
<div class="block">This method is called when a resource is read from a file.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../org/mpxj/listener/ProjectListener.html#resourceRead-org.mpxj.Resource-">resourceRead</a></code>&nbsp;in interface&nbsp;<code><a href="../../../org/mpxj/listener/ProjectListener.html" title="interface in org.mpxj.listener">ProjectListener</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>resource</code> - resource instance</dd>
</dl>
</li>
</ul>
<a name="resourceWritten-org.mpxj.Resource-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resourceWritten</h4>
<pre>public&nbsp;void&nbsp;resourceWritten(<a href="../../../org/mpxj/Resource.html" title="class in org.mpxj">Resource</a>&nbsp;resource)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../org/mpxj/listener/ProjectListener.html#resourceWritten-org.mpxj.Resource-">ProjectListener</a></code></span></div>
<div class="block">This method is called when a resource is written to a file.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../org/mpxj/listener/ProjectListener.html#resourceWritten-org.mpxj.Resource-">resourceWritten</a></code>&nbsp;in interface&nbsp;<code><a href="../../../org/mpxj/listener/ProjectListener.html" title="interface in org.mpxj.listener">ProjectListener</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>resource</code> - resource instance</dd>
</dl>
</li>
</ul>
<a name="calendarRead-org.mpxj.ProjectCalendar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calendarRead</h4>
<pre>public&nbsp;void&nbsp;calendarRead(<a href="../../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;calendar)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../org/mpxj/listener/ProjectListener.html#calendarRead-org.mpxj.ProjectCalendar-">ProjectListener</a></code></span></div>
<div class="block">This method is called when a calendar is read from a file.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../org/mpxj/listener/ProjectListener.html#calendarRead-org.mpxj.ProjectCalendar-">calendarRead</a></code>&nbsp;in interface&nbsp;<code><a href="../../../org/mpxj/listener/ProjectListener.html" title="interface in org.mpxj.listener">ProjectListener</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>calendar</code> - calendar instance</dd>
</dl>
</li>
</ul>
<a name="calendarWritten-org.mpxj.ProjectCalendar-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calendarWritten</h4>
<pre>public&nbsp;void&nbsp;calendarWritten(<a href="../../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;calendar)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../org/mpxj/listener/ProjectListener.html#calendarWritten-org.mpxj.ProjectCalendar-">ProjectListener</a></code></span></div>
<div class="block">This method is called when a calendar is written to a file.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../org/mpxj/listener/ProjectListener.html#calendarWritten-org.mpxj.ProjectCalendar-">calendarWritten</a></code>&nbsp;in interface&nbsp;<code><a href="../../../org/mpxj/listener/ProjectListener.html" title="interface in org.mpxj.listener">ProjectListener</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>calendar</code> - calendar instance</dd>
</dl>
</li>
</ul>
<a name="assignmentRead-org.mpxj.ResourceAssignment-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>assignmentRead</h4>
<pre>public&nbsp;void&nbsp;assignmentRead(<a href="../../../org/mpxj/ResourceAssignment.html" title="class in org.mpxj">ResourceAssignment</a>&nbsp;assignment)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../org/mpxj/listener/ProjectListener.html#assignmentRead-org.mpxj.ResourceAssignment-">ProjectListener</a></code></span></div>
<div class="block">This method is called when an assignment is read from a file.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../org/mpxj/listener/ProjectListener.html#assignmentRead-org.mpxj.ResourceAssignment-">assignmentRead</a></code>&nbsp;in interface&nbsp;<code><a href="../../../org/mpxj/listener/ProjectListener.html" title="interface in org.mpxj.listener">ProjectListener</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>assignment</code> - resource assignment</dd>
</dl>
</li>
</ul>
<a name="assignmentWritten-org.mpxj.ResourceAssignment-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>assignmentWritten</h4>
<pre>public&nbsp;void&nbsp;assignmentWritten(<a href="../../../org/mpxj/ResourceAssignment.html" title="class in org.mpxj">ResourceAssignment</a>&nbsp;assignment)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../org/mpxj/listener/ProjectListener.html#assignmentWritten-org.mpxj.ResourceAssignment-">ProjectListener</a></code></span></div>
<div class="block">This method is called when an assignment is written to a file.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../org/mpxj/listener/ProjectListener.html#assignmentWritten-org.mpxj.ResourceAssignment-">assignmentWritten</a></code>&nbsp;in interface&nbsp;<code><a href="../../../org/mpxj/listener/ProjectListener.html" title="interface in org.mpxj.listener">ProjectListener</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>assignment</code> - assignment instance</dd>
</dl>
</li>
</ul>
<a name="relationRead-org.mpxj.Relation-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>relationRead</h4>
<pre>public&nbsp;void&nbsp;relationRead(<a href="../../../org/mpxj/Relation.html" title="class in org.mpxj">Relation</a>&nbsp;relation)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../org/mpxj/listener/ProjectListener.html#relationRead-org.mpxj.Relation-">ProjectListener</a></code></span></div>
<div class="block">This method is called when a relation is read from a file.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../org/mpxj/listener/ProjectListener.html#relationRead-org.mpxj.Relation-">relationRead</a></code>&nbsp;in interface&nbsp;<code><a href="../../../org/mpxj/listener/ProjectListener.html" title="interface in org.mpxj.listener">ProjectListener</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>relation</code> - relation instance</dd>
</dl>
</li>
</ul>
<a name="relationWritten-org.mpxj.Relation-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>relationWritten</h4>
<pre>public&nbsp;void&nbsp;relationWritten(<a href="../../../org/mpxj/Relation.html" title="class in org.mpxj">Relation</a>&nbsp;relation)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../org/mpxj/listener/ProjectListener.html#relationWritten-org.mpxj.Relation-">ProjectListener</a></code></span></div>
<div class="block">This method is called when a relation is written to a file.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../org/mpxj/listener/ProjectListener.html#relationWritten-org.mpxj.Relation-">relationWritten</a></code>&nbsp;in interface&nbsp;<code><a href="../../../org/mpxj/listener/ProjectListener.html" title="interface in org.mpxj.listener">ProjectListener</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>relation</code> - relation instance</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/DefaultProjectListener.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../../org/mpxj/listener/FieldListener.html" title="interface in org.mpxj.listener"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/listener/DefaultProjectListener.html" target="_top">Frames</a></li>
<li><a href="DefaultProjectListener.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
