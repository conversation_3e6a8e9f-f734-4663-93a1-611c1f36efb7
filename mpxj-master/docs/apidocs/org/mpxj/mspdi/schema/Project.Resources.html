<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Project.Resources (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Project.Resources (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Project.Resources.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html" title="class in org.mpxj.mspdi.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html" title="class in org.mpxj.mspdi.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/mspdi/schema/Project.Resources.html" target="_top">Frames</a></li>
<li><a href="Project.Resources.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.mspdi.schema</div>
<h2 title="Class Project.Resources" class="title">Class Project.Resources</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.mspdi.schema.Project.Resources</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../../org/mpxj/mspdi/schema/Project.html" title="class in org.mpxj.mspdi.schema">Project</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">Project.Resources</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block"><p>Java class for anonymous complex type.

 <p>The following schema fragment specifies the expected content contained within this class.

 <pre>
 &lt;complexType&gt;
   &lt;complexContent&gt;
     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       &lt;sequence&gt;
         &lt;element name="Resource" maxOccurs="unbounded" minOccurs="0"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="UID" type="{http://www.w3.org/2001/XMLSchema}integer"/&gt;
                   &lt;element name="GUID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                   &lt;element name="ID" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                   &lt;element name="Name" minOccurs="0"&gt;
                     &lt;simpleType&gt;
                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                         &lt;maxLength value="512"/&gt;
                       &lt;/restriction&gt;
                     &lt;/simpleType&gt;
                   &lt;/element&gt;
                   &lt;element name="Type" minOccurs="0"&gt;
                     &lt;simpleType&gt;
                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                         &lt;enumeration value="0"/&gt;
                         &lt;enumeration value="1"/&gt;
                       &lt;/restriction&gt;
                     &lt;/simpleType&gt;
                   &lt;/element&gt;
                   &lt;element name="IsNull" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                   &lt;element name="Initials" minOccurs="0"&gt;
                     &lt;simpleType&gt;
                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                         &lt;maxLength value="512"/&gt;
                       &lt;/restriction&gt;
                     &lt;/simpleType&gt;
                   &lt;/element&gt;
                   &lt;element name="Phonetics" minOccurs="0"&gt;
                     &lt;simpleType&gt;
                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                         &lt;maxLength value="512"/&gt;
                       &lt;/restriction&gt;
                     &lt;/simpleType&gt;
                   &lt;/element&gt;
                   &lt;element name="NTAccount" minOccurs="0"&gt;
                     &lt;simpleType&gt;
                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                         &lt;maxLength value="512"/&gt;
                       &lt;/restriction&gt;
                     &lt;/simpleType&gt;
                   &lt;/element&gt;
                   &lt;element name="MaterialLabel" minOccurs="0"&gt;
                     &lt;simpleType&gt;
                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                         &lt;maxLength value="512"/&gt;
                       &lt;/restriction&gt;
                     &lt;/simpleType&gt;
                   &lt;/element&gt;
                   &lt;element name="Code" minOccurs="0"&gt;
                     &lt;simpleType&gt;
                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                         &lt;maxLength value="512"/&gt;
                       &lt;/restriction&gt;
                     &lt;/simpleType&gt;
                   &lt;/element&gt;
                   &lt;element name="Group" minOccurs="0"&gt;
                     &lt;simpleType&gt;
                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                         &lt;maxLength value="512"/&gt;
                       &lt;/restriction&gt;
                     &lt;/simpleType&gt;
                   &lt;/element&gt;
                   &lt;element name="WorkGroup" minOccurs="0"&gt;
                     &lt;simpleType&gt;
                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                         &lt;enumeration value="0"/&gt;
                         &lt;enumeration value="1"/&gt;
                         &lt;enumeration value="2"/&gt;
                         &lt;enumeration value="3"/&gt;
                       &lt;/restriction&gt;
                     &lt;/simpleType&gt;
                   &lt;/element&gt;
                   &lt;element name="EmailAddress" minOccurs="0"&gt;
                     &lt;simpleType&gt;
                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                         &lt;maxLength value="512"/&gt;
                       &lt;/restriction&gt;
                     &lt;/simpleType&gt;
                   &lt;/element&gt;
                   &lt;element name="Hyperlink" minOccurs="0"&gt;
                     &lt;simpleType&gt;
                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                         &lt;maxLength value="512"/&gt;
                       &lt;/restriction&gt;
                     &lt;/simpleType&gt;
                   &lt;/element&gt;
                   &lt;element name="HyperlinkAddress" minOccurs="0"&gt;
                     &lt;simpleType&gt;
                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                         &lt;maxLength value="512"/&gt;
                       &lt;/restriction&gt;
                     &lt;/simpleType&gt;
                   &lt;/element&gt;
                   &lt;element name="HyperlinkSubAddress" minOccurs="0"&gt;
                     &lt;simpleType&gt;
                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                         &lt;maxLength value="512"/&gt;
                       &lt;/restriction&gt;
                     &lt;/simpleType&gt;
                   &lt;/element&gt;
                   &lt;element name="MaxUnits" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                   &lt;element name="PeakUnits" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                   &lt;element name="OverAllocated" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                   &lt;element name="AvailableFrom" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                   &lt;element name="AvailableTo" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                   &lt;element name="Start" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                   &lt;element name="Finish" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                   &lt;element name="CanLevel" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                   &lt;element name="AccrueAt" minOccurs="0"&gt;
                     &lt;simpleType&gt;
                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                         &lt;enumeration value="1"/&gt;
                         &lt;enumeration value="2"/&gt;
                         &lt;enumeration value="3"/&gt;
                         &lt;enumeration value="4"/&gt;
                       &lt;/restriction&gt;
                     &lt;/simpleType&gt;
                   &lt;/element&gt;
                   &lt;element name="Work" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                   &lt;element name="RegularWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                   &lt;element name="OvertimeWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                   &lt;element name="ActualWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                   &lt;element name="RemainingWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                   &lt;element name="ActualOvertimeWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                   &lt;element name="RemainingOvertimeWork" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                   &lt;element name="PercentWorkComplete" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                   &lt;element name="StandardRate" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                   &lt;element name="StandardRateFormat" minOccurs="0"&gt;
                     &lt;simpleType&gt;
                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                         &lt;enumeration value="1"/&gt;
                         &lt;enumeration value="2"/&gt;
                         &lt;enumeration value="3"/&gt;
                         &lt;enumeration value="4"/&gt;
                         &lt;enumeration value="5"/&gt;
                         &lt;enumeration value="7"/&gt;
                         &lt;enumeration value="8"/&gt;
                       &lt;/restriction&gt;
                     &lt;/simpleType&gt;
                   &lt;/element&gt;
                   &lt;element name="Cost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                   &lt;element name="OvertimeRate" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                   &lt;element name="OvertimeRateFormat" minOccurs="0"&gt;
                     &lt;simpleType&gt;
                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                         &lt;enumeration value="1"/&gt;
                         &lt;enumeration value="2"/&gt;
                         &lt;enumeration value="3"/&gt;
                         &lt;enumeration value="4"/&gt;
                         &lt;enumeration value="5"/&gt;
                         &lt;enumeration value="7"/&gt;
                       &lt;/restriction&gt;
                     &lt;/simpleType&gt;
                   &lt;/element&gt;
                   &lt;element name="OvertimeCost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                   &lt;element name="CostPerUse" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                   &lt;element name="ActualCost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                   &lt;element name="ActualOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                   &lt;element name="RemainingCost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                   &lt;element name="RemainingOvertimeCost" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                   &lt;element name="WorkVariance" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                   &lt;element name="CostVariance" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                   &lt;element name="SV" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                   &lt;element name="CV" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                   &lt;element name="ACWP" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                   &lt;element name="CalendarUID" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                   &lt;element name="Notes" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                   &lt;element name="BCWS" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                   &lt;element name="BCWP" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                   &lt;element name="IsGeneric" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                   &lt;element name="IsInactive" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                   &lt;element name="IsEnterprise" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                   &lt;element name="BookingType" minOccurs="0"&gt;
                     &lt;simpleType&gt;
                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                         &lt;enumeration value="0"/&gt;
                         &lt;enumeration value="1"/&gt;
                       &lt;/restriction&gt;
                     &lt;/simpleType&gt;
                   &lt;/element&gt;
                   &lt;element name="ActualWorkProtected" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                   &lt;element name="ActualOvertimeWorkProtected" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                   &lt;element name="ActiveDirectoryGUID" minOccurs="0"&gt;
                     &lt;simpleType&gt;
                       &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
                         &lt;maxLength value="16"/&gt;
                       &lt;/restriction&gt;
                     &lt;/simpleType&gt;
                   &lt;/element&gt;
                   &lt;element name="CreationDate" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                   &lt;element name="ExtendedAttribute" maxOccurs="unbounded" minOccurs="0"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;sequence&gt;
                             &lt;element name="FieldID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="Value" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="ValueGUID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="DurationFormat" minOccurs="0"&gt;
                               &lt;simpleType&gt;
                                 &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                   &lt;enumeration value="3"/&gt;
                                   &lt;enumeration value="4"/&gt;
                                   &lt;enumeration value="5"/&gt;
                                   &lt;enumeration value="6"/&gt;
                                   &lt;enumeration value="7"/&gt;
                                   &lt;enumeration value="8"/&gt;
                                   &lt;enumeration value="9"/&gt;
                                   &lt;enumeration value="10"/&gt;
                                   &lt;enumeration value="11"/&gt;
                                   &lt;enumeration value="12"/&gt;
                                   &lt;enumeration value="19"/&gt;
                                   &lt;enumeration value="20"/&gt;
                                   &lt;enumeration value="21"/&gt;
                                   &lt;enumeration value="35"/&gt;
                                   &lt;enumeration value="36"/&gt;
                                   &lt;enumeration value="37"/&gt;
                                   &lt;enumeration value="38"/&gt;
                                   &lt;enumeration value="39"/&gt;
                                   &lt;enumeration value="40"/&gt;
                                   &lt;enumeration value="41"/&gt;
                                   &lt;enumeration value="42"/&gt;
                                   &lt;enumeration value="43"/&gt;
                                   &lt;enumeration value="44"/&gt;
                                   &lt;enumeration value="51"/&gt;
                                   &lt;enumeration value="52"/&gt;
                                   &lt;enumeration value="53"/&gt;
                                 &lt;/restriction&gt;
                               &lt;/simpleType&gt;
                             &lt;/element&gt;
                           &lt;/sequence&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                   &lt;element name="Baseline" maxOccurs="unbounded" minOccurs="0"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;sequence&gt;
                             &lt;element name="Number" type="{http://www.w3.org/2001/XMLSchema}integer"/&gt;
                             &lt;element name="Work" type="{http://www.w3.org/2001/XMLSchema}duration" minOccurs="0"/&gt;
                             &lt;element name="Cost" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="BCWS" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                             &lt;element name="BCWP" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                           &lt;/sequence&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                   &lt;element name="OutlineCode" maxOccurs="unbounded" minOccurs="0"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;sequence&gt;
                             &lt;element name="FieldID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                             &lt;element name="ValueID" type="{http://www.w3.org/2001/XMLSchema}integer" minOccurs="0"/&gt;
                             &lt;element name="ValueGUID" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                           &lt;/sequence&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                   &lt;element name="CostCenter" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                   &lt;element name="IsCostResource" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                   &lt;element name="AssnOwner" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                   &lt;element name="AssnOwnerGuid" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
                   &lt;element name="IsBudget" type="{http://www.w3.org/2001/XMLSchema}boolean" minOccurs="0"/&gt;
                   &lt;element name="AvailabilityPeriods" minOccurs="0"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;sequence&gt;
                             &lt;element name="AvailabilityPeriod" maxOccurs="unbounded" minOccurs="0"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="AvailableFrom" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                                       &lt;element name="AvailableTo" type="{http://www.w3.org/2001/XMLSchema}dateTime" minOccurs="0"/&gt;
                                       &lt;element name="AvailableUnits" type="{http://www.w3.org/2001/XMLSchema}float" minOccurs="0"/&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                           &lt;/sequence&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                   &lt;element name="Rates" minOccurs="0"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;sequence&gt;
                             &lt;element name="Rate" maxOccurs="25" minOccurs="0"&gt;
                               &lt;complexType&gt;
                                 &lt;complexContent&gt;
                                   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                                     &lt;sequence&gt;
                                       &lt;element name="RatesFrom" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
                                       &lt;element name="RatesTo" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
                                       &lt;element name="RateTable" minOccurs="0"&gt;
                                         &lt;simpleType&gt;
                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                             &lt;enumeration value="0"/&gt;
                                             &lt;enumeration value="1"/&gt;
                                             &lt;enumeration value="2"/&gt;
                                             &lt;enumeration value="3"/&gt;
                                             &lt;enumeration value="4"/&gt;
                                           &lt;/restriction&gt;
                                         &lt;/simpleType&gt;
                                       &lt;/element&gt;
                                       &lt;element name="StandardRate" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                                       &lt;element name="StandardRateFormat" minOccurs="0"&gt;
                                         &lt;simpleType&gt;
                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                             &lt;enumeration value="1"/&gt;
                                             &lt;enumeration value="2"/&gt;
                                             &lt;enumeration value="3"/&gt;
                                             &lt;enumeration value="4"/&gt;
                                             &lt;enumeration value="5"/&gt;
                                             &lt;enumeration value="7"/&gt;
                                           &lt;/restriction&gt;
                                         &lt;/simpleType&gt;
                                       &lt;/element&gt;
                                       &lt;element name="OvertimeRate" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                                       &lt;element name="OvertimeRateFormat" minOccurs="0"&gt;
                                         &lt;simpleType&gt;
                                           &lt;restriction base="{http://www.w3.org/2001/XMLSchema}integer"&gt;
                                             &lt;enumeration value="1"/&gt;
                                             &lt;enumeration value="2"/&gt;
                                             &lt;enumeration value="3"/&gt;
                                             &lt;enumeration value="4"/&gt;
                                             &lt;enumeration value="5"/&gt;
                                             &lt;enumeration value="7"/&gt;
                                           &lt;/restriction&gt;
                                         &lt;/simpleType&gt;
                                       &lt;/element&gt;
                                       &lt;element name="CostPerUse" type="{http://www.w3.org/2001/XMLSchema}decimal" minOccurs="0"/&gt;
                                     &lt;/sequence&gt;
                                   &lt;/restriction&gt;
                                 &lt;/complexContent&gt;
                               &lt;/complexType&gt;
                             &lt;/element&gt;
                           &lt;/sequence&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                   &lt;element name="TimephasedData" type="{http://schemas.microsoft.com/project}TimephasedDataType" maxOccurs="unbounded" minOccurs="0"/&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
       &lt;/sequence&gt;
     &lt;/restriction&gt;
   &lt;/complexContent&gt;
 &lt;/complexType&gt;
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.html#resource">resource</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.html#Resources--">Resources</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.html#getResource--">getResource</a></span>()</code>
<div class="block">Gets the value of the resource property.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="resource">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>resource</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource</a>&gt; resource</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Resources--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Resources</h4>
<pre>public&nbsp;Resources()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getResource--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getResource</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource</a>&gt;&nbsp;getResource()</pre>
<div class="block">Gets the value of the resource property.

 <p>
 This accessor method returns a reference to the live list,
 not a snapshot. Therefore any modification you make to the
 returned list will be present inside the Jakarta XML Binding object.
 This is why there is not a <CODE>set</CODE> method for the resource property.

 <p>
 For example, to add a new item, do as follows:
 <pre>
    getResource().add(newItem);
 </pre>


 <p>
 Objects of the following type(s) are allowed in the list
 <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources.Resource</code></a></div>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Project.Resources.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html" title="class in org.mpxj.mspdi.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html" title="class in org.mpxj.mspdi.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/mspdi/schema/Project.Resources.html" target="_top">Frames</a></li>
<li><a href="Project.Resources.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
