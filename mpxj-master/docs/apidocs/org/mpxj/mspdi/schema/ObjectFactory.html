<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ObjectFactory (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ObjectFactory (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10,"i27":10,"i28":10,"i29":10,"i30":10,"i31":10,"i32":10,"i33":10,"i34":10,"i35":10,"i36":10,"i37":10,"i38":10,"i39":10,"i40":10,"i41":10,"i42":10,"i43":10,"i44":10,"i45":10,"i46":10,"i47":10,"i48":10,"i49":10,"i50":10,"i51":10,"i52":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ObjectFactory.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/mspdi/schema/Adapter9.html" title="class in org.mpxj.mspdi.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/mspdi/schema/Project.html" title="class in org.mpxj.mspdi.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/mspdi/schema/ObjectFactory.html" target="_top">Frames</a></li>
<li><a href="ObjectFactory.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.mspdi.schema</div>
<h2 title="Class ObjectFactory" class="title">Class ObjectFactory</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.mspdi.schema.ObjectFactory</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ObjectFactory</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">This object contains factory methods for each
 Java content interface and Java element interface
 generated in the org.mpxj.mspdi.schema package.
 <p>An ObjectFactory allows you to programatically
 construct new instances of the Java representation
 for XML content. The Java representation of XML
 content can consist of schema derived interfaces
 and classes representing the binding of schema
 type definitions, element declarations and model
 groups.  Factory methods for each of these are
 provided in this class.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#ObjectFactory--">ObjectFactory</a></span>()</code>
<div class="block">Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: org.mpxj.mspdi.schema</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.html" title="class in org.mpxj.mspdi.schema">Project</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProject--">createProject</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.html" title="class in org.mpxj.mspdi.schema"><code>Project</code></a></div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>jakarta.xml.bind.JAXBElement&lt;<a href="../../../../org/mpxj/mspdi/schema/Project.html" title="class in org.mpxj.mspdi.schema">Project</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProject-org.mpxj.mspdi.schema.Project-">createProject</a></span>(<a href="../../../../org/mpxj/mspdi/schema/Project.html" title="class in org.mpxj.mspdi.schema">Project</a>&nbsp;value)</code>
<div class="block">Create an instance of <code>JAXBElement</code><code>&lt;</code><a href="../../../../org/mpxj/mspdi/schema/Project.html" title="class in org.mpxj.mspdi.schema"><code>Project</code></a><code>&gt;</code></div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.html" title="class in org.mpxj.mspdi.schema">Project.Assignments</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectAssignments--">createProjectAssignments</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.html" title="class in org.mpxj.mspdi.schema"><code>Project.Assignments</code></a></div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.Assignment.html" title="class in org.mpxj.mspdi.schema">Project.Assignments.Assignment</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectAssignmentsAssignment--">createProjectAssignmentsAssignment</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.Assignment.html" title="class in org.mpxj.mspdi.schema"><code>Project.Assignments.Assignment</code></a></div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.Assignment.Baseline.html" title="class in org.mpxj.mspdi.schema">Project.Assignments.Assignment.Baseline</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectAssignmentsAssignmentBaseline--">createProjectAssignmentsAssignmentBaseline</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.Assignment.Baseline.html" title="class in org.mpxj.mspdi.schema"><code>Project.Assignments.Assignment.Baseline</code></a></div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.Assignment.ExtendedAttribute.html" title="class in org.mpxj.mspdi.schema">Project.Assignments.Assignment.ExtendedAttribute</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectAssignmentsAssignmentExtendedAttribute--">createProjectAssignmentsAssignmentExtendedAttribute</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.Assignment.ExtendedAttribute.html" title="class in org.mpxj.mspdi.schema"><code>Project.Assignments.Assignment.ExtendedAttribute</code></a></div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.html" title="class in org.mpxj.mspdi.schema">Project.Calendars</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectCalendars--">createProjectCalendars</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars</code></a></div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectCalendarsCalendar--">createProjectCalendarsCalendar</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar</code></a></div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.Exceptions.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.Exceptions</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectCalendarsCalendarExceptions--">createProjectCalendarsCalendarExceptions</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.Exceptions.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.Exceptions</code></a></div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.Exceptions.Exception.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.Exceptions.Exception</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectCalendarsCalendarExceptionsException--">createProjectCalendarsCalendarExceptionsException</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.Exceptions.Exception.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.Exceptions.Exception</code></a></div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.Exceptions.Exception.TimePeriod.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.Exceptions.Exception.TimePeriod</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectCalendarsCalendarExceptionsExceptionTimePeriod--">createProjectCalendarsCalendarExceptionsExceptionTimePeriod</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.Exceptions.Exception.TimePeriod.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.Exceptions.Exception.TimePeriod</code></a></div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.Exceptions.Exception.WorkingTimes.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.Exceptions.Exception.WorkingTimes</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectCalendarsCalendarExceptionsExceptionWorkingTimes--">createProjectCalendarsCalendarExceptionsExceptionWorkingTimes</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.Exceptions.Exception.WorkingTimes.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.Exceptions.Exception.WorkingTimes</code></a></div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.Exceptions.Exception.WorkingTimes.WorkingTime.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.Exceptions.Exception.WorkingTimes.WorkingTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectCalendarsCalendarExceptionsExceptionWorkingTimesWorkingTime--">createProjectCalendarsCalendarExceptionsExceptionWorkingTimesWorkingTime</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.Exceptions.Exception.WorkingTimes.WorkingTime.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.Exceptions.Exception.WorkingTimes.WorkingTime</code></a></div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WeekDays.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.WeekDays</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectCalendarsCalendarWeekDays--">createProjectCalendarsCalendarWeekDays</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WeekDays.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.WeekDays</code></a></div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WeekDays.WeekDay.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.WeekDays.WeekDay</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectCalendarsCalendarWeekDaysWeekDay--">createProjectCalendarsCalendarWeekDaysWeekDay</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WeekDays.WeekDay.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.WeekDays.WeekDay</code></a></div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WeekDays.WeekDay.TimePeriod.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.WeekDays.WeekDay.TimePeriod</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectCalendarsCalendarWeekDaysWeekDayTimePeriod--">createProjectCalendarsCalendarWeekDaysWeekDayTimePeriod</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WeekDays.WeekDay.TimePeriod.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.WeekDays.WeekDay.TimePeriod</code></a></div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WeekDays.WeekDay.WorkingTimes.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.WeekDays.WeekDay.WorkingTimes</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectCalendarsCalendarWeekDaysWeekDayWorkingTimes--">createProjectCalendarsCalendarWeekDaysWeekDayWorkingTimes</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WeekDays.WeekDay.WorkingTimes.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.WeekDays.WeekDay.WorkingTimes</code></a></div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WeekDays.WeekDay.WorkingTimes.WorkingTime.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.WeekDays.WeekDay.WorkingTimes.WorkingTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectCalendarsCalendarWeekDaysWeekDayWorkingTimesWorkingTime--">createProjectCalendarsCalendarWeekDaysWeekDayWorkingTimesWorkingTime</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WeekDays.WeekDay.WorkingTimes.WorkingTime.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.WeekDays.WeekDay.WorkingTimes.WorkingTime</code></a></div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.WorkWeeks</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectCalendarsCalendarWorkWeeks--">createProjectCalendarsCalendarWorkWeeks</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.WorkWeeks</code></a></div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.WorkWeek.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.WorkWeeks.WorkWeek</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectCalendarsCalendarWorkWeeksWorkWeek--">createProjectCalendarsCalendarWorkWeeksWorkWeek</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.WorkWeek.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.WorkWeeks.WorkWeek</code></a></div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.WorkWeek.TimePeriod.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.WorkWeeks.WorkWeek.TimePeriod</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectCalendarsCalendarWorkWeeksWorkWeekTimePeriod--">createProjectCalendarsCalendarWorkWeeksWorkWeekTimePeriod</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.WorkWeek.TimePeriod.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.WorkWeeks.WorkWeek.TimePeriod</code></a></div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectCalendarsCalendarWorkWeeksWorkWeekWeekDays--">createProjectCalendarsCalendarWorkWeeksWorkWeekWeekDays</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays</code></a></div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.WeekDay.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.WeekDay</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectCalendarsCalendarWorkWeeksWorkWeekWeekDaysWeekDay--">createProjectCalendarsCalendarWorkWeeksWorkWeekWeekDaysWeekDay</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.WeekDay.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.WeekDay</code></a></div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.WeekDay.WorkingTimes.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.WeekDay.WorkingTimes</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectCalendarsCalendarWorkWeeksWorkWeekWeekDaysWeekDayWorkingTimes--">createProjectCalendarsCalendarWorkWeeksWorkWeekWeekDaysWeekDayWorkingTimes</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.WeekDay.WorkingTimes.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.WeekDay.WorkingTimes</code></a></div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.WeekDay.WorkingTimes.WorkingTime.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.WeekDay.WorkingTimes.WorkingTime</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectCalendarsCalendarWorkWeeksWorkWeekWeekDaysWeekDayWorkingTimesWorkingTime--">createProjectCalendarsCalendarWorkWeeksWorkWeekWeekDaysWeekDayWorkingTimesWorkingTime</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.WeekDay.WorkingTimes.WorkingTime.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.WeekDay.WorkingTimes.WorkingTime</code></a></div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.ExtendedAttributes.html" title="class in org.mpxj.mspdi.schema">Project.ExtendedAttributes</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectExtendedAttributes--">createProjectExtendedAttributes</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.ExtendedAttributes.html" title="class in org.mpxj.mspdi.schema"><code>Project.ExtendedAttributes</code></a></div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.ExtendedAttributes.ExtendedAttribute.html" title="class in org.mpxj.mspdi.schema">Project.ExtendedAttributes.ExtendedAttribute</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectExtendedAttributesExtendedAttribute--">createProjectExtendedAttributesExtendedAttribute</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.ExtendedAttributes.ExtendedAttribute.html" title="class in org.mpxj.mspdi.schema"><code>Project.ExtendedAttributes.ExtendedAttribute</code></a></div>
</td>
</tr>
<tr id="i27" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.ExtendedAttributes.ExtendedAttribute.ValueList.html" title="class in org.mpxj.mspdi.schema">Project.ExtendedAttributes.ExtendedAttribute.ValueList</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectExtendedAttributesExtendedAttributeValueList--">createProjectExtendedAttributesExtendedAttributeValueList</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.ExtendedAttributes.ExtendedAttribute.ValueList.html" title="class in org.mpxj.mspdi.schema"><code>Project.ExtendedAttributes.ExtendedAttribute.ValueList</code></a></div>
</td>
</tr>
<tr id="i28" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.ExtendedAttributes.ExtendedAttribute.ValueList.Value.html" title="class in org.mpxj.mspdi.schema">Project.ExtendedAttributes.ExtendedAttribute.ValueList.Value</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectExtendedAttributesExtendedAttributeValueListValue--">createProjectExtendedAttributesExtendedAttributeValueListValue</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.ExtendedAttributes.ExtendedAttribute.ValueList.Value.html" title="class in org.mpxj.mspdi.schema"><code>Project.ExtendedAttributes.ExtendedAttribute.ValueList.Value</code></a></div>
</td>
</tr>
<tr id="i29" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.html" title="class in org.mpxj.mspdi.schema">Project.OutlineCodes</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectOutlineCodes--">createProjectOutlineCodes</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.html" title="class in org.mpxj.mspdi.schema"><code>Project.OutlineCodes</code></a></div>
</td>
</tr>
<tr id="i30" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.html" title="class in org.mpxj.mspdi.schema">Project.OutlineCodes.OutlineCode</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectOutlineCodesOutlineCode--">createProjectOutlineCodesOutlineCode</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.html" title="class in org.mpxj.mspdi.schema"><code>Project.OutlineCodes.OutlineCode</code></a></div>
</td>
</tr>
<tr id="i31" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Masks.html" title="class in org.mpxj.mspdi.schema">Project.OutlineCodes.OutlineCode.Masks</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectOutlineCodesOutlineCodeMasks--">createProjectOutlineCodesOutlineCodeMasks</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Masks.html" title="class in org.mpxj.mspdi.schema"><code>Project.OutlineCodes.OutlineCode.Masks</code></a></div>
</td>
</tr>
<tr id="i32" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Masks.Mask.html" title="class in org.mpxj.mspdi.schema">Project.OutlineCodes.OutlineCode.Masks.Mask</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectOutlineCodesOutlineCodeMasksMask--">createProjectOutlineCodesOutlineCodeMasksMask</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Masks.Mask.html" title="class in org.mpxj.mspdi.schema"><code>Project.OutlineCodes.OutlineCode.Masks.Mask</code></a></div>
</td>
</tr>
<tr id="i33" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.html" title="class in org.mpxj.mspdi.schema">Project.OutlineCodes.OutlineCode.Values</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectOutlineCodesOutlineCodeValues--">createProjectOutlineCodesOutlineCodeValues</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.html" title="class in org.mpxj.mspdi.schema"><code>Project.OutlineCodes.OutlineCode.Values</code></a></div>
</td>
</tr>
<tr id="i34" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html" title="class in org.mpxj.mspdi.schema">Project.OutlineCodes.OutlineCode.Values.Value</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectOutlineCodesOutlineCodeValuesValue--">createProjectOutlineCodesOutlineCodeValuesValue</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html" title="class in org.mpxj.mspdi.schema"><code>Project.OutlineCodes.OutlineCode.Values.Value</code></a></div>
</td>
</tr>
<tr id="i35" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.html" title="class in org.mpxj.mspdi.schema">Project.Resources</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectResources--">createProjectResources</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources</code></a></div>
</td>
</tr>
<tr id="i36" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectResourcesResource--">createProjectResourcesResource</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources.Resource</code></a></div>
</td>
</tr>
<tr id="i37" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.AvailabilityPeriods.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.AvailabilityPeriods</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectResourcesResourceAvailabilityPeriods--">createProjectResourcesResourceAvailabilityPeriods</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.AvailabilityPeriods.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources.Resource.AvailabilityPeriods</code></a></div>
</td>
</tr>
<tr id="i38" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.AvailabilityPeriods.AvailabilityPeriod.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.AvailabilityPeriods.AvailabilityPeriod</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectResourcesResourceAvailabilityPeriodsAvailabilityPeriod--">createProjectResourcesResourceAvailabilityPeriodsAvailabilityPeriod</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.AvailabilityPeriods.AvailabilityPeriod.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources.Resource.AvailabilityPeriods.AvailabilityPeriod</code></a></div>
</td>
</tr>
<tr id="i39" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Baseline.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.Baseline</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectResourcesResourceBaseline--">createProjectResourcesResourceBaseline</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Baseline.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources.Resource.Baseline</code></a></div>
</td>
</tr>
<tr id="i40" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.ExtendedAttribute.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.ExtendedAttribute</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectResourcesResourceExtendedAttribute--">createProjectResourcesResourceExtendedAttribute</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.ExtendedAttribute.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources.Resource.ExtendedAttribute</code></a></div>
</td>
</tr>
<tr id="i41" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.OutlineCode.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.OutlineCode</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectResourcesResourceOutlineCode--">createProjectResourcesResourceOutlineCode</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.OutlineCode.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources.Resource.OutlineCode</code></a></div>
</td>
</tr>
<tr id="i42" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Rates.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.Rates</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectResourcesResourceRates--">createProjectResourcesResourceRates</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Rates.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources.Resource.Rates</code></a></div>
</td>
</tr>
<tr id="i43" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Rates.Rate.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.Rates.Rate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectResourcesResourceRatesRate--">createProjectResourcesResourceRatesRate</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Rates.Rate.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources.Resource.Rates.Rate</code></a></div>
</td>
</tr>
<tr id="i44" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.html" title="class in org.mpxj.mspdi.schema">Project.Tasks</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectTasks--">createProjectTasks</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.html" title="class in org.mpxj.mspdi.schema"><code>Project.Tasks</code></a></div>
</td>
</tr>
<tr id="i45" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.Task.html" title="class in org.mpxj.mspdi.schema">Project.Tasks.Task</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectTasksTask--">createProjectTasksTask</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.Task.html" title="class in org.mpxj.mspdi.schema"><code>Project.Tasks.Task</code></a></div>
</td>
</tr>
<tr id="i46" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.Task.Baseline.html" title="class in org.mpxj.mspdi.schema">Project.Tasks.Task.Baseline</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectTasksTaskBaseline--">createProjectTasksTaskBaseline</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.Task.Baseline.html" title="class in org.mpxj.mspdi.schema"><code>Project.Tasks.Task.Baseline</code></a></div>
</td>
</tr>
<tr id="i47" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.Task.ExtendedAttribute.html" title="class in org.mpxj.mspdi.schema">Project.Tasks.Task.ExtendedAttribute</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectTasksTaskExtendedAttribute--">createProjectTasksTaskExtendedAttribute</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.Task.ExtendedAttribute.html" title="class in org.mpxj.mspdi.schema"><code>Project.Tasks.Task.ExtendedAttribute</code></a></div>
</td>
</tr>
<tr id="i48" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.Task.OutlineCode.html" title="class in org.mpxj.mspdi.schema">Project.Tasks.Task.OutlineCode</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectTasksTaskOutlineCode--">createProjectTasksTaskOutlineCode</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.Task.OutlineCode.html" title="class in org.mpxj.mspdi.schema"><code>Project.Tasks.Task.OutlineCode</code></a></div>
</td>
</tr>
<tr id="i49" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.Task.PredecessorLink.html" title="class in org.mpxj.mspdi.schema">Project.Tasks.Task.PredecessorLink</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectTasksTaskPredecessorLink--">createProjectTasksTaskPredecessorLink</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.Task.PredecessorLink.html" title="class in org.mpxj.mspdi.schema"><code>Project.Tasks.Task.PredecessorLink</code></a></div>
</td>
</tr>
<tr id="i50" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.WBSMasks.html" title="class in org.mpxj.mspdi.schema">Project.WBSMasks</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectWBSMasks--">createProjectWBSMasks</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.WBSMasks.html" title="class in org.mpxj.mspdi.schema"><code>Project.WBSMasks</code></a></div>
</td>
</tr>
<tr id="i51" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/Project.WBSMasks.WBSMask.html" title="class in org.mpxj.mspdi.schema">Project.WBSMasks.WBSMask</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createProjectWBSMasksWBSMask--">createProjectWBSMasksWBSMask</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.WBSMasks.WBSMask.html" title="class in org.mpxj.mspdi.schema"><code>Project.WBSMasks.WBSMask</code></a></div>
</td>
</tr>
<tr id="i52" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/mspdi/schema/TimephasedDataType.html" title="class in org.mpxj.mspdi.schema">TimephasedDataType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/schema/ObjectFactory.html#createTimephasedDataType--">createTimephasedDataType</a></span>()</code>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/TimephasedDataType.html" title="class in org.mpxj.mspdi.schema"><code>TimephasedDataType</code></a></div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ObjectFactory--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ObjectFactory</h4>
<pre>public&nbsp;ObjectFactory()</pre>
<div class="block">Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: org.mpxj.mspdi.schema</div>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="createProject--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProject</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.html" title="class in org.mpxj.mspdi.schema">Project</a>&nbsp;createProject()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.html" title="class in org.mpxj.mspdi.schema"><code>Project</code></a></div>
</li>
</ul>
<a name="createProjectAssignments--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectAssignments</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.html" title="class in org.mpxj.mspdi.schema">Project.Assignments</a>&nbsp;createProjectAssignments()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.html" title="class in org.mpxj.mspdi.schema"><code>Project.Assignments</code></a></div>
</li>
</ul>
<a name="createProjectAssignmentsAssignment--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectAssignmentsAssignment</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.Assignment.html" title="class in org.mpxj.mspdi.schema">Project.Assignments.Assignment</a>&nbsp;createProjectAssignmentsAssignment()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.Assignment.html" title="class in org.mpxj.mspdi.schema"><code>Project.Assignments.Assignment</code></a></div>
</li>
</ul>
<a name="createProjectResources--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectResources</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.html" title="class in org.mpxj.mspdi.schema">Project.Resources</a>&nbsp;createProjectResources()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources</code></a></div>
</li>
</ul>
<a name="createProjectResourcesResource--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectResourcesResource</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource</a>&nbsp;createProjectResourcesResource()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources.Resource</code></a></div>
</li>
</ul>
<a name="createProjectResourcesResourceRates--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectResourcesResourceRates</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Rates.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.Rates</a>&nbsp;createProjectResourcesResourceRates()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Rates.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources.Resource.Rates</code></a></div>
</li>
</ul>
<a name="createProjectResourcesResourceAvailabilityPeriods--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectResourcesResourceAvailabilityPeriods</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.AvailabilityPeriods.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.AvailabilityPeriods</a>&nbsp;createProjectResourcesResourceAvailabilityPeriods()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.AvailabilityPeriods.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources.Resource.AvailabilityPeriods</code></a></div>
</li>
</ul>
<a name="createProjectTasks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectTasks</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.html" title="class in org.mpxj.mspdi.schema">Project.Tasks</a>&nbsp;createProjectTasks()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.html" title="class in org.mpxj.mspdi.schema"><code>Project.Tasks</code></a></div>
</li>
</ul>
<a name="createProjectTasksTask--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectTasksTask</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.Task.html" title="class in org.mpxj.mspdi.schema">Project.Tasks.Task</a>&nbsp;createProjectTasksTask()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.Task.html" title="class in org.mpxj.mspdi.schema"><code>Project.Tasks.Task</code></a></div>
</li>
</ul>
<a name="createProjectCalendars--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectCalendars</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.html" title="class in org.mpxj.mspdi.schema">Project.Calendars</a>&nbsp;createProjectCalendars()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars</code></a></div>
</li>
</ul>
<a name="createProjectCalendarsCalendar--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectCalendarsCalendar</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar</a>&nbsp;createProjectCalendarsCalendar()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar</code></a></div>
</li>
</ul>
<a name="createProjectCalendarsCalendarWorkWeeks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectCalendarsCalendarWorkWeeks</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.WorkWeeks</a>&nbsp;createProjectCalendarsCalendarWorkWeeks()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.WorkWeeks</code></a></div>
</li>
</ul>
<a name="createProjectCalendarsCalendarWorkWeeksWorkWeek--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectCalendarsCalendarWorkWeeksWorkWeek</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.WorkWeek.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.WorkWeeks.WorkWeek</a>&nbsp;createProjectCalendarsCalendarWorkWeeksWorkWeek()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.WorkWeek.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.WorkWeeks.WorkWeek</code></a></div>
</li>
</ul>
<a name="createProjectCalendarsCalendarWorkWeeksWorkWeekWeekDays--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectCalendarsCalendarWorkWeeksWorkWeekWeekDays</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays</a>&nbsp;createProjectCalendarsCalendarWorkWeeksWorkWeekWeekDays()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays</code></a></div>
</li>
</ul>
<a name="createProjectCalendarsCalendarWorkWeeksWorkWeekWeekDaysWeekDay--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectCalendarsCalendarWorkWeeksWorkWeekWeekDaysWeekDay</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.WeekDay.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.WeekDay</a>&nbsp;createProjectCalendarsCalendarWorkWeeksWorkWeekWeekDaysWeekDay()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.WeekDay.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.WeekDay</code></a></div>
</li>
</ul>
<a name="createProjectCalendarsCalendarWorkWeeksWorkWeekWeekDaysWeekDayWorkingTimes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectCalendarsCalendarWorkWeeksWorkWeekWeekDaysWeekDayWorkingTimes</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.WeekDay.WorkingTimes.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.WeekDay.WorkingTimes</a>&nbsp;createProjectCalendarsCalendarWorkWeeksWorkWeekWeekDaysWeekDayWorkingTimes()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.WeekDay.WorkingTimes.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.WeekDay.WorkingTimes</code></a></div>
</li>
</ul>
<a name="createProjectCalendarsCalendarExceptions--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectCalendarsCalendarExceptions</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.Exceptions.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.Exceptions</a>&nbsp;createProjectCalendarsCalendarExceptions()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.Exceptions.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.Exceptions</code></a></div>
</li>
</ul>
<a name="createProjectCalendarsCalendarExceptionsException--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectCalendarsCalendarExceptionsException</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.Exceptions.Exception.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.Exceptions.Exception</a>&nbsp;createProjectCalendarsCalendarExceptionsException()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.Exceptions.Exception.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.Exceptions.Exception</code></a></div>
</li>
</ul>
<a name="createProjectCalendarsCalendarExceptionsExceptionWorkingTimes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectCalendarsCalendarExceptionsExceptionWorkingTimes</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.Exceptions.Exception.WorkingTimes.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.Exceptions.Exception.WorkingTimes</a>&nbsp;createProjectCalendarsCalendarExceptionsExceptionWorkingTimes()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.Exceptions.Exception.WorkingTimes.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.Exceptions.Exception.WorkingTimes</code></a></div>
</li>
</ul>
<a name="createProjectCalendarsCalendarWeekDays--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectCalendarsCalendarWeekDays</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WeekDays.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.WeekDays</a>&nbsp;createProjectCalendarsCalendarWeekDays()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WeekDays.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.WeekDays</code></a></div>
</li>
</ul>
<a name="createProjectCalendarsCalendarWeekDaysWeekDay--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectCalendarsCalendarWeekDaysWeekDay</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WeekDays.WeekDay.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.WeekDays.WeekDay</a>&nbsp;createProjectCalendarsCalendarWeekDaysWeekDay()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WeekDays.WeekDay.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.WeekDays.WeekDay</code></a></div>
</li>
</ul>
<a name="createProjectCalendarsCalendarWeekDaysWeekDayWorkingTimes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectCalendarsCalendarWeekDaysWeekDayWorkingTimes</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WeekDays.WeekDay.WorkingTimes.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.WeekDays.WeekDay.WorkingTimes</a>&nbsp;createProjectCalendarsCalendarWeekDaysWeekDayWorkingTimes()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WeekDays.WeekDay.WorkingTimes.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.WeekDays.WeekDay.WorkingTimes</code></a></div>
</li>
</ul>
<a name="createProjectExtendedAttributes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectExtendedAttributes</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.ExtendedAttributes.html" title="class in org.mpxj.mspdi.schema">Project.ExtendedAttributes</a>&nbsp;createProjectExtendedAttributes()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.ExtendedAttributes.html" title="class in org.mpxj.mspdi.schema"><code>Project.ExtendedAttributes</code></a></div>
</li>
</ul>
<a name="createProjectExtendedAttributesExtendedAttribute--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectExtendedAttributesExtendedAttribute</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.ExtendedAttributes.ExtendedAttribute.html" title="class in org.mpxj.mspdi.schema">Project.ExtendedAttributes.ExtendedAttribute</a>&nbsp;createProjectExtendedAttributesExtendedAttribute()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.ExtendedAttributes.ExtendedAttribute.html" title="class in org.mpxj.mspdi.schema"><code>Project.ExtendedAttributes.ExtendedAttribute</code></a></div>
</li>
</ul>
<a name="createProjectExtendedAttributesExtendedAttributeValueList--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectExtendedAttributesExtendedAttributeValueList</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.ExtendedAttributes.ExtendedAttribute.ValueList.html" title="class in org.mpxj.mspdi.schema">Project.ExtendedAttributes.ExtendedAttribute.ValueList</a>&nbsp;createProjectExtendedAttributesExtendedAttributeValueList()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.ExtendedAttributes.ExtendedAttribute.ValueList.html" title="class in org.mpxj.mspdi.schema"><code>Project.ExtendedAttributes.ExtendedAttribute.ValueList</code></a></div>
</li>
</ul>
<a name="createProjectWBSMasks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectWBSMasks</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.WBSMasks.html" title="class in org.mpxj.mspdi.schema">Project.WBSMasks</a>&nbsp;createProjectWBSMasks()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.WBSMasks.html" title="class in org.mpxj.mspdi.schema"><code>Project.WBSMasks</code></a></div>
</li>
</ul>
<a name="createProjectOutlineCodes--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectOutlineCodes</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.html" title="class in org.mpxj.mspdi.schema">Project.OutlineCodes</a>&nbsp;createProjectOutlineCodes()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.html" title="class in org.mpxj.mspdi.schema"><code>Project.OutlineCodes</code></a></div>
</li>
</ul>
<a name="createProjectOutlineCodesOutlineCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectOutlineCodesOutlineCode</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.html" title="class in org.mpxj.mspdi.schema">Project.OutlineCodes.OutlineCode</a>&nbsp;createProjectOutlineCodesOutlineCode()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.html" title="class in org.mpxj.mspdi.schema"><code>Project.OutlineCodes.OutlineCode</code></a></div>
</li>
</ul>
<a name="createProjectOutlineCodesOutlineCodeMasks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectOutlineCodesOutlineCodeMasks</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Masks.html" title="class in org.mpxj.mspdi.schema">Project.OutlineCodes.OutlineCode.Masks</a>&nbsp;createProjectOutlineCodesOutlineCodeMasks()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Masks.html" title="class in org.mpxj.mspdi.schema"><code>Project.OutlineCodes.OutlineCode.Masks</code></a></div>
</li>
</ul>
<a name="createProjectOutlineCodesOutlineCodeValues--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectOutlineCodesOutlineCodeValues</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.html" title="class in org.mpxj.mspdi.schema">Project.OutlineCodes.OutlineCode.Values</a>&nbsp;createProjectOutlineCodesOutlineCodeValues()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.html" title="class in org.mpxj.mspdi.schema"><code>Project.OutlineCodes.OutlineCode.Values</code></a></div>
</li>
</ul>
<a name="createTimephasedDataType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createTimephasedDataType</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/TimephasedDataType.html" title="class in org.mpxj.mspdi.schema">TimephasedDataType</a>&nbsp;createTimephasedDataType()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/TimephasedDataType.html" title="class in org.mpxj.mspdi.schema"><code>TimephasedDataType</code></a></div>
</li>
</ul>
<a name="createProjectAssignmentsAssignmentExtendedAttribute--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectAssignmentsAssignmentExtendedAttribute</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.Assignment.ExtendedAttribute.html" title="class in org.mpxj.mspdi.schema">Project.Assignments.Assignment.ExtendedAttribute</a>&nbsp;createProjectAssignmentsAssignmentExtendedAttribute()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.Assignment.ExtendedAttribute.html" title="class in org.mpxj.mspdi.schema"><code>Project.Assignments.Assignment.ExtendedAttribute</code></a></div>
</li>
</ul>
<a name="createProjectAssignmentsAssignmentBaseline--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectAssignmentsAssignmentBaseline</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.Assignment.Baseline.html" title="class in org.mpxj.mspdi.schema">Project.Assignments.Assignment.Baseline</a>&nbsp;createProjectAssignmentsAssignmentBaseline()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Assignments.Assignment.Baseline.html" title="class in org.mpxj.mspdi.schema"><code>Project.Assignments.Assignment.Baseline</code></a></div>
</li>
</ul>
<a name="createProjectResourcesResourceExtendedAttribute--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectResourcesResourceExtendedAttribute</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.ExtendedAttribute.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.ExtendedAttribute</a>&nbsp;createProjectResourcesResourceExtendedAttribute()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.ExtendedAttribute.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources.Resource.ExtendedAttribute</code></a></div>
</li>
</ul>
<a name="createProjectResourcesResourceBaseline--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectResourcesResourceBaseline</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Baseline.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.Baseline</a>&nbsp;createProjectResourcesResourceBaseline()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Baseline.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources.Resource.Baseline</code></a></div>
</li>
</ul>
<a name="createProjectResourcesResourceOutlineCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectResourcesResourceOutlineCode</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.OutlineCode.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.OutlineCode</a>&nbsp;createProjectResourcesResourceOutlineCode()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.OutlineCode.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources.Resource.OutlineCode</code></a></div>
</li>
</ul>
<a name="createProjectResourcesResourceRatesRate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectResourcesResourceRatesRate</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Rates.Rate.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.Rates.Rate</a>&nbsp;createProjectResourcesResourceRatesRate()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.Rates.Rate.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources.Resource.Rates.Rate</code></a></div>
</li>
</ul>
<a name="createProjectResourcesResourceAvailabilityPeriodsAvailabilityPeriod--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectResourcesResourceAvailabilityPeriodsAvailabilityPeriod</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.AvailabilityPeriods.AvailabilityPeriod.html" title="class in org.mpxj.mspdi.schema">Project.Resources.Resource.AvailabilityPeriods.AvailabilityPeriod</a>&nbsp;createProjectResourcesResourceAvailabilityPeriodsAvailabilityPeriod()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Resources.Resource.AvailabilityPeriods.AvailabilityPeriod.html" title="class in org.mpxj.mspdi.schema"><code>Project.Resources.Resource.AvailabilityPeriods.AvailabilityPeriod</code></a></div>
</li>
</ul>
<a name="createProjectTasksTaskPredecessorLink--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectTasksTaskPredecessorLink</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.Task.PredecessorLink.html" title="class in org.mpxj.mspdi.schema">Project.Tasks.Task.PredecessorLink</a>&nbsp;createProjectTasksTaskPredecessorLink()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.Task.PredecessorLink.html" title="class in org.mpxj.mspdi.schema"><code>Project.Tasks.Task.PredecessorLink</code></a></div>
</li>
</ul>
<a name="createProjectTasksTaskExtendedAttribute--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectTasksTaskExtendedAttribute</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.Task.ExtendedAttribute.html" title="class in org.mpxj.mspdi.schema">Project.Tasks.Task.ExtendedAttribute</a>&nbsp;createProjectTasksTaskExtendedAttribute()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.Task.ExtendedAttribute.html" title="class in org.mpxj.mspdi.schema"><code>Project.Tasks.Task.ExtendedAttribute</code></a></div>
</li>
</ul>
<a name="createProjectTasksTaskBaseline--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectTasksTaskBaseline</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.Task.Baseline.html" title="class in org.mpxj.mspdi.schema">Project.Tasks.Task.Baseline</a>&nbsp;createProjectTasksTaskBaseline()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.Task.Baseline.html" title="class in org.mpxj.mspdi.schema"><code>Project.Tasks.Task.Baseline</code></a></div>
</li>
</ul>
<a name="createProjectTasksTaskOutlineCode--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectTasksTaskOutlineCode</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.Task.OutlineCode.html" title="class in org.mpxj.mspdi.schema">Project.Tasks.Task.OutlineCode</a>&nbsp;createProjectTasksTaskOutlineCode()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Tasks.Task.OutlineCode.html" title="class in org.mpxj.mspdi.schema"><code>Project.Tasks.Task.OutlineCode</code></a></div>
</li>
</ul>
<a name="createProjectCalendarsCalendarWorkWeeksWorkWeekTimePeriod--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectCalendarsCalendarWorkWeeksWorkWeekTimePeriod</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.WorkWeek.TimePeriod.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.WorkWeeks.WorkWeek.TimePeriod</a>&nbsp;createProjectCalendarsCalendarWorkWeeksWorkWeekTimePeriod()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.WorkWeek.TimePeriod.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.WorkWeeks.WorkWeek.TimePeriod</code></a></div>
</li>
</ul>
<a name="createProjectCalendarsCalendarWorkWeeksWorkWeekWeekDaysWeekDayWorkingTimesWorkingTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectCalendarsCalendarWorkWeeksWorkWeekWeekDaysWeekDayWorkingTimesWorkingTime</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.WeekDay.WorkingTimes.WorkingTime.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.WeekDay.WorkingTimes.WorkingTime</a>&nbsp;createProjectCalendarsCalendarWorkWeeksWorkWeekWeekDaysWeekDayWorkingTimesWorkingTime()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.WeekDay.WorkingTimes.WorkingTime.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.WorkWeeks.WorkWeek.WeekDays.WeekDay.WorkingTimes.WorkingTime</code></a></div>
</li>
</ul>
<a name="createProjectCalendarsCalendarExceptionsExceptionTimePeriod--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectCalendarsCalendarExceptionsExceptionTimePeriod</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.Exceptions.Exception.TimePeriod.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.Exceptions.Exception.TimePeriod</a>&nbsp;createProjectCalendarsCalendarExceptionsExceptionTimePeriod()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.Exceptions.Exception.TimePeriod.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.Exceptions.Exception.TimePeriod</code></a></div>
</li>
</ul>
<a name="createProjectCalendarsCalendarExceptionsExceptionWorkingTimesWorkingTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectCalendarsCalendarExceptionsExceptionWorkingTimesWorkingTime</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.Exceptions.Exception.WorkingTimes.WorkingTime.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.Exceptions.Exception.WorkingTimes.WorkingTime</a>&nbsp;createProjectCalendarsCalendarExceptionsExceptionWorkingTimesWorkingTime()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.Exceptions.Exception.WorkingTimes.WorkingTime.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.Exceptions.Exception.WorkingTimes.WorkingTime</code></a></div>
</li>
</ul>
<a name="createProjectCalendarsCalendarWeekDaysWeekDayTimePeriod--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectCalendarsCalendarWeekDaysWeekDayTimePeriod</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WeekDays.WeekDay.TimePeriod.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.WeekDays.WeekDay.TimePeriod</a>&nbsp;createProjectCalendarsCalendarWeekDaysWeekDayTimePeriod()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WeekDays.WeekDay.TimePeriod.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.WeekDays.WeekDay.TimePeriod</code></a></div>
</li>
</ul>
<a name="createProjectCalendarsCalendarWeekDaysWeekDayWorkingTimesWorkingTime--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectCalendarsCalendarWeekDaysWeekDayWorkingTimesWorkingTime</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WeekDays.WeekDay.WorkingTimes.WorkingTime.html" title="class in org.mpxj.mspdi.schema">Project.Calendars.Calendar.WeekDays.WeekDay.WorkingTimes.WorkingTime</a>&nbsp;createProjectCalendarsCalendarWeekDaysWeekDayWorkingTimesWorkingTime()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.Calendars.Calendar.WeekDays.WeekDay.WorkingTimes.WorkingTime.html" title="class in org.mpxj.mspdi.schema"><code>Project.Calendars.Calendar.WeekDays.WeekDay.WorkingTimes.WorkingTime</code></a></div>
</li>
</ul>
<a name="createProjectExtendedAttributesExtendedAttributeValueListValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectExtendedAttributesExtendedAttributeValueListValue</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.ExtendedAttributes.ExtendedAttribute.ValueList.Value.html" title="class in org.mpxj.mspdi.schema">Project.ExtendedAttributes.ExtendedAttribute.ValueList.Value</a>&nbsp;createProjectExtendedAttributesExtendedAttributeValueListValue()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.ExtendedAttributes.ExtendedAttribute.ValueList.Value.html" title="class in org.mpxj.mspdi.schema"><code>Project.ExtendedAttributes.ExtendedAttribute.ValueList.Value</code></a></div>
</li>
</ul>
<a name="createProjectWBSMasksWBSMask--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectWBSMasksWBSMask</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.WBSMasks.WBSMask.html" title="class in org.mpxj.mspdi.schema">Project.WBSMasks.WBSMask</a>&nbsp;createProjectWBSMasksWBSMask()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.WBSMasks.WBSMask.html" title="class in org.mpxj.mspdi.schema"><code>Project.WBSMasks.WBSMask</code></a></div>
</li>
</ul>
<a name="createProjectOutlineCodesOutlineCodeMasksMask--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectOutlineCodesOutlineCodeMasksMask</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Masks.Mask.html" title="class in org.mpxj.mspdi.schema">Project.OutlineCodes.OutlineCode.Masks.Mask</a>&nbsp;createProjectOutlineCodesOutlineCodeMasksMask()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Masks.Mask.html" title="class in org.mpxj.mspdi.schema"><code>Project.OutlineCodes.OutlineCode.Masks.Mask</code></a></div>
</li>
</ul>
<a name="createProjectOutlineCodesOutlineCodeValuesValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>createProjectOutlineCodesOutlineCodeValuesValue</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html" title="class in org.mpxj.mspdi.schema">Project.OutlineCodes.OutlineCode.Values.Value</a>&nbsp;createProjectOutlineCodesOutlineCodeValuesValue()</pre>
<div class="block">Create an instance of <a href="../../../../org/mpxj/mspdi/schema/Project.OutlineCodes.OutlineCode.Values.Value.html" title="class in org.mpxj.mspdi.schema"><code>Project.OutlineCodes.OutlineCode.Values.Value</code></a></div>
</li>
</ul>
<a name="createProject-org.mpxj.mspdi.schema.Project-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>createProject</h4>
<pre>public&nbsp;jakarta.xml.bind.JAXBElement&lt;<a href="../../../../org/mpxj/mspdi/schema/Project.html" title="class in org.mpxj.mspdi.schema">Project</a>&gt;&nbsp;createProject(<a href="../../../../org/mpxj/mspdi/schema/Project.html" title="class in org.mpxj.mspdi.schema">Project</a>&nbsp;value)</pre>
<div class="block">Create an instance of <code>JAXBElement</code><code>&lt;</code><a href="../../../../org/mpxj/mspdi/schema/Project.html" title="class in org.mpxj.mspdi.schema"><code>Project</code></a><code>&gt;</code></div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - Java instance representing xml element's value.</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>the new instance of <code>JAXBElement</code><code>&lt;</code><a href="../../../../org/mpxj/mspdi/schema/Project.html" title="class in org.mpxj.mspdi.schema"><code>Project</code></a><code>&gt;</code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ObjectFactory.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/mspdi/schema/Adapter9.html" title="class in org.mpxj.mspdi.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/mspdi/schema/Project.html" title="class in org.mpxj.mspdi.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/mspdi/schema/ObjectFactory.html" target="_top">Frames</a></li>
<li><a href="ObjectFactory.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
