<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ByteArrayHelper (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ByteArrayHelper (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9,"i4":9,"i5":9,"i6":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ByteArrayHelper.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/mpxj/common/ByteArray.html" title="class in org.mpxj.common"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/mpxj/common/CharsetHelper.html" title="class in org.mpxj.common"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/common/ByteArrayHelper.html" target="_top">Frames</a></li>
<li><a href="ByteArrayHelper.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.common</div>
<h2 title="Class ByteArrayHelper" class="title">Class ByteArrayHelper</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.common.ByteArrayHelper</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">ByteArrayHelper</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">Helper methods for working with byte arrays.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/common/ByteArrayHelper.html#ByteArrayHelper--">ByteArrayHelper</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/common/ByteArrayHelper.html#getInt-byte:A-int-">getInt</a></span>(byte[]&nbsp;data,
      int&nbsp;offset)</code>
<div class="block">This method reads a four byte integer from the input array.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static long</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/common/ByteArrayHelper.html#getLong-byte:A-int-">getLong</a></span>(byte[]&nbsp;data,
       int&nbsp;offset)</code>
<div class="block">This method reads an eight byte integer from the input array.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/common/ByteArrayHelper.html#getShort-byte:A-int-">getShort</a></span>(byte[]&nbsp;data,
        int&nbsp;offset)</code>
<div class="block">This method reads a two byte integer from the input array.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/common/ByteArrayHelper.html#hexdump-byte:A-boolean-">hexdump</a></span>(byte[]&nbsp;buffer,
       boolean&nbsp;ascii)</code>
<div class="block">This method generates a formatted version of the data contained
 in a byte array.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/common/ByteArrayHelper.html#hexdump-byte:A-boolean-int-java.lang.String-">hexdump</a></span>(byte[]&nbsp;buffer,
       boolean&nbsp;ascii,
       int&nbsp;columns,
       <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;prefix)</code>
<div class="block">This method generates a formatted version of the data contained
 in a byte array.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/common/ByteArrayHelper.html#hexdump-byte:A-int-int-boolean-">hexdump</a></span>(byte[]&nbsp;buffer,
       int&nbsp;offset,
       int&nbsp;length,
       boolean&nbsp;ascii)</code>
<div class="block">This method generates a formatted version of the data contained
 in a byte array.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/common/ByteArrayHelper.html#hexdump-byte:A-int-int-boolean-int-java.lang.String-">hexdump</a></span>(byte[]&nbsp;buffer,
       int&nbsp;offset,
       int&nbsp;length,
       boolean&nbsp;ascii,
       int&nbsp;columns,
       <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;prefix)</code>
<div class="block">This method generates a formatted version of the data contained
 in a byte array.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ByteArrayHelper--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ByteArrayHelper</h4>
<pre>public&nbsp;ByteArrayHelper()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getShort-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getShort</h4>
<pre>public static final&nbsp;int&nbsp;getShort(byte[]&nbsp;data,
                                 int&nbsp;offset)</pre>
<div class="block">This method reads a two byte integer from the input array.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>data</code> - the input array</dd>
<dd><code>offset</code> - offset of integer data in the array</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>integer value</dd>
</dl>
</li>
</ul>
<a name="getInt-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getInt</h4>
<pre>public static final&nbsp;int&nbsp;getInt(byte[]&nbsp;data,
                               int&nbsp;offset)</pre>
<div class="block">This method reads a four byte integer from the input array.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>data</code> - the input array</dd>
<dd><code>offset</code> - offset of integer data in the array</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>integer value</dd>
</dl>
</li>
</ul>
<a name="getLong-byte:A-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getLong</h4>
<pre>public static final&nbsp;long&nbsp;getLong(byte[]&nbsp;data,
                                 int&nbsp;offset)</pre>
<div class="block">This method reads an eight byte integer from the input array.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>data</code> - the input array</dd>
<dd><code>offset</code> - offset of integer data in the array</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>integer value</dd>
</dl>
</li>
</ul>
<a name="hexdump-byte:A-int-int-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hexdump</h4>
<pre>public static final&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;hexdump(byte[]&nbsp;buffer,
                                   int&nbsp;offset,
                                   int&nbsp;length,
                                   boolean&nbsp;ascii)</pre>
<div class="block">This method generates a formatted version of the data contained
 in a byte array. The data is written both in hex, and as ASCII
 characters.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>buffer</code> - data to be displayed</dd>
<dd><code>offset</code> - offset of start of data to be displayed</dd>
<dd><code>length</code> - length of data to be displayed</dd>
<dd><code>ascii</code> - flag indicating whether ASCII equivalent chars should also be displayed</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>formatted string</dd>
</dl>
</li>
</ul>
<a name="hexdump-byte:A-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hexdump</h4>
<pre>public static final&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;hexdump(byte[]&nbsp;buffer,
                                   boolean&nbsp;ascii)</pre>
<div class="block">This method generates a formatted version of the data contained
 in a byte array. The data is written both in hex, and as ASCII
 characters.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>buffer</code> - data to be displayed</dd>
<dd><code>ascii</code> - flag indicating whether ASCII equivalent chars should also be displayed</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>formatted string</dd>
</dl>
</li>
</ul>
<a name="hexdump-byte:A-boolean-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>hexdump</h4>
<pre>public static final&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;hexdump(byte[]&nbsp;buffer,
                                   boolean&nbsp;ascii,
                                   int&nbsp;columns,
                                   <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;prefix)</pre>
<div class="block">This method generates a formatted version of the data contained
 in a byte array. The data is written both in hex, and as ASCII
 characters. The data is organised into fixed width columns.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>buffer</code> - data to be displayed</dd>
<dd><code>ascii</code> - flag indicating whether ASCII equivalent chars should also be displayed</dd>
<dd><code>columns</code> - number of columns</dd>
<dd><code>prefix</code> - prefix to be added before the start of the data</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>formatted string</dd>
</dl>
</li>
</ul>
<a name="hexdump-byte:A-int-int-boolean-int-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>hexdump</h4>
<pre>public static final&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;hexdump(byte[]&nbsp;buffer,
                                   int&nbsp;offset,
                                   int&nbsp;length,
                                   boolean&nbsp;ascii,
                                   int&nbsp;columns,
                                   <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;prefix)</pre>
<div class="block">This method generates a formatted version of the data contained
 in a byte array. The data is written both in hex, and as ASCII
 characters. The data is organised into fixed width columns.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>buffer</code> - data to be displayed</dd>
<dd><code>offset</code> - offset into buffer</dd>
<dd><code>length</code> - number of bytes to display</dd>
<dd><code>ascii</code> - flag indicating whether ASCII equivalent chars should also be displayed</dd>
<dd><code>columns</code> - number of columns</dd>
<dd><code>prefix</code> - prefix to be added before the start of the data</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>formatted string</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ByteArrayHelper.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/mpxj/common/ByteArray.html" title="class in org.mpxj.common"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/mpxj/common/CharsetHelper.html" title="class in org.mpxj.common"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/common/ByteArrayHelper.html" target="_top">Frames</a></li>
<li><a href="ByteArrayHelper.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
