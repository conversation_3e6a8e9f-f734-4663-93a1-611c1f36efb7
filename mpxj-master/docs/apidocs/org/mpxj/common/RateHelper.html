<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>RateHelper (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="RateHelper (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":9,"i1":9,"i2":9,"i3":9};
var tabs = {65535:["t0","All Methods"],1:["t1","Static Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RateHelper.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/mpxj/common/ProjectFieldLists.html" title="class in org.mpxj.common"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/mpxj/common/ReaderTokenizer.html" title="class in org.mpxj.common"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/common/RateHelper.html" target="_top">Frames</a></li>
<li><a href="RateHelper.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.common</div>
<h2 title="Class RateHelper" class="title">Class RateHelper</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.common.RateHelper</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">RateHelper</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">Utility method for working with Rates.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/common/RateHelper.html#RateHelper--">RateHelper</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t1" class="tableTab"><span><a href="javascript:show(1);">Static Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/common/RateHelper.html#convertFromHours-org.mpxj.TimeUnitDefaultsContainer-java.math.BigDecimal-org.mpxj.TimeUnit-">convertFromHours</a></span>(<a href="../../../org/mpxj/TimeUnitDefaultsContainer.html" title="interface in org.mpxj">TimeUnitDefaultsContainer</a>&nbsp;defaults,
                <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value,
                <a href="../../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;targetUnits)</code>
<div class="block">Convert a rate from amount per hour to an amount per target unit.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/common/RateHelper.html#convertFromHours-org.mpxj.TimeUnitDefaultsContainer-double-org.mpxj.TimeUnit-">convertFromHours</a></span>(<a href="../../../org/mpxj/TimeUnitDefaultsContainer.html" title="interface in org.mpxj">TimeUnitDefaultsContainer</a>&nbsp;defaults,
                double&nbsp;value,
                <a href="../../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;targetUnits)</code>
<div class="block">Convert a rate from amount per hour to an amount per target unit.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/common/RateHelper.html#convertFromHours-org.mpxj.TimeUnitDefaultsContainer-org.mpxj.Rate-org.mpxj.TimeUnit-">convertFromHours</a></span>(<a href="../../../org/mpxj/TimeUnitDefaultsContainer.html" title="interface in org.mpxj">TimeUnitDefaultsContainer</a>&nbsp;defaults,
                <a href="../../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a>&nbsp;rate,
                <a href="../../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;targetUnits)</code>
<div class="block">Convert a rate from amount per hour to an amount per target unit.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>static double</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/common/RateHelper.html#convertToHours-org.mpxj.TimeUnitDefaultsContainer-org.mpxj.Rate-">convertToHours</a></span>(<a href="../../../org/mpxj/TimeUnitDefaultsContainer.html" title="interface in org.mpxj">TimeUnitDefaultsContainer</a>&nbsp;defaults,
              <a href="../../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a>&nbsp;rate)</code>
<div class="block">Convert a rate to hours.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="RateHelper--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>RateHelper</h4>
<pre>public&nbsp;RateHelper()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="convertToHours-org.mpxj.TimeUnitDefaultsContainer-org.mpxj.Rate-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertToHours</h4>
<pre>public static&nbsp;double&nbsp;convertToHours(<a href="../../../org/mpxj/TimeUnitDefaultsContainer.html" title="interface in org.mpxj">TimeUnitDefaultsContainer</a>&nbsp;defaults,
                                    <a href="../../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a>&nbsp;rate)</pre>
<div class="block">Convert a rate to hours.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>defaults</code> - defaults used for conversion</dd>
<dd><code>rate</code> - rate to convert</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>converted rate</dd>
</dl>
</li>
</ul>
<a name="convertFromHours-org.mpxj.TimeUnitDefaultsContainer-org.mpxj.Rate-org.mpxj.TimeUnit-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertFromHours</h4>
<pre>public static&nbsp;<a href="../../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a>&nbsp;convertFromHours(<a href="../../../org/mpxj/TimeUnitDefaultsContainer.html" title="interface in org.mpxj">TimeUnitDefaultsContainer</a>&nbsp;defaults,
                                    <a href="../../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a>&nbsp;rate,
                                    <a href="../../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;targetUnits)</pre>
<div class="block">Convert a rate from amount per hour to an amount per target unit.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>defaults</code> - defaults used for conversion</dd>
<dd><code>rate</code> - rate to convert</dd>
<dd><code>targetUnits</code> - required units</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>new Rate instance</dd>
</dl>
</li>
</ul>
<a name="convertFromHours-org.mpxj.TimeUnitDefaultsContainer-java.math.BigDecimal-org.mpxj.TimeUnit-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>convertFromHours</h4>
<pre>public static&nbsp;<a href="../../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a>&nbsp;convertFromHours(<a href="../../../org/mpxj/TimeUnitDefaultsContainer.html" title="interface in org.mpxj">TimeUnitDefaultsContainer</a>&nbsp;defaults,
                                    <a href="https://docs.oracle.com/javase/8/docs/api/java/math/BigDecimal.html?is-external=true" title="class or interface in java.math">BigDecimal</a>&nbsp;value,
                                    <a href="../../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;targetUnits)</pre>
<div class="block">Convert a rate from amount per hour to an amount per target unit.
 Handles rounding in a way which provides better compatibility with MSPDI files.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>defaults</code> - defaults used for conversion</dd>
<dd><code>value</code> - rate to convert</dd>
<dd><code>targetUnits</code> - required units</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>new Rate instance</dd>
</dl>
</li>
</ul>
<a name="convertFromHours-org.mpxj.TimeUnitDefaultsContainer-double-org.mpxj.TimeUnit-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>convertFromHours</h4>
<pre>public static&nbsp;<a href="../../../org/mpxj/Rate.html" title="class in org.mpxj">Rate</a>&nbsp;convertFromHours(<a href="../../../org/mpxj/TimeUnitDefaultsContainer.html" title="interface in org.mpxj">TimeUnitDefaultsContainer</a>&nbsp;defaults,
                                    double&nbsp;value,
                                    <a href="../../../org/mpxj/TimeUnit.html" title="enum in org.mpxj">TimeUnit</a>&nbsp;targetUnits)</pre>
<div class="block">Convert a rate from amount per hour to an amount per target unit.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>defaults</code> - defaults used for conversion</dd>
<dd><code>value</code> - rate to convert</dd>
<dd><code>targetUnits</code> - required units</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>new Rate instance</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/RateHelper.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/mpxj/common/ProjectFieldLists.html" title="class in org.mpxj.common"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/mpxj/common/ReaderTokenizer.html" title="class in org.mpxj.common"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/common/RateHelper.html" target="_top">Frames</a></li>
<li><a href="RateHelper.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
