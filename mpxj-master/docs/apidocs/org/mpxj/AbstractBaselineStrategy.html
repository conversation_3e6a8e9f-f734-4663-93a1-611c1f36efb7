<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AbstractBaselineStrategy (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AbstractBaselineStrategy (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/AbstractBaselineStrategy.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../org/mpxj/AbstractFieldContainer.html" title="class in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/AbstractBaselineStrategy.html" target="_top">Frames</a></li>
<li><a href="AbstractBaselineStrategy.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj</div>
<h2 title="Class AbstractBaselineStrategy" class="title">Class AbstractBaselineStrategy</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.AbstractBaselineStrategy</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../org/mpxj/BaselineStrategy.html" title="interface in org.mpxj">BaselineStrategy</a></dd>
</dl>
<dl>
<dt>Direct Known Subclasses:</dt>
<dd><a href="../../org/mpxj/asta/AstaBaselineStrategy.html" title="class in org.mpxj.asta">AstaBaselineStrategy</a>, <a href="../../org/mpxj/DefaultBaselineStrategy.html" title="class in org.mpxj">DefaultBaselineStrategy</a>, <a href="../../org/mpxj/primavera/PrimaveraBaselineStrategy.html" title="class in org.mpxj.primavera">PrimaveraBaselineStrategy</a></dd>
</dl>
<hr>
<br>
<pre>public abstract class <span class="typeNameLabel">AbstractBaselineStrategy</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>
implements <a href="../../org/mpxj/BaselineStrategy.html" title="interface in org.mpxj">BaselineStrategy</a></pre>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/AbstractBaselineStrategy.html#AbstractBaselineStrategy--">AbstractBaselineStrategy</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/AbstractBaselineStrategy.html#clearBaseline-org.mpxj.ProjectFile-int-">clearBaseline</a></span>(<a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
             int&nbsp;index)</code>
<div class="block">Clear the requested baseline for the supplied project.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>protected <a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/AbstractBaselineStrategy.html#getBaselineFields-int-">getBaselineFields</a></span>(int&nbsp;index)</code>
<div class="block">Determines the set of baseline fields to populate.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/AbstractBaselineStrategy.html#getKeyForTask-org.mpxj.Task-">getKeyForTask</a></span>(<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;task)</code>
<div class="block">This method is used to generate the key which connect tasks from the
 current and baseline schedules.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>protected <a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a>[]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/AbstractBaselineStrategy.html#getSourceFields--">getSourceFields</a></span>()</code>
<div class="block">Fields from which values are retrieved in the baseline schedule
 before being applied as baseline attributes to the main schedule.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/AbstractBaselineStrategy.html#populateBaseline-org.mpxj.ProjectFile-org.mpxj.ProjectFile-int-">populateBaseline</a></span>(<a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
                <a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;baseline,
                int&nbsp;index)</code>
<div class="block">Use the supplied baseline project to set the baselineN cost, duration, finish,
 fixed cost accrual, fixed cost, start and work attributes for the tasks
 in the supplied project.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="AbstractBaselineStrategy--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>AbstractBaselineStrategy</h4>
<pre>public&nbsp;AbstractBaselineStrategy()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="clearBaseline-org.mpxj.ProjectFile-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>clearBaseline</h4>
<pre>public&nbsp;void&nbsp;clearBaseline(<a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
                          int&nbsp;index)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../org/mpxj/BaselineStrategy.html#clearBaseline-org.mpxj.ProjectFile-int-">BaselineStrategy</a></code></span></div>
<div class="block">Clear the requested baseline for the supplied project.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/BaselineStrategy.html#clearBaseline-org.mpxj.ProjectFile-int-">clearBaseline</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/BaselineStrategy.html" title="interface in org.mpxj">BaselineStrategy</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>project</code> - target project</dd>
<dd><code>index</code> - baseline to populate (0-10)</dd>
</dl>
</li>
</ul>
<a name="populateBaseline-org.mpxj.ProjectFile-org.mpxj.ProjectFile-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>populateBaseline</h4>
<pre>public&nbsp;void&nbsp;populateBaseline(<a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project,
                             <a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;baseline,
                             int&nbsp;index)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../org/mpxj/BaselineStrategy.html#populateBaseline-org.mpxj.ProjectFile-org.mpxj.ProjectFile-int-">BaselineStrategy</a></code></span></div>
<div class="block">Use the supplied baseline project to set the baselineN cost, duration, finish,
 fixed cost accrual, fixed cost, start and work attributes for the tasks
 in the supplied project.
 <p/>
 The index argument selects which of the 10 baselines to populate. Passing
 an index of 0 populates the main baseline.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/BaselineStrategy.html#populateBaseline-org.mpxj.ProjectFile-org.mpxj.ProjectFile-int-">populateBaseline</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/BaselineStrategy.html" title="interface in org.mpxj">BaselineStrategy</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>project</code> - target project</dd>
<dd><code>baseline</code> - baseline project</dd>
<dd><code>index</code> - baseline to populate (0-10)</dd>
</dl>
</li>
</ul>
<a name="getKeyForTask-org.mpxj.Task-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getKeyForTask</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>&nbsp;getKeyForTask(<a href="../../org/mpxj/Task.html" title="class in org.mpxj">Task</a>&nbsp;task)</pre>
<div class="block">This method is used to generate the key which connect tasks from the
 current and baseline schedules. This key should be unique for each task
 in the schedule. Instances where the key is not unique will result in an
 incorrect baseline being applied to a task in the current schedule.

 If a task in the baseline schedule and a task in the main schedule both
 generate the same key value, they are treated as the same task and values
 from the task in the baseline schedule will be used to populate baseline
 attributes in the main schedule.

 This default implementation assumes that the task's GUID attribute is
 sufficient to match tasks in the baseline and main schedules. It is expected
 that this method is overridden for specific schedule types.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>task</code> - task from which a key is generated</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>key value</dd>
</dl>
</li>
</ul>
<a name="getBaselineFields-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineFields</h4>
<pre>protected&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a>[]&nbsp;getBaselineFields(int&nbsp;index)</pre>
<div class="block">Determines the set of baseline fields to populate. This is either the
 main baseline fields (when index is 0), or the baseline 1-10 fields.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>index</code> - index of the baseline to populate (0-10)</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>array of baseline fields</dd>
</dl>
</li>
</ul>
<a name="getSourceFields--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getSourceFields</h4>
<pre>protected&nbsp;<a href="../../org/mpxj/TaskField.html" title="enum in org.mpxj">TaskField</a>[]&nbsp;getSourceFields()</pre>
<div class="block">Fields from which values are retrieved in the baseline schedule
 before being applied as baseline attributes to the main schedule.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>source fields for baseline values</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/AbstractBaselineStrategy.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev&nbsp;Class</li>
<li><a href="../../org/mpxj/AbstractFieldContainer.html" title="class in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/AbstractBaselineStrategy.html" target="_top">Frames</a></li>
<li><a href="AbstractBaselineStrategy.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
