<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Column (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Column (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Column.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../org/mpxj/CodeValue.html" title="interface in org.mpxj"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../org/mpxj/ConstraintField.html" title="enum in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/Column.html" target="_top">Frames</a></li>
<li><a href="Column.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj</div>
<h2 title="Class Column" class="title">Class Column</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.Column</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">Column</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">This class represents a column in an MS Project table. The attributes held
 here describe the layout of the column, along with the title text that has
 been associated with the column. The title text will either be the default
 value supplied by MS Project, or it will be a user defined value.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Column.html#ALIGN_CENTER">ALIGN_CENTER</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Column.html#ALIGN_LEFT">ALIGN_LEFT</a></span></code>
<div class="block">Column alignment constants.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Column.html#ALIGN_RIGHT">ALIGN_RIGHT</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/Column.html#Column-org.mpxj.ProjectFile-">Column</a></span>(<a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project)</code>
<div class="block">Constructor.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Column.html#getAlignData--">getAlignData</a></span>()</code>
<div class="block">Retrieves a value representing the alignment of data displayed in
 the column.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Column.html#getAlignTitle--">getAlignTitle</a></span>()</code>
<div class="block">Retrieves a value representing the alignment of the column title text.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Column.html#getFieldType--">getFieldType</a></span>()</code>
<div class="block">Retrieves the type data displayed in the column.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Column.html#getTitle--">getTitle</a></span>()</code>
<div class="block">Retrieves the column title.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Column.html#getTitle-java.util.Locale-">getTitle</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Locale.html?is-external=true" title="class or interface in java.util">Locale</a>&nbsp;locale)</code>
<div class="block">Retrieves the column title for the given locale.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Column.html#getWidth--">getWidth</a></span>()</code>
<div class="block">Retrieves the width of the column represented as a number of
 characters.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Column.html#setAlignData-int-">setAlignData</a></span>(int&nbsp;alignment)</code>
<div class="block">Sets the alignment of the data in the column.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Column.html#setAlignTitle-int-">setAlignTitle</a></span>(int&nbsp;alignment)</code>
<div class="block">Sets the alignment of the column title.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Column.html#setFieldType-org.mpxj.FieldType-">setFieldType</a></span>(<a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;type)</code>
<div class="block">Sets the type data displayed in the column.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Column.html#setTitle-java.lang.String-">setTitle</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;title)</code>
<div class="block">Sets the user defined column title.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Column.html#setWidth-int-">setWidth</a></span>(int&nbsp;width)</code>
<div class="block">Sets the width of the column in characters.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/Column.html#toString--">toString</a></span>()</code>
<div class="block">This method dumps the contents of this column as a String.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="ALIGN_LEFT">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ALIGN_LEFT</h4>
<pre>public static final&nbsp;int ALIGN_LEFT</pre>
<div class="block">Column alignment constants.</div>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#org.mpxj.Column.ALIGN_LEFT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ALIGN_CENTER">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>ALIGN_CENTER</h4>
<pre>public static final&nbsp;int ALIGN_CENTER</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#org.mpxj.Column.ALIGN_CENTER">Constant Field Values</a></dd>
</dl>
</li>
</ul>
<a name="ALIGN_RIGHT">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ALIGN_RIGHT</h4>
<pre>public static final&nbsp;int ALIGN_RIGHT</pre>
<dl>
<dt><span class="seeLabel">See Also:</span></dt>
<dd><a href="../../constant-values.html#org.mpxj.Column.ALIGN_RIGHT">Constant Field Values</a></dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Column-org.mpxj.ProjectFile-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Column</h4>
<pre>public&nbsp;Column(<a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;project)</pre>
<div class="block">Constructor.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>project</code> - reference to the parent project</dd>
</dl>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getAlignData--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAlignData</h4>
<pre>public&nbsp;int&nbsp;getAlignData()</pre>
<div class="block">Retrieves a value representing the alignment of data displayed in
 the column.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>alignment type</dd>
</dl>
</li>
</ul>
<a name="getAlignTitle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAlignTitle</h4>
<pre>public&nbsp;int&nbsp;getAlignTitle()</pre>
<div class="block">Retrieves a value representing the alignment of the column title text.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>alignment type</dd>
</dl>
</li>
</ul>
<a name="getFieldType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFieldType</h4>
<pre>public&nbsp;<a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;getFieldType()</pre>
<div class="block">Retrieves the type data displayed in the column. This identifier indicates
 what data will appear in the column, and the default column title
 that will appear if the user has not provided a user defined column title.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>field type</dd>
</dl>
</li>
</ul>
<a name="getTitle--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTitle</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getTitle()</pre>
<div class="block">Retrieves the column title.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>column title</dd>
</dl>
</li>
</ul>
<a name="getTitle-java.util.Locale-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTitle</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getTitle(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Locale.html?is-external=true" title="class or interface in java.util">Locale</a>&nbsp;locale)</pre>
<div class="block">Retrieves the column title for the given locale.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>locale</code> - required locale for the default column title</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>column title</dd>
</dl>
</li>
</ul>
<a name="getWidth--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getWidth</h4>
<pre>public&nbsp;int&nbsp;getWidth()</pre>
<div class="block">Retrieves the width of the column represented as a number of
 characters.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>column width</dd>
</dl>
</li>
</ul>
<a name="setAlignData-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAlignData</h4>
<pre>public&nbsp;void&nbsp;setAlignData(int&nbsp;alignment)</pre>
<div class="block">Sets the alignment of the data in the column.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>alignment</code> - data alignment</dd>
</dl>
</li>
</ul>
<a name="setAlignTitle-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAlignTitle</h4>
<pre>public&nbsp;void&nbsp;setAlignTitle(int&nbsp;alignment)</pre>
<div class="block">Sets the alignment of the column title.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>alignment</code> - column title alignment</dd>
</dl>
</li>
</ul>
<a name="setFieldType-org.mpxj.FieldType-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setFieldType</h4>
<pre>public&nbsp;void&nbsp;setFieldType(<a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;type)</pre>
<div class="block">Sets the type data displayed in the column. This identifier indicates
 what data will appear in the column, and the default column title
 that will appear if the user has not provided a user defined column title.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>type</code> - field type</dd>
</dl>
</li>
</ul>
<a name="setTitle-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTitle</h4>
<pre>public&nbsp;void&nbsp;setTitle(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;title)</pre>
<div class="block">Sets the user defined column title.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>title</code> - user defined column title</dd>
</dl>
</li>
</ul>
<a name="setWidth-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setWidth</h4>
<pre>public&nbsp;void&nbsp;setWidth(int&nbsp;width)</pre>
<div class="block">Sets the width of the column in characters.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>width</code> - column width</dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toString()</pre>
<div class="block">This method dumps the contents of this column as a String.
 Note that this facility is provided as a debugging aid.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>formatted contents of this column</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Column.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../org/mpxj/CodeValue.html" title="interface in org.mpxj"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../org/mpxj/ConstraintField.html" title="enum in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/Column.html" target="_top">Frames</a></li>
<li><a href="Column.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
