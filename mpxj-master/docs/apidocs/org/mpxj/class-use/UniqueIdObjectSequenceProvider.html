<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Interface org.mpxj.UniqueIdObjectSequenceProvider (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Interface org.mpxj.UniqueIdObjectSequenceProvider (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../overview-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/class-use/UniqueIdObjectSequenceProvider.html" target="_top">Frames</a></li>
<li><a href="UniqueIdObjectSequenceProvider.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Interface org.mpxj.UniqueIdObjectSequenceProvider" class="title">Uses of Interface<br>org.mpxj.UniqueIdObjectSequenceProvider</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj">org.mpxj</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="org.mpxj">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a> in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a> that implement <a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></span></code>
<div class="block">This class represents a project plan.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectFileSharedData.html" title="class in org.mpxj">ProjectFileSharedData</a></span></code>
<div class="block">Implements a container for common data which can be shared across multiple ProjectFile instances.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing fields, and an explanation">
<caption><span>Fields in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a> declared as <a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProjectEntityContainer.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectEntityContainer.html#m_sequenceProvider">m_sequenceProvider</a></span></code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing constructors, and an explanation">
<caption><span>Constructors in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a> with parameters of type <a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ActivityCodeContainer.html#ActivityCodeContainer-org.mpxj.UniqueIdObjectSequenceProvider-">ActivityCodeContainer</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ActivityCode.Builder.html#Builder-org.mpxj.UniqueIdObjectSequenceProvider-">Builder</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ActivityCodeValue.Builder.html#Builder-org.mpxj.UniqueIdObjectSequenceProvider-">Builder</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/CostAccount.Builder.html#Builder-org.mpxj.UniqueIdObjectSequenceProvider-">Builder</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/Currency.Builder.html#Builder-org.mpxj.UniqueIdObjectSequenceProvider-">Builder</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ExpenseCategory.Builder.html#Builder-org.mpxj.UniqueIdObjectSequenceProvider-">Builder</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/Location.Builder.html#Builder-org.mpxj.UniqueIdObjectSequenceProvider-">Builder</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/NotesTopic.Builder.html#Builder-org.mpxj.UniqueIdObjectSequenceProvider-">Builder</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectCode.Builder.html#Builder-org.mpxj.UniqueIdObjectSequenceProvider-">Builder</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectCodeValue.Builder.html#Builder-org.mpxj.UniqueIdObjectSequenceProvider-">Builder</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ResourceAssignmentCode.Builder.html#Builder-org.mpxj.UniqueIdObjectSequenceProvider-">Builder</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ResourceAssignmentCodeValue.Builder.html#Builder-org.mpxj.UniqueIdObjectSequenceProvider-">Builder</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ResourceCode.Builder.html#Builder-org.mpxj.UniqueIdObjectSequenceProvider-">Builder</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ResourceCodeValue.Builder.html#Builder-org.mpxj.UniqueIdObjectSequenceProvider-">Builder</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/RoleCode.Builder.html#Builder-org.mpxj.UniqueIdObjectSequenceProvider-">Builder</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/RoleCodeValue.Builder.html#Builder-org.mpxj.UniqueIdObjectSequenceProvider-">Builder</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/Shift.Builder.html#Builder-org.mpxj.UniqueIdObjectSequenceProvider-">Builder</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/UnitOfMeasure.Builder.html#Builder-org.mpxj.UniqueIdObjectSequenceProvider-">Builder</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/UserDefinedField.Builder.html#Builder-org.mpxj.UniqueIdObjectSequenceProvider-">Builder</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ShiftPeriod.Builder.html#Builder-org.mpxj.UniqueIdObjectSequenceProvider-org.mpxj.Shift-">Builder</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider,
       <a href="../../../org/mpxj/Shift.html" title="class in org.mpxj">Shift</a>&nbsp;shift)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/CostAccountContainer.html#CostAccountContainer-org.mpxj.UniqueIdObjectSequenceProvider-">CostAccountContainer</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/CurrencyContainer.html#CurrencyContainer-org.mpxj.UniqueIdObjectSequenceProvider-">CurrencyContainer</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ExpenseCategoryContainer.html#ExpenseCategoryContainer-org.mpxj.UniqueIdObjectSequenceProvider-">ExpenseCategoryContainer</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/LocationContainer.html#LocationContainer-org.mpxj.UniqueIdObjectSequenceProvider-">LocationContainer</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/NotesTopicContainer.html#NotesTopicContainer-org.mpxj.UniqueIdObjectSequenceProvider-">NotesTopicContainer</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectCodeContainer.html#ProjectCodeContainer-org.mpxj.UniqueIdObjectSequenceProvider-">ProjectCodeContainer</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectEntityContainer.html#ProjectEntityContainer-org.mpxj.UniqueIdObjectSequenceProvider-">ProjectEntityContainer</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ResourceAssignmentCodeContainer.html#ResourceAssignmentCodeContainer-org.mpxj.UniqueIdObjectSequenceProvider-">ResourceAssignmentCodeContainer</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ResourceCodeContainer.html#ResourceCodeContainer-org.mpxj.UniqueIdObjectSequenceProvider-">ResourceCodeContainer</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/RoleCodeContainer.html#RoleCodeContainer-org.mpxj.UniqueIdObjectSequenceProvider-">RoleCodeContainer</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ShiftContainer.html#ShiftContainer-org.mpxj.UniqueIdObjectSequenceProvider-">ShiftContainer</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/ShiftPeriodContainer.html#ShiftPeriodContainer-org.mpxj.UniqueIdObjectSequenceProvider-">ShiftPeriodContainer</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="altColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/UnitOfMeasureContainer.html#UnitOfMeasureContainer-org.mpxj.UniqueIdObjectSequenceProvider-">UnitOfMeasureContainer</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/WorkContourContainer.html#WorkContourContainer-org.mpxj.UniqueIdObjectSequenceProvider-">WorkContourContainer</a></span>(<a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">UniqueIdObjectSequenceProvider</a>&nbsp;sequenceProvider)</code>
<div class="block">Constructor.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../org/mpxj/UniqueIdObjectSequenceProvider.html" title="interface in org.mpxj">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../overview-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/class-use/UniqueIdObjectSequenceProvider.html" target="_top">Frames</a></li>
<li><a href="UniqueIdObjectSequenceProvider.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
