<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class org.mpxj.ProjectCalendarException (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Class org.mpxj.ProjectCalendarException (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../org/mpxj/ProjectCalendarException.html" title="class in org.mpxj">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../overview-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/class-use/ProjectCalendarException.html" target="_top">Frames</a></li>
<li><a href="ProjectCalendarException.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class org.mpxj.ProjectCalendarException" class="title">Uses of Class<br>org.mpxj.ProjectCalendarException</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../org/mpxj/ProjectCalendarException.html" title="class in org.mpxj">ProjectCalendarException</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj">org.mpxj</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.common">org.mpxj.common</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="org.mpxj">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectCalendarException.html" title="class in org.mpxj">ProjectCalendarException</a> in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a> that return <a href="../../../org/mpxj/ProjectCalendarException.html" title="class in org.mpxj">ProjectCalendarException</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectCalendarException.html" title="class in org.mpxj">ProjectCalendarException</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProjectCalendar.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectCalendar.html#addCalendarException-java.time.LocalDate-">addCalendarException</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDate.html?is-external=true" title="class or interface in java.time">LocalDate</a>&nbsp;date)</code>
<div class="block">Add an exception to the calendar.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectCalendarException.html" title="class in org.mpxj">ProjectCalendarException</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProjectCalendar.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectCalendar.html#addCalendarException-java.time.LocalDate-java.time.LocalDate-">addCalendarException</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDate.html?is-external=true" title="class or interface in java.time">LocalDate</a>&nbsp;fromDate,
                    <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDate.html?is-external=true" title="class or interface in java.time">LocalDate</a>&nbsp;toDate)</code>
<div class="block">Add an exception to the calendar.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectCalendarException.html" title="class in org.mpxj">ProjectCalendarException</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProjectCalendar.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectCalendar.html#addCalendarException-org.mpxj.RecurringData-">addCalendarException</a></span>(<a href="../../../org/mpxj/RecurringData.html" title="class in org.mpxj">RecurringData</a>&nbsp;recurringData)</code>
<div class="block">Add a recurring exception to the calendar.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectCalendarException.html" title="class in org.mpxj">ProjectCalendarException</a></code></td>
<td class="colLast"><span class="typeNameLabel">ProjectCalendar.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectCalendar.html#getException-java.time.LocalDate-">getException</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDate.html?is-external=true" title="class or interface in java.time">LocalDate</a>&nbsp;date)</code>
<div class="block">Retrieve a calendar exception which applies to this date.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a> that return types with arguments of type <a href="../../../org/mpxj/ProjectCalendarException.html" title="class in org.mpxj">ProjectCalendarException</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectCalendarException.html" title="class in org.mpxj">ProjectCalendarException</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">ProjectCalendarWeek.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectCalendarWeek.html#convertToRecurringExceptions-java.time.LocalDate-java.time.LocalDate-">convertToRecurringExceptions</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDate.html?is-external=true" title="class or interface in java.time">LocalDate</a>&nbsp;earliestStartDate,
                            <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDate.html?is-external=true" title="class or interface in java.time">LocalDate</a>&nbsp;latestFinishDate)</code>
<div class="block">Converts this working week into a set of equivalent recurring exceptions.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectCalendarException.html" title="class in org.mpxj">ProjectCalendarException</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">ProjectCalendar.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectCalendar.html#getCalendarExceptions--">getCalendarExceptions</a></span>()</code>
<div class="block">This method retrieves a list of exceptions to the current calendar.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectCalendarException.html" title="class in org.mpxj">ProjectCalendarException</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">ProjectCalendar.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectCalendar.html#getExpandedCalendarExceptions--">getExpandedCalendarExceptions</a></span>()</code>
<div class="block">This method retrieves a list of exceptions to the current calendar.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectCalendarException.html" title="class in org.mpxj">ProjectCalendarException</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">ProjectCalendar.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectCalendar.html#getExpandedCalendarExceptionsWithWorkWeeks--">getExpandedCalendarExceptionsWithWorkWeeks</a></span>()</code>
<div class="block">Expand any exceptions in the calendar, and include any working weeks
 defined by this calendar as exceptions.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectCalendarException.html" title="class in org.mpxj">ProjectCalendarException</a>&gt;</code></td>
<td class="colLast"><span class="typeNameLabel">ProjectCalendarException.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectCalendarException.html#getExpandedExceptions--">getExpandedExceptions</a></span>()</code>
<div class="block">Expand the current exception into a list of exception.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a> with parameters of type <a href="../../../org/mpxj/ProjectCalendarException.html" title="class in org.mpxj">ProjectCalendarException</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><span class="typeNameLabel">ProjectCalendarException.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectCalendarException.html#compareTo-org.mpxj.ProjectCalendarException-">compareTo</a></span>(<a href="../../../org/mpxj/ProjectCalendarException.html" title="class in org.mpxj">ProjectCalendarException</a>&nbsp;o)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><span class="typeNameLabel">ProjectCalendarException.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectCalendarException.html#contains-org.mpxj.ProjectCalendarException-">contains</a></span>(<a href="../../../org/mpxj/ProjectCalendarException.html" title="class in org.mpxj">ProjectCalendarException</a>&nbsp;exception)</code>
<div class="block">Returns true if any part of the supplied calendar exception overlaps this one.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ProjectCalendar.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ProjectCalendar.html#removeCalendarException-org.mpxj.ProjectCalendarException-">removeCalendarException</a></span>(<a href="../../../org/mpxj/ProjectCalendarException.html" title="class in org.mpxj">ProjectCalendarException</a>&nbsp;exception)</code>
<div class="block">Remove an exception from the calendar.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.common">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/ProjectCalendarException.html" title="class in org.mpxj">ProjectCalendarException</a> in <a href="../../../org/mpxj/common/package-summary.html">org.mpxj.common</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Method parameters in <a href="../../../org/mpxj/common/package-summary.html">org.mpxj.common</a> with type arguments of type <a href="../../../org/mpxj/ProjectCalendarException.html" title="class in org.mpxj">ProjectCalendarException</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static void</code></td>
<td class="colLast"><span class="typeNameLabel">ProjectCalendarHelper.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/common/ProjectCalendarHelper.html#mergeExceptions-org.mpxj.ProjectCalendar-java.util.List-">mergeExceptions</a></span>(<a href="../../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a>&nbsp;target,
               <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectCalendarException.html" title="class in org.mpxj">ProjectCalendarException</a>&gt;&nbsp;sourceExceptions)</code>
<div class="block">Merge the supplied list of exceptions into the target calendar.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../org/mpxj/ProjectCalendarException.html" title="class in org.mpxj">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../overview-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/class-use/ProjectCalendarException.html" target="_top">Frames</a></li>
<li><a href="ProjectCalendarException.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
