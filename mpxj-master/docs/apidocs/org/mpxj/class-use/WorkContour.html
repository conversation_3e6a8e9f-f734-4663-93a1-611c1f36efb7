<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class org.mpxj.WorkContour (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Class org.mpxj.WorkContour (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../overview-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/class-use/WorkContour.html" target="_top">Frames</a></li>
<li><a href="WorkContour.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class org.mpxj.WorkContour" class="title">Uses of Class<br>org.mpxj.WorkContour</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj">org.mpxj</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.mpp">org.mpxj.mpp</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.mspdi">org.mpxj.mspdi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.mspdi.schema">org.mpxj.mspdi.schema</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="org.mpxj">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a> in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing fields, and an explanation">
<caption><span>Fields in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a> declared as <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></code></td>
<td class="colLast"><span class="typeNameLabel">WorkContour.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/WorkContour.html#BACK_LOADED">BACK_LOADED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></code></td>
<td class="colLast"><span class="typeNameLabel">WorkContour.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/WorkContour.html#BELL">BELL</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></code></td>
<td class="colLast"><span class="typeNameLabel">WorkContour.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/WorkContour.html#CONTOURED">CONTOURED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></code></td>
<td class="colLast"><span class="typeNameLabel">WorkContour.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/WorkContour.html#DOUBLE_PEAK">DOUBLE_PEAK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></code></td>
<td class="colLast"><span class="typeNameLabel">WorkContour.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/WorkContour.html#EARLY_PEAK">EARLY_PEAK</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></code></td>
<td class="colLast"><span class="typeNameLabel">WorkContour.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/WorkContour.html#FLAT">FLAT</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></code></td>
<td class="colLast"><span class="typeNameLabel">WorkContour.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/WorkContour.html#FRONT_LOADED">FRONT_LOADED</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></code></td>
<td class="colLast"><span class="typeNameLabel">WorkContour.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/WorkContour.html#LATE_PEAK">LATE_PEAK</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></code></td>
<td class="colLast"><span class="typeNameLabel">WorkContour.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/WorkContour.html#TURTLE">TURTLE</a></span></code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a> that return <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></code></td>
<td class="colLast"><span class="typeNameLabel">ResourceAssignment.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ResourceAssignment.html#getWorkContour--">getWorkContour</a></span>()</code>
<div class="block">This method returns the Work Contour type of this Assignment.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/package-summary.html">org.mpxj</a> with parameters of type <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">ResourceAssignment.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/ResourceAssignment.html#setWorkContour-org.mpxj.WorkContour-">setWorkContour</a></span>(<a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a>&nbsp;workContour)</code>
<div class="block">This method sets the Work Contour type of this Assignment.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.mpp">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a> in <a href="../../../org/mpxj/mpp/package-summary.html">org.mpxj.mpp</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/mpp/package-summary.html">org.mpxj.mpp</a> that return <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></code></td>
<td class="colLast"><span class="typeNameLabel">WorkContourHelper.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/WorkContourHelper.html#getInstance-org.mpxj.ProjectFile-int-">getInstance</a></span>(<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file,
           int&nbsp;type)</code>
<div class="block">Retrieve a WorkContour instance based on its Microsoft Project ID value.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/mpp/package-summary.html">org.mpxj.mpp</a> with parameters of type <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><span class="typeNameLabel">WorkContourHelper.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mpp/WorkContourHelper.html#getID-org.mpxj.WorkContour-">getID</a></span>(<a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a>&nbsp;contour)</code>
<div class="block">Given a WorkContour instance, retrieve its Microsoft Project ID value.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.mspdi">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a> in <a href="../../../org/mpxj/mspdi/package-summary.html">org.mpxj.mspdi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/mspdi/package-summary.html">org.mpxj.mspdi</a> that return <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></code></td>
<td class="colLast"><span class="typeNameLabel">DatatypeConverter.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mspdi/DatatypeConverter.html#parseWorkContour-java.lang.String-">parseWorkContour</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Parse a work contour.</div>
</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/mspdi/package-summary.html">org.mpxj.mspdi</a> with parameters of type <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>static <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="typeNameLabel">DatatypeConverter.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mspdi/DatatypeConverter.html#printWorkContour-org.mpxj.WorkContour-">printWorkContour</a></span>(<a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a>&nbsp;value)</code>
<div class="block">Print a work contour.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.mspdi.schema">
<!--   -->
</a>
<h3>Uses of <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a> in <a href="../../../org/mpxj/mspdi/schema/package-summary.html">org.mpxj.mspdi.schema</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing fields, and an explanation">
<caption><span>Fields in <a href="../../../org/mpxj/mspdi/schema/package-summary.html">org.mpxj.mspdi.schema</a> declared as <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></code></td>
<td class="colLast"><span class="typeNameLabel">Project.Assignments.Assignment.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mspdi/schema/Project.Assignments.Assignment.html#workContour">workContour</a></span></code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/mspdi/schema/package-summary.html">org.mpxj.mspdi.schema</a> that return <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></code></td>
<td class="colLast"><span class="typeNameLabel">Project.Assignments.Assignment.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mspdi/schema/Project.Assignments.Assignment.html#getWorkContour--">getWorkContour</a></span>()</code>
<div class="block">Gets the value of the workContour property.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></code></td>
<td class="colLast"><span class="typeNameLabel">Adapter12.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mspdi/schema/Adapter12.html#unmarshal-java.lang.String-">unmarshal</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>&nbsp;</td>
</tr>
</tbody>
</table>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../org/mpxj/mspdi/schema/package-summary.html">org.mpxj.mspdi.schema</a> with parameters of type <a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><span class="typeNameLabel">Adapter12.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mspdi/schema/Adapter12.html#marshal-org.mpxj.WorkContour-">marshal</a></span>(<a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a>&nbsp;value)</code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><span class="typeNameLabel">Project.Assignments.Assignment.</span><code><span class="memberNameLink"><a href="../../../org/mpxj/mspdi/schema/Project.Assignments.Assignment.html#setWorkContour-org.mpxj.WorkContour-">setWorkContour</a></span>(<a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">WorkContour</a>&nbsp;value)</code>
<div class="block">Sets the value of the workContour property.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../org/mpxj/WorkContour.html" title="class in org.mpxj">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../overview-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/class-use/WorkContour.html" target="_top">Frames</a></li>
<li><a href="WorkContour.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
