<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>TemporaryCalendar (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="TemporaryCalendar (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/TemporaryCalendar.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../org/mpxj/TaskType.html" title="enum in org.mpxj"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../org/mpxj/TestOperator.html" title="enum in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/TemporaryCalendar.html" target="_top">Frames</a></li>
<li><a href="TemporaryCalendar.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.org.mpxj.ProjectCalendar">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.org.mpxj.ProjectCalendar">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj</div>
<h2 title="Class TemporaryCalendar" class="title">Class TemporaryCalendar</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="../../org/mpxj/ProjectCalendarDays.html" title="class in org.mpxj">org.mpxj.ProjectCalendarDays</a></li>
<li>
<ul class="inheritance">
<li><a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">org.mpxj.ProjectCalendar</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.TemporaryCalendar</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../org/mpxj/ProjectEntityWithMutableUniqueID.html" title="interface in org.mpxj">ProjectEntityWithMutableUniqueID</a>, <a href="../../org/mpxj/ProjectEntityWithUniqueID.html" title="interface in org.mpxj">ProjectEntityWithUniqueID</a>, <a href="../../org/mpxj/TimeUnitDefaultsContainer.html" title="interface in org.mpxj">TimeUnitDefaultsContainer</a></dd>
</dl>
<hr>
<br>
<pre>public class <span class="typeNameLabel">TemporaryCalendar</span>
extends <a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a></pre>
<div class="block">Represents a temporary calendar which is not intended to form part of the
 schedule.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.mpxj.ProjectCalendar">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.mpxj.<a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a></h3>
<code><a href="../../org/mpxj/ProjectCalendar.html#DEFAULT_BASE_CALENDAR_NAME">DEFAULT_BASE_CALENDAR_NAME</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.org.mpxj.ProjectCalendarDays">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;org.mpxj.<a href="../../org/mpxj/ProjectCalendarDays.html" title="class in org.mpxj">ProjectCalendarDays</a></h3>
<code><a href="../../org/mpxj/ProjectCalendarDays.html#DEFAULT_WORKING_AFTERNOON">DEFAULT_WORKING_AFTERNOON</a>, <a href="../../org/mpxj/ProjectCalendarDays.html#DEFAULT_WORKING_MORNING">DEFAULT_WORKING_MORNING</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/TemporaryCalendar.html#TemporaryCalendar-org.mpxj.ProjectFile-">TemporaryCalendar</a></span>(<a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file)</code>
<div class="block">Constructor.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.mpxj.ProjectCalendar">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.mpxj.<a href="../../org/mpxj/ProjectCalendar.html" title="class in org.mpxj">ProjectCalendar</a></h3>
<code><a href="../../org/mpxj/ProjectCalendar.html#addCalendarException-java.time.LocalDate-">addCalendarException</a>, <a href="../../org/mpxj/ProjectCalendar.html#addCalendarException-java.time.LocalDate-java.time.LocalDate-">addCalendarException</a>, <a href="../../org/mpxj/ProjectCalendar.html#addCalendarException-org.mpxj.RecurringData-">addCalendarException</a>, <a href="../../org/mpxj/ProjectCalendar.html#addCalendarHours-java.time.DayOfWeek-">addCalendarHours</a>, <a href="../../org/mpxj/ProjectCalendar.html#addWorkWeek--">addWorkWeek</a>, <a href="../../org/mpxj/ProjectCalendar.html#clearCalendarExceptions--">clearCalendarExceptions</a>, <a href="../../org/mpxj/ProjectCalendar.html#clearWorkWeeks--">clearWorkWeeks</a>, <a href="../../org/mpxj/ProjectCalendar.html#getCalendarExceptions--">getCalendarExceptions</a>, <a href="../../org/mpxj/ProjectCalendar.html#getCalendarMinutesPerDay--">getCalendarMinutesPerDay</a>, <a href="../../org/mpxj/ProjectCalendar.html#getCalendarMinutesPerMonth--">getCalendarMinutesPerMonth</a>, <a href="../../org/mpxj/ProjectCalendar.html#getCalendarMinutesPerWeek--">getCalendarMinutesPerWeek</a>, <a href="../../org/mpxj/ProjectCalendar.html#getCalendarMinutesPerYear--">getCalendarMinutesPerYear</a>, <a href="../../org/mpxj/ProjectCalendar.html#getDate-java.time.LocalDateTime-org.mpxj.Duration-">getDate</a>, <a href="../../org/mpxj/ProjectCalendar.html#getDaysPerMonth--">getDaysPerMonth</a>, <a href="../../org/mpxj/ProjectCalendar.html#getDayType-java.time.DayOfWeek-">getDayType</a>, <a href="../../org/mpxj/ProjectCalendar.html#getDerivedCalendars--">getDerivedCalendars</a>, <a href="../../org/mpxj/ProjectCalendar.html#getDuration-java.time.LocalDateTime-java.time.LocalDateTime-">getDuration</a>, <a href="../../org/mpxj/ProjectCalendar.html#getException-java.time.LocalDate-">getException</a>, <a href="../../org/mpxj/ProjectCalendar.html#getExpandedCalendarExceptions--">getExpandedCalendarExceptions</a>, <a href="../../org/mpxj/ProjectCalendar.html#getExpandedCalendarExceptionsWithWorkWeeks--">getExpandedCalendarExceptionsWithWorkWeeks</a>, <a href="../../org/mpxj/ProjectCalendar.html#getFinishTime-java.time.LocalDate-">getFinishTime</a>, <a href="../../org/mpxj/ProjectCalendar.html#getGUID--">getGUID</a>, <a href="../../org/mpxj/ProjectCalendar.html#getHours-java.time.DayOfWeek-">getHours</a>, <a href="../../org/mpxj/ProjectCalendar.html#getHours-java.time.LocalDate-">getHours</a>, <a href="../../org/mpxj/ProjectCalendar.html#getHours-java.time.LocalDateTime-">getHours</a>, <a href="../../org/mpxj/ProjectCalendar.html#getMinutesPerDay--">getMinutesPerDay</a>, <a href="../../org/mpxj/ProjectCalendar.html#getMinutesPerMonth--">getMinutesPerMonth</a>, <a href="../../org/mpxj/ProjectCalendar.html#getMinutesPerWeek--">getMinutesPerWeek</a>, <a href="../../org/mpxj/ProjectCalendar.html#getMinutesPerYear--">getMinutesPerYear</a>, <a href="../../org/mpxj/ProjectCalendar.html#getNextWorkStart-java.time.LocalDateTime-">getNextWorkStart</a>, <a href="../../org/mpxj/ProjectCalendar.html#getParent--">getParent</a>, <a href="../../org/mpxj/ProjectCalendar.html#getParentFile--">getParentFile</a>, <a href="../../org/mpxj/ProjectCalendar.html#getParentUniqueID--">getParentUniqueID</a>, <a href="../../org/mpxj/ProjectCalendar.html#getPersonal--">getPersonal</a>, <a href="../../org/mpxj/ProjectCalendar.html#getPreviousWorkFinish-java.time.LocalDateTime-">getPreviousWorkFinish</a>, <a href="../../org/mpxj/ProjectCalendar.html#getRanges-java.time.DayOfWeek-">getRanges</a>, <a href="../../org/mpxj/ProjectCalendar.html#getRanges-java.time.LocalDate-">getRanges</a>, <a href="../../org/mpxj/ProjectCalendar.html#getResourceCount--">getResourceCount</a>, <a href="../../org/mpxj/ProjectCalendar.html#getResources--">getResources</a>, <a href="../../org/mpxj/ProjectCalendar.html#getStartTime-java.time.LocalDate-">getStartTime</a>, <a href="../../org/mpxj/ProjectCalendar.html#getTasks--">getTasks</a>, <a href="../../org/mpxj/ProjectCalendar.html#getType--">getType</a>, <a href="../../org/mpxj/ProjectCalendar.html#getUniqueID--">getUniqueID</a>, <a href="../../org/mpxj/ProjectCalendar.html#getWork-java.time.DayOfWeek-org.mpxj.TimeUnit-">getWork</a>, <a href="../../org/mpxj/ProjectCalendar.html#getWork-java.time.LocalDateTime-java.time.LocalDateTime-org.mpxj.TimeUnit-">getWork</a>, <a href="../../org/mpxj/ProjectCalendar.html#getWork-java.time.LocalDate-org.mpxj.TimeUnit-">getWork</a>, <a href="../../org/mpxj/ProjectCalendar.html#getWorkWeek-java.time.LocalDate-">getWorkWeek</a>, <a href="../../org/mpxj/ProjectCalendar.html#getWorkWeeks--">getWorkWeeks</a>, <a href="../../org/mpxj/ProjectCalendar.html#isDerived--">isDerived</a>, <a href="../../org/mpxj/ProjectCalendar.html#isWorkingDate-java.time.LocalDate-">isWorkingDate</a>, <a href="../../org/mpxj/ProjectCalendar.html#isWorkingDay-java.time.DayOfWeek-">isWorkingDay</a>, <a href="../../org/mpxj/ProjectCalendar.html#remove--">remove</a>, <a href="../../org/mpxj/ProjectCalendar.html#removeCalendarException-org.mpxj.ProjectCalendarException-">removeCalendarException</a>, <a href="../../org/mpxj/ProjectCalendar.html#removeCalendarHours-java.time.DayOfWeek-">removeCalendarHours</a>, <a href="../../org/mpxj/ProjectCalendar.html#removeWorkWeek-org.mpxj.ProjectCalendarWeek-">removeWorkWeek</a>, <a href="../../org/mpxj/ProjectCalendar.html#setCalendarMinutesPerDay-java.lang.Integer-">setCalendarMinutesPerDay</a>, <a href="../../org/mpxj/ProjectCalendar.html#setCalendarMinutesPerMonth-java.lang.Integer-">setCalendarMinutesPerMonth</a>, <a href="../../org/mpxj/ProjectCalendar.html#setCalendarMinutesPerWeek-java.lang.Integer-">setCalendarMinutesPerWeek</a>, <a href="../../org/mpxj/ProjectCalendar.html#setCalendarMinutesPerYear-java.lang.Integer-">setCalendarMinutesPerYear</a>, <a href="../../org/mpxj/ProjectCalendar.html#setGUID-java.util.UUID-">setGUID</a>, <a href="../../org/mpxj/ProjectCalendar.html#setParent-org.mpxj.ProjectCalendar-">setParent</a>, <a href="../../org/mpxj/ProjectCalendar.html#setPersonal-boolean-">setPersonal</a>, <a href="../../org/mpxj/ProjectCalendar.html#setType-org.mpxj.CalendarType-">setType</a>, <a href="../../org/mpxj/ProjectCalendar.html#setUniqueID-java.lang.Integer-">setUniqueID</a>, <a href="../../org/mpxj/ProjectCalendar.html#toString--">toString</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.mpxj.ProjectCalendarDays">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.mpxj.<a href="../../org/mpxj/ProjectCalendarDays.html" title="class in org.mpxj">ProjectCalendarDays</a></h3>
<code><a href="../../org/mpxj/ProjectCalendarDays.html#addDefaultCalendarDays--">addDefaultCalendarDays</a>, <a href="../../org/mpxj/ProjectCalendarDays.html#addDefaultCalendarHours--">addDefaultCalendarHours</a>, <a href="../../org/mpxj/ProjectCalendarDays.html#addDefaultCalendarHours-java.time.DayOfWeek-">addDefaultCalendarHours</a>, <a href="../../org/mpxj/ProjectCalendarDays.html#getCalendarDayType-java.time.DayOfWeek-">getCalendarDayType</a>, <a href="../../org/mpxj/ProjectCalendarDays.html#getCalendarDayTypes--">getCalendarDayTypes</a>, <a href="../../org/mpxj/ProjectCalendarDays.html#getCalendarHours--">getCalendarHours</a>, <a href="../../org/mpxj/ProjectCalendarDays.html#getCalendarHours-java.time.DayOfWeek-">getCalendarHours</a>, <a href="../../org/mpxj/ProjectCalendarDays.html#getName--">getName</a>, <a href="../../org/mpxj/ProjectCalendarDays.html#setCalendarDayType-java.time.DayOfWeek-org.mpxj.DayType-">setCalendarDayType</a>, <a href="../../org/mpxj/ProjectCalendarDays.html#setName-java.lang.String-">setName</a>, <a href="../../org/mpxj/ProjectCalendarDays.html#setWorkingDay-java.time.DayOfWeek-boolean-">setWorkingDay</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="TemporaryCalendar-org.mpxj.ProjectFile-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>TemporaryCalendar</h4>
<pre>public&nbsp;TemporaryCalendar(<a href="../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;file)</pre>
<div class="block">Constructor.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>file</code> - the parent file to which this record belongs.</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/TemporaryCalendar.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../org/mpxj/TaskType.html" title="enum in org.mpxj"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../org/mpxj/TestOperator.html" title="enum in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/TemporaryCalendar.html" target="_top">Frames</a></li>
<li><a href="TemporaryCalendar.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.org.mpxj.ProjectCalendar">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#methods.inherited.from.class.org.mpxj.ProjectCalendar">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li>Method</li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
