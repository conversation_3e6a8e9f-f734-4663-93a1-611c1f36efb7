<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Document.Calendars.Calendar.ExceptedDays.ExceptedDay (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Document.Calendars.Calendar.ExceptedDays.ExceptedDay (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.html" title="class in org.mpxj.conceptdraw.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.html" title="class in org.mpxj.conceptdraw.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.html" target="_top">Frames</a></li>
<li><a href="Document.Calendars.Calendar.ExceptedDays.ExceptedDay.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.conceptdraw.schema</div>
<h2 title="Class Document.Calendars.Calendar.ExceptedDays.ExceptedDay" class="title">Class Document.Calendars.Calendar.ExceptedDays.ExceptedDay</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.conceptdraw.schema.Document.Calendars.Calendar.ExceptedDays.ExceptedDay</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.ExceptedDays</a></dd>
</dl>
<hr>
<br>
<pre>public static class <span class="typeNameLabel">Document.Calendars.Calendar.ExceptedDays.ExceptedDay</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block"><p>Java class for anonymous complex type.

 <p>The following schema fragment specifies the expected content contained within this class.

 <pre>
 &lt;complexType&gt;
   &lt;complexContent&gt;
     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       &lt;sequence&gt;
         &lt;element name="Date" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
         &lt;element name="IsDayWorking" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
         &lt;element name="TimePeriods"&gt;
           &lt;complexType&gt;
             &lt;complexContent&gt;
               &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                 &lt;sequence&gt;
                   &lt;element name="TimePeriod" maxOccurs="unbounded" minOccurs="0"&gt;
                     &lt;complexType&gt;
                       &lt;complexContent&gt;
                         &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
                           &lt;sequence&gt;
                             &lt;element name="From" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                             &lt;element name="To" type="{http://www.w3.org/2001/XMLSchema}time"/&gt;
                           &lt;/sequence&gt;
                         &lt;/restriction&gt;
                       &lt;/complexContent&gt;
                     &lt;/complexType&gt;
                   &lt;/element&gt;
                 &lt;/sequence&gt;
               &lt;/restriction&gt;
             &lt;/complexContent&gt;
           &lt;/complexType&gt;
         &lt;/element&gt;
       &lt;/sequence&gt;
     &lt;/restriction&gt;
   &lt;/complexContent&gt;
 &lt;/complexType&gt;
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods</a></span></code>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDate.html?is-external=true" title="class or interface in java.time">LocalDate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.html#date">date</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.html#isDayWorking">isDayWorking</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.html#timePeriods">timePeriods</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.html#ExceptedDay--">ExceptedDay</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDate.html?is-external=true" title="class or interface in java.time">LocalDate</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.html#getDate--">getDate</a></span>()</code>
<div class="block">Gets the value of the date property.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.html#getTimePeriods--">getTimePeriods</a></span>()</code>
<div class="block">Gets the value of the timePeriods property.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.html#isIsDayWorking--">isIsDayWorking</a></span>()</code>
<div class="block">Gets the value of the isDayWorking property.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.html#setDate-java.time.LocalDate-">setDate</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDate.html?is-external=true" title="class or interface in java.time">LocalDate</a>&nbsp;value)</code>
<div class="block">Sets the value of the date property.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.html#setIsDayWorking-boolean-">setIsDayWorking</a></span>(boolean&nbsp;value)</code>
<div class="block">Sets the value of the isDayWorking property.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.html#setTimePeriods-org.mpxj.conceptdraw.schema.Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods-">setTimePeriods</a></span>(<a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods</a>&nbsp;value)</code>
<div class="block">Sets the value of the timePeriods property.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="date">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>date</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDate.html?is-external=true" title="class or interface in java.time">LocalDate</a> date</pre>
</li>
</ul>
<a name="isDayWorking">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isDayWorking</h4>
<pre>protected&nbsp;boolean isDayWorking</pre>
</li>
</ul>
<a name="timePeriods">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>timePeriods</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods</a> timePeriods</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ExceptedDay--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ExceptedDay</h4>
<pre>public&nbsp;ExceptedDay()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getDate--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDate</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDate.html?is-external=true" title="class or interface in java.time">LocalDate</a>&nbsp;getDate()</pre>
<div class="block">Gets the value of the date property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setDate-java.time.LocalDate-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setDate</h4>
<pre>public&nbsp;void&nbsp;setDate(<a href="https://docs.oracle.com/javase/8/docs/api/java/time/LocalDate.html?is-external=true" title="class or interface in java.time">LocalDate</a>&nbsp;value)</pre>
<div class="block">Sets the value of the date property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="isIsDayWorking--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>isIsDayWorking</h4>
<pre>public&nbsp;boolean&nbsp;isIsDayWorking()</pre>
<div class="block">Gets the value of the isDayWorking property.</div>
</li>
</ul>
<a name="setIsDayWorking-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setIsDayWorking</h4>
<pre>public&nbsp;void&nbsp;setIsDayWorking(boolean&nbsp;value)</pre>
<div class="block">Sets the value of the isDayWorking property.</div>
</li>
</ul>
<a name="getTimePeriods--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTimePeriods</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods</a>&nbsp;getTimePeriods()</pre>
<div class="block">Gets the value of the timePeriods property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods</code></a></dd>
</dl>
</li>
</ul>
<a name="setTimePeriods-org.mpxj.conceptdraw.schema.Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setTimePeriods</h4>
<pre>public&nbsp;void&nbsp;setTimePeriods(<a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.html" title="class in org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods</a>&nbsp;value)</pre>
<div class="block">Sets the value of the timePeriods property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.html" title="class in org.mpxj.conceptdraw.schema"><code>Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.html" title="class in org.mpxj.conceptdraw.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.html" title="class in org.mpxj.conceptdraw.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/conceptdraw/schema/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.html" target="_top">Frames</a></li>
<li><a href="Document.Calendars.Calendar.ExceptedDays.ExceptedDay.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
