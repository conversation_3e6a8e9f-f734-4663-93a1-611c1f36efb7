<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Package org.mpxj.conceptdraw.schema (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Package org.mpxj.conceptdraw.schema (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/conceptdraw/schema/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 title="Uses of Package org.mpxj.conceptdraw.schema" class="title">Uses of Package<br>org.mpxj.conceptdraw.schema</h1>
</div>
<div class="contentContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../org/mpxj/conceptdraw/schema/package-summary.html">org.mpxj.conceptdraw.schema</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.conceptdraw.schema">org.mpxj.conceptdraw.schema</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.conceptdraw.schema">
<!--   -->
</a>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing classes, and an explanation">
<caption><span>Classes in <a href="../../../../org/mpxj/conceptdraw/schema/package-summary.html">org.mpxj.conceptdraw.schema</a> used by <a href="../../../../org/mpxj/conceptdraw/schema/package-summary.html">org.mpxj.conceptdraw.schema</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/ActiveFilter.html#org.mpxj.conceptdraw.schema">ActiveFilter</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Callouts.html#org.mpxj.conceptdraw.schema">Callouts</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Callouts.Callout.html#org.mpxj.conceptdraw.schema">Callouts.Callout</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.html#org.mpxj.conceptdraw.schema">Document</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.Calendars.html#org.mpxj.conceptdraw.schema">Document.Calendars</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.Calendars.Calendar.html#org.mpxj.conceptdraw.schema">Document.Calendars.Calendar</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.Calendars.Calendar.ExceptedDays.html#org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.ExceptedDays</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.html#org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.ExceptedDays.ExceptedDay</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.html#org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.TimePeriod.html#org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.ExceptedDays.ExceptedDay.TimePeriods.TimePeriod</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.Calendars.Calendar.WeekDays.html#org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.WeekDays</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.Calendars.Calendar.WeekDays.WeekDay.html#org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.WeekDays.WeekDay</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods.html#org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods.TimePeriod.html#org.mpxj.conceptdraw.schema">Document.Calendars.Calendar.WeekDays.WeekDay.TimePeriods.TimePeriod</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.Dashboards.html#org.mpxj.conceptdraw.schema">Document.Dashboards</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.Dashboards.Dashboard.html#org.mpxj.conceptdraw.schema">Document.Dashboards.Dashboard</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.Links.html#org.mpxj.conceptdraw.schema">Document.Links</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.Links.Link.html#org.mpxj.conceptdraw.schema">Document.Links.Link</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.Markers.html#org.mpxj.conceptdraw.schema">Document.Markers</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.Markers.Marker.html#org.mpxj.conceptdraw.schema">Document.Markers.Marker</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.PrintingProperties.html#org.mpxj.conceptdraw.schema">Document.PrintingProperties</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.ProjectPortfolioView.html#org.mpxj.conceptdraw.schema">Document.ProjectPortfolioView</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.Projects.html#org.mpxj.conceptdraw.schema">Document.Projects</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.Projects.Project.html#org.mpxj.conceptdraw.schema">Document.Projects.Project</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.Projects.Project.Task.html#org.mpxj.conceptdraw.schema">Document.Projects.Project.Task</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.Projects.Project.Task.ResourceAssignments.html#org.mpxj.conceptdraw.schema">Document.Projects.Project.Task.ResourceAssignments</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.Projects.Project.Task.ResourceAssignments.ResourceAssignment.html#org.mpxj.conceptdraw.schema">Document.Projects.Project.Task.ResourceAssignments.ResourceAssignment</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.Resources.html#org.mpxj.conceptdraw.schema">Document.Resources</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.Resources.Resource.html#org.mpxj.conceptdraw.schema">Document.Resources.Resource</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.ResourceUsageDiagram.html#org.mpxj.conceptdraw.schema">Document.ResourceUsageDiagram</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Document.WorkspaceProperties.html#org.mpxj.conceptdraw.schema">Document.WorkspaceProperties</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/Hyperlinks.html#org.mpxj.conceptdraw.schema">Hyperlinks</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/PPVItemsType.html#org.mpxj.conceptdraw.schema">PPVItemsType</a>
<div class="block">Java class for PPVItemsType complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/PPVItemsType.PPVItem.html#org.mpxj.conceptdraw.schema">PPVItemsType.PPVItem</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/PPVItemsType.PPVItem.CompleteJournal.html#org.mpxj.conceptdraw.schema">PPVItemsType.PPVItem.CompleteJournal</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/PPVItemsType.PPVItem.CompleteJournal.CompleteJournalEntry.html#org.mpxj.conceptdraw.schema">PPVItemsType.PPVItem.CompleteJournal.CompleteJournalEntry</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/StyleProject.html#org.mpxj.conceptdraw.schema">StyleProject</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/StyleProject.GridRowStyle.html#org.mpxj.conceptdraw.schema">StyleProject.GridRowStyle</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/TimeScale.html#org.mpxj.conceptdraw.schema">TimeScale</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/ViewProperties.html#org.mpxj.conceptdraw.schema">ViewProperties</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="altColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/ViewProperties.GridColumns.html#org.mpxj.conceptdraw.schema">ViewProperties.GridColumns</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colOne"><a href="../../../../org/mpxj/conceptdraw/schema/class-use/ViewProperties.GridColumns.Column.html#org.mpxj.conceptdraw.schema">ViewProperties.GridColumns.Column</a>
<div class="block">Java class for anonymous complex type.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li class="navBarCell1Rev">Use</li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/conceptdraw/schema/package-use.html" target="_top">Frames</a></li>
<li><a href="package-use.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
