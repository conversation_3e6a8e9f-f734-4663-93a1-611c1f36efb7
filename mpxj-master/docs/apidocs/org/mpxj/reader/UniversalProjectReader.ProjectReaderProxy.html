<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>UniversalProjectReader.ProjectReaderProxy (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="UniversalProjectReader.ProjectReaderProxy (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UniversalProjectReader.ProjectReaderProxy.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/mpxj/reader/UniversalProjectReader.html" title="class in org.mpxj.reader"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/reader/UniversalProjectReader.ProjectReaderProxy.html" target="_top">Frames</a></li>
<li><a href="UniversalProjectReader.ProjectReaderProxy.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.reader</div>
<h2 title="Interface UniversalProjectReader.ProjectReaderProxy" class="title">Interface UniversalProjectReader.ProjectReaderProxy</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Superinterfaces:</dt>
<dd><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/AutoCloseable.html?is-external=true" title="class or interface in java.lang">AutoCloseable</a></dd>
</dl>
<dl>
<dt>Enclosing class:</dt>
<dd><a href="../../../org/mpxj/reader/UniversalProjectReader.html" title="class in org.mpxj.reader">UniversalProjectReader</a></dd>
</dl>
<hr>
<br>
<pre>public static interface <span class="typeNameLabel">UniversalProjectReader.ProjectReaderProxy</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/AutoCloseable.html?is-external=true" title="class or interface in java.lang">AutoCloseable</a></pre>
<div class="block">The classes implementing this interface provide access to an instance of
 the <code>ProjectReader</code> class (via the <code>getProjectReader</code> method)
 which is the class that <code>UniversalProjectReader</code> has determined
 should be used to read the file or stream you have passed it. You can use
 the <code>ProjectReader</code> instance to take decisions in your own code
 based on the type of file represented by the reader, or you can customize
 the behaviour of the reader by setting its properties before reading a
 schedule.
 <p>
 Once you have obtained an instance of the <code>ProjectReaderProxy</code>
 class, you can call the <code>read</code> or <code>readAll</code> methods to read
 the schedule data. Note that you must use these methods to read the
 schedule data from the supplied file or stream rather than calling the
 read methods on the <code>ProjectReader</code> instance as the
 <code>UniversalProjectReader</code> has pre-processed the data you
 supplied to locate the schedule.
 <p>
 Note: you must release the resources held by instances of this class by
 arranging to call the <code>close</code> method. To assist with this, this
 interface extends <code>AutoCloseable</code>.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/reader/ProjectReader.html" title="interface in org.mpxj.reader">ProjectReader</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/UniversalProjectReader.ProjectReaderProxy.html#getProjectReader--">getProjectReader</a></span>()</code>
<div class="block">Retrieve the <code>ProjectReader</code> instance which will be used to read
 the supplied file or stream.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/UniversalProjectReader.ProjectReaderProxy.html#read--">read</a></span>()</code>
<div class="block">Read a single <code>ProjectFile</code> instance from the supplied file or stream.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/UniversalProjectReader.ProjectReaderProxy.html#readAll--">readAll</a></span>()</code>
<div class="block">Read a list of <code>ProjectFile</code> instances from the supplied file or stream.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.AutoCloseable">
<!--   -->
</a>
<h3>Methods inherited from interface&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/AutoCloseable.html?is-external=true" title="class or interface in java.lang">AutoCloseable</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/AutoCloseable.html?is-external=true#close--" title="class or interface in java.lang">close</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getProjectReader--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectReader</h4>
<pre><a href="../../../org/mpxj/reader/ProjectReader.html" title="interface in org.mpxj.reader">ProjectReader</a>&nbsp;getProjectReader()</pre>
<div class="block">Retrieve the <code>ProjectReader</code> instance which will be used to read
 the supplied file or stream.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>ProjectReader</code> instance</dd>
</dl>
</li>
</ul>
<a name="read--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>read</h4>
<pre><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;read()
          throws <a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></pre>
<div class="block">Read a single <code>ProjectFile</code> instance from the supplied file or stream.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>ProjectFile</code> instance or <code>null</code></dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></code></dd>
</dl>
</li>
</ul>
<a name="readAll--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>readAll</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;&nbsp;readAll()
                   throws <a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></pre>
<div class="block">Read a list of <code>ProjectFile</code> instances from the supplied file or stream.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd><code>ProjectFile</code> instance or an empty list</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UniversalProjectReader.ProjectReaderProxy.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/mpxj/reader/UniversalProjectReader.html" title="class in org.mpxj.reader"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li>Next&nbsp;Class</li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/reader/UniversalProjectReader.ProjectReaderProxy.html" target="_top">Frames</a></li>
<li><a href="UniversalProjectReader.ProjectReaderProxy.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
