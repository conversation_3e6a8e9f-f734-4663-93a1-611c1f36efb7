<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ProjectReader (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ProjectReader (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":6,"i1":6,"i2":6,"i3":6,"i4":6,"i5":6,"i6":6};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],4:["t3","Abstract Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProjectReader.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/mpxj/reader/ProjectReaderUtility.html" title="class in org.mpxj.reader"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/reader/ProjectReader.html" target="_top">Frames</a></li>
<li><a href="ProjectReader.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.reader</div>
<h2 title="Interface ProjectReader" class="title">Interface ProjectReader</h2>
</div>
<div class="contentContainer">
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Known Implementing Classes:</dt>
<dd><a href="../../../org/mpxj/reader/AbstractProjectFileReader.html" title="class in org.mpxj.reader">AbstractProjectFileReader</a>, <a href="../../../org/mpxj/reader/AbstractProjectReader.html" title="class in org.mpxj.reader">AbstractProjectReader</a>, <a href="../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a>, <a href="../../../org/mpxj/asta/AstaFileReader.html" title="class in org.mpxj.asta">AstaFileReader</a>, <a href="../../../org/mpxj/asta/AstaJdbcReader.html" title="class in org.mpxj.asta">AstaJdbcReader</a>, <a href="../../../org/mpxj/asta/AstaMdbReader.html" title="class in org.mpxj.asta">AstaMdbReader</a>, <a href="../../../org/mpxj/asta/AstaSqliteReader.html" title="class in org.mpxj.asta">AstaSqliteReader</a>, <a href="../../../org/mpxj/asta/AstaTextFileReader.html" title="class in org.mpxj.asta">AstaTextFileReader</a>, <a href="../../../org/mpxj/conceptdraw/ConceptDrawProjectReader.html" title="class in org.mpxj.conceptdraw">ConceptDrawProjectReader</a>, <a href="../../../org/mpxj/edrawproject/EdrawProjectReader.html" title="class in org.mpxj.edrawproject">EdrawProjectReader</a>, <a href="../../../org/mpxj/fasttrack/FastTrackReader.html" title="class in org.mpxj.fasttrack">FastTrackReader</a>, <a href="../../../org/mpxj/ganttdesigner/GanttDesignerReader.html" title="class in org.mpxj.ganttdesigner">GanttDesignerReader</a>, <a href="../../../org/mpxj/ganttproject/GanttProjectReader.html" title="class in org.mpxj.ganttproject">GanttProjectReader</a>, <a href="../../../org/mpxj/merlin/MerlinReader.html" title="class in org.mpxj.merlin">MerlinReader</a>, <a href="../../../org/mpxj/mpd/MPDDatabaseReader.html" title="class in org.mpxj.mpd">MPDDatabaseReader</a>, <a href="../../../org/mpxj/mpd/MPDFileReader.html" title="class in org.mpxj.mpd">MPDFileReader</a>, <a href="../../../org/mpxj/mpp/MPPReader.html" title="class in org.mpxj.mpp">MPPReader</a>, <a href="../../../org/mpxj/mpx/MPXReader.html" title="class in org.mpxj.mpx">MPXReader</a>, <a href="../../../org/mpxj/mspdi/MSPDIReader.html" title="class in org.mpxj.mspdi">MSPDIReader</a>, <a href="../../../org/mpxj/openplan/OpenPlanReader.html" title="class in org.mpxj.openplan">OpenPlanReader</a>, <a href="../../../org/mpxj/primavera/p3/P3DatabaseReader.html" title="class in org.mpxj.primavera.p3">P3DatabaseReader</a>, <a href="../../../org/mpxj/primavera/p3/P3PRXFileReader.html" title="class in org.mpxj.primavera.p3">P3PRXFileReader</a>, <a href="../../../org/mpxj/phoenix/PhoenixReader.html" title="class in org.mpxj.phoenix">PhoenixReader</a>, <a href="../../../org/mpxj/planner/PlannerReader.html" title="class in org.mpxj.planner">PlannerReader</a>, <a href="../../../org/mpxj/primavera/PrimaveraDatabaseFileReader.html" title="class in org.mpxj.primavera">PrimaveraDatabaseFileReader</a>, <a href="../../../org/mpxj/primavera/PrimaveraDatabaseReader.html" title="class in org.mpxj.primavera">PrimaveraDatabaseReader</a>, <a href="../../../org/mpxj/primavera/PrimaveraPMFileReader.html" title="class in org.mpxj.primavera">PrimaveraPMFileReader</a>, <a href="../../../org/mpxj/primavera/PrimaveraXERFileReader.html" title="class in org.mpxj.primavera">PrimaveraXERFileReader</a>, <a href="../../../org/mpxj/projectcommander/ProjectCommanderReader.html" title="class in org.mpxj.projectcommander">ProjectCommanderReader</a>, <a href="../../../org/mpxj/projectlibre/ProjectLibreReader.html" title="class in org.mpxj.projectlibre">ProjectLibreReader</a>, <a href="../../../org/mpxj/sage/SageReader.html" title="class in org.mpxj.sage">SageReader</a>, <a href="../../../org/mpxj/sdef/SDEFReader.html" title="class in org.mpxj.sdef">SDEFReader</a>, <a href="../../../org/mpxj/primavera/suretrak/SureTrakDatabaseReader.html" title="class in org.mpxj.primavera.suretrak">SureTrakDatabaseReader</a>, <a href="../../../org/mpxj/primavera/suretrak/SureTrakSTXFileReader.html" title="class in org.mpxj.primavera.suretrak">SureTrakSTXFileReader</a>, <a href="../../../org/mpxj/synchro/SynchroReader.html" title="class in org.mpxj.synchro">SynchroReader</a>, <a href="../../../org/mpxj/turboproject/TurboProjectReader.html" title="class in org.mpxj.turboproject">TurboProjectReader</a>, <a href="../../../org/mpxj/reader/UniversalProjectReader.html" title="class in org.mpxj.reader">UniversalProjectReader</a></dd>
</dl>
<hr>
<br>
<pre>public interface <span class="typeNameLabel">ProjectReader</span></pre>
<div class="block">This interface is implemented by all classes which can read project
 files of any type and generate an ProjectFile instance from the contents
 of the file.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t3" class="tableTab"><span><a href="javascript:show(4);">Abstract Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/ProjectReader.html#addProjectListener-org.mpxj.listener.ProjectListener-">addProjectListener</a></span>(<a href="../../../org/mpxj/listener/ProjectListener.html" title="interface in org.mpxj.listener">ProjectListener</a>&nbsp;listener)</code>
<div class="block">Add a listener to receive events as a project is being read.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/ProjectReader.html#read-java.io.File-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>
<div class="block">Read a single schedule from a file where a File instance is supplied.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/ProjectReader.html#read-java.io.InputStream-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)</code>
<div class="block">Read a single schedule from a file where the contents of the project file
 are supplied via an input stream.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/ProjectReader.html#read-java.lang.String-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)</code>
<div class="block">Read a single schedule from a file where the file name is supplied.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/ProjectReader.html#readAll-java.io.File-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>
<div class="block">Read all schedules from a file where a File instance is supplied.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/ProjectReader.html#readAll-java.io.InputStream-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)</code>
<div class="block">Read all schedules from a file where the contents of the project file
 are supplied via an input stream.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/reader/ProjectReader.html#readAll-java.lang.String-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)</code>
<div class="block">Read all schedules from a file where the file name is supplied.</div>
</td>
</tr>
</table>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="addProjectListener-org.mpxj.listener.ProjectListener-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>addProjectListener</h4>
<pre>void&nbsp;addProjectListener(<a href="../../../org/mpxj/listener/ProjectListener.html" title="interface in org.mpxj.listener">ProjectListener</a>&nbsp;listener)</pre>
<div class="block">Add a listener to receive events as a project is being read.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>listener</code> - ProjectListener instance</dd>
</dl>
</li>
</ul>
<a name="read-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>read</h4>
<pre><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;read(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)
          throws <a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></pre>
<div class="block">Read a single schedule from a file where the file name is supplied.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fileName</code> - file name</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ProjectFile instance</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></code></dd>
</dl>
</li>
</ul>
<a name="readAll-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readAll</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;&nbsp;readAll(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)
                   throws <a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></pre>
<div class="block">Read all schedules from a file where the file name is supplied.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>fileName</code> - file name</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ProjectFile instance</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></code></dd>
</dl>
</li>
</ul>
<a name="read-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>read</h4>
<pre><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;read(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)
          throws <a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></pre>
<div class="block">Read a single schedule from a file where a File instance is supplied.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>file</code> - File instance</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ProjectFile instance</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></code></dd>
</dl>
</li>
</ul>
<a name="readAll-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>readAll</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;&nbsp;readAll(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)
                   throws <a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></pre>
<div class="block">Read all schedules from a file where a File instance is supplied.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>file</code> - File instance</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ProjectFile instance</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></code></dd>
</dl>
</li>
</ul>
<a name="read-java.io.InputStream-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>read</h4>
<pre><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;read(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)
          throws <a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></pre>
<div class="block">Read a single schedule from a file where the contents of the project file
 are supplied via an input stream.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inputStream</code> - InputStream instance</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ProjectFile instance</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></code></dd>
</dl>
</li>
</ul>
<a name="readAll-java.io.InputStream-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>readAll</h4>
<pre><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;&nbsp;readAll(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)
                   throws <a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></pre>
<div class="block">Read all schedules from a file where the contents of the project file
 are supplied via an input stream.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>inputStream</code> - InputStream instance</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ProjectFile instance</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProjectReader.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/mpxj/reader/ProjectReaderUtility.html" title="class in org.mpxj.reader"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/reader/ProjectReader.html" target="_top">Frames</a></li>
<li><a href="ProjectReader.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
