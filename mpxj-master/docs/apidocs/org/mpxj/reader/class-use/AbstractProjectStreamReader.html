<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Class org.mpxj.reader.AbstractProjectStreamReader (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Class org.mpxj.reader.AbstractProjectStreamReader (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../overview-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/reader/class-use/AbstractProjectStreamReader.html" target="_top">Frames</a></li>
<li><a href="AbstractProjectStreamReader.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Class org.mpxj.reader.AbstractProjectStreamReader" class="title">Uses of Class<br>org.mpxj.reader.AbstractProjectStreamReader</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.asta">org.mpxj.asta</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.conceptdraw">org.mpxj.conceptdraw</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.edrawproject">org.mpxj.edrawproject</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.ganttdesigner">org.mpxj.ganttdesigner</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.ganttproject">org.mpxj.ganttproject</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.mpp">org.mpxj.mpp</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.mpx">org.mpxj.mpx</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.mspdi">org.mpxj.mspdi</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.openplan">org.mpxj.openplan</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.phoenix">org.mpxj.phoenix</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.planner">org.mpxj.planner</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.primavera">org.mpxj.primavera</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.primavera.p3">org.mpxj.primavera.p3</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.primavera.suretrak">org.mpxj.primavera.suretrak</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.projectcommander">org.mpxj.projectcommander</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.projectlibre">org.mpxj.projectlibre</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.sage">org.mpxj.sage</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.sdef">org.mpxj.sdef</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.synchro">org.mpxj.synchro</a></td>
<td class="colLast">&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><a href="#org.mpxj.turboproject">org.mpxj.turboproject</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="org.mpxj.asta">
<!--   -->
</a>
<h3>Uses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/asta/package-summary.html">org.mpxj.asta</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/asta/package-summary.html">org.mpxj.asta</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/asta/AstaFileReader.html" title="class in org.mpxj.asta">AstaFileReader</a></span></code>
<div class="block">This class provides a generic front end to read project data from
 an Asta PP file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/asta/AstaTextFileReader.html" title="class in org.mpxj.asta">AstaTextFileReader</a></span></code>
<div class="block">This class provides a generic front end to read project data from
 a text-based Asta PP file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.conceptdraw">
<!--   -->
</a>
<h3>Uses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/conceptdraw/package-summary.html">org.mpxj.conceptdraw</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/conceptdraw/package-summary.html">org.mpxj.conceptdraw</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/conceptdraw/ConceptDrawProjectReader.html" title="class in org.mpxj.conceptdraw">ConceptDrawProjectReader</a></span></code>
<div class="block">This class creates a new ProjectFile instance by reading a ConceptDraw Project file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.edrawproject">
<!--   -->
</a>
<h3>Uses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/edrawproject/package-summary.html">org.mpxj.edrawproject</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/edrawproject/package-summary.html">org.mpxj.edrawproject</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/edrawproject/EdrawProjectReader.html" title="class in org.mpxj.edrawproject">EdrawProjectReader</a></span></code>
<div class="block">This class creates a new ProjectFile instance by reading an Edraw Project EDPX file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.ganttdesigner">
<!--   -->
</a>
<h3>Uses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/ganttdesigner/package-summary.html">org.mpxj.ganttdesigner</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/ganttdesigner/package-summary.html">org.mpxj.ganttdesigner</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttdesigner/GanttDesignerReader.html" title="class in org.mpxj.ganttdesigner">GanttDesignerReader</a></span></code>
<div class="block">This class creates a new ProjectFile instance by reading a GanttDesigner file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.ganttproject">
<!--   -->
</a>
<h3>Uses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/ganttproject/package-summary.html">org.mpxj.ganttproject</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/ganttproject/package-summary.html">org.mpxj.ganttproject</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/ganttproject/GanttProjectReader.html" title="class in org.mpxj.ganttproject">GanttProjectReader</a></span></code>
<div class="block">This class creates a new ProjectFile instance by reading a GanttProject file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.mpp">
<!--   -->
</a>
<h3>Uses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/mpp/package-summary.html">org.mpxj.mpp</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/mpp/package-summary.html">org.mpxj.mpp</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mpp/MPPReader.html" title="class in org.mpxj.mpp">MPPReader</a></span></code>
<div class="block">This class creates a new ProjectFile instance by reading an MPP file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.mpx">
<!--   -->
</a>
<h3>Uses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/mpx/package-summary.html">org.mpxj.mpx</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/mpx/package-summary.html">org.mpxj.mpx</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mpx/MPXReader.html" title="class in org.mpxj.mpx">MPXReader</a></span></code>
<div class="block">This class creates a new ProjectFile instance by reading an MPX file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.mspdi">
<!--   -->
</a>
<h3>Uses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/mspdi/package-summary.html">org.mpxj.mspdi</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/mspdi/package-summary.html">org.mpxj.mspdi</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/mspdi/MSPDIReader.html" title="class in org.mpxj.mspdi">MSPDIReader</a></span></code>
<div class="block">This class creates a new ProjectFile instance by reading an MSPDI file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.openplan">
<!--   -->
</a>
<h3>Uses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/openplan/package-summary.html">org.mpxj.openplan</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/openplan/package-summary.html">org.mpxj.openplan</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/openplan/OpenPlanReader.html" title="class in org.mpxj.openplan">OpenPlanReader</a></span></code>
<div class="block">Reads schedule data from a Deltek Open Plan BK3 file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.phoenix">
<!--   -->
</a>
<h3>Uses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/phoenix/package-summary.html">org.mpxj.phoenix</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/phoenix/package-summary.html">org.mpxj.phoenix</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/phoenix/PhoenixReader.html" title="class in org.mpxj.phoenix">PhoenixReader</a></span></code>
<div class="block">This class creates a new ProjectFile instance by reading a Phoenix Project Manager file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.planner">
<!--   -->
</a>
<h3>Uses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/planner/package-summary.html">org.mpxj.planner</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/planner/package-summary.html">org.mpxj.planner</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/PlannerReader.html" title="class in org.mpxj.planner">PlannerReader</a></span></code>
<div class="block">This class creates a new ProjectFile instance by reading a Planner file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.primavera">
<!--   -->
</a>
<h3>Uses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/primavera/package-summary.html">org.mpxj.primavera</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/primavera/package-summary.html">org.mpxj.primavera</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/PrimaveraPMFileReader.html" title="class in org.mpxj.primavera">PrimaveraPMFileReader</a></span></code>
<div class="block">This class creates a new ProjectFile instance by reading a Primavera PM file.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/PrimaveraXERFileReader.html" title="class in org.mpxj.primavera">PrimaveraXERFileReader</a></span></code>
<div class="block">This class creates a new ProjectFile instance by reading a Primavera XER file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.primavera.p3">
<!--   -->
</a>
<h3>Uses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/primavera/p3/package-summary.html">org.mpxj.primavera.p3</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/primavera/p3/package-summary.html">org.mpxj.primavera.p3</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/p3/P3PRXFileReader.html" title="class in org.mpxj.primavera.p3">P3PRXFileReader</a></span></code>
<div class="block">Reads a schedule data from a P3 PRX file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.primavera.suretrak">
<!--   -->
</a>
<h3>Uses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/primavera/suretrak/package-summary.html">org.mpxj.primavera.suretrak</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/primavera/suretrak/package-summary.html">org.mpxj.primavera.suretrak</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/primavera/suretrak/SureTrakSTXFileReader.html" title="class in org.mpxj.primavera.suretrak">SureTrakSTXFileReader</a></span></code>
<div class="block">Reads a schedule data from a SureTrak STX file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.projectcommander">
<!--   -->
</a>
<h3>Uses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/projectcommander/package-summary.html">org.mpxj.projectcommander</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/projectcommander/package-summary.html">org.mpxj.projectcommander</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/projectcommander/ProjectCommanderReader.html" title="class in org.mpxj.projectcommander">ProjectCommanderReader</a></span></code>
<div class="block">Reads schedule data from a Project Commander file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.projectlibre">
<!--   -->
</a>
<h3>Uses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/projectlibre/package-summary.html">org.mpxj.projectlibre</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/projectlibre/package-summary.html">org.mpxj.projectlibre</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/projectlibre/ProjectLibreReader.html" title="class in org.mpxj.projectlibre">ProjectLibreReader</a></span></code>
<div class="block">Reads a ProjectLibre POD file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.sage">
<!--   -->
</a>
<h3>Uses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/sage/package-summary.html">org.mpxj.sage</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/sage/package-summary.html">org.mpxj.sage</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/sage/SageReader.html" title="class in org.mpxj.sage">SageReader</a></span></code>
<div class="block">Read schedule grid files generated by Sage 100 Contractor.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.sdef">
<!--   -->
</a>
<h3>Uses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/sdef/package-summary.html">org.mpxj.sdef</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/sdef/package-summary.html">org.mpxj.sdef</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/sdef/SDEFReader.html" title="class in org.mpxj.sdef">SDEFReader</a></span></code>
<div class="block">Read the contents of an SDEF file.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.synchro">
<!--   -->
</a>
<h3>Uses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/synchro/package-summary.html">org.mpxj.synchro</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/synchro/package-summary.html">org.mpxj.synchro</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/synchro/SynchroReader.html" title="class in org.mpxj.synchro">SynchroReader</a></span></code>
<div class="block">Reads Synchro SP files.</div>
</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList"><a name="org.mpxj.turboproject">
<!--   -->
</a>
<h3>Uses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/turboproject/package-summary.html">org.mpxj.turboproject</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing subclasses, and an explanation">
<caption><span>Subclasses of <a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">AbstractProjectStreamReader</a> in <a href="../../../../org/mpxj/turboproject/package-summary.html">org.mpxj.turboproject</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code>class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/turboproject/TurboProjectReader.html" title="class in org.mpxj.turboproject">TurboProjectReader</a></span></code>
<div class="block">This class creates a new ProjectFile instance by reading a TurboProject PEP file.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../org/mpxj/reader/AbstractProjectStreamReader.html" title="class in org.mpxj.reader">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../overview-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/reader/class-use/AbstractProjectStreamReader.html" target="_top">Frames</a></li>
<li><a href="AbstractProjectStreamReader.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
