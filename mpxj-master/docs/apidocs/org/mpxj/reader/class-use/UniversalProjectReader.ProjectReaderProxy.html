<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Uses of Interface org.mpxj.reader.UniversalProjectReader.ProjectReaderProxy (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Uses of Interface org.mpxj.reader.UniversalProjectReader.ProjectReaderProxy (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../org/mpxj/reader/UniversalProjectReader.ProjectReaderProxy.html" title="interface in org.mpxj.reader">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../overview-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/reader/class-use/UniversalProjectReader.ProjectReaderProxy.html" target="_top">Frames</a></li>
<li><a href="UniversalProjectReader.ProjectReaderProxy.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h2 title="Uses of Interface org.mpxj.reader.UniversalProjectReader.ProjectReaderProxy" class="title">Uses of Interface<br>org.mpxj.reader.UniversalProjectReader.ProjectReaderProxy</h2>
</div>
<div class="classUseContainer">
<ul class="blockList">
<li class="blockList">
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing packages, and an explanation">
<caption><span>Packages that use <a href="../../../../org/mpxj/reader/UniversalProjectReader.ProjectReaderProxy.html" title="interface in org.mpxj.reader">UniversalProjectReader.ProjectReaderProxy</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Package</th>
<th class="colLast" scope="col">Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><a href="#org.mpxj.reader">org.mpxj.reader</a></td>
<td class="colLast">&nbsp;</td>
</tr>
</tbody>
</table>
</li>
<li class="blockList">
<ul class="blockList">
<li class="blockList"><a name="org.mpxj.reader">
<!--   -->
</a>
<h3>Uses of <a href="../../../../org/mpxj/reader/UniversalProjectReader.ProjectReaderProxy.html" title="interface in org.mpxj.reader">UniversalProjectReader.ProjectReaderProxy</a> in <a href="../../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a></h3>
<table class="useSummary" border="0" cellpadding="3" cellspacing="0" summary="Use table, listing methods, and an explanation">
<caption><span>Methods in <a href="../../../../org/mpxj/reader/package-summary.html">org.mpxj.reader</a> that return <a href="../../../../org/mpxj/reader/UniversalProjectReader.ProjectReaderProxy.html" title="interface in org.mpxj.reader">UniversalProjectReader.ProjectReaderProxy</a></span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tbody>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/reader/UniversalProjectReader.ProjectReaderProxy.html" title="interface in org.mpxj.reader">UniversalProjectReader.ProjectReaderProxy</a></code></td>
<td class="colLast"><span class="typeNameLabel">UniversalProjectReader.</span><code><span class="memberNameLink"><a href="../../../../org/mpxj/reader/UniversalProjectReader.html#getProjectReaderProxy-java.io.File-">getProjectReaderProxy</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>
<div class="block">Retrieve a <code>ProjectReaderProxy</code> instance which provides access to
 the <code>ProjectReader</code> required to read a schedule from the supplied
 <code>File instance</code>.</div>
</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/reader/UniversalProjectReader.ProjectReaderProxy.html" title="interface in org.mpxj.reader">UniversalProjectReader.ProjectReaderProxy</a></code></td>
<td class="colLast"><span class="typeNameLabel">UniversalProjectReader.</span><code><span class="memberNameLink"><a href="../../../../org/mpxj/reader/UniversalProjectReader.html#getProjectReaderProxy-java.io.InputStream-">getProjectReaderProxy</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/InputStream.html?is-external=true" title="class or interface in java.io">InputStream</a>&nbsp;inputStream)</code>
<div class="block">Retrieve a <code>ProjectReaderProxy</code> instance which provides access to
 the <code>ProjectReader</code> required to read a schedule from the supplied
 <code>InputStream instance</code>.</div>
</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/reader/UniversalProjectReader.ProjectReaderProxy.html" title="interface in org.mpxj.reader">UniversalProjectReader.ProjectReaderProxy</a></code></td>
<td class="colLast"><span class="typeNameLabel">UniversalProjectReader.</span><code><span class="memberNameLink"><a href="../../../../org/mpxj/reader/UniversalProjectReader.html#getProjectReaderProxy-java.lang.String-">getProjectReaderProxy</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;fileName)</code>
<div class="block">Retrieve a <code>ProjectReaderProxy</code> instance which provides access to
 the <code>ProjectReader</code> required to read a schedule from the named file.</div>
</td>
</tr>
</tbody>
</table>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="../package-summary.html">Package</a></li>
<li><a href="../../../../org/mpxj/reader/UniversalProjectReader.ProjectReaderProxy.html" title="interface in org.mpxj.reader">Class</a></li>
<li class="navBarCell1Rev">Use</li>
<li><a href="../../../../overview-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li>Prev</li>
<li>Next</li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/reader/class-use/UniversalProjectReader.ProjectReaderProxy.html" target="_top">Frames</a></li>
<li><a href="UniversalProjectReader.ProjectReaderProxy.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
