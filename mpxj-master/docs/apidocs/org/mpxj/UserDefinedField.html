<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>UserDefinedField (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="UserDefinedField (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UserDefinedField.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../org/mpxj/UnitOfMeasureContainer.html" title="class in org.mpxj"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../org/mpxj/UserDefinedField.Builder.html" title="class in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/UserDefinedField.html" target="_top">Frames</a></li>
<li><a href="UserDefinedField.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj</div>
<h2 title="Class UserDefinedField" class="title">Class UserDefinedField</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.UserDefinedField</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>, <a href="../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a></dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">UserDefinedField</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>
implements <a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></pre>
<div class="block">Represents a user defined field.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Nested Class Summary table, listing nested classes, and an explanation">
<caption><span>Nested Classes</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Class and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>static class&nbsp;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/UserDefinedField.Builder.html" title="class in org.mpxj">UserDefinedField.Builder</a></span></code>
<div class="block">User defined field builder.</div>
</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/DataType.html" title="enum in org.mpxj">DataType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/UserDefinedField.html#getDataType--">getDataType</a></span>()</code>
<div class="block">Retrieve the data type of this field.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="../../org/mpxj/FieldTypeClass.html" title="enum in org.mpxj">FieldTypeClass</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/UserDefinedField.html#getFieldTypeClass--">getFieldTypeClass</a></span>()</code>
<div class="block">Retrieve an enum representing the type of entity to which this field belongs.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/UserDefinedField.html#getName--">getName</a></span>()</code>
<div class="block">Retrieve the name of this field using the default locale.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/UserDefinedField.html#getName-java.util.Locale-">getName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Locale.html?is-external=true" title="class or interface in java.util">Locale</a>&nbsp;locale)</code>
<div class="block">Retrieve the name of this field using the supplied locale.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/UserDefinedField.html#getSummaryTaskOnly--">getSummaryTaskOnly</a></span>()</code>
<div class="block">For a user defined field with a FieldTypeClas of TASK,  this method
 returns true if this is a user defined field for WBS
 (represented as summary tasks in MPXJ).</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/UserDefinedField.html#getUniqueID--">getUniqueID</a></span>()</code>
<div class="block">Retrieve the unique ID.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/UserDefinedField.html#getUnitsType--">getUnitsType</a></span>()</code>
<div class="block">Retrieve the associated units field, if any.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>int</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/UserDefinedField.html#getValue--">getValue</a></span>()</code>
<div class="block">This method is used to retrieve the int value (not the ordinal)
 associated with an enum instance.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/UserDefinedField.html#name--">name</a></span>()</code>
<div class="block">Retrieve the enum name.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/UserDefinedField.html#toString--">toString</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getValue--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getValue</h4>
<pre>public&nbsp;int&nbsp;getValue()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../org/mpxj/MpxjEnum.html#getValue--">MpxjEnum</a></code></span></div>
<div class="block">This method is used to retrieve the int value (not the ordinal)
 associated with an enum instance.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/MpxjEnum.html#getValue--">getValue</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/MpxjEnum.html" title="interface in org.mpxj">MpxjEnum</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>enum value</dd>
</dl>
</li>
</ul>
<a name="getUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUniqueID</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&nbsp;getUniqueID()</pre>
<div class="block">Retrieve the unique ID.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>unique ID</dd>
</dl>
</li>
</ul>
<a name="getSummaryTaskOnly--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getSummaryTaskOnly</h4>
<pre>public&nbsp;boolean&nbsp;getSummaryTaskOnly()</pre>
<div class="block">For a user defined field with a FieldTypeClas of TASK,  this method
 returns true if this is a user defined field for WBS
 (represented as summary tasks in MPXJ).</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if this is a WBS user defined field</dd>
</dl>
</li>
</ul>
<a name="getFieldTypeClass--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getFieldTypeClass</h4>
<pre>public&nbsp;<a href="../../org/mpxj/FieldTypeClass.html" title="enum in org.mpxj">FieldTypeClass</a>&nbsp;getFieldTypeClass()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../org/mpxj/FieldType.html#getFieldTypeClass--">FieldType</a></code></span></div>
<div class="block">Retrieve an enum representing the type of entity to which this field belongs.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/FieldType.html#getFieldTypeClass--">getFieldTypeClass</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>field type class</dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getName()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../org/mpxj/FieldType.html#getName--">FieldType</a></code></span></div>
<div class="block">Retrieve the name of this field using the default locale.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/FieldType.html#getName--">getName</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>field name</dd>
</dl>
</li>
</ul>
<a name="name--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>name</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;name()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../org/mpxj/FieldType.html#name--">FieldType</a></code></span></div>
<div class="block">Retrieve the enum name.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/FieldType.html#name--">name</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>enum name</dd>
</dl>
</li>
</ul>
<a name="getName-java.util.Locale-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getName(<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Locale.html?is-external=true" title="class or interface in java.util">Locale</a>&nbsp;locale)</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../org/mpxj/FieldType.html#getName-java.util.Locale-">FieldType</a></code></span></div>
<div class="block">Retrieve the name of this field using the supplied locale.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/FieldType.html#getName-java.util.Locale-">getName</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>locale</code> - target locale</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>field name</dd>
</dl>
</li>
</ul>
<a name="getDataType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getDataType</h4>
<pre>public&nbsp;<a href="../../org/mpxj/DataType.html" title="enum in org.mpxj">DataType</a>&nbsp;getDataType()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../org/mpxj/FieldType.html#getDataType--">FieldType</a></code></span></div>
<div class="block">Retrieve the data type of this field.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/FieldType.html#getDataType--">getDataType</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>data type</dd>
</dl>
</li>
</ul>
<a name="getUnitsType--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getUnitsType</h4>
<pre>public&nbsp;<a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a>&nbsp;getUnitsType()</pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../org/mpxj/FieldType.html#getUnitsType--">FieldType</a></code></span></div>
<div class="block">Retrieve the associated units field, if any.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../org/mpxj/FieldType.html#getUnitsType--">getUnitsType</a></code>&nbsp;in interface&nbsp;<code><a href="../../org/mpxj/FieldType.html" title="interface in org.mpxj">FieldType</a></code></dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>units field</dd>
</dl>
</li>
</ul>
<a name="toString--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>toString</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;toString()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/UserDefinedField.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../org/mpxj/UnitOfMeasureContainer.html" title="class in org.mpxj"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../org/mpxj/UserDefinedField.Builder.html" title="class in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/UserDefinedField.html" target="_top">Frames</a></li>
<li><a href="UserDefinedField.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.class.summary">Nested</a>&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li>Constr&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
