<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>ProjectConfig (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="ProjectConfig (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProjectConfig.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../org/mpxj/ProjectCodeValue.Builder.html" title="class in org.mpxj"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../org/mpxj/ProjectDateFormat.html" title="enum in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/ProjectConfig.html" target="_top">Frames</a></li>
<li><a href="ProjectConfig.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj</div>
<h2 title="Class ProjectConfig" class="title">Class ProjectConfig</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.ProjectConfig</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">ProjectConfig</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block">Container for configuration details used to control the behaviour of the ProjectFile class.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#ProjectConfig--">ProjectConfig</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#getAutoAssignmentUniqueID--">getAutoAssignmentUniqueID</a></span>()</code>
<div class="block">Retrieve the flag that determines whether the assignment unique ID
 is generated automatically.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#getAutoCalendarUniqueID--">getAutoCalendarUniqueID</a></span>()</code>
<div class="block">Retrieve the flag that determines whether the calendar unique ID
 is generated automatically.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#getAutoOutlineLevel--">getAutoOutlineLevel</a></span>()</code>
<div class="block">Retrieve the flag that determines whether outline level is generated
 automatically.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#getAutoOutlineNumber--">getAutoOutlineNumber</a></span>()</code>
<div class="block">Retrieve the flag that determines whether outline numbers are generated
 automatically.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#getAutoRelationUniqueID--">getAutoRelationUniqueID</a></span>()</code>
<div class="block">Retrieve the flag that determines whether the relation unique ID
 is generated automatically.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#getAutoResourceID--">getAutoResourceID</a></span>()</code>
<div class="block">Retrieve the flag that determines whether the resource ID
 is generated automatically.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#getAutoResourceUniqueID--">getAutoResourceUniqueID</a></span>()</code>
<div class="block">Retrieve the flag that determines whether the resource unique ID
 is generated automatically.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#getAutoTaskID--">getAutoTaskID</a></span>()</code>
<div class="block">Retrieve the flag that determines whether the task ID
 is generated automatically.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#getAutoTaskUniqueID--">getAutoTaskUniqueID</a></span>()</code>
<div class="block">Retrieve the flag that determines whether the task unique ID
 is generated automatically.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#getAutoWBS--">getAutoWBS</a></span>()</code>
<div class="block">Retrieve the flag that determines whether WBS is generated
 automatically.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="../../org/mpxj/BaselineStrategy.html" title="interface in org.mpxj">BaselineStrategy</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#getBaselineStrategy--">getBaselineStrategy</a></span>()</code>
<div class="block">Retrieve the strategy used by this project to populate baseline attributes from another schedule.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code>boolean</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#getCompleteThroughIsNextWorkStart--">getCompleteThroughIsNextWorkStart</a></span>()</code>
<div class="block">Returns true if a task's Complete Through attribute is reported as
 the time work can next start.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#getSubprojectWorkingDirectory--">getSubprojectWorkingDirectory</a></span>()</code>
<div class="block">Retrieve the directory to search for subproject files.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#setAutoAssignmentUniqueID-boolean-">setAutoAssignmentUniqueID</a></span>(boolean&nbsp;flag)</code>
<div class="block">Used to set whether the assignment unique ID field is automatically populated.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#setAutoCalendarUniqueID-boolean-">setAutoCalendarUniqueID</a></span>(boolean&nbsp;flag)</code>
<div class="block">Used to set whether the calendar unique ID field is automatically populated.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#setAutoOutlineLevel-boolean-">setAutoOutlineLevel</a></span>(boolean&nbsp;flag)</code>
<div class="block">Used to set whether outline level numbers are automatically created.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#setAutoOutlineNumber-boolean-">setAutoOutlineNumber</a></span>(boolean&nbsp;flag)</code>
<div class="block">Used to set whether outline numbers are automatically created.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#setAutoRelationUniqueID-boolean-">setAutoRelationUniqueID</a></span>(boolean&nbsp;flag)</code>
<div class="block">Used to set whether the relation unique ID field is automatically populated.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#setAutoResourceID-boolean-">setAutoResourceID</a></span>(boolean&nbsp;flag)</code>
<div class="block">Used to set whether the resource ID field is automatically populated.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#setAutoResourceUniqueID-boolean-">setAutoResourceUniqueID</a></span>(boolean&nbsp;flag)</code>
<div class="block">Used to set whether the resource unique ID field is automatically populated.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#setAutoTaskID-boolean-">setAutoTaskID</a></span>(boolean&nbsp;flag)</code>
<div class="block">Used to set whether the task ID field is automatically populated.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#setAutoTaskUniqueID-boolean-">setAutoTaskUniqueID</a></span>(boolean&nbsp;flag)</code>
<div class="block">Used to set whether the task unique ID field is automatically populated.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#setAutoWBS-boolean-">setAutoWBS</a></span>(boolean&nbsp;flag)</code>
<div class="block">Used to set whether WBS numbers are automatically created.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#setBaselineStrategy-org.mpxj.BaselineStrategy-">setBaselineStrategy</a></span>(<a href="../../org/mpxj/BaselineStrategy.html" title="interface in org.mpxj">BaselineStrategy</a>&nbsp;strategy)</code>
<div class="block">Set the strategy used by this project to populate baseline attributes from another schedule.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#setCompleteThroughIsNextWorkStart-boolean-">setCompleteThroughIsNextWorkStart</a></span>(boolean&nbsp;completeThroughIsNextWorkStart)</code>
<div class="block">When set to true a task's Complete Through attribute is reported as
 the time work can next start.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../org/mpxj/ProjectConfig.html#setSubprojectWorkingDirectory-java.io.File-">setSubprojectWorkingDirectory</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;workingDirectory)</code>
<div class="block">Specify a directory to use when searching for subproject files to expand.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="ProjectConfig--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>ProjectConfig</h4>
<pre>public&nbsp;ProjectConfig()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="setAutoWBS-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAutoWBS</h4>
<pre>public&nbsp;void&nbsp;setAutoWBS(boolean&nbsp;flag)</pre>
<div class="block">Used to set whether WBS numbers are automatically created.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>flag</code> - true if automatic WBS required.</dd>
</dl>
</li>
</ul>
<a name="setAutoOutlineLevel-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAutoOutlineLevel</h4>
<pre>public&nbsp;void&nbsp;setAutoOutlineLevel(boolean&nbsp;flag)</pre>
<div class="block">Used to set whether outline level numbers are automatically created.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>flag</code> - true if automatic outline level required.</dd>
</dl>
</li>
</ul>
<a name="setAutoOutlineNumber-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAutoOutlineNumber</h4>
<pre>public&nbsp;void&nbsp;setAutoOutlineNumber(boolean&nbsp;flag)</pre>
<div class="block">Used to set whether outline numbers are automatically created.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>flag</code> - true if automatic outline number required.</dd>
</dl>
</li>
</ul>
<a name="setAutoTaskUniqueID-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAutoTaskUniqueID</h4>
<pre>public&nbsp;void&nbsp;setAutoTaskUniqueID(boolean&nbsp;flag)</pre>
<div class="block">Used to set whether the task unique ID field is automatically populated.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>flag</code> - true if automatic unique ID required.</dd>
</dl>
</li>
</ul>
<a name="setAutoCalendarUniqueID-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAutoCalendarUniqueID</h4>
<pre>public&nbsp;void&nbsp;setAutoCalendarUniqueID(boolean&nbsp;flag)</pre>
<div class="block">Used to set whether the calendar unique ID field is automatically populated.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>flag</code> - true if automatic unique ID required.</dd>
</dl>
</li>
</ul>
<a name="setAutoAssignmentUniqueID-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAutoAssignmentUniqueID</h4>
<pre>public&nbsp;void&nbsp;setAutoAssignmentUniqueID(boolean&nbsp;flag)</pre>
<div class="block">Used to set whether the assignment unique ID field is automatically populated.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>flag</code> - true if automatic unique ID required.</dd>
</dl>
</li>
</ul>
<a name="setAutoTaskID-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAutoTaskID</h4>
<pre>public&nbsp;void&nbsp;setAutoTaskID(boolean&nbsp;flag)</pre>
<div class="block">Used to set whether the task ID field is automatically populated.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>flag</code> - true if automatic ID required.</dd>
</dl>
</li>
</ul>
<a name="getAutoWBS--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAutoWBS</h4>
<pre>public&nbsp;boolean&nbsp;getAutoWBS()</pre>
<div class="block">Retrieve the flag that determines whether WBS is generated
 automatically.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean, default is false.</dd>
</dl>
</li>
</ul>
<a name="getAutoOutlineLevel--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAutoOutlineLevel</h4>
<pre>public&nbsp;boolean&nbsp;getAutoOutlineLevel()</pre>
<div class="block">Retrieve the flag that determines whether outline level is generated
 automatically.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean, default is false.</dd>
</dl>
</li>
</ul>
<a name="getAutoOutlineNumber--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAutoOutlineNumber</h4>
<pre>public&nbsp;boolean&nbsp;getAutoOutlineNumber()</pre>
<div class="block">Retrieve the flag that determines whether outline numbers are generated
 automatically.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean, default is false.</dd>
</dl>
</li>
</ul>
<a name="getAutoTaskUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAutoTaskUniqueID</h4>
<pre>public&nbsp;boolean&nbsp;getAutoTaskUniqueID()</pre>
<div class="block">Retrieve the flag that determines whether the task unique ID
 is generated automatically.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean, default is false.</dd>
</dl>
</li>
</ul>
<a name="getAutoCalendarUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAutoCalendarUniqueID</h4>
<pre>public&nbsp;boolean&nbsp;getAutoCalendarUniqueID()</pre>
<div class="block">Retrieve the flag that determines whether the calendar unique ID
 is generated automatically.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean, default is false.</dd>
</dl>
</li>
</ul>
<a name="getAutoAssignmentUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAutoAssignmentUniqueID</h4>
<pre>public&nbsp;boolean&nbsp;getAutoAssignmentUniqueID()</pre>
<div class="block">Retrieve the flag that determines whether the assignment unique ID
 is generated automatically.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean, default is true.</dd>
</dl>
</li>
</ul>
<a name="getAutoTaskID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAutoTaskID</h4>
<pre>public&nbsp;boolean&nbsp;getAutoTaskID()</pre>
<div class="block">Retrieve the flag that determines whether the task ID
 is generated automatically.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean, default is false.</dd>
</dl>
</li>
</ul>
<a name="setAutoResourceUniqueID-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAutoResourceUniqueID</h4>
<pre>public&nbsp;void&nbsp;setAutoResourceUniqueID(boolean&nbsp;flag)</pre>
<div class="block">Used to set whether the resource unique ID field is automatically populated.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>flag</code> - true if automatic unique ID required.</dd>
</dl>
</li>
</ul>
<a name="setAutoResourceID-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAutoResourceID</h4>
<pre>public&nbsp;void&nbsp;setAutoResourceID(boolean&nbsp;flag)</pre>
<div class="block">Used to set whether the resource ID field is automatically populated.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>flag</code> - true if automatic ID required.</dd>
</dl>
</li>
</ul>
<a name="getAutoResourceUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAutoResourceUniqueID</h4>
<pre>public&nbsp;boolean&nbsp;getAutoResourceUniqueID()</pre>
<div class="block">Retrieve the flag that determines whether the resource unique ID
 is generated automatically.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean, default is false.</dd>
</dl>
</li>
</ul>
<a name="getAutoResourceID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAutoResourceID</h4>
<pre>public&nbsp;boolean&nbsp;getAutoResourceID()</pre>
<div class="block">Retrieve the flag that determines whether the resource ID
 is generated automatically.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean, default is false.</dd>
</dl>
</li>
</ul>
<a name="setAutoRelationUniqueID-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAutoRelationUniqueID</h4>
<pre>public&nbsp;void&nbsp;setAutoRelationUniqueID(boolean&nbsp;flag)</pre>
<div class="block">Used to set whether the relation unique ID field is automatically populated.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>flag</code> - true if automatic unique ID required.</dd>
</dl>
</li>
</ul>
<a name="getAutoRelationUniqueID--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAutoRelationUniqueID</h4>
<pre>public&nbsp;boolean&nbsp;getAutoRelationUniqueID()</pre>
<div class="block">Retrieve the flag that determines whether the relation unique ID
 is generated automatically.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>boolean, default is false.</dd>
</dl>
</li>
</ul>
<a name="getCompleteThroughIsNextWorkStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompleteThroughIsNextWorkStart</h4>
<pre>public&nbsp;boolean&nbsp;getCompleteThroughIsNextWorkStart()</pre>
<div class="block">Returns true if a task's Complete Through attribute is reported as
 the time work can next start. Defaults to false. When set to true this
 matches the behaviour of MS Project versions prior to 2007.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>true if Complete Through is next work start date</dd>
</dl>
</li>
</ul>
<a name="setCompleteThroughIsNextWorkStart-boolean-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCompleteThroughIsNextWorkStart</h4>
<pre>public&nbsp;void&nbsp;setCompleteThroughIsNextWorkStart(boolean&nbsp;completeThroughIsNextWorkStart)</pre>
<div class="block">When set to true a task's Complete Through attribute is reported as
 the time work can next start. Defaults to false. When set to true this
 matches the behaviour of MS Project versions prior to 2007.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>completeThroughIsNextWorkStart</code> - true if Complete Through is next work start date</dd>
</dl>
</li>
</ul>
<a name="getBaselineStrategy--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getBaselineStrategy</h4>
<pre>public&nbsp;<a href="../../org/mpxj/BaselineStrategy.html" title="interface in org.mpxj">BaselineStrategy</a>&nbsp;getBaselineStrategy()</pre>
<div class="block">Retrieve the strategy used by this project to populate baseline attributes from another schedule.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>baseline strategy</dd>
</dl>
</li>
</ul>
<a name="setBaselineStrategy-org.mpxj.BaselineStrategy-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setBaselineStrategy</h4>
<pre>public&nbsp;void&nbsp;setBaselineStrategy(<a href="../../org/mpxj/BaselineStrategy.html" title="interface in org.mpxj">BaselineStrategy</a>&nbsp;strategy)</pre>
<div class="block">Set the strategy used by this project to populate baseline attributes from another schedule.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>strategy</code> - baseline strategy</dd>
</dl>
</li>
</ul>
<a name="setSubprojectWorkingDirectory-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setSubprojectWorkingDirectory</h4>
<pre>public&nbsp;void&nbsp;setSubprojectWorkingDirectory(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;workingDirectory)</pre>
<div class="block">Specify a directory to use when searching for subproject files to expand.
 MPXJ will attempt to use the full path of a subproject when attempting
 to expand it, or the process working directory. If a value
 is supplied here, this directory will be used instead of the process
 working directory.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>workingDirectory</code> - directory to search for subproject files</dd>
</dl>
</li>
</ul>
<a name="getSubprojectWorkingDirectory--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getSubprojectWorkingDirectory</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;getSubprojectWorkingDirectory()</pre>
<div class="block">Retrieve the directory to search for subproject files.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>directory to search for subproject files</dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/ProjectConfig.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../index-all.html">Index</a></li>
<li><a href="../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../org/mpxj/ProjectCodeValue.Builder.html" title="class in org.mpxj"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../org/mpxj/ProjectDateFormat.html" title="enum in org.mpxj"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../index.html?org/mpxj/ProjectConfig.html" target="_top">Frames</a></li>
<li><a href="ProjectConfig.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
