<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>LocaleData_zh (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="LocaleData_zh (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/LocaleData_zh.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/mpxj/mpx/LocaleData_sv.html" title="class in org.mpxj.mpx"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/mpxj/mpx/MPXJFormats.html" title="class in org.mpxj.mpx"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/mpx/LocaleData_zh.html" target="_top">Frames</a></li>
<li><a href="LocaleData_zh.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.java.util.ResourceBundle">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.java.util.ResourceBundle">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.mpx</div>
<h2 title="Class LocaleData_zh" class="title">Class LocaleData_zh</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/util/ResourceBundle.html?is-external=true" title="class or interface in java.util">java.util.ResourceBundle</a></li>
<li>
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/util/ListResourceBundle.html?is-external=true" title="class or interface in java.util">java.util.ListResourceBundle</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.mpx.LocaleData_zh</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public final class <span class="typeNameLabel">LocaleData_zh</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/util/ListResourceBundle.html?is-external=true" title="class or interface in java.util">ListResourceBundle</a></pre>
<div class="block">This class defines the Chinese translation of resource required by MPX files.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== NESTED CLASS SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="nested.class.summary">
<!--   -->
</a>
<h3>Nested Class Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="nested.classes.inherited.from.class.java.util.ResourceBundle">
<!--   -->
</a>
<h3>Nested classes/interfaces inherited from class&nbsp;java.util.<a href="https://docs.oracle.com/javase/8/docs/api/java/util/ResourceBundle.html?is-external=true" title="class or interface in java.util">ResourceBundle</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/ResourceBundle.Control.html?is-external=true" title="class or interface in java.util">ResourceBundle.Control</a></code></li>
</ul>
</li>
</ul>
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<ul class="blockList">
<li class="blockList"><a name="fields.inherited.from.class.java.util.ResourceBundle">
<!--   -->
</a>
<h3>Fields inherited from class&nbsp;java.util.<a href="https://docs.oracle.com/javase/8/docs/api/java/util/ResourceBundle.html?is-external=true" title="class or interface in java.util">ResourceBundle</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/ResourceBundle.html?is-external=true#parent" title="class or interface in java.util">parent</a></code></li>
</ul>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpx/LocaleData_zh.html#LocaleData_zh--">LocaleData_zh</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>[][]</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/mpx/LocaleData_zh.html#getContents--">getContents</a></span>()</code>&nbsp;</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.ListResourceBundle">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.util.<a href="https://docs.oracle.com/javase/8/docs/api/java/util/ListResourceBundle.html?is-external=true" title="class or interface in java.util">ListResourceBundle</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/ListResourceBundle.html?is-external=true#getKeys--" title="class or interface in java.util">getKeys</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/ListResourceBundle.html?is-external=true#handleGetObject-java.lang.String-" title="class or interface in java.util">handleGetObject</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/ListResourceBundle.html?is-external=true#handleKeySet--" title="class or interface in java.util">handleKeySet</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.util.ResourceBundle">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.util.<a href="https://docs.oracle.com/javase/8/docs/api/java/util/ResourceBundle.html?is-external=true" title="class or interface in java.util">ResourceBundle</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/ResourceBundle.html?is-external=true#clearCache--" title="class or interface in java.util">clearCache</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/ResourceBundle.html?is-external=true#clearCache-java.lang.ClassLoader-" title="class or interface in java.util">clearCache</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/ResourceBundle.html?is-external=true#containsKey-java.lang.String-" title="class or interface in java.util">containsKey</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/ResourceBundle.html?is-external=true#getBaseBundleName--" title="class or interface in java.util">getBaseBundleName</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/ResourceBundle.html?is-external=true#getBundle-java.lang.String-" title="class or interface in java.util">getBundle</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/ResourceBundle.html?is-external=true#getBundle-java.lang.String-java.util.Locale-" title="class or interface in java.util">getBundle</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/ResourceBundle.html?is-external=true#getBundle-java.lang.String-java.util.Locale-java.lang.ClassLoader-" title="class or interface in java.util">getBundle</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/ResourceBundle.html?is-external=true#getBundle-java.lang.String-java.util.Locale-java.lang.ClassLoader-java.util.ResourceBundle.Control-" title="class or interface in java.util">getBundle</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/ResourceBundle.html?is-external=true#getBundle-java.lang.String-java.util.Locale-java.util.ResourceBundle.Control-" title="class or interface in java.util">getBundle</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/ResourceBundle.html?is-external=true#getBundle-java.lang.String-java.util.ResourceBundle.Control-" title="class or interface in java.util">getBundle</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/ResourceBundle.html?is-external=true#getLocale--" title="class or interface in java.util">getLocale</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/ResourceBundle.html?is-external=true#getObject-java.lang.String-" title="class or interface in java.util">getObject</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/ResourceBundle.html?is-external=true#getString-java.lang.String-" title="class or interface in java.util">getString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/ResourceBundle.html?is-external=true#getStringArray-java.lang.String-" title="class or interface in java.util">getStringArray</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/ResourceBundle.html?is-external=true#keySet--" title="class or interface in java.util">keySet</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/util/ResourceBundle.html?is-external=true#setParent-java.util.ResourceBundle-" title="class or interface in java.util">setParent</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="LocaleData_zh--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>LocaleData_zh</h4>
<pre>public&nbsp;LocaleData_zh()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getContents--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>getContents</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a>[][]&nbsp;getContents()</pre>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/ListResourceBundle.html?is-external=true#getContents--" title="class or interface in java.util">getContents</a></code>&nbsp;in class&nbsp;<code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/ListResourceBundle.html?is-external=true" title="class or interface in java.util">ListResourceBundle</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/LocaleData_zh.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/mpxj/mpx/LocaleData_sv.html" title="class in org.mpxj.mpx"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/mpxj/mpx/MPXJFormats.html" title="class in org.mpxj.mpx"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/mpx/LocaleData_zh.html" target="_top">Frames</a></li>
<li><a href="LocaleData_zh.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li><a href="#nested.classes.inherited.from.class.java.util.ResourceBundle">Nested</a>&nbsp;|&nbsp;</li>
<li><a href="#fields.inherited.from.class.java.util.ResourceBundle">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
