<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>Project (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="Project (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10,"i9":10,"i10":10,"i11":10,"i12":10,"i13":10,"i14":10,"i15":10,"i16":10,"i17":10,"i18":10,"i19":10,"i20":10,"i21":10,"i22":10,"i23":10,"i24":10,"i25":10,"i26":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Project.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/planner/schema/Predecessors.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/planner/schema/Properties.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/planner/schema/Project.html" target="_top">Frames</a></li>
<li><a href="Project.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.planner.schema</div>
<h2 title="Class Project" class="title">Class Project</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.planner.schema.Project</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<hr>
<br>
<pre>public class <span class="typeNameLabel">Project</span>
extends <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></pre>
<div class="block"><p>Java class for anonymous complex type.

 <p>The following schema fragment specifies the expected content contained within this class.

 <pre>
 &lt;complexType&gt;
   &lt;complexContent&gt;
     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
       &lt;sequence&gt;
         &lt;element ref="{}properties" maxOccurs="unbounded" minOccurs="0"/&gt;
         &lt;element ref="{}phases" minOccurs="0"/&gt;
         &lt;element ref="{}calendars" minOccurs="0"/&gt;
         &lt;element ref="{}tasks" minOccurs="0"/&gt;
         &lt;element ref="{}resource-groups" minOccurs="0"/&gt;
         &lt;element ref="{}resources" minOccurs="0"/&gt;
         &lt;element ref="{}allocations" minOccurs="0"/&gt;
       &lt;/sequence&gt;
       &lt;attribute name="mrproject-version" use="required" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       &lt;attribute name="name" use="required" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       &lt;attribute name="company" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       &lt;attribute name="manager" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       &lt;attribute name="project-start" use="required" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       &lt;attribute name="calendar" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
       &lt;attribute name="phase" type="{http://www.w3.org/2001/XMLSchema}string" /&gt;
     &lt;/restriction&gt;
   &lt;/complexContent&gt;
 &lt;/complexType&gt;
 </pre></div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- =========== FIELD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.summary">
<!--   -->
</a>
<h3>Field Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Field Summary table, listing fields, and an explanation">
<caption><span>Fields</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Field and Description</th>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/planner/schema/Allocations.html" title="class in org.mpxj.planner.schema">Allocations</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#allocations">allocations</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#calendar">calendar</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/planner/schema/Calendars.html" title="class in org.mpxj.planner.schema">Calendars</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#calendars">calendars</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#company">company</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#manager">manager</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#mrprojectVersion">mrprojectVersion</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#name">name</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#phase">phase</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/planner/schema/Phases.html" title="class in org.mpxj.planner.schema">Phases</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#phases">phases</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#projectStart">projectStart</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/planner/schema/Properties.html" title="class in org.mpxj.planner.schema">Properties</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#properties">properties</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/planner/schema/ResourceGroups.html" title="class in org.mpxj.planner.schema">ResourceGroups</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#resourceGroups">resourceGroups</a></span></code>&nbsp;</td>
</tr>
<tr class="altColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/planner/schema/Resources.html" title="class in org.mpxj.planner.schema">Resources</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#resources">resources</a></span></code>&nbsp;</td>
</tr>
<tr class="rowColor">
<td class="colFirst"><code>protected <a href="../../../../org/mpxj/planner/schema/Tasks.html" title="class in org.mpxj.planner.schema">Tasks</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#tasks">tasks</a></span></code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#Project--">Project</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/planner/schema/Allocations.html" title="class in org.mpxj.planner.schema">Allocations</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#getAllocations--">getAllocations</a></span>()</code>
<div class="block">Gets the value of the allocations property.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#getCalendar--">getCalendar</a></span>()</code>
<div class="block">Gets the value of the calendar property.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/planner/schema/Calendars.html" title="class in org.mpxj.planner.schema">Calendars</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#getCalendars--">getCalendars</a></span>()</code>
<div class="block">Gets the value of the calendars property.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#getCompany--">getCompany</a></span>()</code>
<div class="block">Gets the value of the company property.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#getManager--">getManager</a></span>()</code>
<div class="block">Gets the value of the manager property.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#getMrprojectVersion--">getMrprojectVersion</a></span>()</code>
<div class="block">Gets the value of the mrprojectVersion property.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#getName--">getName</a></span>()</code>
<div class="block">Gets the value of the name property.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#getPhase--">getPhase</a></span>()</code>
<div class="block">Gets the value of the phase property.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/planner/schema/Phases.html" title="class in org.mpxj.planner.schema">Phases</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#getPhases--">getPhases</a></span>()</code>
<div class="block">Gets the value of the phases property.</div>
</td>
</tr>
<tr id="i9" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#getProjectStart--">getProjectStart</a></span>()</code>
<div class="block">Gets the value of the projectStart property.</div>
</td>
</tr>
<tr id="i10" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/planner/schema/Properties.html" title="class in org.mpxj.planner.schema">Properties</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#getProperties--">getProperties</a></span>()</code>
<div class="block">Gets the value of the properties property.</div>
</td>
</tr>
<tr id="i11" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/planner/schema/ResourceGroups.html" title="class in org.mpxj.planner.schema">ResourceGroups</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#getResourceGroups--">getResourceGroups</a></span>()</code>
<div class="block">Gets the value of the resourceGroups property.</div>
</td>
</tr>
<tr id="i12" class="altColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/planner/schema/Resources.html" title="class in org.mpxj.planner.schema">Resources</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#getResources--">getResources</a></span>()</code>
<div class="block">Gets the value of the resources property.</div>
</td>
</tr>
<tr id="i13" class="rowColor">
<td class="colFirst"><code><a href="../../../../org/mpxj/planner/schema/Tasks.html" title="class in org.mpxj.planner.schema">Tasks</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#getTasks--">getTasks</a></span>()</code>
<div class="block">Gets the value of the tasks property.</div>
</td>
</tr>
<tr id="i14" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#setAllocations-org.mpxj.planner.schema.Allocations-">setAllocations</a></span>(<a href="../../../../org/mpxj/planner/schema/Allocations.html" title="class in org.mpxj.planner.schema">Allocations</a>&nbsp;value)</code>
<div class="block">Sets the value of the allocations property.</div>
</td>
</tr>
<tr id="i15" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#setCalendar-java.lang.String-">setCalendar</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the calendar property.</div>
</td>
</tr>
<tr id="i16" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#setCalendars-org.mpxj.planner.schema.Calendars-">setCalendars</a></span>(<a href="../../../../org/mpxj/planner/schema/Calendars.html" title="class in org.mpxj.planner.schema">Calendars</a>&nbsp;value)</code>
<div class="block">Sets the value of the calendars property.</div>
</td>
</tr>
<tr id="i17" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#setCompany-java.lang.String-">setCompany</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the company property.</div>
</td>
</tr>
<tr id="i18" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#setManager-java.lang.String-">setManager</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the manager property.</div>
</td>
</tr>
<tr id="i19" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#setMrprojectVersion-java.lang.String-">setMrprojectVersion</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the mrprojectVersion property.</div>
</td>
</tr>
<tr id="i20" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#setName-java.lang.String-">setName</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the name property.</div>
</td>
</tr>
<tr id="i21" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#setPhase-java.lang.String-">setPhase</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the phase property.</div>
</td>
</tr>
<tr id="i22" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#setPhases-org.mpxj.planner.schema.Phases-">setPhases</a></span>(<a href="../../../../org/mpxj/planner/schema/Phases.html" title="class in org.mpxj.planner.schema">Phases</a>&nbsp;value)</code>
<div class="block">Sets the value of the phases property.</div>
</td>
</tr>
<tr id="i23" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#setProjectStart-java.lang.String-">setProjectStart</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</code>
<div class="block">Sets the value of the projectStart property.</div>
</td>
</tr>
<tr id="i24" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#setResourceGroups-org.mpxj.planner.schema.ResourceGroups-">setResourceGroups</a></span>(<a href="../../../../org/mpxj/planner/schema/ResourceGroups.html" title="class in org.mpxj.planner.schema">ResourceGroups</a>&nbsp;value)</code>
<div class="block">Sets the value of the resourceGroups property.</div>
</td>
</tr>
<tr id="i25" class="rowColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#setResources-org.mpxj.planner.schema.Resources-">setResources</a></span>(<a href="../../../../org/mpxj/planner/schema/Resources.html" title="class in org.mpxj.planner.schema">Resources</a>&nbsp;value)</code>
<div class="block">Sets the value of the resources property.</div>
</td>
</tr>
<tr id="i26" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../../org/mpxj/planner/schema/Project.html#setTasks-org.mpxj.planner.schema.Tasks-">setTasks</a></span>(<a href="../../../../org/mpxj/planner/schema/Tasks.html" title="class in org.mpxj.planner.schema">Tasks</a>&nbsp;value)</code>
<div class="block">Sets the value of the tasks property.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ============ FIELD DETAIL =========== -->
<ul class="blockList">
<li class="blockList"><a name="field.detail">
<!--   -->
</a>
<h3>Field Detail</h3>
<a name="properties">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>properties</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/planner/schema/Properties.html" title="class in org.mpxj.planner.schema">Properties</a>&gt; properties</pre>
</li>
</ul>
<a name="phases">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>phases</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/planner/schema/Phases.html" title="class in org.mpxj.planner.schema">Phases</a> phases</pre>
</li>
</ul>
<a name="calendars">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calendars</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/planner/schema/Calendars.html" title="class in org.mpxj.planner.schema">Calendars</a> calendars</pre>
</li>
</ul>
<a name="tasks">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>tasks</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/planner/schema/Tasks.html" title="class in org.mpxj.planner.schema">Tasks</a> tasks</pre>
</li>
</ul>
<a name="resourceGroups">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resourceGroups</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/planner/schema/ResourceGroups.html" title="class in org.mpxj.planner.schema">ResourceGroups</a> resourceGroups</pre>
</li>
</ul>
<a name="resources">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>resources</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/planner/schema/Resources.html" title="class in org.mpxj.planner.schema">Resources</a> resources</pre>
</li>
</ul>
<a name="allocations">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>allocations</h4>
<pre>protected&nbsp;<a href="../../../../org/mpxj/planner/schema/Allocations.html" title="class in org.mpxj.planner.schema">Allocations</a> allocations</pre>
</li>
</ul>
<a name="mrprojectVersion">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>mrprojectVersion</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> mrprojectVersion</pre>
</li>
</ul>
<a name="name">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>name</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> name</pre>
</li>
</ul>
<a name="company">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>company</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> company</pre>
</li>
</ul>
<a name="manager">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>manager</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> manager</pre>
</li>
</ul>
<a name="projectStart">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>projectStart</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> projectStart</pre>
</li>
</ul>
<a name="calendar">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>calendar</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> calendar</pre>
</li>
</ul>
<a name="phase">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>phase</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a> phase</pre>
</li>
</ul>
</li>
</ul>
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="Project--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>Project</h4>
<pre>public&nbsp;Project()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getProperties--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProperties</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../../org/mpxj/planner/schema/Properties.html" title="class in org.mpxj.planner.schema">Properties</a>&gt;&nbsp;getProperties()</pre>
<div class="block">Gets the value of the properties property.

 <p>
 This accessor method returns a reference to the live list,
 not a snapshot. Therefore any modification you make to the
 returned list will be present inside the Jakarta XML Binding object.
 This is why there is not a <CODE>set</CODE> method for the properties property.

 <p>
 For example, to add a new item, do as follows:
 <pre>
    getProperties().add(newItem);
 </pre>


 <p>
 Objects of the following type(s) are allowed in the list
 <a href="../../../../org/mpxj/planner/schema/Properties.html" title="class in org.mpxj.planner.schema"><code>Properties</code></a></div>
</li>
</ul>
<a name="getPhases--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPhases</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/planner/schema/Phases.html" title="class in org.mpxj.planner.schema">Phases</a>&nbsp;getPhases()</pre>
<div class="block">Gets the value of the phases property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/planner/schema/Phases.html" title="class in org.mpxj.planner.schema"><code>Phases</code></a></dd>
</dl>
</li>
</ul>
<a name="setPhases-org.mpxj.planner.schema.Phases-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setPhases</h4>
<pre>public&nbsp;void&nbsp;setPhases(<a href="../../../../org/mpxj/planner/schema/Phases.html" title="class in org.mpxj.planner.schema">Phases</a>&nbsp;value)</pre>
<div class="block">Sets the value of the phases property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/planner/schema/Phases.html" title="class in org.mpxj.planner.schema"><code>Phases</code></a></dd>
</dl>
</li>
</ul>
<a name="getCalendars--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCalendars</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/planner/schema/Calendars.html" title="class in org.mpxj.planner.schema">Calendars</a>&nbsp;getCalendars()</pre>
<div class="block">Gets the value of the calendars property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/planner/schema/Calendars.html" title="class in org.mpxj.planner.schema"><code>Calendars</code></a></dd>
</dl>
</li>
</ul>
<a name="setCalendars-org.mpxj.planner.schema.Calendars-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCalendars</h4>
<pre>public&nbsp;void&nbsp;setCalendars(<a href="../../../../org/mpxj/planner/schema/Calendars.html" title="class in org.mpxj.planner.schema">Calendars</a>&nbsp;value)</pre>
<div class="block">Sets the value of the calendars property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/planner/schema/Calendars.html" title="class in org.mpxj.planner.schema"><code>Calendars</code></a></dd>
</dl>
</li>
</ul>
<a name="getTasks--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getTasks</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/planner/schema/Tasks.html" title="class in org.mpxj.planner.schema">Tasks</a>&nbsp;getTasks()</pre>
<div class="block">Gets the value of the tasks property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/planner/schema/Tasks.html" title="class in org.mpxj.planner.schema"><code>Tasks</code></a></dd>
</dl>
</li>
</ul>
<a name="setTasks-org.mpxj.planner.schema.Tasks-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setTasks</h4>
<pre>public&nbsp;void&nbsp;setTasks(<a href="../../../../org/mpxj/planner/schema/Tasks.html" title="class in org.mpxj.planner.schema">Tasks</a>&nbsp;value)</pre>
<div class="block">Sets the value of the tasks property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/planner/schema/Tasks.html" title="class in org.mpxj.planner.schema"><code>Tasks</code></a></dd>
</dl>
</li>
</ul>
<a name="getResourceGroups--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResourceGroups</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/planner/schema/ResourceGroups.html" title="class in org.mpxj.planner.schema">ResourceGroups</a>&nbsp;getResourceGroups()</pre>
<div class="block">Gets the value of the resourceGroups property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/planner/schema/ResourceGroups.html" title="class in org.mpxj.planner.schema"><code>ResourceGroups</code></a></dd>
</dl>
</li>
</ul>
<a name="setResourceGroups-org.mpxj.planner.schema.ResourceGroups-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResourceGroups</h4>
<pre>public&nbsp;void&nbsp;setResourceGroups(<a href="../../../../org/mpxj/planner/schema/ResourceGroups.html" title="class in org.mpxj.planner.schema">ResourceGroups</a>&nbsp;value)</pre>
<div class="block">Sets the value of the resourceGroups property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/planner/schema/ResourceGroups.html" title="class in org.mpxj.planner.schema"><code>ResourceGroups</code></a></dd>
</dl>
</li>
</ul>
<a name="getResources--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getResources</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/planner/schema/Resources.html" title="class in org.mpxj.planner.schema">Resources</a>&nbsp;getResources()</pre>
<div class="block">Gets the value of the resources property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/planner/schema/Resources.html" title="class in org.mpxj.planner.schema"><code>Resources</code></a></dd>
</dl>
</li>
</ul>
<a name="setResources-org.mpxj.planner.schema.Resources-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setResources</h4>
<pre>public&nbsp;void&nbsp;setResources(<a href="../../../../org/mpxj/planner/schema/Resources.html" title="class in org.mpxj.planner.schema">Resources</a>&nbsp;value)</pre>
<div class="block">Sets the value of the resources property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/planner/schema/Resources.html" title="class in org.mpxj.planner.schema"><code>Resources</code></a></dd>
</dl>
</li>
</ul>
<a name="getAllocations--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getAllocations</h4>
<pre>public&nbsp;<a href="../../../../org/mpxj/planner/schema/Allocations.html" title="class in org.mpxj.planner.schema">Allocations</a>&nbsp;getAllocations()</pre>
<div class="block">Gets the value of the allocations property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="../../../../org/mpxj/planner/schema/Allocations.html" title="class in org.mpxj.planner.schema"><code>Allocations</code></a></dd>
</dl>
</li>
</ul>
<a name="setAllocations-org.mpxj.planner.schema.Allocations-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setAllocations</h4>
<pre>public&nbsp;void&nbsp;setAllocations(<a href="../../../../org/mpxj/planner/schema/Allocations.html" title="class in org.mpxj.planner.schema">Allocations</a>&nbsp;value)</pre>
<div class="block">Sets the value of the allocations property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="../../../../org/mpxj/planner/schema/Allocations.html" title="class in org.mpxj.planner.schema"><code>Allocations</code></a></dd>
</dl>
</li>
</ul>
<a name="getMrprojectVersion--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getMrprojectVersion</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getMrprojectVersion()</pre>
<div class="block">Gets the value of the mrprojectVersion property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setMrprojectVersion-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setMrprojectVersion</h4>
<pre>public&nbsp;void&nbsp;setMrprojectVersion(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the mrprojectVersion property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getName--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getName</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getName()</pre>
<div class="block">Gets the value of the name property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setName-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setName</h4>
<pre>public&nbsp;void&nbsp;setName(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the name property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCompany--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCompany</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getCompany()</pre>
<div class="block">Gets the value of the company property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCompany-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCompany</h4>
<pre>public&nbsp;void&nbsp;setCompany(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the company property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getManager--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getManager</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getManager()</pre>
<div class="block">Gets the value of the manager property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setManager-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setManager</h4>
<pre>public&nbsp;void&nbsp;setManager(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the manager property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getProjectStart--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getProjectStart</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getProjectStart()</pre>
<div class="block">Gets the value of the projectStart property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setProjectStart-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProjectStart</h4>
<pre>public&nbsp;void&nbsp;setProjectStart(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the projectStart property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getCalendar--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getCalendar</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getCalendar()</pre>
<div class="block">Gets the value of the calendar property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setCalendar-java.lang.String-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setCalendar</h4>
<pre>public&nbsp;void&nbsp;setCalendar(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the calendar property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="getPhase--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getPhase</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;getPhase()</pre>
<div class="block">Gets the value of the phase property.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>possible object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
<a name="setPhase-java.lang.String-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>setPhase</h4>
<pre>public&nbsp;void&nbsp;setPhase(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;value)</pre>
<div class="block">Sets the value of the phase property.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>value</code> - allowed object is
     <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang"><code>String</code></a></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/Project.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/planner/schema/Predecessors.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../../org/mpxj/planner/schema/Properties.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/planner/schema/Project.html" target="_top">Frames</a></li>
<li><a href="Project.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li><a href="#field.summary">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li><a href="#field.detail">Field</a>&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
