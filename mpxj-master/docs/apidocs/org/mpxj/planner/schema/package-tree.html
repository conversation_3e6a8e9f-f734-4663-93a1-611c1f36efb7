<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>org.mpxj.planner.schema Class Hierarchy (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="org.mpxj.planner.schema Class Hierarchy (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/planner/package-tree.html">Prev</a></li>
<li><a href="../../../../org/mpxj/primavera/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/planner/schema/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<div class="header">
<h1 class="title">Hierarchy For Package org.mpxj.planner.schema</h1>
<span class="packageHierarchyLabel">Package Hierarchies:</span>
<ul class="horizontal">
<li><a href="../../../../overview-tree.html">All Packages</a></li>
</ul>
</div>
<div class="contentContainer">
<h2 title="Class Hierarchy">Class Hierarchy</h2>
<ul>
<li type="circle">java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang"><span class="typeNameLink">Object</span></a>
<ul>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/Allocation.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Allocation</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/Allocations.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Allocations</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/Calendar.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Calendar</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/Calendars.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Calendars</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/Constraint.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Constraint</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/Day.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Day</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/Days.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Days</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/DayType.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">DayType</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/DayTypes.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">DayTypes</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/DefaultWeek.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">DefaultWeek</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/Group.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Group</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/Interval.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Interval</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/ListItem.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">ListItem</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/ObjectFactory.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">ObjectFactory</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/OverriddenDayType.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">OverriddenDayType</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/OverriddenDayTypes.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">OverriddenDayTypes</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/Phase.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Phase</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/Phases.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Phases</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/Predecessor.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Predecessor</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/Predecessors.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Predecessors</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/Project.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Project</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/Properties.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Properties</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/Property.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Property</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/Resource.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Resource</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/ResourceGroups.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">ResourceGroups</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/Resources.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Resources</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/Task.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Task</span></a></li>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/Tasks.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Tasks</span></a></li>
<li type="circle">jakarta.xml.bind.annotation.adapters.XmlAdapter&lt;ValueType,BoundType&gt;
<ul>
<li type="circle">org.mpxj.planner.schema.<a href="../../../../org/mpxj/planner/schema/Adapter1.html" title="class in org.mpxj.planner.schema"><span class="typeNameLink">Adapter1</span></a></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li>Class</li>
<li>Use</li>
<li class="navBarCell1Rev">Tree</li>
<li><a href="../../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../../index-all.html">Index</a></li>
<li><a href="../../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../../org/mpxj/planner/package-tree.html">Prev</a></li>
<li><a href="../../../../org/mpxj/primavera/package-tree.html">Next</a></li>
</ul>
<ul class="navList">
<li><a href="../../../../index.html?org/mpxj/planner/schema/package-tree.html" target="_top">Frames</a></li>
<li><a href="package-tree.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
