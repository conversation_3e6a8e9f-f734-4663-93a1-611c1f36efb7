<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- NewPage -->
<html lang="en">
<head>
<!-- Generated by javadoc -->
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>AstaMdbReader (MPXJ 14.1.0 API)</title>
<link rel="stylesheet" type="text/css" href="../../../stylesheet.css" title="Style">
<script type="text/javascript" src="../../../script.js"></script>
</head>
					<script async src="https://www.googletagmanager.com/gtag/js?id=G-9R48LPVHKE"></script>
					<script>
					  window.dataLayer = window.dataLayer || [];
					  function gtag(){dataLayer.push(arguments);}
					  gtag('js', new Date());
					  gtag('config', 'G-9R48LPVHKE');
					</script>
<body>
<script type="text/javascript"><!--
    try {
        if (location.href.indexOf('is-external=true') == -1) {
            parent.document.title="AstaMdbReader (MPXJ 14.1.0 API)";
        }
    }
    catch(err) {
    }
//-->
var methods = {"i0":10,"i1":10,"i2":10,"i3":10,"i4":10,"i5":10,"i6":10,"i7":10,"i8":10};
var tabs = {65535:["t0","All Methods"],2:["t2","Instance Methods"],8:["t4","Concrete Methods"]};
var altColor = "altColor";
var rowColor = "rowColor";
var tableTab = "tableTab";
var activeTableTab = "activeTableTab";
</script>
<noscript>
<div>JavaScript is disabled on your browser.</div>
</noscript>
<!-- ========= START OF TOP NAVBAR ======= -->
<div class="topNav"><a name="navbar.top">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.top" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.top.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/AstaMdbReader.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/mpxj/asta/AstaJdbcReader.html" title="class in org.mpxj.asta"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/mpxj/asta/AstaSqliteReader.html" title="class in org.mpxj.asta"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/asta/AstaMdbReader.html" target="_top">Frames</a></li>
<li><a href="AstaMdbReader.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_top">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_top");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.top">
<!--   -->
</a></div>
<!-- ========= END OF TOP NAVBAR ========= -->
<!-- ======== START OF CLASS DATA ======== -->
<div class="header">
<div class="subTitle">org.mpxj.asta</div>
<h2 title="Class AstaMdbReader" class="title">Class AstaMdbReader</h2>
</div>
<div class="contentContainer">
<ul class="inheritance">
<li><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">java.lang.Object</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/mpxj/reader/AbstractProjectReader.html" title="class in org.mpxj.reader">org.mpxj.reader.AbstractProjectReader</a></li>
<li>
<ul class="inheritance">
<li><a href="../../../org/mpxj/reader/AbstractProjectFileReader.html" title="class in org.mpxj.reader">org.mpxj.reader.AbstractProjectFileReader</a></li>
<li>
<ul class="inheritance">
<li>org.mpxj.asta.AstaMdbReader</li>
</ul>
</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="description">
<ul class="blockList">
<li class="blockList">
<dl>
<dt>All Implemented Interfaces:</dt>
<dd><a href="../../../org/mpxj/reader/ProjectReader.html" title="interface in org.mpxj.reader">ProjectReader</a></dd>
</dl>
<hr>
<br>
<pre>public final class <span class="typeNameLabel">AstaMdbReader</span>
extends <a href="../../../org/mpxj/reader/AbstractProjectFileReader.html" title="class in org.mpxj.reader">AbstractProjectFileReader</a></pre>
<div class="block">This class provides a generic front end to read project data from
 a database.</div>
</li>
</ul>
</div>
<div class="summary">
<ul class="blockList">
<li class="blockList">
<!-- ======== CONSTRUCTOR SUMMARY ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.summary">
<!--   -->
</a>
<h3>Constructor Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Constructor Summary table, listing constructors, and an explanation">
<caption><span>Constructors</span><span class="tabEnd">&nbsp;</span></caption>
<tr>
<th class="colOne" scope="col">Constructor and Description</th>
</tr>
<tr class="altColor">
<td class="colOne"><code><span class="memberNameLink"><a href="../../../org/mpxj/asta/AstaMdbReader.html#AstaMdbReader--">AstaMdbReader</a></span>()</code>&nbsp;</td>
</tr>
</table>
</li>
</ul>
<!-- ========== METHOD SUMMARY =========== -->
<ul class="blockList">
<li class="blockList"><a name="method.summary">
<!--   -->
</a>
<h3>Method Summary</h3>
<table class="memberSummary" border="0" cellpadding="3" cellspacing="0" summary="Method Summary table, listing methods, and an explanation">
<caption><span id="t0" class="activeTableTab"><span>All Methods</span><span class="tabEnd">&nbsp;</span></span><span id="t2" class="tableTab"><span><a href="javascript:show(2);">Instance Methods</a></span><span class="tabEnd">&nbsp;</span></span><span id="t4" class="tableTab"><span><a href="javascript:show(8);">Concrete Methods</a></span><span class="tabEnd">&nbsp;</span></span></caption>
<tr>
<th class="colFirst" scope="col">Modifier and Type</th>
<th class="colLast" scope="col">Method and Description</th>
</tr>
<tr id="i0" class="altColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/asta/AstaMdbReader.html#allocateResources-java.io.File-">allocateResources</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>
<div class="block">Allocate any resources necessary to work with the database before we start reading.</div>
</td>
</tr>
<tr id="i1" class="rowColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;org.mpxj.asta.Row&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/asta/AstaMdbReader.html#getRows-java.lang.String-java.util.Map-">getRows</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tableName,
       <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;keys)</code>
<div class="block">Retrieve a set of rows from a named table matching the supplied keys.</div>
</td>
</tr>
<tr id="i2" class="altColor">
<td class="colFirst"><code>protected <a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;org.mpxj.asta.Row&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/asta/AstaMdbReader.html#getRows-java.lang.String-java.util.Map-java.util.Map-">getRows</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tableName,
       <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;keys,
       <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;nameMap)</code>
<div class="block">Retrieve a set of rows from a named table matching the supplied keys.</div>
</td>
</tr>
<tr id="i3" class="rowColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/asta/AstaMdbReader.html#listProjects--">listProjects</a></span>()</code>
<div class="block">Populates a Map instance representing the IDs and names of
 projects available in the current database.</div>
</td>
</tr>
<tr id="i4" class="altColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/asta/AstaMdbReader.html#read--">read</a></span>()</code>
<div class="block">Read a project from the current data source.</div>
</td>
</tr>
<tr id="i5" class="rowColor">
<td class="colFirst"><code><a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a></code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/asta/AstaMdbReader.html#read-java.io.File-">read</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>
<div class="block">Read a single schedule from a file where a File instance is supplied.</div>
</td>
</tr>
<tr id="i6" class="altColor">
<td class="colFirst"><code><a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/asta/AstaMdbReader.html#readAll-java.io.File-">readAll</a></span>(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</code>
<div class="block">Default implementation of readAll.</div>
</td>
</tr>
<tr id="i7" class="rowColor">
<td class="colFirst"><code>protected void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/asta/AstaMdbReader.html#releaseResources--">releaseResources</a></span>()</code>
<div class="block">Release any resources once we've finished reading.</div>
</td>
</tr>
<tr id="i8" class="altColor">
<td class="colFirst"><code>void</code></td>
<td class="colLast"><code><span class="memberNameLink"><a href="../../../org/mpxj/asta/AstaMdbReader.html#setProjectID-int-">setProjectID</a></span>(int&nbsp;projectID)</code>
<div class="block">Set the ID of the project to be read.</div>
</td>
</tr>
</table>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.mpxj.reader.AbstractProjectFileReader">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.mpxj.reader.<a href="../../../org/mpxj/reader/AbstractProjectFileReader.html" title="class in org.mpxj.reader">AbstractProjectFileReader</a></h3>
<code><a href="../../../org/mpxj/reader/AbstractProjectFileReader.html#read-java.io.InputStream-">read</a>, <a href="../../../org/mpxj/reader/AbstractProjectFileReader.html#read-java.lang.String-">read</a>, <a href="../../../org/mpxj/reader/AbstractProjectFileReader.html#readAll-java.io.InputStream-">readAll</a>, <a href="../../../org/mpxj/reader/AbstractProjectFileReader.html#readAll-java.lang.String-">readAll</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.org.mpxj.reader.AbstractProjectReader">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;org.mpxj.reader.<a href="../../../org/mpxj/reader/AbstractProjectReader.html" title="class in org.mpxj.reader">AbstractProjectReader</a></h3>
<code><a href="../../../org/mpxj/reader/AbstractProjectReader.html#addListenersToProject-org.mpxj.ProjectFile-">addListenersToProject</a>, <a href="../../../org/mpxj/reader/AbstractProjectReader.html#addListenersToReader-org.mpxj.reader.ProjectReader-">addListenersToReader</a>, <a href="../../../org/mpxj/reader/AbstractProjectReader.html#addProjectListener-org.mpxj.listener.ProjectListener-">addProjectListener</a></code></li>
</ul>
<ul class="blockList">
<li class="blockList"><a name="methods.inherited.from.class.java.lang.Object">
<!--   -->
</a>
<h3>Methods inherited from class&nbsp;java.lang.<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true" title="class or interface in java.lang">Object</a></h3>
<code><a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#clone--" title="class or interface in java.lang">clone</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#equals-java.lang.Object-" title="class or interface in java.lang">equals</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#finalize--" title="class or interface in java.lang">finalize</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#getClass--" title="class or interface in java.lang">getClass</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#hashCode--" title="class or interface in java.lang">hashCode</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notify--" title="class or interface in java.lang">notify</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#notifyAll--" title="class or interface in java.lang">notifyAll</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#toString--" title="class or interface in java.lang">toString</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait--" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-" title="class or interface in java.lang">wait</a>, <a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Object.html?is-external=true#wait-long-int-" title="class or interface in java.lang">wait</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
<div class="details">
<ul class="blockList">
<li class="blockList">
<!-- ========= CONSTRUCTOR DETAIL ======== -->
<ul class="blockList">
<li class="blockList"><a name="constructor.detail">
<!--   -->
</a>
<h3>Constructor Detail</h3>
<a name="AstaMdbReader--">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>AstaMdbReader</h4>
<pre>public&nbsp;AstaMdbReader()</pre>
</li>
</ul>
</li>
</ul>
<!-- ============ METHOD DETAIL ========== -->
<ul class="blockList">
<li class="blockList"><a name="method.detail">
<!--   -->
</a>
<h3>Method Detail</h3>
<a name="getRows-java.lang.String-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRows</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;org.mpxj.asta.Row&gt;&nbsp;getRows(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tableName,
                                          <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;keys)
                                   throws org.mpxj.asta.AstaDatabaseException</pre>
<div class="block">Retrieve a set of rows from a named table matching the supplied keys.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>tableName</code> - table to retrieve rows from</dd>
<dd><code>keys</code> - name and integer value keys</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>list of rows</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>org.mpxj.asta.AstaDatabaseException</code></dd>
</dl>
</li>
</ul>
<a name="getRows-java.lang.String-java.util.Map-java.util.Map-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>getRows</h4>
<pre>protected&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;org.mpxj.asta.Row&gt;&nbsp;getRows(<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&nbsp;tableName,
                                          <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>&gt;&nbsp;keys,
                                          <a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;nameMap)
                                   throws org.mpxj.asta.AstaDatabaseException</pre>
<div class="block">Retrieve a set of rows from a named table matching the supplied keys.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>tableName</code> - table to retrieve rows from</dd>
<dd><code>keys</code> - name and integer value keys</dd>
<dd><code>nameMap</code> - column name map</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>list of rows</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code>org.mpxj.asta.AstaDatabaseException</code></dd>
</dl>
</li>
</ul>
<a name="allocateResources-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>allocateResources</h4>
<pre>protected&nbsp;void&nbsp;allocateResources(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)</pre>
<div class="block">Allocate any resources necessary to work with the database before we start reading.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>file</code> - database file</dd>
</dl>
</li>
</ul>
<a name="releaseResources--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>releaseResources</h4>
<pre>protected&nbsp;void&nbsp;releaseResources()</pre>
<div class="block">Release any resources once we've finished reading.</div>
</li>
</ul>
<a name="listProjects--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>listProjects</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/Map.html?is-external=true" title="class or interface in java.util">Map</a>&lt;<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/Integer.html?is-external=true" title="class or interface in java.lang">Integer</a>,<a href="https://docs.oracle.com/javase/8/docs/api/java/lang/String.html?is-external=true" title="class or interface in java.lang">String</a>&gt;&nbsp;listProjects()
                                 throws <a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></pre>
<div class="block">Populates a Map instance representing the IDs and names of
 projects available in the current database.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>Map instance containing ID and name pairs</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></code></dd>
</dl>
</li>
</ul>
<a name="read--">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>read</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;read()
                 throws <a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></pre>
<div class="block">Read a project from the current data source.</div>
<dl>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ProjectFile instance</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></code></dd>
</dl>
</li>
</ul>
<a name="setProjectID-int-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>setProjectID</h4>
<pre>public&nbsp;void&nbsp;setProjectID(int&nbsp;projectID)</pre>
<div class="block">Set the ID of the project to be read.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>projectID</code> - project ID</dd>
</dl>
</li>
</ul>
<a name="read-java.io.File-">
<!--   -->
</a>
<ul class="blockList">
<li class="blockList">
<h4>read</h4>
<pre>public&nbsp;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&nbsp;read(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)
                 throws <a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from interface:&nbsp;<code><a href="../../../org/mpxj/reader/ProjectReader.html#read-java.io.File-">ProjectReader</a></code></span></div>
<div class="block">Read a single schedule from a file where a File instance is supplied.</div>
<dl>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>file</code> - File instance</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>ProjectFile instance</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></code></dd>
</dl>
</li>
</ul>
<a name="readAll-java.io.File-">
<!--   -->
</a>
<ul class="blockListLast">
<li class="blockList">
<h4>readAll</h4>
<pre>public&nbsp;<a href="https://docs.oracle.com/javase/8/docs/api/java/util/List.html?is-external=true" title="class or interface in java.util">List</a>&lt;<a href="../../../org/mpxj/ProjectFile.html" title="class in org.mpxj">ProjectFile</a>&gt;&nbsp;readAll(<a href="https://docs.oracle.com/javase/8/docs/api/java/io/File.html?is-external=true" title="class or interface in java.io">File</a>&nbsp;file)
                          throws <a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></pre>
<div class="block"><span class="descfrmTypeLabel">Description copied from class:&nbsp;<code><a href="../../../org/mpxj/reader/AbstractProjectFileReader.html#readAll-java.io.File-">AbstractProjectFileReader</a></code></span></div>
<div class="block">Default implementation of readAll. Reads a single project,
 if successful, returns a list with a single entry. If unsuccessful,
 returns an empty list.</div>
<dl>
<dt><span class="overrideSpecifyLabel">Specified by:</span></dt>
<dd><code><a href="../../../org/mpxj/reader/ProjectReader.html#readAll-java.io.File-">readAll</a></code>&nbsp;in interface&nbsp;<code><a href="../../../org/mpxj/reader/ProjectReader.html" title="interface in org.mpxj.reader">ProjectReader</a></code></dd>
<dt><span class="overrideSpecifyLabel">Overrides:</span></dt>
<dd><code><a href="../../../org/mpxj/reader/AbstractProjectFileReader.html#readAll-java.io.File-">readAll</a></code>&nbsp;in class&nbsp;<code><a href="../../../org/mpxj/reader/AbstractProjectFileReader.html" title="class in org.mpxj.reader">AbstractProjectFileReader</a></code></dd>
<dt><span class="paramLabel">Parameters:</span></dt>
<dd><code>file</code> - File instance</dd>
<dt><span class="returnLabel">Returns:</span></dt>
<dd>list of projects</dd>
<dt><span class="throwsLabel">Throws:</span></dt>
<dd><code><a href="../../../org/mpxj/MPXJException.html" title="class in org.mpxj">MPXJException</a></code></dd>
</dl>
</li>
</ul>
</li>
</ul>
</li>
</ul>
</div>
</div>
<!-- ========= END OF CLASS DATA ========= -->
<!-- ======= START OF BOTTOM NAVBAR ====== -->
<div class="bottomNav"><a name="navbar.bottom">
<!--   -->
</a>
<div class="skipNav"><a href="#skip.navbar.bottom" title="Skip navigation links">Skip navigation links</a></div>
<a name="navbar.bottom.firstrow">
<!--   -->
</a>
<ul class="navList" title="Navigation">
<li><a href="../../../overview-summary.html">Overview</a></li>
<li><a href="package-summary.html">Package</a></li>
<li class="navBarCell1Rev">Class</li>
<li><a href="class-use/AstaMdbReader.html">Use</a></li>
<li><a href="package-tree.html">Tree</a></li>
<li><a href="../../../deprecated-list.html">Deprecated</a></li>
<li><a href="../../../index-all.html">Index</a></li>
<li><a href="../../../help-doc.html">Help</a></li>
</ul>
</div>
<div class="subNav">
<ul class="navList">
<li><a href="../../../org/mpxj/asta/AstaJdbcReader.html" title="class in org.mpxj.asta"><span class="typeNameLink">Prev&nbsp;Class</span></a></li>
<li><a href="../../../org/mpxj/asta/AstaSqliteReader.html" title="class in org.mpxj.asta"><span class="typeNameLink">Next&nbsp;Class</span></a></li>
</ul>
<ul class="navList">
<li><a href="../../../index.html?org/mpxj/asta/AstaMdbReader.html" target="_top">Frames</a></li>
<li><a href="AstaMdbReader.html" target="_top">No&nbsp;Frames</a></li>
</ul>
<ul class="navList" id="allclasses_navbar_bottom">
<li><a href="../../../allclasses-noframe.html">All&nbsp;Classes</a></li>
</ul>
<div>
<script type="text/javascript"><!--
  allClassesLink = document.getElementById("allclasses_navbar_bottom");
  if(window==top) {
    allClassesLink.style.display = "block";
  }
  else {
    allClassesLink.style.display = "none";
  }
  //-->
</script>
</div>
<div>
<ul class="subNavList">
<li>Summary:&nbsp;</li>
<li>Nested&nbsp;|&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.summary">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.summary">Method</a></li>
</ul>
<ul class="subNavList">
<li>Detail:&nbsp;</li>
<li>Field&nbsp;|&nbsp;</li>
<li><a href="#constructor.detail">Constr</a>&nbsp;|&nbsp;</li>
<li><a href="#method.detail">Method</a></li>
</ul>
</div>
<a name="skip.navbar.bottom">
<!--   -->
</a></div>
<!-- ======== END OF BOTTOM NAVBAR ======= -->
<p class="legalCopy"><small>Copyright &#169; 2000&#x2013;2025 <a href="http://mpxj.org">MPXJ</a>. All rights reserved.</small></p>
</body>
</html>
