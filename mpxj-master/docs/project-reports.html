<!DOCTYPE html>
<!--
 | Generated by Apache Maven Doxia Site Renderer 1.9.2 from org.apache.maven.plugins:maven-site-plugin:3.9.1:CategorySummaryDocumentRenderer
 | Rendered using Apache Maven Fluido Skin 1.9
-->
<html xmlns="http://www.w3.org/1999/xhtml" lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="generator" content="Apache Maven Doxia Site Renderer 1.9.2" />
    <title>MPXJ &#x2013; Generated Reports</title>
    <link rel="stylesheet" href="./css/apache-maven-fluido-1.9.min.css" />
    <link rel="stylesheet" href="./css/site.css" />
    <link rel="stylesheet" href="./css/print.css" media="print" />
    <script src="./js/apache-maven-fluido-1.9.min.js"></script>
  </head>
  <body class="topBarDisabled">
    <a href="https://github.com/joniles/mpxj">
      <img style="position: absolute; top: 0; right: 0; border: 0; z-index: 10000;"
        src="https://s3.amazonaws.com/github/ribbons/forkme_right_darkblue_121621.png"
        alt="Fork me on GitHub">
    </a>
    <div class="container-fluid">
      <header>
        <div id="banner">
          <div class="pull-left"><div id="bannerLeft"><h2>MPXJ</h2>
</div>
</div>
          <div class="pull-right"></div>
          <div class="clear"><hr/></div>
        </div>

        <div id="breadcrumbs">
          <ul class="breadcrumb">
        <li id="publishDate">Last Published: 2025-06-05</li>
          </ul>
        </div>
      </header>
      <div class="row-fluid">
        <header id="leftColumn" class="span3">
          <nav class="well sidebar-nav">
  <ul class="nav nav-list">
   <li class="nav-header">MPXJ</li>
    <li><a href="summary.html" title="Summary"><span class="none"></span>Summary</a></li>
    <li><a href="changes-report.html" title="Changes"><span class="none"></span>Changes</a></li>
    <li><a href="team.html" title="Team"><span class="none"></span>Team</a></li>
    <li><a href="mailing-lists.html" title="Mailing List"><span class="none"></span>Mailing List</a></li>
    <li><a href="issue-management.html" title="Issues"><span class="none"></span>Issues</a></li>
    <li><a href="scm.html" title="Source"><span class="none"></span>Source</a></li>
    <li><a href="apidocs/index.html" title="Javadoc"><span class="none"></span>Javadoc</a></li>
    <li><a href="dependency-updates-report.html" title="Dependencies"><span class="none"></span>Dependencies</a></li>
    <li><a href="plugin-updates-report.html" title="Plugins"><span class="none"></span>Plugins</a></li>
  </ul>
          </nav>
          <div class="well sidebar-nav">
            <hr />
            <div id="poweredBy">
              <div class="clear"></div>
              <div class="clear"></div>
              <div class="clear"></div>
<a href="http://maven.apache.org/" title="Built by Maven" class="poweredBy"><img class="builtBy" alt="Built by Maven" src="./images/logos/maven-feather.png" /></a>
            </div>
          </div>
        </header>
        <main id="bodyColumn"  class="span9" >
<section>
<h2><a name="Generated_Reports"></a>Generated Reports</h2>
<p>This document provides an overview of the various reports that are automatically generated by <a class="externalLink" href="http://maven.apache.org">Maven</a> . Each report is briefly described below.</p><section>
<h3><a name="Overview"></a>Overview</h3>
<table border="0" class="table table-striped">
<tr class="a">
<th>Document</th>
<th>Description</th></tr>
<tr class="b">
<td><a href="changes-report.html">Changes</a></td>
<td>Changes report on releases of this project.</td></tr>
<tr class="a">
<td><a href="apidocs/index.html">Javadoc</a></td>
<td>Javadoc API documentation.</td></tr>
<tr class="b">
<td><a href="testapidocs/index.html">Test Javadoc</a></td>
<td>Test Javadoc API documentation.</td></tr>
<tr class="a">
<td><a href="dependency-updates-report.html">Dependency Updates Report</a></td>
<td>Provides details of the dependencies which have updated versions available.</td></tr>
<tr class="b">
<td><a href="plugin-updates-report.html">Plugin Updates Report</a></td>
<td>Provides details of the plugins used by this project which have newer versions available.</td></tr></table></section></section>
        </main>
      </div>
    </div>
    <hr/>
    <footer>
      <div class="container-fluid">
        <div class="row-fluid">
            <p>&#169;      2000&#x2013;2025
<a href="http://mpxj.org">MPXJ</a>
</p>
        </div>
      </div>
    </footer>
  </body>
</html>
