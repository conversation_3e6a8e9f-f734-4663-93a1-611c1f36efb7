<?xml version="1.0" encoding="UTF-8"?>
<project
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd"
	xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

	<modelVersion>4.0.0</modelVersion>
	<groupId>net.sf.mpxj</groupId>
	<artifactId>mpxj-tests</artifactId>
	<version>14.1.0</version>
	<name>MPXJ Tests</name>

	<dependencies>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.13.1</version>
		</dependency>

		<dependency>
			<groupId>net.sf.mpxj</groupId>
			<artifactId>mpxj</artifactId>
			<version>${project.version}</version>
		</dependency>
	</dependencies>

	<build>
		<sourceDirectory>src/test</sourceDirectory>
		<directory>target.tests</directory>
		<plugins>
			<!-- Ensure the compiler is using the correct Java version -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.1</version>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
