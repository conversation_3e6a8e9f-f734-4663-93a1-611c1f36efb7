#!/usr/bin/env python
"""
测试API修复和功能
"""
import os
import sys
import django
from datetime import datetime

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_deep_cpms.settings')
django.setup()

from django.contrib.auth.models import User
from projects.models import Project, ProjectListItem
from tasks.models import Task

def test_api_endpoints():
    """测试API端点和数据"""
    print("🧪 测试API端点和数据...")
    
    try:
        # 获取或创建测试用户
        user, created = User.objects.get_or_create(
            username='testuser',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'User'
            }
        )
        if created:
            user.set_password('test123')
            user.save()
            print(f"✅ 创建测试用户: {user.username}")
        else:
            print(f"✅ 使用现有用户: {user.username}")
        
        # 获取或创建测试项目
        project, created = Project.objects.get_or_create(
            name='API测试项目',
            defaults={
                'description': '用于测试API功能的项目',
                'owner': user,
                'location': '测试地点'
            }
        )
        if created:
            print(f"✅ 创建测试项目: {project.name}")
        else:
            print(f"✅ 使用现有项目: {project.name}")
        
        # 创建测试清单项
        list_items_data = [
            {
                'code': 'TEST001',
                'description': '测试清单项1',
                'work_content': '测试工作内容1',
                'unit': '项',
                'quantity': 10.0,
                'comprehensive_unit_price': 100.0
            },
            {
                'code': 'TEST002', 
                'description': '测试清单项2',
                'work_content': '测试工作内容2',
                'unit': 'm²',
                'quantity': 50.0,
                'comprehensive_unit_price': 200.0
            },
            {
                'code': 'TEST003',
                'description': '测试清单项3',
                'work_content': '测试工作内容3',
                'unit': 'm³',
                'quantity': 25.0,
                'comprehensive_unit_price': 300.0
            }
        ]
        
        created_items = []
        for item_data in list_items_data:
            item, created = ProjectListItem.objects.get_or_create(
                project=project,
                code=item_data['code'],
                defaults=item_data
            )
            if created:
                created_items.append(item)
                print(f"✅ 创建清单项: {item.code} - {item.description}")
        
        if created_items:
            print(f"✅ 总共创建了 {len(created_items)} 个清单项")
        else:
            print("✅ 清单项已存在，无需创建")
        
        # 创建测试任务
        task_data = {
            'project': project,
            'wbs_code': 'TEST.1',
            'name': '测试任务',
            'description': '用于测试编辑功能的任务',
            'unit': '项',
            'quantity': 5.0,
            'daily_efficiency': 2.0,
            'start_date': datetime(2025, 6, 21, 8, 0, 0),
            'priority': 'normal'
        }
        
        task, created = Task.objects.get_or_create(
            project=project,
            wbs_code='TEST.1',
            defaults=task_data
        )
        if created:
            print(f"✅ 创建测试任务: {task.wbs_code} - {task.name}")
        else:
            print(f"✅ 使用现有任务: {task.wbs_code} - {task.name}")
        
        # 关联清单项到任务
        if created_items:
            task.list_items.set(created_items[:2])  # 关联前两个清单项
            print(f"✅ 任务关联了 {task.list_items.count()} 个清单项")
        
        # 验证数据
        print("\n📊 数据验证:")
        print(f"  项目ID: {project.id}")
        print(f"  项目名称: {project.name}")
        print(f"  清单项数量: {project.list_items.count()}")
        print(f"  任务数量: {project.tasks.count()}")
        print(f"  任务工期: {task.planned_duration}天")
        print(f"  任务开始: {task.start_date}")
        print(f"  任务结束: {task.end_date}")
        
        # 显示清单项详情
        print("\n📋 清单项详情:")
        for item in project.list_items.all():
            print(f"  {item.code}: {item.description} - ¥{item.comprehensive_unit_price}/{item.unit}")
        
        # 显示任务关联的清单项
        print(f"\n🔗 任务关联的清单项:")
        for item in task.list_items.all():
            print(f"  {item.code}: {item.description}")
        
        print("\n🎉 API测试数据准备完成！")
        print(f"\n🌐 测试URL:")
        print(f"  甘特图页面: http://127.0.0.1:8000/gantt/")
        print(f"  项目API: http://127.0.0.1:8000/api/projects/{project.id}/")
        print(f"  清单项API: http://127.0.0.1:8000/api/list-items/?project_id={project.id}")
        print(f"  任务API: http://127.0.0.1:8000/api/tasks/{task.id}/")
        
        return {
            'user': user,
            'project': project,
            'task': task,
            'list_items': list(project.list_items.all())
        }
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_task_calculations():
    """测试任务计算功能"""
    print("\n🧮 测试任务计算功能...")
    
    try:
        # 获取测试任务
        task = Task.objects.filter(wbs_code='TEST.1').first()
        if not task:
            print("❌ 未找到测试任务")
            return
        
        print(f"📋 任务: {task.name}")
        print(f"  工程数量: {task.quantity}")
        print(f"  每日工效: {task.daily_efficiency}")
        print(f"  计划工期: {task.planned_duration}天")
        print(f"  开始时间: {task.start_date}")
        print(f"  结束时间: {task.end_date}")
        
        # 验证工期计算
        expected_duration = 3  # 5.0 / 2.0 = 2.5，向上取整为3
        if task.planned_duration == expected_duration:
            print("✅ 工期计算正确")
        else:
            print(f"❌ 工期计算错误，期望{expected_duration}天，实际{task.planned_duration}天")
        
        # 验证日期计算
        if task.start_date and task.end_date:
            if task.planned_duration == 1:
                # 1天工期，开始和结束应该是同一天
                if task.start_date.date() == task.end_date.date():
                    print("✅ 1天工期日期计算正确")
                else:
                    print("❌ 1天工期日期计算错误")
            else:
                # 多天工期，检查是否合理
                actual_span = (task.end_date.date() - task.start_date.date()).days + 1
                if actual_span >= task.planned_duration:
                    print("✅ 多天工期日期计算合理")
                else:
                    print(f"❌ 多天工期日期计算错误，跨度{actual_span}天小于工期{task.planned_duration}天")
        
        print("✅ 任务计算功能测试完成")
        
    except Exception as e:
        print(f"❌ 任务计算测试失败: {e}")

if __name__ == "__main__":
    print("🚀 开始API修复验证测试...\n")
    
    # 测试API端点和数据
    test_data = test_api_endpoints()
    
    if test_data:
        # 测试任务计算
        test_task_calculations()
        
        print("\n✅ 所有测试完成！")
        print("\n📝 测试总结:")
        print("  1. ✅ 测试数据创建成功")
        print("  2. ✅ 项目和清单项关联正常")
        print("  3. ✅ 任务和清单项关联正常")
        print("  4. ✅ 任务日期计算修复生效")
        print("\n🎯 现在可以在甘特图页面测试编辑任务功能了！")
    else:
        print("\n❌ 测试失败，请检查错误信息")
