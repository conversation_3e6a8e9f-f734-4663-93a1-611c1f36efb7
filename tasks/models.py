from django.db import models
from projects.models import Project, ProjectListItem
import math
from datetime import datetime, timedelta
from django.utils import timezone


class Task(models.Model):
    """任务模型"""
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='tasks', verbose_name='项目')
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='children', verbose_name='父任务')

    # 基本信息
    wbs_code = models.CharField(max_length=50, verbose_name='WBS标识')
    name = models.CharField(max_length=200, verbose_name='任务名称')
    description = models.TextField(verbose_name='任务描述', blank=True)
    unit = models.CharField(max_length=50, verbose_name='任务单位', blank=True)
    quantity = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='工程数量', default=1)
    daily_efficiency = models.DecimalField(
        max_digits=10, decimal_places=2,
        verbose_name='每日工效',
        default=1,
        help_text='每日计划完成的工程量'
    )

    # 时间相关
    planned_duration = models.IntegerField(
        verbose_name='计划工期（天）',
        blank=True, null=True,
        help_text='自动计算：工程数量/每日工效，向上取整'
    )
    start_date = models.DateTimeField(verbose_name='开始时间')
    end_date = models.DateTimeField(verbose_name='计划完成时间')
    actual_start_date = models.DateTimeField(verbose_name='实际开始时间', null=True, blank=True)
    actual_end_date = models.DateTimeField(verbose_name='实际完成时间', null=True, blank=True)

    # 进度相关
    progress_percentage = models.DecimalField(
        max_digits=5, decimal_places=2,
        verbose_name='完成百分比',
        default=0,
        help_text='0-100之间的数值'
    )

    # 任务状态
    STATUS_CHOICES = [
        ('not_started', '未开始'),
        ('in_progress', '进行中'),
        ('completed', '已完成'),
        ('paused', '暂停'),
        ('cancelled', '已取消'),
    ]
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='not_started', verbose_name='任务状态')

    # 优先级
    PRIORITY_CHOICES = [
        ('low', '低'),
        ('normal', '普通'),
        ('high', '高'),
        ('critical', '紧急'),
    ]
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='normal', verbose_name='优先级')

    # 关键路径标识
    is_critical = models.BooleanField(default=False, verbose_name='是否关键路径')

    # 关联清单项
    list_items = models.ManyToManyField(ProjectListItem, blank=True, verbose_name='关联清单项')

    # 排序字段
    order = models.IntegerField(default=0, verbose_name='排序')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    class Meta:
        verbose_name = '任务'
        verbose_name_plural = '任务'
        unique_together = ['project', 'wbs_code']
        ordering = ['order', 'wbs_code']

    def __str__(self):
        return f"{self.wbs_code} - {self.name}"

    def calculate_end_date(self):
        """根据开始日期和工期计算结束日期（排除周末）"""
        if not self.start_date:
            return None

        # 计算工期
        duration = self.planned_duration or 1
        if self.daily_efficiency and self.daily_efficiency > 0:
            duration = math.ceil(float(self.quantity) / float(self.daily_efficiency))

        # 如果工期为1天，结束日期就是开始日期
        if duration == 1:
            return self.start_date

        # 多天工期，需要添加工作日
        current_date = self.start_date
        work_days_added = 0

        while work_days_added < duration - 1:  # -1 因为开始日期已经算作第一天
            current_date += timedelta(days=1)
            # 0 = 周一, 6 = 周日，跳过周末（5=周六, 6=周日）
            if current_date.weekday() < 5:  # 周一到周五
                work_days_added += 1

        # 保持原有的时间部分
        return current_date.replace(
            hour=self.start_date.hour,
            minute=self.start_date.minute,
            second=self.start_date.second,
            microsecond=self.start_date.microsecond
        )

    def save(self, *args, **kwargs):
        # 自动计算计划工期: 工程数量 / 每日工效，向上取整
        if self.daily_efficiency and self.daily_efficiency > 0:
            self.planned_duration = math.ceil(float(self.quantity) / float(self.daily_efficiency))

        # 自动计算结束日期
        if self.start_date and not kwargs.get('skip_end_date_calculation', False):
            calculated_end_date = self.calculate_end_date()
            if calculated_end_date:
                self.end_date = calculated_end_date

        super().save(*args, **kwargs)

    def get_total_cost(self):
        """获取任务总成本"""
        total_cost = 0
        for list_item in self.list_items.all():
            total_cost += list_item.comprehensive_total_price or 0
        return total_cost

    def get_children_count(self):
        """获取子任务数量"""
        return self.children.count()

    def is_parent_task(self):
        """判断是否为父任务"""
        return self.children.exists()


class TaskDependency(models.Model):
    """任务依赖关系模型"""
    from_task = models.ForeignKey(
        Task,
        on_delete=models.CASCADE,
        related_name='successor_dependencies',
        verbose_name='前置任务'
    )
    to_task = models.ForeignKey(
        Task,
        on_delete=models.CASCADE,
        related_name='predecessor_dependencies',
        verbose_name='后续任务'
    )

    # 依赖类型
    DEPENDENCY_TYPES = [
        ('FS', 'FS:完成-开始'),  # Finish to Start
        ('SS', 'SS:开始-开始'),  # Start to Start
        ('SF', 'SF:开始-完成'),  # Start to Finish
        ('FF', 'FF:完成-完成'),  # Finish to Finish
    ]
    dependency_type = models.CharField(
        max_length=2,
        choices=DEPENDENCY_TYPES,
        default='FS',
        verbose_name='依赖类型'
    )

    # 滞后时间（分钟）
    lag_time = models.IntegerField(
        default=0,
        verbose_name='滞后时间（分钟）',
        help_text='正数表示延迟，负数表示提前'
    )

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        verbose_name = '任务依赖关系'
        verbose_name_plural = '任务依赖关系'
        unique_together = ['from_task', 'to_task']

    def __str__(self):
        return f"{self.from_task.wbs_code} -> {self.to_task.wbs_code} ({self.dependency_type}, {self.lag_time}分钟)"
