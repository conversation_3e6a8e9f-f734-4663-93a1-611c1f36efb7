# Generated by Django 5.2.3 on 2025-06-16 13:31

import math
from datetime import timedelta
from django.db import migrations


def fix_task_end_dates(apps, schema_editor):
    """修复所有任务的结束日期"""
    Task = apps.get_model('tasks', 'Task')

    for task in Task.objects.all():
        if not task.start_date:
            continue

        # 计算正确的工期
        duration = task.planned_duration or 1
        if task.daily_efficiency and task.daily_efficiency > 0:
            duration = math.ceil(float(task.quantity) / float(task.daily_efficiency))

        # 计算正确的结束日期
        if duration == 1:
            # 1天工期，结束日期就是开始日期
            correct_end_date = task.start_date
        else:
            # 多天工期，需要添加工作日
            current_date = task.start_date
            work_days_added = 0

            while work_days_added < duration - 1:  # -1 因为开始日期已经算作第一天
                current_date += timedelta(days=1)
                # 0 = 周一, 6 = 周日，跳过周末（5=周六, 6=周日）
                if current_date.weekday() < 5:  # 周一到周五
                    work_days_added += 1

            # 保持原有的时间部分
            correct_end_date = current_date.replace(
                hour=task.start_date.hour,
                minute=task.start_date.minute,
                second=task.start_date.second,
                microsecond=task.start_date.microsecond
            )

        # 更新任务的结束日期和工期
        task.planned_duration = duration
        task.end_date = correct_end_date
        task.save()

        print(f"修复任务 {task.wbs_code}: 工期={duration}天, 开始={task.start_date.date()}, 结束={correct_end_date.date()}")


def reverse_fix_task_end_dates(apps, schema_editor):
    """回滚操作 - 不做任何事情"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('tasks', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(fix_task_end_dates, reverse_fix_task_end_dates),
    ]
