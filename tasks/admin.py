from django.contrib import admin
from .models import Task, TaskDependency


@admin.register(Task)
class TaskAdmin(admin.ModelAdmin):
    list_display = ['wbs_code', 'name', 'project', 'status', 'priority', 'start_date', 'end_date', 'progress_percentage', 'is_critical']
    list_filter = ['project', 'status', 'priority', 'is_critical']
    search_fields = ['wbs_code', 'name', 'description']
    filter_horizontal = ['list_items']
    readonly_fields = ['planned_duration']
    fieldsets = (
        ('基本信息', {
            'fields': ('project', 'parent', 'wbs_code', 'name', 'description', 'order')
        }),
        ('工程信息', {
            'fields': ('unit', 'quantity', 'daily_efficiency', 'planned_duration')
        }),
        ('时间信息', {
            'fields': ('start_date', 'end_date', 'actual_start_date', 'actual_end_date')
        }),
        ('状态信息', {
            'fields': ('status', 'priority', 'progress_percentage', 'is_critical')
        }),
        ('关联信息', {
            'fields': ('list_items',)
        }),
    )


@admin.register(TaskDependency)
class TaskDependencyAdmin(admin.ModelAdmin):
    list_display = ['from_task', 'to_task', 'dependency_type', 'lag_time']
    list_filter = ['dependency_type']
    search_fields = ['from_task__wbs_code', 'from_task__name', 'to_task__wbs_code', 'to_task__name']
