/**
 * 高级甘特图组件
 * 基于Highcharts Gantt 12.2.0官方示例
 */

class AdvancedGantt {
    constructor(containerId, options = {}) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.chart = null;
        this.options = {
            title: options.title || '项目甘特图',
            subtitle: options.subtitle || '拖拽任务条可调整时间，点击任务查看详情',
            data: options.data || [],
            dependencies: options.dependencies || [],
            enableDragDrop: options.enableDragDrop !== false,
            enableInteraction: options.enableInteraction !== false,
            showCurrentDate: options.showCurrentDate !== false,
            showWeekends: options.showWeekends !== false,
            ...options
        };

        this.init();
    }

    init() {
        if (!this.container) {
            console.error('甘特图容器不存在:', this.containerId);
            return;
        }

        if (typeof Highcharts === 'undefined') {
            console.error('Highcharts未加载');
            return;
        }

        if (typeof Highcharts.ganttChart !== 'function') {
            console.error('Highcharts Gantt模块未加载');
            return;
        }

        this.setupWeekendPlugin();
        this.createChart();
    }

    // 设置周末显示插件
    setupWeekendPlugin() {
        if (this.options.showWeekends) {
            // 周末显示插件
            Highcharts.addEvent(Highcharts.Axis, 'foundExtremes', e => {
                if (e.target.options.custom && e.target.options.custom.weekendPlotBands) {
                    const axis = e.target,
                        chart = axis.chart,
                        day = 24 * 36e5,
                        isWeekend = t => /[06]/.test(chart.time.dateFormat('%w', t)),
                        plotBands = [];

                    let inWeekend = false;

                    for (
                        let x = Math.floor(axis.min / day) * day;
                        x <= Math.ceil(axis.max / day) * day;
                        x += day
                    ) {
                        const last = plotBands.at(-1);
                        if (isWeekend(x) && !inWeekend) {
                            plotBands.push({
                                from: x,
                                color: {
                                    pattern: {
                                        path: 'M 0 10 L 10 0 M -1 1 L 1 -1 M 9 11 L 11 9',
                                        width: 10,
                                        height: 10,
                                        color: 'rgba(128,128,128,0.15)'
                                    }
                                }
                            });
                            inWeekend = true;
                        }

                        if (!isWeekend(x) && inWeekend && last) {
                            last.to = x;
                            inWeekend = false;
                        }
                    }
                    axis.options.plotBands = plotBands;
                }
            });
        }
    }

    // 创建甘特图
    createChart() {
        const day = 24 * 36e5;
        const today = Math.floor(Date.now() / day) * day;

        const chartOptions = {
            chart: {
                plotBackgroundColor: 'rgba(128,128,128,0.02)',
                plotBorderColor: 'rgba(128,128,128,0.1)',
                plotBorderWidth: 1,
                height: this.options.height || 600
            },

            title: {
                text: this.options.title,
                style: {
                    fontSize: '18px',
                    fontWeight: 'bold'
                }
            },

            subtitle: {
                text: this.options.subtitle
            },

            plotOptions: {
                series: {
                    animation: !this.options.enableDragDrop,
                    connectors: {
                        dashStyle: 'ShortDot',
                        lineWidth: 2,
                        radius: 5,
                        startMarker: {
                            enabled: false
                        }
                    },
                    groupPadding: 0.1,
                    pointPadding: 0.1,
                    dataLabels: [{
                        enabled: true,
                        align: 'left',
                        format: '{point.name}',
                        padding: 10,
                        style: {
                            fontWeight: 'normal',
                            textOutline: 'none',
                            fontSize: '11px'
                        }
                    }, {
                        enabled: true,
                        align: 'right',
                        format: '{#if point.completed}{(multiply point.completed.amount 100):.0f}%{/if}',
                        padding: 10,
                        style: {
                            fontWeight: 'normal',
                            textOutline: 'none',
                            opacity: 0.6,
                            fontSize: '10px'
                        }
                    }],
                    // 拖拽功能
                    dragDrop: this.options.enableDragDrop ? {
                        draggableX: true,
                        draggableY: false,
                        dragPrecisionX: day / 3 // 精确到8小时
                    } : undefined,
                    allowPointSelect: this.options.enableInteraction,
                    point: {
                        events: {
                            click: (e) => {
                                if (this.options.onTaskClick) {
                                    this.options.onTaskClick(e.point);
                                }
                            },
                            select: (e) => {
                                if (this.options.onTaskSelect) {
                                    this.options.onTaskSelect(e.target);
                                }
                            },
                            unselect: (e) => {
                                if (this.options.onTaskUnselect) {
                                    this.options.onTaskUnselect(e.target);
                                }
                            }
                        }
                    }
                },
                gantt: {
                    borderRadius: 2,
                    grouping: false,
                    dataLabels: {
                        enabled: true,
                        inside: false,
                        align: 'left',
                        verticalAlign: 'middle',
                        style: {
                            fontWeight: 'bold',
                            fontSize: '11px',
                            color: '#333'
                        }
                    },
                    // 自定义点渲染器
                    point: {
                        events: {
                            afterSetState: function() {
                                if (this.custom && this.custom.isSummary) {
                                    // 摘要任务：创建带箭头的形状
                                    this.renderSummaryShape();
                                }
                            }
                        }
                    }
                }
            },

            xAxis: {
                type: 'datetime',
                currentDateIndicator: this.options.showCurrentDate ? {
                    color: '#2caffe',
                    dashStyle: 'ShortDot',
                    width: 2,
                    label: {
                        format: ''
                    }
                } : false,
                // 三层时间刻度配置
                dateTimeLabelFormats: {
                    millisecond: '%H:%M:%S.%L',
                    second: '%H:%M:%S',
                    minute: '%H:%M',
                    hour: '%H:%M',
                    day: '%e日',
                    week: '%e日',
                    month: '%m月',
                    year: '%Y年'
                },
                tickInterval: 24 * 3600 * 1000, // 每天一个刻度
                tickPixelInterval: 40,
                labels: {
                    style: {
                        fontSize: '10px',
                        color: '#666'
                    },
                    rotation: 0,
                    format: '{value:%e}' // 日期
                },
                grid: {
                    enabled: true,
                    borderWidth: 1,
                    borderColor: '#e6e6e6'
                },
                gridLineWidth: 1,
                gridLineColor: '#f0f0f0',
                lineWidth: 1,
                lineColor: '#ccc',
                tickWidth: 1,
                tickColor: '#ccc',
                // 添加多级时间轴
                units: [
                    ['day', [1]],
                    ['week', [1]],
                    ['month', [1]],
                    ['year', [1]]
                ],
                custom: this.options.showWeekends ? {
                    today,
                    weekendPlotBands: true
                } : undefined
            },

            yAxis: {
                type: 'category',
                grid: {
                    borderWidth: 0
                },
                gridLineWidth: 0,
                labels: {
                    symbol: {
                        width: 8,
                        height: 6,
                        x: -4,
                        y: -2
                    }
                },
                staticScale: 30
            },

            tooltip: {
                pointFormat: '<span style="font-weight: bold">{point.name}</span><br>' +
                    '{point.start:%e %b}' +
                    '{#unless point.milestone} → {point.end:%e %b}{/unless}' +
                    '<br>' +
                    '{#if point.completed}' +
                    '完成度: {multiply point.completed.amount 100}%<br>' +
                    '{/if}' +
                    '负责人: {#if point.owner}{point.owner}{else}未分配{/if}'
            },

            series: this.formatSeriesData(),

            // 添加依赖关系连线配置
            connectors: {
                enabled: true,
                lineWidth: 2,
                lineColor: '#666666',
                marker: {
                    enabled: true,
                    symbol: 'arrow',
                    width: 8,
                    height: 8
                }
            },

            accessibility: {
                keyboardNavigation: {
                    seriesNavigation: {
                        mode: 'serialize'
                    }
                },
                point: {
                    descriptionFormatter: function (point) {
                        const completedValue = point.completed ?
                                point.completed.amount || point.completed : null,
                            completed = completedValue ?
                                ' 任务 ' + Math.round(completedValue * 1000) / 10 +
                                    '% 已完成。' :
                                '',
                            dependency = point.dependency &&
                                point.series.chart.get(point.dependency).name,
                            dependsOn = dependency ?
                                ' 依赖于 ' + dependency + '。' : '';

                        return Highcharts.format(
                            point.milestone ?
                                '{point.yCategory}。里程碑在 {point.x:%Y-%m-%d}。' +
                                '负责人: {point.owner}。{dependsOn}' :
                                '{point.yCategory}。{completed} 开始 ' +
                                '{point.x:%Y-%m-%d}，结束 {point.x2:%Y-%m-%d}。负责人: ' +
                                '{point.owner}。{dependsOn}',
                            { point, completed, dependsOn }
                        );
                    }
                }
            },

            credits: {
                enabled: false
            }
        };

        try {
            // 添加自定义CSS样式
            this.addCustomStyles();

            this.chart = Highcharts.ganttChart(this.containerId, chartOptions);
            console.log('高级甘特图创建成功');

            // 添加自定义渲染逻辑
            this.setupCustomRendering();

            if (this.options.onChartReady) {
                this.options.onChartReady(this.chart);
            }

        } catch (error) {
            console.error('创建甘特图失败:', error);
            if (this.options.onError) {
                this.options.onError(error);
            }
        }
    }

    // 格式化系列数据
    formatSeriesData() {
        if (!this.options.data || this.options.data.length === 0) {
            return [{
                name: '示例项目',
                data: this.getDefaultData()
            }];
        }

        // 如果数据已经是系列格式，直接返回
        if (Array.isArray(this.options.data) && this.options.data[0] && this.options.data[0].name) {
            return this.options.data;
        }

        // 处理任务数据，添加依赖关系和样式
        const processedData = this.options.data.map(task => {
            const processedTask = { ...task };

            // 处理依赖关系连线 - 使用dependency属性
            if (this.options.dependencies && this.options.dependencies.length > 0) {
                const taskDependencies = this.options.dependencies.filter(dep => dep.to === task.id);
                if (taskDependencies.length > 0) {
                    // 只使用第一个前置任务作为主要依赖
                    processedTask.dependency = taskDependencies[0].from;
                }
            }

            // 根据任务类型设置样式
            if (task.summary) {
                // 摘要任务样式 - 使用特殊的形状和颜色
                processedTask.color = '#8b5cf6';
                processedTask.borderColor = '#7c3aed';
                processedTask.borderWidth = 2;
                processedTask.pointWidth = 25;
                processedTask.height = 18;
                // 摘要任务使用菱形或特殊形状
                processedTask.marker = {
                    symbol: 'url(data:image/svg+xml;base64,' + btoa(`
                        <svg xmlns="http://www.w3.org/2000/svg" width="100" height="20" viewBox="0 0 100 20">
                            <path d="M0,10 L85,2 L100,10 L85,18 L0,18 Z"
                                  fill="#8b5cf6"
                                  stroke="#7c3aed"
                                  stroke-width="2"/>
                        </svg>
                    `) + ')',
                    width: 25,
                    height: 18
                };
                processedTask.custom = {
                    isSummary: true
                };
                processedTask.className = 'summary-task';
            } else {
                // 子任务样式 - 标准矩形进度条
                processedTask.color = task.critical ? '#ef4444' :
                                    (task.status === 'completed' ? '#10b981' : '#3b82f6');
                processedTask.borderColor = task.critical ? '#dc2626' :
                                          (task.status === 'completed' ? '#059669' : '#2563eb');
                processedTask.borderWidth = 1;
                processedTask.pointWidth = 20;
                processedTask.height = 14;
                processedTask.borderRadius = 2;
                processedTask.custom = {
                    isSummary: false
                };
                processedTask.className = 'child-task';
            }

            return processedTask;
        });

        // 包装成单个系列
        return [{
            name: '项目任务',
            data: processedData
        }];
    }

    // 获取默认示例数据
    getDefaultData() {
        const day = 24 * 36e5;
        const today = Math.floor(Date.now() / day) * day;

        return [{
            name: '项目启动',
            id: 'project_start',
            start: today,
            end: today + 2 * day,
            completed: {
                amount: 1
            },
            owner: '项目经理'
        }, {
            name: '需求分析',
            id: 'requirements',
            dependency: 'project_start',
            start: today + 2 * day,
            end: today + 5 * day,
            completed: {
                amount: 0.8
            },
            owner: '业务分析师'
        }, {
            name: '系统设计',
            id: 'design',
            dependency: 'requirements',
            start: today + 5 * day,
            end: today + 8 * day,
            completed: {
                amount: 0.3
            },
            owner: '架构师'
        }, {
            name: '开发实现',
            id: 'development',
            dependency: 'design',
            start: today + 8 * day,
            end: today + 15 * day,
            completed: {
                amount: 0.1
            },
            owner: '开发团队'
        }];
    }

    // 更新数据
    updateData(newData) {
        this.options.data = newData;
        if (this.chart) {
            const seriesData = this.formatSeriesData();
            this.chart.series.forEach((series, index) => {
                if (seriesData[index]) {
                    series.setData(seriesData[index].data, false);
                }
            });
            this.chart.redraw();
        }
    }

    // 添加任务
    addTask(taskData) {
        if (this.chart && this.chart.series[0]) {
            this.chart.series[0].addPoint(taskData);
        }
    }

    // 移除选中的任务
    removeSelectedTasks() {
        if (this.chart) {
            const selectedPoints = this.chart.getSelectedPoints();
            selectedPoints.forEach(point => point.remove());
        }
    }

    // 导出图表
    exportChart(options = {}) {
        if (this.chart) {
            this.chart.exportChart({
                type: 'image/png',
                filename: 'gantt-chart',
                ...options
            });
        }
    }

    // 添加自定义CSS样式
    addCustomStyles() {
        const styleId = 'advanced-gantt-styles';
        if (document.getElementById(styleId)) {
            return; // 样式已存在
        }

        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
            /* 摘要任务样式 - 带阴影的粗边框 */
            .highcharts-gantt-series .summary-task .highcharts-point {
                stroke-width: 3 !important;
                filter: drop-shadow(2px 2px 4px rgba(0,0,0,0.3));
                rx: 0 !important;
                ry: 0 !important;
            }

            /* 子任务样式 - 圆角矩形 */
            .highcharts-gantt-series .child-task .highcharts-point {
                stroke-width: 1 !important;
                rx: 3 !important;
                ry: 3 !important;
            }

            /* 甘特图整体样式优化 */
            .highcharts-gantt-series .highcharts-point {
                transition: all 0.2s ease;
            }

            .highcharts-gantt-series .highcharts-point:hover {
                filter: brightness(1.1);
            }

            /* 时间轴样式优化 */
            .highcharts-xaxis-labels text {
                font-size: 11px !important;
                fill: #666 !important;
            }

            .highcharts-grid-line {
                stroke: #e6e6e6 !important;
                stroke-width: 1 !important;
            }
        `;
        document.head.appendChild(style);
    }

    // 设置自定义渲染
    setupCustomRendering() {
        if (!this.chart) return;

        // 简化的自定义渲染 - 主要通过CSS和配置实现
        console.log('甘特图自定义渲染设置完成');
    }

    // 销毁图表
    destroy() {
        if (this.chart) {
            this.chart.destroy();
            this.chart = null;
        }
    }
}

// 全局导出
window.AdvancedGantt = AdvancedGantt;
