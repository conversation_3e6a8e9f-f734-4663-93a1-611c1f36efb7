#!/usr/bin/env python
"""
简单测试甘特图API
"""
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_deep_cpms.settings')
django.setup()

from projects.models import Project
from projects.views import ProjectViewSet

def test_date_handling():
    """测试日期处理功能"""
    print("🧪 测试日期处理功能...")
    
    try:
        # 创建视图实例
        view = ProjectViewSet()
        
        # 测试各种日期格式
        from datetime import date, datetime
        
        # 测试date对象
        test_date = date(2025, 6, 21)
        result = view.safe_isoformat(test_date)
        print(f"  ✅ date对象: {test_date} -> {result}")
        
        # 测试datetime对象
        test_datetime = datetime(2025, 6, 21, 8, 0, 0)
        result = view.safe_isoformat(test_datetime)
        print(f"  ✅ datetime对象: {test_datetime} -> {result}")
        
        # 测试None
        result = view.safe_isoformat(None)
        print(f"  ✅ None值: None -> {result}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 日期处理测试失败: {e}")
        return False

def test_project_data():
    """测试项目数据"""
    print("\n📊 测试项目数据...")
    
    try:
        # 获取测试项目
        project = Project.objects.filter(name='API测试项目').first()
        if not project:
            print("  ❌ 未找到测试项目")
            return False
        
        print(f"  📁 项目: {project.name}")
        print(f"  📅 开始日期: {project.start_date} (类型: {type(project.start_date)})")
        
        # 检查任务
        tasks = project.tasks.all()
        print(f"  📋 任务数量: {tasks.count()}")
        
        for task in tasks:
            print(f"    - {task.wbs_code}: {task.name}")
            print(f"      开始: {task.start_date} (类型: {type(task.start_date)})")
            print(f"      结束: {task.end_date} (类型: {type(task.end_date)})")
            print(f"      工期: {task.planned_duration}天")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 项目数据测试失败: {e}")
        return False

def test_timestamp_conversion():
    """测试时间戳转换逻辑"""
    print("\n🕐 测试时间戳转换逻辑...")
    
    try:
        from datetime import date, datetime, time
        
        # 模拟API中的时间戳转换逻辑
        project = Project.objects.filter(name='API测试项目').first()
        if not project:
            print("  ❌ 未找到测试项目")
            return False
        
        # 测试项目开始日期转换
        project_start = project.start_date
        print(f"  项目开始日期: {project_start} (类型: {type(project_start)})")
        
        if hasattr(project_start, 'timestamp'):
            timestamp = project_start.timestamp() * 1000
            print(f"  直接转换时间戳: {timestamp}")
        else:
            # date对象转换
            dt = datetime.combine(project_start, time.min)
            timestamp = dt.timestamp() * 1000
            print(f"  date转datetime再转时间戳: {timestamp}")
        
        # 测试任务日期转换
        tasks = project.tasks.all()
        for task in tasks:
            if task.start_date:
                if hasattr(task.start_date, 'timestamp'):
                    ts = task.start_date.timestamp() * 1000
                    print(f"  任务开始时间戳: {ts}")
                else:
                    dt = datetime.combine(task.start_date, time.min)
                    ts = dt.timestamp() * 1000
                    print(f"  任务开始时间戳(转换): {ts}")
        
        print("  ✅ 时间戳转换逻辑正常")
        return True
        
    except Exception as e:
        print(f"  ❌ 时间戳转换测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 开始简单甘特图API测试...\n")
    
    # 测试日期处理
    date_ok = test_date_handling()
    
    # 测试项目数据
    project_ok = test_project_data()
    
    # 测试时间戳转换
    timestamp_ok = test_timestamp_conversion()
    
    print(f"\n📝 测试总结:")
    print(f"  1. {'✅' if date_ok else '❌'} 日期处理功能")
    print(f"  2. {'✅' if project_ok else '❌'} 项目数据读取")
    print(f"  3. {'✅' if timestamp_ok else '❌'} 时间戳转换逻辑")
    
    if date_ok and project_ok and timestamp_ok:
        print(f"\n🎉 所有基础功能测试通过！")
        print(f"甘特图API的日期处理问题已修复，现在应该可以正常工作了。")
        print(f"\n🌐 请在浏览器中测试甘特图页面：")
        print(f"  http://127.0.0.1:8000/gantt/")
        print(f"  选择'API测试项目'并点击'加载甘特图'")
    else:
        print(f"\n❌ 部分测试失败，请检查错误信息")
