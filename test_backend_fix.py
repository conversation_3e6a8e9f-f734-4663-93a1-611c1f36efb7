#!/usr/bin/env python
"""
测试后端日期计算修复
"""
import os
import sys
import django
from datetime import datetime

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_deep_cpms.settings')
django.setup()

from tasks.models import Task
from projects.models import Project

def test_backend_date_calculation():
    """测试后端日期计算修复"""
    print("🧪 测试后端日期计算修复...")
    
    # 获取第一个项目
    try:
        project = Project.objects.first()
        if not project:
            print("❌ 没有找到项目，请先创建项目")
            return
        
        print(f"📁 使用项目: {project.name}")
        
        # 测试用例1：1天工期
        print("\n📋 测试用例1：1天工期")
        start_date = datetime(2025, 6, 21, 8, 0, 0)  # 2025年6月21日 8:00
        
        task1 = Task(
            project=project,
            wbs_code="TEST.1",
            name="测试任务1天工期",
            quantity=1.0,
            daily_efficiency=1.0,
            start_date=start_date,
            end_date=start_date  # 临时设置，会被自动计算覆盖
        )
        task1.save()
        
        print(f"  开始时间: {task1.start_date}")
        print(f"  结束时间: {task1.end_date}")
        print(f"  计划工期: {task1.planned_duration}天")
        print(f"  ✅ 1天工期测试: {'通过' if task1.start_date.date() == task1.end_date.date() else '失败'}")
        
        # 测试用例2：3天工期
        print("\n📋 测试用例2：3天工期")
        task2 = Task(
            project=project,
            wbs_code="TEST.2",
            name="测试任务3天工期",
            quantity=3.0,
            daily_efficiency=1.0,
            start_date=start_date,
            end_date=start_date  # 临时设置，会被自动计算覆盖
        )
        task2.save()
        
        print(f"  开始时间: {task2.start_date}")
        print(f"  结束时间: {task2.end_date}")
        print(f"  计划工期: {task2.planned_duration}天")
        
        # 计算预期的结束日期（跳过周末）
        expected_days = (task2.end_date.date() - task2.start_date.date()).days
        print(f"  实际跨度: {expected_days + 1}天")
        print(f"  ✅ 3天工期测试: {'通过' if task2.planned_duration == 3 else '失败'}")
        
        # 测试用例3：小数工程量
        print("\n📋 测试用例3：小数工程量（2.5天）")
        task3 = Task(
            project=project,
            wbs_code="TEST.3",
            name="测试任务小数工期",
            quantity=2.5,
            daily_efficiency=1.0,
            start_date=start_date,
            end_date=start_date  # 临时设置，会被自动计算覆盖
        )
        task3.save()
        
        print(f"  工程数量: {task3.quantity}")
        print(f"  每日工效: {task3.daily_efficiency}")
        print(f"  开始时间: {task3.start_date}")
        print(f"  结束时间: {task3.end_date}")
        print(f"  计划工期: {task3.planned_duration}天")
        print(f"  ✅ 小数工期测试: {'通过' if task3.planned_duration == 3 else '失败'}")
        
        # 清理测试数据
        print("\n🧹 清理测试数据...")
        Task.objects.filter(wbs_code__startswith="TEST.").delete()
        print("✅ 测试数据已清理")
        
        print("\n🎉 后端日期计算修复测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_backend_date_calculation()
