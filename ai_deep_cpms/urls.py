"""
URL configuration for ai_deep_cpms project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.views.generic import TemplateView

urlpatterns = [
    path('admin/', admin.site.urls),
    path('accounts/', include('allauth.urls')),
    path('', include('projects.urls')),
    path('', include('tasks.urls')),
    path('gantt/', TemplateView.as_view(template_name='gantt.html'), name='gantt'),
    path('interactive-gantt/', TemplateView.as_view(template_name='interactive_gantt.html'), name='interactive_gantt'),
    path('test-gantt/', TemplateView.as_view(template_name='test_gantt.html'), name='test_gantt'),
    path('test-highcharts/', TemplateView.as_view(template_name='test_highcharts.html'), name='test_highcharts'),
    path('test-advanced-gantt/', TemplateView.as_view(template_name='test_advanced_gantt.html'), name='test_advanced_gantt'),
    path('debug-gantt/', TemplateView.as_view(template_name='debug_gantt.html'), name='debug_gantt'),
    path('test-gantt-data/', TemplateView.as_view(template_name='test_gantt_data.html'), name='test_gantt_data'),
    path('simple-gantt-test/', TemplateView.as_view(template_name='simple_gantt_test.html'), name='simple_gantt_test'),
    path('project/', TemplateView.as_view(template_name='project_detail.html'), name='project_detail'),
    path('project-tasks/', TemplateView.as_view(template_name='project_tasks.html'), name='project_tasks'),
    path('create-project/', TemplateView.as_view(template_name='create_project.html'), name='create_project'),
    path('test-login/', TemplateView.as_view(template_name='test_login.html'), name='test_login'),
    path('', TemplateView.as_view(template_name='index.html'), name='home'),
]

# 开发环境下提供媒体文件服务
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
