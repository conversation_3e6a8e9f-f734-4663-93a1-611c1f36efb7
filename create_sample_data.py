#!/usr/bin/env python
"""
创建示例数据的脚本
"""
import os
import sys
import django
from datetime import datetime, timedelta

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'ai_deep_cpms.settings')
django.setup()

from django.contrib.auth.models import User
from authentication.models import UserProfile, SubscriptionPlan
from projects.models import Project, Holiday, ProjectListItem
from tasks.models import Task, TaskDependency
from quotas.models import ProjectQuota, QuotaResourceConsumption
from resources.models import ProjectResource


def create_sample_data():
    print("开始创建示例数据...")
    
    # 创建超级用户
    if not User.objects.filter(username='admin').exists():
        admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='admin123'
        )
        print("创建超级用户: admin/admin123")
    else:
        admin_user = User.objects.get(username='admin')
        print("超级用户已存在")
    
    # 创建测试用户
    try:
        test_user = User.objects.get(username='testuser')
        # 更新邮箱
        if test_user.email != '<EMAIL>':
            test_user.email = '<EMAIL>'
            test_user.save()
            print("更新测试用户邮箱: <EMAIL>")
        else:
            print("测试用户已存在: <EMAIL>/test123")
    except User.DoesNotExist:
        test_user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='test123'
        )
        print("创建测试用户: <EMAIL>/test123")

    # 确保管理员用户有邮箱
    if admin_user.email != '<EMAIL>':
        admin_user.email = '<EMAIL>'
        admin_user.save()
        print("更新管理员邮箱: <EMAIL>")
    
    # 创建用户配置文件
    if not hasattr(test_user, 'profile'):
        UserProfile.objects.create(
            user=test_user,
            company='测试公司',
            phone='13800138000'
        )
        print("创建用户配置文件")
    
    # 创建订阅计划
    if not SubscriptionPlan.objects.exists():
        SubscriptionPlan.objects.create(
            name='基础版',
            description='适合小型项目的基础功能',
            price=10.00,
            duration_days=30,
            max_projects=5,
            max_tasks_per_project=100
        )
        
        SubscriptionPlan.objects.create(
            name='专业版',
            description='适合中大型项目的专业功能',
            price=25.00,
            duration_days=30,
            max_projects=20,
            max_tasks_per_project=1000
        )
        print("创建订阅计划")
    
    # 创建节假日
    if not Holiday.objects.exists():
        holidays = [
            ('2025-01-01', '元旦'),
            ('2025-02-10', '春节'),
            ('2025-02-11', '春节'),
            ('2025-02-12', '春节'),
            ('2025-04-05', '清明节'),
            ('2025-05-01', '劳动节'),
            ('2025-06-09', '端午节'),
            ('2025-09-15', '中秋节'),
            ('2025-10-01', '国庆节'),
            ('2025-10-02', '国庆节'),
            ('2025-10-03', '国庆节'),
        ]
        
        for date_str, name in holidays:
            Holiday.objects.create(
                date=datetime.strptime(date_str, '%Y-%m-%d').date(),
                name=name
            )
        print("创建节假日数据")
    
    # 创建示例项目
    if not Project.objects.filter(owner=test_user).exists():
        project = Project.objects.create(
            name='办公楼建设项目',
            description='某公司新办公楼建设项目，包含土建、装修、设备安装等工程',
            owner=test_user,
            start_date=datetime.now().date(),
            location='北京市朝阳区',
            work_hours_per_day=8,
            status='active'
        )
        print(f"创建示例项目: {project.name}")
        
        # 创建项目清单
        list_items_data = [
            ('001', '土方开挖', '立方米', 1000, 50),
            ('002', '混凝土浇筑', '立方米', 500, 300),
            ('003', '钢筋制作安装', '吨', 100, 4500),
            ('004', '砌体工程', '立方米', 200, 180),
            ('005', '屋面防水', '平方米', 800, 80),
        ]
        
        for code, desc, unit, qty, price in list_items_data:
            ProjectListItem.objects.create(
                project=project,
                code=code,
                description=desc,
                unit=unit,
                quantity=qty,
                comprehensive_unit_price=price
            )
        print("创建项目清单")
        
        # 创建任务
        tasks_data = [
            ('1', '前期准备', '项', 1, 1, 5),
            ('1.1', '设计方案', '项', 1, 1, 10),
            ('1.2', '施工许可', '项', 1, 1, 5),
            ('2', '土建工程', '项', 1, 1, 60),
            ('2.1', '基础工程', '项', 1, 1, 20),
            ('2.2', '主体结构', '项', 1, 1, 40),
            ('3', '装修工程', '项', 1, 1, 30),
            ('3.1', '内装修', '项', 1, 1, 25),
            ('3.2', '外装修', '项', 1, 1, 15),
        ]
        
        created_tasks = {}
        start_date = datetime.now()
        
        for wbs, name, unit, qty, eff, duration in tasks_data:
            task = Task.objects.create(
                project=project,
                wbs_code=wbs,
                name=name,
                unit=unit,
                quantity=qty,
                daily_efficiency=eff,
                start_date=start_date,
                end_date=start_date + timedelta(days=duration),
                status='not_started'
            )
            created_tasks[wbs] = task
            start_date += timedelta(days=duration)
        
        # 设置父子关系
        created_tasks['1.1'].parent = created_tasks['1']
        created_tasks['1.1'].save()
        created_tasks['1.2'].parent = created_tasks['1']
        created_tasks['1.2'].save()
        created_tasks['2.1'].parent = created_tasks['2']
        created_tasks['2.1'].save()
        created_tasks['2.2'].parent = created_tasks['2']
        created_tasks['2.2'].save()
        created_tasks['3.1'].parent = created_tasks['3']
        created_tasks['3.1'].save()
        created_tasks['3.2'].parent = created_tasks['3']
        created_tasks['3.2'].save()
        
        print("创建项目任务")
        
        # 创建任务依赖关系
        TaskDependency.objects.create(
            from_task=created_tasks['1'],
            to_task=created_tasks['2'],
            dependency_type='FS'
        )
        TaskDependency.objects.create(
            from_task=created_tasks['2'],
            to_task=created_tasks['3'],
            dependency_type='FS'
        )
        print("创建任务依赖关系")
        
        # 创建定额数据
        quota = ProjectQuota.objects.create(
            project=project,
            quota_number='A001',
            name='土方开挖定额',
            work_content='人工配合机械开挖土方',
            unit='立方米',
            unit_price=45.00,
            category='comprehensive'
        )
        
        # 创建定额资源消耗
        QuotaResourceConsumption.objects.create(
            quota=quota,
            resource_number='R001',
            resource_name='挖掘机',
            category='machinery',
            unit='台班',
            consumption=0.1,
            unit_price=300.00
        )
        
        QuotaResourceConsumption.objects.create(
            quota=quota,
            resource_number='R002',
            resource_name='普工',
            category='labor',
            unit='工日',
            consumption=0.5,
            unit_price=150.00
        )
        print("创建定额数据")
        
        # 创建资源数据
        ProjectResource.objects.create(
            project=project,
            quota=quota,
            resource_number='R001',
            name='挖掘机',
            category='machinery',
            unit='台班',
            market_price=320.00,
            budget_price=300.00,
            supplier='某机械租赁公司',
            specification='CAT320D'
        )
        
        ProjectResource.objects.create(
            project=project,
            quota=quota,
            resource_number='R002',
            name='普工',
            category='labor',
            unit='工日',
            market_price=160.00,
            budget_price=150.00,
            specification='8小时工作制'
        )
        print("创建资源数据")
    
    print("示例数据创建完成！")
    print("\n登录信息:")
    print("管理员: admin / admin123")
    print("测试用户: testuser / test123")
    print("\n访问地址:")
    print("前端: http://127.0.0.1:8000/")
    print("管理后台: http://127.0.0.1:8000/admin/")
    print("API: http://127.0.0.1:8000/api/")


if __name__ == '__main__':
    create_sample_data()
