# Generated by Django 5.2.3 on 2025-06-15 23:58

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SubscriptionPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='计划名称')),
                ('description', models.TextField(blank=True, verbose_name='计划描述')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='价格（美元）')),
                ('duration_days', models.IntegerField(default=30, verbose_name='有效期（天）')),
                ('max_projects', models.IntegerField(default=10, verbose_name='最大项目数')),
                ('max_tasks_per_project', models.IntegerField(default=1000, verbose_name='每个项目最大任务数')),
                ('max_storage_mb', models.IntegerField(default=1000, verbose_name='最大存储空间（MB）')),
                ('can_export', models.BooleanField(default=True, verbose_name='可以导出')),
                ('can_import', models.BooleanField(default=True, verbose_name='可以导入')),
                ('can_use_api', models.BooleanField(default=True, verbose_name='可以使用API')),
                ('can_collaborate', models.BooleanField(default=True, verbose_name='可以协作')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否激活')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '订阅计划',
                'verbose_name_plural': '订阅计划',
                'ordering': ['price'],
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('trial_start_date', models.DateTimeField(auto_now_add=True, verbose_name='试用开始时间')),
                ('trial_end_date', models.DateTimeField(blank=True, null=True, verbose_name='试用结束时间')),
                ('is_trial_active', models.BooleanField(default=True, verbose_name='试用是否激活')),
                ('subscription_status', models.CharField(choices=[('trial', '试用期'), ('active', '已订阅'), ('expired', '已过期'), ('cancelled', '已取消')], default='trial', max_length=20, verbose_name='订阅状态')),
                ('subscription_start_date', models.DateTimeField(blank=True, null=True, verbose_name='订阅开始时间')),
                ('subscription_end_date', models.DateTimeField(blank=True, null=True, verbose_name='订阅结束时间')),
                ('company', models.CharField(blank=True, max_length=200, verbose_name='公司名称')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='电话号码')),
                ('avatar', models.ImageField(blank=True, null=True, upload_to='avatars/', verbose_name='头像')),
                ('language', models.CharField(default='zh-hans', max_length=10, verbose_name='语言偏好')),
                ('timezone', models.CharField(default='Asia/Shanghai', max_length=50, verbose_name='时区')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户配置',
                'verbose_name_plural': '用户配置',
            },
        ),
        migrations.CreateModel(
            name='UserSubscription',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('start_date', models.DateTimeField(verbose_name='开始时间')),
                ('end_date', models.DateTimeField(verbose_name='结束时间')),
                ('payment_amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='支付金额')),
                ('payment_method', models.CharField(blank=True, max_length=50, verbose_name='支付方式')),
                ('payment_transaction_id', models.CharField(blank=True, max_length=100, verbose_name='交易ID')),
                ('status', models.CharField(choices=[('pending', '待支付'), ('active', '激活'), ('expired', '已过期'), ('cancelled', '已取消'), ('refunded', '已退款')], default='pending', max_length=20, verbose_name='状态')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('plan', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='authentication.subscriptionplan', verbose_name='订阅计划')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subscriptions', to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户订阅记录',
                'verbose_name_plural': '用户订阅记录',
                'ordering': ['-created_at'],
            },
        ),
    ]
